import logging
from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import Integrity<PERSON>rror
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
import uuid

# Import models
from app.modules.tenants.restaurants.menu.models.menu_item import MenuItem

# Import schemas
from app.modules.tenants.restaurants.menu.schemas.menu_item import MenuItemUpdate

# Import WebSocket utility
from app.websockets.manager import emit_to_tenant

# Import helper services
from .menu_item_allergen_manager import MenuItemAllergenManager
from .menu_item_groups_updater import MenuItemGroupsUpdater

logger = logging.getLogger(__name__)


class MenuItemUpdater:
    def __init__(self, db_session: AsyncSession):
        self.db = db_session
        self.allergen_manager = MenuItemAllergenManager(db_session)
        self.groups_updater = MenuItemGroupsUpdater(db_session)

    async def update_item(
        self, item_id: uuid.UUID, item_in: MenuItemUpdate, tenant_id: uuid.UUID
    ) -> Optional[MenuItem]:
        """Updates an existing menu item."""
        # Check if we need to update groups to determine if we need full details
        item_data_temp = item_in.model_dump(exclude_unset=True)
        has_groups_update = self._has_groups_update(item_data_temp)

        db_item = await self._get_item_for_update(item_id, tenant_id, has_groups_update)
        if not db_item:
            return None

        # Extract and validate update data
        update_data = await self._prepare_update_data(item_data_temp, db_item, tenant_id)
        
        # Store original state for comparison
        original_state = self._capture_original_state(db_item)

        try:
            # Apply updates
            await self._apply_updates(db_item, update_data, tenant_id)
            
            # Commit changes
            await self.db.commit()
            await self.db.refresh(db_item)

            # Emit WebSocket event if state changed
            if self._state_changed(original_state, db_item):
                await self._emit_update_event(item_id, db_item, tenant_id)

            # Return updated item with full details
            return await self._load_updated_item(item_id, tenant_id)

        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"Integrity error updating item {item_id} for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Error updating menu item, possibly due to constraint violation.",
            )
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Unexpected error updating item {item_id} for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    def _has_groups_update(self, item_data: dict) -> bool:
        """Check if the update includes group changes."""
        return (
            "variant_groups" in item_data
            or "modifier_groups" in item_data
            or "optional_groups" in item_data
        )

    async def _get_item_for_update(self, item_id: uuid.UUID, tenant_id: uuid.UUID, 
                                  include_details: bool) -> Optional[MenuItem]:
        """Get the item to be updated with appropriate detail level."""
        # This would typically call the main service's get_item method
        # For now, we'll implement a basic version
        from sqlalchemy import select
        from sqlalchemy.orm import selectinload, joinedload
        
        query = select(MenuItem).where(MenuItem.id == item_id, MenuItem.tenant_id == tenant_id)
        
        if include_details:
            from app.modules.core.functions.customizations.models.variant_group import VariantGroup
            from app.modules.core.functions.customizations.models.modifier_group import ModifierGroup
            from app.modules.core.functions.customizations.models.optional_group import OptionalGroup
            
            query = query.options(
                joinedload(MenuItem.category),
                selectinload(MenuItem.variant_groups).selectinload(VariantGroup.options),
                selectinload(MenuItem.modifier_groups).selectinload(ModifierGroup.options),
                selectinload(MenuItem.optional_groups).selectinload(OptionalGroup.options),
                selectinload(MenuItem.allergens),
            )
        
        result = await self.db.execute(query)
        return result.scalars().first()

    async def _prepare_update_data(self, item_data: dict, db_item: MenuItem, tenant_id: uuid.UUID) -> dict:
        """Prepare and validate update data."""
        # Extract nested data for special handling
        variant_groups_data = item_data.pop("variant_groups", None)
        modifier_groups_data = item_data.pop("modifier_groups", None)
        optional_groups_data = item_data.pop("optional_groups", None)
        allergen_ids_data = item_data.pop("allergen_ids", None)

        update_data = {k: v for k, v in item_data.items() if k != "inventory_item_ids"}

        # Validate that we have something to update
        has_groups_update = (
            variant_groups_data is not None
            or modifier_groups_data is not None
            or optional_groups_data is not None
        )
        
        if not update_data and not has_groups_update:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No fields provided for item update.",
            )

        # Validate category if changed
        if "category_id" in update_data and update_data["category_id"] != db_item.category_id:
            await self._validate_category(update_data["category_id"], tenant_id)

        return {
            "base_data": update_data,
            "variant_groups": variant_groups_data,
            "modifier_groups": modifier_groups_data,
            "optional_groups": optional_groups_data,
            "allergen_ids": allergen_ids_data,
        }

    async def _validate_category(self, category_id: uuid.UUID, tenant_id: uuid.UUID):
        """Validate that the category exists for the tenant."""
        from app.modules.tenants.restaurants.menu.services.menu_category_service import MenuCategoryService
        
        category_service = MenuCategoryService(self.db)
        category = await category_service.get_category(category_id, tenant_id)
        if not category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Menu category with id {category_id} not found for this tenant.",
            )

    def _capture_original_state(self, db_item: MenuItem) -> dict:
        """Capture original state for comparison."""
        return {
            "is_available": db_item.is_available,
            "is_active": db_item.is_active,
        }

    async def _apply_updates(self, db_item: MenuItem, update_data: dict, tenant_id: uuid.UUID):
        """Apply all updates to the menu item."""
        # Update base fields
        if update_data["base_data"]:
            for key, value in update_data["base_data"].items():
                setattr(db_item, key, value)

        # Handle allergen associations
        if update_data["allergen_ids"] is not None:
            # Convert allergen_ids to the format expected by update_item_allergens
            allergens_data = [{"id": allergen_id} for allergen_id in update_data["allergen_ids"]]
            await self.allergen_manager.update_item_allergens(db_item, allergens_data, tenant_id)
            await self.db.flush()

        # Handle groups updates
        has_groups_update = (
            update_data["variant_groups"] is not None
            or update_data["modifier_groups"] is not None
            or update_data["optional_groups"] is not None
        )
        
        if has_groups_update:
            await self.groups_updater.update_all_groups(
                db_item, 
                update_data["variant_groups"],
                update_data["modifier_groups"], 
                update_data["optional_groups"],
                tenant_id
            )
            await self.db.flush()

        # Add the item to session if it has base data changes
        if update_data["base_data"]:
            self.db.add(db_item)

    def _state_changed(self, original_state: dict, db_item: MenuItem) -> bool:
        """Check if the item state has changed."""
        return (
            original_state["is_available"] != db_item.is_available
            or original_state["is_active"] != db_item.is_active
        )

    async def _emit_update_event(self, item_id: uuid.UUID, db_item: MenuItem, tenant_id: uuid.UUID):
        """Emit WebSocket event for item update."""
        try:
            payload = {
                "item_id": db_item.id,
                "is_available": db_item.is_available,
                "is_active": db_item.is_active,
                "name": db_item.name,
            }
            await emit_to_tenant(tenant_id, "menu_item_update", payload)
            logger.info(f"WebSocket event 'menu_item_update' emitted for item {item_id}, tenant {tenant_id}")
        except Exception as ws_error:
            logger.error(f"Failed to emit WebSocket event for item {item_id} update: {ws_error}")

    async def _load_updated_item(self, item_id: uuid.UUID, tenant_id: uuid.UUID) -> MenuItem:
        """Load the updated item with full details."""
        from sqlalchemy import select
        from sqlalchemy.orm import selectinload, joinedload
        from app.modules.core.functions.customizations.models.variant_group import VariantGroup
        from app.modules.core.functions.customizations.models.modifier_group import ModifierGroup
        from app.modules.core.functions.customizations.models.optional_group import OptionalGroup
        
        stmt = (
            select(MenuItem)
            .options(
                joinedload(MenuItem.category),
                selectinload(MenuItem.variant_groups).selectinload(VariantGroup.options),
                selectinload(MenuItem.modifier_groups).selectinload(ModifierGroup.options),
                selectinload(MenuItem.optional_groups).selectinload(OptionalGroup.options),
                selectinload(MenuItem.allergens),
            )
            .where(MenuItem.id == item_id, MenuItem.tenant_id == tenant_id)
        )
        result = await self.db.execute(stmt)
        return result.scalars().one()