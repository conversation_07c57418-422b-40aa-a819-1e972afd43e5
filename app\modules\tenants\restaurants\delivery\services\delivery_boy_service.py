from typing import List, Optional
import uuid
from datetime import datetime  # Added
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

# Corrigido: NotFoundException -> NotFoundError, BadRequestException -> BusinessLogicError
from app.core.exceptions import NotFoundError, BusinessLogicError
from app.modules.tenants.restaurants.delivery.models.delivery_boy import DeliveryBoy
from app.modules.tenants.restaurants.delivery.models.delivery_boy_location import (
    DeliveryBoyLocation,
)  # Added
from app.modules.tenants.restaurants.delivery.schemas.delivery_boy import (  # noqa: E402
    DeliveryBoyCreate,
    DeliveryBoyUpdate,
)
from app.modules.tenants.restaurants.delivery.schemas.delivery_boy_location import (  # noqa: E402
    DeliveryBoyLocationCreate,
)  # Added

# Assuming DELIVERY_PERSON is in TenantStaffSubRole
from app.core.roles import TenantStaffSubRole, TenantRole  # noqa: E402
from app.services.tenant_user_association_service import (
    TenantUserAssociationService,
)  # To manage associations


class DeliveryBoyService:
    async def create_delivery_boy(
        self, db: AsyncSession, delivery_boy_data: DeliveryBoyCreate
    ) -> DeliveryBoy:
        # Check if a delivery boy with this user_id already exists for this tenant FIRST
        stmt_existing = select(DeliveryBoy).where(
            DeliveryBoy.tenant_id == delivery_boy_data.tenant_id,
            DeliveryBoy.user_id == delivery_boy_data.user_id,
        )
        existing_delivery_boy = (await db.execute(stmt_existing)).scalars().first()
        if existing_delivery_boy:
            raise BusinessLogicError(  # Corrigido para BusinessLogicError
                f"Delivery boy with user_id {delivery_boy_data.user_id} already exists for this tenant."  # noqa: E501
            )

        # If not existing, THEN create the instance
        db_delivery_boy = DeliveryBoy(**delivery_boy_data.model_dump())

        # Ensure TenantUserAssociation with DELIVERY_PERSON sub_role
        tua_service = TenantUserAssociationService()
        association = await tua_service._get_association(
            db, tenant_id=delivery_boy_data.tenant_id, user_id=delivery_boy_data.user_id
        )
        if not association:
            # If no association, create one with staff role and delivery_person sub_role
            # This assumes the user is already a 'staff' or this needs more complex logic
            # For simplicity, we'll create/update it.
            # A more robust solution might require checking existing role or tenant_type.
            await tua_service.add_user_to_tenant(
                db,
                tenant_id=delivery_boy_data.tenant_id,
                user_id=delivery_boy_data.user_id,
                role=TenantRole.STAFF,  # Defaulting to STAFF, might need adjustment
                staff_sub_role=TenantStaffSubRole.DELIVERY_PERSON,
            )
        elif association.staff_sub_role != TenantStaffSubRole.DELIVERY_PERSON:
            # If association exists but not as delivery person, update it
            # This might override an existing sub-role, careful consideration needed
            association.staff_sub_role = TenantStaffSubRole.DELIVERY_PERSON
            db.add(association)

        # Explicitly set timestamps just before adding, though mixin should handle it
        now = datetime.utcnow()
        db_delivery_boy.created_at = now
        db_delivery_boy.updated_at = now

        db.add(db_delivery_boy)
        return db_delivery_boy

    async def get_delivery_boy_by_id(
        self, db: AsyncSession, delivery_boy_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Optional[DeliveryBoy]:
        stmt = select(DeliveryBoy).where(
            DeliveryBoy.id == delivery_boy_id, DeliveryBoy.tenant_id == tenant_id
        )
        result = await db.execute(stmt)
        return result.scalars().first()

    async def get_delivery_boy_by_user_id(
        self, db: AsyncSession, user_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Optional[DeliveryBoy]:
        """
        Retrieves a delivery boy profile by their user_id and tenant_id.
        """
        stmt = select(DeliveryBoy).where(
            DeliveryBoy.user_id == user_id, DeliveryBoy.tenant_id == tenant_id
        )
        result = await db.execute(stmt)
        return result.scalars().first()

    async def get_delivery_boys_by_tenant(
        self, db: AsyncSession, tenant_id: uuid.UUID, skip: int = 0, limit: int = 100
    ) -> List[DeliveryBoy]:
        stmt = (
            select(DeliveryBoy).where(DeliveryBoy.tenant_id == tenant_id).offset(skip).limit(limit)
        )
        result = await db.execute(stmt)
        return result.scalars().all()

    async def update_delivery_boy(
        self,
        db: AsyncSession,
        delivery_boy_id: uuid.UUID,
        tenant_id: uuid.UUID,
        update_data: DeliveryBoyUpdate,
    ) -> Optional[DeliveryBoy]:
        db_delivery_boy = await self.get_delivery_boy_by_id(db, delivery_boy_id, tenant_id)
        if not db_delivery_boy:
            return None

        update_data_dict = update_data.model_dump(exclude_unset=True)
        for key, value in update_data_dict.items():
            setattr(db_delivery_boy, key, value)

        return db_delivery_boy

    async def update_delivery_boy_location(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID,
        delivery_boy_id: uuid.UUID,
        location_data: DeliveryBoyLocationCreate,
    ) -> DeliveryBoyLocation:
        # First, get the delivery boy to ensure it exists and to update its last known location
        db_delivery_boy = await self.get_delivery_boy_by_id(
            db, delivery_boy_id=delivery_boy_id, tenant_id=tenant_id
        )
        if not db_delivery_boy:
            # Corrigido para NotFoundError
            raise NotFoundError(
                f"DeliveryBoy with id {delivery_boy_id} not found for tenant {tenant_id}"
            )

        # Create new location entry
        new_location = DeliveryBoyLocation(
            tenant_id=tenant_id,
            delivery_boy_id=delivery_boy_id,
            latitude=location_data.latitude,
            longitude=location_data.longitude,
            accuracy=location_data.accuracy,
            timestamp=location_data.timestamp or datetime.utcnow(),
        )
        db.add(new_location)

        # Update DeliveryBoy's last known location fields
        db_delivery_boy.last_latitude = new_location.latitude
        db_delivery_boy.last_longitude = new_location.longitude
        db_delivery_boy.last_location_update_at = new_location.timestamp

        return new_location

    async def get_last_known_location(
        self, db: AsyncSession, tenant_id: uuid.UUID, delivery_boy_id: uuid.UUID
    ) -> Optional[DeliveryBoyLocation]:
        stmt = (
            select(DeliveryBoyLocation)
            .where(
                DeliveryBoyLocation.tenant_id == tenant_id,
                DeliveryBoyLocation.delivery_boy_id == delivery_boy_id,
            )
            .order_by(DeliveryBoyLocation.timestamp.desc())
        )
        result = await db.execute(stmt)
        return result.scalars().first()

    async def set_delivery_boy_active_status(
        self,
        db: AsyncSession,
        delivery_boy_id: uuid.UUID,
        tenant_id: uuid.UUID,
        is_active: bool,
    ) -> Optional[DeliveryBoy]:
        db_delivery_boy = await self.get_delivery_boy_by_id(db, delivery_boy_id, tenant_id)
        if not db_delivery_boy:
            raise NotFoundError("Delivery boy not found.")  # Corrigido para NotFoundError

        db_delivery_boy.is_active = is_active
        return db_delivery_boy
