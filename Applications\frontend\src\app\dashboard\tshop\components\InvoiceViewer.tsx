'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  DocumentTextIcon,
  EyeIcon,
  ArrowDownTrayIcon,
  MagnifyingGlassIcon,
  CalendarIcon,
  BuildingOfficeIcon
} from '@heroicons/react/24/outline';
import { formatCurrency } from '@/lib/utils';

interface Invoice {
  id: string;
  invoice_number: string;
  supplier_name: string;
  supplier_id: string;
  amount: number;
  issue_date: string;
  due_date: string;
  status: 'pending' | 'paid' | 'overdue' | 'cancelled';
  file_url: string;
  file_name: string;
  file_size: number;
  order_number?: string;
  description?: string;
  payment_method?: string;
  paid_date?: string;
}

// Mock data - replace with real API call
const mockInvoices: Invoice[] = [
  {
    id: '1',
    invoice_number: 'INV-2024-001',
    supplier_name: 'TechSupply Pro',
    supplier_id: 'sup1',
    amount: 2450.00,
    issue_date: '2024-01-15T00:00:00Z',
    due_date: '2024-02-15T00:00:00Z',
    status: 'paid',
    file_url: '/invoices/inv-2024-001.pdf',
    file_name: 'invoice-techsupply-001.pdf',
    file_size: 245760,
    order_number: 'ORD-2024-001',
    description: 'Equipamentos de informática',
    payment_method: 'Transferência bancária',
    paid_date: '2024-01-20T00:00:00Z'
  },
  {
    id: '2',
    invoice_number: 'INV-2024-002',
    supplier_name: 'Office Solutions',
    supplier_id: 'sup2',
    amount: 1890.50,
    issue_date: '2024-01-18T00:00:00Z',
    due_date: '2024-02-18T00:00:00Z',
    status: 'pending',
    file_url: '/invoices/inv-2024-002.pdf',
    file_name: 'invoice-office-002.pdf',
    file_size: 189432,
    order_number: 'ORD-2024-002',
    description: 'Material de escritório'
  },
  {
    id: '3',
    invoice_number: 'INV-2024-003',
    supplier_name: 'Industrial Equipment Co',
    supplier_id: 'sup3',
    amount: 5200.00,
    issue_date: '2024-01-10T00:00:00Z',
    due_date: '2024-01-25T00:00:00Z',
    status: 'overdue',
    file_url: '/invoices/inv-2024-003.pdf',
    file_name: 'invoice-industrial-003.pdf',
    file_size: 312456,
    order_number: 'ORD-2024-003',
    description: 'Equipamentos industriais'
  }
];

export function InvoiceViewer() {
  const [invoices] = useState<Invoice[]>(mockInvoices);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedSupplier, setSelectedSupplier] = useState('all');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Pendente';
      case 'paid':
        return 'Pago';
      case 'overdue':
        return 'Vencido';
      case 'cancelled':
        return 'Cancelado';
      default:
        return status;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = invoice.invoice_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         invoice.supplier_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         invoice.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = selectedStatus === 'all' || invoice.status === selectedStatus;
    const matchesSupplier = selectedSupplier === 'all' || invoice.supplier_id === selectedSupplier;
    
    return matchesSearch && matchesStatus && matchesSupplier;
  });

  const suppliers = [...new Set(invoices.map(inv => ({ id: inv.supplier_id, name: inv.supplier_name })))];

  const handleViewInvoice = (invoice: Invoice) => {
    // Open invoice in new tab
    window.open(invoice.file_url, '_blank');
  };

  const handleDownloadInvoice = (invoice: Invoice) => {
    // Download invoice file
    const link = document.createElement('a');
    link.href = invoice.file_url;
    link.download = invoice.file_name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const isOverdue = (dueDate: string) => {
    return new Date(dueDate) < new Date();
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <DocumentTextIcon className="h-5 w-5 mr-2" />
            Faturas e Documentos
          </CardTitle>
          <CardDescription>
            Visualize e gerencie todas as faturas dos seus fornecedores B2B
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="relative">
              <MagnifyingGlassIcon className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
              <Input
                placeholder="Buscar faturas..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os status</SelectItem>
                <SelectItem value="pending">Pendente</SelectItem>
                <SelectItem value="paid">Pago</SelectItem>
                <SelectItem value="overdue">Vencido</SelectItem>
                <SelectItem value="cancelled">Cancelado</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={selectedSupplier} onValueChange={setSelectedSupplier}>
              <SelectTrigger>
                <SelectValue placeholder="Fornecedor" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os fornecedores</SelectItem>
                {suppliers.map(supplier => (
                  <SelectItem key={supplier.id} value={supplier.id}>
                    {supplier.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <div className="text-sm text-gray-600 flex items-center">
              {filteredInvoices.length} faturas encontradas
            </div>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="p-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">
                    {invoices.filter(inv => inv.status === 'paid').length}
                  </p>
                  <p className="text-sm text-gray-600">Pagas</p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-yellow-600">
                    {invoices.filter(inv => inv.status === 'pending').length}
                  </p>
                  <p className="text-sm text-gray-600">Pendentes</p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-red-600">
                    {invoices.filter(inv => inv.status === 'overdue').length}
                  </p>
                  <p className="text-sm text-gray-600">Vencidas</p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-blue-600">
                    {formatCurrency(invoices.reduce((sum, inv) => sum + inv.amount, 0))}
                  </p>
                  <p className="text-sm text-gray-600">Total</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      {/* Invoices List */}
      <div className="space-y-4">
        {filteredInvoices.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">Nenhuma fatura encontrada com os filtros selecionados.</p>
            </CardContent>
          </Card>
        ) : (
          filteredInvoices.map((invoice) => (
            <Card key={invoice.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="space-y-3 flex-1">
                    {/* Invoice Header */}
                    <div className="flex items-center space-x-3">
                      <h3 className="font-semibold text-lg">{invoice.invoice_number}</h3>
                      <Badge className={getStatusColor(invoice.status)}>
                        {getStatusText(invoice.status)}
                      </Badge>
                      {invoice.status === 'overdue' && isOverdue(invoice.due_date) && (
                        <Badge variant="destructive">
                          Vencida
                        </Badge>
                      )}
                    </div>

                    {/* Invoice Details */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="text-gray-500">Fornecedor</p>
                        <div className="flex items-center">
                          <BuildingOfficeIcon className="h-4 w-4 mr-1 text-gray-400" />
                          <p className="font-medium">{invoice.supplier_name}</p>
                        </div>
                      </div>
                      <div>
                        <p className="text-gray-500">Data de Emissão</p>
                        <div className="flex items-center">
                          <CalendarIcon className="h-4 w-4 mr-1 text-gray-400" />
                          <p className="font-medium">
                            {new Date(invoice.issue_date).toLocaleDateString('pt-BR')}
                          </p>
                        </div>
                      </div>
                      <div>
                        <p className="text-gray-500">Vencimento</p>
                        <div className="flex items-center">
                          <CalendarIcon className="h-4 w-4 mr-1 text-gray-400" />
                          <p className={`font-medium ${isOverdue(invoice.due_date) && invoice.status !== 'paid' ? 'text-red-600' : ''}`}>
                            {new Date(invoice.due_date).toLocaleDateString('pt-BR')}
                          </p>
                        </div>
                      </div>
                      <div>
                        <p className="text-gray-500">Valor</p>
                        <p className="font-bold text-lg text-green-600">
                          {formatCurrency(invoice.amount)}
                        </p>
                      </div>
                    </div>

                    {/* Additional Info */}
                    <div className="flex items-center justify-between text-sm">
                      <div className="space-y-1">
                        {invoice.order_number && (
                          <p className="text-gray-600">
                            <span className="font-medium">Pedido:</span> {invoice.order_number}
                          </p>
                        )}
                        {invoice.description && (
                          <p className="text-gray-600">
                            <span className="font-medium">Descrição:</span> {invoice.description}
                          </p>
                        )}
                        {invoice.paid_date && (
                          <p className="text-gray-600">
                            <span className="font-medium">Pago em:</span> {new Date(invoice.paid_date).toLocaleDateString('pt-BR')}
                          </p>
                        )}
                      </div>
                      
                      <div className="text-right">
                        <p className="text-gray-500">{invoice.file_name}</p>
                        <p className="text-xs text-gray-400">{formatFileSize(invoice.file_size)}</p>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex flex-col space-y-2 ml-4">
                    <Button
                      onClick={() => handleViewInvoice(invoice)}
                      size="sm"
                      variant="outline"
                    >
                      <EyeIcon className="h-4 w-4 mr-2" />
                      Visualizar
                    </Button>
                    
                    <Button
                      onClick={() => handleDownloadInvoice(invoice)}
                      size="sm"
                      variant="outline"
                    >
                      <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}
