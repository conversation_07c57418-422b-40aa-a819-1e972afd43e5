import uuid
from datetime import datetime
from pydantic import BaseModel, Field
from app.modules.core.functions.offerts.models.coupon import DiscountType
from decimal import Decimal

# Base Schema
class CouponBase(BaseModel):
    code: str = Field(..., max_length=50)
    discount_type: DiscountType
    value: Decimal
    expires_at: datetime | None = None
    usage_limit: int | None = None
    is_active: bool = True

    class Config:
        from_attributes = True

# Schema for creating a coupon
class CouponCreate(CouponBase):
    tenant_id: uuid.UUID

# Schema for updating a coupon
class CouponUpdate(BaseModel):
    code: str | None = Field(None, max_length=50)
    discount_type: DiscountType | None = None
    value: float | None = None
    expires_at: datetime | None = None
    usage_limit: int | None = None
    is_active: bool | None = None

# Schema for reading a coupon
class CouponRead(CouponBase):
    id: uuid.UUID
    tenant_id: uuid.UUID
    usage_count: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True 