"""Shopping List service for tenant-specific shopping list management."""

import uuid
from datetime import date, datetime
from decimal import Decimal
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, or_, func, text
from sqlalchemy.orm import selectinload

from app.modules.shared.shopping_list.models.shopping_list import (
    ShoppingList,
    ShoppingListItem,
    ShoppingListStatus,
    ShoppingListItemStatus,
    ShoppingListItemPriority
)
from app.core.exceptions import NotFoundError, ValidationError


class ShoppingListService:
    """Service for managing shopping lists."""

    async def get_shopping_lists(
        self,
        db: AsyncSession,
        *,
        tenant_id: uuid.UUID,
        status: Optional[ShoppingListStatus] = None,
        auto_generated: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[ShoppingList]:
        """Get shopping lists with filters."""

        query = select(ShoppingList).options(
            selectinload(ShoppingList.items)
        ).where(ShoppingList.tenant_id == tenant_id)

        if status:
            query = query.where(ShoppingList.status == status.value)

        if auto_generated is not None:
            query = query.where(ShoppingList.auto_generated == auto_generated)

        query = query.order_by(ShoppingList.created_at.desc())
        query = query.offset(skip).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    async def get_shopping_list_by_id(
        self,
        db: AsyncSession,
        *,
        list_id: uuid.UUID,
        tenant_id: uuid.UUID
    ) -> Optional[ShoppingList]:
        """Get shopping list by ID."""

        query = select(ShoppingList).options(
            selectinload(ShoppingList.items)
        ).where(
            and_(
                ShoppingList.id == list_id,
                ShoppingList.tenant_id == tenant_id
            )
        )

        result = await db.execute(query)
        return result.scalar_one_or_none()

    async def get_shopping_list_items(
        self,
        db: AsyncSession,
        *,
        list_id: uuid.UUID,
        tenant_id: uuid.UUID,
        status: Optional[ShoppingListItemStatus] = None,
        priority: Optional[ShoppingListItemPriority] = None
    ) -> List[ShoppingListItem]:
        """Get items for a shopping list."""

        # Verify list belongs to tenant
        shopping_list = await self.get_shopping_list_by_id(
            db, list_id=list_id, tenant_id=tenant_id
        )

        if not shopping_list:
            raise NotFoundError("Shopping list not found")

        query = select(ShoppingListItem).where(
            ShoppingListItem.shopping_list_id == list_id
        )

        if status:
            query = query.where(ShoppingListItem.status == status.value)

        if priority:
            query = query.where(ShoppingListItem.priority == priority.value)

        query = query.order_by(ShoppingListItem.sort_order)

        result = await db.execute(query)
        return result.scalars().all()

    async def create_list(
        self,
        db: AsyncSession,
        *,
        list_in: "ShoppingListCreate",
        tenant_id: uuid.UUID
    ) -> ShoppingList:
        """Create a new shopping list."""
        from app.modules.shared.shopping_list.schemas.shopping_list import ShoppingListCreate

        list_data = list_in.model_dump()
        shopping_list = ShoppingList(
            tenant_id=tenant_id,
            **list_data
        )

        db.add(shopping_list)
        await db.commit()
        await db.refresh(shopping_list)
        return shopping_list

    async def create_shopping_list_item(
        self,
        db: AsyncSession,
        *,
        list_id: uuid.UUID,
        item_data: "ShoppingListItemCreate",
        tenant_id: uuid.UUID
    ) -> ShoppingListItem:
        """Create a new shopping list item."""
        from app.modules.shared.shopping_list.schemas.shopping_list import ShoppingListItemCreate

        # Verify list belongs to tenant
        list_exists = await db.execute(
            select(ShoppingList.id).where(
                and_(
                    ShoppingList.id == list_id,
                    ShoppingList.tenant_id == tenant_id
                )
            )
        )
        if not list_exists.scalar_one_or_none():
            raise ValueError("Shopping list not found")

        item_dict = item_data.model_dump()
        shopping_item = ShoppingListItem(
            shopping_list_id=list_id,
            **item_dict
        )

        db.add(shopping_item)
        await db.commit()
        await db.refresh(shopping_item)
        return shopping_item

    async def get_shopping_list_item_by_id(
        self,
        db: AsyncSession,
        *,
        item_id: uuid.UUID,
        tenant_id: uuid.UUID
    ) -> Optional[ShoppingListItem]:
        """Get a shopping list item by ID."""
        query = select(ShoppingListItem).join(ShoppingList).where(
            and_(
                ShoppingListItem.id == item_id,
                ShoppingList.tenant_id == tenant_id
            )
        )

        result = await db.execute(query)
        return result.scalar_one_or_none()

    async def update_shopping_list_item(
        self,
        db: AsyncSession,
        *,
        item_id: uuid.UUID,
        item_data: "ShoppingListItemUpdate",
        tenant_id: uuid.UUID
    ) -> Optional[ShoppingListItem]:
        """Update a shopping list item."""
        from app.modules.shared.shopping_list.schemas.shopping_list import ShoppingListItemUpdate

        # Get item and verify it belongs to tenant
        item = await self.get_shopping_list_item_by_id(
            db=db, item_id=item_id, tenant_id=tenant_id
        )

        if not item:
            return None

        # Update fields
        update_data = item_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(item, field, value)

        await db.commit()
        await db.refresh(item)
        return item

    async def delete_shopping_list_item(
        self,
        db: AsyncSession,
        *,
        item_id: uuid.UUID,
        tenant_id: uuid.UUID
    ) -> bool:
        """Delete a shopping list item."""
        item = await self.get_shopping_list_item_by_id(
            db=db, item_id=item_id, tenant_id=tenant_id
        )

        if not item:
            return False

        await db.delete(item)
        await db.commit()
        return True


# Service instance
shopping_list_service = ShoppingListService()
