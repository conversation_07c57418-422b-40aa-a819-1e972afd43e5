"""
Media System Models

Modelos SQLAlchemy para o sistema de mídia.
"""

from datetime import datetime
from enum import Enum
from typing import Optional

from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    ForeignKey,
    Integer,
    String,
    Text,
    BigInteger,
    Enum as SQLEnum,
    Index,
    text,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from app.db.base import Base
from app.modules.core.tenants.models.tenant import Tenant


class MediaUploadSource(str, Enum):
    """Origem do upload."""
    DIRECT = "direct"
    FTP = "ftp"


class MediaUploadStatus(str, Enum):
    """Status do upload de arquivo de mídia."""
    PENDING = "pending"
    UPLOADING = "uploading"
    COMPLETED = "completed"
    FAILED = "failed"
    PROCESSING = "processing"
    DELETED = "deleted"


class MediaFileType(str, Enum):
    """Tipo de arquivo de mídia."""
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"
    DOCUMENT = "document"
    ARCHIVE = "archive"
    TEXT = "text"
    OTHER = "other"


class MediaContextType(str, Enum):
    """Tipo de contexto para organização de arquivos."""
    USER = "user"          # Arquivos pessoais do usuário
    TENANT = "tenant"      # Arquivos do tenant/negócio
    MENU_ITEM = "menu_item"  # Fotos de itens do menu
    PROFILE = "profile"    # Fotos de perfil
    FINANCIAL = "financial"  # Documentos financeiros


class MediaContext(Base):
    """Modelo para contextos de mídia."""
    __tablename__ = "media_contexts"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    context_type = Column(String(50), nullable=False)
    context_id = Column(UUID(as_uuid=True), nullable=False)  # user_id ou tenant_id
    
    # Sistema de quota
    quota_limit_mb = Column(Integer, default=2048, nullable=False)  # 2GB padrão
    used_space_mb = Column(Integer, default=0, nullable=False)
    is_quota_enabled = Column(Boolean, default=True, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relacionamentos
    uploads = relationship("MediaUpload", back_populates="context",
                          cascade="all, delete-orphan", lazy="noload")
    directories = relationship("MediaDirectory", back_populates="context", lazy="noload")
    
    # Informações do usuário que fez upload
    uploaded_by = Column(UUID(as_uuid=True), nullable=False)  # user_id
    
    # Rastreamento de origem (FTP vs. Direto)
    upload_source = Column(SQLEnum(MediaUploadSource), 
                         default=MediaUploadSource.DIRECT, nullable=False)
    ftp_user_id = Column(UUID(as_uuid=True), 
                       ForeignKey("media_ftp_users.id"), nullable=True)
    
    # Informações do arquivo
    filename = Column(String(255), nullable=False)

    @property
    def quota_usage_percentage(self) -> float:
        """Calcula a porcentagem de uso da quota."""
        if not self.is_quota_enabled or self.quota_limit_mb == 0:
            return 0.0
        return (self.used_space_mb / self.quota_limit_mb) * 100

    @property
    def available_space_mb(self) -> int:
        """Calcula o espaço disponível em MB."""
        if not self.is_quota_enabled:
            return float('inf')
        return max(0, self.quota_limit_mb - self.used_space_mb)

    @property
    def is_quota_exceeded(self) -> bool:
        """Verifica se a quota foi excedida."""
        if not self.is_quota_enabled:
            return False
        return self.used_space_mb >= self.quota_limit_mb

    def can_upload(self, file_size_mb: int) -> bool:
        """Verifica se pode fazer upload de um arquivo."""
        if not self.is_quota_enabled:
            return True
        return (self.used_space_mb + file_size_mb) <= self.quota_limit_mb

    def get_storage_path(self) -> str:
        """Retorna o caminho de armazenamento baseado no contexto."""
        # Converte para string para comparação, pois pode vir do banco como string
        context_type_str = self.context_type.value if hasattr(self.context_type, 'value') else str(self.context_type)

        if context_type_str == MediaContextType.USER.value:
            return f"/media_data/user/{self.context_id}"
        elif context_type_str == MediaContextType.TENANT.value:
            return f"/media_data/tenant/{self.context_id}"
        elif context_type_str == MediaContextType.MENU_ITEM.value:
            return f"/media_data/menu_item/{self.context_id}"
        elif context_type_str == MediaContextType.PROFILE.value:
            return f"/media_data/profile/{self.context_id}"
        elif context_type_str == MediaContextType.FINANCIAL.value:
            return f"/media_data/financial/{self.context_id}"
        else:
            raise ValueError(f"Contexto inválido: {self.context_type}")

    # Índice único para context_type + context_id
    __table_args__ = (
        Index('uq_media_context_type_id', 'context_type', 'context_id', unique=True),
    )


class MediaDirectory(Base):
    """Modelo para diretórios de mídia."""
    __tablename__ = "media_directories"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    context_id = Column(UUID(as_uuid=True), 
                       ForeignKey("media_contexts.id", ondelete="CASCADE"),
                       nullable=False)
    name = Column(String(100), nullable=False)
    path = Column(String(500), nullable=False)
    parent_id = Column(UUID(as_uuid=True), ForeignKey("media_directories.id"),
                      nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relacionamentos
    context = relationship("MediaContext", back_populates="directories", lazy="noload")
    parent = relationship("MediaDirectory", remote_side=[id],
                         back_populates="children", lazy="noload")
    children = relationship("MediaDirectory", back_populates="parent",
                           cascade="all, delete-orphan", lazy="noload")
    uploads = relationship("MediaUpload", back_populates="directory", lazy="noload")


class MediaUpload(Base):
    """Modelo para uploads de mídia."""
    __tablename__ = "media_uploads"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    context_id = Column(UUID(as_uuid=True),
                       ForeignKey("media_contexts.id", ondelete="CASCADE"),
                       nullable=False)
    directory_id = Column(UUID(as_uuid=True),
                         ForeignKey("media_directories.id"), nullable=True)
    
    # Informações do usuário que fez upload
    uploaded_by = Column(UUID(as_uuid=True), nullable=False)  # user_id
    
    # Rastreamento de origem (FTP vs. Direto)
    upload_source = Column(SQLEnum(MediaUploadSource), 
                         default=MediaUploadSource.DIRECT, nullable=False)
    ftp_user_id = Column(UUID(as_uuid=True), 
                       ForeignKey("media_ftp_users.id"), nullable=True)

    # Informações do arquivo
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(BigInteger, nullable=False)
    mime_type = Column(String(100), nullable=False)
    media_type = Column(SQLEnum(MediaFileType), nullable=False)
    upload_status = Column(SQLEnum(MediaUploadStatus), default=MediaUploadStatus.PENDING)
    
    # Processamento
    thumbnail_path = Column(String(500), nullable=True)
    compressed_path = Column(String(500), nullable=True)
    file_metadata = Column(Text, nullable=True)  # JSON metadata
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relacionamentos
    context = relationship("MediaContext", back_populates="uploads", lazy="noload")
    directory = relationship("MediaDirectory", back_populates="uploads", lazy="noload")
    ftp_user = relationship("MediaFTPUser", back_populates="uploads", lazy="noload")
    menu_item_media = relationship("MediaMenuItemMedia", back_populates="upload",
                                  cascade="all, delete-orphan", lazy="noload")


class MediaMenuItemMedia(Base):
    """Modelo para associar uploads de mídia a itens de menu."""
    __tablename__ = "media_menu_item_media"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    menu_item_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    media_upload_id = Column(UUID(as_uuid=True),
                            ForeignKey("media_uploads.id", ondelete="CASCADE"),
                            nullable=False, index=True)
    display_order = Column(Integer, default=0, nullable=False, index=True)
    is_primary = Column(Boolean, default=False, nullable=False, index=True)
    alt_text = Column(String(255), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relacionamentos
    upload = relationship("MediaUpload", back_populates="menu_item_media", lazy="noload")

    # Constraint para garantir apenas uma imagem principal por produto
    __table_args__ = (
        Index('uq_menu_item_primary_media_v2', 'menu_item_id', 'is_primary',
              unique=True, postgresql_where=text('is_primary = true')),
    )


class MediaFTPUser(Base):
    """Modelo para usuários FTP dentro do sistema de mídia."""
    __tablename__ = "media_ftp_users"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"),
                      nullable=False)
    username = Column(String(50), nullable=False, unique=True)
    password_hash = Column(String(255), nullable=False)
    home_directory = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    folder_uuid = Column(UUID(as_uuid=True), unique=True, nullable=False,
                        default=uuid.uuid4)
    
    quota_limit_mb = Column(Integer, default=1024, nullable=False)
    used_space_mb = Column(Integer, default=0, nullable=False)
    is_quota_enabled = Column(Boolean, default=True, nullable=False)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_login = Column(DateTime(timezone=True), nullable=True)

    # Relacionamentos
    tenant = relationship("Tenant", back_populates="media_ftp_users", lazy="noload")
    uploads = relationship("MediaUpload", back_populates="ftp_user",
                          cascade="all, delete-orphan", lazy="noload")


# Adicionar o relacionamento de volta no modelo Tenant
Tenant.media_ftp_users = relationship("MediaFTPUser", back_populates="tenant", cascade="all, delete-orphan", lazy="noload")

# TESTE DE EDIÇÃO
