from typing import Optional, Dict, Any, List
import uuid
from datetime import datetime, date, time
from pydantic import BaseModel, ConfigDict

from app.modules.shared.hr.core.models.work_schedule import (
    Day,
    ScheduleType,
    ScheduleRecurrence,
    LeaveType,
    LeaveRequestStatus,
)

# Schedule Template Schemas


class ScheduleTemplateEntryBase(BaseModel):
    """Base schema for ScheduleTemplateEntry."""

    day_of_week: Day
    start_time: time
    end_time: time
    break_duration_minutes: int = 0
    is_day_off: bool = False


class ScheduleTemplateEntryCreate(ScheduleTemplateEntryBase):
    """Schema for creating a ScheduleTemplateEntry."""


class ScheduleTemplateEntryUpdate(BaseModel):
    """Schema for updating a ScheduleTemplateEntry."""

    day_of_week: Optional[Day] = None
    start_time: Optional[time] = None
    end_time: Optional[time] = None
    break_duration_minutes: Optional[int] = None
    is_day_off: Optional[bool] = None


class ScheduleTemplateEntryRead(ScheduleTemplateEntryBase):
    """Schema for reading a ScheduleTemplateEntry."""

    id: uuid.UUID
    template_id: uuid.UUID
    tenant_id: uuid.UUID

    model_config = ConfigDict(from_attributes=True)


class ScheduleTemplateBase(BaseModel):
    """Base schema for ScheduleTemplate."""

    name: str
    description: Optional[str] = None
    schedule_type: ScheduleType = ScheduleType.REGULAR
    recurrence: ScheduleRecurrence = ScheduleRecurrence.WEEKLY
    default_start_time: Optional[time] = None
    default_end_time: Optional[time] = None
    active_days: Optional[List[str]] = None
    is_active: bool = True


class ScheduleTemplateCreate(ScheduleTemplateBase):
    """Schema for creating a ScheduleTemplate."""

    entries: Optional[List[ScheduleTemplateEntryCreate]] = None


class ScheduleTemplateUpdate(BaseModel):
    """Schema for updating a ScheduleTemplate."""

    name: Optional[str] = None
    description: Optional[str] = None
    schedule_type: Optional[ScheduleType] = None
    recurrence: Optional[ScheduleRecurrence] = None
    default_start_time: Optional[time] = None
    default_end_time: Optional[time] = None
    active_days: Optional[List[str]] = None
    is_active: Optional[bool] = None


class ScheduleTemplateRead(ScheduleTemplateBase):
    """Schema for reading a ScheduleTemplate."""

    id: uuid.UUID
    tenant_id: uuid.UUID

    model_config = ConfigDict(from_attributes=True)


class ScheduleTemplateWithEntries(ScheduleTemplateRead):
    """Schema for ScheduleTemplate with entries."""

    entries: List[ScheduleTemplateEntryRead] = []

    model_config = ConfigDict(from_attributes=True)


# Work Schedule Schemas


class WorkShiftBase(BaseModel):
    """Base schema for WorkShift."""

    date: date
    start_time: time
    end_time: time
    break_duration_minutes: int = 0
    notes: Optional[str] = None


class WorkShiftCreate(WorkShiftBase):
    """Schema for creating a WorkShift."""


class WorkShiftUpdate(BaseModel):
    """Schema for updating a WorkShift."""

    date: Optional[date] = None
    start_time: Optional[time] = None
    end_time: Optional[time] = None
    break_duration_minutes: Optional[int] = None
    is_completed: Optional[bool] = None
    notes: Optional[str] = None


class WorkShiftRead(WorkShiftBase):
    """Schema for reading a WorkShift."""

    id: uuid.UUID
    schedule_id: uuid.UUID
    tenant_id: uuid.UUID
    is_completed: bool
    is_modified: bool

    model_config = ConfigDict(from_attributes=True)


class WorkScheduleBase(BaseModel):
    """Base schema for WorkSchedule."""

    name: Optional[str] = None
    start_date: date
    end_date: Optional[date] = None
    is_active: bool = True
    metadata: Optional[Dict[str, Any]] = None


class WorkScheduleCreate(WorkScheduleBase):
    """Schema for creating a WorkSchedule."""

    employee_id: uuid.UUID
    template_id: Optional[uuid.UUID] = None
    shifts: Optional[List[WorkShiftCreate]] = None


class WorkScheduleUpdate(BaseModel):
    """Schema for updating a WorkSchedule."""

    name: Optional[str] = None
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    is_active: Optional[bool] = None
    is_approved: Optional[bool] = None
    approved_by: Optional[uuid.UUID] = None
    metadata: Optional[Dict[str, Any]] = None


class WorkScheduleRead(WorkScheduleBase):
    """Schema for reading a WorkSchedule."""

    id: uuid.UUID
    employee_id: uuid.UUID
    template_id: Optional[uuid.UUID] = None
    tenant_id: uuid.UUID
    is_approved: bool
    approved_by: Optional[uuid.UUID] = None
    approval_date: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


class WorkScheduleWithShifts(WorkScheduleRead):
    """Schema for WorkSchedule with shifts."""

    shifts: List[WorkShiftRead] = []
    template: Optional[ScheduleTemplateRead] = None

    model_config = ConfigDict(from_attributes=True)


# Leave Request Schemas


class LeaveRequestBase(BaseModel):
    """Base schema for LeaveRequest."""

    leave_type: LeaveType
    start_date: date
    end_date: date
    duration_days: float
    reason: Optional[str] = None


class LeaveRequestCreate(LeaveRequestBase):
    """Schema for creating a LeaveRequest."""

    employee_id: uuid.UUID


class LeaveRequestUpdate(BaseModel):
    """Schema for updating a LeaveRequest."""

    leave_type: Optional[LeaveType] = None
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    duration_days: Optional[float] = None
    reason: Optional[str] = None
    status: Optional[LeaveRequestStatus] = None
    review_notes: Optional[str] = None


class LeaveRequestRead(LeaveRequestBase):
    """Schema for reading a LeaveRequest."""

    id: uuid.UUID
    employee_id: uuid.UUID
    tenant_id: uuid.UUID
    request_date: datetime
    status: LeaveRequestStatus
    reviewed_by: Optional[uuid.UUID] = None
    review_date: Optional[datetime] = None
    review_notes: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


# Specialized Scheduling Input/Output Schemas


class GenerateScheduleRequest(BaseModel):
    """Schema for generating a schedule."""

    employee_id: uuid.UUID
    template_id: uuid.UUID
    start_date: date
    end_date: Optional[date] = None
    exclude_dates: Optional[List[date]] = None


class LeaveBalanceResponse(BaseModel):
    """Schema for leave balance response."""

    employee_id: uuid.UUID
    employee_name: str
    vacation_days_available: float = 0.0
    sick_days_available: float = 0.0
    personal_days_available: float = 0.0
    leave_balances: Dict[str, float] = {}

    model_config = ConfigDict(from_attributes=True)
