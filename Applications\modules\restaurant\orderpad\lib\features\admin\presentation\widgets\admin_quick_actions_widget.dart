import 'package:flutter/material.dart';

class AdminQuickActionsWidget extends StatelessWidget {
  final List<AdminQuickAction> actions;
  final int crossAxisCount;
  final double crossAxisSpacing;
  final double mainAxisSpacing;

  const AdminQuickActionsWidget({
    super.key,
    required this.actions,
    this.crossAxisCount = 4,
    this.crossAxisSpacing = 16,
    this.mainAxisSpacing = 16,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.flash_on,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Ações Rápidas',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: crossAxisCount,
                crossAxisSpacing: crossAxisSpacing,
                mainAxisSpacing: mainAxisSpacing,
                childAspectRatio: 1.0,
              ),
              itemCount: actions.length,
              itemBuilder: (context, index) {
                return AdminQuickActionCard(action: actions[index]);
              },
            ),
          ],
        ),
      ),
    );
  }
}

class AdminQuickActionCard extends StatefulWidget {
  final AdminQuickAction action;

  const AdminQuickActionCard({
    super.key,
    required this.action,
  });

  @override
  State<AdminQuickActionCard> createState() => _AdminQuickActionCardState();
}

class _AdminQuickActionCardState extends State<AdminQuickActionCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Card(
            elevation: widget.action.enabled ? 2 : 1,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: InkWell(
              onTap: widget.action.enabled ? () {
                _animationController.forward().then((_) {
                  _animationController.reverse();
                });
                widget.action.onTap?.call();
              } : null,
              borderRadius: BorderRadius.circular(12),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  gradient: widget.action.enabled
                      ? LinearGradient(
                          colors: [
                            widget.action.color.withValues(alpha: 0.1),
                            widget.action.color.withValues(alpha: 0.05),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        )
                      : null,
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Stack(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: widget.action.enabled
                                ? widget.action.color.withValues(alpha: 0.2)
                                : theme.colorScheme.onSurface.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            widget.action.icon,
                            color: widget.action.enabled
                                ? widget.action.color
                                : theme.colorScheme.onSurface.withValues(alpha: 0.5),
                            size: 24,
                          ),
                        ),
                        
                        if (widget.action.badge != null)
                          Positioned(
                            right: 0,
                            top: 0,
                            child: Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: Colors.red,
                                borderRadius: BorderRadius.circular(10),
                              ),
                              constraints: const BoxConstraints(
                                minWidth: 20,
                                minHeight: 20,
                              ),
                              child: Text(
                                widget.action.badge!,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 10,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                      ],
                    ),
                    
                    const SizedBox(height: 12),
                    
                    Text(
                      widget.action.title,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: widget.action.enabled
                            ? theme.colorScheme.onSurface
                            : theme.colorScheme.onSurface.withValues(alpha: 0.5),
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    if (widget.action.subtitle != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        widget.action.subtitle!,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: widget.action.enabled
                              ? theme.colorScheme.onSurface.withValues(alpha: 0.7)
                              : theme.colorScheme.onSurface.withValues(alpha: 0.4),
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class AdminQuickAction {
  final String title;
  final String? subtitle;
  final IconData icon;
  final Color color;
  final VoidCallback? onTap;
  final String? badge;
  final bool enabled;

  const AdminQuickAction({
    required this.title,
    this.subtitle,
    required this.icon,
    required this.color,
    this.onTap,
    this.badge,
    this.enabled = true,
  });

  // Predefined actions for admin dashboard
  static AdminQuickAction viewReports({
    VoidCallback? onTap,
    String? badge,
  }) {
    return AdminQuickAction(
      title: 'Relatórios',
      subtitle: 'Vendas e Analytics',
      icon: Icons.analytics,
      color: Colors.blue,
      onTap: onTap,
      badge: badge,
    );
  }

  static AdminQuickAction manageMenu({
    VoidCallback? onTap,
    String? badge,
  }) {
    return AdminQuickAction(
      title: 'Gerenciar Menu',
      subtitle: 'Itens e Preços',
      icon: Icons.restaurant_menu,
      color: Colors.orange,
      onTap: onTap,
      badge: badge,
    );
  }

  static AdminQuickAction manageStaff({
    VoidCallback? onTap,
    String? badge,
  }) {
    return AdminQuickAction(
      title: 'Funcionários',
      subtitle: 'Equipe e Horários',
      icon: Icons.people,
      color: Colors.green,
      onTap: onTap,
      badge: badge,
    );
  }

  static AdminQuickAction manageTables({
    VoidCallback? onTap,
    String? badge,
  }) {
    return AdminQuickAction(
      title: 'Mesas',
      subtitle: 'Layout e Status',
      icon: Icons.table_restaurant,
      color: Colors.purple,
      onTap: onTap,
      badge: badge,
    );
  }

  static AdminQuickAction viewOrders({
    VoidCallback? onTap,
    String? badge,
  }) {
    return AdminQuickAction(
      title: 'Pedidos',
      subtitle: 'Monitorar Status',
      icon: Icons.receipt_long,
      color: Colors.teal,
      onTap: onTap,
      badge: badge,
    );
  }

  static AdminQuickAction settings({
    VoidCallback? onTap,
    String? badge,
  }) {
    return AdminQuickAction(
      title: 'Configurações',
      subtitle: 'Sistema e Perfil',
      icon: Icons.settings,
      color: Colors.grey,
      onTap: onTap,
      badge: badge,
    );
  }

  static AdminQuickAction inventory({
    VoidCallback? onTap,
    String? badge,
  }) {
    return AdminQuickAction(
      title: 'Estoque',
      subtitle: 'Ingredientes',
      icon: Icons.inventory,
      color: Colors.brown,
      onTap: onTap,
      badge: badge,
    );
  }

  static AdminQuickAction promotions({
    VoidCallback? onTap,
    String? badge,
  }) {
    return AdminQuickAction(
      title: 'Promoções',
      subtitle: 'Ofertas e Descontos',
      icon: Icons.local_offer,
      color: Colors.red,
      onTap: onTap,
      badge: badge,
    );
  }

  static AdminQuickAction backup({
    VoidCallback? onTap,
    String? badge,
  }) {
    return AdminQuickAction(
      title: 'Backup',
      subtitle: 'Dados e Segurança',
      icon: Icons.backup,
      color: Colors.indigo,
      onTap: onTap,
      badge: badge,
    );
  }

  static AdminQuickAction notifications({
    VoidCallback? onTap,
    String? badge,
  }) {
    return AdminQuickAction(
      title: 'Notificações',
      subtitle: 'Alertas e Avisos',
      icon: Icons.notifications,
      color: Colors.amber,
      onTap: onTap,
      badge: badge,
    );
  }

  static AdminQuickAction customerFeedback({
    VoidCallback? onTap,
    String? badge,
  }) {
    return AdminQuickAction(
      title: 'Feedback',
      subtitle: 'Avaliações',
      icon: Icons.star_rate,
      color: Colors.yellow,
      onTap: onTap,
      badge: badge,
    );
  }

  static AdminQuickAction financialReports({
    VoidCallback? onTap,
    String? badge,
  }) {
    return AdminQuickAction(
      title: 'Financeiro',
      subtitle: 'Receitas e Custos',
      icon: Icons.account_balance,
      color: Colors.green[700]!,
      onTap: onTap,
      badge: badge,
    );
  }
}

class CompactQuickActionsWidget extends StatelessWidget {
  final List<AdminQuickAction> actions;
  final int maxActions;
  final VoidCallback? onViewAll;

  const CompactQuickActionsWidget({
    super.key,
    required this.actions,
    this.maxActions = 6,
    this.onViewAll,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final displayActions = actions.take(maxActions).toList();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Ações Rápidas',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            if (onViewAll != null)
              TextButton(
                onPressed: onViewAll,
                child: const Text('Ver Todas'),
              ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.0,
          ),
          itemCount: displayActions.length,
          itemBuilder: (context, index) {
            final action = displayActions[index];
            return Card(
              elevation: 1,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              child: InkWell(
                onTap: action.onTap,
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Stack(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: action.color.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              action.icon,
                              color: action.color,
                              size: 20,
                            ),
                          ),
                          
                          if (action.badge != null)
                            Positioned(
                              right: 0,
                              top: 0,
                              child: Container(
                                padding: const EdgeInsets.all(2),
                                decoration: BoxDecoration(
                                  color: Colors.red,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                constraints: const BoxConstraints(
                                  minWidth: 16,
                                  minHeight: 16,
                                ),
                                child: Text(
                                  action.badge!,
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 8,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                        ],
                      ),
                      
                      const SizedBox(height: 8),
                      
                      Text(
                        action.title,
                        style: theme.textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}