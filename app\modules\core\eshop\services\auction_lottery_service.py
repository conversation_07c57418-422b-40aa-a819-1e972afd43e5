"""
Auction and Lottery Service
===========================

Business logic for auction and lottery operations.
"""

import random
import string
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Optional, List, Tuple
from uuid import UUID

from sqlalchemy import and_, or_, desc, func
from sqlalchemy.orm import Session, joinedload
from sqlalchemy.exc import IntegrityError

from app.modules.core.eshop.models.auction_lottery import (
    Auction, AuctionBid, Lottery, LotteryTicket,
    AuctionStatus, LotteryStatus
)
from app.modules.core.eshop.models.product import Product
from app.modules.core.users.models.user import User
from app.modules.core.eshop.schemas.auction_lottery_schemas import (
    AuctionCreate, AuctionUpdate, BidCreate,
    LotteryCreate, LotteryUpdate, TicketCreate
)
from app.core.exceptions import ValidationError, NotFoundError, ForbiddenError


class AuctionService:
    """Service for auction operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_auction(self, auction_data: AuctionCreate, vendor_id: UUID, tenant_id: UUID) -> Auction:
        """Create a new auction."""
        # Validate product exists and belongs to vendor
        product = self.db.query(Product).filter(
            Product.id == auction_data.product_id,
            Product.tenant_id == tenant_id
        ).first()
        
        if not product:
            raise NotFoundError("Product not found")
        
        # Check if product already has an active auction
        existing_auction = self.db.query(Auction).filter(
            Auction.product_id == auction_data.product_id,
            Auction.status.in_([AuctionStatus.DRAFT, AuctionStatus.ACTIVE])
        ).first()
        
        if existing_auction:
            raise ValidationError("Product already has an active auction")
        
        # Create auction
        auction = Auction(
            tenant_id=tenant_id,
            product_id=auction_data.product_id,
            vendor_id=vendor_id,
            title=auction_data.title,
            description=auction_data.description,
            starting_bid=auction_data.starting_bid,
            buy_now_price=auction_data.buy_now_price,
            bid_increment=auction_data.bid_increment,
            start_time=auction_data.start_time,
            end_time=auction_data.end_time,
            auto_extend=auction_data.auto_extend,
            extend_minutes=auction_data.extend_minutes,
            max_extensions=auction_data.max_extensions,
            status=AuctionStatus.DRAFT
        )
        
        self.db.add(auction)
        self.db.commit()
        self.db.refresh(auction)
        
        return auction
    
    def update_auction(self, auction_id: UUID, auction_data: AuctionUpdate, vendor_id: UUID) -> Auction:
        """Update an existing auction."""
        auction = self.db.query(Auction).filter(
            Auction.id == auction_id,
            Auction.vendor_id == vendor_id
        ).first()
        
        if not auction:
            raise NotFoundError("Auction not found")
        
        if auction.status not in [AuctionStatus.DRAFT, AuctionStatus.ACTIVE]:
            raise ValidationError("Cannot update ended or cancelled auction")
        
        # Update fields
        for field, value in auction_data.dict(exclude_unset=True).items():
            setattr(auction, field, value)
        
        self.db.commit()
        self.db.refresh(auction)
        
        return auction
    
    def place_bid(self, bid_data: BidCreate, bidder_id: UUID) -> Tuple[AuctionBid, bool]:
        """Place a bid on an auction. Returns (bid, is_extension_triggered)."""
        auction = self.db.query(Auction).filter(
            Auction.id == bid_data.auction_id,
            Auction.status == AuctionStatus.ACTIVE
        ).first()
        
        if not auction:
            raise NotFoundError("Active auction not found")
        
        # Check if auction has ended
        if datetime.utcnow() >= auction.end_time:
            raise ValidationError("Auction has ended")
        
        # Validate bid amount
        minimum_bid = auction.current_bid or auction.starting_bid
        if bid_data.bid_amount < minimum_bid + auction.bid_increment:
            raise ValidationError(f"Bid must be at least {minimum_bid + auction.bid_increment}")
        
        # Check if bidder is not the vendor
        if bidder_id == auction.vendor_id:
            raise ValidationError("Vendor cannot bid on their own auction")
        
        # Mark previous winning bid as not winning
        self.db.query(AuctionBid).filter(
            AuctionBid.auction_id == bid_data.auction_id,
            AuctionBid.is_winning == True
        ).update({"is_winning": False})
        
        # Create new bid
        bid = AuctionBid(
            auction_id=bid_data.auction_id,
            bidder_id=bidder_id,
            bid_amount=bid_data.bid_amount,
            is_winning=True
        )
        
        self.db.add(bid)
        
        # Update auction
        auction.current_bid = bid_data.bid_amount
        auction.winning_bid = bid_data.bid_amount
        auction.winner_id = bidder_id
        auction.total_bids += 1
        
        # Update unique bidders count
        unique_bidders = self.db.query(func.count(func.distinct(AuctionBid.bidder_id))).filter(
            AuctionBid.auction_id == bid_data.auction_id
        ).scalar()
        auction.unique_bidders = unique_bidders
        
        # Check for auto-extension
        is_extension_triggered = False
        if auction.auto_extend and auction.extensions_used < auction.max_extensions:
            time_remaining = (auction.end_time - datetime.utcnow()).total_seconds()
            if time_remaining <= auction.extend_minutes * 60:
                auction.end_time += timedelta(minutes=auction.extend_minutes)
                auction.extensions_used += 1
                is_extension_triggered = True
        
        self.db.commit()
        self.db.refresh(bid)
        
        return bid, is_extension_triggered
    
    def buy_now(self, auction_id: UUID, buyer_id: UUID) -> Auction:
        """Execute buy-now purchase."""
        auction = self.db.query(Auction).filter(
            Auction.id == auction_id,
            Auction.status == AuctionStatus.ACTIVE
        ).first()
        
        if not auction:
            raise NotFoundError("Active auction not found")
        
        if not auction.buy_now_price:
            raise ValidationError("Buy-now option not available")
        
        if buyer_id == auction.vendor_id:
            raise ValidationError("Vendor cannot buy their own auction")
        
        # End auction immediately
        auction.status = AuctionStatus.ENDED
        auction.winner_id = buyer_id
        auction.winning_bid = auction.buy_now_price
        auction.end_time = datetime.utcnow()
        
        self.db.commit()
        self.db.refresh(auction)
        
        return auction
    
    def activate_auction(self, auction_id: UUID, vendor_id: UUID) -> Auction:
        """Activate a draft auction."""
        auction = self.db.query(Auction).filter(
            Auction.id == auction_id,
            Auction.vendor_id == vendor_id,
            Auction.status == AuctionStatus.DRAFT
        ).first()
        
        if not auction:
            raise NotFoundError("Draft auction not found")
        
        auction.status = AuctionStatus.ACTIVE
        self.db.commit()
        self.db.refresh(auction)
        
        return auction
    
    def cancel_auction(self, auction_id: UUID, vendor_id: UUID) -> Auction:
        """Cancel an auction."""
        auction = self.db.query(Auction).filter(
            Auction.id == auction_id,
            Auction.vendor_id == vendor_id
        ).first()
        
        if not auction:
            raise NotFoundError("Auction not found")
        
        if auction.status not in [AuctionStatus.DRAFT, AuctionStatus.ACTIVE]:
            raise ValidationError("Cannot cancel ended auction")
        
        auction.status = AuctionStatus.CANCELLED
        self.db.commit()
        self.db.refresh(auction)
        
        return auction
    
    def finalize_expired_auctions(self) -> List[Auction]:
        """Finalize all expired auctions."""
        expired_auctions = self.db.query(Auction).filter(
            Auction.status == AuctionStatus.ACTIVE,
            Auction.end_time <= datetime.utcnow()
        ).all()
        
        for auction in expired_auctions:
            auction.status = AuctionStatus.ENDED
        
        self.db.commit()
        
        return expired_auctions
    
    def get_auction_bids(self, auction_id: UUID, page: int = 1, size: int = 50) -> Tuple[List[AuctionBid], int]:
        """Get bids for an auction with pagination."""
        query = self.db.query(AuctionBid).filter(
            AuctionBid.auction_id == auction_id
        ).order_by(desc(AuctionBid.created_at))
        
        total = query.count()
        bids = query.offset((page - 1) * size).limit(size).all()
        
        return bids, total


class LotteryService:
    """Service for lottery operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_lottery(self, lottery_data: LotteryCreate, vendor_id: UUID, tenant_id: UUID) -> Lottery:
        """Create a new lottery."""
        # Validate product exists
        product = self.db.query(Product).filter(
            Product.id == lottery_data.product_id,
            Product.tenant_id == tenant_id
        ).first()
        
        if not product:
            raise NotFoundError("Product not found")
        
        # Check if product already has an active lottery
        existing_lottery = self.db.query(Lottery).filter(
            Lottery.product_id == lottery_data.product_id,
            Lottery.status.in_([LotteryStatus.DRAFT, LotteryStatus.ACTIVE])
        ).first()
        
        if existing_lottery:
            raise ValidationError("Product already has an active lottery")
        
        # Create lottery
        lottery = Lottery(
            tenant_id=tenant_id,
            product_id=lottery_data.product_id,
            vendor_id=vendor_id,
            title=lottery_data.title,
            description=lottery_data.description,
            is_free=lottery_data.is_free,
            ticket_price=lottery_data.ticket_price,
            max_tickets_per_user=lottery_data.max_tickets_per_user,
            total_tickets_available=lottery_data.total_tickets_available,
            start_time=lottery_data.start_time,
            end_time=lottery_data.end_time,
            draw_time=lottery_data.draw_time,
            status=LotteryStatus.DRAFT
        )
        
        self.db.add(lottery)
        self.db.commit()
        self.db.refresh(lottery)
        
        return lottery
    
    def buy_tickets(self, ticket_data: TicketCreate, participant_id: UUID) -> List[LotteryTicket]:
        """Buy lottery tickets."""
        lottery = self.db.query(Lottery).filter(
            Lottery.id == ticket_data.lottery_id,
            Lottery.status == LotteryStatus.ACTIVE
        ).first()
        
        if not lottery:
            raise NotFoundError("Active lottery not found")
        
        # Check if lottery is still accepting tickets
        if datetime.utcnow() >= lottery.end_time:
            raise ValidationError("Lottery ticket sales have ended")
        
        # Check user ticket limit
        if lottery.max_tickets_per_user:
            user_tickets = self.db.query(func.count(LotteryTicket.id)).filter(
                LotteryTicket.lottery_id == ticket_data.lottery_id,
                LotteryTicket.participant_id == participant_id
            ).scalar()
            
            if user_tickets + ticket_data.quantity > lottery.max_tickets_per_user:
                raise ValidationError(f"Cannot exceed {lottery.max_tickets_per_user} tickets per user")
        
        # Check total ticket limit
        if lottery.total_tickets_available:
            if lottery.tickets_sold + ticket_data.quantity > lottery.total_tickets_available:
                raise ValidationError("Not enough tickets available")
        
        # Generate tickets
        tickets = []
        for _ in range(ticket_data.quantity):
            ticket_number = self._generate_ticket_number()
            
            ticket = LotteryTicket(
                lottery_id=ticket_data.lottery_id,
                participant_id=participant_id,
                ticket_number=ticket_number,
                is_paid=True,
                payment_amount=lottery.ticket_price if not lottery.is_free else None
            )
            
            tickets.append(ticket)
            self.db.add(ticket)
        
        # Update lottery
        lottery.tickets_sold += ticket_data.quantity
        
        self.db.commit()
        
        for ticket in tickets:
            self.db.refresh(ticket)
        
        return tickets
    
    def draw_winner(self, lottery_id: UUID) -> Tuple[Lottery, LotteryTicket]:
        """Draw a random winner for the lottery."""
        lottery = self.db.query(Lottery).filter(
            Lottery.id == lottery_id,
            Lottery.status == LotteryStatus.ACTIVE
        ).first()
        
        if not lottery:
            raise NotFoundError("Active lottery not found")
        
        if lottery.tickets_sold == 0:
            raise ValidationError("No tickets sold for this lottery")
        
        # Get all tickets
        tickets = self.db.query(LotteryTicket).filter(
            LotteryTicket.lottery_id == lottery_id,
            LotteryTicket.is_paid == True
        ).all()
        
        if not tickets:
            raise ValidationError("No valid tickets found")
        
        # Select random winner
        winning_ticket = random.choice(tickets)
        winning_ticket.is_winning = True
        
        # Update lottery
        lottery.status = LotteryStatus.DRAWN
        lottery.winner_id = winning_ticket.participant_id
        lottery.winning_ticket_id = winning_ticket.id
        lottery.drawn_at = datetime.utcnow()
        
        self.db.commit()
        self.db.refresh(lottery)
        self.db.refresh(winning_ticket)
        
        return lottery, winning_ticket
    
    def _generate_ticket_number(self) -> str:
        """Generate a unique ticket number."""
        return ''.join(random.choices(string.ascii_uppercase + string.digits, k=10))
