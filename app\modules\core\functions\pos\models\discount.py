"""Discount models for POS module."""

import uuid  # noqa: E402
import enum
from sqlalchemy import (
    Column,
    String,
    ForeignKey,
    Numeric,
    Boolean,
    Enum,
    JSON,
    DateTime,
    Integer,
)
from sqlalchemy.dialects.postgresql import UUID  # noqa: E402
from sqlalchemy.orm import relationship
from datetime import datetime

from app.db.base import Base  # noqa: E402


class DiscountType(str, enum.Enum):
    """Enum for discount types."""

    PERCENTAGE = "percentage"
    FIXED_AMOUNT = "fixed_amount"
    BUY_X_GET_Y = "buy_x_get_y"
    BUNDLE = "bundle"
    COUPON = "coupon"
    LOYALTY = "loyalty"
    MANUAL = "manual"
    OTHER = "other"


class DiscountScope(str, enum.Enum):
    """Enum for discount scopes."""

    ITEM = "item"
    TRANSACTION = "transaction"
    CATEGORY = "category"


class Discount(Base):
    """
    Model for discounts that can be applied to transactions or items.

    This represents a discount rule that can be applied in the POS.
    """

    __tablename__ = "discounts"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)

    name = Column(String, nullable=False)
    description = Column(String, nullable=True)
    discount_type = Column(Enum(DiscountType), nullable=False)
    scope = Column(Enum(DiscountScope), nullable=False)

    # Discount value
    percentage = Column(Numeric(5, 2), nullable=True)  # For percentage discounts
    amount = Column(Numeric(10, 2), nullable=True)  # For fixed amount discounts

    # Validity
    is_active = Column(Boolean, default=True)
    start_date = Column(DateTime, nullable=True)
    end_date = Column(DateTime, nullable=True)

    # Conditions
    min_purchase_amount = Column(Numeric(10, 2), nullable=True)
    max_discount_amount = Column(Numeric(10, 2), nullable=True)
    applicable_products = Column(JSON, nullable=True)  # List of product IDs or categories
    coupon_code = Column(String, nullable=True, index=True)

    # Usage limits
    max_uses = Column(Integer, nullable=True)
    current_uses = Column(Integer, default=0)

    # Relationships
    tenant = relationship("Tenant")

    def __repr__(self):
        return f"<Discount(id={self.id}, name='{self.name}', type='{self.discount_type}')>"


class AppliedDiscount(Base):
    """
    Model for tracking discounts applied to transactions or items.

    This represents an instance of a discount being applied.
    """

    __tablename__ = "applied_discounts"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    discount_id = Column(
        UUID(as_uuid=True), ForeignKey("discounts.id"), nullable=True
    )  # Null for manual discounts

    # What the discount was applied to
    transaction_id = Column(UUID(as_uuid=True), ForeignKey("sale_transactions.id"), nullable=True)
    sale_item_id = Column(UUID(as_uuid=True), ForeignKey("sale_items.id"), nullable=True)

    # Discount details
    discount_type = Column(Enum(DiscountType), nullable=False)
    percentage = Column(Numeric(5, 2), nullable=True)
    amount = Column(Numeric(10, 2), nullable=False)  # Actual amount discounted
    reason = Column(String, nullable=True)  # For manual discounts

    # Who applied it
    applied_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    applied_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    # Relationships
    tenant = relationship("Tenant")
    discount = relationship("Discount", foreign_keys=[discount_id])
    transaction = relationship("SaleTransaction", foreign_keys=[transaction_id])
    sale_item = relationship("SaleItem", foreign_keys=[sale_item_id])
    user = relationship("User", foreign_keys=[applied_by])

    def __repr__(self):
        target = (
            f"transaction={self.transaction_id}"
            if self.transaction_id
            else f"item={self.sale_item_id}"
        )
        return f"<AppliedDiscount(id={self.id}, {target}, amount={self.amount})>"
