import 'package:flutter/material.dart';
import 'package:timeago/timeago.dart' as timeago;

class RecentActivityWidget extends StatelessWidget {
  final List<ActivityItem> activities;
  final bool isLoading;
  final VoidCallback? onRefresh;
  final VoidCallback? onViewAll;

  const RecentActivityWidget({
    super.key,
    required this.activities,
    this.isLoading = false,
    this.onRefresh,
    this.onViewAll,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.history,
                  color: theme.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Atividades Recentes',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                
                if (onRefresh != null)
                  IconButton(
                    onPressed: onRefresh,
                    icon: const Icon(Icons.refresh),
                    tooltip: 'Atualizar',
                  ),
                
                if (onViewAll != null)
                  TextButton(
                    onPressed: onViewAll,
                    child: const Text('Ver Todas'),
                  ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            if (isLoading)
              _buildLoadingState()
            else if (activities.isEmpty)
              _buildEmptyState(theme)
            else
              _buildActivityList(theme),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Column(
      children: List.generate(5, (index) => _buildLoadingItem()),
    );
  }

  Widget _buildLoadingItem() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(20),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: double.infinity,
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  width: 120,
                  height: 12,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(
            Icons.history_outlined,
            size: 64,
            color: theme.colorScheme.onSurface.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'Nenhuma atividade recente',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'As atividades aparecerão aqui conforme acontecem',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildActivityList(ThemeData theme) {
    return Column(
      children: activities.take(10).map((activity) {
        return _buildActivityItem(activity, theme);
      }).toList(),
    );
  }

  Widget _buildActivityItem(ActivityItem activity, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: activity.color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              activity.icon,
              color: activity.color,
              size: 20,
            ),
          ),
          
          const SizedBox(width: 12),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  activity.title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                
                if (activity.subtitle != null) ..[
                  const SizedBox(height: 2),
                  Text(
                    activity.subtitle!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ],
                
                const SizedBox(height: 4),
                
                Row(
                  children: [
                    Text(
                      timeago.format(activity.timestamp, locale: 'pt_BR'),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withOpacity(0.5),
                      ),
                    ),
                    
                    if (activity.user != null) ..[
                      Text(
                        ' • ',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.5),
                        ),
                      ),
                      Text(
                        activity.user!,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.7),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
          
          if (activity.onTap != null)
            IconButton(
              onPressed: activity.onTap,
              icon: const Icon(Icons.chevron_right),
              iconSize: 20,
            ),
        ],
      ),
    );
  }
}

class ActivityItem {
  final String title;
  final String? subtitle;
  final IconData icon;
  final Color color;
  final DateTime timestamp;
  final String? user;
  final VoidCallback? onTap;
  final ActivityType type;

  const ActivityItem({
    required this.title,
    this.subtitle,
    required this.icon,
    required this.color,
    required this.timestamp,
    this.user,
    this.onTap,
    required this.type,
  });

  factory ActivityItem.orderCreated({
    required String orderNumber,
    required String table,
    required DateTime timestamp,
    required String waiter,
    VoidCallback? onTap,
  }) {
    return ActivityItem(
      title: 'Novo pedido #$orderNumber',
      subtitle: 'Mesa $table',
      icon: Icons.add_shopping_cart,
      color: Colors.green,
      timestamp: timestamp,
      user: waiter,
      onTap: onTap,
      type: ActivityType.orderCreated,
    );
  }

  factory ActivityItem.orderCompleted({
    required String orderNumber,
    required double total,
    required DateTime timestamp,
    VoidCallback? onTap,
  }) {
    return ActivityItem(
      title: 'Pedido #$orderNumber finalizado',
      subtitle: 'Total: R\$ ${total.toStringAsFixed(2)}',
      icon: Icons.check_circle,
      color: Colors.blue,
      timestamp: timestamp,
      onTap: onTap,
      type: ActivityType.orderCompleted,
    );
  }

  factory ActivityItem.tableStatusChanged({
    required String tableName,
    required String status,
    required DateTime timestamp,
    required String user,
    VoidCallback? onTap,
  }) {
    return ActivityItem(
      title: 'Mesa $tableName - $status',
      icon: Icons.table_restaurant,
      color: Colors.orange,
      timestamp: timestamp,
      user: user,
      onTap: onTap,
      type: ActivityType.tableStatusChanged,
    );
  }

  factory ActivityItem.menuItemUpdated({
    required String itemName,
    required String action,
    required DateTime timestamp,
    required String user,
    VoidCallback? onTap,
  }) {
    return ActivityItem(
      title: '$action: $itemName',
      icon: Icons.restaurant_menu,
      color: Colors.purple,
      timestamp: timestamp,
      user: user,
      onTap: onTap,
      type: ActivityType.menuItemUpdated,
    );
  }

  factory ActivityItem.userLogin({
    required String userName,
    required DateTime timestamp,
    VoidCallback? onTap,
  }) {
    return ActivityItem(
      title: 'Login realizado',
      subtitle: userName,
      icon: Icons.login,
      color: Colors.teal,
      timestamp: timestamp,
      onTap: onTap,
      type: ActivityType.userLogin,
    );
  }

  factory ActivityItem.paymentReceived({
    required String orderNumber,
    required double amount,
    required String paymentMethod,
    required DateTime timestamp,
    VoidCallback? onTap,
  }) {
    return ActivityItem(
      title: 'Pagamento recebido #$orderNumber',
      subtitle: 'R\$ ${amount.toStringAsFixed(2)} - $paymentMethod',
      icon: Icons.payment,
      color: Colors.green,
      timestamp: timestamp,
      onTap: onTap,
      type: ActivityType.paymentReceived,
    );
  }

  factory ActivityItem.deliveryDispatched({
    required String orderNumber,
    required String address,
    required DateTime timestamp,
    required String driver,
    VoidCallback? onTap,
  }) {
    return ActivityItem(
      title: 'Entrega despachada #$orderNumber',
      subtitle: address,
      icon: Icons.delivery_dining,
      color: Colors.indigo,
      timestamp: timestamp,
      user: driver,
      onTap: onTap,
      type: ActivityType.deliveryDispatched,
    );
  }
}

enum ActivityType {
  orderCreated,
  orderCompleted,
  tableStatusChanged,
  menuItemUpdated,
  userLogin,
  paymentReceived,
  deliveryDispatched,
}

class ActivityFilter {
  final List<ActivityType> types;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? user;

  const ActivityFilter({
    this.types = const [],
    this.startDate,
    this.endDate,
    this.user,
  });

  bool matches(ActivityItem activity) {
    if (types.isNotEmpty && !types.contains(activity.type)) {
      return false;
    }

    if (startDate != null && activity.timestamp.isBefore(startDate!)) {
      return false;
    }

    if (endDate != null && activity.timestamp.isAfter(endDate!)) {
      return false;
    }

    if (user != null && activity.user != user) {
      return false;
    }

    return true;
  }
}

class CompactActivityWidget extends StatelessWidget {
  final List<ActivityItem> activities;
  final int maxItems;
  final VoidCallback? onViewAll;

  const CompactActivityWidget({
    super.key,
    required this.activities,
    this.maxItems = 3,
    this.onViewAll,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final displayActivities = activities.take(maxItems).toList();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Atividades Recentes',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            if (onViewAll != null)
              TextButton(
                onPressed: onViewAll,
                child: const Text('Ver Todas'),
              ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        if (displayActivities.isEmpty)
          Container(
            padding: const EdgeInsets.all(16),
            child: Text(
              'Nenhuma atividade recente',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          )
        else
          ...displayActivities.map((activity) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: activity.color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Icon(
                      activity.icon,
                      color: activity.color,
                      size: 16,
                    ),
                  ),
                  
                  const SizedBox(width: 8),
                  
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          activity.title,
                          style: theme.textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          timeago.format(activity.timestamp, locale: 'pt_BR'),
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface.withOpacity(0.5),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }),
      ],
    );
  }
}