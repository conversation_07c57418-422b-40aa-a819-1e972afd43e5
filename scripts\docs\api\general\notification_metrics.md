# General - Notification Metrics

**Categoria:** General
**Módulo:** Notification Metrics
**Total de Endpoints:** 8
**Gerado em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [GET /api/modules/core/notifications/metrics/dashboard-stats](#get-apimodulescorenotificationsmetricsdashboard-stats) - Get Dashboard Stats
- [GET /api/modules/core/notifications/metrics/engagement-report](#get-apimodulescorenotificationsmetricsengagement-report) - Get Engagement Report
- [GET /api/modules/core/notifications/metrics/my-metrics](#get-apimodulescorenotificationsmetricsmy-metrics) - Get My Metrics
- [GET /api/modules/core/notifications/metrics/performance-insights](#get-apimodulescorenotificationsmetricsperformance-insights) - Get Performance Insights
- [POST /api/modules/core/notifications/metrics/record-daily](#post-apimodulescorenotificationsmetricsrecord-daily) - Record Daily Metrics
- [GET /api/modules/core/notifications/metrics/system](#get-apimodulescorenotificationsmetricssystem) - Get System Metrics
- [GET /api/modules/core/notifications/metrics/tenant/{tenant_id}](#get-apimodulescorenotificationsmetricstenanttenant-id) - Get Tenant Metrics
- [GET /api/modules/core/notifications/metrics/user/{user_id}](#get-apimodulescorenotificationsmetricsuseruser-id) - Get User Metrics

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### NotificationSystemMetrics

**Descrição:** Schema para métricas do sistema (admin).

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `total_notifications` | integer | ✅ | - |
| `active_notifications` | integer | ✅ | - |
| `expired_notifications` | integer | ✅ | - |
| `queue_size` | integer | ✅ | - |
| `failed_notifications` | integer | ✅ | - |
| `average_delivery_time` | number | ✅ | - |
| `system_load` | number | ✅ | - |
| `error_rate` | number | ✅ | - |

### NotificationTenantMetrics

**Descrição:** Schema para métricas do tenant.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `total_sent` | integer | ✅ | - |
| `total_delivered` | integer | ✅ | - |
| `total_read` | integer | ✅ | - |
| `total_clicked` | integer | ✅ | - |
| `delivery_rate` | number | ✅ | - |
| `open_rate` | number | ✅ | - |
| `click_through_rate` | number | ✅ | - |
| `engagement_score` | number | ✅ | - |

## 🔗 Endpoints Detalhados

### GET /api/modules/core/notifications/metrics/dashboard-stats {#get-apimodulescorenotificationsmetricsdashboard-stats}

**Resumo:** Get Dashboard Stats
**Descrição:** Obtém estatísticas resumidas para o dashboard.

**🔐 Autenticação:** Requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/notifications/metrics/dashboard-stats" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/notifications/metrics/engagement-report {#get-apimodulescorenotificationsmetricsengagement-report}

**Resumo:** Get Engagement Report
**Descrição:** Obtém relatório de engajamento detalhado.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_id` | string | query | ❌ | - |
| `period_days` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/notifications/metrics/engagement-report" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/notifications/metrics/my-metrics {#get-apimodulescorenotificationsmetricsmy-metrics}

**Resumo:** Get My Metrics
**Descrição:** Obtém métricas do usuário atual.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `period_days` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/notifications/metrics/my-metrics" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/notifications/metrics/performance-insights {#get-apimodulescorenotificationsmetricsperformance-insights}

**Resumo:** Get Performance Insights
**Descrição:** Obtém insights de performance das notificações.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_id` | string | query | ❌ | - |
| `period_days` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/notifications/metrics/performance-insights" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/notifications/metrics/record-daily {#post-apimodulescorenotificationsmetricsrecord-daily}

**Resumo:** Record Daily Metrics
**Descrição:** Força o registro de métricas diárias.

Normalmente executado automaticamente via cron.

**🔐 Autenticação:** Requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/metrics/record-daily" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/notifications/metrics/system {#get-apimodulescorenotificationsmetricssystem}

**Resumo:** Get System Metrics
**Descrição:** Obtém métricas gerais do sistema de notificações.

Apenas para administradores.

**🔐 Autenticação:** Requerida

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [NotificationSystemMetrics](#notificationsystemmetrics)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/notifications/metrics/system" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/notifications/metrics/tenant/{tenant_id} {#get-apimodulescorenotificationsmetricstenanttenant-id}

**Resumo:** Get Tenant Metrics
**Descrição:** Obtém métricas de um tenant específico.

Admins podem ver qualquer tenant.
Tenant owners podem ver apenas seus próprios tenants.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_id` | string | path | ✅ | - |
| `period_days` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [NotificationTenantMetrics](#notificationtenantmetrics)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/notifications/metrics/tenant/{tenant_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/notifications/metrics/user/{user_id} {#get-apimodulescorenotificationsmetricsuseruser-id}

**Resumo:** Get User Metrics
**Descrição:** Obtém métricas de um usuário específico.

Usuários podem ver apenas suas próprias métricas.
Admins podem ver qualquer usuário.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `user_id` | string | path | ✅ | - |
| `period_days` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/notifications/metrics/user/{user_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
