# Eshop - Modifiers

**Categoria:** Eshop
**Mó<PERSON>lo:** Modifiers
**Total de Endpoints:** 6
**Gerado em:** 27/06/2025, 20:20:26

## 📋 Endpoints

- [GET /api/eshop/modifiers/groups/](#get-apieshopmodifiersgroups) - Read Modifier Groups
- [POST /api/eshop/modifiers/groups/](#post-apieshopmodifiersgroups) - Create Modifier Group
- [GET /api/eshop/modifiers/groups/{group_id}](#get-apieshopmodifiersgroupsgroup-id) - Read Modifier Group
- [GET /api/eshop/modifiers/options/](#get-apieshopmodifiersoptions) - Read Modifier Options
- [POST /api/eshop/modifiers/options/](#post-apieshopmodifiersoptions) - Create Modifier Option
- [GET /api/eshop/modifiers/options/{option_id}](#get-apieshopmodifiersoptionsoption-id) - Read Modifier Option

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### ProductModifierGroupCreate

**Descrição:** Schema for creating a new product modifier group.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Modifier group name |
| `description` | unknown | ❌ | Modifier group description |
| `min_selection` | integer | ❌ | Minimum number of selections required |
| `max_selection` | integer | ❌ | Maximum number of selections allowed |
| `display_order` | integer | ❌ | Display order within the product |
| `is_required` | boolean | ❌ | Whether this modifier group is required |
| `is_active` | boolean | ❌ | Whether this modifier group is active |
| `is_template` | boolean | ❌ | Whether this is a template group |
| `template_id` | unknown | ❌ | Reference to template group |
| `tenant_id` | unknown | ❌ | Tenant ID (null for global groups) |

### ProductModifierGroupResponse

**Descrição:** Schema for product modifier group responses.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Modifier group name |
| `description` | unknown | ❌ | Modifier group description |
| `min_selection` | integer | ❌ | Minimum number of selections required |
| `max_selection` | integer | ❌ | Maximum number of selections allowed |
| `display_order` | integer | ❌ | Display order within the product |
| `is_required` | boolean | ❌ | Whether this modifier group is required |
| `is_active` | boolean | ❌ | Whether this modifier group is active |
| `is_template` | boolean | ❌ | Whether this is a template group |
| `template_id` | unknown | ❌ | Reference to template group |
| `id` | string | ✅ | - |
| `tenant_id` | unknown | ✅ | - |
| `options` | Array[ProductModifierOptionResponse] | ❌ | Modifier options |

### ProductModifierOptionCreate

**Descrição:** Schema for creating a new product modifier option.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Modifier option name |
| `description` | unknown | ❌ | Modifier option description |
| `price_adjustment` | unknown | ❌ | Additional cost for this modifier |
| `price_adjustment_type` | string | ❌ | Price adjustment type |
| `display_order` | integer | ❌ | Display order within the group |
| `is_default` | boolean | ❌ | Whether this is the default selection |
| `is_active` | boolean | ❌ | Whether this modifier option is active |
| `stock_quantity` | unknown | ❌ | Limited availability for this modifier |
| `image_url` | unknown | ❌ | Image URL for this modifier |
| `tenant_id` | unknown | ❌ | Tenant ID (null for global options) |
| `modifier_group_id` | string | ✅ | Parent modifier group ID |

### ProductModifierOptionResponse

**Descrição:** Schema for product modifier option responses.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Modifier option name |
| `description` | unknown | ❌ | Modifier option description |
| `price_adjustment` | string | ❌ | Additional cost for this modifier |
| `price_adjustment_type` | string | ❌ | Price adjustment type |
| `display_order` | integer | ❌ | Display order within the group |
| `is_default` | boolean | ❌ | Whether this is the default selection |
| `is_active` | boolean | ❌ | Whether this modifier option is active |
| `stock_quantity` | unknown | ❌ | Limited availability for this modifier |
| `image_url` | unknown | ❌ | Image URL for this modifier |
| `id` | string | ✅ | - |
| `tenant_id` | unknown | ✅ | - |
| `modifier_group_id` | string | ✅ | - |

## 🔗 Endpoints Detalhados

### GET /api/eshop/modifiers/groups/ {#get-apieshopmodifiersgroups}

**Resumo:** Read Modifier Groups
**Descrição:** Retrieve modifier groups.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `is_active` | string | query | ❌ | - |
| `include_options` | boolean | query | ❌ | - |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/modifiers/groups/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/eshop/modifiers/groups/ {#post-apieshopmodifiersgroups}

**Resumo:** Create Modifier Group
**Descrição:** Create a new modifier group.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ProductModifierGroupCreate](#productmodifiergroupcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductModifierGroupResponse](#productmodifiergroupresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/eshop/modifiers/groups/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/eshop/modifiers/groups/{group_id} {#get-apieshopmodifiersgroupsgroup-id}

**Resumo:** Read Modifier Group
**Descrição:** Retrieve a specific modifier group.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `group_id` | string | path | ✅ | - |
| `include_options` | boolean | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductModifierGroupResponse](#productmodifiergroupresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/modifiers/groups/{group_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/eshop/modifiers/options/ {#get-apieshopmodifiersoptions}

**Resumo:** Read Modifier Options
**Descrição:** Retrieve modifier options.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `modifier_group_id` | string | query | ❌ | - |
| `is_active` | string | query | ❌ | - |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/modifiers/options/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/eshop/modifiers/options/ {#post-apieshopmodifiersoptions}

**Resumo:** Create Modifier Option
**Descrição:** Create a new modifier option.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ProductModifierOptionCreate](#productmodifieroptioncreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductModifierOptionResponse](#productmodifieroptionresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/eshop/modifiers/options/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/eshop/modifiers/options/{option_id} {#get-apieshopmodifiersoptionsoption-id}

**Resumo:** Read Modifier Option
**Descrição:** Retrieve a specific modifier option.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `option_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductModifierOptionResponse](#productmodifieroptionresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/modifiers/options/{option_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
