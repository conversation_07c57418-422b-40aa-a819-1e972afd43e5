'use client';

import { useState } from 'react';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  EyeSlashIcon,
  ArrowUpIcon,
  ArrowDownIcon,
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';
import AddSocialMediaModal from './AddSocialMediaModal';
import SocialMediaLinkCard from './SocialMediaLinkCard';

export interface SocialMediaLink {
  id: string;
  platform: string;
  url: string;
  display_name: string;
  icon: string;
  is_active: boolean;
  order: number;
}

interface SocialMediaTabProps {
  socialMediaLinks: SocialMediaLink[];
  onUpdate: (links: SocialMediaLink[]) => void;
}

export default function SocialMediaTab({
  socialMediaLinks = [],
  onUpdate,
}: SocialMediaTabProps) {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [editingLink, setEditingLink] = useState<SocialMediaLink | null>(null);

  const handleAddLink = (newLink: Omit<SocialMediaLink, 'id' | 'order'>) => {
    const maxOrder = socialMediaLinks.length > 0 
      ? Math.max(...socialMediaLinks.map(link => link.order))
      : -1;

    const linkWithId: SocialMediaLink = {
      ...newLink,
      id: `social_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      order: maxOrder + 1,
    };

    const updatedLinks = [...socialMediaLinks, linkWithId];
    onUpdate(updatedLinks);
    setIsAddModalOpen(false);
    toast.success('Social media link added successfully!');
  };

  const handleEditLink = (updatedLink: SocialMediaLink | Omit<SocialMediaLink, 'id' | 'order'>) => {
    // If it's a full SocialMediaLink (editing case), use it directly
    // If it's missing id/order (shouldn't happen in edit mode), merge with existing
    const finalLink = 'id' in updatedLink ? updatedLink : { ...editingLink!, ...updatedLink };

    const updatedLinks = socialMediaLinks.map(link =>
      link.id === finalLink.id ? finalLink : link
    );
    onUpdate(updatedLinks);
    setEditingLink(null);
    toast.success('Social media link updated successfully!');
  };

  const handleDeleteLink = (linkId: string) => {
    const updatedLinks = socialMediaLinks.filter(link => link.id !== linkId);
    // Reorder remaining links
    const reorderedLinks = updatedLinks.map((link, index) => ({
      ...link,
      order: index,
    }));
    onUpdate(reorderedLinks);
    toast.success('Social media link deleted successfully!');
  };

  const handleToggleActive = (linkId: string) => {
    const updatedLinks = socialMediaLinks.map(link =>
      link.id === linkId ? { ...link, is_active: !link.is_active } : link
    );
    onUpdate(updatedLinks);
  };

  const handleMoveUp = (linkId: string) => {
    const currentIndex = socialMediaLinks.findIndex(link => link.id === linkId);
    if (currentIndex > 0) {
      const newLinks = [...socialMediaLinks];
      [newLinks[currentIndex], newLinks[currentIndex - 1]] = 
        [newLinks[currentIndex - 1], newLinks[currentIndex]];
      
      // Update order values
      const reorderedLinks = newLinks.map((link, index) => ({
        ...link,
        order: index,
      }));
      onUpdate(reorderedLinks);
    }
  };

  const handleMoveDown = (linkId: string) => {
    const currentIndex = socialMediaLinks.findIndex(link => link.id === linkId);
    if (currentIndex < socialMediaLinks.length - 1) {
      const newLinks = [...socialMediaLinks];
      [newLinks[currentIndex], newLinks[currentIndex + 1]] = 
        [newLinks[currentIndex + 1], newLinks[currentIndex]];
      
      // Update order values
      const reorderedLinks = newLinks.map((link, index) => ({
        ...link,
        order: index,
      }));
      onUpdate(reorderedLinks);
    }
  };

  // Sort links by order for display
  const sortedLinks = [...socialMediaLinks].sort((a, b) => a.order - b.order);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Social Media Links</h3>
          <p className="text-sm text-gray-500">
            Manage your restaurant's social media presence. Add links to your social media profiles.
          </p>
        </div>
        <button
          onClick={() => setIsAddModalOpen(true)}
          className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors flex items-center space-x-2"
        >
          <PlusIcon className="h-4 w-4" />
          <span>Add Social Media</span>
        </button>
      </div>

      {/* Social Media Links List */}
      {sortedLinks.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
          <div className="mx-auto h-12 w-12 text-gray-400">
            <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
            </svg>
          </div>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No social media links</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by adding your first social media link.
          </p>
          <div className="mt-6">
            <button
              onClick={() => setIsAddModalOpen(true)}
              className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
            >
              Add Social Media Link
            </button>
          </div>
        </div>
      ) : (
        <div className="grid gap-4">
          {sortedLinks.map((link, index) => (
            <SocialMediaLinkCard
              key={link.id}
              link={link}
              isFirst={index === 0}
              isLast={index === sortedLinks.length - 1}
              onEdit={() => setEditingLink(link)}
              onDelete={() => handleDeleteLink(link.id)}
              onToggleActive={() => handleToggleActive(link.id)}
              onMoveUp={() => handleMoveUp(link.id)}
              onMoveDown={() => handleMoveDown(link.id)}
            />
          ))}
        </div>
      )}

      {/* Add Social Media Modal */}
      <AddSocialMediaModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onAdd={handleAddLink}
        existingPlatforms={socialMediaLinks.map(link => link.platform)}
      />

      {/* Edit Social Media Modal */}
      {editingLink && (
        <AddSocialMediaModal
          isOpen={true}
          onClose={() => setEditingLink(null)}
          onAdd={handleEditLink}
          existingPlatforms={socialMediaLinks
            .filter(link => link.id !== editingLink.id)
            .map(link => link.platform)
          }
          editingLink={editingLink}
        />
      )}
    </div>
  );
}
