import uuid
from typing import Optional
from pydantic import BaseModel, Field, ConfigDict
import datetime


# === Shopping List Category Schemas ===

class ShoppingListCategoryBase(BaseModel):
    """Base schema para categoria de lista de compras."""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    display_order: int = Field(0, ge=0)
    is_active: bool = True
    color: Optional[str] = Field(
        None, 
        pattern=r'^#[0-9A-Fa-f]{6}$',
        description="Hex color code for the category"
    )

    model_config = ConfigDict(from_attributes=True)


class ShoppingListCategoryCreate(ShoppingListCategoryBase):
    """Schema para criação de categoria de lista de compras."""
    pass


class ShoppingListCategoryUpdate(BaseModel):
    """Schema para atualização de categoria de lista de compras."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    display_order: Optional[int] = Field(None, ge=0)
    is_active: Optional[bool] = None
    color: Optional[str] = Field(
        None, 
        pattern=r'^#[0-9A-Fa-f]{6}$',
        description="Hex color code for the category"
    )

    model_config = ConfigDict(from_attributes=True)


class ShoppingListCategoryRead(ShoppingListCategoryBase):
    """Schema para leitura de categoria de lista de compras."""
    id: uuid.UUID
    tenant_id: uuid.UUID
    created_at: datetime.datetime
    updated_at: datetime.datetime
