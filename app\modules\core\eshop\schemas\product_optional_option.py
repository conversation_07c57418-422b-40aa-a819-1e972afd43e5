from typing import Optional
from pydantic import BaseModel, Field
from uuid import UUID
from decimal import Decimal


class ProductOptionalOptionBase(BaseModel):
    """Base schema for product optional options."""
    name: str = Field(..., min_length=1, max_length=100, description="Optional option name")
    description: Optional[str] = Field(None, max_length=255, description="Optional option description")
    price_adjustment: Decimal = Field(0.0, description="Additional cost for this optional")
    price_adjustment_type: str = Field("fixed", pattern="^(fixed|percentage)$", description="Price adjustment type")
    display_order: int = Field(0, description="Display order within the group")
    is_default: bool = Field(False, description="Whether this is the default selection")
    is_active: bool = Field(True, description="Whether this optional option is active")
    stock_quantity: Optional[int] = Field(None, ge=0, description="Limited availability for this optional")
    sku: Optional[str] = Field(None, max_length=100, description="Separate SKU for this optional item")
    image_url: Optional[str] = Field(None, max_length=500, description="Image URL for this optional")
    related_product_id: Optional[UUID] = Field(None, description="Related product ID if this optional is another product")


class ProductOptionalOptionCreate(ProductOptionalOptionBase):
    """Schema for creating a new product optional option."""
    tenant_id: Optional[UUID] = Field(None, description="Tenant ID (null for global options)")
    optional_group_id: UUID = Field(..., description="Parent optional group ID")


class ProductOptionalOptionUpdate(BaseModel):
    """Schema for updating a product optional option."""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="Optional option name")
    description: Optional[str] = Field(None, max_length=255, description="Optional option description")
    price_adjustment: Optional[Decimal] = Field(None, description="Additional cost for this optional")
    price_adjustment_type: Optional[str] = Field(None, pattern="^(fixed|percentage)$", description="Price adjustment type")
    display_order: Optional[int] = Field(None, description="Display order within the group")
    is_default: Optional[bool] = Field(None, description="Whether this is the default selection")
    is_active: Optional[bool] = Field(None, description="Whether this optional option is active")
    stock_quantity: Optional[int] = Field(None, ge=0, description="Limited availability for this optional")
    sku: Optional[str] = Field(None, max_length=100, description="Separate SKU for this optional item")
    image_url: Optional[str] = Field(None, max_length=500, description="Image URL for this optional")
    related_product_id: Optional[UUID] = Field(None, description="Related product ID if this optional is another product")


class ProductOptionalOptionResponse(ProductOptionalOptionBase):
    """Schema for product optional option responses."""
    id: UUID
    tenant_id: Optional[UUID]
    optional_group_id: UUID
    
    class Config:
        from_attributes = True
