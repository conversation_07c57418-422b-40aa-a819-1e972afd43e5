# Admin - Tenant View

**Categoria:** Admin
**Módulo:** Tenant View
**Total de Endpoints:** 4
**Gerado em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [GET /api/tenants/admin/audit/tenant/{tenant_id}](#get-apitenantsadminaudittenanttenant-id) - Admin: Get tenant audit log
- [POST /api/tenants/admin/tenant/{tenant_id}/action](#post-apitenantsadmintenanttenant-idaction) - Admin: Perform action on tenant
- [GET /api/tenants/admin/tenant/{tenant_id}/dashboard-data](#get-apitenantsadmintenanttenant-iddashboard-data) - Admin: Get tenant dashboard data
- [GET /api/tenants/admin/tenant/{tenant_id}/view](#get-apitenantsadmintenanttenant-idview) - Admin: View any tenant

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### Tenant

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `is_active` | boolean | ❌ | - |
| `id` | string | ✅ | - |
| `created_at` | unknown | ❌ | - |
| `updated_at` | unknown | ❌ | - |
| `name` | unknown | ❌ | - |
| `tenant_slug` | unknown | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/tenants/admin/audit/tenant/{tenant_id} {#get-apitenantsadminaudittenanttenant-id}

**Resumo:** Admin: Get tenant audit log
**Descrição:** Get audit log for a specific tenant

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_id` | string | path | ✅ | - |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/tenants/admin/audit/tenant/{tenant_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/tenants/admin/tenant/{tenant_id}/action {#post-apitenantsadmintenanttenant-idaction}

**Resumo:** Admin: Perform action on tenant
**Descrição:** Perform administrative actions on a tenant with full audit logging

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** {'type': 'object', 'additionalProperties': True, 'title': 'Action Data'}

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/tenants/admin/tenant/{tenant_id}/action" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/tenants/admin/tenant/{tenant_id}/dashboard-data {#get-apitenantsadmintenanttenant-iddashboard-data}

**Resumo:** Admin: Get tenant dashboard data
**Descrição:** Get comprehensive dashboard data for a tenant (admin view)

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/tenants/admin/tenant/{tenant_id}/dashboard-data" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/tenants/admin/tenant/{tenant_id}/view {#get-apitenantsadmintenanttenant-idview}

**Resumo:** Admin: View any tenant
**Descrição:** Allows system administrators to view any tenant for admin purposes

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [Tenant](#tenant)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/tenants/admin/tenant/{tenant_id}/view" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
