'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { ChevronDownIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { getAllCountries, CountryOption } from '@/services/locationService';
import { getFlagPath } from '@/utils/flagMapping';

interface CountrySelectorProps {
  selectedCountry: string;
  onCountryChange: (country: string) => void;
  disabled?: boolean;
  className?: string;
}

export default function CountrySelector({
  selectedCountry,
  onCountryChange,
  disabled = false,
  className = ''
}: CountrySelectorProps) {
  const [countries, setCountries] = useState<CountryOption[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredCountries, setFilteredCountries] = useState<CountryOption[]>([]);

  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);



  // Get selected country details
  const selectedCountryData = countries.find(c => c.code === selectedCountry);

  useEffect(() => {
    const fetchCountries = async () => {
      console.log('🌍 CountrySelector: Starting to fetch countries...');
      setIsLoading(true);
      setError(null);

      try {
        const countryOptions = await getAllCountries();
        console.log('🌍 CountrySelector: Received countries:', {
          count: countryOptions.length,
          firstFew: countryOptions.slice(0, 3).map(c => ({ code: c.code, name: c.name }))
        });
        setCountries(countryOptions);
        setFilteredCountries(countryOptions);
      } catch (err) {
        console.error('❌ CountrySelector: Error loading countries:', err);
        setError('Failed to load countries');
      } finally {
        setIsLoading(false);
        console.log('🌍 CountrySelector: Finished loading countries');
      }
    };

    fetchCountries();
  }, []);

  // Filter countries based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredCountries(countries);
    } else {
      const filtered = countries.filter(country =>
        country.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        country.code.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredCountries(filtered);
    }
  }, [searchTerm, countries]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  const handleCountrySelect = (countryCode: string) => {
    console.log('🌍 CountrySelector: Country changed from', selectedCountry, 'to', countryCode);
    onCountryChange(countryCode);
    setIsOpen(false);
    setSearchTerm('');
  };

  if (isLoading) {
    return (
      <div className={`${className} opacity-50 flex items-center justify-center p-2 border border-gray-300 rounded-md`}>
        <span className="text-gray-500">Loading countries...</span>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Selected Country Display */}
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={`w-full flex items-center justify-between p-2 border border-gray-300 rounded-md hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
          disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white cursor-pointer'
        } ${error ? 'border-red-300' : ''}`}
      >
        <div className="flex items-center space-x-2">
          {selectedCountryData ? (
            <>
              <div className="relative w-5 h-4 flex-shrink-0">
                <Image
                  src={getFlagPath(selectedCountryData.code)}
                  alt={`${selectedCountryData.name} flag`}
                  fill
                  className="object-cover rounded-sm"
                  onError={(e) => {
                    // Fallback to emoji if SVG fails
                    const target = e.currentTarget;
                    target.style.display = 'none';
                    const span = document.createElement('span');
                    span.textContent = selectedCountryData.flag || '🏳️';
                    span.className = 'text-sm';
                    target.parentNode?.insertBefore(span, target);
                  }}
                />
              </div>
              <span className="text-sm truncate">{selectedCountryData.name}</span>
            </>
          ) : (
            <span className="text-gray-500 text-sm">Select a country</span>
          )}
        </div>
        <ChevronDownIcon className={`w-4 h-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Dropdown */}
      {isOpen && !disabled && (
        <div className="absolute z-[9999] w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-hidden">
          {/* Search Input */}
          <div className="p-2 border-b border-gray-200">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Search countries..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-8 pr-3 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>

          {/* Countries List */}
          <div className="max-h-48 overflow-y-auto">
            {filteredCountries.length > 0 ? (
              filteredCountries.map((country) => (
                <button
                  key={country.code}
                  type="button"
                  onClick={() => handleCountrySelect(country.code)}
                  className={`w-full flex items-center space-x-2 px-3 py-2 text-left hover:bg-gray-100 focus:outline-none focus:bg-gray-100 ${
                    selectedCountry === country.code ? 'bg-primary-50 text-primary-700' : 'text-gray-900'
                  }`}
                >
                  <div className="relative w-5 h-4 flex-shrink-0">
                    <Image
                      src={getFlagPath(country.code)}
                      alt={`${country.name} flag`}
                      fill
                      className="object-cover rounded-sm"
                      onError={(e) => {
                        // Fallback to emoji if SVG fails
                        const target = e.currentTarget;
                        target.style.display = 'none';
                        const span = document.createElement('span');
                        span.textContent = country.flag || '🏳️';
                        span.className = 'text-sm flex-shrink-0';
                        target.parentNode?.insertBefore(span, target);
                      }}
                    />
                  </div>
                  <span className="text-sm truncate flex-1">{country.name}</span>
                  <span className="text-xs text-gray-500 ml-auto">{country.code}</span>
                </button>
              ))
            ) : (
              <div className="px-3 py-2 text-sm text-gray-500">
                No countries found
              </div>
            )}
          </div>
        </div>
      )}

      {error && (
        <p className="mt-1 text-sm text-red-600">
          {error}
        </p>
      )}
    </div>
  );
}
