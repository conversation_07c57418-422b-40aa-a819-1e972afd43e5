"""
System Notification Service

Serviço para notificações específicas do sistema (B2B, pedidos, leilões, etc.).
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession

from app.modules.core.users.models.user import User
from app.modules.core.tenants.models.tenant import Tenant

from ..models import (
    Notification, NotificationPriority, NotificationSenderType, 
    NotificationStatus, NotificationTargetType, NotificationTemplate
)
from .notification_service import NotificationService
from .notification_delivery_service import NotificationDeliveryService
from ..websockets.notification_websockets import notification_ws_manager

logger = logging.getLogger(__name__)


class SystemNotificationService:
    """Serviço para notificações específicas do sistema."""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.notification_service = NotificationService(db)
        self.delivery_service = NotificationDeliveryService(db)

    async def notify_b2b_approval_request(
        self,
        user_id: UUID,
        user_type: str,  # "tcustomer" or "tvendor_supplier"
        tenant_id: Optional[UUID] = None
    ) -> Notification:
        """
        Notifica sobre solicitação de aprovação B2B.
        
        Args:
            user_id: ID do usuário solicitante
            user_type: Tipo de aprovação (tcustomer/tvendor_supplier)
            tenant_id: ID do tenant (se aplicável)
        """
        # Busca usuário
        user = await self._get_user(user_id)
        if not user:
            raise ValueError("Usuário não encontrado")

        # Define conteúdo baseado no tipo
        if user_type == "tcustomer":
            title = "Nova Solicitação de Aprovação TCustomer"
            content = f"Usuário {user.email} solicitou aprovação como TCustomer para compras B2B."
        else:
            title = "Nova Solicitação de Aprovação TVendorSupplier"
            content = f"Usuário {user.email} solicitou aprovação como TVendorSupplier para vendas B2B."

        # Cria notificação para admins
        notification = await self._create_system_notification(
            title=title,
            content=content,
            target_type=NotificationTargetType.TENANT_OWNERS,
            tenant_id=tenant_id,
            priority=NotificationPriority.HIGH,
            action_url=f"/admin/b2b-approvals/{user_id}",
            metadata={
                "event_type": "b2b_approval_request",
                "user_id": str(user_id),
                "user_type": user_type,
                "user_email": user.email
            }
        )

        # Envia via WebSocket
        await notification_ws_manager.broadcast_to_admins({
            "type": "b2b_approval_request",
            "notification_id": str(notification.id),
            "user_id": str(user_id),
            "user_type": user_type,
            "user_email": user.email,
            "title": title,
            "content": content,
            "timestamp": datetime.utcnow().isoformat()
        })

        logger.info(f"Notificação B2B approval enviada: {user_type} para {user.email}")
        return notification

    async def notify_b2b_approval_status(
        self,
        user_id: UUID,
        user_type: str,
        status: str,  # "approved" or "rejected"
        reason: Optional[str] = None
    ) -> Notification:
        """
        Notifica sobre mudança de status de aprovação B2B.
        
        Args:
            user_id: ID do usuário
            user_type: Tipo de aprovação
            status: Status da aprovação
            reason: Motivo (se rejeitado)
        """
        user = await self._get_user(user_id)
        if not user:
            raise ValueError("Usuário não encontrado")

        if status == "approved":
            title = f"Aprovação {user_type.upper()} Aprovada!"
            content = f"Sua solicitação de aprovação como {user_type.upper()} foi aprovada. Você já pode acessar as funcionalidades B2B."
            priority = NotificationPriority.HIGH
        else:
            title = f"Aprovação {user_type.upper()} Rejeitada"
            content = f"Sua solicitação de aprovação como {user_type.upper()} foi rejeitada."
            if reason:
                content += f" Motivo: {reason}"
            priority = NotificationPriority.NORMAL

        notification = await self._create_system_notification(
            title=title,
            content=content,
            target_type=NotificationTargetType.SPECIFIC_USER,
            target_id=user_id,
            priority=priority,
            action_url="/profile/b2b-status",
            metadata={
                "event_type": "b2b_approval_status",
                "user_id": str(user_id),
                "user_type": user_type,
                "status": status,
                "reason": reason
            }
        )

        # Envia via WebSocket
        await notification_ws_manager.send_personal_message(user_id, {
            "type": "b2b_approval_status",
            "notification_id": str(notification.id),
            "status": status,
            "user_type": user_type,
            "title": title,
            "content": content,
            "timestamp": datetime.utcnow().isoformat()
        })

        logger.info(f"Notificação B2B status enviada: {status} para {user.email}")
        return notification

    async def notify_order_status_change(
        self,
        order_id: UUID,
        customer_id: UUID,
        old_status: str,
        new_status: str,
        tenant_id: Optional[UUID] = None
    ) -> Notification:
        """
        Notifica sobre mudança de status de pedido.
        
        Args:
            order_id: ID do pedido
            customer_id: ID do cliente
            old_status: Status anterior
            new_status: Novo status
            tenant_id: ID do tenant
        """
        status_messages = {
            "pending": "Pedido recebido e aguardando confirmação",
            "confirmed": "Pedido confirmado e em preparação",
            "preparing": "Pedido sendo preparado",
            "ready": "Pedido pronto para entrega/retirada",
            "shipped": "Pedido enviado para entrega",
            "delivered": "Pedido entregue com sucesso",
            "cancelled": "Pedido cancelado",
            "refunded": "Pedido reembolsado"
        }

        title = f"Pedido #{str(order_id)[:8]} - Status Atualizado"
        content = f"Seu pedido foi atualizado: {status_messages.get(new_status, new_status)}"

        # Define prioridade baseada no status
        priority = NotificationPriority.HIGH if new_status in ["delivered", "cancelled"] else NotificationPriority.NORMAL

        notification = await self._create_system_notification(
            title=title,
            content=content,
            target_type=NotificationTargetType.SPECIFIC_USER,
            target_id=customer_id,
            tenant_id=tenant_id,
            priority=priority,
            action_url=f"/orders/{order_id}",
            metadata={
                "event_type": "order_status_change",
                "order_id": str(order_id),
                "customer_id": str(customer_id),
                "old_status": old_status,
                "new_status": new_status
            }
        )

        # Envia via WebSocket
        await notification_ws_manager.send_personal_message(customer_id, {
            "type": "order_status_change",
            "notification_id": str(notification.id),
            "order_id": str(order_id),
            "old_status": old_status,
            "new_status": new_status,
            "title": title,
            "content": content,
            "timestamp": datetime.utcnow().isoformat()
        })

        logger.info(f"Notificação order status enviada: {order_id} -> {new_status}")
        return notification

    async def notify_auction_bid(
        self,
        auction_id: UUID,
        bidder_id: UUID,
        bid_amount: float,
        previous_bidder_id: Optional[UUID] = None
    ) -> List[Notification]:
        """
        Notifica sobre novo lance em leilão.
        
        Args:
            auction_id: ID do leilão
            bidder_id: ID do licitante
            bid_amount: Valor do lance
            previous_bidder_id: ID do licitante anterior
        """
        notifications = []

        # Notifica licitante anterior (se houver)
        if previous_bidder_id and previous_bidder_id != bidder_id:
            title = "Você foi superado no leilão!"
            content = f"Alguém fez um lance maior que o seu no leilão. Valor atual: R$ {bid_amount:.2f}"

            notification = await self._create_system_notification(
                title=title,
                content=content,
                target_type=NotificationTargetType.SPECIFIC_USER,
                target_id=previous_bidder_id,
                priority=NotificationPriority.HIGH,
                action_url=f"/auctions/{auction_id}",
                metadata={
                    "event_type": "auction_outbid",
                    "auction_id": str(auction_id),
                    "bid_amount": bid_amount,
                    "bidder_id": str(bidder_id)
                }
            )
            notifications.append(notification)

            # WebSocket para licitante anterior
            await notification_ws_manager.send_personal_message(previous_bidder_id, {
                "type": "auction_outbid",
                "notification_id": str(notification.id),
                "auction_id": str(auction_id),
                "bid_amount": bid_amount,
                "title": title,
                "content": content,
                "timestamp": datetime.utcnow().isoformat()
            })

        # Notifica todos os interessados no leilão
        await notification_ws_manager.broadcast_to_all({
            "type": "auction_new_bid",
            "auction_id": str(auction_id),
            "bid_amount": bid_amount,
            "bidder_id": str(bidder_id),
            "timestamp": datetime.utcnow().isoformat()
        })

        logger.info(f"Notificações auction bid enviadas: {auction_id} - R$ {bid_amount}")
        return notifications

    async def notify_auction_end(
        self,
        auction_id: UUID,
        winner_id: Optional[UUID] = None,
        winning_bid: Optional[float] = None
    ) -> List[Notification]:
        """
        Notifica sobre fim de leilão.
        
        Args:
            auction_id: ID do leilão
            winner_id: ID do vencedor (se houver)
            winning_bid: Lance vencedor
        """
        notifications = []

        if winner_id:
            # Notifica vencedor
            title = "Parabéns! Você venceu o leilão!"
            content = f"Você venceu o leilão com o lance de R$ {winning_bid:.2f}. Aguarde instruções para pagamento."

            notification = await self._create_system_notification(
                title=title,
                content=content,
                target_type=NotificationTargetType.SPECIFIC_USER,
                target_id=winner_id,
                priority=NotificationPriority.URGENT,
                action_url=f"/auctions/{auction_id}/payment",
                metadata={
                    "event_type": "auction_won",
                    "auction_id": str(auction_id),
                    "winning_bid": winning_bid
                }
            )
            notifications.append(notification)

            # WebSocket para vencedor
            await notification_ws_manager.send_personal_message(winner_id, {
                "type": "auction_won",
                "notification_id": str(notification.id),
                "auction_id": str(auction_id),
                "winning_bid": winning_bid,
                "title": title,
                "content": content,
                "timestamp": datetime.utcnow().isoformat()
            })

        # Broadcast para todos sobre fim do leilão
        await notification_ws_manager.broadcast_to_all({
            "type": "auction_ended",
            "auction_id": str(auction_id),
            "winner_id": str(winner_id) if winner_id else None,
            "winning_bid": winning_bid,
            "timestamp": datetime.utcnow().isoformat()
        })

        logger.info(f"Notificações auction end enviadas: {auction_id}")
        return notifications

    async def _create_system_notification(
        self,
        title: str,
        content: str,
        target_type: NotificationTargetType,
        target_id: Optional[UUID] = None,
        tenant_id: Optional[UUID] = None,
        priority: NotificationPriority = NotificationPriority.NORMAL,
        action_url: Optional[str] = None,
        metadata: Optional[Dict] = None
    ) -> Notification:
        """Cria notificação do sistema."""
        # Cria usuário sistema fictício para notificações automáticas
        system_user = type('SystemUser', (), {
            'id': UUID('00000000-0000-0000-0000-000000000000'),
            'email': '<EMAIL>',
            'system_role': 'admin'
        })()

        notification = Notification(
            title=title,
            content=content,
            action_url=action_url,
            sender_id=system_user.id,
            sender_type=NotificationSenderType.SYSTEM,
            target_type=target_type,
            target_id=target_id,
            tenant_id=tenant_id,
            priority=priority,
            status=NotificationStatus.QUEUED,
            auto_expire_hours=72,  # 3 dias para notificações do sistema
            read_by={},
            deleted_by={}
        )

        # Adiciona metadata se fornecida
        if metadata:
            # Armazena metadata no campo JSON (se existir)
            pass

        self.db.add(notification)
        await self.db.commit()
        await self.db.refresh(notification)

        return notification

    async def _get_user(self, user_id: UUID) -> Optional[User]:
        """Busca usuário por ID."""
        from sqlalchemy.future import select
        
        stmt = select(User).where(User.id == user_id)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
