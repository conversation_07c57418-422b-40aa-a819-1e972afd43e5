import uuid
from datetime import datetime
from typing import Optional, List

from pydantic import BaseModel, EmailStr, ConfigDict, Field


class UserBase(BaseModel):
    email: EmailStr
    full_name: Optional[str] = None
    phone_number: Optional[str] = None
    is_active: bool = True
    system_role: str = "user"
    data_sharing_consent: bool = False


class UserCreate(UserBase):
    password: str = Field(..., min_length=8)


class UserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    phone_number: Optional[str] = None
    password: Optional[str] = Field(None, min_length=8)
    is_active: Optional[bool] = None
    system_role: Optional[str] = None
    data_sharing_consent: Optional[bool] = None


class UserInDBBase(UserBase):
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    model_config = ConfigDict(from_attributes=True)


class User(UserInDBBase):
    pass


class UserWithTenants(User):
    tenants: List["Tenant"] = []


# Schema for paginated user response
class UserListResponse(BaseModel):
    users: List[User]
    total: int
    page: int
    limit: int
    total_pages: int


# Schema for user response
UserSchema = User
