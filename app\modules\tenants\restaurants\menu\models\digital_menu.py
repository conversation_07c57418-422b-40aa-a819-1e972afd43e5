"""
Digital Menu model for multi-menu support.
"""

import uuid
from datetime import datetime
from typing import TYPE_CHECKING

from sqlalchemy import (
    <PERSON>olean,
    Column,
    DateTime,
    Foreign<PERSON>ey,
    Integer,
    JSON,
    String,
    Text,
)
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.orm import relationship

from app.db.base import Base
from app.modules.core.tenants.models.tenant import Tenant

if TYPE_CHECKING:
    from app.modules.tenants.restaurants.menu.models.menu_category import MenuCategory


class DigitalMenu(Base):
    """
    Represents a digital menu that can contain multiple categories and items.
    Supports time-based scheduling and multiple menus per tenant.
    """

    __tablename__ = "digital_menus"

    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    tenant_id = Column(PG_UUID(as_uuid=True), ForeignKey(Tenant.id), nullable=False, index=True)

    # Basic menu information
    name = Column(String(150), nullable=False, index=True)
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    display_order = Column(Integer, default=0, nullable=False)

    # Scheduling configuration
    schedule_enabled = Column(Boolean, default=False, nullable=False)
    schedule_config = Column(JSON, nullable=True)  # Store timezone and schedule rules

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    tenant = relationship("Tenant")
    categories = relationship(
        "MenuCategory",
        back_populates="digital_menu",
        cascade="all, delete-orphan",
        order_by="MenuCategory.display_order"
    )

    def __repr__(self):
        return f"<DigitalMenu(id={self.id}, name='{self.name}', tenant_id={self.tenant_id})>"
