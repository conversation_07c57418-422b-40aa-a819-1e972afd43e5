import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/models/menu_item_model.dart';
import '../../../../core/providers/menu_provider.dart';
import '../../../../shared/index.dart';
import '../../../../core/constants/app_constants.dart';

class MenuPage extends ConsumerStatefulWidget {
  const MenuPage({super.key});

  @override
  ConsumerState<MenuPage> createState() => _MenuPageState();
}

class _MenuPageState extends ConsumerState<MenuPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();
  String? _selectedCategory;

  @override
  void initState() {
    super.initState();
    
    // Load menu when page initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(menuProvider.notifier).loadMenu();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final menuState = ref.watch(menuProvider);
    final theme = Theme.of(context);
    
    // Initialize tab controller when categories are loaded
    if (menuState.categories.isNotEmpty && !mounted) {
      _tabController = TabController(
        length: menuState.categories.length + 1, // +1 for "All" tab
        vsync: this,
      );
    }

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: const Text('Menu'),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.read(menuProvider.notifier).loadMenu(),
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.filter_list),
            onSelected: _onFilterSelected,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'all',
                child: Text('Todos os itens'),
              ),
              const PopupMenuItem(
                value: 'available',
                child: Text('Disponíveis'),
              ),
              const PopupMenuItem(
                value: 'unavailable',
                child: Text('Indisponíveis'),
              ),
              const PopupMenuItem(
                value: 'vegetarian',
                child: Text('Vegetarianos'),
              ),
              const PopupMenuItem(
                value: 'vegan',
                child: Text('Veganos'),
              ),
              const PopupMenuItem(
                value: 'gluten_free',
                child: Text('Sem glúten'),
              ),
            ],
          ),
        ],
        bottom: menuState.categories.isNotEmpty
            ? TabBar(
                controller: _tabController,
                isScrollable: true,
                tabs: [
                  const Tab(text: 'Todos'),
                  ...menuState.categories.map((category) => Tab(text: category)),
                ],
              )
            : null,
      ),
      body: menuState.isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : Column(
              children: [
                // Search bar
                if (_searchQuery.isNotEmpty || _selectedCategory != null)
                  _buildActiveFilters(),
                
                // Menu content
                Expanded(
                  child: menuState.categories.isNotEmpty
                      ? TabBarView(
                          controller: _tabController,
                          children: [
                            _buildMenuGrid(ref.read(menuProvider.notifier).filteredItems),
                            ...menuState.categories.map((category) {
                              final categoryItems = menuState.items
                                  .where((item) => item.category == category)
                                  .toList();
                              return _buildMenuGrid(_applyFilters(categoryItems));
                            }),
                          ],
                        )
                      : _buildMenuGrid(ref.read(menuProvider.notifier).filteredItems),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddItemDialog,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildActiveFilters() {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      color: theme.colorScheme.surface,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Filtros ativos:',
            style: theme.textTheme.labelMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              if (_searchQuery.isNotEmpty)
                _buildFilterChip(
                  'Busca: "$_searchQuery"',
                  () => setState(() {
                    _searchQuery = '';
                    _searchController.clear();
                  }),
                ),
              if (_selectedCategory != null)
                _buildFilterChip(
                  'Filtro: $_selectedCategory',
                  () => setState(() => _selectedCategory = null),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, VoidCallback onRemove) {
    final theme = Theme.of(context);
    
    return Chip(
      label: Text(
        label,
        style: theme.textTheme.labelSmall,
      ),
      deleteIcon: const Icon(Icons.close, size: 16),
      onDeleted: onRemove,
      backgroundColor: theme.colorScheme.primaryContainer,
      deleteIconColor: theme.colorScheme.onPrimaryContainer,
    );
  }

  Widget _buildMenuGrid(List<MenuItemModel> items) {
    if (items.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(menuProvider.notifier).loadMenu();
      },
      child: GridView.builder(
        padding: const EdgeInsets.all(16),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.75,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
        ),
        itemCount: items.length,
        itemBuilder: (context, index) {
          final item = items[index];
          return MenuItemCard(
            item: item,
            onTap: () => _showItemDetails(item),
            onToggleAvailability: () => _toggleAvailability(item),
            onEdit: () => _showEditItemDialog(item),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    final theme = Theme.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.restaurant_menu_outlined,
            size: 64,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'Nenhum item encontrado',
            style: theme.textTheme.titleLarge?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Adicione itens ao menu ou ajuste os filtros',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _showAddItemDialog,
            icon: const Icon(Icons.add),
            label: const Text('Adicionar Item'),
          ),
        ],
      ),
    );
  }

  List<MenuItemModel> _applyFilters(List<MenuItemModel> items) {
    var filteredItems = items;

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filteredItems = filteredItems.where((item) {
        return item.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               item.description.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               item.category.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
    }

    // Apply category filter
    if (_selectedCategory != null) {
      switch (_selectedCategory) {
        case 'available':
          filteredItems = filteredItems.where((item) => item.isAvailable).toList();
          break;
        case 'unavailable':
          filteredItems = filteredItems.where((item) => !item.isAvailable).toList();
          break;
        case 'vegetarian':
          // TODO: Add isVegetarian field to MenuItemModel if needed
          // filteredItems = filteredItems.where((item) => item.isVegetarian).toList();
          break;
        case 'vegan':
          // TODO: Add isVegan field back to MenuItemModel
          // filteredItems = filteredItems.where((item) => item.isVegan).toList();
          break;
        case 'gluten_free':
          // filteredItems = filteredItems.where((item) => item.isGlutenFree).toList(); // TODO: Add isGlutenFree field to MenuItemModel
          break;
      }
    }

    return filteredItems;
  }

  void _onFilterSelected(String filter) {
    setState(() {
      if (filter == 'all') {
        _selectedCategory = null;
      } else {
        _selectedCategory = filter;
      }
    });
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Buscar no Menu'),
        content: TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            hintText: 'Digite o nome, descrição ou categoria...',
            prefixIcon: Icon(Icons.search),
          ),
          autofocus: true,
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _searchQuery = '';
                _searchController.clear();
              });
              Navigator.pop(context);
            },
            child: const Text('Limpar'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fechar'),
          ),
        ],
      ),
    );
  }

  void _showItemDetails(MenuItemModel item) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => MenuItemDetailsBottomSheet(item: item),
    );
  }

  void _toggleAvailability(MenuItemModel item) {
    ref.read(menuProvider.notifier).toggleItemAvailability(item.id);
  }

  void _showAddItemDialog() {
    showDialog(
      context: context,
      builder: (context) => const AddMenuItemDialog(),
    );
  }

  void _showEditItemDialog(MenuItemModel item) {
    showDialog(
      context: context,
      builder: (context) => EditMenuItemDialog(item: item),
    );
  }
}

class MenuItemCard extends StatelessWidget {
  final MenuItemModel item;
  final VoidCallback? onTap;
  final VoidCallback? onToggleAvailability;
  final VoidCallback? onEdit;

  const MenuItemCard({
    super.key,
    required this.item,
    this.onTap,
    this.onToggleAvailability,
    this.onEdit,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: item.isAvailable
                  ? theme.colorScheme.outline.withValues(alpha: 0.2)
                  : Colors.red.withValues(alpha: 0.3),
            ),
            boxShadow: [
              BoxShadow(
                color: theme.colorScheme.shadow.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image placeholder
              Expanded(
                flex: 3,
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(12),
                      topRight: Radius.circular(12),
                    ),
                  ),
                  child: Stack(
                    children: [
                      Center(
                        child: Icon(
                          Icons.restaurant,
                          size: 32,
                          color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                        ),
                      ),
                      
                      // Availability indicator
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: item.isAvailable ? Colors.green : Colors.red,
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                      
                      // Edit button
                      if (onEdit != null)
                        Positioned(
                          top: 8,
                          left: 8,
                          child: GestureDetector(
                            onTap: onEdit,
                            child: Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: theme.colorScheme.surface.withValues(alpha: 0.9),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.edit,
                                size: 16,
                                color: theme.colorScheme.primary,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
              
              // Content
              Expanded(
                flex: 2,
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Name and price
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              item.name,
                              style: theme.textTheme.titleSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: item.isAvailable
                                    ? theme.colorScheme.onSurface
                                    : theme.colorScheme.onSurface.withValues(alpha: 0.5),
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Text(
                            'R\$ ${item.price.toStringAsFixed(2)}',
                            style: theme.textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.primary,
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 4),
                      
                      // Description
                      Expanded(
                        child: Text(
                          item.description,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      
                      const SizedBox(height: 8),
                      
                      // Tags and availability toggle
                      Row(
                        children: [
                          // Dietary tags
                          Expanded(
                            child: Wrap(
                              spacing: 4,
                              // TODO: Add dietaryTags field to MenuItemModel
                              children: <Widget>[], // item.dietaryTags.take(2).map((tag) => Container(...)).toList()
                            ),
                          ),
                          
                          // Availability toggle
                          if (onToggleAvailability != null)
                            GestureDetector(
                              onTap: onToggleAvailability,
                              child: Icon(
                                item.isAvailable
                                    ? Icons.visibility
                                    : Icons.visibility_off,
                                size: 20,
                                color: item.isAvailable
                                    ? Colors.green
                                    : Colors.red,
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }


}

class MenuItemDetailsBottomSheet extends StatelessWidget {
  final MenuItemModel item;

  const MenuItemDetailsBottomSheet({
    super.key,
    required this.item,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Expanded(
                child: Text(
                  item.name,
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Text(
                'R\$ ${item.price.toStringAsFixed(2)}',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // Category and availability
          Row(
            children: [
              Chip(
                label: Text(item.category),
                backgroundColor: theme.colorScheme.primaryContainer,
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: item.isAvailable
                      ? Colors.green.withValues(alpha: 0.1)
                      : Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  item.isAvailable ? 'Disponível' : 'Indisponível',
                  style: theme.textTheme.labelMedium?.copyWith(
                    color: item.isAvailable ? Colors.green : Colors.red,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Description
          Text(
            'Descrição',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            item.description,
            style: theme.textTheme.bodyMedium,
          ),
          
          const SizedBox(height: 16),
          
          // TODO: Add dietaryTags field to MenuItemModel
          
          // Preparation time
          if (item.preparationTime > 0) ...[
            Row(
              children: [
                Icon(
                  Icons.timer,
                  size: 20,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
                const SizedBox(width: 8),
                Text(
                  'Tempo de preparo: ${item.preparationTime} minutos',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
          ],
          
          // Allergens
          if (item.allergens != null && item.allergens!.isNotEmpty) ...[
            Text(
              'Alérgenos',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              (item.allergens ?? []).join(', '),
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 16),
          ],
          
          // Modifiers
          if (item.modifiers != null && item.modifiers!.isNotEmpty) ...[
            Text(
              'Modificadores Disponíveis',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ...(item.modifiers ?? []).map((modifier) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    modifier.name,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  ...modifier.options.map((option) => Padding(
                    padding: const EdgeInsets.only(left: 16, bottom: 2),
                    child: Text(
                      '• $option',
                      style: theme.textTheme.bodySmall,
                    ),
                  )),
                ],
              ),
            )),
          ],
          
          const SizedBox(height: 24),
          
          // Close button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Fechar'),
            ),
          ),
        ],
      ),
    );
  }


}

class AddMenuItemDialog extends ConsumerStatefulWidget {
  const AddMenuItemDialog({super.key});

  @override
  ConsumerState<AddMenuItemDialog> createState() => _AddMenuItemDialogState();
}

class _AddMenuItemDialogState extends ConsumerState<AddMenuItemDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _preparationTimeController = TextEditingController();
  
  String _selectedCategory = MenuCategories.appetizers;
  // bool _isVegetarian = false; // TODO: Add to MenuItemModel if needed
  // bool _isVegan = false; // TODO: Add isVegan field back to MenuItemModel
  bool _isGlutenFree = false;
  bool _isSpicy = false;

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _preparationTimeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AlertDialog(
      title: const Text('Adicionar Item ao Menu'),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CustomTextField(
                  controller: _nameController,
                  label: 'Nome do Item',
                  validator: (value) {
                    if (value?.isEmpty ?? true) {
                      return 'Nome é obrigatório';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 16),
                
                CustomTextField(
                  controller: _descriptionController,
                  label: 'Descrição',
                  maxLines: 3,
                  validator: (value) {
                    if (value?.isEmpty ?? true) {
                      return 'Descrição é obrigatória';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 16),
                
                Row(
                  children: [
                    Expanded(
                      child: CustomTextField(
                        controller: _priceController,
                        label: 'Preço (R\$)',
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value?.isEmpty ?? true) {
                            return 'Preço é obrigatório';
                          }
                          if (double.tryParse(value!) == null) {
                            return 'Preço inválido';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: CustomTextField(
                        controller: _preparationTimeController,
                        label: 'Tempo (min)',
                        keyboardType: TextInputType.number,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                DropdownButtonFormField<String>(
                  value: _selectedCategory,
                  decoration: const InputDecoration(
                    labelText: 'Categoria',
                    border: OutlineInputBorder(),
                  ),
                  items: MenuCategories.all.map((category) {
                    return DropdownMenuItem(
                      value: category,
                      child: Text(category),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedCategory = value!;
                    });
                  },
                ),
                
                const SizedBox(height: 16),
                
                Text(
                  'Informações Dietéticas',
                  style: theme.textTheme.titleMedium,
                ),
                
                // TODO: Add isVegetarian field to MenuItemModel if needed
                // CheckboxListTile(
                //   title: const Text('Vegetariano'),
                //   value: _isVegetarian,
                //   onChanged: (value) {
                //     setState(() {
                //       _isVegetarian = value ?? false;
                //     });
                //   },
                //   controlAffinity: ListTileControlAffinity.leading,
                // ),
                
                // TODO: Add isVegan field back to MenuItemModel
                // CheckboxListTile(
                //   title: const Text('Vegano'),
                //   value: _isVegan,
                //   onChanged: (value) {
                //     setState(() {
                //       _isVegan = value ?? false;
                //       // if (_isVegan) _isVegetarian = true; // TODO: Add isVegetarian field
                //     });
                //   },
                //   controlAffinity: ListTileControlAffinity.leading,
                // ),
                
                CheckboxListTile(
                  title: const Text('Sem Glúten'),
                  value: _isGlutenFree,
                  onChanged: (value) {
                    setState(() {
                      _isGlutenFree = value ?? false;
                    });
                  },
                  controlAffinity: ListTileControlAffinity.leading,
                ),
                
                CheckboxListTile(
                  title: const Text('Picante'),
                  value: _isSpicy,
                  onChanged: (value) {
                    setState(() {
                      _isSpicy = value ?? false;
                    });
                  },
                  controlAffinity: ListTileControlAffinity.leading,
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancelar'),
        ),
        ElevatedButton(
          onPressed: _saveItem,
          child: const Text('Salvar'),
        ),
      ],
    );
  }

  void _saveItem() {
    if (_formKey.currentState?.validate() ?? false) {
      final item = MenuItemModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: _nameController.text,
        description: _descriptionController.text,
        price: double.parse(_priceController.text),
        category: _selectedCategory,
        createdAt: DateTime.now(),
        preparationTime: int.tryParse(_preparationTimeController.text) ?? 0,
      );
      
      ref.read(menuProvider.notifier).createMenuItem(item);
      Navigator.pop(context);
    }
  }
}

class EditMenuItemDialog extends ConsumerStatefulWidget {
  final MenuItemModel item;
  
  const EditMenuItemDialog({
    super.key,
    required this.item,
  });

  @override
  ConsumerState<EditMenuItemDialog> createState() => _EditMenuItemDialogState();
}

class _EditMenuItemDialogState extends ConsumerState<EditMenuItemDialog> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _nameController;
  late final TextEditingController _descriptionController;
  late final TextEditingController _priceController;
  late final TextEditingController _preparationTimeController;
  
  late String _selectedCategory;
  // late bool _isVegetarian; // TODO: Add to MenuItemModel
  // late bool _isVegan; // TODO: Add isVegan field back to MenuItemModel
  late bool _isGlutenFree;
  late bool _isSpicy;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.item.name);
    _descriptionController = TextEditingController(text: widget.item.description);
    _priceController = TextEditingController(text: widget.item.price.toString());
    _preparationTimeController = TextEditingController(
      text: widget.item.preparationTime.toString(),
    );
    
    _selectedCategory = widget.item.category;
    // _isVegetarian = widget.item.isVegetarian; // TODO: Add to MenuItemModel
    // _isVegan = widget.item.isVegan; // TODO: Add isVegan field back to MenuItemModel
    // _isGlutenFree = widget.item.isGlutenFree; // TODO: Add isGlutenFree field to MenuItemModel
    _isGlutenFree = false; // Default value since field doesn't exist yet
    // _isSpicy = widget.item.isSpicy; // TODO: Add isSpicy field to MenuItemModel
    _isSpicy = false; // Default value since field doesn't exist yet
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _preparationTimeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AlertDialog(
      title: const Text('Editar Item do Menu'),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CustomTextField(
                  controller: _nameController,
                  label: 'Nome do Item',
                  validator: (value) {
                    if (value?.isEmpty ?? true) {
                      return 'Nome é obrigatório';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 16),
                
                CustomTextField(
                  controller: _descriptionController,
                  label: 'Descrição',
                  maxLines: 3,
                  validator: (value) {
                    if (value?.isEmpty ?? true) {
                      return 'Descrição é obrigatória';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 16),
                
                Row(
                  children: [
                    Expanded(
                      child: CustomTextField(
                        controller: _priceController,
                        label: 'Preço (R\$)',
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value?.isEmpty ?? true) {
                            return 'Preço é obrigatório';
                          }
                          if (double.tryParse(value!) == null) {
                            return 'Preço inválido';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: CustomTextField(
                        controller: _preparationTimeController,
                        label: 'Tempo (min)',
                        keyboardType: TextInputType.number,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                DropdownButtonFormField<String>(
                  value: _selectedCategory,
                  decoration: const InputDecoration(
                    labelText: 'Categoria',
                    border: OutlineInputBorder(),
                  ),
                  items: MenuCategories.all.map((category) {
                    return DropdownMenuItem(
                      value: category,
                      child: Text(category),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedCategory = value!;
                    });
                  },
                ),
                
                const SizedBox(height: 16),
                
                Text(
                  'Informações Dietéticas',
                  style: theme.textTheme.titleMedium,
                ),
                
                // TODO: Add isVegetarian field to MenuItemModel if needed
                // CheckboxListTile(
                //   title: const Text('Vegetariano'),
                //   value: _isVegetarian,
                //   onChanged: (value) {
                //     setState(() {
                //       _isVegetarian = value ?? false;
                //     });
                //   },
                //   controlAffinity: ListTileControlAffinity.leading,
                // ),
                
                // TODO: Add isVegan field back to MenuItemModel
                // CheckboxListTile(
                //   title: const Text('Vegano'),
                //   value: _isVegan,
                //   onChanged: (value) {
                //     setState(() {
                //       _isVegan = value ?? false;
                //       // if (_isVegan) _isVegetarian = true; // TODO: Add isVegetarian field
                //     });
                //   },
                //   controlAffinity: ListTileControlAffinity.leading,
                // ),
                
                CheckboxListTile(
                  title: const Text('Sem Glúten'),
                  value: _isGlutenFree,
                  onChanged: (value) {
                    setState(() {
                      _isGlutenFree = value ?? false;
                    });
                  },
                  controlAffinity: ListTileControlAffinity.leading,
                ),
                
                CheckboxListTile(
                  title: const Text('Picante'),
                  value: _isSpicy,
                  onChanged: (value) {
                    setState(() {
                      _isSpicy = value ?? false;
                    });
                  },
                  controlAffinity: ListTileControlAffinity.leading,
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancelar'),
        ),
        TextButton(
          onPressed: () => _deleteItem(),
          child: Text(
            'Excluir',
            style: TextStyle(color: theme.colorScheme.error),
          ),
        ),
        ElevatedButton(
          onPressed: _saveItem,
          child: const Text('Salvar'),
        ),
      ],
    );
  }

  void _saveItem() {
    if (_formKey.currentState?.validate() ?? false) {
      final updatedItem = widget.item.copyWith(
        name: _nameController.text,
        description: _descriptionController.text,
        price: double.parse(_priceController.text),
        category: _selectedCategory,
        // isVegetarian: _isVegetarian, // TODO: Add to MenuItemModel
        // isVegan: _isVegan, // TODO: Add to MenuItemModel
        // isGlutenFree: _isGlutenFree, // TODO: Add isGlutenFree field back to MenuItemModel
        // isSpicy: _isSpicy, // TODO: Add isSpicy field back to MenuItemModel
        preparationTime: int.tryParse(_preparationTimeController.text) ?? 0,
      );
      
      ref.read(menuProvider.notifier).updateMenuItem(updatedItem);
      Navigator.pop(context);
    }
  }

  void _deleteItem() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmar Exclusão'),
        content: Text('Tem certeza que deseja excluir "${widget.item.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancelar'),
          ),
          TextButton(
            onPressed: () {
              ref.read(menuProvider.notifier).deleteMenuItem(widget.item.id);
              Navigator.pop(context); // Close confirmation dialog
              Navigator.pop(context); // Close edit dialog
            },
            child: Text(
              'Excluir',
              style: TextStyle(color: Theme.of(context).colorScheme.error),
            ),
          ),
        ],
      ),
    );
  }
}