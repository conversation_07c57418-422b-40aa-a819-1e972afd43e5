import uuid
from typing import List, Optional, Annotated  # Import Annotated
from fastapi import (
    APIRouter,
    Depends,
    HTTPException,
    status,
    Query,
    Path,
)  # Import Path

from sqlalchemy.ext.asyncio import AsyncSession  # noqa: E402

from app.core.db_dependencies import get_db  # noqa: E402
from app.modules.core.tenants.dependencies.tenant_dependencies import (  # noqa: E402
    get_current_tenant_from_header as get_current_tenant,
    require_tenant_role,  # Corrigido para importar a dependência correta
)
from app.core.dependencies import (  # noqa: E402
    # Import service dependencies
    get_product_service,
    get_online_order_service,
)
from app.modules.core.users.models.user import User  # noqa: E402
from app.modules.core.tenants.models.tenant import Tenant
from app.modules.core.roles.models.roles import RolePermissions  # Importar Roles
from app.core.exceptions import NotFoundError, BusinessLogicError
from app.core.i18n import _

# Importar a instância do serviço de associação, não a classe, se for usar
# como dependência singleton
from app.modules.core.users.services.tenant_user_association_service import (
    tenant_user_association_service,
)  # noqa: E402

# Import Service Classes (not instances)
from app.modules.tenants.shops.services.product_service import ProductService  # noqa: E402
from app.modules.tenants.shops.services.online_order_service import OnlineOrderService

# Schemas remain the same
from app.modules.tenants.shops.schemas import product as product_schema  # noqa: E402
from app.modules.tenants.shops.schemas import online_order as order_schema

# Remove direct service instantiation - will use Depends

# Define roles for clarity using RolePermissions
# VIEW_ROLES inclui costumer, STAFF, MANAGER, OWNER. É adequado para
# quando um consumidor precisa de acesso.
costumer_access_roles = RolePermissions.VIEW_ROLES
staff_roles = RolePermissions.STAFF_ROLES
admin_roles = RolePermissions.ADMIN_ROLES

router = APIRouter(
    prefix="/store",  # Changed prefix
    tags=["Shops - Online Store"],
)

# =============================================
# Public/costumer Endpoints
# =============================================

# Endpoint for listing products (Public View - only active)


@router.get(
    "/{tenant_id_or_slug}/products/",  # Tenant identified in path, slug might be better
    response_model=List[product_schema.ProductRead],
    summary="List Products (Public)",
    description="Get a list of active products available for sale in the online store.",
    # No authentication required
)
async def read_products_public(
    # Get tenant identifier from path
    tenant_id_or_slug: Annotated[str, Path(..., description="Tenant ID or unique slug")],
    # Query parameters
    # Dependencies first (reordered to satisfy linter)
    db: Annotated[AsyncSession, Depends(get_db)],
    product_service: Annotated[ProductService, Depends(get_product_service)],
    # TODO: Need a way to resolve tenant_id_or_slug to an actual tenant_id UUID.
    # This could be another dependency: get_tenant_by_slug_or_id or a service call.
    # For now, assuming tenant_id_or_slug IS the UUID for simplicity.
    # Query parameters next
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),  # Lower default/max for public
    name: Optional[str] = Query(
        None, description="Filter by product name (case-insensitive, partial match)"
    ),
    category_id: Optional[uuid.UUID] = Query(None, description="Filter by category ID"),
    is_featured: Optional[bool] = Query(None, description="Filter by featured status"),
):
    # TODO: Implement robust tenant resolution by slug or ID.
    # For now, we'll attempt UUID conversion and proceed if valid.
    # A proper implementation would involve a service call to fetch tenant by slug/ID
    # and ensure it's active before proceeding.
    try:
        # This is a placeholder. A real lookup is needed.
        # Example: tenant = await tenant_service.get_tenant_by_slug_or_id(db, tenant_id_or_slug)
        # if not tenant or not tenant.is_active:
        #     raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Tenant not found or not active.")  # noqa: E501
        # tenant_uuid = tenant.id
        tenant_uuid = uuid.UUID(tenant_id_or_slug)  # Simplified: assumes it's a UUID for now
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid tenant identifier format.",
        )

    # Ensure tenant exists (basic check, ideally service layer handles this better)
    # This is a simplified check. A real service might do more.
    # temp_tenant_service = TenantService() # Not ideal to instantiate here
    # resolved_tenant = await temp_tenant_service.get_tenant(db=db, tenant_id=tenant_uuid)
    # if not resolved_tenant or not resolved_tenant.is_active:
    #     raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Tenant not found or not active.")  # noqa: E501

    products = await product_service.get_products(
        db=db,
        tenant_id=tenant_uuid,
        skip=skip,
        limit=limit,
        name=name,
        sku=None,
        category_id=category_id,
        is_active=True,  # IMPORTANT: Only active products
        is_featured=is_featured,
    )
    return products


# =============================================
# Order Endpoints (costumer & Admin)
# =============================================


@router.post(
    "/orders/",
    response_model=order_schema.OnlineOrderRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create Online Order",
    description="Create a new online order (requires authenticated costumer).",
    # Use require_role or just get_current_active_user if any logged-in user can order
)
async def create_online_order(
    order_in: order_schema.OnlineOrderCreate,  # Body
    # Dependencies
    db: Annotated[AsyncSession, Depends(get_db)],
    current_tenant: Annotated[Tenant, Depends(get_current_tenant)],  # Tenant from X-Tenant-ID
    current_user: Annotated[
        User,
        Depends(
            require_tenant_role(required_roles=costumer_access_roles, tenant_id_source="header")
        ),
    ],
    online_order_service: Annotated[OnlineOrderService, Depends(get_online_order_service)],
):
    try:
        # product_service is now injected into online_order_service via DI
        order = await online_order_service.create_order(
            db=db,
            order_in=order_in,
            tenant_id=current_tenant.id,
            costumer_id=current_user.id,
            # No need to pass product_service here anymore
        )
        return order
    except (
        NotFoundError,
        BusinessLogicError,
        ValueError,
    ) as e:  # Removed NotImplementedError if resolved
        # Catch specific business logic errors, validation errors, or missing implementations
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception:
        # Log error e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("Failed to create order"),
        )


@router.get(
    "/my-orders/",
    response_model=List[order_schema.OnlineOrderRead],
    summary="List My Orders",
    description="Get a list of orders placed by the currently authenticated user.",
)
async def read_my_orders(
    # Dependencies first (reordered to satisfy linter)
    db: Annotated[AsyncSession, Depends(get_db)],
    current_tenant: Annotated[Tenant, Depends(get_current_tenant)],  # Tenant from X-Tenant-ID
    current_user: Annotated[
        User,
        Depends(
            require_tenant_role(required_roles=costumer_access_roles, tenant_id_source="header")
        ),
    ],
    online_order_service: Annotated[OnlineOrderService, Depends(get_online_order_service)],
    # Query parameters next
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
):
    orders = await online_order_service.get_orders_for_costumer(
        db=db,
        tenant_id=current_tenant.id,
        costumer_id=current_user.id,
        skip=skip,
        limit=limit,
    )
    return orders


@router.get(
    "/orders/{order_id}",
    response_model=order_schema.OnlineOrderRead,
    summary="Get Order Details",
    description="Get details of a specific order by ID. Users can only see their own orders unless they have staff/admin roles.",  # noqa: E501
)
async def read_order(
    # Path param
    order_id: Annotated[uuid.UUID, Path(..., description="ID of the order to retrieve")],
    # Dependencies
    db: Annotated[AsyncSession, Depends(get_db)],
    current_tenant: Annotated[Tenant, Depends(get_current_tenant)],  # Tenant from X-Tenant-ID
    current_user: Annotated[
        User,
        Depends(
            require_tenant_role(
                required_roles=RolePermissions.VIEW_ROLES,
                tenant_id_source="header",
            )
        ),
    ],
    online_order_service: Annotated[OnlineOrderService, Depends(get_online_order_service)],
    # tenant_user_association_service is already imported globally
):
    user_tenant_role_str = await tenant_user_association_service.get_user_tenant_role(
        db=db, user_id=current_user.id, tenant_id=current_tenant.id
    )

    # System Admins or users with Staff roles in the tenant can see any order in that tenant
    can_view_any_order = current_user.system_role == "admin" or (  # SystemRole.ADMIN.value
        user_tenant_role_str
        and RolePermissions.has_permission(user_tenant_role_str, RolePermissions.STAFF_ROLES)
    )

    costumer_id_filter = None if can_view_any_order else current_user.id

    order = await online_order_service.get_order_by_id(
        db=db,
        order_id=order_id,
        tenant_id=current_tenant.id,
        costumer_id=costumer_id_filter,
    )
    if not order:
        # Use HTTPException for standard FastAPI error handling
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Order not found or access denied",
        )
    return order


@router.get(
    "/admin/orders/",  # Separate path for admin listing
    response_model=List[order_schema.OnlineOrderRead],
    summary="List All Orders (Admin)",
    description="Get a list of all online orders for the current tenant.",
    dependencies=[
        Depends(require_tenant_role(required_roles=staff_roles, tenant_id_source="header"))
    ],
)
async def read_all_orders_admin(
    # Query parameters
    # Dependencies first (reordered to satisfy linter)
    db: Annotated[AsyncSession, Depends(get_db)],
    current_tenant: Annotated[Tenant, Depends(get_current_tenant)],
    online_order_service: Annotated[OnlineOrderService, Depends(get_online_order_service)],
    # current_user is implicitly validated by require_role
    # Query parameters next
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=200),
    status_filter: Optional[str] = Query(
        None, alias="status", description="Filter orders by status"
    ),
):
    orders = await online_order_service.get_orders_for_tenant(
        db=db, tenant_id=current_tenant.id, skip=skip, limit=limit, status=status_filter
    )
    return orders


@router.put(
    "/orders/{order_id}/status",
    response_model=order_schema.OnlineOrderRead,
    summary="Update Order Status (Admin)",
    description="Update the status of an existing online order.",
    dependencies=[
        Depends(require_tenant_role(required_roles=admin_roles, tenant_id_source="header"))
    ],
)
async def update_order_status(
    # Path param
    order_id: Annotated[uuid.UUID, Path(..., description="ID of the order to update status")],
    status_in: order_schema.OnlineOrderStatusUpdate,  # Body
    # Dependencies
    db: Annotated[AsyncSession, Depends(get_db)],
    current_tenant: Annotated[Tenant, Depends(get_current_tenant)],
    online_order_service: Annotated[OnlineOrderService, Depends(get_online_order_service)],
    # current_user implicitly validated by require_role
):
    try:
        updated_order = await online_order_service.update_order_status(
            db=db, order_id=order_id, status_in=status_in, tenant_id=current_tenant.id
        )
        return updated_order
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except BusinessLogicError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception:
        # Log error e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update order status",
        )


# Remove duplicated endpoint comments
