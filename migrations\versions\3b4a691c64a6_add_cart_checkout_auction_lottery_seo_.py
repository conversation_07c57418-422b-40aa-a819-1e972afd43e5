"""add_cart_checkout_auction_lottery_seo_systems

Revision ID: 3b4a691c64a6
Revises: 730e15e8ec37
Create Date: 2025-06-27 15:06:22.780914

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3b4a691c64a6'
down_revision: Union[str, None] = '730e15e8ec37'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('seo_meta',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('content_type', sa.String(length=50), nullable=False),
    sa.Column('content_id', sa.UUID(), nullable=False),
    sa.Column('language_code', sa.String(length=10), nullable=False),
    sa.Column('meta_title', sa.String(length=200), nullable=True),
    sa.Column('meta_description', sa.Text(), nullable=True),
    sa.Column('meta_keywords', sa.Text(), nullable=True),
    sa.Column('canonical_url', sa.String(length=500), nullable=True),
    sa.Column('robots_directive', sa.String(length=100), nullable=False),
    sa.Column('og_title', sa.String(length=95), nullable=True),
    sa.Column('og_description', sa.String(length=300), nullable=True),
    sa.Column('og_image_url', sa.String(length=500), nullable=True),
    sa.Column('og_image_alt', sa.String(length=255), nullable=True),
    sa.Column('og_type', sa.String(length=50), nullable=False),
    sa.Column('og_site_name', sa.String(length=100), nullable=True),
    sa.Column('twitter_card_type', sa.String(length=50), nullable=False),
    sa.Column('twitter_title', sa.String(length=70), nullable=True),
    sa.Column('twitter_description', sa.String(length=200), nullable=True),
    sa.Column('twitter_image_url', sa.String(length=500), nullable=True),
    sa.Column('twitter_image_alt', sa.String(length=255), nullable=True),
    sa.Column('twitter_creator', sa.String(length=100), nullable=True),
    sa.Column('twitter_site', sa.String(length=100), nullable=True),
    sa.Column('structured_data', sa.JSON(), nullable=True),
    sa.Column('additional_meta_tags', sa.JSON(), nullable=True),
    sa.Column('focus_keyword', sa.String(length=100), nullable=True),
    sa.Column('keyword_density', sa.String(length=10), nullable=True),
    sa.Column('readability_score', sa.String(length=20), nullable=True),
    sa.Column('seo_score', sa.Integer(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('priority', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('created_by_id', sa.UUID(), nullable=True),
    sa.Column('updated_by_id', sa.UUID(), nullable=True),
    sa.ForeignKeyConstraint(['created_by_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['language_code'], ['languages.code'], ),
    sa.ForeignKeyConstraint(['updated_by_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_seo_meta_content_id'), 'seo_meta', ['content_id'], unique=False)
    op.create_index(op.f('ix_seo_meta_content_type'), 'seo_meta', ['content_type'], unique=False)
    op.create_index(op.f('ix_seo_meta_language_code'), 'seo_meta', ['language_code'], unique=False)
    op.create_table('sitemap_entries',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('content_type', sa.String(length=50), nullable=False),
    sa.Column('content_id', sa.UUID(), nullable=False),
    sa.Column('language_code', sa.String(length=10), nullable=False),
    sa.Column('url', sa.String(length=500), nullable=False),
    sa.Column('priority', sa.Numeric(precision=3, scale=2), nullable=False),
    sa.Column('change_frequency', sa.String(length=20), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('is_indexed', sa.Boolean(), nullable=False),
    sa.Column('title', sa.String(length=200), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('image_url', sa.String(length=500), nullable=True),
    sa.Column('last_modified', sa.DateTime(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('crawl_count', sa.Integer(), nullable=False),
    sa.Column('last_crawled_at', sa.DateTime(), nullable=True),
    sa.Column('created_by_id', sa.UUID(), nullable=True),
    sa.ForeignKeyConstraint(['created_by_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['language_code'], ['languages.code'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_sitemap_entries_content_id'), 'sitemap_entries', ['content_id'], unique=False)
    op.create_index(op.f('ix_sitemap_entries_content_type'), 'sitemap_entries', ['content_type'], unique=False)
    op.create_index(op.f('ix_sitemap_entries_language_code'), 'sitemap_entries', ['language_code'], unique=False)
    op.create_index(op.f('ix_sitemap_entries_url'), 'sitemap_entries', ['url'], unique=False)
    op.create_table('url_slugs',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('content_type', sa.String(length=50), nullable=False),
    sa.Column('content_id', sa.UUID(), nullable=False),
    sa.Column('language_code', sa.String(length=10), nullable=False),
    sa.Column('slug', sa.String(length=255), nullable=False),
    sa.Column('full_path', sa.String(length=500), nullable=True),
    sa.Column('is_primary', sa.Boolean(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('redirect_to_id', sa.UUID(), nullable=True),
    sa.Column('priority', sa.Integer(), nullable=False),
    sa.Column('change_frequency', sa.String(length=20), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('last_accessed_at', sa.DateTime(), nullable=True),
    sa.Column('created_by_id', sa.UUID(), nullable=True),
    sa.ForeignKeyConstraint(['created_by_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['language_code'], ['languages.code'], ),
    sa.ForeignKeyConstraint(['redirect_to_id'], ['url_slugs.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_url_slugs_active', 'url_slugs', ['is_active', 'is_primary'], unique=False)
    op.create_index('ix_url_slugs_content', 'url_slugs', ['content_type', 'content_id'], unique=False)
    op.create_index(op.f('ix_url_slugs_content_id'), 'url_slugs', ['content_id'], unique=False)
    op.create_index(op.f('ix_url_slugs_content_type'), 'url_slugs', ['content_type'], unique=False)
    op.create_index(op.f('ix_url_slugs_language_code'), 'url_slugs', ['language_code'], unique=False)
    op.create_index(op.f('ix_url_slugs_slug'), 'url_slugs', ['slug'], unique=False)
    op.create_index('ix_url_slugs_slug_lang', 'url_slugs', ['slug', 'language_code'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_url_slugs_slug_lang', table_name='url_slugs')
    op.drop_index(op.f('ix_url_slugs_slug'), table_name='url_slugs')
    op.drop_index(op.f('ix_url_slugs_language_code'), table_name='url_slugs')
    op.drop_index(op.f('ix_url_slugs_content_type'), table_name='url_slugs')
    op.drop_index(op.f('ix_url_slugs_content_id'), table_name='url_slugs')
    op.drop_index('ix_url_slugs_content', table_name='url_slugs')
    op.drop_index('ix_url_slugs_active', table_name='url_slugs')
    op.drop_table('url_slugs')
    op.drop_index(op.f('ix_sitemap_entries_url'), table_name='sitemap_entries')
    op.drop_index(op.f('ix_sitemap_entries_language_code'), table_name='sitemap_entries')
    op.drop_index(op.f('ix_sitemap_entries_content_type'), table_name='sitemap_entries')
    op.drop_index(op.f('ix_sitemap_entries_content_id'), table_name='sitemap_entries')
    op.drop_table('sitemap_entries')
    op.drop_index(op.f('ix_seo_meta_language_code'), table_name='seo_meta')
    op.drop_index(op.f('ix_seo_meta_content_type'), table_name='seo_meta')
    op.drop_index(op.f('ix_seo_meta_content_id'), table_name='seo_meta')
    op.drop_table('seo_meta')
    # ### end Alembic commands ###
