# General - Financial Control

**Categoria:** General
**Módulo:** Financial Control
**Total de Endpoints:** 9
**Gerado em:** 27/06/2025, 20:20:26

## 📋 Endpoints

- [GET /api/financial/control/entries](#get-apifinancialcontrolentries) - List Control Entries
- [POST /api/financial/control/entries](#post-apifinancialcontrolentries) - Create Control Entry
- [POST /api/financial/control/entries/bulk-update](#post-apifinancialcontrolentriesbulk-update) - Bulk Update Entries
- [DELETE /api/financial/control/entries/{entry_id}](#delete-apifinancialcontrolentriesentry-id) - Delete Control Entry
- [GET /api/financial/control/entries/{entry_id}](#get-apifinancialcontrolentriesentry-id) - Get Control Entry
- [PUT /api/financial/control/entries/{entry_id}](#put-apifinancialcontrolentriesentry-id) - Update Control Entry
- [PATCH /api/financial/control/entries/{entry_id}/archive](#patch-apifinancialcontrolentriesentry-idarchive) - Archive Entry
- [PATCH /api/financial/control/entries/{entry_id}/mark-paid](#patch-apifinancialcontrolentriesentry-idmark-paid) - Mark Entry As Paid
- [GET /api/financial/control/health](#get-apifinancialcontrolhealth) - Health Check

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### ControlEntryBulkUpdate

**Descrição:** Schema for bulk updating control entries.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `entry_ids` | Array[string] | ✅ | List of entry IDs |
| `updates` | ControlEntryUpdate | ✅ | Updates to apply |

### ControlEntryCreate

**Descrição:** Schema for creating control entries.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `entry_type` | ControlEntryType | ✅ | - |
| `amount` | unknown | ✅ | Entry amount |
| `gross_amount` | unknown | ❌ | Amount before taxes/discounts |
| `net_amount` | unknown | ❌ | Amount after taxes/discounts |
| `tax_amount` | unknown | ❌ | Tax amount |
| `discount_amount` | unknown | ❌ | Discount amount |
| `title` | string | ✅ | Entry title |
| `description` | unknown | ❌ | Entry description |
| `reference_number` | unknown | ❌ | Reference number |
| `external_reference` | unknown | ❌ | External reference |
| `entry_date` | string | ✅ | Entry date |
| `due_date` | unknown | ❌ | Due date |
| `payment_date` | unknown | ❌ | Payment date |
| `category_id` | unknown | ❌ | Category ID |
| `subcategory` | unknown | ❌ | Subcategory |
| `tags` | unknown | ❌ | Tags |
| `supplier_id` | unknown | ❌ | Supplier ID |
| `order_id` | unknown | ❌ | Order ID |
| `payment_method` | unknown | ❌ | Payment method |
| `payment_reference` | unknown | ❌ | Payment reference |
| `bank_account` | unknown | ❌ | Bank account |
| `is_recurring` | boolean | ❌ | Is recurring entry |
| `is_tax_deductible` | boolean | ❌ | Is tax deductible |
| `requires_approval` | boolean | ❌ | Requires approval |
| `notes` | unknown | ❌ | Additional notes |
| `additional_data` | unknown | ❌ | Additional metadata |
| `status` | ControlEntryStatus | ❌ | Entry status |

### ControlEntryList

**Descrição:** Schema for paginated control entry lists.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `items` | Array[ControlEntryResponse] | ✅ | - |
| `total` | integer | ✅ | - |
| `page` | integer | ✅ | - |
| `size` | integer | ✅ | - |
| `pages` | integer | ✅ | - |

### ControlEntryResponse

**Descrição:** Schema for control entry responses.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `entry_type` | ControlEntryType | ✅ | - |
| `amount` | string | ✅ | Entry amount |
| `gross_amount` | unknown | ❌ | Amount before taxes/discounts |
| `net_amount` | unknown | ❌ | Amount after taxes/discounts |
| `tax_amount` | unknown | ❌ | Tax amount |
| `discount_amount` | unknown | ❌ | Discount amount |
| `title` | string | ✅ | Entry title |
| `description` | unknown | ❌ | Entry description |
| `reference_number` | unknown | ❌ | Reference number |
| `external_reference` | unknown | ❌ | External reference |
| `entry_date` | string | ✅ | Entry date |
| `due_date` | unknown | ❌ | Due date |
| `payment_date` | unknown | ❌ | Payment date |
| `category_id` | unknown | ❌ | Category ID |
| `subcategory` | unknown | ❌ | Subcategory |
| `tags` | unknown | ❌ | Tags |
| `supplier_id` | unknown | ❌ | Supplier ID |
| `order_id` | unknown | ❌ | Order ID |
| `payment_method` | unknown | ❌ | Payment method |
| `payment_reference` | unknown | ❌ | Payment reference |
| `bank_account` | unknown | ❌ | Bank account |
| `is_recurring` | boolean | ❌ | Is recurring entry |
| `is_tax_deductible` | boolean | ❌ | Is tax deductible |
| `requires_approval` | boolean | ❌ | Requires approval |
| `notes` | unknown | ❌ | Additional notes |
| `additional_data` | unknown | ❌ | Additional metadata |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `status` | ControlEntryStatus | ✅ | - |
| `transaction_id` | unknown | ❌ | - |
| `is_automated` | boolean | ✅ | - |
| `is_overdue` | boolean | ✅ | - |
| `calculated_net_amount` | string | ✅ | - |
| `approved_by` | unknown | ❌ | - |
| `approved_at` | unknown | ❌ | - |
| `approval_notes` | unknown | ❌ | - |
| `created_by` | string | ✅ | - |
| `updated_by` | unknown | ❌ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `category_name` | unknown | ❌ | - |
| `supplier_name` | unknown | ❌ | - |
| `document_count` | integer | ❌ | - |

### ControlEntryUpdate

**Descrição:** Schema for updating control entries.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `entry_type` | unknown | ❌ | - |
| `status` | unknown | ❌ | - |
| `amount` | unknown | ❌ | - |
| `gross_amount` | unknown | ❌ | - |
| `net_amount` | unknown | ❌ | - |
| `tax_amount` | unknown | ❌ | - |
| `discount_amount` | unknown | ❌ | - |
| `title` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `reference_number` | unknown | ❌ | - |
| `external_reference` | unknown | ❌ | - |
| `entry_date` | unknown | ❌ | - |
| `due_date` | unknown | ❌ | - |
| `payment_date` | unknown | ❌ | - |
| `category_id` | unknown | ❌ | - |
| `subcategory` | unknown | ❌ | - |
| `tags` | unknown | ❌ | - |
| `supplier_id` | unknown | ❌ | - |
| `order_id` | unknown | ❌ | - |
| `payment_method` | unknown | ❌ | - |
| `payment_reference` | unknown | ❌ | - |
| `bank_account` | unknown | ❌ | - |
| `is_recurring` | unknown | ❌ | - |
| `is_tax_deductible` | unknown | ❌ | - |
| `requires_approval` | unknown | ❌ | - |
| `notes` | unknown | ❌ | - |
| `additional_data` | unknown | ❌ | - |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/financial/control/entries {#get-apifinancialcontrolentries}

**Resumo:** List Control Entries
**Descrição:** List financial control entries with filtering and pagination.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `entry_type` | string | query | ❌ | - |
| `status` | string | query | ❌ | - |
| `category_id` | string | query | ❌ | - |
| `supplier_id` | string | query | ❌ | - |
| `date_from` | string | query | ❌ | - |
| `date_to` | string | query | ❌ | - |
| `amount_min` | string | query | ❌ | - |
| `amount_max` | string | query | ❌ | - |
| `search` | string | query | ❌ | - |
| `is_overdue` | string | query | ❌ | - |
| `sort_by` | string | query | ❌ | - |
| `sort_order` | string | query | ❌ | - |
| `page` | integer | query | ❌ | - |
| `size` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ControlEntryList](#controlentrylist)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/control/entries"
```

---

### POST /api/financial/control/entries {#post-apifinancialcontrolentries}

**Resumo:** Create Control Entry
**Descrição:** Create a new financial control entry.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ControlEntryCreate](#controlentrycreate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ControlEntryResponse](#controlentryresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/financial/control/entries" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/financial/control/entries/bulk-update {#post-apifinancialcontrolentriesbulk-update}

**Resumo:** Bulk Update Entries
**Descrição:** Bulk update financial control entries.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ControlEntryBulkUpdate](#controlentrybulkupdate)

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/financial/control/entries/bulk-update" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/financial/control/entries/{entry_id} {#delete-apifinancialcontrolentriesentry-id}

**Resumo:** Delete Control Entry
**Descrição:** Delete a financial control entry.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `entry_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/financial/control/entries/{entry_id}"
```

---

### GET /api/financial/control/entries/{entry_id} {#get-apifinancialcontrolentriesentry-id}

**Resumo:** Get Control Entry
**Descrição:** Get a financial control entry by ID.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `entry_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ControlEntryResponse](#controlentryresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/control/entries/{entry_id}"
```

---

### PUT /api/financial/control/entries/{entry_id} {#put-apifinancialcontrolentriesentry-id}

**Resumo:** Update Control Entry
**Descrição:** Update a financial control entry.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `entry_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ControlEntryUpdate](#controlentryupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ControlEntryResponse](#controlentryresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/financial/control/entries/{entry_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PATCH /api/financial/control/entries/{entry_id}/archive {#patch-apifinancialcontrolentriesentry-idarchive}

**Resumo:** Archive Entry
**Descrição:** Archive a financial control entry.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `entry_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ControlEntryResponse](#controlentryresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PATCH "http://localhost:8000/api/financial/control/entries/{entry_id}/archive" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PATCH /api/financial/control/entries/{entry_id}/mark-paid {#patch-apifinancialcontrolentriesentry-idmark-paid}

**Resumo:** Mark Entry As Paid
**Descrição:** Mark an entry as paid.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `entry_id` | string | path | ✅ | - |
| `payment_date` | string | query | ❌ | - |
| `payment_method` | string | query | ❌ | - |
| `payment_reference` | string | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ControlEntryResponse](#controlentryresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PATCH "http://localhost:8000/api/financial/control/entries/{entry_id}/mark-paid" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/financial/control/health {#get-apifinancialcontrolhealth}

**Resumo:** Health Check
**Descrição:** Health check for financial control module.

**🔐 Autenticação:** Não requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/control/health"
```

---
