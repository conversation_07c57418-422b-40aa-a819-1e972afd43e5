name: orderpad
description: "Sistema de pedidos para restaurantes - Interface para garçons e proprietários"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter

  # State Management
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3

  # Navigation
  go_router: ^12.1.3

  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.2.2

  # UI Components
  cupertino_icons: ^1.0.6
  google_fonts: ^6.1.0
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  lottie: ^2.7.0

  # Utilities
  intl: ^0.19.0
  uuid: ^4.2.1
  equatable: ^2.0.5
  json_annotation: ^4.8.1

  # Networking (for future use)
  dio: ^5.4.0
  connectivity_plus: ^5.0.2

  # Device Features
  permission_handler: ^11.1.0
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0

  # Printing (for receipts)
  printing: ^5.11.1
  pdf: ^3.10.7

  # Image Handling
  image_picker: ^1.0.4
  image: ^4.1.3

  # Charts and Graphs
  fl_chart: ^0.65.0

  # Date and Time
  table_calendar: ^3.0.9

  # Form Validation
  formz: ^0.6.1

  # Animations
  animations: ^2.0.8

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Code Generation
  build_runner: ^2.4.7
  hive_generator: ^2.0.1
  json_serializable: ^6.7.1
  riverpod_generator: ^2.3.9

  # Linting
  flutter_lints: ^3.0.1
  very_good_analysis: ^5.1.0

  # Testing
  mocktail: ^1.0.2
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/mock_data/

  # Fonts
  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
          weight: 400
        - asset: assets/fonts/Roboto-Medium.ttf
          weight: 500
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700

# Flutter configuration
flutter_gen:
  output: lib/core/gen/
  line_length: 80

  integrations:
    flutter_svg: true
    lottie: true

  assets:
    enabled: true
    package_parameter_enabled: false
    style: dot-delimiter

  fonts:
    enabled: true

  colors:
    enabled: true
    inputs:
      - assets/colors/colors.xml