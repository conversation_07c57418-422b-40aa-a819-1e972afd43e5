"""
Notification Queue API

Endpoints para gerenciamento da fila de notificações.
"""

import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.role_auth import require_admin_user
from app.modules.core.users.models.user import User

from ..schemas import NotificationQueueResponse
from ..services import NotificationQueueService

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/stats")
async def get_queue_stats(
    current_user: User = Depends(require_admin_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Obtém estatísticas da fila de notificações.
    
    Apenas para administradores.
    """
    service = NotificationQueueService(db)
    
    stats = await service.get_queue_stats()
    
    return stats


@router.get("/entries", response_model=List[NotificationQueueResponse])
async def list_queue_entries(
    status: Optional[str] = Query(None),
    limit: int = Query(100, ge=1, le=1000),
    current_user: User = Depends(require_admin_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Lista entradas da fila de notificações.
    
    Apenas para administradores.
    """
    service = NotificationQueueService(db)
    
    entries = await service.get_queue_entries(status, limit)
    
    return entries


@router.post("/process-batch")
async def process_batch(
    batch_size: int = Query(10, ge=1, le=100),
    current_user: User = Depends(require_admin_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Processa um lote de notificações da fila.
    
    Apenas para administradores.
    """
    service = NotificationQueueService(db)
    
    batch = await service.get_next_batch(batch_size)
    
    # TODO: Implementar processamento real das notificações
    # Por enquanto, apenas marca como concluídas
    processed = 0
    for entry in batch:
        success = await service.mark_as_completed(entry.id)
        if success:
            processed += 1
    
    return {
        "message": f"Processadas {processed} notificações",
        "processed": processed,
        "batch_size": len(batch)
    }


@router.post("/retry-failed")
async def retry_failed_notifications(
    max_age_hours: int = Query(24, ge=1, le=168),  # Máximo 1 semana
    current_user: User = Depends(require_admin_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Recoloca notificações falhadas na fila para retry.
    
    Apenas para administradores.
    """
    service = NotificationQueueService(db)
    
    count = await service.retry_failed_notifications(max_age_hours)
    
    return {
        "message": f"Recolocadas {count} notificações na fila",
        "count": count
    }


@router.post("/cleanup")
async def cleanup_old_entries(
    days_old: int = Query(30, ge=1, le=365),
    current_user: User = Depends(require_admin_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Remove entradas antigas da fila.
    
    Apenas para administradores.
    """
    service = NotificationQueueService(db)
    
    count = await service.cleanup_old_entries(days_old)
    
    return {
        "message": f"Removidas {count} entradas antigas",
        "count": count
    }


@router.get("/health")
async def get_queue_health(
    current_user: User = Depends(require_admin_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Verifica a saúde da fila de notificações.
    
    Apenas para administradores.
    """
    service = NotificationQueueService(db)
    
    stats = await service.get_queue_stats()
    
    # Calcula indicadores de saúde
    total = stats["total"]
    pending = stats["pending"]
    processing = stats["processing"]
    failed = stats["failed"]
    
    # Indicadores
    queue_load = (pending + processing) / max(total, 1) * 100
    error_rate = stats["failure_rate"]
    avg_time = stats["avg_processing_time_seconds"]
    
    # Determina status da saúde
    health_status = "healthy"
    issues = []
    
    if queue_load > 80:
        health_status = "warning"
        issues.append("Alta carga na fila")
    
    if error_rate > 10:
        health_status = "critical" if error_rate > 25 else "warning"
        issues.append(f"Taxa de erro alta: {error_rate:.1f}%")
    
    if avg_time > 300:  # 5 minutos
        health_status = "warning"
        issues.append("Tempo de processamento alto")
    
    if pending > 1000:
        health_status = "critical"
        issues.append("Muitas notificações pendentes")
    
    return {
        "status": health_status,
        "queue_load": round(queue_load, 2),
        "error_rate": error_rate,
        "avg_processing_time": avg_time,
        "pending_count": pending,
        "processing_count": processing,
        "failed_count": failed,
        "issues": issues,
        "recommendations": _get_health_recommendations(health_status, issues)
    }


def _get_health_recommendations(status: str, issues: List[str]) -> List[str]:
    """Gera recomendações baseadas no status da saúde."""
    recommendations = []
    
    if status == "critical":
        recommendations.append("Ação imediata necessária")
        recommendations.append("Considere aumentar workers de processamento")
        recommendations.append("Verifique logs de erro detalhados")
    
    elif status == "warning":
        recommendations.append("Monitore de perto")
        recommendations.append("Considere otimizações de performance")
    
    if "Alta carga na fila" in issues:
        recommendations.append("Aumente a frequência de processamento")
        recommendations.append("Considere processamento em paralelo")
    
    if any("Taxa de erro" in issue for issue in issues):
        recommendations.append("Investigue causas dos erros")
        recommendations.append("Verifique configurações de destinatários")
    
    if "Tempo de processamento alto" in issues:
        recommendations.append("Otimize queries de banco de dados")
        recommendations.append("Considere cache para dados frequentes")
    
    if "Muitas notificações pendentes" in issues:
        recommendations.append("Aumente capacidade de processamento")
        recommendations.append("Considere priorização de notificações")
    
    return recommendations
