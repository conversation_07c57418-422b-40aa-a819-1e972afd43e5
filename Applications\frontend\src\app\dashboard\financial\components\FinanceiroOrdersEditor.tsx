'use client';

import { useState, useMemo, useEffect, useCallback } from 'react';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { FinanceiroOrdersLayout } from './FinanceiroOrdersLayout';
import { OrderDetailsModal } from './OrderDetailsModal';
import { apiClient } from '@/lib/api/client';

export function FinanceiroOrdersEditor() {
  // State management
  const [orders, setOrders] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('today');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState('desc');
  
  // Modal states
  const [showOrderModal, setShowOrderModal] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<any>(null);

  // Load orders from API
  useEffect(() => {
    loadOrders();
  }, [statusFilter, dateFilter, loadOrders]);

  const loadOrders = useCallback(async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();

      if (statusFilter !== 'all') {
        params.append('status', statusFilter);
      }

      // Add date filtering logic here
      const response = await apiClient.get(`/financial/orders/?${params.toString()}`);
      setOrders(Array.isArray(response) ? response : []);
    } catch (err) {
      setError('Failed to load orders');
      console.error('Error loading orders:', err);
    } finally {
      setLoading(false);
    }
  }, [statusFilter]);

  // Computed values
  const filteredOrders = useMemo(() => {
    let filtered = orders.filter(order => {
      // Search filter
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        return (
          order.order_number?.toLowerCase().includes(searchLower) ||
          order.customer?.name?.toLowerCase().includes(searchLower) ||
          order.customer?.email?.toLowerCase().includes(searchLower)
        );
      }
      return true;
    });

    // Date filter
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    filtered = filtered.filter(order => {
      const orderDate = new Date(order.created_at);
      
      switch (dateFilter) {
        case 'today':
          return orderDate >= today;
        case 'week':
          const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
          return orderDate >= weekAgo;
        case 'month':
          const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
          return orderDate >= monthAgo;
        default:
          return true;
      }
    });

    // Sort orders
    filtered.sort((a, b) => {
      let aValue, bValue;
      
      switch (sortBy) {
        case 'created_at':
          aValue = new Date(a.created_at);
          bValue = new Date(b.created_at);
          break;
        case 'total':
          aValue = parseFloat(a.total || 0);
          bValue = parseFloat(b.total || 0);
          break;
        case 'order_number':
          aValue = a.order_number || '';
          bValue = b.order_number || '';
          break;
        default:
          return 0;
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [orders, searchTerm, dateFilter, sortBy, sortOrder]);

  // Statistics
  const stats = useMemo(() => {
    const totalOrders = filteredOrders.length;
    const totalRevenue = filteredOrders.reduce((sum, order) => sum + parseFloat(order.total || 0), 0);
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
    
    const statusCounts = filteredOrders.reduce((acc, order) => {
      acc[order.status] = (acc[order.status] || 0) + 1;
      return acc;
    }, {});

    return {
      totalOrders,
      totalRevenue,
      averageOrderValue,
      statusCounts
    };
  }, [filteredOrders]);

  // Handlers
  const handleOrderSelect = (order: any) => {
    setSelectedOrder(order);
    setShowOrderModal(true);
  };

  const handleStatusUpdate = async (orderId: string, newStatus: string) => {
    try {
      await apiClient.patch(`/financial/orders/${orderId}/status`, {
        status: newStatus
      });
      
      // Update local state
      setOrders(prev => prev.map(order => 
        order.id === orderId ? { ...order, status: newStatus } : order
      ));
    } catch (err) {
      console.error('Error updating order status:', err);
    }
  };

  const handleRefreshOrders = () => {
    loadOrders();
  };

  const handleExportOrders = () => {
    // Export functionality
    const csvContent = generateCSV(filteredOrders);
    downloadCSV(csvContent, `orders-${new Date().toISOString().split('T')[0]}.csv`);
  };

  const generateCSV = (orders: any[]) => {
    const headers = ['Order Number', 'Customer', 'Status', 'Total', 'Date'];
    const rows = orders.map(order => [
      order.order_number || '',
      order.customer?.name || order.customer?.email || '',
      order.status || '',
      order.total || '0',
      new Date(order.created_at).toLocaleDateString()
    ]);
    
    return [headers, ...rows].map(row => row.join(',')).join('\n');
  };

  const downloadCSV = (content: string, filename: string) => {
    const blob = new Blob([content], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <DndContext collisionDetection={closestCenter}>
      <div className="h-full flex flex-col">
        <FinanceiroOrdersLayout
          orders={filteredOrders}
          stats={stats}
          loading={loading}
          error={error}
          statusFilter={statusFilter}
          dateFilter={dateFilter}
          searchTerm={searchTerm}
          sortBy={sortBy}
          sortOrder={sortOrder}
          onStatusFilterChange={setStatusFilter}
          onDateFilterChange={setDateFilter}
          onSearchChange={setSearchTerm}
          onSortChange={setSortBy}
          onSortOrderChange={setSortOrder}
          onOrderSelect={handleOrderSelect}
          onStatusUpdate={handleStatusUpdate}
          onRefresh={handleRefreshOrders}
          onExport={handleExportOrders}
        />
      </div>

      {/* Order Details Modal */}
      {showOrderModal && selectedOrder && (
        <OrderDetailsModal
          order={selectedOrder}
          onClose={() => {
            setShowOrderModal(false);
            setSelectedOrder(null);
          }}
          onStatusUpdate={handleStatusUpdate}
        />
      )}
    </DndContext>
  );
}
