# Shops - Online Store

**Categoria:** Shops
**Módulo:** Online Store
**Total de Endpoints:** 6
**Gerado em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [GET /api/modules/shops/store/store/admin/orders/](#get-apimodulesshopsstorestoreadminorders) - List All Orders (Admin)
- [GET /api/modules/shops/store/store/my-orders/](#get-apimodulesshopsstorestoremy-orders) - List My Orders
- [POST /api/modules/shops/store/store/orders/](#post-apimodulesshopsstorestoreorders) - Create Online Order
- [GET /api/modules/shops/store/store/orders/{order_id}](#get-apimodulesshopsstorestoreordersorder-id) - Get Order Details
- [PUT /api/modules/shops/store/store/orders/{order_id}/status](#put-apimodulesshopsstorestoreordersorder-idstatus) - Update Order Status (Admin)
- [GET /api/modules/shops/store/store/{tenant_id_or_slug}/products/](#get-apimodulesshopsstorestoretenant-id-or-slugproducts) - List Products (Public)

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### OnlineOrderCreate

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `shipping_address` | unknown | ❌ | Shipping address for the order |
| `items` | Array[OnlineOrderItemCreate] | ✅ | List of items in the order |

### OnlineOrderRead

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `status` | string | ❌ | Current status of the order (e.g., pending, processing, shipped, delivered, cancelled) |
| `shipping_address` | unknown | ❌ | Shipping address details as a JSON object |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `costumer_id` | string | ✅ | - |
| `sale_transaction_id` | unknown | ❌ | - |
| `total_amount` | string | ✅ | Total calculated amount for the order |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `items` | Array[OnlineOrderItemRead] | ❌ | - |
| `costumer` | User | ✅ | - |
| `sale_transaction` | unknown | ❌ | - |

### OnlineOrderStatusUpdate

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `status` | string | ✅ | The new status for the order |

## 🔗 Endpoints Detalhados

### GET /api/modules/shops/store/store/admin/orders/ {#get-apimodulesshopsstorestoreadminorders}

**Resumo:** List All Orders (Admin)
**Descrição:** Get a list of all online orders for the current tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `status` | string | query | ❌ | Filter orders by status |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/shops/store/store/admin/orders/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/shops/store/store/my-orders/ {#get-apimodulesshopsstorestoremy-orders}

**Resumo:** List My Orders
**Descrição:** Get a list of orders placed by the currently authenticated user.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/shops/store/store/my-orders/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/shops/store/store/orders/ {#post-apimodulesshopsstorestoreorders}

**Resumo:** Create Online Order
**Descrição:** Create a new online order (requires authenticated costumer).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [OnlineOrderCreate](#onlineordercreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [OnlineOrderRead](#onlineorderread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/shops/store/store/orders/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/shops/store/store/orders/{order_id} {#get-apimodulesshopsstorestoreordersorder-id}

**Resumo:** Get Order Details
**Descrição:** Get details of a specific order by ID. Users can only see their own orders unless they have staff/admin roles.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `order_id` | string | path | ✅ | ID of the order to retrieve |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [OnlineOrderRead](#onlineorderread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/shops/store/store/orders/{order_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/shops/store/store/orders/{order_id}/status {#put-apimodulesshopsstorestoreordersorder-idstatus}

**Resumo:** Update Order Status (Admin)
**Descrição:** Update the status of an existing online order.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `order_id` | string | path | ✅ | ID of the order to update status |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [OnlineOrderStatusUpdate](#onlineorderstatusupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [OnlineOrderRead](#onlineorderread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/shops/store/store/orders/{order_id}/status" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/shops/store/store/{tenant_id_or_slug}/products/ {#get-apimodulesshopsstorestoretenant-id-or-slugproducts}

**Resumo:** List Products (Public)
**Descrição:** Get a list of active products available for sale in the online store.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_id_or_slug` | string | path | ✅ | Tenant ID or unique slug |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `name` | string | query | ❌ | Filter by product name (case-insensitive, partial match) |
| `category_id` | string | query | ❌ | Filter by category ID |
| `is_featured` | string | query | ❌ | Filter by featured status |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/shops/store/store/{tenant_id_or_slug}/products/"
```

---
