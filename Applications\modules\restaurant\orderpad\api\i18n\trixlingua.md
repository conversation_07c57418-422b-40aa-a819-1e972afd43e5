# I18N - Trixlingua

**Categoria:** I18N
**Módulo:** Trixlingua
**Total de Endpoints:** 22
**Gerado em:** 08/06/2025, 17:26:01

## 📋 Endpoints

- [GET /api/i18n/languages/](#get-apii18nlanguages) - Read Languages
- [POST /api/i18n/languages/](#post-apii18nlanguages) - Create Language
- [GET /api/i18n/languages/check-version/{language_code}](#get-apii18nlanguagescheck-versionlanguage-code) - Check Language Version
- [DELETE /api/i18n/languages/{language_id}](#delete-apii18nlanguageslanguage-id) - Delete Language
- [GET /api/i18n/languages/{language_id}](#get-apii18nlanguageslanguage-id) - Read Language
- [PUT /api/i18n/languages/{language_id}](#put-apii18nlanguageslanguage-id) - Update Language
- [POST /api/i18n/languages/{language_id}/set-default](#post-apii18nlanguageslanguage-idset-default) - Set Default Language
- [GET /api/i18n/translation-changes/by-sector](#get-apii18ntranslation-changesby-sector) - Get Translations By Sector
- [GET /api/i18n/translation-changes/changes](#get-apii18ntranslation-changeschanges) - Get Translation Changes
- [GET /api/i18n/translation-suggestions/](#get-apii18ntranslation-suggestions) - Read Translation Suggestions
- [POST /api/i18n/translation-suggestions/](#post-apii18ntranslation-suggestions) - Create Translation Suggestion
- [GET /api/i18n/translation-suggestions/my](#get-apii18ntranslation-suggestionsmy) - Read My Translation Suggestions
- [DELETE /api/i18n/translation-suggestions/{suggestion_id}](#delete-apii18ntranslation-suggestionssuggestion-id) - Delete Translation Suggestion
- [GET /api/i18n/translation-suggestions/{suggestion_id}](#get-apii18ntranslation-suggestionssuggestion-id) - Read Translation Suggestion
- [PUT /api/i18n/translation-suggestions/{suggestion_id}](#put-apii18ntranslation-suggestionssuggestion-id) - Update Translation Suggestion
- [POST /api/i18n/translation-suggestions/{suggestion_id}/approve](#post-apii18ntranslation-suggestionssuggestion-idapprove) - Approve Translation Suggestion
- [POST /api/i18n/translation-suggestions/{suggestion_id}/reject](#post-apii18ntranslation-suggestionssuggestion-idreject) - Reject Translation Suggestion
- [GET /api/i18n/translations/](#get-apii18ntranslations) - Read Translations
- [POST /api/i18n/translations/](#post-apii18ntranslations) - Create Translation
- [DELETE /api/i18n/translations/{translation_id}](#delete-apii18ntranslationstranslation-id) - Delete Translation
- [GET /api/i18n/translations/{translation_id}](#get-apii18ntranslationstranslation-id) - Read Translation
- [PUT /api/i18n/translations/{translation_id}](#put-apii18ntranslationstranslation-id) - Update Translation

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### LanguageChanges

**Descrição:** Schema for all changes for a language since a specific version.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `language_code` | string | ✅ | - |
| `current_version_code` | string | ✅ | - |
| `has_changes` | boolean | ✅ | - |
| `sectors` | Array[SectorChanges] | ✅ | - |

### LanguageCreate

**Descrição:** Schema for creating a new Language.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `code` | string | ✅ | Language code (e.g., 'en', 'pt-br') |
| `name` | string | ✅ | Language name in English (e.g., 'English', 'Portuguese (Brazil)') |
| `native_name` | string | ✅ | Language name in its native form (e.g., 'English', 'Português (Brasil)') |
| `is_active` | boolean | ❌ | Whether the language is active |
| `is_default` | boolean | ❌ | Whether this is the default language |

### LanguageRead

**Descrição:** Schema for reading a Language.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `code` | string | ✅ | Language code (e.g., 'en', 'pt-br') |
| `name` | string | ✅ | Language name in English (e.g., 'English', 'Portuguese (Brazil)') |
| `native_name` | string | ✅ | Language name in its native form (e.g., 'English', 'Português (Brasil)') |
| `is_active` | boolean | ❌ | Whether the language is active |
| `is_default` | boolean | ❌ | Whether this is the default language |
| `id` | string | ✅ | - |
| `version_code` | string | ✅ | Version code for tracking language updates |

### LanguageUpdate

**Descrição:** Schema for updating an existing Language.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `code` | unknown | ❌ | - |
| `name` | unknown | ❌ | - |
| `native_name` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `is_default` | unknown | ❌ | - |

### TranslationCreate

**Descrição:** Schema for creating a new Translation.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `key` | string | ✅ | Translation key |
| `text` | string | ✅ | Translated text |
| `language_id` | string | ✅ | ID of the language |
| `sector` | string | ❌ | Sector/namespace for the translation (e.g., 'menu', 'auth', 'common') |

### TranslationRead

**Descrição:** Schema for reading a Translation.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `key` | string | ✅ | Translation key |
| `text` | string | ✅ | Translated text |
| `language_id` | string | ✅ | ID of the language |
| `sector` | string | ❌ | Sector/namespace for the translation (e.g., 'menu', 'auth', 'common') |
| `id` | string | ✅ | - |
| `last_updated_by_id` | unknown | ❌ | - |

### TranslationSuggestionCreate

**Descrição:** Schema for creating a new TranslationSuggestion.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `key` | string | ✅ | Translation key |
| `suggested_text` | string | ✅ | Suggested translation text |
| `language_id` | string | ✅ | ID of the language |

### TranslationSuggestionRead

**Descrição:** Schema for reading a TranslationSuggestion.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `key` | string | ✅ | Translation key |
| `suggested_text` | string | ✅ | Suggested translation text |
| `language_id` | string | ✅ | ID of the language |
| `id` | string | ✅ | - |
| `user_id` | string | ✅ | - |
| `status` | string | ✅ | - |

### TranslationSuggestionUpdate

**Descrição:** Schema for updating an existing TranslationSuggestion.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `key` | unknown | ❌ | - |
| `suggested_text` | unknown | ❌ | - |
| `language_id` | unknown | ❌ | - |
| `status` | unknown | ❌ | Status of the suggestion (pending, approved, rejected) |

### TranslationUpdate

**Descrição:** Schema for updating an existing Translation.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `key` | unknown | ❌ | - |
| `text` | unknown | ❌ | - |
| `language_id` | unknown | ❌ | - |
| `sector` | unknown | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/i18n/languages/ {#get-apii18nlanguages}

**Resumo:** Read Languages
**Descrição:** Retrieve all languages with pagination.
This endpoint is public and does not require authentication.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/i18n/languages/"
```

---

### POST /api/i18n/languages/ {#post-apii18nlanguages}

**Resumo:** Create Language
**Descrição:** Create a new language.
Requires ADMIN system role.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [LanguageCreate](#languagecreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LanguageRead](#languageread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/i18n/languages/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/i18n/languages/check-version/{language_code} {#get-apii18nlanguagescheck-versionlanguage-code}

**Resumo:** Check Language Version
**Descrição:** Check if a language version has changed.
This endpoint is public and does not require authentication.

Returns a JSON object with:
- changed: boolean indicating if the version has changed
- version_code: the current version code on the server

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `language_code` | string | path | ✅ | The language code to check |
| `client_version` | string | query | ✅ | The client's current version code |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/i18n/languages/check-version/{language_code}"
```

---

### DELETE /api/i18n/languages/{language_id} {#delete-apii18nlanguageslanguage-id}

**Resumo:** Delete Language
**Descrição:** Delete a language.
Requires ADMIN system role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `language_id` | integer | path | ✅ | The ID of the language to delete |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/i18n/languages/{language_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/i18n/languages/{language_id} {#get-apii18nlanguageslanguage-id}

**Resumo:** Read Language
**Descrição:** Retrieve a specific language by ID.
This endpoint is public and does not require authentication.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `language_id` | integer | path | ✅ | The ID of the language to retrieve |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LanguageRead](#languageread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/i18n/languages/{language_id}"
```

---

### PUT /api/i18n/languages/{language_id} {#put-apii18nlanguageslanguage-id}

**Resumo:** Update Language
**Descrição:** Update a language.
Requires ADMIN system role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `language_id` | integer | path | ✅ | The ID of the language to update |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [LanguageUpdate](#languageupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LanguageRead](#languageread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/i18n/languages/{language_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/i18n/languages/{language_id}/set-default {#post-apii18nlanguageslanguage-idset-default}

**Resumo:** Set Default Language
**Descrição:** Set a language as the default.
Requires ADMIN system role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `language_id` | integer | path | ✅ | The ID of the language to set as default |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LanguageRead](#languageread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/i18n/languages/{language_id}/set-default" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/i18n/translation-changes/by-sector {#get-apii18ntranslation-changesby-sector}

**Resumo:** Get Translations By Sector
**Descrição:** Get all translations for a language, grouped by sector.
This endpoint is public and does not require authentication.

Returns a list of sectors with their translations.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `language_code` | string | query | ✅ | Language code |
| `sector` | string | query | ❌ | Filter by sector |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/i18n/translation-changes/by-sector"
```

---

### GET /api/i18n/translation-changes/changes {#get-apii18ntranslation-changeschanges}

**Resumo:** Get Translation Changes
**Descrição:** Get all translation changes for a language since a specific version.
This endpoint is public and does not require authentication.

Returns a JSON object with:
- language_code: The language code
- current_version_code: The current version code on the server
- has_changes: Boolean indicating if there are changes
- sectors: List of sectors with their changes

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `language_code` | string | query | ✅ | Language code |
| `since_version` | string | query | ✅ | Get changes since this version |
| `sector` | string | query | ❌ | Filter by sector |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LanguageChanges](#languagechanges)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/i18n/translation-changes/changes"
```

---

### GET /api/i18n/translation-suggestions/ {#get-apii18ntranslation-suggestions}

**Resumo:** Read Translation Suggestions
**Descrição:** Retrieve all translation suggestions with pagination and optional filtering.
Requires ADMIN system role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `language_id` | string | query | ❌ | Filter by language ID |
| `key` | string | query | ❌ | Filter by translation key |
| `status` | string | query | ❌ | Filter by status (pending, approved, rejected) |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/i18n/translation-suggestions/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/i18n/translation-suggestions/ {#post-apii18ntranslation-suggestions}

**Resumo:** Create Translation Suggestion
**Descrição:** Create a new translation suggestion.
Any authenticated user can create a suggestion.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TranslationSuggestionCreate](#translationsuggestioncreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TranslationSuggestionRead](#translationsuggestionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/i18n/translation-suggestions/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/i18n/translation-suggestions/my {#get-apii18ntranslation-suggestionsmy}

**Resumo:** Read My Translation Suggestions
**Descrição:** Retrieve all translation suggestions made by the current user.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `language_id` | string | query | ❌ | Filter by language ID |
| `key` | string | query | ❌ | Filter by translation key |
| `status` | string | query | ❌ | Filter by status (pending, approved, rejected) |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/i18n/translation-suggestions/my" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### DELETE /api/i18n/translation-suggestions/{suggestion_id} {#delete-apii18ntranslation-suggestionssuggestion-id}

**Resumo:** Delete Translation Suggestion
**Descrição:** Delete a translation suggestion.
Users can only delete their own pending suggestions.
Admins can delete any suggestion.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `suggestion_id` | integer | path | ✅ | The ID of the suggestion to delete |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/i18n/translation-suggestions/{suggestion_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/i18n/translation-suggestions/{suggestion_id} {#get-apii18ntranslation-suggestionssuggestion-id}

**Resumo:** Read Translation Suggestion
**Descrição:** Retrieve a specific translation suggestion by ID.
Users can only view their own suggestions unless they are admins.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `suggestion_id` | integer | path | ✅ | The ID of the suggestion to retrieve |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TranslationSuggestionRead](#translationsuggestionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/i18n/translation-suggestions/{suggestion_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/i18n/translation-suggestions/{suggestion_id} {#put-apii18ntranslation-suggestionssuggestion-id}

**Resumo:** Update Translation Suggestion
**Descrição:** Update a translation suggestion.
Users can only update their own pending suggestions.
Admins can update any suggestion, including changing the status.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `suggestion_id` | integer | path | ✅ | The ID of the suggestion to update |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TranslationSuggestionUpdate](#translationsuggestionupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TranslationSuggestionRead](#translationsuggestionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/i18n/translation-suggestions/{suggestion_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/i18n/translation-suggestions/{suggestion_id}/approve {#post-apii18ntranslation-suggestionssuggestion-idapprove}

**Resumo:** Approve Translation Suggestion
**Descrição:** Approve a translation suggestion and create a translation from it.
Requires ADMIN system role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `suggestion_id` | integer | path | ✅ | The ID of the suggestion to approve |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TranslationSuggestionRead](#translationsuggestionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/i18n/translation-suggestions/{suggestion_id}/approve" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/i18n/translation-suggestions/{suggestion_id}/reject {#post-apii18ntranslation-suggestionssuggestion-idreject}

**Resumo:** Reject Translation Suggestion
**Descrição:** Reject a translation suggestion.
Requires ADMIN system role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `suggestion_id` | integer | path | ✅ | The ID of the suggestion to reject |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TranslationSuggestionRead](#translationsuggestionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/i18n/translation-suggestions/{suggestion_id}/reject" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/i18n/translations/ {#get-apii18ntranslations}

**Resumo:** Read Translations
**Descrição:** Retrieve all translations with pagination and optional filtering.
This endpoint is public and does not require authentication.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `language_id` | string | query | ❌ | Filter by language ID |
| `key` | string | query | ❌ | Filter by translation key |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/i18n/translations/"
```

---

### POST /api/i18n/translations/ {#post-apii18ntranslations}

**Resumo:** Create Translation
**Descrição:** Create a new translation.
Requires ADMIN system role.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TranslationCreate](#translationcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TranslationRead](#translationread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/i18n/translations/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/i18n/translations/{translation_id} {#delete-apii18ntranslationstranslation-id}

**Resumo:** Delete Translation
**Descrição:** Delete a translation.
Requires ADMIN system role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `translation_id` | integer | path | ✅ | The ID of the translation to delete |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/i18n/translations/{translation_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/i18n/translations/{translation_id} {#get-apii18ntranslationstranslation-id}

**Resumo:** Read Translation
**Descrição:** Retrieve a specific translation by ID.
This endpoint is public and does not require authentication.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `translation_id` | integer | path | ✅ | The ID of the translation to retrieve |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TranslationRead](#translationread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/i18n/translations/{translation_id}"
```

---

### PUT /api/i18n/translations/{translation_id} {#put-apii18ntranslationstranslation-id}

**Resumo:** Update Translation
**Descrição:** Update a translation.
Requires ADMIN system role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `translation_id` | integer | path | ✅ | The ID of the translation to update |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TranslationUpdate](#translationupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TranslationRead](#translationread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/i18n/translations/{translation_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
