"""
Checkout Models for EShop System
================================

Modelos para processo de checkout com suporte a múltiplos métodos de pagamento,
endereços de entrega, e integração com carrinho, pedidos e pagamentos.
"""

import uuid
import enum
from datetime import datetime, timed<PERSON><PERSON>
from typing import TYPE_CHECKING, Optional
from decimal import Decimal

from sqlalchemy import (
    Column, String, ForeignKey, Text, Enum, DateTime, 
    Boolean, Index, func, Numeric, JSON
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, Mapped, mapped_column

from app.db.base import Base

# Use TYPE_CHECKING to handle circular dependencies
if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.core.users.models.user import User
    from app.modules.core.functions.cart.models.cart import Cart
    from app.modules.core.functions.orders.models.order import Order


class CheckoutStatus(str, enum.Enum):
    """Status da sessão de checkout."""
    
    INITIATED = "initiated"
    PAYMENT_PENDING = "payment_pending"
    PAYMENT_PROCESSING = "payment_processing"
    PAYMENT_CONFIRMED = "payment_confirmed"
    PAYMENT_FAILED = "payment_failed"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    EXPIRED = "expired"


class PaymentMethod(str, enum.Enum):
    """Métodos de pagamento disponíveis."""
    
    CREDIT_CARD = "credit_card"
    DEBIT_CARD = "debit_card"
    PIX = "pix"
    BANK_TRANSFER = "bank_transfer"
    DIGITAL_WALLET = "digital_wallet"
    CASH_ON_DELIVERY = "cash_on_delivery"
    BANK_SLIP = "bank_slip"


class ShippingMethod(str, enum.Enum):
    """Métodos de entrega disponíveis."""
    
    STANDARD = "standard"
    EXPRESS = "express"
    OVERNIGHT = "overnight"
    PICKUP = "pickup"
    SAME_DAY = "same_day"


class CheckoutSession(Base):
    """
    Modelo para sessão de checkout.
    
    Gerencia o processo completo de checkout desde a iniciação
    até a conclusão do pedido, incluindo pagamento e entrega.
    """
    
    __tablename__ = "eshop_checkout_sessions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("tenants.id"), 
        nullable=False, 
        index=True
    )
    
    # Relacionamentos principais
    cart_id = Column(
        UUID(as_uuid=True),
        ForeignKey("eshop_carts.id"),
        nullable=False,
        index=True
    )
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=True,
        index=True
    )
    order_id = Column(
        UUID(as_uuid=True),
        ForeignKey("orders.id"),
        nullable=True,
        index=True
    )
    
    # Status e configurações
    status = Column(
        Enum(CheckoutStatus), 
        default=CheckoutStatus.INITIATED, 
        nullable=False, 
        index=True
    )
    
    # Contexto de mercado (B2B/B2C)
    market_context = Column(String(20), nullable=False, default='b2c')
    
    # Informações de entrega
    shipping_address = Column(JSON, nullable=True)
    billing_address = Column(JSON, nullable=True)
    shipping_method = Column(Enum(ShippingMethod), nullable=True)
    shipping_cost = Column(Numeric(10, 2), default=0.00, nullable=False)
    shipping_notes = Column(Text, nullable=True)
    
    # Informações de pagamento
    payment_method = Column(Enum(PaymentMethod), nullable=True)
    payment_provider = Column(String(50), nullable=True)
    payment_external_id = Column(String(255), nullable=True)
    payment_reference = Column(String(255), nullable=True)
    
    # Totais calculados
    subtotal = Column(Numeric(10, 2), nullable=False)
    tax_amount = Column(Numeric(10, 2), default=0.00, nullable=False)
    discount_amount = Column(Numeric(10, 2), default=0.00, nullable=False)
    total_amount = Column(Numeric(10, 2), nullable=False)
    currency = Column(String(3), default='BRL', nullable=False)
    
    # Informações adicionais
    customer_notes = Column(Text, nullable=True)
    special_instructions = Column(Text, nullable=True)
    metadata = Column(JSON, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )
    expires_at = Column(DateTime, nullable=True)
    payment_confirmed_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    
    # Relacionamentos
    tenant = relationship("Tenant")
    user = relationship("User")
    cart = relationship("Cart")
    order = relationship("Order")
    
    # Índices para performance
    __table_args__ = (
        Index("ix_checkout_tenant_user", "tenant_id", "user_id"),
        Index("ix_checkout_status_expires", "status", "expires_at"),
        Index("ix_checkout_market_context", "market_context"),
        Index("ix_checkout_payment_method", "payment_method"),
        Index("ix_checkout_created", "created_at"),
    )
    
    def __repr__(self):
        return (
            f"<CheckoutSession(id={self.id}, "
            f"cart_id={self.cart_id}, "
            f"status='{self.status}', "
            f"total={self.total_amount})>"
        )
    
    @property
    def is_expired(self) -> bool:
        """Verifica se a sessão de checkout está expirada."""
        if self.expires_at is None:
            return False
        return datetime.utcnow() > self.expires_at
    
    @property
    def is_payment_pending(self) -> bool:
        """Verifica se o pagamento está pendente."""
        return self.status in [
            CheckoutStatus.PAYMENT_PENDING,
            CheckoutStatus.PAYMENT_PROCESSING
        ]
    
    @property
    def is_completed(self) -> bool:
        """Verifica se o checkout foi completado."""
        return self.status == CheckoutStatus.COMPLETED
    
    def set_expiration(self, minutes: int = 30):
        """Define a expiração da sessão de checkout."""
        self.expires_at = datetime.utcnow() + timedelta(minutes=minutes)
    
    def calculate_totals(self, cart_subtotal: Decimal, shipping_cost: Decimal = None):
        """
        Calcula os totais do checkout baseado no carrinho.
        
        Args:
            cart_subtotal: Subtotal do carrinho
            shipping_cost: Custo de entrega (opcional)
        """
        self.subtotal = cart_subtotal
        
        if shipping_cost is not None:
            self.shipping_cost = shipping_cost
        
        # Calcular taxa (exemplo: 10%)
        self.tax_amount = self.subtotal * Decimal('0.1')
        
        # Calcular total
        self.total_amount = (
            self.subtotal + 
            self.tax_amount + 
            self.shipping_cost - 
            self.discount_amount
        )
    
    def update_payment_info(
        self, 
        payment_method: PaymentMethod,
        payment_provider: str = None,
        external_id: str = None,
        reference: str = None
    ):
        """
        Atualiza informações de pagamento.
        
        Args:
            payment_method: Método de pagamento selecionado
            payment_provider: Provedor de pagamento
            external_id: ID externo do pagamento
            reference: Referência do pagamento
        """
        self.payment_method = payment_method
        self.payment_provider = payment_provider
        self.payment_external_id = external_id
        self.payment_reference = reference
        self.updated_at = datetime.utcnow()
    
    def confirm_payment(self):
        """Confirma o pagamento e atualiza status."""
        self.status = CheckoutStatus.PAYMENT_CONFIRMED
        self.payment_confirmed_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
    
    def complete_checkout(self, order_id: uuid.UUID):
        """
        Completa o checkout e associa ao pedido.
        
        Args:
            order_id: ID do pedido criado
        """
        self.order_id = order_id
        self.status = CheckoutStatus.COMPLETED
        self.completed_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
