'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { toast } from 'sonner';
import { useAuth } from '@/lib/auth/AuthProvider';
import { apiClient } from '@/lib/api/apiClient';
import { Product } from '@/types/eshop';

// Types for EShop Cart
export interface EshopCartItem {
  id: string;
  cart_id: string;
  product_id: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  selected_variants?: Record<string, any>;
  selected_modifiers?: Record<string, any>;
  special_instructions?: string;
  created_at: string;
  updated_at: string;
  // Product data (from join)
  product_name?: string;
  product_image_url?: string;
  product_sku?: string;
  product?: Product;
}

export interface EshopCart {
  id: string;
  tenant_id: string;
  user_id?: string;
  session_id?: string;
  status: 'active' | 'expired' | 'converted' | 'abandoned';
  market_context: 'b2b' | 'b2c';
  subtotal: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  currency: string;
  notes?: string;
  expires_at?: string;
  created_at: string;
  updated_at: string;
  items: EshopCartItem[];
  // Calculated properties
  total_items: number;
  is_expired: boolean;
}

export interface AddToCartRequest {
  product_id: string;
  quantity: number;
  selected_variants?: Record<string, any>;
  selected_modifiers?: Record<string, any>;
  special_instructions?: string;
}

interface EshopCartContextType {
  cart: EshopCart | null;
  isLoading: boolean;
  isOpen: boolean;
  marketContext: 'b2b' | 'b2c';
  
  // Cart actions
  addItem: (request: AddToCartRequest) => Promise<void>;
  updateItemQuantity: (itemId: string, quantity: number) => Promise<void>;
  removeItem: (itemId: string) => Promise<void>;
  clearCart: () => Promise<void>;
  refreshCart: () => Promise<void>;
  
  // UI actions
  openCart: () => void;
  closeCart: () => void;
  toggleCart: () => void;
  
  // Market context
  setMarketContext: (context: 'b2b' | 'b2c') => void;
}

const EshopCartContext = createContext<EshopCartContextType | undefined>(undefined);

interface EshopCartProviderProps {
  children: ReactNode;
  initialMarketContext?: 'b2b' | 'b2c';
}

export function EshopCartProvider({ children, initialMarketContext = 'b2c' }: EshopCartProviderProps) {
  const [cart, setCart] = useState<EshopCart | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [marketContext, setMarketContext] = useState<'b2b' | 'b2c'>(initialMarketContext);
  
  const { isAuthenticated, user } = useAuth();

  // Generate or get session ID for anonymous users
  const getSessionId = useCallback((): string => {
    if (typeof window === 'undefined') return '';

    let sessionId = localStorage.getItem('eshop_session_id');
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      localStorage.setItem('eshop_session_id', sessionId);
    }
    return sessionId;
  }, []);

  const refreshCart = useCallback(async () => {
    try {
      setIsLoading(true);

      const headers: Record<string, string> = {};
      if (!isAuthenticated) {
        headers['X-Session-ID'] = getSessionId();
      }

      const response = await apiClient.get(
        `/modules/core/functions/cart?market_context=${marketContext}`,
        { headers }
      );

      setCart(response.data);
    } catch (error) {
      console.error('Error loading cart:', error);
      // Don't show error toast for initial load failures
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, marketContext, getSessionId]);

  // Load cart on mount and when auth state changes
  useEffect(() => {
    refreshCart();
  }, [isAuthenticated, marketContext, refreshCart]);



  const addItem = async (request: AddToCartRequest) => {
    try {
      setIsLoading(true);
      
      const headers: Record<string, string> = {};
      if (!isAuthenticated) {
        headers['X-Session-ID'] = getSessionId();
      }

      const response = await apiClient.post(
        `/modules/core/functions/cart/items?market_context=${marketContext}`,
        request,
        { headers }
      );

      if (response.data.success) {
        setCart(response.data.cart);
        toast.success('Item adicionado ao carrinho');
        
        // Auto-open cart when item is added
        setIsOpen(true);
      } else {
        toast.error(response.data.message || 'Erro ao adicionar item');
      }
    } catch (error: any) {
      console.error('Error adding item to cart:', error);
      toast.error(error.response?.data?.detail || 'Erro ao adicionar item ao carrinho');
    } finally {
      setIsLoading(false);
    }
  };

  const updateItemQuantity = async (itemId: string, quantity: number) => {
    if (quantity <= 0) {
      await removeItem(itemId);
      return;
    }

    try {
      setIsLoading(true);
      
      const headers: Record<string, string> = {};
      if (!isAuthenticated) {
        headers['X-Session-ID'] = getSessionId();
      }

      const response = await apiClient.put(
        `/modules/core/functions/cart/items/${itemId}`,
        { quantity },
        { headers }
      );

      if (response.data.success) {
        setCart(response.data.cart);
        toast.success('Quantidade atualizada');
      } else {
        toast.error(response.data.message || 'Erro ao atualizar quantidade');
      }
    } catch (error: any) {
      console.error('Error updating item quantity:', error);
      toast.error(error.response?.data?.detail || 'Erro ao atualizar quantidade');
    } finally {
      setIsLoading(false);
    }
  };

  const removeItem = async (itemId: string) => {
    try {
      setIsLoading(true);
      
      const headers: Record<string, string> = {};
      if (!isAuthenticated) {
        headers['X-Session-ID'] = getSessionId();
      }

      const response = await apiClient.delete(
        `/modules/core/functions/cart/items/${itemId}`,
        { headers }
      );

      if (response.data.success) {
        setCart(response.data.cart);
        toast.success('Item removido do carrinho');
      } else {
        toast.error(response.data.message || 'Erro ao remover item');
      }
    } catch (error: any) {
      console.error('Error removing item from cart:', error);
      toast.error(error.response?.data?.detail || 'Erro ao remover item');
    } finally {
      setIsLoading(false);
    }
  };

  const clearCart = async () => {
    try {
      setIsLoading(true);
      
      const headers: Record<string, string> = {};
      if (!isAuthenticated) {
        headers['X-Session-ID'] = getSessionId();
      }

      const response = await apiClient.delete(
        `/modules/core/functions/cart/clear`,
        { headers }
      );

      if (response.data.success) {
        setCart(response.data.cart);
        toast.success('Carrinho limpo');
      } else {
        toast.error(response.data.message || 'Erro ao limpar carrinho');
      }
    } catch (error: any) {
      console.error('Error clearing cart:', error);
      toast.error(error.response?.data?.detail || 'Erro ao limpar carrinho');
    } finally {
      setIsLoading(false);
    }
  };

  // UI actions
  const openCart = () => setIsOpen(true);
  const closeCart = () => setIsOpen(false);
  const toggleCart = () => setIsOpen(!isOpen);

  const value: EshopCartContextType = {
    cart,
    isLoading,
    isOpen,
    marketContext,
    addItem,
    updateItemQuantity,
    removeItem,
    clearCart,
    refreshCart,
    openCart,
    closeCart,
    toggleCart,
    setMarketContext,
  };

  return (
    <EshopCartContext.Provider value={value}>
      {children}
    </EshopCartContext.Provider>
  );
}

export function useEshopCart(): EshopCartContextType {
  const context = useContext(EshopCartContext);
  if (context === undefined) {
    throw new Error('useEshopCart must be used within an EshopCartProvider');
  }
  return context;
}
