# app/modules/sharedModules/staff_management/api/staff_api.py
import uuid
from fastapi import APIRouter, Depends, HTTPException, status, Path
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Annotated  # Adicionado Annotated

from app.core.dependencies import get_db, get_current_tenant, require_tenant_role
from app.core.roles import TenantRole, RolePermissions  # Adicionado para roles
from app.models.tenant import Tenant
from app.models.user import User
from app.services.user_service import UserService
from app.services.tenant_user_association_service import TenantUserAssociationService

# Import Subscription Service
from app.modules.subscriptions import services as subscription_services
from app.modules.sharedModules.staff_management.schemas.staff import (
    StaffAssociationCreate,
    StaffAssociationUpdate,
    StaffMember,
)

# Assuming TenantUserAssociation model might be useful for type hints or direct checks

router = APIRouter()

# Dependency to get services (optional, but can centralize service instantiation)


def get_user_service(
    db: AsyncSession = Depends(get_db),
) -> UserService:  # Mudado para AsyncSession
    return UserService(db)


# Mudado para AsyncSession
def get_association_service(
    db: AsyncSession = Depends(get_db),
) -> TenantUserAssociationService:
    return TenantUserAssociationService(db)


@router.post(
    "/members",
    response_model=StaffMember,
    status_code=status.HTTP_201_CREATED,
    summary="Add Existing User as Staff Member",
    description="Associates an existing user (found by email) with the current tenant using a specific role (e.g., 'manager', 'staf').',",  # noqa: E501
)
async def add_staff_member(  # Marcado como async
    staff_data: StaffAssociationCreate,
    current_tenant: Tenant = Depends(get_current_tenant),  # Mantido para obter tenant.id
    performing_user: User = Depends(
        require_tenant_role(RolePermissions.ADMIN_ROLES, tenant_id_source="header")
    ),
    user_service: UserService = Depends(get_user_service),
    association_service: TenantUserAssociationService = Depends(get_association_service),
    # db: AsyncSession = Depends(get_db) # Se os serviços não pegarem do Depends(get_db)
):
    """
    Adds an existing user to the current tenant's staff team.
    Requires OWNER or MANAGER role for the performing user.
    - Finds the user by email.
    - Creates or updates the association with the given role.
    - Returns the details of the added staff member.
    """
    # TODO: Os serviços UserService e TenantUserAssociationService precisam ser adaptados para async
    # e para receber AsyncSession se ainda não estiverem.
    # Por agora, vamos assumir que as dependências get_user_service e get_association_service
    # fornecem instâncias que funcionam com a AsyncSession passada por get_db.

    # --- Verificação de Limite de Assinatura ---
    # Obter o limite de staff para o plano atual do tenant
    staff_limit = await subscription_services.get_tenant_feature_limit(
        db=association_service.db,  # Reutiliza a sessão db injetada no serviço
        tenant_id=current_tenant.id,
        feature_key="max_staff_users",  # Chave definida no plano
    )

    # Se há um limite definido (não é None)
    if staff_limit is not None:
        # Obter contagem atual de staff
        current_staff_count = await association_service.count_staff_members(
            tenant_id=current_tenant.id
        )
        # Verificar se adicionar mais um excede o limite
        if current_staff_count >= staff_limit:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Limite de {staff_limit} membros da equipe atingido para o seu plano de assinatura. Faça upgrade para adicionar mais.",  # noqa: E501
            )
    # --- Fim da Verificação de Limite ---

    # Assumindo que o serviço é async
    db_user = await user_service.get_user_by_email(email=staff_data.user_email)
    if not db_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"User with email {staff_data.user_email} not found.",
        )

    # Prevent associating with 'costumer_customer' role via this endpoint
    if staff_data.role == "costumer_customer":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot assign 'costumer_customer' role via the staff management endpoint.",
        )

    association = await association_service.add_user_to_tenant(  # Assumindo que o serviço é async
        user_id=db_user.id, tenant_id=current_tenant.id, role=staff_data.role
    )
    if not association:
        # This case might indicate an internal issue if user/tenant exist
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to associate user with tenant.",
        )

    # Combine user info and role for the response
    staff_member_data = db_user.__dict__
    staff_member_data["role"] = association.role
    return StaffMember.model_validate(staff_member_data)


@router.get(
    "/members",
    response_model=List[StaffMember],
    summary="List Staff Members",
    description="Retrieves a list of all users associated with the current tenant who have roles other than 'costumer_customer'.",  # noqa: E501
)
async def list_staff_members(  # Mudado para async
    current_tenant: Tenant = Depends(get_current_tenant),  # Mantido para obter tenant.id
    performing_user: User = Depends(
        require_tenant_role(RolePermissions.ADMIN_ROLES, tenant_id_source="header")
    ),
    user_service: UserService = Depends(get_user_service),
    association_service: TenantUserAssociationService = Depends(get_association_service),
):
    """
    Lists all staff members (non-customers) associated with the current tenant.
    Requires OWNER or MANAGER role for the performing user.
    """
    associations = await association_service.get_tenant_users(
        tenant_id=current_tenant.id
    )  # Assumindo que o serviço é async
    staff_members = []
    for assoc in associations:
        if assoc.role != TenantRole.costumer.value:  # Usar enum
            # Assumindo que o serviço é async
            user = await user_service.get_user(user_id=assoc.user_id)
            if user:
                staff_member_data = (
                    user.__dict__.copy()
                )  # Usar copy() para evitar modificar o __dict__ original
                staff_member_data["role"] = assoc.role
                staff_members.append(StaffMember.model_validate(staff_member_data))
            # else: Log potential inconsistency?

    return staff_members


@router.get(
    "/members/{user_id}",
    response_model=StaffMember,
    summary="Get Staff Member Details",
    description="Retrieves details for a specific staff member associated with the current tenant.",
)
async def get_staff_member(  # Mudado para async
    user_id: Annotated[uuid.UUID, Path(..., description="The UUID of the user to retrieve")],
    current_tenant: Tenant = Depends(get_current_tenant),  # Mantido para obter tenant.id
    performing_user: User = Depends(
        require_tenant_role(RolePermissions.ADMIN_ROLES, tenant_id_source="header")
    ),
    user_service: UserService = Depends(get_user_service),
    association_service: TenantUserAssociationService = Depends(get_association_service),
):
    """
    Gets details of a specific staff member by their user ID, ensuring they belong
    to the current tenant and are not just a customer.
    Requires OWNER or MANAGER role for the performing user.
    """
    association = await association_service.get_association(
        user_id=user_id, tenant_id=current_tenant.id
    )  # Assumindo que o serviço é async
    if not association:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"User with ID {user_id} not associated with this tenant.",
        )

    if association.role == TenantRole.costumer.value:  # Usar enum
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"User with ID {user_id} is not a staff member of this tenant.",
        )

    user = await user_service.get_user(user_id=user_id)  # Assumindo que o serviço é async
    if not user:
        # Should not happen if association exists, indicates data inconsistency
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"User details not found for ID {user_id}, but association exists.",
        )

    staff_member_data = user.__dict__.copy()  # Usar copy()
    staff_member_data["role"] = association.role
    return StaffMember.model_validate(staff_member_data)


@router.put(
    "/members/{user_id}",
    response_model=StaffMember,
    summary="Update Staff Member Role",
    description="Updates the role of an existing staff member within the current tenant.",
)
async def update_staff_member_role(  # Mudado para async
    user_id: Annotated[uuid.UUID, Path(..., description="The UUID of the user to update")],
    staff_update: StaffAssociationUpdate,  # Body parameter
    # Dependencies
    current_tenant: Tenant = Depends(get_current_tenant),
    performing_user: User = Depends(
        require_tenant_role(RolePermissions.ADMIN_ROLES, tenant_id_source="header")
    ),
    user_service: UserService = Depends(get_user_service),
    association_service: TenantUserAssociationService = Depends(get_association_service),
):
    """
    Updates the role for a specific staff member of the current tenant.
    Requires OWNER or MANAGER role for the performing user.
    - Ensures the user is currently associated with the tenant.
    - Updates the association with the new role.
    - Returns the updated staff member details.
    """
    existing_association = await association_service.get_association(
        user_id=user_id, tenant_id=current_tenant.id
    )  # Async
    if not existing_association:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"User with ID {user_id} not associated with this tenant.",
        )

    # Validar a role de entrada
    try:
        target_role = TenantRole(staff_update.role)
        if target_role == TenantRole.costumer:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot assign 'costumer' role via the staff management endpoint.",
            )
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid role: {staff_update.role}. Valid roles are {', '.join([r.value for r in TenantRole if r != TenantRole.costumer])}.",  # noqa: E501
        )

    updated_association = await association_service.add_user_to_tenant(  # Async
        user_id=user_id, tenant_id=current_tenant.id, role=target_role.value
    )

    if not updated_association:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user role.",
        )

    user = await user_service.get_user(user_id=user_id)  # Async
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"User details not found for ID {user_id} after role update.",
        )

    staff_member_data = user.__dict__.copy()  # Usar copy()
    staff_member_data["role"] = updated_association.role
    return StaffMember.model_validate(staff_member_data)


@router.delete(
    "/members/{user_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Remove Staff Member from Tenant",
    description="Removes the association between a user and the current tenant, effectively removing them from the staff team. Does not delete the user.",  # noqa: E501
)
async def remove_staff_member(  # Mudado para async
    user_id: Annotated[uuid.UUID, Path(..., description="The UUID of the user to remove as staff")],
    current_tenant: Tenant = Depends(get_current_tenant),  # Mantido para obter tenant.id
    performing_user: User = Depends(
        require_tenant_role(RolePermissions.ADMIN_ROLES, tenant_id_source="header")
    ),
    association_service: TenantUserAssociationService = Depends(get_association_service),
):
    """
    Removes a user's staff association from the current tenant.
    Requires OWNER or MANAGER role for the performing user.
    - Checks if the user is associated and is not a 'costumer'.
    - Removes the TenantUserAssociation record.
    - Returns HTTP 204 No Content on success.
    """
    association = await association_service.get_association(
        user_id=user_id, tenant_id=current_tenant.id
    )  # Async
    if not association:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"User with ID {user_id} not found as an associate of this tenant.",
        )

    if association.role == TenantRole.costumer.value:  # Usar enum
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot remove a 'costumer' using the staff management endpoint.",
        )

    success = await association_service.remove_user_from_tenant(  # Async
        user_id=user_id, tenant_id=current_tenant.id
    )
    if not success:
        # This might happen if the association was deleted between the check and the delete attempt.
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Staff member with ID {user_id} could not be removed or was not found.",
        )

    # No content to return on successful deletion
    return None
