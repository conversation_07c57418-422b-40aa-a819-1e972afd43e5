import React, { useState, useEffect, useCallback } from 'react';
import {
  PlusIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  BookmarkIcon,
  UsersIcon
} from '@heroicons/react/24/outline';
import { useCurrency } from '@/hooks/useCurrency';
import { apiClientWrapper as apiClient } from '@/lib/api/client';

interface GroupSelectorProps {
  type: 'variant' | 'modifier' | 'optional';
  onSelectGroup: (groupId: string) => void;
  onCreateNew: () => void;
  disabled?: boolean;
  digitalMenuId?: string; // Add digital menu ID for filtering
}

export function GroupSelector({
  type,
  onSelectGroup,
  onCreateNew,
  disabled = false,
  digitalMenuId,
}: GroupSelectorProps) {
  const [groups, setGroups] = useState<any[]>([]);
  const [filteredGroups, setFilteredGroups] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [expanded, setExpanded] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const { formatCurrency } = useCurrency();

  const typeConfig = {
    variant: {
      name: 'Variante',
      icon: BookmarkIcon,
      color: 'orange',
      endpoint: '/modules/restaurants/menu/variant-groups'
    },
    modifier: {
      name: 'Modificador',
      icon: BookmarkIcon,
      color: 'green',
      endpoint: '/modules/restaurants/menu/modifier-groups'
    },
    optional: {
      name: 'Opcional',
      icon: BookmarkIcon,
      color: 'purple',
      endpoint: '/modules/restaurants/menu/optional-groups'
    }
  };

  const config = typeConfig[type];

  const loadGroups = useCallback(async () => {
    try {
      setLoading(true);
      let url = `${config.endpoint}?include_usage_count=true`;

      // Add digital menu filter if provided
      if (digitalMenuId) {
        url += `&digital_menu_id=${digitalMenuId}`;
      }

      const response = await apiClient.get(url);

      if (Array.isArray(response)) {
        // Deduplicate groups by ID to prevent React key conflicts
        const uniqueGroups = response.filter((group, index, self) =>
          index === self.findIndex(g => g.id === group.id)
        );

        if (uniqueGroups.length !== response.length) {
          console.warn(`GroupSelector (${type}) - Removed ${response.length - uniqueGroups.length} duplicate groups`);
        }

        setGroups(uniqueGroups);
      } else {
        console.error(`GroupSelector (${type}) - Response is not an array:`, response);
        setGroups([]);
      }

    } catch (error) {
      console.error(`GroupSelector (${type}) - Error loading groups:`, error);
      setGroups([]);
    } finally {
      setLoading(false);
    }
  };

  // Filter groups based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredGroups(groups);
    } else {
      const filtered = groups.filter((group: any) =>
        group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (group.description && group.description.toLowerCase().includes(searchTerm.toLowerCase()))
      );
      setFilteredGroups(filtered);
    }
  }, [groups, searchTerm]);

  const handleSelectGroup = (groupId: string) => {
    onSelectGroup(groupId);
    setExpanded(false);
  };

  console.log(`🔍 GroupSelector (${type}) - Rendering component`, {
    groups: groups.length,
    filteredGroups: filteredGroups.length,
    loading,
    expanded,
    disabled
  });

  return (
    <div className="space-y-3" data-testid={`group-selector-${type}`}>
      {/* Debug Info */}
      {process.env.NODE_ENV === 'development' && (
        <div className="text-xs text-gray-500 bg-yellow-50 p-2 rounded">
          Debug: {type} - Groups: {groups.length} - Loading: {loading ? 'true' : 'false'}
        </div>
      )}

      {/* Header */}
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium text-gray-900">
          Grupos de {config.name}
        </h4>
        <div className="flex items-center space-x-2">
          {groups.length > 0 && (
            <button
              type="button"
              onClick={() => setExpanded(!expanded)}
              className="btn-secondary text-sm"
              disabled={disabled}
              data-testid={`use-group-button-${type}`}
            >
              <config.icon className="h-4 w-4 mr-1" />
              Usar Grupo ({groups.length})
              {expanded ? (
                <ChevronUpIcon className="h-4 w-4 ml-1" />
              ) : (
                <ChevronDownIcon className="h-4 w-4 ml-1" />
              )}
            </button>
          )}
          <button
            type="button"
            onClick={onCreateNew}
            className="btn-secondary text-sm"
            disabled={disabled}
            data-testid={`create-new-button-${type}`}
          >
            <PlusIcon className="h-4 w-4 mr-1" />
            Criar Novo
          </button>
        </div>
      </div>

      {/* Groups List */}
      {expanded && (
        <div className="bg-gray-50 rounded-lg p-3 space-y-2">
          <div className="flex items-center justify-between mb-2">
            <h5 className="text-xs font-medium text-gray-700">
              Grupos Disponíveis
            </h5>
            {groups.length > 5 && (
              <span className="text-xs text-gray-500">
                {filteredGroups.length} de {groups.length}
              </span>
            )}
          </div>

          {/* Search input when more than 5 groups */}
          {groups.length > 5 && (
            <div className="mb-3">
              <input
                type="text"
                placeholder="Buscar por nome ou descrição..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-3 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={disabled}
              />
            </div>
          )}
          
          {loading ? (
            <div className="text-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-400 mx-auto"></div>
              <p className="text-xs text-gray-500 mt-2">Carregando grupos...</p>
            </div>
          ) : filteredGroups.length === 0 ? (
            <div className="text-center py-4 text-gray-500">
              <config.icon className="h-8 w-8 mx-auto mb-2 text-gray-400" />
              {groups.length === 0 ? (
                <>
                  <p className="text-xs">Nenhum grupo de {config.name.toLowerCase()} encontrado</p>
                  <p className="text-xs">Crie grupos para reutilizá-los em outros itens</p>
                  <p className="text-xs text-gray-400 mt-1">
                    Grupos são compartilhados entre múltiplos itens
                  </p>
                </>
              ) : (
                <>
                  <p className="text-xs">Nenhum resultado encontrado</p>
                  <p className="text-xs">Tente uma busca diferente</p>
                </>
              )}
            </div>
          ) : (
            <div className="space-y-2">
              {filteredGroups.map((group) => (
                <div
                  key={group.id}
                  className="bg-white rounded-lg p-3 border border-gray-200 hover:border-gray-300 transition-colors"
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex-1">
                      <h6 className="text-sm font-medium text-gray-900">
                        {group.name}
                      </h6>
                      {group.description && (
                        <p className="text-xs text-gray-600 mt-1">
                          {group.description}
                        </p>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="flex items-center text-xs text-gray-500">
                        <UsersIcon className="h-3 w-3 mr-1" />
                        {group.usage_count || 0} item{(group.usage_count || 0) !== 1 ? 's' : ''}
                      </div>
                      <button
                        type="button"
                        onClick={() => handleSelectGroup(group.id)}
                        className="btn-primary text-xs"
                        disabled={disabled}
                      >
                        Usar
                      </button>
                    </div>
                  </div>
                  
                  {/* Group Preview */}
                  <div className="text-xs text-gray-600">
                    {group.options && group.options.length > 0 ? (
                      <>
                        <span className="font-medium">Opções:</span>
                        {group.options.slice(0, 3).map((option: any, idx: number) => (
                          <span key={idx}>
                            {idx > 0 && ', '}
                            {option.name}
                            {parseFloat(option.price_adjustment) > 0 && ` (+${formatCurrency(parseFloat(option.price_adjustment))})`}
                          </span>
                        ))}
                        {group.options.length > 3 && (
                          <span> e mais {group.options.length - 3}...</span>
                        )}
                      </>
                    ) : (
                      <span className="text-gray-500 italic">
                        Grupo sem opções - adicione opções após usar o grupo
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
