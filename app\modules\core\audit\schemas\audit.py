"""
Audit schemas for tracking all critical system operations.
"""

from enum import Enum
from typing import Any, Dict, Optional, List
from datetime import datetime
from pydantic import BaseModel, Field
from uuid import UUID


class AuditAction(str, Enum):
    """Comprehensive audit action types for all system operations."""
    # Basic CRUD operations
    CREATE = "create"
    READ = "read"
    UPDATE = "update"
    DELETE = "delete"

    # Authentication & Authorization
    LOGIN = "login"
    LOGOUT = "logout"
    LOGIN_FAILED = "login_failed"
    PASSWORD_CHANGE = "password_change"
    PASSWORD_RESET = "password_reset"

    # Administrative actions
    ADMIN_ACCESS = "admin_access"
    USER_ACTIVATE = "user_activate"
    USER_DEACTIVATE = "user_deactivate"
    ROLE_ASSIGN = "role_assign"
    ROLE_REVOKE = "role_revoke"

    # B2B specific actions
    B2B_APPLICATION_SUBMIT = "b2b_application_submit"
    B2B_APPLICATION_APPROVE = "b2b_application_approve"
    B2B_APPLICATION_REJECT = "b2b_application_reject"
    B2B_CREDIT_LIMIT_SET = "b2b_credit_limit_set"
    B2B_PRICING_TIER_CHANGE = "b2b_pricing_tier_change"

    # Financial operations
    PAYMENT_PROCESS = "payment_process"
    PAYMENT_REFUND = "payment_refund"
    INVOICE_GENERATE = "invoice_generate"
    COMMISSION_CALCULATE = "commission_calculate"
    COMMISSION_PAY = "commission_pay"

    # Order operations
    ORDER_PLACE = "order_place"
    ORDER_CANCEL = "order_cancel"
    ORDER_STATUS_CHANGE = "order_status_change"
    ORDER_REFUND = "order_refund"

    # Inventory operations
    INVENTORY_ADJUST = "inventory_adjust"
    PRODUCT_APPROVE = "product_approve"
    PRODUCT_REJECT = "product_reject"

    # System operations
    SYSTEM_CONFIG_CHANGE = "system_config_change"
    DATA_EXPORT = "data_export"
    DATA_IMPORT = "data_import"
    BACKUP_CREATE = "backup_create"
    BACKUP_RESTORE = "backup_restore"


class AuditResource(str, Enum):
    """Comprehensive audit resource types covering all system entities."""
    # Core entities
    USER = "user"
    TENANT = "tenant"
    RESTAURANT = "restaurant"

    # B2B entities
    TCUSTOMER = "tcustomer"
    TVENDOR_SUPPLIER = "tvendor_supplier"
    B2B_APPLICATION = "b2b_application"
    CREDIT_LIMIT = "credit_limit"
    PRICING_TIER = "pricing_tier"

    # E-commerce entities
    PRODUCT = "product"
    CATEGORY = "category"
    INVENTORY = "inventory"
    CART = "cart"
    ORDER = "order"
    ORDER_ITEM = "order_item"

    # Financial entities
    PAYMENT = "payment"
    INVOICE = "invoice"
    COMMISSION = "commission"
    REFUND = "refund"
    TRANSACTION = "transaction"
    FINANCIAL_ACCOUNT = "financial_account"

    # Content entities
    MENU = "menu"
    MENU_ITEM = "menu_item"
    MEDIA = "media"
    BLOG_POST = "blog_post"

    # Communication entities
    NOTIFICATION = "notification"
    TICKET = "ticket"
    MESSAGE = "message"
    EMAIL = "email"

    # System entities
    SETTINGS = "settings"
    CONFIGURATION = "configuration"
    LOG = "log"
    AUDIT = "audit"

    # Shipping & Logistics
    SHIPMENT = "shipment"
    SHIPPING_METHOD = "shipping_method"
    DELIVERY = "delivery"

    # Marketing & Promotions
    OFFER = "offer"
    DISCOUNT = "discount"
    VOUCHER = "voucher"
    COUPON = "coupon"

    # Analytics & Reports
    REPORT = "report"
    ANALYTICS = "analytics"
    DASHBOARD = "dashboard"


class OperationType(str, Enum):
    """Database operation types."""
    CREATE = "CREATE"
    READ = "READ"
    UPDATE = "UPDATE"
    DELETE = "DELETE"
    EXECUTE = "EXECUTE"


class AuditStatus(str, Enum):
    """Audit operation status."""
    SUCCESS = "SUCCESS"
    FAILURE = "FAILURE"
    PENDING = "PENDING"
    PARTIAL = "PARTIAL"


class SecurityLevel(str, Enum):
    """Security classification levels."""
    NORMAL = "NORMAL"
    SENSITIVE = "SENSITIVE"
    CRITICAL = "CRITICAL"


class AuditLogBase(BaseModel):
    """Base audit log schema with comprehensive tracking fields."""
    user_id: Optional[UUID] = None
    action: AuditAction
    resource: AuditResource
    resource_id: Optional[str] = None
    tenant_id: Optional[UUID] = None
    operation_type: OperationType

    # Session and request context
    session_id: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    request_id: Optional[str] = None
    correlation_id: Optional[str] = None

    # Data tracking
    old_values: Optional[Dict[str, Any]] = None
    new_values: Optional[Dict[str, Any]] = None
    details: Optional[Dict[str, Any]] = None

    # Security and compliance
    security_level: SecurityLevel = SecurityLevel.NORMAL
    compliance_tags: Optional[List[str]] = None


class AuditLogCreate(AuditLogBase):
    """Schema for creating audit log entries."""
    status: AuditStatus = AuditStatus.SUCCESS
    error_message: Optional[str] = None
    duration_ms: Optional[str] = None


class AuditLogUpdate(BaseModel):
    """Schema for updating audit log entries (mainly for status updates)."""
    status: Optional[AuditStatus] = None
    error_message: Optional[str] = None
    duration_ms: Optional[str] = None
    details: Optional[Dict[str, Any]] = None


class AuditLogRead(AuditLogBase):
    """Schema for reading audit log entries."""
    id: UUID
    timestamp: datetime
    status: AuditStatus
    error_message: Optional[str] = None
    duration_ms: Optional[str] = None

    class Config:
        from_attributes = True


class AuditLogFilter(BaseModel):
    """Schema for filtering audit log queries."""
    user_id: Optional[UUID] = None
    tenant_id: Optional[UUID] = None
    action: Optional[AuditAction] = None
    resource: Optional[AuditResource] = None
    resource_id: Optional[str] = None
    operation_type: Optional[OperationType] = None
    status: Optional[AuditStatus] = None
    security_level: Optional[SecurityLevel] = None

    # Time range filters
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None

    # Search filters
    search_term: Optional[str] = Field(None, description="Search in details, error messages, etc.")
    ip_address: Optional[str] = None

    # Pagination
    skip: int = Field(0, ge=0)
    limit: int = Field(100, ge=1, le=1000)

    # Sorting
    sort_by: str = Field("timestamp", description="Field to sort by")
    sort_order: str = Field("desc", pattern="^(asc|desc)$")


class AuditLogListResponse(BaseModel):
    """Schema for audit log list responses."""
    items: List[AuditLogRead]
    total: int
    skip: int
    limit: int
    has_more: bool


class AuditLogStats(BaseModel):
    """Schema for audit log statistics."""
    total_entries: int
    success_count: int
    failure_count: int
    pending_count: int

    # Breakdown by resource type
    resource_breakdown: Dict[str, int]

    # Breakdown by action type
    action_breakdown: Dict[str, int]

    # Recent activity (last 24 hours)
    recent_activity_count: int

    # Security metrics
    sensitive_operations_count: int
    critical_operations_count: int

    # Performance metrics
    avg_duration_ms: Optional[float] = None
    max_duration_ms: Optional[float] = None


class AuditLogExport(BaseModel):
    """Schema for audit log export requests."""
    filters: AuditLogFilter
    format: str = Field("json", pattern="^(json|csv|xlsx)$")
    include_sensitive: bool = Field(False, description="Include sensitive data in export")


class ComplianceReport(BaseModel):
    """Schema for compliance reporting."""
    report_type: str
    period_start: datetime
    period_end: datetime
    total_operations: int
    compliant_operations: int
    non_compliant_operations: int
    compliance_percentage: float

    # Detailed breakdown
    operations_by_type: Dict[str, int]
    security_incidents: List[Dict[str, Any]]
    recommendations: List[str]
