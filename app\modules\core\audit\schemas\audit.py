"""
Audit schemas for tracking administrative actions.
"""

from enum import Enum
from typing import Any, Dict, Optional
from datetime import datetime
from pydantic import BaseModel


class AuditAction(str, Enum):
    """Audit action types."""
    CREATE = "create"
    VIEW = "view"
    UPDATE = "update"
    DELETE = "delete"
    LOGIN = "login"
    LOGOUT = "logout"
    ADMIN_ACCESS = "admin_access"


class AuditResource(str, Enum):
    """Audit resource types."""
    USER = "user"
    TENANT = "tenant"
    RESTAURANT = "restaurant"
    MENU = "menu"
    ORDER = "order"
    NOTIFICATION = "notification"
    TICKET = "ticket"
    MEDIA = "media"
    SETTINGS = "settings"


class AuditLogBase(BaseModel):
    """Base audit log schema."""
    user_id: str
    action: AuditAction
    resource: AuditResource
    resource_id: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None


class AuditLogCreate(AuditLogBase):
    """Schema for creating audit log entries."""
    pass


class AuditLogRead(AuditLogBase):
    """Schema for reading audit log entries."""
    id: str
    timestamp: datetime
    
    class Config:
        from_attributes = True
