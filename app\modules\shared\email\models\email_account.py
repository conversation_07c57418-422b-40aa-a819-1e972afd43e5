"""Email Account model for the Email module."""

import uuid  # noqa: E402
from datetime import datetime
from typing import TYPE_CHECKING, List, Optional

from sqlalchemy import (  # noqa: E402
    Column,
    String,
    Boolean,
    DateTime,
    ForeignKey,
    Index,
    Integer,
)
from sqlalchemy.dialects.postgresql import UUID  # noqa: E402
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import Base  # noqa: E402

if TYPE_CHECKING:
    from app.models.user import User  # noqa: E402
    from app.modules.shared.email.models.email_domain import EmailDomain
    from app.modules.shared.email.models.email_metadata import EmailMetadata


class EmailAccount(Base):
    """Email Account model.

    Represents an email account (username@domain).
    """

    __tablename__ = "email_accounts"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Email account details
    email_domain_id = Column(
        UUID(as_uuid=True), <PERSON><PERSON>ey("email_domains.id"), nullable=False, index=True
    )
    username = Column(String, nullable=False)  # The part before @
    full_email = Column(String, nullable=False, index=True)  # username@domain

    # Authentication and quota
    password_hash = Column(String, nullable=False)
    quota_mb = Column(Integer, nullable=False, default=1024)  # 1GB default

    # Status
    is_active = Column(Boolean, default=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )
    last_login = Column(DateTime(timezone=True), nullable=True)

    # Link to system user (optional)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True, index=True)

    # Relationships
    email_domain = relationship("EmailDomain", back_populates="email_accounts")
    user = relationship("User", back_populates="email_accounts")

    email_metadata = relationship(
        "EmailMetadata", back_populates="email_account", cascade="all, delete-orphan"
    )

    # Indexes
    __table_args__ = (
        Index(
            "ix_email_accounts_email_domain_id_username",
            email_domain_id,
            username,
            unique=True,
        ),
    )

    def __repr__(self):
        return f"<EmailAccount(id={self.id}, full_email='{self.full_email}')>"
