"""PayPal payment processor implementation."""

# Mock implementation to avoid dependency issues
from app.modules.core.payments.processors.base import PaymentProcessorInterface  # noqa: E402


class PayPalProcessor(PaymentProcessorInterface):
    """Mock PayPal processor implementation."""

    def __init__(self, processor_config):
        super().__init__(processor_config)

    async def process_payment(self, *args, **kwargs):
        return {"status": "pending", "external_id": "mock_payment_id"}

    async def process_refund(self, *args, **kwargs):
        return {"status": "pending", "external_id": "mock_refund_id"}

    async def get_payment_status(self, *args, **kwargs):
        return {"status": "pending", "external_id": "mock_payment_id"}

    async def get_refund_status(self, *args, **kwargs):
        return {"status": "pending", "external_id": "mock_refund_id"}


__all__ = ["PayPalProcessor"]
