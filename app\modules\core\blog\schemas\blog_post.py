"""
Blog Post Schemas

Pydantic models for blog post validation and serialization.
"""

import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any

from pydantic import BaseModel, Field, validator

# Import related schemas
from .blog_author import BlogAuthorRead
from .blog_category import BlogCategoryRead
from .blog_tag import BlogTagRead
from .blog_seo import BlogSEORead
from ..models.blog_post import PostVisibility
from app.modules.core.i18n.schemas.language import LanguageRead
from .blog_comment import BlogCommentRead
from app.modules.core.functions.media_system.schemas import MediaUploadRead


class BlogPostTranslationBase(BaseModel):
    """Base schema for blog post translations."""
    language_code: str
    title: str
    subtitle: Optional[str] = None
    excerpt: Optional[str] = None
    content: str
    meta_title: Optional[str] = None
    meta_description: Optional[str] = None

    class Config:
        from_attributes = True


class BlogPostTranslationCreate(BlogPostTranslationBase):
    """Schema for creating blog post translations."""
    pass


class BlogPostTranslationUpdate(BaseModel):
    """Schema for updating blog post translations."""
    title: Optional[str] = Field(None, max_length=255)
    subtitle: Optional[str] = Field(None, max_length=500)
    excerpt: Optional[str] = None
    content: Optional[str] = None
    meta_title: Optional[str] = Field(None, max_length=60)
    meta_description: Optional[str] = Field(None, max_length=160)


class BlogPostTranslationRead(BlogPostTranslationBase):
    """Schema for reading blog post translations."""
    id: uuid.UUID
    post_id: uuid.UUID


class BlogPostBase(BaseModel):
    """Base schema for blog posts."""
    slug: str
    author_id: uuid.UUID
    category_id: Optional[uuid.UUID] = None
    featured_image_id: Optional[uuid.UUID] = None
    status: str = "draft"
    visibility: str = "public"
    is_featured: bool = False
    published_at: Optional[datetime] = None
    scheduled_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class BlogPostCreate(BlogPostBase):
    """Schema for creating blog posts."""
    tags: Optional[List[uuid.UUID]] = []
    translations: List[BlogPostTranslationCreate]


class BlogPostUpdate(BaseModel):
    """Schema for updating blog posts."""
    slug: Optional[str] = None
    author_id: Optional[uuid.UUID] = None
    category_id: Optional[uuid.UUID] = None
    featured_image_id: Optional[uuid.UUID] = None
    status: Optional[str] = None
    is_featured: Optional[bool] = None
    visibility: Optional[str] = None
    published_at: Optional[datetime] = None
    scheduled_at: Optional[datetime] = None
    tags: Optional[List[uuid.UUID]] = None
    translations: Optional[List[BlogPostTranslationCreate]] = None

    @validator('status')
    def validate_status(cls, v):
        if v is not None:
            allowed_statuses = ['draft', 'published', 'scheduled', 'archived']
            if v not in allowed_statuses:
                raise ValueError(f'Status must be one of: {allowed_statuses}')
        return v

    @validator('visibility')
    def validate_visibility(cls, v):
        if v is not None:
            allowed_visibility = [PostVisibility.PUBLIC.value, PostVisibility.PRIVATE.value, PostVisibility.MEMBER_ONLY.value]
            if v not in allowed_visibility:
                raise ValueError(f'Visibility must be one of: {allowed_visibility}')
        return v


class BlogPostRead(BlogPostBase):
    """Schema for reading blog posts."""
    id: uuid.UUID
    view_count: int
    like_count: int
    comment_count: int
    created_at: datetime
    updated_at: datetime

    # Relationships
    author: Optional[BlogAuthorRead] = None
    category: Optional[BlogCategoryRead] = None
    tags: List[BlogTagRead] = []
    translations: List[BlogPostTranslationRead] = []
    seo: Optional[BlogSEORead] = None
    comments: List[BlogCommentRead] = []
    content: Optional[str] = None

    class Config:
        from_attributes = True


class BlogPostList(BaseModel):
    """Schema for blog post list items (simplified)."""
    id: uuid.UUID
    slug: str
    status: str
    visibility: str
    is_featured: bool
    published_at: Optional[datetime] = None
    view_count: int
    like_count: int
    comment_count: int
    author: BlogAuthorRead
    category: Optional[BlogCategoryRead] = None
    featured_image: Optional[MediaUploadRead] = None
    created_at: datetime
    updated_at: datetime
    title: Optional[str] = None
    excerpt: Optional[str] = None

    # Simplified relationships
    tags: List[BlogTagRead] = []

    class Config:
        from_attributes = True


class BlogPostSearchResult(BaseModel):
    """Schema for blog post search results."""
    id: uuid.UUID
    title: str
    slug: str
    excerpt: Optional[str]
    score: float

    class Config:
        from_attributes = True
