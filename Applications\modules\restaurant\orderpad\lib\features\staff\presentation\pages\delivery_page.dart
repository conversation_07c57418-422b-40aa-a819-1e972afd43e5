import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/models/order_model.dart';
import '../../../../core/providers/orders_provider.dart';
import '../../../../core/router/app_router.dart';
import '../../../../shared/widgets/loading_overlay.dart';

class DeliveryPage extends ConsumerStatefulWidget {
  const DeliveryPage({super.key});

  @override
  ConsumerState<DeliveryPage> createState() => _DeliveryPageState();
}

class _DeliveryPageState extends ConsumerState<DeliveryPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    
    // Load orders when page initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(ordersProvider.notifier).loadOrders();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final ordersState = ref.watch(ordersProvider);
    final theme = Theme.of(context);
    
    // Filter only delivery orders
    final deliveryOrders = ordersState.orders
        .where((order) => order.type == OrderType.delivery)
        .toList();
    
    final pendingDeliveries = deliveryOrders
        .where((order) => order.status == 'pending' || order.status == 'preparing')
        .toList();
    
    final readyDeliveries = deliveryOrders
        .where((order) => order.status == 'ready')
        .toList();
    
    final outForDelivery = deliveryOrders
        .where((order) => order.status == 'delivered')
        .toList();
    
    final completedDeliveries = deliveryOrders
        .where((order) => order.status == 'completed')
        .toList();

    return Scaffold(
      backgroundColor: theme.colorScheme.background,
      appBar: AppBar(
        title: const Text('Delivery'),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.read(ordersProvider.notifier).loadOrders(),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: [
            Tab(
              child: _buildTabWithBadge(
                'Preparando',
                pendingDeliveries.length,
                Colors.orange,
              ),
            ),
            Tab(
              child: _buildTabWithBadge(
                'Prontos',
                readyDeliveries.length,
                Colors.green,
              ),
            ),
            Tab(
              child: _buildTabWithBadge(
                'Saiu',
                outForDelivery.length,
                Colors.blue,
              ),
            ),
            Tab(
              child: _buildTabWithBadge(
                'Entregues',
                completedDeliveries.length,
                Colors.grey,
              ),
            ),
          ],
        ),
      ),
      body: ordersState.isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Statistics bar
                _buildStatisticsBar(deliveryOrders),
                
                // Delivery content
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildDeliveryList(pendingDeliveries),
                      _buildDeliveryList(readyDeliveries),
                      _buildDeliveryList(outForDelivery),
                      _buildDeliveryList(completedDeliveries),
                    ],
                  ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.pushNamed(
          AppRoutes.createOrder,
          queryParameters: {'type': OrderType.delivery},
        ),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildTabWithBadge(String title, int count, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(title),
        if (count > 0) ...[
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Text(
              count.toString(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildStatisticsBar(List<OrderModel> deliveryOrders) {
    final theme = Theme.of(context);
    
    final totalDeliveries = deliveryOrders.length;
    final activeDeliveries = deliveryOrders
        .where((order) => order.status != 'completed' && order.status != 'cancelled')
        .length;
    final totalRevenue = deliveryOrders
        .where((order) => order.status == 'completed')
        .fold(0.0, (sum, order) => sum + order.total);
    final avgDeliveryTime = _calculateAverageDeliveryTime(deliveryOrders);
    
    return Container(
      padding: const EdgeInsets.all(16),
      color: theme.colorScheme.surface,
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'Total',
              totalDeliveries.toString(),
              Icons.delivery_dining,
              theme.colorScheme.primary,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              'Ativos',
              activeDeliveries.toString(),
              Icons.local_shipping,
              Colors.orange,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              'Receita',
              'R\$ ${totalRevenue.toStringAsFixed(0)}',
              Icons.attach_money,
              Colors.green,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              'Tempo Médio',
              '${avgDeliveryTime}min',
              Icons.timer,
              Colors.blue,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 20,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeliveryList(List<OrderModel> orders) {
    if (orders.isEmpty) {
      return _buildEmptyState();
    }

    // Apply search filter
    final filteredOrders = _searchQuery.isEmpty
        ? orders
        : orders.where((order) {
            return order.orderNumber.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                   order.customerName?.toLowerCase().contains(_searchQuery.toLowerCase()) == true ||
                   // order.customerPhone?.contains(_searchQuery) == true ||
                   order.deliveryAddress?.toLowerCase().contains(_searchQuery.toLowerCase()) == true;
          }).toList();

    if (filteredOrders.isEmpty) {
      return _buildNoResultsState();
    }

    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(ordersProvider.notifier).loadOrders();
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredOrders.length,
        itemBuilder: (context, index) {
          final order = filteredOrders[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: DeliveryCard(
              order: order,
              onTap: () => _onOrderTap(order),
              onStatusChange: (newStatus) => _onStatusChange(order, newStatus),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    final theme = Theme.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.delivery_dining_outlined,
            size: 64,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'Nenhum delivery encontrado',
            style: theme.textTheme.titleLarge?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Os pedidos de delivery aparecerão aqui',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => context.pushNamed(
              AppRoutes.createOrder,
              queryParameters: {'type': OrderType.delivery},
            ),
            icon: const Icon(Icons.add),
            label: const Text('Novo Delivery'),
          ),
        ],
      ),
    );
  }

  Widget _buildNoResultsState() {
    final theme = Theme.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'Nenhum resultado encontrado',
            style: theme.textTheme.titleLarge?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tente ajustar os filtros de busca',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ),
          const SizedBox(height: 24),
          OutlinedButton(
            onPressed: () {
              setState(() {
                _searchQuery = '';
                _searchController.clear();
              });
            },
            child: const Text('Limpar Filtros'),
          ),
        ],
      ),
    );
  }

  void _onOrderTap(OrderModel order) {
    context.pushNamed(
      AppRoutes.orderDetails,
      pathParameters: {'orderId': order.id},
    );
  }

  void _onStatusChange(OrderModel order, String newStatus) {
    ref.read(ordersProvider.notifier).updateOrderStatus(order.id, newStatus);
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Buscar Deliveries'),
        content: TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            hintText: 'Digite o número, cliente, telefone ou endereço...',
            prefixIcon: Icon(Icons.search),
          ),
          autofocus: true,
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _searchQuery = '';
                _searchController.clear();
              });
              Navigator.pop(context);
            },
            child: const Text('Limpar'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fechar'),
          ),
        ],
      ),
    );
  }

  int _calculateAverageDeliveryTime(List<OrderModel> orders) {
    final completedOrders = orders
        .where((order) => order.status == 'completed' && order.duration != null)
        .toList();
    
    if (completedOrders.isEmpty) return 0;
    
    final totalMinutes = completedOrders
        .map((order) => order.duration!.inMinutes)
        .reduce((a, b) => a + b);
    
    return (totalMinutes / completedOrders.length).round();
  }
}

class DeliveryCard extends StatelessWidget {
  final OrderModel order;
  final VoidCallback? onTap;
  final Function(String)? onStatusChange;

  const DeliveryCard({
    super.key,
    required this.order,
    this.onTap,
    this.onStatusChange,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final statusColor = _getStatusColor(order.status);
    final urgencyColor = _getUrgencyColor(order);
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: statusColor.withValues(alpha: 0.3),
            ),
            boxShadow: [
              BoxShadow(
                color: statusColor.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              // Header
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.delivery_dining,
                              size: 20,
                              color: theme.colorScheme.primary,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              '#${order.orderNumber}',
                              style: theme.textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        if (order.customerName?.isNotEmpty == true)
                          Text(
                            order.customerName!,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                            ),
                          ),
                      ],
                    ),
                  ),
                  
                  // Status and time
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      GestureDetector(
                        onTap: () => _showStatusMenu(context),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: statusColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                order.statusDisplayName,
                                style: theme.textTheme.labelMedium?.copyWith(
                                  color: statusColor,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(width: 4),
                              Icon(
                                Icons.keyboard_arrow_down,
                                size: 16,
                                color: statusColor,
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _formatTime(order.createdAt),
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Customer info
              // Phone functionality commented out as customerPhone field doesn't exist in OrderModel
              // if (order.customerPhone?.isNotEmpty == true)
              //   Row(
              //     children: [
              //       Icon(
              //         Icons.phone,
              //         size: 16,
              //         color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              //       ),
              //       const SizedBox(width: 8),
              //       Text(
              //         order.customerPhone!,
              //         style: theme.textTheme.bodyMedium?.copyWith(
              //           color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              //         ),
              //       ),
              //       const Spacer(),
              //       IconButton(
              //         onPressed: () => _makePhoneCall(order.customerPhone!),
              //         icon: Icon(
              //           Icons.call,
              //           color: theme.colorScheme.primary,
              //           size: 20,
              //         ),
              //         constraints: const BoxConstraints(),
              //         padding: EdgeInsets.zero,
              //       ),
              //   ],
              // ),
              
              if (order.deliveryAddress?.isNotEmpty == true) ...[
                const SizedBox(height: 8),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.location_on,
                      size: 16,
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        order.deliveryAddress!,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => _openMaps(order.deliveryAddress!),
                      icon: Icon(
                        Icons.map,
                        color: theme.colorScheme.primary,
                        size: 20,
                      ),
                      constraints: const BoxConstraints(),
                      padding: EdgeInsets.zero,
                    ),
                  ],
                ),
              ],
              
              const SizedBox(height: 16),
              
              // Order details
              Row(
                children: [
                  // Items and value
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.restaurant_menu,
                              size: 16,
                              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${order.itemCount} ${order.itemCount == 1 ? 'item' : 'itens'}',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(
                              Icons.attach_money,
                              size: 16,
                              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'R\$ ${order.total.toStringAsFixed(2)}',
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.primary,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  
                  // Duration and urgency
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      if (urgencyColor != null)
                        Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: urgencyColor,
                            shape: BoxShape.circle,
                          ),
                        ),
                      const SizedBox(height: 4),
                      Text(
                        order.durationText,
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: urgencyColor ?? theme.colorScheme.onSurface,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              // Delivery fee removido - campo não existe no modelo
              // if (order.deliveryFee > 0) ...[
              //   const SizedBox(height: 8),
              //   Row(
              //     children: [
              //       Icon(
              //         Icons.local_shipping,
              //         size: 16,
              //         color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              //       ),
              //       const SizedBox(width: 4),
              //       Text(
              //         'Taxa de entrega: R\$ ${order.deliveryFee.toStringAsFixed(2)}',
              //         style: theme.textTheme.bodySmall?.copyWith(
              //           color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              //         ),
              //       ),
              //     ],
              //   ),
              // ],
            ],
          ),
        ),
      ),
    );
  }

  void _showStatusMenu(BuildContext context) {
    if (onStatusChange == null) return;
    
    final statuses = [
      'pending',
      'preparing',
      'ready',
      'delivered',
      'completed',
    ];
    
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Alterar Status',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            ...statuses.map((status) => ListTile(
              leading: Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: _getStatusColor(status.name),
                  shape: BoxShape.circle,
                ),
              ),
              title: Text(_getStatusDisplayName(status.name)),
              selected: order.status == status.name,
              onTap: () {
                onStatusChange!(status.name);
                Navigator.pop(context);
              },
            )),
          ],
        ),
      ),
    );
  }

  void _makePhoneCall(String phoneNumber) {
    // TODO: Implement phone call functionality
    // This would typically use url_launcher package
    print('Calling: $phoneNumber');
  }

  void _openMaps(String address) {
    // TODO: Implement maps functionality
    // This would typically use url_launcher package to open maps
    print('Opening maps for: $address');
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'preparing':
        return Colors.blue;
      case 'ready':
        return Colors.green;
      case 'delivered':
        return Colors.teal;
      case 'completed':
        return Colors.grey;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Color? _getUrgencyColor(OrderModel order) {
    final duration = order.duration;
    if (duration == null) return null;
    
    if (duration.inMinutes > 45) return Colors.red;
    if (duration.inMinutes > 30) return Colors.orange;
    return null;
  }

  String _getStatusDisplayName(String status) {
    switch (status) {
      case 'pending':
        return 'Pendente';
      case 'preparing':
        return 'Preparando';
      case 'ready':
        return 'Pronto';
      case 'delivered':
        return 'Saiu para entrega';
      case 'completed':
        return 'Entregue';
      case 'cancelled':
        return 'Cancelado';
      default:
        return 'Desconhecido';
    }
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inMinutes < 1) {
      return 'Agora';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}min';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h';
    } else {
      return '${dateTime.day}/${dateTime.month}';
    }
  }
}