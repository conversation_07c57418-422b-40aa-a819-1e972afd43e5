# Tenant Settings - Main

**Categoria:** Tenant Settings
**Módulo:** Main
**Total de Endpoints:** 6
**Gerado em:** 27/06/2025, 20:20:27

## 📋 Endpoints

- [GET /api/tenants/settings/](#get-apitenantssettings) - Get Tenant Settings
- [PUT /api/tenants/settings/](#put-apitenantssettings) - Update Tenant Settings
- [POST /api/tenants/settings/sync-tenant-data](#post-apitenantssettingssync-tenant-data) - Sync Tenant Data
- [GET /api/tenants/tenant-settings/](#get-apitenantstenant-settings) - Get Tenant Settings
- [PUT /api/tenants/tenant-settings/](#put-apitenantstenant-settings) - Update Tenant Settings
- [POST /api/tenants/tenant-settings/sync-tenant-data](#post-apitenantstenant-settingssync-tenant-data) - Sync Tenant Data

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### TenantSettingsRead

**Descrição:** Schema for reading tenant settings.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `business_name` | unknown | ❌ | Business name |
| `business_type` | unknown | ❌ | Type of business |
| `tenant_slug` | unknown | ❌ | Unique slug for tenant identification in public URLs |
| `default_currency` | string | ❌ | Default currency code (1:1 ratio for stability) |
| `currency_config` | unknown | ❌ | Multi-currency configuration with exchange rates and formatting |
| `timezone` | string | ❌ | Timezone identifier |
| `operating_hours` | unknown | ❌ | Operating hours configuration by day of week |
| `multi_language_enabled` | boolean | ❌ | Enable multi-language support |
| `available_languages` | unknown | ❌ | List of available language codes |
| `default_language` | string | ❌ | Default language code |
| `loyalty_enabled` | boolean | ❌ | Enable loyalty system |
| `loyalty_config` | unknown | ❌ | Loyalty system configuration |
| `country` | unknown | ❌ | Country code (ISO 3166-1 alpha-2) |
| `address` | unknown | ❌ | Complete address with coordinates |
| `base_tax_rate` | string | ❌ | Base tax rate percentage |
| `tax_calculation_method` | string | ❌ | Tax calculation method: 'incremental' or 'inclusive' |
| `wifi_networks` | unknown | ❌ | WiFi networks configuration by zone |
| `social_media_links` | unknown | ❌ | Social media platform links with icons |
| `subscription_plan` | unknown | ❌ | Current subscription plan |
| `subscription_status` | string | ❌ | Subscription status |
| `additional_settings` | unknown | ❌ | Additional configuration settings |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | unknown | ❌ | - |
| `updated_at` | unknown | ❌ | - |
| `has_social_media_links` | boolean | ❌ | Whether social media links are configured |

### TenantSettingsUpdate

**Descrição:** Schema for updating tenant settings. All fields are optional.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `business_name` | unknown | ❌ | Business name |
| `business_type` | unknown | ❌ | Type of business |
| `tenant_slug` | unknown | ❌ | Unique slug for tenant identification in public URLs |
| `default_currency` | unknown | ❌ | Default currency code (1:1 ratio for stability) |
| `currency_config` | unknown | ❌ | Multi-currency configuration with exchange rates and formatting |
| `timezone` | unknown | ❌ | Timezone identifier |
| `operating_hours` | unknown | ❌ | Operating hours configuration by day of week |
| `multi_language_enabled` | unknown | ❌ | Enable multi-language support |
| `available_languages` | unknown | ❌ | List of available language codes |
| `default_language` | unknown | ❌ | Default language code |
| `loyalty_enabled` | unknown | ❌ | Enable loyalty system |
| `loyalty_config` | unknown | ❌ | Loyalty system configuration |
| `country` | unknown | ❌ | Country code (ISO 3166-1 alpha-2) |
| `address` | unknown | ❌ | Complete address with coordinates |
| `base_tax_rate` | unknown | ❌ | Base tax rate percentage |
| `tax_calculation_method` | unknown | ❌ | Tax calculation method: 'incremental' or 'inclusive' |
| `wifi_networks` | unknown | ❌ | WiFi networks configuration by zone |
| `social_media_links` | unknown | ❌ | Social media platform links with icons |
| `subscription_plan` | unknown | ❌ | Current subscription plan |
| `subscription_status` | unknown | ❌ | Subscription status |
| `additional_settings` | unknown | ❌ | Additional configuration settings |

## 🔗 Endpoints Detalhados

### GET /api/tenants/settings/ {#get-apitenantssettings}

**Resumo:** Get Tenant Settings
**Descrição:** Get current tenant settings configuration.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/tenants/settings/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/tenants/settings/ {#put-apitenantssettings}

**Resumo:** Update Tenant Settings
**Descrição:** Update tenant settings configuration.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TenantSettingsUpdate](#tenantsettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/tenants/settings/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/tenants/settings/sync-tenant-data {#post-apitenantssettingssync-tenant-data}

**Resumo:** Sync Tenant Data
**Descrição:** Manually sync tenant data from Tenant model to TenantSettings.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/tenants/settings/sync-tenant-data" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/tenants/tenant-settings/ {#get-apitenantstenant-settings}

**Resumo:** Get Tenant Settings
**Descrição:** Get current tenant settings configuration.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/tenants/tenant-settings/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/tenants/tenant-settings/ {#put-apitenantstenant-settings}

**Resumo:** Update Tenant Settings
**Descrição:** Update tenant settings configuration.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TenantSettingsUpdate](#tenantsettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/tenants/tenant-settings/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/tenants/tenant-settings/sync-tenant-data {#post-apitenantstenant-settingssync-tenant-data}

**Resumo:** Sync Tenant Data
**Descrição:** Manually sync tenant data from Tenant model to TenantSettings.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/tenants/tenant-settings/sync-tenant-data" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
