"""Payment transaction models for core payments module."""

import uuid  # noqa: E402
import enum
from typing import TYPE_CHECKING, List, Optional
from sqlalchemy import Column, String, ForeignKey, JSON, Enum, Text, Numeric, DateTime
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base  # noqa: E402

# Use TYPE_CHECKING to handle circular dependencies for type hinting
if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.core.users.models.user import User
    from app.modules.core.payments.models.payment import Payment
    from app.modules.core.payments.models.payment_processor import PaymentProcessor
    from app.modules.core.payments.models.payment_method import PaymentMethod


class PaymentStatus(str, enum.Enum):
    """Enum for payment status."""

    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    REFUNDED = "refunded"
    PARTIALLY_REFUNDED = "partially_refunded"
    CANCELLED = "cancelled"


class PaymentTransaction(Base):
    """
    Model for payment transactions.

    This represents a payment transaction processed through a payment processor.
    """

    __tablename__ = "payment_transactions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    processor_id = Column(UUID(as_uuid=True), ForeignKey("payment_processors.id"), nullable=True)
    method_id = Column(UUID(as_uuid=True), ForeignKey("core_payment_methods.id"), nullable=True)
    payment_id = Column(UUID(as_uuid=True), ForeignKey("payments.id"), nullable=True, index=True)

    # Transaction details
    amount = Column(Numeric(10, 2), nullable=False)
    currency = Column(String(3), nullable=False, default="BRL")
    status = Column(Enum(PaymentStatus), nullable=False, default=PaymentStatus.PENDING)

    # External references
    external_id = Column(String, nullable=True, index=True)  # ID in the payment processor system
    external_reference = Column(String, nullable=True)  # Reference number, authorization code, etc.

    # Source information
    source_type = Column(String, nullable=False)  # e.g., "pos", "online_store", "subscription"
    source_id = Column(UUID(as_uuid=True), nullable=True, index=True)  # ID of the source entity

    # Customer information
    customer_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    customer_email = Column(String, nullable=True)
    customer_name = Column(String, nullable=True)

    # Additional information
    description = Column(Text, nullable=True)
    transaction_metadata = Column(JSON, nullable=True)

    # Timestamps
    processed_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    tenant = relationship("app.modules.core.tenants.models.tenant.Tenant", viewonly=True)
    processor = relationship("app.modules.core.payments.models.payment_processor.PaymentProcessor")
    method = relationship("app.modules.core.payments.models.payment_method.PaymentMethod")
    customer = relationship("app.modules.core.users.models.user.User", foreign_keys=[customer_id])
    payment = relationship(
        "app.modules.core.payments.models.payment.Payment", back_populates="transactions"
    )
    refunds = relationship(
        "PaymentRefund", back_populates="transaction", cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<PaymentTransaction(id={self.id}, amount={self.amount}, status='{self.status}')>"


class PaymentRefund(Base):
    """
    Model for payment refunds.

    This represents a refund of a payment transaction.
    """

    __tablename__ = "payment_refunds"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    transaction_id = Column(
        UUID(as_uuid=True),
        ForeignKey("payment_transactions.id"),
        nullable=False,
        index=True,
    )
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)

    # Refund details
    amount = Column(Numeric(10, 2), nullable=False)
    reason = Column(Text, nullable=True)
    status = Column(Enum(PaymentStatus), nullable=False, default=PaymentStatus.PENDING)

    # External references
    external_id = Column(String, nullable=True, index=True)  # ID in the payment processor system
    external_reference = Column(String, nullable=True)  # Reference number, authorization code, etc.

    # Additional information
    refund_metadata = Column(JSON, nullable=True)

    # User who initiated the refund
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # Timestamps
    processed_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    transaction = relationship("PaymentTransaction", back_populates="refunds")
    tenant = relationship("app.modules.core.tenants.models.tenant.Tenant", viewonly=True)
    creator = relationship("app.modules.core.users.models.user.User", foreign_keys=[created_by])

    def __repr__(self):
        return f"<PaymentRefund(id={self.id}, transaction_id={self.transaction_id}, amount={self.amount}, status='{self.status}')>"  # noqa: E501
