from typing import Optional
import uuid
from datetime import datetime  # Adicionado para type hints
from pydantic import BaseModel, Field, ConfigDict  # Adicionado ConfigDict
from app.schemas import BaseSchema
from app.modules.tenants.restaurants.delivery.enums import DeliveryBoyStatus

# TimestampMixin removido, pois os campos virão do modelo ORM

# Forward declaration for DeliveryAssignmentRead to avoid circular imports
# This will be properly typed when DeliveryAssignmentRead is defined.


class DeliveryAssignmentRead(BaseModel):
    id: uuid.UUID
    # Add other relevant fields from DeliveryAssignmentRead if needed for context
    # For now, keeping it minimal as it's a forward reference.
    model_config = ConfigDict(from_attributes=True)


class DeliveryBoyBase(BaseSchema):
    name: str = Field(..., min_length=1, max_length=100, description="Name of the delivery boy")
    phone_number: Optional[str] = Field(
        None, max_length=20, description="Phone number of the delivery boy"
    )
    vehicle_type: Optional[str] = Field(
        None, max_length=50, description="Type of vehicle (e.g., Moto, Bicicleta)"
    )
    vehicle_plate: Optional[str] = Field(None, max_length=20, description="Vehicle plate number")
    current_status: DeliveryBoyStatus = Field(
        default=DeliveryBoyStatus.AVAILABLE,
        description="Current status of the delivery boy",
    )
    is_active: bool = Field(default=True, description="Indicates if the delivery boy is active")
    user_id: uuid.UUID = Field(..., description="Associated user ID")


class DeliveryBoyCreate(DeliveryBoyBase):
    tenant_id: (
        uuid.UUID
    )  # Added tenant_id as it's required on creation, usually from path or context


class DeliveryBoyUpdate(BaseSchema):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    phone_number: Optional[str] = Field(None, max_length=20)
    vehicle_type: Optional[str] = Field(None, max_length=50)
    vehicle_plate: Optional[str] = Field(None, max_length=20)
    current_status: Optional[DeliveryBoyStatus] = None
    is_active: Optional[bool] = None


class DeliveryBoyRead(DeliveryBoyBase):  # Removido TimestampMixin da herança
    id: uuid.UUID
    tenant_id: uuid.UUID
    created_at: datetime  # Adicionado explicitamente
    updated_at: datetime  # Adicionado explicitamente
    # If you want to include assigned_orders in the read schema:
    # from .delivery_assignment import DeliveryAssignmentRead # Proper import
    # assigned_orders: List[DeliveryAssignmentRead] = []

    model_config = ConfigDict(from_attributes=True)
