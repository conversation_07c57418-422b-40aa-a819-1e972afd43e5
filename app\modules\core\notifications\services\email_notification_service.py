"""
Email Notification Service

Serviço para envio de notificações por email.
"""

import logging
import smtplib
from datetime import datetime
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from typing import Dict, List, Optional
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.modules.core.users.models.user import User

from ..models import Notification, NotificationPriority

logger = logging.getLogger(__name__)


class EmailNotificationService:
    """Serviço para envio de notificações por email."""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.smtp_server = getattr(settings, 'SMTP_SERVER', 'localhost')
        self.smtp_port = getattr(settings, 'SMTP_PORT', 587)
        self.smtp_username = getattr(settings, 'SMTP_USERNAME', '')
        self.smtp_password = getattr(settings, 'SMTP_PASSWORD', '')
        self.smtp_use_tls = getattr(settings, 'SMTP_USE_TLS', True)
        self.from_email = getattr(settings, 'FROM_EMAIL', '<EMAIL>')
        self.from_name = getattr(settings, 'FROM_NAME', 'Trix Platform')

    async def send_notification_email(
        self,
        notification: Notification,
        recipient: User,
        template_type: str = "default"
    ) -> bool:
        """
        Envia notificação por email.
        
        Args:
            notification: Notificação a ser enviada
            recipient: Destinatário
            template_type: Tipo de template a usar
            
        Returns:
            True se enviado com sucesso
        """
        try:
            # Verifica se usuário aceita emails
            if not self._should_send_email(notification, recipient):
                logger.info(f"Email não enviado para {recipient.email} - preferências do usuário")
                return False

            # Gera conteúdo do email
            subject, html_content, text_content = self._generate_email_content(
                notification, recipient, template_type
            )

            # Envia email
            success = await self._send_email(
                to_email=recipient.email,
                to_name=getattr(recipient, 'name', recipient.email.split('@')[0]),
                subject=subject,
                html_content=html_content,
                text_content=text_content
            )

            if success:
                logger.info(f"Email enviado com sucesso para {recipient.email}")
                await self._log_email_sent(notification, recipient)
            else:
                logger.error(f"Falha ao enviar email para {recipient.email}")

            return success

        except Exception as e:
            logger.error(f"Erro ao enviar email para {recipient.email}: {e}")
            return False

    async def send_bulk_notification_emails(
        self,
        notification: Notification,
        recipients: List[User],
        template_type: str = "default"
    ) -> Dict[str, int]:
        """
        Envia notificação por email para múltiplos destinatários.
        
        Args:
            notification: Notificação
            recipients: Lista de destinatários
            template_type: Tipo de template
            
        Returns:
            Estatísticas de envio
        """
        stats = {"sent": 0, "failed": 0, "skipped": 0}

        for recipient in recipients:
            try:
                success = await self.send_notification_email(
                    notification, recipient, template_type
                )
                
                if success:
                    stats["sent"] += 1
                else:
                    stats["failed"] += 1
                    
            except Exception as e:
                logger.error(f"Erro ao enviar email para {recipient.email}: {e}")
                stats["failed"] += 1

        logger.info(f"Envio em lote concluído: {stats}")
        return stats

    def _should_send_email(self, notification: Notification, user: User) -> bool:
        """
        Verifica se deve enviar email para o usuário.
        
        Args:
            notification: Notificação
            user: Usuário
            
        Returns:
            True se deve enviar
        """
        # Verifica se usuário está ativo
        if not user.is_active:
            return False

        # Sempre envia emails para notificações urgentes
        if notification.priority == NotificationPriority.URGENT:
            return True

        # TODO: Implementar verificação de preferências do usuário
        # Por enquanto, envia para notificações de alta prioridade
        if notification.priority == NotificationPriority.HIGH:
            return True

        # Para outras prioridades, verifica preferências (mock)
        user_preferences = self._get_user_email_preferences(user)
        return user_preferences.get("email_notifications", False)

    def _get_user_email_preferences(self, user: User) -> Dict:
        """
        Obtém preferências de email do usuário.
        
        Args:
            user: Usuário
            
        Returns:
            Dicionário com preferências
        """
        # TODO: Implementar sistema de preferências real
        # Por enquanto, retorna preferências padrão
        return {
            "email_notifications": True,
            "urgent_only": False,
            "daily_digest": False,
            "marketing_emails": False
        }

    def _generate_email_content(
        self,
        notification: Notification,
        recipient: User,
        template_type: str
    ) -> tuple[str, str, str]:
        """
        Gera conteúdo do email.
        
        Args:
            notification: Notificação
            recipient: Destinatário
            template_type: Tipo de template
            
        Returns:
            Tupla (subject, html_content, text_content)
        """
        # Subject
        priority_prefix = ""
        if notification.priority == NotificationPriority.URGENT:
            priority_prefix = "[URGENTE] "
        elif notification.priority == NotificationPriority.HIGH:
            priority_prefix = "[IMPORTANTE] "

        subject = f"{priority_prefix}{notification.title}"

        # Conteúdo texto
        text_content = f"""
Olá {getattr(recipient, 'name', recipient.email.split('@')[0])},

{notification.content}

---
Trix Platform
{datetime.utcnow().strftime('%d/%m/%Y %H:%M')}

Para gerenciar suas preferências de notificação, acesse: {self._get_base_url()}/profile/notifications
        """.strip()

        # Conteúdo HTML
        html_content = self._generate_html_template(
            notification, recipient, template_type
        )

        return subject, html_content, text_content

    def _generate_html_template(
        self,
        notification: Notification,
        recipient: User,
        template_type: str
    ) -> str:
        """
        Gera template HTML para o email.
        
        Args:
            notification: Notificação
            recipient: Destinatário
            template_type: Tipo de template
            
        Returns:
            HTML do email
        """
        recipient_name = getattr(recipient, 'name', recipient.email.split('@')[0])
        base_url = self._get_base_url()
        
        # Cor baseada na prioridade
        priority_colors = {
            NotificationPriority.URGENT: "#dc3545",
            NotificationPriority.HIGH: "#fd7e14",
            NotificationPriority.NORMAL: "#0d6efd",
            NotificationPriority.LOW: "#6c757d"
        }
        
        priority_color = priority_colors.get(notification.priority, "#0d6efd")

        html = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{notification.title}</title>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background-color: {priority_color}; color: white; padding: 20px; text-align: center; }}
        .content {{ background-color: #f8f9fa; padding: 30px; }}
        .footer {{ background-color: #e9ecef; padding: 15px; text-align: center; font-size: 12px; }}
        .button {{ display: inline-block; padding: 12px 24px; background-color: {priority_color}; color: white; text-decoration: none; border-radius: 5px; margin: 15px 0; }}
        .priority {{ font-weight: bold; color: {priority_color}; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Trix Platform</h1>
            <p>Nova Notificação</p>
        </div>
        
        <div class="content">
            <h2>{notification.title}</h2>
            
            <p>Olá <strong>{recipient_name}</strong>,</p>
            
            <p>{notification.content}</p>
            
            <p class="priority">Prioridade: {notification.priority.value.upper()}</p>
        """

        # Adiciona botão de ação se houver URL
        if notification.action_url:
            action_url = f"{base_url}{notification.action_url}"
            html += f"""
            <p>
                <a href="{action_url}" class="button">Ver Detalhes</a>
            </p>
            """

        html += f"""
        </div>
        
        <div class="footer">
            <p>Enviado em {datetime.utcnow().strftime('%d/%m/%Y às %H:%M')}</p>
            <p>
                <a href="{base_url}/profile/notifications">Gerenciar Preferências</a> | 
                <a href="{base_url}/notifications">Ver Todas as Notificações</a>
            </p>
            <p>© {datetime.utcnow().year} Trix Platform. Todos os direitos reservados.</p>
        </div>
    </div>
</body>
</html>
        """

        return html

    async def _send_email(
        self,
        to_email: str,
        to_name: str,
        subject: str,
        html_content: str,
        text_content: str
    ) -> bool:
        """
        Envia email via SMTP.
        
        Args:
            to_email: Email do destinatário
            to_name: Nome do destinatário
            subject: Assunto
            html_content: Conteúdo HTML
            text_content: Conteúdo texto
            
        Returns:
            True se enviado com sucesso
        """
        try:
            # Cria mensagem
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = f"{self.from_name} <{self.from_email}>"
            msg['To'] = f"{to_name} <{to_email}>"

            # Adiciona conteúdo
            part1 = MIMEText(text_content, 'plain', 'utf-8')
            part2 = MIMEText(html_content, 'html', 'utf-8')
            
            msg.attach(part1)
            msg.attach(part2)

            # Envia via SMTP
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                if self.smtp_use_tls:
                    server.starttls()
                
                if self.smtp_username and self.smtp_password:
                    server.login(self.smtp_username, self.smtp_password)
                
                server.send_message(msg)

            return True

        except Exception as e:
            logger.error(f"Erro SMTP ao enviar email: {e}")
            return False

    async def _log_email_sent(self, notification: Notification, recipient: User):
        """
        Registra envio de email.
        
        Args:
            notification: Notificação
            recipient: Destinatário
        """
        # TODO: Implementar log de emails enviados
        # Pode ser uma tabela separada ou campo na notificação
        pass

    def _get_base_url(self) -> str:
        """Obtém URL base da aplicação."""
        return getattr(settings, 'BASE_URL', 'https://trix.com')

    async def send_test_email(self, to_email: str) -> bool:
        """
        Envia email de teste.
        
        Args:
            to_email: Email de destino
            
        Returns:
            True se enviado com sucesso
        """
        subject = "Teste de Email - Trix Platform"
        text_content = "Este é um email de teste do sistema de notificações da Trix Platform."
        html_content = f"""
        <html>
        <body>
            <h2>Teste de Email</h2>
            <p>Este é um email de teste do sistema de notificações da Trix Platform.</p>
            <p>Enviado em: {datetime.utcnow().strftime('%d/%m/%Y %H:%M:%S')}</p>
        </body>
        </html>
        """

        return await self._send_email(
            to_email=to_email,
            to_name="Usuário Teste",
            subject=subject,
            html_content=html_content,
            text_content=text_content
        )
