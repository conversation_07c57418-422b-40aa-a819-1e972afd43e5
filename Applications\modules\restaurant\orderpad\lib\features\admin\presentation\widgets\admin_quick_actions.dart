import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class AdminQuickActions extends StatelessWidget {
  const AdminQuickActions({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Ações Rápidas',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.5,
              children: [
                _QuickActionCard(
                  title: 'Gerenciar Mesas',
                  icon: Icons.table_restaurant,
                  color: Colors.blue,
                  onTap: () => context.go('/admin/tables'),
                ),
                _QuickActionCard(
                  title: 'Ver Pedidos',
                  icon: Icons.receipt_long,
                  color: Colors.green,
                  onTap: () => context.go('/admin/orders'),
                ),
                _QuickActionCard(
                  title: 'Relatórios',
                  icon: Icons.analytics,
                  color: Colors.purple,
                  onTap: () => context.go('/admin/reports'),
                ),
                _QuickActionCard(
                  title: 'Configurações',
                  icon: Icons.settings,
                  color: Colors.orange,
                  onTap: () => context.go('/admin/settings'),
                ),
                _QuickActionCard(
                  title: 'Funcionários',
                  icon: Icons.people,
                  color: Colors.teal,
                  onTap: () => context.go('/admin/staff'),
                ),
                _QuickActionCard(
                  title: 'Cardápio',
                  icon: Icons.menu_book,
                  color: Colors.red,
                  onTap: () => context.go('/admin/menu'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _QuickActionCard extends StatelessWidget {
  final String title;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;

  const _QuickActionCard({
    required this.title,
    required this.icon,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: color.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 32,
                color: color,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}