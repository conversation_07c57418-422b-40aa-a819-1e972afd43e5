'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeftIcon, CreditCardIcon, TruckIcon, MapPinIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { useEshopCart } from '@/contexts/EshopCartContext';
import { useAuth } from '@/lib/auth/AuthProvider';
import { formatCurrency } from '@/lib/utils';
import { toast } from 'sonner';
import { apiClient } from '@/lib/api/apiClient';

interface CheckoutSession {
  id: string;
  status: string;
  subtotal: number;
  tax_amount: number;
  discount_amount: number;
  shipping_cost: number;
  total_amount: number;
  currency: string;
  shipping_address?: any;
  billing_address?: any;
  payment_method?: string;
  expires_at?: string;
}

interface ShippingAddress {
  street: string;
  number: string;
  complement?: string;
  neighborhood: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
}

export default function EshopCheckoutPage() {
  const router = useRouter();
  const { cart, marketContext } = useEshopCart();
  const { isAuthenticated, user } = useAuth();
  
  const [checkoutSession, setCheckoutSession] = useState<CheckoutSession | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  
  // Form states
  const [shippingAddress, setShippingAddress] = useState<ShippingAddress>({
    street: '',
    number: '',
    complement: '',
    neighborhood: '',
    city: '',
    state: '',
    postal_code: '',
    country: 'BR'
  });
  
  const [paymentMethod, setPaymentMethod] = useState<'credit_card' | 'debit_card' | 'pix' | 'bank_transfer'>('credit_card');
  const [sameAsBilling, setSameAsBilling] = useState(true);

  // Redirect if cart is empty
  useEffect(() => {
    if (!cart || cart.items.length === 0) {
      router.push('/eshop');
      return;
    }
  }, [cart, router]);

  // Initialize checkout session
  useEffect(() => {
    if (cart && cart.items.length > 0) {
      initiateCheckout();
    }
  }, [cart]);

  const initiateCheckout = async () => {
    try {
      setIsLoading(true);
      
      const headers: Record<string, string> = {};
      if (!isAuthenticated) {
        const sessionId = localStorage.getItem('eshop_session_id');
        if (sessionId) {
          headers['X-Session-ID'] = sessionId;
        }
      }

      const response = await apiClient.post(
        '/modules/core/functions/checkout/initiate',
        {
          cart_id: cart?.id,
          market_context: marketContext,
          shipping_address: null,
          billing_address: null
        },
        { headers }
      );

      if (response.data.success) {
        setCheckoutSession(response.data.checkout_session);
      } else {
        toast.error(response.data.message || 'Erro ao iniciar checkout');
        router.push('/eshop');
      }
    } catch (error: any) {
      console.error('Error initiating checkout:', error);
      toast.error(error.response?.data?.detail || 'Erro ao iniciar checkout');
      router.push('/eshop');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddressSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!checkoutSession) return;
    
    try {
      setIsLoading(true);
      
      const headers: Record<string, string> = {};
      if (!isAuthenticated) {
        const sessionId = localStorage.getItem('eshop_session_id');
        if (sessionId) {
          headers['X-Session-ID'] = sessionId;
        }
      }

      const response = await apiClient.put(
        `/modules/core/functions/checkout/${checkoutSession.id}`,
        {
          shipping_address: shippingAddress,
          billing_address: sameAsBilling ? shippingAddress : null
        },
        { headers }
      );

      if (response.data.success) {
        setCheckoutSession(response.data.checkout_session);
        setCurrentStep(2);
        toast.success('Endereço salvo com sucesso');
      } else {
        toast.error(response.data.message || 'Erro ao salvar endereço');
      }
    } catch (error: any) {
      console.error('Error updating address:', error);
      toast.error(error.response?.data?.detail || 'Erro ao salvar endereço');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePaymentSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!checkoutSession) return;
    
    try {
      setIsLoading(true);
      
      const headers: Record<string, string> = {};
      if (!isAuthenticated) {
        const sessionId = localStorage.getItem('eshop_session_id');
        if (sessionId) {
          headers['X-Session-ID'] = sessionId;
        }
      }

      const response = await apiClient.post(
        `/modules/core/functions/checkout/${checkoutSession.id}/complete`,
        {
          payment_method: paymentMethod,
          payment_provider: 'stripe', // or other provider
          save_payment_method: false
        },
        { headers }
      );

      if (response.data.success) {
        toast.success('Pedido realizado com sucesso!');
        router.push(`/eshop/order-confirmation/${response.data.order_id}`);
      } else {
        toast.error(response.data.message || 'Erro ao processar pagamento');
      }
    } catch (error: any) {
      console.error('Error completing checkout:', error);
      toast.error(error.response?.data?.detail || 'Erro ao processar pagamento');
    } finally {
      setIsLoading(false);
    }
  };

  if (!cart || cart.items.length === 0) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.back()}
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Voltar
            </Button>
            <h1 className="text-2xl font-bold text-gray-900">
              Checkout {marketContext === 'b2b' ? 'B2B' : 'B2C'}
            </h1>
          </div>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <nav aria-label="Progress">
            <ol className="flex items-center">
              {[
                { id: 1, name: 'Endereço', icon: MapPinIcon },
                { id: 2, name: 'Pagamento', icon: CreditCardIcon },
                { id: 3, name: 'Confirmação', icon: TruckIcon }
              ].map((step, stepIdx) => (
                <li key={step.name} className={`${stepIdx !== 2 ? 'pr-8 sm:pr-20' : ''} relative`}>
                  <div className="flex items-center">
                    <div
                      className={`relative flex h-8 w-8 items-center justify-center rounded-full ${
                        step.id <= currentStep
                          ? 'bg-indigo-600 text-white'
                          : 'border-2 border-gray-300 bg-white text-gray-500'
                      }`}
                    >
                      <step.icon className="h-4 w-4" />
                    </div>
                    <span className={`ml-4 text-sm font-medium ${
                      step.id <= currentStep ? 'text-indigo-600' : 'text-gray-500'
                    }`}>
                      {step.name}
                    </span>
                  </div>
                  {stepIdx !== 2 && (
                    <div
                      className={`absolute top-4 left-4 -ml-px mt-0.5 h-0.5 w-full ${
                        step.id < currentStep ? 'bg-indigo-600' : 'bg-gray-300'
                      }`}
                    />
                  )}
                </li>
              ))}
            </ol>
          </nav>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {currentStep === 1 && (
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-6">Endereço de Entrega</h2>
                <form onSubmit={handleAddressSubmit} className="space-y-4">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">CEP</label>
                      <input
                        type="text"
                        value={shippingAddress.postal_code}
                        onChange={(e) => setShippingAddress(prev => ({ ...prev, postal_code: e.target.value }))}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Estado</label>
                      <input
                        type="text"
                        value={shippingAddress.state}
                        onChange={(e) => setShippingAddress(prev => ({ ...prev, state: e.target.value }))}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                        required
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Cidade</label>
                    <input
                      type="text"
                      value={shippingAddress.city}
                      onChange={(e) => setShippingAddress(prev => ({ ...prev, city: e.target.value }))}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Bairro</label>
                    <input
                      type="text"
                      value={shippingAddress.neighborhood}
                      onChange={(e) => setShippingAddress(prev => ({ ...prev, neighborhood: e.target.value }))}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                      required
                    />
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <div className="sm:col-span-2">
                      <label className="block text-sm font-medium text-gray-700">Rua</label>
                      <input
                        type="text"
                        value={shippingAddress.street}
                        onChange={(e) => setShippingAddress(prev => ({ ...prev, street: e.target.value }))}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Número</label>
                      <input
                        type="text"
                        value={shippingAddress.number}
                        onChange={(e) => setShippingAddress(prev => ({ ...prev, number: e.target.value }))}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Complemento</label>
                    <input
                      type="text"
                      value={shippingAddress.complement}
                      onChange={(e) => setShippingAddress(prev => ({ ...prev, complement: e.target.value }))}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>

                  <div className="flex items-center">
                    <input
                      id="same-as-billing"
                      type="checkbox"
                      checked={sameAsBilling}
                      onChange={(e) => setSameAsBilling(e.target.checked)}
                      className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                    />
                    <label htmlFor="same-as-billing" className="ml-2 block text-sm text-gray-900">
                      Usar mesmo endereço para cobrança
                    </label>
                  </div>

                  <Button type="submit" className="w-full" disabled={isLoading}>
                    Continuar para Pagamento
                  </Button>
                </form>
              </div>
            )}

            {currentStep === 2 && (
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-6">Método de Pagamento</h2>
                <form onSubmit={handlePaymentSubmit} className="space-y-4">
                  <div className="space-y-3">
                    {[
                      { id: 'credit_card', name: 'Cartão de Crédito', description: 'Visa, Mastercard, Elo' },
                      { id: 'debit_card', name: 'Cartão de Débito', description: 'Débito online' },
                      { id: 'pix', name: 'PIX', description: 'Pagamento instantâneo' },
                      { id: 'bank_transfer', name: 'Transferência Bancária', description: 'TED/DOC' }
                    ].map((method) => (
                      <div key={method.id} className="flex items-center">
                        <input
                          id={method.id}
                          name="payment-method"
                          type="radio"
                          value={method.id}
                          checked={paymentMethod === method.id}
                          onChange={(e) => setPaymentMethod(e.target.value as any)}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                        />
                        <label htmlFor={method.id} className="ml-3 block text-sm font-medium text-gray-700">
                          {method.name}
                          <span className="block text-sm text-gray-500">{method.description}</span>
                        </label>
                      </div>
                    ))}
                  </div>

                  <div className="flex space-x-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setCurrentStep(1)}
                      className="flex-1"
                    >
                      Voltar
                    </Button>
                    <Button type="submit" className="flex-1" disabled={isLoading}>
                      Finalizar Pedido
                    </Button>
                  </div>
                </form>
              </div>
            )}
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow p-6 sticky top-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Resumo do Pedido</h2>
              
              <div className="space-y-3">
                {cart.items.map((item) => (
                  <div key={item.id} className="flex justify-between text-sm">
                    <div>
                      <p className="font-medium text-gray-900">{item.product_name}</p>
                      <p className="text-gray-500">Qtd: {item.quantity}</p>
                    </div>
                    <p className="font-medium text-gray-900">{formatCurrency(item.total_price)}</p>
                  </div>
                ))}
              </div>

              <div className="border-t border-gray-200 pt-4 mt-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <p className="text-gray-600">Subtotal</p>
                  <p className="font-medium">{formatCurrency(checkoutSession?.subtotal || cart.subtotal)}</p>
                </div>
                {checkoutSession?.shipping_cost && checkoutSession.shipping_cost > 0 && (
                  <div className="flex justify-between text-sm">
                    <p className="text-gray-600">Frete</p>
                    <p className="font-medium">{formatCurrency(checkoutSession.shipping_cost)}</p>
                  </div>
                )}
                {checkoutSession?.tax_amount && checkoutSession.tax_amount > 0 && (
                  <div className="flex justify-between text-sm">
                    <p className="text-gray-600">Impostos</p>
                    <p className="font-medium">{formatCurrency(checkoutSession.tax_amount)}</p>
                  </div>
                )}
                <div className="flex justify-between text-lg font-bold border-t border-gray-200 pt-2">
                  <p>Total</p>
                  <p>{formatCurrency(checkoutSession?.total_amount || cart.total_amount)}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
