"""Email Alias model for the Email module."""

import uuid  # noqa: E402
from datetime import datetime
from typing import TYPE_CHECKING, Optional

from sqlalchemy import (  # noqa: E402
    Column,
    String,
    Boolean,
    DateTime,
    ForeignKey,
    Index,
)
from sqlalchemy.dialects.postgresql import UUID  # noqa: E402
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import Base  # noqa: E402

if TYPE_CHECKING:
    from app.modules.shared.email.models.email_domain import EmailDomain  # noqa: E402


class EmailAlias(Base):
    """Email Alias model.

    Represents an email alias (source@domain -> destination).
    """

    __tablename__ = "email_aliases"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Email alias details
    email_domain_id = Column(
        UUID(as_uuid=True), ForeignKey("email_domains.id"), nullable=False, index=True
    )
    source = Column(
        String, nullable=False
    )  # The alias email (can be full email or just local part)
    destination = Column(String, nullable=False)  # The target email (full email)

    # Status
    is_active = Column(Boolean, default=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )

    # Relationships
    email_domain = relationship("EmailDomain", back_populates="email_aliases")

    # Indexes
    __table_args__ = (
        Index(
            "ix_email_aliases_email_domain_id_source",
            email_domain_id,
            source,
            unique=True,
        ),
    )

    def __repr__(self):
        return f"<EmailAlias(id={self.id}, source='{self.source}', destination='{self.destination}')>"  # noqa: E501
