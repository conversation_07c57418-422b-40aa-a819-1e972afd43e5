"""Quota Service for Email module."""

import logging  # noqa: E402
import uuid
import os
from typing import Optional

from sqlalchemy import select, func, and_  # noqa: E402
from sqlalchemy.ext.asyncio import AsyncSession

from app.modules.shared.email.models import EmailAccount, EmailMetadata  # noqa: E402
from app.modules.shared.email.api.schemas import EmailAccountUsage

logger = logging.getLogger(__name__)


class QuotaService:
    """Service for managing email storage quotas."""

    async def get_usage(self, db: AsyncSession, account_id: uuid.UUID) -> EmailAccountUsage:
        """Get storage usage for an account."""
        # Get account
        account = await db.get(EmailAccount, account_id)
        if not account:
            raise ValueError(f"Account with ID {account_id} not found")

        # Get total size of emails
        result = await db.execute(
            select(func.sum(EmailMetadata.size_bytes)).where(
                and_(
                    EmailMetadata.email_account_id == account_id,
                    EmailMetadata.is_deleted.is_(False),
                )
            )
        )
        total_bytes = result.scalar() or 0

        # Calculate quota in bytes
        quota_bytes = account.quota_mb * 1024 * 1024

        # Calculate usage percentage
        usage_percentage = (total_bytes / quota_bytes) * 100 if quota_bytes > 0 else 0

        return EmailAccountUsage(
            used_bytes=total_bytes,
            quota_bytes=quota_bytes,
            usage_percentage=usage_percentage,
        )

    async def check_quota(self, db: AsyncSession, account_id: uuid.UUID, size_to_add: int) -> bool:
        """Check if adding an email of size_to_add would exceed quota."""
        # Get usage
        usage = await self.get_usage(db, account_id)

        # Check if adding size_to_add would exceed quota
        return (usage.used_bytes + size_to_add) <= usage.quota_bytes

    async def update_quota(
        self, db: AsyncSession, account_id: uuid.UUID, new_quota_mb: int
    ) -> EmailAccount:
        """Update quota for an account."""
        # Get account
        account = await db.get(EmailAccount, account_id)
        if not account:
            raise ValueError(f"Account with ID {account_id} not found")

        # Update quota
        account.quota_mb = new_quota_mb
        await db.commit()
        await db.refresh(account)

        return account
