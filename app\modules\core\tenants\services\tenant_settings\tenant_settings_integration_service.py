"""
Integration service for tenant settings with external systems and validation.
"""

import uuid
from typing import List, Optional, Dict, Any

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.modules.core.tenants.schemas.tenant_settings.responses import (
    LanguageOption,
    CurrencyOption,
    CurrencyConfiguration,
    SubscriptionInfo,
)
from app.modules.core.i18n.models.language import Language
from app.core.exceptions import BusinessLogicError


class TenantSettingsIntegrationService:
    """Service for integrating tenant settings with external systems."""

    # Integration Methods
    async def get_available_languages(self, db: AsyncSession) -> List[LanguageOption]:
        """Get available languages from the i18n system."""
        result = await db.execute(
            select(Language).where(Language.is_active == True)
        )
        languages = result.scalars().all()

        return [
            LanguageOption(
                code=lang.code,
                name=lang.name,
                native_name=lang.name,  # Assuming name is in native script
                is_active=lang.is_active,
                is_default=lang.is_default,
            )
            for lang in languages
        ]

    async def get_available_currencies(self) -> List[CurrencyOption]:
        """Get available currencies with formatting options."""
        # This would typically come from a currency service or external API
        # For now, returning common currencies with Brazilian formatting
        return [
            CurrencyOption(
                code="BRL",
                name="Real Brasileiro",
                symbol="R$",
                decimal_places=2,
                decimal_separator=",",
                thousands_separator=".",
                symbol_position="left",
                symbol_spacing=True,
            ),
            CurrencyOption(
                code="USD",
                name="US Dollar",
                symbol="$",
                decimal_places=2,
                decimal_separator=".",
                thousands_separator=",",
                symbol_position="left",
                symbol_spacing=False,
            ),
            CurrencyOption(
                code="EUR",
                name="Euro",
                symbol="€",
                decimal_places=2,
                decimal_separator=",",
                thousands_separator=".",
                symbol_position="left",
                symbol_spacing=True,
            ),
            CurrencyOption(
                code="GBP",
                name="British Pound",
                symbol="£",
                decimal_places=2,
                decimal_separator=".",
                thousands_separator=",",
                symbol_position="left",
                symbol_spacing=False,
            ),
            CurrencyOption(
                code="JPY",
                name="Japanese Yen",
                symbol="¥",
                decimal_places=0,
                decimal_separator=".",
                thousands_separator=",",
                symbol_position="left",
                symbol_spacing=False,
            ),
        ]

    async def get_subscription_info(
        self, db: AsyncSession, tenant_id: uuid.UUID
    ) -> SubscriptionInfo:
        """Get subscription information for a tenant."""
        # This would typically integrate with a subscription service
        # For now, returning mock data
        return SubscriptionInfo(
            plan_name="Professional",
            plan_id="prof_plan_001",
            status="active",
            start_date=None,
            end_date=None,
            next_billing_date=None,
            features=[
                {"name": "Multi-language Support", "enabled": True},
                {"name": "Advanced Analytics", "enabled": True},
                {"name": "Custom Branding", "enabled": True},
                {"name": "API Access", "enabled": True},
            ],
            usage={
                "monthly_transactions": 1250,
                "storage_used_gb": 2.5,
                "api_calls": 5000,
            },
            limits={
                "monthly_transactions": 10000,
                "storage_gb": 50,
                "api_calls": 50000,
            },
        )

    # Validation Methods
    async def _validate_currency_config(self, currency_config: Dict[str, Any]) -> None:
        """Validate currency configuration structure and values."""
        required_fields = [
            "default_currency",
            "enabled_currencies",
            "exchange_rates",
            "currency_formatting",
        ]

        for field in required_fields:
            if field not in currency_config:
                raise BusinessLogicError(f"Missing required field: {field}")

        # Validate default currency is in enabled currencies
        default_currency = currency_config["default_currency"]
        enabled_currencies = currency_config["enabled_currencies"]

        if default_currency not in enabled_currencies:
            raise BusinessLogicError(
                "Default currency must be included in enabled currencies"
            )

        # Validate exchange rates
        exchange_rates = currency_config["exchange_rates"]
        if default_currency not in exchange_rates:
            raise BusinessLogicError(
                "Default currency must have an exchange rate (should be 1.0)"
            )

        if exchange_rates[default_currency] != 1.0:
            raise BusinessLogicError(
                "Default currency exchange rate must be 1.0 for stability"
            )

        # Validate currency formatting
        currency_formatting = currency_config["currency_formatting"]
        for currency in enabled_currencies:
            if currency not in currency_formatting:
                raise BusinessLogicError(
                    f"Currency formatting missing for enabled currency: {currency}"
                )

            formatting = currency_formatting[currency]
            required_format_fields = [
                "decimal_separator",
                "thousands_separator",
                "symbol_position",
                "symbol_spacing",
            ]

            for format_field in required_format_fields:
                if format_field not in formatting:
                    raise BusinessLogicError(
                        f"Missing formatting field {format_field} for currency {currency}"
                    )

            # Validate separator values
            if formatting["decimal_separator"] not in [".", ","]:
                raise BusinessLogicError(
                    "Decimal separator must be '.' or ','"
                )

            if formatting["thousands_separator"] not in [".", ",", " ", ""]:
                raise BusinessLogicError(
                    "Thousands separator must be '.', ',', space, or empty"
                )

            # Validate symbol position
            if formatting["symbol_position"] not in ["left", "right"]:
                raise BusinessLogicError(
                    "Symbol position must be 'left' or 'right'"
                )

    async def _validate_operating_hours(self, operating_hours: Dict[str, Any]) -> None:
        """Validate operating hours configuration with support for service_hours, break_periods, and happy_hour."""
        valid_days = [
            "monday",
            "tuesday",
            "wednesday",
            "thursday",
            "friday",
            "saturday",
            "sunday",
        ]

        for day, config in operating_hours.items():
            if day not in valid_days:
                raise BusinessLogicError(f"Invalid day: {day}")

            if not isinstance(config, dict):
                raise BusinessLogicError(f"Day configuration must be an object: {day}")

            # Validate required fields
            if "is_open" not in config:
                raise BusinessLogicError(f"Missing required field 'is_open' for day {day}")

            # If the day is open, validate time slot structures
            if config["is_open"]:
                # Check for new structure (service_hours, break_periods, happy_hour)
                has_new_structure = any(
                    key in config for key in ["service_hours", "break_periods", "happy_hour"]
                )

                # Check for legacy structure (open_time, close_time, or slots)
                has_legacy_structure = any(
                    key in config for key in ["open_time", "close_time", "slots"]
                )

                if has_new_structure:
                    # Validate new structure
                    await self._validate_time_slot_categories(day, config)
                elif has_legacy_structure:
                    # Validate legacy structure for backward compatibility
                    await self._validate_legacy_operating_hours(day, config)
                else:
                    # If open but no time configuration, require at least service_hours
                    raise BusinessLogicError(
                        f"Day {day} is marked as open but has no time configuration. "
                        "Please provide service_hours, or use legacy format with open_time/close_time."
                    )

    async def _validate_time_slot_categories(self, day: str, config: Dict[str, Any]) -> None:
        """Validate time slot categories (service_hours, break_periods, happy_hour)."""
        valid_slot_types = ["service_hours", "break_periods", "happy_hour"]

        for slot_type in valid_slot_types:
            if slot_type in config:
                slots = config[slot_type]
                if not isinstance(slots, list):
                    raise BusinessLogicError(f"{slot_type} for {day} must be a list")

                for i, slot in enumerate(slots):
                    await self._validate_time_slot(day, slot_type, i, slot)

    async def _validate_time_slot(self, day: str, slot_type: str, index: int, slot: Dict[str, Any]) -> None:
        """Validate individual time slot."""
        if not isinstance(slot, dict):
            raise BusinessLogicError(f"Time slot {index} in {slot_type} for {day} must be an object")

        # Required fields for time slots
        required_fields = ["id", "open", "close", "type"]
        for field in required_fields:
            if field not in slot:
                raise BusinessLogicError(
                    f"Missing required field '{field}' in time slot {index} of {slot_type} for {day}"
                )

        # Validate time format (HH:MM)
        time_pattern = r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$'
        import re

        if not re.match(time_pattern, slot["open"]):
            raise BusinessLogicError(
                f"Invalid open time format in {slot_type} slot {index} for {day}. Use HH:MM format."
            )

        if not re.match(time_pattern, slot["close"]):
            raise BusinessLogicError(
                f"Invalid close time format in {slot_type} slot {index} for {day}. Use HH:MM format."
            )

        # Validate that close time is after open time
        if slot["close"] <= slot["open"]:
            raise BusinessLogicError(
                f"Close time must be after open time in {slot_type} slot {index} for {day}"
            )

        # Validate slot type matches category
        expected_type = slot_type.replace("_hours", "").replace("_periods", "")
        if expected_type == "break":
            expected_type = "break"
        elif expected_type == "happy":
            expected_type = "happy_hour"

        if slot["type"] not in ["service", "break", "happy_hour"]:
            raise BusinessLogicError(
                f"Invalid slot type '{slot['type']}' in {slot_type} slot {index} for {day}. "
                "Must be 'service', 'break', or 'happy_hour'."
            )

    async def _validate_legacy_operating_hours(self, day: str, config: Dict[str, Any]) -> None:
        """Validate legacy operating hours format for backward compatibility."""
        if "slots" in config:
            # Legacy slots format
            slots = config["slots"]
            if not isinstance(slots, list):
                raise BusinessLogicError(f"Slots for {day} must be a list")

            for i, slot in enumerate(slots):
                if not isinstance(slot, dict):
                    raise BusinessLogicError(f"Slot {i} for {day} must be an object")

                required_fields = ["open", "close"]
                for field in required_fields:
                    if field not in slot:
                        raise BusinessLogicError(f"Missing field '{field}' in slot {i} for {day}")
        else:
            # Very old legacy format with open_time/close_time
            if "open_time" not in config or "close_time" not in config:
                raise BusinessLogicError(
                    f"Legacy format for {day} requires 'open_time' and 'close_time' fields"
                )

    async def _validate_available_languages(
        self, db: AsyncSession, language_codes: List[str]
    ) -> None:
        """Validate that all provided language codes exist in the system."""
        result = await db.execute(
            select(Language.code).where(Language.code.in_(language_codes))
        )
        existing_codes = {row[0] for row in result.fetchall()}

        missing_codes = set(language_codes) - existing_codes
        if missing_codes:
            raise BusinessLogicError(
                f"Invalid language codes: {', '.join(missing_codes)}"
            )

    async def _validate_loyalty_config(self, loyalty_config: Dict[str, Any]) -> None:
        """Validate loyalty system configuration."""
        if not isinstance(loyalty_config, dict):
            raise BusinessLogicError("Loyalty configuration must be an object")

        # Validate points configuration
        if "points_per_currency_unit" in loyalty_config:
            points_per_unit = loyalty_config["points_per_currency_unit"]
            if not isinstance(points_per_unit, (int, float)) or points_per_unit < 0:
                raise BusinessLogicError(
                    "Points per currency unit must be a non-negative number"
                )

        if "currency_per_point" in loyalty_config:
            currency_per_point = loyalty_config["currency_per_point"]
            if not isinstance(currency_per_point, (int, float)) or currency_per_point < 0:
                raise BusinessLogicError(
                    "Currency per point must be a non-negative number"
                )

        if "minimum_points_to_redeem" in loyalty_config:
            min_points = loyalty_config["minimum_points_to_redeem"]
            if not isinstance(min_points, int) or min_points < 0:
                raise BusinessLogicError(
                    "Minimum points to redeem must be a non-negative integer"
                )

    async def _validate_wifi_networks(self, wifi_networks: List[Dict[str, Any]]) -> None:
        """Validate WiFi networks configuration."""
        if not isinstance(wifi_networks, list):
            raise BusinessLogicError("WiFi networks must be a list")

        for i, network in enumerate(wifi_networks):
            if not isinstance(network, dict):
                raise BusinessLogicError(f"WiFi network {i} must be an object")

            if "name" not in network:
                raise BusinessLogicError(f"WiFi network {i} must have a name")

            if not isinstance(network["name"], str) or not network["name"].strip():
                raise BusinessLogicError(f"WiFi network {i} name must be a non-empty string")

            # Validate security type if provided
            if "security_type" in network:
                valid_security_types = ["Open", "WEP", "WPA", "WPA2", "WPA3"]
                if network["security_type"] not in valid_security_types:
                    raise BusinessLogicError(
                        f"Invalid security type for network {i}: {network['security_type']}"
                    )

    async def _validate_social_media_links(self, social_media_links: List[Dict[str, Any]]) -> None:
        """Validate social media links configuration."""
        if not isinstance(social_media_links, list):
            raise BusinessLogicError("Social media links must be a list")

        for i, link in enumerate(social_media_links):
            if not isinstance(link, dict):
                raise BusinessLogicError(f"Social media link {i} must be an object")

            # Required fields
            required_fields = ["id", "platform", "url", "display_name", "icon"]
            for field in required_fields:
                if field not in link:
                    raise BusinessLogicError(f"Social media link {i} must have a {field}")

            # Validate URL format
            url = link["url"]
            if not isinstance(url, str) or not url.strip():
                raise BusinessLogicError(f"Social media link {i} URL must be a non-empty string")

            if not url.startswith(('http://', 'https://')):
                raise BusinessLogicError(f"Social media link {i} URL must start with http:// or https://")

            # Validate platform
            platform = link["platform"]
            if not isinstance(platform, str) or not platform.strip():
                raise BusinessLogicError(f"Social media link {i} platform must be a non-empty string")

            # Validate display name
            display_name = link["display_name"]
            if not isinstance(display_name, str) or not display_name.strip():
                raise BusinessLogicError(f"Social media link {i} display_name must be a non-empty string")

            # Validate icon
            icon = link["icon"]
            if not isinstance(icon, str) or not icon.strip():
                raise BusinessLogicError(f"Social media link {i} icon must be a non-empty string")

            # Validate optional fields
            if "is_active" in link and not isinstance(link["is_active"], bool):
                raise BusinessLogicError(f"Social media link {i} is_active must be a boolean")

            if "order" in link and not isinstance(link["order"], int):
                raise BusinessLogicError(f"Social media link {i} order must be an integer")
