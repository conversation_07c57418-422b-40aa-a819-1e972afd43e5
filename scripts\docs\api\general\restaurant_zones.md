# General - Restaurant Zones

**Categoria:** General
**Módulo:** Restaurant Zones
**Total de Endpoints:** 1
**Gerado em:** 27/06/2025, 20:20:27

## 📋 Endpoints

- [GET /api/modules/restaurants/settings/zones/](#get-apimodulesrestaurantssettingszones) - Get Restaurant Zones

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 🔗 Endpoints Detalhados

### GET /api/modules/restaurants/settings/zones/ {#get-apimodulesrestaurantssettingszones}

**Resumo:** Get Restaurant Zones
**Descrição:** Get all available zones for restaurant including zones from tables and custom zones.

**🔐 Autenticação:** Requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/settings/zones/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
