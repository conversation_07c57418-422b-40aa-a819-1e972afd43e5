import uuid
import logging
from typing import Annotated, List, TYPE_CHECKING

from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2<PERSON><PERSON><PERSON><PERSON>earer
from jose import jwt, JWTError
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.db_dependencies import get_db
from app.modules.core.auth.schemas.token import TokenPayload
from app.modules.core.auth.services.auth_service import auth_service
from app.modules.core.roles.models.roles import SystemRole
from app.modules.core.auth.security import (
    ALGORITHM,
    SECRET_KEY,
    oauth2_scheme,
)

if TYPE_CHECKING:
    from app.modules.core.users.models.user import User

logger = logging.getLogger(__name__)

# Standard exceptions for authentication and authorization
credentials_exception = HTTPException(
    status_code=status.HTTP_401_UNAUTHORIZED,
    detail="Could not validate credentials",
    headers={"WWW-Authenticate": "Bearer"},
)

permission_denied_exception = HTTPException(
    status_code=status.HTTP_403_FORBIDDEN,
    detail="Not enough permissions",
)


async def get_current_user(
    token: Annotated[str, Depends(oauth2_scheme)],
    db: Annotated[AsyncSession, Depends(get_db)],
) -> "User":
    """
    FastAPI dependency to get the current authenticated user from the JWT token.

    Args:
        token: The JWT token from the Authorization header.
        db: The async database session.

    Returns:
        The User object if the token is valid and the user exists.

    Raises:
        HTTPException: If the token is invalid or the user doesn't exist.
    """
    try:
        # Decode the JWT token
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        token_data = TokenPayload(**payload)

        # Check if token has the required fields
        if token_data.sub is None:
            logger.warning("get_current_user: Token missing 'sub' field.")
            raise credentials_exception

        # Check if token is of the correct type
        if token_data.type != "access":
            logger.warning(
                f"get_current_user: Invalid token type: {token_data.type}. Expected 'access'."
            )
            raise credentials_exception

        # Get the user from the database
        user_id = uuid.UUID(token_data.sub)
        user = await auth_service.get_user_by_id(db, user_id=user_id)

        if user is None:
            logger.warning(f"get_current_user: User with ID {user_id} not found in database.")
            raise credentials_exception

        return user

    except JWTError as e:
        logger.error(f"get_current_user: JWT error: {e}")
        raise credentials_exception


async def get_current_active_user(current_user: Annotated["User", Depends(get_current_user)]) -> "User":
    """
    FastAPI dependency to get the current authenticated user and check if they are active.

    Args:
        current_user: The User object from the get_current_user dependency.

    Returns:
        The User object if the user is active.

    Raises:
        HTTPException: If the user is inactive.
    """
    if not current_user.is_active:
        logger.warning(f"get_current_active_user: User {current_user.id} is inactive.")
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user
