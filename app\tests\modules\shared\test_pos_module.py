"""
Comprehensive tests for POS (Point of Sale) module.

Tests all POS functionality including cash registers, transactions,
and proper authorization.
"""

import httpx
from typing import Dict


class TestPOSCashRegisters:
    """Test POS cash register functionality."""

    async def test_list_cash_registers_tenant_owner(
        self, async_client: httpx.AsyncClient, tenant_headers_tenant_owner: Dict[str, str]
    ):
        """Test listing cash registers as tenant owner."""
        response = await async_client.get(
            "/api/modules/pos/cash-registers", headers=tenant_headers_tenant_owner
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    async def test_list_cash_registers_customer_forbidden(
        self, async_client: httpx.AsyncClient, tenant_headers_customer: Dict[str, str]
    ):
        """Test customer cannot list cash registers."""
        response = await async_client.get(
            "/api/modules/pos/cash-registers", headers=tenant_headers_customer
        )
        assert response.status_code == 403

    async def test_list_cash_registers_missing_tenant_header(
        self, async_client: httpx.AsyncClient, auth_headers_tenant_owner: Dict[str, str]
    ):
        """Test cash registers access without X-Tenant-ID header."""
        response = await async_client.get(
            "/api/modules/pos/cash-registers", headers=auth_headers_tenant_owner
        )
        assert response.status_code == 400

    async def test_create_cash_register_tenant_owner(
        self, async_client: httpx.AsyncClient, tenant_headers_tenant_owner: Dict[str, str]
    ):
        """Test creating cash register as tenant owner."""
        cash_register_data = {
            "name": "Test Register",
            "location": "Test Location",
            "is_active": True,
        }
        response = await async_client.post(
            "/api/modules/pos/cash-registers",
            headers=tenant_headers_tenant_owner,
            json=cash_register_data,
        )
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "Test Register"
        assert data["location"] == "Test Location"
        assert data["is_active"] is True

    async def test_create_cash_register_customer_forbidden(
        self, async_client: httpx.AsyncClient, tenant_headers_customer: Dict[str, str]
    ):
        """Test customer cannot create cash register."""
        cash_register_data = {
            "name": "Test Register",
            "location": "Test Location",
            "is_active": True,
        }
        response = await async_client.post(
            "/api/modules/pos/cash-registers",
            headers=tenant_headers_customer,
            json=cash_register_data,
        )
        assert response.status_code == 403


class TestPOSTransactions:
    """Test POS transaction functionality."""

    async def test_list_transactions_tenant_owner(
        self, async_client: httpx.AsyncClient, tenant_headers_tenant_owner: Dict[str, str]
    ):
        """Test listing transactions as tenant owner."""
        response = await async_client.get(
            "/api/modules/pos/transactions", headers=tenant_headers_tenant_owner
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    async def test_list_transactions_customer_forbidden(
        self, async_client: httpx.AsyncClient, tenant_headers_customer: Dict[str, str]
    ):
        """Test customer cannot list transactions."""
        response = await async_client.get(
            "/api/modules/pos/transactions", headers=tenant_headers_customer
        )
        assert response.status_code == 403

    async def test_create_transaction_requires_cash_register(
        self, async_client: httpx.AsyncClient, tenant_headers_tenant_owner: Dict[str, str]
    ):
        """Test creating transaction requires valid cash register."""
        # First create a cash register
        cash_register_data = {
            "name": "Transaction Test Register",
            "location": "Test Location",
            "is_active": True,
        }
        register_response = await async_client.post(
            "/api/modules/pos/cash-registers",
            headers=tenant_headers_tenant_owner,
            json=cash_register_data,
        )
        assert register_response.status_code == 201
        register_data = register_response.json()
        cash_register_id = register_data["id"]

        # Now create a transaction
        transaction_data = {
            "total_amount": 25.50,
            "payment_method": "cash",
            "items": [{"name": "Test Item", "quantity": 2, "unit_price": 12.75}],
        }
        response = await async_client.post(
            f"/api/modules/pos/cash-registers/{cash_register_id}/transactions",
            headers=tenant_headers_tenant_owner,
            json=transaction_data,
        )
        assert response.status_code == 201
        data = response.json()
        assert data["total_amount"] == 25.50
        assert data["payment_method"] == "cash"

    async def test_create_transaction_customer_forbidden(
        self, async_client: httpx.AsyncClient, tenant_headers_customer: Dict[str, str]
    ):
        """Test customer cannot create transactions."""
        transaction_data = {
            "total_amount": 25.50,
            "payment_method": "cash",
            "items": [{"name": "Test Item", "quantity": 2, "unit_price": 12.75}],
        }
        # Use a dummy cash register ID
        response = await async_client.post(
            "/api/modules/pos/cash-registers/dummy-id/transactions",
            headers=tenant_headers_customer,
            json=transaction_data,
        )
        assert response.status_code == 403


class TestPOSValidation:
    """Test POS data validation."""

    async def test_create_cash_register_invalid_data(
        self, async_client: httpx.AsyncClient, tenant_headers_tenant_owner: Dict[str, str]
    ):
        """Test creating cash register with invalid data."""
        invalid_data = {
            "name": "",  # Empty name should be invalid
            "location": "Test Location",
        }
        response = await async_client.post(
            "/api/modules/pos/cash-registers",
            headers=tenant_headers_tenant_owner,
            json=invalid_data,
        )
        assert response.status_code == 422

    async def test_create_transaction_invalid_amount(
        self, async_client: httpx.AsyncClient, tenant_headers_tenant_owner: Dict[str, str]
    ):
        """Test creating transaction with invalid amount."""
        # First create a cash register
        cash_register_data = {
            "name": "Validation Test Register",
            "location": "Test Location",
            "is_active": True,
        }
        register_response = await async_client.post(
            "/api/modules/pos/cash-registers",
            headers=tenant_headers_tenant_owner,
            json=cash_register_data,
        )
        assert register_response.status_code == 201
        register_data = register_response.json()
        cash_register_id = register_data["id"]

        # Try to create transaction with negative amount
        invalid_transaction = {
            "total_amount": -10.00,  # Negative amount should be invalid
            "payment_method": "cash",
            "items": [],
        }
        response = await async_client.post(
            f"/api/modules/pos/cash-registers/{cash_register_id}/transactions",
            headers=tenant_headers_tenant_owner,
            json=invalid_transaction,
        )
        assert response.status_code == 422

    async def test_create_transaction_missing_items(
        self, async_client: httpx.AsyncClient, tenant_headers_tenant_owner: Dict[str, str]
    ):
        """Test creating transaction without items."""
        # First create a cash register
        cash_register_data = {
            "name": "Items Test Register",
            "location": "Test Location",
            "is_active": True,
        }
        register_response = await async_client.post(
            "/api/modules/pos/cash-registers",
            headers=tenant_headers_tenant_owner,
            json=cash_register_data,
        )
        assert register_response.status_code == 201
        register_data = register_response.json()
        cash_register_id = register_data["id"]

        # Try to create transaction without items
        transaction_without_items = {
            "total_amount": 10.00,
            "payment_method": "cash",
            # Missing items field
        }
        response = await async_client.post(
            f"/api/modules/pos/cash-registers/{cash_register_id}/transactions",
            headers=tenant_headers_tenant_owner,
            json=transaction_without_items,
        )
        # This might be 422 (validation error) or 201 (if items are optional)
        # Check the actual API implementation
        assert response.status_code in [201, 422]


class TestPOSFiltering:
    """Test POS filtering and querying."""

    async def test_filter_transactions_by_date(
        self, async_client: httpx.AsyncClient, tenant_headers_tenant_owner: Dict[str, str]
    ):
        """Test filtering transactions by date."""
        response = await async_client.get(
            "/api/modules/pos/transactions?start_date=2024-01-01&end_date=2024-12-31",
            headers=tenant_headers_tenant_owner,
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    async def test_filter_transactions_by_payment_method(
        self, async_client: httpx.AsyncClient, tenant_headers_tenant_owner: Dict[str, str]
    ):
        """Test filtering transactions by payment method."""
        response = await async_client.get(
            "/api/modules/pos/transactions?payment_method=cash",
            headers=tenant_headers_tenant_owner,
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    async def test_filter_cash_registers_by_active_status(
        self, async_client: httpx.AsyncClient, tenant_headers_tenant_owner: Dict[str, str]
    ):
        """Test filtering cash registers by active status."""
        response = await async_client.get(
            "/api/modules/pos/cash-registers?is_active=true",
            headers=tenant_headers_tenant_owner,
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
