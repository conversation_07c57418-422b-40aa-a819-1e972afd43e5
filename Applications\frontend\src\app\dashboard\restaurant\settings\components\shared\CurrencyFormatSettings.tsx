'use client';

import { useState, useEffect } from 'react';
import { CurrencyDollarIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import CurrencySelector from './CurrencySelector';

interface CurrencyFormat {
  decimal_separator: '.' | ',';
  thousands_separator: '.' | ',' | ' ' | '';
  symbol_position: 'left' | 'right';
  symbol_spacing: boolean;
}

interface CurrencyFormatSettingsProps {
  currency: string;
  format?: CurrencyFormat;
  onCurrencyChange: (currency: string) => void;
  onFormatChange: (format: CurrencyFormat) => void;
}

const DEFAULT_FORMATS: Record<string, CurrencyFormat> = {
  'USD': { decimal_separator: '.', thousands_separator: ',', symbol_position: 'left', symbol_spacing: false },
  'BRL': { decimal_separator: ',', thousands_separator: '.', symbol_position: 'left', symbol_spacing: true },
  'EUR': { decimal_separator: ',', thousands_separator: '.', symbol_position: 'right', symbol_spacing: true },
  'GBP': { decimal_separator: '.', thousands_separator: ',', symbol_position: 'left', symbol_spacing: false },
  'JPY': { decimal_separator: '.', thousands_separator: ',', symbol_position: 'left', symbol_spacing: false },
};

const CURRENCY_SYMBOLS: Record<string, string> = {
  'USD': '$', 'BRL': 'R$', 'EUR': '€', 'GBP': '£', 'CAD': 'C$', 
  'AUD': 'A$', 'JPY': '¥', 'CHF': 'CHF', 'CNY': '¥', 'INR': '₹'
};

export default function CurrencyFormatSettings({ 
  currency, 
  format, 
  onCurrencyChange, 
  onFormatChange 
}: CurrencyFormatSettingsProps) {
  const currentFormat = format || DEFAULT_FORMATS[currency] || DEFAULT_FORMATS['USD'];
  const symbol = CURRENCY_SYMBOLS[currency] || currency;

  // Debug logs for props changes (only when currency or format actually change)
  useEffect(() => {
    console.log(`[CurrencyFormatSettings] Props changed - currency: ${currency}, format:`, format);
    console.log(`[CurrencyFormatSettings] Current format being used:`, currentFormat);
  }, [currency, format, currentFormat]);

  // Monitor currentFormat changes
  useEffect(() => {
    console.log(`[CurrencyFormatSettings] currentFormat recalculated:`, currentFormat);
    console.log(`[CurrencyFormatSettings] - format prop:`, format);
    console.log(`[CurrencyFormatSettings] - DEFAULT_FORMATS[${currency}]:`, DEFAULT_FORMATS[currency]);
    console.log(`[CurrencyFormatSettings] - fallback DEFAULT_FORMATS['USD']:`, DEFAULT_FORMATS['USD']);
  }, [currentFormat, currency, format]);

  const handleCurrencyChange = (newCurrency: string) => {
    console.log(`[CurrencyFormatSettings] Currency changing from ${currency} to ${newCurrency}`);
    onCurrencyChange(newCurrency);
    // Auto-apply default format for the new currency
    const defaultFormat = DEFAULT_FORMATS[newCurrency] || DEFAULT_FORMATS['USD'];
    console.log(`[CurrencyFormatSettings] Applying default format for ${newCurrency}:`, defaultFormat);
    onFormatChange(defaultFormat);
  };

  const handleFormatChange = (field: keyof CurrencyFormat, value: any) => {
    const newFormat = { ...currentFormat, [field]: value };
    console.log(`[CurrencyFormatSettings] Format changing - ${field}:`, value, 'New format:', newFormat);
    onFormatChange(newFormat);
  };

  const formatExample = (amount: number) => {
    const amountStr = amount.toFixed(2);
    const [integer, decimal] = amountStr.split('.');
    
    // Apply thousands separator
    let formattedInteger = integer;
    if (currentFormat.thousands_separator && integer.length > 3) {
      formattedInteger = integer.replace(/\B(?=(\d{3})+(?!\d))/g, currentFormat.thousands_separator);
    }
    
    // Combine with decimal separator
    const formattedAmount = formattedInteger + currentFormat.decimal_separator + decimal;
    
    // Apply symbol position and spacing
    const spacing = currentFormat.symbol_spacing ? ' ' : '';
    if (currentFormat.symbol_position === 'left') {
      return symbol + spacing + formattedAmount;
    } else {
      return formattedAmount + spacing + symbol;
    }
  };

  return (
    <div className="space-y-6">
      {/* Currency Selection */}
      <CurrencySelector
        value={currency}
        onChange={handleCurrencyChange}
        label="Default Currency"
      />

      {/* Format Configuration */}
      <div className="border-t border-gray-200 pt-6">
        <div className="flex items-center space-x-2 mb-4">
          <CurrencyDollarIcon className="h-5 w-5 text-gray-400" />
          <h4 className="text-sm font-medium text-gray-900">Currency Formatting</h4>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Decimal Separator */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Decimal Separator
            </label>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="decimal_separator"
                  value="."
                  checked={currentFormat.decimal_separator === '.'}
                  onChange={(e) => handleFormatChange('decimal_separator', e.target.value as '.' | ',')}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700">Period (.)</span>
                <span className="ml-2 text-xs text-gray-500">1234.56</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="decimal_separator"
                  value=","
                  checked={currentFormat.decimal_separator === ','}
                  onChange={(e) => handleFormatChange('decimal_separator', e.target.value as '.' | ',')}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700">Comma (,)</span>
                <span className="ml-2 text-xs text-gray-500">1234,56</span>
              </label>
            </div>
          </div>

          {/* Thousands Separator */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Thousands Separator
            </label>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="thousands_separator"
                  value=","
                  checked={currentFormat.thousands_separator === ','}
                  onChange={(e) => handleFormatChange('thousands_separator', e.target.value)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700">Comma (,)</span>
                <span className="ml-2 text-xs text-gray-500">1,234</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="thousands_separator"
                  value="."
                  checked={currentFormat.thousands_separator === '.'}
                  onChange={(e) => handleFormatChange('thousands_separator', e.target.value)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700">Period (.)</span>
                <span className="ml-2 text-xs text-gray-500">1.234</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="thousands_separator"
                  value=" "
                  checked={currentFormat.thousands_separator === ' '}
                  onChange={(e) => handleFormatChange('thousands_separator', e.target.value)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700">Space ( )</span>
                <span className="ml-2 text-xs text-gray-500">1 234</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="thousands_separator"
                  value=""
                  checked={currentFormat.thousands_separator === ''}
                  onChange={(e) => handleFormatChange('thousands_separator', e.target.value)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700">None</span>
                <span className="ml-2 text-xs text-gray-500">1234</span>
              </label>
            </div>
          </div>
        </div>

        {/* Symbol Position and Spacing */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          {/* Symbol Position */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Symbol Position
            </label>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="symbol_position"
                  value="left"
                  checked={currentFormat.symbol_position === 'left'}
                  onChange={(e) => handleFormatChange('symbol_position', e.target.value as 'left' | 'right')}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700">Before amount</span>
                <span className="ml-2 text-xs text-gray-500">{symbol}123.45</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="symbol_position"
                  value="right"
                  checked={currentFormat.symbol_position === 'right'}
                  onChange={(e) => handleFormatChange('symbol_position', e.target.value as 'left' | 'right')}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700">After amount</span>
                <span className="ml-2 text-xs text-gray-500">123.45{symbol}</span>
              </label>
            </div>
          </div>

          {/* Symbol Spacing */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Symbol Spacing
            </label>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={currentFormat.symbol_spacing}
                  onChange={(e) => handleFormatChange('symbol_spacing', e.target.checked)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">Add space between symbol and amount</span>
              </label>
              <div className="ml-6 text-xs text-gray-500">
                {currentFormat.symbol_spacing ? (
                  currentFormat.symbol_position === 'left' ? `${symbol} 123.45` : `123.45 ${symbol}`
                ) : (
                  currentFormat.symbol_position === 'left' ? `${symbol}123.45` : `123.45${symbol}`
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Preview */}
      <div className="border-t border-gray-200 pt-6">
        <h4 className="text-sm font-medium text-gray-900 mb-3">Format Preview</h4>
        <div className="p-4 bg-gray-50 border border-gray-200 rounded-md">
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Small amount:</span>
              <span className="font-mono font-medium">{formatExample(12.50)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Medium amount:</span>
              <span className="font-mono font-medium">{formatExample(1234.56)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Large amount:</span>
              <span className="font-mono font-medium">{formatExample(123456.78)}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
