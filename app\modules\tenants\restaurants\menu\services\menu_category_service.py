import logging
from typing import Optional, Sequence
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from sqlalchemy.exc import IntegrityError
from fastapi import HTTPException, status
import uuid

# Import models
from app.modules.tenants.restaurants.menu.models.menu_category import (
    MenuCategory,
)
from app.modules.tenants.restaurants.menu.models.menu_item import MenuItem
from app.modules.core.functions.customizations.models.variant_group import VariantGroup
from app.modules.core.functions.customizations.models.modifier_group import ModifierGroup
from app.modules.core.functions.customizations.models.optional_group import OptionalGroup

# Import schemas
from app.modules.tenants.restaurants.menu.schemas.menu_category import (  # noqa: E402
    MenuCategoryCreate,
    MenuCategoryUpdate,
)

logger = logging.getLogger(__name__)


class MenuCategoryService:
    def __init__(self, db_session: AsyncSession):
        self.db = db_session

    async def create_category(
        self, category_in: MenuCategoryCreate, tenant_id: uuid.UUID
    ) -> MenuCategory:
        """
        Creates a new menu category for the given tenant.
        Validates parent_id if provided.
        """
        # Validate parent_id if provided
        if hasattr(category_in, 'parent_id') and category_in.parent_id:
            parent_category = await self.get_category(
                category_id=category_in.parent_id, tenant_id=tenant_id
            )
            if not parent_category:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=(
                        f"Parent category with id {category_in.parent_id} "
                        f"not found or not active for this tenant."
                    ),
                )

        try:
            db_category = MenuCategory(**category_in.model_dump(), tenant_id=tenant_id)
            self.db.add(db_category)
            await self.db.commit()
            await self.db.refresh(db_category)
            logger.info(f"Menu category created: {db_category.id} for tenant {tenant_id}")
            return db_category
        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"Error creating category for tenant {tenant_id}: {e}")
            # Check for specific constraint violations
            if "duplicate key" in str(e) and "ix_menu_categories_tenant_id_name_parent" in str(e):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=(
                        "A category with this name already exists at this level for this tenant."
                    ),
                )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Error creating menu category.",
            )
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Unexpected error creating category for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def get_category(
        self,
        category_id: uuid.UUID,
        tenant_id: uuid.UUID,
        include_children: bool = True,
    ) -> Optional[MenuCategory]:
        """
        Gets a specific active menu category by ID for the given tenant.
        Optionally includes its direct children.
        """
        stmt = select(MenuCategory).where(
            MenuCategory.id == category_id,
            MenuCategory.tenant_id == tenant_id,
            MenuCategory.is_active,
        )
        options = [selectinload(MenuCategory.parent)]  # Always load parent
        if include_children:
            # Load children, and for each child, load its own children (1 level deep for children)
            # For deeper nesting, a more complex recursive loading strategy or
            # multiple queries might be needed.
            options.append(selectinload(MenuCategory.children).selectinload(MenuCategory.children))

        stmt = stmt.options(*options)
        result = await self.db.execute(stmt)
        category = result.scalars().first()

        if category and include_children:
            # Force access to children to ensure they're loaded in the async context
            _ = category.children

        return category

    async def get_categories(
        self,
        tenant_id: uuid.UUID,
        parent_id: Optional[uuid.UUID] = None,
        digital_menu_id: Optional[uuid.UUID] = None,
        include_children: bool = True,
        include_items: bool = False,
        only_top_level: bool = False,
        skip: int = 0,
        limit: int = 100,
    ) -> Sequence[MenuCategory]:
        """
        Gets a list of active menu categories for the given tenant.
        Can filter by parent_id, digital_menu_id, or fetch only top-level categories.

        Parameters:
            tenant_id: The tenant ID to filter by
            parent_id: If provided, filters categories by this parent ID
            digital_menu_id: If provided, filters categories by this digital menu ID
            include_children: Whether to load children relationships
            include_items: Whether to load menu items with full details (variant_groups, modifier_groups, optional_groups)
            only_top_level: If True, only returns categories with no parent
            skip: Number of records to skip (pagination)
            limit: Max number of records to return (pagination)

        Returns:
            A sequence of MenuCategory objects
        """
        # Log the filter parameters for debugging
        logger.info(
            f"get_categories called with tenant_id={tenant_id}, parent_id={parent_id}, "
            f"digital_menu_id={digital_menu_id}, only_top_level={only_top_level}, "
            f"include_children={include_children}, include_items={include_items}"
        )

        # Start with base query for active categories for this tenant
        stmt = select(MenuCategory).where(
            MenuCategory.tenant_id == tenant_id,
            MenuCategory.is_active.is_(True),  # SQLAlchemy style comparison
        )

        # Apply digital menu filter if provided
        if digital_menu_id is not None:
            logger.info(f"Applying digital_menu_id filter: {digital_menu_id}")
            stmt = stmt.where(MenuCategory.digital_menu_id == digital_menu_id)

        # Apply filters for parent_id or only_top_level
        # These are mutually exclusive - only one should be applied
        if only_top_level:  # Fixed comparison style
            logger.info("Applying only_top_level filter")
            stmt = stmt.where(MenuCategory.parent_id.is_(None))
        elif parent_id is not None:
            logger.info(f"Applying parent_id filter: {parent_id}")
            stmt = stmt.where(MenuCategory.parent_id == parent_id)
        else:
            logger.info("No parent filters applied, returning all categories")

        # Apply ordering and pagination
        stmt = (
            stmt.order_by(MenuCategory.display_order, MenuCategory.name).offset(skip).limit(limit)
        )

        # Set up eager loading options
        options = []

        # Always load parent for context
        options.append(selectinload(MenuCategory.parent))

        if include_children:
            # Load children, and for each child, load its own children (1 level deep for children)
            options.append(
                selectinload(MenuCategory.children).selectinload(MenuCategory.children)
            )

        if include_items:
            # Load menu items with full details including variant_groups, modifier_groups, and optional_groups
            options.append(
                selectinload(MenuCategory.menu_items)
                .selectinload(MenuItem.variant_groups)
                .selectinload(VariantGroup.options)
            )
            options.append(
                selectinload(MenuCategory.menu_items)
                .selectinload(MenuItem.modifier_groups)
                .selectinload(ModifierGroup.options)
            )
            options.append(
                selectinload(MenuCategory.menu_items)
                .selectinload(MenuItem.optional_groups)
                .selectinload(OptionalGroup.options)
            )

        if options:  # Only add options if there are any
            stmt = stmt.options(*options)

        # Execute query
        try:
            result = await self.db.execute(stmt)
            categories = result.scalars().all()
            logger.info(f"Retrieved {len(categories)} categories")

            # Relationships are already loaded via selectinload, no need to force access
            # The selectinload options ensure all relationships are loaded in the async context

            return categories
        except Exception as e:
            logger.error(f"Error retrieving categories for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving menu categories",
            )

    async def _is_descendant(
        self,
        potential_descendant_id: uuid.UUID,
        ancestor_id: uuid.UUID,
        tenant_id: uuid.UUID,
    ) -> bool:
        """
        Check if potential_descendant_id is a descendant of ancestor_id in the category hierarchy.
        This prevents cycles when setting a category's parent.

        Args:
            potential_descendant_id: The ID of the category that might be a descendant
            ancestor_id: The ID of the potential ancestor category
            tenant_id: The tenant ID to restrict the search to

        Returns:
            True if potential_descendant_id is a descendant of ancestor_id, False otherwise
        """
        logger.info(
            f"Checking if category {potential_descendant_id} is a descendant of {ancestor_id} "
            f"for tenant {tenant_id}"
        )

        # Base case: if they're the same, it would create a cycle
        if potential_descendant_id == ancestor_id:
            logger.info(
                f"Category {potential_descendant_id} is the same as {ancestor_id} - cycle detected"
            )
            return True

        try:
            # Get all direct children of the ancestor
            children_stmt = select(MenuCategory.id).where(
                MenuCategory.parent_id == ancestor_id,
                MenuCategory.tenant_id == tenant_id,
                MenuCategory.is_active.is_(True),  # SQLAlchemy style comparison
            )
            children_result = await self.db.execute(children_stmt)
            children_ids = children_result.scalars().all()

            logger.info(f"Found {len(children_ids)} direct children of category {ancestor_id}")

            # Check if the potential descendant is a direct child
            if potential_descendant_id in children_ids:
                logger.info(
                    f"Category {potential_descendant_id} is a direct child of {ancestor_id} - cycle detected"
                )
                return True

            # Recursively check each child
            for child_id in children_ids:
                if await self._is_descendant(potential_descendant_id, child_id, tenant_id):
                    logger.info(
                        f"Category {potential_descendant_id} is a descendant of child {child_id} - cycle detected"
                    )
                    return True

            # If we get here, no descendant relationship was found
            logger.info(
                f"No descendant relationship found between {potential_descendant_id} and {ancestor_id}"
            )
            return False

        except Exception as e:
            logger.error(f"Error checking descendant relationship: {e}")
            # In case of error, assume there might be a cycle to be safe
            return True

    async def update_category(
        self,
        category_id: uuid.UUID,
        category_in: MenuCategoryUpdate,
        tenant_id: uuid.UUID,
    ) -> Optional[MenuCategory]:
        """
        Updates an existing menu category, including parent_id.

        Args:
            category_id: The ID of the category to update
            category_in: The update data
            tenant_id: The tenant ID to restrict the operation to

        Returns:
            The updated category if successful, None if the category doesn't exist

        Raises:
            HTTPException: For various error conditions with appropriate status codes
        """
        logger.info(f"Updating category {category_id} for tenant {tenant_id}")

        # Verify the category exists and belongs to this tenant
        db_category = await self.get_category(category_id, tenant_id)
        if not db_category:
            logger.warning(
                f"Attempted to update non-existent category {category_id} for tenant {tenant_id}"
            )
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Category with ID {category_id} not found or not active for this tenant",
            )

        # Validate update data
        update_data = category_in.model_dump(exclude_unset=True)
        if not update_data:
            logger.warning(f"Empty update data for category {category_id}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No update data provided",
            )

        # Check for self-reference if parent_id is being set
        if update_data.get("parent_id") == category_id:
            logger.warning(f"Attempted to set category {category_id} as its own parent")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="A category cannot be its own parent",
            )

        # Check for cycles if parent_id is being updated
        if (
            "parent_id" in update_data
            and update_data["parent_id"] is not None
            and update_data["parent_id"] != db_category.parent_id
        ):
            # Verify the new parent exists and belongs to this tenant
            new_parent = await self.get_category(update_data["parent_id"], tenant_id)
            if not new_parent:
                logger.warning(
                    f"Parent category {update_data['parent_id']} not found for tenant {tenant_id}"
                )
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=(
                        f"Parent category with id {update_data['parent_id']} "
                        f"not found or not active for this tenant."
                    ),
                )

            # Check if the new parent is a descendant of this category (would create a cycle)
            if await self._is_descendant(update_data["parent_id"], category_id, tenant_id):
                logger.warning(
                    f"Cycle detected: attempted to set category {category_id}'s parent to "
                    f"its descendant {update_data['parent_id']}"
                )
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=(
                        "Cannot set a category's parent to one of its descendants "
                        "(would create a cycle)."
                    ),
                )

        try:
            # Update fields from the input model
            for key, value in update_data.items():
                setattr(db_category, key, value)

            self.db.add(db_category)
            await self.db.commit()
            await self.db.refresh(db_category)
            logger.info(f"Menu category updated: {category_id} for tenant {tenant_id}")

            # Ensure relationships are loaded
            await self.db.refresh(db_category, attribute_names=["parent", "children"])

            return db_category

        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"Error updating category {category_id} for tenant {tenant_id}: {e}")
            # Check for specific constraint violations
            if "duplicate key" in str(e) and "ix_menu_categories_tenant_id_name_parent" in str(e):
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,  # More appropriate status for conflicts
                    detail=(
                        "A category with this name already exists at this level for this tenant."
                    ),
                )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Error updating menu category.",
            )
        except Exception as e:
            await self.db.rollback()
            logger.exception(
                f"Unexpected error updating category {category_id} for tenant {tenant_id}: {e}"
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def _deactivate_category_and_children_recursive(
        self, category_id: uuid.UUID, tenant_id: uuid.UUID
    ):
        """
        Helper to recursively deactivate a category and all its children.
        """
        # Deactivate the current category
        category_to_deactivate = await self.db.get(MenuCategory, category_id)
        if (
            category_to_deactivate
            and category_to_deactivate.tenant_id == tenant_id
            and category_to_deactivate.is_active
        ):
            category_to_deactivate.is_active = False
            self.db.add(category_to_deactivate)
            logger.info(f"Deactivating category {category_id} for tenant {tenant_id}")

            # Find and deactivate children
            children_stmt = select(MenuCategory.id).where(
                MenuCategory.parent_id == category_id,
                MenuCategory.tenant_id == tenant_id,
                MenuCategory.is_active,  # Only act on active children
            )
            children_result = await self.db.execute(children_stmt)
            children_ids = children_result.scalars().all()

            for child_id in children_ids:
                await self._deactivate_category_and_children_recursive(child_id, tenant_id)
        # No commit here, commit will be done by the calling public method

    async def delete_category(self, category_id: uuid.UUID, tenant_id: uuid.UUID) -> MenuCategory:
        """
        Soft deletes a menu category and all its active children recursively for the given tenant.

        Args:
            category_id: The ID of the category to delete
            tenant_id: The tenant ID to restrict the operation to

        Returns:
            The deactivated category with updated is_active=False

        Raises:
            HTTPException: For various error conditions with appropriate status codes
        """
        logger.info(f"Soft deleting category {category_id} for tenant {tenant_id}")

        # Use a direct fetch to see if it exists, regardless of active status
        root_category_for_delete = await self.db.get(MenuCategory, category_id)

        # Verify the category exists and belongs to this tenant
        if not root_category_for_delete:
            logger.warning(f"Category {category_id} not found")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Category with ID {category_id} not found",
            )

        if root_category_for_delete.tenant_id != tenant_id:
            logger.warning(
                f"Unauthorized attempt to delete category {category_id} "
                f"for tenant {tenant_id}. Category belongs to tenant {root_category_for_delete.tenant_id}"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You do not have permission to delete this category",
            )

        # If already inactive, return it but don't modify anything
        if not root_category_for_delete.is_active:
            logger.info(f"Category {category_id} for tenant {tenant_id} is already inactive.")
            # Load related data for the response
            await self.db.refresh(root_category_for_delete, attribute_names=["parent", "children"])
            return root_category_for_delete

        try:
            # Perform the recursive deactivation
            await self._deactivate_category_and_children_recursive(category_id, tenant_id)
            await self.db.commit()
            logger.info(
                f"Menu category {category_id} and its children (if any) "
                f"successfully deactivated for tenant {tenant_id}"
            )

            # Refresh the category to get its updated state with is_active=False
            await self.db.refresh(root_category_for_delete)

            # Load related data for the response
            await self.db.refresh(root_category_for_delete, attribute_names=["parent", "children"])

            # Force access to children to load them in the async context
            _ = root_category_for_delete.children

            return root_category_for_delete

        except Exception as e:
            await self.db.rollback()
            logger.exception(
                f"Error soft deleting category {category_id} and its children "
                f"for tenant {tenant_id}: {e}"
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error during category deactivation",
            )
