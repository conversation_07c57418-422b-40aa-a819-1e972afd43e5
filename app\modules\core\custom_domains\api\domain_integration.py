"""API endpoints for domain integration."""

import logging  # noqa: E402
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status  # noqa: E402
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.dependencies import get_db, get_current_active_user, get_current_tenant  # noqa: E402
from app.models.user import User
from app.models.tenant import Tenant
from app.modules.core.custom_domains import schemas
from app.modules.core.custom_domains.services.domain_integration_service import (
    DomainIntegrationService,
)
from app.modules.core.custom_domains.schemas.domain_integration import (  # noqa: E402
    DomainRegistrationWithTenantCreate,
)


logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/available-domains", response_model=list[schemas.DomainRegistrationRead])
async def list_available_domains(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    current_tenant: Tenant = Depends(get_current_tenant),
):
    """
    List domains registered by the current user that are available for association with the current tenant.  # noqa: E501
    """
    integration_service = DomainIntegrationService(db)
    domains = await integration_service.get_available_domains_for_tenant(
        user_id=current_user.id,
        tenant_id=current_tenant.id,
    )
    return domains


@router.post(
    "/register-and-associate",
    response_model=schemas.CustomDomain,
    status_code=status.HTTP_201_CREATED,
)
async def register_domain_and_associate(
    request: DomainRegistrationWithTenantCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    current_tenant: Tenant = Depends(get_current_tenant),
):
    """
    Register a new domain and associate it with the current tenant.
    """
    # Ensure the tenant ID in the request matches the current tenant
    if request.tenant_id != current_tenant.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Tenant ID in request does not match current tenant",
        )

    integration_service = DomainIntegrationService(db)

    try:
        result = await integration_service.register_domain_and_associate_with_tenant(
            user_id=current_user.id,
            domain_name=request.domain_name,
            tld=request.tld,
            tenant_id=current_tenant.id,
            frontend_type=request.frontend_type,
            registrar=request.registrar,
            period_years=request.period_years,
            auto_renew=request.auto_renew,
            whois_privacy=request.whois_privacy,
            nameservers=request.nameservers,
            contacts=request.contacts,
        )
        return result["custom_domain"]
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"Error registering and associating domain: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while registering and associating the domain",
        )


@router.post(
    "/associate-registered-domain",
    response_model=schemas.CustomDomain,
    status_code=status.HTTP_201_CREATED,
)
async def associate_registered_domain(
    domain_registration_id: UUID,
    frontend_type: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    current_tenant: Tenant = Depends(get_current_tenant),
):
    """
    Associate an existing registered domain with the current tenant.
    """
    integration_service = DomainIntegrationService(db)

    try:
        custom_domain = await integration_service.associate_registered_domain_with_tenant(
            domain_registration_id=domain_registration_id,
            tenant_id=current_tenant.id,
            frontend_type=frontend_type,
        )
        return custom_domain
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"Error associating registered domain: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while associating the domain",
        )
