"""Financial Control Service."""

from typing import List, Optional, Dict, Any
from uuid import UUID
from datetime import date, datetime
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc, asc
from sqlalchemy.orm import selectinload

from app.core.exceptions import NotFoundError, ValidationError
from app.core.pagination import PaginationParams, PaginatedResponse
from ..models.control_entry import FinancialControlEntry, ControlEntryStatus, ControlEntryType
from ..models.control_category import ControlCategory
from ..models.control_document import ControlDocument
from ..schemas.control_entry_schemas import (
    ControlEntryCreate, ControlEntryUpdate, ControlEntryResponse, 
    ControlEntryFilters, ControlEntryBulkUpdate
)


class ControlService:
    """Service for financial control operations."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_entry(
        self, 
        entry_data: ControlEntryCreate, 
        tenant_id: UUID, 
        user_id: UUID
    ) -> FinancialControlEntry:
        """Create a new financial control entry."""
        
        # Validate category if provided
        if entry_data.category_id:
            category = await self.db.get(ControlCategory, entry_data.category_id)
            if not category or category.tenant_id != tenant_id:
                raise ValidationError("Invalid category")
        
        # Create entry
        entry = FinancialControlEntry(
            tenant_id=tenant_id,
            created_by=user_id,
            **entry_data.model_dump(exclude_unset=True)
        )
        
        # Auto-calculate net amount if not provided
        if not entry.net_amount:
            entry.net_amount = entry.calculated_net_amount
        
        # Auto-generate reference number if not provided
        if not entry.reference_number:
            entry.reference_number = await self._generate_reference_number(
                tenant_id, entry.entry_type
            )
        
        self.db.add(entry)
        await self.db.commit()
        await self.db.refresh(entry)
        
        return entry
    
    async def get_entry(self, entry_id: UUID, tenant_id: UUID) -> FinancialControlEntry:
        """Get a financial control entry by ID."""
        
        query = select(FinancialControlEntry).where(
            and_(
                FinancialControlEntry.id == entry_id,
                FinancialControlEntry.tenant_id == tenant_id
            )
        ).options(
            selectinload(FinancialControlEntry.category),
            selectinload(FinancialControlEntry.documents),
        )
        
        result = await self.db.execute(query)
        entry = result.scalar_one_or_none()
        
        if not entry:
            raise NotFoundError("Financial control entry not found")
        
        return entry
    
    async def update_entry(
        self, 
        entry_id: UUID, 
        entry_data: ControlEntryUpdate, 
        tenant_id: UUID, 
        user_id: UUID
    ) -> FinancialControlEntry:
        """Update a financial control entry."""
        
        entry = await self.get_entry(entry_id, tenant_id)
        
        # Validate category if being updated
        if entry_data.category_id:
            category = await self.db.get(ControlCategory, entry_data.category_id)
            if not category or category.tenant_id != tenant_id:
                raise ValidationError("Invalid category")
        
        # Update fields
        update_data = entry_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(entry, field, value)
        
        entry.updated_by = user_id
        
        # Recalculate net amount if amount changed
        if 'amount' in update_data or 'tax_amount' in update_data or 'discount_amount' in update_data:
            if not entry_data.net_amount:
                entry.net_amount = entry.calculated_net_amount
        
        await self.db.commit()
        await self.db.refresh(entry)
        
        return entry
    
    async def delete_entry(self, entry_id: UUID, tenant_id: UUID) -> bool:
        """Delete a financial control entry."""
        
        entry = await self.get_entry(entry_id, tenant_id)
        await self.db.delete(entry)
        await self.db.commit()
        
        return True
    
    async def list_entries(
        self,
        tenant_id: UUID,
        filters: Optional[ControlEntryFilters] = None,
        pagination: Optional[PaginationParams] = None
    ):
        """List financial control entries with filtering and pagination."""
        
        query = select(FinancialControlEntry).where(
            FinancialControlEntry.tenant_id == tenant_id
        ).options(
            selectinload(FinancialControlEntry.category),
            selectinload(FinancialControlEntry.documents),
        )
        
        # Apply filters
        if filters:
            query = self._apply_filters(query, filters)
        
        # Apply sorting
        if filters and filters.sort_by:
            sort_field = getattr(FinancialControlEntry, filters.sort_by, None)
            if sort_field:
                if filters.sort_order == "asc":
                    query = query.order_by(asc(sort_field))
                else:
                    query = query.order_by(desc(sort_field))
        else:
            query = query.order_by(desc(FinancialControlEntry.entry_date))
        
        # Apply pagination
        if pagination:
            offset = (pagination.page - 1) * pagination.size
            query = query.offset(offset).limit(pagination.size)
        
        result = await self.db.execute(query)
        entries = result.scalars().all()
        
        # Get total count
        count_query = select(func.count(FinancialControlEntry.id)).where(
            FinancialControlEntry.tenant_id == tenant_id
        )
        if filters:
            count_query = self._apply_filters(count_query, filters)
        
        count_result = await self.db.execute(count_query)
        total = count_result.scalar()
        
        return {
            "items": entries,
            "total": total,
            "page": pagination.page if pagination else 1,
            "size": pagination.size if pagination else len(entries),
            "pages": ((total - 1) // pagination.size + 1) if pagination else 1
        }
    
    async def bulk_update_entries(
        self, 
        bulk_data: ControlEntryBulkUpdate, 
        tenant_id: UUID, 
        user_id: UUID
    ) -> List[FinancialControlEntry]:
        """Bulk update financial control entries."""
        
        # Get entries
        query = select(FinancialControlEntry).where(
            and_(
                FinancialControlEntry.id.in_(bulk_data.entry_ids),
                FinancialControlEntry.tenant_id == tenant_id
            )
        )
        
        result = await self.db.execute(query)
        entries = result.scalars().all()
        
        if len(entries) != len(bulk_data.entry_ids):
            raise ValidationError("Some entries not found or not accessible")
        
        # Apply updates
        update_data = bulk_data.updates.model_dump(exclude_unset=True)
        for entry in entries:
            for field, value in update_data.items():
                setattr(entry, field, value)
            entry.updated_by = user_id
        
        await self.db.commit()
        
        return entries
    
    async def archive_entry(self, entry_id: UUID, tenant_id: UUID, user_id: UUID) -> FinancialControlEntry:
        """Archive a financial control entry."""
        
        entry = await self.get_entry(entry_id, tenant_id)
        entry.status = ControlEntryStatus.ARCHIVED
        entry.updated_by = user_id
        
        await self.db.commit()
        await self.db.refresh(entry)
        
        return entry
    
    async def mark_as_paid(
        self, 
        entry_id: UUID, 
        tenant_id: UUID, 
        user_id: UUID,
        payment_date: Optional[date] = None,
        payment_method: Optional[str] = None,
        payment_reference: Optional[str] = None
    ) -> FinancialControlEntry:
        """Mark an entry as paid."""
        
        entry = await self.get_entry(entry_id, tenant_id)
        
        entry.status = ControlEntryStatus.PAID
        entry.payment_date = payment_date or date.today()
        if payment_method:
            entry.payment_method = payment_method
        if payment_reference:
            entry.payment_reference = payment_reference
        entry.updated_by = user_id
        
        await self.db.commit()
        await self.db.refresh(entry)
        
        return entry
    
    def _apply_filters(self, query, filters: ControlEntryFilters):
        """Apply filters to query."""
        
        if filters.entry_type:
            query = query.where(FinancialControlEntry.entry_type == filters.entry_type)
        
        if filters.status:
            query = query.where(FinancialControlEntry.status.in_(filters.status))
        
        if filters.category_id:
            query = query.where(FinancialControlEntry.category_id == filters.category_id)
        
        if filters.supplier_id:
            query = query.where(FinancialControlEntry.supplier_id == filters.supplier_id)
        
        if filters.date_from:
            query = query.where(FinancialControlEntry.entry_date >= filters.date_from)
        
        if filters.date_to:
            query = query.where(FinancialControlEntry.entry_date <= filters.date_to)
        
        if filters.due_date_from:
            query = query.where(FinancialControlEntry.due_date >= filters.due_date_from)
        
        if filters.due_date_to:
            query = query.where(FinancialControlEntry.due_date <= filters.due_date_to)
        
        if filters.amount_min:
            query = query.where(FinancialControlEntry.amount >= filters.amount_min)
        
        if filters.amount_max:
            query = query.where(FinancialControlEntry.amount <= filters.amount_max)
        
        if filters.search:
            search_term = f"%{filters.search}%"
            query = query.where(
                or_(
                    FinancialControlEntry.title.ilike(search_term),
                    FinancialControlEntry.description.ilike(search_term),
                    FinancialControlEntry.reference_number.ilike(search_term),
                    FinancialControlEntry.external_reference.ilike(search_term)
                )
            )
        
        if filters.is_overdue is not None:
            if filters.is_overdue:
                query = query.where(
                    and_(
                        FinancialControlEntry.status != ControlEntryStatus.PAID,
                        FinancialControlEntry.due_date < date.today()
                    )
                )
            else:
                query = query.where(
                    or_(
                        FinancialControlEntry.status == ControlEntryStatus.PAID,
                        FinancialControlEntry.due_date >= date.today(),
                        FinancialControlEntry.due_date.is_(None)
                    )
                )
        
        if filters.is_recurring is not None:
            query = query.where(FinancialControlEntry.is_recurring == filters.is_recurring)
        
        if filters.is_tax_deductible is not None:
            query = query.where(FinancialControlEntry.is_tax_deductible == filters.is_tax_deductible)
        
        return query
    
    async def _generate_reference_number(self, tenant_id: UUID, entry_type: ControlEntryType) -> str:
        """Generate a unique reference number."""
        
        prefix = "INC" if entry_type == ControlEntryType.INCOME else "EXP"
        
        # Get the next sequence number
        query = select(func.count(FinancialControlEntry.id)).where(
            and_(
                FinancialControlEntry.tenant_id == tenant_id,
                FinancialControlEntry.entry_type == entry_type,
                FinancialControlEntry.reference_number.like(f"{prefix}-%")
            )
        )
        
        result = await self.db.execute(query)
        count = result.scalar() or 0
        
        return f"{prefix}-{count + 1:06d}"
