'use client';

import { useState, useEffect, useCallback } from 'react';
import { ClockIcon } from '@heroicons/react/24/outline';
import { KDSOrder, KDSOrderItem, KDSFilterType, KDSStats } from '@/types/kds';
import { kdsService } from '@/services/kdsService';
import { useKDSWebSocket } from './websockets/KDSWebSocketProvider';
import { useAuth } from '@/lib/auth/AuthProvider';
import { useTenant } from '@/lib/tenant/TenantProvider';
import KDSOrderCard from './KDSOrderCard';
import toast from 'react-hot-toast';

interface KDSOrderListProps {
  filter: KDSFilterType;
  onStatsUpdate: (stats: KDSStats) => void;
  onOrdersUpdate?: (orders: KDSOrder[]) => void;
  activeTab: 'orders' | 'completed';
}

export default function KDSOrderList({ filter, onStatsUpdate, onOrdersUpdate, activeTab }: KDSOrderListProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [orders, setOrders] = useState<KDSOrder[]>([]);

  // Authentication and tenant hooks
  const { isAuthenticated, user, isTenantOwner, isTenantStaff, isAdmin } = useAuth();
  const { currentTenant } = useTenant();

  // Hook do WebSocket para tempo real
  const { isConnected, newOrders } = useKDSWebSocket();

  // Função para atualizar um pedido específico
  const updateOrder = (updatedOrder: KDSOrder) => {
    setOrders(prevOrders =>
      prevOrders.map(order =>
        order.id === updatedOrder.id ? updatedOrder : order
      )
    );
  };

  // Buscar pedidos da API
  const fetchOrders = useCallback(async () => {
    // Don't fetch if not authenticated or no tenant selected
    if (!isAuthenticated || !user || !currentTenant) {
      setIsLoading(false);
      return;
    }

    // Check if user has staff permissions (KDS requires STAFF_ROLES: owner, manager, staff)
    const hasStaffPermissions = isAdmin() || isTenantOwner() || isTenantStaff();
    if (!hasStaffPermissions) {
      setError('Acesso negado. KDS requer permissões de staff.');
      setIsLoading(false);
      return;
    }

    try {
      setError(null);
      const fetchedOrders = await kdsService.getOrders({
        status: filter === 'all' ? undefined : filter,
        limit: 100
      });

      setOrders(fetchedOrders);

      // Calcular e enviar estatísticas
      const stats = kdsService.calculateStats(fetchedOrders);
      onStatsUpdate(stats);

      // Enviar pedidos para o componente pai
      onOrdersUpdate?.(fetchedOrders);

    } catch (err) {
      console.error('Erro ao buscar pedidos:', err);
      setError('Erro ao carregar pedidos. Tente novamente.');
      toast.error('Erro ao carregar pedidos');
    } finally {
      setIsLoading(false);
    }
  }, [filter, onStatsUpdate, isAuthenticated, user, currentTenant, isAdmin, isTenantOwner, isTenantStaff, setOrders, onOrdersUpdate]);

  // Carregar pedidos na inicialização e quando o filtro mudar
  useEffect(() => {
    // Only fetch if authenticated and has tenant
    if (isAuthenticated && user && currentTenant) {
      fetchOrders();
    }
  }, [fetchOrders, isAuthenticated, user, currentTenant]);

  // Atualizar automaticamente a cada 30 segundos
  useEffect(() => {
    // Only set interval if authenticated and has tenant
    if (!isAuthenticated || !user || !currentTenant) {
      return;
    }

    const interval = setInterval(fetchOrders, 30000);
    return () => clearInterval(interval);
  }, [fetchOrders, isAuthenticated, user, currentTenant]);

  // Atualizar status do pedido
  const handleOrderStatusChange = async (orderId: string, status: KDSOrder['status']) => {
    try {
      const updatedOrder = await kdsService.updateOrderStatus(orderId, status);

      // Atualizar via hook do Socket.IO
      updateOrder(updatedOrder);

      // Feedback visual
      switch (status) {
        case 'preparing':
          toast.success('Pedido iniciado na cozinha!');
          break;
        case 'ready':
          toast.success('Pedido marcado como pronto!');
          break;
        case 'served':
          toast.success('Pedido concluído!');
          break;
      }

    } catch (err) {
      console.error('Erro ao atualizar status do pedido:', err);
      toast.error('Erro ao atualizar pedido');
    }
  };

  // Atualizar status de item individual
  const handleItemStatusChange = async (
    orderId: string,
    itemId: string,
    status: KDSOrderItem['status']
  ) => {
    try {
      const updatedOrder = await kdsService.updateOrderItem(orderId, itemId, { status });

      // Atualizar via hook do Socket.IO
      updateOrder(updatedOrder);

      if (status === 'completed') {
        toast.success('Item marcado como concluído!');
      }

    } catch (err) {
      console.error('Erro ao atualizar item:', err);
      toast.error('Erro ao atualizar item');
    }
  };

  // Marcar item como concluído/não concluído
  const handleToggleItemDone = async (orderId: string, itemId: string, done: boolean) => {
    try {
      const updatedOrder = await kdsService.toggleItemDone(orderId, itemId, done);

      // Verificar se todos os itens estão concluídos
      const allItemsCompleted = updatedOrder.items.every(item => item.done);

      // Se todos os itens estão concluídos e o pedido está em preparo,
      // automaticamente mover para "ready" (ordens completas)
      if (allItemsCompleted && updatedOrder.status === 'preparing') {
        const finalOrder = await kdsService.updateOrderStatus(orderId, 'ready');
        updateOrder(finalOrder);
        toast.success('🎉 Pedido concluído! Movido para ordens completas.');
      } else {
        // Atualizar via hook do Socket.IO
        updateOrder(updatedOrder);
        toast.success(done ? 'Item marcado como concluído!' : 'Item desmarcado');
      }

    } catch (err) {
      console.error('Erro ao atualizar status do item:', err);
      toast.error('Erro ao atualizar item');
    }
  };

  // Atualizar notas da cozinha para um item
  const handleUpdateItemKitchenNotes = async (
    orderId: string,
    itemId: string,
    notes: string
  ) => {
    try {
      const updatedOrder = await kdsService.updateItemKitchenNotes(orderId, itemId, notes);

      // Atualizar via hook do Socket.IO
      updateOrder(updatedOrder);

      toast.success('Nota da cozinha atualizada!');

    } catch (err) {
      console.error('Erro ao atualizar notas do item:', err);
      toast.error('Erro ao atualizar nota');
    }
  };

  // Atualizar notas gerais da cozinha para o pedido
  const handleUpdateOrderKitchenNotes = async (orderId: string, notes: string) => {
    try {
      const updatedOrder = await kdsService.updateOrderKitchenNotes(orderId, notes);

      // Atualizar via hook do Socket.IO
      updateOrder(updatedOrder);

      toast.success('Nota geral da cozinha atualizada!');

    } catch (err) {
      console.error('Erro ao atualizar notas do pedido:', err);
      toast.error('Erro ao atualizar nota');
    }
  };

  // Filtrar pedidos baseado na tab ativa e filtro selecionado
  const filteredOrders = (() => {
    let baseFiltered = orders;

    // Primeiro filtrar por tab
    if (activeTab === 'orders') {
      // Tab "Em Preparo": pending, preparing
      baseFiltered = orders.filter(order =>
        order.status === 'pending' || order.status === 'preparing'
      );
    } else {
      // Tab "Finalizados": ready, served
      baseFiltered = orders.filter(order =>
        order.status === 'ready' || order.status === 'served'
      );
    }

    // Depois aplicar filtro adicional se necessário
    if (filter === 'all') {
      return baseFiltered;
    } else {
      return baseFiltered.filter(order => order.status === filter);
    }
  })();



  // Ordenar pedidos por prioridade e tempo
  const sortedOrders = [...filteredOrders].sort((a, b) => {
    // Primeiro por prioridade
    const priorityOrder = { urgent: 3, high: 2, normal: 1 };
    const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
    if (priorityDiff !== 0) return priorityDiff;

    // Depois por tempo (mais antigos primeiro)
    return new Date(a.order_time).getTime() - new Date(b.order_time).getTime();
  });

  // Show loading if not authenticated or no tenant
  if (!isAuthenticated || !user || !currentTenant) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Aguardando autenticação...</p>
        </div>
      </div>
    );
  }

  // Check if user has staff permissions
  const hasStaffPermissions = isAdmin() || isTenantOwner() || isTenantStaff();
  if (!hasStaffPermissions) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-red-600 text-2xl">🚫</span>
          </div>
          <p className="text-gray-900 font-medium mb-2">Acesso Restrito</p>
          <p className="text-gray-600">O KDS requer permissões de staff (Owner, Manager ou Staff).</p>
          <p className="text-sm text-gray-500 mt-2">
            Sistema: {user?.system_role} |
            Owner: {isTenantOwner() ? 'Sim' : 'Não'} |
            Staff: {isTenantStaff() ? 'Sim' : 'Não'}
          </p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando pedidos...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <ClockIcon className="mx-auto h-12 w-12" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Erro ao carregar</h3>
          <p className="text-gray-500 mb-4">{error}</p>
          <button
            onClick={fetchOrders}
            className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  if (sortedOrders.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center text-gray-500">
          <ClockIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhum pedido</h3>
          <p className="text-gray-500">
            {filter === 'all'
              ? 'Tudo em dia! Nenhum pedido ativo para exibir.'
              : `Nenhum pedido ${filter === 'pending' ? 'pendente' : filter === 'preparing' ? 'em preparo' : 'pronto'} no momento.`
            }
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 p-4 overflow-y-auto bg-gray-50">
      {/* Indicador de conexão WebSocket */}
      {!isConnected && (
        <div className="mb-4 bg-yellow-50 border-l-4 border-yellow-400 rounded-lg p-3 shadow-sm">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-6 w-6 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-yellow-800">
                ⚠️ Conexão em tempo real desconectada. Os pedidos podem não ser atualizados automaticamente.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Layout flexível que se adapta ao conteúdo dos cards */}
      <div className="columns-1 lg:columns-2 xl:columns-3 2xl:columns-4 gap-4 space-y-4">
        {sortedOrders.map(order => (
          <div key={order.id} className="break-inside-avoid mb-4">
            <KDSOrderCard
              order={order}
              onStatusChange={handleOrderStatusChange}
              onItemStatusChange={handleItemStatusChange}
              onToggleItemDone={handleToggleItemDone}
              onUpdateItemKitchenNotes={handleUpdateItemKitchenNotes}
              onUpdateOrderKitchenNotes={handleUpdateOrderKitchenNotes}
            />
          </div>
        ))}
      </div>
    </div>
  );
}
