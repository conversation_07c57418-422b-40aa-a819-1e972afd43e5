"""
TVendorSupplier Model for EShop B2B System
==========================================

Modelo especializado para fornecedores B2B (TVendorSupplier) com campos específicos
para gestão de produtos, comissões, verificação empresarial e performance de vendas.

Este modelo estende o sistema de associação tenant-user existente,
fornecendo campos adicionais específicos para operações B2B de fornecimento.
"""

import uuid
import enum
from datetime import datetime, timedelta
from typing import TYPE_CHECKING, Optional
from decimal import Decimal

from sqlalchemy import (
    Column, String, ForeignKey, Text, Enum, DateTime, 
    Boolean, Index, func, Numeric, JSON, Integer
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, Mapped, mapped_column

from app.db.base import Base

# Use TYPE_CHECKING to handle circular dependencies
if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.core.users.models.user import User
    from app.modules.core.users.models.tenant_user_association import TenantUserAssociation


class TVendorStatus(str, enum.Enum):
    """Status do fornecedor B2B."""
    
    PENDING = "pending"
    ACTIVE = "active"
    SUSPENDED = "suspended"
    INACTIVE = "inactive"
    REJECTED = "rejected"
    UNDER_REVIEW = "under_review"


class SupplierType(str, enum.Enum):
    """Tipo de fornecedor."""
    
    MANUFACTURER = "manufacturer"
    DISTRIBUTOR = "distributor"
    WHOLESALER = "wholesaler"
    RETAILER = "retailer"
    DROPSHIPPER = "dropshipper"
    SERVICE_PROVIDER = "service_provider"
    OTHER = "other"


class VerificationStatus(str, enum.Enum):
    """Status de verificação do fornecedor."""
    
    PENDING = "pending"
    DOCUMENTS_SUBMITTED = "documents_submitted"
    UNDER_REVIEW = "under_review"
    VERIFIED = "verified"
    REJECTED = "rejected"
    EXPIRED = "expired"


class TVendorSupplier(Base):
    """
    Modelo para fornecedores B2B (TVendorSupplier).
    
    Estende o sistema de associação tenant-user com campos específicos
    para gestão de fornecedores, incluindo verificação de negócio,
    gestão de produtos, comissões e performance de vendas.
    """
    
    __tablename__ = "eshop_tvendor_suppliers"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("tenants.id"), 
        nullable=False, 
        index=True
    )
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        index=True
    )
    
    # Referência à associação tenant-user (onde estão role, commission_rate, etc.)
    tenant_user_association_id = Column(
        UUID(as_uuid=True),
        nullable=True,  # Pode ser preenchido após criação da associação
        index=True
    )
    
    # Status e configurações
    status = Column(
        Enum(TVendorStatus), 
        default=TVendorStatus.PENDING, 
        nullable=False, 
        index=True
    )
    
    # Informações empresariais
    company_name = Column(String(255), nullable=False, index=True)
    supplier_type = Column(Enum(SupplierType), nullable=False)
    tax_id = Column(String(50), nullable=False, unique=True, index=True)  # CNPJ/EIN
    business_registration_number = Column(String(100), nullable=True)
    
    # Endereço empresarial
    business_address = Column(JSON, nullable=True)
    
    # Informações de contato empresarial
    business_phone = Column(String(20), nullable=True)
    business_email = Column(String(255), nullable=True)
    website = Column(String(255), nullable=True)
    
    # Verificação empresarial
    verification_status = Column(
        Enum(VerificationStatus), 
        default=VerificationStatus.PENDING, 
        nullable=False, 
        index=True
    )
    verification_date = Column(DateTime, nullable=True)
    verification_notes = Column(Text, nullable=True)
    verification_expires_at = Column(DateTime, nullable=True)
    
    # Documentos de verificação (URLs ou IDs de arquivos)
    verification_documents = Column(JSON, nullable=True)
    
    # Configurações de comissão
    commission_rate = Column(Numeric(5, 2), default=0.00, nullable=False)  # Porcentagem
    commission_type = Column(String(20), default='percentage', nullable=False)  # percentage, fixed
    minimum_commission = Column(Numeric(10, 2), default=0.00, nullable=False)
    
    # Configurações de produtos
    max_products_allowed = Column(Integer, default=100, nullable=False)
    auto_approve_products = Column(Boolean, default=False, nullable=False)
    require_product_approval = Column(Boolean, default=True, nullable=False)
    
    # Informações financeiras
    annual_revenue = Column(Numeric(15, 2), nullable=True)
    employee_count = Column(Integer, nullable=True)
    years_in_business = Column(Integer, nullable=True)
    
    # Estatísticas de vendas
    total_products = Column(Integer, default=0, nullable=False)
    active_products = Column(Integer, default=0, nullable=False)
    total_sales = Column(Numeric(12, 2), default=0.00, nullable=False)
    total_orders = Column(Integer, default=0, nullable=False)
    average_order_value = Column(Numeric(10, 2), default=0.00, nullable=False)
    total_commission_earned = Column(Numeric(12, 2), default=0.00, nullable=False)
    
    # Performance metrics
    customer_rating = Column(Numeric(3, 2), default=0.00, nullable=False)  # 0.00 to 5.00
    total_reviews = Column(Integer, default=0, nullable=False)
    fulfillment_rate = Column(Numeric(5, 2), default=100.00, nullable=False)  # Percentage
    on_time_delivery_rate = Column(Numeric(5, 2), default=100.00, nullable=False)  # Percentage
    
    # Configurações de conta
    can_manage_inventory = Column(Boolean, default=True, nullable=False)
    can_set_pricing = Column(Boolean, default=True, nullable=False)
    can_create_promotions = Column(Boolean, default=False, nullable=False)
    
    # Informações do gerente de conta
    account_manager_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=True,
        index=True
    )
    
    # Configurações de pagamento
    payment_terms = Column(String(50), default='net_30', nullable=False)
    payment_method = Column(String(50), nullable=True)  # bank_transfer, check, etc.
    bank_account_info = Column(JSON, nullable=True)  # Encrypted bank details
    
    # Configurações de entrega
    shipping_zones = Column(JSON, nullable=True)  # Zonas de entrega suportadas
    shipping_methods = Column(JSON, nullable=True)  # Métodos de entrega oferecidos
    processing_time_days = Column(Integer, default=1, nullable=False)
    
    # Metadados adicionais
    additional_metadata = Column(JSON, nullable=True)
    notes = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )
    approved_at = Column(DateTime, nullable=True)
    last_activity_at = Column(DateTime, nullable=True)
    last_sale_at = Column(DateTime, nullable=True)
    
    # Relacionamentos
    tenant = relationship("Tenant")
    user = relationship("User", foreign_keys=[user_id])
    account_manager = relationship("User", foreign_keys=[account_manager_id])
    
    # Índices para performance
    __table_args__ = (
        Index("ix_tvendor_tenant_user", "tenant_id", "user_id"),
        Index("ix_tvendor_status_tenant", "status", "tenant_id"),
        Index("ix_tvendor_company_name", "company_name"),
        Index("ix_tvendor_verification_status", "verification_status"),
        Index("ix_tvendor_account_manager", "account_manager_id"),
        Index("ix_tvendor_created", "created_at"),
        Index("ix_tvendor_supplier_type", "supplier_type"),
    )
    
    def __repr__(self):
        return (
            f"<TVendorSupplier(id={self.id}, "
            f"company_name='{self.company_name}', "
            f"status='{self.status}', "
            f"total_sales={self.total_sales})>"
        )
    
    @property
    def is_verified(self) -> bool:
        """Verifica se o fornecedor está verificado."""
        return self.verification_status == VerificationStatus.VERIFIED
    
    @property
    def is_active(self) -> bool:
        """Verifica se o fornecedor está ativo."""
        return self.status == TVendorStatus.ACTIVE
    
    @property
    def verification_expired(self) -> bool:
        """Verifica se a verificação expirou."""
        if not self.verification_expires_at:
            return False
        return datetime.utcnow() > self.verification_expires_at
    
    @property
    def commission_rate_decimal(self) -> Decimal:
        """Retorna a taxa de comissão como decimal (para cálculos)."""
        return self.commission_rate / 100
    
    def approve_vendor(self, approved_by_id: uuid.UUID, notes: str = None):
        """
        Aprova o fornecedor.
        
        Args:
            approved_by_id: ID do usuário que aprovou
            notes: Notas da aprovação
        """
        self.status = TVendorStatus.ACTIVE
        self.verification_status = VerificationStatus.VERIFIED
        self.verification_date = datetime.utcnow()
        self.approved_at = datetime.utcnow()
        
        # Definir expiração da verificação (1 ano)
        self.verification_expires_at = datetime.utcnow() + timedelta(days=365)
        
        if notes:
            self.verification_notes = notes
        
        self.updated_at = datetime.utcnow()
    
    def reject_vendor(self, rejected_by_id: uuid.UUID, reason: str):
        """
        Rejeita o fornecedor.
        
        Args:
            rejected_by_id: ID do usuário que rejeitou
            reason: Motivo da rejeição
        """
        self.status = TVendorStatus.REJECTED
        self.verification_status = VerificationStatus.REJECTED
        self.verification_date = datetime.utcnow()
        self.verification_notes = reason
        self.updated_at = datetime.utcnow()
    
    def update_sales_stats(self, order_value: Decimal, commission_amount: Decimal):
        """
        Atualiza estatísticas de vendas.
        
        Args:
            order_value: Valor do pedido
            commission_amount: Valor da comissão
        """
        self.total_orders += 1
        self.total_sales += order_value
        self.total_commission_earned += commission_amount
        self.average_order_value = self.total_sales / self.total_orders
        self.last_sale_at = datetime.utcnow()
        self.last_activity_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
    
    def update_product_count(self, active_count: int, total_count: int):
        """
        Atualiza contadores de produtos.
        
        Args:
            active_count: Número de produtos ativos
            total_count: Número total de produtos
        """
        self.active_products = active_count
        self.total_products = total_count
        self.updated_at = datetime.utcnow()
    
    def calculate_commission(self, sale_amount: Decimal) -> Decimal:
        """
        Calcula comissão baseada no valor da venda.
        
        Args:
            sale_amount: Valor da venda
            
        Returns:
            Decimal: Valor da comissão
        """
        if self.commission_type == 'percentage':
            commission = sale_amount * self.commission_rate_decimal
            return max(commission, self.minimum_commission)
        else:
            return self.minimum_commission
