"""
Blog API Router

Main router for blog API endpoints.
"""

from fastapi import APIRouter

from .blog_posts import router as posts_router
from .blog_categories import router as categories_router
from .blog_tags import router as tags_router
from .blog_authors import router as authors_router
from .blog_comments import router as comments_router

# Create main blog router
router = APIRouter(prefix="/blog", tags=["blog"])

# Include sub-routers
router.include_router(posts_router, prefix="/posts", tags=["blog-posts"])
router.include_router(categories_router, prefix="/categories", tags=["blog-categories"])
router.include_router(tags_router, prefix="/tags", tags=["blog-tags"])
router.include_router(authors_router, prefix="/authors", tags=["blog-authors"])
router.include_router(comments_router, prefix="/comments", tags=["blog-comments"])


@router.get("/", summary="Blog System Status")
async def blog_status():
    """
    Get blog system status and information.
    
    Returns basic information about the blog system including
    available features and API version.
    """
    return {
        "status": "active",
        "module": "blog",
        "version": "1.0.0",
        "features": [
            "multi_language_support",
            "seo_optimization", 
            "comment_system",
            "category_management",
            "tag_system",
            "author_profiles",
            "content_scheduling",
            "search_functionality"
        ],
        "endpoints": {
            "posts": "/blog/posts",
            "categories": "/blog/categories", 
            "tags": "/blog/tags",
            "authors": "/blog/authors",
            "comments": "/blog/comments"
        }
    }
