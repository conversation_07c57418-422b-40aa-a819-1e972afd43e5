import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/auth/presentation/pages/splash_page.dart';
import '../../features/staff/presentation/pages/staff_dashboard_page.dart';
import '../../features/staff/presentation/pages/orders_page.dart';
import '../../features/staff/presentation/pages/tables_page.dart';
import '../../features/staff/presentation/pages/delivery_page.dart';
import '../../features/staff/presentation/pages/menu_page.dart';
import '../../features/staff/presentation/pages/order_details_page.dart';
import '../../features/staff/presentation/pages/create_order_page.dart';
import '../../features/admin/presentation/pages/admin_dashboard_page.dart';
import '../../features/admin/presentation/pages/staff_management_page.dart';
import '../../features/admin/presentation/pages/menu_management_page.dart';
import '../../features/admin/presentation/pages/table_management_page.dart';
import '../../features/admin/presentation/pages/reports_page.dart';
import '../../features/admin/presentation/pages/settings_page.dart';
import '../services/storage_service.dart';
import '../constants/app_constants.dart';

// Route names
class AppRoutes {
  static const String splash = '/splash';
  static const String login = '/login';
  
  // Staff routes
  static const String staffDashboard = '/staff';
  static const String orders = '/staff/orders';
  static const String tables = '/staff/tables';
  static const String delivery = '/staff/delivery';
  static const String menu = '/staff/menu';
  static const String orderDetails = '/staff/orders/:orderId';
  static const String createOrder = '/staff/create-order';
  
  // Admin routes
  static const String adminDashboard = '/admin';
  static const String staffManagement = '/admin/staff';
  static const String menuManagement = '/admin/menu';
  static const String tableManagement = '/admin/tables';
  static const String reports = '/admin/reports';
  static const String settings = '/admin/settings';
}

// Router provider
final appRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: AppRoutes.splash,
    debugLogDiagnostics: true,
    redirect: (context, state) {
      final isLoggedIn = StorageService.isLoggedIn();
      final userRole = StorageService.getCurrentUserRole();
      final location = state.location;
      
      // If not logged in and not on login/splash page, redirect to login
      if (!isLoggedIn && 
          location != AppRoutes.login && 
          location != AppRoutes.splash) {
        return AppRoutes.login;
      }
      
      // If logged in and on login/splash page, redirect based on role
      if (isLoggedIn && 
          (location == AppRoutes.login || location == AppRoutes.splash)) {
        if (userRole == AppConstants.roleOwner) {
          return AppRoutes.adminDashboard;
        } else {
          return AppRoutes.staffDashboard;
        }
      }
      
      // Role-based access control
      if (isLoggedIn) {
        final isAdminRoute = location.startsWith('/admin');
        final isStaffRoute = location.startsWith('/staff');
        
        if (userRole == AppConstants.roleOwner && isStaffRoute) {
          return AppRoutes.adminDashboard;
        }
        
        if (userRole == AppConstants.roleStaff && isAdminRoute) {
          return AppRoutes.staffDashboard;
        }
      }
      
      return null; // No redirect needed
    },
    routes: [
      // Splash route
      GoRoute(
        path: AppRoutes.splash,
        name: 'splash',
        builder: (context, state) => const SplashPage(),
      ),
      
      // Auth routes
      GoRoute(
        path: AppRoutes.login,
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),
      
      // Staff routes
      GoRoute(
        path: AppRoutes.staffDashboard,
        name: 'staff-dashboard',
        builder: (context, state) => const StaffDashboardPage(),
        routes: [
          GoRoute(
            path: 'orders',
            name: 'orders',
            builder: (context, state) => const OrdersPage(),
            routes: [
              GoRoute(
                path: ':orderId',
                name: 'order-details',
                builder: (context, state) {
                  final orderId = state.pathParameters['orderId']!;
                  return OrderDetailsPage(orderId: orderId);
                },
              ),
            ],
          ),
          GoRoute(
            path: 'tables',
            name: 'tables',
            builder: (context, state) => const TablesPage(),
          ),
          GoRoute(
            path: 'delivery',
            name: 'delivery',
            builder: (context, state) => const DeliveryPage(),
          ),
          GoRoute(
            path: 'menu',
            name: 'menu',
            builder: (context, state) => const MenuPage(),
          ),
          GoRoute(
            path: 'create-order',
            name: 'create-order',
            builder: (context, state) {
              final tableId = state.queryParameters['tableId'];
              final orderType = state.queryParameters['type'] ?? 'dine_in';
              return CreateOrderPage(
                tableId: tableId,
                orderType: orderType,
              );
            },
          ),
        ],
      ),
      
      // Admin routes
      GoRoute(
        path: AppRoutes.adminDashboard,
        name: 'admin-dashboard',
        builder: (context, state) => const AdminDashboardPage(),
        routes: [
          GoRoute(
            path: 'staff',
            name: 'staff-management',
            builder: (context, state) => const StaffManagementPage(),
          ),
          GoRoute(
            path: 'menu',
            name: 'menu-management',
            builder: (context, state) => const MenuManagementPage(),
          ),
          GoRoute(
            path: 'tables',
            name: 'table-management',
            builder: (context, state) => const TableManagementPage(),
          ),
          GoRoute(
            path: 'reports',
            name: 'reports',
            builder: (context, state) => const ReportsPage(),
          ),
          GoRoute(
            path: 'settings',
            name: 'settings',
            builder: (context, state) => const SettingsPage(),
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(
        title: const Text('Página não encontrada'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Página não encontrada',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'A página "${state.location}" não existe.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                final userRole = StorageService.getCurrentUserRole();
                if (userRole == AppConstants.roleOwner) {
                  context.go(AppRoutes.adminDashboard);
                } else {
                  context.go(AppRoutes.staffDashboard);
                }
              },
              child: const Text('Voltar ao início'),
            ),
          ],
        ),
      ),
    ),
  );
});

// Navigation helper extension
extension AppRouterExtension on GoRouter {
  void goToStaffDashboard() => go(AppRoutes.staffDashboard);
  void goToAdminDashboard() => go(AppRoutes.adminDashboard);
  void goToLogin() => go(AppRoutes.login);
  
  void goToOrders() => go(AppRoutes.orders);
  void goToTables() => go(AppRoutes.tables);
  void goToDelivery() => go(AppRoutes.delivery);
  void goToMenu() => go(AppRoutes.menu);
  
  void goToOrderDetails(String orderId) {
    go('/staff/orders/$orderId');
  }
  
  void goToCreateOrder({String? tableId, String orderType = 'dine_in'}) {
    final queryParams = <String, String>{'type': orderType};
    if (tableId != null) {
      queryParams['tableId'] = tableId;
    }
    go(Uri(path: AppRoutes.createOrder, queryParameters: queryParams).toString());
  }
  
  void goToStaffManagement() => go(AppRoutes.staffManagement);
  void goToMenuManagement() => go(AppRoutes.menuManagement);
  void goToTableManagement() => go(AppRoutes.tableManagement);
  void goToReports() => go(AppRoutes.reports);
  void goToSettings() => go(AppRoutes.settings);
}

// Navigation helper functions
class AppNavigation {
  static void logout(BuildContext context) {
    StorageService.clearCurrentUser();
    context.go(AppRoutes.login);
  }
  
  static void navigateBasedOnRole(BuildContext context) {
    final userRole = StorageService.getCurrentUserRole();
    if (userRole == AppConstants.roleOwner) {
      context.go(AppRoutes.adminDashboard);
    } else {
      context.go(AppRoutes.staffDashboard);
    }
  }
  
  static bool canAccessAdminRoutes() {
    final userRole = StorageService.getCurrentUserRole();
    return userRole == AppConstants.roleOwner;
  }
  
  static bool canAccessStaffRoutes() {
    final userRole = StorageService.getCurrentUserRole();
    return userRole == AppConstants.roleStaff || userRole == AppConstants.roleOwner;
  }
}