"""
Help Center File Service

Serviço para gerenciamento de arquivos no sistema de help center.
Integra com o sistema de mídia existente.
"""

import logging
import os
from typing import List, Optional
from uuid import UUID

from fastapi import UploadFile
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.modules.core.users.models.user import User
from app.modules.core.functions.media_system.models import (
    MediaContext, MediaUpload, MediaFileType, MediaUploadStatus,
    MediaContextType
)
from app.modules.core.functions.media_system.services.media_upload_service import MediaUploadService

from ..models import TicketMessage, MessageType

logger = logging.getLogger(__name__)


class HelpCenterFileService:
    """Serviço para gerenciamento de arquivos do help center."""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.media_service = MediaUploadService(db)

    async def upload_ticket_file(
        self,
        file: UploadFile,
        user: User,
        ticket_id: UUID,
        message_id: Optional[UUID] = None
    ) -> Optional[MediaUpload]:
        """
        Faz upload de arquivo para um ticket.
        
        Args:
            file: Arquivo para upload
            user: Usuário fazendo o upload
            ticket_id: ID do ticket
            message_id: ID da mensagem (opcional)
            
        Returns:
            MediaUpload criado ou None se erro
        """
        try:
            # Determinar tipo de arquivo
            media_type = self._get_media_type_from_mime(file.content_type)
            
            # Criar contexto de mídia para help center se não existir
            context = await self._get_or_create_help_center_context(user.id)
            if not context:
                logger.error(f"Não foi possível criar contexto de mídia para usuário {user.id}")
                return None
            
            # Verificar quota
            file_size_mb = len(await file.read()) // (1024 * 1024)
            await file.seek(0)  # Reset file pointer
            
            if not context.can_upload(file_size_mb):
                logger.warning(f"Quota excedida para usuário {user.id}")
                return None
            
            # Fazer upload usando o serviço de mídia
            upload = await self.media_service.upload_file(
                file=file,
                context_id=context.id,
                uploaded_by=user.id,
                directory_name="help_center_files"
            )
            
            if upload:
                # Adicionar metadados específicos do help center
                metadata = {
                    "ticket_id": str(ticket_id),
                    "message_id": str(message_id) if message_id else None,
                    "uploaded_for": "help_center"
                }
                
                upload.file_metadata = str(metadata)
                await self.db.commit()
                
                logger.info(f"Arquivo {file.filename} enviado para ticket {ticket_id}")
            
            return upload
            
        except Exception as e:
            logger.error(f"Erro ao fazer upload de arquivo: {e}")
            return None

    async def get_ticket_files(
        self,
        ticket_id: UUID,
        user: User
    ) -> List[MediaUpload]:
        """
        Obtém todos os arquivos de um ticket.
        
        Args:
            ticket_id: ID do ticket
            user: Usuário solicitante
            
        Returns:
            Lista de uploads do ticket
        """
        try:
            # Buscar uploads que contenham o ticket_id nos metadados
            stmt = select(MediaUpload).where(
                MediaUpload.file_metadata.contains(f'"ticket_id": "{ticket_id}"')
            )
            
            result = await self.db.execute(stmt)
            uploads = result.scalars().all()
            
            # Filtrar apenas arquivos que o usuário pode acessar
            accessible_uploads = []
            for upload in uploads:
                if await self._user_can_access_file(upload, user):
                    accessible_uploads.append(upload)
            
            return accessible_uploads
            
        except Exception as e:
            logger.error(f"Erro ao buscar arquivos do ticket {ticket_id}: {e}")
            return []

    async def delete_ticket_file(
        self,
        file_id: UUID,
        user: User
    ) -> bool:
        """
        Deleta um arquivo de ticket.
        
        Args:
            file_id: ID do arquivo
            user: Usuário solicitante
            
        Returns:
            True se deletado com sucesso
        """
        try:
            # Buscar o upload
            stmt = select(MediaUpload).where(MediaUpload.id == file_id)
            result = await self.db.execute(stmt)
            upload = result.scalar_one_or_none()
            
            if not upload:
                return False
            
            # Verificar permissão
            if not await self._user_can_delete_file(upload, user):
                return False
            
            # Deletar usando o serviço de mídia
            success = await self.media_service.delete_file(file_id)
            
            if success:
                logger.info(f"Arquivo {file_id} deletado por usuário {user.id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Erro ao deletar arquivo {file_id}: {e}")
            return False

    async def get_file_url(
        self,
        file_id: UUID,
        user: User
    ) -> Optional[str]:
        """
        Obtém URL de acesso a um arquivo.
        
        Args:
            file_id: ID do arquivo
            user: Usuário solicitante
            
        Returns:
            URL do arquivo ou None se sem acesso
        """
        try:
            # Buscar o upload
            stmt = select(MediaUpload).where(MediaUpload.id == file_id)
            result = await self.db.execute(stmt)
            upload = result.scalar_one_or_none()
            
            if not upload:
                return None
            
            # Verificar permissão
            if not await self._user_can_access_file(upload, user):
                return None
            
            # Retornar URL usando o serviço de mídia
            return await self.media_service.get_file_url(file_id)
            
        except Exception as e:
            logger.error(f"Erro ao obter URL do arquivo {file_id}: {e}")
            return None

    async def cleanup_orphaned_files(self) -> int:
        """
        Remove arquivos órfãos (não associados a mensagens).
        
        Returns:
            Número de arquivos removidos
        """
        try:
            # Buscar uploads do help center sem mensagens associadas
            stmt = select(MediaUpload).where(
                MediaUpload.file_metadata.contains('"uploaded_for": "help_center"')
            )
            
            result = await self.db.execute(stmt)
            uploads = result.scalars().all()
            
            orphaned_count = 0
            
            for upload in uploads:
                # Verificar se existe mensagem associada
                metadata = eval(upload.file_metadata) if upload.file_metadata else {}
                message_id = metadata.get("message_id")
                
                if message_id:
                    # Verificar se a mensagem ainda existe
                    msg_stmt = select(TicketMessage).where(TicketMessage.id == UUID(message_id))
                    msg_result = await self.db.execute(msg_stmt)
                    message = msg_result.scalar_one_or_none()
                    
                    if not message:
                        # Mensagem não existe, arquivo é órfão
                        await self.media_service.delete_file(upload.id)
                        orphaned_count += 1
                        logger.info(f"Arquivo órfão removido: {upload.id}")
            
            return orphaned_count
            
        except Exception as e:
            logger.error(f"Erro ao limpar arquivos órfãos: {e}")
            return 0

    async def _get_or_create_help_center_context(self, user_id: UUID) -> Optional[MediaContext]:
        """
        Obtém ou cria contexto de mídia para help center.
        
        Args:
            user_id: ID do usuário
            
        Returns:
            MediaContext ou None se erro
        """
        try:
            # Buscar contexto existente
            stmt = select(MediaContext).where(
                MediaContext.context_type == "help_center",
                MediaContext.context_id == user_id
            )
            
            result = await self.db.execute(stmt)
            context = result.scalar_one_or_none()
            
            if not context:
                # Criar novo contexto
                context = MediaContext(
                    context_type="help_center",
                    context_id=user_id,
                    quota_limit_mb=1024,  # 1GB para help center
                    is_quota_enabled=True
                )
                
                self.db.add(context)
                await self.db.commit()
                await self.db.refresh(context)
                
                logger.info(f"Contexto de mídia criado para help center - usuário {user_id}")
            
            return context
            
        except Exception as e:
            logger.error(f"Erro ao obter/criar contexto de mídia: {e}")
            return None

    def _get_media_type_from_mime(self, mime_type: str) -> MediaFileType:
        """
        Determina o tipo de mídia baseado no MIME type.
        
        Args:
            mime_type: MIME type do arquivo
            
        Returns:
            MediaFileType correspondente
        """
        if not mime_type:
            return MediaFileType.OTHER
        
        mime_lower = mime_type.lower()
        
        if mime_lower.startswith('image/'):
            return MediaFileType.IMAGE
        elif mime_lower.startswith('video/'):
            return MediaFileType.VIDEO
        elif mime_lower.startswith('audio/'):
            return MediaFileType.AUDIO
        elif mime_lower in ['application/pdf', 'text/plain', 'application/msword',
                           'application/vnd.openxmlformats-officedocument.wordprocessingml.document']:
            return MediaFileType.DOCUMENT
        elif mime_lower in ['application/zip', 'application/x-rar-compressed',
                           'application/x-7z-compressed']:
            return MediaFileType.ARCHIVE
        elif mime_lower.startswith('text/'):
            return MediaFileType.TEXT
        else:
            return MediaFileType.OTHER

    async def _user_can_access_file(self, upload: MediaUpload, user: User) -> bool:
        """
        Verifica se usuário pode acessar o arquivo.
        
        Args:
            upload: Upload a verificar
            user: Usuário
            
        Returns:
            True se pode acessar
        """
        # Admin pode acessar todos os arquivos
        if user.system_role == "admin":
            return True
        
        # Usuário pode acessar arquivos que ele enviou
        if upload.uploaded_by == user.id:
            return True
        
        # Verificar se o usuário tem acesso ao ticket
        try:
            metadata = eval(upload.file_metadata) if upload.file_metadata else {}
            ticket_id = metadata.get("ticket_id")
            
            if ticket_id:
                from ..services.ticket_service import TicketService
                ticket_service = TicketService(self.db)
                ticket = await ticket_service.get_ticket(UUID(ticket_id), user)
                return ticket is not None
        except:
            pass
        
        return False

    async def _user_can_delete_file(self, upload: MediaUpload, user: User) -> bool:
        """
        Verifica se usuário pode deletar o arquivo.
        
        Args:
            upload: Upload a verificar
            user: Usuário
            
        Returns:
            True se pode deletar
        """
        # Admin pode deletar todos os arquivos
        if user.system_role == "admin":
            return True
        
        # Usuário pode deletar apenas arquivos que ele enviou
        return upload.uploaded_by == user.id
