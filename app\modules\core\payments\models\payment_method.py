"""Payment method models for core payments module."""

import uuid  # noqa: E402
import enum
from typing import TYPE_CHECKING, List, Optional
from sqlalchemy import <PERSON>um<PERSON>, String, <PERSON>olean, Foreign<PERSON>ey, JSON, Enum, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base  # noqa: E402

# Use TYPE_CHECKING to handle circular dependencies for type hinting
if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.core.payments.models.payment_processor import PaymentProcessor


class PaymentMethodType(str, enum.Enum):
    """Enum for payment method types."""

    CASH = "cash"
    CREDIT_CARD = "credit_card"
    DEBIT_CARD = "debit_card"
    BANK_TRANSFER = "bank_transfer"
    MOBILE_PAYMENT = "mobile_payment"
    STORE_CREDIT = "store_credit"
    GIFT_CARD = "gift_card"
    CHECK = "check"
    CRYPTO = "crypto"
    PIX = "pix"  # Brazilian instant payment system
    BOLETO = "boleto"  # Brazilian payment slip
    OTHER = "other"


class PaymentMethod(Base):
    """
    Model for payment methods available to tenants.

    This represents a payment method that can be used in transactions.
    """

    __tablename__ = "core_payment_methods"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    processor_id = Column(UUID(as_uuid=True), ForeignKey("payment_processors.id"), nullable=True)

    name = Column(String, nullable=False)
    method_type = Column(Enum(PaymentMethodType), nullable=False)
    is_active = Column(Boolean, default=True)
    is_default = Column(Boolean, default=False)

    # Display options
    icon = Column(String, nullable=True)  # Icon name or URL
    display_order = Column(Integer, default=0)  # Order for display in UI

    # Configuration for the payment method
    processor_method_id = Column(String, nullable=True)  # ID in the payment processor system
    processor_config = Column(JSON, nullable=True)  # Configuration for the payment processor

    # Relationships
    tenant = relationship("app.modules.core.tenants.models.tenant.Tenant", viewonly=True)
    processor = relationship(
        "app.modules.core.payments.models.payment_processor.PaymentProcessor",
        back_populates="payment_methods",
    )

    def __repr__(self):
        return f"<PaymentMethod(id={self.id}, name='{self.name}', type='{self.method_type}')>"
