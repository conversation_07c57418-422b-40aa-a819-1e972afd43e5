"""Sale item model for POS module."""

import uuid  # noqa: E402
from sqlalchemy import Column, String, ForeignKey, Numeric, Integer, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base  # noqa: E402


class SaleItem(Base):
    """
    Model for items in a sale transaction.

    This represents a product or service sold in a transaction.
    """

    __tablename__ = "sale_items"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    transaction_id = Column(
        UUID(as_uuid=True),
        ForeignKey("sale_transactions.id"),
        nullable=False,
        index=True,
    )
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)

    # Product information
    product_id = Column(UUID(as_uuid=True), nullable=True)  # Optional link to inventory product
    product_name = Column(String, nullable=False)  # Name at time of sale
    product_code = Column(String, nullable=True)  # SKU, barcode, etc.

    # Quantity and pricing
    quantity = Column(Integer, nullable=False, default=1)
    unit_price = Column(Numeric(10, 2), nullable=False)
    total_price = Column(Numeric(10, 2), nullable=False)  # unit_price * quantity - discounts

    # Discounts
    discount_amount = Column(Numeric(10, 2), nullable=False, default=0)
    discount_percentage = Column(Numeric(5, 2), nullable=True)  # If percentage-based discount
    discount_reason = Column(String, nullable=True)

    # Additional information
    notes = Column(String, nullable=True)
    metadata = Column(JSON, nullable=True)  # For additional product-specific data

    # Relationships
    transaction = relationship("SaleTransaction", back_populates="items")
    tenant = relationship("Tenant")
    refund_items = relationship("RefundItem", back_populates="sale_item")

    def __repr__(self):
        return f"<SaleItem(id={self.id}, product='{self.product_name}', quantity={self.quantity}, total={self.total_price})>"  # noqa: E501
