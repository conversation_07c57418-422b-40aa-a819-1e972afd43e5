"""
Notification Queue Service

Serviço para gerenciamento da fila de notificações.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Optional
from uuid import UUID

from sqlalchemy import and_, desc, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from ..models import Notification, NotificationQueue, NotificationStatus

logger = logging.getLogger(__name__)


class NotificationQueueService:
    """Serviço para gerenciamento da fila de notificações."""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def add_to_queue(
        self,
        notification_id: UUID,
        priority: int = 0,
        scheduled_at: Optional[datetime] = None
    ) -> NotificationQueue:
        """
        Adiciona notificação à fila de processamento.
        
        Args:
            notification_id: ID da notificação
            priority: Prioridade na fila (maior = mais prioritário)
            scheduled_at: Agendamento opcional
            
        Returns:
            Entrada da fila criada
        """
        queue_entry = NotificationQueue(
            notification_id=notification_id,
            priority=priority,
            scheduled_at=scheduled_at or datetime.utcnow(),
            status="pending"
        )
        
        self.db.add(queue_entry)
        await self.db.commit()
        await self.db.refresh(queue_entry)
        
        logger.info(f"Notificação {notification_id} adicionada à fila com prioridade {priority}")
        return queue_entry

    async def get_next_batch(self, batch_size: int = 10) -> List[NotificationQueue]:
        """
        Obtém próximo lote de notificações para processamento.
        
        Args:
            batch_size: Tamanho do lote
            
        Returns:
            Lista de entradas da fila
        """
        now = datetime.utcnow()
        
        stmt = select(NotificationQueue).where(
            and_(
                NotificationQueue.status == "pending",
                NotificationQueue.scheduled_at <= now,
                NotificationQueue.retry_count < NotificationQueue.max_retries
            )
        ).order_by(
            desc(NotificationQueue.priority),
            NotificationQueue.created_at
        ).limit(batch_size)
        
        result = await self.db.execute(stmt)
        queue_entries = result.scalars().all()
        
        # Marca como processando
        for entry in queue_entries:
            entry.status = "processing"
        
        await self.db.commit()
        
        logger.info(f"Obtido lote de {len(queue_entries)} notificações para processamento")
        return queue_entries

    async def mark_as_completed(self, queue_id: UUID) -> bool:
        """
        Marca entrada da fila como concluída.
        
        Args:
            queue_id: ID da entrada da fila
            
        Returns:
            True se marcada com sucesso
        """
        stmt = select(NotificationQueue).where(NotificationQueue.id == queue_id)
        result = await self.db.execute(stmt)
        queue_entry = result.scalar_one_or_none()
        
        if not queue_entry:
            return False
        
        queue_entry.status = "completed"
        queue_entry.processed_at = datetime.utcnow()
        
        await self.db.commit()
        
        logger.info(f"Entrada da fila {queue_id} marcada como concluída")
        return True

    async def mark_as_failed(
        self,
        queue_id: UUID,
        error_message: str,
        retry: bool = True
    ) -> bool:
        """
        Marca entrada da fila como falhada.
        
        Args:
            queue_id: ID da entrada da fila
            error_message: Mensagem de erro
            retry: Se deve tentar novamente
            
        Returns:
            True se marcada com sucesso
        """
        stmt = select(NotificationQueue).where(NotificationQueue.id == queue_id)
        result = await self.db.execute(stmt)
        queue_entry = result.scalar_one_or_none()
        
        if not queue_entry:
            return False
        
        queue_entry.retry_count += 1
        queue_entry.error_message = error_message
        
        if retry and queue_entry.retry_count < queue_entry.max_retries:
            # Reagenda para retry com backoff exponencial
            delay_minutes = 2 ** queue_entry.retry_count
            queue_entry.scheduled_at = datetime.utcnow() + timedelta(minutes=delay_minutes)
            queue_entry.status = "pending"
            logger.info(f"Entrada da fila {queue_id} reagendada para retry em {delay_minutes} minutos")
        else:
            queue_entry.status = "failed"
            queue_entry.processed_at = datetime.utcnow()
            logger.error(f"Entrada da fila {queue_id} marcada como falhada permanentemente")
        
        await self.db.commit()
        return True

    async def get_queue_stats(self) -> dict:
        """
        Obtém estatísticas da fila.
        
        Returns:
            Dicionário com estatísticas
        """
        # Conta por status
        stmt_pending = select(func.count(NotificationQueue.id)).where(
            NotificationQueue.status == "pending"
        )
        stmt_processing = select(func.count(NotificationQueue.id)).where(
            NotificationQueue.status == "processing"
        )
        stmt_completed = select(func.count(NotificationQueue.id)).where(
            NotificationQueue.status == "completed"
        )
        stmt_failed = select(func.count(NotificationQueue.id)).where(
            NotificationQueue.status == "failed"
        )
        
        pending_result = await self.db.execute(stmt_pending)
        processing_result = await self.db.execute(stmt_processing)
        completed_result = await self.db.execute(stmt_completed)
        failed_result = await self.db.execute(stmt_failed)
        
        pending_count = pending_result.scalar()
        processing_count = processing_result.scalar()
        completed_count = completed_result.scalar()
        failed_count = failed_result.scalar()
        
        total = pending_count + processing_count + completed_count + failed_count
        
        # Calcula tempo médio de processamento
        stmt_avg_time = select(
            func.avg(
                func.extract('epoch', NotificationQueue.processed_at - NotificationQueue.created_at)
            )
        ).where(
            and_(
                NotificationQueue.status == "completed",
                NotificationQueue.processed_at.is_not(None)
            )
        )
        
        avg_time_result = await self.db.execute(stmt_avg_time)
        avg_processing_time = avg_time_result.scalar() or 0
        
        return {
            "total": total,
            "pending": pending_count,
            "processing": processing_count,
            "completed": completed_count,
            "failed": failed_count,
            "success_rate": (completed_count / total * 100) if total > 0 else 0,
            "failure_rate": (failed_count / total * 100) if total > 0 else 0,
            "avg_processing_time_seconds": avg_processing_time
        }

    async def cleanup_old_entries(self, days_old: int = 30) -> int:
        """
        Remove entradas antigas da fila.
        
        Args:
            days_old: Idade em dias para remoção
            
        Returns:
            Número de entradas removidas
        """
        cutoff_date = datetime.utcnow() - timedelta(days=days_old)
        
        stmt = select(NotificationQueue).where(
            and_(
                NotificationQueue.created_at < cutoff_date,
                NotificationQueue.status.in_(["completed", "failed"])
            )
        )
        
        result = await self.db.execute(stmt)
        old_entries = result.scalars().all()
        
        count = len(old_entries)
        
        for entry in old_entries:
            await self.db.delete(entry)
        
        await self.db.commit()
        
        logger.info(f"Removidas {count} entradas antigas da fila")
        return count

    async def retry_failed_notifications(self, max_age_hours: int = 24) -> int:
        """
        Recoloca notificações falhadas na fila para retry.
        
        Args:
            max_age_hours: Idade máxima em horas para retry
            
        Returns:
            Número de notificações recolocadas
        """
        cutoff_date = datetime.utcnow() - timedelta(hours=max_age_hours)
        
        stmt = select(NotificationQueue).where(
            and_(
                NotificationQueue.status == "failed",
                NotificationQueue.created_at >= cutoff_date,
                NotificationQueue.retry_count < NotificationQueue.max_retries
            )
        )
        
        result = await self.db.execute(stmt)
        failed_entries = result.scalars().all()
        
        count = 0
        for entry in failed_entries:
            entry.status = "pending"
            entry.scheduled_at = datetime.utcnow()
            entry.error_message = None
            count += 1
        
        await self.db.commit()
        
        logger.info(f"Recolocadas {count} notificações falhadas na fila")
        return count

    async def get_queue_entries(
        self,
        status: Optional[str] = None,
        limit: int = 100
    ) -> List[NotificationQueue]:
        """
        Lista entradas da fila com filtros.
        
        Args:
            status: Status para filtrar
            limit: Limite de resultados
            
        Returns:
            Lista de entradas da fila
        """
        stmt = select(NotificationQueue)
        
        if status:
            stmt = stmt.where(NotificationQueue.status == status)
        
        stmt = stmt.order_by(
            desc(NotificationQueue.priority),
            NotificationQueue.created_at
        ).limit(limit)
        
        result = await self.db.execute(stmt)
        return result.scalars().all()
