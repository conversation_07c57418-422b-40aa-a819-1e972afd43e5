"""Financial Control Report Schemas."""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field
from uuid import UUID

from ..models.control_report import ReportType, ReportStatus


class ReportParametersSchema(BaseModel):
    """Schema for report parameters."""
    
    period_start: Optional[datetime] = None
    period_end: Optional[datetime] = None
    category_ids: Optional[List[UUID]] = None
    supplier_ids: Optional[List[UUID]] = None
    include_archived: bool = Field(False)
    group_by: Optional[str] = None
    format: str = Field("pdf", pattern="^(pdf|excel|csv)$")


class ControlReportBase(BaseModel):
    """Base schema for control reports."""
    
    report_type: ReportType
    title: str = Field(..., min_length=1, max_length=255, description="Report title")
    description: Optional[str] = Field(None, description="Report description")
    
    parameters: Optional[Dict[str, Any]] = Field(None, description="Report parameters")
    filters: Optional[Dict[str, Any]] = Field(None, description="Applied filters")
    
    period_start: Optional[datetime] = Field(None, description="Report period start")
    period_end: Optional[datetime] = Field(None, description="Report period end")
    
    is_scheduled: bool = Field(False, description="Is scheduled report")
    schedule_config: Optional[Dict[str, Any]] = Field(None, description="Schedule configuration")
    
    expires_at: Optional[datetime] = Field(None, description="Report expiry date")
    is_public: bool = Field(False, description="Is public report")
    shared_with: Optional[List[UUID]] = Field(None, description="Shared with users")
    
    tags: Optional[List[str]] = Field(None, description="Report tags")


class ControlReportCreate(ControlReportBase):
    """Schema for creating control reports."""
    pass


class ControlReportResponse(ControlReportBase):
    """Schema for control report responses."""
    
    id: UUID
    tenant_id: UUID
    status: ReportStatus
    
    data: Optional[Dict[str, Any]] = None
    summary: Optional[Dict[str, Any]] = None
    charts_data: Optional[Dict[str, Any]] = None
    
    total_records: Optional[int] = None
    file_size: Optional[int] = None
    format: Optional[str] = None
    
    file_path: Optional[str] = None
    file_url: Optional[str] = None
    
    error_message: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None
    retry_count: int = 0
    
    is_expired: bool
    duration_seconds: int
    
    generated_by: UUID
    updated_by: Optional[UUID] = None
    generated_at: datetime
    completed_at: Optional[datetime] = None
    updated_at: datetime
    
    class Config:
        from_attributes = True


class ControlReportList(BaseModel):
    """Schema for report lists."""
    
    items: List[ControlReportResponse]
    total: int
    page: int
    size: int
    pages: int
