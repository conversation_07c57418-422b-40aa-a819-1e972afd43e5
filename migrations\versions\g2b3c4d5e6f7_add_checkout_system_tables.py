"""add_checkout_system_tables

Revision ID: g2b3c4d5e6f7
Revises: f1a2b3c4d5e6
Create Date: 2025-06-27 16:05:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'g2b3c4d5e6f7'
down_revision: Union[str, None] = 'f1a2b3c4d5e6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    
    # Create enum types for checkout system
    checkout_status_enum = postgresql.ENUM(
        'initiated', 'payment_pending', 'payment_confirmed', 
        'completed', 'expired', 'cancelled',
        name='checkoutstatus'
    )
    checkout_status_enum.create(op.get_bind())
    
    shipping_method_enum = postgresql.ENUM(
        'standard', 'express', 'overnight', 'pickup', 'digital',
        name='shippingmethod'
    )
    shipping_method_enum.create(op.get_bind())
    
    payment_method_enum = postgresql.ENUM(
        'credit_card', 'debit_card', 'pix', 'bank_transfer', 'cash', 'wallet',
        name='paymentmethod'
    )
    payment_method_enum.create(op.get_bind())
    
    # Create eshop_checkout_sessions table
    op.create_table(
        'eshop_checkout_sessions',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('cart_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('order_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('status', checkout_status_enum, nullable=False),
        sa.Column('market_context', sa.String(length=20), nullable=False),
        sa.Column('shipping_address', sa.JSON(), nullable=True),
        sa.Column('billing_address', sa.JSON(), nullable=True),
        sa.Column('shipping_method', shipping_method_enum, nullable=True),
        sa.Column('shipping_cost', sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column('shipping_notes', sa.Text(), nullable=True),
        sa.Column('payment_method', payment_method_enum, nullable=True),
        sa.Column('payment_provider', sa.String(length=50), nullable=True),
        sa.Column('payment_external_id', sa.String(length=255), nullable=True),
        sa.Column('payment_reference', sa.String(length=255), nullable=True),
        sa.Column('subtotal', sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column('tax_amount', sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column('discount_amount', sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column('total_amount', sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column('currency', sa.String(length=3), nullable=False),
        sa.Column('customer_notes', sa.Text(), nullable=True),
        sa.Column('special_instructions', sa.Text(), nullable=True),
        sa.Column('metadata', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('expires_at', sa.DateTime(), nullable=True),
        sa.Column('payment_confirmed_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['cart_id'], ['eshop_carts.id'], ),
        sa.ForeignKeyConstraint(['order_id'], ['orders.id'], ),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for eshop_checkout_sessions
    op.create_index('ix_checkout_tenant_user', 'eshop_checkout_sessions', ['tenant_id', 'user_id'], unique=False)
    op.create_index('ix_checkout_status_expires', 'eshop_checkout_sessions', ['status', 'expires_at'], unique=False)
    op.create_index('ix_checkout_market_context', 'eshop_checkout_sessions', ['market_context'], unique=False)
    op.create_index('ix_checkout_payment_method', 'eshop_checkout_sessions', ['payment_method'], unique=False)
    op.create_index('ix_checkout_created', 'eshop_checkout_sessions', ['created_at'], unique=False)
    op.create_index(op.f('ix_eshop_checkout_sessions_tenant_id'), 'eshop_checkout_sessions', ['tenant_id'], unique=False)
    op.create_index(op.f('ix_eshop_checkout_sessions_cart_id'), 'eshop_checkout_sessions', ['cart_id'], unique=False)
    op.create_index(op.f('ix_eshop_checkout_sessions_user_id'), 'eshop_checkout_sessions', ['user_id'], unique=False)
    op.create_index(op.f('ix_eshop_checkout_sessions_order_id'), 'eshop_checkout_sessions', ['order_id'], unique=False)
    op.create_index(op.f('ix_eshop_checkout_sessions_status'), 'eshop_checkout_sessions', ['status'], unique=False)


def downgrade() -> None:
    """Downgrade schema."""
    
    # Drop indexes for eshop_checkout_sessions
    op.drop_index(op.f('ix_eshop_checkout_sessions_status'), table_name='eshop_checkout_sessions')
    op.drop_index(op.f('ix_eshop_checkout_sessions_order_id'), table_name='eshop_checkout_sessions')
    op.drop_index(op.f('ix_eshop_checkout_sessions_user_id'), table_name='eshop_checkout_sessions')
    op.drop_index(op.f('ix_eshop_checkout_sessions_cart_id'), table_name='eshop_checkout_sessions')
    op.drop_index(op.f('ix_eshop_checkout_sessions_tenant_id'), table_name='eshop_checkout_sessions')
    op.drop_index('ix_checkout_created', table_name='eshop_checkout_sessions')
    op.drop_index('ix_checkout_payment_method', table_name='eshop_checkout_sessions')
    op.drop_index('ix_checkout_market_context', table_name='eshop_checkout_sessions')
    op.drop_index('ix_checkout_status_expires', table_name='eshop_checkout_sessions')
    op.drop_index('ix_checkout_tenant_user', table_name='eshop_checkout_sessions')
    
    # Drop table
    op.drop_table('eshop_checkout_sessions')
    
    # Drop enum types
    payment_method_enum = postgresql.ENUM(name='paymentmethod')
    payment_method_enum.drop(op.get_bind())
    
    shipping_method_enum = postgresql.ENUM(name='shippingmethod')
    shipping_method_enum.drop(op.get_bind())
    
    checkout_status_enum = postgresql.ENUM(name='checkoutstatus')
    checkout_status_enum.drop(op.get_bind())
