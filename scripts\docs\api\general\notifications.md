# General - Notifications

**Categoria:** General
**<PERSON><PERSON><PERSON><PERSON>:** Notifications
**Total de Endpoints:** 10
**Gera<PERSON> em:** 27/06/2025, 20:20:27

## 📋 Endpoints

- [GET /api/modules/core/notifications/](#get-apimodulescorenotifications) - List Notifications
- [POST /api/modules/core/notifications/](#post-apimodulescorenotifications) - Create Notification
- [GET /api/modules/core/notifications/admin/all](#get-apimodulescorenotificationsadminall) - Admin List All Notifications
- [POST /api/modules/core/notifications/bulk-action](#post-apimodulescorenotificationsbulk-action) - Bulk Action
- [POST /api/modules/core/notifications/mark-read](#post-apimodulescorenotificationsmark-read) - Mark Notifications As Read
- [DELETE /api/modules/core/notifications/{notification_id}](#delete-apimodulescorenotificationsnotification-id) - Delete Notification
- [GET /api/modules/core/notifications/{notification_id}](#get-apimodulescorenotificationsnotification-id) - Get Notification
- [PUT /api/modules/core/notifications/{notification_id}](#put-apimodulescorenotificationsnotification-id) - Update Notification
- [POST /api/modules/core/notifications/{notification_id}/click](#post-apimodulescorenotificationsnotification-idclick) - Register Click
- [GET /api/modules/core/notifications/{notification_id}/preview](#get-apimodulescorenotificationsnotification-idpreview) - Get Delivery Preview

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### NotificationBulkAction

**Descrição:** Schema para ações em lote.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `notification_ids` | Array[string] | ✅ | - |
| `action` | string | ✅ | - |
| `delete_for_all` | boolean | ❌ | - |

### NotificationCreate

**Descrição:** Schema para criação de notificação.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `title` | string | ✅ | - |
| `content` | string | ✅ | - |
| `image_url` | unknown | ❌ | - |
| `action_url` | unknown | ❌ | - |
| `target_type` | NotificationTargetType | ✅ | - |
| `target_id` | unknown | ❌ | - |
| `tenant_id` | unknown | ❌ | - |
| `priority` | NotificationPriority | ❌ | - |
| `auto_expire_hours` | integer | ❌ | - |
| `max_lifetime_days` | integer | ❌ | - |

### NotificationListResponse

**Descrição:** Schema de resposta para lista de notificações.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `notifications` | Array[NotificationResponse] | ✅ | - |
| `total` | integer | ✅ | - |
| `page` | integer | ✅ | - |
| `per_page` | integer | ✅ | - |
| `has_next` | boolean | ✅ | - |
| `has_prev` | boolean | ✅ | - |

### NotificationMarkAsRead

**Descrição:** Schema para marcar notificação como lida.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `notification_ids` | Array[string] | ✅ | - |

### NotificationResponse

**Descrição:** Schema de resposta para notificação.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `title` | string | ✅ | - |
| `content` | string | ✅ | - |
| `image_url` | unknown | ❌ | - |
| `action_url` | unknown | ❌ | - |
| `target_type` | NotificationTargetType | ✅ | - |
| `target_id` | unknown | ❌ | - |
| `tenant_id` | unknown | ❌ | - |
| `priority` | NotificationPriority | ❌ | - |
| `auto_expire_hours` | integer | ❌ | - |
| `max_lifetime_days` | integer | ❌ | - |
| `id` | string | ✅ | - |
| `sender_id` | string | ✅ | - |
| `sender_type` | NotificationSenderType | ✅ | - |
| `status` | NotificationStatus | ✅ | - |
| `view_count` | integer | ✅ | - |
| `click_count` | integer | ✅ | - |
| `delivery_count` | integer | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | unknown | ✅ | - |
| `expires_at` | unknown | ✅ | - |
| `sent_at` | unknown | ✅ | - |
| `is_expired` | boolean | ✅ | - |
| `is_read` | unknown | ❌ | - |
| `is_deleted` | unknown | ❌ | - |

### NotificationUpdate

**Descrição:** Schema para atualização de notificação.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `title` | unknown | ❌ | - |
| `content` | unknown | ❌ | - |
| `image_url` | unknown | ❌ | - |
| `action_url` | unknown | ❌ | - |
| `priority` | unknown | ❌ | - |
| `auto_expire_hours` | unknown | ❌ | - |
| `status` | unknown | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/modules/core/notifications/ {#get-apimodulescorenotifications}

**Resumo:** List Notifications
**Descrição:** Lista notificações do usuário com filtros e paginação.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `page` | integer | query | ❌ | - |
| `per_page` | integer | query | ❌ | - |
| `status` | string | query | ❌ | - |
| `priority` | string | query | ❌ | - |
| `is_read` | string | query | ❌ | - |
| `search` | string | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [NotificationListResponse](#notificationlistresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/notifications/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/notifications/ {#post-apimodulescorenotifications}

**Resumo:** Create Notification
**Descrição:** Cria uma nova notificação.

Requer autenticação. Admins podem criar qualquer tipo de notificação.
Tenant owners podem criar notificações para seus tenants.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [NotificationCreate](#notificationcreate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [NotificationResponse](#notificationresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/notifications/admin/all {#get-apimodulescorenotificationsadminall}

**Resumo:** Admin List All Notifications
**Descrição:** Lista todas as notificações do sistema (apenas admin).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `page` | integer | query | ❌ | - |
| `per_page` | integer | query | ❌ | - |
| `status` | string | query | ❌ | - |
| `sender_type` | string | query | ❌ | - |
| `target_type` | string | query | ❌ | - |
| `tenant_id` | string | query | ❌ | - |
| `search` | string | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [NotificationListResponse](#notificationlistresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/notifications/admin/all" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/notifications/bulk-action {#post-apimodulescorenotificationsbulk-action}

**Resumo:** Bulk Action
**Descrição:** Executa ação em lote nas notificações.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [NotificationBulkAction](#notificationbulkaction)

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/bulk-action" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/core/notifications/mark-read {#post-apimodulescorenotificationsmark-read}

**Resumo:** Mark Notifications As Read
**Descrição:** Marca notificações como lidas.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [NotificationMarkAsRead](#notificationmarkasread)

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/mark-read" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/core/notifications/{notification_id} {#delete-apimodulescorenotificationsnotification-id}

**Resumo:** Delete Notification
**Descrição:** Deleta uma notificação.

Se delete_for_all=True, deleta para todos os usuários (apenas admin/owner).
Caso contrário, deleta apenas para o usuário atual.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `notification_id` | string | path | ✅ | - |
| `delete_for_all` | boolean | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/core/notifications/{notification_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/notifications/{notification_id} {#get-apimodulescorenotificationsnotification-id}

**Resumo:** Get Notification
**Descrição:** Obtém uma notificação específica.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `notification_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [NotificationResponse](#notificationresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/notifications/{notification_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/core/notifications/{notification_id} {#put-apimodulescorenotificationsnotification-id}

**Resumo:** Update Notification
**Descrição:** Atualiza uma notificação.

Apenas o remetente ou admin podem editar.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `notification_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [NotificationUpdate](#notificationupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [NotificationResponse](#notificationresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/core/notifications/{notification_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/core/notifications/{notification_id}/click {#post-apimodulescorenotificationsnotification-idclick}

**Resumo:** Register Click
**Descrição:** Registra um clique na notificação.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `notification_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/{notification_id}/click" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/notifications/{notification_id}/preview {#get-apimodulescorenotificationsnotification-idpreview}

**Resumo:** Get Delivery Preview
**Descrição:** Obtém preview da entrega da notificação.

Mostra quantos usuários receberão a notificação.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `notification_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/notifications/{notification_id}/preview" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
