"""Service for managing cash register sessions."""

import uuid  # noqa: E402
from typing import List, Optional
from datetime import datetime
from decimal import Decimal
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func

from app.modules.core.functions.pos.models.cash_register_session import CashRegisterSession  # noqa: E402
from app.modules.core.functions.pos.models.cash_register import CashRegister
from app.modules.core.functions.pos.models.sale_transaction import SaleTransaction
from app.modules.core.functions.pos.schemas.cash_register_session import (
    CashRegisterSessionOpen,
    CashRegisterSessionClose,
)
from app.core.exceptions import BusinessLogicError  # noqa: E402


class CashRegisterSessionService:
    """Service for managing cash register sessions."""

    async def get(
        self, db: AsyncSession, id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Optional[CashRegisterSession]:
        """
        Get a cash register session by ID, ensuring it belongs to the specified tenant.
        """
        result = await db.execute(
            select(CashRegisterSession).where(
                CashRegisterSession.id == id, CashRegisterSession.tenant_id == tenant_id
            )
        )
        return result.scalars().first()

    async def get_active_session(
        self, db: AsyncSession, cash_register_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Optional[CashRegisterSession]:
        """
        Get the active session for a cash register, if any.
        """
        result = await db.execute(
            select(CashRegisterSession).where(
                CashRegisterSession.cash_register_id == cash_register_id,
                CashRegisterSession.tenant_id == tenant_id,
                CashRegisterSession.is_open,
            )
        )
        return result.scalars().first()

    async def get_multi(
        self,
        db: AsyncSession,
        *,
        tenant_id: uuid.UUID,
        cash_register_id: Optional[uuid.UUID] = None,
        operator_id: Optional[uuid.UUID] = None,
        is_open: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[CashRegisterSession]:
        """
        Get multiple cash register sessions with optional filtering.
        """
        query = select(CashRegisterSession).where(CashRegisterSession.tenant_id == tenant_id)

        if cash_register_id:
            query = query.where(CashRegisterSession.cash_register_id == cash_register_id)

        if operator_id:
            query = query.where(CashRegisterSession.operator_id == operator_id)

        if is_open is not None:
            query = query.where(CashRegisterSession.is_open == is_open)

        query = (
            query.order_by(CashRegisterSession.opening_timestamp.desc()).offset(skip).limit(limit)
        )

        result = await db.execute(query)
        return result.scalars().all()

    async def open_session(
        self,
        db: AsyncSession,
        *,
        cash_register_id: uuid.UUID,
        operator_id: uuid.UUID,
        tenant_id: uuid.UUID,
        session_data: CashRegisterSessionOpen,
    ) -> CashRegisterSession:
        """
        Open a new cash register session.

        Validates:
        - The cash register exists and belongs to the tenant
        - The cash register is active
        - There is no active session for this cash register
        """
        # Check if cash register exists and belongs to tenant
        result = await db.execute(
            select(CashRegister).where(
                CashRegister.id == cash_register_id, CashRegister.tenant_id == tenant_id
            )
        )
        cash_register = result.scalars().first()

        if not cash_register:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cash register with id {cash_register_id} not found or does not belong to the tenant.",  # noqa: E501
            )

        if not cash_register.is_active:
            raise BusinessLogicError(f"Cash register with id {cash_register_id} is not active.")

        # Check if there's already an active session
        active_session = await self.get_active_session(db, cash_register_id, tenant_id)
        if active_session:
            raise BusinessLogicError(
                f"Cash register with id {cash_register_id} already has an active session."
            )

        # Create new session
        db_session = CashRegisterSession(
            tenant_id=tenant_id,
            cash_register_id=cash_register_id,
            operator_id=operator_id,
            is_open=True,
            opening_timestamp=datetime.utcnow(),
            opening_balance=session_data.opening_balance,
            opening_notes=session_data.opening_notes,
        )

        db.add(db_session)
        await db.commit()
        await db.refresh(db_session)

        return db_session

    async def close_session(
        self,
        db: AsyncSession,
        *,
        session_id: uuid.UUID,
        tenant_id: uuid.UUID,
        session_data: CashRegisterSessionClose,
    ) -> CashRegisterSession:
        """
        Close an existing cash register session.

        Validates:
        - The session exists and belongs to the tenant
        - The session is currently open
        - Calculates expected cash amount based on transactions
        - Calculates discrepancy between expected and actual cash
        """
        # Get the session
        session = await self.get(db, session_id, tenant_id)

        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cash register session with id {session_id} not found or does not belong to the tenant.",  # noqa: E501
            )

        if not session.is_open:
            raise BusinessLogicError(
                f"Cash register session with id {session_id} is already closed."
            )

        # Calculate expected cash amount from transactions
        result = await db.execute(
            select(func.sum(SaleTransaction.total_amount)).where(
                SaleTransaction.cash_register_id == session.cash_register_id,
                SaleTransaction.tenant_id == tenant_id,
                # Add condition to filter transactions within this session's timeframe
                SaleTransaction.created_at >= session.opening_timestamp,
                # Only include cash transactions
                SaleTransaction.payment_method == "cash",
            )
        )

        total_cash_sales = result.scalar_one_or_none() or Decimal("0.00")
        expected_cash = session.opening_balance + total_cash_sales

        # Calculate discrepancy
        discrepancy = session_data.actual_cash_amount - expected_cash

        # Update session
        session.is_open = False
        session.closing_timestamp = datetime.utcnow()
        session.closing_balance = session_data.closing_balance
        session.closing_notes = session_data.closing_notes
        session.expected_cash_amount = expected_cash
        session.actual_cash_amount = session_data.actual_cash_amount
        session.discrepancy_amount = discrepancy
        session.discrepancy_reason = session_data.discrepancy_reason

        db.add(session)
        await db.commit()
        await db.refresh(session)

        return session


# Instance of the service to be used in endpoints
cash_register_session_service = CashRegisterSessionService()
