"""add_b2b_authorization_system_with_strings

Revision ID: 730e15e8ec37
Revises: cdf903a1f729
Create Date: 2025-06-27 13:52:43.993827

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '730e15e8ec37'
down_revision: Union[str, None] = 'cdf903a1f729'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('b2b_authorization_requests',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('entity_type', sa.String(length=50), nullable=False),
    sa.Column('entity_id', sa.UUID(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('approval_type', sa.String(length=50), nullable=False),
    sa.Column('company_name', sa.String(length=200), nullable=False),
    sa.Column('tax_id', sa.String(length=50), nullable=False),
    sa.Column('business_type', sa.String(length=100), nullable=True),
    sa.Column('contact_person', sa.String(length=200), nullable=False),
    sa.Column('contact_email', sa.String(length=255), nullable=False),
    sa.Column('contact_phone', sa.String(length=50), nullable=True),
    sa.Column('business_address', sa.JSON(), nullable=True),
    sa.Column('billing_address', sa.JSON(), nullable=True),
    sa.Column('requested_credit_limit', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('requested_payment_terms', sa.String(length=50), nullable=True),
    sa.Column('requested_commission_rate', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('supplier_type', sa.String(length=50), nullable=True),
    sa.Column('approved_by', sa.UUID(), nullable=True),
    sa.Column('approved_at', sa.DateTime(), nullable=True),
    sa.Column('approved_credit_limit', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('approved_payment_terms', sa.String(length=50), nullable=True),
    sa.Column('approved_commission_rate', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('rejection_reason', sa.Text(), nullable=True),
    sa.Column('rejection_notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('expires_at', sa.DateTime(), nullable=True),
    sa.Column('additional_info', sa.JSON(), nullable=True),
    sa.Column('internal_notes', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['approved_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_b2b_auth_company', 'b2b_authorization_requests', ['company_name'], unique=False)
    op.create_index('ix_b2b_auth_created', 'b2b_authorization_requests', ['created_at'], unique=False)
    op.create_index('ix_b2b_auth_entity', 'b2b_authorization_requests', ['entity_type', 'entity_id'], unique=False)
    op.create_index('ix_b2b_auth_expires', 'b2b_authorization_requests', ['expires_at'], unique=False)
    op.create_index('ix_b2b_auth_tenant_status', 'b2b_authorization_requests', ['tenant_id', 'status'], unique=False)
    op.create_index('ix_b2b_auth_user_status', 'b2b_authorization_requests', ['user_id', 'status'], unique=False)
    op.create_index(op.f('ix_b2b_authorization_requests_company_name'), 'b2b_authorization_requests', ['company_name'], unique=False)
    op.create_index(op.f('ix_b2b_authorization_requests_entity_id'), 'b2b_authorization_requests', ['entity_id'], unique=False)
    op.create_index(op.f('ix_b2b_authorization_requests_entity_type'), 'b2b_authorization_requests', ['entity_type'], unique=False)
    op.create_index(op.f('ix_b2b_authorization_requests_status'), 'b2b_authorization_requests', ['status'], unique=False)
    op.create_index(op.f('ix_b2b_authorization_requests_tax_id'), 'b2b_authorization_requests', ['tax_id'], unique=False)
    op.create_index(op.f('ix_b2b_authorization_requests_tenant_id'), 'b2b_authorization_requests', ['tenant_id'], unique=False)
    op.create_index(op.f('ix_b2b_authorization_requests_user_id'), 'b2b_authorization_requests', ['user_id'], unique=False)
    op.create_table('b2b_authorization_settings',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('auto_approve_tcostumer', sa.Boolean(), nullable=False),
    sa.Column('auto_approve_tvendor', sa.Boolean(), nullable=False),
    sa.Column('max_auto_credit_limit', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('max_auto_commission_rate', sa.Numeric(precision=5, scale=2), nullable=False),
    sa.Column('request_expiry_days', sa.Integer(), nullable=False),
    sa.Column('document_retention_days', sa.Integer(), nullable=False),
    sa.Column('notify_on_new_request', sa.Boolean(), nullable=False),
    sa.Column('notify_on_approval', sa.Boolean(), nullable=False),
    sa.Column('notify_on_rejection', sa.Boolean(), nullable=False),
    sa.Column('notification_emails', sa.JSON(), nullable=True),
    sa.Column('required_documents_tcostumer', sa.JSON(), nullable=True),
    sa.Column('required_documents_tvendor', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_b2b_auth_settings_tenant', 'b2b_authorization_settings', ['tenant_id'], unique=False)
    op.create_index(op.f('ix_b2b_authorization_settings_tenant_id'), 'b2b_authorization_settings', ['tenant_id'], unique=True)
    op.create_table('b2b_authorization_documents',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('authorization_request_id', sa.UUID(), nullable=False),
    sa.Column('document_type', sa.String(length=50), nullable=False),
    sa.Column('document_name', sa.String(length=255), nullable=False),
    sa.Column('original_filename', sa.String(length=255), nullable=False),
    sa.Column('file_path', sa.String(length=500), nullable=False),
    sa.Column('file_size', sa.Integer(), nullable=False),
    sa.Column('file_type', sa.String(length=100), nullable=False),
    sa.Column('file_hash', sa.String(length=64), nullable=False),
    sa.Column('is_verified', sa.Boolean(), nullable=False),
    sa.Column('verified_by', sa.UUID(), nullable=True),
    sa.Column('verified_at', sa.DateTime(), nullable=True),
    sa.Column('verification_notes', sa.Text(), nullable=True),
    sa.Column('uploaded_at', sa.DateTime(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['authorization_request_id'], ['b2b_authorization_requests.id'], ),
    sa.ForeignKeyConstraint(['verified_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_b2b_auth_docs_request', 'b2b_authorization_documents', ['authorization_request_id'], unique=False)
    op.create_index('ix_b2b_auth_docs_type', 'b2b_authorization_documents', ['document_type'], unique=False)
    op.create_index('ix_b2b_auth_docs_uploaded', 'b2b_authorization_documents', ['uploaded_at'], unique=False)
    op.create_index('ix_b2b_auth_docs_verified', 'b2b_authorization_documents', ['is_verified'], unique=False)
    op.create_index(op.f('ix_b2b_authorization_documents_authorization_request_id'), 'b2b_authorization_documents', ['authorization_request_id'], unique=False)
    op.create_index(op.f('ix_b2b_authorization_documents_document_type'), 'b2b_authorization_documents', ['document_type'], unique=False)
    op.create_table('b2b_authorization_history',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('authorization_request_id', sa.UUID(), nullable=False),
    sa.Column('action', sa.String(length=100), nullable=False),
    sa.Column('previous_status', sa.String(length=50), nullable=True),
    sa.Column('new_status', sa.String(length=50), nullable=True),
    sa.Column('performed_by', sa.UUID(), nullable=False),
    sa.Column('performed_at', sa.DateTime(), nullable=False),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('changes', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['authorization_request_id'], ['b2b_authorization_requests.id'], ),
    sa.ForeignKeyConstraint(['performed_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_b2b_auth_history_action', 'b2b_authorization_history', ['action'], unique=False)
    op.create_index('ix_b2b_auth_history_performed', 'b2b_authorization_history', ['performed_at'], unique=False)
    op.create_index('ix_b2b_auth_history_request', 'b2b_authorization_history', ['authorization_request_id'], unique=False)
    op.create_index('ix_b2b_auth_history_user', 'b2b_authorization_history', ['performed_by'], unique=False)
    op.create_index(op.f('ix_b2b_authorization_history_action'), 'b2b_authorization_history', ['action'], unique=False)
    op.create_index(op.f('ix_b2b_authorization_history_authorization_request_id'), 'b2b_authorization_history', ['authorization_request_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_b2b_authorization_history_authorization_request_id'), table_name='b2b_authorization_history')
    op.drop_index(op.f('ix_b2b_authorization_history_action'), table_name='b2b_authorization_history')
    op.drop_index('ix_b2b_auth_history_user', table_name='b2b_authorization_history')
    op.drop_index('ix_b2b_auth_history_request', table_name='b2b_authorization_history')
    op.drop_index('ix_b2b_auth_history_performed', table_name='b2b_authorization_history')
    op.drop_index('ix_b2b_auth_history_action', table_name='b2b_authorization_history')
    op.drop_table('b2b_authorization_history')
    op.drop_index(op.f('ix_b2b_authorization_documents_document_type'), table_name='b2b_authorization_documents')
    op.drop_index(op.f('ix_b2b_authorization_documents_authorization_request_id'), table_name='b2b_authorization_documents')
    op.drop_index('ix_b2b_auth_docs_verified', table_name='b2b_authorization_documents')
    op.drop_index('ix_b2b_auth_docs_uploaded', table_name='b2b_authorization_documents')
    op.drop_index('ix_b2b_auth_docs_type', table_name='b2b_authorization_documents')
    op.drop_index('ix_b2b_auth_docs_request', table_name='b2b_authorization_documents')
    op.drop_table('b2b_authorization_documents')
    op.drop_index(op.f('ix_b2b_authorization_settings_tenant_id'), table_name='b2b_authorization_settings')
    op.drop_index('ix_b2b_auth_settings_tenant', table_name='b2b_authorization_settings')
    op.drop_table('b2b_authorization_settings')
    op.drop_index(op.f('ix_b2b_authorization_requests_user_id'), table_name='b2b_authorization_requests')
    op.drop_index(op.f('ix_b2b_authorization_requests_tenant_id'), table_name='b2b_authorization_requests')
    op.drop_index(op.f('ix_b2b_authorization_requests_tax_id'), table_name='b2b_authorization_requests')
    op.drop_index(op.f('ix_b2b_authorization_requests_status'), table_name='b2b_authorization_requests')
    op.drop_index(op.f('ix_b2b_authorization_requests_entity_type'), table_name='b2b_authorization_requests')
    op.drop_index(op.f('ix_b2b_authorization_requests_entity_id'), table_name='b2b_authorization_requests')
    op.drop_index(op.f('ix_b2b_authorization_requests_company_name'), table_name='b2b_authorization_requests')
    op.drop_index('ix_b2b_auth_user_status', table_name='b2b_authorization_requests')
    op.drop_index('ix_b2b_auth_tenant_status', table_name='b2b_authorization_requests')
    op.drop_index('ix_b2b_auth_expires', table_name='b2b_authorization_requests')
    op.drop_index('ix_b2b_auth_entity', table_name='b2b_authorization_requests')
    op.drop_index('ix_b2b_auth_created', table_name='b2b_authorization_requests')
    op.drop_index('ix_b2b_auth_company', table_name='b2b_authorization_requests')
    op.drop_table('b2b_authorization_requests')
    # ### end Alembic commands ###
