import uuid
from typing import Optional, List
from pydantic import BaseModel, Field, ConfigDict
from decimal import Decimal
import datetime

# === Inventory Category Schemas ===

class InventoryCategoryBase(BaseModel):
    """Base schema para categoria de inventário."""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    display_order: int = Field(1, ge=1)
    is_active: bool = True

    model_config = ConfigDict(from_attributes=True)


class InventoryCategoryCreate(InventoryCategoryBase):
    """Schema para criação de categoria de inventário."""
    pass


class InventoryCategoryUpdate(BaseModel):
    """Schema para atualização de categoria de inventário."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    display_order: Optional[int] = Field(None, ge=1)
    is_active: Optional[bool] = None

    model_config = ConfigDict(from_attributes=True)


class InventoryCategoryRead(InventoryCategoryBase):
    """Schema para leitura de categoria de inventário."""
    id: uuid.UUID
    tenant_id: uuid.UUID
    created_at: datetime.datetime
    updated_at: datetime.datetime


# --- Inventory Item Schemas ---


class InventoryItemBase(BaseModel):
    """Schema base com campos comuns para InventoryItem."""

    name: str = Field(..., description="Nome do item de inventário.")
    description: Optional[str] = Field(None, description="Descrição detalhada do item.")
    sku: Optional[str] = Field(None, description="SKU (Stock Keeping Unit) do item.")
    category_id: Optional[uuid.UUID] = Field(None, description="ID da categoria do item.")
    quantity: int = Field(
        0, description="Quantidade atual em estoque.", ge=0
    )  # Garante não negativo
    unit_cost: Optional[Decimal] = Field(
        None, description="Custo unitário do item.", ge=0
    )  # Garante não negativo

    model_config = ConfigDict(from_attributes=True)


class InventoryItemCreate(InventoryItemBase):
    """Schema para criar um novo item de inventário. tenant_id será adicionado pelo serviço."""

    # tenant_id não está aqui, será injetado pelo serviço/endpoint


class InventoryItemUpdate(BaseModel):
    """Schema para atualizar um item de inventário. Todos os campos são opcionais."""

    name: Optional[str] = Field(None, description="Novo nome do item.")
    description: Optional[str] = Field(None, description="Nova descrição do item.")
    sku: Optional[str] = Field(None, description="Novo SKU do item.")
    category_id: Optional[uuid.UUID] = Field(None, description="Novo ID da categoria.")
    # quantity é atualizada via endpoint específico de ajuste de estoque
    unit_cost: Optional[Decimal] = Field(None, description="Novo custo unitário.", ge=0)

    model_config = ConfigDict(from_attributes=True)


class InventoryItemInDBBase(InventoryItemBase):
    """Schema base para itens como estão no banco de dados, incluindo IDs."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    created_at: datetime.datetime
    updated_at: datetime.datetime

    model_config = ConfigDict(from_attributes=True)


class InventoryItemRead(InventoryItemInDBBase):
    """Schema para retornar dados de um item de inventário ao cliente."""


# Schema específico para o endpoint de ajuste de estoque


class InventoryItemAdjustStock(BaseModel):
    change: int = Field(
        ..., description="A mudança na quantidade (+ para adicionar, - para remover)."
    )
