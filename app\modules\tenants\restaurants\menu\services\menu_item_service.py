import logging
from typing import Optional, Sequence, List
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.exc import IntegrityError
from fastapi import HTTPException, status
import uuid

# Import models
from app.modules.tenants.restaurants.menu.models.menu_category import MenuCategory
from app.modules.tenants.restaurants.menu.models.menu_item import MenuItem

# Import schemas
from app.modules.tenants.restaurants.menu.schemas.menu_item import (
    MenuItemCreate,
    MenuItemUpdate,
)

# Import WebSocket utility
from app.websockets.manager import emit_to_tenant

# Import services
from app.modules.tenants.restaurants.menu.services.menu_category_service import MenuCategoryService
from app.modules.tenants.restaurants.menu.services.helpers.menu_item_creator import MenuItemCreator
from app.modules.tenants.restaurants.menu.services.helpers.menu_item_updater import MenuItemUpdater
from app.modules.tenants.restaurants.menu.services.helpers.menu_item_groups_creator import MenuItemGroupsCreator
from app.modules.tenants.restaurants.menu.services.helpers.menu_item_groups_updater import MenuItemGroupsUpdater
from app.modules.tenants.restaurants.menu.services.helpers.menu_item_image_loader import MenuItemImageLoader
from app.modules.tenants.restaurants.menu.services.helpers.menu_item_allergen_manager import MenuItemAllergenManager

logger = logging.getLogger(__name__)


class MenuItemService:
    def __init__(self, db_session: AsyncSession):
        self.db = db_session
        self.category_service = MenuCategoryService(db_session)
        self.creator = MenuItemCreator(db_session)
        self.updater = MenuItemUpdater(db_session)
        self.groups_creator = MenuItemGroupsCreator(db_session)
        self.groups_updater = MenuItemGroupsUpdater(db_session)
        self.image_loader = MenuItemImageLoader(db_session)
        self.allergen_manager = MenuItemAllergenManager(db_session)

    async def create_item(self, item_in: MenuItemCreate, tenant_id: uuid.UUID) -> MenuItem:
        """Creates a new menu item, potentially with nested groups/options."""
        logger.info(f"🔍 Creating menu item: {item_in.name} for tenant {tenant_id}")
        
        # Validate category exists for the tenant
        category = await self.category_service.get_category(item_in.category_id, tenant_id)
        if not category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Menu category with id {item_in.category_id} not found for this tenant.",
            )

        return await self.creator.create_item(item_in, tenant_id)

    async def get_item(
        self, item_id: uuid.UUID, tenant_id: uuid.UUID, include_details: bool = True
    ) -> Optional[MenuItem]:
        """Gets a specific menu item by ID for the given tenant, optionally including full details."""
        query = select(MenuItem).where(MenuItem.id == item_id, MenuItem.tenant_id == tenant_id)

        # Configure loading options based on include_details
        options = self._get_loading_options(include_details)
        query = query.options(*options)
        
        result = await self.db.execute(query)
        item = result.scalars().first()

        if not item:
            logger.warning(f"Item {item_id} not found for tenant {tenant_id}")
            return None

        # Force loading of relationships to avoid MissingGreenlet errors
        self._force_load_relationships(item, include_details)

        # Load images for this item
        await self.image_loader.load_all_images([item])

        return item

    async def get_items(
        self,
        tenant_id: uuid.UUID,
        digital_menu_id: Optional[uuid.UUID] = None,
        category_id: Optional[uuid.UUID] = None,
        skip: int = 0,
        limit: int = 100,
        include_details: bool = False,
    ) -> Sequence[MenuItem]:
        """Gets a list of menu items for the tenant, optionally filtered by digital menu and/or category."""
        query = select(MenuItem).where(MenuItem.tenant_id == tenant_id)

        # Apply filters
        query = self._apply_filters(query, digital_menu_id, category_id)
        query = query.order_by(MenuItem.display_order, MenuItem.name).offset(skip).limit(limit)

        # Configure loading options
        options = self._get_loading_options(include_details)
        query = query.options(*options)
        
        result = await self.db.execute(query)
        items = result.scalars().all()

        # Force loading of relationships for all items
        for item in items:
            self._force_load_relationships(item, include_details)

        # Load images for all items
        await self.image_loader.load_all_images(items)

        return items

    async def update_item(
        self, item_id: uuid.UUID, item_in: MenuItemUpdate, tenant_id: uuid.UUID
    ) -> Optional[MenuItem]:
        """Updates an existing menu item."""
        return await self.updater.update_item(item_id, item_in, tenant_id)

    async def delete_item(self, item_id: uuid.UUID, tenant_id: uuid.UUID) -> bool:
        """Permanently deletes a menu item from the database (hard delete)."""
        logger.info(f"🗑️ Starting hard delete for item {item_id} for tenant {tenant_id}")

        db_item = await self.get_item(item_id, tenant_id, include_details=False)
        if not db_item:
            logger.warning(f"Attempted to delete non-existent item {item_id} for tenant {tenant_id}")
            return False

        try:
            # Hard delete - permanently remove from database
            await self.db.delete(db_item)
            await self.db.commit()
            logger.info(f"Menu item {item_id} permanently deleted for tenant {tenant_id}")

            # Emit WebSocket event after successful deletion
            await self._emit_deletion_event(item_id, db_item.name, tenant_id)
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Error deleting item {item_id} for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    def _get_loading_options(self, include_details: bool) -> List:
        """Get SQLAlchemy loading options based on detail level."""
        base_options = [
            joinedload(MenuItem.category),
            selectinload(MenuItem.variant_groups),
            selectinload(MenuItem.modifier_groups),
            selectinload(MenuItem.optional_groups),
            selectinload(MenuItem.allergens),
        ]

        if include_details:
            from app.modules.core.functions.customizations.models.variant_group import VariantGroup
            from app.modules.core.functions.customizations.models.modifier_group import ModifierGroup
            from app.modules.core.functions.customizations.models.optional_group import OptionalGroup
            
            return [
                joinedload(MenuItem.category),
                selectinload(MenuItem.variant_groups).selectinload(VariantGroup.options),
                selectinload(MenuItem.modifier_groups).selectinload(ModifierGroup.options),
                selectinload(MenuItem.optional_groups).selectinload(OptionalGroup.options),
                selectinload(MenuItem.allergens),
            ]

        return base_options

    def _apply_filters(self, query, digital_menu_id: Optional[uuid.UUID], category_id: Optional[uuid.UUID]):
        """Apply filters to the query."""
        # Filter by digital menu if specified
        if digital_menu_id is not None:
            query = query.join(MenuCategory).where(MenuCategory.digital_menu_id == digital_menu_id)

        # Filter by category if specified
        if category_id is not None:
            query = query.where(MenuItem.category_id == category_id)

        return query

    def _force_load_relationships(self, item: MenuItem, include_details: bool):
        """Force loading of relationships to avoid MissingGreenlet errors."""
        if hasattr(item, "category") and item.category:
            _ = item.category.name
        _ = item.variant_groups
        _ = item.modifier_groups
        _ = item.optional_groups

        # Force loading allergens
        if hasattr(item, "allergens"):
            allergens_list = list(item.allergens)
            logger.debug(f"Loaded {len(allergens_list)} allergens for item {item.id}")

        # Force loading of options if include_details is True
        if include_details:
            for vg in item.variant_groups:
                _ = vg.options
            for mg in item.modifier_groups:
                _ = mg.options
            for og in item.optional_groups:
                _ = og.options

    async def _emit_deletion_event(self, item_id: uuid.UUID, item_name: str, tenant_id: uuid.UUID):
        """Emit WebSocket event for item deletion."""
        try:
            payload = {
                "item_id": item_id,
                "deleted": True,
                "name": item_name,
            }
            await emit_to_tenant(tenant_id, "menu_item_deleted", payload)
            logger.info(f"WebSocket event 'menu_item_deleted' emitted for item {item_id}, tenant {tenant_id}")
        except Exception as ws_error:
            logger.error(f"Failed to emit WebSocket event for item {item_id} deletion: {ws_error}")
