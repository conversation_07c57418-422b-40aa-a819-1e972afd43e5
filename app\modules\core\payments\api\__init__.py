"""API endpoints for core payments module."""

from fastapi import APIRouter  # noqa: E402

from .payment_processor_api import router as processor_router  # noqa: E402
from .payment_method_api import router as method_router
from .payment_transaction_api import router as transaction_router
from .payment_processor_integration_api import router as integration_router

router = APIRouter()

router.include_router(processor_router, tags=["payment_processors"])
router.include_router(method_router, tags=["payment_methods"])
router.include_router(transaction_router, tags=["payment_transactions"])
router.include_router(integration_router, tags=["payment_processor_integration"])

__all__ = ["router"]
