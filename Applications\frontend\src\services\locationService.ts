/**
 * Global Location service using country-state-city library + APIs
 * Uses local data for countries/states/cities and APIs for address search
 */

// Import with enhanced error handling and debugging
let Country: any, State: any, City: any;
let libraryLoaded = false;

try {
  console.log('🔄 Attempting to import country-state-city library...');
  const csc = require('country-state-city');

  if (!csc || !csc.Country || !csc.State || !csc.City) {
    throw new Error('Library imported but missing required components');
  }

  Country = csc.Country;
  State = csc.State;
  City = csc.City;
  libraryLoaded = true;

  console.log('✅ Successfully imported country-state-city library');
  console.log('📊 Library info:', {
    hasCountry: !!Country,
    hasState: !!State,
    hasCity: !!City,
    countryMethods: Object.getOwnPropertyNames(Country),
    stateMethods: Object.getOwnPropertyNames(State),
    cityMethods: Object.getOwnPropertyNames(City)
  });

} catch (error) {
  console.error('❌ Failed to import country-state-city library:', error);
  libraryLoaded = false;

  // Create mock objects to prevent errors
  Country = {
    getAllCountries: () => {
      console.warn('🚨 Using mock Country.getAllCountries - library not loaded');
      return [];
    }
  };
  State = {
    getStatesOfCountry: () => {
      console.warn('🚨 Using mock State.getStatesOfCountry - library not loaded');
      return [];
    }
  };
  City = {
    getCitiesOfState: () => {
      console.warn('🚨 Using mock City.getCitiesOfState - library not loaded');
      return [];
    }
  };
}

export interface LocationData {
  country: string;
  state: string;
  city: string;
  zipCode?: string;
}

export interface CountryOption {
  code: string;
  name: string;
  flag?: string;
}

export interface StateOption {
  code: string;
  name: string;
  country: string;
}

export interface CityOption {
  name: string;
  state: string;
  country: string;
  zipCodes?: string[];
}

export interface AddressSuggestion {
  display_name: string;
  street: string;
  neighborhood?: string;
  city: string;
  state: string;
  zipCode?: string;
  latitude: number;
  longitude: number;
}

// Simple and robust API client without AbortController
class LocationAPIClient {
  private cache = new Map<string, any>();
  private readonly cacheTimeout = 5 * 60 * 1000; // 5 minutes
  private isApiAvailable = true;

  async get(url: string): Promise<any> {
    // Check cache first
    const cached = this.cache.get(url);
    if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
      console.log('Returning cached data for:', url);
      return cached.data;
    }

    // If API is known to be unavailable, throw immediately
    if (!this.isApiAvailable) {
      throw new Error('API is not available');
    }

    try {
      console.log('Fetching from API:', url);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      // Cache the result
      this.cache.set(url, {
        data,
        timestamp: Date.now()
      });

      console.log('Successfully fetched from API');
      return data;
    } catch (error) {
      console.error('API error:', error);
      this.isApiAvailable = false;
      throw error;
    }
  }

  // Method to check if API is available
  isAvailable(): boolean {
    return this.isApiAvailable;
  }

  // Method to reset API availability (for retry)
  resetAvailability(): void {
    this.isApiAvailable = true;
  }
}

const apiClient = new LocationAPIClient();

/**
 * Fetch with timeout and proper headers for Nominatim
 */
async function fetchWithTimeout(url: string, timeout = 10000): Promise<Response> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(url, {
      signal: controller.signal,
      headers: {
        'User-Agent': 'Trix Restaurant Management System (<EMAIL>)',
        'Accept': 'application/json',
        'Accept-Language': 'en-US,en;q=0.9'
      }
    });
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
}

/**
 * Comprehensive fallback data for countries
 */
const FALLBACK_COUNTRIES: CountryOption[] = [
  { code: 'US', name: 'United States', flag: '🇺🇸' },
  { code: 'BR', name: 'Brazil', flag: '🇧🇷' },
  { code: 'CA', name: 'Canada', flag: '🇨🇦' },
  { code: 'GB', name: 'United Kingdom', flag: '🇬🇧' },
  { code: 'DE', name: 'Germany', flag: '🇩🇪' },
  { code: 'FR', name: 'France', flag: '🇫🇷' },
  { code: 'IT', name: 'Italy', flag: '🇮🇹' },
  { code: 'ES', name: 'Spain', flag: '🇪🇸' },
  { code: 'PT', name: 'Portugal', flag: '🇵🇹' },
  { code: 'MX', name: 'Mexico', flag: '🇲🇽' },
  { code: 'AR', name: 'Argentina', flag: '🇦🇷' },
  { code: 'CL', name: 'Chile', flag: '🇨🇱' },
  { code: 'CO', name: 'Colombia', flag: '🇨🇴' },
  { code: 'PE', name: 'Peru', flag: '🇵🇪' },
  { code: 'VE', name: 'Venezuela', flag: '🇻🇪' },
  { code: 'EC', name: 'Ecuador', flag: '🇪🇨' },
  { code: 'BO', name: 'Bolivia', flag: '🇧🇴' },
  { code: 'PY', name: 'Paraguay', flag: '🇵🇾' },
  { code: 'UY', name: 'Uruguay', flag: '🇺🇾' },
  { code: 'AU', name: 'Australia', flag: '🇦🇺' },
  { code: 'NZ', name: 'New Zealand', flag: '🇳🇿' },
  { code: 'JP', name: 'Japan', flag: '🇯🇵' },
  { code: 'CN', name: 'China', flag: '🇨🇳' },
  { code: 'IN', name: 'India', flag: '🇮🇳' },
  { code: 'KR', name: 'South Korea', flag: '🇰🇷' },
  { code: 'TH', name: 'Thailand', flag: '🇹🇭' },
  { code: 'SG', name: 'Singapore', flag: '🇸🇬' },
  { code: 'MY', name: 'Malaysia', flag: '🇲🇾' },
  { code: 'ID', name: 'Indonesia', flag: '🇮🇩' },
  { code: 'PH', name: 'Philippines', flag: '🇵🇭' },
  { code: 'VN', name: 'Vietnam', flag: '🇻🇳' },
  { code: 'RU', name: 'Russia', flag: '🇷🇺' },
  { code: 'ZA', name: 'South Africa', flag: '🇿🇦' },
  { code: 'EG', name: 'Egypt', flag: '🇪🇬' },
  { code: 'NG', name: 'Nigeria', flag: '🇳🇬' },
  { code: 'KE', name: 'Kenya', flag: '🇰🇪' },
  { code: 'MA', name: 'Morocco', flag: '🇲🇦' },
  { code: 'TN', name: 'Tunisia', flag: '🇹🇳' },
  { code: 'DZ', name: 'Algeria', flag: '🇩🇿' },
  { code: 'GH', name: 'Ghana', flag: '🇬🇭' },
  { code: 'ET', name: 'Ethiopia', flag: '🇪🇹' },
  { code: 'TZ', name: 'Tanzania', flag: '🇹🇿' },
  { code: 'UG', name: 'Uganda', flag: '🇺🇬' },
  { code: 'RW', name: 'Rwanda', flag: '🇷🇼' },
  { code: 'AO', name: 'Angola', flag: '🇦🇴' },
  { code: 'MZ', name: 'Mozambique', flag: '🇲🇿' },
  { code: 'ZW', name: 'Zimbabwe', flag: '🇿🇼' },
  { code: 'BW', name: 'Botswana', flag: '🇧🇼' },
  { code: 'NA', name: 'Namibia', flag: '🇳🇦' },
  { code: 'ZM', name: 'Zambia', flag: '🇿🇲' },
  { code: 'NL', name: 'Netherlands', flag: '🇳🇱' },
  { code: 'BE', name: 'Belgium', flag: '🇧🇪' },
  { code: 'CH', name: 'Switzerland', flag: '🇨🇭' },
  { code: 'AT', name: 'Austria', flag: '🇦🇹' },
  { code: 'SE', name: 'Sweden', flag: '🇸🇪' },
  { code: 'NO', name: 'Norway', flag: '🇳🇴' },
  { code: 'DK', name: 'Denmark', flag: '🇩🇰' },
  { code: 'FI', name: 'Finland', flag: '🇫🇮' },
  { code: 'PL', name: 'Poland', flag: '🇵🇱' },
  { code: 'CZ', name: 'Czech Republic', flag: '🇨🇿' },
  { code: 'HU', name: 'Hungary', flag: '🇭🇺' },
  { code: 'RO', name: 'Romania', flag: '🇷🇴' },
  { code: 'BG', name: 'Bulgaria', flag: '🇧🇬' },
  { code: 'GR', name: 'Greece', flag: '🇬🇷' },
  { code: 'TR', name: 'Turkey', flag: '🇹🇷' },
  { code: 'IL', name: 'Israel', flag: '🇮🇱' },
  { code: 'AE', name: 'United Arab Emirates', flag: '🇦🇪' },
  { code: 'SA', name: 'Saudi Arabia', flag: '🇸🇦' },
  { code: 'QA', name: 'Qatar', flag: '🇶🇦' },
  { code: 'KW', name: 'Kuwait', flag: '🇰🇼' },
  { code: 'BH', name: 'Bahrain', flag: '🇧🇭' },
  { code: 'OM', name: 'Oman', flag: '🇴🇲' },
  { code: 'JO', name: 'Jordan', flag: '🇯🇴' },
  { code: 'LB', name: 'Lebanon', flag: '🇱🇧' },
  { code: 'IR', name: 'Iran', flag: '🇮🇷' },
  { code: 'IQ', name: 'Iraq', flag: '🇮🇶' },
  { code: 'PK', name: 'Pakistan', flag: '🇵🇰' },
  { code: 'BD', name: 'Bangladesh', flag: '🇧🇩' },
  { code: 'LK', name: 'Sri Lanka', flag: '🇱🇰' },
  { code: 'MM', name: 'Myanmar', flag: '🇲🇲' },
  { code: 'KH', name: 'Cambodia', flag: '🇰🇭' },
  { code: 'LA', name: 'Laos', flag: '🇱🇦' }
];

/**
 * Get all countries using country-state-city library with enhanced debugging
 */
export async function getAllCountries(): Promise<CountryOption[]> {
  console.log('🌍 getAllCountries() called');
  console.log('📚 Library loaded status:', libraryLoaded);

  try {
    if (!libraryLoaded) {
      console.warn('⚠️ Library not loaded, using fallback countries');
      return FALLBACK_COUNTRIES.sort((a, b) => a.name.localeCompare(b.name));
    }

    console.log('🔄 Loading countries from country-state-city library...');
    console.log('🔍 Country object:', Country);
    console.log('🔍 Country.getAllCountries type:', typeof Country.getAllCountries);

    // Get all countries from the library
    const countries = Country.getAllCountries();
    console.log('📊 Raw countries from library:', {
      type: typeof countries,
      isArray: Array.isArray(countries),
      length: countries?.length,
      firstItem: countries?.[0]
    });

    if (!countries || !Array.isArray(countries) || countries.length === 0) {
      console.warn('⚠️ No countries returned from library, using fallback');
      return FALLBACK_COUNTRIES.sort((a, b) => a.name.localeCompare(b.name));
    }

    const result: CountryOption[] = countries.map((country: any, index: number) => {
      if (index < 3) { // Log first 3 countries for debugging
        console.log(`🏳️ Country ${index}:`, {
          isoCode: country.isoCode,
          name: country.name,
          hasIsoCode: !!country.isoCode,
          hasName: !!country.name
        });
      }

      return {
        code: country.isoCode,
        name: country.name,
        flag: getFlagEmoji(country.isoCode)
      };
    });

    console.log(`✅ Successfully loaded ${result.length} countries from library`);
    return result.sort((a, b) => a.name.localeCompare(b.name));

  } catch (error) {
    console.error('❌ Error loading countries from library:', error);
    console.error('📋 Error details:', {
      message: (error as any)?.message,
      stack: (error as any)?.stack,
      name: (error as any)?.name
    });

    console.log('🔄 Using fallback countries...');
    return FALLBACK_COUNTRIES.sort((a, b) => a.name.localeCompare(b.name));
  }
}

/**
 * Convert country code to flag emoji
 */
function getFlagEmoji(countryCode: string): string {
  const codePoints = countryCode
    .toUpperCase()
    .split('')
    .map((char: string) => 127397 + char.charCodeAt(0));
  return String.fromCodePoint(...codePoints);
}

/**
 * Fallback states data for major countries
 */
const FALLBACK_STATES: Record<string, StateOption[]> = {
  'US': [
    { code: 'AL', name: 'Alabama', country: 'US' },
    { code: 'AK', name: 'Alaska', country: 'US' },
    { code: 'AZ', name: 'Arizona', country: 'US' },
    { code: 'AR', name: 'Arkansas', country: 'US' },
    { code: 'CA', name: 'California', country: 'US' },
    { code: 'CO', name: 'Colorado', country: 'US' },
    { code: 'CT', name: 'Connecticut', country: 'US' },
    { code: 'DE', name: 'Delaware', country: 'US' },
    { code: 'FL', name: 'Florida', country: 'US' },
    { code: 'GA', name: 'Georgia', country: 'US' },
    { code: 'HI', name: 'Hawaii', country: 'US' },
    { code: 'ID', name: 'Idaho', country: 'US' },
    { code: 'IL', name: 'Illinois', country: 'US' },
    { code: 'IN', name: 'Indiana', country: 'US' },
    { code: 'IA', name: 'Iowa', country: 'US' },
    { code: 'KS', name: 'Kansas', country: 'US' },
    { code: 'KY', name: 'Kentucky', country: 'US' },
    { code: 'LA', name: 'Louisiana', country: 'US' },
    { code: 'ME', name: 'Maine', country: 'US' },
    { code: 'MD', name: 'Maryland', country: 'US' },
    { code: 'MA', name: 'Massachusetts', country: 'US' },
    { code: 'MI', name: 'Michigan', country: 'US' },
    { code: 'MN', name: 'Minnesota', country: 'US' },
    { code: 'MS', name: 'Mississippi', country: 'US' },
    { code: 'MO', name: 'Missouri', country: 'US' },
    { code: 'MT', name: 'Montana', country: 'US' },
    { code: 'NE', name: 'Nebraska', country: 'US' },
    { code: 'NV', name: 'Nevada', country: 'US' },
    { code: 'NH', name: 'New Hampshire', country: 'US' },
    { code: 'NJ', name: 'New Jersey', country: 'US' },
    { code: 'NM', name: 'New Mexico', country: 'US' },
    { code: 'NY', name: 'New York', country: 'US' },
    { code: 'NC', name: 'North Carolina', country: 'US' },
    { code: 'ND', name: 'North Dakota', country: 'US' },
    { code: 'OH', name: 'Ohio', country: 'US' },
    { code: 'OK', name: 'Oklahoma', country: 'US' },
    { code: 'OR', name: 'Oregon', country: 'US' },
    { code: 'PA', name: 'Pennsylvania', country: 'US' },
    { code: 'RI', name: 'Rhode Island', country: 'US' },
    { code: 'SC', name: 'South Carolina', country: 'US' },
    { code: 'SD', name: 'South Dakota', country: 'US' },
    { code: 'TN', name: 'Tennessee', country: 'US' },
    { code: 'TX', name: 'Texas', country: 'US' },
    { code: 'UT', name: 'Utah', country: 'US' },
    { code: 'VT', name: 'Vermont', country: 'US' },
    { code: 'VA', name: 'Virginia', country: 'US' },
    { code: 'WA', name: 'Washington', country: 'US' },
    { code: 'WV', name: 'West Virginia', country: 'US' },
    { code: 'WI', name: 'Wisconsin', country: 'US' },
    { code: 'WY', name: 'Wyoming', country: 'US' }
  ],
  'BR': [
    { code: 'AC', name: 'Acre', country: 'BR' },
    { code: 'AL', name: 'Alagoas', country: 'BR' },
    { code: 'AP', name: 'Amapá', country: 'BR' },
    { code: 'AM', name: 'Amazonas', country: 'BR' },
    { code: 'BA', name: 'Bahia', country: 'BR' },
    { code: 'CE', name: 'Ceará', country: 'BR' },
    { code: 'DF', name: 'Distrito Federal', country: 'BR' },
    { code: 'ES', name: 'Espírito Santo', country: 'BR' },
    { code: 'GO', name: 'Goiás', country: 'BR' },
    { code: 'MA', name: 'Maranhão', country: 'BR' },
    { code: 'MT', name: 'Mato Grosso', country: 'BR' },
    { code: 'MS', name: 'Mato Grosso do Sul', country: 'BR' },
    { code: 'MG', name: 'Minas Gerais', country: 'BR' },
    { code: 'PA', name: 'Pará', country: 'BR' },
    { code: 'PB', name: 'Paraíba', country: 'BR' },
    { code: 'PR', name: 'Paraná', country: 'BR' },
    { code: 'PE', name: 'Pernambuco', country: 'BR' },
    { code: 'PI', name: 'Piauí', country: 'BR' },
    { code: 'RJ', name: 'Rio de Janeiro', country: 'BR' },
    { code: 'RN', name: 'Rio Grande do Norte', country: 'BR' },
    { code: 'RS', name: 'Rio Grande do Sul', country: 'BR' },
    { code: 'RO', name: 'Rondônia', country: 'BR' },
    { code: 'RR', name: 'Roraima', country: 'BR' },
    { code: 'SC', name: 'Santa Catarina', country: 'BR' },
    { code: 'SP', name: 'São Paulo', country: 'BR' },
    { code: 'SE', name: 'Sergipe', country: 'BR' },
    { code: 'TO', name: 'Tocantins', country: 'BR' }
  ],
  'CA': [
    { code: 'AB', name: 'Alberta', country: 'CA' },
    { code: 'BC', name: 'British Columbia', country: 'CA' },
    { code: 'MB', name: 'Manitoba', country: 'CA' },
    { code: 'NB', name: 'New Brunswick', country: 'CA' },
    { code: 'NL', name: 'Newfoundland and Labrador', country: 'CA' },
    { code: 'NS', name: 'Nova Scotia', country: 'CA' },
    { code: 'NT', name: 'Northwest Territories', country: 'CA' },
    { code: 'NU', name: 'Nunavut', country: 'CA' },
    { code: 'ON', name: 'Ontario', country: 'CA' },
    { code: 'PE', name: 'Prince Edward Island', country: 'CA' },
    { code: 'QC', name: 'Quebec', country: 'CA' },
    { code: 'SK', name: 'Saskatchewan', country: 'CA' },
    { code: 'YT', name: 'Yukon', country: 'CA' }
  ]
};

/**
 * Get states/provinces for a specific country with enhanced debugging
 */
export async function getStatesForCountry(countryCode: string): Promise<StateOption[]> {
  console.log(`🏛️ getStatesForCountry() called with: ${countryCode}`);
  console.log('📚 Library loaded status:', libraryLoaded);

  if (!countryCode) {
    console.warn('⚠️ No country code provided');
    return [];
  }

  try {
    if (!libraryLoaded) {
      console.warn('⚠️ Library not loaded, using fallback states');
      const fallbackStates = FALLBACK_STATES[countryCode] || [];
      console.log(`📋 Using ${fallbackStates.length} fallback states for ${countryCode}`);
      return fallbackStates;
    }

    console.log(`🔄 Loading states for country: ${countryCode}`);
    console.log('🔍 State object:', State);
    console.log('🔍 State.getStatesOfCountry type:', typeof State.getStatesOfCountry);

    // Get states from the library
    const states = State.getStatesOfCountry(countryCode);
    console.log('📊 Raw states from library:', {
      countryCode,
      type: typeof states,
      isArray: Array.isArray(states),
      length: states?.length,
      firstItem: states?.[0]
    });

    if (!states || !Array.isArray(states)) {
      console.warn(`⚠️ No states returned from library for ${countryCode}, using fallback`);
      const fallbackStates = FALLBACK_STATES[countryCode] || [];
      console.log(`📋 Using ${fallbackStates.length} fallback states for ${countryCode}`);
      return fallbackStates;
    }

    const result: StateOption[] = states.map((state: any, index: number) => {
      if (index < 3) { // Log first 3 states for debugging
        console.log(`🏛️ State ${index} for ${countryCode}:`, {
          isoCode: state.isoCode,
          name: state.name,
          hasIsoCode: !!state.isoCode,
          hasName: !!state.name
        });
      }

      return {
        code: state.isoCode,
        name: state.name,
        country: countryCode
      };
    });

    console.log(`✅ Successfully loaded ${result.length} states for ${countryCode}`);
    return result.sort((a, b) => a.name.localeCompare(b.name));

  } catch (error) {
    console.error(`❌ Error loading states for ${countryCode}:`, error);
    console.error('📋 Error details:', {
      message: (error as any)?.message,
      stack: (error as any)?.stack,
      name: (error as any)?.name
    });

    // Return fallback states if library fails
    const fallbackStates = FALLBACK_STATES[countryCode] || [];
    console.log(`🔄 Using ${fallbackStates.length} fallback states for ${countryCode}`);
    return fallbackStates;
  }
}

/**
 * Fallback cities data for major states
 */
const FALLBACK_CITIES: Record<string, Record<string, CityOption[]>> = {
  'US': {
    'California': [
      { name: 'Los Angeles', state: 'California', country: 'US' },
      { name: 'San Francisco', state: 'California', country: 'US' },
      { name: 'San Diego', state: 'California', country: 'US' },
      { name: 'Sacramento', state: 'California', country: 'US' },
      { name: 'San Jose', state: 'California', country: 'US' },
      { name: 'Oakland', state: 'California', country: 'US' },
      { name: 'Fresno', state: 'California', country: 'US' },
      { name: 'Long Beach', state: 'California', country: 'US' }
    ],
    'New York': [
      { name: 'New York City', state: 'New York', country: 'US' },
      { name: 'Buffalo', state: 'New York', country: 'US' },
      { name: 'Rochester', state: 'New York', country: 'US' },
      { name: 'Yonkers', state: 'New York', country: 'US' },
      { name: 'Syracuse', state: 'New York', country: 'US' },
      { name: 'Albany', state: 'New York', country: 'US' }
    ],
    'Texas': [
      { name: 'Houston', state: 'Texas', country: 'US' },
      { name: 'San Antonio', state: 'Texas', country: 'US' },
      { name: 'Dallas', state: 'Texas', country: 'US' },
      { name: 'Austin', state: 'Texas', country: 'US' },
      { name: 'Fort Worth', state: 'Texas', country: 'US' },
      { name: 'El Paso', state: 'Texas', country: 'US' }
    ],
    'Florida': [
      { name: 'Miami', state: 'Florida', country: 'US' },
      { name: 'Tampa', state: 'Florida', country: 'US' },
      { name: 'Orlando', state: 'Florida', country: 'US' },
      { name: 'Jacksonville', state: 'Florida', country: 'US' },
      { name: 'St. Petersburg', state: 'Florida', country: 'US' },
      { name: 'Tallahassee', state: 'Florida', country: 'US' }
    ]
  },
  'BR': {
    'São Paulo': [
      { name: 'São Paulo', state: 'São Paulo', country: 'BR' },
      { name: 'Campinas', state: 'São Paulo', country: 'BR' },
      { name: 'Santos', state: 'São Paulo', country: 'BR' },
      { name: 'São Bernardo do Campo', state: 'São Paulo', country: 'BR' },
      { name: 'Santo André', state: 'São Paulo', country: 'BR' },
      { name: 'Osasco', state: 'São Paulo', country: 'BR' }
    ],
    'Rio de Janeiro': [
      { name: 'Rio de Janeiro', state: 'Rio de Janeiro', country: 'BR' },
      { name: 'Niterói', state: 'Rio de Janeiro', country: 'BR' },
      { name: 'Nova Iguaçu', state: 'Rio de Janeiro', country: 'BR' },
      { name: 'Duque de Caxias', state: 'Rio de Janeiro', country: 'BR' },
      { name: 'Campos dos Goytacazes', state: 'Rio de Janeiro', country: 'BR' }
    ],
    'Minas Gerais': [
      { name: 'Belo Horizonte', state: 'Minas Gerais', country: 'BR' },
      { name: 'Uberlândia', state: 'Minas Gerais', country: 'BR' },
      { name: 'Contagem', state: 'Minas Gerais', country: 'BR' },
      { name: 'Juiz de Fora', state: 'Minas Gerais', country: 'BR' }
    ]
  },
  'CA': {
    'Ontario': [
      { name: 'Toronto', state: 'Ontario', country: 'CA' },
      { name: 'Ottawa', state: 'Ontario', country: 'CA' },
      { name: 'Hamilton', state: 'Ontario', country: 'CA' },
      { name: 'London', state: 'Ontario', country: 'CA' },
      { name: 'Windsor', state: 'Ontario', country: 'CA' }
    ],
    'Quebec': [
      { name: 'Montreal', state: 'Quebec', country: 'CA' },
      { name: 'Quebec City', state: 'Quebec', country: 'CA' },
      { name: 'Laval', state: 'Quebec', country: 'CA' },
      { name: 'Gatineau', state: 'Quebec', country: 'CA' }
    ],
    'British Columbia': [
      { name: 'Vancouver', state: 'British Columbia', country: 'CA' },
      { name: 'Victoria', state: 'British Columbia', country: 'CA' },
      { name: 'Burnaby', state: 'British Columbia', country: 'CA' },
      { name: 'Richmond', state: 'British Columbia', country: 'CA' }
    ]
  }
};

/**
 * Get cities for a specific state and country with enhanced debugging
 */
export async function getCitiesForState(countryCode: string, stateName: string): Promise<CityOption[]> {
  console.log(`🏙️ getCitiesForState() called with: ${countryCode}, ${stateName}`);
  console.log('📚 Library loaded status:', libraryLoaded);

  if (!countryCode || !stateName) {
    console.warn('⚠️ Missing country code or state name');
    return [];
  }

  try {
    if (!libraryLoaded) {
      console.warn('⚠️ Library not loaded, using fallback cities');
      const fallbackCities = FALLBACK_CITIES[countryCode]?.[stateName] || [];
      console.log(`📋 Using ${fallbackCities.length} fallback cities for ${stateName}, ${countryCode}`);
      return fallbackCities;
    }

    console.log(`🔄 Loading cities for state: ${stateName} in country: ${countryCode}`);

    // First, find the state by name to get its state code
    console.log('🔍 Getting states for country to find state code...');
    const states = State.getStatesOfCountry(countryCode);
    console.log('📊 Available states:', {
      count: states?.length,
      names: states?.map((s: any) => s.name).slice(0, 5) // Show first 5 for debugging
    });

    // Try exact match first
    let targetState = states.find((state: any) => state.name === stateName);
    console.log('🎯 Exact match result:', targetState ? `Found: ${targetState.name}` : 'Not found');

    // If no exact match, try case-insensitive partial match
    if (!targetState) {
      console.log('🔍 Trying case-insensitive partial match...');
      targetState = states.find((state: any) =>
        state.name.toLowerCase().includes(stateName.toLowerCase()) ||
        stateName.toLowerCase().includes(state.name.toLowerCase())
      );
      console.log('🎯 Partial match result:', targetState ? `Found: ${targetState.name}` : 'Not found');
    }

    if (!targetState) {
      console.warn(`❌ State ${stateName} not found in country ${countryCode}`);
      console.log('📋 Available states:', states.map((s: any) => s.name));
      const fallbackCities = FALLBACK_CITIES[countryCode]?.[stateName] || [];
      console.log(`🔄 Using ${fallbackCities.length} fallback cities`);
      return fallbackCities;
    }

    console.log(`✅ Found matching state: ${targetState.name} (${targetState.isoCode}) for search: ${stateName}`);

    // Get cities from the library using state code
    console.log('🔄 Loading cities from library...');
    const cities = City.getCitiesOfState(countryCode, targetState.isoCode);
    console.log('📊 Raw cities from library:', {
      countryCode,
      stateCode: targetState.isoCode,
      stateName: targetState.name,
      type: typeof cities,
      isArray: Array.isArray(cities),
      length: cities?.length,
      firstItem: cities?.[0]
    });

    if (!cities || !Array.isArray(cities)) {
      console.warn(`⚠️ No cities returned from library for ${targetState.name}, using fallback`);
      const fallbackCities = FALLBACK_CITIES[countryCode]?.[stateName] || [];
      console.log(`📋 Using ${fallbackCities.length} fallback cities`);
      return fallbackCities;
    }

    const result: CityOption[] = cities.map((city: any, index: number) => {
      if (index < 3) { // Log first 3 cities for debugging
        console.log(`🏙️ City ${index} for ${targetState.name}:`, {
          name: city.name,
          hasName: !!city.name
        });
      }

      return {
        name: city.name,
        state: targetState.name, // Use the actual state name from library
        country: countryCode
      };
    });

    console.log(`✅ Successfully loaded ${result.length} cities for ${targetState.name}, ${countryCode}`);
    return result.sort((a, b) => a.name.localeCompare(b.name));

  } catch (error) {
    console.error(`❌ Error loading cities for ${stateName}, ${countryCode}:`, error);
    console.error('📋 Error details:', {
      message: (error as any)?.message,
      stack: (error as any)?.stack,
      name: (error as any)?.name
    });

    // Return fallback cities if library fails
    const fallbackCities = FALLBACK_CITIES[countryCode]?.[stateName] || [];
    console.log(`🔄 Using ${fallbackCities.length} fallback cities for ${stateName}, ${countryCode}`);
    return fallbackCities;
  }
}

/**
 * Enhanced address autocomplete with neighborhood, city, and ZIP code
 * Works globally with any country
 */
export async function searchAddresses(
  query: string,
  countryCode: string,
  stateName?: string,
  cityName?: string
): Promise<AddressSuggestion[]> {
  // If API is not available, return empty array
  if (!apiClient.isAvailable()) {
    console.log('API not available for address search');
    return [];
  }

  try {
    // Build search query with geographic constraints for better results
    let searchQuery = query;
    const constraints = [];

    if (cityName) constraints.push(cityName);
    if (stateName) constraints.push(stateName);
    if (countryCode) constraints.push(countryCode);

    if (constraints.length > 0) {
      searchQuery += `, ${constraints.join(', ')}`;
    }

    const url = 'https://nominatim.openstreetmap.org/search?' + new URLSearchParams({
      format: 'json',
      q: searchQuery,
      countrycodes: countryCode.toLowerCase(),
      addressdetails: '1',
      limit: '10',
      dedupe: '1',
      bounded: '0',
      extratags: '1'
    });

    const data = await apiClient.get(url);

    return data.map((item: any) => {
      const address = item.address || {};

      // Extract street information with multiple fallbacks
      const houseNumber = address.house_number || '';
      const street = address.road ||
                    address.street ||
                    address.pedestrian ||
                    address.path ||
                    address.footway || '';

      let fullStreet = street;
      if (houseNumber && street) {
        fullStreet = `${houseNumber} ${street}`.trim();
      } else if (!street && houseNumber) {
        fullStreet = houseNumber;
      }

      // Extract neighborhood/district with multiple fallbacks
      const neighborhood = address.neighbourhood ||
                          address.suburb ||
                          address.district ||
                          address.quarter ||
                          address.subdistrict ||
                          address.borough || '';

      // Extract city information with multiple fallbacks
      const city = address.city ||
                  address.town ||
                  address.village ||
                  address.municipality ||
                  address.hamlet ||
                  address.locality || '';

      // Extract state information with multiple fallbacks
      const state = address.state ||
                   address.province ||
                   address.region ||
                   address.county || '';

      // Extract ZIP code
      const zipCode = address.postcode || address.postal_code || '';

      // Create display name: "Street, Neighborhood, City, ZIP"
      const displayParts = [];
      if (fullStreet) displayParts.push(fullStreet);
      if (neighborhood) displayParts.push(neighborhood);
      if (city) displayParts.push(city);
      if (zipCode) displayParts.push(zipCode);

      return {
        display_name: displayParts.join(', '),
        street: fullStreet,
        neighborhood,
        city,
        state,
        zipCode,
        latitude: parseFloat(item.lat),
        longitude: parseFloat(item.lon)
      };
    }).filter((suggestion: any) => {
      // Filter to ensure we have meaningful results
      return suggestion.street || suggestion.city || suggestion.neighborhood;
    })
    // Deduplicate results based on display_name
    .filter((suggestion: any, index: number, array: any[]) => {
      return array.findIndex((s: any) => s.display_name === suggestion.display_name) === index;
    })
    // Deduplicate by street + city combination
    .filter((suggestion: any, index: number, array: any[]) => {
      const key = `${suggestion.street}-${suggestion.city}`;
      return array.findIndex((s: any) => `${s.street}-${s.city}` === key) === index;
    })
    .slice(0, 8); // Limit to top 8 unique results

  } catch (error) {
    console.error('Error searching addresses:', error);
    return [];
  }
}
