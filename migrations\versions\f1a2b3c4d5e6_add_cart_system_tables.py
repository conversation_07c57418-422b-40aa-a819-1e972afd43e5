"""add_cart_system_tables

Revision ID: f1a2b3c4d5e6
Revises: e8f9a2b1c4d5
Create Date: 2025-06-27 16:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'f1a2b3c4d5e6'
down_revision: Union[str, None] = 'e8f9a2b1c4d5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    
    # Create enum type for CartStatus
    cart_status_enum = postgresql.ENUM(
        'active', 'abandoned', 'converted', 'expired',
        name='cartstatus'
    )
    cart_status_enum.create(op.get_bind())
    
    # Create eshop_carts table
    op.create_table(
        'eshop_carts',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('session_id', sa.String(length=255), nullable=True),
        sa.Column('status', cart_status_enum, nullable=False),
        sa.Column('market_context', sa.String(length=20), nullable=False),
        sa.Column('subtotal', sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column('tax_amount', sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column('discount_amount', sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column('shipping_amount', sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column('total_amount', sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column('currency', sa.String(length=3), nullable=False),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('metadata', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('expires_at', sa.DateTime(), nullable=True),
        sa.Column('converted_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create eshop_cart_items table
    op.create_table(
        'eshop_cart_items',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('cart_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('product_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('quantity', sa.Integer(), nullable=False),
        sa.Column('unit_price', sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column('total_price', sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column('selected_variants', sa.JSON(), nullable=True),
        sa.Column('selected_modifiers', sa.JSON(), nullable=True),
        sa.Column('special_instructions', sa.Text(), nullable=True),
        sa.Column('metadata', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['cart_id'], ['eshop_carts.id'], ),
        sa.ForeignKeyConstraint(['product_id'], ['eshop_products.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for eshop_carts
    op.create_index('ix_carts_tenant_user', 'eshop_carts', ['tenant_id', 'user_id'], unique=False)
    op.create_index('ix_carts_tenant_session', 'eshop_carts', ['tenant_id', 'session_id'], unique=False)
    op.create_index('ix_carts_status_expires', 'eshop_carts', ['status', 'expires_at'], unique=False)
    op.create_index('ix_carts_market_context', 'eshop_carts', ['market_context'], unique=False)
    op.create_index(op.f('ix_eshop_carts_tenant_id'), 'eshop_carts', ['tenant_id'], unique=False)
    op.create_index(op.f('ix_eshop_carts_user_id'), 'eshop_carts', ['user_id'], unique=False)
    op.create_index(op.f('ix_eshop_carts_session_id'), 'eshop_carts', ['session_id'], unique=False)
    op.create_index(op.f('ix_eshop_carts_status'), 'eshop_carts', ['status'], unique=False)
    
    # Create indexes for eshop_cart_items
    op.create_index('ix_cart_items_cart_product', 'eshop_cart_items', ['cart_id', 'product_id'], unique=False)
    op.create_index('ix_cart_items_created', 'eshop_cart_items', ['created_at'], unique=False)
    op.create_index(op.f('ix_eshop_cart_items_cart_id'), 'eshop_cart_items', ['cart_id'], unique=False)
    op.create_index(op.f('ix_eshop_cart_items_product_id'), 'eshop_cart_items', ['product_id'], unique=False)


def downgrade() -> None:
    """Downgrade schema."""
    
    # Drop indexes for eshop_cart_items
    op.drop_index(op.f('ix_eshop_cart_items_product_id'), table_name='eshop_cart_items')
    op.drop_index(op.f('ix_eshop_cart_items_cart_id'), table_name='eshop_cart_items')
    op.drop_index('ix_cart_items_created', table_name='eshop_cart_items')
    op.drop_index('ix_cart_items_cart_product', table_name='eshop_cart_items')
    
    # Drop indexes for eshop_carts
    op.drop_index(op.f('ix_eshop_carts_status'), table_name='eshop_carts')
    op.drop_index(op.f('ix_eshop_carts_session_id'), table_name='eshop_carts')
    op.drop_index(op.f('ix_eshop_carts_user_id'), table_name='eshop_carts')
    op.drop_index(op.f('ix_eshop_carts_tenant_id'), table_name='eshop_carts')
    op.drop_index('ix_carts_market_context', table_name='eshop_carts')
    op.drop_index('ix_carts_status_expires', table_name='eshop_carts')
    op.drop_index('ix_carts_tenant_session', table_name='eshop_carts')
    op.drop_index('ix_carts_tenant_user', table_name='eshop_carts')
    
    # Drop tables
    op.drop_table('eshop_cart_items')
    op.drop_table('eshop_carts')
    
    # Drop enum type
    cart_status_enum = postgresql.ENUM(name='cartstatus')
    cart_status_enum.drop(op.get_bind())
