# General - Payment Processors

**Categoria:** General
**Módulo:** Payment Processors
**Total de Endpoints:** 6
**Gerado em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [GET /api/modules/core/payments/processors](#get-apimodulescorepaymentsprocessors) - List Payment Processors
- [POST /api/modules/core/payments/processors](#post-apimodulescorepaymentsprocessors) - Create Payment Processor
- [DELETE /api/modules/core/payments/processors/{processor_id}](#delete-apimodulescorepaymentsprocessorsprocessor-id) - Delete Payment Processor
- [GET /api/modules/core/payments/processors/{processor_id}](#get-apimodulescorepaymentsprocessorsprocessor-id) - Get Payment Processor
- [PUT /api/modules/core/payments/processors/{processor_id}](#put-apimodulescorepaymentsprocessorsprocessor-id) - Update Payment Processor
- [GET /api/modules/core/payments/processors/{processor_id}/admin](#get-apimodulescorepaymentsprocessorsprocessor-idadmin) - Get Payment Processor (Admin)

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### PaymentProcessorCreate

**Descrição:** Schema for creating a new payment processor.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `processor_type` | PaymentProcessorType | ✅ | - |
| `is_active` | boolean | ❌ | - |
| `is_default` | boolean | ❌ | - |
| `sandbox_mode` | boolean | ❌ | - |
| `webhook_url` | unknown | ❌ | - |
| `additional_config` | unknown | ❌ | - |
| `api_key` | unknown | ❌ | - |
| `api_secret` | unknown | ❌ | - |
| `webhook_secret` | unknown | ❌ | - |

### PaymentProcessorRead

**Descrição:** Schema for reading a payment processor.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `processor_type` | PaymentProcessorType | ✅ | - |
| `is_active` | boolean | ❌ | - |
| `is_default` | boolean | ❌ | - |
| `sandbox_mode` | boolean | ❌ | - |
| `webhook_url` | unknown | ❌ | - |
| `additional_config` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |

### PaymentProcessorReadAdmin

**Descrição:** Schema for reading a payment processor with sensitive information.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `processor_type` | PaymentProcessorType | ✅ | - |
| `is_active` | boolean | ❌ | - |
| `is_default` | boolean | ❌ | - |
| `sandbox_mode` | boolean | ❌ | - |
| `webhook_url` | unknown | ❌ | - |
| `additional_config` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `api_key` | unknown | ❌ | - |
| `webhook_secret` | unknown | ❌ | - |

### PaymentProcessorUpdate

**Descrição:** Schema for updating an existing payment processor.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | - |
| `processor_type` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `is_default` | unknown | ❌ | - |
| `sandbox_mode` | unknown | ❌ | - |
| `api_key` | unknown | ❌ | - |
| `api_secret` | unknown | ❌ | - |
| `webhook_url` | unknown | ❌ | - |
| `webhook_secret` | unknown | ❌ | - |
| `additional_config` | unknown | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/modules/core/payments/processors {#get-apimodulescorepaymentsprocessors}

**Resumo:** List Payment Processors
**Descrição:** List all payment processors for the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `is_active` | string | query | ❌ | Filter by active status |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/payments/processors" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/payments/processors {#post-apimodulescorepaymentsprocessors}

**Resumo:** Create Payment Processor
**Descrição:** Create a new payment processor for the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [PaymentProcessorCreate](#paymentprocessorcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PaymentProcessorRead](#paymentprocessorread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/payments/processors" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/core/payments/processors/{processor_id} {#delete-apimodulescorepaymentsprocessorsprocessor-id}

**Resumo:** Delete Payment Processor
**Descrição:** Delete an existing payment processor.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `processor_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/core/payments/processors/{processor_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/payments/processors/{processor_id} {#get-apimodulescorepaymentsprocessorsprocessor-id}

**Resumo:** Get Payment Processor
**Descrição:** Get details of a specific payment processor.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `processor_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PaymentProcessorRead](#paymentprocessorread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/payments/processors/{processor_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/core/payments/processors/{processor_id} {#put-apimodulescorepaymentsprocessorsprocessor-id}

**Resumo:** Update Payment Processor
**Descrição:** Update an existing payment processor.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `processor_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [PaymentProcessorUpdate](#paymentprocessorupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PaymentProcessorRead](#paymentprocessorread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/core/payments/processors/{processor_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/payments/processors/{processor_id}/admin {#get-apimodulescorepaymentsprocessorsprocessor-idadmin}

**Resumo:** Get Payment Processor (Admin)
**Descrição:** Get details of a specific payment processor, including sensitive information.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `processor_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PaymentProcessorReadAdmin](#paymentprocessorreadadmin)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/payments/processors/{processor_id}/admin" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
