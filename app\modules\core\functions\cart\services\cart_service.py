"""
Cart Service for EShop System
=============================

Serviço para gerenciamento de carrinho de compras com suporte a
sessões persistentes, produtos do eshop e contexto B2B/B2C.
"""

import uuid
import logging
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Optional, List, Dict, Any, Tuple

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from sqlalchemy import and_, or_, func, update, delete

from app.core.exceptions import ValidationError, NotFoundError
from app.modules.core.functions.cart.models.cart import Cart, CartItem, CartStatus
from app.modules.core.functions.cart.schemas.cart import (
    CartCreate, CartUpdate, CartRead, CartItemCreate, 
    CartItemUpdate, CartStats, AddToCartRequest
)
from app.modules.core.eshop.models.product import Product as EshopProduct

logger = logging.getLogger(__name__)


class CartService:
    """Serviço para gerenciamento de carrinho de compras."""
    
    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session
    
    async def get_or_create_cart(
        self, 
        tenant_id: uuid.UUID,
        user_id: Optional[uuid.UUID] = None,
        session_id: Optional[str] = None,
        market_context: str = 'b2c'
    ) -> Cart:
        """
        Obtém carrinho existente ou cria um novo.
        
        Args:
            tenant_id: ID do tenant
            user_id: ID do usuário (opcional para carrinho anônimo)
            session_id: ID da sessão (para carrinho anônimo)
            market_context: Contexto do mercado (b2b/b2c)
            
        Returns:
            Cart: Carrinho existente ou novo
        """
        try:
            # Buscar carrinho existente
            query = select(Cart).options(
                selectinload(Cart.items).selectinload(CartItem.product)
            ).where(
                and_(
                    Cart.tenant_id == tenant_id,
                    Cart.status == CartStatus.ACTIVE
                )
            )
            
            if user_id:
                query = query.where(Cart.user_id == user_id)
            elif session_id:
                query = query.where(Cart.session_id == session_id)
            else:
                raise ValidationError("user_id ou session_id deve ser fornecido")
            
            result = await self.db_session.execute(query)
            cart = result.scalars().first()
            
            if cart:
                # Verificar se o carrinho não expirou
                if cart.is_expired:
                    cart.status = CartStatus.EXPIRED
                    await self.db_session.commit()
                    cart = None
            
            # Criar novo carrinho se necessário
            if not cart:
                cart = Cart(
                    tenant_id=tenant_id,
                    user_id=user_id,
                    session_id=session_id,
                    market_context=market_context,
                    status=CartStatus.ACTIVE
                )
                cart.set_expiration(hours=24)  # Expira em 24 horas
                
                self.db_session.add(cart)
                await self.db_session.commit()
                await self.db_session.refresh(cart)
                
                logger.info(f"Novo carrinho criado: {cart.id}")
            
            return cart
            
        except Exception as e:
            logger.error(f"Erro ao obter/criar carrinho: {e}")
            await self.db_session.rollback()
            raise
    
    async def add_item_to_cart(
        self,
        cart_id: uuid.UUID,
        request: AddToCartRequest
    ) -> CartItem:
        """
        Adiciona item ao carrinho.
        
        Args:
            cart_id: ID do carrinho
            request: Dados do item a ser adicionado
            
        Returns:
            CartItem: Item adicionado ao carrinho
        """
        try:
            # Verificar se o carrinho existe e está ativo
            cart = await self.get_cart_by_id(cart_id)
            if not cart or cart.status != CartStatus.ACTIVE:
                raise NotFoundError("Carrinho não encontrado ou inativo")
            
            # Verificar se o produto existe
            product_query = select(Product).where(Product.id == request.product_id)
            result = await self.db_session.execute(product_query)
            product = result.scalars().first()
            
            if not product:
                raise NotFoundError("Produto não encontrado")
            
            # Verificar se já existe item similar no carrinho
            existing_item = await self._find_similar_cart_item(
                cart_id, request.product_id, request.selected_variants, 
                request.selected_modifiers
            )
            
            if existing_item:
                # Atualizar quantidade do item existente
                existing_item.quantity += request.quantity
                existing_item.calculate_total()
                cart_item = existing_item
            else:
                # Criar novo item
                cart_item = CartItem(
                    cart_id=cart_id,
                    product_id=request.product_id,
                    quantity=request.quantity,
                    unit_price=product.price,
                    selected_variants=request.selected_variants,
                    selected_modifiers=request.selected_modifiers,
                    special_instructions=request.special_instructions
                )
                cart_item.calculate_total()
                self.db_session.add(cart_item)
            
            # Atualizar totais do carrinho
            await self._update_cart_totals(cart)
            
            await self.db_session.commit()
            await self.db_session.refresh(cart_item)
            
            logger.info(f"Item adicionado ao carrinho {cart_id}: {cart_item.id}")
            return cart_item
            
        except Exception as e:
            logger.error(f"Erro ao adicionar item ao carrinho: {e}")
            await self.db_session.rollback()
            raise
    
    async def update_cart_item(
        self,
        cart_item_id: uuid.UUID,
        quantity: int,
        selected_variants: Optional[Dict[str, str]] = None,
        selected_modifiers: Optional[List[str]] = None,
        special_instructions: Optional[str] = None
    ) -> CartItem:
        """
        Atualiza item do carrinho.
        
        Args:
            cart_item_id: ID do item do carrinho
            quantity: Nova quantidade
            selected_variants: Variações atualizadas
            selected_modifiers: Modificadores atualizados
            special_instructions: Instruções especiais
            
        Returns:
            CartItem: Item atualizado
        """
        try:
            # Buscar item do carrinho
            query = select(CartItem).options(
                selectinload(CartItem.cart)
            ).where(CartItem.id == cart_item_id)
            
            result = await self.db_session.execute(query)
            cart_item = result.scalars().first()
            
            if not cart_item:
                raise NotFoundError("Item do carrinho não encontrado")
            
            # Atualizar dados do item
            cart_item.quantity = quantity
            if selected_variants is not None:
                cart_item.selected_variants = selected_variants
            if selected_modifiers is not None:
                cart_item.selected_modifiers = selected_modifiers
            if special_instructions is not None:
                cart_item.special_instructions = special_instructions
            
            cart_item.calculate_total()
            
            # Atualizar totais do carrinho
            await self._update_cart_totals(cart_item.cart)
            
            await self.db_session.commit()
            await self.db_session.refresh(cart_item)
            
            logger.info(f"Item do carrinho atualizado: {cart_item_id}")
            return cart_item
            
        except Exception as e:
            logger.error(f"Erro ao atualizar item do carrinho: {e}")
            await self.db_session.rollback()
            raise
    
    async def remove_cart_item(self, cart_item_id: uuid.UUID) -> bool:
        """
        Remove item do carrinho.
        
        Args:
            cart_item_id: ID do item a ser removido
            
        Returns:
            bool: True se removido com sucesso
        """
        try:
            # Buscar item do carrinho
            query = select(CartItem).options(
                selectinload(CartItem.cart)
            ).where(CartItem.id == cart_item_id)
            
            result = await self.db_session.execute(query)
            cart_item = result.scalars().first()
            
            if not cart_item:
                raise NotFoundError("Item do carrinho não encontrado")
            
            cart = cart_item.cart
            
            # Remover item
            await self.db_session.delete(cart_item)
            
            # Atualizar totais do carrinho
            await self._update_cart_totals(cart)
            
            await self.db_session.commit()
            
            logger.info(f"Item removido do carrinho: {cart_item_id}")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao remover item do carrinho: {e}")
            await self.db_session.rollback()
            raise
    
    async def get_cart_by_id(self, cart_id: uuid.UUID) -> Optional[Cart]:
        """
        Busca carrinho por ID.
        
        Args:
            cart_id: ID do carrinho
            
        Returns:
            Cart: Carrinho encontrado ou None
        """
        query = select(Cart).options(
            selectinload(Cart.items).selectinload(CartItem.product)
        ).where(Cart.id == cart_id)
        
        result = await self.db_session.execute(query)
        return result.scalars().first()
    
    async def clear_cart(self, cart_id: uuid.UUID) -> bool:
        """
        Limpa todos os itens do carrinho.
        
        Args:
            cart_id: ID do carrinho
            
        Returns:
            bool: True se limpo com sucesso
        """
        try:
            # Remover todos os itens
            await self.db_session.execute(
                delete(CartItem).where(CartItem.cart_id == cart_id)
            )
            
            # Atualizar totais do carrinho
            cart = await self.get_cart_by_id(cart_id)
            if cart:
                await self._update_cart_totals(cart)
            
            await self.db_session.commit()
            
            logger.info(f"Carrinho limpo: {cart_id}")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao limpar carrinho: {e}")
            await self.db_session.rollback()
            raise
    
    async def _find_similar_cart_item(
        self,
        cart_id: uuid.UUID,
        product_id: uuid.UUID,
        selected_variants: Optional[Dict[str, str]],
        selected_modifiers: Optional[List[str]]
    ) -> Optional[CartItem]:
        """Busca item similar no carrinho (mesmo produto e customizações)."""
        query = select(CartItem).where(
            and_(
                CartItem.cart_id == cart_id,
                CartItem.product_id == product_id,
                CartItem.selected_variants == selected_variants,
                CartItem.selected_modifiers == selected_modifiers
            )
        )
        
        result = await self.db_session.execute(query)
        return result.scalars().first()
    
    async def _update_cart_totals(self, cart: Cart):
        """Atualiza os totais do carrinho baseado nos itens."""
        # Recalcular subtotal
        subtotal_query = select(func.sum(CartItem.total_price)).where(
            CartItem.cart_id == cart.id
        )
        result = await self.db_session.execute(subtotal_query)
        subtotal = result.scalar() or Decimal('0.00')
        
        # Atualizar totais (lógica de tax e shipping pode ser expandida)
        cart.subtotal = subtotal
        cart.tax_amount = subtotal * Decimal('0.1')  # 10% de taxa (exemplo)
        cart.total_amount = cart.subtotal + cart.tax_amount + cart.shipping_amount - cart.discount_amount
        cart.updated_at = datetime.utcnow()


# Instância global do serviço
cart_service = None


def get_cart_service(db_session: AsyncSession) -> CartService:
    """Factory function para obter instância do CartService."""
    return CartService(db_session)
