import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import status
import uuid

# Import your app instance and other necessary components
from app.main import app
from app.modules.core.users.models.user import User
from app.modules.core.roles.models.roles import UserRole
from app.modules.core.users.models.user_associations import UserAssociation, AssociationType

# Fixtures for test users with different roles will be needed.
# For now, we assume they exist and are provided by conftest.py or similar.

@pytest.mark.asyncio
async def test_list_products_unauthenticated(client: AsyncClient, db_session: AsyncSession):
    """
    Tests that unauthenticated users can only access public, approved B2C products.
    """
    response = await client.get("/api/v1/eshop/products")
    assert response.status_code == status.HTTP_200_OK
    
    products = response.json()
    for product in products:
        assert product["market_type"] == "b2c"
        assert product["approval_status"] == "approved"

@pytest.mark.asyncio
async def test_create_product_unauthorized(client: AsyncClient, authenticated_user: User):
    """
    Tests that a regular user without TVendorSupplier association cannot create a product.
    """
    headers = {"Authorization": f"Bearer {authenticated_user['access_token']}"}
    product_data = {
        "name": "Unauthorized Product",
        "description": "This should not be created.",
        "base_price": 10.00,
        "category_id": str(uuid.uuid4()), # Dummy UUID
        "market_type": "b2c",
    }
    response = await client.post("/api/v1/eshop/products", json=product_data, headers=headers)
    assert response.status_code == status.HTTP_403_FORBIDDEN

# We will need to add more tests for the following scenarios:
# - A TVendorSupplier creating a product successfully.
# - An admin creating a product successfully.
# - A user with TCostumer role being able to see B2B products.
# - A vendor being able to update their own product.
# - A vendor being unable to update another vendor's product.
# - An admin being able to update any product. 