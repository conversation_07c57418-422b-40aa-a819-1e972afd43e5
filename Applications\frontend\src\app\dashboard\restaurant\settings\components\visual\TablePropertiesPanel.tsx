'use client';

import React, { useState, useEffect } from 'react';
import {
  XMarkIcon,
  CheckIcon,
  PencilIcon,
  TableCellsIcon,
  UserGroupIcon,
  MapPinIcon,
  CogIcon,
  TrashIcon,
} from '@heroicons/react/24/outline';
import { Table, TableStatus } from '@/types/pos';
import {
  getStatusText,
  getStatusIcon,
  getCapacityText,
  getZoneDisplayName,
} from '@/utils/table/TableVisualUtils';

// ===============================
// INTERFACES
// ===============================

export interface TablePropertiesPanelProps {
  table: Table | null;
  zones: string[];
  isOpen: boolean;
  onClose: () => void;
  onSave: (table: Table) => void;
  onDelete?: (table: Table) => void;
  className?: string;
}

interface TableFormData {
  name: string;
  capacity: number;
  zone: string;
  status: TableStatus;
  position_x: number;
  position_y: number;
  width: number;
  height: number;
  shape: 'rectangle' | 'circle' | 'custom';
  is_active: boolean;
  qrcode_enabled: boolean;
}

// ===============================
// COMPONENT
// ===============================

export default function TablePropertiesPanel({
  table,
  zones,
  isOpen,
  onClose,
  onSave,
  onDelete,
  className = ''
}: TablePropertiesPanelProps) {
  const [formData, setFormData] = useState<TableFormData | null>(null);
  const [hasChanges, setHasChanges] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Initialize form data when table changes
  useEffect(() => {
    if (table) {
      setFormData({
        name: table.name || '',
        capacity: table.capacity || 4,
        zone: table.zone || '',
        status: table.status || 'available',
        position_x: table.position_x || 0,
        position_y: table.position_y || 0,
        width: table.width || 120,
        height: table.height || 80,
        shape: (table.shape as 'rectangle' | 'circle' | 'custom') || 'rectangle',
        is_active: table.is_active ?? true,
        qrcode_enabled: table.qrcode_enabled ?? true,
      });
      setHasChanges(false);
    } else {
      setFormData(null);
      setHasChanges(false);
    }
  }, [table]);

  // Check for changes
  useEffect(() => {
    if (!table || !formData) {
      setHasChanges(false);
      return;
    }

    const hasChanged = 
      formData.name !== (table.name || '') ||
      formData.capacity !== (table.capacity || 4) ||
      formData.zone !== (table.zone || '') ||
      formData.status !== (table.status || 'available') ||
      formData.position_x !== (table.position_x || 0) ||
      formData.position_y !== (table.position_y || 0) ||
      formData.width !== (table.width || 120) ||
      formData.height !== (table.height || 80) ||
      formData.shape !== (table.shape || 'rectangle') ||
      formData.is_active !== (table.is_active ?? true) ||
      formData.qrcode_enabled !== (table.qrcode_enabled ?? true);

    setHasChanges(hasChanged);
  }, [table, formData]);

  const handleInputChange = (field: keyof TableFormData, value: any) => {
    if (!formData) return;
    
    setFormData(prev => prev ? { ...prev, [field]: value } : null);
  };

  const handleSave = async () => {
    if (!table || !formData || !hasChanges) return;

    setIsLoading(true);
    try {
      const updatedTable: Table = {
        ...table,
        name: formData.name,
        capacity: formData.capacity,
        zone: formData.zone,
        status: formData.status,
        position_x: formData.position_x,
        position_y: formData.position_y,
        width: formData.width,
        height: formData.height,
        shape: formData.shape,
        is_active: formData.is_active,
        qrcode_enabled: formData.qrcode_enabled,
      };

      await onSave(updatedTable);
      setHasChanges(false);
    } catch (error) {
      console.error('Error saving table:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!table || !onDelete) return;

    if (confirm(`Tem certeza que deseja deletar a mesa "${table.name}"?`)) {
      setIsLoading(true);
      try {
        await onDelete(table);
        onClose();
      } catch (error) {
        console.error('Error deleting table:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleCancel = () => {
    if (hasChanges) {
      if (confirm('Você tem alterações não salvas. Deseja descartar?')) {
        onClose();
      }
    } else {
      onClose();
    }
  };

  if (!isOpen || !table || !formData) {
    return null;
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-lg ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <TableCellsIcon className="h-5 w-5 text-gray-600" />
          <h3 className="text-lg font-semibold text-gray-900">
            Propriedades da Mesa
          </h3>
        </div>
        <button
          onClick={handleCancel}
          className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <XMarkIcon className="h-5 w-5" />
        </button>
      </div>

      {/* Content */}
      <div className="p-4 space-y-6 max-h-96 overflow-y-auto">
        
        {/* Basic Info */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-900 flex items-center space-x-2">
            <PencilIcon className="h-4 w-4" />
            <span>Informações Básicas</span>
          </h4>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Nome da Mesa
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Ex: Mesa VIP"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Capacidade
              </label>
              <input
                type="number"
                min="1"
                max="20"
                value={formData.capacity}
                onChange={(e) => handleInputChange('capacity', parseInt(e.target.value) || 1)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Zona
              </label>
              <select
                value={formData.zone}
                onChange={(e) => handleInputChange('zone', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Selecionar zona</option>
                {zones.map(zone => (
                  <option key={zone} value={zone}>{zone}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                value={formData.status}
                onChange={(e) => handleInputChange('status', e.target.value as TableStatus)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="available">Disponível</option>
                <option value="occupied">Ocupada</option>
                <option value="reserved">Reservada</option>
                <option value="cleaning">Limpeza</option>
                <option value="inactive">Inativa</option>
              </select>
            </div>
          </div>
        </div>

        {/* Position & Size */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-900 flex items-center space-x-2">
            <CogIcon className="h-4 w-4" />
            <span>Posição e Tamanho</span>
          </h4>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Posição X
              </label>
              <input
                type="number"
                value={formData.position_x}
                onChange={(e) => handleInputChange('position_x', parseInt(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Posição Y
              </label>
              <input
                type="number"
                value={formData.position_y}
                onChange={(e) => handleInputChange('position_y', parseInt(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Largura
              </label>
              <input
                type="number"
                min="60"
                max="300"
                value={formData.width}
                onChange={(e) => handleInputChange('width', parseInt(e.target.value) || 120)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Altura
              </label>
              <input
                type="number"
                min="40"
                max="300"
                value={formData.height}
                onChange={(e) => handleInputChange('height', parseInt(e.target.value) || 80)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Forma
            </label>
            <select
              value={formData.shape}
              onChange={(e) => handleInputChange('shape', e.target.value as 'rectangle' | 'circle' | 'custom')}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="rectangle">Retângulo</option>
              <option value="circle">Círculo</option>
              <option value="custom">Personalizada</option>
            </select>
          </div>
        </div>

        {/* Settings */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-900">Configurações</h4>
          
          <div className="space-y-3">
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={formData.is_active}
                onChange={(e) => handleInputChange('is_active', e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">Mesa ativa</span>
            </label>

            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={formData.qrcode_enabled}
                onChange={(e) => handleInputChange('qrcode_enabled', e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">QR Code habilitado</span>
            </label>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="flex items-center justify-between p-4 border-t border-gray-200">
        <div className="flex items-center space-x-2">
          {onDelete && (
            <button
              onClick={handleDelete}
              disabled={isLoading}
              className="flex items-center space-x-2 px-3 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50"
            >
              <TrashIcon className="h-4 w-4" />
              <span>Deletar</span>
            </button>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={handleCancel}
            disabled={isLoading}
            className="px-4 py-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors disabled:opacity-50"
          >
            Cancelar
          </button>
          
          <button
            onClick={handleSave}
            disabled={isLoading || !hasChanges}
            className={`
              flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors
              ${hasChanges 
                ? 'bg-blue-600 text-white hover:bg-blue-700' 
                : 'bg-gray-100 text-gray-400 cursor-not-allowed'
              }
              ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}
            `}
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                <span>Salvando...</span>
              </>
            ) : (
              <>
                <CheckIcon className="h-4 w-4" />
                <span>Salvar</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
