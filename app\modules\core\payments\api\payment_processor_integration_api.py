"""API endpoints for payment processor integration."""

import uuid  # noqa: E402
from typing import Dict, Any, Optional, List, TYPE_CHECKING
from fastapi import APIRouter, Depends, HTTPException, status, Body, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db  # noqa: E402
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    get_current_tenant_from_header as get_current_tenant,
    require_tenant_role,
)
from app.modules.core.roles.models.roles import RolePermissions, TenantRole
from app.core.exceptions import BusinessLogicError
from app.modules.core.tenants.models.tenant import Tenant

# Use TYPE_CHECKING to avoid circular import
if TYPE_CHECKING:
    from app.modules.core.users.models.user import User

from app.modules.core.payments.schemas.payment_transaction import (  # noqa: E402
    PaymentTransactionRead,
    PaymentTransactionWithRefundsRead,
    PaymentRefundRead,
)
from app.modules.core.payments.services.payment_processor_integration_service import (  # noqa: E402
    payment_processor_integration_service,
)
from app.modules.core.payments.services.payment_transaction_service import (  # noqa: E402
    payment_transaction_service,
)

router = APIRouter()


@router.post(
    "/transactions/{transaction_id}/process",
    response_model=PaymentTransactionRead,
    status_code=status.HTTP_200_OK,
    summary="Process Payment",
    description="Process a payment using the appropriate payment processor.",
)
async def process_payment(
    *,
    db: AsyncSession = Depends(get_db),
    transaction_id: uuid.UUID,
    payment_method_id: Optional[uuid.UUID] = None,
    customer_id: Optional[uuid.UUID] = None,
    metadata: Dict[str, Any] = Body(default={}),
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(required_roles=RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
) -> PaymentTransactionRead:
    """
    Process a payment using the appropriate payment processor.
    Requires STAFF, MANAGER, or OWNER role.
    """
    try:
        transaction = await payment_processor_integration_service.process_payment(
            db=db,
            transaction_id=transaction_id,
            tenant_id=current_tenant.id,
            payment_method_id=payment_method_id,
            customer_id=customer_id,
            metadata=metadata,
        )
        return transaction
    except BusinessLogicError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )


@router.post(
    "/refunds/{refund_id}/process",
    response_model=PaymentRefundRead,
    status_code=status.HTTP_200_OK,
    summary="Process Refund",
    description="Process a refund using the appropriate payment processor.",
)
async def process_refund(
    *,
    db: AsyncSession = Depends(get_db),
    refund_id: uuid.UUID,
    reason: Optional[str] = None,
    metadata: Dict[str, Any] = Body(default={}),
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(required_roles=RolePermissions.ADMIN_ROLES, tenant_id_source="header")
    ),
) -> PaymentRefundRead:
    """
    Process a refund using the appropriate payment processor.
    Requires MANAGER or OWNER role.
    """
    try:
        refund = await payment_processor_integration_service.process_refund(
            db=db,
            refund_id=refund_id,
            tenant_id=current_tenant.id,
            reason=reason,
            metadata=metadata,
        )
        return refund
    except BusinessLogicError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )


@router.post(
    "/transactions/{transaction_id}/check-status",
    response_model=PaymentTransactionRead,
    status_code=status.HTTP_200_OK,
    summary="Check Payment Status",
    description="Check the status of a payment with the payment processor.",
)
async def check_payment_status(
    *,
    db: AsyncSession = Depends(get_db),
    transaction_id: uuid.UUID,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(required_roles=RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
) -> PaymentTransactionRead:
    """
    Check the status of a payment with the payment processor.
    Requires STAFF, MANAGER, or OWNER role.
    """
    try:
        transaction = await payment_processor_integration_service.check_payment_status(
            db=db, transaction_id=transaction_id, tenant_id=current_tenant.id
        )
        return transaction
    except BusinessLogicError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )


@router.post(
    "/refunds/{refund_id}/check-status",
    response_model=PaymentRefundRead,
    status_code=status.HTTP_200_OK,
    summary="Check Refund Status",
    description="Check the status of a refund with the payment processor.",
)
async def check_refund_status(
    *,
    db: AsyncSession = Depends(get_db),
    refund_id: uuid.UUID,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(required_roles=RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
) -> PaymentRefundRead:
    """
    Check the status of a refund with the payment processor.
    Requires STAFF, MANAGER, or OWNER role.
    """
    try:
        refund = await payment_processor_integration_service.check_refund_status(
            db=db, refund_id=refund_id, tenant_id=current_tenant.id
        )
        return refund
    except BusinessLogicError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )


@router.get(
    "/transactions/{transaction_id}/with-refunds",
    response_model=PaymentTransactionWithRefundsRead,
    status_code=status.HTTP_200_OK,
    summary="Get Transaction with Refunds",
    description="Get a transaction with its refunds and calculated refund totals.",
)
async def get_transaction_with_refunds(
    *,
    db: AsyncSession = Depends(get_db),
    transaction_id: uuid.UUID,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(required_roles=RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
) -> PaymentTransactionWithRefundsRead:
    """
    Get a transaction with its refunds and calculated refund totals.
    Requires STAFF, MANAGER, or OWNER role.
    """
    transaction, refunds, refunded_amount, remaining_amount = (
        await payment_transaction_service.get_transaction_with_refunds(
            db=db, id=transaction_id, tenant_id=current_tenant.id
        )
    )

    if not transaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Payment transaction with id {transaction_id} not found or does not belong to the tenant.",  # noqa: E501
        )

    # Create response with refunds and calculated amounts
    response = PaymentTransactionWithRefundsRead.model_validate(transaction)
    response.refunds = refunds
    response.refunded_amount = refunded_amount
    response.remaining_amount = remaining_amount

    return response
