import logging
import uuid
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import selectinload
from fastapi import HTTPException, status

from app.modules.core.functions.customizations.models.optional_group import OptionalGroup
from app.modules.core.functions.customizations.models.optional_option import OptionalOption
from app.modules.tenants.restaurants.menu.schemas.optional_group import (
    OptionalGroupCreate,
    OptionalGroupUpdate,
    OptionalGroupRead,
)
from app.modules.tenants.restaurants.menu.schemas.optional_option import (
    OptionalOptionCreate,
    OptionalOptionUpdate,
    OptionalOptionRead,
)

logger = logging.getLogger(__name__)


class MenuOptionalService:
    def __init__(self, db_session: AsyncSession):
        self.db = db_session

    async def create_optional_group(
        self, menu_item_id: uuid.UUID, group_in: OptionalGroupCreate, tenant_id: uuid.UUID, commit: bool = True
    ) -> OptionalGroupRead:
        """Create a new optional group for a menu item."""
        try:
            # Create the optional group
            db_group = OptionalGroup(
                tenant_id=tenant_id,
                menu_item_id=menu_item_id,
                name=group_in.name,
                description=group_in.description,
                min_selection=0,  # Always 0 for optionals
                max_selection=group_in.max_selection,
                display_order=group_in.display_order,
                is_active=group_in.is_active,
            )
            self.db.add(db_group)
            await self.db.flush()  # Get the ID

            # Create options if provided
            for option_data in group_in.options:
                db_option = OptionalOption(
                    tenant_id=tenant_id,
                    optional_group_id=db_group.id,
                    name=option_data.name,
                    price_adjustment=option_data.price_adjustment,
                    display_order=option_data.display_order,
                    is_active=option_data.is_active,
                )
                self.db.add(db_option)

            if commit:
                await self.db.commit()
                await self.db.refresh(db_group)

                # Load with options for return
                result = await self.db.execute(
                    select(OptionalGroup)
                    .options(selectinload(OptionalGroup.options))
                    .where(OptionalGroup.id == db_group.id)
                )
                db_group_with_options = result.scalar_one()
                return OptionalGroupRead.model_validate(db_group_with_options)
            else:
                # When not committing, still need to load options for validation
                result = await self.db.execute(
                    select(OptionalGroup)
                    .options(selectinload(OptionalGroup.options))
                    .where(OptionalGroup.id == db_group.id)
                )
                db_group_with_options = result.scalar_one()
                return OptionalGroupRead.model_validate(db_group_with_options)

        except Exception as e:
            if commit:
                await self.db.rollback()
            logger.error(f"Error creating optional group: {type(e).__name__}: {str(e)}")
            logger.error(f"Exception details: {repr(e)}")
            if hasattr(e, 'errors'):
                logger.error(f"Validation errors: {e.errors()}")
            logger.error(f"db_group_with_options type: {type(db_group_with_options)}")
            logger.error(f"db_group_with_options attributes: {dir(db_group_with_options)}")
            if hasattr(db_group_with_options, 'options'):
                logger.error(f"options type: {type(db_group_with_options.options)}")
                logger.error(f"options content: {db_group_with_options.options}")
            raise HTTPException(
                status_code=500, 
                detail=f"Internal server error: {type(e).__name__}: {str(e)}"
            )

    async def get_optional_groups_by_item(
        self, menu_item_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> List[OptionalGroupRead]:
        """Get all optional groups for a menu item."""
        try:
            result = await self.db.execute(
                select(OptionalGroup)
                .options(selectinload(OptionalGroup.options))
                .where(
                    OptionalGroup.menu_item_id == menu_item_id,
                    OptionalGroup.tenant_id == tenant_id,
                    OptionalGroup.is_active == True,
                )
                .order_by(OptionalGroup.display_order, OptionalGroup.name)
            )
            groups = result.scalars().all()
            return [OptionalGroupRead.model_validate(group) for group in groups]

        except Exception as e:
            logger.exception(f"Error fetching optional groups for item {menu_item_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def update_optional_group(
        self, group_id: uuid.UUID, group_in: OptionalGroupUpdate, tenant_id: uuid.UUID
    ) -> OptionalGroupRead:
        """Update an existing optional group."""
        try:
            result = await self.db.execute(
                select(OptionalGroup)
                .options(selectinload(OptionalGroup.options))
                .where(
                    OptionalGroup.id == group_id,
                    OptionalGroup.tenant_id == tenant_id,
                )
            )
            db_group = result.scalar_one_or_none()

            if not db_group:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Optional group not found",
                )

            # Update fields
            update_data = group_in.model_dump(exclude_unset=True)
            for key, value in update_data.items():
                setattr(db_group, key, value)

            await self.db.commit()
            await self.db.refresh(db_group)

            return OptionalGroupRead.model_validate(db_group)

        except HTTPException:
            raise
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Error updating optional group {group_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def delete_optional_group(
        self, group_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> bool:
        """Soft delete an optional group."""
        try:
            result = await self.db.execute(
                select(OptionalGroup).where(
                    OptionalGroup.id == group_id,
                    OptionalGroup.tenant_id == tenant_id,
                )
            )
            db_group = result.scalar_one_or_none()

            if not db_group:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Optional group not found",
                )

            db_group.is_active = False
            await self.db.commit()
            return True

        except HTTPException:
            raise
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Error deleting optional group {group_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )
