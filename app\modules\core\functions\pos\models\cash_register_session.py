"""Cash register session model for POS module."""

import uuid  # noqa: E402
from datetime import datetime
from sqlalchemy import Column, ForeignKey, Numeric, DateTime, Boolean, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base  # noqa: E402


class CashRegisterSession(Base):
    """
    Model for tracking cash register sessions (open/close).

    A session represents a period when a cash register is operated by a specific user.
    """

    __tablename__ = "cash_register_sessions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    cash_register_id = Column(
        UUID(as_uuid=True), ForeignKey("cash_registers.id"), nullable=False, index=True
    )
    operator_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)

    # Session status
    is_open = Column(Boolean, default=True, nullable=False)

    # Opening details
    opening_timestamp = Column(DateTime, default=datetime.utcnow, nullable=False)
    opening_balance = Column(Numeric(10, 2), nullable=False, default=0)
    opening_notes = Column(Text, nullable=True)

    # Closing details
    closing_timestamp = Column(DateTime, nullable=True)
    closing_balance = Column(Numeric(10, 2), nullable=True)
    closing_notes = Column(Text, nullable=True)

    # Expected vs actual cash (for reconciliation)
    expected_cash_amount = Column(Numeric(10, 2), nullable=True)
    actual_cash_amount = Column(Numeric(10, 2), nullable=True)
    discrepancy_amount = Column(Numeric(10, 2), nullable=True)
    discrepancy_reason = Column(Text, nullable=True)

    # Relationships
    tenant = relationship("Tenant")
    cash_register = relationship("CashRegister", back_populates="sessions")
    operator = relationship("User")
    transactions = relationship("SaleTransaction", back_populates="session")

    def __repr__(self):
        return f"<CashRegisterSession(id={self.id}, cash_register_id={self.cash_register_id}, is_open={self.is_open})>"  # noqa: E501
