'use client';

import Link from 'next/link';
import Image from 'next/image';
import { useState } from 'react';
import { Product } from '@/types/eshop';
import { useEshopCart } from '@/contexts/EshopCartContext';
import { Button } from '@/components/ui/button';
import { ShoppingCartIcon, PlusIcon } from '@heroicons/react/24/outline';

interface ProductCardProps {
  product: Product;
}

export function ProductCard({ product }: ProductCardProps) {
  const [isFavorite, setIsFavorite] = useState(product.isFavorite);
  const { addItem, isLoading } = useEshopCart();

  const toggleFavorite = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsFavorite(!isFavorite);
    // Aqui você pode adicionar a chamada à API para atualizar o status de favorito
  };

  const handleAddToCart = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (product.stock_quantity <= 0) return;

    await addItem({
      product_id: product.id,
      quantity: 1,
      selected_variants: {},
      selected_modifiers: {},
      special_instructions: ''
    });
  };

  return (
    <Link href={`/eshop/product/${product.slug || product.id}`} className="block group">
      <div className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-xl hover:-translate-y-1 transition-all duration-300 h-full flex flex-col">
        <div className="relative h-48">
          <Image
            src={product.featured_image_url || '/api/placeholder/300/300'}
            alt={product.name}
            fill
            className="object-cover bg-gray-200"
          />
          {product.sale_price && product.base_price && (
            <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded-md text-xs font-bold">
              - {Math.round(((product.base_price - product.sale_price) / product.base_price) * 100)}%
            </div>
          )}
          <button
            onClick={toggleFavorite}
            className="absolute top-2 right-2 p-2 bg-white/80 backdrop-blur-sm rounded-full text-gray-600 hover:text-red-500 transition-colors"
            aria-label="Toggle Favorite"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={`h-6 w-6 ${isFavorite ? 'text-red-500' : 'text-gray-500'}`}
              fill={isFavorite ? 'currentColor' : 'none'}
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4.318 6.318a4.5 4.5 0 016.364 0L12 7.636l1.318-1.318a4.5 4.5 0 016.364 6.364L12 20.364l-7.682-7.682a4.5 4.5 0 010-6.364z"
              />
            </svg>
          </button>
        </div>
        <div className="p-4 flex flex-col flex-grow">
          <p className="text-xs text-gray-500 mb-1">{product.category_name || 'Categoria'}</p>
          <h3 className="text-md font-semibold text-gray-800 group-hover:text-blue-600 transition-colors flex-grow">
            {product.name}
          </h3>
          <div className="flex items-center mt-2">
            <div className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <svg
                  key={i}
                  className={`h-4 w-4 ${i < Math.round(product.average_rating || 0) ? 'text-yellow-400' : 'text-gray-300'}`}
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              ))}
            </div>
            <p className="text-xs text-gray-500 ml-2">({product.review_count || 0})</p>
          </div>
          <div className="mt-4">
            {product.sale_price ? (
              <div className="flex items-baseline space-x-2">
                <p className="text-xl font-bold text-red-600">
                  R$ {product.sale_price.toFixed(2)}
                </p>
                <p className="text-sm text-gray-500 line-through">
                  R$ {product.base_price.toFixed(2)}
                </p>
              </div>
            ) : (
              <p className="text-xl font-bold text-gray-800">
                R$ {product.base_price.toFixed(2)}
              </p>
            )}
          </div>
          <p className={`mt-2 text-xs ${product.stock_quantity > 0 ? 'text-green-600' : 'text-red-600'}`}>
            {product.stock_quantity > 0 ? 'Em estoque' : 'Fora de estoque'}
          </p>

          {/* Add to Cart Button */}
          <div className="mt-4">
            <Button
              onClick={handleAddToCart}
              disabled={product.stock_quantity <= 0 || isLoading}
              className="w-full"
              size="sm"
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Adicionando...
                </div>
              ) : (
                <div className="flex items-center justify-center">
                  <ShoppingCartIcon className="h-4 w-4 mr-2" />
                  {product.stock_quantity > 0 ? 'Adicionar ao Carrinho' : 'Indisponível'}
                </div>
              )}
            </Button>
          </div>
        </div>
      </div>
    </Link>
  );
} 