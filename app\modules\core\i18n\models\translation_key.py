"""
Translation key model for the i18n module.
"""

from sqlalchemy import <PERSON>umn, Integer, String, Text
from sqlalchemy.orm import relationship

from app.db.base import Base


class TranslationKey(Base):
    """
    Model for translation keys.

    A translation key is a unique identifier for a translatable string.
    It is used to look up translations in different languages.
    """

    __tablename__ = "i18n_translation_keys"

    id = Column(Integer, primary_key=True, index=True)
    key_string = Column(String(255), unique=True, index=True, nullable=False)
    module = Column(String(100), index=True, nullable=False)
    description = Column(Text, nullable=True)

    # Relationships
    translations = relationship(
        "Translation", back_populates="key_obj", cascade="all, delete-orphan"
    )
    suggestions = relationship(
        "TranslationSuggestion", back_populates="key_obj", cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<TranslationKey(id={self.id}, key_string='{self.key_string}', module='{self.module}')>"
