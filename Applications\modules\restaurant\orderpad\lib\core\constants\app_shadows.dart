/// Application shadow constants and utilities
/// This file contains all shadow definitions, elevation styles,
/// and shadow helpers for consistent depth and visual hierarchy throughout the application.

import 'package:flutter/material.dart';
import 'app_colors.dart';

/// Material Design elevation levels
class AppElevations {
  static const double level0 = 0.0;
  static const double level1 = 1.0;
  static const double level2 = 2.0;
  static const double level3 = 3.0;
  static const double level4 = 4.0;
  static const double level5 = 5.0;
  static const double level6 = 6.0;
  static const double level8 = 8.0;
  static const double level12 = 12.0;
  static const double level16 = 16.0;
  static const double level24 = 24.0;
  
  // Component-specific elevations
  static const double card = level1;
  static const double cardHovered = level2;
  static const double cardPressed = level8;
  static const double button = level2;
  static const double buttonHovered = level4;
  static const double buttonPressed = level8;
  static const double fab = level6;
  static const double fabHovered = level8;
  static const double fabPressed = level12;
  static const double appBar = level4;
  static const double drawer = level16;
  static const double modal = level24;
  static const double bottomSheet = level16;
  static const double menu = level8;
  static const double tooltip = level4;
  static const double snackbar = level6;
  static const double dialog = level24;
  static const double navigationBar = level3;
  static const double navigationRail = level1;
  static const double searchBar = level6;
  static const double chip = level1;
  static const double chipSelected = level2;
}

/// Shadow definitions
class AppShadows {
  // Basic shadows
  static const List<BoxShadow> none = [];
  
  static const List<BoxShadow> xs = [
    BoxShadow(
      color: Color(0x0A000000),
      offset: Offset(0, 1),
      blurRadius: 2,
      spreadRadius: 0,
    ),
  ];
  
  static const List<BoxShadow> sm = [
    BoxShadow(
      color: Color(0x0F000000),
      offset: Offset(0, 1),
      blurRadius: 3,
      spreadRadius: 0,
    ),
    BoxShadow(
      color: Color(0x1A000000),
      offset: Offset(0, 1),
      blurRadius: 2,
      spreadRadius: 0,
    ),
  ];
  
  static const List<BoxShadow> md = [
    BoxShadow(
      color: Color(0x0F000000),
      offset: Offset(0, 4),
      blurRadius: 6,
      spreadRadius: -1,
    ),
    BoxShadow(
      color: Color(0x1A000000),
      offset: Offset(0, 2),
      blurRadius: 4,
      spreadRadius: -1,
    ),
  ];
  
  static const List<BoxShadow> lg = [
    BoxShadow(
      color: Color(0x0F000000),
      offset: Offset(0, 10),
      blurRadius: 15,
      spreadRadius: -3,
    ),
    BoxShadow(
      color: Color(0x1A000000),
      offset: Offset(0, 4),
      blurRadius: 6,
      spreadRadius: -2,
    ),
  ];
  
  static const List<BoxShadow> xl = [
    BoxShadow(
      color: Color(0x0F000000),
      offset: Offset(0, 20),
      blurRadius: 25,
      spreadRadius: -5,
    ),
    BoxShadow(
      color: Color(0x1A000000),
      offset: Offset(0, 10),
      blurRadius: 10,
      spreadRadius: -5,
    ),
  ];
  
  static const List<BoxShadow> xxl = [
    BoxShadow(
      color: Color(0x19000000),
      offset: Offset(0, 25),
      blurRadius: 50,
      spreadRadius: -12,
    ),
  ];
  
  // Material Design elevation shadows
  static const List<BoxShadow> elevation1 = [
    BoxShadow(
      color: Color(0x1F000000),
      offset: Offset(0, 2),
      blurRadius: 1,
      spreadRadius: -1,
    ),
    BoxShadow(
      color: Color(0x14000000),
      offset: Offset(0, 1),
      blurRadius: 1,
      spreadRadius: 0,
    ),
    BoxShadow(
      color: Color(0x12000000),
      offset: Offset(0, 1),
      blurRadius: 3,
      spreadRadius: 0,
    ),
  ];
  
  static const List<BoxShadow> elevation2 = [
    BoxShadow(
      color: Color(0x1F000000),
      offset: Offset(0, 3),
      blurRadius: 1,
      spreadRadius: -2,
    ),
    BoxShadow(
      color: Color(0x14000000),
      offset: Offset(0, 2),
      blurRadius: 2,
      spreadRadius: 0,
    ),
    BoxShadow(
      color: Color(0x12000000),
      offset: Offset(0, 1),
      blurRadius: 5,
      spreadRadius: 0,
    ),
  ];
  
  static const List<BoxShadow> elevation3 = [
    BoxShadow(
      color: Color(0x1F000000),
      offset: Offset(0, 3),
      blurRadius: 3,
      spreadRadius: -2,
    ),
    BoxShadow(
      color: Color(0x14000000),
      offset: Offset(0, 3),
      blurRadius: 4,
      spreadRadius: 0,
    ),
    BoxShadow(
      color: Color(0x12000000),
      offset: Offset(0, 1),
      blurRadius: 8,
      spreadRadius: 0,
    ),
  ];
  
  static const List<BoxShadow> elevation4 = [
    BoxShadow(
      color: Color(0x1F000000),
      offset: Offset(0, 2),
      blurRadius: 4,
      spreadRadius: -1,
    ),
    BoxShadow(
      color: Color(0x14000000),
      offset: Offset(0, 4),
      blurRadius: 5,
      spreadRadius: 0,
    ),
    BoxShadow(
      color: Color(0x12000000),
      offset: Offset(0, 1),
      blurRadius: 10,
      spreadRadius: 0,
    ),
  ];
  
  static const List<BoxShadow> elevation6 = [
    BoxShadow(
      color: Color(0x1F000000),
      offset: Offset(0, 3),
      blurRadius: 5,
      spreadRadius: -1,
    ),
    BoxShadow(
      color: Color(0x14000000),
      offset: Offset(0, 6),
      blurRadius: 10,
      spreadRadius: 0,
    ),
    BoxShadow(
      color: Color(0x12000000),
      offset: Offset(0, 1),
      blurRadius: 18,
      spreadRadius: 0,
    ),
  ];
  
  static const List<BoxShadow> elevation8 = [
    BoxShadow(
      color: Color(0x1F000000),
      offset: Offset(0, 5),
      blurRadius: 5,
      spreadRadius: -3,
    ),
    BoxShadow(
      color: Color(0x14000000),
      offset: Offset(0, 8),
      blurRadius: 10,
      spreadRadius: 1,
    ),
    BoxShadow(
      color: Color(0x12000000),
      offset: Offset(0, 3),
      blurRadius: 14,
      spreadRadius: 2,
    ),
  ];
  
  static const List<BoxShadow> elevation12 = [
    BoxShadow(
      color: Color(0x1F000000),
      offset: Offset(0, 7),
      blurRadius: 8,
      spreadRadius: -4,
    ),
    BoxShadow(
      color: Color(0x14000000),
      offset: Offset(0, 12),
      blurRadius: 17,
      spreadRadius: 2,
    ),
    BoxShadow(
      color: Color(0x12000000),
      offset: Offset(0, 5),
      blurRadius: 22,
      spreadRadius: 4,
    ),
  ];
  
  static const List<BoxShadow> elevation16 = [
    BoxShadow(
      color: Color(0x1F000000),
      offset: Offset(0, 8),
      blurRadius: 10,
      spreadRadius: -5,
    ),
    BoxShadow(
      color: Color(0x14000000),
      offset: Offset(0, 16),
      blurRadius: 24,
      spreadRadius: 2,
    ),
    BoxShadow(
      color: Color(0x12000000),
      offset: Offset(0, 6),
      blurRadius: 30,
      spreadRadius: 5,
    ),
  ];
  
  static const List<BoxShadow> elevation24 = [
    BoxShadow(
      color: Color(0x1F000000),
      offset: Offset(0, 11),
      blurRadius: 15,
      spreadRadius: -7,
    ),
    BoxShadow(
      color: Color(0x14000000),
      offset: Offset(0, 24),
      blurRadius: 38,
      spreadRadius: 3,
    ),
    BoxShadow(
      color: Color(0x12000000),
      offset: Offset(0, 9),
      blurRadius: 46,
      spreadRadius: 8,
    ),
  ];
  
  // Component-specific shadows
  static const List<BoxShadow> card = elevation1;
  static const List<BoxShadow> cardHovered = elevation2;
  static const List<BoxShadow> cardPressed = elevation8;
  
  static const List<BoxShadow> button = elevation2;
  static const List<BoxShadow> buttonHovered = elevation4;
  static const List<BoxShadow> buttonPressed = elevation8;
  
  static const List<BoxShadow> fab = elevation6;
  static const List<BoxShadow> fabHovered = elevation8;
  static const List<BoxShadow> fabPressed = elevation12;
  
  static const List<BoxShadow> appBar = elevation4;
  static const List<BoxShadow> drawer = elevation16;
  static const List<BoxShadow> modal = elevation24;
  static const List<BoxShadow> bottomSheet = elevation16;
  static const List<BoxShadow> menu = elevation8;
  static const List<BoxShadow> tooltip = elevation4;
  static const List<BoxShadow> snackbar = elevation6;
  static const List<BoxShadow> dialog = elevation24;
  
  // Colored shadows
  static const List<BoxShadow> primary = [
    BoxShadow(
      color: Color(0x40007AFF),
      offset: Offset(0, 4),
      blurRadius: 12,
      spreadRadius: 0,
    ),
  ];
  
  static const List<BoxShadow> success = [
    BoxShadow(
      color: Color(0x4034C759),
      offset: Offset(0, 4),
      blurRadius: 12,
      spreadRadius: 0,
    ),
  ];
  
  static const List<BoxShadow> error = [
    BoxShadow(
      color: Color(0x40FF3B30),
      offset: Offset(0, 4),
      blurRadius: 12,
      spreadRadius: 0,
    ),
  ];
  
  static const List<BoxShadow> warning = [
    BoxShadow(
      color: Color(0x40FF9500),
      offset: Offset(0, 4),
      blurRadius: 12,
      spreadRadius: 0,
    ),
  ];
  
  static const List<BoxShadow> info = [
    BoxShadow(
      color: Color(0x40007AFF),
      offset: Offset(0, 4),
      blurRadius: 12,
      spreadRadius: 0,
    ),
  ];
  
  // Special effect shadows
  static const List<BoxShadow> glow = [
    BoxShadow(
      color: Color(0x40007AFF),
      offset: Offset(0, 0),
      blurRadius: 20,
      spreadRadius: 0,
    ),
  ];
  
  static const List<BoxShadow> inner = [
    BoxShadow(
      color: Color(0x1A000000),
      offset: Offset(0, 2),
      blurRadius: 4,
      spreadRadius: -2,
    ),
  ];
  
  static const List<BoxShadow> neuomorphism = [
    BoxShadow(
      color: Color(0x1AFFFFFF),
      offset: Offset(-2, -2),
      blurRadius: 6,
      spreadRadius: 0,
    ),
    BoxShadow(
      color: Color(0x1A000000),
      offset: Offset(2, 2),
      blurRadius: 6,
      spreadRadius: 0,
    ),
  ];
  
  static const List<BoxShadow> neuomorphismPressed = [
    BoxShadow(
      color: Color(0x1A000000),
      offset: Offset(-1, -1),
      blurRadius: 3,
      spreadRadius: 0,
    ),
    BoxShadow(
      color: Color(0x1AFFFFFF),
      offset: Offset(1, 1),
      blurRadius: 3,
      spreadRadius: 0,
    ),
  ];
}

/// Dark theme shadows
class AppShadowsDark {
  static const List<BoxShadow> xs = [
    BoxShadow(
      color: Color(0x1A000000),
      offset: Offset(0, 1),
      blurRadius: 2,
      spreadRadius: 0,
    ),
  ];
  
  static const List<BoxShadow> sm = [
    BoxShadow(
      color: Color(0x26000000),
      offset: Offset(0, 1),
      blurRadius: 3,
      spreadRadius: 0,
    ),
    BoxShadow(
      color: Color(0x33000000),
      offset: Offset(0, 1),
      blurRadius: 2,
      spreadRadius: 0,
    ),
  ];
  
  static const List<BoxShadow> md = [
    BoxShadow(
      color: Color(0x26000000),
      offset: Offset(0, 4),
      blurRadius: 6,
      spreadRadius: -1,
    ),
    BoxShadow(
      color: Color(0x33000000),
      offset: Offset(0, 2),
      blurRadius: 4,
      spreadRadius: -1,
    ),
  ];
  
  static const List<BoxShadow> lg = [
    BoxShadow(
      color: Color(0x26000000),
      offset: Offset(0, 10),
      blurRadius: 15,
      spreadRadius: -3,
    ),
    BoxShadow(
      color: Color(0x33000000),
      offset: Offset(0, 4),
      blurRadius: 6,
      spreadRadius: -2,
    ),
  ];
  
  static const List<BoxShadow> xl = [
    BoxShadow(
      color: Color(0x26000000),
      offset: Offset(0, 20),
      blurRadius: 25,
      spreadRadius: -5,
    ),
    BoxShadow(
      color: Color(0x33000000),
      offset: Offset(0, 10),
      blurRadius: 10,
      spreadRadius: -5,
    ),
  ];
  
  static const List<BoxShadow> neuomorphism = [
    BoxShadow(
      color: Color(0x1A333333),
      offset: Offset(-2, -2),
      blurRadius: 6,
      spreadRadius: 0,
    ),
    BoxShadow(
      color: Color(0x33000000),
      offset: Offset(2, 2),
      blurRadius: 6,
      spreadRadius: 0,
    ),
  ];
}

/// Shadow utilities and helpers
class ShadowUtils {
  /// Get shadow based on elevation level
  static List<BoxShadow> getElevationShadow(double elevation) {
    if (elevation <= 0) return AppShadows.none;
    if (elevation <= 1) return AppShadows.elevation1;
    if (elevation <= 2) return AppShadows.elevation2;
    if (elevation <= 3) return AppShadows.elevation3;
    if (elevation <= 4) return AppShadows.elevation4;
    if (elevation <= 6) return AppShadows.elevation6;
    if (elevation <= 8) return AppShadows.elevation8;
    if (elevation <= 12) return AppShadows.elevation12;
    if (elevation <= 16) return AppShadows.elevation16;
    return AppShadows.elevation24;
  }
  
  /// Get shadow based on theme brightness
  static List<BoxShadow> getThemeShadow(Brightness brightness, String size) {
    final isDark = brightness == Brightness.dark;
    
    switch (size.toLowerCase()) {
      case 'xs':
        return isDark ? AppShadowsDark.xs : AppShadows.xs;
      case 'sm':
        return isDark ? AppShadowsDark.sm : AppShadows.sm;
      case 'md':
        return isDark ? AppShadowsDark.md : AppShadows.md;
      case 'lg':
        return isDark ? AppShadowsDark.lg : AppShadows.lg;
      case 'xl':
        return isDark ? AppShadowsDark.xl : AppShadows.xl;
      default:
        return isDark ? AppShadowsDark.md : AppShadows.md;
    }
  }
  
  /// Create custom shadow
  static List<BoxShadow> createShadow({
    required Color color,
    required Offset offset,
    required double blurRadius,
    double spreadRadius = 0,
  }) {
    return [
      BoxShadow(
        color: color,
        offset: offset,
        blurRadius: blurRadius,
        spreadRadius: spreadRadius,
      ),
    ];
  }
  
  /// Create colored shadow
  static List<BoxShadow> createColoredShadow({
    required Color color,
    double opacity = 0.25,
    Offset offset = const Offset(0, 4),
    double blurRadius = 12,
    double spreadRadius = 0,
  }) {
    return [
      BoxShadow(
        color: color.withValues(alpha: opacity),
        offset: offset,
        blurRadius: blurRadius,
        spreadRadius: spreadRadius,
      ),
    ];
  }
  
  /// Create glow effect
  static List<BoxShadow> createGlow({
    required Color color,
    double opacity = 0.5,
    double blurRadius = 20,
    double spreadRadius = 0,
  }) {
    return [
      BoxShadow(
        color: color.withValues(alpha: opacity),
        offset: Offset.zero,
        blurRadius: blurRadius,
        spreadRadius: spreadRadius,
      ),
    ];
  }
  
  /// Create multiple shadows
  static List<BoxShadow> createMultipleShadows(List<Map<String, dynamic>> shadowConfigs) {
    return shadowConfigs.map((config) {
      return BoxShadow(
        color: config['color'] ?? Colors.black.withValues(alpha: 0.1),
        offset: config['offset'] ?? const Offset(0, 2),
        blurRadius: config['blurRadius'] ?? 4.0,
        spreadRadius: config['spreadRadius'] ?? 0.0,
      );
    }).toList();
  }
  
  /// Modify shadow opacity
  static List<BoxShadow> withValues(alpha: List<BoxShadow> shadows, double opacity) {
    return shadows.map((shadow) {
      return shadow.copyWith(
        color: shadow.color.withValues(alpha: shadow.color.opacity * opacity),
      );
    }).toList();
  }
  
  /// Scale shadow
  static List<BoxShadow> scale(List<BoxShadow> shadows, double factor) {
    return shadows.map((shadow) {
      return shadow.copyWith(
        offset: shadow.offset * factor,
        blurRadius: shadow.blurRadius * factor,
        spreadRadius: shadow.spreadRadius * factor,
      );
    }).toList();
  }
  
  /// Combine multiple shadow lists
  static List<BoxShadow> combine(List<List<BoxShadow>> shadowLists) {
    final combined = <BoxShadow>[];
    for (final shadows in shadowLists) {
      combined.addAll(shadows);
    }
    return combined;
  }
  
  /// Get component shadow
  static List<BoxShadow> getComponentShadow(String component, {String state = 'default'}) {
    switch (component.toLowerCase()) {
      case 'card':
        switch (state) {
          case 'hovered':
            return AppShadows.cardHovered;
          case 'pressed':
            return AppShadows.cardPressed;
          default:
            return AppShadows.card;
        }
      case 'button':
        switch (state) {
          case 'hovered':
            return AppShadows.buttonHovered;
          case 'pressed':
            return AppShadows.buttonPressed;
          default:
            return AppShadows.button;
        }
      case 'fab':
        switch (state) {
          case 'hovered':
            return AppShadows.fabHovered;
          case 'pressed':
            return AppShadows.fabPressed;
          default:
            return AppShadows.fab;
        }
      case 'appbar':
        return AppShadows.appBar;
      case 'drawer':
        return AppShadows.drawer;
      case 'modal':
        return AppShadows.modal;
      case 'bottomsheet':
        return AppShadows.bottomSheet;
      case 'menu':
        return AppShadows.menu;
      case 'tooltip':
        return AppShadows.tooltip;
      case 'snackbar':
        return AppShadows.snackbar;
      case 'dialog':
        return AppShadows.dialog;
      default:
        return AppShadows.md;
    }
  }
  
  /// Get status shadow
  static List<BoxShadow> getStatusShadow(String status) {
    switch (status.toLowerCase()) {
      case 'success':
        return AppShadows.success;
      case 'error':
        return AppShadows.error;
      case 'warning':
        return AppShadows.warning;
      case 'info':
        return AppShadows.info;
      case 'primary':
        return AppShadows.primary;
      default:
        return AppShadows.md;
    }
  }
  
  /// Create animated shadow
  static List<BoxShadow> animatedShadow({
    required List<BoxShadow> fromShadow,
    required List<BoxShadow> toShadow,
    required double animationValue,
  }) {
    if (fromShadow.length != toShadow.length) {
      return fromShadow;
    }
    
    final animatedShadows = <BoxShadow>[];
    for (int i = 0; i < fromShadow.length; i++) {
      final from = fromShadow[i];
      final to = toShadow[i];
      
      animatedShadows.add(
        BoxShadow(
          color: Color.lerp(from.color, to.color, animationValue)!,
          offset: Offset.lerp(from.offset, to.offset, animationValue)!,
          blurRadius: lerpDouble(from.blurRadius, to.blurRadius, animationValue)!,
          spreadRadius: lerpDouble(from.spreadRadius, to.spreadRadius, animationValue)!,
        ),
      );
    }
    
    return animatedShadows;
  }
  
  /// Create directional shadow
  static List<BoxShadow> directionalShadow({
    required Color color,
    required double angle, // in radians
    required double distance,
    double blurRadius = 4,
    double spreadRadius = 0,
    double opacity = 0.25,
  }) {
    final x = distance * cos(angle);
    final y = distance * sin(angle);
    
    return [
      BoxShadow(
        color: color.withValues(alpha: opacity),
        offset: Offset(x, y),
        blurRadius: blurRadius,
        spreadRadius: spreadRadius,
      ),
    ];
  }
  
  /// Create neumorphism shadow
  static List<BoxShadow> neumorphismShadow({
    bool isDark = false,
    bool isPressed = false,
    double intensity = 1.0,
  }) {
    if (isDark) {
      return isPressed
          ? scale(AppShadowsDark.neuomorphism, intensity)
          : scale(AppShadowsDark.neuomorphism, intensity);
    } else {
      return isPressed
          ? scale(AppShadows.neuomorphismPressed, intensity)
          : scale(AppShadows.neuomorphism, intensity);
    }
  }
}

/// Shadow animation helpers
class ShadowAnimation {
  /// Create pulsing shadow animation
  static List<BoxShadow> pulsingShadow({
    required List<BoxShadow> baseShadow,
    required double animationValue,
    double pulseIntensity = 0.5,
  }) {
    final pulseValue = (sin(animationValue * 2 * pi) + 1) / 2;
    final intensity = 1.0 + (pulseValue * pulseIntensity);
    return ShadowUtils.scale(baseShadow, intensity);
  }
  
  /// Create floating shadow animation
  static List<BoxShadow> floatingShadow({
    required List<BoxShadow> baseShadow,
    required double animationValue,
    double floatDistance = 2.0,
  }) {
    final floatValue = sin(animationValue * 2 * pi) * floatDistance;
    return baseShadow.map((shadow) {
      return shadow.copyWith(
        offset: shadow.offset + Offset(0, floatValue),
      );
    }).toList();
  }
  
  /// Create ripple shadow animation
  static List<BoxShadow> rippleShadow({
    required Color color,
    required double animationValue,
    double maxRadius = 50,
    double opacity = 0.3,
  }) {
    final radius = animationValue * maxRadius;
    final currentOpacity = opacity * (1 - animationValue);
    
    return [
      BoxShadow(
        color: color.withValues(alpha: currentOpacity),
        offset: Offset.zero,
        blurRadius: radius,
        spreadRadius: radius * 0.5,
      ),
    ];
  }
}