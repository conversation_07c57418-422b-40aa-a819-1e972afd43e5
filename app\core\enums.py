from enum import Enum


class OrderSource(str, Enum):
    WEB = "WEB"
    KIOSK = "KIOSK"
    APP = "APP"
    POS = "POS"
    UNKNOWN = "UNKNOWN"  # Default value or for unspecified cases


class MarketType(str, Enum):
    """Enum unificado para tipos de mercado usado em todo o sistema EShop."""
    PUBLIC = "public"  # Mercado público geral
    B2B = "b2b"        # Business to Business
    B2C = "b2c"        # Business to Consumer
    ALL = "all"        # Todos os tipos
    BOTH = "both"      # B2B e B2C (alias para ALL)


# Adicionar outros Enums globais aqui conforme necessário
