'use client';

import React, { createContext, useContext, ReactNode } from 'react';
import { useMenuManagement } from '@/hooks/useMenuManagement';
import type {
  DigitalMenu,
  MenuCategory,
  MenuItem,
  MenuCategoryCreate,
  MenuCategoryUpdate,
  MenuItemCreate,
  MenuItemUpdate,
} from '@/hooks/useMenuManagement';

// Define the context type
interface MenuContextType {
  // State
  digitalMenus: DigitalMenu[];
  selectedMenu: DigitalMenu | null;
  categories: MenuCategory[];
  items: MenuItem[];
  loading: boolean;
  error: string | null;

  // Digital Menu Operations
  loadDigitalMenus: () => Promise<void>;
  createDigitalMenu: (menuData: { name: string; description?: string }) => Promise<DigitalMenu>;
  selectDigitalMenu: (menu: DigitalMenu | null) => Promise<void>;
  updateDigitalMenu: (menuId: string, menuData: { name: string; description?: string }) => Promise<DigitalMenu>;
  deleteDigitalMenu: (menuId: string) => Promise<void>;

  // Category Operations
  createCategory: (categoryData: MenuCategoryCreate & { digital_menu_id?: string }) => Promise<MenuCategory>;
  updateCategory: (categoryId: string, categoryData: MenuCategoryUpdate) => Promise<MenuCategory>;
  deleteCategory: (categoryId: string) => Promise<void>;
  reorderCategories: (reorderedCategories: MenuCategory[]) => Promise<void>;

  // Item Operations
  createItem: (itemData: MenuItemCreate & { digital_menu_id?: string }) => Promise<MenuItem>;
  updateItem: (itemId: string, itemData: MenuItemUpdate) => Promise<MenuItem>;
  deleteItem: (itemId: string) => Promise<void>;
  reorderItems: (categoryId: string, reorderedItems: MenuItem[]) => Promise<void>;

  // Data fetching
  fetchCategories: () => Promise<void>;
  fetchItems: () => Promise<void>;
}

// Create the context
const MenuContext = createContext<MenuContextType | undefined>(undefined);

// Provider component
interface MenuProviderProps {
  children: ReactNode;
}

export function MenuProvider({ children }: MenuProviderProps) {
  const menuManagement = useMenuManagement();

  return (
    <MenuContext.Provider value={menuManagement}>
      {children}
    </MenuContext.Provider>
  );
}

// Hook to use the menu context
export function useMenuContext(): MenuContextType {
  const context = useContext(MenuContext);
  if (context === undefined) {
    throw new Error('useMenuContext must be used within a MenuProvider');
  }
  return context;
}

// Export types for convenience
export type {
  DigitalMenu,
  MenuCategory,
  MenuItem,
  MenuCategoryCreate,
  MenuCategoryUpdate,
  MenuItemCreate,
  MenuItemUpdate,
};
