'use client';

import React, { useState } from 'react';
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  DocumentTextIcon,
  PlusIcon,
  XMarkIcon,
  CloudArrowUpIcon
} from '@heroicons/react/24/outline';
import { useTShopCart } from '@/contexts/TShopCartContext';
import { toast } from 'sonner';

interface BulkOrderModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface BulkOrderItem {
  id: string;
  product_name: string;
  quantity: number;
  notes?: string;
}

export function BulkOrderModal({ isOpen, onClose }: BulkOrderModalProps) {
  const { requestBulkQuote } = useTShopCart();
  const [items, setItems] = useState<BulkOrderItem[]>([
    { id: '1', product_name: '', quantity: 1 }
  ]);
  const [notes, setNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const addItem = () => {
    const newItem: BulkOrderItem = {
      id: Date.now().toString(),
      product_name: '',
      quantity: 1
    };
    setItems([...items, newItem]);
  };

  const removeItem = (id: string) => {
    if (items.length > 1) {
      setItems(items.filter(item => item.id !== id));
    }
  };

  const updateItem = (id: string, field: keyof BulkOrderItem, value: string | number) => {
    setItems(items.map(item => 
      item.id === id ? { ...item, [field]: value } : item
    ));
  };

  const handleSubmit = async () => {
    const validItems = items.filter(item => item.product_name.trim() && item.quantity > 0);
    
    if (validItems.length === 0) {
      toast.error('Adicione pelo menos um item válido');
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Convert to the format expected by the API
      const bulkItems = validItems.map(item => ({
        product_id: item.id, // This would need to be resolved from product name
        quantity: item.quantity
      }));

      await requestBulkQuote(bulkItems);
      toast.success('Solicitação de cotação em lote enviada com sucesso!');
      onClose();
      
      // Reset form
      setItems([{ id: '1', product_name: '', quantity: 1 }]);
      setNotes('');
    } catch (error) {
      toast.error('Falha ao enviar solicitação de cotação');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <DocumentTextIcon className="h-5 w-5 mr-2" />
            Solicitação de Cotação em Lote
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Instructions */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-start space-x-3">
                <CloudArrowUpIcon className="h-5 w-5 text-blue-600 mt-0.5" />
                <div className="text-sm">
                  <p className="font-medium text-gray-900 mb-1">
                    Como funciona a cotação em lote:
                  </p>
                  <ul className="text-gray-600 space-y-1">
                    <li>• Liste os produtos que você precisa</li>
                    <li>• Nossos fornecedores receberão sua solicitação</li>
                    <li>• Você receberá cotações competitivas em até 24h</li>
                    <li>• Compare preços e escolha a melhor oferta</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Items List */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Itens para Cotação</h3>
              <Button onClick={addItem} size="sm" variant="outline">
                <PlusIcon className="h-4 w-4 mr-2" />
                Adicionar Item
              </Button>
            </div>

            {items.map((item, index) => (
              <Card key={item.id}>
                <CardContent className="p-4">
                  <div className="flex items-start space-x-4">
                    <div className="flex-1 space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Nome do Produto
                        </label>
                        <Input
                          placeholder="Ex: Notebook Dell Inspiron 15..."
                          value={item.product_name}
                          onChange={(e) => updateItem(item.id, 'product_name', e.target.value)}
                        />
                      </div>
                      
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Quantidade
                          </label>
                          <Input
                            type="number"
                            min="1"
                            value={item.quantity}
                            onChange={(e) => updateItem(item.id, 'quantity', parseInt(e.target.value) || 1)}
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Item #{index + 1}
                          </label>
                          <Badge variant="outline" className="mt-1">
                            {item.quantity} unidade{item.quantity !== 1 ? 's' : ''}
                          </Badge>
                        </div>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Observações (opcional)
                        </label>
                        <Input
                          placeholder="Especificações, cor, modelo..."
                          value={item.notes || ''}
                          onChange={(e) => updateItem(item.id, 'notes', e.target.value)}
                        />
                      </div>
                    </div>
                    
                    {items.length > 1 && (
                      <Button
                        onClick={() => removeItem(item.id)}
                        size="sm"
                        variant="ghost"
                        className="text-red-600 hover:text-red-700 mt-6"
                      >
                        <XMarkIcon className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* General Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Observações Gerais
            </label>
            <Textarea
              placeholder="Informações adicionais sobre sua solicitação, prazo de entrega desejado, condições especiais..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={4}
            />
          </div>

          {/* Summary */}
          <Card className="bg-gray-50">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Resumo da Solicitação</p>
                  <p className="text-sm text-gray-600">
                    {items.filter(item => item.product_name.trim()).length} itens para cotação
                  </p>
                </div>
                <Badge variant="secondary">
                  Total: {items.reduce((sum, item) => sum + item.quantity, 0)} unidades
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex space-x-3">
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting || items.filter(item => item.product_name.trim()).length === 0}
              className="flex-1"
            >
              {isSubmitting ? 'Enviando...' : 'Enviar Solicitação'}
            </Button>
            
            <Button
              onClick={onClose}
              variant="outline"
              disabled={isSubmitting}
            >
              Cancelar
            </Button>
          </div>

          {/* Footer Note */}
          <div className="text-xs text-gray-500 text-center">
            Você receberá as cotações por email e poderá acompanhar o status na aba "Pedidos"
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
