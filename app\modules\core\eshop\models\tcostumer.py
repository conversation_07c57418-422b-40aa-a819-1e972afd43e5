"""
TCostumer Model for EShop B2B System
====================================

Modelo especializado para clientes B2B (TCostumer) com campos específicos
para gestão de crédito, verificação empresarial e termos de pagamento.

Este modelo estende o sistema de associação tenant-user existente,
fornecendo campos adicionais específicos para operações B2B.
"""

import uuid
import enum
from datetime import datetime, timedelta
from typing import TYPE_CHECKING, Optional
from decimal import Decimal

from sqlalchemy import (
    Column, String, ForeignKey, Text, Enum, DateTime,
    Boolean, Index, func, Numeric, JSON, Integer
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, Mapped, mapped_column

from app.db.base import Base

# Use TYPE_CHECKING to handle circular dependencies
if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.core.users.models.user import User
    from app.modules.core.users.models.tenant_user_association import TenantUserAssociation


class TCostumerStatus(str, enum.Enum):
    """Status do cliente B2B."""
    
    PENDING = "pending"
    ACTIVE = "active"
    SUSPENDED = "suspended"
    INACTIVE = "inactive"
    REJECTED = "rejected"


class BusinessType(str, enum.Enum):
    """Tipo de negócio do cliente B2B."""
    
    CORPORATION = "corporation"
    LLC = "llc"
    PARTNERSHIP = "partnership"
    SOLE_PROPRIETORSHIP = "sole_proprietorship"
    NONPROFIT = "nonprofit"
    GOVERNMENT = "government"
    OTHER = "other"


class PaymentTerms(str, enum.Enum):
    """Termos de pagamento para clientes B2B."""
    
    IMMEDIATE = "immediate"
    NET_15 = "net_15"
    NET_30 = "net_30"
    NET_45 = "net_45"
    NET_60 = "net_60"
    CUSTOM = "custom"


class TCostumer(Base):
    """
    Modelo para clientes B2B (TCostumer).
    
    Estende o sistema de associação tenant-user com campos específicos
    para gestão de clientes empresariais, incluindo verificação de negócio,
    limites de crédito e termos de pagamento personalizados.
    """
    
    __tablename__ = "eshop_tcostumers"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("tenants.id"), 
        nullable=False, 
        index=True
    )
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        index=True
    )
    
    # Referência à associação tenant-user (onde estão role, pricing_tier, etc.)
    tenant_user_association_id = Column(
        UUID(as_uuid=True),
        nullable=True,  # Pode ser preenchido após criação da associação
        index=True
    )
    
    # Status e configurações
    status = Column(
        Enum(TCostumerStatus), 
        default=TCostumerStatus.PENDING, 
        nullable=False, 
        index=True
    )
    
    # Informações empresariais
    company_name = Column(String(255), nullable=False, index=True)
    business_type = Column(Enum(BusinessType), nullable=False)
    tax_id = Column(String(50), nullable=False, unique=True, index=True)  # CNPJ/EIN
    business_registration_number = Column(String(100), nullable=True)
    
    # Endereço empresarial
    business_address = Column(JSON, nullable=True)
    
    # Informações de contato empresarial
    business_phone = Column(String(20), nullable=True)
    business_email = Column(String(255), nullable=True)
    website = Column(String(255), nullable=True)
    
    # Gestão de crédito
    credit_limit = Column(Numeric(12, 2), default=0.00, nullable=False)
    available_credit = Column(Numeric(12, 2), default=0.00, nullable=False)
    credit_used = Column(Numeric(12, 2), default=0.00, nullable=False)
    
    # Termos de pagamento
    payment_terms = Column(Enum(PaymentTerms), default=PaymentTerms.NET_30, nullable=False)
    custom_payment_days = Column(Integer, nullable=True)  # Para CUSTOM payment terms
    
    # Desconto padrão
    default_discount_rate = Column(Numeric(5, 2), default=0.00, nullable=False)
    
    # Verificação empresarial
    business_verification_status = Column(
        String(20), 
        default='pending', 
        nullable=False, 
        index=True
    )  # pending, verified, rejected
    business_verification_date = Column(DateTime, nullable=True)
    business_verification_notes = Column(Text, nullable=True)
    
    # Documentos de verificação (URLs ou IDs de arquivos)
    verification_documents = Column(JSON, nullable=True)
    
    # Informações financeiras
    annual_revenue = Column(Numeric(15, 2), nullable=True)
    employee_count = Column(Integer, nullable=True)
    years_in_business = Column(Integer, nullable=True)
    
    # Estatísticas de compras
    total_orders = Column(Integer, default=0, nullable=False)
    total_spent = Column(Numeric(12, 2), default=0.00, nullable=False)
    average_order_value = Column(Numeric(10, 2), default=0.00, nullable=False)
    last_order_date = Column(DateTime, nullable=True)
    
    # Configurações de conta
    auto_approve_orders = Column(Boolean, default=False, nullable=False)
    require_po_number = Column(Boolean, default=False, nullable=False)
    
    # Informações do representante de vendas
    sales_rep_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=True,
        index=True
    )
    
    # Metadados adicionais
    metadata = Column(JSON, nullable=True)
    notes = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )
    approved_at = Column(DateTime, nullable=True)
    last_activity_at = Column(DateTime, nullable=True)
    
    # Relacionamentos
    tenant = relationship("Tenant")
    user = relationship("User", foreign_keys=[user_id])
    sales_rep = relationship("User", foreign_keys=[sales_rep_id])
    
    # Índices para performance
    __table_args__ = (
        Index("ix_tcostumer_tenant_user", "tenant_id", "user_id"),
        Index("ix_tcostumer_status_tenant", "status", "tenant_id"),
        Index("ix_tcostumer_company_name", "company_name"),
        Index("ix_tcostumer_verification_status", "business_verification_status"),
        Index("ix_tcostumer_sales_rep", "sales_rep_id"),
        Index("ix_tcostumer_created", "created_at"),
    )
    
    def __repr__(self):
        return (
            f"<TCostumer(id={self.id}, "
            f"company_name='{self.company_name}', "
            f"status='{self.status}', "
            f"credit_limit={self.credit_limit})>"
        )
    
    @property
    def is_verified(self) -> bool:
        """Verifica se o cliente está verificado."""
        return self.business_verification_status == 'verified'
    
    @property
    def is_active(self) -> bool:
        """Verifica se o cliente está ativo."""
        return self.status == TCostumerStatus.ACTIVE
    
    @property
    def credit_utilization_rate(self) -> float:
        """Calcula a taxa de utilização do crédito."""
        if self.credit_limit == 0:
            return 0.0
        return float(self.credit_used / self.credit_limit * 100)
    
    @property
    def has_available_credit(self) -> bool:
        """Verifica se há crédito disponível."""
        return self.available_credit > 0
    
    def update_credit_usage(self, amount: Decimal, operation: str = 'add'):
        """
        Atualiza o uso de crédito.
        
        Args:
            amount: Valor a ser adicionado ou subtraído
            operation: 'add' para usar crédito, 'subtract' para liberar
        """
        if operation == 'add':
            self.credit_used += amount
            self.available_credit = self.credit_limit - self.credit_used
        elif operation == 'subtract':
            self.credit_used = max(0, self.credit_used - amount)
            self.available_credit = self.credit_limit - self.credit_used
        
        self.updated_at = datetime.utcnow()
    
    def approve_business(self, approved_by_id: uuid.UUID, notes: str = None):
        """
        Aprova o negócio do cliente.
        
        Args:
            approved_by_id: ID do usuário que aprovou
            notes: Notas da aprovação
        """
        self.status = TCostumerStatus.ACTIVE
        self.business_verification_status = 'verified'
        self.business_verification_date = datetime.utcnow()
        self.approved_at = datetime.utcnow()
        
        if notes:
            self.business_verification_notes = notes
        
        self.updated_at = datetime.utcnow()
    
    def reject_business(self, rejected_by_id: uuid.UUID, reason: str):
        """
        Rejeita o negócio do cliente.
        
        Args:
            rejected_by_id: ID do usuário que rejeitou
            reason: Motivo da rejeição
        """
        self.status = TCostumerStatus.REJECTED
        self.business_verification_status = 'rejected'
        self.business_verification_date = datetime.utcnow()
        self.business_verification_notes = reason
        self.updated_at = datetime.utcnow()
    
    def update_purchase_stats(self, order_value: Decimal):
        """
        Atualiza estatísticas de compras.
        
        Args:
            order_value: Valor do pedido
        """
        self.total_orders += 1
        self.total_spent += order_value
        self.average_order_value = self.total_spent / self.total_orders
        self.last_order_date = datetime.utcnow()
        self.last_activity_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
