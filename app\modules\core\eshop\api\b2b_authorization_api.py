"""
B2B Authorization API
====================

API endpoints para o sistema de autorização B2B.
"""

import uuid
from typing import Optional, List
from math import ceil

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.user_auth import get_current_user
from app.modules.core.users.models.user import User
from app.modules.core.eshop.services.b2b_authorization_service import B2BAuthorizationService
from app.modules.core.eshop.schemas.b2b_authorization import (
    B2BAuthorizationRequestCreate,
    B2BAuthorizationRequestUpdate,
    B2BAuthorizationRequestResponse,
    B2BAuthorizationRequestListResponse,
    B2BAuthorizationApproval,
    B2BAuthorizationRejection,
    B2BAuthorizationDocumentUpload,
    B2BAuthorizationDocumentResponse,
    B2BAuthorizationRequestFilter,
    B2BAuthorizationStats,
    B2BAuthorizationSettingsUpdate,
    B2BAuthorizationSettingsResponse,
    DocumentType,
    AuthorizationStatus,
    EntityType,
    ApprovalType
)

router = APIRouter(prefix="/b2b-authorization", tags=["B2B Authorization"])


@router.post("/requests", response_model=B2BAuthorizationRequestResponse)
async def create_authorization_request(
    request_data: B2BAuthorizationRequestCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Cria uma nova solicitação de autorização B2B."""
    
    service = B2BAuthorizationService(db)
    
    authorization_request = await service.create_authorization_request(
        request_data=request_data,
        user_id=current_user.id,
        tenant_id=current_user.tenant_id
    )
    
    return authorization_request


@router.get("/requests", response_model=B2BAuthorizationRequestListResponse)
async def list_authorization_requests(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    status: Optional[List[AuthorizationStatus]] = Query(None),
    entity_type: Optional[List[EntityType]] = Query(None),
    approval_type: Optional[List[ApprovalType]] = Query(None),
    company_name: Optional[str] = Query(None),
    tax_id: Optional[str] = Query(None),
    contact_email: Optional[str] = Query(None),
    is_expired: Optional[bool] = Query(None),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Lista solicitações de autorização com filtros."""
    
    # TODO: Implementar controle de acesso baseado em roles
    # Apenas admins e staff podem ver todas as solicitações
    # Usuários normais só veem suas próprias solicitações
    
    service = B2BAuthorizationService(db)
    
    filters = B2BAuthorizationRequestFilter(
        status=status,
        entity_type=entity_type,
        approval_type=approval_type,
        company_name=company_name,
        tax_id=tax_id,
        contact_email=contact_email,
        is_expired=is_expired
    )
    
    requests, total = await service.list_requests(
        tenant_id=current_user.tenant_id,
        filters=filters,
        page=page,
        per_page=per_page
    )
    
    return B2BAuthorizationRequestListResponse(
        items=requests,
        total=total,
        page=page,
        per_page=per_page,
        pages=ceil(total / per_page)
    )


@router.get("/requests/{request_id}", response_model=B2BAuthorizationRequestResponse)
async def get_authorization_request(
    request_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Busca uma solicitação de autorização específica."""
    
    service = B2BAuthorizationService(db)
    
    authorization_request = await service.get_request_by_id(
        request_id=request_id,
        tenant_id=current_user.tenant_id
    )
    
    if not authorization_request:
        raise HTTPException(status_code=404, detail="Solicitação não encontrada")
    
    # TODO: Verificar se usuário tem permissão para ver esta solicitação
    
    return authorization_request


@router.put("/requests/{request_id}", response_model=B2BAuthorizationRequestResponse)
async def update_authorization_request(
    request_id: uuid.UUID,
    update_data: B2BAuthorizationRequestUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Atualiza uma solicitação de autorização (apenas se pendente)."""
    
    service = B2BAuthorizationService(db)
    
    authorization_request = await service.get_request_by_id(
        request_id=request_id,
        tenant_id=current_user.tenant_id
    )
    
    if not authorization_request:
        raise HTTPException(status_code=404, detail="Solicitação não encontrada")
    
    # Verificar se usuário é o dono da solicitação
    if authorization_request.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Sem permissão para editar esta solicitação")
    
    # Verificar se ainda pode ser editada
    if authorization_request.status != AuthorizationStatus.PENDING:
        raise HTTPException(
            status_code=400,
            detail="Solicitação não pode ser editada após sair do status pendente"
        )
    
    # TODO: Implementar método de atualização no service
    # authorization_request = await service.update_request(request_id, update_data, current_user.id)
    
    return authorization_request


@router.post("/requests/{request_id}/approve", response_model=B2BAuthorizationRequestResponse)
async def approve_authorization_request(
    request_id: uuid.UUID,
    approval_data: B2BAuthorizationApproval,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Aprova uma solicitação de autorização."""
    
    # TODO: Verificar se usuário tem permissão para aprovar (admin/staff)
    
    service = B2BAuthorizationService(db)
    
    authorization_request = await service.approve_request(
        request_id=request_id,
        approval_data=approval_data,
        approver_id=current_user.id,
        tenant_id=current_user.tenant_id
    )
    
    return authorization_request


@router.post("/requests/{request_id}/reject", response_model=B2BAuthorizationRequestResponse)
async def reject_authorization_request(
    request_id: uuid.UUID,
    rejection_data: B2BAuthorizationRejection,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Rejeita uma solicitação de autorização."""
    
    # TODO: Verificar se usuário tem permissão para rejeitar (admin/staff)
    
    service = B2BAuthorizationService(db)
    
    authorization_request = await service.reject_request(
        request_id=request_id,
        rejection_data=rejection_data,
        rejector_id=current_user.id,
        tenant_id=current_user.tenant_id
    )
    
    return authorization_request


@router.post("/requests/{request_id}/documents", response_model=B2BAuthorizationDocumentResponse)
async def upload_authorization_document(
    request_id: uuid.UUID,
    document_type: DocumentType = Form(...),
    document_name: str = Form(...),
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Faz upload de documento para uma solicitação de autorização."""
    
    service = B2BAuthorizationService(db)
    
    # Verificar se usuário tem permissão para fazer upload
    authorization_request = await service.get_request_by_id(
        request_id=request_id,
        tenant_id=current_user.tenant_id
    )
    
    if not authorization_request:
        raise HTTPException(status_code=404, detail="Solicitação não encontrada")
    
    # Usuário pode fazer upload se for o dono da solicitação ou admin/staff
    if authorization_request.user_id != current_user.id:
        # TODO: Verificar se é admin/staff
        raise HTTPException(status_code=403, detail="Sem permissão para fazer upload nesta solicitação")
    
    document = await service.upload_document(
        request_id=request_id,
        document_type=document_type,
        document_name=document_name,
        file=file,
        tenant_id=current_user.tenant_id
    )
    
    return document


@router.get("/requests/{request_id}/documents", response_model=List[B2BAuthorizationDocumentResponse])
async def list_authorization_documents(
    request_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Lista documentos de uma solicitação de autorização."""
    
    service = B2BAuthorizationService(db)
    
    authorization_request = await service.get_request_by_id(
        request_id=request_id,
        tenant_id=current_user.tenant_id
    )
    
    if not authorization_request:
        raise HTTPException(status_code=404, detail="Solicitação não encontrada")
    
    # TODO: Verificar permissões de acesso
    
    return authorization_request.documents


@router.post("/requests/{request_id}/documents/{document_id}/verify")
async def verify_authorization_document(
    request_id: uuid.UUID,
    document_id: uuid.UUID,
    verification_data: dict,  # TODO: Criar schema específico
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Verifica um documento de autorização."""
    
    # TODO: Implementar verificação de documentos
    # Apenas staff/admin podem verificar documentos
    
    pass


@router.get("/statistics", response_model=B2BAuthorizationStats)
async def get_authorization_statistics(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Obtém estatísticas de autorização."""
    
    # TODO: Verificar se usuário tem permissão para ver estatísticas
    
    service = B2BAuthorizationService(db)
    
    stats = await service.get_statistics(tenant_id=current_user.tenant_id)
    
    return stats


@router.get("/settings", response_model=B2BAuthorizationSettingsResponse)
async def get_authorization_settings(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Obtém configurações de autorização do tenant."""
    
    # TODO: Verificar se usuário tem permissão para ver configurações (admin)
    
    service = B2BAuthorizationService(db)
    
    settings = await service.get_or_create_settings(tenant_id=current_user.tenant_id)
    
    return settings


@router.put("/settings", response_model=B2BAuthorizationSettingsResponse)
async def update_authorization_settings(
    settings_data: B2BAuthorizationSettingsUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Atualiza configurações de autorização do tenant."""
    
    # TODO: Verificar se usuário tem permissão para alterar configurações (admin)
    # TODO: Implementar método de atualização de configurações no service
    
    service = B2BAuthorizationService(db)
    
    settings = await service.get_or_create_settings(tenant_id=current_user.tenant_id)
    
    # Atualizar campos fornecidos
    for field, value in settings_data.dict(exclude_unset=True).items():
        setattr(settings, field, value)
    
    await db.commit()
    
    return settings
