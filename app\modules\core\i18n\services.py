from datetime import datetime, timezone
from typing import List, Optional, TYPE_CHECKING, Union, Dict
from uuid import UUID
import json
import hashlib
from sqlalchemy import func

from sqlalchemy.orm import Session, joinedload
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, and_

from app.modules.i18n import models, schemas

if TYPE_CHECKING:
    from app.models.user import User  # Supondo que o modelo User está aqui para type hinting


class LanguageService:
    async def get_language(self, db: AsyncSession, language_id: int) -> Optional[models.Language]:
        return await db.get(models.Language, language_id)

    async def get_language_by_code(self, db: AsyncSession, code: str) -> Optional[models.Language]:
        result = await db.execute(select(models.Language).where(models.Language.code == code))
        return result.scalar_one_or_none()

    async def get_languages(
        self, db: AsyncSession, skip: int = 0, limit: int = 100
    ) -> List[models.Language]:
        result = await db.execute(select(models.Language).offset(skip).limit(limit))
        return list(result.scalars().all())

    async def create_language(
        self, db: AsyncSession, language: schemas.LanguageCreate
    ) -> models.Language:
        """Create a new language - async version for use with AsyncSession"""
        if language.is_default:
            # Garante que apenas um idioma seja o padrão
            await db.execute(
                update(models.Language)
                .where(models.Language.is_default == True)
                .values(is_default=False)
            )

        # Criar o objeto Language utilizando o construtor (não model_validate)
        db_language = models.Language(
            code=language.code,
            name=language.name,
            is_active=language.is_active,
            is_default=language.is_default,
        )
        db.add(db_language)
        await db.commit()
        await db.refresh(db_language)
        return db_language

    async def update_language(
        self, db: AsyncSession, language_db: models.Language, language_in: schemas.LanguageUpdate
    ) -> models.Language:
        update_data = language_in.model_dump(exclude_unset=True)

        if update_data.get("is_default") is True and not language_db.is_default:
            # Se está definindo este como padrão, desmarca qualquer outro
            await db.execute(
                update(models.Language)
                .where(models.Language.is_default == True)
                .values(is_default=False)
            )
        elif update_data.get("is_default") is False and language_db.is_default:
            # Não permitir desmarcar o default diretamente se for o único.
            # A lógica para mudar o default deve ser feita através de set_default_language
            # ou ao criar/atualizar outro idioma para ser o padrão.
            # No entanto, se o usuário explicitamente define is_default=False, devemos honrar.
            # Mas é mais seguro gerenciar via set_default_language.
            # Para este método, vamos permitir a atualização direta.
            pass

        for field, value in update_data.items():
            setattr(language_db, field, value)

        # db.add(language_db) # Not strictly necessary if language_db is already in session and modified
        await db.commit()
        await db.refresh(language_db)
        return language_db

    async def delete_language(
        self, db: AsyncSession, language_id: int
    ) -> Optional[models.Language]:
        db_language = await self.get_language(db, language_id)
        if db_language:
            if db_language.is_default:
                # Impedir a exclusão do idioma padrão.
                # O usuário deve primeiro definir outro idioma como padrão.
                # Idealmente, lançaria uma exceção aqui.
                return None  # Ou levantar HTTPException em um contexto de API

            # Verificar se há traduções ou sugestões associadas
            # Se houver, pode ser necessário impedir a exclusão ou lidar com elas (cascade, nullify)
            # Por simplicidade, vamos permitir a exclusão, mas em um sistema real isso precisaria de mais lógica.

            await db.delete(db_language)
            await db.commit()
        return db_language

    async def get_default_language(self, db: AsyncSession) -> Optional[models.Language]:
        result = await db.execute(select(models.Language).where(models.Language.is_default == True))
        return result.scalar_one_or_none()

    async def set_default_language(self, db: AsyncSession, language_id: int) -> models.Language:
        language_to_set = await self.get_language(db, language_id)
        if not language_to_set:
            # Idealmente, lançaria uma exceção aqui (ex: LanguageNotFound)
            raise ValueError(
                f"Language with id {language_id} not found."
            )  # Adaptar para HTTPException se em API

        # Desmarca o idioma padrão atual, se houver
        current_default = await self.get_default_language(db)
        if current_default and current_default.id != language_to_set.id:
            current_default.is_default = False
            # db.add(current_default) # Not strictly necessary

        language_to_set.is_default = True
        # db.add(language_to_set) # Not strictly necessary
        await db.commit()
        await db.refresh(language_to_set)
        return language_to_set


class TranslationKeyService:
    async def get_key(self, db: AsyncSession, key_id: int) -> Optional[models.TranslationKey]:
        return await db.get(models.TranslationKey, key_id)

    async def get_key_by_string(
        self, db: AsyncSession, key_string: str
    ) -> Optional[models.TranslationKey]:
        result = await db.execute(
            select(models.TranslationKey).where(models.TranslationKey.key_string == key_string)
        )
        return result.scalar_one_or_none()

    async def get_keys(
        self, db: AsyncSession, skip: int = 0, limit: int = 100, module: Optional[str] = None
    ) -> List[models.TranslationKey]:
        query = select(models.TranslationKey)
        if module:
            query = query.where(models.TranslationKey.module == module)
        result = await db.execute(query.offset(skip).limit(limit))
        return list(result.scalars().all())

    async def create_key(
        self, db: AsyncSession, key: schemas.TranslationKeyCreate
    ) -> models.TranslationKey:
        # Criar o objeto TranslationKey utilizando o construtor (não model_validate)
        db_key = models.TranslationKey(
            key_string=key.key_string, module=key.module, description=key.description
        )
        db.add(db_key)
        await db.commit()
        await db.refresh(db_key)
        return db_key

    async def update_key(
        self, db: AsyncSession, key_db: models.TranslationKey, key_in: schemas.TranslationKeyUpdate
    ) -> models.TranslationKey:
        update_data = key_in.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(key_db, field, value)

        # db.add(key_db)
        await db.commit()
        await db.refresh(key_db)
        return key_db

    async def delete_key(self, db: AsyncSession, key_id: int) -> Optional[models.TranslationKey]:
        db_key = await self.get_key(db, key_id)
        if db_key:
            # Verificar se existem traduções associadas
            translations_count_result = await db.execute(
                select(func.count(models.Translation.id)).where(models.Translation.key_id == key_id)
            )
            translations_count = translations_count_result.scalar_one()

            if translations_count > 0:
                # Impedir a exclusão se houver traduções.
                # Em um cenário real, poderia levantar uma exceção.
                # Ou implementar soft delete para a chave.
                # raise HTTPException(status_code=409, detail="Cannot delete key with existing translations.")
                return None

            await db.delete(db_key)
            await db.commit()
        return db_key


# Import the TranslationService from the new file
from app.modules.i18n.translation_service import TranslationService


class TranslationSuggestionService:
    async def get_suggestion(
        self, db: AsyncSession, suggestion_id: int
    ) -> Optional[models.TranslationSuggestion]:
        """Obtém uma sugestão pelo ID"""
        return await db.get(
            models.TranslationSuggestion,
            suggestion_id,
            options=[
                joinedload(models.TranslationSuggestion.language),
                joinedload(models.TranslationSuggestion.translation_key),
                joinedload(models.TranslationSuggestion.user),
            ],
        )

    async def get_suggestions(
        self,
        db: AsyncSession,
        skip: int = 0,
        limit: int = 100,
        status: Optional[schemas.TranslationSuggestionStatus] = None,
        lang_code: Optional[str] = None,
    ) -> List[models.TranslationSuggestion]:
        query = select(models.TranslationSuggestion).order_by(
            models.TranslationSuggestion.created_at.desc()
        )
        query = query.options(  # Eager load related data
            joinedload(models.TranslationSuggestion.language),
            joinedload(models.TranslationSuggestion.translation_key),
            joinedload(models.TranslationSuggestion.user),
        )

        if status is not None:
            query = query.where(models.TranslationSuggestion.status == status.value)

        if lang_code:
            query = query.join(
                models.Language, models.TranslationSuggestion.language_id == models.Language.id
            ).where(models.Language.code == lang_code)

        result = await db.execute(query.offset(skip).limit(limit))
        return list(result.scalars().all())

    async def create_suggestion(
        self, db: AsyncSession, suggestion_in: schemas.TranslationSuggestionCreate
    ) -> models.TranslationSuggestion:
        # user_id from suggestion_in is already a UUID
        db_suggestion = models.TranslationSuggestion(
            suggested_text=suggestion_in.suggested_text,
            current_text_on_suggestion=suggestion_in.current_text_on_suggestion,
            status=suggestion_in.status,
            admin_notes=suggestion_in.admin_notes,
            key_id=suggestion_in.key_id,
            language_id=suggestion_in.language_id,
            user_id=suggestion_in.user_id,
        )

        translation_service = TranslationService(db)
        existing_translation = await translation_service.get_translation_by_key_and_lang(
            db, db_suggestion.key_id, db_suggestion.language_id
        )

        if existing_translation:
            db_suggestion.current_text_on_suggestion = existing_translation.translated_text

        db.add(db_suggestion)
        await db.commit()
        await db.refresh(db_suggestion)
        return db_suggestion

    async def _update_suggestion_status(
        self,
        db: AsyncSession,
        suggestion_db: models.TranslationSuggestion,
        new_status: schemas.TranslationSuggestionStatus,
        admin_user_id: UUID,
        admin_notes: Optional[str] = None,
    ) -> models.TranslationSuggestion:
        suggestion_db.status = new_status
        suggestion_db.reviewed_at = datetime.now(timezone.utc)
        suggestion_db.reviewer_id = admin_user_id  # Assuming reviewer_id field exists and is UUID
        if admin_notes:
            suggestion_db.admin_notes = admin_notes

        # db.add(suggestion_db) # Not needed
        # Commit will be handled by caller
        return suggestion_db

    async def approve_suggestion(
        self,
        db: AsyncSession,
        suggestion_id: int,
        admin_user_id: UUID,
        admin_notes: Optional[str] = None,
    ) -> models.TranslationSuggestion:
        suggestion_db = await self.get_suggestion(db, suggestion_id)
        if not suggestion_db:
            # raise HTTPException(status_code=404, detail="Suggestion not found")
            raise ValueError("Suggestion not found")

        if suggestion_db.status != schemas.TranslationSuggestionStatus.PENDING:
            # raise HTTPException(status_code=400, detail="Only pending suggestions can be approved")
            raise ValueError("Only pending suggestions can be approved")

        suggestion_db = await self._update_suggestion_status(
            db,
            suggestion_db,
            schemas.TranslationSuggestionStatus.APPROVED,
            admin_user_id,
            admin_notes,
        )
        # The actual translation object that was created or updated
        updated_or_created_translation = None

        translation_service = TranslationService(db)
        existing_translation = await translation_service.get_translation_by_key_and_lang(
            db, suggestion_db.key_id, suggestion_db.language_id
        )

        if existing_translation:
            update_data = schemas.TranslationUpdate(
                translated_text=suggestion_db.suggested_text,
                is_approved=True,
            )
            updated_or_created_translation = await translation_service.update_translation(
                db=db,
                translation_db=existing_translation,
                translation_in=update_data,
                user_id=admin_user_id,  # admin_user_id is the one performing the update
            )
        else:
            new_translation_schema = schemas.TranslationCreate(
                translated_text=suggestion_db.suggested_text,
                key_id=suggestion_db.key_id,
                language_id=suggestion_db.language_id,
                is_approved=True,
                version=1,
                last_updated_by_user_id=admin_user_id,  # admin_user_id creates the new translation
            )
            updated_or_created_translation = await translation_service.create_translation(
                db=db, translation=new_translation_schema
            )

        await db.commit()  # Commit all changes: suggestion status and translation
        await db.refresh(suggestion_db)
        if updated_or_created_translation:  # Refresh if it exists
            await db.refresh(updated_or_created_translation)

        # Return the suggestion object as per original return type, though the translation is the main outcome
        return suggestion_db

    async def reject_suggestion(
        self,
        db: AsyncSession,
        suggestion_id: int,
        admin_user_id: UUID,
        admin_notes: Optional[str] = None,
    ) -> models.TranslationSuggestion:
        suggestion_db = await self.get_suggestion(db, suggestion_id)
        if not suggestion_db:
            # raise HTTPException(status_code=404, detail="Suggestion not found")
            raise ValueError("Suggestion not found")

        if suggestion_db.status != schemas.TranslationSuggestionStatus.PENDING:
            # raise HTTPException(status_code=400, detail="Only pending suggestions can be rejected")
            raise ValueError("Only pending suggestions can be rejected")

        suggestion_db = await self._update_suggestion_status(
            db,
            suggestion_db,
            schemas.TranslationSuggestionStatus.REJECTED,
            admin_user_id,
            admin_notes,
        )

        await db.commit()
        await db.refresh(suggestion_db)

        return suggestion_db

    async def implement_suggestion_manually(
        self,
        db: AsyncSession,
        suggestion_id: int,
        admin_user_id: UUID,
        admin_notes: Optional[str] = None,
    ) -> models.TranslationSuggestion:
        suggestion_db = await self.get_suggestion(db, suggestion_id)
        if not suggestion_db:
            raise ValueError("Suggestion not found")

        # Pode ser implementada manualmente mesmo se não estiver PENDING,
        # por exemplo, se foi REJECTED mas depois o admin decidiu usar parte dela.
        # Ou se estava PENDING e o admin implementou de outra forma.
        # if suggestion_db.status != schemas.TranslationSuggestionStatus.PENDING:
        #     raise ValueError(f"Suggestion is not pending, current status: {suggestion_db.status.value}")

        await self._update_suggestion_status(
            db,
            suggestion_db,
            schemas.TranslationSuggestionStatus.IMPLEMENTED_MANUALLY,
            admin_user_id,
            admin_notes,
        )
        await db.commit()
        await db.refresh(suggestion_db)
        return suggestion_db


# Singleton instances for services
language_service = LanguageService()
translation_key_service = TranslationKeyService()
translation_service = TranslationService()
translation_suggestion_service = TranslationSuggestionService()
