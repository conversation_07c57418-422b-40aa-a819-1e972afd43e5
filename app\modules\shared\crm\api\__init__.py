"""API endpoints for CRM module."""

from fastapi import APIRouter  # noqa: E402

from .account_api import router as account_router  # noqa: E402
from .contact_api import router as contact_router
from .interaction_api import router as interaction_router
from .loyalty_api import router as loyalty_router
from .pricing_api import router as pricing_router

router = APIRouter(prefix="/crm")

router.include_router(account_router)
router.include_router(contact_router)
router.include_router(interaction_router)
router.include_router(loyalty_router)
router.include_router(pricing_router)

__all__ = ["router"]
