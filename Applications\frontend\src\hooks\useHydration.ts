'use client';

import { useEffect, useState } from 'react';

/**
 * Hook para lidar com hidratação e evitar erros de SSR mismatch
 * Retorna true apenas após a hidratação no cliente
 */
export function useHydration() {
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  return isHydrated;
}

/**
 * Hook para verificar se estamos no cliente
 * Útil para evitar erros de SSR quando usando APIs do browser
 */
export function useIsClient() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(typeof window !== 'undefined');
  }, []);

  return isClient;
}
