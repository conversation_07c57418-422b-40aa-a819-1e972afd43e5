"""Financial Control Entry Models."""

import uuid
import enum
from typing import TYPE_CHECKING, Optional
from decimal import Decimal
from sqlalchemy import (
    Column, String, ForeignKey, Numeric, Text, Enum, Date, DateTime, 
    Boolean, Index, Table, func
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base

# Use TYPE_CHECKING to handle circular dependencies
if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.core.users.models.user import User
    from app.modules.shared.financial.transactions.models.transaction import FinancialTransaction
    from app.modules.shared.supplier.models.supplier import Supplier
    from .control_category import ControlCategory
    from .control_document import ControlDocument


class ControlEntryType(str, enum.Enum):
    """Enum for control entry types."""
    
    INCOME = "income"
    EXPENSE = "expense"
    TRANSFER = "transfer"
    ADJUSTMENT = "adjustment"


class ControlEntryStatus(str, enum.Enum):
    """Enum for control entry status."""
    
    PAID = "paid"
    PENDING = "pending"
    OVERDUE = "overdue"
    ARCHIVED = "archived"  # For cancelled entries (preserved for control)
    SCHEDULED = "scheduled"


# Association table for control entry documents
control_entry_documents = Table(
    "control_entry_documents",
    Base.metadata,
    Column("control_entry_id", UUID(as_uuid=True), 
           ForeignKey("financial_control_entries.id", ondelete="CASCADE"), 
           primary_key=True),
    Column("control_document_id", UUID(as_uuid=True), 
           ForeignKey("financial_control_documents.id", ondelete="CASCADE"), 
           primary_key=True),
)


class FinancialControlEntry(Base):
    """
    Model for financial control entries.
    
    This is the main model for the financial control system, providing
    comprehensive tracking of income, expenses, and financial operations
    with advanced categorization and document management.
    """
    
    __tablename__ = "financial_control_entries"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("tenants.id"), 
        nullable=False, 
        index=True
    )
    
    # Entry details
    entry_type = Column(Enum(ControlEntryType), nullable=False, index=True)
    status = Column(Enum(ControlEntryStatus), nullable=False, default=ControlEntryStatus.PENDING, index=True)
    amount = Column(Numeric(12, 2), nullable=False)
    gross_amount = Column(Numeric(12, 2), nullable=True)  # Amount before taxes/discounts
    net_amount = Column(Numeric(12, 2), nullable=True)   # Amount after taxes/discounts
    tax_amount = Column(Numeric(12, 2), nullable=True, default=0)
    discount_amount = Column(Numeric(12, 2), nullable=True, default=0)
    
    # Description and reference
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    reference_number = Column(String(100), nullable=True, index=True)
    external_reference = Column(String(100), nullable=True)  # Supplier invoice number, etc.
    
    # Dates
    entry_date = Column(Date, nullable=False, index=True)
    due_date = Column(Date, nullable=True, index=True)
    payment_date = Column(Date, nullable=True)
    
    # Category and classification
    category_id = Column(
        UUID(as_uuid=True),
        ForeignKey("financial_control_categories.id"),
        nullable=True,
        index=True
    )
    subcategory = Column(String(100), nullable=True)
    tags = Column(Text, nullable=True)  # JSON array of tags
    
    # Integration references
    transaction_id = Column(
        UUID(as_uuid=True),
        ForeignKey("financial_transactions.id"),
        nullable=True,
        index=True
    )
    supplier_id = Column(
        UUID(as_uuid=True),
        ForeignKey("suppliers.id"),
        nullable=True,
        index=True
    )
    order_id = Column(UUID(as_uuid=True), nullable=True, index=True)  # Reference to orders
    
    # Payment information
    payment_method = Column(String(50), nullable=True)
    payment_reference = Column(String(100), nullable=True)
    bank_account = Column(String(100), nullable=True)
    
    # Control flags
    is_recurring = Column(Boolean, default=False)
    is_automated = Column(Boolean, default=False)  # Auto-generated from orders/suppliers
    is_tax_deductible = Column(Boolean, default=False)
    requires_approval = Column(Boolean, default=False)
    
    # Approval workflow
    approved_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    approved_at = Column(DateTime, nullable=True)
    approval_notes = Column(Text, nullable=True)
    
    # Audit fields
    created_by = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        index=True
    )
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    created_at = Column(DateTime, nullable=False, default=func.now())
    updated_at = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now())
    
    # Additional metadata
    notes = Column(Text, nullable=True)
    additional_data = Column(Text, nullable=True)  # JSON for additional data
    
    # Relationships
    tenant = relationship("app.modules.core.tenants.models.tenant.Tenant", viewonly=True)
    category = relationship("ControlCategory", back_populates="entries")
    transaction = relationship(
        "app.modules.shared.financial.transactions.models.transaction.FinancialTransaction",
        viewonly=True
    )
    supplier = relationship("app.modules.shared.supplier.models.supplier.Supplier", viewonly=True)
    created_by_user = relationship(
        "app.modules.core.users.models.user.User", 
        foreign_keys=[created_by],
        viewonly=True
    )
    updated_by_user = relationship(
        "app.modules.core.users.models.user.User", 
        foreign_keys=[updated_by],
        viewonly=True
    )
    approved_by_user = relationship(
        "app.modules.core.users.models.user.User", 
        foreign_keys=[approved_by],
        viewonly=True
    )
    
    # Documents relationship
    documents = relationship(
        "ControlDocument",
        secondary=control_entry_documents,
        back_populates="entries",
        lazy="selectin"
    )
    
    # Table indexes for performance
    __table_args__ = (
        Index("ix_control_entries_tenant_date", "tenant_id", "entry_date"),
        Index("ix_control_entries_tenant_type", "tenant_id", "entry_type"),
        Index("ix_control_entries_tenant_status", "tenant_id", "status"),
        Index("ix_control_entries_tenant_category", "tenant_id", "category_id"),
        Index("ix_control_entries_tenant_supplier", "tenant_id", "supplier_id"),
        Index("ix_control_entries_due_date", "due_date"),
        Index("ix_control_entries_payment_date", "payment_date"),
    )
    
    def __repr__(self):
        return (
            f"<FinancialControlEntry(id={self.id}, "
            f"type='{self.entry_type}', "
            f"amount={self.amount}, "
            f"status='{self.status}')>"
        )
    
    @property
    def is_overdue(self) -> bool:
        """Check if entry is overdue."""
        if self.status == ControlEntryStatus.PAID or not self.due_date:
            return False
        from datetime import date
        return self.due_date < date.today()
    
    @property
    def calculated_net_amount(self) -> Decimal:
        """Calculate net amount if not explicitly set."""
        if self.net_amount is not None:
            return self.net_amount
        
        base_amount = self.gross_amount or self.amount
        tax = self.tax_amount or Decimal('0')
        discount = self.discount_amount or Decimal('0')
        
        if self.entry_type == ControlEntryType.INCOME:
            return base_amount - tax - discount
        else:
            return base_amount + tax - discount
