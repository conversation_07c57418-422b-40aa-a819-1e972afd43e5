"""
Auction and Lottery Models
=========================

Modelos para leilões e sorteios no sistema EShop.
"""

import uuid
import enum
from datetime import datetime
from typing import TYPE_CHECKING, Optional

from sqlalchemy import (
    Column, String, ForeignKey, Text, Enum, DateTime, 
    Boolean, Index, func, Numeric, Integer
)
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship

from app.db.base import Base

# Use TYPE_CHECKING to handle circular dependencies
if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.core.users.models.user import User
    from app.modules.core.eshop.models.product import Product


class AuctionStatus(str, enum.Enum):
    """Status do leilão."""
    
    DRAFT = "draft"
    SCHEDULED = "scheduled"
    ACTIVE = "active"
    ENDED = "ended"
    CANCELLED = "cancelled"


class LotteryStatus(str, enum.Enum):
    """Status do sorteio."""
    
    DRAFT = "draft"
    ACTIVE = "active"
    DRAWING = "drawing"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class Auction(Base):
    """
    Modelo para leilões de produtos no EShop.
    
    Permite que vendedores criem leilões com lance inicial,
    duração definida e opção "Comprar Já".
    """
    
    __tablename__ = "eshop_auctions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("tenants.id"), 
        nullable=False, 
        index=True
    )
    
    # Produto em leilão
    product_id = Column(
        UUID(as_uuid=True),
        ForeignKey("eshop_products.id"),
        nullable=False,
        index=True
    )
    
    # Vendedor
    vendor_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        index=True
    )
    
    # Configurações do leilão
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    
    # Valores
    starting_bid = Column(Numeric(10, 2), nullable=False)
    current_bid = Column(Numeric(10, 2), nullable=True)
    reserve_price = Column(Numeric(10, 2), nullable=True)  # Preço mínimo para venda
    buy_now_price = Column(Numeric(10, 2), nullable=True)  # Comprar agora
    
    # Incremento mínimo de lance
    bid_increment = Column(Numeric(10, 2), default=1.00, nullable=False)
    
    # Tempo
    start_time = Column(DateTime(timezone=True), nullable=False)
    end_time = Column(DateTime(timezone=True), nullable=False)
    
    # Status
    status = Column(Enum(AuctionStatus), default=AuctionStatus.DRAFT, nullable=False)
    
    # Vencedor
    winner_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=True,
        index=True
    )
    winning_bid = Column(Numeric(10, 2), nullable=True)
    
    # Configurações
    auto_extend = Column(Boolean, default=True, nullable=False)  # Estender se lance nos últimos minutos
    extend_minutes = Column(Integer, default=5, nullable=False)
    max_extensions = Column(Integer, default=3, nullable=False)
    extensions_used = Column(Integer, default=0, nullable=False)
    
    # Estatísticas
    total_bids = Column(Integer, default=0, nullable=False)
    unique_bidders = Column(Integer, default=0, nullable=False)
    views_count = Column(Integer, default=0, nullable=False)
    
    # Metadados
    auction_rules = Column(JSONB, nullable=True)  # Regras específicas
    featured_images = Column(JSONB, nullable=True)  # Imagens adicionais
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relacionamentos
    tenant = relationship("app.modules.core.tenants.models.tenant.Tenant", viewonly=True)
    product = relationship("app.modules.core.eshop.models.product.Product", viewonly=True)
    vendor = relationship("app.modules.core.users.models.user.User", foreign_keys=[vendor_id], viewonly=True)
    winner = relationship("app.modules.core.users.models.user.User", foreign_keys=[winner_id], viewonly=True)
    bids = relationship("AuctionBid", back_populates="auction", cascade="all, delete-orphan")
    
    # Índices para performance
    __table_args__ = (
        Index("ix_auctions_tenant_status", "tenant_id", "status"),
        Index("ix_auctions_vendor_status", "vendor_id", "status"),
        Index("ix_auctions_end_time", "end_time"),
        Index("ix_auctions_active", "status", "start_time", "end_time"),
    )
    
    def __repr__(self):
        return (
            f"<Auction(id={self.id}, "
            f"title='{self.title}', "
            f"status='{self.status}')>"
        )
    
    @property
    def is_active(self) -> bool:
        """Verifica se o leilão está ativo."""
        now = datetime.utcnow()
        return (
            self.status == AuctionStatus.ACTIVE and
            self.start_time <= now <= self.end_time
        )
    
    @property
    def time_remaining(self) -> Optional[int]:
        """Retorna segundos restantes do leilão."""
        if not self.is_active:
            return None
        
        now = datetime.utcnow()
        if now >= self.end_time:
            return 0
            
        return int((self.end_time - now).total_seconds())


class AuctionBid(Base):
    """
    Modelo para lances em leilões.
    """
    
    __tablename__ = "eshop_auction_bids"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Leilão
    auction_id = Column(
        UUID(as_uuid=True),
        ForeignKey("eshop_auctions.id"),
        nullable=False,
        index=True
    )
    
    # Licitante
    bidder_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        index=True
    )
    
    # Valor do lance
    bid_amount = Column(Numeric(10, 2), nullable=False)
    
    # Status
    is_winning = Column(Boolean, default=False, nullable=False)
    is_auto_bid = Column(Boolean, default=False, nullable=False)  # Lance automático
    
    # Metadados
    ip_address = Column(String(45), nullable=True)  # Para auditoria
    user_agent = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # Relacionamentos
    auction = relationship("Auction", back_populates="bids")
    bidder = relationship("app.modules.core.users.models.user.User", viewonly=True)
    
    # Índices para performance
    __table_args__ = (
        Index("ix_auction_bids_auction_amount", "auction_id", "bid_amount"),
        Index("ix_auction_bids_bidder", "bidder_id", "created_at"),
        Index("ix_auction_bids_winning", "auction_id", "is_winning"),
    )
    
    def __repr__(self):
        return (
            f"<AuctionBid(id={self.id}, "
            f"auction_id={self.auction_id}, "
            f"amount={self.bid_amount})>"
        )


class Lottery(Base):
    """
    Modelo para sorteios de produtos no EShop.
    
    Permite que vendedores criem sorteios onde usuários
    podem participar gratuitamente ou comprando bilhetes.
    """
    
    __tablename__ = "eshop_lotteries"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("tenants.id"), 
        nullable=False, 
        index=True
    )
    
    # Produto do sorteio
    product_id = Column(
        UUID(as_uuid=True),
        ForeignKey("eshop_products.id"),
        nullable=False,
        index=True
    )
    
    # Vendedor
    vendor_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        index=True
    )
    
    # Configurações do sorteio
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    
    # Configurações de participação
    is_free = Column(Boolean, default=True, nullable=False)
    ticket_price = Column(Numeric(10, 2), nullable=True)  # Se não for gratuito
    max_tickets_per_user = Column(Integer, nullable=True)  # Limite por usuário
    total_tickets_available = Column(Integer, nullable=True)  # Total de bilhetes
    
    # Tempo
    start_time = Column(DateTime(timezone=True), nullable=False)
    end_time = Column(DateTime(timezone=True), nullable=False)
    draw_time = Column(DateTime(timezone=True), nullable=True)  # Quando será o sorteio
    
    # Status
    status = Column(Enum(LotteryStatus), default=LotteryStatus.DRAFT, nullable=False)
    
    # Vencedor
    winner_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=True,
        index=True
    )
    winning_ticket_id = Column(
        UUID(as_uuid=True),
        ForeignKey("eshop_lottery_tickets.id", use_alter=True, name="fk_lottery_winning_ticket"),
        nullable=True
    )
    
    # Estatísticas
    total_participants = Column(Integer, default=0, nullable=False)
    total_tickets_sold = Column(Integer, default=0, nullable=False)
    total_revenue = Column(Numeric(10, 2), default=0.00, nullable=False)
    
    # Configurações
    auto_draw = Column(Boolean, default=True, nullable=False)  # Sorteio automático
    require_approval = Column(Boolean, default=False, nullable=False)  # Requer aprovação admin
    
    # Metadados
    lottery_rules = Column(JSONB, nullable=True)  # Regras específicas
    draw_method = Column(String(50), default="random", nullable=False)  # Método do sorteio
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relacionamentos
    tenant = relationship("app.modules.core.tenants.models.tenant.Tenant", viewonly=True)
    product = relationship("app.modules.core.eshop.models.product.Product", viewonly=True)
    vendor = relationship("app.modules.core.users.models.user.User", foreign_keys=[vendor_id], viewonly=True)
    winner = relationship("app.modules.core.users.models.user.User", foreign_keys=[winner_id], viewonly=True)
    tickets = relationship("LotteryTicket", back_populates="lottery", cascade="all, delete-orphan")
    winning_ticket = relationship("LotteryTicket", foreign_keys=[winning_ticket_id])
    
    # Índices para performance
    __table_args__ = (
        Index("ix_lotteries_tenant_status", "tenant_id", "status"),
        Index("ix_lotteries_vendor_status", "vendor_id", "status"),
        Index("ix_lotteries_draw_time", "draw_time"),
        Index("ix_lotteries_active", "status", "start_time", "end_time"),
    )
    
    def __repr__(self):
        return (
            f"<Lottery(id={self.id}, "
            f"title='{self.title}', "
            f"status='{self.status}')>"
        )
    
    @property
    def is_active(self) -> bool:
        """Verifica se o sorteio está ativo."""
        now = datetime.utcnow()
        return (
            self.status == LotteryStatus.ACTIVE and
            self.start_time <= now <= self.end_time
        )


class LotteryTicket(Base):
    """
    Modelo para bilhetes de sorteio.
    """
    
    __tablename__ = "eshop_lottery_tickets"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Sorteio
    lottery_id = Column(
        UUID(as_uuid=True),
        ForeignKey("eshop_lotteries.id"),
        nullable=False,
        index=True
    )
    
    # Participante
    participant_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        index=True
    )
    
    # Número do bilhete
    ticket_number = Column(String(50), nullable=False, index=True)
    
    # Status
    is_winning = Column(Boolean, default=False, nullable=False)
    is_paid = Column(Boolean, default=True, nullable=False)  # Para bilhetes pagos
    
    # Pagamento (se aplicável)
    payment_amount = Column(Numeric(10, 2), nullable=True)
    payment_reference = Column(String(100), nullable=True)
    
    # Metadados
    purchase_ip = Column(String(45), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # Relacionamentos
    lottery = relationship("Lottery", back_populates="tickets", foreign_keys=[lottery_id])
    participant = relationship("app.modules.core.users.models.user.User", viewonly=True)
    
    # Índices para performance
    __table_args__ = (
        Index("ix_lottery_tickets_lottery_participant", "lottery_id", "participant_id"),
        Index("ix_lottery_tickets_number", "lottery_id", "ticket_number", unique=True),
        Index("ix_lottery_tickets_winning", "lottery_id", "is_winning"),
    )
    
    def __repr__(self):
        return (
            f"<LotteryTicket(id={self.id}, "
            f"lottery_id={self.lottery_id}, "
            f"number='{self.ticket_number}')>"
        )