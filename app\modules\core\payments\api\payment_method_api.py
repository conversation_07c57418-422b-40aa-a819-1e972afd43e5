"""API endpoints for payment methods."""

import uuid
from typing import List, Optional, TYPE_CHECKING

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.core.exceptions import BusinessLogicError
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    get_current_tenant,
    require_tenant_role,
)
from app.modules.core.roles.models.roles import TenantRole, RolePermissions

from app.modules.core.tenants.models.tenant import Tenant

# Use TYPE_CHECKING to avoid circular import
if TYPE_CHECKING:
    from app.modules.core.users.models.user import User

from app.modules.core.payments.schemas.payment_method import (  # noqa: E402
    PaymentMethodCreate,
    PaymentMethodUpdate,
    PaymentMethodRead,
)
from app.modules.core.payments.services.payment_method_service import (  # noqa: E402
    payment_method_service,
)

router = APIRouter()


@router.post(
    "/methods",
    response_model=PaymentMethodRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create Payment Method",
    description="Create a new payment method for the tenant.",
)
async def create_payment_method(
    *,
    db: AsyncSession = Depends(get_db),
    method_in: PaymentMethodCreate,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(required_roles=RolePermissions.ADMIN_ROLES, tenant_id_source="header")
    ),
) -> PaymentMethodRead:
    """
    Creates a new payment method for the tenant.
    Requires MANAGER or OWNER role.
    """
    try:
        method = await payment_method_service.create(
            db=db, tenant_id=current_tenant.id, obj_in=method_in
        )
        return method
    except BusinessLogicError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get(
    "/methods",
    response_model=List[PaymentMethodRead],
    status_code=status.HTTP_200_OK,
    summary="List Payment Methods",
    description="List all payment methods for the tenant.",
)
async def list_payment_methods(
    *,
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(required_roles=RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    skip: int = 0,
    limit: int = 100,
) -> List[PaymentMethodRead]:
    """
    Lists all payment methods for the tenant.
    Requires STAFF, MANAGER, or OWNER role.
    """
    methods = await payment_method_service.get_multi(
        db=db, tenant_id=current_tenant.id, is_active=is_active, skip=skip, limit=limit
    )
    return methods


@router.get(
    "/methods/{method_id}",
    response_model=PaymentMethodRead,
    status_code=status.HTTP_200_OK,
    summary="Get Payment Method",
    description="Get details of a specific payment method.",
)
async def get_payment_method(
    *,
    db: AsyncSession = Depends(get_db),
    method_id: uuid.UUID,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(required_roles=RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
) -> PaymentMethodRead:
    """
    Gets details of a specific payment method.
    Requires STAFF, MANAGER, or OWNER role.
    """
    method = await payment_method_service.get(
        db=db, id=method_id, tenant_id=current_tenant.id
    )

    if not method:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Payment method with id {method_id} not found or does not belong to the tenant.",
        )

    return method


@router.put(
    "/methods/{method_id}",
    response_model=PaymentMethodRead,
    status_code=status.HTTP_200_OK,
    summary="Update Payment Method",
    description="Update an existing payment method.",
)
async def update_payment_method(
    *,
    db: AsyncSession = Depends(get_db),
    method_id: uuid.UUID,
    method_in: PaymentMethodUpdate,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(required_roles=RolePermissions.ADMIN_ROLES, tenant_id_source="header")
    ),
) -> PaymentMethodRead:
    """
    Updates an existing payment method.
    Requires MANAGER or OWNER role.
    """
    try:
        method = await payment_method_service.update(
            db=db, id=method_id, tenant_id=current_tenant.id, obj_in=method_in
        )

        if not method:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Payment method with id {method_id} not found or does not belong to the tenant.",
            )

        return method
    except BusinessLogicError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.delete(
    "/methods/{method_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete Payment Method",
    description="Delete an existing payment method.",
)
async def delete_payment_method(
    *,
    db: AsyncSession = Depends(get_db),
    method_id: uuid.UUID,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(required_roles=[TenantRole.OWNER.value], tenant_id_source="header")
    ),
) -> None:
    """
    Deletes an existing payment method.
    Requires OWNER role.
    """
    deleted = await payment_method_service.delete(
        db=db, id=method_id, tenant_id=current_tenant.id
    )

    if not deleted:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Payment method with id {method_id} not found or does not belong to the tenant.",
        )
