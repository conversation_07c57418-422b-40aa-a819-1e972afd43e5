"""
Testes para o sistema de mídia.
"""

import pytest
import tempfile
import os
from uuid import uuid4
from pathlib import Path
from unittest.mock import Mock, patch

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.modules.core.functions.media_system.models import (
    MediaContext,
    MediaUpload,
    MediaDirectory,
    MediaContextType,
    MediaType,
    UploadStatus
)
from app.modules.core.functions.media_system.services import (
    MediaContextService,
    MediaUploadService,
    MediaDirectoryService,
    MediaProcessingService
)
from app.modules.core.functions.media_system.schemas import (
    MediaContextCreate,
    MediaDirectoryCreate
)


class TestMediaContextService:
    """Testes para MediaContextService."""

    def test_create_context(self, db_session: Session):
        """Testa criação de contexto."""
        service = MediaContextService(db_session)
        
        context_data = MediaContextCreate(
            context_type=MediaContextType.USER,
            context_id=uuid4(),
            quota_limit_mb=1024
        )
        
        context = service.create_context(context_data)
        
        assert context is not None
        assert context.context_type == MediaContextType.USER
        assert context.quota_limit_mb == 1024
        assert context.used_space_mb == 0

    def test_get_or_create_context_existing(self, db_session: Session):
        """Testa obtenção de contexto existente."""
        service = MediaContextService(db_session)
        context_id = uuid4()
        
        # Cria contexto
        context_data = MediaContextCreate(
            context_type=MediaContextType.USER,
            context_id=context_id
        )
        created_context = service.create_context(context_data)
        
        # Obtém contexto existente
        obtained_context = service.get_or_create_context(
            MediaContextType.USER, 
            context_id
        )
        
        assert obtained_context.id == created_context.id

    def test_get_or_create_context_new(self, db_session: Session):
        """Testa criação de novo contexto."""
        service = MediaContextService(db_session)
        context_id = uuid4()
        
        context = service.get_or_create_context(
            MediaContextType.TENANT, 
            context_id
        )
        
        assert context is not None
        assert context.context_type == MediaContextType.TENANT
        assert context.context_id == context_id

    def test_check_quota_available(self, db_session: Session):
        """Testa verificação de quota com espaço disponível."""
        service = MediaContextService(db_session)
        
        context_data = MediaContextCreate(
            context_type=MediaContextType.USER,
            context_id=uuid4(),
            quota_limit_mb=1024
        )
        context = service.create_context(context_data)
        
        result = service.check_quota(context.id, 100)
        
        assert result["can_upload"] is True
        assert result["quota_limit_mb"] == 1024
        assert result["used_space_mb"] == 0

    def test_check_quota_exceeded(self, db_session: Session):
        """Testa verificação de quota excedida."""
        service = MediaContextService(db_session)
        
        context_data = MediaContextCreate(
            context_type=MediaContextType.USER,
            context_id=uuid4(),
            quota_limit_mb=100
        )
        context = service.create_context(context_data)
        
        # Simula uso de espaço
        context.used_space_mb = 90
        db_session.commit()
        
        result = service.check_quota(context.id, 20)
        
        assert result["can_upload"] is False
        assert "muito grande" in result["reason"].lower()

    def test_update_used_space(self, db_session: Session):
        """Testa atualização de espaço usado."""
        service = MediaContextService(db_session)
        
        context_data = MediaContextCreate(
            context_type=MediaContextType.USER,
            context_id=uuid4()
        )
        context = service.create_context(context_data)
        
        # Adiciona espaço
        success = service.update_used_space(context.id, 100)
        assert success is True
        
        # Verifica atualização
        updated_context = service.get_context(context.id)
        assert updated_context.used_space_mb == 100
        
        # Remove espaço
        service.update_used_space(context.id, -50)
        updated_context = service.get_context(context.id)
        assert updated_context.used_space_mb == 50


class TestMediaDirectoryService:
    """Testes para MediaDirectoryService."""

    def test_create_directory(self, db_session: Session):
        """Testa criação de diretório."""
        # Cria contexto primeiro
        context_service = MediaContextService(db_session)
        context = context_service.get_or_create_context(
            MediaContextType.USER, 
            uuid4()
        )
        
        # Cria diretório
        directory_service = MediaDirectoryService(db_session)
        directory_data = MediaDirectoryCreate(
            context_id=context.id,
            name="test_dir",
            path="/test_dir"
        )
        
        directory = directory_service.create_directory(directory_data)
        
        assert directory is not None
        assert directory.name == "test_dir"
        assert directory.path == "/test_dir"
        assert directory.context_id == context.id

    def test_create_subdirectory(self, db_session: Session):
        """Testa criação de subdiretório."""
        # Cria contexto
        context_service = MediaContextService(db_session)
        context = context_service.get_or_create_context(
            MediaContextType.USER, 
            uuid4()
        )
        
        directory_service = MediaDirectoryService(db_session)
        
        # Cria diretório pai
        parent_data = MediaDirectoryCreate(
            context_id=context.id,
            name="parent",
            path="/parent"
        )
        parent = directory_service.create_directory(parent_data)
        
        # Cria subdiretório
        child_data = MediaDirectoryCreate(
            context_id=context.id,
            name="child",
            path="/parent/child",
            parent_id=parent.id
        )
        child = directory_service.create_directory(child_data)
        
        assert child is not None
        assert child.parent_id == parent.id

    def test_get_directory_tree(self, db_session: Session):
        """Testa obtenção de árvore de diretórios."""
        # Cria contexto
        context_service = MediaContextService(db_session)
        context = context_service.get_or_create_context(
            MediaContextType.USER, 
            uuid4()
        )
        
        directory_service = MediaDirectoryService(db_session)
        
        # Cria estrutura de diretórios
        root_data = MediaDirectoryCreate(
            context_id=context.id,
            name="root",
            path="/root"
        )
        root = directory_service.create_directory(root_data)
        
        child_data = MediaDirectoryCreate(
            context_id=context.id,
            name="child",
            path="/root/child",
            parent_id=root.id
        )
        directory_service.create_directory(child_data)
        
        # Obtém árvore
        tree = directory_service.get_directory_tree(context.id)
        
        assert len(tree) == 1
        assert tree[0].name == "root"
        assert len(tree[0].children) == 1
        assert tree[0].children[0].name == "child"


class TestMediaUploadService:
    """Testes para MediaUploadService."""

    @pytest.fixture
    def mock_file(self):
        """Cria arquivo mock para testes."""
        mock_file = Mock()
        mock_file.filename = "test.jpg"
        mock_file.content_type = "image/jpeg"
        mock_file.read.return_value = b"fake image content"
        return mock_file

    @patch('pathlib.Path.mkdir')
    @patch('builtins.open')
    def test_upload_file_success(self, mock_open, mock_mkdir, db_session: Session, mock_file):
        """Testa upload de arquivo com sucesso."""
        # Cria contexto
        context_service = MediaContextService(db_session)
        context = context_service.get_or_create_context(
            MediaContextType.USER, 
            uuid4()
        )
        
        upload_service = MediaUploadService(db_session)
        user_id = uuid4()
        
        # Mock do arquivo
        mock_file_handle = Mock()
        mock_open.return_value.__enter__.return_value = mock_file_handle
        
        # Faz upload
        result = upload_service.upload_file(
            file=mock_file,
            context=context,
            uploaded_by=user_id
        )
        
        # Verifica resultado
        assert result.success is True
        assert result.upload_id is not None
        assert "sucesso" in result.message.lower()

    def test_get_uploads_by_context(self, db_session: Session):
        """Testa listagem de uploads por contexto."""
        # Cria contexto
        context_service = MediaContextService(db_session)
        context = context_service.get_or_create_context(
            MediaContextType.USER, 
            uuid4()
        )
        
        # Cria upload diretamente no banco
        upload = MediaUpload(
            context_id=context.id,
            uploaded_by=uuid4(),
            filename="test.jpg",
            original_filename="test.jpg",
            file_path="/fake/path/test.jpg",
            file_size=1024,
            mime_type="image/jpeg",
            media_type=MediaType.IMAGE,
            upload_status=UploadStatus.COMPLETED
        )
        db_session.add(upload)
        db_session.commit()
        
        # Lista uploads
        upload_service = MediaUploadService(db_session)
        uploads = upload_service.get_uploads_by_context(context.id)
        
        assert len(uploads) == 1
        assert uploads[0].filename == "test.jpg"


class TestMediaProcessingService:
    """Testes para MediaProcessingService."""

    def test_extract_image_metadata(self, db_session: Session):
        """Testa extração de metadados de imagem."""
        service = MediaProcessingService(db_session)
        
        # Mock de imagem PIL
        mock_img = Mock()
        mock_img.width = 800
        mock_img.height = 600
        mock_img.format = "JPEG"
        mock_img.mode = "RGB"
        mock_img.info = {}
        
        metadata = service._extract_image_metadata(mock_img)
        
        assert metadata["type"] == "image"
        assert metadata["width"] == 800
        assert metadata["height"] == 600
        assert metadata["format"] == "JPEG"

    def test_process_generic_file(self, db_session: Session):
        """Testa processamento de arquivo genérico."""
        service = MediaProcessingService(db_session)
        
        # Cria upload mock
        upload = Mock()
        upload.file_size = 1024
        upload.mime_type = "application/pdf"
        upload.created_at = "2023-01-01"
        
        result = service._process_generic(upload)
        
        assert result is True
        # Verifica se metadados foram definidos
        assert upload.file_metadata is not None


# Fixtures para testes
@pytest.fixture
def db_session():
    """Fixture para sessão de banco de dados de teste."""
    # Mock da sessão do banco
    session = Mock(spec=Session)
    session.add = Mock()
    session.commit = Mock()
    session.refresh = Mock()
    session.query = Mock()
    session.rollback = Mock()
    return session


@pytest.fixture
def test_client():
    """Fixture para cliente de teste da API."""
    from app.main import app
    return TestClient(app)
