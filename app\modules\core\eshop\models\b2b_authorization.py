"""
B2B Authorization Models
========================

Modelos para workflow de aprovação de TCostumer e TVendorSupplier.
"""

import uuid
import enum
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, List
from decimal import Decimal

from sqlalchemy import Column, String, ForeignKey, Index, Boolean, Integer, Text, DateTime, Numeric
from sqlalchemy import Enum as SAEnum, JSON
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.sql import text

from app.db.base import Base
from app.modules.core.tenants.models.tenant import Tenant
from app.modules.core.users.models.user import User


class AuthorizationStatus(enum.Enum):
    """Status do processo de autorização."""
    PENDING = "PENDING"
    UNDER_REVIEW = "UNDER_REVIEW"
    APPROVED = "APPROVED"
    REJECTED = "REJECTED"
    SUSPENDED = "SUSPENDED"
    EXPIRED = "EXPIRED"


class DocumentType(enum.Enum):
    """Tipos de documentos para verificação."""
    BUSINESS_LICENSE = "BUSINESS_LICENSE"
    TAX_CERTIFICATE = "TAX_CERTIFICATE"
    BANK_STATEMENT = "BANK_STATEMENT"
    IDENTITY_DOCUMENT = "IDENTITY_DOCUMENT"
    PROOF_OF_ADDRESS = "PROOF_OF_ADDRESS"
    FINANCIAL_STATEMENT = "FINANCIAL_STATEMENT"
    TRADE_REFERENCE = "TRADE_REFERENCE"
    INSURANCE_CERTIFICATE = "INSURANCE_CERTIFICATE"


class ApprovalType(enum.Enum):
    """Tipo de aprovação."""
    AUTOMATIC = "AUTOMATIC"
    MANUAL = "MANUAL"
    CONDITIONAL = "CONDITIONAL"


class B2BAuthorizationRequest(Base):
    """
    Solicitação de autorização B2B para TCostumer ou TVendorSupplier.
    """
    
    __tablename__ = "b2b_authorization_requests"
    
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(
        PG_UUID(as_uuid=True), 
        ForeignKey("tenants.id"), 
        nullable=False, 
        index=True
    )
    
    # Usuário solicitante
    user_id = Column(
        PG_UUID(as_uuid=True), 
        ForeignKey("users.id"), 
        nullable=False, 
        index=True
    )
    
    # Tipo de entidade (TCostumer ou TVendorSupplier)
    entity_type = Column(String(50), nullable=False, index=True)  # 'tcostumer' ou 'tvendor'
    entity_id = Column(PG_UUID(as_uuid=True), nullable=True, index=True)  # ID da entidade criada
    
    # Status e tipo de aprovação
    status = Column(
        String(50),
        nullable=False,
        default=AuthorizationStatus.PENDING.value,
        index=True
    )
    approval_type = Column(
        String(50),
        nullable=False,
        default=ApprovalType.MANUAL.value
    )
    
    # Informações da empresa
    company_name = Column(String(200), nullable=False, index=True)
    tax_id = Column(String(50), nullable=False, index=True)
    business_type = Column(String(100), nullable=True)
    
    # Informações de contato
    contact_person = Column(String(200), nullable=False)
    contact_email = Column(String(255), nullable=False)
    contact_phone = Column(String(50), nullable=True)
    
    # Endereços
    business_address = Column(JSON, nullable=True)
    billing_address = Column(JSON, nullable=True)
    
    # Informações financeiras solicitadas
    requested_credit_limit = Column(Numeric(15, 2), nullable=True)
    requested_payment_terms = Column(String(50), nullable=True)
    
    # Para TVendorSupplier
    requested_commission_rate = Column(Numeric(5, 2), nullable=True)
    supplier_type = Column(String(50), nullable=True)
    
    # Aprovação
    approved_by = Column(PG_UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    approved_at = Column(DateTime, nullable=True)
    approved_credit_limit = Column(Numeric(15, 2), nullable=True)
    approved_payment_terms = Column(String(50), nullable=True)
    approved_commission_rate = Column(Numeric(5, 2), nullable=True)
    
    # Rejeição
    rejection_reason = Column(Text, nullable=True)
    rejection_notes = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    expires_at = Column(DateTime, nullable=True)  # Expiração da solicitação
    
    # Metadados adicionais
    additional_info = Column(JSON, nullable=True)
    internal_notes = Column(Text, nullable=True)
    
    # Relationships
    tenant = relationship("Tenant")
    user = relationship("User", foreign_keys=[user_id])
    approver = relationship("User", foreign_keys=[approved_by])
    documents = relationship("B2BAuthorizationDocument", back_populates="authorization_request")
    history = relationship("B2BAuthorizationHistory", back_populates="authorization_request")
    
    __table_args__ = (
        Index("ix_b2b_auth_tenant_status", "tenant_id", "status"),
        Index("ix_b2b_auth_user_status", "user_id", "status"),
        Index("ix_b2b_auth_entity", "entity_type", "entity_id"),
        Index("ix_b2b_auth_company", "company_name"),
        Index("ix_b2b_auth_created", "created_at"),
        Index("ix_b2b_auth_expires", "expires_at"),
    )
    
    def is_expired(self) -> bool:
        """Verifica se a solicitação expirou."""
        if not self.expires_at:
            return False
        return datetime.utcnow() > self.expires_at
    
    def can_be_approved(self) -> bool:
        """Verifica se pode ser aprovada."""
        return self.status in [AuthorizationStatus.PENDING, AuthorizationStatus.UNDER_REVIEW]
    
    def get_required_documents(self) -> List[DocumentType]:
        """Retorna documentos obrigatórios baseado no tipo de entidade."""
        base_docs = [
            DocumentType.BUSINESS_LICENSE,
            DocumentType.TAX_CERTIFICATE,
            DocumentType.IDENTITY_DOCUMENT,
            DocumentType.PROOF_OF_ADDRESS
        ]
        
        if self.entity_type == 'tcostumer':
            if self.requested_credit_limit and self.requested_credit_limit > 10000:
                base_docs.extend([
                    DocumentType.FINANCIAL_STATEMENT,
                    DocumentType.BANK_STATEMENT
                ])
        elif self.entity_type == 'tvendor':
            base_docs.extend([
                DocumentType.INSURANCE_CERTIFICATE,
                DocumentType.TRADE_REFERENCE
            ])
        
        return base_docs


class B2BAuthorizationDocument(Base):
    """
    Documentos anexados à solicitação de autorização.
    """
    
    __tablename__ = "b2b_authorization_documents"
    
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    authorization_request_id = Column(
        PG_UUID(as_uuid=True), 
        ForeignKey("b2b_authorization_requests.id"), 
        nullable=False, 
        index=True
    )
    
    # Tipo e informações do documento
    document_type = Column(
        String(50),
        nullable=False,
        index=True
    )
    document_name = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    
    # Arquivo
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    file_type = Column(String(100), nullable=False)
    file_hash = Column(String(64), nullable=False)  # SHA-256
    
    # Status de verificação
    is_verified = Column(Boolean, default=False, nullable=False)
    verified_by = Column(PG_UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    verified_at = Column(DateTime, nullable=True)
    verification_notes = Column(Text, nullable=True)
    
    # Timestamps
    uploaded_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relationships
    authorization_request = relationship("B2BAuthorizationRequest", back_populates="documents")
    verifier = relationship("User")
    
    __table_args__ = (
        Index("ix_b2b_auth_docs_request", "authorization_request_id"),
        Index("ix_b2b_auth_docs_type", "document_type"),
        Index("ix_b2b_auth_docs_verified", "is_verified"),
        Index("ix_b2b_auth_docs_uploaded", "uploaded_at"),
    )


class B2BAuthorizationHistory(Base):
    """
    Histórico de mudanças na autorização.
    """
    
    __tablename__ = "b2b_authorization_history"
    
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    authorization_request_id = Column(
        PG_UUID(as_uuid=True), 
        ForeignKey("b2b_authorization_requests.id"), 
        nullable=False, 
        index=True
    )
    
    # Mudança
    action = Column(String(100), nullable=False, index=True)  # 'created', 'submitted', 'approved', etc.
    previous_status = Column(String(50), nullable=True)
    new_status = Column(String(50), nullable=True)
    
    # Usuário que fez a ação
    performed_by = Column(PG_UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    performed_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Detalhes da mudança
    notes = Column(Text, nullable=True)
    changes = Column(JSON, nullable=True)  # Detalhes das mudanças
    
    # Relationships
    authorization_request = relationship("B2BAuthorizationRequest", back_populates="history")
    performer = relationship("User")
    
    __table_args__ = (
        Index("ix_b2b_auth_history_request", "authorization_request_id"),
        Index("ix_b2b_auth_history_action", "action"),
        Index("ix_b2b_auth_history_performed", "performed_at"),
        Index("ix_b2b_auth_history_user", "performed_by"),
    )


class B2BAuthorizationSettings(Base):
    """
    Configurações de autorização por tenant.
    """
    
    __tablename__ = "b2b_authorization_settings"
    
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(
        PG_UUID(as_uuid=True), 
        ForeignKey("tenants.id"), 
        nullable=False, 
        unique=True,
        index=True
    )
    
    # Configurações automáticas
    auto_approve_tcostumer = Column(Boolean, default=False, nullable=False)
    auto_approve_tvendor = Column(Boolean, default=False, nullable=False)
    
    # Limites para aprovação automática
    max_auto_credit_limit = Column(Numeric(15, 2), default=Decimal('5000.00'), nullable=False)
    max_auto_commission_rate = Column(Numeric(5, 2), default=Decimal('5.00'), nullable=False)
    
    # Configurações de expiração
    request_expiry_days = Column(Integer, default=30, nullable=False)
    document_retention_days = Column(Integer, default=365, nullable=False)
    
    # Notificações
    notify_on_new_request = Column(Boolean, default=True, nullable=False)
    notify_on_approval = Column(Boolean, default=True, nullable=False)
    notify_on_rejection = Column(Boolean, default=True, nullable=False)
    
    # Emails para notificações
    notification_emails = Column(JSON, nullable=True)  # Lista de emails
    
    # Configurações de documentos obrigatórios
    required_documents_tcostumer = Column(JSON, nullable=True)
    required_documents_tvendor = Column(JSON, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    tenant = relationship("Tenant")
    
    __table_args__ = (
        Index("ix_b2b_auth_settings_tenant", "tenant_id"),
    )
