"""Interaction models for CRM module."""

import uuid
import enum
from datetime import datetime
from typing import TYPE_CHECKING, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, Text, Enum as SQLAlchemyE<PERSON>, Integer, DateTime, JSON, String
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.orm import relationship, Mapped, mapped_column

from app.db.base import Base

if TYPE_CHECKING:
    from app.modules.core.users.models.user import User
    from app.modules.core.tenants.models.tenant import Tenant
    from .account import Account
    from .contact import Contact


class InteractionType(str, enum.Enum):
    """Interaction type enum."""

    EMAIL = "email"
    CALL = "call"
    MEETING = "meeting"
    TASK = "task"
    NOTE = "note"
    PURCHASE = "purchase"
    SUPPORT = "support"
    COMPLAINT = "complaint"
    FEEDBACK = "feedback"
    OTHER = "other"


class InteractionChannel(str, enum.Enum):
    """Interaction channel enum."""

    EMAIL = "email"
    PHONE = "phone"
    IN_PERSON = "in_person"
    WEB = "web"
    MOBILE_APP = "mobile_app"
    SOCIAL_MEDIA = "social_media"
    CHAT = "chat"
    VIDEO = "video"
    OTHER = "other"


class Interaction(Base):
    """Interaction model for CRM module.

    This represents an interaction with a customer account or contact.
    """

    __tablename__ = "crm_interactions"

    id: Mapped[uuid.UUID] = mapped_column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id: Mapped[uuid.UUID] = mapped_column(
        PG_UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True
    )
    account_id: Mapped[uuid.UUID] = mapped_column(
        PG_UUID(as_uuid=True), ForeignKey("crm_accounts.id"), nullable=False
    )
    contact_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        PG_UUID(as_uuid=True), ForeignKey("crm_contacts.id")
    )
    created_by_user_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        PG_UUID(as_uuid=True), ForeignKey("users.id")
    )

    interaction_type: Mapped[InteractionType] = mapped_column(SQLAlchemyEnum(InteractionType), nullable=False)
    channel: Mapped[InteractionChannel] = mapped_column(SQLAlchemyEnum(InteractionChannel), nullable=False)
    subject: Mapped[str] = mapped_column(String, nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)

    interaction_date: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=datetime.utcnow)
    duration_minutes: Mapped[Optional[int]] = mapped_column(Integer)

    requires_followup: Mapped[bool] = mapped_column(default=False)
    followup_date: Mapped[Optional[datetime]] = mapped_column(DateTime)
    followup_notes: Mapped[Optional[str]] = mapped_column(Text)

    is_completed: Mapped[bool] = mapped_column(default=True)
    outcome: Mapped[Optional[str]] = mapped_column(String)
    interaction_metadata: Mapped[Optional[dict]] = mapped_column(JSON)

    # Relationships
    tenant: Mapped["Tenant"] = relationship(back_populates="crm_interactions")
    account: Mapped["Account"] = relationship(back_populates="interactions")
    contact: Mapped[Optional["Contact"]] = relationship(back_populates="interactions")
    created_by: Mapped[Optional["User"]] = relationship()

    def __repr__(self):
        return (
            f"<Interaction(id={self.id}, "
            f"type='{self.interaction_type}', "
            f"date='{self.interaction_date}')>"
        )

# Forward references for relationships
if TYPE_CHECKING:
    pass
