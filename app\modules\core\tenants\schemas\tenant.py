from __future__ import annotations
import uuid
from datetime import datetime
from typing import Optional, TYPE_CHECKING

from pydantic import BaseModel, ConfigDict, Field

from app.modules.core.roles.models.roles import TenantType

if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant as TenantModel

class TenantBase(BaseModel):
    is_active: bool = True


class TenantCreate(TenantBase):
    pass  # Inherits everything from TenantBase


class TenantUpdate(BaseModel):
    is_active: Optional[bool] = None


class TenantInDBBase(TenantBase):
    id: uuid.UUID
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    model_config = ConfigDict(from_attributes=True)


class Tenant(TenantInDBBase):
    name: Optional[str] = None
    tenant_slug: Optional[str] = None

    @classmethod
    def from_orm_model(cls, tenant_model: "TenantModel") -> "Tenant":
        data = {
            "id": tenant_model.id,
            "is_active": tenant_model.is_active,
            "created_at": tenant_model.created_at,
            "updated_at": tenant_model.updated_at,
            "name": tenant_model.settings.business_name if tenant_model.settings else None,
            "tenant_slug": tenant_model.restaurant_settings.tenant_slug if tenant_model.restaurant_settings else None,
        }
        return cls(**data)


class TenantSimple(TenantInDBBase):
    pass
