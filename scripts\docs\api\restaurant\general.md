# Restaurant - General

**Categoria:** Restaurant
**Módulo:** General
**Total de Endpoints:** 70
**Gerado em:** 27/06/2025, 20:20:27

## 📋 Endpoints

- [POST /api/modules/restaurants/kds/auth](#post-apimodulesrestaurantskdsauth) - Authenticate Kds
- [GET /api/modules/restaurants/kds/current-code](#get-apimodulesrestaurantskdscurrent-code) - Get Current Temp Code
- [POST /api/modules/restaurants/kds/generate-temp-code](#post-apimodulesrestaurantskdsgenerate-temp-code) - Generate Kds Temp Code
- [GET /api/modules/restaurants/kds/orders](#get-apimodulesrestaurantskdsorders) - Get KDS Orders
- [GET /api/modules/restaurants/kds/orders/](#get-apimodulesrestaurantskdsorders) - Read Kitchen Orders
- [POST /api/modules/restaurants/kds/orders/](#post-apimodulesrestaurantskdsorders) - Create Kitchen Order
- [GET /api/modules/restaurants/kds/orders/{order_id}](#get-apimodulesrestaurantskdsordersorder-id) - Read Kitchen Order
- [PATCH /api/modules/restaurants/kds/orders/{order_id}/status](#patch-apimodulesrestaurantskdsordersorder-idstatus) - Update Kitchen Order Status
- [GET /api/modules/restaurants/kds/status](#get-apimodulesrestaurantskdsstatus) - Get KDS Status
- [GET /api/modules/restaurants/menu/categories/](#get-apimodulesrestaurantsmenucategories) - Read Menu Categories
- [POST /api/modules/restaurants/menu/categories/](#post-apimodulesrestaurantsmenucategories) - Create Menu Category
- [PUT /api/modules/restaurants/menu/categories/reorder](#put-apimodulesrestaurantsmenucategoriesreorder) - Reorder Categories
- [DELETE /api/modules/restaurants/menu/categories/{category_id}](#delete-apimodulesrestaurantsmenucategoriescategory-id) - Delete Menu Category
- [GET /api/modules/restaurants/menu/categories/{category_id}](#get-apimodulesrestaurantsmenucategoriescategory-id) - Read Menu Category
- [PUT /api/modules/restaurants/menu/categories/{category_id}](#put-apimodulesrestaurantsmenucategoriescategory-id) - Update Menu Category
- [GET /api/modules/restaurants/menu/digital-menus/](#get-apimodulesrestaurantsmenudigital-menus) - Get Digital Menus
- [POST /api/modules/restaurants/menu/digital-menus/](#post-apimodulesrestaurantsmenudigital-menus) - Create Digital Menu
- [GET /api/modules/restaurants/menu/digital-menus/current](#get-apimodulesrestaurantsmenudigital-menuscurrent) - Get Current Digital Menu
- [PUT /api/modules/restaurants/menu/digital-menus/reorder](#put-apimodulesrestaurantsmenudigital-menusreorder) - Reorder Digital Menus
- [DELETE /api/modules/restaurants/menu/digital-menus/{menu_id}](#delete-apimodulesrestaurantsmenudigital-menusmenu-id) - Delete Digital Menu
- [GET /api/modules/restaurants/menu/digital-menus/{menu_id}](#get-apimodulesrestaurantsmenudigital-menusmenu-id) - Get Digital Menu
- [PUT /api/modules/restaurants/menu/digital-menus/{menu_id}](#put-apimodulesrestaurantsmenudigital-menusmenu-id) - Update Digital Menu
- [GET /api/modules/restaurants/menu/items/](#get-apimodulesrestaurantsmenuitems) - Read Menu Items
- [POST /api/modules/restaurants/menu/items/](#post-apimodulesrestaurantsmenuitems) - Create Menu Item
- [PUT /api/modules/restaurants/menu/items/reorder](#put-apimodulesrestaurantsmenuitemsreorder) - Reorder Items
- [DELETE /api/modules/restaurants/menu/items/{item_id}](#delete-apimodulesrestaurantsmenuitemsitem-id) - Delete Menu Item
- [GET /api/modules/restaurants/menu/items/{item_id}](#get-apimodulesrestaurantsmenuitemsitem-id) - Read Menu Item
- [PUT /api/modules/restaurants/menu/items/{item_id}](#put-apimodulesrestaurantsmenuitemsitem-id) - Update Menu Item
- [GET /api/modules/restaurants/menu/modifier-groups](#get-apimodulesrestaurantsmenumodifier-groups) - Get Modifier Groups
- [GET /api/modules/restaurants/menu/modifier-groups/{group_id}](#get-apimodulesrestaurantsmenumodifier-groupsgroup-id) - Get Modifier Group By Id
- [GET /api/modules/restaurants/menu/optional-groups](#get-apimodulesrestaurantsmenuoptional-groups) - Get Optional Groups
- [GET /api/modules/restaurants/menu/optional-groups/{group_id}](#get-apimodulesrestaurantsmenuoptional-groupsgroup-id) - Get Optional Group By Id
- [GET /api/modules/restaurants/menu/variant-groups](#get-apimodulesrestaurantsmenuvariant-groups) - Get Variant Groups
- [GET /api/modules/restaurants/menu/variant-groups/{group_id}](#get-apimodulesrestaurantsmenuvariant-groupsgroup-id) - Get Variant Group By Id
- [GET /api/modules/restaurants/reservations/blacklist/](#get-apimodulesrestaurantsreservationsblacklist) - Get all blacklist entries
- [POST /api/modules/restaurants/reservations/blacklist/](#post-apimodulesrestaurantsreservationsblacklist) - Create a new blacklist entry
- [GET /api/modules/restaurants/reservations/blacklist/check](#get-apimodulesrestaurantsreservationsblacklistcheck) - Check if a customer is blacklisted
- [DELETE /api/modules/restaurants/reservations/blacklist/{blacklist_id}](#delete-apimodulesrestaurantsreservationsblacklistblacklist-id) - Delete a blacklist entry
- [GET /api/modules/restaurants/reservations/blacklist/{blacklist_id}](#get-apimodulesrestaurantsreservationsblacklistblacklist-id) - Get a blacklist entry by ID
- [PUT /api/modules/restaurants/reservations/blacklist/{blacklist_id}](#put-apimodulesrestaurantsreservationsblacklistblacklist-id) - Update a blacklist entry
- [GET /api/modules/restaurants/reservations/reservations/](#get-apimodulesrestaurantsreservationsreservations) - Get all reservations
- [POST /api/modules/restaurants/reservations/reservations/](#post-apimodulesrestaurantsreservationsreservations) - Create a new reservation
- [GET /api/modules/restaurants/reservations/reservations/available-tables](#get-apimodulesrestaurantsreservationsreservationsavailable-tables) - Find available tables for a reservation
- [DELETE /api/modules/restaurants/reservations/reservations/{reservation_id}](#delete-apimodulesrestaurantsreservationsreservationsreservation-id) - Delete a reservation
- [GET /api/modules/restaurants/reservations/reservations/{reservation_id}](#get-apimodulesrestaurantsreservationsreservationsreservation-id) - Get a reservation by ID
- [PUT /api/modules/restaurants/reservations/reservations/{reservation_id}](#put-apimodulesrestaurantsreservationsreservationsreservation-id) - Update a reservation
- [PATCH /api/modules/restaurants/reservations/reservations/{reservation_id}/status](#patch-apimodulesrestaurantsreservationsreservationsreservation-idstatus) - Update reservation status
- [GET /api/modules/restaurants/settings/](#get-apimodulesrestaurantssettings) - Get Restaurant Settings
- [PUT /api/modules/restaurants/settings/](#put-apimodulesrestaurantssettings) - Update Restaurant Settings
- [PUT /api/modules/restaurants/settings/business/](#put-apimodulesrestaurantssettingsbusiness) - Update Restaurant Business Settings
- [PUT /api/modules/restaurants/settings/location/](#put-apimodulesrestaurantssettingslocation) - Update Restaurant Location Settings
- [PUT /api/modules/restaurants/settings/operating-hours/](#put-apimodulesrestaurantssettingsoperating-hours) - Update Operating Hours
- [PUT /api/modules/restaurants/settings/social-media/](#put-apimodulesrestaurantssettingssocial-media) - Update Restaurant Social Media Settings
- [PUT /api/modules/restaurants/settings/special-calendar/](#put-apimodulesrestaurantssettingsspecial-calendar) - Update Special Calendar
- [PUT /api/modules/restaurants/settings/wifi/](#put-apimodulesrestaurantssettingswifi) - Update Restaurant Wifi Settings
- [GET /api/modules/restaurants/settings/zones/](#get-apimodulesrestaurantssettingszones) - Get Restaurant Zones
- [POST /api/modules/restaurants/settings/zones/](#post-apimodulesrestaurantssettingszones) - Add Custom Zone
- [DELETE /api/modules/restaurants/settings/zones/{zone_name}](#delete-apimodulesrestaurantssettingszoneszone-name) - Remove Custom Zone
- [GET /api/modules/restaurants/tables/layouts/](#get-apimodulesrestaurantstableslayouts) - Get all table layouts
- [POST /api/modules/restaurants/tables/layouts/](#post-apimodulesrestaurantstableslayouts) - Create a new table layout
- [DELETE /api/modules/restaurants/tables/layouts/{layout_id}](#delete-apimodulesrestaurantstableslayoutslayout-id) - Delete a table layout
- [GET /api/modules/restaurants/tables/layouts/{layout_id}](#get-apimodulesrestaurantstableslayoutslayout-id) - Get a table layout by ID
- [PUT /api/modules/restaurants/tables/layouts/{layout_id}](#put-apimodulesrestaurantstableslayoutslayout-id) - Update a table layout
- [GET /api/modules/restaurants/tables/tables/](#get-apimodulesrestaurantstablestables) - Get all tables
- [POST /api/modules/restaurants/tables/tables/](#post-apimodulesrestaurantstablestables) - Create a new table
- [GET /api/modules/restaurants/tables/tables/available](#get-apimodulesrestaurantstablestablesavailable) - Get available tables
- [DELETE /api/modules/restaurants/tables/tables/{table_id}](#delete-apimodulesrestaurantstablestablestable-id) - Delete a table
- [GET /api/modules/restaurants/tables/tables/{table_id}](#get-apimodulesrestaurantstablestablestable-id) - Get a table by ID
- [PUT /api/modules/restaurants/tables/tables/{table_id}](#put-apimodulesrestaurantstablestablestable-id) - Update a table
- [PATCH /api/modules/restaurants/tables/tables/{table_id}/status](#patch-apimodulesrestaurantstablestablestable-idstatus) - Update table status

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### CustomerBlacklistCreate

**Descrição:** Schema for creating a new customer blacklist entry.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `blacklist_type` | BlacklistType | ❌ | Type of blacklist entry |
| `reason` | unknown | ❌ | Reason for blacklisting |
| `suspension_start_date` | unknown | ❌ | Start date of suspension |
| `suspension_end_date` | unknown | ❌ | End date of suspension |
| `require_deposit` | boolean | ❌ | Whether to require a deposit for future reservations |
| `deposit_amount` | unknown | ❌ | Amount of the deposit to require |
| `customer_id` | unknown | ❌ | ID of the customer to blacklist |
| `guest_name` | unknown | ❌ | Name of the guest (for non-registered customers) |
| `guest_email` | unknown | ❌ | Email of the guest (for non-registered customers) |
| `guest_phone` | unknown | ❌ | Phone number of the guest (for non-registered customers) |

### CustomerBlacklistRead

**Descrição:** Schema for reading a customer blacklist entry.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `blacklist_type` | BlacklistType | ❌ | Type of blacklist entry |
| `reason` | unknown | ❌ | Reason for blacklisting |
| `suspension_start_date` | unknown | ❌ | Start date of suspension |
| `suspension_end_date` | unknown | ❌ | End date of suspension |
| `require_deposit` | boolean | ❌ | Whether to require a deposit for future reservations |
| `deposit_amount` | unknown | ❌ | Amount of the deposit to require |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `customer_id` | unknown | ❌ | - |
| `guest_name` | unknown | ❌ | - |
| `guest_email` | unknown | ❌ | - |
| `guest_phone` | unknown | ❌ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `customer` | unknown | ❌ | - |
| `is_active` | boolean | ✅ | - |

### CustomerBlacklistUpdate

**Descrição:** Schema for updating an existing customer blacklist entry.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `blacklist_type` | unknown | ❌ | Type of blacklist entry |
| `reason` | unknown | ❌ | Reason for blacklisting |
| `suspension_start_date` | unknown | ❌ | Start date of suspension |
| `suspension_end_date` | unknown | ❌ | End date of suspension |
| `require_deposit` | unknown | ❌ | Whether to require a deposit for future reservations |
| `deposit_amount` | unknown | ❌ | Amount of the deposit to require |

### DigitalMenuCreate

**Descrição:** Schema for creating a new Digital Menu.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Name of the digital menu (e.g., Breakfast Menu) |
| `description` | unknown | ❌ | Optional description for the menu |
| `is_active` | boolean | ❌ | Whether the menu is currently active |
| `display_order` | integer | ❌ | Order in which the menu should be displayed |
| `schedule_enabled` | boolean | ❌ | Whether time-based scheduling is enabled for this menu |
| `schedule_config` | unknown | ❌ | JSON configuration for menu scheduling (timezone, time ranges, days) |

### DigitalMenuRead

**Descrição:** Schema for reading a Digital Menu, includes the ID and metadata.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Name of the digital menu (e.g., Breakfast Menu) |
| `description` | unknown | ❌ | Optional description for the menu |
| `is_active` | boolean | ❌ | Whether the menu is currently active |
| `display_order` | integer | ❌ | Order in which the menu should be displayed |
| `schedule_enabled` | boolean | ❌ | Whether time-based scheduling is enabled for this menu |
| `schedule_config` | unknown | ❌ | JSON configuration for menu scheduling (timezone, time ranges, days) |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### DigitalMenuUpdate

**Descrição:** Schema for updating an existing Digital Menu. All fields are optional.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `display_order` | unknown | ❌ | - |
| `schedule_enabled` | unknown | ❌ | - |
| `schedule_config` | unknown | ❌ | - |

### DigitalMenuWithCategories

**Descrição:** Schema for reading a Digital Menu with its categories.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Name of the digital menu (e.g., Breakfast Menu) |
| `description` | unknown | ❌ | Optional description for the menu |
| `is_active` | boolean | ❌ | Whether the menu is currently active |
| `display_order` | integer | ❌ | Order in which the menu should be displayed |
| `schedule_enabled` | boolean | ❌ | Whether time-based scheduling is enabled for this menu |
| `schedule_config` | unknown | ❌ | JSON configuration for menu scheduling (timezone, time ranges, days) |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `categories` | Array[MenuCategoryRead] | ❌ | List of categories in this menu |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### KDSAuthRequest

**Descrição:** Schema para requisição de autenticação do KDS.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `restaurant_uuid` | string | ✅ | UUID do restaurante |
| `temp_code` | string | ✅ | Código temporário de 6 dígitos |

### KDSAuthResponse

**Descrição:** Schema para resposta de autenticação do KDS.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `access_token` | string | ✅ | Token de acesso JWT |
| `token_type` | string | ❌ | Tipo do token |
| `expires_in` | integer | ✅ | Segundos até expiração do token |
| `tenant_id` | string | ✅ | ID do tenant autenticado |
| `tenant_name` | string | ✅ | Nome do restaurante |

### KDSTempCodeGenerate

**Descrição:** Schema para resposta de geração de código temporário.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `code` | string | ✅ | Código de 6 dígitos gerado |
| `expires_at` | string | ✅ | Data/hora de expiração |
| `expires_in_hours` | integer | ✅ | Horas até expiração |
| `tenant_id` | string | ✅ | ID do tenant |
| `message` | string | ✅ | Mensagem informativa |

### KitchenOrderCreate

**Descrição:** Schema para criar um novo KitchenOrder. tenant_id será adicionado no serviço.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `order_details` | object | ✅ | Detalhes do pedido (itens, quantidades, notas, etc.) |
| `status` | string | ❌ | Status atual do pedido (pending, preparing, ready, served) |
| `source_sale_id` | unknown | ❌ | ID opcional da transação de venda de origem (POS) |
| `source_online_order_id` | unknown | ❌ | ID opcional do pedido online de origem |
| `source_order_id` | unknown | ❌ | ID opcional do pedido compartilhado de origem |
| `creator_user_id` | unknown | ❌ | ID opcional do usuário que criou o pedido (ex: usuário do pedido online) |

### KitchenOrderRead

**Descrição:** Schema para retornar dados de KitchenOrder na API.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `order_details` | object | ✅ | Detalhes do pedido (itens, quantidades, notas, etc.) |
| `status` | string | ❌ | Status atual do pedido (pending, preparing, ready, served) |
| `source_sale_id` | unknown | ❌ | ID opcional da transação de venda de origem (POS) |
| `source_online_order_id` | unknown | ❌ | ID opcional do pedido online de origem |
| `source_order_id` | unknown | ❌ | ID opcional do pedido compartilhado de origem |
| `creator_user_id` | unknown | ❌ | ID opcional do usuário que criou o pedido (ex: usuário do pedido online) |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### KitchenOrderUpdate

**Descrição:** Schema para atualizar um KitchenOrder existente. Permite atualizar status e detalhes.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `status` | unknown | ❌ | Novo status do pedido (pending, preparing, ready, served) |
| `order_details` | unknown | ❌ | Detalhes atualizados do pedido (itens, notas, etc.) |

### MenuCategoryCreate

**Descrição:** Schema for creating a new Menu Category.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Name of the menu category (e.g., Appetizers) |
| `description` | unknown | ❌ | Optional description for the category |
| `display_order` | integer | ❌ | Order in which the category should be displayed |
| `is_active` | boolean | ❌ | Whether the category is currently active |
| `is_default` | unknown | ❌ | Whether this is the default 'Sem Categoria' category |
| `parent_id` | unknown | ❌ | ID of the parent category, if this is a subcategory |
| `digital_menu_id` | unknown | ❌ | ID of the digital menu this category belongs to |

### MenuCategoryRead

**Descrição:** Schema for reading a Menu Category, includes the ID and optional relationships.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Name of the menu category (e.g., Appetizers) |
| `description` | unknown | ❌ | Optional description for the category |
| `display_order` | integer | ❌ | Order in which the category should be displayed |
| `is_active` | boolean | ❌ | Whether the category is currently active |
| `is_default` | unknown | ❌ | Whether this is the default 'Sem Categoria' category |
| `parent_id` | unknown | ❌ | ID of the parent category, if this is a subcategory |
| `digital_menu_id` | unknown | ❌ | ID of the digital menu this category belongs to |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `menu_items` | unknown | ❌ | - |
| `children` | unknown | ❌ | - |

### MenuCategoryReadSimple

**Descrição:** Simplified schema for Menu Category without children to avoid lazy loading issues.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `id` | string | ✅ | - |
| `name` | string | ✅ | - |
| `description` | unknown | ❌ | - |
| `display_order` | integer | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `is_default` | unknown | ❌ | - |
| `parent_id` | unknown | ❌ | - |
| `digital_menu_id` | unknown | ❌ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### MenuCategoryUpdate

**Descrição:** Schema for updating an existing Menu Category. All fields are optional.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `display_order` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `is_default` | unknown | ❌ | - |
| `parent_id` | unknown | ❌ | ID of the parent category to move this category under. Use null to make it a top-level category. |
| `digital_menu_id` | unknown | ❌ | ID of the digital menu to assign this category to |

### MenuItemCreate

**Descrição:** Schema for creating a new Menu Item, including category and potentially nested groups.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Name of the menu item (e.g., Classic Burger) |
| `description` | unknown | ❌ | Detailed description of the item |
| `base_price` | unknown | ✅ | Base price of the item before variants/modifiers |
| `image_url` | unknown | ❌ | URL for the item's primary image |
| `allergen_ids` | Array[string] | ❌ | List of allergen IDs associated with this item |
| `is_available` | boolean | ❌ | Is the item currently available for ordering? |
| `is_active` | boolean | ❌ | Is the item active in the system (for soft delete)? |
| `is_combo` | boolean | ❌ | Is this item a combo meal? |
| `discount_percentage` | unknown | ❌ | Optional discount percentage (e.g., 10.5 for 10.5%) |
| `display_order` | integer | ❌ | Order within the menu category |
| `category_id` | string | ✅ | ID of the category this item belongs to |
| `variant_groups` | Array[VariantGroupCreate] | ❌ | Variant groups (e.g., Size) and their options |
| `modifier_groups` | Array[ModifierGroupCreate] | ❌ | Modifier groups (e.g., Add-ons) and their options |
| `optional_groups` | Array[OptionalGroupCreate] | ❌ | Optional groups (e.g., Sides, Drinks) and their options |

### MenuItemUpdate

**Descrição:** Schema for updating an existing Menu Item. All fields optional.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `base_price` | unknown | ❌ | - |
| `image_url` | unknown | ❌ | - |
| `allergen_ids` | unknown | ❌ | - |
| `is_available` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `is_combo` | unknown | ❌ | - |
| `discount_percentage` | unknown | ❌ | - |
| `display_order` | unknown | ❌ | - |
| `category_id` | unknown | ❌ | - |
| `allergens` | unknown | ❌ | - |
| `variant_groups` | unknown | ❌ | Variant groups to replace existing ones |
| `modifier_groups` | unknown | ❌ | Modifier groups to replace existing ones |
| `optional_groups` | unknown | ❌ | Optional groups to replace existing ones |

### ModifierGroupReadWithOptions

**Descrição:** Schema for reading a Modifier Group with options.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `menu_item_id` | unknown | ❌ | - |
| `name` | string | ✅ | - |
| `description` | unknown | ❌ | - |
| `min_selection` | integer | ✅ | - |
| `max_selection` | integer | ✅ | - |
| `display_order` | integer | ✅ | - |
| `is_required` | boolean | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `options` | Array[app__modules__tenants__restaurants__menu__schemas__modifier_option__ModifierOptionRead] | ❌ | - |
| `usage_count` | unknown | ❌ | - |

### OptionalGroupReadWithOptions

**Descrição:** Schema for reading an Optional Group with options.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `menu_item_id` | unknown | ❌ | - |
| `name` | string | ✅ | - |
| `description` | unknown | ✅ | - |
| `min_selection` | integer | ✅ | - |
| `max_selection` | integer | ✅ | - |
| `display_order` | integer | ✅ | - |
| `is_required` | boolean | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `options` | Array[app__modules__tenants__restaurants__menu__schemas__optional_option__OptionalOptionRead] | ❌ | - |
| `usage_count` | unknown | ❌ | - |

### ReservationCreate

**Descrição:** Schema for creating a new reservation.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `party_size` | integer | ✅ | Number of people in the party |
| `reservation_date` | string | ✅ | Date and time of the reservation |
| `duration_minutes` | unknown | ❌ | Duration of the reservation in minutes |
| `special_requests` | unknown | ❌ | Special requests for the reservation |
| `occasion` | unknown | ❌ | Occasion for the reservation |
| `notes` | unknown | ❌ | Additional notes about the reservation |
| `table_id` | unknown | ❌ | ID of the table for the reservation |
| `customer_id` | unknown | ❌ | ID of the customer making the reservation |
| `guest_name` | unknown | ❌ | Name of the guest (for non-registered customers) |
| `guest_email` | unknown | ❌ | Email of the guest (for non-registered customers) |
| `guest_phone` | unknown | ❌ | Phone number of the guest (for non-registered customers) |
| `deposit_required` | unknown | ❌ | Whether a deposit is required |
| `deposit_amount` | unknown | ❌ | Amount of the deposit |

### ReservationRead

**Descrição:** Schema for reading a reservation.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `party_size` | integer | ✅ | Number of people in the party |
| `reservation_date` | string | ✅ | Date and time of the reservation |
| `duration_minutes` | unknown | ❌ | Duration of the reservation in minutes |
| `special_requests` | unknown | ❌ | Special requests for the reservation |
| `occasion` | unknown | ❌ | Occasion for the reservation |
| `notes` | unknown | ❌ | Additional notes about the reservation |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `table_id` | unknown | ❌ | - |
| `customer_id` | unknown | ❌ | - |
| `reservation_number` | string | ✅ | - |
| `status` | ReservationStatus | ✅ | - |
| `guest_name` | unknown | ❌ | - |
| `guest_email` | unknown | ❌ | - |
| `guest_phone` | unknown | ❌ | - |
| `deposit_required` | boolean | ✅ | - |
| `deposit_amount` | unknown | ❌ | - |
| `deposit_paid` | boolean | ✅ | - |
| `payment_id` | unknown | ❌ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `table` | unknown | ❌ | - |
| `customer` | unknown | ❌ | - |

### ReservationUpdate

**Descrição:** Schema for updating an existing reservation.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `table_id` | unknown | ❌ | ID of the table for the reservation |
| `party_size` | unknown | ❌ | Number of people in the party |
| `reservation_date` | unknown | ❌ | Date and time of the reservation |
| `duration_minutes` | unknown | ❌ | Duration of the reservation in minutes |
| `status` | unknown | ❌ | Status of the reservation |
| `special_requests` | unknown | ❌ | Special requests for the reservation |
| `occasion` | unknown | ❌ | Occasion for the reservation |
| `notes` | unknown | ❌ | Additional notes about the reservation |
| `deposit_required` | unknown | ❌ | Whether a deposit is required |
| `deposit_amount` | unknown | ❌ | Amount of the deposit |
| `deposit_paid` | unknown | ❌ | Whether the deposit has been paid |
| `payment_id` | unknown | ❌ | Reference to payment |

### RestaurantBusinessSettingsUpdate

**Descrição:** Schema for updating restaurant business settings.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `tenant_slug` | unknown | ❌ | Unique slug for tenant identification in public URLs |

### RestaurantLocationSettingsUpdate

**Descrição:** Schema for updating restaurant location settings.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `address_extensions` | unknown | ❌ | Restaurant-specific address extensions |

### RestaurantOperatingHoursUpdate

**Descrição:** Schema for updating restaurant operating hours.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `operating_hours` | unknown | ❌ | Operating hours configuration by day of week |

### RestaurantSocialMediaSettingsUpdate

**Descrição:** Schema for updating restaurant social media settings.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `social_media_links` | unknown | ❌ | Social media platform links with icons |

### RestaurantSpecialCalendarUpdate

**Descrição:** Schema for updating restaurant special calendar.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `special_calendar` | unknown | ❌ | Special calendar events by date |

### RestaurantTenantSettingsRead

**Descrição:** Schema for reading restaurant tenant settings.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `tenant_slug` | unknown | ❌ | Unique slug for tenant identification in public URLs |
| `wifi_networks` | unknown | ❌ | WiFi networks configuration by zone |
| `social_media_links` | unknown | ❌ | Social media platform links with icons |
| `address_extensions` | unknown | ❌ | Restaurant-specific address extensions |
| `operating_hours` | unknown | ❌ | Restaurant operating hours with service, break, and happy hour periods |
| `special_calendar` | unknown | ❌ | Special calendar events with custom hours or closures |
| `additional_restaurant_settings` | unknown | ❌ | Additional restaurant-specific settings |
| `id` | string | ✅ | Unique identifier for the restaurant settings |
| `tenant_id` | string | ✅ | ID of the tenant these settings belong to |
| `has_tenant_slug` | boolean | ✅ | Whether tenant slug is configured |
| `has_wifi_networks` | boolean | ✅ | Whether WiFi networks are configured |
| `has_social_media_links` | boolean | ✅ | Whether social media links are configured |
| `has_address_extensions` | boolean | ✅ | Whether address extensions are configured |
| `delivery_radius_km` | unknown | ❌ | Delivery radius in kilometers |
| `phone_is_whatsapp` | boolean | ❌ | Whether primary phone is WhatsApp |
| `has_operating_hours` | boolean | ❌ | Whether operating hours are configured |
| `has_special_calendar` | boolean | ❌ | Whether special calendar events are configured |
| `is_open_today` | boolean | ❌ | Whether restaurant is open today |

### RestaurantTenantSettingsUpdate

**Descrição:** Schema for updating restaurant tenant settings.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `tenant_slug` | unknown | ❌ | Unique slug for tenant identification in public URLs |
| `wifi_networks` | unknown | ❌ | WiFi networks configuration by zone |
| `social_media_links` | unknown | ❌ | Social media platform links with icons |
| `address_extensions` | unknown | ❌ | Restaurant-specific address extensions |
| `operating_hours` | unknown | ❌ | Restaurant operating hours with service, break, and happy hour periods |
| `special_calendar` | unknown | ❌ | Special calendar events with custom hours or closures |
| `additional_restaurant_settings` | unknown | ❌ | Additional restaurant-specific settings |

### RestaurantWiFiSettingsUpdate

**Descrição:** Schema for updating restaurant WiFi settings.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `wifi_networks` | unknown | ❌ | WiFi networks configuration by zone |

### TableCreate

**Descrição:** Schema for creating a new table.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `table_number` | string | ✅ | Table number or identifier |
| `name` | unknown | ❌ | Optional display name for the table |
| `capacity` | integer | ✅ | Number of people the table can accommodate |
| `zone` | unknown | ❌ | Zone where the table is located |
| `status` | TableStatus | ❌ | Current status of the table |
| `position_x` | unknown | ❌ | X coordinate in the layout |
| `position_y` | unknown | ❌ | Y coordinate in the layout |
| `width` | unknown | ❌ | Width of the table in the layout |
| `height` | unknown | ❌ | Height of the table in the layout |
| `shape` | unknown | ❌ | Shape of the table (rectangle, circle, custom) |
| `custom_shape_data` | unknown | ❌ | Data for custom shapes |
| `is_active` | boolean | ❌ | Whether the table is active |
| `notes` | unknown | ❌ | Additional notes about the table |
| `qrcode_enabled` | boolean | ❌ | Whether the QR code is enabled for this table |
| `layout_id` | unknown | ❌ | ID of the layout this table belongs to |

### TableLayoutCreate

**Descrição:** Schema for creating a new table layout.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Name of the layout |
| `description` | unknown | ❌ | Description of the layout |
| `floor_number` | unknown | ❌ | Floor number |
| `width` | unknown | ❌ | Width of the layout |
| `height` | unknown | ❌ | Height of the layout |
| `background_image_url` | unknown | ❌ | URL to background image |
| `is_active` | boolean | ❌ | Whether the layout is active |
| `layout_data` | unknown | ❌ | Additional layout information |

### TableLayoutRead

**Descrição:** Schema for reading a table layout.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Name of the layout |
| `description` | unknown | ❌ | Description of the layout |
| `floor_number` | unknown | ❌ | Floor number |
| `width` | unknown | ❌ | Width of the layout |
| `height` | unknown | ❌ | Height of the layout |
| `background_image_url` | unknown | ❌ | URL to background image |
| `is_active` | boolean | ❌ | Whether the layout is active |
| `layout_data` | unknown | ❌ | Additional layout information |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `tables` | unknown | ❌ | - |

### TableLayoutUpdate

**Descrição:** Schema for updating an existing table layout.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | Name of the layout |
| `description` | unknown | ❌ | Description of the layout |
| `floor_number` | unknown | ❌ | Floor number |
| `width` | unknown | ❌ | Width of the layout |
| `height` | unknown | ❌ | Height of the layout |
| `background_image_url` | unknown | ❌ | URL to background image |
| `is_active` | unknown | ❌ | Whether the layout is active |
| `layout_data` | unknown | ❌ | Additional layout information |

### TableRead

**Descrição:** Schema for reading a table.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `table_number` | string | ✅ | Table number or identifier |
| `name` | unknown | ❌ | Optional display name for the table |
| `capacity` | integer | ✅ | Number of people the table can accommodate |
| `zone` | unknown | ❌ | Zone where the table is located |
| `status` | TableStatus | ❌ | Current status of the table |
| `position_x` | unknown | ❌ | X coordinate in the layout |
| `position_y` | unknown | ❌ | Y coordinate in the layout |
| `width` | unknown | ❌ | Width of the table in the layout |
| `height` | unknown | ❌ | Height of the table in the layout |
| `shape` | unknown | ❌ | Shape of the table (rectangle, circle, custom) |
| `custom_shape_data` | unknown | ❌ | Data for custom shapes |
| `is_active` | boolean | ❌ | Whether the table is active |
| `notes` | unknown | ❌ | Additional notes about the table |
| `qrcode_enabled` | boolean | ❌ | Whether the QR code is enabled for this table |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `layout_id` | unknown | ❌ | - |
| `qrcode_id` | unknown | ❌ | Unique identifier for the QR code |
| `layout` | unknown | ❌ | - |

### TableUpdate

**Descrição:** Schema for updating an existing table.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `table_number` | unknown | ❌ | Table number or identifier |
| `name` | unknown | ❌ | Optional display name for the table |
| `capacity` | unknown | ❌ | Number of people the table can accommodate |
| `zone` | unknown | ❌ | Zone where the table is located |
| `status` | unknown | ❌ | Current status of the table |
| `layout_id` | unknown | ❌ | ID of the layout this table belongs to |
| `position_x` | unknown | ❌ | X coordinate in the layout |
| `position_y` | unknown | ❌ | Y coordinate in the layout |
| `width` | unknown | ❌ | Width of the table in the layout |
| `height` | unknown | ❌ | Height of the table in the layout |
| `shape` | unknown | ❌ | Shape of the table (rectangle, circle, custom) |
| `custom_shape_data` | unknown | ❌ | Data for custom shapes |
| `is_active` | unknown | ❌ | Whether the table is active |
| `notes` | unknown | ❌ | Additional notes about the table |
| `qrcode_enabled` | unknown | ❌ | Whether the QR code is enabled for this table |

### VariantGroupReadWithOptions

**Descrição:** Schema for reading a Variant Group with options.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `menu_item_id` | unknown | ❌ | - |
| `name` | string | ✅ | - |
| `description` | unknown | ❌ | - |
| `min_selection` | integer | ✅ | - |
| `max_selection` | integer | ✅ | - |
| `display_order` | integer | ✅ | - |
| `is_required` | boolean | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `requires_default_selection` | boolean | ❌ | - |
| `options` | Array[app__modules__tenants__restaurants__menu__schemas__variant_option__VariantOptionRead] | ❌ | - |
| `usage_count` | unknown | ❌ | - |

## 🔗 Endpoints Detalhados

### POST /api/modules/restaurants/kds/auth {#post-apimodulesrestaurantskdsauth}

**Resumo:** Authenticate Kds
**Descrição:** Autentica um app KDS usando UUID do restaurante + código temporário de 6 dígitos.
Endpoint público - não requer autenticação prévia.

Args:
    auth_request: UUID do restaurante e código temporário

Returns:
    Token JWT para acesso às APIs do KDS

**🔐 Autenticação:** Não requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [KDSAuthRequest](#kdsauthrequest)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [KDSAuthResponse](#kdsauthresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/restaurants/kds/auth" \
  -H "Content-Type: application/json" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/restaurants/kds/current-code {#get-apimodulesrestaurantskdscurrent-code}

**Resumo:** Get Current Temp Code
**Descrição:** Obtém o código temporário ativo atual para o tenant.
Apenas OWNERS e MANAGERS podem visualizar códigos.

Returns:
    Código ativo se existir, None caso contrário

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/kds/current-code" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/restaurants/kds/generate-temp-code {#post-apimodulesrestaurantskdsgenerate-temp-code}

**Resumo:** Generate Kds Temp Code
**Descrição:** Gera um código temporário de 6 dígitos para autenticação do KDS.
Apenas OWNERS e MANAGERS podem gerar códigos.

Args:
    expires_hours: Horas até expiração (padrão: 24h, máximo: 168h)

Returns:
    Código gerado com informações de expiração

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `expires_hours` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [KDSTempCodeGenerate](#kdstempcodegenerate)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/restaurants/kds/generate-temp-code" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/kds/orders {#get-apimodulesrestaurantskdsorders}

**Resumo:** Get KDS Orders
**Descrição:** Get orders displayed on the Kitchen Display System.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `status_filter` | string | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/kds/orders" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/kds/orders/ {#get-apimodulesrestaurantskdsorders}

**Resumo:** Read Kitchen Orders
**Descrição:** Recupera todos os pedidos da cozinha para o tenant atual, opcionalmente filtrados por status.
Requer papel de OWNER, MANAGER ou STAFF no tenant (customers não têm acesso).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `status` | string | query | ❌ | Filtrar por status (pending, preparing, ready, served) |
| `skip` | integer | query | ❌ | Número de registros para pular |
| `limit` | integer | query | ❌ | Número máximo de registros para retornar |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/kds/orders/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/restaurants/kds/orders/ {#post-apimodulesrestaurantskdsorders}

**Resumo:** Create Kitchen Order
**Descrição:** Cria um novo pedido na cozinha (KDS).
Requer papel de OWNER ou MANAGER no tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [KitchenOrderCreate](#kitchenordercreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [KitchenOrderRead](#kitchenorderread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/restaurants/kds/orders/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/restaurants/kds/orders/{order_id} {#get-apimodulesrestaurantskdsordersorder-id}

**Resumo:** Read Kitchen Order
**Descrição:** Recupera um pedido específico da cozinha pelo ID.
Requer papel de OWNER, MANAGER ou STAFF no tenant (customers não têm acesso).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `order_id` | string | path | ✅ | ID do pedido a ser recuperado |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [KitchenOrderRead](#kitchenorderread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/kds/orders/{order_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PATCH /api/modules/restaurants/kds/orders/{order_id}/status {#patch-apimodulesrestaurantskdsordersorder-idstatus}

**Resumo:** Update Kitchen Order Status
**Descrição:** Atualiza o status e/ou detalhes de um pedido da cozinha.
Requer papel de OWNER ou MANAGER no tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `order_id` | string | path | ✅ | ID do pedido a ser atualizado |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [KitchenOrderUpdate](#kitchenorderupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [KitchenOrderRead](#kitchenorderread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PATCH "http://localhost:8000/api/modules/restaurants/kds/orders/{order_id}/status" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/restaurants/kds/status {#get-apimodulesrestaurantskdsstatus}

**Resumo:** Get KDS Status
**Descrição:** Get the current status of the Kitchen Display System.

**🔐 Autenticação:** Requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/kds/status" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/menu/categories/ {#get-apimodulesrestaurantsmenucategories}

**Resumo:** Read Menu Categories
**Descrição:** Retrieve active menu categories for the current tenant.

- Use `digital_menu_id` to filter categories by digital menu
- Use `parent_id` to filter categories by their parent
- Use `only_top_level=true` to get only top-level categories (no parent)
- Use `include_children=false` to exclude children from the response

Requires at least costumer role in the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `digital_menu_id` | string | query | ❌ | Filter by digital menu ID |
| `parent_id` | string | query | ❌ | Filter categories by parent ID |
| `only_top_level` | boolean | query | ❌ | Only return top-level categories (no parent) |
| `include_children` | boolean | query | ❌ | Include children in the response |
| `include_items` | boolean | query | ❌ | Include menu items in the response |
| `skip` | integer | query | ❌ | Number of records to skip |
| `limit` | integer | query | ❌ | Maximum number of records to return |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/menu/categories/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/restaurants/menu/categories/ {#post-apimodulesrestaurantsmenucategories}

**Resumo:** Create Menu Category
**Descrição:** Create a new menu category for the current tenant.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [MenuCategoryCreate](#menucategorycreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [MenuCategoryReadSimple](#menucategoryreadsimple)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/restaurants/menu/categories/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/modules/restaurants/menu/categories/reorder {#put-apimodulesrestaurantsmenucategoriesreorder}

**Resumo:** Reorder Categories
**Descrição:** Reorder menu categories by updating their display_order.
Expects a list of objects with 'id' and 'display_order' fields.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** {'type': 'array', 'items': {'type': 'object', 'additionalProperties': True}, 'title': 'Category Orders'}

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/menu/categories/reorder" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/restaurants/menu/categories/{category_id} {#delete-apimodulesrestaurantsmenucategoriescategory-id}

**Resumo:** Delete Menu Category
**Descrição:** Deactivate (soft delete) a menu category by ID for the current tenant.
This will also deactivate all child categories recursively.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | The ID of the category to deactivate |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/restaurants/menu/categories/{category_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/menu/categories/{category_id} {#get-apimodulesrestaurantsmenucategoriescategory-id}

**Resumo:** Read Menu Category
**Descrição:** Retrieve a specific active menu category by ID for the current tenant.
Requires at least costumer role in the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | The ID of the category to retrieve |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [MenuCategoryRead](#menucategoryread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/menu/categories/{category_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/restaurants/menu/categories/{category_id} {#put-apimodulesrestaurantsmenucategoriescategory-id}

**Resumo:** Update Menu Category
**Descrição:** Update a specific menu category by ID for the current tenant.

- Can update name, description, display_order, is_active
- Can change parent_id to move the category in the hierarchy
- Cannot create cycles in the category hierarchy

Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | The ID of the category to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [MenuCategoryUpdate](#menucategoryupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [MenuCategoryRead](#menucategoryread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/menu/categories/{category_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/restaurants/menu/digital-menus/ {#get-apimodulesrestaurantsmenudigital-menus}

**Resumo:** Get Digital Menus
**Descrição:** Get all digital menus for the current tenant.
Requires tenant owner or staff permissions.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `include_categories` | boolean | query | ❌ | Include categories in response |
| `skip` | integer | query | ❌ | Number of records to skip |
| `limit` | integer | query | ❌ | Number of records to return |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/menu/digital-menus/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/restaurants/menu/digital-menus/ {#post-apimodulesrestaurantsmenudigital-menus}

**Resumo:** Create Digital Menu
**Descrição:** Create a new digital menu.
Requires tenant owner or staff permissions.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [DigitalMenuCreate](#digitalmenucreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [DigitalMenuRead](#digitalmenuread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/restaurants/menu/digital-menus/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/restaurants/menu/digital-menus/current {#get-apimodulesrestaurantsmenudigital-menuscurrent}

**Resumo:** Get Current Digital Menu
**Descrição:** Get the currently active digital menu based on time scheduling.
Requires tenant owner or staff permissions.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `timezone` | string | query | ❌ | Timezone for schedule checking |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [DigitalMenuWithCategories](#digitalmenuwithcategories)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/menu/digital-menus/current" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/restaurants/menu/digital-menus/reorder {#put-apimodulesrestaurantsmenudigital-menusreorder}

**Resumo:** Reorder Digital Menus
**Descrição:** Reorder digital menus by updating their display_order.
Requires tenant owner or staff permissions.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** {'type': 'array', 'items': {'type': 'object', 'additionalProperties': True}, 'title': 'Menu Orders'}

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/menu/digital-menus/reorder" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/restaurants/menu/digital-menus/{menu_id} {#delete-apimodulesrestaurantsmenudigital-menusmenu-id}

**Resumo:** Delete Digital Menu
**Descrição:** Hard delete a digital menu and all related data (categories, items, variants, modifiers, optionals).
This action is irreversible. Requires tenant owner or staff permissions.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `menu_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [DigitalMenuRead](#digitalmenuread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/restaurants/menu/digital-menus/{menu_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/menu/digital-menus/{menu_id} {#get-apimodulesrestaurantsmenudigital-menusmenu-id}

**Resumo:** Get Digital Menu
**Descrição:** Get a specific digital menu by ID.
Requires tenant owner or staff permissions.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `menu_id` | string | path | ✅ | - |
| `include_categories` | boolean | query | ❌ | Include categories in response |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [DigitalMenuWithCategories](#digitalmenuwithcategories)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/menu/digital-menus/{menu_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/restaurants/menu/digital-menus/{menu_id} {#put-apimodulesrestaurantsmenudigital-menusmenu-id}

**Resumo:** Update Digital Menu
**Descrição:** Update a digital menu.
Requires tenant owner or staff permissions.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `menu_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [DigitalMenuUpdate](#digitalmenuupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [DigitalMenuRead](#digitalmenuread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/menu/digital-menus/{menu_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/restaurants/menu/items/ {#get-apimodulesrestaurantsmenuitems}

**Resumo:** Read Menu Items
**Descrição:** Retrieve menu items for the current tenant, optionally filtered by digital menu and/or category.
By default, returns only active items; set 'include_inactive=true' to include soft deleted items.
By default, returns a simplified list; set 'include_details=true' for full data.
Requires at least costumer role in the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `digital_menu_id` | string | query | ❌ | Filter items by digital menu ID |
| `category_id` | string | query | ❌ | Filter items by category ID |
| `include_details` | boolean | query | ❌ | Include full details like variants and modifiers in the list |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/menu/items/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/restaurants/menu/items/ {#post-apimodulesrestaurantsmenuitems}

**Resumo:** Create Menu Item
**Descrição:** Create a new menu item for the current tenant.
Supports creating nested variant and modifier groups/options.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [MenuItemCreate](#menuitemcreate)

**📥 Respostas:**

**201:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/restaurants/menu/items/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/modules/restaurants/menu/items/reorder {#put-apimodulesrestaurantsmenuitemsreorder}

**Resumo:** Reorder Items
**Descrição:** Reorder menu items by updating their display_order.
Expects a list of objects with 'id' and 'display_order' fields.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** {'type': 'array', 'items': {'type': 'object', 'additionalProperties': True}, 'title': 'Item Orders'}

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/menu/items/reorder" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/restaurants/menu/items/{item_id} {#delete-apimodulesrestaurantsmenuitemsitem-id}

**Resumo:** Delete Menu Item
**Descrição:** Permanently delete a menu item by ID for the current tenant.
WARNING: This action cannot be undone. The item will be completely removed from the database.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `item_id` | string | path | ✅ | The ID of the item to permanently delete |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/restaurants/menu/items/{item_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/menu/items/{item_id} {#get-apimodulesrestaurantsmenuitemsitem-id}

**Resumo:** Read Menu Item
**Descrição:** Retrieve a specific active menu item by ID, including its category, variants, and modifiers.
Requires at least costumer role in the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `item_id` | string | path | ✅ | The ID of the item to retrieve |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/menu/items/{item_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/restaurants/menu/items/{item_id} {#put-apimodulesrestaurantsmenuitemsitem-id}

**Resumo:** Update Menu Item
**Descrição:** Update a specific menu item by ID for the current tenant.
Note: This currently updates only the base fields of the item.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `item_id` | string | path | ✅ | The ID of the item to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [MenuItemUpdate](#menuitemupdate)

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/menu/items/{item_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/restaurants/menu/modifier-groups {#get-apimodulesrestaurantsmenumodifier-groups}

**Resumo:** Get Modifier Groups
**Descrição:** Retrieve modifier groups for the current tenant.
These can be used as templates for other menu items.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `digital_menu_id` | string | query | ❌ | Filter groups by digital menu ID |
| `include_usage_count` | boolean | query | ❌ | Include usage count for each group |
| `skip` | integer | query | ❌ | Number of records to skip |
| `limit` | integer | query | ❌ | Maximum number of records to return |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/menu/modifier-groups" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/menu/modifier-groups/{group_id} {#get-apimodulesrestaurantsmenumodifier-groupsgroup-id}

**Resumo:** Get Modifier Group By Id
**Descrição:** Retrieve a specific modifier group by ID.
Used for template functionality.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `group_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ModifierGroupReadWithOptions](#modifiergroupreadwithoptions)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/menu/modifier-groups/{group_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/menu/optional-groups {#get-apimodulesrestaurantsmenuoptional-groups}

**Resumo:** Get Optional Groups
**Descrição:** Retrieve optional groups for the current tenant.
These can be used as templates for other menu items.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `digital_menu_id` | string | query | ❌ | Filter groups by digital menu ID |
| `include_usage_count` | boolean | query | ❌ | Include usage count for each group |
| `skip` | integer | query | ❌ | Number of records to skip |
| `limit` | integer | query | ❌ | Maximum number of records to return |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/menu/optional-groups" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/menu/optional-groups/{group_id} {#get-apimodulesrestaurantsmenuoptional-groupsgroup-id}

**Resumo:** Get Optional Group By Id
**Descrição:** Retrieve a specific optional group by ID.
Used for template functionality.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `group_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [OptionalGroupReadWithOptions](#optionalgroupreadwithoptions)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/menu/optional-groups/{group_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/menu/variant-groups {#get-apimodulesrestaurantsmenuvariant-groups}

**Resumo:** Get Variant Groups
**Descrição:** Retrieve variant groups for the current tenant.
These can be used as templates for other menu items.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `digital_menu_id` | string | query | ❌ | Filter groups by digital menu ID |
| `include_usage_count` | boolean | query | ❌ | Include usage count for each group |
| `skip` | integer | query | ❌ | Number of records to skip |
| `limit` | integer | query | ❌ | Maximum number of records to return |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/menu/variant-groups" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/menu/variant-groups/{group_id} {#get-apimodulesrestaurantsmenuvariant-groupsgroup-id}

**Resumo:** Get Variant Group By Id
**Descrição:** Retrieve a specific variant group by ID.
Used for template functionality.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `group_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [VariantGroupReadWithOptions](#variantgroupreadwithoptions)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/menu/variant-groups/{group_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/reservations/blacklist/ {#get-apimodulesrestaurantsreservationsblacklist}

**Resumo:** Get all blacklist entries
**Descrição:** Get all blacklist entries for the current tenant with optional filters.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `customer_id` | string | query | ❌ | Filter by customer ID |
| `blacklist_type` | string | query | ❌ | Filter by blacklist type |
| `is_active` | string | query | ❌ | Filter by active status |
| `skip` | integer | query | ❌ | Number of entries to skip |
| `limit` | integer | query | ❌ | Maximum number of entries to return |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/reservations/blacklist/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/restaurants/reservations/blacklist/ {#post-apimodulesrestaurantsreservationsblacklist}

**Resumo:** Create a new blacklist entry
**Descrição:** Create a new blacklist entry for the current tenant.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [CustomerBlacklistCreate](#customerblacklistcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CustomerBlacklistRead](#customerblacklistread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/restaurants/reservations/blacklist/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/restaurants/reservations/blacklist/check {#get-apimodulesrestaurantsreservationsblacklistcheck}

**Resumo:** Check if a customer is blacklisted
**Descrição:** Check if a customer is blacklisted for the current tenant.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `customer_id` | string | query | ❌ | Customer ID to check |
| `guest_email` | string | query | ❌ | Guest email to check |
| `guest_phone` | string | query | ❌ | Guest phone to check |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/reservations/blacklist/check" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### DELETE /api/modules/restaurants/reservations/blacklist/{blacklist_id} {#delete-apimodulesrestaurantsreservationsblacklistblacklist-id}

**Resumo:** Delete a blacklist entry
**Descrição:** Delete a blacklist entry for the current tenant.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `blacklist_id` | string | path | ✅ | The ID of the blacklist entry to delete |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/restaurants/reservations/blacklist/{blacklist_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/reservations/blacklist/{blacklist_id} {#get-apimodulesrestaurantsreservationsblacklistblacklist-id}

**Resumo:** Get a blacklist entry by ID
**Descrição:** Get a blacklist entry by ID for the current tenant.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `blacklist_id` | string | path | ✅ | The ID of the blacklist entry to get |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CustomerBlacklistRead](#customerblacklistread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/reservations/blacklist/{blacklist_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/restaurants/reservations/blacklist/{blacklist_id} {#put-apimodulesrestaurantsreservationsblacklistblacklist-id}

**Resumo:** Update a blacklist entry
**Descrição:** Update a blacklist entry for the current tenant.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `blacklist_id` | string | path | ✅ | The ID of the blacklist entry to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [CustomerBlacklistUpdate](#customerblacklistupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CustomerBlacklistRead](#customerblacklistread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/reservations/blacklist/{blacklist_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/restaurants/reservations/reservations/ {#get-apimodulesrestaurantsreservationsreservations}

**Resumo:** Get all reservations
**Descrição:** Get all reservations for the current tenant with optional filters.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `table_id` | string | query | ❌ | Filter by table ID |
| `customer_id` | string | query | ❌ | Filter by customer ID |
| `status` | string | query | ❌ | Filter by reservation status |
| `date_from` | string | query | ❌ | Filter by reservation date (from) |
| `date_to` | string | query | ❌ | Filter by reservation date (to) |
| `skip` | integer | query | ❌ | Number of reservations to skip |
| `limit` | integer | query | ❌ | Maximum number of reservations to return |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/reservations/reservations/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/restaurants/reservations/reservations/ {#post-apimodulesrestaurantsreservationsreservations}

**Resumo:** Create a new reservation
**Descrição:** Create a new reservation for the current tenant.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ReservationCreate](#reservationcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ReservationRead](#reservationread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/restaurants/reservations/reservations/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/restaurants/reservations/reservations/available-tables {#get-apimodulesrestaurantsreservationsreservationsavailable-tables}

**Resumo:** Find available tables for a reservation
**Descrição:** Find available tables for a reservation with the specified parameters.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `reservation_date` | string | query | ✅ | Date and time of the reservation |
| `duration_minutes` | integer | query | ❌ | Duration of the reservation in minutes |
| `party_size` | integer | query | ✅ | Number of people in the party |
| `layout_id` | string | query | ❌ | Filter by layout ID |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/reservations/reservations/available-tables" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### DELETE /api/modules/restaurants/reservations/reservations/{reservation_id} {#delete-apimodulesrestaurantsreservationsreservationsreservation-id}

**Resumo:** Delete a reservation
**Descrição:** Delete a reservation for the current tenant.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `reservation_id` | string | path | ✅ | The ID of the reservation to delete |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/restaurants/reservations/reservations/{reservation_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/reservations/reservations/{reservation_id} {#get-apimodulesrestaurantsreservationsreservationsreservation-id}

**Resumo:** Get a reservation by ID
**Descrição:** Get a reservation by ID for the current tenant.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `reservation_id` | string | path | ✅ | The ID of the reservation to get |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ReservationRead](#reservationread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/reservations/reservations/{reservation_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/restaurants/reservations/reservations/{reservation_id} {#put-apimodulesrestaurantsreservationsreservationsreservation-id}

**Resumo:** Update a reservation
**Descrição:** Update a reservation for the current tenant.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `reservation_id` | string | path | ✅ | The ID of the reservation to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ReservationUpdate](#reservationupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ReservationRead](#reservationread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/reservations/reservations/{reservation_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PATCH /api/modules/restaurants/reservations/reservations/{reservation_id}/status {#patch-apimodulesrestaurantsreservationsreservationsreservation-idstatus}

**Resumo:** Update reservation status
**Descrição:** Update the status of a reservation for the current tenant.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `reservation_id` | string | path | ✅ | The ID of the reservation to update |
| `status` | string | query | ✅ | The new status for the reservation |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ReservationRead](#reservationread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PATCH "http://localhost:8000/api/modules/restaurants/reservations/reservations/{reservation_id}/status" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/settings/ {#get-apimodulesrestaurantssettings}

**Resumo:** Get Restaurant Settings
**Descrição:** Retrieve restaurant-specific tenant settings including tenant_slug, WiFi networks, social media links, and address extensions.

**🔐 Autenticação:** Requerida

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [RestaurantTenantSettingsRead](#restauranttenantsettingsread)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/settings/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/restaurants/settings/ {#put-apimodulesrestaurantssettings}

**Resumo:** Update Restaurant Settings
**Descrição:** Update restaurant-specific tenant settings with comprehensive validation.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [RestaurantTenantSettingsUpdate](#restauranttenantsettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [RestaurantTenantSettingsRead](#restauranttenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/settings/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/modules/restaurants/settings/business/ {#put-apimodulesrestaurantssettingsbusiness}

**Resumo:** Update Restaurant Business Settings
**Descrição:** Update restaurant business settings (tenant_slug).

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [RestaurantBusinessSettingsUpdate](#restaurantbusinesssettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [RestaurantTenantSettingsRead](#restauranttenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/settings/business/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/modules/restaurants/settings/location/ {#put-apimodulesrestaurantssettingslocation}

**Resumo:** Update Restaurant Location Settings
**Descrição:** Update restaurant location settings.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [RestaurantLocationSettingsUpdate](#restaurantlocationsettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [RestaurantTenantSettingsRead](#restauranttenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/settings/location/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/modules/restaurants/settings/operating-hours/ {#put-apimodulesrestaurantssettingsoperating-hours}

**Resumo:** Update Operating Hours
**Descrição:** Update restaurant operating hours with service hours, break periods, and happy hour.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [RestaurantOperatingHoursUpdate](#restaurantoperatinghoursupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [RestaurantTenantSettingsRead](#restauranttenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/settings/operating-hours/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/modules/restaurants/settings/social-media/ {#put-apimodulesrestaurantssettingssocial-media}

**Resumo:** Update Restaurant Social Media Settings
**Descrição:** Update restaurant social media settings.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [RestaurantSocialMediaSettingsUpdate](#restaurantsocialmediasettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [RestaurantTenantSettingsRead](#restauranttenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/settings/social-media/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/modules/restaurants/settings/special-calendar/ {#put-apimodulesrestaurantssettingsspecial-calendar}

**Resumo:** Update Special Calendar
**Descrição:** Update restaurant special calendar with holidays and special events.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [RestaurantSpecialCalendarUpdate](#restaurantspecialcalendarupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [RestaurantTenantSettingsRead](#restauranttenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/settings/special-calendar/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/modules/restaurants/settings/wifi/ {#put-apimodulesrestaurantssettingswifi}

**Resumo:** Update Restaurant Wifi Settings
**Descrição:** Update restaurant WiFi settings.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [RestaurantWiFiSettingsUpdate](#restaurantwifisettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [RestaurantTenantSettingsRead](#restauranttenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/settings/wifi/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/restaurants/settings/zones/ {#get-apimodulesrestaurantssettingszones}

**Resumo:** Get Restaurant Zones
**Descrição:** Get all available zones for restaurant including zones from tables and custom zones.

**🔐 Autenticação:** Requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/settings/zones/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/restaurants/settings/zones/ {#post-apimodulesrestaurantssettingszones}

**Resumo:** Add Custom Zone
**Descrição:** Add a custom zone to restaurant.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** {'additionalProperties': {'type': 'string'}, 'type': 'object', 'title': 'Zone Data'}

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [RestaurantTenantSettingsRead](#restauranttenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/restaurants/settings/zones/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/restaurants/settings/zones/{zone_name} {#delete-apimodulesrestaurantssettingszoneszone-name}

**Resumo:** Remove Custom Zone
**Descrição:** Remove a custom zone from restaurant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `zone_name` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [RestaurantTenantSettingsRead](#restauranttenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/restaurants/settings/zones/{zone_name}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/tables/layouts/ {#get-apimodulesrestaurantstableslayouts}

**Resumo:** Get all table layouts
**Descrição:** Get all table layouts for the current tenant with optional filters.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `is_active` | string | query | ❌ | Filter by active status |
| `include_tables` | boolean | query | ❌ | Include tables in the response |
| `skip` | integer | query | ❌ | Number of layouts to skip |
| `limit` | integer | query | ❌ | Maximum number of layouts to return |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/tables/layouts/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/restaurants/tables/layouts/ {#post-apimodulesrestaurantstableslayouts}

**Resumo:** Create a new table layout
**Descrição:** Create a new table layout for the current tenant.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TableLayoutCreate](#tablelayoutcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TableLayoutRead](#tablelayoutread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/restaurants/tables/layouts/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/restaurants/tables/layouts/{layout_id} {#delete-apimodulesrestaurantstableslayoutslayout-id}

**Resumo:** Delete a table layout
**Descrição:** Delete a table layout for the current tenant.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `layout_id` | string | path | ✅ | The ID of the layout to delete |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/restaurants/tables/layouts/{layout_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/tables/layouts/{layout_id} {#get-apimodulesrestaurantstableslayoutslayout-id}

**Resumo:** Get a table layout by ID
**Descrição:** Get a table layout by ID for the current tenant.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `layout_id` | string | path | ✅ | The ID of the layout to get |
| `include_tables` | boolean | query | ❌ | Include tables in the response |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TableLayoutRead](#tablelayoutread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/tables/layouts/{layout_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/restaurants/tables/layouts/{layout_id} {#put-apimodulesrestaurantstableslayoutslayout-id}

**Resumo:** Update a table layout
**Descrição:** Update a table layout for the current tenant.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `layout_id` | string | path | ✅ | The ID of the layout to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TableLayoutUpdate](#tablelayoutupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TableLayoutRead](#tablelayoutread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/tables/layouts/{layout_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/restaurants/tables/tables/ {#get-apimodulesrestaurantstablestables}

**Resumo:** Get all tables
**Descrição:** Get all tables for the current tenant with optional filters.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `layout_id` | string | query | ❌ | Filter by layout ID |
| `status` | string | query | ❌ | Filter by table status |
| `capacity_min` | string | query | ❌ | Minimum capacity |
| `capacity_max` | string | query | ❌ | Maximum capacity |
| `is_active` | string | query | ❌ | Filter by active status |
| `zone` | string | query | ❌ | Filter by zone |
| `skip` | integer | query | ❌ | Number of tables to skip |
| `limit` | integer | query | ❌ | Maximum number of tables to return |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/tables/tables/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/restaurants/tables/tables/ {#post-apimodulesrestaurantstablestables}

**Resumo:** Create a new table
**Descrição:** Create a new table for the current tenant.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TableCreate](#tablecreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TableRead](#tableread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/restaurants/tables/tables/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/restaurants/tables/tables/available {#get-apimodulesrestaurantstablestablesavailable}

**Resumo:** Get available tables
**Descrição:** Get all available tables for the current tenant.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `capacity` | string | query | ❌ | Minimum capacity required |
| `layout_id` | string | query | ❌ | Filter by layout ID |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/tables/tables/available" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### DELETE /api/modules/restaurants/tables/tables/{table_id} {#delete-apimodulesrestaurantstablestablestable-id}

**Resumo:** Delete a table
**Descrição:** Delete a table for the current tenant.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `table_id` | string | path | ✅ | The ID of the table to delete |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/restaurants/tables/tables/{table_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/tables/tables/{table_id} {#get-apimodulesrestaurantstablestablestable-id}

**Resumo:** Get a table by ID
**Descrição:** Get a table by ID for the current tenant.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `table_id` | string | path | ✅ | The ID of the table to get |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TableRead](#tableread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/tables/tables/{table_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/restaurants/tables/tables/{table_id} {#put-apimodulesrestaurantstablestablestable-id}

**Resumo:** Update a table
**Descrição:** Update a table for the current tenant.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `table_id` | string | path | ✅ | The ID of the table to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TableUpdate](#tableupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TableRead](#tableread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/tables/tables/{table_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PATCH /api/modules/restaurants/tables/tables/{table_id}/status {#patch-apimodulesrestaurantstablestablestable-idstatus}

**Resumo:** Update table status
**Descrição:** Update the status of a table for the current tenant.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `table_id` | string | path | ✅ | The ID of the table to update |
| `status` | string | query | ✅ | The new status for the table |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TableRead](#tableread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PATCH "http://localhost:8000/api/modules/restaurants/tables/tables/{table_id}/status" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
