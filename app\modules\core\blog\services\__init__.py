"""
Blog Services

Business logic layer for the blog system.
"""

from .blog_post_service import Blog<PERSON>ostService
from .blog_category_service import BlogCategoryService
from .blog_tag_service import BlogTagService
from .blog_author_service import BlogAuthorService
from .blog_comment_service import Blog<PERSON>ommentService
from .blog_seo_service import Blog<PERSON>OService
from .blog_search_service import BlogSearchService

# Service instances
blog_post_service = BlogPostService()
blog_category_service = BlogCategoryService()
blog_tag_service = BlogTagService()
blog_author_service = BlogAuthorService()
blog_comment_service = BlogCommentService()
blog_seo_service = BlogSEOService()
blog_search_service = BlogSearchService()

__all__ = [
    "BlogPostService",
    "BlogCategoryService",
    "BlogTagService",
    "BlogAuthorService",
    "BlogCommentService",
    "BlogSEOService",
    "BlogSearchService",
    "blog_post_service",
    "blog_category_service",
    "blog_tag_service",
    "blog_author_service",
    "blog_comment_service",
    "blog_seo_service",
    "blog_search_service",
]
