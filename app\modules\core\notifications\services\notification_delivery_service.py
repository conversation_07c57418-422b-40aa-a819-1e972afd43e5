"""
Notification Delivery Service

Serviço para entrega e direcionamento de notificações.
"""

import logging
from datetime import datetime
from typing import List, Set
from uuid import UUID

from sqlalchemy import and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.modules.core.users.models.user import User
from app.modules.core.users.services.tenant_user_association_service import TenantUserAssociationService
from app.modules.core.tenants.models.tenant import Tenant

from ..models import Notification, NotificationTargetType

logger = logging.getLogger(__name__)


class NotificationDeliveryService:
    """Serviço para entrega de notificações."""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_target_users(self, notification: Notification) -> List[User]:
        """
        Obtém lista de usuários que devem receber a notificação.
        
        Args:
            notification: Notificação a ser entregue
            
        Returns:
            Lista de usuários destinatários
        """
        target_type = notification.target_type
        
        if target_type == NotificationTargetType.ALL_USERS:
            return await self._get_all_users()
        
        elif target_type == NotificationTargetType.SPECIFIC_USER:
            return await self._get_specific_user(notification.target_id)
        
        elif target_type == NotificationTargetType.TENANT_USERS:
            return await self._get_tenant_users(notification.tenant_id)
        
        elif target_type == NotificationTargetType.TENANT_OWNERS:
            return await self._get_tenant_owners(notification.tenant_id)
        
        elif target_type == NotificationTargetType.TENANT_STAFF:
            return await self._get_tenant_staff(notification.tenant_id)
        
        elif target_type == NotificationTargetType.TENANT_CUSTOMERS:
            return await self._get_tenant_customers(notification.tenant_id)
        
        else:
            logger.warning(f"Tipo de destinatário não reconhecido: {target_type}")
            return []

    async def _get_all_users(self) -> List[User]:
        """Obtém todos os usuários ativos."""
        stmt = select(User).where(User.is_active == True)
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def _get_specific_user(self, user_id: UUID) -> List[User]:
        """Obtém usuário específico."""
        if not user_id:
            return []
        
        stmt = select(User).where(
            and_(
                User.id == user_id,
                User.is_active == True
            )
        )
        result = await self.db.execute(stmt)
        user = result.scalar_one_or_none()
        
        return [user] if user else []

    async def _get_tenant_users(self, tenant_id: UUID) -> List[User]:
        """Obtém todos os usuários de um tenant."""
        if not tenant_id:
            return []
        
        # TODO: Implementar query com join para TenantUserAssociation
        # Por enquanto, retorna lista vazia
        logger.warning("_get_tenant_users não implementado - requer TenantUserAssociation")
        return []

    async def _get_tenant_owners(self, tenant_id: UUID) -> List[User]:
        """Obtém proprietários de um tenant."""
        if not tenant_id:
            return []
        
        # TODO: Implementar query com join para TenantUserAssociation
        # Filtrar por role = 'owner'
        logger.warning("_get_tenant_owners não implementado - requer TenantUserAssociation")
        return []

    async def _get_tenant_staff(self, tenant_id: UUID) -> List[User]:
        """Obtém funcionários de um tenant."""
        if not tenant_id:
            return []
        
        # TODO: Implementar query com join para TenantUserAssociation
        # Filtrar por role = 'staff'
        logger.warning("_get_tenant_staff não implementado - requer TenantUserAssociation")
        return []

    async def _get_tenant_customers(self, tenant_id: UUID) -> List[User]:
        """Obtém clientes de um tenant."""
        if not tenant_id:
            return []
        
        # TODO: Implementar query com join para TenantUserAssociation
        # Filtrar por role = 'customer'
        logger.warning("_get_tenant_customers não implementado - requer TenantUserAssociation")
        return []

    async def validate_delivery_permissions(
        self,
        notification: Notification,
        sender: User
    ) -> bool:
        """
        Valida se o remetente tem permissão para enviar a notificação.
        
        Args:
            notification: Notificação a ser enviada
            sender: Usuário remetente
            
        Returns:
            True se tem permissão
        """
        # Admin pode enviar para qualquer um
        if sender.system_role == "admin":
            return True
        
        target_type = notification.target_type
        
        # Usuários normais só podem enviar para usuários específicos
        if target_type == NotificationTargetType.SPECIFIC_USER:
            return True
        
        # Tenant owners e staff podem enviar para usuários do seu tenant
        if target_type in [
            NotificationTargetType.TENANT_USERS,
            NotificationTargetType.TENANT_STAFF,
            NotificationTargetType.TENANT_CUSTOMERS
        ]:
            # Verifica se o sender é owner ou staff do tenant
            if notification.tenant_id:
                association_service = TenantUserAssociationService(self.db)
                user_role = await association_service.get_user_tenant_role(
                    db=self.db,
                    user_id=sender.id,
                    tenant_id=notification.tenant_id
                )

                # Permite se for owner ou staff
                if user_role in ["owner", "staff"]:
                    return True

            return False
        
        # Outros tipos requerem permissões especiais
        if target_type == NotificationTargetType.ALL_USERS:
            return sender.system_role == "admin"
        
        if target_type == NotificationTargetType.TENANT_OWNERS:
            return sender.system_role == "admin"
        
        return False

    async def calculate_delivery_count(self, notification: Notification) -> int:
        """
        Calcula quantos usuários receberão a notificação.
        
        Args:
            notification: Notificação
            
        Returns:
            Número estimado de destinatários
        """
        target_users = await self.get_target_users(notification)
        return len(target_users)

    async def get_delivery_preview(
        self,
        notification: Notification,
        limit: int = 10
    ) -> dict:
        """
        Obtém preview da entrega da notificação.
        
        Args:
            notification: Notificação
            limit: Limite de usuários no preview
            
        Returns:
            Dicionário com informações do preview
        """
        target_users = await self.get_target_users(notification)
        total_recipients = len(target_users)
        
        # Limita usuários para preview
        preview_users = target_users[:limit]
        
        user_previews = [
            {
                "id": str(user.id),
                "email": user.email,
                "name": getattr(user, 'name', user.email.split('@')[0])
            }
            for user in preview_users
        ]
        
        return {
            "total_recipients": total_recipients,
            "preview_users": user_previews,
            "has_more": total_recipients > limit,
            "target_type": notification.target_type.value,
            "tenant_id": str(notification.tenant_id) if notification.tenant_id else None
        }

    async def mark_as_delivered(
        self,
        notification: Notification,
        delivered_count: int
    ) -> None:
        """
        Marca notificação como entregue e atualiza contador.
        
        Args:
            notification: Notificação
            delivered_count: Número de entregas realizadas
        """
        notification.delivery_count = delivered_count
        notification.status = "sent"
        notification.sent_at = datetime.utcnow()
        
        await self.db.commit()
        
        logger.info(
            f"Notificação {notification.id} marcada como entregue para {delivered_count} usuários"
        )

    def get_user_notification_preferences(self, user: User) -> dict:
        """
        Obtém preferências de notificação do usuário.
        
        Args:
            user: Usuário
            
        Returns:
            Dicionário com preferências
        """
        # TODO: Implementar sistema de preferências
        # Por enquanto, retorna preferências padrão
        return {
            "email_notifications": True,
            "push_notifications": True,
            "in_app_notifications": True,
            "notification_frequency": "immediate",
            "quiet_hours": {
                "enabled": False,
                "start": "22:00",
                "end": "08:00"
            }
        }

    def should_deliver_to_user(
        self,
        notification: Notification,
        user: User
    ) -> bool:
        """
        Verifica se deve entregar notificação para usuário específico.
        
        Args:
            notification: Notificação
            user: Usuário
            
        Returns:
            True se deve entregar
        """
        # Verifica se usuário está ativo
        if not user.is_active:
            return False
        
        # Verifica se notificação não expirou
        if notification.is_expired:
            return False
        
        # Verifica se usuário já deletou a notificação
        if notification.is_deleted_by_user(str(user.id)):
            return False
        
        # Verifica preferências do usuário
        preferences = self.get_user_notification_preferences(user)
        if not preferences.get("in_app_notifications", True):
            return False
        
        # TODO: Implementar verificação de quiet hours
        
        return True
