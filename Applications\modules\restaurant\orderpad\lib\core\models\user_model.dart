import 'package:hive/hive.dart';

part 'user_model.g.dart';

@HiveType(typeId: 7)
class UserModel extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String name;

  @HiveField(2)
  String email;

  @HiveField(3)
  String role; // admin, staff, manager

  @HiveField(4)
  String? phone;

  @HiveField(5)
  String? avatar;

  @HiveField(6)
  bool isActive;

  @HiveField(7)
  List<String> permissions;

  @HiveField(8)
  DateTime createdAt;

  @HiveField(9)
  DateTime? updatedAt;

  @HiveField(10)
  DateTime? lastLoginAt;

  @HiveField(11)
  String? department;

  @HiveField(12)
  double? hourlyRate;

  UserModel({
    required this.id,
    required this.name,
    required this.email,
    required this.role,
    this.phone,
    this.avatar,
    this.isActive = true,
    this.permissions = const [],
    required this.createdAt,
    this.updatedAt,
    this.lastLoginAt,
    this.department,
    this.hourlyRate,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      role: json['role'],
      phone: json['phone'],
      avatar: json['avatar'],
      isActive: json['isActive'] ?? true,
      permissions: json['permissions'] != null 
          ? List<String>.from(json['permissions']) 
          : [],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
      lastLoginAt: json['lastLoginAt'] != null 
          ? DateTime.parse(json['lastLoginAt']) 
          : null,
      department: json['department'],
      hourlyRate: json['hourlyRate']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'role': role,
      'phone': phone,
      'avatar': avatar,
      'isActive': isActive,
      'permissions': permissions,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'lastLoginAt': lastLoginAt?.toIso8601String(),
      'department': department,
      'hourlyRate': hourlyRate,
    };
  }

  bool get isAdmin => role == 'admin';
  bool get isStaff => role == 'staff';
  bool get isManager => role == 'manager';

  bool hasPermission(String permission) {
    return permissions.contains(permission) || isAdmin;
  }
}