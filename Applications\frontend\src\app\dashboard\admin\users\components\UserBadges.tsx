'use client';

import Image from 'next/image';
import { Award, Star, Trophy, Medal, Crown, Shield, Heart, Zap } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

import { UserBadge } from '@/types/user';

interface UserBadgesProps {
  badges: UserBadge[];
}

export function UserBadges({ badges }: UserBadgesProps) {
  const getBadgeIcon = (badgeName: string) => {
    const name = badgeName.toLowerCase();
    
    if (name.includes('first') || name.includes('primeiro')) return Star;
    if (name.includes('loyal') || name.includes('fiel')) return Heart;
    if (name.includes('premium') || name.includes('vip')) return Crown;
    if (name.includes('champion') || name.includes('campeao')) return Trophy;
    if (name.includes('expert') || name.includes('especialista')) return Shield;
    if (name.includes('fast') || name.includes('rapido')) return Zap;
    if (name.includes('medal') || name.includes('medalha')) return Medal;
    
    return Award;
  };

  const getBadgeColor = (badgeName: string) => {
    const name = badgeName.toLowerCase();
    
    if (name.includes('gold') || name.includes('ouro')) return 'bg-yellow-100 text-yellow-800 border-yellow-300';
    if (name.includes('silver') || name.includes('prata')) return 'bg-gray-100 text-gray-800 border-gray-300';
    if (name.includes('bronze') || name.includes('bronze')) return 'bg-orange-100 text-orange-800 border-orange-300';
    if (name.includes('premium') || name.includes('vip')) return 'bg-purple-100 text-purple-800 border-purple-300';
    if (name.includes('diamond') || name.includes('diamante')) return 'bg-blue-100 text-blue-800 border-blue-300';
    
    return 'bg-green-100 text-green-800 border-green-300';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const groupBadgesByCategory = (badges: UserBadge[]) => {
    const categories: { [key: string]: UserBadge[] } = {
      'Conquistas': [],
      'Fidelidade': [],
      'Experiência': [],
      'Especiais': [],
    };

    badges.forEach(badge => {
      const name = badge.name.toLowerCase();
      
      if (name.includes('first') || name.includes('primeiro') || name.includes('achievement')) {
        categories['Conquistas'].push(badge);
      } else if (name.includes('loyal') || name.includes('fiel') || name.includes('return')) {
        categories['Fidelidade'].push(badge);
      } else if (name.includes('expert') || name.includes('master') || name.includes('pro')) {
        categories['Experiência'].push(badge);
      } else {
        categories['Especiais'].push(badge);
      }
    });

    // Remove empty categories
    Object.keys(categories).forEach(key => {
      if (categories[key].length === 0) {
        delete categories[key];
      }
    });

    return categories;
  };

  if (badges.length === 0) {
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Badges Conquistadas</h3>
        <Card>
          <CardContent className="p-6 text-center text-gray-500">
            <Award className="h-8 w-8 mx-auto mb-2 text-gray-400" />
            <p>Nenhuma badge conquistada ainda</p>
            <p className="text-sm mt-1">
              As badges são conquistadas através de ações e conquistas no sistema
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const categorizedBadges = groupBadgesByCategory(badges);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Badges Conquistadas</h3>
        <Badge variant="secondary" className="bg-blue-100 text-blue-800">
          {badges.length} {badges.length === 1 ? 'Badge' : 'Badges'}
        </Badge>
      </div>

      {Object.entries(categorizedBadges).map(([category, categoryBadges]) => (
        <div key={category} className="space-y-3">
          <h4 className="font-medium text-gray-700 border-b pb-1">{category}</h4>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
            {categoryBadges.map((badge) => {
              const IconComponent = getBadgeIcon(badge.name);
              const colorClass = getBadgeColor(badge.name);
              
              return (
                <Card key={badge.id} className={`border-2 ${colorClass.split(' ').pop()?.replace('text-', 'border-')}`}>
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <div className={`w-12 h-12 rounded-full flex items-center justify-center relative ${colorClass.split(' ').slice(0, 2).join(' ')}`}>
                        {badge.icon_url ? (
                          <div className="w-8 h-8 relative">
                            <Image
                              src={badge.icon_url}
                              alt={badge.name}
                              fill
                              className="object-contain"
                            />
                          </div>
                        ) : (
                          <IconComponent className="h-6 w-6" />
                        )}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <h5 className="font-semibold text-sm truncate">{badge.name}</h5>
                        <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                          {badge.description}
                        </p>
                        <p className="text-xs text-gray-500 mt-2">
                          Conquistada em {formatDate(badge.earned_at)}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      ))}

      {/* Badge Statistics */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50">
        <CardHeader>
          <CardTitle className="text-sm flex items-center gap-2">
            <Trophy className="h-4 w-4" />
            Estatísticas de Badges
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold text-blue-600">{badges.length}</p>
              <p className="text-xs text-gray-600">Total</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-green-600">
                {Object.keys(categorizedBadges).length}
              </p>
              <p className="text-xs text-gray-600">Categorias</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-purple-600">
                {badges.filter(b => b.name.toLowerCase().includes('premium') || 
                                   b.name.toLowerCase().includes('vip') ||
                                   b.name.toLowerCase().includes('special')).length}
              </p>
              <p className="text-xs text-gray-600">Especiais</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-orange-600">
                {badges.filter(b => {
                  const earnedDate = new Date(b.earned_at);
                  const thirtyDaysAgo = new Date();
                  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                  return earnedDate >= thirtyDaysAgo;
                }).length}
              </p>
              <p className="text-xs text-gray-600">Últimos 30 dias</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
