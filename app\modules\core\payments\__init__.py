"""Core payments module for multi-tenant payment processing."""

from fastapi import APIRouter  # noqa: E402

from app.modules.core.payments.api import router as payments_router  # noqa: E402

# Export the router for registration with the main application
router = APIRouter(prefix="/payments")
router.include_router(payments_router)

__all__ = ["router"]

# Export models, schemas, and services for direct import
