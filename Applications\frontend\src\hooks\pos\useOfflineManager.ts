'use client';

import { useEffect, useCallback, useState } from 'react';
import { usePOSStore } from '@/stores/posStore';
import { ConnectionStatus } from '@/types/pos';

/**
 * Hook para gerenciar funcionalidades offline do POS
 * Monitora conexão, sincronização e cache offline
 */
export function useOfflineManager() {
  const [isInitialized, setIsInitialized] = useState(false);

  const {
    isOfflineMode,
    connectionStatus,
    setOfflineMode,
    setConnectionStatus,
    triggerManualSync
  } = usePOSStore();

  // Check network connection
  const checkConnection = useCallback(async () => {
    try {
      // Use navigator.onLine as primary check
      const isOnline = navigator.onLine;

      const newStatus: ConnectionStatus = {
        isOnline,
        lastChecked: new Date().toISOString()
      };

      setConnectionStatus(newStatus);
      return isOnline;
    } catch (error) {
      const offlineStatus: ConnectionStatus = {
        isOnline: false,
        lastChecked: new Date().toISOString()
      };

      setConnectionStatus(offlineStatus);
      return false;
    }
  }, [setConnectionStatus]);

  // Initialize offline capabilities
  const initializeOfflineMode = useCallback(async () => {
    if (isInitialized) return;

    try {
      // Check initial connection
      await checkConnection();

      // Don't automatically enable offline mode
      // Let the user manually toggle if needed

      setIsInitialized(true);
    } catch (error) {
      console.error('Failed to initialize offline mode:', error);
    }
  }, [isInitialized, checkConnection]);

  // Auto-sync when coming back online
  const handleOnlineSync = useCallback(async () => {
    if (connectionStatus?.isOnline && triggerManualSync) {
      try {
        await triggerManualSync();
      } catch (error) {
        console.error('Auto-sync failed:', error);
      }
    }
  }, [connectionStatus?.isOnline, triggerManualSync]);

  // Monitor network status
  useEffect(() => {
    const handleOnline = () => {
      checkConnection().then(isOnline => {
        if (isOnline) {
          setOfflineMode(false);
          handleOnlineSync();
        }
      });
    };

    const handleOffline = () => {
      setOfflineMode(true);
      setConnectionStatus({
        isOnline: false,
        lastChecked: new Date().toISOString()
      });
    };

    // Listen to browser online/offline events
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Periodic connection check
    const intervalId = setInterval(checkConnection, 30000); // Check every 30 seconds

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      clearInterval(intervalId);
    };
  }, [checkConnection, setOfflineMode, setConnectionStatus, handleOnlineSync]);

  // Cache management
  const clearOfflineCache = useCallback(async () => {
    try {
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        );
      }

      // Clear localStorage cache
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith('pos-offline-')) {
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.error('Failed to clear offline cache:', error);
    }
  }, []);

  // Force offline mode for testing
  const toggleOfflineMode = useCallback(() => {
    setOfflineMode(!isOfflineMode);
  }, [isOfflineMode, setOfflineMode]);

  // Get offline storage info
  const getOfflineStorageInfo = useCallback(async () => {
    try {
      if ('storage' in navigator && 'estimate' in navigator.storage) {
        const estimate = await navigator.storage.estimate();
        return {
          quota: estimate.quota,
          usage: estimate.usage,
          available: estimate.quota ? estimate.quota - (estimate.usage || 0) : 0
        };
      }
    } catch (error) {
      console.error('Failed to get storage info:', error);
    }
    return null;
  }, []);

  return {
    // State
    isOnline: connectionStatus?.isOnline ?? true,
    isOfflineMode,
    connectionStatus,
    isInitialized,

    // Actions
    initializeOfflineMode,
    checkConnection,
    clearOfflineCache,
    toggleOfflineMode,
    getOfflineStorageInfo,

    // Sync
    triggerSync: triggerManualSync
  };
}
