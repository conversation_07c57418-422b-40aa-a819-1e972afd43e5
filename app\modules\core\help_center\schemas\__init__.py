"""
Help Center Schemas

Schemas Pydantic para validação de dados do sistema de help center.
"""

from .ticket_schemas import (
    TicketCreate, TicketUpdate, TicketResponse, TicketListResponse,
    TicketFilters, TicketMarkAsRead, TicketAssignmentRequest, BulkTicketOperation,
    TicketCategoryEnum, TicketStatusEnum, TicketPriorityEnum
)
from .message_schemas import (
    TicketMessageCreate, TicketMessageResponse, TicketMessageListResponse,
    MessageMarkAsRead, MessageTypeEnum
)
from .knowledge_base_schemas import (
    KnowledgeBaseArticleCreate, KnowledgeBaseArticleUpdate,
    KnowledgeBaseArticleResponse, KnowledgeBaseArticleListResponse,
    KnowledgeBaseSearchRequest
)
from .analytics_schemas import (
    HelpCenterMetricsResponse, AdminDashboardMetrics
)

__all__ = [
    # Ticket schemas
    "TicketCreate", "TicketUpdate", "TicketResponse", "TicketListResponse",
    "TicketFilters", "TicketMarkAsRead", "TicketAssignmentRequest", "BulkTicketOperation",
    "TicketCategoryEnum", "TicketStatusEnum", "TicketPriorityEnum",

    # Message schemas
    "TicketMessageCreate", "TicketMessageResponse", "TicketMessageListResponse",
    "MessageMarkAsRead", "MessageTypeEnum",

    # Knowledge Base schemas
    "KnowledgeBaseArticleCreate", "KnowledgeBaseArticleUpdate",
    "KnowledgeBaseArticleResponse", "KnowledgeBaseArticleListResponse",
    "KnowledgeBaseSearchRequest",

    # Analytics schemas
    "HelpCenterMetricsResponse", "AdminDashboardMetrics"
]
