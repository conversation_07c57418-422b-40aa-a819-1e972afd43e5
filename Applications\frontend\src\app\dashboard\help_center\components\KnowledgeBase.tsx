'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/lib/auth/AuthProvider';
import { apiClient } from '@/lib/api/client';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { KnowledgeBaseManagement } from './KnowledgeBaseManagement';
import {
  MagnifyingGlassIcon,
  BookOpenIcon,
  EyeIcon,
  HandThumbUpIcon,
  HandThumbDownIcon,
  TagIcon
} from '@heroicons/react/24/outline';

interface KnowledgeBaseArticle {
  id: string;
  title: string;
  content: string;
  category?: string;
  tags?: string;
  view_count: number;
  helpful_count: number;
  not_helpful_count: number;
  helpfulness_ratio: number;
  created_at: string;
  created_by_name?: string;
}

interface KnowledgeBaseResponse {
  articles: KnowledgeBaseArticle[];
  total: number;
  page: number;
  per_page: number;
  has_next: boolean;
  has_prev: boolean;
}

export function KnowledgeBase() {
  const { user, isAdmin } = useAuth();

  // Hooks sempre no topo
  const [articles, setArticles] = useState<KnowledgeBaseArticle[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [categories, setCategories] = useState<string[]>([]);
  const [expandedArticle, setExpandedArticle] = useState<string | null>(null);

  const fetchArticles = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: '1',
        per_page: '20'
      });

      if (searchQuery) params.append('search', searchQuery);
      if (selectedCategory) params.append('category', selectedCategory);

      // Para admin, não enviar X-Tenant-ID header se necessário
      const config = {};

      const response = await apiClient.get<KnowledgeBaseResponse>(
        `/modules/core/help-center/kb/articles?${params.toString()}`,
        config
      );

      // O apiClient pode retornar diferentes estruturas, vamos tratar todas
      let articles = [];

      if (response && typeof response === 'object') {
        // Se response tem articles diretamente
        if (Array.isArray((response as any).articles)) {
          articles = (response as any).articles;
        }
        // Se response.data tem articles
        else if ((response as any).data && Array.isArray((response as any).data.articles)) {
          articles = (response as any).data.articles;
        }
        // Se response é um array diretamente
        else if (Array.isArray(response)) {
          articles = response;
        }
        // Se response.data é um array diretamente
        else if ((response as any).data && Array.isArray((response as any).data)) {
          articles = (response as any).data;
        }
      }

      setArticles(articles);

      // Extract unique categories
      const uniqueCategories = Array.from(
        new Set(articles.map((article: any) => article.category).filter(Boolean))
      ) as string[];
      setCategories(uniqueCategories);

    } catch (err: any) {
      console.error('Erro ao carregar artigos:', err);
      setError('Erro ao carregar artigos da base de conhecimento.');
    } finally {
      setIsLoading(false);
    }
  }, [searchQuery, selectedCategory]);

  useEffect(() => {
    if (!isAdmin()) {
      fetchArticles();
    }
  }, [searchQuery, selectedCategory, fetchArticles, isAdmin]);

  // Se for admin, mostrar interface de gerenciamento
  if (isAdmin()) {
    return <KnowledgeBaseManagement />;
  }

  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  const handleCategoryFilter = (category: string) => {
    setSelectedCategory(category);
  };

  const toggleArticle = async (articleId: string) => {
    if (expandedArticle === articleId) {
      setExpandedArticle(null);
    } else {
      setExpandedArticle(articleId);
      
      // Increment view count
      try {
        await apiClient.get(`/modules/core/help-center/kb/articles/${articleId}`);
      } catch (err) {
        console.error('Erro ao registrar visualização:', err);
      }
    }
  };

  const handleHelpfulVote = async (articleId: string, isHelpful: boolean) => {
    try {
      await apiClient.post(
        `/modules/core/help-center/kb/articles/${articleId}/helpful?is_helpful=${isHelpful}`
      );
      
      // Refresh articles to show updated counts
      fetchArticles();
    } catch (err: any) {
      console.error('Erro ao votar:', err);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const truncateContent = (content: string, maxLength = 200) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">{error}</div>
        <button
          onClick={fetchArticles}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Tentar Novamente
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="space-y-4">
        {/* Search Bar */}
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            placeholder="Buscar artigos..."
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Category Filter */}
        {categories.length > 0 && (
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => handleCategoryFilter('')}
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                selectedCategory === ''
                  ? 'bg-blue-100 text-blue-800'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Todas as categorias
            </button>
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => handleCategoryFilter(category)}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                  selectedCategory === category
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Articles List */}
      {articles.length === 0 ? (
        <div className="text-center py-12">
          <BookOpenIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">
            Nenhum artigo encontrado
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            Tente ajustar sua busca ou filtros.
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {articles.map((article) => (
            <div key={article.id} className="glass rounded-lg overflow-hidden">
              <div
                className="p-6 cursor-pointer hover:bg-gray-50 transition-colors"
                onClick={() => toggleArticle(article.id)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      {article.title}
                    </h3>
                    
                    <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                      {article.category && (
                        <div className="flex items-center space-x-1">
                          <TagIcon className="h-4 w-4" />
                          <span>{article.category}</span>
                        </div>
                      )}
                      <div className="flex items-center space-x-1">
                        <EyeIcon className="h-4 w-4" />
                        <span>{article.view_count} visualizações</span>
                      </div>
                      <span>Criado em {formatDate(article.created_at)}</span>
                      {article.created_by_name && (
                        <span>por {article.created_by_name}</span>
                      )}
                    </div>

                    <p className="text-gray-600">
                      {expandedArticle === article.id 
                        ? article.content 
                        : truncateContent(article.content)
                      }
                    </p>

                    {article.tags && (
                      <div className="mt-3 flex flex-wrap gap-1">
                        {article.tags.split(',').map((tag, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                          >
                            {tag.trim()}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Expanded Content Actions */}
              {expandedArticle === article.id && (
                <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-600">
                      Este artigo foi útil?
                    </div>
                    <div className="flex items-center space-x-4">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleHelpfulVote(article.id, true);
                        }}
                        className="flex items-center space-x-1 text-green-600 hover:text-green-700"
                      >
                        <HandThumbUpIcon className="h-4 w-4" />
                        <span>{article.helpful_count}</span>
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleHelpfulVote(article.id, false);
                        }}
                        className="flex items-center space-x-1 text-red-600 hover:text-red-700"
                      >
                        <HandThumbDownIcon className="h-4 w-4" />
                        <span>{article.not_helpful_count}</span>
                      </button>
                      <div className="text-sm text-gray-500">
                        {Math.round(article.helpfulness_ratio * 100)}% útil
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
