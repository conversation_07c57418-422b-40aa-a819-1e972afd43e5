"""Namecheap API processor for domain operations."""

import json  # noqa: E402
import logging
import xml.etree.ElementTree as ET
from datetime import date, datetime
from typing import Dict, List, Optional, Any

import httpx  # noqa: E402

from app.modules.shared.domain_rent.processors.base_processor import BaseProcessor  # noqa: E402
from app.modules.shared.domain_rent.schemas.domain_schemas import (
    DomainAvailabilityResult,
    DomainPriceInfo,
    ContactInfo,
)
from app.modules.shared.domain_rent.exceptions import (  # noqa: E402
    RegistrarApiError,
    DomainNotAvailableError,
)


logger = logging.getLogger(__name__)


class NamecheapProcessor(BaseProcessor):
    """Processor for Namecheap domain registrar API."""

    def __init__(
        self,
        api_user: str,
        api_key: str,
        client_ip: str,
        api_url: str = "https://api.namecheap.com/xml.response",
        is_sandbox: bool = False,
    ):
        """Initialize Namecheap processor.

        Args:
            api_user: Namecheap API user
            api_key: Namecheap API key
            client_ip: Client IP address (required by Namecheap)
            api_url: Namecheap API URL
            is_sandbox: Whether to use sandbox environment
        """
        self.api_user = api_user
        self.api_key = api_key
        self.client_ip = client_ip
        self.api_url = api_url
        self.is_sandbox = is_sandbox

        # Common parameters for all API requests
        self.common_params = {
            "ApiUser": api_user,
            "ApiKey": api_key,
            "UserName": api_user,
            "ClientIp": client_ip,
        }

    @property
    def registrar_name(self) -> str:
        """Return the name of the registrar."""
        return "namecheap"

    async def _make_request(
        self,
        command: str,
        params: Optional[Dict[str, Any]] = None,
    ) -> ET.Element:
        """Make a request to the Namecheap API.

        Args:
            command: API command
            params: Additional parameters

        Returns:
            XML response as ElementTree

        Raises:
            RegistrarApiError: If the API request fails
        """
        # Prepare request parameters
        request_params = {
            "Command": command,
            **self.common_params,
        }

        if params:
            request_params.update(params)

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.api_url,
                    data=request_params,
                    timeout=30.0,
                )

                # Check for HTTP errors
                if response.status_code != 200:
                    raise RegistrarApiError(f"Namecheap API HTTP error: {response.status_code}")

                # Parse XML response
                try:
                    root = ET.fromstring(response.text)
                except ET.ParseError as e:
                    raise RegistrarApiError(f"Invalid XML response: {str(e)}")

                # Check for API errors
                status = root.attrib.get("Status")
                if status != "OK":
                    error = root.find(".//Errors/Error")
                    error_msg = error.text if error is not None else "Unknown error"
                    raise RegistrarApiError(f"Namecheap API error: {error_msg}")

                # Return command response
                command_response = root.find(f".//CommandResponse/{command}Response")
                if command_response is None:
                    raise RegistrarApiError(f"No response for command {command}")

                return command_response

        except httpx.RequestError as e:
            raise RegistrarApiError(f"Namecheap API request failed: {str(e)}")

    async def search_availability(
        self, domain_name: str, tlds: Optional[List[str]] = None
    ) -> List[DomainAvailabilityResult]:
        """Search for domain availability.

        Args:
            domain_name: Domain name without TLD
            tlds: List of TLDs to check (if None, check common TLDs)

        Returns:
            List of availability results for each domain/TLD combination
        """
        # If no TLDs provided, use common ones
        if not tlds:
            tlds = ["com", "net", "org", "io", "co"]

        results = []

        # Namecheap allows checking multiple domains at once
        domains_to_check = [f"{domain_name}.{tld}" for tld in tlds]
        domains_param = ",".join(domains_to_check)

        try:
            # Call Namecheap API to check availability
            response = await self._make_request(
                "namecheap.domains.check",
                {"DomainList": domains_param},
            )

            # Process results
            domain_elements = response.findall(".//DomainCheckResult")
            for domain_element in domain_elements:
                full_domain = domain_element.attrib.get("Domain", "")
                available = domain_element.attrib.get("Available", "false").lower() == "true"

                # Extract TLD from full domain
                tld = full_domain.split(".")[-1] if "." in full_domain else ""

                # Get price information if available
                price_info = await self._get_price_info(full_domain) if available else None

                results.append(
                    DomainAvailabilityResult(
                        domain_name=full_domain,
                        tld=tld,
                        available=available,
                        registrar=self.registrar_name,
                        price_info=price_info,
                        reason=None if available else "Domain already registered",
                    )
                )
        except Exception as e:
            logger.error(f"Error checking availability: {str(e)}")
            # Add error results for all domains
            for full_domain in domains_to_check:
                tld = full_domain.split(".")[-1] if "." in full_domain else ""
                results.append(
                    DomainAvailabilityResult(
                        domain_name=full_domain,
                        tld=tld,
                        available=False,
                        registrar=self.registrar_name,
                        price_info=None,
                        reason=f"Error checking availability: {str(e)}",
                    )
                )

        return results

    async def _get_price_info(self, domain_name: str) -> Optional[DomainPriceInfo]:
        """Get price information for a domain.

        Args:
            domain_name: Full domain name (including TLD)

        Returns:
            Price information or None if not available
        """
        try:
            # Call Namecheap API to get prices
            response = await self._make_request(
                "namecheap.domains.getpricing",
                {"TLDList": domain_name.split(".")[-1]},
            )

            # Process results
            tld_element = response.find(".//Tld")
            if tld_element is None:
                return None

            registration = {}
            renewal = {}
            transfer = {}

            # Get registration prices
            for price_element in tld_element.findall(".//Registration/Price"):
                duration = price_element.attrib.get("Duration", "")
                if duration:
                    registration[duration] = float(price_element.attrib.get("Price", "0"))

            # Get renewal prices
            for price_element in tld_element.findall(".//Renewal/Price"):
                duration = price_element.attrib.get("Duration", "")
                if duration:
                    renewal[duration] = float(price_element.attrib.get("Price", "0"))

            # Get transfer prices
            for price_element in tld_element.findall(".//Transfer/Price"):
                duration = price_element.attrib.get("Duration", "")
                if duration:
                    transfer[duration] = float(price_element.attrib.get("Price", "0"))

            # Get restore price
            restore_element = tld_element.find(".//Reactivate/Price")
            restore_price = (
                float(restore_element.attrib.get("Price", "0"))
                if restore_element is not None
                else None
            )

            return DomainPriceInfo(
                currency="USD",  # Namecheap uses USD
                registration=registration,
                renewal=renewal,
                transfer=transfer,
                restore=restore_price,
            )
        except Exception as e:
            logger.error(f"Error getting price info for {domain_name}: {str(e)}")
            return None

    async def register_domain(
        self,
        domain_name: str,
        tld: str,
        period_years: int,
        contacts: Dict[str, ContactInfo],
        nameservers: Optional[List[str]] = None,
        whois_privacy: bool = False,
    ) -> Dict[str, Any]:
        """Register a new domain.

        Args:
            domain_name: Domain name without TLD
            tld: TLD to register
            period_years: Registration period in years
            contacts: Contact information for the domain
            nameservers: List of nameserver hostnames
            whois_privacy: Whether to enable WHOIS privacy protection

        Returns:
            Dictionary with registration details (registrar-specific)
        """
        full_domain = f"{domain_name}.{tld}"

        # Check if domain is available
        availability_results = await self.search_availability(domain_name, [tld])
        if not availability_results or not availability_results[0].available:
            raise DomainNotAvailableError(f"Domain {full_domain} is not available")

        # Prepare registration parameters
        registration_params = {
            "DomainName": full_domain,
            "Years": str(period_years),
            "AddFreeWhoisguard": "yes" if whois_privacy else "no",
            "WGEnabled": "yes" if whois_privacy else "no",
        }

        # Add nameservers if provided
        if nameservers:
            for i, nameserver in enumerate(nameservers, 1):
                registration_params[f"Nameserver{i}"] = nameserver

        # Add contact information
        registrant = contacts.get("registrant")
        if registrant:
            registration_params.update(
                {
                    "RegistrantFirstName": registrant.first_name,
                    "RegistrantLastName": registrant.last_name,
                    "RegistrantOrganizationName": registrant.organization or "",
                    "RegistrantAddress1": registrant.address_line_1,
                    "RegistrantAddress2": registrant.address_line_2 or "",
                    "RegistrantCity": registrant.city,
                    "RegistrantStateProvince": registrant.state_province,
                    "RegistrantPostalCode": registrant.postal_code,
                    "RegistrantCountry": registrant.country,
                    "RegistrantPhone": registrant.phone,
                    "RegistrantEmailAddress": str(registrant.email),
                }
            )

        # Add technical contact if different from registrant
        technical = contacts.get("technical", registrant)
        if technical:
            registration_params.update(
                {
                    "TechFirstName": technical.first_name,
                    "TechLastName": technical.last_name,
                    "TechOrganizationName": technical.organization or "",
                    "TechAddress1": technical.address_line_1,
                    "TechAddress2": technical.address_line_2 or "",
                    "TechCity": technical.city,
                    "TechStateProvince": technical.state_province,
                    "TechPostalCode": technical.postal_code,
                    "TechCountry": technical.country,
                    "TechPhone": technical.phone,
                    "TechEmailAddress": str(technical.email),
                }
            )

        # Add admin contact if different from registrant
        admin = contacts.get("admin", registrant)
        if admin:
            registration_params.update(
                {
                    "AdminFirstName": admin.first_name,
                    "AdminLastName": admin.last_name,
                    "AdminOrganizationName": admin.organization or "",
                    "AdminAddress1": admin.address_line_1,
                    "AdminAddress2": admin.address_line_2 or "",
                    "AdminCity": admin.city,
                    "AdminStateProvince": admin.state_province,
                    "AdminPostalCode": admin.postal_code,
                    "AdminCountry": admin.country,
                    "AdminPhone": admin.phone,
                    "AdminEmailAddress": str(admin.email),
                }
            )

        # Add billing contact if different from registrant
        billing = contacts.get("billing", registrant)
        if billing:
            registration_params.update(
                {
                    "AuxBillingFirstName": billing.first_name,
                    "AuxBillingLastName": billing.last_name,
                    "AuxBillingOrganizationName": billing.organization or "",
                    "AuxBillingAddress1": billing.address_line_1,
                    "AuxBillingAddress2": billing.address_line_2 or "",
                    "AuxBillingCity": billing.city,
                    "AuxBillingStateProvince": billing.state_province,
                    "AuxBillingPostalCode": billing.postal_code,
                    "AuxBillingCountry": billing.country,
                    "AuxBillingPhone": billing.phone,
                    "AuxBillingEmailAddress": str(billing.email),
                }
            )

        # Register domain
        try:
            response = await self._make_request(
                "namecheap.domains.create",
                registration_params,
            )

            # Extract domain information from response
            domain_element = response.find(".//DomainCreateResult")
            if domain_element is None:
                raise RegistrarApiError("No domain information in response")

            domain_id = domain_element.attrib.get("ID", "")
            domain_created = domain_element.attrib.get("Registered", "false").lower() == "true"

            if not domain_created:
                raise RegistrarApiError("Domain registration failed")

            # Get expiry date
            expiry_date = None
            domain_info = await self.get_domain_info(domain_name, tld)
            if domain_info and "expiry_date" in domain_info:
                expiry_date = domain_info["expiry_date"]

            return {
                "domain": full_domain,
                "order_id": domain_id,
                "created_at": datetime.now().isoformat(),
                "expiry_date": expiry_date,
                "registrar_data": json.dumps({"domain_id": domain_id}),
            }
        except Exception as e:
            logger.error(f"Error registering domain {full_domain}: {str(e)}")
            raise RegistrarApiError(f"Failed to register domain: {str(e)}")

    async def renew_domain(
        self,
        domain_name: str,
        tld: str,
        period_years: int,
        current_expiry_date: date,
    ) -> Dict[str, Any]:
        """Renew an existing domain.

        Args:
            domain_name: Domain name without TLD
            tld: TLD of the domain
            period_years: Renewal period in years
            current_expiry_date: Current expiry date of the domain

        Returns:
            Dictionary with renewal details (registrar-specific)
        """
        full_domain = f"{domain_name}.{tld}"

        # Prepare renewal parameters
        renewal_params = {
            "DomainName": full_domain,
            "Years": str(period_years),
        }

        # Renew domain
        try:
            response = await self._make_request(
                "namecheap.domains.renew",
                renewal_params,
            )

            # Extract domain information from response
            domain_element = response.find(".//DomainRenewResult")
            if domain_element is None:
                raise RegistrarApiError("No domain information in response")

            domain_id = domain_element.attrib.get("OrderID", "")

            # Calculate new expiry date
            new_expiry_date = date(
                current_expiry_date.year + period_years,
                current_expiry_date.month,
                current_expiry_date.day,
            )

            return {
                "domain": full_domain,
                "order_id": domain_id,
                "created_at": datetime.now().isoformat(),
                "expiry_date": new_expiry_date.isoformat(),
                "registrar_data": json.dumps({"order_id": domain_id}),
            }
        except Exception as e:
            logger.error(f"Error renewing domain {full_domain}: {str(e)}")
            raise RegistrarApiError(f"Failed to renew domain: {str(e)}")

    async def get_domain_info(self, domain_name: str, tld: str) -> Dict[str, Any]:
        """Get information about a domain.

        Args:
            domain_name: Domain name without TLD
            tld: TLD of the domain

        Returns:
            Dictionary with domain information (registrar-specific)
        """
        full_domain = f"{domain_name}.{tld}"

        try:
            response = await self._make_request(
                "namecheap.domains.getinfo",
                {"DomainName": full_domain},
            )

            # Extract domain information from response
            domain_element = response.find(".//DomainGetInfoResult")
            if domain_element is None:
                raise RegistrarApiError("No domain information in response")

            # Extract basic domain information
            domain_info = {
                "domain": full_domain,
                "id": domain_element.attrib.get("ID", ""),
                "status": domain_element.attrib.get("Status", ""),
                "is_locked": domain_element.attrib.get("IsLocked", "false").lower() == "true",
                "auto_renew": domain_element.attrib.get("AutoRenew", "false").lower() == "true",
                "whois_guard": domain_element.attrib.get("WhoisGuard", "NOTPRESENT"),
            }

            # Extract expiry date
            expiry_date_str = domain_element.attrib.get("Expires", "")
            if expiry_date_str:
                try:
                    # Namecheap date format: MM/DD/YYYY
                    month, day, year = map(int, expiry_date_str.split("/"))
                    domain_info["expiry_date"] = date(year, month, day).isoformat()
                except (ValueError, IndexError):
                    logger.warning(f"Invalid expiry date format: {expiry_date_str}")

            # Extract nameservers
            nameservers = []
            dns_element = domain_element.find(".//DnsDetails")
            if dns_element is not None:
                for ns_element in dns_element.findall(".//Nameserver"):
                    if ns_element.text:
                        nameservers.append(ns_element.text)
            domain_info["nameservers"] = nameservers

            return domain_info
        except Exception as e:
            logger.error(f"Error getting domain info for {full_domain}: {str(e)}")
            raise RegistrarApiError(f"Failed to get domain info: {str(e)}")

    async def get_nameservers(self, domain_name: str, tld: str) -> List[str]:
        """Get nameservers for a domain.

        Args:
            domain_name: Domain name without TLD
            tld: TLD of the domain

        Returns:
            List of nameserver hostnames
        """
        domain_info = await self.get_domain_info(domain_name, tld)
        return domain_info.get("nameservers", [])

    async def update_nameservers(self, domain_name: str, tld: str, nameservers: List[str]) -> bool:
        """Update nameservers for a domain.

        Args:
            domain_name: Domain name without TLD
            tld: TLD of the domain
            nameservers: List of nameserver hostnames

        Returns:
            True if successful, False otherwise
        """
        full_domain = f"{domain_name}.{tld}"

        # Prepare nameserver parameters
        ns_params = {
            "DomainName": full_domain,
        }

        # Add nameservers
        for i, nameserver in enumerate(nameservers, 1):
            ns_params[f"Nameserver{i}"] = nameserver

        try:
            await self._make_request(
                "namecheap.domains.dns.setCustom",
                ns_params,
            )
            return True
        except Exception as e:
            logger.error(f"Error updating nameservers for {full_domain}: {str(e)}")
            return False

    async def get_auth_code(self, domain_name: str, tld: str) -> str:
        """Get authorization code for domain transfer.

        Args:
            domain_name: Domain name without TLD
            tld: TLD of the domain

        Returns:
            Authorization code
        """
        full_domain = f"{domain_name}.{tld}"

        try:
            response = await self._make_request(
                "namecheap.domains.getinfo",
                {"DomainName": full_domain},
            )

            # Extract auth code from response
            domain_element = response.find(".//DomainGetInfoResult")
            if domain_element is None:
                raise RegistrarApiError("No domain information in response")

            auth_code = ""
            auth_element = domain_element.find(".//AuthCode")
            if auth_element is not None and auth_element.text:
                auth_code = auth_element.text

            return auth_code
        except Exception as e:
            logger.error(f"Error getting auth code for {full_domain}: {str(e)}")
            raise RegistrarApiError(f"Failed to get auth code: {str(e)}")

    async def toggle_whois_privacy(self, domain_name: str, tld: str, enable: bool) -> bool:
        """Enable or disable WHOIS privacy for a domain.

        Args:
            domain_name: Domain name without TLD
            tld: TLD of the domain
            enable: Whether to enable or disable WHOIS privacy

        Returns:
            True if successful, False otherwise
        """
        full_domain = f"{domain_name}.{tld}"

        try:
            if enable:
                await self._make_request(
                    "namecheap.whoisguard.enable",
                    {"DomainName": full_domain},
                )
            else:
                await self._make_request(
                    "namecheap.whoisguard.disable",
                    {"DomainName": full_domain},
                )
            return True
        except Exception as e:
            logger.error(f"Error toggling WHOIS privacy for {full_domain}: {str(e)}")
            return False

    async def toggle_domain_lock(self, domain_name: str, tld: str, lock: bool) -> bool:
        """Lock or unlock a domain for transfer.

        Args:
            domain_name: Domain name without TLD
            tld: TLD of the domain
            lock: Whether to lock or unlock the domain

        Returns:
            True if successful, False otherwise
        """
        full_domain = f"{domain_name}.{tld}"

        try:
            await self._make_request(
                "namecheap.domains.setRegistrarLock",
                {"DomainName": full_domain, "LockAction": "LOCK" if lock else "UNLOCK"},
            )
            return True
        except Exception as e:
            logger.error(f"Error toggling domain lock for {full_domain}: {str(e)}")
            return False

    async def transfer_domain(
        self,
        domain_name: str,
        tld: str,
        auth_code: str,
        contacts: Dict[str, ContactInfo],
        nameservers: Optional[List[str]] = None,
    ) -> Dict[str, Any]:
        """Transfer a domain from another registrar.

        Args:
            domain_name: Domain name without TLD
            tld: TLD of the domain
            auth_code: Authorization code for transfer
            contacts: Contact information for the domain
            nameservers: List of nameserver hostnames

        Returns:
            Dictionary with transfer details (registrar-specific)
        """
        full_domain = f"{domain_name}.{tld}"

        # Prepare transfer parameters
        transfer_params = {
            "DomainName": full_domain,
            "EPPCode": auth_code,
            "Years": "1",  # Default to 1 year for transfer
        }

        # Add nameservers if provided
        if nameservers:
            for i, nameserver in enumerate(nameservers, 1):
                transfer_params[f"Nameserver{i}"] = nameserver

        # Add registrant contact information
        registrant = contacts.get("registrant")
        if registrant:
            transfer_params.update(
                {
                    "RegistrantFirstName": registrant.first_name,
                    "RegistrantLastName": registrant.last_name,
                    "RegistrantOrganizationName": registrant.organization or "",
                    "RegistrantAddress1": registrant.address_line_1,
                    "RegistrantAddress2": registrant.address_line_2 or "",
                    "RegistrantCity": registrant.city,
                    "RegistrantStateProvince": registrant.state_province,
                    "RegistrantPostalCode": registrant.postal_code,
                    "RegistrantCountry": registrant.country,
                    "RegistrantPhone": registrant.phone,
                    "RegistrantEmailAddress": str(registrant.email),
                }
            )

        # Transfer domain
        try:
            response = await self._make_request(
                "namecheap.domains.transfer.create",
                transfer_params,
            )

            # Extract transfer information from response
            transfer_element = response.find(".//DomainTransferCreateResult")
            if transfer_element is None:
                raise RegistrarApiError("No transfer information in response")

            transfer_id = transfer_element.attrib.get("TransferID", "")

            return {
                "domain": full_domain,
                "order_id": transfer_id,
                "created_at": datetime.now().isoformat(),
                "expiry_date": None,  # Will be determined after transfer completes
                "registrar_data": json.dumps({"transfer_id": transfer_id}),
            }
        except Exception as e:
            logger.error(f"Error transferring domain {full_domain}: {str(e)}")
            raise RegistrarApiError(f"Failed to transfer domain: {str(e)}")
