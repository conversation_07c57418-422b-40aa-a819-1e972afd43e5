'use client';

import { Fragment, useState, useEffect, useCallback } from 'react';
import { Combobox, Transition } from '@headlessui/react';
import { CheckIcon, ChevronUpDownIcon, PlusIcon, MagnifyingGlassIcon } from '@heroicons/react/20/solid';
import { useTenant } from '@/lib/tenant/TenantProvider';
import { useAuth } from '@/lib/auth/AuthProvider';
import { tenantService } from '@/services/api/tenantService';
import { tenantSettingsService } from '@/services/api/tenantSettingsService';
import { Tenant } from '@/types/auth';
import { clsx } from 'clsx';
import { NewTenantModal } from './NewTenantModal';

// Extended tenant interface with business info
interface TenantWithBusinessInfo extends Tenant {
  business_name?: string;
  tenant_slug?: string;
}

// Component for displaying tenant info with lazy loading
function TenantDisplayInfo({
  tenant,
  businessInfo,
  onLoadBusinessInfo,
  getBusinessInfo,
  selected,
  active
}: {
  tenant: Tenant;
  businessInfo?: {business_name?: string, tenant_slug?: string};
  onLoadBusinessInfo: (info: {business_name?: string, tenant_slug?: string}) => void;
  getBusinessInfo: (tenantId: string) => Promise<{business_name?: string, tenant_slug?: string}>;
  selected: boolean;
  active: boolean;
}) {
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Validate tenant.id before making API calls
    if (!tenant.id) {
      console.warn('[TenantSelector] tenant.id is null/undefined');
      return;
    }

    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(tenant.id)) {
      console.error('[TenantSelector] tenant.id is not a valid UUID:', tenant.id);
      return;
    }

    if (!businessInfo && !loading) {
      setLoading(true);
      getBusinessInfo(tenant.id).then(info => {
        onLoadBusinessInfo(info);
        setLoading(false);
      }).catch(() => {
        onLoadBusinessInfo({ business_name: undefined, tenant_slug: undefined });
        setLoading(false);
      });
    }
  }, [tenant.id, businessInfo, loading, getBusinessInfo, onLoadBusinessInfo]);

  return (
    <div className="flex flex-col min-h-[2.5rem] justify-center">
      <span className={clsx('block truncate text-sm', selected ? 'font-medium' : 'font-normal')}>
        {loading ? 'Loading...' : (businessInfo?.business_name || 'Unnamed Business')}
      </span>
      <span className={clsx('text-xs truncate', active ? 'text-gray-200' : 'text-gray-500')}>
        {tenant.id}
      </span>
    </div>
  );
}

export function TenantSelector() {
  const { currentTenant, tenants, setCurrentTenant, refreshTenants, isAdminViewMode, adminViewTenantId } = useTenant();
  const { isAdmin, isTenantOwner } = useAuth();
  const [query, setQuery] = useState('');
  const [allTenants, setAllTenants] = useState<TenantWithBusinessInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [showNewTenantModal, setShowNewTenantModal] = useState(false);
  const [currentTenantWithInfo, setCurrentTenantWithInfo] = useState<TenantWithBusinessInfo | null>(null);
  const [tenantsWithBusinessInfo, setTenantsWithBusinessInfo] = useState<Map<string, {business_name?: string, tenant_slug?: string}>>(new Map());

  // Function to get business info for a tenant without changing context
  const getBusinessInfo = useCallback(async (tenantId: string) => {
    try {
      // Use apiClient directly with tenant header
      const { apiClient } = await import('@/lib/api/client');

      const response = await apiClient.get('/tenants/settings/', {
        headers: {
          'X-Tenant-ID': tenantId
        }
      });

      return {
        business_name: response.data.business_name,
        tenant_slug: response.data.tenant_slug
      };
    } catch (error) {
      console.error(`Failed to get business info for tenant ${tenantId}:`, error);
      return { business_name: undefined, tenant_slug: undefined };
    }
  }, []);

  // Load business info for current tenant only
  useEffect(() => {
    if (currentTenant) {
      // Validate currentTenant.id before making API calls
      if (!currentTenant.id) {
        console.warn('[TenantSelector] currentTenant.id is null/undefined');
        setCurrentTenantWithInfo(null);
        return;
      }

      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(currentTenant.id)) {
        console.error('[TenantSelector] currentTenant.id is not a valid UUID:', currentTenant.id);
        setCurrentTenantWithInfo(null);
        return;
      }

      // Check if we already have the info cached
      const cachedInfo = tenantsWithBusinessInfo.get(currentTenant.id);
      if (cachedInfo) {
        setCurrentTenantWithInfo({
          ...currentTenant,
          business_name: cachedInfo.business_name,
          tenant_slug: cachedInfo.tenant_slug
        });
      } else {
        // Load info if not cached
        getBusinessInfo(currentTenant.id).then(info => {
          setCurrentTenantWithInfo({
            ...currentTenant,
            business_name: info.business_name,
            tenant_slug: info.tenant_slug
          });
          // Cache the info
          setTenantsWithBusinessInfo(prev => new Map(prev).set(currentTenant.id, info));
        }).catch(error => {
          console.error('Failed to load business info for current tenant:', error);
          // Set fallback info
          setCurrentTenantWithInfo({
            ...currentTenant,
            business_name: undefined,
            tenant_slug: undefined
          });
        });
      }
    } else {
      setCurrentTenantWithInfo(null);
    }
  }, [currentTenant, getBusinessInfo, tenantsWithBusinessInfo]);

  // Load all tenants for admins
  useEffect(() => {
    const loadAllTenants = async () => {
      if (isAdmin()) {
        setLoading(true);
        try {
          const adminTenants = await tenantService.getTenants();
          setAllTenants(Array.isArray(adminTenants) ? adminTenants : []);
        } catch (error) {
          console.error('Failed to load all tenants:', error);
          setAllTenants(Array.isArray(tenants) ? tenants : []); // Fallback to user tenants
        } finally {
          setLoading(false);
        }
      } else {
        setAllTenants(Array.isArray(tenants) ? tenants : []);
      }
    };

    loadAllTenants();
  }, [isAdmin, tenants]);

  const availableTenants = isAdmin() ? (Array.isArray(allTenants) ? allTenants : []) : (Array.isArray(tenants) ? tenants : []);

  // Check if user can create new tenants (must be owner of at least one tenant, not admin)
  const canCreateNewTenant = !isAdmin() && isTenantOwner();

  // Check if should show search box (more than 10 tenants)
  const shouldShowSearch = availableTenants.length > 10;

  // Handle new tenant creation
  const handleNewTenantSuccess = async (newTenant: Tenant) => {
    // Refresh tenants list
    if (refreshTenants) {
      await refreshTenants();
    }

    // Update local state for admin (though they shouldn't see this option)
    if (isAdmin()) {
      setAllTenants(prev => [...prev, newTenant]);
    }

    // Set as current tenant
    setCurrentTenant(newTenant);
    setShowNewTenantModal(false);
  };

  // Don't show if no tenants available and user is not admin
  if (availableTenants.length === 0 && !isAdmin()) {
    return null;
  }

  const filteredTenants =
    query === ''
      ? availableTenants
      : availableTenants.filter((tenant) => {
          const businessInfo = tenantsWithBusinessInfo.get(tenant.id);
          const businessName = businessInfo?.business_name?.toLowerCase() || '';
          const tenantSlug = businessInfo?.tenant_slug?.toLowerCase() || '';
          const tenantId = tenant.id.toLowerCase();
          const searchQuery = query.toLowerCase();

          return businessName.includes(searchQuery) ||
                 tenantSlug.includes(searchQuery) ||
                 tenantId.includes(searchQuery);
        });

  // Admin View Mode - show fixed tenant selector
  if (isAdminViewMode && currentTenant) {
    return (
      <div className="relative w-[250px]">
        <div className="relative w-full overflow-hidden glass rounded-lg text-left shadow-lg border border-orange-300/50 bg-orange-50/20 backdrop-blur-md">
          <div className="w-full py-2.5 pl-3 pr-10 text-sm leading-5 text-gray-900 bg-transparent">
            <div className="flex flex-col">
              <span className="block truncate font-medium">
                {currentTenantWithInfo?.business_name || 'Unnamed Business'}
              </span>
              <span className="block truncate text-xs text-orange-600 font-medium">
                Admin View: {currentTenant.id}
              </span>
            </div>
          </div>
          <span className="absolute inset-y-0 right-0 flex items-center pr-2">
            <div className="h-5 w-5 text-orange-500 flex items-center justify-center">
              🔒
            </div>
          </span>
        </div>
      </div>
    );
  }

  return (
    <>
      <Combobox value={currentTenant} onChange={setCurrentTenant}>
        <div className="relative w-[250px]">
          <Combobox.Button className="relative w-full cursor-pointer overflow-hidden glass rounded-lg text-left shadow-lg border border-white/30 hover:ring-2 hover:ring-primary-500/50 sm:text-sm backdrop-blur-md transition-all duration-200">
            <div className="w-full py-2.5 pl-3 pr-10 text-sm leading-5 text-gray-900 bg-transparent">
              {currentTenantWithInfo ? (
                <div className="flex flex-col">
                  <span className="block truncate font-medium">
                    {currentTenantWithInfo.business_name || 'Unnamed Business'}
                  </span>
                  <span className="block truncate text-xs text-gray-500">
                    {currentTenantWithInfo.id}
                  </span>
                </div>
              ) : (
                <span className="block truncate">
                  {isAdmin() ? "Select tenant..." : "Select tenant..."}
                </span>
              )}
            </div>
            <span className="absolute inset-y-0 right-0 flex items-center pr-3">
              <ChevronUpDownIcon className="h-5 w-5 text-gray-500 hover:text-gray-700 transition-colors" aria-hidden="true" />
            </span>
          </Combobox.Button>

          <Transition
            as={Fragment}
            leave="transition ease-in duration-100"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
            afterLeave={() => setQuery('')}
          >
            <Combobox.Options className="absolute z-10 mt-2 max-h-80 w-full overflow-auto glass-strong rounded-xl py-1 text-base shadow-xl border border-white/30 focus:outline-none sm:text-sm backdrop-blur-lg">
              {loading ? (
                <div className="relative cursor-default select-none py-3 px-4 text-gray-600">
                  Loading tenants...
                </div>
              ) : filteredTenants.length === 0 && query !== '' ? (
                <div className="relative cursor-default select-none py-3 px-4 text-gray-600">
                  No tenants found.
                </div>
              ) : (
                <>
                  {/* Search Box - Show when more than 10 tenants */}
                  {shouldShowSearch && (
                    <div className="sticky top-0 bg-white/95 backdrop-blur-sm border-b border-white/30 p-2">
                      <div className="relative">
                        <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <input
                          type="text"
                          value={query}
                          onChange={(e) => setQuery(e.target.value)}
                          placeholder="Buscar tenant..."
                          className="w-full pl-9 pr-3 py-2 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500/50 focus:border-primary-500"
                          autoFocus
                        />
                      </div>
                    </div>
                  )}

                  {isAdmin() && (
                    <div className="sticky top-0 bg-gradient-to-r from-gray-50/90 to-gray-100/90 px-4 py-2 text-xs font-semibold text-gray-600 border-b border-white/30 backdrop-blur-sm">
                      {filteredTenants.length} tenant{filteredTenants.length !== 1 ? 's' : ''} found
                    </div>
                  )}

                  {/* Tenant Options */}
                  {filteredTenants.map((tenant) => (
                    <Combobox.Option
                      key={tenant.id}
                      className={({ active }) =>
                        clsx(
                          'relative cursor-pointer select-none py-3 pl-10 pr-4 mx-1 my-0.5 rounded-lg transition-all duration-200',
                          active ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-md' : 'text-gray-900 hover:bg-white/50'
                        )
                      }
                      value={tenant}
                    >
                      {({ selected, active }) => (
                        <>
                          <TenantDisplayInfo
                            tenant={tenant}
                            businessInfo={tenantsWithBusinessInfo.get(tenant.id)}
                            onLoadBusinessInfo={(info) => {
                              setTenantsWithBusinessInfo(prev => new Map(prev).set(tenant.id, info));
                            }}
                            getBusinessInfo={getBusinessInfo}
                            selected={selected}
                            active={active}
                          />
                          {selected ? (
                            <span
                              className={clsx(
                                'absolute inset-y-0 left-0 flex items-center pl-3',
                                active ? 'text-white' : 'text-primary-600'
                              )}
                            >
                              <CheckIcon className="h-5 w-5 drop-shadow-sm" aria-hidden="true" />
                            </span>
                          ) : null}
                        </>
                      )}
                    </Combobox.Option>
                  ))}

                  {/* New Business Option - Only for tenant owners, not admins */}
                  {canCreateNewTenant && (
                    <>
                      {filteredTenants.length > 0 && (
                        <div className="border-t border-white/30 mx-1 my-1" />
                      )}
                      <div className="relative cursor-pointer select-none py-3 pl-10 pr-4 mx-1 my-0.5 rounded-lg transition-all duration-200 text-gray-900 hover:bg-gradient-to-r hover:from-green-500 hover:to-green-600 hover:text-white hover:shadow-md group"
                           onClick={() => setShowNewTenantModal(true)}
                      >
                        <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                          <PlusIcon className="h-5 w-5 text-green-600 group-hover:text-white transition-colors" aria-hidden="true" />
                        </span>
                        <div className="flex flex-col min-h-[2.5rem] justify-center">
                          <span className="block font-medium text-sm truncate">
                            Novo Negócio
                          </span>
                          <span className="text-xs text-gray-500 group-hover:text-gray-200 transition-colors">
                            Criar novo restaurante
                          </span>
                        </div>
                      </div>
                    </>
                  )}
                </>
              )}
            </Combobox.Options>
          </Transition>
        </div>
      </Combobox>

      {/* New Tenant Modal */}
      <NewTenantModal
        isOpen={showNewTenantModal}
        onClose={() => setShowNewTenantModal(false)}
        onSuccess={handleNewTenantSuccess}
      />
    </>
  );
}
