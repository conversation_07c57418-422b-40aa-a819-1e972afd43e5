from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update, delete

from app.modules.core.i18n.models.language import Language, generate_version_code
from app.modules.core.i18n.schemas import language as schemas


class LanguageService:
    async def create_language(
        self, db: AsyncSession, language_in: schemas.LanguageCreate
    ) -> Language:
        """Create a new language."""
        # If this is set as default, unset any existing default
        if language_in.is_default:
            await self._unset_all_defaults(db)

        db_language = Language(
            code=language_in.code,
            name=language_in.name,
            is_active=language_in.is_active,
            is_default=language_in.is_default,
        )
        db.add(db_language)
        await db.flush()
        await db.refresh(db_language)
        return db_language

    async def get_language(self, db: AsyncSession, language_id: int) -> Optional[Language]:
        """Get a language by ID."""
        result = await db.execute(select(Language).filter(Language.id == language_id))
        return result.scalars().first()

    async def get_language_by_code(self, db: AsyncSession, code: str) -> Optional[Language]:
        """Get a language by code."""
        result = await db.execute(select(Language).filter(Language.code == code))
        return result.scalars().first()

    async def get_default_language(self, db: AsyncSession) -> Optional[Language]:
        """Get the default language."""
        result = await db.execute(select(Language).filter(Language.is_default == True))
        return result.scalars().first()

    async def get_languages(
        self, db: AsyncSession, skip: int = 0, limit: int = 100
    ) -> List[Language]:
        """Get all languages with pagination."""
        result = await db.execute(select(Language).offset(skip).limit(limit))
        return result.scalars().all()

    async def update_language(
        self, db: AsyncSession, language_id: int, language_in: schemas.LanguageUpdate
    ) -> Optional[Language]:
        """Update a language."""
        db_language = await self.get_language(db, language_id)
        if not db_language:
            return None

        # If setting as default, unset any existing default
        if language_in.is_default:
            await self._unset_all_defaults(db)

        # Update fields
        for field, value in language_in.model_dump(exclude_unset=True).items():
            setattr(db_language, field, value)

        # Generate a new version code to indicate the language has been updated
        db_language.version_code = generate_version_code()

        db.add(db_language)
        await db.flush()
        await db.refresh(db_language)
        return db_language

    async def delete_language(self, db: AsyncSession, language_id: int) -> bool:
        """Delete a language."""
        db_language = await self.get_language(db, language_id)
        if not db_language:
            return False

        await db.delete(db_language)
        await db.flush()
        return True

    async def set_default_language(self, db: AsyncSession, language_id: int) -> Optional[Language]:
        """Set a language as the default."""
        # First, unset any existing default language
        current_default = await db.execute(select(Language).where(Language.is_default == True))
        current_default_lang = current_default.scalar_one_or_none()
        if current_default_lang:
            current_default_lang.is_default = False
            db.add(current_default_lang)

        # Set the new default language
        new_default_lang = await self.get_language(db, language_id)
        if new_default_lang:
            new_default_lang.is_default = True
            db.add(new_default_lang)
            await db.flush()
            await db.refresh(new_default_lang)
            # Also refresh the old default if it exists and was changed
            if current_default_lang and current_default_lang.id != new_default_lang.id:
                await db.refresh(current_default_lang)
            return new_default_lang
        return None

    async def _unset_all_defaults(self, db: AsyncSession) -> None:
        """Unset the default flag for all languages."""
        await db.execute(
            update(Language).where(Language.is_default == True).values(is_default=False)
        )

    async def check_version(
        self, db: AsyncSession, code: str, client_version_code: str
    ) -> Dict[str, Any]:
        """
        Check if the language version has changed.

        Args:
            db: Database session
            code: Language code
            client_version_code: Version code from the client

        Returns:
            Dictionary with 'changed' flag and current version_code
        """
        language = await self.get_language_by_code(db, code)
        if not language:
            return {
                "changed": False,
                "version_code": client_version_code,
                "error": "Language not found",
            }

        return {
            "changed": language.version_code != client_version_code,
            "version_code": language.version_code,
        }
