import uuid
import logging
from typing import Optional, List, Union

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import or_, text
from sqlalchemy.orm import selectinload

from app.modules.core.tenants.models.tenant import Tenant
from app.modules.core.users.models.tenant_user_association import TenantUserAssociation
from app.modules.core.tenants.schemas.tenant import TenantCreate, TenantUpdate

logger = logging.getLogger(__name__)


class TenantService:
    """
    Service for operations related to the Tenant model.
    """

    async def get_tenant(self, db: AsyncSession, *, tenant_id: uuid.UUID) -> Optional[Tenant]:
        """
        Fetches an active tenant by its ID with settings loaded.

        Args:
            db: The async database session.
            tenant_id: The UUID of the tenant to fetch.

        Returns:
            The Tenant object if found and active, otherwise None.
        """
        try:
            stmt = select(Tenant).options(
                selectinload(Tenant.settings)
            ).where(
                Tenant.id == tenant_id, Tenant.is_active == True  # noqa: E712
            )
            result = await db.execute(stmt)
            return result.scalars().first()
        except SQLAlchemyError as e:
            logger.error(f"Database error fetching tenant by ID {tenant_id}: {e}", exc_info=True)
            return None

    async def get_tenant_by_slug_or_id(
        self, db: AsyncSession, tenant_id_or_slug: str
    ) -> Optional[Tenant]:
        """
        Fetches an active tenant by its ID or tenant_slug.
        
        Priority: 
        1. Try UUID first (most direct and guaranteed unique access)
        2. Fallback to tenant_slug lookup if not a valid UUID
        
        This ensures that every tenant is ALWAYS accessible by their UUID,
        regardless of whether they have a custom tenant_slug configured.

        Args:
            db: The async database session.
            tenant_id_or_slug: Either a UUID string or tenant_slug.

        Returns:
            The Tenant object if found and active, otherwise None.
        """
        try:
            logger.debug(f"Resolving tenant from identifier: {tenant_id_or_slug}")
            
            # Try to parse as UUID first - this is the guaranteed access method
            try:
                tenant_uuid = uuid.UUID(tenant_id_or_slug)
                logger.debug(f"Identifier is valid UUID: {tenant_uuid}")
                
                # If successful, search by ID
                stmt = select(Tenant).where(
                    Tenant.id == tenant_uuid, Tenant.is_active == True  # noqa: E712
                )
                result = await db.execute(stmt)
                tenant = result.scalars().first()
                
                if tenant:
                    logger.info(f"Tenant found by UUID: {tenant.id}")
                    return tenant
                else:
                    logger.warning(f"Tenant with UUID {tenant_uuid} not found or inactive")
                    return None
                    
            except ValueError:
                # If not a valid UUID, search by tenant_slug in RestaurantTenantSettings
                logger.debug(f"Identifier is not UUID, searching by tenant_slug: {tenant_id_or_slug}")
                
                try:
                    from app.modules.tenants.restaurants.tenant_settings.models.restaurant_tenant_settings import RestaurantTenantSettings

                    # First, find the tenant_id from RestaurantTenantSettings
                    restaurant_stmt = select(RestaurantTenantSettings.tenant_id).where(
                        RestaurantTenantSettings.tenant_slug == tenant_id_or_slug
                    )
                    restaurant_result = await db.execute(restaurant_stmt)
                    tenant_id = restaurant_result.scalars().first()

                    if not tenant_id:
                        logger.warning(f"No tenant found with slug: {tenant_id_or_slug}")
                        return None

                    # Then, get the Tenant object
                    stmt = select(Tenant).where(
                        Tenant.id == tenant_id, Tenant.is_active == True  # noqa: E712
                    )
                    result = await db.execute(stmt)
                    tenant = result.scalars().first()
                    
                    if tenant:
                        logger.info(f"Tenant found by slug '{tenant_id_or_slug}': {tenant.id}")
                        return tenant
                    else:
                        logger.warning(f"Tenant with slug '{tenant_id_or_slug}' found but inactive")
                        return None

                except Exception as e:
                    logger.error(f"Error searching by tenant_slug in RestaurantTenantSettings: {e}")
                    return None
                    
        except SQLAlchemyError as e:
            logger.error(
                f"Database error fetching tenant by ID/slug {tenant_id_or_slug}: {e}", exc_info=True
            )
            return None

    async def get_tenants(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100
    ) -> List[Tenant]:
        """
        Lists active tenants with pagination.

        Args:
            db: The async database session.
            skip: Number of records to skip.
            limit: Maximum number of records to return.

        Returns:
            A list of active Tenant objects.
        """
        try:
            stmt = select(Tenant).where(Tenant.is_active.is_(True)).offset(skip).limit(limit)
            result = await db.execute(stmt)
            return result.scalars().all()
        except SQLAlchemyError as e:
            logger.error(f"Database error fetching tenants: {e}", exc_info=True)
            return []

    async def create_tenant(
        self,
        db: AsyncSession,
        *,
        tenant_in: TenantCreate,
    ) -> Tenant:
        """
        Creates a new tenant.

        Args:
            db: The async database session.
            tenant_in: The schema with data for tenant creation.

        Returns:
            The created Tenant object.

        Raises:
            ValueError: If an error occurs while creating the tenant.
        """
        db_tenant = Tenant(
            is_active=tenant_in.is_active,
        )
        try:
            db.add(db_tenant)
            await db.commit()
            await db.refresh(db_tenant)
            return db_tenant
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(
                f"Database error creating tenant (name: {tenant_in.name}): {e}",
                exc_info=True,
            )
            raise ValueError(f"Could not create tenant: {e}")

    async def update_tenant(
        self,
        db: AsyncSession,
        *,
        db_obj: Tenant,
        obj_in: TenantUpdate,
    ) -> Tenant:
        """
        Updates an existing tenant's data.

        Args:
            db: The async database session.
            db_obj: The Tenant object to update.
            obj_in: The schema with data for updating.

        Returns:
            The updated Tenant object.

        Raises:
            ValueError: If an error occurs while updating the tenant.
        """
        update_data = obj_in.model_dump(exclude_unset=True)

        for field, value in update_data.items():
            setattr(db_obj, field, value)

        try:
            db.add(db_obj)
            await db.commit()
            await db.refresh(db_obj)
            return db_obj
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(
                f"Database error updating tenant {db_obj.id if db_obj else 'UNKNOWN'}: {e}",
                exc_info=True,
            )
            raise ValueError(f"Could not update tenant: {e}")

    async def delete_tenant(self, db: AsyncSession, *, tenant_id: uuid.UUID) -> Optional[Tenant]:
        """
        Marks a tenant as inactive (soft delete).

        Args:
            db: The async database session.
            tenant_id: The UUID of the tenant to mark as inactive.

        Returns:
            The Tenant object marked as inactive or None if not found.

        Raises:
            ValueError: If an error occurs while marking the tenant as inactive.
        """
        db_tenant = await self.get_tenant(db, tenant_id=tenant_id)
        if not db_tenant:
            return None  # Already doesn't exist or is inactive

        db_tenant.is_active = False
        try:
            db.add(db_tenant)
            await db.commit()
            await db.refresh(db_tenant)
            return db_tenant
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(f"Database error deleting (soft) tenant {tenant_id}: {e}", exc_info=True)
            raise ValueError(f"Could not mark tenant as inactive: {e}")

    async def get_user_tenants(self, db: AsyncSession, *, user_id: uuid.UUID) -> List[Tenant]:
        """
        Gets all tenants associated with a user, ensuring related settings
        (like name and slug) are loaded.
        """
        try:
            # Query associations for the user and eager load the full tenant object
            # along with its general settings and restaurant-specific settings.
            stmt = (
                select(TenantUserAssociation)
                .options(
                    selectinload(TenantUserAssociation.tenant).selectinload(
                        Tenant.settings
                    ),
                    selectinload(TenantUserAssociation.tenant).selectinload(
                        Tenant.restaurant_settings
                    )
                )
                .where(TenantUserAssociation.user_id == user_id)
                .where(TenantUserAssociation.tenant.has(is_active=True))
            )

            result = await db.execute(stmt)
            associations = result.scalars().all()

            # Extract the tenant objects from the associations
            tenants = [assoc.tenant for assoc in associations if assoc.tenant]
            
            logger.info(f"Found {len(tenants)} tenants for user {user_id}")
            return tenants
            
        except SQLAlchemyError as e:
            logger.error(
                f"Database error fetching user tenants for user {user_id}: {e}", exc_info=True
            )
            return []


# Service instance
tenant_service = TenantService()
