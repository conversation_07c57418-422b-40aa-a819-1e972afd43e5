"""Service layer for invoices."""

import uuid
from typing import Optional, Sequence
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from jinja2 import Environment, FileSystemLoader
from weasyprint import HTML
import os

from ..models.invoice import Invoice, InvoiceItem, InvoiceStatus
from ..schemas.invoice import InvoiceCreate, InvoiceUpdate

class InvoiceService:
    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_invoice_by_id(self, invoice_id: uuid.UUID, tenant_id: uuid.UUID) -> Optional[Invoice]:
        stmt = select(Invoice).where(Invoice.id == invoice_id, Invoice.tenant_id == tenant_id)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    async def get_invoices(
        self,
        tenant_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        status: Optional[InvoiceStatus] = None,
    ) -> Sequence[Invoice]:
        stmt = select(Invoice).where(Invoice.tenant_id == tenant_id)
        if status:
            stmt = stmt.where(Invoice.status == status)
        stmt = stmt.offset(skip).limit(limit)
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def create_invoice(
        self,
        invoice_data: InvoiceCreate,
        tenant_id: uuid.UUID,
        user_id: uuid.UUID,
    ) -> Invoice:
        # This is a simplified creation method.
        # A more complex method will be needed to create an invoice from an order.
        invoice_items = invoice_data.items
        invoice_dict = invoice_data.model_dump(exclude={"items"})
        
        db_invoice = Invoice(
            **invoice_dict,
            tenant_id=tenant_id,
            created_by=user_id,
        )
        
        self.db.add(db_invoice)
        await self.db.flush()

        for item_data in invoice_items:
            db_item = InvoiceItem(**item_data.model_dump(), invoice_id=db_invoice.id)
            self.db.add(db_item)

        await self.db.commit()
        await self.db.refresh(db_invoice)
        return db_invoice

    async def update_invoice(
        self,
        invoice_id: uuid.UUID,
        invoice_data: InvoiceUpdate,
        tenant_id: uuid.UUID,
    ) -> Optional[Invoice]:
        db_invoice = await self.get_invoice_by_id(invoice_id, tenant_id)
        if not db_invoice:
            return None

        update_data = invoice_data.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_invoice, key, value)

        await self.db.commit()
        await self.db.refresh(db_invoice)
        return db_invoice

    async def delete_invoice(self, invoice_id: uuid.UUID, tenant_id: uuid.UUID) -> bool:
        db_invoice = await self.get_invoice_by_id(invoice_id, tenant_id)
        if not db_invoice:
            return False
        
        await self.db.delete(db_invoice)
        await self.db.commit()
        return True

    async def generate_invoice_pdf(self, invoice_id: uuid.UUID, tenant_id: uuid.UUID) -> Optional[str]:
        db_invoice = await self.get_invoice_by_id(invoice_id, tenant_id)
        if not db_invoice:
            return None

        # 1. Setup Jinja2 environment
        template_dir = os.path.join(os.path.dirname(__file__), '..', 'templates')
        env = Environment(loader=FileSystemLoader(template_dir))
        template = env.get_template('invoice_template.html')

        # 2. Render HTML
        html_out = template.render(invoice=db_invoice)

        # 3. Generate PDF
        pdf_filename = f"invoice_{db_invoice.invoice_number}.pdf"
        # This path should be configured properly, e.g., using a dedicated media directory
        pdf_path = f"/app/media/{pdf_filename}" 
        
        HTML(string=html_out).write_pdf(pdf_path)

        # 4. Update invoice record
        db_invoice.pdf_file_path = pdf_path
        await self.db.commit()

        return pdf_path 