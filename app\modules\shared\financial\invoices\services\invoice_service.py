"""Service layer for invoices."""

import uuid
import os
import hashlib
import secrets
from datetime import datetime, timedelta, date
from typing import Optional, Sequence, Tuple, Dict, Any
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from sqlalchemy.orm import selectinload
from jinja2 import Environment, FileSystemLoader
from weasyprint import HTML
from fastapi import UploadFile

from ..models.invoice import Invoice, InvoiceItem, InvoiceStatus, InvoiceType
from ..schemas.invoice import (
    InvoiceCreate, InvoiceUpdate, B2BInvoiceCreate,
    B2BInvoiceFileUpload, B2BInvoiceStats
)

class InvoiceService:
    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_invoice_by_id(self, invoice_id: uuid.UUID, tenant_id: uuid.UUID) -> Optional[Invoice]:
        stmt = select(Invoice).where(Invoice.id == invoice_id, Invoice.tenant_id == tenant_id)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    async def get_invoices(
        self,
        tenant_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        status: Optional[InvoiceStatus] = None,
    ) -> Sequence[Invoice]:
        stmt = select(Invoice).where(Invoice.tenant_id == tenant_id)
        if status:
            stmt = stmt.where(Invoice.status == status)
        stmt = stmt.offset(skip).limit(limit)
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def create_invoice(
        self,
        invoice_data: InvoiceCreate,
        tenant_id: uuid.UUID,
        user_id: uuid.UUID,
    ) -> Invoice:
        # This is a simplified creation method.
        # A more complex method will be needed to create an invoice from an order.
        invoice_items = invoice_data.items
        invoice_dict = invoice_data.model_dump(exclude={"items"})
        
        db_invoice = Invoice(
            **invoice_dict,
            tenant_id=tenant_id,
            created_by=user_id,
        )
        
        self.db.add(db_invoice)
        await self.db.flush()

        for item_data in invoice_items:
            db_item = InvoiceItem(**item_data.model_dump(), invoice_id=db_invoice.id)
            self.db.add(db_item)

        await self.db.commit()
        await self.db.refresh(db_invoice)
        return db_invoice

    async def update_invoice(
        self,
        invoice_id: uuid.UUID,
        invoice_data: InvoiceUpdate,
        tenant_id: uuid.UUID,
    ) -> Optional[Invoice]:
        db_invoice = await self.get_invoice_by_id(invoice_id, tenant_id)
        if not db_invoice:
            return None

        update_data = invoice_data.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_invoice, key, value)

        await self.db.commit()
        await self.db.refresh(db_invoice)
        return db_invoice

    async def delete_invoice(self, invoice_id: uuid.UUID, tenant_id: uuid.UUID) -> bool:
        db_invoice = await self.get_invoice_by_id(invoice_id, tenant_id)
        if not db_invoice:
            return False
        
        await self.db.delete(db_invoice)
        await self.db.commit()
        return True

    async def generate_invoice_pdf(self, invoice_id: uuid.UUID, tenant_id: uuid.UUID) -> Optional[str]:
        db_invoice = await self.get_invoice_by_id(invoice_id, tenant_id)
        if not db_invoice:
            return None

        # 1. Setup Jinja2 environment
        template_dir = os.path.join(os.path.dirname(__file__), '..', 'templates')
        env = Environment(loader=FileSystemLoader(template_dir))
        template = env.get_template('invoice_template.html')

        # 2. Render HTML
        html_out = template.render(invoice=db_invoice)

        # 3. Generate PDF
        pdf_filename = f"invoice_{db_invoice.invoice_number}.pdf"
        # This path should be configured properly, e.g., using a dedicated media directory
        pdf_path = f"/app/media/{pdf_filename}" 
        
        HTML(string=html_out).write_pdf(pdf_path)

        # 4. Update invoice record
        db_invoice.pdf_file_path = pdf_path
        await self.db.commit()

        return pdf_path

    # B2B specific methods
    async def create_b2b_invoice(
        self,
        invoice_data: B2BInvoiceCreate,
        tenant_id: uuid.UUID,
        created_by: uuid.UUID,
    ) -> Invoice:
        """Create B2B invoice between vendor and customer."""

        # Generate invoice number
        invoice_number = await self._generate_invoice_number(tenant_id)

        # Extract items
        invoice_items = invoice_data.items
        invoice_dict = invoice_data.model_dump(exclude={"items"})

        # Create invoice
        db_invoice = Invoice(
            **invoice_dict,
            tenant_id=tenant_id,
            created_by=created_by,
            invoice_number=invoice_number,
            status=InvoiceStatus.DRAFT.value
        )

        self.db.add(db_invoice)
        await self.db.flush()

        # Add items and calculate totals
        subtotal = Decimal('0.00')
        tax_total = Decimal('0.00')

        for item_data in invoice_items:
            item_dict = item_data.model_dump()

            # Calculate item totals
            quantity = item_dict['quantity']
            unit_price = item_dict['unit_price']
            tax_rate = item_dict.get('tax_rate', Decimal('0.00'))

            total_price = quantity * unit_price
            tax_amount = total_price * (tax_rate / 100)

            item_dict.update({
                'total_price': total_price,
                'tax_amount': tax_amount,
                'invoice_id': db_invoice.id
            })

            db_item = InvoiceItem(**item_dict)
            self.db.add(db_item)

            subtotal += total_price
            tax_total += tax_amount

        # Update invoice totals
        db_invoice.subtotal = subtotal
        db_invoice.tax_amount = tax_total
        db_invoice.total_amount = subtotal + tax_total - db_invoice.discount_amount

        await self.db.commit()
        await self.db.refresh(db_invoice)

        return db_invoice

    async def upload_b2b_invoice_file(
        self,
        invoice_id: uuid.UUID,
        file: UploadFile,
        vendor_id: uuid.UUID,
        tenant_id: uuid.UUID
    ) -> Invoice:
        """Upload file for B2B invoice."""

        # Get invoice and verify ownership
        invoice = await self.get_invoice_by_id(invoice_id, tenant_id)
        if not invoice:
            raise ValueError("Invoice not found")

        if invoice.vendor_id != vendor_id:
            raise ValueError("Access denied: not invoice owner")

        # Validate file
        await self._validate_upload_file(file)

        # Read file content
        file_content = await file.read()
        file_hash = hashlib.sha256(file_content).hexdigest()

        # Generate unique filename
        file_extension = os.path.splitext(file.filename)[1]
        unique_filename = f"{invoice_id}_{secrets.token_hex(8)}{file_extension}"

        # Create directory structure
        upload_dir = self._get_upload_directory(tenant_id, vendor_id)
        os.makedirs(upload_dir, exist_ok=True)

        # Save file
        file_path = os.path.join(upload_dir, unique_filename)
        with open(file_path, 'wb') as f:
            f.write(file_content)

        # Update invoice
        invoice.file_name = unique_filename
        invoice.file_original_name = file.filename
        invoice.file_path = file_path
        invoice.file_size = len(file_content)
        invoice.file_type = file.content_type
        invoice.file_hash = file_hash

        # Update status if draft
        if invoice.status == InvoiceStatus.DRAFT.value:
            invoice.status = InvoiceStatus.PENDING.value

        await self.db.commit()
        await self.db.refresh(invoice)

        return invoice

    async def send_b2b_invoice(
        self,
        invoice_id: uuid.UUID,
        vendor_id: uuid.UUID,
        tenant_id: uuid.UUID
    ) -> Invoice:
        """Send B2B invoice to customer."""

        invoice = await self.get_invoice_by_id(invoice_id, tenant_id)
        if not invoice:
            raise ValueError("Invoice not found")

        if invoice.vendor_id != vendor_id:
            raise ValueError("Access denied: not invoice owner")

        if invoice.status not in [InvoiceStatus.PENDING.value, InvoiceStatus.DRAFT.value]:
            raise ValueError("Invoice cannot be sent in current status")

        # Update status and timestamp
        invoice.status = InvoiceStatus.SENT.value
        invoice.sent_date = datetime.utcnow()

        # TODO: Send notification to customer

        await self.db.commit()
        await self.db.refresh(invoice)

        return invoice

    async def mark_b2b_invoice_as_viewed(
        self,
        invoice_id: uuid.UUID,
        customer_id: uuid.UUID,
        tenant_id: uuid.UUID
    ) -> Invoice:
        """Mark B2B invoice as viewed by customer."""

        invoice = await self.get_invoice_by_id(invoice_id, tenant_id)
        if not invoice:
            raise ValueError("Invoice not found")

        if invoice.customer_b2b_id != customer_id:
            raise ValueError("Access denied: not invoice recipient")

        # Mark as viewed
        invoice.mark_as_viewed(customer_id)

        await self.db.commit()
        await self.db.refresh(invoice)

        return invoice

    async def generate_b2b_download_token(
        self,
        invoice_id: uuid.UUID,
        user_id: uuid.UUID,
        tenant_id: uuid.UUID,
        expires_hours: int = 24
    ) -> str:
        """Generate download token for B2B invoice."""

        invoice = await self.get_invoice_by_id(invoice_id, tenant_id)
        if not invoice:
            raise ValueError("Invoice not found")

        # Check access permissions
        if invoice.vendor_id != user_id and invoice.customer_b2b_id != user_id:
            raise ValueError("Access denied")

        if not invoice.file_path:
            raise ValueError("No file attached to invoice")

        # Generate token
        token = invoice.generate_access_token(expires_hours)

        await self.db.commit()

        return token

    async def get_b2b_invoice_stats(
        self,
        tenant_id: uuid.UUID,
        vendor_id: Optional[uuid.UUID] = None,
        customer_id: Optional[uuid.UUID] = None
    ) -> B2BInvoiceStats:
        """Get B2B invoice statistics."""

        # Base query
        base_query = select(Invoice).where(
            and_(
                Invoice.tenant_id == tenant_id,
                Invoice.invoice_type.in_([InvoiceType.B2B_VENDOR, InvoiceType.B2B_CUSTOMER])
            )
        )

        # Apply filters
        if vendor_id:
            base_query = base_query.where(Invoice.vendor_id == vendor_id)
        if customer_id:
            base_query = base_query.where(Invoice.customer_b2b_id == customer_id)

        # Get all invoices
        result = await self.db.execute(base_query)
        invoices = result.scalars().all()

        # Calculate statistics
        stats = B2BInvoiceStats()

        if not invoices:
            return stats

        stats.total_invoices = len(invoices)
        stats.total_amount = sum(inv.total_amount for inv in invoices)
        stats.paid_amount = sum(inv.total_amount for inv in invoices if inv.status == InvoiceStatus.PAID.value)
        stats.outstanding_amount = stats.total_amount - stats.paid_amount

        # Status breakdown
        for invoice in invoices:
            if invoice.status == InvoiceStatus.DRAFT.value:
                stats.draft_count += 1
            elif invoice.status == InvoiceStatus.PENDING.value:
                stats.pending_count += 1
            elif invoice.status == InvoiceStatus.SENT.value:
                stats.sent_count += 1
            elif invoice.status == InvoiceStatus.VIEWED.value:
                stats.viewed_count += 1
            elif invoice.status == InvoiceStatus.PAID.value:
                stats.paid_count += 1
            elif invoice.status == InvoiceStatus.OVERDUE.value:
                stats.overdue_count += 1
            elif invoice.status == InvoiceStatus.CANCELLED.value:
                stats.cancelled_count += 1
            elif invoice.status == InvoiceStatus.DISPUTED.value:
                stats.disputed_count += 1

        # Time-based stats
        current_month = datetime.utcnow().replace(day=1)
        last_month = (current_month - timedelta(days=1)).replace(day=1)

        stats.this_month_amount = sum(
            inv.total_amount for inv in invoices
            if inv.issue_date >= current_month.date()
        )

        stats.last_month_amount = sum(
            inv.total_amount for inv in invoices
            if last_month.date() <= inv.issue_date < current_month.date()
        )

        # Average payment days (for paid invoices)
        paid_invoices = [inv for inv in invoices if inv.paid_date and inv.issue_date]
        if paid_invoices:
            total_days = sum(
                (inv.paid_date - inv.issue_date).days
                for inv in paid_invoices
            )
            stats.avg_payment_days = total_days / len(paid_invoices)

        return stats

    # Helper methods
    async def _generate_invoice_number(self, tenant_id: uuid.UUID) -> str:
        """Generate unique invoice number for tenant."""

        # Get current year
        year = datetime.utcnow().year

        # Count invoices for this year
        count_query = select(func.count(Invoice.id)).where(
            and_(
                Invoice.tenant_id == tenant_id,
                func.extract('year', Invoice.created_at) == year
            )
        )

        result = await self.db.execute(count_query)
        count = result.scalar() or 0

        # Generate number: INV-YYYY-NNNN
        return f"INV-{year}-{count + 1:04d}"

    async def _validate_upload_file(self, file: UploadFile):
        """Validate uploaded file."""

        # Allowed file types
        allowed_types = {
            'application/pdf': '.pdf',
            'application/msword': '.doc',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
            'application/vnd.ms-excel': '.xls',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',
            'image/jpeg': '.jpg',
            'image/png': '.png',
            'text/plain': '.txt'
        }

        # Check file type
        if file.content_type not in allowed_types:
            raise ValueError(f"File type {file.content_type} not allowed")

        # Check file size (max 10MB)
        max_size = 10 * 1024 * 1024
        file_content = await file.read()
        await file.seek(0)  # Reset file pointer

        if len(file_content) > max_size:
            raise ValueError(f"File size exceeds maximum of {max_size} bytes")

    def _get_upload_directory(self, tenant_id: uuid.UUID, vendor_id: uuid.UUID) -> str:
        """Get upload directory for tenant and vendor."""

        base_dir = "/app/uploads/invoices"  # Should be configurable
        now = datetime.utcnow()

        return os.path.join(
            base_dir,
            str(tenant_id),
            str(vendor_id),
            str(now.year),
            f"{now.month:02d}"
        )