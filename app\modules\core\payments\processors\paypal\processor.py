"""PayPal payment processor implementation."""

import logging  # noqa: E402
import aiohttp
from typing import Dict, Any, Optional
from decimal import Decimal

from app.modules.core.payments.processors.base import PaymentProcessorInterface  # noqa: E402
from app.modules.core.payments.models.payment_transaction import PaymentStatus

logger = logging.getLogger(__name__)


class PayPalProcessor(PaymentProcessorInterface):
    """
    PayPal payment processor implementation.
    """

    def __init__(self, processor_config: Dict[str, Any]):
        """
        Initialize the PayPal processor with the given configuration.

        Args:
            processor_config: A dictionary containing the processor configuration.
                This should include API keys, secrets, and other settings.
        """
        super().__init__(processor_config)

        # Set the API credentials based on sandbox mode
        if self.sandbox_mode:
            self.base_url = "https://api-m.sandbox.paypal.com"
            self.client_id = self.config.get("sandbox_client_id")
            self.client_secret = self.config.get("sandbox_client_secret")
        else:
            self.base_url = "https://api-m.paypal.com"
            self.client_id = self.config.get("client_id")
            self.client_secret = self.config.get("client_secret")

        if not self.client_id or not self.client_secret:
            raise ValueError("PayPal client ID and client secret are required")

        # Store the return URL and cancel URL
        self.return_url = self.config.get("return_url", "https://example.com/return")
        self.cancel_url = self.config.get("cancel_url", "https://example.com/cancel")

    async def _get_access_token(self) -> str:
        """
        Get an access token from PayPal.

        Returns:
            The access token.

        Raises:
            Exception: If the token request fails.
        """
        async with aiohttp.ClientSession() as session:
            auth = aiohttp.BasicAuth(self.client_id, self.client_secret)
            headers = {
                "Accept": "application/json",
                "Accept-Language": "en_US",
            }
            data = {"grant_type": "client_credentials"}

            async with session.post(
                f"{self.base_url}/v1/oauth2/token",
                auth=auth,
                headers=headers,
                data=data,
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"PayPal token error: {error_text}")
                    raise Exception(f"Failed to get PayPal access token: {error_text}")

                result = await response.json()
                return result["access_token"]

    async def process_payment(
        self,
        amount: Decimal,
        currency: str,
        payment_method_id: Optional[str] = None,
        customer_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Process a payment using PayPal.

        Args:
            amount: The amount to charge.
            currency: The currency code (e.g., 'USD', 'BRL').
            payment_method_id: An optional ID of the payment method to use.
            customer_id: An optional ID of the customer in PayPal.
            metadata: Optional additional data to include with the payment.

        Returns:
            A dictionary containing the payment details, including:
                - external_id: The ID of the payment in PayPal.
                - status: The status of the payment.
                - error: An error message if the payment failed.
        """
        try:
            # Get access token
            access_token = await self._get_access_token()

            # Create order
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {access_token}",
                }

                # Prepare the order data
                order_data = {
                    "intent": "CAPTURE",
                    "purchase_units": [
                        {
                            "amount": {
                                "currency_code": currency.upper(),
                                "value": str(amount),
                            },
                            "description": (
                                metadata.get("description", "Payment") if metadata else "Payment"
                            ),
                        }
                    ],
                    "application_context": {
                        "return_url": self.return_url,
                        "cancel_url": self.cancel_url,
                    },
                }

                # Add custom fields if metadata is provided
                if metadata:
                    custom_id = metadata.get("custom_id")
                    if custom_id:
                        order_data["purchase_units"][0]["custom_id"] = custom_id

                # Create the order
                async with session.post(
                    f"{self.base_url}/v2/checkout/orders",
                    headers=headers,
                    json=order_data,
                ) as response:
                    if response.status not in (200, 201):
                        error_text = await response.text()
                        logger.error(f"PayPal order creation error: {error_text}")
                        return {
                            "status": PaymentStatus.FAILED,
                            "error": f"Failed to create PayPal order: {error_text}",
                        }

                    result = await response.json()

                    # Extract the approval URL
                    approval_url = None
                    for link in result.get("links", []):
                        if link.get("rel") == "approve":
                            approval_url = link.get("href")
                            break

                    # Map the status
                    status = self.map_status_to_internal(result.get("status", ""))

                    return {
                        "external_id": result.get("id"),
                        "status": status,
                        "approval_url": approval_url,
                    }

        except Exception as e:
            logger.error(f"Unexpected error processing PayPal payment: {str(e)}")
            return {
                "status": PaymentStatus.FAILED,
                "error": f"Unexpected error: {str(e)}",
            }

    async def process_refund(
        self,
        payment_id: str,
        amount: Optional[Decimal] = None,
        reason: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Process a refund for a payment using PayPal.

        Args:
            payment_id: The ID of the payment in PayPal.
            amount: The amount to refund. If None, refund the full amount.
            reason: An optional reason for the refund.
            metadata: Optional additional data to include with the refund.

        Returns:
            A dictionary containing the refund details, including:
                - external_id: The ID of the refund in PayPal.
                - status: The status of the refund.
                - error: An error message if the refund failed.
        """
        try:
            # Get access token
            access_token = await self._get_access_token()

            # Get the capture ID from the order
            capture_id = await self._get_capture_id_from_order(access_token, payment_id)

            if not capture_id:
                return {
                    "status": PaymentStatus.FAILED,
                    "error": f"Failed to find capture ID for order {payment_id}",
                }

            # Process the refund
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {access_token}",
                }

                # Prepare the refund data
                refund_data = {}

                if amount:
                    refund_data["amount"] = {
                        "value": str(amount),
                        "currency_code": (metadata.get("currency", "USD") if metadata else "USD"),
                    }

                if reason:
                    refund_data["note_to_payer"] = reason

                # Create the refund
                async with session.post(
                    f"{self.base_url}/v2/payments/captures/{capture_id}/refund",
                    headers=headers,
                    json=refund_data,
                ) as response:
                    if response.status not in (200, 201):
                        error_text = await response.text()
                        logger.error(f"PayPal refund error: {error_text}")
                        return {
                            "status": PaymentStatus.FAILED,
                            "error": f"Failed to process PayPal refund: {error_text}",
                        }

                    result = await response.json()

                    # Map the status
                    status = self.map_status_to_internal(result.get("status", ""))

                    return {
                        "external_id": result.get("id"),
                        "status": status,
                    }

        except Exception as e:
            logger.error(f"Unexpected error processing PayPal refund: {str(e)}")
            return {
                "status": PaymentStatus.FAILED,
                "error": f"Unexpected error: {str(e)}",
            }

    async def _get_capture_id_from_order(self, access_token: str, order_id: str) -> Optional[str]:
        """
        Get the capture ID from an order.

        Args:
            access_token: The PayPal access token.
            order_id: The ID of the order.

        Returns:
            The capture ID, or None if not found.
        """
        async with aiohttp.ClientSession() as session:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {access_token}",
            }

            # Get the order details
            async with session.get(
                f"{self.base_url}/v2/checkout/orders/{order_id}", headers=headers
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"PayPal order details error: {error_text}")
                    return None

                result = await response.json()

                # Extract the capture ID
                for purchase_unit in result.get("purchase_units", []):
                    for capture in purchase_unit.get("payments", {}).get("captures", []):
                        return capture.get("id")

                return None

    async def get_payment_status(self, payment_id: str) -> Dict[str, Any]:
        """
        Get the status of a payment from PayPal.

        Args:
            payment_id: The ID of the payment in PayPal.

        Returns:
            A dictionary containing the payment details, including:
                - status: The status of the payment.
                - error: An error message if the status check failed.
        """
        try:
            # Get access token
            access_token = await self._get_access_token()

            # Get the order details
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {access_token}",
                }

                async with session.get(
                    f"{self.base_url}/v2/checkout/orders/{payment_id}", headers=headers
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"PayPal order status error: {error_text}")
                        return {
                            "status": PaymentStatus.FAILED,
                            "error": f"Failed to get PayPal order status: {error_text}",
                        }

                    result = await response.json()

                    # Extract the amount
                    amount = None
                    currency = None
                    for purchase_unit in result.get("purchase_units", []):
                        amount_data = purchase_unit.get("amount", {})
                        amount = Decimal(amount_data.get("value", "0"))
                        currency = amount_data.get("currency_code")
                        break

                    # Map the status
                    status = self.map_status_to_internal(result.get("status", ""))

                    return {
                        "status": status,
                        "external_id": result.get("id"),
                        "amount": amount,
                        "currency": currency,
                    }

        except Exception as e:
            logger.error(f"Unexpected error getting PayPal payment status: {str(e)}")
            return {
                "status": PaymentStatus.FAILED,
                "error": f"Unexpected error: {str(e)}",
            }

    async def get_refund_status(self, refund_id: str) -> Dict[str, Any]:
        """
        Get the status of a refund from PayPal.

        Args:
            refund_id: The ID of the refund in PayPal.

        Returns:
            A dictionary containing the refund details, including:
                - status: The status of the refund.
                - error: An error message if the status check failed.
        """
        try:
            # Get access token
            access_token = await self._get_access_token()

            # Get the refund details
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {access_token}",
                }

                async with session.get(
                    f"{self.base_url}/v2/payments/refunds/{refund_id}", headers=headers
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"PayPal refund status error: {error_text}")
                        return {
                            "status": PaymentStatus.FAILED,
                            "error": f"Failed to get PayPal refund status: {error_text}",
                        }

                    result = await response.json()

                    # Extract the amount
                    amount = None
                    currency = None
                    amount_data = result.get("amount", {})
                    if amount_data:
                        amount = Decimal(amount_data.get("value", "0"))
                        currency = amount_data.get("currency_code")

                    # Map the status
                    status = self.map_status_to_internal(result.get("status", ""))

                    return {
                        "status": status,
                        "external_id": result.get("id"),
                        "amount": amount,
                        "currency": currency,
                    }

        except Exception as e:
            logger.error(f"Unexpected error getting PayPal refund status: {str(e)}")
            return {
                "status": PaymentStatus.FAILED,
                "error": f"Unexpected error: {str(e)}",
            }

    async def create_payment_method(
        self,
        method_type: str,
        method_details: Dict[str, Any],
        customer_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Create a payment method in PayPal.

        Note: PayPal doesn't have a direct equivalent to creating a payment method.
        This method is a placeholder for compatibility with the interface.

        Args:
            method_type: The type of payment method.
            method_details: Details of the payment method.
            customer_id: An optional ID of the customer to associate with the method.

        Returns:
            A dictionary containing the payment method details, including:
                - external_id: The ID of the payment method in PayPal.
                - error: An error message if the creation failed.
        """
        # PayPal doesn't have a direct equivalent to creating a payment method
        # This is a placeholder for compatibility with the interface
        return {
            "error": "PayPal does not support creating payment methods directly. Payment methods are created during the checkout process.",  # noqa: E501
        }

    async def delete_payment_method(self, method_id: str) -> Dict[str, Any]:
        """
        Delete a payment method from PayPal.

        Note: PayPal doesn't have a direct equivalent to deleting a payment method.
        This method is a placeholder for compatibility with the interface.

        Args:
            method_id: The ID of the payment method in PayPal.

        Returns:
            A dictionary containing the result, including:
                - success: Whether the deletion was successful.
                - error: An error message if the deletion failed.
        """
        # PayPal doesn't have a direct equivalent to deleting a payment method
        # This is a placeholder for compatibility with the interface
        return {
            "success": False,
            "error": "PayPal does not support deleting payment methods directly.",
        }

    async def create_customer(self, customer_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a customer in PayPal.

        Note: PayPal doesn't have a direct equivalent to creating a customer.
        This method is a placeholder for compatibility with the interface.

        Args:
            customer_details: Details of the customer.

        Returns:
            A dictionary containing the customer details, including:
                - external_id: The ID of the customer in PayPal.
                - error: An error message if the creation failed.
        """
        # PayPal doesn't have a direct equivalent to creating a customer
        # This is a placeholder for compatibility with the interface
        return {
            "error": "PayPal does not support creating customers directly. Customer information is collected during the checkout process.",  # noqa: E501
        }

    async def update_customer(
        self, customer_id: str, customer_details: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Update a customer in PayPal.

        Note: PayPal doesn't have a direct equivalent to updating a customer.
        This method is a placeholder for compatibility with the interface.

        Args:
            customer_id: The ID of the customer in PayPal.
            customer_details: Updated details of the customer.

        Returns:
            A dictionary containing the customer details, including:
                - external_id: The ID of the customer in PayPal.
                - error: An error message if the update failed.
        """
        # PayPal doesn't have a direct equivalent to updating a customer
        # This is a placeholder for compatibility with the interface
        return {
            "error": "PayPal does not support updating customers directly.",
        }

    async def delete_customer(self, customer_id: str) -> Dict[str, Any]:
        """
        Delete a customer from PayPal.

        Note: PayPal doesn't have a direct equivalent to deleting a customer.
        This method is a placeholder for compatibility with the interface.

        Args:
            customer_id: The ID of the customer in PayPal.

        Returns:
            A dictionary containing the result, including:
                - success: Whether the deletion was successful.
                - error: An error message if the deletion failed.
        """
        # PayPal doesn't have a direct equivalent to deleting a customer
        # This is a placeholder for compatibility with the interface
        return {
            "success": False,
            "error": "PayPal does not support deleting customers directly.",
        }

    @staticmethod
    def map_status_to_internal(processor_status: str) -> PaymentStatus:
        """
        Map a PayPal-specific status to an internal PaymentStatus.

        Args:
            processor_status: The status from PayPal.

        Returns:
            The corresponding internal PaymentStatus.
        """
        status_map = {
            "created": PaymentStatus.PENDING,
            "saved": PaymentStatus.PENDING,
            "approved": PaymentStatus.PENDING,
            "voided": PaymentStatus.CANCELLED,
            "completed": PaymentStatus.COMPLETED,
            "payer_action_required": PaymentStatus.PENDING,
            "captured": PaymentStatus.COMPLETED,
            "declined": PaymentStatus.FAILED,
            "partially_refunded": PaymentStatus.PARTIALLY_REFUNDED,
            "refunded": PaymentStatus.REFUNDED,
        }
        return status_map.get(processor_status.lower(), PaymentStatus.PENDING)
