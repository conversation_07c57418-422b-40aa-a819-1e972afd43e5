"""
Auction and Lottery Background Tasks
===================================

Background tasks for automatic auction/lottery management.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import List

from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.core.database import get_db
from app.modules.core.eshop.models.auction_lottery import (
    Auction, Lottery, AuctionStatus, LotteryStatus
)
from app.modules.core.eshop.services.auction_lottery_service import (
    AuctionService, LotteryService
)
from app.modules.core.eshop.websockets.auction_lottery_websockets import (
    auction_lottery_ws_manager
)

logger = logging.getLogger(__name__)


class AuctionLotteryTaskManager:
    """Manager for auction and lottery background tasks."""
    
    def __init__(self):
        self.is_running = False
        self.check_interval = 60  # Check every minute
    
    async def start_background_tasks(self):
        """Start all background tasks."""
        if self.is_running:
            logger.warning("Background tasks already running")
            return
        
        self.is_running = True
        logger.info("Starting auction/lottery background tasks")
        
        # Start concurrent tasks
        await asyncio.gather(
            self._auction_finalization_task(),
            self._lottery_drawing_task(),
            self._time_update_task(),
            return_exceptions=True
        )
    
    async def stop_background_tasks(self):
        """Stop all background tasks."""
        self.is_running = False
        logger.info("Stopping auction/lottery background tasks")
    
    async def _auction_finalization_task(self):
        """Task to automatically finalize expired auctions."""
        while self.is_running:
            try:
                db = next(get_db())
                service = AuctionService(db)
                
                # Find and finalize expired auctions
                expired_auctions = service.finalize_expired_auctions()
                
                if expired_auctions:
                    logger.info(f"Finalized {len(expired_auctions)} expired auctions")
                    
                    # Notify WebSocket clients
                    for auction in expired_auctions:
                        await auction_lottery_ws_manager.emit_auction_ended(
                            auction_id=auction.id,
                            winner_id=auction.winner_id,
                            winning_bid=float(auction.winning_bid) if auction.winning_bid else None,
                            reason="time_expired"
                        )
                
                db.close()
                
            except Exception as e:
                logger.error(f"Error in auction finalization task: {str(e)}")
            
            # Wait before next check
            await asyncio.sleep(self.check_interval)
    
    async def _lottery_drawing_task(self):
        """Task to automatically draw lottery winners."""
        while self.is_running:
            try:
                db = next(get_db())
                service = LotteryService(db)
                
                # Find lotteries ready for drawing
                now = datetime.utcnow()
                ready_lotteries = db.query(Lottery).filter(
                    and_(
                        Lottery.status == LotteryStatus.ACTIVE,
                        Lottery.draw_time.isnot(None),
                        Lottery.draw_time <= now
                    )
                ).all()
                
                for lottery in ready_lotteries:
                    try:
                        # Draw winner
                        drawn_lottery, winning_ticket = service.draw_winner(lottery.id)
                        
                        # Count total participants
                        from sqlalchemy import func
                        from app.modules.core.eshop.models.auction_lottery import LotteryTicket
                        
                        total_participants = db.query(
                            func.count(func.distinct(LotteryTicket.participant_id))
                        ).filter(
                            LotteryTicket.lottery_id == lottery.id
                        ).scalar()
                        
                        # Notify WebSocket clients
                        await auction_lottery_ws_manager.emit_lottery_winner_drawn(
                            lottery_id=lottery.id,
                            winner_id=drawn_lottery.winner_id,
                            winning_ticket_number=winning_ticket.ticket_number,
                            total_participants=total_participants
                        )
                        
                        logger.info(f"Drew winner for lottery {lottery.id}: {winning_ticket.ticket_number}")
                        
                    except Exception as e:
                        logger.error(f"Error drawing lottery {lottery.id}: {str(e)}")
                
                db.close()
                
            except Exception as e:
                logger.error(f"Error in lottery drawing task: {str(e)}")
            
            # Wait before next check
            await asyncio.sleep(self.check_interval)
    
    async def _time_update_task(self):
        """Task to send time remaining updates for active auctions."""
        while self.is_running:
            try:
                db = next(get_db())
                
                # Get active auctions
                active_auctions = db.query(Auction).filter(
                    Auction.status == AuctionStatus.ACTIVE
                ).all()
                
                for auction in active_auctions:
                    time_remaining = auction.time_remaining
                    
                    if time_remaining is not None and time_remaining > 0:
                        # Send time update every minute for auctions with < 1 hour remaining
                        if time_remaining <= 3600:  # 1 hour
                            await auction_lottery_ws_manager.emit_time_update(
                                auction_id=auction.id,
                                time_remaining=time_remaining
                            )
                
                db.close()
                
            except Exception as e:
                logger.error(f"Error in time update task: {str(e)}")
            
            # Wait before next update
            await asyncio.sleep(60)  # Update every minute
    
    async def finalize_auction_manually(self, auction_id: str) -> bool:
        """Manually finalize a specific auction."""
        try:
            db = next(get_db())
            service = AuctionService(db)
            
            # Get auction
            from uuid import UUID
            auction = db.query(Auction).filter(
                Auction.id == UUID(auction_id),
                Auction.status == AuctionStatus.ACTIVE
            ).first()
            
            if not auction:
                logger.warning(f"Auction {auction_id} not found or not active")
                return False
            
            # Check if expired
            if datetime.utcnow() >= auction.end_time:
                auction.status = AuctionStatus.ENDED
                db.commit()
                
                # Notify WebSocket clients
                await auction_lottery_ws_manager.emit_auction_ended(
                    auction_id=auction.id,
                    winner_id=auction.winner_id,
                    winning_bid=float(auction.winning_bid) if auction.winning_bid else None,
                    reason="time_expired"
                )
                
                logger.info(f"Manually finalized auction {auction_id}")
                db.close()
                return True
            
            db.close()
            return False
            
        except Exception as e:
            logger.error(f"Error manually finalizing auction {auction_id}: {str(e)}")
            return False
    
    async def draw_lottery_manually(self, lottery_id: str) -> bool:
        """Manually draw a lottery winner."""
        try:
            db = next(get_db())
            service = LotteryService(db)
            
            from uuid import UUID
            lottery, winning_ticket = service.draw_winner(UUID(lottery_id))
            
            # Count total participants
            from sqlalchemy import func
            from app.modules.core.eshop.models.auction_lottery import LotteryTicket
            
            total_participants = db.query(
                func.count(func.distinct(LotteryTicket.participant_id))
            ).filter(
                LotteryTicket.lottery_id == UUID(lottery_id)
            ).scalar()
            
            # Notify WebSocket clients
            await auction_lottery_ws_manager.emit_lottery_winner_drawn(
                lottery_id=lottery.id,
                winner_id=lottery.winner_id,
                winning_ticket_number=winning_ticket.ticket_number,
                total_participants=total_participants
            )
            
            logger.info(f"Manually drew winner for lottery {lottery_id}: {winning_ticket.ticket_number}")
            db.close()
            return True
            
        except Exception as e:
            logger.error(f"Error manually drawing lottery {lottery_id}: {str(e)}")
            return False


# Global task manager instance
auction_lottery_task_manager = AuctionLotteryTaskManager()


# Utility functions for external use
async def start_auction_lottery_tasks():
    """Start auction/lottery background tasks."""
    await auction_lottery_task_manager.start_background_tasks()


async def stop_auction_lottery_tasks():
    """Stop auction/lottery background tasks."""
    await auction_lottery_task_manager.stop_background_tasks()


async def finalize_auction(auction_id: str) -> bool:
    """Manually finalize an auction."""
    return await auction_lottery_task_manager.finalize_auction_manually(auction_id)


async def draw_lottery(lottery_id: str) -> bool:
    """Manually draw a lottery."""
    return await auction_lottery_task_manager.draw_lottery_manually(lottery_id)
