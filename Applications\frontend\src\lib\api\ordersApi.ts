import { apiClient } from './client';
import {
  Order,
  OrderFilters,
  OrderListResponse,
  OrderStats,
  OrderCreate,
  OrderUpdate,
  OrderStatusUpdate,
  OrderStatus
} from '@/types/order';

/**
 * Get paginated list of orders with optional filters
 */
export const getOrders = async (
  page: number = 1,
  perPage: number = 10,
  filters?: OrderFilters
): Promise<OrderListResponse> => {
  const params = new URLSearchParams({
    page: page.toString(),
    per_page: perPage.toString(),
  });

  if (filters) {
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value.toString());
      }
    });
  }

  const response = await apiClient.get(`/orders?${params.toString()}`);
  return response.data;
};

/**
 * Get order by ID
 */
export const getOrderById = async (orderId: string): Promise<Order> => {
  const response = await apiClient.get(`/orders/${orderId}`);
  return response.data;
};

/**
 * Get order statistics
 */
export const getOrderStats = async (): Promise<OrderStats> => {
  const response = await apiClient.get('/orders/stats');
  return response.data;
};

/**
 * Create a new order
 */
export const createOrder = async (orderData: OrderCreate): Promise<Order> => {
  const response = await apiClient.post('/orders', orderData);
  return response.data;
};

/**
 * Update an existing order
 */
export const updateOrder = async (
  orderId: string,
  orderData: OrderUpdate
): Promise<Order> => {
  const response = await apiClient.put(`/orders/${orderId}`, orderData);
  return response.data;
};

/**
 * Update order status
 */
export const updateOrderStatus = async (
  orderId: string,
  statusData: OrderStatusUpdate
): Promise<Order> => {
  const response = await apiClient.patch(`/orders/${orderId}/status`, statusData);
  return response.data;
};

/**
 * Confirm order
 */
export const confirmOrder = async (orderId: string, notes?: string): Promise<Order> => {
  return updateOrderStatus(orderId, { status: OrderStatus.CONFIRMED, notes });
};

/**
 * Cancel order
 */
export const cancelOrder = async (orderId: string, notes?: string): Promise<Order> => {
  return updateOrderStatus(orderId, { status: OrderStatus.CANCELLED, notes });
};

/**
 * Mark order as delivered
 */
export const deliverOrder = async (orderId: string, notes?: string): Promise<Order> => {
  return updateOrderStatus(orderId, { status: OrderStatus.DELIVERED, notes });
};

/**
 * Delete an order
 */
export const deleteOrder = async (orderId: string): Promise<void> => {
  await apiClient.delete(`/orders/${orderId}`);
};

/**
 * Export orders to CSV
 */
export const exportOrders = async (filters?: OrderFilters): Promise<Blob> => {
  const params = new URLSearchParams();
  
  if (filters) {
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value.toString());
      }
    });
  }

  const response = await apiClient.get(`/orders/export?${params.toString()}`, {
    responseType: 'blob',
  });
  
  return response.data;
};

/**
 * Get order history/timeline
 */
export const getOrderHistory = async (orderId: string): Promise<any[]> => {
  const response = await apiClient.get(`/orders/${orderId}/history`);
  return response.data;
};

const ordersApi = {
  getOrders,
  getOrderById,
  getOrderStats,
  createOrder,
  updateOrder,
  updateOrderStatus,
  confirmOrder,
  cancelOrder,
  deliverOrder,
  deleteOrder,
  exportOrders,
  getOrderHistory,
};

export default ordersApi;