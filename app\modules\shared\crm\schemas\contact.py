"""Contact schemas for CRM module."""

import uuid  # noqa: E402
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, ConfigDict

from app.modules.shared.crm.models.contact import ContactType, ContactStatus  # noqa: E402


# Base Contact Schema
class ContactBase(BaseModel):
    """Base schema for Contact."""

    first_name: str = Field(..., min_length=1, max_length=100)
    last_name: str = Field(..., min_length=1, max_length=100)
    contact_type: ContactType = Field(default=ContactType.PRIMARY)
    status: ContactStatus = Field(default=ContactStatus.ACTIVE)

    # Contact information
    email: Optional[str] = None
    phone: Optional[str] = None
    mobile: Optional[str] = None

    # Job information
    job_title: Optional[str] = None
    department: Optional[str] = None

    # Preferences
    preferred_contact_method: Optional[str] = None
    communication_preferences: Optional[str] = None

    # Additional information
    notes: Optional[str] = None


# Schema for creating a new contact
class ContactCreate(ContactBase):
    """Schema for creating a new Contact."""

    account_id: uuid.UUID
    user_tenant_association_id: Optional[uuid.UUID] = None
    full_name: Optional[str] = None  # Can be computed from first_name and last_name


# Schema for updating a contact
class ContactUpdate(BaseModel):
    """Schema for updating a Contact."""

    first_name: Optional[str] = Field(None, min_length=1, max_length=100)
    last_name: Optional[str] = Field(None, min_length=1, max_length=100)
    full_name: Optional[str] = None
    contact_type: Optional[ContactType] = None
    status: Optional[ContactStatus] = None

    # Contact information
    email: Optional[str] = None
    phone: Optional[str] = None
    mobile: Optional[str] = None

    # Job information
    job_title: Optional[str] = None
    department: Optional[str] = None

    # Preferences
    preferred_contact_method: Optional[str] = None
    communication_preferences: Optional[str] = None

    # Additional information
    notes: Optional[str] = None


# Schema for reading a contact
class ContactRead(ContactBase):
    """Schema for reading a Contact."""

    id: uuid.UUID
    account_id: uuid.UUID
    tenant_id: uuid.UUID
    user_tenant_association_id: Optional[uuid.UUID] = None
    full_name: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)
