'use client';

import { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import {
  UserCircleIcon,
  ChevronDownIcon,
  UserIcon,
  ShoppingBagIcon,
  ChartBarIcon,
  ArrowRightOnRectangleIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '@/lib/auth/AuthProvider';
import { useHydration } from '@/hooks/useHydration';

interface UserNavProps {
  className?: string;
}

const UserNav = ({ className = '' }: UserNavProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { user, isAuthenticated, logout, userTenantAssociations } = useAuth();
  const isHydrated = useHydration();

  // Debug: Log authentication state
  useEffect(() => {
    console.log('UserNav - Auth State:', {
      user,
      isAuthenticated,
      userTenantAssociations: userTenantAssociations || [],
      systemRole: user?.system_role,
      hasOwnerRole: userTenantAssociations?.some(assoc => assoc.role === 'owner'),
      hasStaffRole: userTenantAssociations?.some(assoc => assoc.role === 'staff'),
    });
  }, [user, isAuthenticated, userTenantAssociations]);

  // Fechar dropdown ao clicar fora
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const toggleDropdown = () => setIsOpen(!isOpen);

  const handleLogout = () => {
    logout();
    setIsOpen(false);
  };

  const menuItems = [
    {
      label: 'Profile',
      href: '/profile',
      icon: UserIcon,
      description: 'Configurações, pontos e dados pessoais',
    },
    {
      label: 'Orders',
      href: '/orders',
      icon: ShoppingBagIcon,
      description: 'Pedidos feitos através da plataforma',
    },
  ];

  // Adicionar Dashboard apenas para roles específicos
  // System admin OU tenant owner/staff têm acesso ao dashboard
  if (user && (user.system_role === 'admin' || userTenantAssociations?.some(assoc => ['owner', 'staff'].includes(assoc.role)))) {
    menuItems.push({
      label: 'Dashboard',
      href: '/dashboard',
      icon: ChartBarIcon,
      description: 'Painel administrativo',
    });
  }

  // Prevent hydration mismatch by showing loading state until hydrated
  if (!isHydrated) {
    return (
      <div className={`flex items-center ${className}`}>
        <div className="bg-gray-200 animate-pulse px-4 py-2 rounded-lg">
          <div className="w-16 h-6 bg-gray-300 rounded"></div>
        </div>
      </div>
    );
  }

  if (!isAuthenticated || !user) {
    // Usuário não autenticado - mostrar botão "Começar"
    return (
      <div className={`flex items-center ${className}`}>
        <Link href="/auth" className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
          Começar
        </Link>
      </div>
    );
  }

  // Usuário autenticado - mostrar dropdown
  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        onClick={toggleDropdown}
        className="flex items-center space-x-3 bg-white/80 backdrop-blur-sm border border-white/20 rounded-lg px-3 py-2 hover:bg-white/90 transition-all duration-200"
        aria-label="Menu do usuário"
      >
        <div className="flex-shrink-0">
          <UserCircleIcon className="h-8 w-8 text-gray-600" />
        </div>
        <div className="hidden md:block text-left">
          <span className="block text-sm font-medium text-gray-900 truncate max-w-32">
            {user.full_name || user.email}
          </span>
        </div>
        <ChevronDownIcon
          className={`h-4 w-4 text-gray-500 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
        />
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute right-0 top-full mt-2 w-80 bg-white/95 backdrop-blur-lg border border-white/20 rounded-xl shadow-xl z-50"
          >
            <div className="p-4 border-b border-gray-100">
              <div className="flex items-center space-x-3">
                <UserCircleIcon className="h-10 w-10 text-gray-600" />
                <div>
                  <span className="block text-sm font-medium text-gray-900">
                    {user.full_name || user.email}
                  </span>
                  <span className="block text-xs text-gray-500">{user.email}</span>
                </div>
              </div>
            </div>

            <div className="p-2">
              {menuItems.map((item) => (
                <Link
                  key={item.label}
                  href={item.href}
                  className="flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors"
                  onClick={() => setIsOpen(false)}
                >
                  <item.icon className="h-5 w-5 text-gray-500" />
                  <div className="flex-1">
                    <span className="block text-sm font-medium text-gray-900">{item.label}</span>
                    <span className="block text-xs text-gray-500">{item.description}</span>
                  </div>
                </Link>
              ))}

              <button
                onClick={handleLogout}
                className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-red-50 text-red-600 transition-colors"
              >
                <ArrowRightOnRectangleIcon className="h-5 w-5" />
                <div className="flex-1 text-left">
                  <span className="block text-sm font-medium">Logout</span>
                  <span className="block text-xs text-red-500">Sair da conta</span>
                </div>
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default UserNav;
