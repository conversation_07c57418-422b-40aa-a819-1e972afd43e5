import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, ConfigDict
from decimal import Decimal
import uuid

from app.modules.core.functions.orders.models.order import OrderStatus


class OrderItemBase(BaseModel):
    """Base schema for order items."""

    menu_item_id: uuid.UUID = Field(..., description="ID of the menu item")
    name: str = Field(..., description="Name of the menu item")
    quantity: int = Field(1, ge=1, description="Quantity of the item")
    unit_price: Decimal = Field(..., gt=0, description="Unit price of the item")
    total_price: Decimal = Field(..., gt=0, description="Total price for this item")
    notes: Optional[str] = Field(None, description="Special instructions for this item")

    # IDs of selected variant and modifier options
    variant_option_ids: List[uuid.UUID] = Field(
        default_factory=list, description="IDs of selected variant options"
    )
    modifier_option_ids: List[uuid.UUID] = Field(
        default_factory=list, description="IDs of selected modifier options"
    )


class OrderItemCreate(OrderItemBase):
    """Schema for creating a new order item."""

    pass


class OrderItemRead(OrderItemBase):
    """Schema for reading an order item."""

    id: uuid.UUID
    order_id: uuid.UUID
    is_cancelled: bool
    created_at: datetime
    updated_at: datetime

    # Include variant and modifier details in the response
    variant_options: Optional[List[Dict[str, Any]]] = None
    modifier_options: Optional[List[Dict[str, Any]]] = None

    model_config = ConfigDict(from_attributes=True)


class OrderStats(BaseModel):
    """Schema for order statistics."""

    total_orders: int = Field(..., description="Total number of orders")
    pending_orders: int = Field(..., description="Number of pending orders")
    confirmed_orders: int = Field(..., description="Number of confirmed orders")
    delivered_orders: int = Field(..., description="Number of delivered orders")
    cancelled_orders: int = Field(..., description="Number of cancelled orders")
    total_revenue: Decimal = Field(..., description="Total revenue from all orders")
    average_order_value: Decimal = Field(..., description="Average order value")

    model_config = ConfigDict(from_attributes=True)


class OrderListResponse(BaseModel):
    """Schema for paginated order list response."""

    orders: List["OrderRead"] = Field(..., description="List of orders")
    total: int = Field(..., description="Total number of orders")
    page: int = Field(..., description="Current page number")
    per_page: int = Field(..., description="Number of orders per page")
    total_pages: int = Field(..., description="Total number of pages")

    model_config = ConfigDict(from_attributes=True)


class OrderBase(BaseModel):
    """Base schema for orders."""

    order_type: str = Field(..., description="Type of order (dine-in, takeout, delivery, etc.)")
    table_number: Optional[str] = Field(None, description="Table number for dine-in orders")
    subtotal: Decimal = Field(..., ge=0, description="Subtotal amount before tax and discounts")
    tax: Decimal = Field(..., ge=0, description="Tax amount")
    discount: Decimal = Field(0, ge=0, description="Discount amount")
    total: Decimal = Field(..., ge=0, description="Total amount after tax and discounts")
    notes: Optional[str] = Field(None, description="Special instructions for the order")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class OrderCreate(OrderBase):
    """Schema for creating a new order."""

    customer_id: Optional[uuid.UUID] = Field(None, description="ID of the customer")
    items: List[OrderItemCreate] = Field(..., min_items=1, description="Items in the order")

    # tenant_id and user_id will be added by the service based on the authenticated user
    # order_number will be generated by the service


class OrderUpdate(BaseModel):
    """Schema for updating an existing order."""

    status: Optional[OrderStatus] = Field(None, description="New status of the order")
    notes: Optional[str] = Field(None, description="Updated special instructions")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Updated metadata")


class OrderRead(OrderBase):
    """Schema for reading an order."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    customer_id: Optional[uuid.UUID]
    user_id: Optional[uuid.UUID]
    order_number: str
    status: OrderStatus
    items: List[OrderItemRead]
    created_at: datetime
    updated_at: datetime

    # Include customer and user details in the response
    customer: Optional[Dict[str, Any]] = None
    user: Optional[Dict[str, Any]] = None

    # Include KDS information if available
    kitchen_order: Optional[Dict[str, Any]] = None

    model_config = ConfigDict(from_attributes=True)
