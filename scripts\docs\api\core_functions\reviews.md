# Core Functions - Reviews

**Categoria:** Core Functions
**Módulo:** Reviews
**Total de Endpoints:** 7
**Gerado em:** 27/06/2025, 20:20:26

## 📋 Endpoints

- [POST /api/modules/core/functions/reviews/](#post-apimodulescorefunctionsreviews) - Create Review
- [GET /api/modules/core/functions/reviews/entity/{entity_type}/{entity_id}](#get-apimodulescorefunctionsreviewsentityentity-typeentity-id) - Get Reviews By Entity
- [GET /api/modules/core/functions/reviews/stats/entity/{entity_type}/{entity_id}](#get-apimodulescorefunctionsreviewsstatsentityentity-typeentity-id) - Get Entity Review Stats
- [GET /api/modules/core/functions/reviews/stats/tenant/{tenant_id}](#get-apimodulescorefunctionsreviewsstatstenanttenant-id) - Get Tenant Review Stats
- [GET /api/modules/core/functions/reviews/tenant/{tenant_id}](#get-apimodulescorefunctionsreviewstenanttenant-id) - Get Reviews By Tenant
- [DELETE /api/modules/core/functions/reviews/{review_id}](#delete-apimodulescorefunctionsreviewsreview-id) - Delete Review
- [GET /api/modules/core/functions/reviews/{review_id}](#get-apimodulescorefunctionsreviewsreview-id) - Get Review By Id

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### ReviewCreate

**Descrição:** Schema para criação de review

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `rating` | integer | ✅ | Rating de 1 a 5 estrelas |
| `comment` | unknown | ❌ | Comentário da review |
| `review_type` | string | ✅ | Tipo da review: 'product', 'service', 'tenant', etc. |
| `entity_type` | unknown | ❌ | Tipo da entidade: 'product', 'menu_item', etc. |
| `entity_id` | unknown | ❌ | ID da entidade específica |
| `tenant_id` | unknown | ❌ | ID do tenant (para reviews gerais) |
| `order_id` | unknown | ❌ | ID do pedido (para compra verificada) |

### ReviewListResponse

**Descrição:** Schema para lista paginada de reviews

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `reviews` | Array[ReviewResponse] | ✅ | - |
| `total` | integer | ✅ | - |
| `page` | integer | ✅ | - |
| `per_page` | integer | ✅ | - |
| `pages` | integer | ✅ | - |
| `has_next` | boolean | ✅ | - |
| `has_prev` | boolean | ✅ | - |

### ReviewResponse

**Descrição:** Schema de resposta para review

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `rating` | integer | ✅ | Rating de 1 a 5 estrelas |
| `comment` | unknown | ❌ | Comentário da review |
| `review_type` | string | ✅ | Tipo da review: 'product', 'service', 'tenant', etc. |
| `id` | string | ✅ | - |
| `user_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `entity_type` | unknown | ❌ | - |
| `entity_id` | unknown | ❌ | - |
| `tenant_id` | unknown | ❌ | - |
| `status` | unknown | ❌ | - |
| `verified_purchase` | unknown | ❌ | - |
| `helpful_count` | unknown | ❌ | - |
| `not_helpful_count` | unknown | ❌ | - |
| `user_name` | unknown | ❌ | - |
| `user_rank` | unknown | ❌ | - |
| `moderated_by` | unknown | ❌ | - |
| `moderated_at` | unknown | ❌ | - |
| `moderation_reason` | unknown | ❌ | - |

### ReviewStatsResponse

**Descrição:** Schema para estatísticas de reviews genéricas

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `entity_type` | unknown | ❌ | - |
| `entity_id` | unknown | ❌ | - |
| `tenant_id` | unknown | ❌ | - |
| `total_reviews` | integer | ✅ | - |
| `average_rating` | string | ✅ | - |
| `rating_distribution` | object | ❌ | Distribuição de ratings {1: count, 2: count, ...} |
| `verified_purchases_count` | unknown | ❌ | - |
| `verified_purchase_percentage` | unknown | ❌ | - |
| `total_helpful_votes` | unknown | ❌ | - |
| `total_not_helpful_votes` | unknown | ❌ | - |
| `helpfulness_ratio` | unknown | ❌ | - |
| `hidden_reviews_count` | unknown | ❌ | - |
| `reported_reviews_count` | unknown | ❌ | - |
| `quality_score` | unknown | ❌ | Score de qualidade das reviews (0-100) |

## 🔗 Endpoints Detalhados

### POST /api/modules/core/functions/reviews/ {#post-apimodulescorefunctionsreviews}

**Resumo:** Create Review
**Descrição:** Criar uma nova review

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ReviewCreate](#reviewcreate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ReviewResponse](#reviewresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/functions/reviews/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/functions/reviews/entity/{entity_type}/{entity_id} {#get-apimodulescorefunctionsreviewsentityentity-typeentity-id}

**Resumo:** Get Reviews By Entity
**Descrição:** Buscar reviews de uma entidade específica

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `entity_type` | string | path | ✅ | - |
| `entity_id` | string | path | ✅ | - |
| `page` | integer | query | ❌ | - |
| `page_size` | integer | query | ❌ | - |
| `rating` | string | query | ❌ | - |
| `verified_only` | boolean | query | ❌ | - |
| `sort_by` | string | query | ❌ | - |
| `sort_order` | string | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ReviewListResponse](#reviewlistresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/functions/reviews/entity/{entity_type}/{entity_id}"
```

---

### GET /api/modules/core/functions/reviews/stats/entity/{entity_type}/{entity_id} {#get-apimodulescorefunctionsreviewsstatsentityentity-typeentity-id}

**Resumo:** Get Entity Review Stats
**Descrição:** Obter estatísticas de reviews de uma entidade

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `entity_type` | string | path | ✅ | - |
| `entity_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ReviewStatsResponse](#reviewstatsresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/functions/reviews/stats/entity/{entity_type}/{entity_id}"
```

---

### GET /api/modules/core/functions/reviews/stats/tenant/{tenant_id} {#get-apimodulescorefunctionsreviewsstatstenanttenant-id}

**Resumo:** Get Tenant Review Stats
**Descrição:** Obter estatísticas de reviews de um tenant

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ReviewStatsResponse](#reviewstatsresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/functions/reviews/stats/tenant/{tenant_id}"
```

---

### GET /api/modules/core/functions/reviews/tenant/{tenant_id} {#get-apimodulescorefunctionsreviewstenanttenant-id}

**Resumo:** Get Reviews By Tenant
**Descrição:** Buscar reviews de um tenant

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_id` | string | path | ✅ | - |
| `page` | integer | query | ❌ | - |
| `page_size` | integer | query | ❌ | - |
| `rating` | string | query | ❌ | - |
| `verified_only` | boolean | query | ❌ | - |
| `sort_by` | string | query | ❌ | - |
| `sort_order` | string | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ReviewListResponse](#reviewlistresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/functions/reviews/tenant/{tenant_id}"
```

---

### DELETE /api/modules/core/functions/reviews/{review_id} {#delete-apimodulescorefunctionsreviewsreview-id}

**Resumo:** Delete Review
**Descrição:** Deletar review (apenas pelo próprio usuário)

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `review_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/core/functions/reviews/{review_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/functions/reviews/{review_id} {#get-apimodulescorefunctionsreviewsreview-id}

**Resumo:** Get Review By Id
**Descrição:** Buscar review por ID

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `review_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ReviewResponse](#reviewresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/functions/reviews/{review_id}"
```

---
