"""Models for CRM module."""

from .account import Account, AccountType, AccountStatus  # noqa: E402
from .contact import Contact, ContactType, ContactStatus
from .interaction import Interaction, InteractionType, InteractionChannel
from .loyalty import (
    LoyaltyProgram,
    LoyaltyProgramType,
    LoyaltyMembership,
    LoyaltyMembershipStatus,
    LoyaltyTransaction,
    LoyaltyTransactionType,
)
from .pricing import (  # noqa: E402
    PricingTier,
    CustomerPricingAssignment,
    PricingRule,
    PricingRuleType,
)

__all__ = [
    # Account
    "Account",
    "AccountType",
    "AccountStatus",
    # Contact
    "Contact",
    "ContactType",
    "ContactStatus",
    # Interaction
    "Interaction",
    "InteractionType",
    "InteractionChannel",
    # Loyalty
    "LoyaltyProgram",
    "LoyaltyProgramType",
    "LoyaltyMembership",
    "LoyaltyMembershipStatus",
    "LoyaltyTransaction",
    "LoyaltyTransactionType",
    # Pricing
    "PricingTier",
    "CustomerPricingAssignment",
    "PricingRule",
    "PricingRuleType",
]
