# Supplier - Tenant

**Categoria:** Supplier
**Módulo:** Tenant
**Total de Endpoints:** 7
**Gerado em:** 27/06/2025, 20:20:27

## 📋 Endpoints

- [GET /api/modules/supplier/purchase-orders](#get-apimodulessupplierpurchase-orders) - Listar Purchase Orders para Supplier
- [PUT /api/modules/supplier/purchase-orders/{order_id}](#put-apimodulessupplierpurchase-ordersorder-id) - Atualizar Purchase Order
- [GET /api/modules/supplier/purchase-orders/{order_id}/items](#get-apimodulessupplierpurchase-ordersorder-iditems) - Listar Itens do Purchase Order
- [PUT /api/modules/supplier/purchase-orders/{order_id}/items/{item_id}](#put-apimodulessupplierpurchase-ordersorder-iditemsitem-id) - Atualizar Item do Purchase Order
- [GET /api/modules/supplier/shopping-lists](#get-apimodulessuppliershopping-lists) - Listar Shopping Lists para Supplier
- [GET /api/modules/supplier/shopping-lists/{list_id}/items](#get-apimodulessuppliershopping-listslist-iditems) - Listar Itens da Shopping List para Supplier
- [GET /api/modules/supplier/tenants](#get-apimodulessuppliertenants) - Listar Tenants do Supplier

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/modules/supplier/purchase-orders {#get-apimodulessupplierpurchase-orders}

**Resumo:** Listar Purchase Orders para Supplier
**Descrição:** Lista purchase orders do supplier baseado no tenant selecionado.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_id` | string | query | ❌ | ID do tenant específico (opcional) |
| `status` | string | query | ❌ | Status do pedido (opcional) |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/supplier/purchase-orders" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/supplier/purchase-orders/{order_id} {#put-apimodulessupplierpurchase-ordersorder-id}

**Resumo:** Atualizar Purchase Order
**Descrição:** Permite ao supplier atualizar status, datas e informações de entrega.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `order_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** {'type': 'object', 'additionalProperties': True, 'title': 'Order Data'}

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/supplier/purchase-orders/{order_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/supplier/purchase-orders/{order_id}/items {#get-apimodulessupplierpurchase-ordersorder-iditems}

**Resumo:** Listar Itens do Purchase Order
**Descrição:** Lista itens de um purchase order específico se o supplier tem acesso.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `order_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/supplier/purchase-orders/{order_id}/items" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/supplier/purchase-orders/{order_id}/items/{item_id} {#put-apimodulessupplierpurchase-ordersorder-iditemsitem-id}

**Resumo:** Atualizar Item do Purchase Order
**Descrição:** Permite ao supplier atualizar status, quantidade e notas de um item.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `order_id` | string | path | ✅ | - |
| `item_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** {'type': 'object', 'additionalProperties': True, 'title': 'Item Data'}

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/supplier/purchase-orders/{order_id}/items/{item_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/supplier/shopping-lists {#get-apimodulessuppliershopping-lists}

**Resumo:** Listar Shopping Lists para Supplier
**Descrição:** Lista shopping lists baseado no tenant selecionado ou todos os tenants.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_id` | string | query | ❌ | ID do tenant específico (opcional) |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/supplier/shopping-lists" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/supplier/shopping-lists/{list_id}/items {#get-apimodulessuppliershopping-listslist-iditems}

**Resumo:** Listar Itens da Shopping List para Supplier
**Descrição:** Lista itens de uma shopping list específica se o supplier tem acesso.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `list_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/supplier/shopping-lists/{list_id}/items" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/supplier/tenants {#get-apimodulessuppliertenants}

**Resumo:** Listar Tenants do Supplier
**Descrição:** Lista todos os tenants onde o usuário atual é supplier.

**🔐 Autenticação:** Requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/supplier/tenants" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
