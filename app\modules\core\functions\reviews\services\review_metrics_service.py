"""
Review Metrics Service

Serviço para cálculo e gerenciamento de métricas de reviews.
"""

from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Tuple
from uuid import UUID

from sqlalchemy import and_, desc, func, select, text
from sqlalchemy.ext.asyncio import AsyncSession

from app.modules.core.functions.reviews.models import (
    Review,
    ReviewStatus,
    ProductReviewMetrics,
    ReviewerMetrics,
    ReviewHelpfulness,
    ReviewReport,
    ReviewerRank
)
from app.modules.core.functions.reviews.schemas import (
    ProductReviewMetricsResponse,
    ReviewerMetricsResponse,
    ReviewDistributionResponse,
    ReviewTrendsResponse,
    ReviewComparisonResponse,
    ReviewAnalyticsResponse
)


class ReviewMetricsService:
    """Serviço para métricas de reviews"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def update_product_metrics(
        self,
        product_id: UUID,
        tenant_id: UUID
    ) -> ProductReviewMetricsResponse:
        """Atualizar métricas de um produto"""
        
        # Buscar ou criar registro de métricas
        metrics_query = select(ProductReviewMetrics).where(
            and_(
                ProductReviewMetrics.product_id == product_id,
                ProductReviewMetrics.tenant_id == tenant_id
            )
        )
        
        metrics = await self.db.scalar(metrics_query)
        
        if not metrics:
            metrics = ProductReviewMetrics(
                product_id=product_id,
                tenant_id=tenant_id
            )
            self.db.add(metrics)
        
        # Calcular métricas
        await self._calculate_product_metrics(metrics, product_id, tenant_id)
        
        await self.db.commit()
        await self.db.refresh(metrics)
        
        return ProductReviewMetricsResponse.from_orm(metrics)
    
    async def update_reviewer_metrics(
        self,
        user_id: UUID,
        tenant_id: UUID
    ) -> ReviewerMetricsResponse:
        """Atualizar métricas de um reviewer"""
        
        # Buscar ou criar registro de métricas
        metrics_query = select(ReviewerMetrics).where(
            and_(
                ReviewerMetrics.user_id == user_id,
                ReviewerMetrics.tenant_id == tenant_id
            )
        )
        
        metrics = await self.db.scalar(metrics_query)
        
        if not metrics:
            metrics = ReviewerMetrics(
                user_id=user_id,
                tenant_id=tenant_id
            )
            self.db.add(metrics)
        
        # Calcular métricas
        await self._calculate_reviewer_metrics(metrics, user_id, tenant_id)
        
        await self.db.commit()
        await self.db.refresh(metrics)
        
        return ReviewerMetricsResponse.from_orm(metrics)
    
    async def get_product_metrics(
        self,
        product_id: UUID,
        tenant_id: UUID
    ) -> Optional[ProductReviewMetricsResponse]:
        """Obter métricas de um produto"""
        
        metrics_query = select(ProductReviewMetrics).where(
            and_(
                ProductReviewMetrics.product_id == product_id,
                ProductReviewMetrics.tenant_id == tenant_id
            )
        )
        
        metrics = await self.db.scalar(metrics_query)
        
        if not metrics:
            return None
        
        return ProductReviewMetricsResponse.from_orm(metrics)
    
    async def get_reviewer_metrics(
        self,
        user_id: UUID,
        tenant_id: UUID
    ) -> Optional[ReviewerMetricsResponse]:
        """Obter métricas de um reviewer"""
        
        metrics_query = select(ReviewerMetrics).where(
            and_(
                ReviewerMetrics.user_id == user_id,
                ReviewerMetrics.tenant_id == tenant_id
            )
        )
        
        metrics = await self.db.scalar(metrics_query)
        
        if not metrics:
            return None
        
        return ReviewerMetricsResponse.from_orm(metrics)
    
    async def get_review_distribution(
        self,
        product_id: UUID,
        tenant_id: UUID
    ) -> ReviewDistributionResponse:
        """Obter distribuição de reviews de um produto"""
        
        # Query base para reviews ativas
        base_query = select(Review).where(
            and_(
                Review.product_id == product_id,
                Review.tenant_id == tenant_id,
                Review.status == ReviewStatus.ACTIVE
            )
        )
        
        # Total de reviews
        total_reviews = await self.db.scalar(
            select(func.count()).select_from(base_query.subquery())
        )
        
        # Média de rating
        avg_rating = await self.db.scalar(
            select(func.avg(Review.rating)).where(
                and_(
                    Review.product_id == product_id,
                    Review.tenant_id == tenant_id,
                    Review.status == ReviewStatus.ACTIVE
                )
            )
        )
        
        # Distribuição por rating
        rating_distribution = {}
        for rating in range(1, 6):
            count = await self.db.scalar(
                select(func.count()).where(
                    and_(
                        Review.product_id == product_id,
                        Review.tenant_id == tenant_id,
                        Review.status == ReviewStatus.ACTIVE,
                        Review.rating == rating
                    )
                )
            )
            rating_distribution[rating] = count or 0
        
        # Reviews por período
        now = datetime.utcnow()
        
        reviews_last_30_days = await self.db.scalar(
            select(func.count()).where(
                and_(
                    Review.product_id == product_id,
                    Review.tenant_id == tenant_id,
                    Review.status == ReviewStatus.ACTIVE,
                    Review.created_at >= now - timedelta(days=30)
                )
            )
        )
        
        reviews_last_90_days = await self.db.scalar(
            select(func.count()).where(
                and_(
                    Review.product_id == product_id,
                    Review.tenant_id == tenant_id,
                    Review.status == ReviewStatus.ACTIVE,
                    Review.created_at >= now - timedelta(days=90)
                )
            )
        )
        
        reviews_last_year = await self.db.scalar(
            select(func.count()).where(
                and_(
                    Review.product_id == product_id,
                    Review.tenant_id == tenant_id,
                    Review.status == ReviewStatus.ACTIVE,
                    Review.created_at >= now - timedelta(days=365)
                )
            )
        )
        
        # Compras verificadas vs não verificadas
        verified_purchases = await self.db.scalar(
            select(func.count()).where(
                and_(
                    Review.product_id == product_id,
                    Review.tenant_id == tenant_id,
                    Review.status == ReviewStatus.ACTIVE,
                    Review.is_verified_purchase == True
                )
            )
        )
        
        unverified_purchases = (total_reviews or 0) - (verified_purchases or 0)
        
        return ReviewDistributionResponse(
            product_id=product_id,
            rating_distribution=rating_distribution,
            total_reviews=total_reviews or 0,
            average_rating=avg_rating or Decimal('0.0'),
            reviews_last_30_days=reviews_last_30_days or 0,
            reviews_last_90_days=reviews_last_90_days or 0,
            reviews_last_year=reviews_last_year or 0,
            verified_purchases=verified_purchases or 0,
            unverified_purchases=unverified_purchases
        )
    
    async def get_review_trends(
        self,
        product_id: UUID,
        tenant_id: UUID,
        months: int = 12
    ) -> ReviewTrendsResponse:
        """Obter tendências de reviews ao longo do tempo"""
        
        # Dados mensais dos últimos N meses
        monthly_data = {}
        now = datetime.utcnow()
        
        for i in range(months):
            month_start = now.replace(day=1) - timedelta(days=i * 30)
            month_end = month_start + timedelta(days=30)
            month_key = month_start.strftime("%Y-%m")
            
            # Count e média para o mês
            month_count = await self.db.scalar(
                select(func.count()).where(
                    and_(
                        Review.product_id == product_id,
                        Review.tenant_id == tenant_id,
                        Review.status == ReviewStatus.ACTIVE,
                        Review.created_at >= month_start,
                        Review.created_at < month_end
                    )
                )
            )
            
            month_avg = await self.db.scalar(
                select(func.avg(Review.rating)).where(
                    and_(
                        Review.product_id == product_id,
                        Review.tenant_id == tenant_id,
                        Review.status == ReviewStatus.ACTIVE,
                        Review.created_at >= month_start,
                        Review.created_at < month_end
                    )
                )
            )
            
            monthly_data[month_key] = {
                "count": month_count or 0,
                "avg_rating": float(month_avg or 0),
                "verified_count": 0  # TODO: Implementar se necessário
            }
        
        # Calcular tendências (últimos 3 meses vs 3 meses anteriores)
        recent_months = list(monthly_data.keys())[:3]
        previous_months = list(monthly_data.keys())[3:6]
        
        recent_avg_rating = sum(
            monthly_data[month]["avg_rating"] for month in recent_months
        ) / len(recent_months) if recent_months else 0
        
        previous_avg_rating = sum(
            monthly_data[month]["avg_rating"] for month in previous_months
        ) / len(previous_months) if previous_months else 0
        
        recent_volume = sum(
            monthly_data[month]["count"] for month in recent_months
        )
        
        previous_volume = sum(
            monthly_data[month]["count"] for month in previous_months
        )
        
        rating_trend = recent_avg_rating - previous_avg_rating
        volume_trend = (
            (recent_volume - previous_volume) / previous_volume * 100
            if previous_volume > 0 else 0
        )
        
        # Determinar direção da tendência
        if rating_trend > 0.1:
            trend_direction = "improving"
        elif rating_trend < -0.1:
            trend_direction = "declining"
        else:
            trend_direction = "stable"
        
        return ReviewTrendsResponse(
            product_id=product_id,
            monthly_data=monthly_data,
            trend_direction=trend_direction,
            rating_trend=rating_trend,
            volume_trend=volume_trend
        )
    
    async def compare_products(
        self,
        product_ids: List[UUID],
        tenant_id: UUID
    ) -> ReviewComparisonResponse:
        """Comparar métricas entre produtos"""
        
        products_data = {}
        comparison_metrics = {}
        
        for product_id in product_ids:
            # Obter métricas básicas
            distribution = await self.get_review_distribution(product_id, tenant_id)
            
            products_data[str(product_id)] = {
                "total_reviews": distribution.total_reviews,
                "average_rating": float(distribution.average_rating),
                "verification_percentage": distribution.verification_percentage,
                "rating_distribution": distribution.rating_distribution
            }
        
        # Calcular métricas comparativas
        if products_data:
            avg_ratings = [data["average_rating"] for data in products_data.values()]
            total_reviews = [data["total_reviews"] for data in products_data.values()]
            
            comparison_metrics = {
                "highest_rating": max(avg_ratings) if avg_ratings else 0,
                "lowest_rating": min(avg_ratings) if avg_ratings else 0,
                "avg_rating_across_products": sum(avg_ratings) / len(avg_ratings) if avg_ratings else 0,
                "most_reviewed_count": max(total_reviews) if total_reviews else 0,
                "least_reviewed_count": min(total_reviews) if total_reviews else 0
            }
        
        return ReviewComparisonResponse(
            products=products_data,
            comparison_metrics=comparison_metrics
        )
    
    async def get_analytics(
        self,
        product_id: UUID,
        tenant_id: UUID
    ) -> ReviewAnalyticsResponse:
        """Obter analytics avançados de reviews"""
        
        # Métricas básicas
        distribution = await self.get_review_distribution(product_id, tenant_id)
        
        # Indicadores de qualidade
        quality_indicators = await self._calculate_quality_indicators(
            product_id, tenant_id
        )
        
        # Recomendações baseadas nas métricas
        recommendations = await self._generate_recommendations(
            product_id, tenant_id, quality_indicators
        )
        
        return ReviewAnalyticsResponse(
            product_id=product_id,
            total_reviews=distribution.total_reviews,
            average_rating=distribution.average_rating,
            sentiment_distribution=None,  # TODO: Implementar análise de sentimento
            top_keywords=None,  # TODO: Implementar extração de palavras-chave
            quality_indicators=quality_indicators,
            recommendations=recommendations
        )
    
    # Métodos privados auxiliares
    
    async def _calculate_product_metrics(
        self,
        metrics: ProductReviewMetrics,
        product_id: UUID,
        tenant_id: UUID
    ):
        """Calcular métricas do produto"""
        
        # Total de reviews ativas
        total_reviews = await self.db.scalar(
            select(func.count()).where(
                and_(
                    Review.product_id == product_id,
                    Review.tenant_id == tenant_id,
                    Review.status == ReviewStatus.ACTIVE
                )
            )
        )
        
        # Média de rating
        avg_rating = await self.db.scalar(
            select(func.avg(Review.rating)).where(
                and_(
                    Review.product_id == product_id,
                    Review.tenant_id == tenant_id,
                    Review.status == ReviewStatus.ACTIVE
                )
            )
        )
        
        # Distribuição por rating
        for rating in range(1, 6):
            count = await self.db.scalar(
                select(func.count()).where(
                    and_(
                        Review.product_id == product_id,
                        Review.tenant_id == tenant_id,
                        Review.status == ReviewStatus.ACTIVE,
                        Review.rating == rating
                    )
                )
            )
            setattr(metrics, f"rating_{rating}_count", count or 0)
        
        # Compras verificadas
        verified_count = await self.db.scalar(
            select(func.count()).where(
                and_(
                    Review.product_id == product_id,
                    Review.tenant_id == tenant_id,
                    Review.status == ReviewStatus.ACTIVE,
                    Review.is_verified_purchase == True
                )
            )
        )
        
        # Votos de utilidade
        helpful_votes = await self.db.scalar(
            select(func.count()).select_from(
                select(ReviewHelpfulness).join(Review).where(
                    and_(
                        Review.product_id == product_id,
                        Review.tenant_id == tenant_id,
                        ReviewHelpfulness.is_helpful == True
                    )
                ).subquery()
            )
        )
        
        not_helpful_votes = await self.db.scalar(
            select(func.count()).select_from(
                select(ReviewHelpfulness).join(Review).where(
                    and_(
                        Review.product_id == product_id,
                        Review.tenant_id == tenant_id,
                        ReviewHelpfulness.is_helpful == False
                    )
                ).subquery()
            )
        )
        
        # Reviews ocultas e reportadas
        hidden_count = await self.db.scalar(
            select(func.count()).where(
                and_(
                    Review.product_id == product_id,
                    Review.tenant_id == tenant_id,
                    Review.status.in_([ReviewStatus.HIDDEN_TEXT, ReviewStatus.HIDDEN_COMPLETE])
                )
            )
        )
        
        reported_count = await self.db.scalar(
            select(func.count()).select_from(
                select(ReviewReport).join(Review).where(
                    and_(
                        Review.product_id == product_id,
                        Review.tenant_id == tenant_id
                    )
                ).subquery()
            )
        )
        
        # Atualizar métricas
        metrics.total_reviews = total_reviews or 0
        metrics.average_rating = avg_rating or Decimal('0.0')
        metrics.verified_purchases_count = verified_count or 0
        metrics.total_helpful_votes = helpful_votes or 0
        metrics.total_not_helpful_votes = not_helpful_votes or 0
        metrics.hidden_reviews_count = hidden_count or 0
        metrics.reported_reviews_count = reported_count or 0
        metrics.last_updated = datetime.utcnow()
    
    async def _calculate_reviewer_metrics(
        self,
        metrics: ReviewerMetrics,
        user_id: UUID,
        tenant_id: UUID
    ):
        """Calcular métricas do reviewer"""
        
        # Total de reviews
        total_reviews = await self.db.scalar(
            select(func.count()).where(
                and_(
                    Review.user_id == user_id,
                    Review.tenant_id == tenant_id
                )
            )
        )
        
        # Reviews verificadas
        verified_reviews = await self.db.scalar(
            select(func.count()).where(
                and_(
                    Review.user_id == user_id,
                    Review.tenant_id == tenant_id,
                    Review.is_verified_purchase == True
                )
            )
        )
        
        # Média de rating dado
        avg_rating_given = await self.db.scalar(
            select(func.avg(Review.rating)).where(
                and_(
                    Review.user_id == user_id,
                    Review.tenant_id == tenant_id
                )
            )
        )
        
        # Votos recebidos
        helpful_votes_received = await self.db.scalar(
            select(func.count()).select_from(
                select(ReviewHelpfulness).join(Review).where(
                    and_(
                        Review.user_id == user_id,
                        Review.tenant_id == tenant_id,
                        ReviewHelpfulness.is_helpful == True
                    )
                ).subquery()
            )
        )
        
        not_helpful_votes_received = await self.db.scalar(
            select(func.count()).select_from(
                select(ReviewHelpfulness).join(Review).where(
                    and_(
                        Review.user_id == user_id,
                        Review.tenant_id == tenant_id,
                        ReviewHelpfulness.is_helpful == False
                    )
                ).subquery()
            )
        )
        
        # Reviews reportadas e ocultas
        reviews_reported = await self.db.scalar(
            select(func.count()).select_from(
                select(ReviewReport).join(Review).where(
                    and_(
                        Review.user_id == user_id,
                        Review.tenant_id == tenant_id
                    )
                ).subquery()
            )
        )
        
        reviews_hidden = await self.db.scalar(
            select(func.count()).where(
                and_(
                    Review.user_id == user_id,
                    Review.tenant_id == tenant_id,
                    Review.status.in_([ReviewStatus.HIDDEN_TEXT, ReviewStatus.HIDDEN_COMPLETE])
                )
            )
        )
        
        # Datas da primeira e última review
        first_review_date = await self.db.scalar(
            select(func.min(Review.created_at)).where(
                and_(
                    Review.user_id == user_id,
                    Review.tenant_id == tenant_id
                )
            )
        )
        
        last_review_date = await self.db.scalar(
            select(func.max(Review.created_at)).where(
                and_(
                    Review.user_id == user_id,
                    Review.tenant_id == tenant_id
                )
            )
        )
        
        # Calcular rank do reviewer
        reviewer_rank = self._calculate_reviewer_rank(
            total_reviews or 0,
            verified_reviews or 0,
            helpful_votes_received or 0,
            reviews_hidden or 0
        )
        
        # Atualizar métricas
        metrics.total_reviews = total_reviews or 0
        metrics.verified_reviews = verified_reviews or 0
        metrics.average_rating_given = avg_rating_given or Decimal('0.0')
        metrics.helpful_votes_received = helpful_votes_received or 0
        metrics.not_helpful_votes_received = not_helpful_votes_received or 0
        metrics.reviews_reported = reviews_reported or 0
        metrics.reviews_hidden = reviews_hidden or 0
        metrics.reviewer_rank = reviewer_rank
        metrics.first_review_date = first_review_date
        metrics.last_review_date = last_review_date
        metrics.last_updated = datetime.utcnow()
    
    def _calculate_reviewer_rank(
        self,
        total_reviews: int,
        verified_reviews: int,
        helpful_votes: int,
        hidden_reviews: int
    ) -> ReviewerRank:
        """Calcular rank do reviewer baseado nas métricas"""
        
        # Calcular score baseado em múltiplos fatores
        activity_score = min(total_reviews / 10, 1.0)  # Max 1.0 com 10+ reviews
        verification_score = (verified_reviews / total_reviews) if total_reviews > 0 else 0
        helpfulness_score = min(helpful_votes / 20, 1.0)  # Max 1.0 com 20+ votos úteis
        quality_score = 1 - (hidden_reviews / max(total_reviews, 1))
        
        overall_score = (
            activity_score * 0.3 +
            verification_score * 0.3 +
            helpfulness_score * 0.3 +
            quality_score * 0.1
        )
        
        # Determinar rank baseado no score
        if overall_score >= 0.8 and total_reviews >= 20:
            return ReviewerRank.TOP
        elif overall_score >= 0.6 and total_reviews >= 10:
            return ReviewerRank.EXPERT
        elif overall_score >= 0.4 and total_reviews >= 5:
            return ReviewerRank.REGULAR
        else:
            return ReviewerRank.NOVICE
    
    async def _calculate_quality_indicators(
        self,
        product_id: UUID,
        tenant_id: UUID
    ) -> Dict[str, float]:
        """Calcular indicadores de qualidade das reviews"""
        
        distribution = await self.get_review_distribution(product_id, tenant_id)
        
        return {
            "verification_rate": distribution.verification_percentage / 100,
            "engagement_rate": 0.0,  # TODO: Implementar baseado em votos
            "report_rate": 0.0,  # TODO: Implementar baseado em reports
            "response_rate": 0.0,  # TODO: Implementar se houver respostas
            "recency_score": 0.0  # TODO: Implementar baseado em reviews recentes
        }
    
    async def _generate_recommendations(
        self,
        product_id: UUID,
        tenant_id: UUID,
        quality_indicators: Dict[str, float]
    ) -> Dict[str, str]:
        """Gerar recomendações baseadas nas métricas"""
        
        recommendations = {}
        
        if quality_indicators["verification_rate"] < 0.3:
            recommendations["verification"] = "Encourage verified purchases to improve review credibility"
        
        # TODO: Adicionar mais recomendações baseadas em outros indicadores
        
        return recommendations