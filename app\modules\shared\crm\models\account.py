"""Account models for CRM module."""

import uuid
import enum
from typing import TYPE_CHECKING, List, Optional
from sqlalchemy import Column, String, ForeignKey, Text, Enum as SQLAlchemyEnum, Integer, Float, DateTime
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, Mapped, mapped_column

from app.db.base import Base
from app.modules.core.tenants.models.tenant import Tenant
from app.modules.core.functions.orders.models.order import Order

if TYPE_CHECKING:
    from app.modules.core.users.models.user import User
    from app.modules.tenants.restaurants.table_reservation.models.reservation import (
        Reservation,
        CustomerBlacklist,
    )

class AccountType(str, enum.Enum):
    INDIVIDUAL = "individual"
    BUSINESS = "business"
    GOVERNMENT = "government"
    NON_PROFIT = "non_profit"
    OTHER = "other"

class AccountStatus(str, enum.Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    LEAD = "lead"
    PROSPECT = "prospect"
    CUSTOMER = "customer"
    FORMER_CUSTOMER = "former_customer"

class Account(Base):
    __tablename__ = "crm_accounts"

    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    user_id: Mapped[Optional[uuid.UUID]] = mapped_column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    
    name: Mapped[str] = mapped_column(String, nullable=False, index=True)
    account_type: Mapped[AccountType] = mapped_column(SQLAlchemyEnum(AccountType), nullable=False, default=AccountType.INDIVIDUAL)
    status: Mapped[AccountStatus] = mapped_column(SQLAlchemyEnum(AccountStatus), nullable=False, default=AccountStatus.LEAD)
    
    email: Mapped[Optional[str]] = mapped_column(String)
    phone: Mapped[Optional[str]] = mapped_column(String)
    website: Mapped[Optional[str]] = mapped_column(String)
    
    address_line1: Mapped[Optional[str]] = mapped_column(String)
    address_line2: Mapped[Optional[str]] = mapped_column(String)
    city: Mapped[Optional[str]] = mapped_column(String)
    state: Mapped[Optional[str]] = mapped_column(String)
    postal_code: Mapped[Optional[str]] = mapped_column(String)
    country: Mapped[Optional[str]] = mapped_column(String)
    
    tax_id: Mapped[Optional[str]] = mapped_column(String)
    industry: Mapped[Optional[str]] = mapped_column(String)
    annual_revenue: Mapped[Optional[float]] = mapped_column(Float)
    number_of_employees: Mapped[Optional[int]] = mapped_column(Integer)

    acquisition_date: Mapped[Optional[DateTime]] = mapped_column(DateTime)
    last_contact_date: Mapped[Optional[DateTime]] = mapped_column(DateTime)
    
    description: Mapped[Optional[str]] = mapped_column(Text)
    notes: Mapped[Optional[str]] = mapped_column(Text)

    # Relationships
    user: Mapped[Optional["User"]] = relationship(back_populates="accounts")
    tenant: Mapped["Tenant"] = relationship(back_populates="crm_accounts")
    
    contacts: Mapped[List["Contact"]] = relationship("Contact", back_populates="account", cascade="all, delete-orphan")
    interactions: Mapped[List["Interaction"]] = relationship("Interaction", back_populates="account", cascade="all, delete-orphan")
    loyalty_memberships: Mapped[List["LoyaltyMembership"]] = relationship("LoyaltyMembership", back_populates="account", cascade="all, delete-orphan")
    pricing_assignments: Mapped[List["CustomerPricingAssignment"]] = relationship("CustomerPricingAssignment", back_populates="account", cascade="all, delete-orphan")
    
    orders: Mapped[List["Order"]] = relationship(
        "Order", foreign_keys="Order.customer_id", cascade="all, delete-orphan"
    )
    
    reservations: Mapped[List["Reservation"]] = relationship(back_populates="customer", cascade="all, delete-orphan")
    blacklist_entries: Mapped[List["CustomerBlacklist"]] = relationship(back_populates="customer", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Account(id={self.id}, name='{self.name}', type='{self.account_type.value}', status='{self.status.value}')>"

# Forward references for relationships
if TYPE_CHECKING:
    from app.modules.shared.crm.models.contact import Contact
    from app.modules.shared.crm.models.interaction import Interaction
    from app.modules.shared.crm.models.loyalty import LoyaltyMembership
    from app.modules.shared.crm.models.pricing import CustomerPricingAssignment
