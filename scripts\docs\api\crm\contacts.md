# Crm - Contacts

**Categoria:** Crm
**Mó<PERSON><PERSON>:** Contacts
**Total de Endpoints:** 6
**Gerado em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [GET /api/modules/crm/crm/contacts/](#get-apimodulescrmcrmcontacts) - Get Contacts
- [POST /api/modules/crm/crm/contacts/](#post-apimodulescrmcrmcontacts) - Create Contact
- [GET /api/modules/crm/crm/contacts/account/{account_id}](#get-apimodulescrmcrmcontactsaccountaccount-id) - Get Account Contacts
- [DELETE /api/modules/crm/crm/contacts/{contact_id}](#delete-apimodulescrmcrmcontactscontact-id) - Delete Contact
- [GET /api/modules/crm/crm/contacts/{contact_id}](#get-apimodulescrmcrmcontactscontact-id) - Get Contact
- [PUT /api/modules/crm/crm/contacts/{contact_id}](#put-apimodulescrmcrmcontactscontact-id) - Update Contact

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### ContactCreate

**Descrição:** Schema for creating a new Contact.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `first_name` | string | ✅ | - |
| `last_name` | string | ✅ | - |
| `contact_type` | ContactType | ❌ | - |
| `status` | ContactStatus | ❌ | - |
| `email` | unknown | ❌ | - |
| `phone` | unknown | ❌ | - |
| `mobile` | unknown | ❌ | - |
| `job_title` | unknown | ❌ | - |
| `department` | unknown | ❌ | - |
| `preferred_contact_method` | unknown | ❌ | - |
| `communication_preferences` | unknown | ❌ | - |
| `notes` | unknown | ❌ | - |
| `account_id` | string | ✅ | - |
| `user_tenant_association_id` | unknown | ❌ | - |
| `full_name` | unknown | ❌ | - |

### ContactRead

**Descrição:** Schema for reading a Contact.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `first_name` | string | ✅ | - |
| `last_name` | string | ✅ | - |
| `contact_type` | ContactType | ❌ | - |
| `status` | ContactStatus | ❌ | - |
| `email` | unknown | ❌ | - |
| `phone` | unknown | ❌ | - |
| `mobile` | unknown | ❌ | - |
| `job_title` | unknown | ❌ | - |
| `department` | unknown | ❌ | - |
| `preferred_contact_method` | unknown | ❌ | - |
| `communication_preferences` | unknown | ❌ | - |
| `notes` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `account_id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `user_tenant_association_id` | unknown | ❌ | - |
| `full_name` | unknown | ❌ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### ContactUpdate

**Descrição:** Schema for updating a Contact.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `first_name` | unknown | ❌ | - |
| `last_name` | unknown | ❌ | - |
| `full_name` | unknown | ❌ | - |
| `contact_type` | unknown | ❌ | - |
| `status` | unknown | ❌ | - |
| `email` | unknown | ❌ | - |
| `phone` | unknown | ❌ | - |
| `mobile` | unknown | ❌ | - |
| `job_title` | unknown | ❌ | - |
| `department` | unknown | ❌ | - |
| `preferred_contact_method` | unknown | ❌ | - |
| `communication_preferences` | unknown | ❌ | - |
| `notes` | unknown | ❌ | - |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/modules/crm/crm/contacts/ {#get-apimodulescrmcrmcontacts}

**Resumo:** Get Contacts
**Descrição:** Get all CRM contacts with optional filtering.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `account_id` | string | query | ❌ | Filter by account ID |
| `skip` | integer | query | ❌ | Number of contacts to skip |
| `limit` | integer | query | ❌ | Maximum number of contacts to return |
| `status` | string | query | ❌ | Filter by contact status |
| `contact_type` | string | query | ❌ | Filter by contact type |
| `search` | string | query | ❌ | Search term for contact name, email, or phone |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/contacts/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/crm/crm/contacts/ {#post-apimodulescrmcrmcontacts}

**Resumo:** Create Contact
**Descrição:** Create a new CRM contact.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ContactCreate](#contactcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ContactRead](#contactread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/crm/crm/contacts/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/crm/crm/contacts/account/{account_id} {#get-apimodulescrmcrmcontactsaccountaccount-id}

**Resumo:** Get Account Contacts
**Descrição:** Get all contacts for a specific account.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `account_id` | string | path | ✅ | The ID of the account to get contacts for |
| `skip` | integer | query | ❌ | Number of contacts to skip |
| `limit` | integer | query | ❌ | Maximum number of contacts to return |
| `status` | string | query | ❌ | Filter by contact status |
| `contact_type` | string | query | ❌ | Filter by contact type |
| `search` | string | query | ❌ | Search term for contact name, email, or phone |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/contacts/account/{account_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### DELETE /api/modules/crm/crm/contacts/{contact_id} {#delete-apimodulescrmcrmcontactscontact-id}

**Resumo:** Delete Contact
**Descrição:** Delete a CRM contact.

Requires tenant owner or manager role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `contact_id` | string | path | ✅ | The ID of the contact to delete |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/crm/crm/contacts/{contact_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/crm/crm/contacts/{contact_id} {#get-apimodulescrmcrmcontactscontact-id}

**Resumo:** Get Contact
**Descrição:** Get a CRM contact by ID.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `contact_id` | string | path | ✅ | The ID of the contact to retrieve |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ContactRead](#contactread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/contacts/{contact_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/crm/crm/contacts/{contact_id} {#put-apimodulescrmcrmcontactscontact-id}

**Resumo:** Update Contact
**Descrição:** Update a CRM contact.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `contact_id` | string | path | ✅ | The ID of the contact to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ContactUpdate](#contactupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ContactRead](#contactread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/crm/crm/contacts/{contact_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
