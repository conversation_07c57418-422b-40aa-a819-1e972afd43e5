"""
Ticket Schemas

Schemas Pydantic para validação de dados de tickets.
"""

from datetime import datetime
from typing import List, Optional
from uuid import UUID
from pydantic import BaseModel, Field
from enum import Enum


class TicketCategoryEnum(str, Enum):
    QUESTION = "question"
    INCIDENT = "incident"
    PROBLEM = "problem"
    REQUEST = "request"
    SUGGESTION = "suggestion"


class TicketStatusEnum(str, Enum):
    NEW = "new"
    OPEN = "open"
    PENDING = "pending"
    RESOLVED = "resolved"
    CLOSED = "closed"


class TicketPriorityEnum(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class TicketCreate(BaseModel):
    title: str = Field(..., min_length=1, max_length=255, description="Título do ticket")
    description: str = Field(..., min_length=1, max_length=5000, description="Descrição do problema")
    category: TicketCategoryEnum = Field(..., description="Categoria do ticket")
    tenant_id: Optional[UUID] = Field(None, description="ID do tenant (opcional)")


class TicketUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, min_length=1, max_length=5000)
    status: Optional[TicketStatusEnum] = None
    priority: Optional[TicketPriorityEnum] = None
    assigned_admin_id: Optional[UUID] = None


class TicketResponse(BaseModel):
    id: UUID
    title: str
    description: str
    category: TicketCategoryEnum
    status: TicketStatusEnum
    priority: TicketPriorityEnum
    user_id: UUID
    tenant_id: Optional[UUID] = None
    assigned_admin_id: Optional[UUID] = None
    is_read_by_admin: bool
    is_read_by_user: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    resolved_at: Optional[datetime] = None
    closed_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    is_expired: bool
    
    # Campos adicionais para resposta
    user_name: Optional[str] = None
    user_email: Optional[str] = None
    assigned_admin_name: Optional[str] = None
    message_count: Optional[int] = 0

    class Config:
        from_attributes = True


class TicketListResponse(BaseModel):
    tickets: List[TicketResponse]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


class TicketFilters(BaseModel):
    status: Optional[TicketStatusEnum] = None
    priority: Optional[TicketPriorityEnum] = None
    category: Optional[TicketCategoryEnum] = None
    search: Optional[str] = None
    assigned_admin_id: Optional[UUID] = None
    user_id: Optional[UUID] = None
    tenant_id: Optional[UUID] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None


class TicketMarkAsRead(BaseModel):
    ticket_ids: List[UUID] = Field(..., min_items=1, description="Lista de IDs dos tickets")


class TicketAssignmentRequest(BaseModel):
    ticket_id: UUID = Field(..., description="ID do ticket")
    admin_id: UUID = Field(..., description="ID do admin para atribuição")


class BulkTicketOperation(BaseModel):
    ticket_ids: List[UUID] = Field(..., min_items=1, description="Lista de IDs dos tickets")
    operation: str = Field(..., description="Operação a ser executada")
    value: Optional[str] = Field(None, description="Valor para a operação")


class TicketStatusChange(BaseModel):
    ticket_id: UUID
    old_status: TicketStatusEnum
    new_status: TicketStatusEnum
    changed_by: UUID
    changed_at: datetime


class TicketPriorityChange(BaseModel):
    ticket_id: UUID
    old_priority: TicketPriorityEnum
    new_priority: TicketPriorityEnum
    changed_by: UUID
    changed_at: datetime


class TicketAssignment(BaseModel):
    ticket_id: UUID
    assigned_to: UUID
    assigned_by: UUID
    assigned_at: datetime
