"""
Blog Comment Schemas

Pydantic models for blog comment validation and serialization.
"""

import uuid
from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field, EmailStr, validator, ConfigDict


class BlogCommentBase(BaseModel):
    """Base schema for blog comments."""
    content: str = Field(..., min_length=1)
    guest_name: Optional[str] = Field(None, max_length=100)
    guest_email: Optional[EmailStr] = None
    guest_website: Optional[str] = Field(None, max_length=500)


class BlogCommentCreate(BlogCommentBase):
    """Schema for creating blog comments."""
    post_id: uuid.UUID
    parent_id: Optional[uuid.UUID] = None
    
    @validator('guest_name')
    def validate_guest_info(cls, v, values):
        # If user is not authenticated, guest info is required
        if not values.get('user_id') and not v:
            raise ValueError('Guest name is required for non-authenticated users')
        return v


class BlogCommentUpdate(BaseModel):
    """Schema for updating blog comments."""
    content: Optional[str] = Field(None, min_length=1)
    status: Optional[str] = Field(None, max_length=20)
    is_pinned: Optional[bool] = None
    
    @validator('status')
    def validate_status(cls, v):
        if v is not None:
            allowed_statuses = ['pending', 'approved', 'rejected', 'spam']
            if v not in allowed_statuses:
                raise ValueError(f'Status must be one of: {allowed_statuses}')
        return v


class BlogCommentRead(BlogCommentBase):
    """Schema for reading blog comments."""
    id: uuid.UUID
    post_id: uuid.UUID
    user_id: Optional[uuid.UUID]
    parent_id: Optional[uuid.UUID]
    status: str
    is_pinned: bool
    like_count: int
    reply_count: int
    created_at: datetime
    updated_at: datetime
    
    # User information (if authenticated user)
    user_display_name: Optional[str] = None
    user_avatar_url: Optional[str] = None
    
    model_config = ConfigDict(from_attributes=True)


class BlogCommentList(BaseModel):
    """Schema for blog comment list items (simplified)."""
    id: uuid.UUID
    post_id: uuid.UUID
    parent_id: Optional[uuid.UUID]
    content: str
    status: str
    is_pinned: bool
    like_count: int
    reply_count: int
    created_at: datetime
    
    # Author information
    author_name: str  # Either user display name or guest name
    author_avatar: Optional[str] = None
    is_guest: bool = False
    
    model_config = ConfigDict(from_attributes=True)


class BlogCommentTree(BaseModel):
    """Schema for threaded comment display."""
    id: uuid.UUID
    content: str
    status: str
    is_pinned: bool
    like_count: int
    created_at: datetime
    
    # Author information
    author_name: str
    author_avatar: Optional[str] = None
    is_guest: bool = False
    
    # Nested replies
    replies: List["BlogCommentTree"] = []
    
    model_config = ConfigDict(from_attributes=True)


class BlogCommentModeration(BaseModel):
    """Schema for comment moderation actions."""
    comment_ids: List[uuid.UUID]
    action: str  # approve, reject, spam, delete
    
    @validator('action')
    def validate_action(cls, v):
        allowed_actions = ['approve', 'reject', 'spam', 'delete']
        if v not in allowed_actions:
            raise ValueError(f'Action must be one of: {allowed_actions}')
        return v


class BlogCommentStats(BaseModel):
    """Schema for comment statistics."""
    total_comments: int
    pending_comments: int
    approved_comments: int
    rejected_comments: int
    spam_comments: int
    
    model_config = ConfigDict(from_attributes=True)


# Update forward references
BlogCommentTree.model_rebuild()
