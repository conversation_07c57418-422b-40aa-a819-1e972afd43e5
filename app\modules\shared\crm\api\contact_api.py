"""
API endpoints for CRM Contact management.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from typing import List, Optional, Annotated, TYPE_CHECKING
import uuid

from sqlalchemy.ext.asyncio import AsyncSession
from app.core.db_dependencies import get_db

# Use TYPE_CHECKING to avoid circular import
if TYPE_CHECKING:
    from app.modules.core.users.models.user import User
from app.modules.core.tenants.models.tenant import Tenant

from app.modules.core.auth.dependencies.auth_dependencies import (
    get_current_active_user,
)  # noqa: E402
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    require_tenant_role,
)  # noqa: E402
from app.modules.core.roles.models.roles import TenantRole
from app.modules.shared.crm.models.contact import ContactStatus, ContactType
from app.modules.shared.crm.schemas.contact import (
    ContactCreate,
    ContactUpdate,
    ContactRead,
)
from app.modules.shared.crm.services.contact_service import contact_service  # noqa: E402

router = APIRouter(prefix="/contacts", tags=["CRM - Contacts"])


@router.post(
    "/",
    response_model=ContactRead,
    status_code=status.HTTP_201_CREATED,
    dependencies=[
        Depends(
            require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF], "header")
        )
    ],
)
async def create_contact(
    contact_in: ContactCreate,
    current_user: Annotated["User", Depends(get_current_active_user)],
    db: Annotated[AsyncSession, Depends(get_db)],
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Create a new CRM contact.

    Requires tenant owner, manager, or staff role.
    """
    try:
        db_contact = await contact_service.create_contact(db, tenant_id, contact_in)
        return db_contact
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create contact: {str(e)}",
        )


@router.get(
    "/{contact_id}",
    response_model=ContactRead,
    dependencies=[
        Depends(
            require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF], "header")
        )
    ],
)
async def get_contact(
    contact_id: uuid.UUID = Path(..., description="The ID of the contact to retrieve"),
    db: Annotated[AsyncSession, Depends(get_db)] = None,
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Get a CRM contact by ID.

    Requires tenant owner, manager, or staff role.
    """
    db_contact = await contact_service.get_contact(db, tenant_id, contact_id)
    if not db_contact:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Contact with ID {contact_id} not found",
        )
    return db_contact


@router.get(
    "/",
    response_model=List[ContactRead],
    dependencies=[
        Depends(
            require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF], "header")
        )
    ],
)
async def get_contacts(
    account_id: Optional[uuid.UUID] = Query(None, description="Filter by account ID"),
    skip: int = Query(0, ge=0, description="Number of contacts to skip"),
    limit: int = Query(100, ge=1, le=100, description="Maximum number of contacts to return"),
    status: Optional[ContactStatus] = Query(None, description="Filter by contact status"),
    contact_type: Optional[ContactType] = Query(None, description="Filter by contact type"),
    search: Optional[str] = Query(
        None, description="Search term for contact name, email, or phone"
    ),
    db: Annotated[AsyncSession, Depends(get_db)] = None,
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Get all CRM contacts with optional filtering.

    Requires tenant owner, manager, or staff role.
    """
    db_contacts = await contact_service.get_contacts(
        db, tenant_id, account_id, skip, limit, status, contact_type, search
    )
    return db_contacts


@router.get(
    "/account/{account_id}",
    response_model=List[ContactRead],
    dependencies=[
        Depends(
            require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF], "header")
        )
    ],
)
async def get_account_contacts(
    account_id: uuid.UUID = Path(..., description="The ID of the account to get contacts for"),
    skip: int = Query(0, ge=0, description="Number of contacts to skip"),
    limit: int = Query(100, ge=1, le=100, description="Maximum number of contacts to return"),
    status: Optional[ContactStatus] = Query(None, description="Filter by contact status"),
    contact_type: Optional[ContactType] = Query(None, description="Filter by contact type"),
    search: Optional[str] = Query(
        None, description="Search term for contact name, email, or phone"
    ),
    db: Annotated[AsyncSession, Depends(get_db)] = None,
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Get all contacts for a specific account.

    Requires tenant owner, manager, or staff role.
    """
    db_contacts = await contact_service.get_contacts(
        db, tenant_id, account_id, skip, limit, status, contact_type, search
    )
    return db_contacts


@router.put(
    "/{contact_id}",
    response_model=ContactRead,
    dependencies=[
        Depends(
            require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF], "header")
        )
    ],
)
async def update_contact(
    contact_in: ContactUpdate,
    contact_id: uuid.UUID = Path(..., description="The ID of the contact to update"),
    db: Annotated[AsyncSession, Depends(get_db)] = None,
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Update a CRM contact.

    Requires tenant owner, manager, or staff role.
    """
    db_contact = await contact_service.update_contact(db, tenant_id, contact_id, contact_in)
    if not db_contact:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Contact with ID {contact_id} not found",
        )
    return db_contact


@router.delete(
    "/{contact_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=[Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER], "header"))],
)
async def delete_contact(
    contact_id: uuid.UUID = Path(..., description="The ID of the contact to delete"),
    db: Annotated[AsyncSession, Depends(get_db)] = None,
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Delete a CRM contact.

    Requires tenant owner or manager role.
    """
    success = await contact_service.delete_contact(db, tenant_id, contact_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Contact with ID {contact_id} not found",
        )
    return None
