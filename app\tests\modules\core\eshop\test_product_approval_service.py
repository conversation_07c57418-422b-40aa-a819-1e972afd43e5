"""
Unit tests for ProductApprovalService.

Tests all approval workflow functionality including:
- Product submission and approval
- Status transitions and validation
- Notification integration
- Permission checks
- Audit trail creation
"""

import pytest
import pytest_asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
from uuid import uuid4

from app.modules.core.eshop.services.approval_service import ProductApprovalService
from app.modules.core.eshop.models.product_approval import (
    ProductApprovalHistory, 
    ProductApprovalSettings
)
from app.modules.core.eshop.schemas.approval_schemas import (
    ProductSubmissionRequest,
    ApprovalActionRequest
)


class TestProductApprovalService:
    """Unit tests for ProductApprovalService."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        session = Mock()
        session.add = Mock()
        session.commit = AsyncMock()
        session.refresh = AsyncMock()
        session.execute = AsyncMock()
        session.scalar = AsyncMock()
        return session

    @pytest.fixture
    def mock_notification_service(self):
        """Mock notification service."""
        service = Mock()
        service.send_approval_notification = AsyncMock()
        return service

    @pytest.fixture
    def approval_service(self, mock_db_session, mock_notification_service):
        """Create ProductApprovalService instance with mocks."""
        return ProductApprovalService(
            db_session=mock_db_session,
            notification_service=mock_notification_service
        )

    @pytest.fixture
    def sample_product_data(self):
        """Sample product data for testing."""
        return {
            "id": str(uuid4()),
            "name": "Test Product",
            "description": "Test Description",
            "price": 29.99,
            "market_type": "B2B",
            "vendor_id": str(uuid4()),
            "tenant_id": str(uuid4())
        }

    @pytest.fixture
    def sample_submission_request(self, sample_product_data):
        """Sample product submission request."""
        return ProductSubmissionRequest(
            product_id=sample_product_data["id"],
            vendor_notes="Please approve this product",
            market_type="B2B",
            commission_rate=0.15
        )

    @pytest_asyncio.async_test
    async def test_submit_product_for_approval_success(
        self, 
        approval_service, 
        mock_db_session,
        mock_notification_service,
        sample_submission_request,
        sample_product_data
    ):
        """Test successful product submission for approval."""
        # Arrange
        mock_product = Mock()
        mock_product.id = sample_product_data["id"]
        mock_product.approval_status = "draft"
        mock_db_session.scalar.return_value = mock_product

        # Act
        result = await approval_service.submit_product_for_approval(
            sample_submission_request, 
            str(uuid4())  # admin_user_id
        )

        # Assert
        assert result.status == "pending"
        assert result.product_id == sample_product_data["id"]
        mock_db_session.add.assert_called()
        mock_db_session.commit.assert_called()
        mock_notification_service.send_approval_notification.assert_called()

    @pytest_asyncio.async_test
    async def test_submit_already_pending_product_fails(
        self,
        approval_service,
        mock_db_session,
        sample_submission_request
    ):
        """Test that submitting already pending product fails."""
        # Arrange
        mock_product = Mock()
        mock_product.approval_status = "pending"
        mock_db_session.scalar.return_value = mock_product

        # Act & Assert
        with pytest.raises(ValueError, match="already pending approval"):
            await approval_service.submit_product_for_approval(
                sample_submission_request,
                str(uuid4())
            )

    @pytest_asyncio.async_test
    async def test_approve_product_success(
        self,
        approval_service,
        mock_db_session,
        mock_notification_service
    ):
        """Test successful product approval."""
        # Arrange
        product_id = str(uuid4())
        admin_user_id = str(uuid4())
        
        mock_product = Mock()
        mock_product.id = product_id
        mock_product.approval_status = "pending"
        mock_db_session.scalar.return_value = mock_product

        approval_request = ApprovalActionRequest(
            action="approve",
            admin_notes="Product meets all requirements",
            commission_rate=0.12
        )

        # Act
        result = await approval_service.approve_product(
            product_id, 
            approval_request, 
            admin_user_id
        )

        # Assert
        assert result.status == "approved"
        assert mock_product.approval_status == "approved"
        mock_db_session.add.assert_called()
        mock_db_session.commit.assert_called()
        mock_notification_service.send_approval_notification.assert_called()

    @pytest_asyncio.async_test
    async def test_reject_product_success(
        self,
        approval_service,
        mock_db_session,
        mock_notification_service
    ):
        """Test successful product rejection."""
        # Arrange
        product_id = str(uuid4())
        admin_user_id = str(uuid4())
        
        mock_product = Mock()
        mock_product.id = product_id
        mock_product.approval_status = "pending"
        mock_db_session.scalar.return_value = mock_product

        approval_request = ApprovalActionRequest(
            action="reject",
            admin_notes="Product does not meet quality standards",
            rejection_reason="quality_issues"
        )

        # Act
        result = await approval_service.reject_product(
            product_id,
            approval_request,
            admin_user_id
        )

        # Assert
        assert result.status == "rejected"
        assert mock_product.approval_status == "rejected"
        mock_db_session.add.assert_called()
        mock_db_session.commit.assert_called()
        mock_notification_service.send_approval_notification.assert_called()

    @pytest_asyncio.async_test
    async def test_request_revision_success(
        self,
        approval_service,
        mock_db_session,
        mock_notification_service
    ):
        """Test successful revision request."""
        # Arrange
        product_id = str(uuid4())
        admin_user_id = str(uuid4())
        
        mock_product = Mock()
        mock_product.id = product_id
        mock_product.approval_status = "pending"
        mock_db_session.scalar.return_value = mock_product

        approval_request = ApprovalActionRequest(
            action="request_revision",
            admin_notes="Please update product description",
            revision_requirements="More detailed description needed"
        )

        # Act
        result = await approval_service.request_product_revision(
            product_id,
            approval_request,
            admin_user_id
        )

        # Assert
        assert result.status == "revision_requested"
        assert mock_product.approval_status == "revision_requested"
        mock_db_session.add.assert_called()
        mock_db_session.commit.assert_called()
        mock_notification_service.send_approval_notification.assert_called()

    @pytest_asyncio.async_test
    async def test_get_approval_history_success(
        self,
        approval_service,
        mock_db_session
    ):
        """Test retrieving approval history."""
        # Arrange
        product_id = str(uuid4())
        
        mock_history_items = [
            Mock(
                id=str(uuid4()),
                product_id=product_id,
                status="pending",
                admin_notes="Submitted for approval",
                created_at=datetime.now()
            ),
            Mock(
                id=str(uuid4()),
                product_id=product_id,
                status="approved",
                admin_notes="Approved",
                created_at=datetime.now()
            )
        ]
        
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = mock_history_items
        mock_db_session.execute.return_value = mock_result

        # Act
        history = await approval_service.get_product_approval_history(product_id)

        # Assert
        assert len(history) == 2
        assert history[0].status == "pending"
        assert history[1].status == "approved"

    @pytest_asyncio.async_test
    async def test_get_pending_approvals_with_filters(
        self,
        approval_service,
        mock_db_session
    ):
        """Test retrieving pending approvals with filters."""
        # Arrange
        mock_products = [
            Mock(
                id=str(uuid4()),
                name="Product 1",
                market_type="B2B",
                approval_status="pending"
            ),
            Mock(
                id=str(uuid4()),
                name="Product 2", 
                market_type="B2C",
                approval_status="pending"
            )
        ]
        
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = mock_products
        mock_db_session.execute.return_value = mock_result

        # Act
        pending = await approval_service.get_pending_approvals(
            market_type="B2B",
            limit=10,
            offset=0
        )

        # Assert
        assert len(pending) == 2
        mock_db_session.execute.assert_called()

    @pytest_asyncio.async_test
    async def test_bulk_approve_products_success(
        self,
        approval_service,
        mock_db_session,
        mock_notification_service
    ):
        """Test bulk approval of multiple products."""
        # Arrange
        product_ids = [str(uuid4()), str(uuid4()), str(uuid4())]
        admin_user_id = str(uuid4())
        
        mock_products = []
        for pid in product_ids:
            mock_product = Mock()
            mock_product.id = pid
            mock_product.approval_status = "pending"
            mock_products.append(mock_product)
        
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = mock_products
        mock_db_session.execute.return_value = mock_result

        # Act
        results = await approval_service.bulk_approve_products(
            product_ids,
            "Bulk approval - all products meet standards",
            admin_user_id
        )

        # Assert
        assert len(results) == 3
        for result in results:
            assert result.status == "approved"
        
        # Verify all products were updated
        for mock_product in mock_products:
            assert mock_product.approval_status == "approved"

    @pytest_asyncio.async_test
    async def test_approval_settings_management(
        self,
        approval_service,
        mock_db_session
    ):
        """Test approval settings management."""
        # Arrange
        tenant_id = str(uuid4())
        
        mock_settings = Mock()
        mock_settings.tenant_id = tenant_id
        mock_settings.auto_approve_b2c = True
        mock_settings.require_admin_approval_b2b = True
        mock_db_session.scalar.return_value = mock_settings

        # Act
        settings = await approval_service.get_approval_settings(tenant_id)

        # Assert
        assert settings.auto_approve_b2c is True
        assert settings.require_admin_approval_b2b is True

    @pytest_asyncio.async_test
    async def test_auto_approval_for_b2c_products(
        self,
        approval_service,
        mock_db_session,
        sample_submission_request
    ):
        """Test automatic approval for B2C products when enabled."""
        # Arrange
        sample_submission_request.market_type = "B2C"
        
        mock_product = Mock()
        mock_product.approval_status = "draft"
        mock_db_session.scalar.return_value = mock_product
        
        mock_settings = Mock()
        mock_settings.auto_approve_b2c = True
        
        with patch.object(approval_service, 'get_approval_settings', 
                         return_value=mock_settings):
            # Act
            result = await approval_service.submit_product_for_approval(
                sample_submission_request,
                str(uuid4())
            )

            # Assert
            assert result.status == "approved"  # Auto-approved
            assert mock_product.approval_status == "approved"

    @pytest_asyncio.async_test
    async def test_commission_rate_validation(
        self,
        approval_service,
        mock_db_session,
        sample_submission_request
    ):
        """Test commission rate validation during approval."""
        # Arrange
        sample_submission_request.commission_rate = 0.5  # 50% - too high
        
        mock_product = Mock()
        mock_product.approval_status = "draft"
        mock_db_session.scalar.return_value = mock_product

        # Act & Assert
        with pytest.raises(ValueError, match="Commission rate cannot exceed"):
            await approval_service.submit_product_for_approval(
                sample_submission_request,
                str(uuid4())
            )

    @pytest_asyncio.async_test
    async def test_approval_notification_integration(
        self,
        approval_service,
        mock_notification_service,
        mock_db_session,
        sample_submission_request
    ):
        """Test integration with notification service."""
        # Arrange
        mock_product = Mock()
        mock_product.approval_status = "draft"
        mock_product.vendor_id = str(uuid4())
        mock_db_session.scalar.return_value = mock_product

        # Act
        await approval_service.submit_product_for_approval(
            sample_submission_request,
            str(uuid4())
        )

        # Assert
        mock_notification_service.send_approval_notification.assert_called_once()
        call_args = mock_notification_service.send_approval_notification.call_args
        assert call_args[1]["notification_type"] == "product_submitted"
        assert call_args[1]["product_id"] == sample_submission_request.product_id

    def test_approval_status_transitions_validation(self, approval_service):
        """Test that only valid status transitions are allowed."""
        # Valid transitions
        assert approval_service._is_valid_status_transition("draft", "pending")
        assert approval_service._is_valid_status_transition("pending", "approved")
        assert approval_service._is_valid_status_transition("pending", "rejected")
        assert approval_service._is_valid_status_transition("revision_requested", "pending")
        
        # Invalid transitions
        assert not approval_service._is_valid_status_transition("approved", "pending")
        assert not approval_service._is_valid_status_transition("rejected", "approved")
        assert not approval_service._is_valid_status_transition("draft", "approved") 