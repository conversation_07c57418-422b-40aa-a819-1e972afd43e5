"""
Market Context Middleware - B2B/B2C Context Switching.

Middleware for automatic market context detection and enforcement
based on user roles and session preferences.
"""

import uuid
import logging
from typing import Optional, Dict, Any
from fastapi import Request, Response, HTTPException, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from app.db.session import get_db
from app.modules.core.users.models.tenant_user_association import TenantUserAssociation
from app.modules.core.roles.models.roles import MarketContext, RolePermissions
from app.modules.core.auth.dependencies.auth_dependencies import get_current_user_from_token

logger = logging.getLogger(__name__)


class MarketContextMiddleware(BaseHTTPMiddleware):
    """
    Middleware to handle market context switching and enforcement.
    
    This middleware:
    1. Detects user's current market context from headers/session
    2. Validates context access based on user role
    3. Applies context-specific business logic
    4. Adds context information to request state
    """

    def __init__(self, app, excluded_paths: Optional[list] = None):
        super().__init__(app)
        self.excluded_paths = excluded_paths or [
            "/docs", "/redoc", "/openapi.json", "/health",
            "/api/v1/auth/", "/api/v1/system/"
        ]

    async def dispatch(self, request: Request, call_next):
        """Process request and apply market context logic."""
        
        # Skip middleware for excluded paths
        if any(request.url.path.startswith(path) for path in self.excluded_paths):
            return await call_next(request)

        try:
            # Extract market context and user information
            context_info = await self._extract_context_info(request)
            
            if context_info:
                # Add context to request state
                request.state.market_context = context_info['market_context']
                request.state.user_role = context_info['user_role']
                request.state.tenant_id = context_info['tenant_id']
                request.state.b2b_authorized = context_info['b2b_authorized']
                request.state.pricing_tier = context_info.get('pricing_tier')
                request.state.commission_rate = context_info.get('commission_rate')
                
                # Validate context access
                if not await self._validate_context_access(context_info):
                    return JSONResponse(
                        status_code=status.HTTP_403_FORBIDDEN,
                        content={
                            "detail": f"Access denied for market context: {context_info['market_context']}"
                        }
                    )
                
                # Apply context-specific headers
                response = await call_next(request)
                response.headers["X-Market-Context"] = context_info['market_context']
                response.headers["X-User-Role"] = context_info['user_role']
                
                if context_info['b2b_authorized']:
                    response.headers["X-B2B-Authorized"] = "true"
                    
                return response
            else:
                # No user context, proceed normally
                return await call_next(request)
                
        except Exception as e:
            logger.error(f"Market context middleware error: {e}")
            # Don't block request on middleware errors
            return await call_next(request)

    async def _extract_context_info(self, request: Request) -> Optional[Dict[str, Any]]:
        """Extract market context and user information from request."""
        
        try:
            # Get user from token
            user = await get_current_user_from_token(request)
            if not user:
                return None
                
            # Get tenant ID from headers or path
            tenant_id = self._extract_tenant_id(request)
            if not tenant_id:
                return None
                
            # Get user association
            db = next(get_db())
            association = db.query(TenantUserAssociation).filter(
                TenantUserAssociation.user_id == user.id,
                TenantUserAssociation.tenant_id == tenant_id
            ).first()
            
            if not association:
                return None
                
            # Get requested market context from headers
            requested_context = request.headers.get("X-Market-Context")
            
            # Determine effective market context
            effective_context = self._determine_effective_context(
                association, requested_context
            )
            
            return {
                'user_id': user.id,
                'tenant_id': tenant_id,
                'user_role': association.role,
                'market_context': effective_context,
                'b2b_authorized': association.is_authorized_for_b2b,
                'pricing_tier': association.pricing_tier,
                'commission_rate': float(association.commission_rate) if association.commission_rate else None,
                'association': association
            }
            
        except Exception as e:
            logger.error(f"Error extracting context info: {e}")
            return None

    def _extract_tenant_id(self, request: Request) -> Optional[uuid.UUID]:
        """Extract tenant ID from request."""
        
        # Try from headers first
        tenant_header = request.headers.get("X-Tenant-ID")
        if tenant_header:
            try:
                return uuid.UUID(tenant_header)
            except ValueError:
                pass
                
        # Try from path parameters
        path_parts = request.url.path.split('/')
        for i, part in enumerate(path_parts):
            if part == "tenants" and i + 1 < len(path_parts):
                try:
                    return uuid.UUID(path_parts[i + 1])
                except ValueError:
                    pass
                    
        # Try from query parameters
        tenant_query = request.query_params.get("tenant_id")
        if tenant_query:
            try:
                return uuid.UUID(tenant_query)
            except ValueError:
                pass
                
        return None

    def _determine_effective_context(
        self, 
        association: TenantUserAssociation, 
        requested_context: Optional[str]
    ) -> str:
        """Determine the effective market context for the user."""
        
        # If no specific context requested, use user's default
        if not requested_context:
            return association.market_context or 'b2c'
            
        # Validate requested context
        if requested_context not in [ctx.value for ctx in MarketContext]:
            return association.market_context or 'b2c'
            
        # Check if user can access requested context
        if association.can_access_market_context(requested_context):
            return requested_context
        else:
            # Fall back to user's default context
            return association.market_context or 'b2c'

    async def _validate_context_access(self, context_info: Dict[str, Any]) -> bool:
        """Validate if user can access the current market context."""
        
        association = context_info['association']
        market_context = context_info['market_context']
        
        # Check basic role permissions
        if not RolePermissions.has_market_access(association.role, market_context):
            return False
            
        # For B2B context, check authorization
        if market_context == 'b2b':
            if association.is_b2b_role and not association.is_authorized_for_b2b:
                return False
                
        return True


class B2BContextValidator:
    """Utility class for B2B context validation in endpoints."""
    
    @staticmethod
    def require_b2b_context(request: Request) -> None:
        """Require B2B market context for endpoint access."""
        if not hasattr(request.state, 'market_context'):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication required"
            )
            
        if request.state.market_context != 'b2b':
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="B2B context required for this operation"
            )
            
        if hasattr(request.state, 'b2b_authorized') and not request.state.b2b_authorized:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="B2B authorization required"
            )

    @staticmethod
    def require_vendor_role(request: Request) -> None:
        """Require TVENDOR role for endpoint access."""
        B2BContextValidator.require_b2b_context(request)
        
        if request.state.user_role != 'tvendor':
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Vendor role required for this operation"
            )

    @staticmethod
    def require_customer_role(request: Request) -> None:
        """Require TCOSTUMER role for endpoint access."""
        B2BContextValidator.require_b2b_context(request)
        
        if request.state.user_role != 'tcostumer':
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="B2B customer role required for this operation"
            )

    @staticmethod
    def get_pricing_tier(request: Request) -> Optional[str]:
        """Get user's pricing tier for B2B operations."""
        if hasattr(request.state, 'pricing_tier'):
            return request.state.pricing_tier
        return None

    @staticmethod
    def get_commission_rate(request: Request) -> Optional[float]:
        """Get vendor's commission rate for B2B operations."""
        if hasattr(request.state, 'commission_rate'):
            return request.state.commission_rate
        return None


# Dependency functions for FastAPI endpoints

def require_b2b_context(request: Request) -> None:
    """FastAPI dependency to require B2B context."""
    B2BContextValidator.require_b2b_context(request)

def require_vendor_role(request: Request) -> None:
    """FastAPI dependency to require vendor role."""
    B2BContextValidator.require_vendor_role(request)

def require_customer_role(request: Request) -> None:
    """FastAPI dependency to require customer role."""
    B2BContextValidator.require_customer_role(request)

def get_market_context(request: Request) -> str:
    """FastAPI dependency to get current market context."""
    return getattr(request.state, 'market_context', 'b2c')

def get_user_role(request: Request) -> Optional[str]:
    """FastAPI dependency to get current user role."""
    return getattr(request.state, 'user_role', None)

def get_pricing_tier(request: Request) -> Optional[str]:
    """FastAPI dependency to get user's pricing tier."""
    return B2BContextValidator.get_pricing_tier(request)

def get_commission_rate(request: Request) -> Optional[float]:
    """FastAPI dependency to get vendor's commission rate."""
    return B2BContextValidator.get_commission_rate(request) 