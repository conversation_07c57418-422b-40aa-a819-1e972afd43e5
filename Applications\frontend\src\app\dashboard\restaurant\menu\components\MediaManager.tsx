'use client';

import { useState, useRef, useEffect, useCallback } from 'react';
import Image from 'next/image';
import {
  PhotoIcon,
  PlusIcon,
  XMarkIcon,
  MagnifyingGlassIcon,
  StarIcon,
  VideoCameraIcon,
  ArrowsUpDownIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { Loader2 } from 'lucide-react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { mediaUploadService, MediaUploadResponse, UploadProgress } from '@/lib/services/mediaUploadService';
import { ImageViewer } from './ImageViewer';
import { AuthenticatedImage } from '@/components/common/AuthenticatedImage';
import Cookies from 'js-cookie';

interface MediaData {
  id: string;
  url: string;
  type: 'image' | 'video';
  mediaData?: MediaUploadResponse;
  isUploading?: boolean;
  uploadProgress?: UploadProgress;
  error?: string;
  tempId?: string;
  isPrimary?: boolean;
  displayOrder: number;
}

interface MediaManagerProps {
  menuItemId?: string;
  onMediaChange?: (media: MediaData[]) => void;
  maxImages?: number;
  maxVideos?: number;
  disabled?: boolean;
  onGetTempUploads?: () => string[]; // Function to get temp upload IDs
}

export function MediaManager({
  menuItemId,
  onMediaChange,
  maxImages = 9,
  maxVideos = 1,
  disabled = false,
  onGetTempUploads
}: MediaManagerProps) {
  console.log('🔍 MediaManager: Initialized with menuItemId:', menuItemId);
  const [mediaList, setMediaList] = useState<MediaData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [viewerOpen, setViewerOpen] = useState(false);
  const [viewerMediaSrc, setViewerMediaSrc] = useState('');
  const [viewerMediaAlt, setViewerMediaAlt] = useState('');
  const [viewerMediaType, setViewerMediaType] = useState<'image' | 'video'>('image');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Drag & Drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Count current media
  const imageCount = mediaList.filter(m => m.type === 'image').length;
  const videoCount = mediaList.filter(m => m.type === 'video').length;
  const canAddImages = imageCount < maxImages;
  const canAddVideos = videoCount < maxVideos;

  // Notify parent when media list changes
  useEffect(() => {
    if (onMediaChange) {
      onMediaChange(mediaList);
    }
  }, [mediaList, onMediaChange]);

  // Expose temp upload IDs to parent
  useEffect(() => {
    if (onGetTempUploads) {
      // Override the function to return current temp upload IDs
      (onGetTempUploads as any).getTempUploadIds = () => {
        return mediaList
          .filter(media => media.mediaData?.id && !menuItemId)
          .map(media => media.mediaData!.id!);
      };
    }
  }, [mediaList, onGetTempUploads, menuItemId]);

  const loadExistingMedia = useCallback(async () => {
    if (!menuItemId) return;

    try {
      setIsLoading(true);
      const existingMedia = await mediaUploadService.getMenuItemMedia(menuItemId);
      
      const transformedMedia: MediaData[] = existingMedia.map((item, index) => ({
        id: item.id,
        url: item.file_url,
        type: item.media_type === 'video' ? 'video' : 'image',
        mediaData: item,
        isPrimary: item.is_primary,
        displayOrder: item.display_order || index
      }));

      // Sort by display order
      transformedMedia.sort((a, b) => a.displayOrder - b.displayOrder);

      // Ensure the first item is always primary (but don't force it if backend says otherwise)
      if (transformedMedia.length > 0) {
        // Check if any item is already marked as primary
        const hasPrimary = transformedMedia.some(item => item.isPrimary);

        if (!hasPrimary) {
          // If no item is primary, make the first one primary
          transformedMedia[0].isPrimary = true;
        } else {
          // If there's already a primary, ensure only the first one is primary
          transformedMedia.forEach((item, index) => {
            if (item.isPrimary && index !== 0) {
              // If a non-first item is marked as primary, move it to first position
              const primaryItem = transformedMedia.splice(index, 1)[0];
              transformedMedia.unshift(primaryItem);
              // Re-sort display orders
              transformedMedia.forEach((media, idx) => {
                media.displayOrder = idx;
                media.isPrimary = idx === 0;
              });
            }
          });
        }
      }

      setMediaList(transformedMedia);
    } catch (error) {
      console.error('Failed to load existing media:', error);
    } finally {
      setIsLoading(false);
    }
  }, [menuItemId]);

  // Load existing media
  useEffect(() => {
    if (menuItemId) {
      loadExistingMedia();
    }
  }, [menuItemId, loadExistingMedia]);



  const processFile = useCallback(async (file: File) => {
    const isVideo = file.type.startsWith('video/');
    const isImage = file.type.startsWith('image/');

    // Check limits
    if (isImage && !canAddImages) {
      alert(`Máximo de ${maxImages} imagens permitidas`);
      return;
    }
    if (isVideo && !canAddVideos) {
      alert(`Máximo de ${maxVideos} vídeo permitido`);
      return;
    }

    // Create preview
    const previewUrl = mediaUploadService.createPreviewUrl(file);
    const tempId = `uploading-${Date.now()}-${Math.random()}`;
    const tempMediaData: MediaData = {
      id: tempId,
      url: previewUrl,
      type: isVideo ? 'video' : 'image',
      isUploading: true,
      uploadProgress: { loaded: 0, total: file.size, percentage: 0 },
      tempId,
      isPrimary: false, // Never primary during upload
      displayOrder: mediaList.length // Always add at the end
    };

    setMediaList(prevList => [...prevList, tempMediaData]);

    if (!menuItemId) {
      // Upload temporarily for new items
      try {
        const response = await mediaUploadService.uploadTempMedia([file], {
          onProgress: (progress) => {
            setMediaList(prevList =>
              prevList.map(media =>
                media.tempId === tempId
                  ? { ...media, uploadProgress: progress }
                  : media
              )
            );
          },
          onSuccess: (apiResponse) => {
            if (apiResponse.success && apiResponse.uploads && apiResponse.uploads.length > 0) {
              const uploadedMedia = apiResponse.uploads[0];
              setMediaList(prevList =>
                prevList.map(media =>
                  media.tempId === tempId
                    ? {
                        ...media,
                        id: uploadedMedia.id || tempId,
                        url: uploadedMedia.file_url || media.url,
                        isUploading: false,
                        mediaData: uploadedMedia,
                        uploadProgress: undefined
                      }
                    : media
                )
              );
            }
          }
        });
      } catch (error) {
        console.error('Failed to upload temp media:', error);
        setMediaList(prevList =>
          prevList.map(media =>
            media.tempId === tempId
              ? {
                  ...media,
                  isUploading: false,
                  error: error instanceof Error ? error.message : 'Upload failed'
                }
              : media
          )
        );
      }
      return;
    }

    // Upload to server for existing items
    try {
      const response = await mediaUploadService.uploadMenuItemMedia(menuItemId, [file], {
        onProgress: (progress) => {
          setMediaList(prevList =>
            prevList.map(media =>
              media.tempId === tempId
                ? { ...media, uploadProgress: progress }
                : media
            )
          );
        },
        onSuccess: (apiResponse) => {
          if (apiResponse.success && apiResponse.uploads && apiResponse.uploads.length > 0) {
            const uploadedMedia = apiResponse.uploads[0];

            setMediaList(prevList => {
              const newListWithoutTemp = prevList.filter(media => media.tempId !== tempId);
              const isFirstEverMedia = newListWithoutTemp.length === 0; // Only if no media exists at all

              const updatedMediaData: MediaData = {
                id: uploadedMedia.id,
                url: uploadedMedia.file_url,
                type: uploadedMedia.media_type === 'video' ? 'video' : 'image',
                mediaData: uploadedMedia,
                isUploading: false,
                isPrimary: isFirstEverMedia, // Only if this is the very first media ever
                displayOrder: newListWithoutTemp.length, // Always add at the end
                tempId: undefined
              };

              const newList = [...newListWithoutTemp, updatedMediaData];

              // Only set as primary on server if this is the very first media ever
              if (isFirstEverMedia && menuItemId) {
                mediaUploadService.setPrimaryMedia(menuItemId, uploadedMedia.id).catch(error => {
                  console.error('Failed to set first media as primary:', error);
                });
              }

              return newList;
            });

            // Clean up preview URL
            mediaUploadService.revokePreviewUrl(previewUrl);
          }
        },
        onError: (error) => {
          setMediaList(prevList =>
            prevList.map(media =>
              media.tempId === tempId
                ? { ...media, isUploading: false, error: error.message }
                : media
            )
          );
        }
      });
    } catch (error) {
      console.error('Upload failed:', error);
      setMediaList(prevList =>
        prevList.map(media =>
          media.tempId === tempId
            ? {
                ...media,
                isUploading: false,
                error: error instanceof Error ? error.message : 'Upload failed'
              }
            : media
        )
      );
    }
  }, [menuItemId, canAddImages, canAddVideos, maxImages, maxVideos]);

  const handleFileSelect = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;

    for (const file of files) {
      await processFile(file);
    }

    // Clear input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [processFile]);

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = mediaList.findIndex((item) => item.id === active.id);
      const newIndex = mediaList.findIndex((item) => item.id === over?.id);

      const updatedItems = arrayMove(mediaList, oldIndex, newIndex).map((item, index) => ({
        ...item,
        displayOrder: index,
        // The first item (index 0) is always primary
        isPrimary: index === 0
      }));

      setMediaList(updatedItems);

      // Update order on server
      if (menuItemId) {
        try {
          const orderData = updatedItems.map((item, index) => ({
            id: item.id,
            display_order: index
          }));
          await mediaUploadService.updateMediaOrder(menuItemId, orderData);

          // Set the first item as primary
          if (updatedItems.length > 0) {
            await mediaUploadService.setPrimaryMedia(menuItemId, updatedItems[0].id);
          }
        } catch (error) {
          console.error('Failed to update media order:', error);
        }
      }
    }
  };

  const setPrimary = async (mediaId: string) => {
    console.log('🌟 MediaManager.setPrimary: Called with mediaId:', mediaId, 'menuItemId:', menuItemId);

    // Find the media to be set as primary
    const mediaToSetPrimary = mediaList.find(media => media.id === mediaId);
    if (!mediaToSetPrimary) {
      console.error('🌟 MediaManager.setPrimary: Media not found in list:', mediaId);
      return;
    }

    console.log('🌟 MediaManager.setPrimary: Found media to set as primary:', mediaToSetPrimary);

    // Create new list with the primary media first
    const otherMedia = mediaList.filter(media => media.id !== mediaId);
    const updatedList = [
      { ...mediaToSetPrimary, isPrimary: true, displayOrder: 0 },
      ...otherMedia.map((media, index) => ({
        ...media,
        isPrimary: false,
        displayOrder: index + 1
      }))
    ];

    setMediaList(updatedList);

    if (menuItemId) {
      try {
        console.log('🌟 MediaManager.setPrimary: Updating backend for menuItemId:', menuItemId);

        // Update order on server first
        const orderData = updatedList.map((item, index) => ({
          id: item.id,
          display_order: index
        }));
        console.log('🌟 MediaManager.setPrimary: Updating order:', orderData);
        await mediaUploadService.updateMediaOrder(menuItemId, orderData);

        // Then set as primary
        console.log('🌟 MediaManager.setPrimary: Setting primary media:', mediaId);
        await mediaUploadService.setPrimaryMedia(menuItemId, mediaId);

        // Invalidate cache and trigger update event for immediate UI refresh
        // Use the EXACT same cache key format as useMenuItemMedia hook
        const tenantId = localStorage.getItem('tenant_id') ||
                        document.cookie.split('; ').find(row => row.startsWith('tenant_id='))?.split('=')[1];
        const cacheKey = `media_cache_${tenantId}_${menuItemId}`;
        console.log('🌟 MediaManager.setPrimary: Invalidating cache with key:', cacheKey);
        localStorage.removeItem(cacheKey);

        // Also force the hook to bypass cache by dispatching event immediately
        console.log('🌟 MediaManager.setPrimary: Dispatching update event for itemId:', menuItemId);
        window.dispatchEvent(new CustomEvent('menuItemMediaUpdated', {
          detail: { itemId: menuItemId }
        }));
      } catch (error) {
        console.error('Failed to set primary media:', error);
      }
    }
  };

  const removeMedia = async (index: number) => {
    const mediaToRemove = mediaList[index];
    const newMediaList = mediaList.filter((_, i) => i !== index);

    setMediaList(newMediaList);

    if (menuItemId && mediaToRemove.mediaData) {
      try {
        await mediaUploadService.deleteMedia(menuItemId, mediaToRemove.id);
      } catch (error) {
        console.error('Failed to delete media:', error);
      }
    }
  };

  const openViewer = (src: string, alt: string, type: 'image' | 'video' = 'image') => {
    setViewerMediaSrc(src);
    setViewerMediaAlt(alt);
    setViewerMediaType(type);
    setViewerOpen(true);
  };

  const closeViewer = () => {
    setViewerOpen(false);
    setViewerMediaSrc('');
    setViewerMediaAlt('');
    setViewerMediaType('image');
  };

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*,video/*"
          onChange={handleFileSelect}
          className="hidden"
          disabled={disabled || isLoading}
        />
        
        <div className="space-y-2">
          <div className="flex justify-center space-x-2">
            <PhotoIcon className="h-8 w-8 text-gray-400" />
            <VideoCameraIcon className="h-8 w-8 text-gray-400" />
          </div>
          <div>
            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              disabled={disabled || isLoading}
              className="text-primary-600 hover:text-primary-500 font-medium disabled:opacity-50"
            >
              Clique para adicionar mídia
            </button>
            <p className="text-gray-500 text-sm mt-1">
              ou arraste arquivos aqui
            </p>
          </div>
          <div className="text-xs text-gray-400 space-y-1">
            <p>• Imagens: máximo {maxImages} ({imageCount}/{maxImages})</p>
            <p>• Vídeos: máximo {maxVideos} ({videoCount}/{maxVideos})</p>
            <p>• Formatos: JPG, PNG, GIF, WebP, SVG, MP4, WebM, OGG</p>
            <p>• Tamanho: até 10MB (imagens) / 50MB (vídeos)</p>
            <p>• <strong>Clique na ★ para definir a imagem principal</strong></p>
          </div>
        </div>
      </div>

      {/* Media Grid with Drag & Drop */}
      {mediaList.length > 0 && (
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          <SortableContext items={mediaList.map(m => m.id)} strategy={verticalListSortingStrategy}>
            <div className="grid grid-cols-3 gap-4">
              {mediaList.map((media, index) => (
                <SortableMediaItem
                  key={media.id}
                  media={media}
                  index={index}
                  onSetPrimary={setPrimary}
                  onRemove={removeMedia}
                  onView={openViewer}
                />
              ))}
            </div>
          </SortableContext>
        </DndContext>
      )}



      {/* Media Viewer */}
      <ImageViewer
        src={viewerMediaSrc}
        alt={viewerMediaAlt}
        type={viewerMediaType}
        isOpen={viewerOpen}
        onClose={closeViewer}
      />
    </div>
  );
}



// Sortable Media Item Component
interface SortableMediaItemProps {
  media: MediaData;
  index: number;
  onSetPrimary: (mediaId: string) => void;
  onRemove: (index: number) => void;
  onView: (src: string, alt: string, type?: 'image' | 'video') => void;
}

function SortableMediaItem({ media, index, onSetPrimary, onRemove, onView }: SortableMediaItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: media.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`relative aspect-square bg-gray-100 rounded-lg overflow-hidden group ${
        isDragging ? 'shadow-lg opacity-50' : ''
      }`}
    >
      {/* Drag Handle */}
      <div
        {...attributes}
        {...listeners}
        className="absolute top-2 left-2 z-10 p-1 bg-black bg-opacity-50 rounded text-white opacity-0 group-hover:opacity-100 transition-opacity cursor-move"
      >
        <ArrowsUpDownIcon className="h-4 w-4" />
      </div>

      {/* Primary Badge - Always visible */}
      <div className="absolute top-2 right-2 z-10">
        {media.isPrimary ? (
          <div className="p-1 bg-yellow-500 rounded-full shadow-lg">
            <StarIconSolid className="h-4 w-4 text-white" />
          </div>
        ) : (
          <button
            onClick={(e) => {
              e.stopPropagation();
              console.log('🌟 MediaManager: User clicked star to set primary, mediaId:', media.id);
              onSetPrimary(media.id);
            }}
            className="p-1 bg-gray-500 bg-opacity-70 hover:bg-yellow-500 rounded-full transition-colors shadow-lg"
            title="Definir como imagem principal"
          >
            <StarIcon className="h-4 w-4 text-white" />
          </button>
        )}
      </div>

      {/* Media Content */}
      {media.type === 'video' ? (
        <video
          src={media.url}
          className="w-full h-full object-cover"
          controls={false}
          muted
          onClick={() => onView(media.url, `Vídeo ${index + 1}`, 'video')}
        />
      ) : (
        <div
          className="w-full h-full cursor-pointer"
          onClick={() => onView(media.url, `Imagem ${index + 1}`, 'image')}
        >
          {media.url.startsWith('blob:') ? (
            <div className="relative w-full h-full">
              <Image
                src={media.url}
                alt={`Mídia ${index + 1}`}
                fill
                className="object-cover"
              />
            </div>
          ) : (
            <AuthenticatedImage
              src={media.url}
              alt={`Mídia ${index + 1}`}
              className="w-full h-full object-cover"
            />
          )}
        </div>
      )}

      {/* Upload Progress */}
      {media.isUploading && media.uploadProgress && (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div className="text-center text-white">
            <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
            <div className="text-sm">{media.uploadProgress.percentage}%</div>
          </div>
        </div>
      )}

      {/* Error State */}
      {media.error && (
        <div className="absolute inset-0 bg-red-500 bg-opacity-75 flex items-center justify-center">
          <div className="text-center text-white text-sm">
            <XMarkIcon className="h-6 w-6 mx-auto mb-1" />
            <div>Erro no upload</div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      {!media.isUploading && (
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
          <div className="flex space-x-2">
            {/* Primary button is now always visible in top-right corner */}

            {/* View Button */}
            <button
              onClick={(e) => {
                e.stopPropagation();
                onView(media.url, `${media.type === 'video' ? 'Vídeo' : 'Imagem'} ${index + 1}`, media.type);
              }}
              className="p-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors"
              title="Visualizar"
            >
              <MagnifyingGlassIcon className="h-4 w-4" />
            </button>

            {/* Remove Button */}
            <button
              onClick={(e) => {
                e.stopPropagation();
                onRemove(index);
              }}
              className="p-2 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
              title="Remover"
            >
              <XMarkIcon className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
