"""
Service for managing translations.
"""

import logging
import uuid
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, or_, update, delete

from app.modules.core.i18n.models.translation import Translation
from app.modules.core.i18n.models.language import Language, generate_version_code
from app.modules.core.i18n.models.translation_change import TranslationChange, ChangeType
from app.modules.core.i18n.schemas import translation as schemas
from app.modules.core.i18n.services.language_service import LanguageService
from app.modules.core.i18n.services.translation_change_service import TranslationChangeService

# Set up logger
logger = logging.getLogger(__name__)


class TranslationService:
    """Service for managing translations."""

    async def create_translation(
        self,
        db: AsyncSession,
        translation_in: schemas.TranslationCreate,
        user_id: Optional[uuid.UUID] = None,
    ) -> Translation:
        """
        Create a new translation.

        Args:
            db: Database session
            translation_in: Translation data
            user_id: ID of the user creating the translation

        Returns:
            The created Translation object
        """
        # Check if a translation already exists for this key and language
        existing = await self.get_translation_by_key_and_language(
            db, translation_in.key, translation_in.language_id
        )

        if existing:
            # If it exists, update it instead
            previous_text = existing.text

            # Update fields
            for field, value in translation_in.model_dump().items():
                setattr(existing, field, value)

            db.add(existing)
            await db.flush()
            await db.refresh(existing)

            # Record the change
            change_service = TranslationChangeService()
            await change_service.record_change(
                db=db,
                key=existing.key,
                sector=existing.sector,
                language_id=existing.language_id,
                change_type=ChangeType.UPDATED,
                new_text=existing.text,
                previous_text=previous_text,
                changed_by_id=user_id,
            )

            # Update the language version code
            await self._update_language_version(db, existing.language_id)

            return existing

        # Create new translation
        db_translation = Translation(
            key=translation_in.key,
            text=translation_in.text,
            sector=translation_in.sector,
            language_id=translation_in.language_id,
            last_updated_by_id=user_id,
        )

        db.add(db_translation)
        await db.flush()
        await db.refresh(db_translation)

        # Record the change
        change_service = TranslationChangeService()
        await change_service.record_change(
            db=db,
            key=db_translation.key,
            sector=db_translation.sector,
            language_id=db_translation.language_id,
            change_type=ChangeType.ADDED,
            new_text=db_translation.text,
            previous_text=None,
            changed_by_id=user_id,
        )

        # Update the language version code
        await self._update_language_version(db, db_translation.language_id)

        return db_translation

    async def get_translation(
        self, db: AsyncSession, translation_id: uuid.UUID
    ) -> Optional[Translation]:
        """
        Get a translation by ID.

        Args:
            db: Database session
            translation_id: Translation ID

        Returns:
            The Translation object or None if not found
        """
        result = await db.execute(select(Translation).where(Translation.id == translation_id))
        return result.scalar_one_or_none()

    async def get_translation_by_key_and_language(
        self, db: AsyncSession, key: str, language_id: uuid.UUID
    ) -> Optional[Translation]:
        """
        Get a translation by key and language ID.

        Args:
            db: Database session
            key: Translation key
            language_id: Language ID

        Returns:
            The Translation object or None if not found
        """
        result = await db.execute(
            select(Translation).where(
                and_(Translation.key == key, Translation.language_id == language_id)
            )
        )
        return result.scalar_one_or_none()

    async def get_translations(
        self,
        db: AsyncSession,
        language_id: Optional[uuid.UUID] = None,
        key: Optional[str] = None,
        sector: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[Translation]:
        """
        Get translations with optional filtering and pagination.

        Args:
            db: Database session
            language_id: Optional language ID to filter by
            key: Optional key to filter by
            sector: Optional sector to filter by
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            List of Translation objects
        """
        query = select(Translation)

        # Apply filters
        if language_id:
            query = query.where(Translation.language_id == language_id)
        if key:
            query = query.where(Translation.key == key)
        if sector:
            query = query.where(Translation.sector == sector)

        # Apply pagination
        query = query.offset(skip).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    async def get_translations_by_language(
        self, db: AsyncSession, language_id: uuid.UUID, skip: int = 0, limit: int = 100
    ) -> List[Translation]:
        """
        Get all translations for a language with pagination.

        Args:
            db: Database session
            language_id: Language ID
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            List of Translation objects
        """
        return await self.get_translations(db, language_id=language_id, skip=skip, limit=limit)

    async def get_translations_by_language_code(
        self, db: AsyncSession, language_code: str, sector: Optional[str] = None
    ) -> Dict[str, Dict[str, str]]:
        """
        Get all translations for a language code, grouped by sector.

        Args:
            db: Database session
            language_code: Language code
            sector: Optional sector to filter by

        Returns:
            Dictionary of translations grouped by sector
        """
        # Get the language
        language_service = LanguageService()
        language = await language_service.get_language_by_code(db, language_code)

        if not language:
            return {}

        # Build the query
        query = select(Translation).where(Translation.language_id == language.id)

        # Add sector filter if provided
        if sector:
            query = query.where(Translation.sector == sector)

        # Execute the query
        result = await db.execute(query)
        translations = result.scalars().all()

        # Group translations by sector
        translations_dict = {}
        for translation in translations:
            if translation.sector not in translations_dict:
                translations_dict[translation.sector] = {}

            translations_dict[translation.sector][translation.key] = translation.text

        return translations_dict

    async def update_translation(
        self,
        db: AsyncSession,
        translation_id: uuid.UUID,
        translation_in: schemas.TranslationUpdate,
        user_id: Optional[uuid.UUID] = None,
    ) -> Optional[Translation]:
        """
        Update a translation.

        Args:
            db: Database session
            translation_id: Translation ID
            translation_in: Translation update data
            user_id: ID of the user updating the translation

        Returns:
            The updated Translation object or None if not found
        """
        db_translation = await self.get_translation(db, translation_id)
        if not db_translation:
            return None

        # Store previous values for change tracking
        previous_text = db_translation.text
        previous_sector = db_translation.sector

        # Update fields
        update_data = translation_in.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_translation, field, value)

        # Update last_updated_by_id if user_id is provided
        if user_id:
            db_translation.last_updated_by_id = user_id

        db.add(db_translation)
        await db.flush()
        await db.refresh(db_translation)

        # Record the change if text or sector changed
        if "text" in update_data or "sector" in update_data:
            change_service = TranslationChangeService()
            await change_service.record_change(
                db=db,
                key=db_translation.key,
                sector=db_translation.sector,
                language_id=db_translation.language_id,
                change_type=ChangeType.UPDATED,
                new_text=db_translation.text,
                previous_text=previous_text,
                changed_by_id=user_id,
            )

            # Update the language version code
            await self._update_language_version(db, db_translation.language_id)

        return db_translation

    async def delete_translation(
        self, db: AsyncSession, translation_id: uuid.UUID, user_id: Optional[uuid.UUID] = None
    ) -> bool:
        """
        Delete a translation.

        Args:
            db: Database session
            translation_id: Translation ID
            user_id: ID of the user deleting the translation

        Returns:
            True if deleted, False if not found
        """
        db_translation = await self.get_translation(db, translation_id)
        if not db_translation:
            return False

        # Store values for change tracking
        key = db_translation.key
        sector = db_translation.sector
        language_id = db_translation.language_id
        previous_text = db_translation.text

        # Delete the translation
        await db.delete(db_translation)
        await db.flush()

        # Record the change
        change_service = TranslationChangeService()
        await change_service.record_change(
            db=db,
            key=key,
            sector=sector,
            language_id=language_id,
            change_type=ChangeType.DELETED,
            new_text=None,
            previous_text=previous_text,
            changed_by_id=user_id,
        )

        # Update the language version code
        await self._update_language_version(db, language_id)

        return True

    async def _update_language_version(self, db: AsyncSession, language_id: uuid.UUID) -> None:
        """
        Update the version code of a language.

        Args:
            db: Database session
            language_id: Language ID
        """
        language_service = LanguageService()
        language = await language_service.get_language(db, language_id)
        if language:
            # Generate a new version code
            language.version_code = generate_version_code()
            db.add(language)
            await db.flush()
