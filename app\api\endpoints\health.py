"""
Health check endpoint for monitoring system status and dependencies.
"""

import asyncio
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional

import psutil
import redis
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.redis_client import get_redis_client
from app.db.session import get_db

router = APIRouter()


class HealthChecker:
    """Advanced health checker with comprehensive system monitoring."""
    
    def __init__(self):
        self.start_time = time.time()
        self.version = getattr(settings, 'VERSION', '1.0.0')
        self.environment = getattr(settings, 'ENVIRONMENT', 'development')
    
    async def check_database(self, db: AsyncSession) -> Dict[str, Any]:
        """Check database connectivity and performance."""
        try:
            start_time = time.time()
            
            # Test basic connectivity
            result = await db.execute(text("SELECT 1"))
            result.fetchone()
            
            # Test transaction capability
            await db.execute(text("BEGIN"))
            await db.execute(text("SELECT NOW()"))
            await db.execute(text("COMMIT"))
            
            response_time = (time.time() - start_time) * 1000
            
            # Get database stats
            stats_query = text("""
                SELECT 
                    count(*) as active_connections,
                    (SELECT setting FROM pg_settings WHERE name = 'max_connections') as max_connections
                FROM pg_stat_activity 
                WHERE state = 'active'
            """)
            stats_result = await db.execute(stats_query)
            stats = stats_result.fetchone()
            
            return {
                "status": "healthy",
                "response_time_ms": round(response_time, 2),
                "active_connections": stats.active_connections if stats else 0,
                "max_connections": stats.max_connections if stats else 0,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    
    async def check_redis(self, redis_client: redis.Redis) -> Dict[str, Any]:
        """Check Redis connectivity and performance."""
        try:
            start_time = time.time()
            
            # Test basic connectivity
            await redis_client.ping()
            
            # Test read/write operations
            test_key = f"health_check_{int(time.time())}"
            await redis_client.set(test_key, "test_value", ex=60)
            value = await redis_client.get(test_key)
            await redis_client.delete(test_key)
            
            if value != b"test_value":
                raise Exception("Redis read/write test failed")
            
            response_time = (time.time() - start_time) * 1000
            
            # Get Redis info
            info = await redis_client.info()
            
            return {
                "status": "healthy",
                "response_time_ms": round(response_time, 2),
                "connected_clients": info.get("connected_clients", 0),
                "used_memory_human": info.get("used_memory_human", "unknown"),
                "redis_version": info.get("redis_version", "unknown"),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    
    def check_system_resources(self) -> Dict[str, Any]:
        """Check system resource usage."""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            
            # Disk usage
            disk = psutil.disk_usage('/')
            
            # Network stats (if available)
            try:
                network = psutil.net_io_counters()
                network_stats = {
                    "bytes_sent": network.bytes_sent,
                    "bytes_recv": network.bytes_recv,
                    "packets_sent": network.packets_sent,
                    "packets_recv": network.packets_recv
                }
            except:
                network_stats = {"error": "Network stats unavailable"}
            
            return {
                "status": "healthy",
                "cpu": {
                    "usage_percent": cpu_percent,
                    "count": psutil.cpu_count()
                },
                "memory": {
                    "total_gb": round(memory.total / (1024**3), 2),
                    "available_gb": round(memory.available / (1024**3), 2),
                    "used_percent": memory.percent
                },
                "disk": {
                    "total_gb": round(disk.total / (1024**3), 2),
                    "free_gb": round(disk.free / (1024**3), 2),
                    "used_percent": round((disk.used / disk.total) * 100, 2)
                },
                "network": network_stats,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    
    def get_uptime(self) -> Dict[str, Any]:
        """Get application uptime information."""
        uptime_seconds = time.time() - self.start_time
        
        days = int(uptime_seconds // 86400)
        hours = int((uptime_seconds % 86400) // 3600)
        minutes = int((uptime_seconds % 3600) // 60)
        seconds = int(uptime_seconds % 60)
        
        return {
            "uptime_seconds": round(uptime_seconds, 2),
            "uptime_formatted": f"{days}d {hours}h {minutes}m {seconds}s",
            "started_at": datetime.fromtimestamp(
                self.start_time, 
                tz=timezone.utc
            ).isoformat()
        }


# Global health checker instance
health_checker = HealthChecker()


@router.get("/health")
async def basic_health():
    """Basic health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "version": health_checker.version,
        "environment": health_checker.environment
    }


@router.get("/health/detailed")
async def detailed_health(
    db: AsyncSession = Depends(get_db),
    redis_client: redis.Redis = Depends(get_redis_client)
):
    """Detailed health check with all dependencies."""
    
    # Run all checks concurrently
    db_check_task = health_checker.check_database(db)
    redis_check_task = health_checker.check_redis(redis_client)
    
    # Wait for async checks
    db_health, redis_health = await asyncio.gather(
        db_check_task, 
        redis_check_task,
        return_exceptions=True
    )
    
    # Handle exceptions
    if isinstance(db_health, Exception):
        db_health = {
            "status": "unhealthy",
            "error": str(db_health),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    
    if isinstance(redis_health, Exception):
        redis_health = {
            "status": "unhealthy", 
            "error": str(redis_health),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    
    # Get system resources
    system_health = health_checker.check_system_resources()
    
    # Get uptime
    uptime_info = health_checker.get_uptime()
    
    # Determine overall status
    all_healthy = all([
        db_health.get("status") == "healthy",
        redis_health.get("status") == "healthy", 
        system_health.get("status") == "healthy"
    ])
    
    overall_status = "healthy" if all_healthy else "unhealthy"
    
    response = {
        "status": overall_status,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "version": health_checker.version,
        "environment": health_checker.environment,
        "uptime": uptime_info,
        "dependencies": {
            "database": db_health,
            "redis": redis_health,
            "system": system_health
        }
    }
    
    # Return appropriate HTTP status
    if overall_status == "unhealthy":
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=response
        )
    
    return response


@router.get("/health/ready")
async def readiness_check(
    db: AsyncSession = Depends(get_db),
    redis_client: redis.Redis = Depends(get_redis_client)
):
    """Kubernetes-style readiness probe."""
    try:
        # Quick database check
        await db.execute(text("SELECT 1"))
        
        # Quick Redis check
        await redis_client.ping()
        
        return {
            "status": "ready",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail={
                "status": "not_ready",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        )


@router.get("/health/live")
async def liveness_check():
    """Kubernetes-style liveness probe."""
    return {
        "status": "alive",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "uptime": health_checker.get_uptime()
    }
