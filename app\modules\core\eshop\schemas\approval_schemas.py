"""
EShop Product Approval Schemas

Schemas para o sistema de aprovação de produtos do EShop.
Inclui validações, tipos de dados e estruturas para:
- Aprovação e rejeição de produtos
- Histórico de aprovações
- Configurações de aprovação automática
- Operações em lote
"""

from datetime import datetime
from typing import List, Optional
from uuid import UUID
from enum import Enum

from pydantic import BaseModel, Field


class ProductApprovalStatus(str, Enum):
    """Status de aprovação de produtos."""
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    REVISION_REQUESTED = "revision_requested"


class ProductApprovalAction(str, Enum):
    """Ações de aprovação disponíveis."""
    SUBMIT = "submit"
    APPROVE = "approve"
    REJECT = "reject"
    REQUEST_REVISION = "request_revision"
    AUTO_APPROVE = "auto_approve"
    AUTO_REJECT = "auto_reject"


class ProductApprovalReason(str, Enum):
    """Razões para aprovação/rejeição."""
    MEETS_STANDARDS = "meets_standards"
    POLICY_VIOLATION = "policy_violation"
    INCOMPLETE_INFO = "incomplete_info"
    QUALITY_ISSUES = "quality_issues"
    PRICING_ISSUES = "pricing_issues"
    CATEGORY_MISMATCH = "category_mismatch"
    DUPLICATE_PRODUCT = "duplicate_product"
    TRUSTED_VENDOR = "trusted_vendor"
    AUTO_APPROVAL_RULE = "auto_approval_rule"


class ProductApprovalRequest(BaseModel):
    """Schema para solicitação de aprovação de produto."""
    
    comments: Optional[str] = Field(
        None, 
        max_length=1000,
        description="Comentários sobre a submissão"
    )
    reason: Optional[ProductApprovalReason] = Field(
        ProductApprovalReason.MEETS_STANDARDS,
        description="Razão da solicitação de aprovação"
    )


class ProductRejectionRequest(BaseModel):
    """Schema para rejeição de produto."""
    
    rejection_reason: str = Field(
        ...,
        min_length=10,
        max_length=1000,
        description="Razão da rejeição (visível ao vendor)"
    )
    reason: Optional[ProductApprovalReason] = Field(
        ProductApprovalReason.POLICY_VIOLATION,
        description="Categoria da rejeição"
    )
    internal_notes: Optional[str] = Field(
        None,
        max_length=1000,
        description="Notas internas (não visíveis ao vendor)"
    )


class ProductRevisionRequest(BaseModel):
    """Schema para solicitação de revisão de produto."""
    
    revision_notes: str = Field(
        ...,
        min_length=10,
        max_length=1000,
        description="Notas sobre as revisões necessárias"
    )
    reason: Optional[ProductApprovalReason] = Field(
        ProductApprovalReason.INCOMPLETE_INFO,
        description="Razão da solicitação de revisão"
    )


class ProductApprovalHistoryResponse(BaseModel):
    """Schema de resposta para histórico de aprovação."""
    
    id: UUID
    tenant_id: UUID
    product_id: UUID
    action: ProductApprovalAction
    reason: Optional[ProductApprovalReason]
    previous_status: Optional[str]
    new_status: str
    comments: Optional[str]
    internal_notes: Optional[str]  # Pode ser filtrado para vendors
    rejection_reason: Optional[str]
    performed_by: UUID
    performed_by_role: Optional[str]
    is_automated: bool
    automation_rule: Optional[str]
    processing_time_seconds: Optional[str]
    created_at: datetime
    
    class Config:
        from_attributes = True


class ProductApprovalResponse(BaseModel):
    """Schema de resposta para ações de aprovação."""
    
    success: bool
    message: str
    approval_history: Optional[ProductApprovalHistoryResponse] = None


class ProductSummaryResponse(BaseModel):
    """Schema resumido de produto para listagens."""
    
    id: UUID
    name: str
    short_description: Optional[str]
    base_price: float
    vendor_id: UUID
    category_id: UUID
    market_type: str
    approval_status: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class ProductsPendingResponse(BaseModel):
    """Schema de resposta para lista de produtos pendentes."""
    
    products: List[ProductSummaryResponse]
    total: int
    page: int
    per_page: int
    total_pages: int


class ApprovalStatsResponse(BaseModel):
    """Schema de resposta para estatísticas de aprovação."""
    
    pending_approval: int
    approved_today: int
    rejected_today: int
    total_processed: int
    approval_rate: Optional[float] = None  # Percentual de aprovação
    avg_processing_time: Optional[float] = None  # Tempo médio de processamento


class ProductApprovalSettingsResponse(BaseModel):
    """Schema de resposta para configurações de aprovação."""
    
    id: UUID
    tenant_id: UUID
    auto_approve_trusted_vendors: bool
    auto_approve_b2c_products: bool
    auto_approve_b2b_products: bool
    max_auto_approve_price: Optional[str]
    min_vendor_rating: Optional[str]
    notify_admins_on_submission: bool
    notify_vendor_on_approval: bool
    notify_vendor_on_rejection: bool
    approval_deadline_hours: str
    auto_reject_after_days: Optional[str]
    require_approval_for_edits: bool
    require_approval_for_price_changes: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class ProductApprovalSettingsUpdate(BaseModel):
    """Schema para atualização de configurações de aprovação."""
    
    auto_approve_trusted_vendors: Optional[bool] = None
    auto_approve_b2c_products: Optional[bool] = None
    auto_approve_b2b_products: Optional[bool] = None
    max_auto_approve_price: Optional[str] = Field(
        None,
        pattern=r'^\d+(\.\d{1,2})?$',
        description="Preço máximo para auto-aprovação (formato: 123.45)"
    )
    min_vendor_rating: Optional[str] = Field(
        None,
        pattern=r'^[1-5](\.\d)?$',
        description="Rating mínimo do vendor (1.0-5.0)"
    )
    notify_admins_on_submission: Optional[bool] = None
    notify_vendor_on_approval: Optional[bool] = None
    notify_vendor_on_rejection: Optional[bool] = None
    approval_deadline_hours: Optional[str] = Field(
        None,
        pattern=r'^\d{1,3}$',
        description="Prazo para aprovação em horas (1-999)"
    )
    auto_reject_after_days: Optional[str] = Field(
        None,
        pattern=r'^\d{1,2}$',
        description="Auto-rejeitar após X dias (1-99)"
    )
    require_approval_for_edits: Optional[bool] = None
    require_approval_for_price_changes: Optional[bool] = None


class BulkApprovalRequest(BaseModel):
    """Schema para aprovação em lote."""
    
    product_ids: List[UUID] = Field(
        ...,
        min_length=1,
        max_length=50,
        description="Lista de IDs de produtos para aprovar (máximo 50)"
    )
    comments: Optional[str] = Field(
        None,
        max_length=500,
        description="Comentários aplicados a todos os produtos"
    )
    reason: ProductApprovalReason = Field(
        ProductApprovalReason.MEETS_STANDARDS,
        description="Razão da aprovação em lote"
    )


class BulkApprovalResponse(BaseModel):
    """Schema de resposta para aprovação em lote."""
    
    success_count: int
    error_count: int
    total_processed: int
    successful_products: List[UUID]
    failed_products: List[dict]  # {"product_id": UUID, "error": str}
    message: str


class ApprovalWorkflowResponse(BaseModel):
    """Schema de resposta para fluxo de trabalho de aprovação."""
    
    product_id: UUID
    current_status: str
    available_actions: List[str]  # ["approve", "reject", "request_revision"]
    can_auto_approve: bool
    estimated_processing_time: Optional[str]
    next_deadline: Optional[datetime] 