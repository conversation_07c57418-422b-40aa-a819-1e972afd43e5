import uuid
import logging
from typing import Any, Dict, Optional, List, Union, TYPE_CHECKING

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.exc import SQLAlchemyError

from app.modules.core.users.schemas.user import UserCreate, UserUpdate
from app.modules.core.auth.security.token_utils import get_password_hash, verify_password

# Use TYPE_CHECKING to avoid circular import
if TYPE_CHECKING:
    from app.modules.core.users.models.user import User

logger = logging.getLogger(__name__)


class UserService:
    """
    Service for operations related to the User model.
    """

    async def get_user(self, db: AsyncSession, *, user_id: Union[uuid.UUID, str]) -> Optional["User"]:
        """
        Fetches a user by ID.

        Args:
            db: The async database session.
            user_id: The UUID of the user to fetch.

        Returns:
            The User object if found, otherwise None.
        """
        try:
            # Import here to avoid circular import
            from app.modules.core.users.models.user import User
            
            if isinstance(user_id, str):
                user_id = uuid.UUID(user_id)

            stmt = select(User).where(User.id == user_id)
            result = await db.execute(stmt)
            return result.scalars().first()
        except SQLAlchemyError as e:
            logger.error(f"Database error fetching user by ID {user_id}: {e}", exc_info=True)
            return None
        except ValueError as e:
            logger.error(f"Invalid UUID format for user_id: {user_id}. Error: {e}")
            return None

    # Alias for get_user to maintain compatibility with auth_service
    async def get_user_by_id(
        self, db: AsyncSession, *, user_id: Union[uuid.UUID, str]
    ) -> Optional["User"]:
        """Alias for get_user to maintain compatibility with auth_service."""
        return await self.get_user(db, user_id=user_id)

    async def get_user_by_email(self, db: AsyncSession, *, email: str) -> Optional["User"]:
        """
        Fetches a user by email.

        Args:
            db: The async database session.
            email: The email of the user to fetch.

        Returns:
            The User object if found, otherwise None.
        """
        try:
            # Import here to avoid circular import
            from app.modules.core.users.models.user import User
            
            stmt = select(User).where(User.email == email)
            result = await db.execute(stmt)
            return result.scalars().first()
        except SQLAlchemyError as e:
            logger.error(f"Database error fetching user by email {email}: {e}", exc_info=True)
            return None

    async def get_user_by_phone(self, db: AsyncSession, *, phone_number: str) -> Optional["User"]:
        """Get a user by phone number."""
        try:
            # Import here to avoid circular import
            from app.modules.core.users.models.user import User
            
            result = await db.execute(select(User).filter(User.phone_number == phone_number))
            return result.scalars().first()
        except SQLAlchemyError as e:
            logger.error(
                f"Database error fetching user by phone number {phone_number}: {e}",
                exc_info=True,
            )
            return None

    async def get_users(self, db: AsyncSession, *, skip: int = 0, limit: int = 100) -> List["User"]:
        """
        Lists users with pagination, including tenant associations.

        Args:
            db: The async database session.
            skip: Number of records to skip.
            limit: Maximum number of records to return.

        Returns:
            A list of User objects with tenant associations loaded.
        """
        try:
            # Import here to avoid circular import
            from app.modules.core.users.models.user import User
            from sqlalchemy.orm import selectinload
            from app.modules.core.users.models.tenant_user_association import TenantUserAssociation

            stmt = (
                select(User)
                .options(
                    selectinload(User.tenant_associations).selectinload(TenantUserAssociation.tenant)
                )
                .offset(skip)
                .limit(limit)
            )
            result = await db.execute(stmt)
            return result.scalars().all()
        except SQLAlchemyError as e:
            logger.error(f"Database error fetching users: {e}", exc_info=True)
            return []

    async def create_user(self, db: AsyncSession, *, user_in: UserCreate) -> "User":
        """
        Create a new user.

        Ensures the email is unique and hashes the password.
        """
        # Import here to avoid circular import
        from app.modules.core.users.models.user import User
        
        # Check if the email already exists
        existing_user = await self.get_user_by_email(db, email=user_in.email)
        if existing_user:
            raise ValueError("User with this email already exists")

        hashed_password = get_password_hash(user_in.password)
        db_user = User(
            email=user_in.email,
            hashed_password=hashed_password,
            full_name=user_in.full_name,
            phone_number=user_in.phone_number,
            system_role=user_in.system_role,
            # is_active is True by default in the model
        )
        try:
            db.add(db_user)
            await db.commit()
            await db.refresh(db_user)
            return db_user
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(f"Database error creating user: {e}", exc_info=True)
            raise ValueError(f"Could not create user: {e}")

    async def update_user(
        self,
        db: AsyncSession,
        *,
        db_obj: "User",
        obj_in: UserUpdate,
    ) -> "User":
        """
        Update an existing user's data.

        Allows updating fields defined in the UserUpdate schema.
        Password updates are handled carefully.
        """
        update_data = obj_in.model_dump(exclude_unset=True)

        # Special handling for password
        if "password" in update_data and update_data["password"]:
            hashed_password = get_password_hash(update_data["password"])
            db_obj.hashed_password = hashed_password
            del update_data["password"]  # Remove to avoid direct assignment

        # Update other fields
        for field, value in update_data.items():
            setattr(db_obj, field, value)

        try:
            db.add(db_obj)
            await db.commit()
            await db.refresh(db_obj)
            return db_obj
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(
                f"Database error updating user {db_obj.id if db_obj else 'UNKNOWN'}: {e}",
                exc_info=True,
            )
            raise ValueError(f"Could not update user: {e}")

    async def delete_user(self, db: AsyncSession, *, user_id: uuid.UUID) -> Optional["User"]:
        """
        Mark a user as inactive (soft delete).

        Returns the user marked as inactive or None if not found.
        """
        db_user = await self.get_user_by_id(db, user_id=user_id)
        if not db_user:
            return None

        # Implementing soft delete
        db_user.is_active = False
        try:
            db.add(db_user)
            await db.commit()
            await db.refresh(db_user)
            return db_user
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(f"Database error marking user {user_id} as inactive: {e}", exc_info=True)
            raise ValueError(f"Could not mark user as inactive: {e}")

    async def authenticate_user(
        self, db: AsyncSession, *, email: str, password: str
    ) -> Optional["User"]:
        """
        Authenticate a user by email and password.

        Args:
            db: The async database session.
            email: User's email address.
            password: Plain text password.

        Returns:
            The User object if authentication succeeds, otherwise None.
        """
        user = await self.get_user_by_email(db, email=email)
        if not user:
            return None
        if not verify_password(password, user.hashed_password):
            return None
        return user


# Create a global instance
user_service = UserService()
