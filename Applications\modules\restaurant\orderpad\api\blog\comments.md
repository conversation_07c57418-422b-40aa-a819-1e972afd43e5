# Blog - Comments

**Categoria:** Blog
**Módulo:** Comments
**Total de Endpoints:** 5
**Gerado em:** 08/06/2025, 17:26:01

## 📋 Endpoints

- [GET /api/modules/core/blog/comments/](#get-apimodulescoreblogcomments) - Get Comments
- [POST /api/modules/core/blog/comments/](#post-apimodulescoreblogcomments) - Create Comment
- [POST /api/modules/core/blog/comments/moderate](#post-apimodulescoreblogcommentsmoderate) - Moderate Comments
- [GET /api/modules/core/blog/comments/post/{post_id}/tree](#get-apimodulescoreblogcommentspostpost-idtree) - Get Post Comments Tree
- [GET /api/modules/core/blog/comments/stats](#get-apimodulescoreblogcommentsstats) - Get Comment Stats

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### BlogCommentCreate

**Descrição:** Schema for creating blog comments.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `content` | string | ✅ | - |
| `guest_name` | unknown | ❌ | - |
| `guest_email` | unknown | ❌ | - |
| `guest_website` | unknown | ❌ | - |
| `post_id` | string | ✅ | - |
| `parent_id` | unknown | ❌ | - |

### BlogCommentModeration

**Descrição:** Schema for comment moderation actions.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `comment_ids` | Array[string] | ✅ | - |
| `action` | string | ✅ | - |

### BlogCommentRead

**Descrição:** Schema for reading blog comments.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `content` | string | ✅ | - |
| `guest_name` | unknown | ❌ | - |
| `guest_email` | unknown | ❌ | - |
| `guest_website` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `post_id` | string | ✅ | - |
| `user_id` | unknown | ✅ | - |
| `parent_id` | unknown | ✅ | - |
| `status` | string | ✅ | - |
| `is_pinned` | boolean | ✅ | - |
| `like_count` | integer | ✅ | - |
| `reply_count` | integer | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `user_display_name` | unknown | ❌ | - |
| `user_avatar_url` | unknown | ❌ | - |

### BlogCommentStats

**Descrição:** Schema for comment statistics.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `total_comments` | integer | ✅ | - |
| `pending_comments` | integer | ✅ | - |
| `approved_comments` | integer | ✅ | - |
| `rejected_comments` | integer | ✅ | - |
| `spam_comments` | integer | ✅ | - |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/modules/core/blog/comments/ {#get-apimodulescoreblogcomments}

**Resumo:** Get Comments
**Descrição:** Get blog comments with filtering and pagination.

Supports filtering by post and moderation status.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | Number of comments to skip |
| `limit` | integer | query | ❌ | Number of comments to return |
| `post_id` | string | query | ❌ | Filter by post |
| `status` | string | query | ❌ | Filter by status |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/comments/"
```

---

### POST /api/modules/core/blog/comments/ {#post-apimodulescoreblogcomments}

**Resumo:** Create Comment
**Descrição:** Create a new blog comment.

Can be created by authenticated users or guests.
Guest comments require name and email.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [BlogCommentCreate](#blogcommentcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogCommentRead](#blogcommentread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/blog/comments/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/core/blog/comments/moderate {#post-apimodulescoreblogcommentsmoderate}

**Resumo:** Moderate Comments
**Descrição:** Moderate blog comments (approve, reject, mark as spam, delete).

Requires authentication and admin privileges.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [BlogCommentModeration](#blogcommentmoderation)

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/blog/comments/moderate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/blog/comments/post/{post_id}/tree {#get-apimodulescoreblogcommentspostpost-idtree}

**Resumo:** Get Post Comments Tree
**Descrição:** Get comments for a post in threaded tree format.

Returns comments organized hierarchically with replies.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `post_id` | string | path | ✅ | - |
| `status` | string | query | ❌ | Comment status filter |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/comments/post/{post_id}/tree"
```

---

### GET /api/modules/core/blog/comments/stats {#get-apimodulescoreblogcommentsstats}

**Resumo:** Get Comment Stats
**Descrição:** Get comment statistics.

Requires authentication. Returns moderation statistics.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `post_id` | string | query | ❌ | Filter by post |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogCommentStats](#blogcommentstats)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/comments/stats" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
