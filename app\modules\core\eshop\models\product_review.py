import uuid
from sqlalchemy import Column, String, ForeignKey, Index, Boolean, Integer, Text, DateTime
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import Base
from app.modules.core.tenants.models.tenant import Tenant
from app.modules.core.users.models.user import User


class ProductReview(Base):
    """
    Represents a user review for a product in the eshop marketplace.
    """

    __tablename__ = "eshop_product_reviews"

    # Primary key
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Relationships
    product_id = Column(PG_UUID(as_uuid=True), ForeignKey("eshop_products.id"), nullable=False, index=True)
    user_id = Column(PG_UUID(as_uuid=True), ForeignKey(User.id), nullable=False, index=True)
    tenant_id = Column(PG_UUID(as_uuid=True), ForeignKey(Tenant.id), nullable=True, index=True)
    
    # Review content
    rating = Column(Integer, nullable=False)  # 1-5 stars
    title = Column(String(200), nullable=True)  # Review title
    comment = Column(Text, nullable=True)  # Review text
    
    # Review metadata
    is_verified_purchase = Column(Boolean, default=False, nullable=False)  # Did user actually buy this?
    is_approved = Column(Boolean, default=True, nullable=False)  # Admin approval
    is_featured = Column(Boolean, default=False, nullable=False)  # Featured review
    
    # Helpful votes
    helpful_count = Column(Integer, default=0, nullable=False)  # How many found this helpful
    total_votes = Column(Integer, default=0, nullable=False)  # Total votes (helpful + not helpful)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    product = relationship("app.modules.core.eshop.models.product.Product", back_populates="reviews")
    user = relationship("User")
    tenant = relationship("Tenant")

    __table_args__ = (
        Index("ix_eshop_product_reviews_product_id", "product_id"),
        Index("ix_eshop_product_reviews_user_id", "user_id"),
        Index("ix_eshop_product_reviews_tenant_id", "tenant_id"),
        Index("ix_eshop_product_reviews_rating", "rating"),
        Index("ix_eshop_product_reviews_is_approved", "is_approved"),
        Index("ix_eshop_product_reviews_product_rating", "product_id", "rating"),
        Index("ix_eshop_product_reviews_product_approved", "product_id", "is_approved"),
    )

    def __repr__(self):
        return f"<ProductReview(id={self.id}, product_id={self.product_id}, rating={self.rating})>"
