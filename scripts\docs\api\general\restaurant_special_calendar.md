# General - Restaurant Special Calendar

**Categoria:** General
**Módu<PERSON>:** Restaurant Special Calendar
**Total de Endpoints:** 1
**Gerado em:** 27/06/2025, 20:20:27

## 📋 Endpoints

- [PUT /api/modules/restaurants/settings/special-calendar/](#put-apimodulesrestaurantssettingsspecial-calendar) - Update Special Calendar

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### RestaurantSpecialCalendarUpdate

**Descrição:** Schema for updating restaurant special calendar.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `special_calendar` | unknown | ❌ | Special calendar events by date |

### RestaurantTenantSettingsRead

**Descrição:** Schema for reading restaurant tenant settings.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `tenant_slug` | unknown | ❌ | Unique slug for tenant identification in public URLs |
| `wifi_networks` | unknown | ❌ | WiFi networks configuration by zone |
| `social_media_links` | unknown | ❌ | Social media platform links with icons |
| `address_extensions` | unknown | ❌ | Restaurant-specific address extensions |
| `operating_hours` | unknown | ❌ | Restaurant operating hours with service, break, and happy hour periods |
| `special_calendar` | unknown | ❌ | Special calendar events with custom hours or closures |
| `additional_restaurant_settings` | unknown | ❌ | Additional restaurant-specific settings |
| `id` | string | ✅ | Unique identifier for the restaurant settings |
| `tenant_id` | string | ✅ | ID of the tenant these settings belong to |
| `has_tenant_slug` | boolean | ✅ | Whether tenant slug is configured |
| `has_wifi_networks` | boolean | ✅ | Whether WiFi networks are configured |
| `has_social_media_links` | boolean | ✅ | Whether social media links are configured |
| `has_address_extensions` | boolean | ✅ | Whether address extensions are configured |
| `delivery_radius_km` | unknown | ❌ | Delivery radius in kilometers |
| `phone_is_whatsapp` | boolean | ❌ | Whether primary phone is WhatsApp |
| `has_operating_hours` | boolean | ❌ | Whether operating hours are configured |
| `has_special_calendar` | boolean | ❌ | Whether special calendar events are configured |
| `is_open_today` | boolean | ❌ | Whether restaurant is open today |

## 🔗 Endpoints Detalhados

### PUT /api/modules/restaurants/settings/special-calendar/ {#put-apimodulesrestaurantssettingsspecial-calendar}

**Resumo:** Update Special Calendar
**Descrição:** Update restaurant special calendar with holidays and special events.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [RestaurantSpecialCalendarUpdate](#restaurantspecialcalendarupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [RestaurantTenantSettingsRead](#restauranttenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/settings/special-calendar/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
