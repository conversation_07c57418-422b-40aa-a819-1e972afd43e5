'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Cookies from 'js-cookie';
import toast from 'react-hot-toast';
import { authApi } from '@/lib/api/auth';
import {
  AuthContextType,
  AuthState,
  LoginCredentials,
  RegisterCredentials,
  User,
  Tenant
} from '@/types/auth';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [state, setState] = useState<AuthState>({
    user: null,
    tenants: [],
    currentTenant: null,
    userTenantAssociations: [],
    isAuthenticated: false,
    isLoading: true,
  });
  const [isHydrated, setIsHydrated] = useState(false);

  const router = useRouter();

  // Handle hydration
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Initialize auth state on mount (only after hydration)
  useEffect(() => {
    if (isHydrated) {
      initializeAuth();
    }
  }, [isHydrated]);

  const initializeAuth = async () => {
    try {
      // Ensure we're on the client side
      if (typeof window === 'undefined') {
        setState(prev => ({ ...prev, isLoading: false }));
        return;
      }

      const token = Cookies.get('access_token');
      if (!token) {
        setState(prev => ({ ...prev, isLoading: false }));
        return;
      }

      // Verify token and get user data
      const [userResponse, tenants, tenantAssociations] = await Promise.all([
        authApi.getCurrentUser(),
        authApi.getUserTenants(),
        authApi.getUserTenantAssociations(),
      ]);

      let currentTenant = tenants[0] || null;
      let tenantIdToSetInCookie = currentTenant?.id;

      // TEMPORARY FALLBACK: If /auth/tenants is empty but associations exist,
      // try to use the first association's tenant_id to set the cookie.
      // This helps TenantProvider's fallback logic if a cookie is present.
      // The actual 'currentTenant' object might still be null here if 'tenants' is empty.
      if (!currentTenant && tenantAssociations && tenantAssociations.length > 0) {
        const firstAssociation = tenantAssociations[0];
        if (firstAssociation && firstAssociation.tenant_id) {
          console.warn(
            '[AuthProvider initializeAuth] TEMPORARY: /auth/tenants was empty. Using tenant_id from first association for cookie:',
            firstAssociation.tenant_id
          );
          tenantIdToSetInCookie = firstAssociation.tenant_id;
          // Note: We don't have the full Tenant object here, so `currentTenant` in AuthState
          // might still be null if `tenants` array is empty. TenantProvider will try to load it.
        }
      }

      // Set tenant_id cookie
      if (tenantIdToSetInCookie) {
        Cookies.set('tenant_id', tenantIdToSetInCookie, { expires: 7 });
      }

      console.log('[AuthProvider initializeAuth] User Response:', userResponse);
      console.log('[AuthProvider initializeAuth] Tenants from API:', tenants);
      console.log('[AuthProvider initializeAuth] Tenant Associations from API:', tenantAssociations);
      console.log('[AuthProvider initializeAuth] Deduced currentTenant:', currentTenant);

      setState({
        user: userResponse,
        tenants: tenants,
        currentTenant: currentTenant,
        userTenantAssociations: tenantAssociations,
        isAuthenticated: true,
        isLoading: false,
      });
    } catch (error) {
      console.error('Auth initialization failed:', error);
      // Clear invalid token
      Cookies.remove('access_token');
      Cookies.remove('refresh_token');
      Cookies.remove('tenant_id');
      setState(prev => ({ ...prev, isLoading: false }));
    }
  };

  const login = async (credentials: LoginCredentials) => {
    try {
      setState(prev => ({ ...prev, isLoading: true }));

      // Create form data for login
      const formData = new URLSearchParams();
      formData.append('username', credentials.username);
      formData.append('password', credentials.password);

      const tokenResponse = await authApi.login(formData);

      // Store tokens
      Cookies.set('access_token', tokenResponse.access_token, { expires: 7 });
      if (tokenResponse.refresh_token) {
        Cookies.set('refresh_token', tokenResponse.refresh_token, { expires: 30 });
      }

      // Wait a bit for cookies to be set
      await new Promise(resolve => setTimeout(resolve, 100));

      // Get user data first
      const userResponse = await authApi.getCurrentUser();

      // Get tenants and associations - admins get all tenants
      let tenants, tenantAssociations;
      if (userResponse.system_role === 'admin') {
        // Admin gets all tenants
        const [allTenants, userAssociations] = await Promise.all([
          authApi.getAllTenants(), // We need to create this API
          authApi.getUserTenantAssociations(),
        ]);
        tenants = allTenants;
        tenantAssociations = userAssociations;
      } else {
        // Regular user gets only their tenants
        const [userTenants, userAssociations] = await Promise.all([
          authApi.getUserTenants(),
          authApi.getUserTenantAssociations(),
        ]);
        tenants = userTenants;
        tenantAssociations = userAssociations;
      }

      let currentTenant = tenants[0] || null;
      let tenantIdToSetInCookie = currentTenant?.id;

      // TEMPORARY FALLBACK (similar to initializeAuth)
      if (userResponse.system_role !== 'admin' && !currentTenant && tenantAssociations && tenantAssociations.length > 0) {
        const firstAssociation = tenantAssociations[0];
        if (firstAssociation && firstAssociation.tenant_id) {
          console.warn(
            '[AuthProvider login] TEMPORARY: /auth/tenants was empty. Using tenant_id from first association for cookie:',
            firstAssociation.tenant_id
          );
          tenantIdToSetInCookie = firstAssociation.tenant_id;
        }
      }
      
      // Set tenant_id cookie
      if (tenantIdToSetInCookie) {
        Cookies.set('tenant_id', tenantIdToSetInCookie, { expires: 7 });
      }
      
      console.log('[AuthProvider login] User Response:', userResponse);
      console.log('[AuthProvider login] Tenants from API:', tenants);
      console.log('[AuthProvider login] Tenant Associations from API:', tenantAssociations);
      console.log('[AuthProvider login] Deduced currentTenant (from tenants list):', currentTenant); // This might be null
      console.log('[AuthProvider login] Tenant ID set in cookie:', tenantIdToSetInCookie);

      setState({
        user: userResponse,
        tenants: tenants,
        currentTenant: currentTenant,
        userTenantAssociations: tenantAssociations,
        isAuthenticated: true,
        isLoading: false,
      });

      toast.success('Login realizado com sucesso!');
    } catch (error: any) {
      setState(prev => ({ ...prev, isLoading: false }));

      // Handle different error formats
      let message = 'Erro ao fazer login';
      if (error?.response?.data?.detail) {
        if (Array.isArray(error.response.data.detail)) {
          // Validation errors array
          message = error.response.data.detail.map((err: any) => err.msg).join(', ');
        } else if (typeof error.response.data.detail === 'string') {
          // Simple string message
          message = error.response.data.detail;
        }
      }

      toast.error(message);
      throw error;
    }
  };

  const register = async (credentials: RegisterCredentials) => {
    try {
      setState(prev => ({ ...prev, isLoading: true }));

      const tokenResponse = await authApi.register(credentials);

      // Store tokens
      Cookies.set('access_token', tokenResponse.access_token, { expires: 7 });
      if (tokenResponse.refresh_token) {
        Cookies.set('refresh_token', tokenResponse.refresh_token, { expires: 30 });
      }

      // Wait a bit for cookies to be set
      await new Promise(resolve => setTimeout(resolve, 100));

      // Get user data first
      const userResponse = await authApi.getCurrentUser();

      // Get tenants and associations - admins get all tenants
      let tenants, tenantAssociations;
      if (userResponse.system_role === 'admin') {
        // Admin gets all tenants
        const [allTenants, userAssociations] = await Promise.all([
          authApi.getAllTenants(),
          authApi.getUserTenantAssociations(),
        ]);
        tenants = allTenants;
        tenantAssociations = userAssociations;
      } else {
        // Regular user gets only their tenants
        const [userTenants, userAssociations] = await Promise.all([
          authApi.getUserTenants(),
          authApi.getUserTenantAssociations(),
        ]);
        tenants = userTenants;
        tenantAssociations = userAssociations;
      }

      const currentTenant = tenants[0] || null;

      // Set tenant_id cookie for the first tenant
      if (currentTenant) {
        Cookies.set('tenant_id', currentTenant.id, { expires: 7 });
      }

      setState({
        user: userResponse,
        tenants: tenants,
        currentTenant: currentTenant,
        userTenantAssociations: tenantAssociations,
        isAuthenticated: true,
        isLoading: false,
      });

      toast.success('Conta criada com sucesso!');
    } catch (error: any) {
      setState(prev => ({ ...prev, isLoading: false }));

      // Handle different error formats
      let message = 'Erro ao criar conta';
      if (error?.response?.data?.detail) {
        if (Array.isArray(error.response.data.detail)) {
          // Validation errors array
          message = error.response.data.detail.map((err: any) => err.msg).join(', ');
        } else if (typeof error.response.data.detail === 'string') {
          // Simple string message
          message = error.response.data.detail;
        }
      }

      toast.error(message);
      throw error;
    }
  };

  const logout = () => {
    // Clear all auth data
    Cookies.remove('access_token');
    Cookies.remove('refresh_token');
    Cookies.remove('tenant_id');

    setState({
      user: null,
      tenants: [],
      currentTenant: null,
      userTenantAssociations: [],
      isAuthenticated: false,
      isLoading: false,
    });

    toast.success('Logout realizado com sucesso!');

    // Redirect to login page
    window.location.href = '/auth?mode=login';
  };

  const setCurrentTenant = (tenant: Tenant | null) => {
    setState(prev => ({ ...prev, currentTenant: tenant }));
    if (tenant) {
      Cookies.set('tenant_id', tenant.id, { expires: 7 });
    } else {
      Cookies.remove('tenant_id');
    }
  };

  const refreshTenants = async (): Promise<Tenant[]> => {
    try {
      // Get tenants and associations - admins get all tenants
      let tenants, tenantAssociations;
      if (state.user?.system_role === 'admin') {
        // Admin gets all tenants
        const [allTenants, userAssociations] = await Promise.all([
          authApi.getAllTenants(),
          authApi.getUserTenantAssociations(),
        ]);
        tenants = allTenants;
        tenantAssociations = userAssociations;
      } else {
        // Regular user gets only their tenants
        const [userTenants, userAssociations] = await Promise.all([
          authApi.getUserTenants(),
          authApi.getUserTenantAssociations(),
        ]);
        tenants = userTenants;
        tenantAssociations = userAssociations;
      }

      setState(prev => ({
        ...prev,
        tenants: tenants,
        userTenantAssociations: tenantAssociations
      }));
      return tenants;
    } catch (error) {
      console.error('Failed to refresh tenants:', error);
      return state.tenants;
    }
  };

  // Role checking utilities
  const hasRole = (role: string, tenantId?: string): boolean => {
    if (!state.user) return false;

    // Check system roles
    if (state.user.system_role === role) return true;

    // Check tenant-specific roles using userTenantAssociations
    if (tenantId && state.userTenantAssociations) {
      return state.userTenantAssociations.some(
        assoc => assoc.tenant_id === tenantId && assoc.role === role
      );
    }

    return false;
  };

  const isAdmin = useCallback((): boolean => {
    return state.user?.system_role === 'admin';
  }, [state.user]);

  const isTenantOwner = useCallback((tenantId?: string): boolean => {
    // System admins have owner access to all tenants
    if (state.user?.system_role === 'admin') {
      console.log('[AuthProvider isTenantOwner] Access granted (system admin)');
      return true;
    }

    const targetTenantId = tenantId || state.currentTenant?.id;
    console.log(`[AuthProvider isTenantOwner] Checking for tenantId: ${tenantId}, state.currentTenant?.id: ${state.currentTenant?.id}, effective targetTenantId: ${targetTenantId}`);

    if (!targetTenantId) {
      console.log('[AuthProvider isTenantOwner] Access DENIED (no targetTenantId)');
      return false;
    }
    
    const hasAssociation = state.userTenantAssociations?.some(assoc => {
      const match = assoc.tenant_id === targetTenantId && assoc.role === 'owner';
      console.log(`[AuthProvider isTenantOwner] Comparing assoc: { tenant_id: ${assoc.tenant_id}, role: ${assoc.role} } with target: { tenant_id: ${targetTenantId}, role: 'owner' } -> match: ${match}`);
      return match;
    });

    console.log(`[AuthProvider isTenantOwner] User associations:`, state.userTenantAssociations);
    console.log(`[AuthProvider isTenantOwner] Result for targetTenantId ${targetTenantId}: ${hasAssociation || false}`);
    return hasAssociation || false;
  }, [state.user, state.currentTenant, state.userTenantAssociations]);

  const isTenantStaff = useCallback((tenantId?: string): boolean => {
    // System admins have staff access to all tenants
    if (state.user?.system_role === 'admin') return true;

    const targetTenantId = tenantId || state.currentTenant?.id;
    if (!targetTenantId) return false;
    return state.userTenantAssociations?.some(
      assoc => assoc.tenant_id === targetTenantId && assoc.role === 'staff'
    ) || false;
  }, [state.user, state.currentTenant, state.userTenantAssociations]);

  const isTenantCustomer = useCallback((tenantId?: string): boolean => {
    const targetTenantId = tenantId || state.currentTenant?.id;
    if (!targetTenantId) return false;
    return state.userTenantAssociations?.some(
      assoc => assoc.tenant_id === targetTenantId && assoc.role === 'customer'
    ) || false;
  }, [state.currentTenant, state.userTenantAssociations]);
  
  // Need to import useCallback from React
  // import React, { createContext, useContext, useEffect, useState, ReactNode, useCallback } from 'react';

  const value: AuthContextType = {
    ...state,
    login,
    register,
    logout,
    setCurrentTenant,
    refreshTenants,
    hasRole,
    isAdmin,
    isTenantOwner,
    isTenantStaff,
    isTenantCustomer,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
