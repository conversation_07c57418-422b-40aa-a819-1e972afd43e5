# Core Functions - Shipping

**Categoria:** Core Functions
**Módulo:** Shipping
**Total de Endpoints:** 2
**Gerado em:** 27/06/2025, 20:20:26

## 📋 Endpoints

- [GET /api/modules/core/functions/shipping/shipping/cost](#get-apimodulescorefunctionsshippingshippingcost) - Get Shipping Cost
- [POST /api/modules/core/functions/shipping/shipping/shipments](#post-apimodulescorefunctionsshippingshippingshipments) - Create Shipment

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### ShipmentCreate

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `order_id` | string | ✅ | - |
| `shipping_method_id` | string | ✅ | - |
| `carrier_id` | string | ✅ | - |
| `tracking_code` | unknown | ❌ | - |
| `status` | ShipmentStatus | ❌ | - |

### ShipmentRead

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `order_id` | string | ✅ | - |
| `shipping_method_id` | string | ✅ | - |
| `carrier_id` | string | ✅ | - |
| `tracking_code` | unknown | ❌ | - |
| `status` | ShipmentStatus | ❌ | - |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |

## 🔗 Endpoints Detalhados

### GET /api/modules/core/functions/shipping/shipping/cost {#get-apimodulescorefunctionsshippingshippingcost}

**Resumo:** Get Shipping Cost
**Descrição:** Calculates a simulated shipping cost.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `weight` | number | query | ✅ | Weight of the package in kg |
| `distance` | number | query | ✅ | Distance for delivery in km |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/functions/shipping/shipping/cost"
```

---

### POST /api/modules/core/functions/shipping/shipping/shipments {#post-apimodulescorefunctionsshippingshippingshipments}

**Resumo:** Create Shipment
**Descrição:** Creates a new shipment record.

**🔐 Autenticação:** Não requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ShipmentCreate](#shipmentcreate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ShipmentRead](#shipmentread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/functions/shipping/shipping/shipments" \
  -H "Content-Type: application/json" \
  -d '{"example": "data"}'
```

---
