"""Contact models for CRM module."""

import uuid
import enum
from typing import TYPE_CHECKING, List, Optional
from sqlalchemy import <PERSON>umn, String, ForeignKey, Text, Enum as SQLAlchemyEnum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, Mapped, mapped_column

from app.db.base import Base

if TYPE_CHECKING:
    from app.modules.shared.crm.models.account import Account
    from app.modules.shared.crm.models.interaction import Interaction
    from app.modules.core.users.models.user import User


class ContactType(str, enum.Enum):
    """Contact type enum."""

    PRIMARY = "primary"
    BILLING = "billing"
    SHIPPING = "shipping"
    TECHNICAL = "technical"
    MARKETING = "marketing"
    SUPPORT = "support"
    OTHER = "other"


class ContactStatus(str, enum.Enum):
    """Contact status enum."""

    ACTIVE = "active"
    INACTIVE = "inactive"
    FORMER = "former"


class Contact(Base):
    """Contact model for CRM module.

    This represents a contact person within an account.
    """

    __tablename__ = "crm_contacts"

    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    account_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("crm_accounts.id"), nullable=False)
    user_id: Mapped[Optional[uuid.UUID]] = mapped_column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # Basic information
    first_name: Mapped[str] = mapped_column(String, nullable=False)
    last_name: Mapped[str] = mapped_column(String, nullable=False)
    full_name: Mapped[Optional[str]] = mapped_column(String)
    contact_type: Mapped[ContactType] = mapped_column(SQLAlchemyEnum(ContactType), nullable=False, default=ContactType.PRIMARY)
    status: Mapped[ContactStatus] = mapped_column(SQLAlchemyEnum(ContactStatus), nullable=False, default=ContactStatus.ACTIVE)

    # Contact information
    email: Mapped[Optional[str]] = mapped_column(String)
    phone: Mapped[Optional[str]] = mapped_column(String)
    mobile: Mapped[Optional[str]] = mapped_column(String)

    # Job information
    job_title: Mapped[Optional[str]] = mapped_column(String)
    department: Mapped[Optional[str]] = mapped_column(String)

    # Preferences
    preferred_contact_method: Mapped[Optional[str]] = mapped_column(String)
    communication_preferences: Mapped[Optional[str]] = mapped_column(String)

    # Additional information
    notes: Mapped[Optional[str]] = mapped_column(Text)

    # Relationships
    account: Mapped["Account"] = relationship(back_populates="contacts")
    user: Mapped[Optional["User"]] = relationship()
    interactions: Mapped[List["Interaction"]] = relationship("Interaction", back_populates="contact")

    def __repr__(self) -> str:
        return f"<Contact(id={self.id}, name='{self.first_name} {self.last_name}', type='{self.contact_type.value}')>"
