# Crm - Loyalty

**Categoria:** Crm
**Módulo:** Loyalty
**Total de Endpoints:** 10
**Gerado em:** 27/06/2025, 20:20:26

## 📋 Endpoints

- [POST /api/modules/crm/crm/loyalty/memberships](#post-apimodulescrmcrmloyaltymemberships) - Create Loyalty Membership
- [GET /api/modules/crm/crm/loyalty/memberships/account/{account_id}](#get-apimodulescrmcrmloyaltymembershipsaccountaccount-id) - Get Account Loyalty Memberships
- [GET /api/modules/crm/crm/loyalty/memberships/{membership_id}](#get-apimodulescrmcrmloyaltymembershipsmembership-id) - Get Loyalty Membership
- [PUT /api/modules/crm/crm/loyalty/memberships/{membership_id}](#put-apimodulescrmcrmloyaltymembershipsmembership-id) - Update Loyalty Membership
- [GET /api/modules/crm/crm/loyalty/programs](#get-apimodulescrmcrmloyaltyprograms) - Get Loyalty Programs
- [POST /api/modules/crm/crm/loyalty/programs](#post-apimodulescrmcrmloyaltyprograms) - Create Loyalty Program
- [GET /api/modules/crm/crm/loyalty/programs/{program_id}](#get-apimodulescrmcrmloyaltyprogramsprogram-id) - Get Loyalty Program
- [PUT /api/modules/crm/crm/loyalty/programs/{program_id}](#put-apimodulescrmcrmloyaltyprogramsprogram-id) - Update Loyalty Program
- [POST /api/modules/crm/crm/loyalty/transactions](#post-apimodulescrmcrmloyaltytransactions) - Create Loyalty Transaction
- [GET /api/modules/crm/crm/loyalty/transactions/membership/{membership_id}](#get-apimodulescrmcrmloyaltytransactionsmembershipmembership-id) - Get Membership Transactions

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### LoyaltyMembershipCreate

**Descrição:** Schema for creating a new LoyaltyMembership.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `membership_number` | unknown | ❌ | - |
| `status` | LoyaltyMembershipStatus | ❌ | - |
| `points_balance` | integer | ❌ | - |
| `tier_level` | unknown | ❌ | - |
| `join_date` | string | ❌ | - |
| `expiry_date` | unknown | ❌ | - |
| `program_id` | string | ✅ | - |
| `account_id` | string | ✅ | - |

### LoyaltyMembershipRead

**Descrição:** Schema for reading a LoyaltyMembership.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `membership_number` | unknown | ❌ | - |
| `status` | LoyaltyMembershipStatus | ❌ | - |
| `points_balance` | integer | ❌ | - |
| `tier_level` | unknown | ❌ | - |
| `join_date` | string | ❌ | - |
| `expiry_date` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `program_id` | string | ✅ | - |
| `account_id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `last_activity_date` | unknown | ❌ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### LoyaltyMembershipUpdate

**Descrição:** Schema for updating a LoyaltyMembership.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `membership_number` | unknown | ❌ | - |
| `status` | unknown | ❌ | - |
| `points_balance` | unknown | ❌ | - |
| `tier_level` | unknown | ❌ | - |
| `expiry_date` | unknown | ❌ | - |
| `last_activity_date` | unknown | ❌ | - |

### LoyaltyProgramCreate

**Descrição:** Schema for creating a new LoyaltyProgram.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `description` | unknown | ❌ | - |
| `program_type` | LoyaltyProgramType | ✅ | - |
| `is_active` | boolean | ❌ | - |
| `start_date` | unknown | ❌ | - |
| `end_date` | unknown | ❌ | - |
| `earning_rules` | unknown | ❌ | - |
| `redemption_rules` | unknown | ❌ | - |
| `expiration_rules` | unknown | ❌ | - |
| `tier_rules` | unknown | ❌ | - |

### LoyaltyProgramRead

**Descrição:** Schema for reading a LoyaltyProgram.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `description` | unknown | ❌ | - |
| `program_type` | LoyaltyProgramType | ✅ | - |
| `is_active` | boolean | ❌ | - |
| `start_date` | unknown | ❌ | - |
| `end_date` | unknown | ❌ | - |
| `earning_rules` | unknown | ❌ | - |
| `redemption_rules` | unknown | ❌ | - |
| `expiration_rules` | unknown | ❌ | - |
| `tier_rules` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### LoyaltyProgramUpdate

**Descrição:** Schema for updating a LoyaltyProgram.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `program_type` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `start_date` | unknown | ❌ | - |
| `end_date` | unknown | ❌ | - |
| `earning_rules` | unknown | ❌ | - |
| `redemption_rules` | unknown | ❌ | - |
| `expiration_rules` | unknown | ❌ | - |
| `tier_rules` | unknown | ❌ | - |

### LoyaltyTransactionCreate

**Descrição:** Schema for creating a new LoyaltyTransaction.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `transaction_type` | LoyaltyTransactionType | ✅ | - |
| `points` | integer | ✅ | - |
| `reference_type` | unknown | ❌ | - |
| `reference_id` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `transaction_date` | string | ❌ | - |
| `membership_id` | string | ✅ | - |

### LoyaltyTransactionRead

**Descrição:** Schema for reading a LoyaltyTransaction.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `transaction_type` | LoyaltyTransactionType | ✅ | - |
| `points` | integer | ✅ | - |
| `reference_type` | unknown | ❌ | - |
| `reference_id` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `transaction_date` | string | ❌ | - |
| `id` | string | ✅ | - |
| `membership_id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

## 🔗 Endpoints Detalhados

### POST /api/modules/crm/crm/loyalty/memberships {#post-apimodulescrmcrmloyaltymemberships}

**Resumo:** Create Loyalty Membership
**Descrição:** Create a new loyalty membership for a customer.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [LoyaltyMembershipCreate](#loyaltymembershipcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LoyaltyMembershipRead](#loyaltymembershipread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/crm/crm/loyalty/memberships" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/crm/crm/loyalty/memberships/account/{account_id} {#get-apimodulescrmcrmloyaltymembershipsaccountaccount-id}

**Resumo:** Get Account Loyalty Memberships
**Descrição:** Get all loyalty memberships for a specific account.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `account_id` | string | path | ✅ | The ID of the account to get memberships for |
| `skip` | integer | query | ❌ | Number of memberships to skip |
| `limit` | integer | query | ❌ | Maximum number of memberships to return |
| `status` | string | query | ❌ | Filter by membership status |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/loyalty/memberships/account/{account_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/crm/crm/loyalty/memberships/{membership_id} {#get-apimodulescrmcrmloyaltymembershipsmembership-id}

**Resumo:** Get Loyalty Membership
**Descrição:** Get a loyalty membership by ID.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `membership_id` | string | path | ✅ | The ID of the loyalty membership to retrieve |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LoyaltyMembershipRead](#loyaltymembershipread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/loyalty/memberships/{membership_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/crm/crm/loyalty/memberships/{membership_id} {#put-apimodulescrmcrmloyaltymembershipsmembership-id}

**Resumo:** Update Loyalty Membership
**Descrição:** Update a loyalty membership.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `membership_id` | string | path | ✅ | The ID of the loyalty membership to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [LoyaltyMembershipUpdate](#loyaltymembershipupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LoyaltyMembershipRead](#loyaltymembershipread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/crm/crm/loyalty/memberships/{membership_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/crm/crm/loyalty/programs {#get-apimodulescrmcrmloyaltyprograms}

**Resumo:** Get Loyalty Programs
**Descrição:** Get all loyalty programs with optional filtering.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | Number of programs to skip |
| `limit` | integer | query | ❌ | Maximum number of programs to return |
| `is_active` | string | query | ❌ | Filter by active status |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/loyalty/programs" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/crm/crm/loyalty/programs {#post-apimodulescrmcrmloyaltyprograms}

**Resumo:** Create Loyalty Program
**Descrição:** Create a new loyalty program.

Requires tenant owner or manager role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [LoyaltyProgramCreate](#loyaltyprogramcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LoyaltyProgramRead](#loyaltyprogramread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/crm/crm/loyalty/programs" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/crm/crm/loyalty/programs/{program_id} {#get-apimodulescrmcrmloyaltyprogramsprogram-id}

**Resumo:** Get Loyalty Program
**Descrição:** Get a loyalty program by ID.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `program_id` | string | path | ✅ | The ID of the loyalty program to retrieve |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LoyaltyProgramRead](#loyaltyprogramread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/loyalty/programs/{program_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/crm/crm/loyalty/programs/{program_id} {#put-apimodulescrmcrmloyaltyprogramsprogram-id}

**Resumo:** Update Loyalty Program
**Descrição:** Update a loyalty program.

Requires tenant owner or manager role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `program_id` | string | path | ✅ | The ID of the loyalty program to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [LoyaltyProgramUpdate](#loyaltyprogramupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LoyaltyProgramRead](#loyaltyprogramread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/crm/crm/loyalty/programs/{program_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/crm/crm/loyalty/transactions {#post-apimodulescrmcrmloyaltytransactions}

**Resumo:** Create Loyalty Transaction
**Descrição:** Create a new loyalty transaction (earn or redeem points).

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [LoyaltyTransactionCreate](#loyaltytransactioncreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LoyaltyTransactionRead](#loyaltytransactionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/crm/crm/loyalty/transactions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/crm/crm/loyalty/transactions/membership/{membership_id} {#get-apimodulescrmcrmloyaltytransactionsmembershipmembership-id}

**Resumo:** Get Membership Transactions
**Descrição:** Get all transactions for a specific loyalty membership.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `membership_id` | string | path | ✅ | The ID of the membership to get transactions for |
| `skip` | integer | query | ❌ | Number of transactions to skip |
| `limit` | integer | query | ❌ | Maximum number of transactions to return |
| `transaction_type` | string | query | ❌ | Filter by transaction type |
| `start_date` | string | query | ❌ | Filter by start date (inclusive) |
| `end_date` | string | query | ❌ | Filter by end date (inclusive) |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/loyalty/transactions/membership/{membership_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
