"""
Media Directory Service

Serviço para gerenciamento de diretórios de mídia.
"""

import logging
from typing import Optional, List
from uuid import UUID

from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from ..models import MediaDirectory, MediaContext
from ..schemas import MediaDirectoryCreate, MediaDirectoryRead

logger = logging.getLogger(__name__)


class MediaDirectoryService:
    """Serviço para gerenciamento de diretórios de mídia."""

    def __init__(self, db: Session):
        self.db = db

    def create_directory(
        self,
        directory_data: MediaDirectoryCreate
    ) -> Optional[MediaDirectory]:
        """
        Cria um novo diretório de mídia.
        
        Args:
            directory_data: Dados do diretório
            
        Returns:
            MediaDirectory: Diretório criado ou None
        """
        try:
            # Verifica se o contexto existe
            context = self.db.query(MediaContext).filter(
                MediaContext.id == directory_data.context_id
            ).first()

            if not context:
                logger.error(f"Contexto não encontrado: {directory_data.context_id}")
                return None

            # Verifica se o diretório pai existe (se especificado)
            if directory_data.parent_id:
                parent = self.get_directory(directory_data.parent_id)
                if not parent:
                    logger.error(f"Diretório pai não encontrado: {directory_data.parent_id}")
                    return None

                # Verifica se o pai pertence ao mesmo contexto
                if parent.context_id != directory_data.context_id:
                    logger.error("Diretório pai deve pertencer ao mesmo contexto")
                    return None

            directory = MediaDirectory(**directory_data.model_dump())
            self.db.add(directory)
            self.db.commit()
            self.db.refresh(directory)
            
            logger.info(f"Diretório criado: {directory.id} - {directory.name}")
            return directory

        except IntegrityError as e:
            self.db.rollback()
            logger.error(f"Erro de integridade ao criar diretório: {e}")
            return None

        except Exception as e:
            self.db.rollback()
            logger.error(f"Erro ao criar diretório: {e}")
            return None

    def get_directory(self, directory_id: UUID) -> Optional[MediaDirectory]:
        """
        Obtém um diretório por ID.
        
        Args:
            directory_id: ID do diretório
            
        Returns:
            MediaDirectory ou None
        """
        return self.db.query(MediaDirectory).filter(
            MediaDirectory.id == directory_id
        ).first()

    def get_directories_by_context(
        self,
        context_id: UUID,
        parent_id: Optional[UUID] = None
    ) -> List[MediaDirectory]:
        """
        Lista diretórios por contexto e pai.
        
        Args:
            context_id: ID do contexto
            parent_id: ID do diretório pai (None para raiz)
            
        Returns:
            List[MediaDirectory]: Lista de diretórios
        """
        query = self.db.query(MediaDirectory).filter(
            MediaDirectory.context_id == context_id
        )

        if parent_id is None:
            query = query.filter(MediaDirectory.parent_id.is_(None))
        else:
            query = query.filter(MediaDirectory.parent_id == parent_id)

        return query.order_by(MediaDirectory.name).all()

    def get_directory_tree(self, context_id: UUID) -> List[MediaDirectoryRead]:
        """
        Obtém árvore completa de diretórios de um contexto.
        
        Args:
            context_id: ID do contexto
            
        Returns:
            List[MediaDirectoryRead]: Árvore de diretórios
        """
        # Busca todos os diretórios do contexto
        all_directories = self.db.query(MediaDirectory).filter(
            MediaDirectory.context_id == context_id
        ).order_by(MediaDirectory.name).all()

        # Organiza em árvore
        directory_map = {d.id: MediaDirectoryRead.from_orm(d) for d in all_directories}
        root_directories = []

        for directory in all_directories:
            dir_read = directory_map[directory.id]
            
            if directory.parent_id is None:
                root_directories.append(dir_read)
            else:
                parent = directory_map.get(directory.parent_id)
                if parent:
                    parent.children.append(dir_read)

        return root_directories

    def update_directory(
        self,
        directory_id: UUID,
        name: Optional[str] = None,
        parent_id: Optional[UUID] = None
    ) -> Optional[MediaDirectory]:
        """
        Atualiza um diretório.
        
        Args:
            directory_id: ID do diretório
            name: Novo nome (opcional)
            parent_id: Novo pai (opcional)
            
        Returns:
            MediaDirectory atualizado ou None
        """
        try:
            directory = self.get_directory(directory_id)
            if not directory:
                return None

            # Atualiza nome se fornecido
            if name is not None:
                directory.name = name
                # Atualiza path baseado no novo nome
                if directory.parent_id:
                    parent = self.get_directory(directory.parent_id)
                    if parent:
                        directory.path = f"{parent.path}/{name}"
                else:
                    directory.path = f"/{name}"

            # Atualiza pai se fornecido
            if parent_id is not None:
                # Verifica se o novo pai existe e pertence ao mesmo contexto
                if parent_id != directory.id:  # Evita ciclo
                    parent = self.get_directory(parent_id)
                    if parent and parent.context_id == directory.context_id:
                        directory.parent_id = parent_id
                        directory.path = f"{parent.path}/{directory.name}"

            self.db.commit()
            self.db.refresh(directory)
            
            logger.info(f"Diretório atualizado: {directory_id}")
            return directory

        except Exception as e:
            self.db.rollback()
            logger.error(f"Erro ao atualizar diretório: {e}")
            return None

    def delete_directory(self, directory_id: UUID, force: bool = False) -> bool:
        """
        Remove um diretório.
        
        Args:
            directory_id: ID do diretório
            force: Se True, remove mesmo com conteúdo
            
        Returns:
            bool: True se removido com sucesso
        """
        try:
            directory = self.get_directory(directory_id)
            if not directory:
                return False

            # Verifica se tem filhos
            children = self.get_directories_by_context(
                directory.context_id, 
                directory.id
            )

            if children and not force:
                logger.error(f"Diretório {directory_id} tem subdiretórios")
                return False

            # Verifica se tem uploads
            from ..models import MediaUpload
            uploads = self.db.query(MediaUpload).filter(
                MediaUpload.directory_id == directory_id
            ).count()

            if uploads > 0 and not force:
                logger.error(f"Diretório {directory_id} tem uploads")
                return False

            # Remove diretório (cascade remove filhos e uploads se force=True)
            self.db.delete(directory)
            self.db.commit()
            
            logger.info(f"Diretório removido: {directory_id}")
            return True

        except Exception as e:
            self.db.rollback()
            logger.error(f"Erro ao remover diretório: {e}")
            return False

    def move_directory(
        self,
        directory_id: UUID,
        new_parent_id: Optional[UUID]
    ) -> bool:
        """
        Move um diretório para outro pai.
        
        Args:
            directory_id: ID do diretório a mover
            new_parent_id: ID do novo pai (None para raiz)
            
        Returns:
            bool: True se movido com sucesso
        """
        try:
            directory = self.get_directory(directory_id)
            if not directory:
                return False

            # Verifica se o novo pai existe e pertence ao mesmo contexto
            if new_parent_id is not None:
                new_parent = self.get_directory(new_parent_id)
                if not new_parent:
                    logger.error(f"Novo pai não encontrado: {new_parent_id}")
                    return False

                if new_parent.context_id != directory.context_id:
                    logger.error("Novo pai deve pertencer ao mesmo contexto")
                    return False

                # Evita ciclo (mover para um descendente)
                if self._is_descendant(new_parent_id, directory_id):
                    logger.error("Não é possível mover para um descendente")
                    return False

                # Atualiza path
                directory.path = f"{new_parent.path}/{directory.name}"
            else:
                # Move para raiz
                directory.path = f"/{directory.name}"

            directory.parent_id = new_parent_id
            self.db.commit()
            
            logger.info(f"Diretório movido: {directory_id}")
            return True

        except Exception as e:
            self.db.rollback()
            logger.error(f"Erro ao mover diretório: {e}")
            return False

    def _is_descendant(self, potential_ancestor_id: UUID, directory_id: UUID) -> bool:
        """
        Verifica se um diretório é descendente de outro.
        
        Args:
            potential_ancestor_id: ID do potencial ancestral
            directory_id: ID do diretório
            
        Returns:
            bool: True se é descendente
        """
        current = self.get_directory(potential_ancestor_id)
        
        while current and current.parent_id:
            if current.parent_id == directory_id:
                return True
            current = self.get_directory(current.parent_id)
        
        return False

    def get_directory_stats(self, directory_id: UUID) -> dict:
        """
        Obtém estatísticas de um diretório.
        
        Args:
            directory_id: ID do diretório
            
        Returns:
            dict: Estatísticas do diretório
        """
        try:
            from ..models import MediaUpload
            
            # Conta uploads diretos
            direct_uploads = self.db.query(MediaUpload).filter(
                MediaUpload.directory_id == directory_id
            ).count()

            # Conta subdiretórios
            subdirectories = len(self.get_directories_by_context(
                self.get_directory(directory_id).context_id,
                directory_id
            ))

            # Calcula tamanho total (incluindo subdiretórios)
            total_size = self._calculate_directory_size(directory_id)

            return {
                "direct_uploads": direct_uploads,
                "subdirectories": subdirectories,
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2)
            }

        except Exception as e:
            logger.error(f"Erro ao obter estatísticas do diretório: {e}")
            return {}

    def _calculate_directory_size(self, directory_id: UUID) -> int:
        """
        Calcula o tamanho total de um diretório recursivamente.
        
        Args:
            directory_id: ID do diretório
            
        Returns:
            int: Tamanho total em bytes
        """
        from ..models import MediaUpload
        
        total_size = 0
        
        # Soma uploads diretos
        uploads = self.db.query(MediaUpload).filter(
            MediaUpload.directory_id == directory_id
        ).all()
        
        for upload in uploads:
            total_size += upload.file_size

        # Soma subdiretórios recursivamente
        subdirectories = self.get_directories_by_context(
            self.get_directory(directory_id).context_id,
            directory_id
        )
        
        for subdir in subdirectories:
            total_size += self._calculate_directory_size(subdir.id)

        return total_size
