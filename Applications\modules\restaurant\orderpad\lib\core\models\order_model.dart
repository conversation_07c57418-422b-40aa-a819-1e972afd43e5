import 'package:hive/hive.dart';

part 'order_model.g.dart';

@HiveType(typeId: 0)
class OrderModel extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String tableNumber;

  @HiveField(2)
  List<OrderItem> items;

  @HiveField(3)
  double totalAmount;

  @HiveField(4)
  String status;

  @HiveField(5)
  DateTime createdAt;

  @HiveField(6)
  DateTime? updatedAt;

  @HiveField(7)
  String? notes;

  @HiveField(8)
  String? customerName;

  @HiveField(9)
  String orderType; // dine_in, takeaway, delivery

  @HiveField(10)
  String paymentMethod; // cash, card, pix

  @HiveField(11)
  String? deliveryAddress;

  OrderModel({
    required this.id,
    required this.tableNumber,
    required this.items,
    required this.totalAmount,
    required this.status,
    required this.createdAt,
    this.updatedAt,
    this.notes,
    this.customerName,
    this.orderType = 'dine_in',
    this.paymentMethod = 'cash',
    this.deliveryAddress,
  });

  factory OrderModel.fromJson(Map<String, dynamic> json) {
    return OrderModel(
      id: json['id'],
      tableNumber: json['tableNumber'],
      items: (json['items'] as List)
          .map((item) => OrderItem.fromJson(item))
          .toList(),
      totalAmount: json['totalAmount'].toDouble(),
      status: json['status'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
      notes: json['notes'],
      customerName: json['customerName'],
      orderType: json['orderType'] ?? 'dine_in',
      paymentMethod: json['paymentMethod'] ?? 'cash',
      deliveryAddress: json['deliveryAddress'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'tableNumber': tableNumber,
      'items': items.map((item) => item.toJson()).toList(),
      'totalAmount': totalAmount,
      'status': status,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'notes': notes,
      'customerName': customerName,
      'orderType': orderType,
      'paymentMethod': paymentMethod,
      'deliveryAddress': deliveryAddress,
    };
  }
}

@HiveType(typeId: 1)
class OrderItem extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String name;

  @HiveField(2)
  double price;

  @HiveField(3)
  int quantity;

  @HiveField(4)
  String? notes;

  @HiveField(5)
  List<String>? modifiers;

  OrderItem({
    required this.id,
    required this.name,
    required this.price,
    required this.quantity,
    this.notes,
    this.modifiers,
  });

  factory OrderItem.fromJson(Map<String, dynamic> json) {
    return OrderItem(
      id: json['id'],
      name: json['name'],
      price: json['price'].toDouble(),
      quantity: json['quantity'],
      notes: json['notes'],
      modifiers: json['modifiers'] != null 
          ? List<String>.from(json['modifiers']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'price': price,
      'quantity': quantity,
      'notes': notes,
      'modifiers': modifiers,
    };
  }

  double get totalPrice => price * quantity;
}