import 'package:hive/hive.dart';
import 'menu_item_model.dart';

part 'order_model.g.dart';

enum OrderStatus {
  pending,
  preparing,
  ready,
  delivered,
  completed,
  cancelled,
}

enum OrderType {
  dineIn,
  takeaway,
  delivery,
}

@HiveType(typeId: 0)
class OrderModel extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String tableNumber;

  @HiveField(2)
  List<OrderItem> items;

  @HiveField(3)
  double totalAmount;

  @HiveField(4)
  String status;

  @HiveField(5)
  DateTime createdAt;

  @HiveField(6)
  DateTime? updatedAt;

  @HiveField(7)
  String? notes;

  @HiveField(8)
  String? customerName;

  @HiveField(9)
  String orderType; // dine_in, takeaway, delivery

  @HiveField(10)
  String paymentMethod; // cash, card, pix

  @HiveField(11)
  String? deliveryAddress;

  @HiveField(12)
  double discount;

  OrderModel({
    required this.id,
    required this.tableNumber,
    required this.items,
    required this.totalAmount,
    required this.status,
    required this.createdAt,
    this.updatedAt,
    this.notes,
    this.customerName,
    this.orderType = 'dine_in',
    this.paymentMethod = 'cash',
    this.discount = 0.0,
    this.deliveryAddress,
  });

  factory OrderModel.fromJson(Map<String, dynamic> json) {
    return OrderModel(
      id: json['id'],
      tableNumber: json['tableNumber'],
      items: (json['items'] as List)
          .map((item) => OrderItem.fromJson(item))
          .toList(),
      totalAmount: json['totalAmount'].toDouble(),
      status: json['status'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
      notes: json['notes'],
      customerName: json['customerName'],
      orderType: json['orderType'] ?? 'dine_in',
      paymentMethod: json['paymentMethod'] ?? 'cash',
      deliveryAddress: json['deliveryAddress'],
      discount: json['discount']?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'tableNumber': tableNumber,
      'items': items.map((item) => item.toJson()).toList(),
      'totalAmount': totalAmount,
      'status': status,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'notes': notes,
      'customerName': customerName,
      'orderType': orderType,
      'paymentMethod': paymentMethod,
      'deliveryAddress': deliveryAddress,
      'discount': discount,
    };
  }

  String get typeDisplayName {
    switch (orderType) {
      case 'dine_in':
        return 'No Local';
      case 'takeaway':
        return 'Para Viagem';
      case 'delivery':
        return 'Entrega';
      default:
        return 'Desconhecido';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case 'pending':
        return 'Pendente';
      case 'preparing':
        return 'Preparando';
      case 'ready':
        return 'Pronto';
      case 'delivered':
        return 'Entregue';
      case 'completed':
        return 'Concluído';
      case 'cancelled':
        return 'Cancelado';
      default:
        return 'Desconhecido';
    }
  }

  Duration get duration => DateTime.now().difference(createdAt);

  String get orderNumber => '#${id.substring(0, 8).toUpperCase()}';

  int get itemCount => items.fold(0, (sum, item) => sum + item.quantity);

  double get total => items.fold(0.0, (sum, item) => sum + item.totalPrice);

  double get subtotal => items.fold(0.0, (sum, item) => sum + item.totalPrice);

  double get tax => subtotal * 0.1; // 10% tax rate

  OrderModel copyWith({
    String? id,
    String? tableNumber,
    List<OrderItem>? items,
    double? totalAmount,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? notes,
    String? customerName,
    String? orderType,
    String? paymentMethod,
    String? deliveryAddress,
    double? discount,
  }) {
    return OrderModel(
      id: id ?? this.id,
      tableNumber: tableNumber ?? this.tableNumber,
      items: items ?? this.items,
      totalAmount: totalAmount ?? this.totalAmount,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      notes: notes ?? this.notes,
      customerName: customerName ?? this.customerName,
      orderType: orderType ?? this.orderType,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      deliveryAddress: deliveryAddress ?? this.deliveryAddress,
      discount: discount ?? this.discount,
    );
  }

  String get durationText {
    final duration = this.duration;
    if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    } else {
      return '${duration.inMinutes}m';
    }
  }

  String get type => orderType;
}

@HiveType(typeId: 1)
class OrderItem extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String name;

  @HiveField(2)
  double price;

  @HiveField(3)
  int quantity;

  @HiveField(4)
  String? notes;

  @HiveField(5)
  List<String>? modifiers;

  @HiveField(6)
  String? menuItemId;

  @HiveField(7)
  double? unitPrice;

  @HiveField(8)
  List<String>? selectedModifiers;

  OrderItem({
    required this.id,
    required this.name,
    required this.price,
    required this.quantity,
    this.notes,
    this.modifiers,
    this.menuItemId,
    this.unitPrice,
    this.selectedModifiers,
  });

  factory OrderItem.fromJson(Map<String, dynamic> json) {
    return OrderItem(
      id: json['id'],
      name: json['name'],
      price: json['price'].toDouble(),
      quantity: json['quantity'],
      notes: json['notes'],
      modifiers: json['modifiers'] != null 
          ? List<String>.from(json['modifiers']) 
          : null,
      menuItemId: json['menuItemId'],
      unitPrice: json['unitPrice']?.toDouble(),
      selectedModifiers: json['selectedModifiers'] != null 
          ? List<String>.from(json['selectedModifiers']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'price': price,
      'quantity': quantity,
      'notes': notes,
      'modifiers': modifiers,
      'menuItemId': menuItemId,
      'unitPrice': unitPrice,
      'selectedModifiers': selectedModifiers,
    };
  }

  OrderItem copyWith({
    String? id,
    String? name,
    double? price,
    int? quantity,
    String? notes,
    List<String>? modifiers,
    String? menuItemId,
    double? unitPrice,
    List<String>? selectedModifiers,
  }) {
    return OrderItem(
      id: id ?? this.id,
      name: name ?? this.name,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      notes: notes ?? this.notes,
      modifiers: modifiers ?? this.modifiers,
      menuItemId: menuItemId ?? this.menuItemId,
      unitPrice: unitPrice ?? this.unitPrice,
      selectedModifiers: selectedModifiers ?? this.selectedModifiers,
    );
  }

  double get totalPrice => price * quantity;

  // Getter para compatibilidade com código existente
  MenuItemModel get menuItem {
    return MenuItemModel(
      id: menuItemId ?? id,
      name: name,
      description: '',
      price: unitPrice ?? price,
      category: '',
      imageUrl: '',
      isAvailable: true,
      preparationTime: 0,
      modifiers: [],
      createdAt: DateTime.now(),
    );
  }
}