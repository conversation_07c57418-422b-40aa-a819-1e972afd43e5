# Shared - Inventory

**Categoria:** Shared
**<PERSON><PERSON><PERSON><PERSON>:** Inventory
**Total de Endpoints:** 11
**Gerado em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [GET /api/modules/inventory/categories](#get-apimodulesinventorycategories) - Listar Categorias de Inventário
- [POST /api/modules/inventory/categories](#post-apimodulesinventorycategories) - Criar Categoria de Inventário
- [DELETE /api/modules/inventory/categories/{category_id}](#delete-apimodulesinventorycategoriescategory-id) - Excluir Categoria de Inventário
- [GET /api/modules/inventory/categories/{category_id}](#get-apimodulesinventorycategoriescategory-id) - Obter Categoria de Inventário
- [PUT /api/modules/inventory/categories/{category_id}](#put-apimodulesinventorycategoriescategory-id) - Atualizar Categoria de Inventário
- [GET /api/modules/inventory/items](#get-apimodulesinventoryitems) - Listar Itens de Inventário
- [POST /api/modules/inventory/items](#post-apimodulesinventoryitems) - Criar Item de Inventário
- [GET /api/modules/inventory/items/{item_id}](#get-apimodulesinventoryitemsitem-id) - Obter Item de Inventário
- [PUT /api/modules/inventory/items/{item_id}](#put-apimodulesinventoryitemsitem-id) - Atualizar Item de Inventário
- [POST /api/modules/inventory/items/{item_id}/adjust-stock](#post-apimodulesinventoryitemsitem-idadjust-stock) - Ajustar Estoque do Item
- [POST /api/modules/inventory/sync-to-shopping-list](#post-apimodulesinventorysync-to-shopping-list) - Sincronizar com Lista de Compras

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### InventoryCategoryCreate

**Descrição:** Schema para criação de categoria de inventário.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `description` | unknown | ❌ | - |
| `display_order` | integer | ❌ | - |
| `is_active` | boolean | ❌ | - |

### InventoryCategoryRead

**Descrição:** Schema para leitura de categoria de inventário.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `description` | unknown | ❌ | - |
| `display_order` | integer | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### InventoryCategoryUpdate

**Descrição:** Schema para atualização de categoria de inventário.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `display_order` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |

### InventoryItemAdjustStock

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `change` | integer | ✅ | A mudança na quantidade (+ para adicionar, - para remover). |

### InventoryItemCreate

**Descrição:** Schema para criar um novo item de inventário. tenant_id será adicionado pelo serviço.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Nome do item de inventário. |
| `description` | unknown | ❌ | Descrição detalhada do item. |
| `sku` | unknown | ❌ | SKU (Stock Keeping Unit) do item. |
| `category_id` | unknown | ❌ | ID da categoria do item. |
| `quantity` | integer | ❌ | Quantidade atual em estoque. |
| `unit_cost` | unknown | ❌ | Custo unitário do item. |

### InventoryItemRead

**Descrição:** Schema para retornar dados de um item de inventário ao cliente.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Nome do item de inventário. |
| `description` | unknown | ❌ | Descrição detalhada do item. |
| `sku` | unknown | ❌ | SKU (Stock Keeping Unit) do item. |
| `category_id` | unknown | ❌ | ID da categoria do item. |
| `quantity` | integer | ❌ | Quantidade atual em estoque. |
| `unit_cost` | unknown | ❌ | Custo unitário do item. |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### InventoryItemUpdate

**Descrição:** Schema para atualizar um item de inventário. Todos os campos são opcionais.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | Novo nome do item. |
| `description` | unknown | ❌ | Nova descrição do item. |
| `sku` | unknown | ❌ | Novo SKU do item. |
| `category_id` | unknown | ❌ | Novo ID da categoria. |
| `unit_cost` | unknown | ❌ | Novo custo unitário. |

## 🔗 Endpoints Detalhados

### GET /api/modules/inventory/categories {#get-apimodulesinventorycategories}

**Resumo:** Listar Categorias de Inventário
**Descrição:** Lista as categorias de inventário do tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `active_only` | boolean | query | ❌ | Apenas categorias ativas |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/inventory/categories" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/inventory/categories {#post-apimodulesinventorycategories}

**Resumo:** Criar Categoria de Inventário
**Descrição:** Cria uma nova categoria de inventário.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [InventoryCategoryCreate](#inventorycategorycreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [InventoryCategoryRead](#inventorycategoryread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/inventory/categories" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/inventory/categories/{category_id} {#delete-apimodulesinventorycategoriescategory-id}

**Resumo:** Excluir Categoria de Inventário
**Descrição:** Exclui uma categoria (soft delete).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [InventoryCategoryRead](#inventorycategoryread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/inventory/categories/{category_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/inventory/categories/{category_id} {#get-apimodulesinventorycategoriescategory-id}

**Resumo:** Obter Categoria de Inventário
**Descrição:** Obtém detalhes de uma categoria específica.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [InventoryCategoryRead](#inventorycategoryread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/inventory/categories/{category_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/inventory/categories/{category_id} {#put-apimodulesinventorycategoriescategory-id}

**Resumo:** Atualizar Categoria de Inventário
**Descrição:** Atualiza dados de uma categoria.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [InventoryCategoryUpdate](#inventorycategoryupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [InventoryCategoryRead](#inventorycategoryread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/inventory/categories/{category_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/inventory/items {#get-apimodulesinventoryitems}

**Resumo:** Listar Itens de Inventário
**Descrição:** Lista os itens de inventário pertencentes ao tenant atual, com filtros opcionais.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | Número de itens a pular |
| `limit` | integer | query | ❌ | Número máximo de itens a retornar |
| `name` | string | query | ❌ | Filtrar por nome (busca parcial, case-insensitive) |
| `sku` | string | query | ❌ | Filtrar por SKU (busca exata) |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/inventory/items" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/inventory/items {#post-apimodulesinventoryitems}

**Resumo:** Criar Item de Inventário
**Descrição:** Cria um novo item de inventário para o tenant atual.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [InventoryItemCreate](#inventoryitemcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [InventoryItemRead](#inventoryitemread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/inventory/items" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/inventory/items/{item_id} {#get-apimodulesinventoryitemsitem-id}

**Resumo:** Obter Item de Inventário
**Descrição:** Obtém os detalhes de um item de inventário específico pelo seu ID, pertencente ao tenant atual.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `item_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [InventoryItemRead](#inventoryitemread)
**404:** Item não encontrado para este tenant
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/inventory/items/{item_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/inventory/items/{item_id} {#put-apimodulesinventoryitemsitem-id}

**Resumo:** Atualizar Item de Inventário
**Descrição:** Atualiza os detalhes de um item de inventário (exceto quantidade). A quantidade deve ser ajustada via endpoint específico.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `item_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [InventoryItemUpdate](#inventoryitemupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [InventoryItemRead](#inventoryitemread)
**404:** Item não encontrado para este tenant
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/inventory/items/{item_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/inventory/items/{item_id}/adjust-stock {#post-apimodulesinventoryitemsitem-idadjust-stock}

**Resumo:** Ajustar Estoque do Item
**Descrição:** Ajusta a quantidade em estoque de um item específico. Use um valor positivo para adicionar e negativo para remover.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `item_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [InventoryItemAdjustStock](#inventoryitemadjuststock)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [InventoryItemRead](#inventoryitemread)
**404:** Item não encontrado para este tenant
**400:** Ajuste inválido (ex: resultaria em estoque negativo)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/inventory/items/{item_id}/adjust-stock" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/inventory/sync-to-shopping-list {#post-apimodulesinventorysync-to-shopping-list}

**Resumo:** Sincronizar com Lista de Compras
**Descrição:** Automaticamente adiciona itens com estoque baixo à lista de compras.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `threshold` | integer | query | ❌ | Limite de estoque baixo |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/inventory/sync-to-shopping-list" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
