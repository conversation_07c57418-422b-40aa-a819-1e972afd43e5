"""add_b2b_fields_to_invoices

Revision ID: cdf903a1f729
Revises: e8f9a2b1c4d5
Create Date: 2025-06-27 13:34:38.470835

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'cdf903a1f729'
down_revision: Union[str, None] = 'e8f9a2b1c4d5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('eshop_tcostumers',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('tenant_user_association_id', sa.UUID(), nullable=True),
    sa.Column('status', sa.Enum('PENDING', 'ACTIVE', 'SUSPENDED', 'INACTIVE', 'REJECTED', name='tcostumerstatus'), nullable=False),
    sa.Column('company_name', sa.String(length=255), nullable=False),
    sa.Column('business_type', sa.Enum('CORPORATION', 'LLC', 'PARTNERSHIP', 'SOLE_PROPRIETORSHIP', 'NONPROFIT', 'GOVERNMENT', 'OTHER', name='businesstype'), nullable=False),
    sa.Column('tax_id', sa.String(length=50), nullable=False),
    sa.Column('business_registration_number', sa.String(length=100), nullable=True),
    sa.Column('business_address', sa.JSON(), nullable=True),
    sa.Column('business_phone', sa.String(length=20), nullable=True),
    sa.Column('business_email', sa.String(length=255), nullable=True),
    sa.Column('website', sa.String(length=255), nullable=True),
    sa.Column('credit_limit', sa.Numeric(precision=12, scale=2), nullable=False),
    sa.Column('available_credit', sa.Numeric(precision=12, scale=2), nullable=False),
    sa.Column('credit_used', sa.Numeric(precision=12, scale=2), nullable=False),
    sa.Column('payment_terms', sa.Enum('IMMEDIATE', 'NET_15', 'NET_30', 'NET_45', 'NET_60', 'CUSTOM', name='paymentterms'), nullable=False),
    sa.Column('custom_payment_days', sa.Integer(), nullable=True),
    sa.Column('default_discount_rate', sa.Numeric(precision=5, scale=2), nullable=False),
    sa.Column('business_verification_status', sa.String(length=20), nullable=False),
    sa.Column('business_verification_date', sa.DateTime(), nullable=True),
    sa.Column('business_verification_notes', sa.Text(), nullable=True),
    sa.Column('verification_documents', sa.JSON(), nullable=True),
    sa.Column('annual_revenue', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('employee_count', sa.Integer(), nullable=True),
    sa.Column('years_in_business', sa.Integer(), nullable=True),
    sa.Column('total_orders', sa.Integer(), nullable=False),
    sa.Column('total_spent', sa.Numeric(precision=12, scale=2), nullable=False),
    sa.Column('average_order_value', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('last_order_date', sa.DateTime(), nullable=True),
    sa.Column('auto_approve_orders', sa.Boolean(), nullable=False),
    sa.Column('require_po_number', sa.Boolean(), nullable=False),
    sa.Column('sales_rep_id', sa.UUID(), nullable=True),
    sa.Column('additional_metadata', sa.JSON(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('approved_at', sa.DateTime(), nullable=True),
    sa.Column('last_activity_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['sales_rep_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_eshop_tcostumers_business_verification_status'), 'eshop_tcostumers', ['business_verification_status'], unique=False)
    op.create_index(op.f('ix_eshop_tcostumers_company_name'), 'eshop_tcostumers', ['company_name'], unique=False)
    op.create_index(op.f('ix_eshop_tcostumers_sales_rep_id'), 'eshop_tcostumers', ['sales_rep_id'], unique=False)
    op.create_index(op.f('ix_eshop_tcostumers_status'), 'eshop_tcostumers', ['status'], unique=False)
    op.create_index(op.f('ix_eshop_tcostumers_tax_id'), 'eshop_tcostumers', ['tax_id'], unique=True)
    op.create_index(op.f('ix_eshop_tcostumers_tenant_id'), 'eshop_tcostumers', ['tenant_id'], unique=False)
    op.create_index(op.f('ix_eshop_tcostumers_tenant_user_association_id'), 'eshop_tcostumers', ['tenant_user_association_id'], unique=False)
    op.create_index(op.f('ix_eshop_tcostumers_user_id'), 'eshop_tcostumers', ['user_id'], unique=False)
    op.create_index('ix_tcostumer_company_name', 'eshop_tcostumers', ['company_name'], unique=False)
    op.create_index('ix_tcostumer_created', 'eshop_tcostumers', ['created_at'], unique=False)
    op.create_index('ix_tcostumer_sales_rep', 'eshop_tcostumers', ['sales_rep_id'], unique=False)
    op.create_index('ix_tcostumer_status_tenant', 'eshop_tcostumers', ['status', 'tenant_id'], unique=False)
    op.create_index('ix_tcostumer_tenant_user', 'eshop_tcostumers', ['tenant_id', 'user_id'], unique=False)
    op.create_index('ix_tcostumer_verification_status', 'eshop_tcostumers', ['business_verification_status'], unique=False)
    op.create_table('eshop_tvendor_suppliers',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('tenant_user_association_id', sa.UUID(), nullable=True),
    sa.Column('status', sa.Enum('PENDING', 'ACTIVE', 'SUSPENDED', 'INACTIVE', 'REJECTED', 'UNDER_REVIEW', name='tvendorstatus'), nullable=False),
    sa.Column('company_name', sa.String(length=255), nullable=False),
    sa.Column('supplier_type', sa.Enum('MANUFACTURER', 'DISTRIBUTOR', 'WHOLESALER', 'RETAILER', 'DROPSHIPPER', 'SERVICE_PROVIDER', 'OTHER', name='suppliertype'), nullable=False),
    sa.Column('tax_id', sa.String(length=50), nullable=False),
    sa.Column('business_registration_number', sa.String(length=100), nullable=True),
    sa.Column('business_address', sa.JSON(), nullable=True),
    sa.Column('business_phone', sa.String(length=20), nullable=True),
    sa.Column('business_email', sa.String(length=255), nullable=True),
    sa.Column('website', sa.String(length=255), nullable=True),
    sa.Column('verification_status', sa.Enum('PENDING', 'DOCUMENTS_SUBMITTED', 'UNDER_REVIEW', 'VERIFIED', 'REJECTED', 'EXPIRED', name='verificationstatus'), nullable=False),
    sa.Column('verification_date', sa.DateTime(), nullable=True),
    sa.Column('verification_notes', sa.Text(), nullable=True),
    sa.Column('verification_expires_at', sa.DateTime(), nullable=True),
    sa.Column('verification_documents', sa.JSON(), nullable=True),
    sa.Column('commission_rate', sa.Numeric(precision=5, scale=2), nullable=False),
    sa.Column('commission_type', sa.String(length=20), nullable=False),
    sa.Column('minimum_commission', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('max_products_allowed', sa.Integer(), nullable=False),
    sa.Column('auto_approve_products', sa.Boolean(), nullable=False),
    sa.Column('require_product_approval', sa.Boolean(), nullable=False),
    sa.Column('annual_revenue', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('employee_count', sa.Integer(), nullable=True),
    sa.Column('years_in_business', sa.Integer(), nullable=True),
    sa.Column('total_products', sa.Integer(), nullable=False),
    sa.Column('active_products', sa.Integer(), nullable=False),
    sa.Column('total_sales', sa.Numeric(precision=12, scale=2), nullable=False),
    sa.Column('total_orders', sa.Integer(), nullable=False),
    sa.Column('average_order_value', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('total_commission_earned', sa.Numeric(precision=12, scale=2), nullable=False),
    sa.Column('customer_rating', sa.Numeric(precision=3, scale=2), nullable=False),
    sa.Column('total_reviews', sa.Integer(), nullable=False),
    sa.Column('fulfillment_rate', sa.Numeric(precision=5, scale=2), nullable=False),
    sa.Column('on_time_delivery_rate', sa.Numeric(precision=5, scale=2), nullable=False),
    sa.Column('can_manage_inventory', sa.Boolean(), nullable=False),
    sa.Column('can_set_pricing', sa.Boolean(), nullable=False),
    sa.Column('can_create_promotions', sa.Boolean(), nullable=False),
    sa.Column('account_manager_id', sa.UUID(), nullable=True),
    sa.Column('payment_terms', sa.String(length=50), nullable=False),
    sa.Column('payment_method', sa.String(length=50), nullable=True),
    sa.Column('bank_account_info', sa.JSON(), nullable=True),
    sa.Column('shipping_zones', sa.JSON(), nullable=True),
    sa.Column('shipping_methods', sa.JSON(), nullable=True),
    sa.Column('processing_time_days', sa.Integer(), nullable=False),
    sa.Column('additional_metadata', sa.JSON(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('approved_at', sa.DateTime(), nullable=True),
    sa.Column('last_activity_at', sa.DateTime(), nullable=True),
    sa.Column('last_sale_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['account_manager_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_eshop_tvendor_suppliers_account_manager_id'), 'eshop_tvendor_suppliers', ['account_manager_id'], unique=False)
    op.create_index(op.f('ix_eshop_tvendor_suppliers_company_name'), 'eshop_tvendor_suppliers', ['company_name'], unique=False)
    op.create_index(op.f('ix_eshop_tvendor_suppliers_status'), 'eshop_tvendor_suppliers', ['status'], unique=False)
    op.create_index(op.f('ix_eshop_tvendor_suppliers_tax_id'), 'eshop_tvendor_suppliers', ['tax_id'], unique=True)
    op.create_index(op.f('ix_eshop_tvendor_suppliers_tenant_id'), 'eshop_tvendor_suppliers', ['tenant_id'], unique=False)
    op.create_index(op.f('ix_eshop_tvendor_suppliers_tenant_user_association_id'), 'eshop_tvendor_suppliers', ['tenant_user_association_id'], unique=False)
    op.create_index(op.f('ix_eshop_tvendor_suppliers_user_id'), 'eshop_tvendor_suppliers', ['user_id'], unique=False)
    op.create_index(op.f('ix_eshop_tvendor_suppliers_verification_status'), 'eshop_tvendor_suppliers', ['verification_status'], unique=False)
    op.create_index('ix_tvendor_account_manager', 'eshop_tvendor_suppliers', ['account_manager_id'], unique=False)
    op.create_index('ix_tvendor_company_name', 'eshop_tvendor_suppliers', ['company_name'], unique=False)
    op.create_index('ix_tvendor_created', 'eshop_tvendor_suppliers', ['created_at'], unique=False)
    op.create_index('ix_tvendor_status_tenant', 'eshop_tvendor_suppliers', ['status', 'tenant_id'], unique=False)
    op.create_index('ix_tvendor_supplier_type', 'eshop_tvendor_suppliers', ['supplier_type'], unique=False)
    op.create_index('ix_tvendor_tenant_user', 'eshop_tvendor_suppliers', ['tenant_id', 'user_id'], unique=False)
    op.create_index('ix_tvendor_verification_status', 'eshop_tvendor_suppliers', ['verification_status'], unique=False)
    op.create_table('shop_product_categories',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('parent_category_id', sa.UUID(), nullable=True),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['parent_category_id'], ['shop_product_categories.id'], ),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_shop_product_categories_parent_category_id'), 'shop_product_categories', ['parent_category_id'], unique=False)
    op.create_index('ix_shop_product_categories_parent_id', 'shop_product_categories', ['parent_category_id'], unique=False)
    op.create_index('ix_shop_product_categories_tenant_id', 'shop_product_categories', ['tenant_id'], unique=False)
    op.create_table('shop_products',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('category_id', sa.UUID(), nullable=True),
    sa.Column('inventory_item_id', sa.UUID(), nullable=True),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('price', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('sku', sa.String(), nullable=True),
    sa.Column('image_url', sa.String(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('is_featured', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['category_id'], ['shop_product_categories.id'], ),
    sa.ForeignKeyConstraint(['inventory_item_id'], ['inventory_items.id'], ),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_shop_products_category_id', 'shop_products', ['category_id'], unique=False)
    op.create_index(op.f('ix_shop_products_inventory_item_id'), 'shop_products', ['inventory_item_id'], unique=True)
    op.create_index('ix_shop_products_sku', 'shop_products', ['sku'], unique=False)
    op.create_index(op.f('ix_shop_products_tenant_id'), 'shop_products', ['tenant_id'], unique=False)
    op.add_column('invoices', sa.Column('vendor_id', sa.UUID(), nullable=True))
    op.add_column('invoices', sa.Column('customer_b2b_id', sa.UUID(), nullable=True))
    op.add_column('invoices', sa.Column('viewed_date', sa.DateTime(), nullable=True))
    op.add_column('invoices', sa.Column('sent_date', sa.DateTime(), nullable=True))
    op.add_column('invoices', sa.Column('file_name', sa.String(length=255), nullable=True))
    op.add_column('invoices', sa.Column('file_original_name', sa.String(length=255), nullable=True))
    op.add_column('invoices', sa.Column('file_path', sa.String(length=500), nullable=True))
    op.add_column('invoices', sa.Column('file_size', sa.Integer(), nullable=True))
    op.add_column('invoices', sa.Column('file_type', sa.String(length=50), nullable=True))
    op.add_column('invoices', sa.Column('file_hash', sa.String(length=64), nullable=True))
    op.add_column('invoices', sa.Column('access_token', sa.String(length=128), nullable=True))
    op.add_column('invoices', sa.Column('access_expires_at', sa.DateTime(), nullable=True))
    op.add_column('invoices', sa.Column('download_count', sa.Integer(), nullable=False))
    op.add_column('invoices', sa.Column('max_downloads', sa.Integer(), nullable=False))
    op.add_column('invoices', sa.Column('notify_on_view', sa.Boolean(), nullable=False))
    op.add_column('invoices', sa.Column('notify_on_due', sa.Boolean(), nullable=False))
    op.add_column('invoices', sa.Column('reminder_sent', sa.Boolean(), nullable=False))
    op.create_index('ix_invoices_access_token', 'invoices', ['access_token'], unique=False)
    op.create_index(op.f('ix_invoices_customer_b2b_id'), 'invoices', ['customer_b2b_id'], unique=False)
    op.create_index('ix_invoices_customer_status', 'invoices', ['customer_b2b_id', 'status'], unique=False)
    op.create_index('ix_invoices_due_date_status', 'invoices', ['due_date', 'status'], unique=False)
    op.create_index('ix_invoices_vendor_customer', 'invoices', ['vendor_id', 'customer_b2b_id'], unique=False)
    op.create_index(op.f('ix_invoices_vendor_id'), 'invoices', ['vendor_id'], unique=False)
    op.create_index('ix_invoices_vendor_status', 'invoices', ['vendor_id', 'status'], unique=False)
    op.create_unique_constraint(None, 'invoices', ['access_token'])
    op.create_foreign_key(None, 'invoices', 'eshop_tvendor_suppliers', ['vendor_id'], ['id'])
    op.create_foreign_key(None, 'invoices', 'eshop_tcostumers', ['customer_b2b_id'], ['id'])
    op.drop_index(op.f('ix_languages_code'), table_name='languages')
    op.create_index(op.f('ix_languages_code'), 'languages', ['code'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_languages_code'), table_name='languages')
    op.create_index(op.f('ix_languages_code'), 'languages', ['code'], unique=False)
    op.drop_constraint(None, 'invoices', type_='foreignkey')
    op.drop_constraint(None, 'invoices', type_='foreignkey')
    op.drop_constraint(None, 'invoices', type_='unique')
    op.drop_index('ix_invoices_vendor_status', table_name='invoices')
    op.drop_index(op.f('ix_invoices_vendor_id'), table_name='invoices')
    op.drop_index('ix_invoices_vendor_customer', table_name='invoices')
    op.drop_index('ix_invoices_due_date_status', table_name='invoices')
    op.drop_index('ix_invoices_customer_status', table_name='invoices')
    op.drop_index(op.f('ix_invoices_customer_b2b_id'), table_name='invoices')
    op.drop_index('ix_invoices_access_token', table_name='invoices')
    op.drop_column('invoices', 'reminder_sent')
    op.drop_column('invoices', 'notify_on_due')
    op.drop_column('invoices', 'notify_on_view')
    op.drop_column('invoices', 'max_downloads')
    op.drop_column('invoices', 'download_count')
    op.drop_column('invoices', 'access_expires_at')
    op.drop_column('invoices', 'access_token')
    op.drop_column('invoices', 'file_hash')
    op.drop_column('invoices', 'file_type')
    op.drop_column('invoices', 'file_size')
    op.drop_column('invoices', 'file_path')
    op.drop_column('invoices', 'file_original_name')
    op.drop_column('invoices', 'file_name')
    op.drop_column('invoices', 'sent_date')
    op.drop_column('invoices', 'viewed_date')
    op.drop_column('invoices', 'customer_b2b_id')
    op.drop_column('invoices', 'vendor_id')
    op.drop_index(op.f('ix_shop_products_tenant_id'), table_name='shop_products')
    op.drop_index('ix_shop_products_sku', table_name='shop_products')
    op.drop_index(op.f('ix_shop_products_inventory_item_id'), table_name='shop_products')
    op.drop_index('ix_shop_products_category_id', table_name='shop_products')
    op.drop_table('shop_products')
    op.drop_index('ix_shop_product_categories_tenant_id', table_name='shop_product_categories')
    op.drop_index('ix_shop_product_categories_parent_id', table_name='shop_product_categories')
    op.drop_index(op.f('ix_shop_product_categories_parent_category_id'), table_name='shop_product_categories')
    op.drop_table('shop_product_categories')
    op.drop_index('ix_tvendor_verification_status', table_name='eshop_tvendor_suppliers')
    op.drop_index('ix_tvendor_tenant_user', table_name='eshop_tvendor_suppliers')
    op.drop_index('ix_tvendor_supplier_type', table_name='eshop_tvendor_suppliers')
    op.drop_index('ix_tvendor_status_tenant', table_name='eshop_tvendor_suppliers')
    op.drop_index('ix_tvendor_created', table_name='eshop_tvendor_suppliers')
    op.drop_index('ix_tvendor_company_name', table_name='eshop_tvendor_suppliers')
    op.drop_index('ix_tvendor_account_manager', table_name='eshop_tvendor_suppliers')
    op.drop_index(op.f('ix_eshop_tvendor_suppliers_verification_status'), table_name='eshop_tvendor_suppliers')
    op.drop_index(op.f('ix_eshop_tvendor_suppliers_user_id'), table_name='eshop_tvendor_suppliers')
    op.drop_index(op.f('ix_eshop_tvendor_suppliers_tenant_user_association_id'), table_name='eshop_tvendor_suppliers')
    op.drop_index(op.f('ix_eshop_tvendor_suppliers_tenant_id'), table_name='eshop_tvendor_suppliers')
    op.drop_index(op.f('ix_eshop_tvendor_suppliers_tax_id'), table_name='eshop_tvendor_suppliers')
    op.drop_index(op.f('ix_eshop_tvendor_suppliers_status'), table_name='eshop_tvendor_suppliers')
    op.drop_index(op.f('ix_eshop_tvendor_suppliers_company_name'), table_name='eshop_tvendor_suppliers')
    op.drop_index(op.f('ix_eshop_tvendor_suppliers_account_manager_id'), table_name='eshop_tvendor_suppliers')
    op.drop_table('eshop_tvendor_suppliers')
    op.drop_index('ix_tcostumer_verification_status', table_name='eshop_tcostumers')
    op.drop_index('ix_tcostumer_tenant_user', table_name='eshop_tcostumers')
    op.drop_index('ix_tcostumer_status_tenant', table_name='eshop_tcostumers')
    op.drop_index('ix_tcostumer_sales_rep', table_name='eshop_tcostumers')
    op.drop_index('ix_tcostumer_created', table_name='eshop_tcostumers')
    op.drop_index('ix_tcostumer_company_name', table_name='eshop_tcostumers')
    op.drop_index(op.f('ix_eshop_tcostumers_user_id'), table_name='eshop_tcostumers')
    op.drop_index(op.f('ix_eshop_tcostumers_tenant_user_association_id'), table_name='eshop_tcostumers')
    op.drop_index(op.f('ix_eshop_tcostumers_tenant_id'), table_name='eshop_tcostumers')
    op.drop_index(op.f('ix_eshop_tcostumers_tax_id'), table_name='eshop_tcostumers')
    op.drop_index(op.f('ix_eshop_tcostumers_status'), table_name='eshop_tcostumers')
    op.drop_index(op.f('ix_eshop_tcostumers_sales_rep_id'), table_name='eshop_tcostumers')
    op.drop_index(op.f('ix_eshop_tcostumers_company_name'), table_name='eshop_tcostumers')
    op.drop_index(op.f('ix_eshop_tcostumers_business_verification_status'), table_name='eshop_tcostumers')
    op.drop_table('eshop_tcostumers')
    # ### end Alembic commands ###
