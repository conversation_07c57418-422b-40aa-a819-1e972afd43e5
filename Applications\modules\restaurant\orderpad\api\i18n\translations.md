# I18N - Translations

**Categoria:** I18N
**Módulo:** Translations
**Total de Endpoints:** 5
**Gerado em:** 08/06/2025, 17:26:01

## 📋 Endpoints

- [GET /api/i18n/translations/](#get-apii18ntranslations) - Read Translations
- [POST /api/i18n/translations/](#post-apii18ntranslations) - Create Translation
- [DELETE /api/i18n/translations/{translation_id}](#delete-apii18ntranslationstranslation-id) - Delete Translation
- [GET /api/i18n/translations/{translation_id}](#get-apii18ntranslationstranslation-id) - Read Translation
- [PUT /api/i18n/translations/{translation_id}](#put-apii18ntranslationstranslation-id) - Update Translation

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### TranslationCreate

**Descrição:** Schema for creating a new Translation.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `key` | string | ✅ | Translation key |
| `text` | string | ✅ | Translated text |
| `language_id` | string | ✅ | ID of the language |
| `sector` | string | ❌ | Sector/namespace for the translation (e.g., 'menu', 'auth', 'common') |

### TranslationRead

**Descrição:** Schema for reading a Translation.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `key` | string | ✅ | Translation key |
| `text` | string | ✅ | Translated text |
| `language_id` | string | ✅ | ID of the language |
| `sector` | string | ❌ | Sector/namespace for the translation (e.g., 'menu', 'auth', 'common') |
| `id` | string | ✅ | - |
| `last_updated_by_id` | unknown | ❌ | - |

### TranslationUpdate

**Descrição:** Schema for updating an existing Translation.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `key` | unknown | ❌ | - |
| `text` | unknown | ❌ | - |
| `language_id` | unknown | ❌ | - |
| `sector` | unknown | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/i18n/translations/ {#get-apii18ntranslations}

**Resumo:** Read Translations
**Descrição:** Retrieve all translations with pagination and optional filtering.
This endpoint is public and does not require authentication.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `language_id` | string | query | ❌ | Filter by language ID |
| `key` | string | query | ❌ | Filter by translation key |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/i18n/translations/"
```

---

### POST /api/i18n/translations/ {#post-apii18ntranslations}

**Resumo:** Create Translation
**Descrição:** Create a new translation.
Requires ADMIN system role.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TranslationCreate](#translationcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TranslationRead](#translationread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/i18n/translations/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/i18n/translations/{translation_id} {#delete-apii18ntranslationstranslation-id}

**Resumo:** Delete Translation
**Descrição:** Delete a translation.
Requires ADMIN system role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `translation_id` | integer | path | ✅ | The ID of the translation to delete |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/i18n/translations/{translation_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/i18n/translations/{translation_id} {#get-apii18ntranslationstranslation-id}

**Resumo:** Read Translation
**Descrição:** Retrieve a specific translation by ID.
This endpoint is public and does not require authentication.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `translation_id` | integer | path | ✅ | The ID of the translation to retrieve |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TranslationRead](#translationread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/i18n/translations/{translation_id}"
```

---

### PUT /api/i18n/translations/{translation_id} {#put-apii18ntranslationstranslation-id}

**Resumo:** Update Translation
**Descrição:** Update a translation.
Requires ADMIN system role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `translation_id` | integer | path | ✅ | The ID of the translation to update |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TranslationUpdate](#translationupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TranslationRead](#translationread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/i18n/translations/{translation_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
