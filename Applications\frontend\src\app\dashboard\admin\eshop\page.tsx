"use client"

import React from 'react'
import { motion } from 'framer-motion'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  ChartBarIcon,
  PlusIcon,
  ClockIcon,
  CurrencyDollarIcon,
  BanknotesIcon,
  UsersIcon,
  ShoppingBagIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline'

export default function EShopAdminDashboardPage() {
    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold text-gray-900">E-Shop Dashboard</h1>
                    <p className="text-gray-600 mt-1">Visão geral das operações do e-commerce</p>
                </div>
                <div className="flex space-x-3">
                    <Button variant="outline" className="flex items-center space-x-2">
                        <ChartBarIcon className="h-4 w-4" />
                        <span>Relatórios</span>
                    </Button>
                    <Button className="flex items-center space-x-2">
                        <PlusIcon className="h-4 w-4" />
                        <span>Novo Produto</span>
                    </Button>
                </div>
            </div>

            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                >
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Produtos Pendentes</p>
                                    <p className="text-3xl font-bold text-orange-600">{stats.pendingProducts}</p>
                                    <p className="text-xs text-gray-500 mt-1">Aguardando aprovação</p>
                                </div>
                                <div className="p-3 bg-orange-100 rounded-full">
                                    <ClockIcon className="h-6 w-6 text-orange-600" />
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </motion.div>

                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                >
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Vendas do Mês</p>
                                    <p className="text-3xl font-bold text-green-600">R$ {stats.monthlySales.toLocaleString()}</p>
                                    <p className="text-xs text-green-600 mt-1">+{stats.salesGrowth}% vs mês anterior</p>
                                </div>
                                <div className="p-3 bg-green-100 rounded-full">
                                    <CurrencyDollarIcon className="h-6 w-6 text-green-600" />
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </motion.div>

                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                >
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Comissões Geradas</p>
                                    <p className="text-3xl font-bold text-blue-600">R$ {stats.generatedCommissions.toLocaleString()}</p>
                                    <p className="text-xs text-gray-500 mt-1">Este mês</p>
                                </div>
                                <div className="p-3 bg-blue-100 rounded-full">
                                    <BanknotesIcon className="h-6 w-6 text-blue-600" />
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </motion.div>

                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                >
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Vendedores Ativos</p>
                                    <p className="text-3xl font-bold text-purple-600">{stats.activeSellers}</p>
                                    <p className="text-xs text-gray-500 mt-1">Total cadastrados</p>
                                </div>
                                <div className="p-3 bg-purple-100 rounded-full">
                                    <UsersIcon className="h-6 w-6 text-purple-600" />
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </motion.div>
            </div>

            {/* Quick Actions Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Recent Orders */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <ShoppingBagIcon className="h-5 w-5" />
                            <span>Pedidos Recentes</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {recentOrders.map((order) => (
                                <div key={order.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <p className="font-medium">#{order.orderNumber}</p>
                                        <p className="text-sm text-gray-600">{order.customerName}</p>
                                    </div>
                                    <div className="text-right">
                                        <p className="font-medium">R$ {order.total.toLocaleString()}</p>
                                        <Badge 
                                            variant={order.status === 'pending' ? 'secondary' : 
                                                   order.status === 'confirmed' ? 'default' : 'destructive'}
                                            className="text-xs"
                                        >
                                            {order.status === 'pending' ? 'Pendente' :
                                             order.status === 'confirmed' ? 'Confirmado' : 'Cancelado'}
                                        </Badge>
                                    </div>
                                </div>
                            ))}
                        </div>
                        <div className="mt-4">
                            <Button variant="outline" className="w-full">
                                Ver Todos os Pedidos
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Quick Actions */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Cog6ToothIcon className="h-5 w-5" />
                            <span>Ações Rápidas</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-2 gap-4">
                            <Button 
                                variant="outline" 
                                className="h-20 flex flex-col items-center justify-center space-y-2"
                                onClick={() => router.push('/dashboard/admin/eshop/moderation/approval')}
                            >
                                <ClockIcon className="h-6 w-6" />
                                <span className="text-sm">Aprovar Produtos</span>
                            </Button>
                            
                            <Button 
                                variant="outline" 
                                className="h-20 flex flex-col items-center justify-center space-y-2"
                                onClick={() => router.push('/dashboard/admin/eshop/sellers/list')}
                            >
                                <UsersIcon className="h-6 w-6" />
                                <span className="text-sm">Gerenciar Vendedores</span>
                            </Button>
                            
                            <Button 
                                variant="outline" 
                                className="h-20 flex flex-col items-center justify-center space-y-2"
                                onClick={() => router.push('/dashboard/admin/eshop/orders')}
                            >
                                <ShoppingBagIcon className="h-6 w-6" />
                                <span className="text-sm">Ver Pedidos</span>
                            </Button>
                            
                            <Button 
                                variant="outline" 
                                className="h-20 flex flex-col items-center justify-center space-y-2"
                                onClick={() => router.push('/dashboard/admin/eshop/settings')}
                            >
                                <Cog6ToothIcon className="h-6 w-6" />
                                <span className="text-sm">Configurações</span>
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Performance Chart */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                        <ChartBarIcon className="h-5 w-5" />
                        <span>Performance de Vendas (Últimos 7 dias)</span>
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="h-64 flex items-end justify-between space-x-2">
                        {salesChart.map((day, index) => (
                            <div key={index} className="flex flex-col items-center space-y-2 flex-1">
                                <div 
                                    className="bg-blue-500 rounded-t w-full transition-all duration-300 hover:bg-blue-600"
                                    style={{ height: `${(day.sales / Math.max(...salesChart.map(d => d.sales))) * 200}px` }}
                                ></div>
                                <div className="text-center">
                                    <p className="text-xs font-medium">R$ {day.sales.toLocaleString()}</p>
                                    <p className="text-xs text-gray-500">{day.day}</p>
                                </div>
                            </div>
                        ))}
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}