'use client';

import React, { useState, useCallback, useEffect } from 'react';
import LayoutCanvas from './LayoutCanvas';
import TablePropertiesPanel from './TablePropertiesPanel';
import { Table, TableCreate, TableUpdate } from '@/types/pos';
import { 
  DEFAULT_GRID_CONFIG, 
  ZOOM_LEVELS, 
  CANVAS_DIMENSIONS,
  GridConfig 
} from '@/utils/table/GridPatterns';
import toast from 'react-hot-toast';
import {
  MagnifyingGlassPlusIcon,
  MagnifyingGlassMinusIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';

// ===============================
// INTERFACES
// ===============================

export interface VisualLayoutModeProps {
  tables: Table[];
  zones: string[];
  loading: boolean;
  error: string | null;
  onCreateTable: (data: TableCreate) => Promise<Table | null>;
  onUpdateTable: (id: string, data: TableUpdate) => Promise<Table | null>;
  onDeleteTable: (id: string) => Promise<boolean>;
  filterByZone: (zone: string) => Table[];
  getZoneStatus: (zone: string) => { hasTable: boolean; tableCount: number; isCustom: boolean };
  onViewModeChange: (mode: 'list' | 'visual') => void;
}

// ===============================
// COMPONENT
// ===============================

export default function VisualLayoutMode({
  tables,
  zones,
  loading,
  error,
  onCreateTable,
  onUpdateTable,
  onDeleteTable,
  filterByZone,
  getZoneStatus,
  onViewModeChange
}: VisualLayoutModeProps) {
  const [selectedTable, setSelectedTable] = useState<Table | null>(null);
  const [selectedZone, setSelectedZone] = useState<string>('');
  const [zoom, setZoom] = useState<number>(ZOOM_LEVELS.default);
  const [gridConfig, setGridConfig] = useState<GridConfig>(DEFAULT_GRID_CONFIG);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showPropertiesPanel, setShowPropertiesPanel] = useState(false);

  // Always filter tables by selected zone
  const filteredTables = selectedZone ? filterByZone(selectedZone) : [];

  // Initialize with first zone if none selected
  useEffect(() => {
    if (!selectedZone && zones.length > 0) {
      setSelectedZone(zones[0]);
    }
  }, [selectedZone, zones]);

  const handleTableUpdate = useCallback(async (updatedTable: Table) => {
    try {
      setIsLoading(true);
      
      const updateData: TableUpdate = {
        table_number: updatedTable.table_number,
        name: updatedTable.name,
        capacity: updatedTable.capacity,
        zone: updatedTable.zone || undefined,
        qrcode_enabled: updatedTable.qrcode_enabled,
        position_x: updatedTable.position_x,
        position_y: updatedTable.position_y,
        width: updatedTable.width,
        height: updatedTable.height,
        shape: updatedTable.shape,
        is_active: updatedTable.is_active
      };

      const result = await onUpdateTable(updatedTable.id, updateData);
      if (result) {
        setHasUnsavedChanges(false);
        toast.success('Mesa atualizada com sucesso!');
      }
    } catch (error) {
      toast.error('Erro ao atualizar mesa');
      console.error('Error updating table:', error);
    } finally {
      setIsLoading(false);
    }
  }, [onUpdateTable]);

  const handleTableCreate = useCallback(async (position?: { x: number; y: number }) => {
    try {
      const newTableData: TableCreate = {
        table_number: `${tables.length + 1}`,
        name: `Nova Mesa ${tables.length + 1}`,
        capacity: 4,
        zone: selectedZone,
        qrcode_enabled: true,
        position_x: position?.x || 200,
        position_y: position?.y || 200,
        width: 120,
        height: 80,
        shape: 'rectangle',
        is_active: true
      };

      const result = await onCreateTable(newTableData);
      if (result) {
        setSelectedTable(result);
        setShowPropertiesPanel(true);
        setHasUnsavedChanges(true);
        toast.success('Mesa criada com sucesso!');
      }
    } catch (error) {
      toast.error('Erro ao criar mesa');
      console.error('Error creating table:', error);
    }
  }, [tables.length, selectedZone, onCreateTable]);

  const handleTableSelect = useCallback((table: Table | null) => {
    setSelectedTable(table);
    setShowPropertiesPanel(!!table);
  }, []);

  const handleTableEdit = useCallback((table: Table) => {
    setSelectedTable(table);
    setShowPropertiesPanel(true);
  }, []);

  const handleTableDelete = useCallback(async (table: Table) => {
    if (!confirm(`Tem certeza que deseja deletar a mesa "${table.name}"?`)) return;

    try {
      setIsLoading(true);
      const success = await onDeleteTable(table.id);
      if (success) {
        if (selectedTable?.id === table.id) {
          setSelectedTable(null);
          setShowPropertiesPanel(false);
        }
        setHasUnsavedChanges(true);
        toast.success('Mesa deletada com sucesso!');
      }
    } catch (error) {
      toast.error('Erro ao deletar mesa');
      console.error('Error deleting table:', error);
    } finally {
      setIsLoading(false);
    }
  }, [selectedTable, onDeleteTable]);

  const handleSaveChanges = useCallback(async () => {
    setIsLoading(true);
    try {
      // Simulate batch save operation
      await new Promise(resolve => setTimeout(resolve, 1000));
      setHasUnsavedChanges(false);
      toast.success('Alterações salvas com sucesso!');
    } catch (error) {
      toast.error('Erro ao salvar alterações');
      console.error('Error saving changes:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleGridConfigChange = useCallback((config: Partial<GridConfig>) => {
    setGridConfig(prev => ({ ...prev, ...config }));
  }, []);

  const handleZoomReset = useCallback(() => {
    setZoom(ZOOM_LEVELS.default);
  }, []);

  const handlePropertiesSave = useCallback(async (updatedTable: Table) => {
    await handleTableUpdate(updatedTable);
    setShowPropertiesPanel(false);
  }, [handleTableUpdate]);

  const handlePropertiesClose = useCallback(() => {
    setShowPropertiesPanel(false);
    setSelectedTable(null);
  }, []);



  const handleZoomIn = useCallback(() => {
    const newZoom = Math.min(zoom + ZOOM_LEVELS.step, ZOOM_LEVELS.max);
    setZoom(newZoom);
  }, [zoom]);

  const handleZoomOut = useCallback(() => {
    const newZoom = Math.max(zoom - ZOOM_LEVELS.step, ZOOM_LEVELS.min);
    setZoom(newZoom);
  }, [zoom]);

  const handleRefresh = useCallback(() => {
    // TODO: Implement refresh functionality
    toast.success('Dados atualizados!');
    setHasUnsavedChanges(false);
  }, []);

  return (
    <div className="space-y-6">
      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-red-800 text-sm">
            Erro ao carregar mesas: {error}
          </div>
          {error.includes('autenticação') && (
            <div className="mt-2 text-xs text-red-600">
              💡 Usando dados de exemplo. Para acessar dados reais, faça login novamente.
            </div>
          )}
        </div>
      )}

      {/* Canvas Area */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="mb-4">
          {/* Action Bar */}
          <div className="flex items-center justify-between">
            {/* Left Side - Controls */}
            <div className="flex items-center space-x-2">
              {/* Zoom Controls */}
              <div className="flex items-center space-x-1">
                <button
                  onClick={handleZoomOut}
                  disabled={zoom <= ZOOM_LEVELS.min}
                  className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Zoom Out"
                >
                  <MagnifyingGlassMinusIcon className="h-4 w-4" />
                </button>

                <div className="px-3 py-2 bg-gray-50 rounded-lg min-w-[60px] text-center">
                  <span className="text-sm font-medium text-gray-700">
                    {Math.round(zoom * 100)}%
                  </span>
                </div>

                <button
                  onClick={handleZoomIn}
                  disabled={zoom >= ZOOM_LEVELS.max}
                  className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Zoom In"
                >
                  <MagnifyingGlassPlusIcon className="h-4 w-4" />
                </button>
              </div>

              <button
                onClick={handleRefresh}
                className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                title="Atualizar"
              >
                <ArrowPathIcon className="h-4 w-4" />
              </button>
            </div>

            {/* Right Side - Zone Selector */}
            <div className="flex items-center space-x-1">
              {zones.map(zone => {
                const zoneStatus = getZoneStatus(zone);
                const isDisabled = !zoneStatus.hasTable;

                return (
                  <button
                    key={zone}
                    onClick={() => !isDisabled && setSelectedZone(zone)}
                    disabled={isDisabled}
                    className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                      selectedZone === zone
                        ? 'bg-blue-600 text-white'
                        : isDisabled
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                    title={isDisabled ? 'Zona sem mesas - crie uma mesa nesta zona para ativá-la' : ''}
                  >
                    {zone} ({zoneStatus.tableCount})
                    {zoneStatus.isCustom && !zoneStatus.hasTable && (
                      <span className="ml-1 text-xs opacity-75">vazia</span>
                    )}
                  </button>
                );
              })}
            </div>
          </div>
        </div>
            
            {loading ? (
              <div className="flex items-center justify-center py-12 border border-gray-300 rounded-lg">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                <span className="ml-2 text-gray-600">Carregando mesas...</span>
              </div>
            ) : (
              <div className="border border-gray-300 rounded-lg overflow-auto max-h-[600px]">
                <LayoutCanvas
                  tables={tables}
                  selectedZone={selectedZone}
                  selectedTable={selectedTable}
                  gridConfig={gridConfig}
                  zoom={zoom}
                  canvasWidth={CANVAS_DIMENSIONS.defaultWidth}
                  canvasHeight={CANVAS_DIMENSIONS.defaultHeight}
                  onTableUpdate={handleTableUpdate}
                  onTableCreate={handleTableCreate}
                  onTableSelect={handleTableSelect}
                  onTableEdit={handleTableEdit}
                  onTableDelete={handleTableDelete}
                  onZoomChange={setZoom}
                />
              </div>
            )}
          </div>

      {/* Table Properties Panel */}
      {showPropertiesPanel && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="w-full max-w-2xl mx-4">
            <TablePropertiesPanel
              table={selectedTable}
              zones={zones}
              isOpen={showPropertiesPanel}
              onClose={handlePropertiesClose}
              onSave={handlePropertiesSave}
              onDelete={handleTableDelete}
            />
          </div>
        </div>
      )}
    </div>
  );
}
