"""API endpoints for payment processors."""

import uuid
from typing import List, Optional, TYPE_CHECKING
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db  # noqa: E402
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    get_current_tenant_from_header as get_current_tenant,
    require_tenant_role,
)
from app.modules.core.roles.models.roles import RolePermissions, TenantRole
from app.core.exceptions import BusinessLogicError
from app.modules.core.tenants.models.tenant import Tenant

# Use TYPE_CHECKING to avoid circular import
if TYPE_CHECKING:
    from app.modules.core.users.models.user import User

from app.modules.core.payments.schemas.payment_processor import (  # noqa: E402
    PaymentProcessorCreate,
    PaymentProcessorUpdate,
    PaymentProcessorRead,
    PaymentProcessorReadAdmin,
)
from app.modules.core.payments.services.payment_processor_service import (  # noqa: E402
    payment_processor_service,
)

router = APIRouter()


@router.post(
    "/processors",
    response_model=PaymentProcessorRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create Payment Processor",
    description="Create a new payment processor for the tenant.",
)
async def create_payment_processor(
    *,
    db: AsyncSession = Depends(get_db),
    processor_in: PaymentProcessorCreate,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(required_roles=RolePermissions.ADMIN_ROLES, tenant_id_source="header")
    ),
) -> PaymentProcessorRead:
    """
    Creates a new payment processor for the tenant.
    Requires MANAGER or OWNER role.
    """
    try:
        processor = await payment_processor_service.create(
            db=db, tenant_id=current_tenant.id, obj_in=processor_in
        )
        return processor
    except BusinessLogicError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get(
    "/processors",
    response_model=List[PaymentProcessorRead],
    status_code=status.HTTP_200_OK,
    summary="List Payment Processors",
    description="List all payment processors for the tenant.",
)
async def list_payment_processors(
    *,
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(required_roles=RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    skip: int = 0,
    limit: int = 100,
) -> List[PaymentProcessorRead]:
    """
    Lists all payment processors for the tenant.
    Requires STAFF, MANAGER, or OWNER role.
    """
    processors = await payment_processor_service.get_multi(
        db=db, tenant_id=current_tenant.id, is_active=is_active, skip=skip, limit=limit
    )
    return processors


@router.get(
    "/processors/{processor_id}",
    response_model=PaymentProcessorRead,
    status_code=status.HTTP_200_OK,
    summary="Get Payment Processor",
    description="Get details of a specific payment processor.",
)
async def get_payment_processor(
    *,
    db: AsyncSession = Depends(get_db),
    processor_id: uuid.UUID,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(required_roles=RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
) -> PaymentProcessorRead:
    """
    Gets details of a specific payment processor.
    Requires STAFF, MANAGER, or OWNER role.
    """
    processor = await payment_processor_service.get(
        db=db, id=processor_id, tenant_id=current_tenant.id
    )

    if not processor:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Payment processor with id {processor_id} not found or does not belong to the tenant.",  # noqa: E501
        )

    return processor


@router.get(
    "/processors/{processor_id}/admin",
    response_model=PaymentProcessorReadAdmin,
    status_code=status.HTTP_200_OK,
    summary="Get Payment Processor (Admin)",
    description="Get details of a specific payment processor, including sensitive information.",
)
async def get_payment_processor_admin(
    *,
    db: AsyncSession = Depends(get_db),
    processor_id: uuid.UUID,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(required_roles=[TenantRole.OWNER.value], tenant_id_source="header")
    ),
) -> PaymentProcessorReadAdmin:
    """
    Gets details of a specific payment processor, including sensitive information.
    Requires OWNER role.
    """
    processor = await payment_processor_service.get(
        db=db, id=processor_id, tenant_id=current_tenant.id
    )

    if not processor:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Payment processor with id {processor_id} not found or does not belong to the tenant.",  # noqa: E501
        )

    return processor


@router.put(
    "/processors/{processor_id}",
    response_model=PaymentProcessorRead,
    status_code=status.HTTP_200_OK,
    summary="Update Payment Processor",
    description="Update an existing payment processor.",
)
async def update_payment_processor(
    *,
    db: AsyncSession = Depends(get_db),
    processor_id: uuid.UUID,
    processor_in: PaymentProcessorUpdate,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(required_roles=RolePermissions.ADMIN_ROLES, tenant_id_source="header")
    ),
) -> PaymentProcessorRead:
    """
    Updates an existing payment processor.
    Requires MANAGER or OWNER role.
    """
    try:
        processor = await payment_processor_service.update(
            db=db, id=processor_id, tenant_id=current_tenant.id, obj_in=processor_in
        )

        if not processor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Payment processor with id {processor_id} not found or does not belong to the tenant.",  # noqa: E501
            )

        return processor
    except BusinessLogicError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.delete(
    "/processors/{processor_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete Payment Processor",
    description="Delete an existing payment processor.",
)
async def delete_payment_processor(
    *,
    db: AsyncSession = Depends(get_db),
    processor_id: uuid.UUID,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(required_roles=[TenantRole.OWNER.value], tenant_id_source="header")
    ),
) -> None:
    """
    Deletes an existing payment processor.
    Requires OWNER role.
    """
    deleted = await payment_processor_service.delete(
        db=db, id=processor_id, tenant_id=current_tenant.id
    )

    if not deleted:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Payment processor with id {processor_id} not found or does not belong to the tenant.",  # noqa: E501
        )
