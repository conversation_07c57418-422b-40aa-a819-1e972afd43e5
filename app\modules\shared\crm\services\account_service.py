"""Account service for CRM module."""

import uuid  # noqa: E402
import logging
from typing import List, Optional
from datetime import datetime
from sqlalchemy import select, update, delete
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError

from app.modules.shared.crm.models.account import Account, AccountStatus  # noqa: E402
from app.modules.shared.crm.schemas.account import AccountCreate, AccountUpdate

logger = logging.getLogger(__name__)


class AccountService:
    """Service for managing CRM accounts."""

    @staticmethod
    async def create_account(
        db: AsyncSession, tenant_id: uuid.UUID, account_in: AccountCreate
    ) -> Account:
        """Create a new account."""
        try:
            account_data = account_in.model_dump(exclude_unset=True)
            db_account = Account(tenant_id=tenant_id, **account_data)

            db.add(db_account)
            await db.commit()
            await db.refresh(db_account)

            return db_account
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(f"Error creating account: {e}")
            raise

    @staticmethod
    async def get_account(
        db: AsyncSession, tenant_id: uuid.UUID, account_id: uuid.UUID
    ) -> Optional[Account]:
        """Get an account by ID."""
        try:
            query = select(Account).where(Account.tenant_id == tenant_id, Account.id == account_id)
            result = await db.execute(query)
            return result.scalars().first()
        except SQLAlchemyError as e:
            logger.error(f"Error getting account {account_id}: {e}")
            raise

    @staticmethod
    async def get_accounts(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        status: Optional[AccountStatus] = None,
        account_type: Optional[str] = None,
        search: Optional[str] = None,
    ) -> List[Account]:
        """Get all accounts with optional filtering."""
        try:
            query = select(Account).where(Account.tenant_id == tenant_id)

            # Apply filters if provided
            if status:
                query = query.where(Account.status == status)

            if account_type:
                query = query.where(Account.account_type == account_type)

            if search:
                search_term = f"%{search}%"
                query = query.where(
                    Account.name.ilike(search_term)
                    | Account.email.ilike(search_term)
                    | Account.phone.ilike(search_term)
                )

            # Apply pagination
            query = query.offset(skip).limit(limit)

            result = await db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            logger.error(f"Error getting accounts: {e}")
            raise

    @staticmethod
    async def update_account(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        account_id: uuid.UUID,
        account_in: AccountUpdate,
    ) -> Optional[Account]:
        """Update an account."""
        try:
            # Get the account first to ensure it exists and belongs to the tenant
            db_account = await AccountService.get_account(db, tenant_id, account_id)
            if not db_account:
                return None

            # Update the account
            account_data = account_in.model_dump(exclude_unset=True)

            # Update the account in the database
            query = (
                update(Account)
                .where(Account.id == account_id, Account.tenant_id == tenant_id)
                .values(**account_data)
                .execution_options(synchronize_session="fetch")
            )
            await db.execute(query)
            await db.commit()

            # Refresh and return the updated account
            return await AccountService.get_account(db, tenant_id, account_id)
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(f"Error updating account {account_id}: {e}")
            raise

    @staticmethod
    async def delete_account(db: AsyncSession, tenant_id: uuid.UUID, account_id: uuid.UUID) -> bool:
        """Delete an account."""
        try:
            # Get the account first to ensure it exists and belongs to the tenant
            db_account = await AccountService.get_account(db, tenant_id, account_id)
            if not db_account:
                return False

            # Delete the account
            query = (
                delete(Account)
                .where(Account.id == account_id, Account.tenant_id == tenant_id)
                .execution_options(synchronize_session="fetch")
            )
            await db.execute(query)
            await db.commit()

            return True
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(f"Error deleting account {account_id}: {e}")
            raise

    @staticmethod
    async def update_last_contact_date(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        account_id: uuid.UUID,
        contact_date: datetime = None,
    ) -> Optional[Account]:
        """Update the last contact date for an account."""
        try:
            # Get the account first to ensure it exists and belongs to the tenant
            db_account = await AccountService.get_account(db, tenant_id, account_id)
            if not db_account:
                return None

            # Use the provided date or current datetime
            last_contact_date = contact_date or datetime.utcnow()

            # Update the account
            query = (
                update(Account)
                .where(Account.id == account_id, Account.tenant_id == tenant_id)
                .values(last_contact_date=last_contact_date)
                .execution_options(synchronize_session="fetch")
            )
            await db.execute(query)
            await db.commit()

            # Refresh and return the updated account
            return await AccountService.get_account(db, tenant_id, account_id)
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(f"Error updating last contact date for account {account_id}: {e}")
            raise


# Create a singleton instance
account_service = AccountService()
