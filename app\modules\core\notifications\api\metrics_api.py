"""
Notification Metrics API

Endpoints para métricas e analytics de notificações.
"""

import logging
from typing import Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.user_auth import get_current_active_user
from app.modules.core.auth.dependencies.role_auth import require_admin_user
from app.modules.core.users.models.user import User

from ..schemas import (
    NotificationSystemMetrics, NotificationTenantMetrics
)
from ..services import NotificationMetricsService

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/system", response_model=NotificationSystemMetrics)
async def get_system_metrics(
    current_user: User = Depends(require_admin_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Obtém métricas gerais do sistema de notificações.
    
    Apenas para administradores.
    """
    service = NotificationMetricsService(db)
    
    metrics = await service.get_system_metrics()
    
    return NotificationSystemMetrics(
        total_notifications=metrics["total_notifications"],
        active_notifications=metrics["active_notifications"],
        expired_notifications=metrics["expired_notifications"],
        queue_size=metrics["queue_stats"]["pending"],
        failed_notifications=metrics["queue_stats"]["failed"],
        average_delivery_time=metrics["queue_stats"]["avg_processing_time_seconds"],
        system_load=metrics["queue_stats"]["pending"] / max(metrics["total_notifications"], 1) * 100,
        error_rate=metrics["queue_stats"]["failure_rate"]
    )


@router.get("/tenant/{tenant_id}", response_model=NotificationTenantMetrics)
async def get_tenant_metrics(
    tenant_id: UUID,
    period_days: int = Query(30, ge=1, le=365),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Obtém métricas de um tenant específico.
    
    Admins podem ver qualquer tenant.
    Tenant owners podem ver apenas seus próprios tenants.
    """
    # TODO: Verificar se o usuário tem acesso ao tenant
    # Por enquanto, permite acesso
    
    service = NotificationMetricsService(db)
    
    metrics = await service.get_tenant_metrics(tenant_id, period_days)
    
    return NotificationTenantMetrics(
        total_sent=metrics["total_sent"],
        total_delivered=metrics["total_delivered"],
        total_read=metrics["total_views"],
        total_clicked=metrics["total_clicks"],
        delivery_rate=metrics["delivery_rate"],
        open_rate=metrics["open_rate"],
        click_through_rate=metrics["click_through_rate"],
        engagement_score=metrics["engagement_score"]
    )


@router.get("/user/{user_id}")
async def get_user_metrics(
    user_id: UUID,
    period_days: int = Query(30, ge=1, le=365),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Obtém métricas de um usuário específico.
    
    Usuários podem ver apenas suas próprias métricas.
    Admins podem ver qualquer usuário.
    """
    # Verifica permissão
    if current_user.system_role != "admin" and current_user.id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Sem permissão para ver métricas deste usuário"
        )
    
    service = NotificationMetricsService(db)
    
    metrics = await service.get_user_metrics(user_id, period_days)
    
    return metrics


@router.get("/my-metrics")
async def get_my_metrics(
    period_days: int = Query(30, ge=1, le=365),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Obtém métricas do usuário atual.
    """
    service = NotificationMetricsService(db)
    
    metrics = await service.get_user_metrics(current_user.id, period_days)
    
    return metrics


@router.get("/engagement-report")
async def get_engagement_report(
    tenant_id: Optional[UUID] = Query(None),
    period_days: int = Query(30, ge=1, le=365),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Obtém relatório de engajamento detalhado.
    """
    service = NotificationMetricsService(db)
    
    if tenant_id:
        # TODO: Verificar permissão para o tenant
        metrics = await service.get_tenant_metrics(tenant_id, period_days)
        
        return {
            "type": "tenant",
            "tenant_id": str(tenant_id),
            "period_days": period_days,
            "metrics": metrics
        }
    else:
        # Métricas do usuário
        metrics = await service.get_user_metrics(current_user.id, period_days)
        
        return {
            "type": "user",
            "user_id": str(current_user.id),
            "period_days": period_days,
            "metrics": metrics
        }


@router.get("/dashboard-stats")
async def get_dashboard_stats(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Obtém estatísticas resumidas para o dashboard.
    """
    service = NotificationMetricsService(db)
    
    if current_user.system_role == "admin":
        # Métricas do sistema para admin
        system_metrics = await service.get_system_metrics()
        
        return {
            "type": "admin",
            "total_notifications": system_metrics["total_notifications"],
            "active_notifications": system_metrics["active_notifications"],
            "queue_size": system_metrics["queue_stats"]["pending"],
            "engagement": system_metrics["engagement"],
            "daily_stats": system_metrics["daily_stats"][-7:]  # Últimos 7 dias
        }
    else:
        # Métricas do usuário
        user_metrics = await service.get_user_metrics(current_user.id, 7)  # Última semana
        
        return {
            "type": "user",
            "sent": user_metrics["sent"],
            "received": user_metrics["received"]
        }


@router.post("/record-daily")
async def record_daily_metrics(
    current_user: User = Depends(require_admin_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Força o registro de métricas diárias.
    
    Normalmente executado automaticamente via cron.
    """
    service = NotificationMetricsService(db)
    
    await service.record_daily_metrics()
    
    return {"message": "Métricas diárias registradas com sucesso"}


@router.get("/performance-insights")
async def get_performance_insights(
    tenant_id: Optional[UUID] = Query(None),
    period_days: int = Query(30, ge=1, le=365),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Obtém insights de performance das notificações.
    """
    service = NotificationMetricsService(db)
    
    if tenant_id:
        # TODO: Verificar permissão
        metrics = await service.get_tenant_metrics(tenant_id, period_days)
        
        # Gera insights baseados nas métricas
        insights = []
        
        if metrics["delivery_rate"] < 90:
            insights.append({
                "type": "warning",
                "title": "Taxa de entrega baixa",
                "description": f"Taxa de entrega de {metrics['delivery_rate']:.1f}% está abaixo do ideal (90%+)",
                "recommendation": "Verifique a configuração dos destinatários e a validade dos dados de contato"
            })
        
        if metrics["open_rate"] < 20:
            insights.append({
                "type": "info",
                "title": "Taxa de abertura baixa",
                "description": f"Taxa de abertura de {metrics['open_rate']:.1f}% pode ser melhorada",
                "recommendation": "Considere melhorar os títulos das notificações e o timing de envio"
            })
        
        if metrics["click_through_rate"] < 5:
            insights.append({
                "type": "info",
                "title": "Taxa de cliques baixa",
                "description": f"Taxa de cliques de {metrics['click_through_rate']:.1f}% indica baixo engajamento",
                "recommendation": "Revise o conteúdo das notificações e adicione calls-to-action mais claros"
            })
        
        if metrics["engagement_score"] > 70:
            insights.append({
                "type": "success",
                "title": "Excelente engajamento",
                "description": f"Score de engajamento de {metrics['engagement_score']:.1f} está muito bom",
                "recommendation": "Continue com a estratégia atual de notificações"
            })
        
        return {
            "tenant_id": str(tenant_id),
            "period_days": period_days,
            "metrics": metrics,
            "insights": insights
        }
    
    else:
        # Insights para o usuário atual
        user_metrics = await service.get_user_metrics(current_user.id, period_days)
        
        insights = []
        
        sent_metrics = user_metrics["sent"]
        if sent_metrics["total_sent"] > 0:
            avg_engagement = sent_metrics["avg_engagement"]
            
            if avg_engagement < 10:
                insights.append({
                    "type": "info",
                    "title": "Engajamento baixo",
                    "description": "Suas notificações têm baixo engajamento",
                    "recommendation": "Tente personalizar mais o conteúdo e melhorar o timing"
                })
            elif avg_engagement > 50:
                insights.append({
                    "type": "success",
                    "title": "Ótimo engajamento",
                    "description": "Suas notificações têm alto engajamento",
                    "recommendation": "Continue com a estratégia atual"
                })
        
        received_metrics = user_metrics["received"]
        if received_metrics["read_rate"] < 50:
            insights.append({
                "type": "info",
                "title": "Muitas notificações não lidas",
                "description": f"Você leu apenas {received_metrics['read_rate']:.1f}% das notificações recebidas",
                "recommendation": "Considere ajustar suas preferências de notificação"
            })
        
        return {
            "user_id": str(current_user.id),
            "period_days": period_days,
            "metrics": user_metrics,
            "insights": insights
        }
