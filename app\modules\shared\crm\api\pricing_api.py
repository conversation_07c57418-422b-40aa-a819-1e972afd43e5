"""API endpoints for CRM pricing tiers and rules."""

import uuid  # noqa: E402
from typing import List, Optional, Annotated
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db  # noqa: E402
from app.modules.core.auth.dependencies.auth_dependencies import (
    get_current_active_user,
)  # noqa: E402
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    require_tenant_role,
)  # noqa: E402
from app.modules.core.roles.models.roles import TenantRole
from app.modules.core.users.models.user import User
from app.modules.shared.crm.models.pricing import PricingRuleType
from app.modules.shared.crm.schemas.pricing import (
    PricingTierCreate,
    PricingTierUpdate,
    PricingTierRead,
    CustomerPricingAssignmentCreate,
    CustomerPricingAssignmentUpdate,
    CustomerPricingAssignmentRead,
    PricingRuleCreate,
    PricingRuleUpdate,
    PricingRuleRead,
)
from app.modules.shared.crm.services.pricing_service import pricing_service  # noqa: E402

router = APIRouter(prefix="/pricing", tags=["CRM - Pricing"])


# ==================== Pricing Tier Endpoints ====================


@router.post(
    "/tiers",
    response_model=PricingTierRead,
    status_code=status.HTTP_201_CREATED,
    dependencies=[Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER], "header"))],
)
async def create_pricing_tier(
    tier_in: PricingTierCreate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    db: Annotated[AsyncSession, Depends(get_db)],
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Create a new pricing tier.

    Requires tenant owner or manager role.
    """
    try:
        db_tier = await pricing_service.create_pricing_tier(db, tenant_id, tier_in)
        return db_tier
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create pricing tier: {str(e)}",
        )


@router.get(
    "/tiers/{tier_id}",
    response_model=PricingTierRead,
    dependencies=[
        Depends(
            require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF], "header")
        )
    ],
)
async def get_pricing_tier(
    tier_id: uuid.UUID = Path(..., description="The ID of the pricing tier to retrieve"),
    db: Annotated[AsyncSession, Depends(get_db)] = None,
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Get a pricing tier by ID.

    Requires tenant owner, manager, or staff role.
    """
    db_tier = await pricing_service.get_pricing_tier(db, tenant_id, tier_id)
    if not db_tier:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Pricing tier with ID {tier_id} not found",
        )
    return db_tier


@router.get(
    "/tiers",
    response_model=List[PricingTierRead],
    dependencies=[
        Depends(
            require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF], "header")
        )
    ],
)
async def get_pricing_tiers(
    skip: int = Query(0, ge=0, description="Number of tiers to skip"),
    limit: int = Query(100, ge=1, le=100, description="Maximum number of tiers to return"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    db: Annotated[AsyncSession, Depends(get_db)] = None,
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Get all pricing tiers with optional filtering.

    Requires tenant owner, manager, or staff role.
    """
    db_tiers = await pricing_service.get_pricing_tiers(db, tenant_id, skip, limit, is_active)
    return db_tiers


@router.put(
    "/tiers/{tier_id}",
    response_model=PricingTierRead,
    dependencies=[Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER], "header"))],
)
async def update_pricing_tier(
    tier_in: PricingTierUpdate,
    tier_id: uuid.UUID = Path(..., description="The ID of the pricing tier to update"),
    db: Annotated[AsyncSession, Depends(get_db)] = None,
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Update a pricing tier.

    Requires tenant owner or manager role.
    """
    db_tier = await pricing_service.update_pricing_tier(db, tenant_id, tier_id, tier_in)
    if not db_tier:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Pricing tier with ID {tier_id} not found",
        )
    return db_tier


# ==================== Customer Pricing Assignment Endpoints ====================


@router.post(
    "/assignments",
    response_model=CustomerPricingAssignmentRead,
    status_code=status.HTTP_201_CREATED,
    dependencies=[Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER], "header"))],
)
async def assign_pricing_tier_to_customer(
    assignment_in: CustomerPricingAssignmentCreate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    db: Annotated[AsyncSession, Depends(get_db)],
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Assign a pricing tier to a customer.

    Requires tenant owner or manager role.
    """
    try:
        db_assignment = await pricing_service.assign_pricing_tier_to_customer(
            db, tenant_id, assignment_in
        )
        return db_assignment
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to assign pricing tier to customer: {str(e)}",
        )


@router.get(
    "/assignments/{assignment_id}",
    response_model=CustomerPricingAssignmentRead,
    dependencies=[
        Depends(
            require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF], "header")
        )
    ],
)
async def get_customer_pricing_assignment(
    assignment_id: uuid.UUID = Path(
        ..., description="The ID of the pricing assignment to retrieve"
    ),
    db: Annotated[AsyncSession, Depends(get_db)] = None,
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Get a customer pricing assignment by ID.

    Requires tenant owner, manager, or staff role.
    """
    db_assignment = await pricing_service.get_customer_pricing_assignment(
        db, tenant_id, assignment_id
    )
    if not db_assignment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Customer pricing assignment with ID {assignment_id} not found",
        )
    return db_assignment


@router.get(
    "/assignments/account/{account_id}",
    response_model=List[CustomerPricingAssignmentRead],
    dependencies=[
        Depends(
            require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF], "header")
        )
    ],
)
async def get_customer_pricing_assignments(
    account_id: uuid.UUID = Path(
        ..., description="The ID of the account to get pricing assignments for"
    ),
    skip: int = Query(0, ge=0, description="Number of assignments to skip"),
    limit: int = Query(100, ge=1, le=100, description="Maximum number of assignments to return"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    db: Annotated[AsyncSession, Depends(get_db)] = None,
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Get all pricing assignments for a specific customer.

    Requires tenant owner, manager, or staff role.
    """
    db_assignments = await pricing_service.get_customer_pricing_assignments(
        db, tenant_id, account_id, skip, limit, is_active
    )
    return db_assignments


@router.put(
    "/assignments/{assignment_id}",
    response_model=CustomerPricingAssignmentRead,
    dependencies=[Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER], "header"))],
)
async def update_customer_pricing_assignment(
    assignment_in: CustomerPricingAssignmentUpdate,
    assignment_id: uuid.UUID = Path(..., description="The ID of the pricing assignment to update"),
    db: Annotated[AsyncSession, Depends(get_db)] = None,
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Update a customer pricing assignment.

    Requires tenant owner or manager role.
    """
    db_assignment = await pricing_service.update_customer_pricing_assignment(
        db, tenant_id, assignment_id, assignment_in
    )
    if not db_assignment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Customer pricing assignment with ID {assignment_id} not found",
        )
    return db_assignment


# ==================== Pricing Rule Endpoints ====================


@router.post(
    "/rules",
    response_model=PricingRuleRead,
    status_code=status.HTTP_201_CREATED,
    dependencies=[Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER], "header"))],
)
async def create_pricing_rule(
    rule_in: PricingRuleCreate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    db: Annotated[AsyncSession, Depends(get_db)],
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Create a new pricing rule.

    Requires tenant owner or manager role.
    """
    try:
        db_rule = await pricing_service.create_pricing_rule(db, tenant_id, rule_in)
        return db_rule
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create pricing rule: {str(e)}",
        )


@router.get(
    "/rules/{rule_id}",
    response_model=PricingRuleRead,
    dependencies=[
        Depends(
            require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF], "header")
        )
    ],
)
async def get_pricing_rule(
    rule_id: uuid.UUID = Path(..., description="The ID of the pricing rule to retrieve"),
    db: Annotated[AsyncSession, Depends(get_db)] = None,
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Get a pricing rule by ID.

    Requires tenant owner, manager, or staff role.
    """
    db_rule = await pricing_service.get_pricing_rule(db, tenant_id, rule_id)
    if not db_rule:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Pricing rule with ID {rule_id} not found",
        )
    return db_rule


@router.get(
    "/rules/tier/{tier_id}",
    response_model=List[PricingRuleRead],
    dependencies=[
        Depends(
            require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF], "header")
        )
    ],
)
async def get_pricing_rules_for_tier(
    tier_id: uuid.UUID = Path(..., description="The ID of the pricing tier to get rules for"),
    skip: int = Query(0, ge=0, description="Number of rules to skip"),
    limit: int = Query(100, ge=1, le=100, description="Maximum number of rules to return"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    rule_type: Optional[PricingRuleType] = Query(None, description="Filter by rule type"),
    db: Annotated[AsyncSession, Depends(get_db)] = None,
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Get all pricing rules for a specific tier.

    Requires tenant owner, manager, or staff role.
    """
    db_rules = await pricing_service.get_pricing_rules_for_tier(
        db, tenant_id, tier_id, skip, limit, is_active, rule_type
    )
    return db_rules


@router.put(
    "/rules/{rule_id}",
    response_model=PricingRuleRead,
    dependencies=[Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER], "header"))],
)
async def update_pricing_rule(
    rule_in: PricingRuleUpdate,
    rule_id: uuid.UUID = Path(..., description="The ID of the pricing rule to update"),
    db: Annotated[AsyncSession, Depends(get_db)] = None,
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Update a pricing rule.

    Requires tenant owner or manager role.
    """
    db_rule = await pricing_service.update_pricing_rule(db, tenant_id, rule_id, rule_in)
    if not db_rule:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Pricing rule with ID {rule_id} not found",
        )
    return db_rule
