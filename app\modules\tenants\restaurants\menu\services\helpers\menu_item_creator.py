import logging
from typing import List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.exc import IntegrityError
from fastapi import HTTPException, status
import uuid

# Import models
from app.modules.tenants.restaurants.menu.models.menu_item import MenuItem
from app.modules.core.functions.customizations.models.variant_group import VariantGroup
from app.modules.core.functions.customizations.models.variant_option import VariantOption
from app.modules.core.functions.customizations.models.modifier_group import ModifierGroup
from app.modules.core.functions.customizations.models.modifier_option import ModifierOption
from app.modules.core.functions.customizations.models.optional_group import OptionalGroup
from app.modules.core.functions.customizations.models.optional_option import OptionalOption

# Import schemas
from app.modules.tenants.restaurants.menu.schemas.menu_item import MenuItemCreate

# Import helper services
from .menu_item_allergen_manager import MenuItemAllergenManager
from .menu_item_groups_creator import MenuItemGroupsCreator

logger = logging.getLogger(__name__)


class MenuItemCreator:
    def __init__(self, db_session: AsyncSession):
        self.db = db_session
        self.allergen_manager = MenuItemAllergenManager(db_session)
        self.groups_creator = MenuItemGroupsCreator(db_session)

    async def create_item(self, item_in: MenuItemCreate, tenant_id: uuid.UUID) -> MenuItem:
        """Creates a new menu item with all nested structures."""
        try:
            # Create base menu item
            db_item = await self._create_base_item(item_in, tenant_id)
            
            # Handle allergen associations
            if item_in.allergen_ids:
                await self.allergen_manager.manage_allergen_associations(db_item, item_in.allergen_ids)

            # Handle nested groups
            await self.groups_creator.create_all_groups(db_item, item_in, tenant_id)

            # Commit and return fully loaded item
            await self.db.commit()
            return await self._load_created_item(db_item.id, tenant_id)

        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"🔍 IntegrityError creating item for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Error creating menu item. Database constraint violation: {str(e)}",
            )
        except Exception as e:
            await self.db.rollback()
            logger.error(f"❌ Unexpected error creating menu item: {e}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An unexpected error occurred while creating the menu item.",
            )

    async def _create_base_item(self, item_in: MenuItemCreate, tenant_id: uuid.UUID) -> MenuItem:
        """Create the base menu item without nested structures."""
        # Separate base item data from nested structures
        item_data = item_in.model_dump(
            exclude={"variant_groups", "modifier_groups", "optional_groups", 
                    "inventory_item_ids", "allergen_ids", "images"}
        )

        # Map allergens_json to allergens field in the model
        if "allergens_json" in item_data:
            allergens_value = item_data.pop("allergens_json")
            if allergens_value is not None:
                item_data["allergens"] = allergens_value

        db_item = MenuItem(**item_data, tenant_id=tenant_id)
        self.db.add(db_item)
        await self.db.flush()  # Flush to get the db_item.id for nested items
        
        logger.info(f"Base menu item created: {db_item.id} for tenant {tenant_id}")
        return db_item

    async def _load_created_item(self, item_id: uuid.UUID, tenant_id: uuid.UUID) -> MenuItem:
        """Load the fully created item with all relationships."""
        from sqlalchemy import select
        
        stmt = (
            select(MenuItem)
            .options(
                selectinload(MenuItem.variant_groups).selectinload(VariantGroup.options),
                selectinload(MenuItem.modifier_groups).selectinload(ModifierGroup.options),
                selectinload(MenuItem.optional_groups).selectinload(OptionalGroup.options),
                selectinload(MenuItem.allergens),
                joinedload(MenuItem.category),
            )
            .where(MenuItem.id == item_id, MenuItem.tenant_id == tenant_id)
        )
        result = await self.db.execute(stmt)
        return result.scalars().one()