from datetime import datetime, timedelta, timezone
import logging
from typing import Optional, Any, Dict

from jose import jwt, JW<PERSON>rror

from app.core.config import settings
from app.modules.core.auth.schemas.token import TokenPayload
from app.modules.core.auth.security_modern import (
    modern_password_hasher,
    get_password_hash as modern_get_password_hash,
    verify_password as modern_verify_password,
    verify_password_with_migration
)

ALGORITHM = settings.ALGORITHM
SECRET_KEY = settings.SECRET_KEY
ACCESS_TOKEN_EXPIRE_MINUTES = settings.ACCESS_TOKEN_EXPIRE_MINUTES
REFRESH_TOKEN_EXPIRE_DAYS = settings.REFRESH_TOKEN_EXPIRE_DAYS

logger = logging.getLogger(__name__)


class InvalidTokenTypeError(JWTError):
    """Custom exception for invalid token type."""


class MissingSubError(JWTError):
    """Custom exception for missing subject (sub) in token."""


def create_access_token(
    subject: Any,
    expires_delta: Optional[timedelta] = None,
    custom_claims: Optional[dict] = None,
) -> str:
    """
    Creates a new JWT access token.

    Args:
        subject: The unique identifier of the user (or whatever is relevant) to be included in the token.
        expires_delta: Custom expiration time. If None, uses the default from settings.
        custom_claims: A dictionary of custom claims to be added to the payload.

    Returns:
        The JWT token encoded as a string.
    """
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode = {"exp": expire, "sub": str(subject), "type": "access"}
    if custom_claims:
        to_encode.update(custom_claims)

    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def create_refresh_token(subject: Any, expires_delta: Optional[timedelta] = None) -> str:
    """
    Creates a new JWT refresh token.

    Args:
        subject: The unique identifier of the user.
        expires_delta: Custom expiration time. If None, uses the default from settings.

    Returns:
        The JWT refresh token encoded as a string.
    """
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)

    to_encode = {"exp": expire, "sub": str(subject), "type": "refresh"}
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verifies if a plain text password matches a hash.
    Uses modern implementation with automatic migration from bcrypt to Argon2id.

    Args:
        plain_password: The plain text password.
        hashed_password: The stored password hash.

    Returns:
        True if the password matches, False otherwise.
    """
    return modern_verify_password(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """
    Generates a password hash using modern Argon2id algorithm.

    Args:
        password: The plain text password.

    Returns:
        The password hash using Argon2id.
    """
    return modern_get_password_hash(password)


def decode_token(token: str) -> Optional[TokenPayload]:
    """
    Decodes a JWT token and validates its signature and expiration.

    Args:
        token: The JWT token to decode.

    Returns:
        The token payload as a TokenPayload object if valid, None otherwise.
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return TokenPayload(**payload)
    except JWTError:
        # Invalid token (signature, expiration, format, etc.)
        return None


def verify_refresh_token(token: str) -> Dict[str, Any]:
    """
    Verifies a refresh token, validates its type, and returns the decoded payload.

    Args:
        token: The JWT refresh token.

    Returns:
        The decoded payload if the token is valid and of type 'refresh'.

    Raises:
        ExpiredSignatureError: If the token has expired.
        InvalidTokenTypeError: If the token type is not 'refresh'.
        MissingSubError: If the 'sub' field is missing.
        JWTError: For other decoding/signature errors.
    """
    try:
        payload = jwt.decode(
            token,
            SECRET_KEY,
            algorithms=[ALGORITHM],
            options={"verify_exp": True},  # Ensure expiration is checked
        )

        token_type = payload.get("type")
        if token_type != "refresh":
            logger.warning(
                f"verify_refresh_token: Invalid token type: {token_type}. Expected 'refresh'."
            )
            raise InvalidTokenTypeError("Invalid token type for refresh")

        if payload.get("sub") is None:
            logger.warning("verify_refresh_token: user_id (sub) not found in payload.")
            raise MissingSubError("Missing sub in refresh token")

        return payload

    except jwt.ExpiredSignatureError as e:
        logger.warning("verify_refresh_token: Refresh token expired (ExpiredSignatureError).")
        raise e  # Re-raise the specific error
    except JWTError as e:
        logger.error(f"verify_refresh_token: Generic JWT error: {e}")  # Log other JWT errors
        raise e  # Re-raise other JWT errors (like invalid signature)
