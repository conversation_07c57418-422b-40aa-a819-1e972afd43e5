"""Financial transaction schemas."""

import uuid
from datetime import date
from decimal import Decimal
from typing import Optional, List
from pydantic import BaseModel, Field, validator, UUID4, ConfigDict

from ..models.transaction import TransactionType


class FinancialTransactionBase(BaseModel):
    """Base schema for financial transactions."""
    
    transaction_type: TransactionType = Field(
        ..., 
        description="Type of transaction (income or expense)"
    )
    amount: Decimal = Field(
        ..., 
        gt=0, 
        max_digits=10, 
        decimal_places=2,
        description="Transaction amount"
    )
    description: str = Field(
        ..., 
        min_length=1, 
        max_length=500,
        description="Transaction description"
    )
    transaction_date: date = Field(
        ...,
        description="Date when the transaction occurred"
    )
    payment_method_id: Optional[UUID4] = Field(
        None,
        description="ID of the payment method used"
    )
    category_id: Optional[UUID4] = Field(
        None,
        description="ID of the financial category"
    )
    reference_number: Optional[str] = Field(
        None,
        max_length=100,
        description="Reference number (invoice, receipt, etc.)"
    )
    notes: Optional[str] = Field(
        None,
        max_length=1000,
        description="Additional notes"
    )


class FinancialTransactionCreate(FinancialTransactionBase):
    """Schema for creating a financial transaction."""
    
    @validator('amount')
    def validate_amount(cls, v):
        """Validate amount is positive."""
        if v <= 0:
            raise ValueError('Amount must be greater than 0')
        return v

    media_upload_ids: Optional[List[UUID4]] = []


class FinancialTransactionUpdate(FinancialTransactionBase):
    """Schema for updating a financial transaction."""
    
    transaction_type: Optional[TransactionType] = Field(
        None,
        description="Type of transaction (income or expense)"
    )
    amount: Optional[Decimal] = Field(
        None,
        gt=0,
        max_digits=10,
        decimal_places=2,
        description="Transaction amount"
    )
    description: Optional[str] = Field(
        None,
        min_length=1,
        max_length=500,
        description="Transaction description"
    )
    transaction_date: Optional[date] = Field(
        None,
        description="Date when the transaction occurred"
    )
    payment_method_id: Optional[UUID4] = Field(
        None,
        description="ID of the payment method used"
    )
    category_id: Optional[UUID4] = Field(
        None,
        description="ID of the financial category"
    )
    reference_number: Optional[str] = Field(
        None,
        max_length=100,
        description="Reference number (invoice, receipt, etc.)"
    )
    notes: Optional[str] = Field(
        None,
        max_length=1000,
        description="Additional notes"
    )

    @validator('amount')
    def validate_amount(cls, v):
        """Validate amount is positive."""
        if v is not None and v <= 0:
            raise ValueError('Amount must be greater than 0')
        return v

    media_upload_ids: Optional[List[UUID4]] = None


class FinancialTransactionRead(FinancialTransactionBase):
    """Schema for reading a financial transaction."""
    
    id: UUID4 = Field(..., description="Transaction ID")
    tenant_id: UUID4 = Field(..., description="Tenant ID")
    created_by: UUID4 = Field(..., description="ID of user who created transaction")
    
    # Related data (optional, loaded when needed)
    category_name: Optional[str] = Field(None, description="Category name")
    payment_method_name: Optional[str] = Field(None, description="Payment method name")
    created_by_name: Optional[str] = Field(None, description="Name of user who created")
    
    # Document attachments count
    documents_count: Optional[int] = Field(0, description="Number of attached documents")
    
    class Config:
        from_attributes = True


class FinancialTransactionSummary(BaseModel):
    """Schema for transaction summary/statistics."""
    
    total_income: Decimal = Field(
        default=Decimal('0.00'),
        description="Total income amount"
    )
    total_expense: Decimal = Field(
        default=Decimal('0.00'),
        description="Total expense amount"
    )
    net_amount: Decimal = Field(
        default=Decimal('0.00'),
        description="Net amount (income - expense)"
    )
    transaction_count: int = Field(
        default=0,
        description="Total number of transactions"
    )
    income_count: int = Field(
        default=0,
        description="Number of income transactions"
    )
    expense_count: int = Field(
        default=0,
        description="Number of expense transactions"
    )


class FinancialTransactionFilter(BaseModel):
    """Schema for filtering financial transactions."""
    
    transaction_type: Optional[TransactionType] = Field(
        None,
        description="Filter by transaction type"
    )
    category_id: Optional[UUID4] = Field(
        None,
        description="Filter by category ID"
    )
    payment_method_id: Optional[UUID4] = Field(
        None,
        description="Filter by payment method ID"
    )
    date_from: Optional[date] = Field(
        None,
        description="Filter transactions from this date"
    )
    date_to: Optional[date] = Field(
        None,
        description="Filter transactions to this date"
    )
    amount_min: Optional[Decimal] = Field(
        None,
        ge=0,
        description="Minimum amount filter"
    )
    amount_max: Optional[Decimal] = Field(
        None,
        ge=0,
        description="Maximum amount filter"
    )
    search: Optional[str] = Field(
        None,
        max_length=100,
        description="Search in description and reference number"
    )


class FinancialTransactionListResponse(BaseModel):
    """Schema for paginated transaction list response."""
    
    transactions: List[FinancialTransactionRead] = Field(
        default_factory=list,
        description="List of transactions"
    )
    total: int = Field(
        default=0,
        description="Total number of transactions"
    )
    page: int = Field(
        default=1,
        description="Current page number"
    )
    per_page: int = Field(
        default=20,
        description="Items per page"
    )
    pages: int = Field(
        default=0,
        description="Total number of pages"
    )
    summary: Optional[FinancialTransactionSummary] = Field(
        None,
        description="Summary statistics for filtered results"
    )


class MediaUploadInfo(BaseModel):
    """Schema for representing a media upload in a response."""

    id: UUID4
    filename: str
    file_path: str
    mime_type: str

    model_config = ConfigDict(from_attributes=True)


class FinancialTransactionInDB(FinancialTransactionBase):
    """Schema for representing a transaction in a response."""

    id: UUID4
    tenant_id: UUID4
    created_by: UUID4
    media_uploads: List[MediaUploadInfo] = []

    model_config = ConfigDict(from_attributes=True)
