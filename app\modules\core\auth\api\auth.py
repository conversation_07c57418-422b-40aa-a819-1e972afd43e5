import logging
from typing import Any, Annotated

from fastapi import APIRouter, Depends, HTTPException, status, Form
from fastapi.security import OAuth2P<PERSON><PERSON>R<PERSON><PERSON>Form
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.auth.schemas.token import Token
from app.modules.core.auth.services.auth_service import auth_service
from app.modules.core.auth.security import (
    create_access_token,
    create_refresh_token,
    verify_refresh_token,
    verify_password,
    get_password_hash,
    credentials_exception,
)

from app.modules.core.auth.dependencies.auth_dependencies import get_current_active_user
from app.modules.core.users.schemas.user import UserCreate, User as UserSchema
from app.modules.core.users.services.user_service import user_service
from jose import JWTError

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/login", response_model=Token)
async def login_access_token(
    form_data: Annotated[OAuth2PasswordRequestForm, Depends()],
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Login endpoint, get an access token for future requests.
    """
    logger.info(f"login_access_token: Login attempt for user: {form_data.username}")

    user = await auth_service.authenticate_user(
        db, email=form_data.username, password=form_data.password
    )
    if not user:
        logger.warning(
            f"login_access_token: Authentication failed for user: {form_data.username}"
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    if not user.is_active:
        logger.warning(
            f"login_access_token: Inactive user attempted login: {form_data.username}"
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Inactive user"
        )

    logger.info(
        f"login_access_token: Authentication successful for user: {form_data.username}"
    )
    access_token, refresh_token = auth_service.create_tokens(user_id=user.id)

    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
    }


@router.post("/refresh-token", response_model=Token)
async def refresh_token(
    refresh_token: str = Form(...),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get a new access token using a refresh token.
    """
    try:
        # Verify the refresh token
        payload = verify_refresh_token(refresh_token)
        user_id = payload.get("sub")

        # Check if the user exists and is active
        user = await auth_service.get_user_by_id(db, user_id=user_id)
        if not user or not user.is_active:
            logger.warning(f"refresh_token: User {user_id} not found or inactive.")
            raise credentials_exception

        # Create new tokens
        access_token, new_refresh_token = auth_service.create_tokens(user.id)

        return {
            "access_token": access_token,
            "refresh_token": new_refresh_token,
            "token_type": "bearer",
        }

    except JWTError as e:
        logger.error(f"refresh_token: JWT error: {e}")
        raise credentials_exception


@router.post("/register", response_model=Token)
async def register_user(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Register a new user account.
    Public endpoint - no authentication required.
    """
    logger.info(f"register_user: Registration attempt for email: {user_data.email}")

    # Check if user already exists
    existing_user = await user_service.get_user_by_email(db, email=user_data.email)
    if existing_user:
        logger.warning(f"register_user: User already exists: {user_data.email}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="A user with this email already exists",
        )

    # Create new user
    try:
        user = await user_service.create_user(db, user_in=user_data)
        logger.info(f"register_user: User created successfully: {user.email}")

        # Auto-login: create tokens for the new user
        access_token, refresh_token = auth_service.create_tokens(user.id)

        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
        }

    except Exception as e:
        logger.error(f"register_user: Error creating user {user_data.email}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error creating user account",
        )


@router.post("/change-password")
async def change_password(
    current_password: str = Form(...),
    new_password: str = Form(..., min_length=8),
    current_user: UserSchema = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Change user password.
    Requires authentication and current password verification.
    """
    logger.info(f"change_password: Password change request for user: {current_user.email}")

    # Verify current password
    if not verify_password(current_password, current_user.hashed_password):
        logger.warning(f"change_password: Invalid current password for user: {current_user.email}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Current password is incorrect",
        )

    # Update password
    try:
        hashed_new_password = get_password_hash(new_password)
        current_user.hashed_password = hashed_new_password

        db.add(current_user)
        await db.commit()

        logger.info(f"change_password: Password changed successfully for user: {current_user.email}")
        return {"message": "Password changed successfully"}

    except Exception as e:
        await db.rollback()
        logger.error(f"change_password: Error changing password for user {current_user.email}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error changing password",
        )


@router.post("/forgot-password")
async def forgot_password(
    email: str = Form(...),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Request password reset.
    Sends reset instructions to email (placeholder implementation).
    """
    logger.info(f"forgot_password: Password reset request for email: {email}")

    # Check if user exists
    user = await user_service.get_user_by_email(db, email=email)
    if not user:
        # Don't reveal if email exists or not for security
        logger.warning(f"forgot_password: User not found for email: {email}")
        return {"message": "If the email exists, password reset instructions have been sent"}

    # TODO: Implement email sending with reset token
    # For now, just log the request
    logger.info(f"forgot_password: Would send reset email to: {email}")

    return {"message": "If the email exists, password reset instructions have been sent"}


@router.post("/reset-password")
async def reset_password(
    token: str = Form(...),
    new_password: str = Form(..., min_length=8),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Reset password using token.
    Placeholder implementation - requires token validation system.
    """
    logger.info(f"reset_password: Password reset attempt with token")

    # TODO: Implement token validation and password reset
    # For now, return not implemented
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="Password reset with token not yet implemented. Please contact an administrator.",
    )


@router.get("/me", response_model=UserSchema)
async def get_current_user_info(
    current_user: UserSchema = Depends(get_current_active_user),
) -> Any:
    """
    Get current user information.
    Requires authentication.
    """
    logger.info(f"get_current_user_info: Request for user: {current_user.email}")
    return current_user


@router.post("/test-simple")
async def test_simple() -> Any:
    """
    Simple test endpoint without database access.
    """
    return {"message": "Simple test successful", "status": "ok"}


@router.post("/test-db")
async def test_db(db: AsyncSession = Depends(get_db)) -> Any:
    """
    Test endpoint with database access.
    """
    try:
        # Simple database query
        from sqlalchemy import text
        result = await db.execute(text("SELECT 1 as test"))
        row = result.fetchone()
        return {"message": "Database test successful", "result": row.test if row else None}
    except Exception as e:
        return {"message": "Database test failed", "error": str(e)}


@router.post("/test-auth")
async def test_auth(
    email: str = Form(...),
    password: str = Form(...),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Test authentication step by step.
    """
    try:
        logger.info(f"test_auth: Starting test for {email}")

        # Step 1: Test user lookup
        from app.modules.core.users.services.user_service import user_service
        user = await user_service.get_user_by_email(db, email=email)

        if not user:
            return {"step": "user_lookup", "success": False, "message": "User not found"}

        logger.info(f"test_auth: User found: {user.id}")

        # Step 2: Test password verification
        from app.modules.core.auth.security import verify_password
        if not verify_password(password, user.hashed_password):
            return {"step": "password_verify", "success": False, "message": "Password incorrect"}

        logger.info(f"test_auth: Password verified")

        # Step 3: Test token creation
        access_token, refresh_token = auth_service.create_tokens(user.id)

        logger.info(f"test_auth: Tokens created successfully")

        return {
            "step": "complete",
            "success": True,
            "user_id": str(user.id),
            "email": user.email,
            "access_token": access_token[:50] + "...",
            "refresh_token": refresh_token[:50] + "..."
        }

    except Exception as e:
        logger.error(f"test_auth: Error: {e}")
        return {"step": "error", "success": False, "error": str(e)}



@router.get("/tenants")
async def get_user_tenants(
    current_user: UserSchema = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get user's associated tenants.
    Requires authentication.
    """
    logger.info(f"get_user_tenants: Request for user: {current_user.email}")

    try:
        # Query tenant associations for the current user
        from sqlalchemy import text

        result = await db.execute(
            text("""
                SELECT
                    tua.id as association_id,
                    tua.user_id,
                    tua.tenant_id,
                    tua.role,
                    tua.created_at,
                    tua.updated_at,
                    t.name as tenant_name,
                    t.tenant_slug,
                    t.tenant_type,
                    t.is_active as tenant_is_active
                FROM tenant_user_associations tua
                JOIN tenants t ON tua.tenant_id = t.id
                WHERE tua.user_id = :user_id
                AND t.is_active = true
                ORDER BY tua.created_at DESC
            """),
            {"user_id": current_user.id}
        )

        associations = result.fetchall()

        # Format the response
        tenant_associations = []
        for assoc in associations:
            tenant_associations.append({
                "id": str(assoc.association_id),
                "user_id": str(assoc.user_id),
                "tenant_id": str(assoc.tenant_id),
                "role": assoc.role,
                "created_at": assoc.created_at.isoformat(),
                "updated_at": assoc.updated_at.isoformat(),
                "tenant": {
                    "id": str(assoc.tenant_id),
                    "name": assoc.tenant_name,
                    "subdomain": assoc.tenant_slug,  # Use tenant_slug as subdomain
                    "tenant_slug": assoc.tenant_slug,
                    "tenant_type": assoc.tenant_type,
                    "is_active": assoc.tenant_is_active
                }
            })

        logger.info(f"Found {len(tenant_associations)} tenant associations for user: {current_user.email}")
        return tenant_associations

    except Exception as e:
        logger.error(f"Error getting user tenants: {e}")
        return []
