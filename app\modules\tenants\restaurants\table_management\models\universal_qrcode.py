"""
Universal QR code model for restaurant table management.
"""

import uuid
from typing import TYPE_CHECKING, List, Optional
from sqlalchemy import Column, String, Text, Boolean, ForeignKey, DateTime
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import Base

# Use TYPE_CHECKING to handle circular dependencies for type hinting
if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.tenants.restaurants.table_management.models.qrcode_scan import QRCodeScan


class UniversalQRCode(Base):
    """
    Model for universal QR codes that can be used across a restaurant.

    Universal QR codes are not tied to a specific table and can be used for:
    - Entrance QR codes
    - Take-out order QR codes
    - Special event QR codes
    - etc.
    """

    __tablename__ = "restaurant_universal_qrcodes"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    qrcode_id = Column(String(50), nullable=False, unique=True, index=True)
    qrcode_secret = Column(String(100), nullable=False)
    is_active = Column(Boolean, nullable=False, default=True)
    requires_approval = Column(Boolean, nullable=False, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False
    )

    # Relationships
    tenant = relationship("app.modules.core.tenants.models.tenant.Tenant", viewonly=True)
    scans = relationship(
        "app.modules.tenants.restaurants.table_management.models.qrcode_scan.QRCodeScan",
        back_populates="universal_qrcode",
        cascade="all, delete-orphan",
    )

    def __repr__(self):
        return f"<UniversalQRCode(id={self.id}, name='{self.name}', qrcode_id='{self.qrcode_id}')>"
