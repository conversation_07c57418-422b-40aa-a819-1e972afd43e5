from datetime import datetime
from typing import List, Optional, Dict, Any
import uuid
from pydantic import BaseModel, Field, ConfigDict, EmailStr

from app.modules.shared.hr.models.recruitment import (
    JobStatus,
    JobType,
    CandidateStatus,
    InterviewStatus,
    AssessmentType,
)

# Base Job Schema


class JobBase(BaseModel):
    """Base schema for Job."""

    title: str = Field(..., min_length=1, max_length=255)
    description: str
    department: Optional[str] = None
    location: Optional[str] = None
    job_type: JobType
    salary_min: Optional[float] = None
    salary_max: Optional[float] = None
    salary_currency: Optional[str] = None
    salary_is_public: bool = False
    posting_date: Optional[datetime] = None
    closing_date: Optional[datetime] = None
    required_skills: Optional[List[str]] = None
    required_experience: Optional[str] = None
    required_education: Optional[str] = None
    benefits: Optional[str] = None
    remote_allowed: bool = False
    number_of_openings: int = 1
    internal_notes: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


# Schema for creating a new job


class JobCreate(JobBase):
    """Schema for creating a new Job."""

    hiring_manager_id: Optional[uuid.UUID] = None
    recruiter_id: Optional[uuid.UUID] = None
    status: JobStatus = JobStatus.DRAFT


# Schema for updating a job


class JobUpdate(BaseModel):
    """Schema for updating a Job."""

    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    department: Optional[str] = None
    location: Optional[str] = None
    job_type: Optional[JobType] = None
    status: Optional[JobStatus] = None
    salary_min: Optional[float] = None
    salary_max: Optional[float] = None
    salary_currency: Optional[str] = None
    salary_is_public: Optional[bool] = None
    posting_date: Optional[datetime] = None
    closing_date: Optional[datetime] = None
    required_skills: Optional[List[str]] = None
    required_experience: Optional[str] = None
    required_education: Optional[str] = None
    benefits: Optional[str] = None
    remote_allowed: Optional[bool] = None
    number_of_openings: Optional[int] = None
    hiring_manager_id: Optional[uuid.UUID] = None
    recruiter_id: Optional[uuid.UUID] = None
    internal_notes: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


# Schema for reading a job


class JobRead(JobBase):
    """Schema for reading a Job."""

    id: uuid.UUID
    status: JobStatus
    hiring_manager_id: Optional[uuid.UUID] = None
    recruiter_id: Optional[uuid.UUID] = None
    tenant_id: uuid.UUID
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Base Candidate Schema


class CandidateBase(BaseModel):
    """Base schema for Candidate."""

    job_id: uuid.UUID
    first_name: str = Field(..., min_length=1, max_length=100)
    last_name: str = Field(..., min_length=1, max_length=100)
    email: EmailStr
    phone: Optional[str] = None
    source: Optional[str] = None
    notes: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


# Schema for creating a new candidate


class CandidateCreate(CandidateBase):
    """Schema for creating a new Candidate."""

    resume_path: Optional[str] = None
    cover_letter_path: Optional[str] = None
    status: CandidateStatus = CandidateStatus.NEW
    rating: Optional[int] = None


# Schema for updating a candidate


class CandidateUpdate(BaseModel):
    """Schema for updating a Candidate."""

    first_name: Optional[str] = Field(None, min_length=1, max_length=100)
    last_name: Optional[str] = Field(None, min_length=1, max_length=100)
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    resume_path: Optional[str] = None
    cover_letter_path: Optional[str] = None
    status: Optional[CandidateStatus] = None
    source: Optional[str] = None
    rating: Optional[int] = None
    notes: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


# Schema for reading a candidate


class CandidateRead(CandidateBase):
    """Schema for reading a Candidate."""

    id: uuid.UUID
    resume_path: Optional[str] = None
    cover_letter_path: Optional[str] = None
    status: CandidateStatus
    application_date: datetime
    rating: Optional[int] = None
    tenant_id: uuid.UUID
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Base Interview Schema


class InterviewBase(BaseModel):
    """Base schema for Interview."""

    candidate_id: uuid.UUID
    title: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    interview_type: str
    scheduled_start: datetime
    scheduled_end: datetime
    location: Optional[str] = None
    interviewers: Optional[List[uuid.UUID]] = None
    metadata: Optional[Dict[str, Any]] = None


# Schema for creating a new interview


class InterviewCreate(InterviewBase):
    """Schema for creating a new Interview."""

    status: InterviewStatus = InterviewStatus.SCHEDULED
    feedback: Optional[str] = None
    rating: Optional[int] = None


# Schema for updating an interview


class InterviewUpdate(BaseModel):
    """Schema for updating an Interview."""

    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    interview_type: Optional[str] = None
    scheduled_start: Optional[datetime] = None
    scheduled_end: Optional[datetime] = None
    location: Optional[str] = None
    status: Optional[InterviewStatus] = None
    interviewers: Optional[List[uuid.UUID]] = None
    feedback: Optional[str] = None
    rating: Optional[int] = None
    metadata: Optional[Dict[str, Any]] = None


# Schema for reading an interview


class InterviewRead(InterviewBase):
    """Schema for reading an Interview."""

    id: uuid.UUID
    status: InterviewStatus
    feedback: Optional[str] = None
    rating: Optional[int] = None
    tenant_id: uuid.UUID
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Base Assessment Schema


class AssessmentBase(BaseModel):
    """Base schema for Assessment."""

    candidate_id: uuid.UUID
    title: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    assessment_type: AssessmentType
    due_date: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None


# Schema for creating a new assessment


class AssessmentCreate(AssessmentBase):
    """Schema for creating a new Assessment."""

    is_completed: bool = False
    score: Optional[float] = None
    max_score: Optional[float] = None
    passed: Optional[bool] = None
    feedback: Optional[str] = None


# Schema for updating an assessment


class AssessmentUpdate(BaseModel):
    """Schema for updating an Assessment."""

    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    assessment_type: Optional[AssessmentType] = None
    due_date: Optional[datetime] = None
    completed_date: Optional[datetime] = None
    is_completed: Optional[bool] = None
    score: Optional[float] = None
    max_score: Optional[float] = None
    passed: Optional[bool] = None
    feedback: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


# Schema for reading an assessment


class AssessmentRead(AssessmentBase):
    """Schema for reading an Assessment."""

    id: uuid.UUID
    assigned_date: datetime
    completed_date: Optional[datetime] = None
    is_completed: bool
    score: Optional[float] = None
    max_score: Optional[float] = None
    passed: Optional[bool] = None
    feedback: Optional[str] = None
    tenant_id: uuid.UUID
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Schema for job application


class JobApplicationCreate(BaseModel):
    """Schema for creating a job application."""

    job_id: uuid.UUID
    first_name: str = Field(..., min_length=1, max_length=100)
    last_name: str = Field(..., min_length=1, max_length=100)
    email: EmailStr
    phone: Optional[str] = None
    cover_letter: Optional[str] = None
    source: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
