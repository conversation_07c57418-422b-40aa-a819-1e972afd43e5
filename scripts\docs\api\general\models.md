# General - Models

**Categoria:** General
**Módulo:** Models
**Total de Endpoints:** 3
**Gerado em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [GET /api/models](#get-apimodels) - List Models
- [GET /api/v1/models](#get-apiv1models) - List Models V1
- [GET /v1/models](#get-v1models) - Models Compatibility

## 🔗 Endpoints Detalhados

### GET /api/models {#get-apimodels}

**Resumo:** List Models
**Descrição:** List available models for API compatibility.

This endpoint provides compatibility for clients expecting a /models endpoint.

**🔐 Autenticação:** Não requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/models"
```

---

### GET /api/v1/models {#get-apiv1models}

**Resumo:** List Models V1
**Descrição:** List available models for v1 API compatibility.

This endpoint provides compatibility for clients expecting a /v1/models endpoint.

**🔐 Autenticação:** Não requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/v1/models"
```

---

### GET /v1/models {#get-v1models}

**Resumo:** Models Compatibility
**Descrição:** Endpoint de compatibilidade para /v1/models.

**🔐 Autenticação:** Não requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/v1/models"
```

---
