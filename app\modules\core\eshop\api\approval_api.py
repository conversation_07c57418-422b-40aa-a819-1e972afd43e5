"""
EShop Product Approval API

Gerencia o sistema de aprovação de produtos para o marketplace,
incluindo submissão, aprovação, rejeição e solicitação de revisão.
"""

import asyncio
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.auth_dependencies import get_current_active_user
from app.modules.core.users.models.user import User
from app.websockets.manager import emit_to_tenant

from ..services.approval_service import ProductApprovalService
from ..models import (
    ProductApprovalHistory, ProductApprovalAction, ProductApprovalReason
)
from ..schemas.approval_schemas import (
    ProductApprovalHistoryResponse,
    ProductApprovalRequest,
    ProductApprovalResponse,
    ProductRejectionRequest,
    ProductRevisionRequest,
    ProductsPendingResponse
)

router = APIRouter(prefix="/approval", tags=["Product Approval"])


@router.post("/{product_id}/submit", response_model=ProductApprovalResponse)
async def submit_product_for_approval(
    product_id: UUID,
    request: ProductApprovalRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Submete produto para aprovação.
    
    Permite que vendors submetam seus produtos para aprovação
    pelos moderadores/admins do marketplace.
    """
    approval_service = ProductApprovalService(db)
    
    try:
        approval_history = await approval_service.submit_for_approval(
            product_id=product_id,
            submitted_by=current_user,
            comments=request.comments
        )
        
        # Notificação WebSocket em tempo real
        await emit_to_tenant(
            tenant_id=approval_history.tenant_id,
            event="product_submitted_for_approval",
            data={
                "product_id": str(product_id),
                "action": approval_history.action.value,
                "submitted_by": str(current_user.id),
                "timestamp": approval_history.created_at.isoformat()
            }
        )
        
        return ProductApprovalResponse(
            success=True,
            message="Produto submetido para aprovação com sucesso",
            approval_history=ProductApprovalHistoryResponse.from_orm(approval_history)
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


@router.post("/{product_id}/approve", response_model=ProductApprovalResponse)
async def approve_product(
    product_id: UUID,
    request: ProductApprovalRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Aprova um produto.
    
    Apenas admins e moderadores podem aprovar produtos.
    """
    # Verificar permissões (simplificado)
    if not current_user.system_role or current_user.system_role not in ["admin", "moderator"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Apenas admins e moderadores podem aprovar produtos"
        )
    
    approval_service = ProductApprovalService(db)
    
    try:
        approval_history = await approval_service.approve_product(
            product_id=product_id,
            approved_by=current_user,
            comments=request.comments,
            reason=request.reason or ProductApprovalReason.MEETS_STANDARDS
        )
        
        # Notificação WebSocket em tempo real
        await emit_to_tenant(
            tenant_id=approval_history.tenant_id,
            event="product_approved",
            data={
                "product_id": str(product_id),
                "action": approval_history.action.value,
                "approved_by": str(current_user.id),
                "timestamp": approval_history.created_at.isoformat()
            }
        )
        
        return ProductApprovalResponse(
            success=True,
            message="Produto aprovado com sucesso",
            approval_history=ProductApprovalHistoryResponse.from_orm(approval_history)
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


@router.post("/{product_id}/reject", response_model=ProductApprovalResponse)
async def reject_product(
    product_id: UUID,
    request: ProductRejectionRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Rejeita um produto.
    
    Apenas admins e moderadores podem rejeitar produtos.
    """
    # Verificar permissões
    if not current_user.system_role or current_user.system_role not in ["admin", "moderator"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Apenas admins e moderadores podem rejeitar produtos"
        )
    
    approval_service = ProductApprovalService(db)
    
    try:
        approval_history = await approval_service.reject_product(
            product_id=product_id,
            rejected_by=current_user,
            rejection_reason=request.rejection_reason,
            reason=request.reason or ProductApprovalReason.POLICY_VIOLATION,
            internal_notes=request.internal_notes
        )
        
        # Notificação WebSocket em tempo real
        await emit_to_tenant(
            tenant_id=approval_history.tenant_id,
            event="product_rejected",
            data={
                "product_id": str(product_id),
                "action": approval_history.action.value,
                "rejected_by": str(current_user.id),
                "rejection_reason": request.rejection_reason,
                "timestamp": approval_history.created_at.isoformat()
            }
        )
        
        return ProductApprovalResponse(
            success=True,
            message="Produto rejeitado",
            approval_history=ProductApprovalHistoryResponse.from_orm(approval_history)
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


@router.post("/{product_id}/request-revision", response_model=ProductApprovalResponse)
async def request_product_revision(
    product_id: UUID,
    request: ProductRevisionRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Solicita revisão de um produto.
    
    Permite que moderadores solicitem revisões sem rejeitar completamente.
    """
    # Verificar permissões
    if not current_user.system_role or current_user.system_role not in ["admin", "moderator"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Apenas admins e moderadores podem solicitar revisões"
        )
    
    approval_service = ProductApprovalService(db)
    
    try:
        approval_history = await approval_service.request_revision(
            product_id=product_id,
            requested_by=current_user,
            revision_notes=request.revision_notes,
            reason=request.reason or ProductApprovalReason.INCOMPLETE_INFO
        )
        
        # Notificação WebSocket em tempo real
        await emit_to_tenant(
            tenant_id=approval_history.tenant_id,
            event="product_revision_requested",
            data={
                "product_id": str(product_id),
                "action": approval_history.action.value,
                "requested_by": str(current_user.id),
                "revision_notes": request.revision_notes,
                "timestamp": approval_history.created_at.isoformat()
            }
        )
        
        return ProductApprovalResponse(
            success=True,
            message="Revisão solicitada com sucesso",
            approval_history=ProductApprovalHistoryResponse.from_orm(approval_history)
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


@router.get("/pending", response_model=ProductsPendingResponse)
async def get_products_pending_approval(
    page: int = Query(1, ge=1, description="Número da página"),
    per_page: int = Query(20, ge=1, le=100, description="Itens por página"),
    tenant_id: Optional[UUID] = Query(None, description="Filtrar por tenant"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Lista produtos pendentes de aprovação.
    
    Admins globais podem ver todos os produtos, admins de tenant
    veem apenas produtos do seu tenant.
    """
    # Verificar permissões
    if not current_user.system_role or current_user.system_role not in ["admin", "moderator"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Apenas admins e moderadores podem ver produtos pendentes"
        )
    
    # Se não é admin global, filtrar por tenant do usuário
    if current_user.system_role != "admin" and tenant_id is None:
        # Aqui deveria buscar o tenant do usuário (simplificado)
        tenant_id = None  # Implementar lógica de tenant do usuário
    
    approval_service = ProductApprovalService(db)
    
    try:
        products, total = await approval_service.get_products_pending_approval(
            tenant_id=tenant_id,
            page=page,
            per_page=per_page
        )
        
        return ProductsPendingResponse(
            products=products,
            total=total,
            page=page,
            per_page=per_page,
            total_pages=(total + per_page - 1) // per_page
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


@router.get("/{product_id}/history", response_model=List[ProductApprovalHistoryResponse])
async def get_product_approval_history(
    product_id: UUID,
    page: int = Query(1, ge=1, description="Número da página"),
    per_page: int = Query(10, ge=1, le=50, description="Itens por página"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Obtém histórico de aprovação de um produto.
    
    Vendors podem ver histórico dos seus produtos,
    admins podem ver histórico de qualquer produto.
    """
    approval_service = ProductApprovalService(db)
    
    try:
        # Verificar se o produto existe e se o usuário tem acesso
        product = await approval_service._get_product(product_id)
        if not product:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Produto não encontrado"
            )
        
        # Verificar permissões
        is_admin = current_user.system_role in ["admin", "moderator"]
        is_owner = product.vendor_id == current_user.id
        
        if not is_admin and not is_owner:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Sem permissão para ver histórico deste produto"
            )
        
        history, total = await approval_service.get_approval_history(
            product_id=product_id,
            page=page,
            per_page=per_page
        )
        
        # Filtrar informações sensíveis para vendors
        response_history = []
        for entry in history:
            entry_response = ProductApprovalHistoryResponse.from_orm(entry)
            
            # Vendors não devem ver notas internas
            if not is_admin:
                entry_response.internal_notes = None
            
            response_history.append(entry_response)
        
        return response_history
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )


@router.get("/stats/dashboard")
async def get_approval_dashboard_stats(
    tenant_id: Optional[UUID] = Query(None, description="Filtrar por tenant"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Obtém estatísticas para dashboard de aprovação.
    
    Retorna contadores de produtos por status de aprovação.
    """
    # Verificar permissões
    if not current_user.system_role or current_user.system_role not in ["admin", "moderator"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Apenas admins e moderadores podem ver estatísticas"
        )
    
    approval_service = ProductApprovalService(db)
    
    try:
        # Implementar estatísticas (simplificado por agora)
        pending_products, pending_total = await approval_service.get_products_pending_approval(
            tenant_id=tenant_id,
            page=1,
            per_page=1
        )
        
        return {
            "pending_approval": pending_total,
            "approved_today": 0,  # Implementar
            "rejected_today": 0,  # Implementar
            "total_processed": 0  # Implementar
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno: {str(e)}"
        )