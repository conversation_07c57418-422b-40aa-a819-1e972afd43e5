# Eshop - Categories

**Categoria:** Eshop
**Mó<PERSON>lo:** Categories
**Total de Endpoints:** 10
**Gerado em:** 27/06/2025, 20:20:26

## 📋 Endpoints

- [GET /api/eshop/categories/](#get-apieshopcategories) - List Categories
- [POST /api/eshop/categories/](#post-apieshopcategories) - Create Category
- [GET /api/eshop/categories/tree](#get-apieshopcategoriestree) - Read Category Tree
- [GET /api/eshop/categories/universal](#get-apieshopcategoriesuniversal) - List Universal Categories
- [POST /api/eshop/categories/universal](#post-apieshopcategoriesuniversal) - Create Universal Category
- [DELETE /api/eshop/categories/universal/{category_id}](#delete-apieshopcategoriesuniversalcategory-id) - Delete Universal Category
- [PUT /api/eshop/categories/universal/{category_id}](#put-apieshopcategoriesuniversalcategory-id) - Update Universal Category
- [DELETE /api/eshop/categories/{category_id}](#delete-apieshopcategoriescategory-id) - Delete Category
- [GET /api/eshop/categories/{category_id}](#get-apieshopcategoriescategory-id) - Read Category
- [PUT /api/eshop/categories/{category_id}](#put-apieshopcategoriescategory-id) - Update Category

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### ProductCategoryResponse

**Descrição:** Schema for product category responses.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Category name |
| `description` | unknown | ❌ | Category description |
| `slug` | string | ✅ | URL-friendly category name |
| `parent_id` | unknown | ❌ | Parent category ID for subcategories |
| `display_order` | integer | ❌ | Display order |
| `is_active` | boolean | ❌ | Whether category is active |
| `is_featured` | boolean | ❌ | Whether category is featured |
| `meta_title` | unknown | ❌ | SEO meta title |
| `meta_description` | unknown | ❌ | SEO meta description |
| `image_url` | unknown | ❌ | Category image URL |
| `icon` | unknown | ❌ | Category icon |
| `id` | string | ✅ | - |
| `tenant_id` | unknown | ✅ | - |
| `product_count` | unknown | ❌ | Number of products in this category |

### ProductCategoryUpdate

**Descrição:** Schema for updating a product category.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | Category name |
| `description` | unknown | ❌ | Category description |
| `slug` | unknown | ❌ | URL-friendly category name |
| `parent_id` | unknown | ❌ | Parent category ID for subcategories |
| `display_order` | unknown | ❌ | Display order |
| `is_active` | unknown | ❌ | Whether category is active |
| `is_featured` | unknown | ❌ | Whether category is featured |
| `meta_title` | unknown | ❌ | SEO meta title |
| `meta_description` | unknown | ❌ | SEO meta description |
| `image_url` | unknown | ❌ | Category image URL |
| `icon` | unknown | ❌ | Category icon |

### eshopCategoryCreate

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `slug` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `parent_id` | unknown | ❌ | - |
| `market_type` | MarketType | ❌ | - |

### eshopCategoryRead

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `slug` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `parent_id` | unknown | ❌ | - |
| `market_type` | MarketType | ❌ | - |
| `id` | string | ✅ | - |

### eshopCategoryUpdate

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | - |
| `slug` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `parent_id` | unknown | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/eshop/categories/ {#get-apieshopcategories}

**Resumo:** List Categories
**Descrição:** List all categories, optionally filtered by market type

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `market_type` | string | query | ❌ | Filter categories by market type |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/categories/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/eshop/categories/ {#post-apieshopcategories}

**Resumo:** Create Category

**🔐 Autenticação:** Não requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [eshopCategoryCreate](#eshopcategorycreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [eshopCategoryRead](#eshopcategoryread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/eshop/categories/" \
  -H "Content-Type: application/json" \
  -d '{"example": "data"}'
```

---

### GET /api/eshop/categories/tree {#get-apieshopcategoriestree}

**Resumo:** Read Category Tree
**Descrição:** Retrieve the complete category tree structure.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `is_active` | string | query | ❌ | Filter by active status |
| `market_type` | string | query | ❌ | Filter categories by market type |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/categories/tree" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/eshop/categories/universal {#get-apieshopcategoriesuniversal}

**Resumo:** List Universal Categories
**Descrição:** List all universal categories for admin purposes, optionally filtered by market type

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `market_type` | string | query | ❌ | Filter categories by market type |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/categories/universal"
```

---

### POST /api/eshop/categories/universal {#post-apieshopcategoriesuniversal}

**Resumo:** Create Universal Category
**Descrição:** Create a universal category for admin purposes

**🔐 Autenticação:** Não requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [eshopCategoryCreate](#eshopcategorycreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [eshopCategoryRead](#eshopcategoryread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/eshop/categories/universal" \
  -H "Content-Type: application/json" \
  -d '{"example": "data"}'
```

---

### DELETE /api/eshop/categories/universal/{category_id} {#delete-apieshopcategoriesuniversalcategory-id}

**Resumo:** Delete Universal Category
**Descrição:** Delete a universal category for admin purposes (cascade delete for children)

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/eshop/categories/universal/{category_id}"
```

---

### PUT /api/eshop/categories/universal/{category_id} {#put-apieshopcategoriesuniversalcategory-id}

**Resumo:** Update Universal Category
**Descrição:** Update a universal category for admin purposes

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [eshopCategoryUpdate](#eshopcategoryupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [eshopCategoryRead](#eshopcategoryread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/eshop/categories/universal/{category_id}" \
  -H "Content-Type: application/json" \
  -d '{"example": "data"}'
```

---

### DELETE /api/eshop/categories/{category_id} {#delete-apieshopcategoriescategory-id}

**Resumo:** Delete Category
**Descrição:** Delete a specific category by ID.
Requires admin role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | The ID of the category to delete |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/eshop/categories/{category_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/eshop/categories/{category_id} {#get-apieshopcategoriescategory-id}

**Resumo:** Read Category
**Descrição:** Retrieve a specific category by ID.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | The ID of the category to retrieve |
| `include_children` | boolean | query | ❌ | Include child categories |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductCategoryResponse](#productcategoryresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/categories/{category_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/eshop/categories/{category_id} {#put-apieshopcategoriescategory-id}

**Resumo:** Update Category
**Descrição:** Update a specific category by ID.
Requires admin role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | The ID of the category to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ProductCategoryUpdate](#productcategoryupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductCategoryResponse](#productcategoryresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/eshop/categories/{category_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
