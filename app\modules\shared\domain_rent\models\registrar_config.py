"""Registrar Configuration model for the Domain Rent module."""

import uuid  # noqa: E402
import enum
from typing import TYPE_CHECKING

from sqlalchemy import (  # noqa: E402
    Column,
    String,
    Boolean,
    Enum,
    Index,
    JSON,
)
from sqlalchemy.dialects.postgresql import UUID  # noqa: E402

from app.db.base import Base  # noqa: E402


class RegistrarType(str, enum.Enum):
    """Type of domain registrar."""

    GODADDY = "godaddy"
    NAMECHEAP = "namecheap"
    OPENSRS = "opensrs"
    RESELLERCLUB = "resellerclub"


class RegistrarConfig(Base):
    """Registrar Configuration model.

    Stores configuration for domain registrars.
    """

    __tablename__ = "registrar_configs"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    registrar = Column(Enum(RegistrarType), nullable=False, index=True, unique=True)

    is_active = Column(<PERSON><PERSON><PERSON>, default=True)

    # Additional configuration data (JSON)
    config_data = Column(JSON, nullable=True)

    def __repr__(self):
        return (
            f"<RegistrarConfig(id={self.id}, "
            f"registrar='{self.registrar}', "
            f"is_active={self.is_active})>"
        )
