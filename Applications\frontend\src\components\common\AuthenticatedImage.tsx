'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { XMarkIcon, PhotoIcon } from '@heroicons/react/24/outline';
import { Loader2 } from 'lucide-react';
import Cookies from 'js-cookie';

export interface AuthenticatedImageProps {
  src: string;
  alt: string;
  className?: string;
  onLoad?: () => void;
  onError?: () => void;
  fallbackIcon?: React.ComponentType<{ className?: string }>;
  showErrorText?: boolean;
}

/**
 * Componente de imagem autenticada que suporta URLs do sistema de mídia
 * e FTP, com autenticação JWT automática e fallbacks graceful.
 * 
 * Consolidado das implementações em MediaManager, ImageUploader e ImageDisplay.
 */
export function AuthenticatedImage({
  src,
  alt,
  className = '',
  onLoad,
  onError,
  fallbackIcon: FallbackIcon = PhotoIcon,
  showErrorText = true
}: AuthenticatedImageProps) {
  const [imageSrc, setImageSrc] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  useEffect(() => {
    const loadAuthenticatedImage = async () => {
      if (!src) {
        setError(true);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(false);

        // Check if this is a blob URL (preview) - use directly
        if (src.startsWith('blob:')) {
          setImageSrc(src);
          setLoading(false);
          onLoad?.();
          return;
        }

        // Check if this is an external HTTP URL - use directly
        if (src.startsWith('http') && !src.includes('/api/')) {
          setImageSrc(src);
          setLoading(false);
          onLoad?.();
          return;
        }

        // Build full URL for relative API URLs
        let fullUrl = src;
        if (src.startsWith('/api/') && !src.startsWith('http')) {
          const baseUrl = (process.env.NEXT_PUBLIC_API_URL || 
            'http://localhost:8000').replace(/\/api$/, '');
          fullUrl = `${baseUrl}${src}`;
        }

        // Check if authentication is needed
        const isFTPUrl = src.includes('/api/modules/core/ftp_system/files/');
        const needsAuth = src.includes('/api/modules/core/media/download/') || 
          (src.startsWith('/api/') && !isFTPUrl);

        if (!needsAuth) {
          // Use URL directly for non-authenticated images
          setImageSrc(fullUrl);
          setLoading(false);
          onLoad?.();
          return;
        }

        // Get authentication token
        const token = Cookies.get('access_token');
        if (!token) {
          console.error('🔍 AuthenticatedImage - No access token available');
          throw new Error('No access token available');
        }

        // Prepare headers for authenticated request
        const headers: Record<string, string> = {};
        if (!isFTPUrl) {
          headers['Authorization'] = `Bearer ${token}`;
          
          // Add tenant ID header if available
          const tenantId = Cookies.get('tenant_id');
          if (tenantId) {
            headers['X-Tenant-ID'] = tenantId;
          }
        }

        // Fetch image with authentication
        const response = await fetch(fullUrl, { headers });

        if (!response.ok) {
          // Enhanced error handling for debugging
          if (response.status === 404) {
            console.warn('🔍 AuthenticatedImage - Image not found:', fullUrl);
            const uploadIdMatch = src.match(/\/download\/([a-f0-9-]+)/);
            if (uploadIdMatch) {
              console.warn('🔍 AuthenticatedImage - Upload ID:', uploadIdMatch[1]);
            }
          } else {
            console.error('🔍 AuthenticatedImage - Request failed:', 
              response.status, response.statusText);
          }

          throw new Error(`HTTP ${response.status}`);
        }

        // Convert response to blob and create object URL
        const blob = await response.blob();
        const imageUrl = URL.createObjectURL(blob);
        setImageSrc(imageUrl);
        setLoading(false);
        onLoad?.();

      } catch (err) {
        console.error('🔍 AuthenticatedImage - Failed to load:', err);
        setError(true);
        setLoading(false);
        onError?.();
      }
    };

    loadAuthenticatedImage();

    // Cleanup blob URL on unmount or src change
    return () => {
      if (imageSrc && imageSrc.startsWith('blob:')) {
        URL.revokeObjectURL(imageSrc);
      }
    };
  }, [src, onLoad, onError, imageSrc]);

  // Loading state
  if (loading) {
    return (
      <div className={`${className} flex items-center justify-center bg-gray-100`}>
        <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
      </div>
    );
  }

  // Error state
  if (error || !imageSrc) {
    return (
      <div className={`${className} flex items-center justify-center bg-gray-100`}>
        <div className="text-center text-gray-400">
          <FallbackIcon className="h-8 w-8 mx-auto mb-1" />
          {showErrorText && (
            <div className="text-xs">Erro ao carregar</div>
          )}
        </div>
      </div>
    );
  }

  // Success state - render image
  return (
    <div className={`relative ${className}`}>
      <Image
        src={imageSrc}
        alt={alt}
        fill
        className="object-cover"
        onLoad={onLoad}
        onError={onError}
      />
    </div>
  );
}

export default AuthenticatedImage;
