'use client';

import { useState, useEffect, useCallback } from 'react';
import {
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { KDSOrder } from '@/types/kds';

interface SimpleNotification {
  id: string;
  type: 'overdue' | 'ready' | 'alert';
  title: string;
  message: string;
  timestamp: string;
  priority: 'low' | 'medium' | 'high';
  autoHide?: boolean;
  duration?: number;
}

interface KDSSimpleNotificationsProps {
  orders: KDSOrder[];
  className?: string;
}

export default function KDSSimpleNotifications({
  orders,
  className = ''
}: KDSSimpleNotificationsProps) {
  const [notifications, setNotifications] = useState<SimpleNotification[]>([]);

  // Não detectar pedidos atrasados aqui - já é mostrado no header
  // useEffect(() => {
  //   // Removido para evitar informações repetitivas
  // }, [orders]);

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  }, []);

  const addNotification = useCallback((notification: SimpleNotification) => {
    setNotifications(prev => {
      // Evitar duplicatas
      const exists = prev.find(n => n.id === notification.id);
      if (exists) return prev;

      const newNotifications = [notification, ...prev].slice(0, 3); // Máximo 3 notificações

      // Auto-hide se configurado
      if (notification.autoHide && notification.duration) {
        setTimeout(() => {
          removeNotification(notification.id);
        }, notification.duration);
      }

      return newNotifications;
    });
  }, [removeNotification]);

  // Detectar pedidos prontos
  useEffect(() => {
    const readyOrders = orders.filter(order =>
      order.status === 'ready' &&
      !notifications.find(n => n.type === 'ready' && n.id.includes(order.id))
    );

    readyOrders.forEach(order => {
      addNotification({
        id: `ready-${order.id}-${Date.now()}`,
        type: 'ready',
        title: 'Pedido Pronto',
        message: `Pedido ${order.order_number} está pronto para entrega`,
        timestamp: new Date().toISOString(),
        priority: 'medium',
        autoHide: true,
        duration: 10000 // 10 segundos
      });
    });
  }, [orders, addNotification, notifications]);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'overdue':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      case 'ready':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-blue-500" />;
    }
  };

  const getNotificationColor = (type: string, priority: string) => {
    if (type === 'overdue') {
      return priority === 'high'
        ? 'border-red-500 bg-red-50 text-red-800'
        : 'border-orange-500 bg-orange-50 text-orange-800';
    }
    if (type === 'ready') {
      return 'border-green-500 bg-green-50 text-green-800';
    }
    return 'border-blue-500 bg-blue-50 text-blue-800';
  };

  if (notifications.length === 0) {
    return null;
  }

  return (
    <div className={`fixed top-20 right-4 space-y-2 z-50 ${className}`}>
      {notifications.map(notification => (
        <div
          key={notification.id}
          className={`
            max-w-sm p-4 rounded-lg border-l-4 shadow-lg
            ${getNotificationColor(notification.type, notification.priority)}
            animate-slide-in-right
          `}
        >
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-3">
              {getNotificationIcon(notification.type)}
              <div className="flex-1">
                <h4 className="font-semibold text-sm">
                  {notification.title}
                </h4>
                <p className="text-sm mt-1">
                  {notification.message}
                </p>
                <p className="text-xs opacity-75 mt-2">
                  {new Date(notification.timestamp).toLocaleTimeString()}
                </p>
              </div>
            </div>
            <button
              onClick={() => removeNotification(notification.id)}
              className="text-gray-400 hover:text-gray-600 ml-2"
            >
              <XMarkIcon className="h-4 w-4" />
            </button>
          </div>
        </div>
      ))}
    </div>
  );
}
