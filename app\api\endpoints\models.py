"""Models endpoint for API compatibility."""

from fastapi import APIRouter
from typing import Dict, Any, List

router = APIRouter()


@router.get("/models", tags=["Models"])
async def list_models() -> Dict[str, Any]:
    """List available models for API compatibility.
    
    This endpoint provides compatibility for clients expecting a /models endpoint.
    """
    return {
        "object": "list",
        "data": [
            {
                "id": "trix-api-v1",
                "object": "model",
                "created": 1677649963,
                "owned_by": "trix",
                "permission": [],
                "root": "trix-api-v1",
                "parent": None
            }
        ]
    }


@router.get("/v1/models", tags=["Models"])
async def list_models_v1() -> Dict[str, Any]:
    """List available models for v1 API compatibility.
    
    This endpoint provides compatibility for clients expecting a /v1/models endpoint.
    """
    return {
        "object": "list",
        "data": [
            {
                "id": "trix-api-v1",
                "object": "model",
                "created": 1677649963,
                "owned_by": "trix",
                "permission": [],
                "root": "trix-api-v1",
                "parent": None
            }
        ]
    }