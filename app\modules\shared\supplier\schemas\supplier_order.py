from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field
import uuid

from app.modules.shared.supplier.models.supplier_order import (
    OrderStatus, 
    ItemAvailability
)


# === Base Schemas ===

class SupplierOrderItemBase(BaseModel):
    name: str = Field(..., description="Nome do item")
    quantity_requested: int = Field(..., gt=0, description="Quantidade solicitada")
    unit_price: Optional[float] = Field(None, ge=0, description="Preço unitário")
    notes: Optional[str] = Field(None, description="Observações do item")


class SupplierOrderBase(BaseModel):
    supplier_id: uuid.UUID = Field(..., description="ID do supplier")
    notes: Optional[str] = Field(None, description="Observações do pedido")


# === Create Schemas ===

class SupplierOrderItemCreate(SupplierOrderItemBase):
    shopping_list_item_id: uuid.UUID = Field(..., description="ID do item da lista de compras")


class SupplierOrderCreate(SupplierOrderBase):
    items: List[SupplierOrderItemCreate] = Field(..., min_items=1, description="Itens do pedido")


# === Update Schemas ===

class SupplierOrderItemUpdate(BaseModel):
    quantity_available: Optional[int] = Field(None, ge=0, description="Quantidade disponível")
    quantity_shipped: Optional[int] = Field(None, ge=0, description="Quantidade enviada")
    availability: Optional[ItemAvailability] = Field(None, description="Status de disponibilidade")
    unit_price: Optional[float] = Field(None, ge=0, description="Preço unitário")
    notes: Optional[str] = Field(None, description="Observações do item")


class SupplierOrderUpdate(BaseModel):
    status: Optional[OrderStatus] = Field(None, description="Status do pedido")
    invoice_number: Optional[str] = Field(None, description="Número da fatura")
    delivery_note: Optional[str] = Field(None, description="Número do albarán")
    notes: Optional[str] = Field(None, description="Observações do pedido")


# === Response Schemas ===

class SupplierOrderItemRead(SupplierOrderItemBase):
    id: uuid.UUID
    order_id: uuid.UUID
    shopping_list_item_id: uuid.UUID
    quantity_available: Optional[int] = None
    quantity_shipped: Optional[int] = None
    total_price: Optional[float] = None
    availability: Optional[ItemAvailability] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class SupplierOrderRead(SupplierOrderBase):
    id: uuid.UUID
    tenant_id: uuid.UUID
    order_number: str
    status: OrderStatus
    invoice_number: Optional[str] = None
    delivery_note: Optional[str] = None
    total_amount: Optional[float] = None
    created_at: datetime
    updated_at: datetime
    confirmed_at: Optional[datetime] = None
    shipped_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class SupplierOrderWithItems(SupplierOrderRead):
    items: List[SupplierOrderItemRead] = []

    class Config:
        from_attributes = True


# === Supplier Response Schemas ===

class SupplierOrderItemResponse(BaseModel):
    id: uuid.UUID
    name: str
    quantity_requested: int
    quantity_available: Optional[int] = None
    quantity_shipped: Optional[int] = None
    unit_price: Optional[float] = None
    total_price: Optional[float] = None
    availability: Optional[ItemAvailability] = None
    notes: Optional[str] = None


class SupplierOrderResponse(BaseModel):
    id: uuid.UUID
    order_number: str
    status: OrderStatus
    tenant_name: str
    invoice_number: Optional[str] = None
    delivery_note: Optional[str] = None
    total_amount: Optional[float] = None
    created_at: datetime
    notes: Optional[str] = None
    items: List[SupplierOrderItemResponse] = []


# === Availability Update Schema ===

class ItemAvailabilityUpdate(BaseModel):
    item_id: uuid.UUID = Field(..., description="ID do item do pedido")
    availability: ItemAvailability = Field(..., description="Status de disponibilidade")
    quantity_available: Optional[int] = Field(None, ge=0, description="Quantidade disponível")
    quantity_shipped: Optional[int] = Field(None, ge=0, description="Quantidade enviada")
    unit_price: Optional[float] = Field(None, ge=0, description="Preço unitário")
    notes: Optional[str] = Field(None, description="Observações")


class OrderAvailabilityUpdate(BaseModel):
    order_id: uuid.UUID = Field(..., description="ID do pedido")
    items: List[ItemAvailabilityUpdate] = Field(..., min_items=1, description="Itens com disponibilidade")
    invoice_number: Optional[str] = Field(None, description="Número da fatura")
    delivery_note: Optional[str] = Field(None, description="Número do albarán")
    notes: Optional[str] = Field(None, description="Observações gerais")


# === History Schema ===

class SupplierOrderHistoryRead(BaseModel):
    id: uuid.UUID
    order_id: uuid.UUID
    previous_status: Optional[str] = None
    new_status: str
    changed_by: Optional[str] = None
    notes: Optional[str] = None
    created_at: datetime

    class Config:
        from_attributes = True


# === Statistics Schema ===

class SupplierOrderStats(BaseModel):
    total_orders: int = 0
    pending_orders: int = 0
    confirmed_orders: int = 0
    shipped_orders: int = 0
    delivered_orders: int = 0
    total_amount: float = 0.0
    avg_order_value: float = 0.0
