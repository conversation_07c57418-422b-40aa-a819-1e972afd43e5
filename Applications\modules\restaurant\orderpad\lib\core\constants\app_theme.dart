/// Application theme constants and configurations
/// This file contains all theme definitions, including light and dark themes,
/// component themes, and theme utilities for consistent styling throughout the application.

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'app_colors.dart';
import 'app_typography.dart';
import 'app_dimensions.dart';
import 'app_spacing.dart';
import 'app_shadows.dart';
import 'app_borders.dart';
import 'app_animations.dart';

/// Application theme data
class AppTheme {
  // Theme mode
  static const ThemeMode defaultThemeMode = ThemeMode.system;
  
  // Theme brightness
  static const Brightness lightBrightness = Brightness.light;
  static const Brightness darkBrightness = Brightness.dark;
  
  /// Light theme
  static ThemeData get lightTheme {
    final colorScheme = OrderPadColorScheme.light;
    
    return ThemeData(
      useMaterial3: true,
      brightness: lightBrightness,
      colorScheme: colorScheme,
      
      // Typography
      textTheme: AppTextTheme.lightTextTheme,
      primaryTextTheme: AppTextTheme.lightTextTheme,
      
      // App bar theme
      appBarTheme: _lightAppBarTheme(colorScheme),
      
      // Bottom navigation bar theme
      bottomNavigationBarTheme: _lightBottomNavigationBarTheme(colorScheme),
      
      // Navigation bar theme
      navigationBarTheme: _lightNavigationBarTheme(colorScheme),
      
      // Navigation rail theme
      navigationRailTheme: _lightNavigationRailTheme(colorScheme),
      
      // Tab bar theme
      tabBarTheme: _lightTabBarTheme(colorScheme),
      
      // Drawer theme
      drawerTheme: _lightDrawerTheme(colorScheme),
      
      // Card theme
      cardTheme: _lightCardTheme(colorScheme),
      
      // List tile theme
      listTileTheme: _lightListTileTheme(colorScheme),
      
      // Elevated button theme
      elevatedButtonTheme: _lightElevatedButtonTheme(colorScheme),
      
      // Outlined button theme
      outlinedButtonTheme: _lightOutlinedButtonTheme(colorScheme),
      
      // Text button theme
      textButtonTheme: _lightTextButtonTheme(colorScheme),
      
      // Icon button theme
      iconButtonTheme: _lightIconButtonTheme(colorScheme),
      
      // Floating action button theme
      floatingActionButtonTheme: _lightFloatingActionButtonTheme(colorScheme),
      
      // Chip theme
      chipTheme: _lightChipTheme(colorScheme),
      
      // Input decoration theme
      inputDecorationTheme: _lightInputDecorationTheme(colorScheme),
      
      // Switch theme
      switchTheme: _lightSwitchTheme(colorScheme),
      
      // Checkbox theme
      checkboxTheme: _lightCheckboxTheme(colorScheme),
      radioTheme: _lightRadioTheme(colorScheme),
      sliderTheme: _lightSliderTheme(colorScheme),
      progressIndicatorTheme: _lightProgressIndicatorTheme(colorScheme),
      snackBarTheme: _lightSnackBarTheme(colorScheme),
      dialogTheme: _lightDialogTheme(colorScheme),
      bottomSheetTheme: _lightBottomSheetTheme(colorScheme),
      tooltipTheme: _lightTooltipTheme(colorScheme),
      dividerTheme: _lightDividerTheme(colorScheme),
      popupMenuTheme: _lightPopupMenuTheme(colorScheme),
      radioTheme: _lightRadioTheme(colorScheme),
      
      // Slider theme
      sliderTheme: _lightSliderTheme(colorScheme),
      
      // Progress indicator theme
      progressIndicatorTheme: _lightProgressIndicatorTheme(colorScheme),
      
      // Dialog theme
      dialogTheme: _lightDialogTheme(colorScheme),
      
      // Bottom sheet theme
      bottomSheetTheme: _lightBottomSheetTheme(colorScheme),
      
      // Snack bar theme
      snackBarTheme: _lightSnackBarTheme(colorScheme),
      
      // Banner theme
      bannerTheme: _lightBannerTheme(colorScheme),
      
      // Divider theme
      dividerTheme: _lightDividerTheme(colorScheme),
      
      // Tooltip theme
      tooltipTheme: _lightTooltipTheme(colorScheme),
      
      // Popup menu theme
      popupMenuTheme: _lightPopupMenuTheme(colorScheme),
      
      // Menu theme
      menuTheme: _lightMenuTheme(colorScheme),
      
      // Data table theme
      dataTableTheme: _lightDataTableTheme(colorScheme),
      
      // Expansion tile theme
      expansionTileTheme: _lightExpansionTileTheme(colorScheme),
      
      // Search bar theme
      searchBarTheme: _lightSearchBarTheme(colorScheme),
      
      // Search view theme
      searchViewTheme: _lightSearchViewTheme(colorScheme),
      
      // Badge theme
      badgeTheme: _lightBadgeTheme(colorScheme),
      
      // Date picker theme
      datePickerTheme: _lightDatePickerTheme(colorScheme),
      
      // Time picker theme
      timePickerTheme: _lightTimePickerTheme(colorScheme),
      
      // Scrollbar theme
      scrollbarTheme: _lightScrollbarTheme(colorScheme),
      
      // Page transitions theme
      pageTransitionsTheme: _pageTransitionsTheme,
      
      // Visual density
      visualDensity: VisualDensity.adaptivePlatformDensity,
      
      // Material tap target size
      materialTapTargetSize: MaterialTapTargetSize.adaptivePlatformDensity,
      
      // Platform brightness
      platform: TargetPlatform.android,
      
      // Splash factory
      splashFactory: InkRipple.splashFactory,
      
      // Highlight color
      highlightColor: colorScheme.primary.withOpacity(0.12),
      
      // Splash color
      splashColor: colorScheme.primary.withOpacity(0.12),
      
      // Hover color
      hoverColor: colorScheme.onSurface.withOpacity(0.08),
      
      // Focus color
      focusColor: colorScheme.onSurface.withOpacity(0.12),
      
      // Unselected widget color
      unselectedWidgetColor: colorScheme.onSurface.withOpacity(0.6),
      
      // Disabled color
      disabledColor: colorScheme.onSurface.withOpacity(0.38),
      
      // Secondary header color
      secondaryHeaderColor: colorScheme.surfaceVariant,
      
      // Background color
      scaffoldBackgroundColor: colorScheme.background,
      
      // Canvas color
      canvasColor: colorScheme.surface,
      
      // Card color
      cardColor: colorScheme.surface,
      
      // Dialog background color
      dialogBackgroundColor: colorScheme.surface,
      
      // Indicator color
      indicatorColor: colorScheme.primary,
      
      // Hint color
      hintColor: colorScheme.onSurface.withOpacity(0.6),
      
      // Error color
      errorColor: colorScheme.error,
      
      // Shadow color
      shadowColor: colorScheme.shadow,
      
      // Surface tint color
      surfaceTintColor: colorScheme.surfaceTint,
      
      // Extensions
      extensions: [
        _lightCustomColors(colorScheme),
      ],
    );
  }
  
  /// Dark theme
  static ThemeData get darkTheme {
    final colorScheme = OrderPadColorScheme.dark;
    
    return ThemeData(
      useMaterial3: true,
      brightness: darkBrightness,
      colorScheme: colorScheme,
      
      // Typography
      textTheme: AppTextTheme.darkTextTheme,
      primaryTextTheme: AppTextTheme.darkTextTheme,
      
      // App bar theme
      appBarTheme: _darkAppBarTheme(colorScheme),
      
      // Bottom navigation bar theme
      bottomNavigationBarTheme: _darkBottomNavigationBarTheme(colorScheme),
      
      // Navigation bar theme
      navigationBarTheme: _darkNavigationBarTheme(colorScheme),
      
      // Navigation rail theme
      navigationRailTheme: _darkNavigationRailTheme(colorScheme),
      
      // Tab bar theme
      tabBarTheme: _darkTabBarTheme(colorScheme),
      
      // Drawer theme
      drawerTheme: _darkDrawerTheme(colorScheme),
      
      // Card theme
      cardTheme: _darkCardTheme(colorScheme),
      
      // List tile theme
      listTileTheme: _darkListTileTheme(colorScheme),
      
      // Elevated button theme
      elevatedButtonTheme: _darkElevatedButtonTheme(colorScheme),
      
      // Outlined button theme
      outlinedButtonTheme: _darkOutlinedButtonTheme(colorScheme),
      
      // Text button theme
      textButtonTheme: _darkTextButtonTheme(colorScheme),
      
      // Icon button theme
      iconButtonTheme: _darkIconButtonTheme(colorScheme),
      
      // Floating action button theme
      floatingActionButtonTheme: _darkFloatingActionButtonTheme(colorScheme),
      
      // Chip theme
      chipTheme: _darkChipTheme(colorScheme),
      
      // Input decoration theme
      inputDecorationTheme: _darkInputDecorationTheme(colorScheme),
      
      // Switch theme
      switchTheme: _darkSwitchTheme(colorScheme),
      
      // Checkbox theme
      checkboxTheme: _darkCheckboxTheme(colorScheme),
      radioTheme: _darkRadioTheme(colorScheme),
      sliderTheme: _darkSliderTheme(colorScheme),
      progressIndicatorTheme: _darkProgressIndicatorTheme(colorScheme),
      snackBarTheme: _darkSnackBarTheme(colorScheme),
      dialogTheme: _darkDialogTheme(colorScheme),
      bottomSheetTheme: _darkBottomSheetTheme(colorScheme),
      tooltipTheme: _darkTooltipTheme(colorScheme),
      dividerTheme: _darkDividerTheme(colorScheme),
      popupMenuTheme: _darkPopupMenuTheme(colorScheme),
      radioTheme: _darkRadioTheme(colorScheme),
      
      // Slider theme
      sliderTheme: _darkSliderTheme(colorScheme),
      
      // Progress indicator theme
      progressIndicatorTheme: _darkProgressIndicatorTheme(colorScheme),
      
      // Dialog theme
      dialogTheme: _darkDialogTheme(colorScheme),
      
      // Bottom sheet theme
      bottomSheetTheme: _darkBottomSheetTheme(colorScheme),
      
      // Snack bar theme
      snackBarTheme: _darkSnackBarTheme(colorScheme),
      
      // Banner theme
      bannerTheme: _darkBannerTheme(colorScheme),
      
      // Divider theme
      dividerTheme: _darkDividerTheme(colorScheme),
      
      // Tooltip theme
      tooltipTheme: _darkTooltipTheme(colorScheme),
      
      // Popup menu theme
      popupMenuTheme: _darkPopupMenuTheme(colorScheme),
      
      // Menu theme
      menuTheme: _darkMenuTheme(colorScheme),
      
      // Data table theme
      dataTableTheme: _darkDataTableTheme(colorScheme),
      
      // Expansion tile theme
      expansionTileTheme: _darkExpansionTileTheme(colorScheme),
      
      // Search bar theme
      searchBarTheme: _darkSearchBarTheme(colorScheme),
      
      // Search view theme
      searchViewTheme: _darkSearchViewTheme(colorScheme),
      
      // Badge theme
      badgeTheme: _darkBadgeTheme(colorScheme),
      
      // Date picker theme
      datePickerTheme: _darkDatePickerTheme(colorScheme),
      
      // Time picker theme
      timePickerTheme: _darkTimePickerTheme(colorScheme),
      
      // Scrollbar theme
      scrollbarTheme: _darkScrollbarTheme(colorScheme),
      
      // Page transitions theme
      pageTransitionsTheme: _pageTransitionsTheme,
      
      // Visual density
      visualDensity: VisualDensity.adaptivePlatformDensity,
      
      // Material tap target size
      materialTapTargetSize: MaterialTapTargetSize.adaptivePlatformDensity,
      
      // Platform brightness
      platform: TargetPlatform.android,
      
      // Splash factory
      splashFactory: InkRipple.splashFactory,
      
      // Highlight color
      highlightColor: colorScheme.primary.withOpacity(0.12),
      
      // Splash color
      splashColor: colorScheme.primary.withOpacity(0.12),
      
      // Hover color
      hoverColor: colorScheme.onSurface.withOpacity(0.08),
      
      // Focus color
      focusColor: colorScheme.onSurface.withOpacity(0.12),
      
      // Unselected widget color
      unselectedWidgetColor: colorScheme.onSurface.withOpacity(0.6),
      
      // Disabled color
      disabledColor: colorScheme.onSurface.withOpacity(0.38),
      
      // Secondary header color
      secondaryHeaderColor: colorScheme.surfaceVariant,
      
      // Background color
      scaffoldBackgroundColor: colorScheme.background,
      
      // Canvas color
      canvasColor: colorScheme.surface,
      
      // Card color
      cardColor: colorScheme.surface,
      
      // Dialog background color
      dialogBackgroundColor: colorScheme.surface,
      
      // Indicator color
      indicatorColor: colorScheme.primary,
      
      // Hint color
      hintColor: colorScheme.onSurface.withOpacity(0.6),
      
      // Error color
      errorColor: colorScheme.error,
      
      // Shadow color
      shadowColor: colorScheme.shadow,
      
      // Surface tint color
      surfaceTintColor: colorScheme.surfaceTint,
      
      // Extensions
      extensions: [
        _darkCustomColors(colorScheme),
      ],
    );
  }
  
  // Light theme component themes
  static AppBarTheme _lightAppBarTheme(ColorScheme colorScheme) {
    return AppBarTheme(
      backgroundColor: colorScheme.surface,
      foregroundColor: colorScheme.onSurface,
      elevation: AppDimensions.elevationAppBar,
      shadowColor: colorScheme.shadow,
      surfaceTintColor: colorScheme.surfaceTint,
      centerTitle: true,
      titleSpacing: AppSpacing.md,
      toolbarHeight: AppDimensions.appBarHeight,
      titleTextStyle: AppTextStyles.headlineSmall.copyWith(
        color: colorScheme.onSurface,
        fontWeight: AppFontWeights.semiBold,
      ),
      iconTheme: IconThemeData(
        color: colorScheme.onSurface,
        size: AppDimensions.iconMd,
      ),
      actionsIconTheme: IconThemeData(
        color: colorScheme.onSurface,
        size: AppDimensions.iconMd,
      ),
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
        systemNavigationBarColor: colorScheme.surface,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
  }
  
  static AppBarTheme _darkAppBarTheme(ColorScheme colorScheme) {
    return AppBarTheme(
      backgroundColor: colorScheme.surface,
      foregroundColor: colorScheme.onSurface,
      elevation: AppDimensions.elevationAppBar,
      shadowColor: colorScheme.shadow,
      surfaceTintColor: colorScheme.surfaceTint,
      centerTitle: true,
      titleSpacing: AppSpacing.md,
      toolbarHeight: AppDimensions.appBarHeight,
      titleTextStyle: AppTextStyles.headlineSmall.copyWith(
        color: colorScheme.onSurface,
        fontWeight: AppFontWeights.semiBold,
      ),
      iconTheme: IconThemeData(
        color: colorScheme.onSurface,
        size: AppDimensions.iconMd,
      ),
      actionsIconTheme: IconThemeData(
        color: colorScheme.onSurface,
        size: AppDimensions.iconMd,
      ),
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        statusBarBrightness: Brightness.dark,
        systemNavigationBarColor: colorScheme.surface,
        systemNavigationBarIconBrightness: Brightness.light,
      ),
    );
  }
  
  static BottomNavigationBarThemeData _lightBottomNavigationBarTheme(ColorScheme colorScheme) {
    return BottomNavigationBarThemeData(
      backgroundColor: colorScheme.surface,
      elevation: AppDimensions.elevationBottomNavigation,
      selectedItemColor: colorScheme.primary,
      unselectedItemColor: colorScheme.onSurface.withOpacity(0.6),
      selectedIconTheme: IconThemeData(
        color: colorScheme.primary,
        size: AppDimensions.iconMd,
      ),
      unselectedIconTheme: IconThemeData(
        color: colorScheme.onSurface.withOpacity(0.6),
        size: AppDimensions.iconMd,
      ),
      selectedLabelStyle: AppTextStyles.labelSmall.copyWith(
        color: colorScheme.primary,
        fontWeight: AppFontWeights.medium,
      ),
      unselectedLabelStyle: AppTextStyles.labelSmall.copyWith(
        color: colorScheme.onSurface.withOpacity(0.6),
        fontWeight: AppFontWeights.regular,
      ),
      type: BottomNavigationBarType.fixed,
      showSelectedLabels: true,
      showUnselectedLabels: true,
    );
  }
  
  static BottomNavigationBarThemeData _darkBottomNavigationBarTheme(ColorScheme colorScheme) {
    return BottomNavigationBarThemeData(
      backgroundColor: colorScheme.surface,
      elevation: AppDimensions.elevationBottomNavigation,
      selectedItemColor: colorScheme.primary,
      unselectedItemColor: colorScheme.onSurface.withOpacity(0.6),
      selectedIconTheme: IconThemeData(
        color: colorScheme.primary,
        size: AppDimensions.iconMd,
      ),
      unselectedIconTheme: IconThemeData(
        color: colorScheme.onSurface.withOpacity(0.6),
        size: AppDimensions.iconMd,
      ),
      selectedLabelStyle: AppTextStyles.labelSmall.copyWith(
        color: colorScheme.primary,
        fontWeight: AppFontWeights.medium,
      ),
      unselectedLabelStyle: AppTextStyles.labelSmall.copyWith(
        color: colorScheme.onSurface.withOpacity(0.6),
        fontWeight: AppFontWeights.regular,
      ),
      type: BottomNavigationBarType.fixed,
      showSelectedLabels: true,
      showUnselectedLabels: true,
    );
  }
  
  static NavigationBarThemeData _lightNavigationBarTheme(ColorScheme colorScheme) {
    return NavigationBarThemeData(
      backgroundColor: colorScheme.surface,
      elevation: AppDimensions.elevationBottomNavigation,
      height: AppDimensions.bottomNavigationHeight,
      indicatorColor: colorScheme.secondaryContainer,
      surfaceTintColor: colorScheme.surfaceTint,
      shadowColor: colorScheme.shadow,
      iconTheme: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.selected)) {
          return IconThemeData(
            color: colorScheme.onSecondaryContainer,
            size: AppDimensions.iconMd,
          );
  }
  
  static RadioThemeData _lightRadioTheme(ColorScheme colorScheme) {
    return RadioThemeData(
      fillColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.disabled)) {
          return colorScheme.onSurface.withOpacity(0.38);
        }
        if (states.contains(MaterialState.selected)) {
          return colorScheme.primary;
        }
        return colorScheme.outline;
      }),
      overlayColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.hovered)) {
          return colorScheme.onSurface.withOpacity(0.08);
        }
        if (states.contains(MaterialState.focused)) {
          return colorScheme.onSurface.withOpacity(0.12);
        }
        if (states.contains(MaterialState.pressed)) {
          return colorScheme.onSurface.withOpacity(0.12);
        }
        return null;
      }),
      splashRadius: AppDimensions.radioSplashRadius,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      visualDensity: VisualDensity.compact,
    );
  }
  
  static RadioThemeData _darkRadioTheme(ColorScheme colorScheme) {
    return RadioThemeData(
      fillColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.disabled)) {
          return colorScheme.onSurface.withOpacity(0.38);
        }
        if (states.contains(MaterialState.selected)) {
          return colorScheme.primary;
        }
        return colorScheme.outline;
      }),
      overlayColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.hovered)) {
          return colorScheme.onSurface.withOpacity(0.08);
        }
        if (states.contains(MaterialState.focused)) {
          return colorScheme.onSurface.withOpacity(0.12);
        }
        if (states.contains(MaterialState.pressed)) {
          return colorScheme.onSurface.withOpacity(0.12);
        }
        return null;
      }),
      splashRadius: AppDimensions.radioSplashRadius,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      visualDensity: VisualDensity.compact,
    );
  }
  
  static SliderThemeData _lightSliderTheme(ColorScheme colorScheme) {
    return SliderThemeData(
      activeTrackColor: colorScheme.primary,
      inactiveTrackColor: colorScheme.primary.withOpacity(0.24),
      disabledActiveTrackColor: colorScheme.onSurface.withOpacity(0.32),
      disabledInactiveTrackColor: colorScheme.onSurface.withOpacity(0.12),
      activeTickMarkColor: colorScheme.onPrimary.withOpacity(0.54),
      inactiveTickMarkColor: colorScheme.primary.withOpacity(0.54),
      disabledActiveTickMarkColor: colorScheme.onSurface.withOpacity(0.12),
      disabledInactiveTickMarkColor: colorScheme.onSurface.withOpacity(0.12),
      thumbColor: colorScheme.primary,
      overlayColor: colorScheme.primary.withOpacity(0.12),
      valueIndicatorColor: colorScheme.primary,
      valueIndicatorTextStyle: AppTextStyles.bodySmall.copyWith(
        color: colorScheme.onPrimary,
      ),
      showValueIndicator: ShowValueIndicator.onlyForDiscrete,
      thumbShape: const RoundSliderThumbShape(
        enabledThumbRadius: AppDimensions.sliderThumbRadius,
      ),
      overlayShape: const RoundSliderOverlayShape(
        overlayRadius: AppDimensions.sliderOverlayRadius,
      ),
      trackHeight: AppDimensions.sliderTrackHeight,
      trackShape: const RoundedRectSliderTrackShape(),
      tickMarkShape: const RoundSliderTickMarkShape(
        tickMarkRadius: AppDimensions.sliderTickMarkRadius,
      ),
      valueIndicatorShape: const PaddleSliderValueIndicatorShape(),
      rangeThumbShape: const RoundRangeSliderThumbShape(
        enabledThumbRadius: AppDimensions.sliderThumbRadius,
      ),
      rangeTrackShape: const RoundedRectRangeSliderTrackShape(),
      rangeValueIndicatorShape: const PaddleRangeSliderValueIndicatorShape(),
    );
  }
  
  static SliderThemeData _darkSliderTheme(ColorScheme colorScheme) {
    return SliderThemeData(
      activeTrackColor: colorScheme.primary,
      inactiveTrackColor: colorScheme.primary.withOpacity(0.24),
      disabledActiveTrackColor: colorScheme.onSurface.withOpacity(0.32),
      disabledInactiveTrackColor: colorScheme.onSurface.withOpacity(0.12),
      activeTickMarkColor: colorScheme.onPrimary.withOpacity(0.54),
      inactiveTickMarkColor: colorScheme.primary.withOpacity(0.54),
      disabledActiveTickMarkColor: colorScheme.onSurface.withOpacity(0.12),
      disabledInactiveTickMarkColor: colorScheme.onSurface.withOpacity(0.12),
      thumbColor: colorScheme.primary,
      overlayColor: colorScheme.primary.withOpacity(0.12),
      valueIndicatorColor: colorScheme.primary,
      valueIndicatorTextStyle: AppTextStyles.bodySmall.copyWith(
        color: colorScheme.onPrimary,
      ),
      showValueIndicator: ShowValueIndicator.onlyForDiscrete,
      thumbShape: const RoundSliderThumbShape(
        enabledThumbRadius: AppDimensions.sliderThumbRadius,
      ),
      overlayShape: const RoundSliderOverlayShape(
        overlayRadius: AppDimensions.sliderOverlayRadius,
      ),
      trackHeight: AppDimensions.sliderTrackHeight,
      trackShape: const RoundedRectSliderTrackShape(),
      tickMarkShape: const RoundSliderTickMarkShape(
        tickMarkRadius: AppDimensions.sliderTickMarkRadius,
      ),
      valueIndicatorShape: const PaddleSliderValueIndicatorShape(),
      rangeThumbShape: const RoundRangeSliderThumbShape(
        enabledThumbRadius: AppDimensions.sliderThumbRadius,
      ),
      rangeTrackShape: const RoundedRectRangeSliderTrackShape(),
      rangeValueIndicatorShape: const PaddleRangeSliderValueIndicatorShape(),
    );
  }
  
  static ProgressIndicatorThemeData _lightProgressIndicatorTheme(ColorScheme colorScheme) {
    return ProgressIndicatorThemeData(
      color: colorScheme.primary,
      linearTrackColor: colorScheme.primary.withOpacity(0.24),
      circularTrackColor: colorScheme.primary.withOpacity(0.24),
      refreshBackgroundColor: colorScheme.surface,
      linearMinHeight: AppDimensions.progressIndicatorHeight,
    );
  }
  
  static ProgressIndicatorThemeData _darkProgressIndicatorTheme(ColorScheme colorScheme) {
    return ProgressIndicatorThemeData(
      color: colorScheme.primary,
      linearTrackColor: colorScheme.primary.withOpacity(0.24),
      circularTrackColor: colorScheme.primary.withOpacity(0.24),
      refreshBackgroundColor: colorScheme.surface,
      linearMinHeight: AppDimensions.progressIndicatorHeight,
    );
   }
   
   static SnackBarThemeData _lightSnackBarTheme(ColorScheme colorScheme) {
     return SnackBarThemeData(
       backgroundColor: colorScheme.inverseSurface,
       contentTextStyle: AppTextStyles.bodyMedium.copyWith(
         color: colorScheme.onInverseSurface,
       ),
       actionTextColor: colorScheme.inversePrimary,
       disabledActionTextColor: colorScheme.onInverseSurface.withOpacity(0.38),
       shape: RoundedRectangleBorder(
         borderRadius: AppBorderRadius.md,
       ),
       behavior: SnackBarBehavior.floating,
       elevation: AppElevations.snackBar,
       actionOverflowThreshold: 0.25,
       showCloseIcon: false,
       closeIconColor: colorScheme.onInverseSurface,
     );
   }
   
   static SnackBarThemeData _darkSnackBarTheme(ColorScheme colorScheme) {
     return SnackBarThemeData(
       backgroundColor: colorScheme.inverseSurface,
       contentTextStyle: AppTextStyles.bodyMedium.copyWith(
         color: colorScheme.onInverseSurface,
       ),
       actionTextColor: colorScheme.inversePrimary,
       disabledActionTextColor: colorScheme.onInverseSurface.withOpacity(0.38),
       shape: RoundedRectangleBorder(
         borderRadius: AppBorderRadius.md,
       ),
       behavior: SnackBarBehavior.floating,
       elevation: AppElevations.snackBar,
       actionOverflowThreshold: 0.25,
       showCloseIcon: false,
       closeIconColor: colorScheme.onInverseSurface,
     );
   }
   
   static DialogTheme _lightDialogTheme(ColorScheme colorScheme) {
     return DialogTheme(
       backgroundColor: colorScheme.surface,
       surfaceTintColor: colorScheme.surfaceTint,
       elevation: AppElevations.dialog,
       shadowColor: AppShadows.dialog.color,
       shape: RoundedRectangleBorder(
         borderRadius: AppBorderRadius.lg,
       ),
       alignment: Alignment.center,
       titleTextStyle: AppTextStyles.headlineSmall.copyWith(
         color: colorScheme.onSurface,
       ),
       contentTextStyle: AppTextStyles.bodyMedium.copyWith(
         color: colorScheme.onSurfaceVariant,
       ),
       actionsPadding: AppPadding.dialogActions,
       buttonPadding: AppPadding.dialogButton,
       iconColor: colorScheme.secondary,
     );
   }
   
   static DialogTheme _darkDialogTheme(ColorScheme colorScheme) {
     return DialogTheme(
       backgroundColor: colorScheme.surface,
       surfaceTintColor: colorScheme.surfaceTint,
       elevation: AppElevations.dialog,
       shadowColor: AppShadowsDark.dialog.color,
       shape: RoundedRectangleBorder(
         borderRadius: AppBorderRadius.lg,
       ),
       alignment: Alignment.center,
       titleTextStyle: AppTextStyles.headlineSmall.copyWith(
         color: colorScheme.onSurface,
       ),
       contentTextStyle: AppTextStyles.bodyMedium.copyWith(
         color: colorScheme.onSurfaceVariant,
       ),
       actionsPadding: AppPadding.dialogActions,
       buttonPadding: AppPadding.dialogButton,
       iconColor: colorScheme.secondary,
     );
   }
   
   static BottomSheetThemeData _lightBottomSheetTheme(ColorScheme colorScheme) {
     return BottomSheetThemeData(
       backgroundColor: colorScheme.surface,
       surfaceTintColor: colorScheme.surfaceTint,
       elevation: AppElevations.bottomSheet,
       modalElevation: AppElevations.bottomSheetModal,
       shadowColor: AppShadows.bottomSheet.color,
       modalBackgroundColor: colorScheme.surface,
       shape: const RoundedRectangleBorder(
         borderRadius: BorderRadius.vertical(
           top: Radius.circular(AppDimensions.bottomSheetBorderRadius),
         ),
       ),
       clipBehavior: Clip.antiAlias,
       constraints: const BoxConstraints(
         maxWidth: AppDimensions.bottomSheetMaxWidth,
       ),
       showDragHandle: true,
       dragHandleColor: colorScheme.onSurfaceVariant.withOpacity(0.4),
       dragHandleSize: const Size(
         AppDimensions.bottomSheetDragHandleWidth,
         AppDimensions.bottomSheetDragHandleHeight,
       ),
     );
   }
   
   static BottomSheetThemeData _darkBottomSheetTheme(ColorScheme colorScheme) {
     return BottomSheetThemeData(
       backgroundColor: colorScheme.surface,
       surfaceTintColor: colorScheme.surfaceTint,
       elevation: AppElevations.bottomSheet,
       modalElevation: AppElevations.bottomSheetModal,
       shadowColor: AppShadowsDark.bottomSheet.color,
       modalBackgroundColor: colorScheme.surface,
       shape: const RoundedRectangleBorder(
         borderRadius: BorderRadius.vertical(
           top: Radius.circular(AppDimensions.bottomSheetBorderRadius),
         ),
       ),
       clipBehavior: Clip.antiAlias,
       constraints: const BoxConstraints(
         maxWidth: AppDimensions.bottomSheetMaxWidth,
       ),
       showDragHandle: true,
       dragHandleColor: colorScheme.onSurfaceVariant.withOpacity(0.4),
       dragHandleSize: const Size(
         AppDimensions.bottomSheetDragHandleWidth,
         AppDimensions.bottomSheetDragHandleHeight,
       ),
     );
   }
   
   static TooltipThemeData _lightTooltipTheme(ColorScheme colorScheme) {
     return TooltipThemeData(
       height: AppDimensions.tooltipHeight,
       padding: AppPadding.tooltip,
       margin: AppMargin.tooltip,
       verticalOffset: AppDimensions.tooltipVerticalOffset,
       preferBelow: true,
       excludeFromSemantics: false,
       decoration: BoxDecoration(
         color: colorScheme.inverseSurface,
         borderRadius: AppBorderRadius.sm,
         boxShadow: [AppShadows.tooltip],
       ),
       textStyle: AppTextStyles.bodySmall.copyWith(
         color: colorScheme.onInverseSurface,
       ),
       textAlign: TextAlign.center,
       triggerMode: TooltipTriggerMode.longPress,
       enableFeedback: true,
       waitDuration: const Duration(milliseconds: 0),
       showDuration: const Duration(seconds: 1, milliseconds: 500),
     );
   }
   
   static TooltipThemeData _darkTooltipTheme(ColorScheme colorScheme) {
     return TooltipThemeData(
       height: AppDimensions.tooltipHeight,
       padding: AppPadding.tooltip,
       margin: AppMargin.tooltip,
       verticalOffset: AppDimensions.tooltipVerticalOffset,
       preferBelow: true,
       excludeFromSemantics: false,
       decoration: BoxDecoration(
         color: colorScheme.inverseSurface,
         borderRadius: AppBorderRadius.sm,
         boxShadow: [AppShadowsDark.tooltip],
       ),
       textStyle: AppTextStyles.bodySmall.copyWith(
         color: colorScheme.onInverseSurface,
       ),
       textAlign: TextAlign.center,
       triggerMode: TooltipTriggerMode.longPress,
       enableFeedback: true,
       waitDuration: const Duration(milliseconds: 0),
       showDuration: const Duration(seconds: 1, milliseconds: 500),
     );
   }
   
   static DividerThemeData _lightDividerTheme(ColorScheme colorScheme) {
     return DividerThemeData(
       color: colorScheme.outline,
       thickness: AppBorderWidth.thin,
       indent: 0,
       endIndent: 0,
       space: AppSpacing.xs,
     );
   }
   
   static DividerThemeData _darkDividerTheme(ColorScheme colorScheme) {
     return DividerThemeData(
       color: colorScheme.outline,
       thickness: AppBorderWidth.thin,
       indent: 0,
       endIndent: 0,
       space: AppSpacing.xs,
     );
   }
   
   static PopupMenuThemeData _lightPopupMenuTheme(ColorScheme colorScheme) {
     return PopupMenuThemeData(
       color: colorScheme.surface,
       surfaceTintColor: colorScheme.surfaceTint,
       shadowColor: AppShadows.popupMenu.color,
       elevation: AppElevations.popupMenu,
       textStyle: AppTextStyles.bodyMedium.copyWith(
         color: colorScheme.onSurface,
       ),
       labelTextStyle: MaterialStateProperty.all(
         AppTextStyles.bodyMedium.copyWith(
           color: colorScheme.onSurface,
         ),
       ),
       enableFeedback: true,
       mouseCursor: MaterialStateProperty.all(SystemMouseCursors.click),
       position: PopupMenuPosition.under,
       shape: RoundedRectangleBorder(
         borderRadius: AppBorderRadius.md,
       ),
     );
   }
   
   static PopupMenuThemeData _darkPopupMenuTheme(ColorScheme colorScheme) {
     return PopupMenuThemeData(
       color: colorScheme.surface,
       surfaceTintColor: colorScheme.surfaceTint,
       shadowColor: AppShadowsDark.popupMenu.color,
       elevation: AppElevations.popupMenu,
       textStyle: AppTextStyles.bodyMedium.copyWith(
         color: colorScheme.onSurface,
       ),
       labelTextStyle: MaterialStateProperty.all(
         AppTextStyles.bodyMedium.copyWith(
           color: colorScheme.onSurface,
         ),
       ),
       enableFeedback: true,
       mouseCursor: MaterialStateProperty.all(SystemMouseCursors.click),
       position: PopupMenuPosition.under,
       shape: RoundedRectangleBorder(
         borderRadius: AppBorderRadius.md,
       ),
     );
   }
         }
         return IconThemeData(
          color: colorScheme.onSurface.withOpacity(0.6),
          size: AppDimensions.iconMd,
        );
      }),
      labelTextStyle: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.selected)) {
          return AppTextStyles.labelSmall.copyWith(
            color: colorScheme.onSurface,
            fontWeight: AppFontWeights.medium,
          );
        }
        return AppTextStyles.labelSmall.copyWith(
          color: colorScheme.onSurface.withOpacity(0.6),
          fontWeight: AppFontWeights.regular,
        );
      }),
    );
  }
  
  static NavigationBarThemeData _darkNavigationBarTheme(ColorScheme colorScheme) {
    return NavigationBarThemeData(
      backgroundColor: colorScheme.surface,
      elevation: AppDimensions.elevationBottomNavigation,
      height: AppDimensions.bottomNavigationHeight,
      indicatorColor: colorScheme.secondaryContainer,
      surfaceTintColor: colorScheme.surfaceTint,
      shadowColor: colorScheme.shadow,
      iconTheme: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.selected)) {
          return IconThemeData(
            color: colorScheme.onSecondaryContainer,
            size: AppDimensions.iconMd,
          );
        }
        return IconThemeData(
          color: colorScheme.onSurface.withOpacity(0.6),
          size: AppDimensions.iconMd,
        );
      }),
      labelTextStyle: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.selected)) {
          return AppTextStyles.labelSmall.copyWith(
            color: colorScheme.onSurface,
            fontWeight: AppFontWeights.medium,
          );
        }
        return AppTextStyles.labelSmall.copyWith(
          color: colorScheme.onSurface.withOpacity(0.6),
          fontWeight: AppFontWeights.regular,
        );
      }),
    );
  }
  
  static NavigationRailThemeData _lightNavigationRailTheme(ColorScheme colorScheme) {
    return NavigationRailThemeData(
      backgroundColor: colorScheme.surface,
      elevation: AppDimensions.elevationDrawer,
      selectedIconTheme: IconThemeData(
        color: colorScheme.onSecondaryContainer,
        size: AppDimensions.iconMd,
      ),
      unselectedIconTheme: IconThemeData(
        color: colorScheme.onSurface.withOpacity(0.6),
        size: AppDimensions.iconMd,
      ),
      selectedLabelTextStyle: AppTextStyles.labelSmall.copyWith(
        color: colorScheme.onSurface,
        fontWeight: AppFontWeights.medium,
      ),
      unselectedLabelTextStyle: AppTextStyles.labelSmall.copyWith(
        color: colorScheme.onSurface.withOpacity(0.6),
        fontWeight: AppFontWeights.regular,
      ),
      indicatorColor: colorScheme.secondaryContainer,
      useIndicator: true,
    );
  }
  
  static NavigationRailThemeData _darkNavigationRailTheme(ColorScheme colorScheme) {
    return NavigationRailThemeData(
      backgroundColor: colorScheme.surface,
      elevation: AppDimensions.elevationDrawer,
      selectedIconTheme: IconThemeData(
        color: colorScheme.onSecondaryContainer,
        size: AppDimensions.iconMd,
      ),
      unselectedIconTheme: IconThemeData(
        color: colorScheme.onSurface.withOpacity(0.6),
        size: AppDimensions.iconMd,
      ),
      selectedLabelTextStyle: AppTextStyles.labelSmall.copyWith(
        color: colorScheme.onSurface,
        fontWeight: AppFontWeights.medium,
      ),
      unselectedLabelTextStyle: AppTextStyles.labelSmall.copyWith(
        color: colorScheme.onSurface.withOpacity(0.6),
        fontWeight: AppFontWeights.regular,
      ),
      indicatorColor: colorScheme.secondaryContainer,
      useIndicator: true,
    );
  }
  
  static TabBarTheme _lightTabBarTheme(ColorScheme colorScheme) {
    return TabBarTheme(
      labelColor: colorScheme.primary,
      unselectedLabelColor: colorScheme.onSurface.withOpacity(0.6),
      labelStyle: AppTextStyles.titleSmall.copyWith(
        fontWeight: AppFontWeights.medium,
      ),
      unselectedLabelStyle: AppTextStyles.titleSmall.copyWith(
        fontWeight: AppFontWeights.regular,
      ),
      indicator: UnderlineTabIndicator(
        borderSide: BorderSide(
          color: colorScheme.primary,
          width: AppBorderWidth.thick,
        ),
      ),
      indicatorColor: colorScheme.primary,
      indicatorSize: TabBarIndicatorSize.tab,
      dividerColor: colorScheme.outline.withOpacity(0.2),
      overlayColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.hovered)) {
          return colorScheme.onSurface.withOpacity(0.08);
        }
        if (states.contains(MaterialState.pressed)) {
          return colorScheme.onSurface.withOpacity(0.12);
        }
        return null;
      }),
    );
  }
  
  static TabBarTheme _darkTabBarTheme(ColorScheme colorScheme) {
    return TabBarTheme(
      labelColor: colorScheme.primary,
      unselectedLabelColor: colorScheme.onSurface.withOpacity(0.6),
      labelStyle: AppTextStyles.titleSmall.copyWith(
        fontWeight: AppFontWeights.medium,
      ),
      unselectedLabelStyle: AppTextStyles.titleSmall.copyWith(
        fontWeight: AppFontWeights.regular,
      ),
      indicator: UnderlineTabIndicator(
        borderSide: BorderSide(
          color: colorScheme.primary,
          width: AppBorderWidth.thick,
        ),
      ),
      indicatorColor: colorScheme.primary,
      indicatorSize: TabBarIndicatorSize.tab,
      dividerColor: colorScheme.outline.withOpacity(0.2),
      overlayColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.hovered)) {
          return colorScheme.onSurface.withOpacity(0.08);
        }
        if (states.contains(MaterialState.pressed)) {
          return colorScheme.onSurface.withOpacity(0.12);
        }
        return null;
      }),
    );
  }
  
  static DrawerThemeData _lightDrawerTheme(ColorScheme colorScheme) {
    return DrawerThemeData(
      backgroundColor: colorScheme.surface,
      elevation: AppDimensions.elevationDrawer,
      shadowColor: colorScheme.shadow,
      surfaceTintColor: colorScheme.surfaceTint,
      width: AppDimensions.drawerWidth,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(AppBorderRadius.lg),
          bottomRight: Radius.circular(AppBorderRadius.lg),
        ),
      ),
    );
  }
  
  static DrawerThemeData _darkDrawerTheme(ColorScheme colorScheme) {
    return DrawerThemeData(
      backgroundColor: colorScheme.surface,
      elevation: AppDimensions.elevationDrawer,
      shadowColor: colorScheme.shadow,
      surfaceTintColor: colorScheme.surfaceTint,
      width: AppDimensions.drawerWidth,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(AppBorderRadius.lg),
          bottomRight: Radius.circular(AppBorderRadius.lg),
        ),
      ),
    );
  }
  
  static CardTheme _lightCardTheme(ColorScheme colorScheme) {
    return CardTheme(
      color: colorScheme.surface,
      elevation: AppDimensions.elevationCard,
      shadowColor: colorScheme.shadow,
      surfaceTintColor: colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
      ),
      clipBehavior: Clip.antiAlias,
      margin: AppMargin.card,
    );
  }
  
  static CardTheme _darkCardTheme(ColorScheme colorScheme) {
    return CardTheme(
      color: colorScheme.surface,
      elevation: AppDimensions.elevationCard,
      shadowColor: colorScheme.shadow,
      surfaceTintColor: colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
      ),
      clipBehavior: Clip.antiAlias,
      margin: AppMargin.card,
    );
  }
  
  static ListTileThemeData _lightListTileTheme(ColorScheme colorScheme) {
    return ListTileThemeData(
      tileColor: colorScheme.surface,
      selectedTileColor: colorScheme.secondaryContainer,
      iconColor: colorScheme.onSurface.withOpacity(0.6),
      selectedColor: colorScheme.onSecondaryContainer,
      textColor: colorScheme.onSurface,
      titleTextStyle: AppTextStyles.bodyLarge.copyWith(
        color: colorScheme.onSurface,
      ),
      subtitleTextStyle: AppTextStyles.bodyMedium.copyWith(
        color: colorScheme.onSurface.withOpacity(0.7),
      ),
      leadingAndTrailingTextStyle: AppTextStyles.labelLarge.copyWith(
        color: colorScheme.onSurface.withOpacity(0.6),
      ),
      contentPadding: AppPadding.listTile,
      horizontalTitleGap: AppSpacing.sm,
      minVerticalPadding: AppSpacing.xs,
      minLeadingWidth: AppDimensions.iconLg + AppSpacing.sm,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.sm),
      ),
      enableFeedback: true,
    );
  }
  
  static ListTileThemeData _darkListTileTheme(ColorScheme colorScheme) {
    return ListTileThemeData(
      tileColor: colorScheme.surface,
      selectedTileColor: colorScheme.secondaryContainer,
      iconColor: colorScheme.onSurface.withOpacity(0.6),
      selectedColor: colorScheme.onSecondaryContainer,
      textColor: colorScheme.onSurface,
      titleTextStyle: AppTextStyles.bodyLarge.copyWith(
        color: colorScheme.onSurface,
      ),
      subtitleTextStyle: AppTextStyles.bodyMedium.copyWith(
        color: colorScheme.onSurface.withOpacity(0.7),
      ),
      leadingAndTrailingTextStyle: AppTextStyles.labelLarge.copyWith(
        color: colorScheme.onSurface.withOpacity(0.6),
      ),
      contentPadding: AppPadding.listTile,
      horizontalTitleGap: AppSpacing.sm,
      minVerticalPadding: AppSpacing.xs,
      minLeadingWidth: AppDimensions.iconLg + AppSpacing.sm,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.sm),
      ),
      enableFeedback: true,
    );
  }
  
  static ElevatedButtonThemeData _lightElevatedButtonTheme(ColorScheme colorScheme) {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        disabledBackgroundColor: colorScheme.onSurface.withOpacity(0.12),
        disabledForegroundColor: colorScheme.onSurface.withOpacity(0.38),
        elevation: AppDimensions.elevationButton,
        shadowColor: colorScheme.shadow,
        surfaceTintColor: colorScheme.surfaceTint,
        padding: AppPadding.button,
        minimumSize: Size(AppDimensions.buttonMinWidth, AppDimensions.buttonHeight),
        maximumSize: Size(AppDimensions.buttonMaxWidth, AppDimensions.buttonHeight),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
        ),
        textStyle: AppTextStyles.labelLarge.copyWith(
          fontWeight: AppFontWeights.medium,
        ),
        animationDuration: AnimationDurations.button,
        enableFeedback: true,
        alignment: Alignment.center,
      ),
    );
  }
  
  static ElevatedButtonThemeData _darkElevatedButtonTheme(ColorScheme colorScheme) {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        disabledBackgroundColor: colorScheme.onSurface.withOpacity(0.12),
        disabledForegroundColor: colorScheme.onSurface.withOpacity(0.38),
        elevation: AppDimensions.elevationButton,
        shadowColor: colorScheme.shadow,
        surfaceTintColor: colorScheme.surfaceTint,
        padding: AppPadding.button,
        minimumSize: Size(AppDimensions.buttonMinWidth, AppDimensions.buttonHeight),
        maximumSize: Size(AppDimensions.buttonMaxWidth, AppDimensions.buttonHeight),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
        ),
        textStyle: AppTextStyles.labelLarge.copyWith(
          fontWeight: AppFontWeights.medium,
        ),
        animationDuration: AnimationDurations.button,
        enableFeedback: true,
        alignment: Alignment.center,
      ),
    );
  }
  
  static OutlinedButtonThemeData _lightOutlinedButtonTheme(ColorScheme colorScheme) {
    return OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: colorScheme.primary,
        disabledForegroundColor: colorScheme.onSurface.withOpacity(0.38),
        backgroundColor: Colors.transparent,
        disabledBackgroundColor: Colors.transparent,
        elevation: 0,
        padding: AppPadding.button,
        minimumSize: Size(AppDimensions.buttonMinWidth, AppDimensions.buttonHeight),
        maximumSize: Size(AppDimensions.buttonMaxWidth, AppDimensions.buttonHeight),
        side: BorderSide(
          color: colorScheme.outline,
          width: AppBorderWidth.thin,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
        ),
        textStyle: AppTextStyles.labelLarge.copyWith(
          fontWeight: AppFontWeights.medium,
        ),
        animationDuration: AnimationDurations.button,
        enableFeedback: true,
        alignment: Alignment.center,
      ),
    );
  }
  
  static OutlinedButtonThemeData _darkOutlinedButtonTheme(ColorScheme colorScheme) {
    return OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: colorScheme.primary,
        disabledForegroundColor: colorScheme.onSurface.withOpacity(0.38),
        backgroundColor: Colors.transparent,
        disabledBackgroundColor: Colors.transparent,
        elevation: 0,
        padding: AppPadding.button,
        minimumSize: Size(AppDimensions.buttonMinWidth, AppDimensions.buttonHeight),
        maximumSize: Size(AppDimensions.buttonMaxWidth, AppDimensions.buttonHeight),
        side: BorderSide(
          color: colorScheme.outline,
          width: AppBorderWidth.thin,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
        ),
        textStyle: AppTextStyles.labelLarge.copyWith(
          fontWeight: AppFontWeights.medium,
        ),
        animationDuration: AnimationDurations.button,
        enableFeedback: true,
        alignment: Alignment.center,
      ),
    );
  }
  
  static TextButtonThemeData _lightTextButtonTheme(ColorScheme colorScheme) {
    return TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: colorScheme.primary,
        disabledForegroundColor: colorScheme.onSurface.withOpacity(0.38),
        backgroundColor: Colors.transparent,
        disabledBackgroundColor: Colors.transparent,
        elevation: 0,
        padding: AppPadding.button,
        minimumSize: Size(AppDimensions.buttonMinWidth, AppDimensions.buttonHeight),
        maximumSize: Size(AppDimensions.buttonMaxWidth, AppDimensions.buttonHeight),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
        ),
        textStyle: AppTextStyles.labelLarge.copyWith(
          fontWeight: AppFontWeights.medium,
        ),
        animationDuration: AnimationDurations.button,
        enableFeedback: true,
        alignment: Alignment.center,
      ),
    );
  }
  
  static TextButtonThemeData _darkTextButtonTheme(ColorScheme colorScheme) {
    return TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: colorScheme.primary,
        disabledForegroundColor: colorScheme.onSurface.withOpacity(0.38),
        backgroundColor: Colors.transparent,
        disabledBackgroundColor: Colors.transparent,
        elevation: 0,
        padding: AppPadding.button,
        minimumSize: Size(AppDimensions.buttonMinWidth, AppDimensions.buttonHeight),
        maximumSize: Size(AppDimensions.buttonMaxWidth, AppDimensions.buttonHeight),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.md),
        ),
        textStyle: AppTextStyles.labelLarge.copyWith(
          fontWeight: AppFontWeights.medium,
        ),
        animationDuration: AnimationDurations.button,
        enableFeedback: true,
        alignment: Alignment.center,
      ),
    );
  }
  
  static IconButtonThemeData _lightIconButtonTheme(ColorScheme colorScheme) {
    return IconButtonThemeData(
      style: IconButton.styleFrom(
        foregroundColor: colorScheme.onSurface,
        disabledForegroundColor: colorScheme.onSurface.withOpacity(0.38),
        backgroundColor: Colors.transparent,
        disabledBackgroundColor: Colors.transparent,
        hoverColor: colorScheme.onSurface.withOpacity(0.08),
        focusColor: colorScheme.onSurface.withOpacity(0.12),
        highlightColor: colorScheme.onSurface.withOpacity(0.12),
        splashColor: colorScheme.onSurface.withOpacity(0.12),
        elevation: 0,
        padding: AppPadding.iconButton,
        minimumSize: Size(AppDimensions.iconButtonSize, AppDimensions.iconButtonSize),
        maximumSize: Size(AppDimensions.iconButtonSize, AppDimensions.iconButtonSize),
        iconSize: AppDimensions.iconMd,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.sm),
        ),
        animationDuration: AnimationDurations.button,
        enableFeedback: true,
        alignment: Alignment.center,
      ),
    );
  }
  
  static IconButtonThemeData _darkIconButtonTheme(ColorScheme colorScheme) {
    return IconButtonThemeData(
      style: IconButton.styleFrom(
        foregroundColor: colorScheme.onSurface,
        disabledForegroundColor: colorScheme.onSurface.withOpacity(0.38),
        backgroundColor: Colors.transparent,
        disabledBackgroundColor: Colors.transparent,
        hoverColor: colorScheme.onSurface.withOpacity(0.08),
        focusColor: colorScheme.onSurface.withOpacity(0.12),
        highlightColor: colorScheme.onSurface.withOpacity(0.12),
        splashColor: colorScheme.onSurface.withOpacity(0.12),
        elevation: 0,
        padding: AppPadding.iconButton,
        minimumSize: Size(AppDimensions.iconButtonSize, AppDimensions.iconButtonSize),
        maximumSize: Size(AppDimensions.iconButtonSize, AppDimensions.iconButtonSize),
        iconSize: AppDimensions.iconMd,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.sm),
        ),
        animationDuration: AnimationDurations.button,
        enableFeedback: true,
        alignment: Alignment.center,
      ),
    );
  }
  
  static FloatingActionButtonThemeData _lightFloatingActionButtonTheme(ColorScheme colorScheme) {
    return FloatingActionButtonThemeData(
      backgroundColor: colorScheme.primaryContainer,
      foregroundColor: colorScheme.onPrimaryContainer,
      disabledElevation: 0,
      elevation: AppDimensions.elevationFab,
      focusElevation: AppDimensions.elevationFab + 2,
      hoverElevation: AppDimensions.elevationFab + 2,
      highlightElevation: AppDimensions.elevationFab + 4,
      splashColor: colorScheme.onPrimaryContainer.withOpacity(0.12),
      focusColor: colorScheme.onPrimaryContainer.withOpacity(0.12),
      hoverColor: colorScheme.onPrimaryContainer.withOpacity(0.08),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.lg),
      ),
      iconSize: AppDimensions.iconMd,
      sizeConstraints: BoxConstraints(
        minWidth: AppDimensions.fabSize,
        minHeight: AppDimensions.fabSize,
        maxWidth: AppDimensions.fabSize,
        maxHeight: AppDimensions.fabSize,
      ),
      smallSizeConstraints: BoxConstraints(
        minWidth: AppDimensions.fabSmallSize,
        minHeight: AppDimensions.fabSmallSize,
        maxWidth: AppDimensions.fabSmallSize,
        maxHeight: AppDimensions.fabSmallSize,
      ),
      largeSizeConstraints: BoxConstraints(
        minWidth: AppDimensions.fabLargeSize,
        minHeight: AppDimensions.fabLargeSize,
        maxWidth: AppDimensions.fabLargeSize,
        maxHeight: AppDimensions.fabLargeSize,
      ),
      extendedSizeConstraints: BoxConstraints(
        minHeight: AppDimensions.fabSize,
        maxHeight: AppDimensions.fabSize,
      ),
      extendedIconLabelSpacing: AppSpacing.sm,
      extendedPadding: AppPadding.fabExtended,
      extendedTextStyle: AppTextStyles.labelLarge.copyWith(
        fontWeight: AppFontWeights.medium,
      ),
      enableFeedback: true,
    );
  }
  
  static FloatingActionButtonThemeData _darkFloatingActionButtonTheme(ColorScheme colorScheme) {
    return FloatingActionButtonThemeData(
      backgroundColor: colorScheme.primaryContainer,
      foregroundColor: colorScheme.onPrimaryContainer,
      disabledElevation: 0,
      elevation: AppDimensions.elevationFab,
      focusElevation: AppDimensions.elevationFab + 2,
      hoverElevation: AppDimensions.elevationFab + 2,
      highlightElevation: AppDimensions.elevationFab + 4,
      splashColor: colorScheme.onPrimaryContainer.withOpacity(0.12),
      focusColor: colorScheme.onPrimaryContainer.withOpacity(0.12),
      hoverColor: colorScheme.onPrimaryContainer.withOpacity(0.08),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.lg),
      ),
      iconSize: AppDimensions.iconMd,
      sizeConstraints: BoxConstraints(
        minWidth: AppDimensions.fabSize,
        minHeight: AppDimensions.fabSize,
        maxWidth: AppDimensions.fabSize,
        maxHeight: AppDimensions.fabSize,
      ),
      smallSizeConstraints: BoxConstraints(
        minWidth: AppDimensions.fabSmallSize,
        minHeight: AppDimensions.fabSmallSize,
        maxWidth: AppDimensions.fabSmallSize,
        maxHeight: AppDimensions.fabSmallSize,
      ),
      largeSizeConstraints: BoxConstraints(
        minWidth: AppDimensions.fabLargeSize,
        minHeight: AppDimensions.fabLargeSize,
        maxWidth: AppDimensions.fabLargeSize,
        maxHeight: AppDimensions.fabLargeSize,
      ),
      extendedSizeConstraints: BoxConstraints(
        minHeight: AppDimensions.fabSize,
        maxHeight: AppDimensions.fabSize,
      ),
      extendedIconLabelSpacing: AppSpacing.sm,
      extendedPadding: AppPadding.fabExtended,
      extendedTextStyle: AppTextStyles.labelLarge.copyWith(
        fontWeight: AppFontWeights.medium,
      ),
      enableFeedback: true,
    );
  }
  
  static ChipThemeData _lightChipTheme(ColorScheme colorScheme) {
    return ChipThemeData(
      backgroundColor: colorScheme.surface,
      deleteIconColor: colorScheme.onSurface.withOpacity(0.6),
      disabledColor: colorScheme.onSurface.withOpacity(0.12),
      selectedColor: colorScheme.secondaryContainer,
      secondarySelectedColor: colorScheme.secondaryContainer,
      shadowColor: colorScheme.shadow,
      surfaceTintColor: colorScheme.surfaceTint,
      selectedShadowColor: colorScheme.shadow,
      checkmarkColor: colorScheme.onSecondaryContainer,
      labelPadding: AppPadding.chipLabel,
      padding: AppPadding.chip,
      side: BorderSide(
        color: colorScheme.outline,
        width: AppBorderWidth.thin,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.sm),
      ),
      labelStyle: AppTextStyles.labelMedium.copyWith(
        color: colorScheme.onSurface,
      ),
      secondaryLabelStyle: AppTextStyles.labelMedium.copyWith(
        color: colorScheme.onSecondaryContainer,
      ),
      brightness: lightBrightness,
      elevation: AppDimensions.elevationChip,
      pressElevation: AppDimensions.elevationChip + 2,
      iconTheme: IconThemeData(
        color: colorScheme.onSurface.withOpacity(0.6),
        size: AppDimensions.iconSm,
      ),
    );
  }
  
  static ChipThemeData _darkChipTheme(ColorScheme colorScheme) {
    return ChipThemeData(
      backgroundColor: colorScheme.surface,
      deleteIconColor: colorScheme.onSurface.withOpacity(0.6),
      disabledColor: colorScheme.onSurface.withOpacity(0.12),
      selectedColor: colorScheme.secondaryContainer,
      secondarySelectedColor: colorScheme.secondaryContainer,
      shadowColor: colorScheme.shadow,
      surfaceTintColor: colorScheme.surfaceTint,
      selectedShadowColor: colorScheme.shadow,
      checkmarkColor: colorScheme.onSecondaryContainer,
      labelPadding: AppPadding.chipLabel,
      padding: AppPadding.chip,
      side: BorderSide(
        color: colorScheme.outline,
        width: AppBorderWidth.thin,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.sm),
      ),
      labelStyle: AppTextStyles.labelMedium.copyWith(
        color: colorScheme.onSurface,
      ),
      secondaryLabelStyle: AppTextStyles.labelMedium.copyWith(
        color: colorScheme.onSecondaryContainer,
      ),
      brightness: darkBrightness,
      elevation: AppDimensions.elevationChip,
      pressElevation: AppDimensions.elevationChip + 2,
      iconTheme: IconThemeData(
        color: colorScheme.onSurface.withOpacity(0.6),
        size: AppDimensions.iconSm,
      ),
    );
  }
  
  static InputDecorationTheme _lightInputDecorationTheme(ColorScheme colorScheme) {
    return InputDecorationTheme(
      filled: true,
      fillColor: colorScheme.surfaceVariant.withOpacity(0.3),
      hoverColor: colorScheme.onSurface.withOpacity(0.08),
      focusColor: colorScheme.primary.withOpacity(0.12),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
        borderSide: BorderSide(
          color: colorScheme.outline,
          width: AppBorderWidth.thin,
        ),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
        borderSide: BorderSide(
          color: colorScheme.outline,
          width: AppBorderWidth.thin,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
        borderSide: BorderSide(
          color: colorScheme.primary,
          width: AppBorderWidth.thick,
        ),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
        borderSide: BorderSide(
          color: colorScheme.error,
          width: AppBorderWidth.thin,
        ),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
        borderSide: BorderSide(
          color: colorScheme.error,
          width: AppBorderWidth.thick,
        ),
      ),
      disabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
        borderSide: BorderSide(
          color: colorScheme.onSurface.withOpacity(0.12),
          width: AppBorderWidth.thin,
        ),
      ),
      contentPadding: AppPadding.inputField,
      isDense: false,
      labelStyle: AppTextStyles.bodyMedium.copyWith(
        color: colorScheme.onSurface.withOpacity(0.6),
      ),
      floatingLabelStyle: AppTextStyles.bodySmall.copyWith(
        color: colorScheme.primary,
      ),
      helperStyle: AppTextStyles.bodySmall.copyWith(
        color: colorScheme.onSurface.withOpacity(0.6),
      ),
      hintStyle: AppTextStyles.bodyMedium.copyWith(
        color: colorScheme.onSurface.withOpacity(0.6),
      ),
      errorStyle: AppTextStyles.bodySmall.copyWith(
        color: colorScheme.error,
      ),
      prefixStyle: AppTextStyles.bodyMedium.copyWith(
        color: colorScheme.onSurface.withOpacity(0.6),
      ),
      suffixStyle: AppTextStyles.bodyMedium.copyWith(
        color: colorScheme.onSurface.withOpacity(0.6),
      ),
      counterStyle: AppTextStyles.bodySmall.copyWith(
        color: colorScheme.onSurface.withOpacity(0.6),
      ),
      prefixIconColor: colorScheme.onSurface.withOpacity(0.6),
      suffixIconColor: colorScheme.onSurface.withOpacity(0.6),
      iconColor: colorScheme.onSurface.withOpacity(0.6),
      errorMaxLines: 2,
      helperMaxLines: 2,
      alignLabelWithHint: true,
      floatingLabelBehavior: FloatingLabelBehavior.auto,
    );
  }
  
  static SwitchThemeData _lightSwitchTheme(ColorScheme colorScheme) {
    return SwitchThemeData(
      thumbColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.disabled)) {
          return colorScheme.onSurface.withOpacity(0.38);
        }
        if (states.contains(MaterialState.selected)) {
          return colorScheme.onPrimary;
        }
        return colorScheme.outline;
      }),
      trackColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.disabled)) {
          return colorScheme.onSurface.withOpacity(0.12);
        }
        if (states.contains(MaterialState.selected)) {
          return colorScheme.primary;
        }
        return colorScheme.surfaceVariant;
      }),
      overlayColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.hovered)) {
          return colorScheme.onSurface.withOpacity(0.08);
        }
        if (states.contains(MaterialState.focused)) {
          return colorScheme.onSurface.withOpacity(0.12);
        }
        if (states.contains(MaterialState.pressed)) {
          return colorScheme.onSurface.withOpacity(0.12);
        }
        return null;
      }),
      splashRadius: AppDimensions.switchSplashRadius,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }
  
  static SwitchThemeData _darkSwitchTheme(ColorScheme colorScheme) {
    return SwitchThemeData(
      thumbColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.disabled)) {
          return colorScheme.onSurface.withOpacity(0.38);
        }
        if (states.contains(MaterialState.selected)) {
          return colorScheme.onPrimary;
        }
        return colorScheme.outline;
      }),
      trackColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.disabled)) {
          return colorScheme.onSurface.withOpacity(0.12);
        }
        if (states.contains(MaterialState.selected)) {
          return colorScheme.primary;
        }
        return colorScheme.surfaceVariant;
      }),
      overlayColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.hovered)) {
          return colorScheme.onSurface.withOpacity(0.08);
        }
        if (states.contains(MaterialState.focused)) {
          return colorScheme.onSurface.withOpacity(0.12);
        }
        if (states.contains(MaterialState.pressed)) {
          return colorScheme.onSurface.withOpacity(0.12);
        }
        return null;
      }),
      splashRadius: AppDimensions.switchSplashRadius,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }
  
  static CheckboxThemeData _lightCheckboxTheme(ColorScheme colorScheme) {
    return CheckboxThemeData(
      fillColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.disabled)) {
          return colorScheme.onSurface.withOpacity(0.38);
        }
        if (states.contains(MaterialState.selected)) {
          return colorScheme.primary;
        }
        return Colors.transparent;
      }),
      checkColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.disabled)) {
          return colorScheme.surface;
        }
        return colorScheme.onPrimary;
      }),
      overlayColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.hovered)) {
          return colorScheme.onSurface.withOpacity(0.08);
        }
        if (states.contains(MaterialState.focused)) {
          return colorScheme.onSurface.withOpacity(0.12);
        }
        if (states.contains(MaterialState.pressed)) {
          return colorScheme.onSurface.withOpacity(0.12);
        }
        return null;
      }),
      splashRadius: AppDimensions.checkboxSplashRadius,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      visualDensity: VisualDensity.compact,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.xs),
      ),
      side: BorderSide(
        color: colorScheme.outline,
        width: AppBorderWidth.thin,
      ),
    );
  }
  
  static CheckboxThemeData _darkCheckboxTheme(ColorScheme colorScheme) {
    return CheckboxThemeData(
      fillColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.disabled)) {
          return colorScheme.onSurface.withOpacity(0.38);
        }
        if (states.contains(MaterialState.selected)) {
          return colorScheme.primary;
        }
        return Colors.transparent;
      }),
      checkColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.disabled)) {
          return colorScheme.surface;
        }
        return colorScheme.onPrimary;
      }),
      overlayColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.hovered)) {
          return colorScheme.onSurface.withOpacity(0.08);
        }
        if (states.contains(MaterialState.focused)) {
          return colorScheme.onSurface.withOpacity(0.12);
        }
        if (states.contains(MaterialState.pressed)) {
          return colorScheme.onSurface.withOpacity(0.12);
        }
        return null;
      }),
      splashRadius: AppDimensions.checkboxSplashRadius,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      visualDensity: VisualDensity.compact,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.xs),
      ),
      side: BorderSide(
        color: colorScheme.outline,
        width: AppBorderWidth.thin,
      ),
    );
  }
      suffixStyle: AppTextStyles.bodyMedium.copyWith(
        color: colorScheme.onSurface.withOpacity(0.6),
      ),
      counterStyle: AppTextStyles.bodySmall.copyWith(
        color: colorScheme.onSurface.withOpacity(0.6),
      ),
      prefixIconColor: colorScheme.onSurface.withOpacity(0.6),
      suffixIconColor: colorScheme.onSurface.withOpacity(0.6),
      iconColor: colorScheme.onSurface.withOpacity(0.6),
      errorMaxLines: 2,
      helperMaxLines: 2,
      alignLabelWithHint: true,
      floatingLabelBehavior: FloatingLabelBehavior.auto,
    );
  }
  
  static InputDecorationTheme _darkInputDecorationTheme(ColorScheme colorScheme) {
    return InputDecorationTheme(
      filled: true,
      fillColor: colorScheme.surfaceVariant.withOpacity(0.3),
      hoverColor: colorScheme.onSurface.withOpacity(0.08),
      focusColor: colorScheme.primary.withOpacity(0.12),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
        borderSide: BorderSide(
          color: colorScheme.outline,
          width: AppBorderWidth.thin,
        ),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
        borderSide: BorderSide(
          color: colorScheme.outline,
          width: AppBorderWidth.thin,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
        borderSide: BorderSide(
          color: colorScheme.primary,
          width: AppBorderWidth.thick,
        ),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
        borderSide: BorderSide(
          color: colorScheme.error,
          width: AppBorderWidth.thin,
        ),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
        borderSide: BorderSide(
          color: colorScheme.error,
          width: AppBorderWidth.thick,
        ),
      ),
      disabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
        borderSide: BorderSide(
          color: colorScheme.onSurface.withOpacity(0.12),
          width: AppBorderWidth.thin,
        ),
      ),
      contentPadding: AppPadding.inputField,
      isDense: false,
      labelStyle: AppTextStyles.bodyMedium.copyWith(
        color: colorScheme.onSurface.withOpacity(0.6),
      ),
      floatingLabelStyle: AppTextStyles.bodySmall.copyWith(
        color: colorScheme.primary,
      ),
      helperStyle: AppTextStyles.bodySmall.copyWith(
        color: colorScheme.onSurface.withOpacity(0.6),
      ),
      hintStyle: AppTextStyles.bodyMedium.copyWith(
        color: colorScheme.onSurface.withOpacity(0.6),
      ),
      errorStyle: AppTextStyles.bodySmall.copyWith(
        color: colorScheme.error,
      ),
      prefixStyle: AppTextStyles.bodyMedium.copyWith(
        color: colorScheme.onSurface.withOpacity(0.6),
      ),