'use client';

import { useState, useCallback } from 'react';
import { api } from '@/lib/apiClient';
import { toast } from 'sonner';

// Adicione os tipos que serão usados pelo hook
export interface Review {
  id: string;
  user_id: string;
  entity_type: string;
  entity_id: string;
  rating: number;
  comment: string | null;
  created_at: string;
  user?: {
    name: string;
    image_url: string | null;
  };
}

export interface ReviewStats {
  average_rating: number;
  total_reviews: number;
  rating_distribution: Record<string, number>;
}

export interface ReviewListResponse {
  items: Review[];
  total: number;
  page: number;
  page_size: number;
}

export interface ReviewCreateData {
  entity_type: string;
  entity_id: string;
  rating: number;
  comment?: string;
}

export function useReviews(entityType: string, entityId: string) {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [stats, setStats] = useState<ReviewStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({ page: 1, pageSize: 10, total: 0 });

  const fetchReviews = useCallback(async (page = 1) => {
    if (!entityType || !entityId) return;
    setLoading(true);
    try {
      const response = await api.get(`/reviews/entity/${entityType}/${entityId}?page=${page}&page_size=${pagination.pageSize}`);
      setReviews(response.items);
      setPagination({
        page: response.page,
        pageSize: response.page_size,
        total: response.total,
      });
    } catch (err: any) {
      setError(err.message || 'Falha ao buscar avaliações.');
      toast.error('Não foi possível carregar as avaliações.');
    } finally {
      setLoading(false);
    }
  }, [entityType, entityId, pagination.pageSize]);

  const fetchStats = useCallback(async () => {
    if (!entityType || !entityId) return;
    try {
      const response = await api.get(`/reviews/stats/entity/${entityType}/${entityId}`);
      setStats(response);
    } catch (err: any) {
      console.error('Falha ao buscar estatísticas de avaliação:', err);
    }
  }, [entityType, entityId]);

  const submitReview = async (data: Omit<ReviewCreateData, 'entity_type' | 'entity_id'>): Promise<boolean> => {
    try {
      await api.post('/reviews/', { ...data, entity_type: entityType, entity_id: entityId });
      toast.success('Sua avaliação foi enviada com sucesso!');
      // Re-fetch data to show the new review
      fetchReviews(1);
      fetchStats();
      return true;
    } catch (error: any) {
      toast.error(error.response?.data?.detail || 'Ocorreu um erro ao enviar sua avaliação.');
      return false;
    }
  };

  return {
    reviews,
    stats,
    loading,
    error,
    pagination,
    fetchReviews,
    fetchStats,
    submitReview,
  };
}