"""Service for integrating domain registration with custom domains."""

import logging  # noqa: E402
import uuid
from typing import List, Optional, Dict, Any

from sqlalchemy.ext.asyncio import AsyncSession  # noqa: E402
from sqlalchemy.future import select

from app.modules.core.custom_domains.models import CustomDomain  # noqa: E402

# Import the service directly from the module to avoid circular imports
from app.modules.core.custom_domains.services import CustomDomainService  # noqa: E402
from app.modules.shared.domain_rent.services.domain_service import DomainService
from app.modules.shared.domain_rent.services.config_service import ConfigService
from app.modules.shared.domain_rent.schemas.domain_schemas import (
    DomainRegistrationRequest,
    ContactInfo,
)
from app.modules.shared.domain_rent.models.domain_registration import (
    DomainRegistration,
)  # noqa: E402


logger = logging.getLogger(__name__)


class DomainIntegrationService:
    """Service for integrating domain registration with custom domains."""

    def __init__(self, db: AsyncSession):
        """Initialize domain integration service.

        Args:
            db: Database session
        """
        self.db = db
        self.config_service = ConfigService()
        self.domain_service = DomainService(db, self.config_service)

    async def register_domain_and_associate_with_tenant(
        self,
        user_id: uuid.UUID,
        domain_name: str,
        tld: str,
        tenant_id: uuid.UUID,
        frontend_type: str,
        registrar: Optional[str] = None,
        period_years: int = 1,
        auto_renew: bool = False,
        whois_privacy: bool = False,
        nameservers: Optional[List[str]] = None,
        contacts: Optional[Dict[str, ContactInfo]] = None,
    ) -> Dict[str, Any]:
        """Register a domain and associate it with a tenant.

        Args:
            user_id: ID of the user registering the domain
            domain_name: Domain name without TLD
            tld: TLD to register
            tenant_id: ID of the tenant to associate with
            frontend_type: Type of frontend (e.g., 'ONLINE_STORE', 'DIGITAL_MENU')
            registrar: Preferred registrar (if None, use best price)
            period_years: Registration period in years
            auto_renew: Whether to automatically renew the domain
            whois_privacy: Whether to enable WHOIS privacy protection
            nameservers: List of nameserver hostnames
            contacts: Contact information for the domain

        Returns:
            Dictionary with registration and association details

        Raises:
            ValueError: If the domain is not available or cannot be registered
        """
        # 1. Register the domain
        registration_request = DomainRegistrationRequest(
            domain_name=domain_name,
            tld=tld,
            registrar=registrar,
            period_years=period_years,
            auto_renew=auto_renew,
            whois_privacy=whois_privacy,
            nameservers=nameservers,
            contacts=contacts or {},
        )

        try:
            domain_registration = await self.domain_service.register_domain(
                user_id=user_id,
                request=registration_request,
                tenant_id=tenant_id,
            )
        except Exception as e:
            logger.error(f"Error registering domain {domain_name}.{tld}: {str(e)}")
            raise ValueError(f"Failed to register domain: {str(e)}")

        # 2. Associate the domain with the tenant
        full_domain = f"{domain_name}.{tld}"

        custom_domain_create = {
            "domain_name": full_domain,
            "frontend_type": frontend_type,
        }

        try:
            # Create a local instance of CustomDomainService to avoid circular imports
            domain_service = CustomDomainService()
            custom_domain = await domain_service.create_custom_domain(
                db=self.db,
                domain_in=custom_domain_create,
                tenant_id=tenant_id,
            )

            # 3. Update the custom domain with the domain registration ID
            custom_domain.domain_registration_id = domain_registration.id
            await self.db.commit()
            await self.db.refresh(custom_domain)
        except Exception as e:
            logger.error(
                f"Error associating domain {full_domain} with tenant {tenant_id}: {str(e)}"
            )
            # TODO: Consider rolling back the domain registration if the association fails
            raise ValueError(f"Failed to associate domain with tenant: {str(e)}")

        return {
            "domain_registration": domain_registration,
            "custom_domain": custom_domain,
        }

    async def associate_registered_domain_with_tenant(
        self,
        domain_registration_id: uuid.UUID,
        tenant_id: uuid.UUID,
        frontend_type: str,
    ) -> CustomDomain:
        """Associate an existing registered domain with a tenant.

        Args:
            domain_registration_id: ID of the domain registration
            tenant_id: ID of the tenant to associate with
            frontend_type: Type of frontend (e.g., 'ONLINE_STORE', 'DIGITAL_MENU')

        Returns:
            Created custom domain

        Raises:
            ValueError: If the domain registration does not exist or is already associated with a tenant  # noqa: E501
        """
        # 1. Get the domain registration
        domain_registration = await self.domain_service.get_domain_by_id(domain_registration_id)
        if not domain_registration:
            raise ValueError(f"Domain registration with ID {domain_registration_id} not found")

        # 2. Check if the domain is already associated with a tenant
        stmt = select(CustomDomain).where(
            CustomDomain.domain_registration_id == domain_registration_id
        )
        result = await self.db.execute(stmt)
        existing_association = result.scalars().first()

        if existing_association:
            raise ValueError(
                f"Domain {domain_registration.domain_name}.{domain_registration.tld} is already associated with a tenant"  # noqa: E501
            )

        # 3. Create the custom domain
        full_domain = f"{domain_registration.domain_name}.{domain_registration.tld}"

        custom_domain_create = {
            "domain_name": full_domain,
            "frontend_type": frontend_type,
        }

        try:
            # Create a local instance of CustomDomainService to avoid circular imports
            domain_service = CustomDomainService()
            custom_domain = await domain_service.create_custom_domain(
                db=self.db,
                domain_in=custom_domain_create,
                tenant_id=tenant_id,
            )

            # 4. Update the custom domain with the domain registration ID
            custom_domain.domain_registration_id = domain_registration_id
            await self.db.commit()
            await self.db.refresh(custom_domain)
        except Exception as e:
            logger.error(
                f"Error associating domain {full_domain} with tenant {tenant_id}: {str(e)}"
            )
            raise ValueError(f"Failed to associate domain with tenant: {str(e)}")

        return custom_domain

    async def get_available_domains_for_tenant(
        self,
        user_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID] = None,
    ) -> List[DomainRegistration]:
        """Get domains registered by a user that are available for association with a tenant.

        Args:
            user_id: ID of the user
            tenant_id: ID of the tenant (optional)

        Returns:
            List of domain registrations
        """
        # 1. Get all domains registered by the user
        user_domains = await self.domain_service.get_user_domains(
            user_id=user_id,
            tenant_id=tenant_id,
            status=None,  # Get all domains regardless of status
        )

        if not user_domains:
            return []

        # 2. Filter out domains that are already associated with a tenant
        domain_ids = [domain.id for domain in user_domains]
        stmt = select(CustomDomain).where(CustomDomain.domain_registration_id.in_(domain_ids))
        result = await self.db.execute(stmt)
        associated_domains = result.scalars().all()

        associated_domain_ids = [domain.domain_registration_id for domain in associated_domains]

        # 3. Return only domains that are not associated with a tenant
        available_domains = [
            domain for domain in user_domains if domain.id not in associated_domain_ids
        ]

        return available_domains
