"""
Reviews API

API genérica para gerenciamento de reviews que pode ser usada por qualquer módulo.
"""

from typing import Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.auth_dependencies import get_current_user
from app.modules.core.users.models.user import User
from app.modules.core.functions.reviews.services.review_service import ReviewService
from app.modules.core.functions.reviews.schemas.review_schemas import (
    ReviewCreate,
    ReviewResponse,
    ReviewListResponse,
    ReviewStatsResponse,
    ReviewFilterParams
)

router = APIRouter()


@router.post("/", response_model=ReviewResponse)
async def create_review(
    review_data: ReviewCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Criar uma nova review"""
    service = ReviewService(db)
    
    try:
        return await service.create_review(review_data, current_user.id)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/entity/{entity_type}/{entity_id}", response_model=ReviewListResponse)
async def get_reviews_by_entity(
    entity_type: str,
    entity_id: UUID,
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    rating: Optional[int] = Query(None, ge=1, le=5),
    verified_only: bool = Query(False),
    sort_by: str = Query("created_at"),
    sort_order: str = Query("desc"),
    db: AsyncSession = Depends(get_db)
):
    """Buscar reviews de uma entidade específica"""
    service = ReviewService(db)
    
    filters = ReviewFilterParams(
        rating=rating,
        verified_only=verified_only,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    return await service.get_reviews_by_entity(
        entity_type=entity_type,
        entity_id=entity_id,
        filters=filters,
        page=page,
        page_size=page_size
    )


@router.get("/tenant/{tenant_id}", response_model=ReviewListResponse)
async def get_reviews_by_tenant(
    tenant_id: UUID,
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    rating: Optional[int] = Query(None, ge=1, le=5),
    verified_only: bool = Query(False),
    sort_by: str = Query("created_at"),
    sort_order: str = Query("desc"),
    db: AsyncSession = Depends(get_db)
):
    """Buscar reviews de um tenant"""
    service = ReviewService(db)
    
    filters = ReviewFilterParams(
        rating=rating,
        verified_only=verified_only,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    return await service.get_reviews_by_tenant(
        tenant_id=tenant_id,
        filters=filters,
        page=page,
        page_size=page_size
    )


@router.get("/stats/entity/{entity_type}/{entity_id}", response_model=ReviewStatsResponse)
async def get_entity_review_stats(
    entity_type: str,
    entity_id: UUID,
    db: AsyncSession = Depends(get_db)
):
    """Obter estatísticas de reviews de uma entidade"""
    service = ReviewService(db)
    
    return await service.get_review_stats(
        entity_type=entity_type,
        entity_id=entity_id
    )


@router.get("/stats/tenant/{tenant_id}", response_model=ReviewStatsResponse)
async def get_tenant_review_stats(
    tenant_id: UUID,
    db: AsyncSession = Depends(get_db)
):
    """Obter estatísticas de reviews de um tenant"""
    service = ReviewService(db)
    
    return await service.get_review_stats(tenant_id=tenant_id)


@router.get("/{review_id}", response_model=ReviewResponse)
async def get_review_by_id(
    review_id: UUID,
    db: AsyncSession = Depends(get_db)
):
    """Buscar review por ID"""
    service = ReviewService(db)
    
    review = await service.get_review_by_id(review_id)
    if not review:
        raise HTTPException(status_code=404, detail="Review not found")
    
    return review


@router.delete("/{review_id}")
async def delete_review(
    review_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Deletar review (apenas pelo próprio usuário)"""
    service = ReviewService(db)
    
    success = await service.delete_review(review_id, current_user.id)
    if not success:
        raise HTTPException(status_code=404, detail="Review not found or not authorized")
    
    return {"message": "Review deleted successfully"}