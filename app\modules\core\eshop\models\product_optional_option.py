import uuid
from sqlalchemy import Column, String, ForeignKey, Index, Boolean, Integer, Numeric
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.orm import relationship

from app.db.base import Base
from app.modules.core.tenants.models.tenant import Tenant


class ProductOptionalOption(Base):
    """
    Represents an individual option within a product optional group (e.g., "Phone Case", "Screen Protector").
    """

    __tablename__ = "eshop_product_optional_options"

    # Primary key
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Tenant isolation
    tenant_id = Column(PG_UUID(as_uuid=True), ForeignKey(Tenant.id), nullable=True, index=True)
    
    # Parent optional group
    optional_group_id = Column(PG_UUID(as_uuid=True), ForeignKey("eshop_product_optional_groups.id"), nullable=False, index=True)
    
    # Option properties
    name = Column(String(100), nullable=False)  # e.g., "Phone Case", "Extended Warranty"
    description = Column(String(255), nullable=True)  # Optional description
    
    # Pricing
    price_adjustment = Column(Numeric(10, 2), default=0.0, nullable=False)  # Additional cost
    price_adjustment_type = Column(String(20), default="fixed", nullable=False)  # "fixed" or "percentage"
    
    # Display and status
    display_order = Column(Integer, default=0, nullable=False)
    is_default = Column(Boolean, default=False, nullable=False)  # Default selection for this group
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Stock and availability
    stock_quantity = Column(Integer, nullable=True)  # Limited availability for this optional
    sku = Column(String(100), nullable=True)  # Separate SKU for this optional item
    
    # Visual representation
    image_url = Column(String(500), nullable=True)  # Image for this optional option
    
    # Related product (if this optional is actually another product)
    related_product_id = Column(PG_UUID(as_uuid=True), ForeignKey("eshop_products.id"), nullable=True)
    
    # Relationships
    tenant = relationship("Tenant")
    optional_group = relationship("ProductOptionalGroup", back_populates="options")
    related_product = relationship("app.modules.core.eshop.models.product.Product", foreign_keys=[related_product_id])

    __table_args__ = (
        Index("ix_eshop_optional_options_tenant_id", "tenant_id"),
        Index("ix_eshop_optional_options_optional_group_id", "optional_group_id"),
        Index("ix_eshop_optional_options_tenant_group", "tenant_id", "optional_group_id"),
        Index("ix_eshop_optional_options_related_product_id", "related_product_id"),
    )

    def __repr__(self):
        return f"<ProductOptionalOption(id={self.id}, name='{self.name}', optional_group_id={self.optional_group_id})>"
