"""
Base schemas for restaurant tenant settings operations.
"""

import uuid
from typing import Optional, Dict, Any

from pydantic import BaseModel, Field, ConfigDict


class RestaurantTenantSettingsBase(BaseModel):
    """Base schema for restaurant tenant settings."""
    
    tenant_slug: Optional[str] = Field(
        None,
        max_length=63,
        pattern=r"^[a-z0-9](?:[a-z0-9\-]{0,61}[a-z0-9])?$",
        description="Unique slug for tenant identification in public URLs"
    )
    wifi_networks: Optional[Dict[str, Any]] = Field(
        None,
        description="WiFi networks configuration by zone"
    )
    social_media_links: Optional[Dict[str, Any]] = Field(
        None,
        description="Social media platform links with icons"
    )
    address_extensions: Optional[Dict[str, Any]] = Field(
        None,
        description="Restaurant-specific address extensions"
    )
    operating_hours: Optional[Dict[str, Any]] = Field(
        None,
        description="Restaurant operating hours with service, break, and happy hour periods"
    )
    special_calendar: Optional[Dict[str, Any]] = Field(
        None,
        description="Special calendar events with custom hours or closures"
    )
    additional_restaurant_settings: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional restaurant-specific settings"
    )


class RestaurantTenantSettingsCreate(RestaurantTenantSettingsBase):
    """Schema for creating restaurant tenant settings."""
    
    tenant_id: uuid.UUID = Field(
        ...,
        description="ID of the tenant these settings belong to"
    )


class RestaurantTenantSettingsUpdate(RestaurantTenantSettingsBase):
    """Schema for updating restaurant tenant settings."""
    
    pass  # All fields are optional for updates


class RestaurantTenantSettingsRead(RestaurantTenantSettingsBase):
    """Schema for reading restaurant tenant settings."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: uuid.UUID = Field(
        ...,
        description="Unique identifier for the restaurant settings"
    )
    tenant_id: uuid.UUID = Field(
        ...,
        description="ID of the tenant these settings belong to"
    )

    # Computed properties
    has_tenant_slug: bool = Field(
        ...,
        description="Whether tenant slug is configured"
    )
    has_wifi_networks: bool = Field(
        ...,
        description="Whether WiFi networks are configured"
    )
    has_social_media_links: bool = Field(
        ...,
        description="Whether social media links are configured"
    )
    has_address_extensions: bool = Field(
        ...,
        description="Whether address extensions are configured"
    )
    delivery_radius_km: Optional[float] = Field(
        None,
        description="Delivery radius in kilometers"
    )
    phone_is_whatsapp: bool = Field(
        False,
        description="Whether primary phone is WhatsApp"
    )
    has_operating_hours: bool = Field(
        False,
        description="Whether operating hours are configured"
    )
    has_special_calendar: bool = Field(
        False,
        description="Whether special calendar events are configured"
    )
    is_open_today: bool = Field(
        False,
        description="Whether restaurant is open today"
    )
