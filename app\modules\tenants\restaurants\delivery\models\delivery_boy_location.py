import uuid
from typing import TYPE_CHECKING

from sqlalchemy import Column, DateTime, Float, ForeignKey, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base

if TYPE_CHECKING:
    from app.modules.tenants.models import Tenant  # noqa: F401  # noqa: E402
    from .delivery_boy import DeliveryBoy  # noqa: F401


class DeliveryBoyLocation(Base):
    __tablename__ = "delivery_boy_locations"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), index=True, nullable=False)
    delivery_boy_id = Column(
        UUID(as_uuid=True), ForeignKey("delivery_boys.id"), index=True, nullable=False
    )
    latitude = Column(Float, nullable=False)
    longitude = Column(Float, nullable=False)
    accuracy = Column(Float, nullable=True)
    timestamp = Column(DateTime, default=func.now(), index=True, nullable=False)
    created_at = Column(DateTime, default=func.now(), nullable=False)

    # Relationships
    delivery_boy = relationship("DeliveryBoy", back_populates="locations")
    tenant = relationship("Tenant")

    def __repr__(self) -> str:
        return f"<DeliveryBoyLocation(id={self.id}, delivery_boy_id={self.delivery_boy_id}, timestamp={self.timestamp})>"  # noqa: E501
