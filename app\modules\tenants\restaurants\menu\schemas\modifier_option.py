import uuid  # Import uuid
from pydantic import BaseModel, ConfigDict, Field
from typing import Optional, Union
from decimal import Decimal

# Assuming InventoryItem schema exists for potential nesting
# from app.modules.sharedModules.inventory.schemas.inventory_item import InventoryItemReadSimple


class ModifierOptionBase(BaseModel):
    """Base schema for Modifier Option."""

    name: str = Field(
        ...,
        max_length=100,
        description="Name of the modifier option (e.g., Extra Cheese, No Onions)",
    )
    price_adjustment: Decimal = Field(
        Decimal("0.00"), description="Price adjustment for selecting this option"
    )
    is_default: Optional[bool] = Field(False, description="Is this option selected by default?")
    display_order: int = Field(0, description="Order within the modifier group")
    is_active: bool = Field(True, description="Whether the option is currently active")


class ModifierOptionCreate(ModifierOptionBase):
    """Schema for creating a new Modifier Option."""

    id: Optional[Union[uuid.UUID, str]] = Field(None, description="Optional ID for existing options")
    # modifier_group_id will be path parameter or part of a nested structure
    # tenant_id will be added by the service
    # Optional: Link inventory items on creation
    # inventory_item_ids: List[int] = []


class ModifierOptionUpdate(ModifierOptionBase):
    """Schema for updating an existing Modifier Option. All fields optional."""

    name: Optional[str] = Field(None, max_length=100)
    price_adjustment: Optional[Decimal] = None
    is_default: Optional[bool] = None
    display_order: Optional[int] = None
    is_active: Optional[bool] = None
    # Optional: Update linked inventory items
    # inventory_item_ids: Optional[List[int]] = None


class ModifierOptionRead(ModifierOptionBase):
    """Schema for reading a Modifier Option."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    modifier_group_id: uuid.UUID
    # Optional: Include linked inventory items
    # inventory_items: List[InventoryItemReadSimple] = []

    model_config = ConfigDict(from_attributes=True)


# Optional simplified version
# class ModifierOptionReadSimple(BaseModel):
#     id: int
#     name: str
#     price_adjustment: Decimal
#     is_default: Optional[bool]
#     model_config = ConfigDict(from_attributes=True)
