# General - Coupons

**Categoria:** General
**Mó<PERSON><PERSON>:** Coupons
**Total de Endpoints:** 5
**Gerado em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [POST /api/modules/core/functions/offerts/coupons/](#post-apimodulescorefunctionsoffertscoupons) - Create Coupon
- [DELETE /api/modules/core/functions/offerts/coupons/{code}](#delete-apimodulescorefunctionsoffertscouponscode) - Delete Coupon
- [GET /api/modules/core/functions/offerts/coupons/{code}](#get-apimodulescorefunctionsoffertscouponscode) - Get Coupon
- [PATCH /api/modules/core/functions/offerts/coupons/{code}](#patch-apimodulescorefunctionsoffertscouponscode) - Update Coupon
- [GET /api/modules/core/functions/offerts/coupons/{code}/validate](#get-apimodulescorefunctionsoffertscouponscodevalidate) - Validate Coupon

## 📊 Schemas

### CouponCreate

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `code` | string | ✅ | - |
| `discount_type` | DiscountType | ✅ | - |
| `value` | unknown | ✅ | - |
| `expires_at` | unknown | ❌ | - |
| `usage_limit` | unknown | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `tenant_id` | string | ✅ | - |

### CouponRead

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `code` | string | ✅ | - |
| `discount_type` | DiscountType | ✅ | - |
| `value` | string | ✅ | - |
| `expires_at` | unknown | ❌ | - |
| `usage_limit` | unknown | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `usage_count` | integer | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### CouponUpdate

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `code` | unknown | ❌ | - |
| `discount_type` | unknown | ❌ | - |
| `value` | unknown | ❌ | - |
| `expires_at` | unknown | ❌ | - |
| `usage_limit` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

## 🔗 Endpoints Detalhados

### POST /api/modules/core/functions/offerts/coupons/ {#post-apimodulescorefunctionsoffertscoupons}

**Resumo:** Create Coupon
**Descrição:** Create a new coupon.
(Requires Tenant Owner authentication)

**🔐 Autenticação:** Não requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [CouponCreate](#couponcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CouponRead](#couponread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/functions/offerts/coupons/" \
  -H "Content-Type: application/json" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/core/functions/offerts/coupons/{code} {#delete-apimodulescorefunctionsoffertscouponscode}

**Resumo:** Delete Coupon
**Descrição:** Delete a coupon.
(Requires Tenant Owner authentication)

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `code` | string | path | ✅ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/core/functions/offerts/coupons/{code}"
```

---

### GET /api/modules/core/functions/offerts/coupons/{code} {#get-apimodulescorefunctionsoffertscouponscode}

**Resumo:** Get Coupon
**Descrição:** Get a coupon by its code.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `code` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CouponRead](#couponread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/functions/offerts/coupons/{code}"
```

---

### PATCH /api/modules/core/functions/offerts/coupons/{code} {#patch-apimodulescorefunctionsoffertscouponscode}

**Resumo:** Update Coupon
**Descrição:** Update a coupon.
(Requires Tenant Owner authentication)

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `code` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [CouponUpdate](#couponupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CouponRead](#couponread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PATCH "http://localhost:8000/api/modules/core/functions/offerts/coupons/{code}" \
  -H "Content-Type: application/json" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/functions/offerts/coupons/{code}/validate {#get-apimodulescorefunctionsoffertscouponscodevalidate}

**Resumo:** Validate Coupon
**Descrição:** Validate a coupon code. Returns true if valid, false otherwise.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `code` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/functions/offerts/coupons/{code}/validate"
```

---
