"""API endpoints for Email module."""

import logging  # noqa: E402
import uuid
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, status  # noqa: E402
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_current_user, get_db, get_current_tenant  # noqa: E402
from app.models.user import User
from app.models.tenant import Tenant
from app.core.roles import TenantRole
from app.api.deps import require_tenant_role

from app.modules.shared.email.models import EmailDomain, EmailAccount, EmailAlias  # noqa: E402
from app.modules.shared.email.api.schemas import (
    EmailDomainCreate,
    EmailDomainUpdate,
    EmailDomainRead,
    EmailDomainDNSRecords,
    EmailAccountCreate,
    EmailAccountUpdate,
    EmailAccountRead,
    EmailAccountUsage,
    EmailAccountPasswordChange,
    EmailAccountPasswordReset,
    EmailAliasCreate,
    <PERSON>ailA<PERSON>sUpdate,
    EmailAliasRead,
    EmailMetadataRead,
    <PERSON>ailContent,
    <PERSON>ailSend,
    <PERSON>ail<PERSON><PERSON>,
    EmailRead,
    EmailFlag,
    MailboxCreate,
)
from app.modules.shared.email.services.provision_service import ProvisionService  # noqa: E402
from app.modules.shared.email.services.email_actions_service import EmailActionsService
from app.modules.shared.email.services.quota_service import QuotaService
from app.modules.shared.email.services.auth_service import EmailAuthService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/email", tags=["Email"])

# Initialize services
provision_service = ProvisionService()
email_actions_service = EmailActionsService()
quota_service = QuotaService()
auth_service = EmailAuthService()


# Email Domain endpoints


@router.get("/domains", response_model=List[EmailDomainRead])
async def list_email_domains(
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """List email domains for the current tenant."""
    return await provision_service.get_email_domains(db, tenant_id=tenant.id)


@router.post("/domains", response_model=EmailDomainRead, status_code=status.HTTP_201_CREATED)
async def create_email_domain(
    domain_in: EmailDomainCreate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Create a new email domain for the current tenant."""
    return await provision_service.create_email_domain(db, tenant_id=tenant.id, domain_in=domain_in)


@router.get("/domains/{domain_id}", response_model=EmailDomainRead)
async def get_email_domain(
    domain_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Get a specific email domain."""
    domain = await provision_service.get_email_domain(db, domain_id=domain_id)
    if not domain or domain.tenant_id != tenant.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email domain not found",
        )
    return domain


@router.put("/domains/{domain_id}", response_model=EmailDomainRead)
async def update_email_domain(
    domain_id: uuid.UUID,
    domain_in: EmailDomainUpdate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Update an email domain."""
    domain = await provision_service.get_email_domain(db, domain_id=domain_id)
    if not domain or domain.tenant_id != tenant.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email domain not found",
        )
    return await provision_service.update_email_domain(db, domain_id=domain_id, domain_in=domain_in)


@router.delete("/domains/{domain_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_email_domain(
    domain_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER])),
):
    """Delete an email domain."""
    domain = await provision_service.get_email_domain(db, domain_id=domain_id)
    if not domain or domain.tenant_id != tenant.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email domain not found",
        )
    await provision_service.delete_email_domain(db, domain_id=domain_id)
    return None


@router.get("/domains/{domain_id}/dns-records", response_model=EmailDomainDNSRecords)
async def get_email_domain_dns_records(
    domain_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Get DNS records for an email domain."""
    domain = await provision_service.get_email_domain(db, domain_id=domain_id)
    if not domain or domain.tenant_id != tenant.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email domain not found",
        )
    return await provision_service.get_dns_records(db, domain_id=domain_id)


@router.post("/domains/{domain_id}/verify-dns", response_model=EmailDomainRead)
async def verify_email_domain_dns(
    domain_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Verify DNS records for an email domain."""
    domain = await provision_service.get_email_domain(db, domain_id=domain_id)
    if not domain or domain.tenant_id != tenant.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email domain not found",
        )
    return await provision_service.verify_dns_records(db, domain_id=domain_id)


@router.post("/domains/{domain_id}/generate-dkim", response_model=EmailDomainRead)
async def generate_dkim_keys(
    domain_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Generate DKIM keys for an email domain."""
    domain = await provision_service.get_email_domain(db, domain_id=domain_id)
    if not domain or domain.tenant_id != tenant.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email domain not found",
        )
    return await provision_service.generate_dkim_keys(db, domain_id=domain_id)


# Email Account endpoints


@router.get("/domains/{domain_id}/accounts", response_model=List[EmailAccountRead])
async def list_email_accounts(
    domain_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """List email accounts for a domain."""
    domain = await provision_service.get_email_domain(db, domain_id=domain_id)
    if not domain or domain.tenant_id != tenant.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email domain not found",
        )
    return await provision_service.get_email_accounts(db, domain_id=domain_id)


@router.post(
    "/domains/{domain_id}/accounts",
    response_model=EmailAccountRead,
    status_code=status.HTTP_201_CREATED,
)
async def create_email_account(
    domain_id: uuid.UUID,
    account_in: EmailAccountCreate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Create a new email account."""
    domain = await provision_service.get_email_domain(db, domain_id=domain_id)
    if not domain or domain.tenant_id != tenant.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email domain not found",
        )

    # Ensure the account is created for the correct domain
    if account_in.email_domain_id != domain_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email domain ID mismatch",
        )

    return await provision_service.create_email_account(db, account_in=account_in)


# Email Account management endpoints


@router.get("/accounts/{account_id}", response_model=EmailAccountRead)
async def get_email_account(
    account_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Get a specific email account."""
    account = await provision_service.get_email_account(db, account_id=account_id)
    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email account not found",
        )

    # Check if the account belongs to the current tenant
    domain = await provision_service.get_email_domain(db, domain_id=account.email_domain_id)
    if not domain or domain.tenant_id != tenant.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email account not found",
        )

    return account


@router.put("/accounts/{account_id}", response_model=EmailAccountRead)
async def update_email_account(
    account_id: uuid.UUID,
    account_in: EmailAccountUpdate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Update an email account."""
    account = await provision_service.get_email_account(db, account_id=account_id)
    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email account not found",
        )

    # Check if the account belongs to the current tenant
    domain = await provision_service.get_email_domain(db, domain_id=account.email_domain_id)
    if not domain or domain.tenant_id != tenant.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email account not found",
        )

    return await provision_service.update_email_account(
        db, account_id=account_id, account_in=account_in
    )


@router.delete("/accounts/{account_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_email_account(
    account_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Delete an email account."""
    account = await provision_service.get_email_account(db, account_id=account_id)
    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email account not found",
        )

    # Check if the account belongs to the current tenant
    domain = await provision_service.get_email_domain(db, domain_id=account.email_domain_id)
    if not domain or domain.tenant_id != tenant.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email account not found",
        )

    await provision_service.delete_email_account(db, account_id=account_id)
    return None


@router.get("/accounts/{account_id}/usage", response_model=EmailAccountUsage)
async def get_email_account_usage(
    account_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Get storage usage for an email account."""
    account = await provision_service.get_email_account(db, account_id=account_id)
    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email account not found",
        )

    # Check if the account belongs to the current tenant
    domain = await provision_service.get_email_domain(db, domain_id=account.email_domain_id)
    if not domain or domain.tenant_id != tenant.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email account not found",
        )

    return await quota_service.get_usage(db, account_id=account_id)


@router.put("/accounts/{account_id}/quota", response_model=EmailAccountRead)
async def update_email_account_quota(
    account_id: uuid.UUID,
    quota_mb: int,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Update quota for an email account."""
    account = await provision_service.get_email_account(db, account_id=account_id)
    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email account not found",
        )

    # Check if the account belongs to the current tenant
    domain = await provision_service.get_email_domain(db, domain_id=account.email_domain_id)
    if not domain or domain.tenant_id != tenant.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email account not found",
        )

    return await quota_service.update_quota(db, account_id=account_id, new_quota_mb=quota_mb)


@router.put("/accounts/{account_id}/password", status_code=status.HTTP_204_NO_CONTENT)
async def change_email_account_password(
    account_id: uuid.UUID,
    password_change: EmailAccountPasswordChange,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Change password for an email account (user must own the account)."""
    account = await provision_service.get_email_account(db, account_id=account_id)
    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email account not found",
        )

    # Check if the current user owns this account
    if account.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to change this account's password",
        )

    # Verify current password
    if not await auth_service.authenticate(
        db, account.full_email, password_change.current_password
    ):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Incorrect current password",
        )

    await auth_service.change_password(
        db, account_id=account_id, new_password=password_change.new_password
    )
    return None


@router.post("/accounts/{account_id}/reset-password", status_code=status.HTTP_204_NO_CONTENT)
async def reset_email_account_password(
    account_id: uuid.UUID,
    password_reset: EmailAccountPasswordReset,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Reset password for an email account (admin only)."""
    account = await provision_service.get_email_account(db, account_id=account_id)
    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email account not found",
        )

    # Check if the account belongs to the current tenant
    domain = await provision_service.get_email_domain(db, domain_id=account.email_domain_id)
    if not domain or domain.tenant_id != tenant.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email account not found",
        )

    await auth_service.reset_password(
        db, account_id=account_id, new_password=password_reset.new_password
    )
    return None


# Email Alias endpoints


@router.get("/domains/{domain_id}/aliases", response_model=List[EmailAliasRead])
async def list_email_aliases(
    domain_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """List email aliases for a domain."""
    domain = await provision_service.get_email_domain(db, domain_id=domain_id)
    if not domain or domain.tenant_id != tenant.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email domain not found",
        )
    return await provision_service.get_email_aliases(db, domain_id=domain_id)


@router.post(
    "/domains/{domain_id}/aliases",
    response_model=EmailAliasRead,
    status_code=status.HTTP_201_CREATED,
)
async def create_email_alias(
    domain_id: uuid.UUID,
    alias_in: EmailAliasCreate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Create a new email alias."""
    domain = await provision_service.get_email_domain(db, domain_id=domain_id)
    if not domain or domain.tenant_id != tenant.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email domain not found",
        )

    # Ensure the alias is created for the correct domain
    if alias_in.email_domain_id != domain_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email domain ID mismatch",
        )

    return await provision_service.create_email_alias(db, alias_in=alias_in)


@router.get("/aliases/{alias_id}", response_model=EmailAliasRead)
async def get_email_alias(
    alias_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Get a specific email alias."""
    alias = await provision_service.get_email_alias(db, alias_id=alias_id)
    if not alias:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email alias not found",
        )

    # Check if the alias belongs to the current tenant
    domain = await provision_service.get_email_domain(db, domain_id=alias.email_domain_id)
    if not domain or domain.tenant_id != tenant.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email alias not found",
        )

    return alias


@router.put("/aliases/{alias_id}", response_model=EmailAliasRead)
async def update_email_alias(
    alias_id: uuid.UUID,
    alias_in: EmailAliasUpdate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Update an email alias."""
    alias = await provision_service.get_email_alias(db, alias_id=alias_id)
    if not alias:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email alias not found",
        )

    # Check if the alias belongs to the current tenant
    domain = await provision_service.get_email_domain(db, domain_id=alias.email_domain_id)
    if not domain or domain.tenant_id != tenant.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email alias not found",
        )

    return await provision_service.update_email_alias(db, alias_id=alias_id, alias_in=alias_in)


@router.delete("/aliases/{alias_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_email_alias(
    alias_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Delete an email alias."""
    alias = await provision_service.get_email_alias(db, alias_id=alias_id)
    if not alias:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email alias not found",
        )

    # Check if the alias belongs to the current tenant
    domain = await provision_service.get_email_domain(db, domain_id=alias.email_domain_id)
    if not domain or domain.tenant_id != tenant.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email alias not found",
        )

    await provision_service.delete_email_alias(db, alias_id=alias_id)
    return None


# Webmail API endpoints will be implemented in a separate file
