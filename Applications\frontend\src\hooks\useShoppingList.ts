import { useState, useCallback } from 'react';
import { shoppingListApi } from '@/lib/api/client';
import { 
  ShoppingList,
  ShoppingListCreate,
  ShoppingListUpdate,
  ShoppingListWithItems,
  ShoppingListItem,
  ShoppingListItemCreate,
  ShoppingListItemUpdate,
  ShoppingListItemTogglePurchased,
  ShoppingListFilters,
  ShoppingListItemFilters,
  Priority
} from '@/types/shopping-list';

export const useShoppingList = () => {
  const [lists, setLists] = useState<ShoppingList[]>([]);
  const [currentList, setCurrentList] = useState<ShoppingListWithItems | null>(null);
  const [items, setItems] = useState<ShoppingListItem[]>([]);
  const [suggestions, setSuggestions] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // === Shopping Lists ===
  
  const fetchLists = useCallback(async (filters?: ShoppingListFilters) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await shoppingListApi.getLists(filters);
      setLists(response);
    } catch (err) {
      setError('Failed to fetch shopping lists.');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const getListById = useCallback(async (id: string): Promise<ShoppingList | null> => {
    setError(null);
    try {
      const list = await shoppingListApi.getListById(id);
      return list;
    } catch (err) {
      setError('Failed to fetch shopping list.');
      console.error(err);
      return null;
    }
  }, []);

  const getListWithItems = useCallback(async (id: string): Promise<ShoppingListWithItems | null> => {
    setError(null);
    try {
      const list = await shoppingListApi.getListById(id);
      const items = await shoppingListApi.getItems(id);
      const listWithItems = { ...list, items };
      setCurrentList(listWithItems);
      setItems(items);
      return listWithItems;
    } catch (err) {
      setError('Failed to fetch shopping list with items.');
      console.error(err);
      return null;
    }
  }, []);

  const createList = async (listData: ShoppingListCreate) => {
    try {
      const newList = await shoppingListApi.createList(listData);
      setLists((prev) => [newList, ...prev]);
      return newList;
    } catch (err) {
      setError('Failed to create shopping list.');
      console.error(err);
      throw err;
    }
  };

  const updateList = async (id: string, listData: ShoppingListUpdate) => {
    try {
      const updatedList = await shoppingListApi.updateList(id, listData);
      setLists((prev) =>
        prev.map((list) => (list.id === id ? updatedList : list))
      );
      if (currentList && currentList.id === id) {
        setCurrentList({ ...currentList, ...updatedList });
      }
      return updatedList;
    } catch (err) {
      setError('Failed to update shopping list.');
      console.error(err);
      throw err;
    }
  };

  const deleteList = async (id: string) => {
    try {
      await shoppingListApi.deleteList(id);
      setLists((prev) => prev.filter((list) => list.id !== id));
      if (currentList && currentList.id === id) {
        setCurrentList(null);
        setItems([]);
      }
    } catch (err) {
      setError('Failed to delete shopping list.');
      console.error(err);
      throw err;
    }
  };

  // === Shopping List Items ===

  const fetchItems = useCallback(async (listId: string, filters?: ShoppingListItemFilters) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await shoppingListApi.getItems(listId);
      setItems(response);
    } catch (err) {
      setError('Failed to fetch shopping list items.');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const createItem = async (listId: string, itemData: ShoppingListItemCreate) => {
    try {
      const newItem = await shoppingListApi.addItem(listId, itemData);
      setItems((prev) => [newItem, ...prev]);
      if (currentList && currentList.id === listId) {
        setCurrentList({
          ...currentList,
          items: [newItem, ...currentList.items]
        });
      }
      return newItem;
    } catch (err) {
      setError('Failed to create shopping list item.');
      console.error(err);
      throw err;
    }
  };

  const updateItem = async (listId: string, itemId: string, itemData: ShoppingListItemUpdate) => {
    try {
      const updatedItem = await shoppingListApi.updateItem(listId, itemId, itemData);
      setItems((prev) =>
        prev.map((item) => (item.id === itemId ? updatedItem : item))
      );
      if (currentList) {
        setCurrentList({
          ...currentList,
          items: currentList.items.map((item) => 
            item.id === itemId ? updatedItem : item
          )
        });
      }
      return updatedItem;
    } catch (err) {
      setError('Failed to update shopping list item.');
      console.error(err);
      throw err;
    }
  };

  const deleteItem = async (listId: string, itemId: string) => {
    try {
      await shoppingListApi.deleteItem(listId, itemId);
      setItems((prev) => prev.filter((item) => item.id !== itemId));
      if (currentList) {
        setCurrentList({
          ...currentList,
          items: currentList.items.filter((item) => item.id !== itemId)
        });
      }
    } catch (err) {
      setError('Failed to delete shopping list item.');
      console.error(err);
      throw err;
    }
  };

  const togglePurchased = async (itemId: string, purchased: boolean) => {
    try {
      if (!currentList?.id) {
        throw new Error('No current list selected');
      }
      const updatedItem = await shoppingListApi.updateItem(currentList.id, itemId, { purchased });
      setItems((prev) =>
        prev.map((item) => (item.id === itemId ? updatedItem : item))
      );
      if (currentList) {
        setCurrentList({
          ...currentList,
          items: currentList.items.map((item) => 
            item.id === itemId ? updatedItem : item
          )
        });
      }
      return updatedItem;
    } catch (err) {
      setError('Failed to toggle item purchased status.');
      console.error(err);
      throw err;
    }
  };

  // === Utility Functions ===

  const getPendingItems = useCallback((): ShoppingListItem[] => {
    return items.filter(item => !item.purchased);
  }, [items]);

  const getPurchasedItems = useCallback((): ShoppingListItem[] => {
    return items.filter(item => item.purchased);
  }, [items]);

  const getItemsByPriority = useCallback((priority: Priority): ShoppingListItem[] => {
    return items.filter(item => item.priority === priority);
  }, [items]);

  const getTotalEstimatedCost = useCallback((): number => {
    return items.reduce((total, item) => {
      if (!item.purchased && item.estimated_price) {
        return total + (item.estimated_price * item.quantity);
      }
      return total;
    }, 0);
  }, [items]);

  // === Integration Functions ===

  const fetchLowStockSuggestions = useCallback(async (threshold: number = 10) => {
    setError(null);
    try {
      // TODO: Implement low stock suggestions API endpoint
      // For now, return empty array as this feature is not yet implemented
      const response: any[] = [];
      setSuggestions(response);
      return response;
    } catch (err) {
      setError('Failed to fetch low stock suggestions.');
      console.error(err);
      return [];
    }
  }, []);

  const createItemFromInventory = async (
    listId: string,
    inventoryItemId: string,
    quantity: number = 1
  ) => {
    try {
      // TODO: Implement proper inventory integration
      // For now, create a basic item using the addItem method
      const itemData = {
        name: `Inventory Item ${inventoryItemId}`,
        quantity: quantity,
        inventory_item_id: inventoryItemId,
        purchased: false
      };
      const newItem = await shoppingListApi.addItem(listId, itemData);
      setItems((prev) => [newItem, ...prev]);
      if (currentList && currentList.id === listId) {
        setCurrentList({
          ...currentList,
          items: [newItem, ...currentList.items]
        });
      }
      return newItem;
    } catch (err) {
      setError('Failed to create item from inventory.');
      console.error(err);
      throw err;
    }
  };

  return {
    // State
    lists,
    currentList,
    items,
    suggestions,
    isLoading,
    error,

    // Shopping Lists
    fetchLists,
    getListById,
    getListWithItems,
    createList,
    updateList,
    deleteList,

    // Shopping List Items
    fetchItems,
    createItem,
    updateItem,
    deleteItem,
    togglePurchased,

    // Utility Functions
    getPendingItems,
    getPurchasedItems,
    getItemsByPriority,
    getTotalEstimatedCost,

    // Integration Functions
    fetchLowStockSuggestions,
    createItemFromInventory,
  };
};
