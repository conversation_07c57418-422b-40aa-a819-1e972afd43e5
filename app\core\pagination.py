"""Pagination utilities for the application."""

from typing import Generic, TypeVar, List
from pydantic import BaseModel, Field

T = TypeVar('T')


class PaginationParams(BaseModel):
    """Parameters for pagination."""
    
    page: int = Field(1, ge=1, description="Page number")
    size: int = Field(20, ge=1, le=100, description="Items per page")


class PaginatedResponse(BaseModel, Generic[T]):
    """Generic paginated response."""
    
    items: List[T] = Field(default_factory=list, description="List of items")
    total: int = Field(0, description="Total number of items")
    page: int = Field(1, description="Current page number")
    size: int = Field(20, description="Items per page")
    pages: int = Field(0, description="Total number of pages")
    
    def __init__(self, **data):
        super().__init__(**data)
        # Calculate pages if not provided
        if self.pages == 0 and self.size > 0:
            self.pages = (self.total - 1) // self.size + 1 if self.total > 0 else 0
