import uuid  # Import uuid
from pydantic import (
    BaseModel,
    ConfigDict,
    Field,
    model_validator,
)  # Adicionado model_validator
from typing import Optional, List, Union  # noqa: E402
from uuid import UUID

# Import the schema for the options
from .variant_option import (  # noqa: E402
    VariantOptionRead,
    VariantOptionCreate,
)  # Need Create for nested creation


class VariantGroupBase(BaseModel):
    """Base schema for Variant Group."""

    name: str = Field(
        ...,
        max_length=100,
        description="Name of the variant group (e.g., Size, Cooking Point)",
    )
    description: Optional[str] = Field(
        None, max_length=255, description="Optional description for templates"
    )
    min_selection: int = Field(
        1, ge=0, description="Minimum number of options that must be selected"
    )
    max_selection: int = Field(
        1, ge=1, description="Maximum number of options that can be selected"
    )
    display_order: int = Field(
        0, description="Order of this group relative to other groups for the item"
    )
    is_required: bool = Field(False, description="Is this group required for the item?")
    is_active: bool = Field(True, description="Is this group active for the item?")
    requires_default_selection: bool = Field(True, description="Requires one option to always be selected as default")

    @model_validator(mode="after")
    def check_max_ge_min(self) -> "VariantGroupBase":
        if (
            self.max_selection < self.min_selection
        ):  # min_selection e max_selection são obrigatórios em Base
            raise ValueError("max_selection must be greater than or equal to min_selection")
        return self


class VariantGroupCreate(VariantGroupBase):
    """Schema for creating a new Variant Group, potentially with options."""

    id: Optional[Union[uuid.UUID, str]] = Field(None, description="Optional ID for existing groups")
    # menu_item_id will be a path parameter or inferred
    # tenant_id will be added by the service
    options: List[VariantOptionCreate] = Field(
        [], description="List of options to create within this group"
    )


class VariantGroupUpdate(VariantGroupBase):
    """Schema for updating an existing Variant Group. All fields optional."""

    name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = Field(None, max_length=255)
    min_selection: Optional[int] = Field(None, ge=0)
    max_selection: Optional[int] = Field(None, ge=1)
    display_order: Optional[int] = None
    is_required: Optional[bool] = None
    is_active: Optional[bool] = None
    requires_default_selection: Optional[bool] = None
    # Updating options might be handled by separate endpoints or require specific logic here
    # options: Optional[List[Union[VariantOptionUpdate, VariantOptionCreate,
    # int]]] = None # Example complex update

    @model_validator(mode="after")
    def check_max_ge_min_update_v2(self) -> "VariantGroupUpdate":
        # Valida se max_selection é >= min_selection, mas apenas se ambos são fornecidos na atualização  # noqa: E501
        # e não são None. Se um não for fornecido, esta validação específica não se aplica
        # (outras validações como ge=0 ou ge=1 ainda se aplicam no nível do campo).
        if self.min_selection is not None and self.max_selection is not None:
            if self.max_selection < self.min_selection:
                raise ValueError(
                    "max_selection must be greater than or equal to min_selection if both are provided"  # noqa: E501
                )
        return self


class VariantGroupRead(VariantGroupBase):
    """Schema for reading a Variant Group, including its options."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    menu_item_id: Optional[uuid.UUID] = None  # Nullable for template groups
    options: List[VariantOptionRead] = []  # Include the options

    model_config = ConfigDict(from_attributes=True)


# Simplified version without options for list performance
class VariantGroupReadSimple(BaseModel):
    """Schema for reading a Variant Group without options for list performance."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    menu_item_id: Optional[uuid.UUID] = None  # Nullable for template groups
    name: str
    description: Optional[str] = None
    min_selection: int
    max_selection: int
    display_order: int
    is_required: bool = False
    is_active: bool = True
    requires_default_selection: bool = True
    usage_count: Optional[int] = 0

    model_config = ConfigDict(from_attributes=True)


# Version with options for template functionality
class VariantGroupReadWithOptions(BaseModel):
    """Schema for reading a Variant Group with options."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    menu_item_id: Optional[uuid.UUID] = None  # Nullable for template groups
    name: str
    description: Optional[str] = None
    min_selection: int
    max_selection: int
    display_order: int
    is_required: bool = False
    is_active: bool = True
    requires_default_selection: bool = True
    options: List[VariantOptionRead] = []  # Include the options
    usage_count: Optional[int] = 0

    model_config = ConfigDict(from_attributes=True)


# No template-specific schemas needed anymore
# Groups are now shared directly without template/instance distinction
