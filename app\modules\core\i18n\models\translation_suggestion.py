"""
Translation suggestion model for the i18n module.
"""

import uuid
import enum
from sqlalchemy import Column, String, Text, ForeignKey, Index, Enum, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base


class TranslationSuggestionStatus(str, enum.Enum):
    """Enum for translation suggestion status."""

    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    IMPLEMENTED_MANUALLY = "implemented_manually"


class TranslationSuggestion(Base):
    """
    Model for storing user suggestions for translations.
    """

    __tablename__ = "translation_suggestions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    key = Column(String(255), nullable=False, index=True)
    suggested_text = Column(Text, nullable=False)
    language_id = Column(UUID(as_uuid=True), ForeignKey("languages.id"), nullable=False)
    status = Column(
        Enum(TranslationSuggestionStatus),
        default=TranslationSuggestionStatus.PENDING,
        nullable=False,
    )

    # Foreign key to TranslationKey
    translation_key_id = Column(Integer, ForeignKey("i18n_translation_keys.id"), nullable=True)

    # Foreign keys
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)

    # Relationships
    language = relationship("Language", backref="translation_suggestions")
    user = relationship("User", back_populates="translation_suggestions")
    key_obj = relationship(
        "TranslationKey", foreign_keys=[translation_key_id], back_populates="suggestions"
    )

    __table_args__ = (Index("ix_translation_suggestions_key_language_id", "key", "language_id"),)

    def __repr__(self):
        return f"<TranslationSuggestion(id={self.id}, key='{self.key}', language_id='{self.language_id}', status='{self.status}')>"
