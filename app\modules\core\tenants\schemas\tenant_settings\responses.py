"""
Response schemas for tenant settings API endpoints.
"""

import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any

from pydantic import BaseModel, Field


class LanguageOption(BaseModel):
    """Schema for language option response."""
    
    code: str = Field(
        ..., 
        max_length=5, 
        description="Language code (e.g., 'en-US', 'pt-BR')"
    )
    name: str = Field(
        ..., 
        max_length=100, 
        description="Language display name"
    )
    native_name: str = Field(
        ..., 
        max_length=100, 
        description="Language name in its native script"
    )
    is_active: bool = Field(
        True, 
        description="Whether this language is active in the system"
    )
    is_default: bool = Field(
        False, 
        description="Whether this is the default system language"
    )


class CurrencyOption(BaseModel):
    """Schema for currency option response."""

    code: str = Field(
        ...,
        min_length=3,
        max_length=3,
        description="Currency code (ISO 4217)"
    )
    name: str = Field(
        ...,
        max_length=100,
        description="Currency display name"
    )
    symbol: str = Field(
        ...,
        max_length=10,
        description="Currency symbol"
    )
    decimal_places: int = Field(
        2,
        ge=0,
        le=4,
        description="Number of decimal places for this currency"
    )
    decimal_separator: str = Field(
        ".",
        max_length=1,
        description="Decimal separator: '.' or ','"
    )
    thousands_separator: str = Field(
        ",",
        max_length=1,
        description="Thousands separator: ',' or '.'"
    )
    symbol_position: str = Field(
        "left",
        description="Symbol position: 'left' or 'right'"
    )
    symbol_spacing: bool = Field(
        False,
        description="Whether to add space between symbol and amount"
    )


class CurrencyConfiguration(BaseModel):
    """Schema for multi-currency configuration."""

    default_currency: str = Field(
        ...,
        min_length=3,
        max_length=3,
        description="Default currency code (1:1 ratio for stability)"
    )
    enabled_currencies: List[str] = Field(
        ...,
        description="List of enabled currency codes"
    )
    exchange_rates: Dict[str, float] = Field(
        ...,
        description="Exchange rates relative to default currency"
    )
    auto_update_rates: bool = Field(
        False,
        description="Whether to automatically update exchange rates"
    )
    rate_update_frequency: Optional[str] = Field(
        None,
        description="Rate update frequency: 'daily', 'hourly', 'manual'"
    )
    display_currency: Optional[str] = Field(
        None,
        description="User's preferred display currency"
    )
    currency_formatting: Dict[str, Dict[str, Any]] = Field(
        ...,
        description="Formatting rules for each currency"
    )


class TimezoneOption(BaseModel):
    """Schema for timezone option response."""
    
    identifier: str = Field(
        ..., 
        max_length=50, 
        description="Timezone identifier (e.g., 'America/Sao_Paulo')"
    )
    display_name: str = Field(
        ..., 
        max_length=100, 
        description="Human-readable timezone name"
    )
    offset: str = Field(
        ..., 
        max_length=10, 
        description="UTC offset (e.g., '-03:00')"
    )
    country: Optional[str] = Field(
        None, 
        max_length=2, 
        description="Country code (ISO 3166-1 alpha-2)"
    )



class SubscriptionInfo(BaseModel):
    """Schema for subscription information response."""
    
    plan_name: str = Field(
        ..., 
        max_length=100, 
        description="Current subscription plan name"
    )
    plan_id: Optional[str] = Field(
        None, 
        description="Plan identifier"
    )
    status: str = Field(
        ..., 
        max_length=20, 
        description="Subscription status"
    )
    start_date: Optional[datetime] = Field(
        None, 
        description="Subscription start date"
    )
    end_date: Optional[datetime] = Field(
        None, 
        description="Subscription end date"
    )
    next_billing_date: Optional[datetime] = Field(
        None, 
        description="Next billing date"
    )
    features: Optional[List[Dict[str, Any]]] = Field(
        None, 
        description="List of available features in this plan"
    )
    usage: Optional[Dict[str, Any]] = Field(
        None, 
        description="Current usage statistics"
    )
    limits: Optional[Dict[str, Any]] = Field(
        None,
        description="Plan limits and quotas"
    )


class OperatingHoursDay(BaseModel):
    """Schema for a single day's operating hours."""

    day: str = Field(
        ...,
        description="Day of the week (monday, tuesday, etc.)"
    )
    is_open: bool = Field(
        True,
        description="Whether the business is open on this day"
    )
    open_time: Optional[str] = Field(
        None,
        pattern=r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$",
        description="Opening time in HH:MM format"
    )
    close_time: Optional[str] = Field(
        None,
        pattern=r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$",
        description="Closing time in HH:MM format"
    )
    breaks: Optional[List[Dict[str, str]]] = Field(
        None,
        description="List of break periods with start and end times"
    )
    is_24_hours: bool = Field(
        False,
        description="Whether the business operates 24 hours on this day"
    )


class LoyaltySystemConfig(BaseModel):
    """Schema for loyalty system configuration."""

    points_per_currency_unit: float = Field(
        1.0,
        ge=0,
        description="Points earned per currency unit spent"
    )
    currency_per_point: float = Field(
        0.01,
        ge=0,
        description="Currency value per loyalty point"
    )
    minimum_points_to_redeem: int = Field(
        100,
        ge=0,
        description="Minimum points required for redemption"
    )
    points_expiry_days: Optional[int] = Field(
        None,
        ge=0,
        description="Days after which points expire (null for no expiry)"
    )
    welcome_bonus_points: int = Field(
        0,
        ge=0,
        description="Bonus points awarded on signup"
    )
    birthday_bonus_points: int = Field(
        0,
        ge=0,
        description="Bonus points awarded on birthday"
    )
    referral_bonus_points: int = Field(
        0,
        ge=0,
        description="Bonus points awarded for referrals"
    )
    tiers: Optional[List[Dict[str, Any]]] = Field(
        None,
        description="Loyalty tier configuration"
    )


class TaxCalculationPreview(BaseModel):
    """Schema for tax calculation preview."""

    base_amount: float = Field(
        ...,
        ge=0,
        description="Base amount before tax"
    )
    tax_rate: float = Field(
        ...,
        ge=0,
        le=100,
        description="Tax rate percentage"
    )
    tax_amount: float = Field(
        ...,
        ge=0,
        description="Calculated tax amount"
    )
    total_amount: float = Field(
        ...,
        ge=0,
        description="Total amount including tax"
    )
    calculation_method: str = Field(
        ...,
        description="Tax calculation method used"
    )


class SettingsValidationError(BaseModel):
    """Schema for settings validation error response."""

    field: str = Field(
        ...,
        description="Field that failed validation"
    )
    message: str = Field(
        ...,
        description="Validation error message"
    )
    code: Optional[str] = Field(
        None,
        description="Error code for programmatic handling"
    )
