"""
Notifications API

Endpoints principais para gerenciamento de notificações.
"""

import logging
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.user_auth import get_current_active_user
from app.modules.core.auth.dependencies.role_auth import require_admin_user
from app.modules.core.users.models.user import User

from ..schemas import (
    NotificationCreate, NotificationUpdate, NotificationResponse,
    NotificationListResponse, NotificationMarkAsRead, NotificationMarkAsDeleted,
    NotificationFilters, NotificationBulkAction
)
from ..services import NotificationService, NotificationDeliveryService

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/", response_model=NotificationResponse)
async def create_notification(
    notification_data: NotificationCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Cria uma nova notificação.
    
    Requer autenticação. Admins podem criar qualquer tipo de notificação.
    Tenant owners podem criar notificações para seus tenants.
    """
    service = NotificationService(db)
    delivery_service = NotificationDeliveryService(db)
    
    # Valida permissões de entrega
    temp_notification = type('obj', (object,), {
        'target_type': notification_data.target_type,
        'tenant_id': notification_data.tenant_id
    })()
    
    if not await delivery_service.validate_delivery_permissions(temp_notification, current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Sem permissão para enviar notificação para este público"
        )
    
    notification = await service.create_notification(notification_data, current_user)
    
    # TODO: Adicionar campos calculados para resposta
    # notification.is_read = notification.is_read_by_user(str(current_user.id))
    # notification.is_deleted = notification.is_deleted_by_user(str(current_user.id))
    
    return notification


@router.get("/", response_model=NotificationListResponse)
async def list_notifications(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    priority: Optional[str] = Query(None),
    is_read: Optional[bool] = Query(None),
    search: Optional[str] = Query(None),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Lista notificações do usuário com filtros e paginação.
    """
    service = NotificationService(db)
    
    # Constrói filtros
    filters = NotificationFilters(
        status=status,
        priority=priority,
        is_read=is_read,
        search=search
    )
    
    notifications, total = await service.get_notifications(
        current_user, filters, page, per_page
    )
    
    # TODO: Adicionar campos calculados
    # for notification in notifications:
    #     notification.is_read = notification.is_read_by_user(str(current_user.id))
    #     notification.is_deleted = notification.is_deleted_by_user(str(current_user.id))
    
    return NotificationListResponse(
        notifications=notifications,
        total=total,
        page=page,
        per_page=per_page,
        has_next=(page * per_page) < total,
        has_prev=page > 1
    )


@router.get("/{notification_id}", response_model=NotificationResponse)
async def get_notification(
    notification_id: UUID,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Obtém uma notificação específica.
    """
    service = NotificationService(db)
    
    notification = await service.get_notification(notification_id, current_user)
    if not notification:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Notificação não encontrada"
        )
    
    # TODO: Adicionar campos calculados
    # notification.is_read = notification.is_read_by_user(str(current_user.id))
    # notification.is_deleted = notification.is_deleted_by_user(str(current_user.id))
    
    return notification


@router.put("/{notification_id}", response_model=NotificationResponse)
async def update_notification(
    notification_id: UUID,
    update_data: NotificationUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Atualiza uma notificação.
    
    Apenas o remetente ou admin podem editar.
    """
    service = NotificationService(db)
    
    notification = await service.update_notification(notification_id, update_data, current_user)
    if not notification:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Notificação não encontrada ou sem permissão para editar"
        )
    
    # TODO: Adicionar campos calculados
    # notification.is_read = notification.is_read_by_user(str(current_user.id))
    # notification.is_deleted = notification.is_deleted_by_user(str(current_user.id))
    
    return notification


@router.delete("/{notification_id}")
async def delete_notification(
    notification_id: UUID,
    delete_for_all: bool = Query(False),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Deleta uma notificação.
    
    Se delete_for_all=True, deleta para todos os usuários (apenas admin/owner).
    Caso contrário, deleta apenas para o usuário atual.
    """
    service = NotificationService(db)
    
    success = await service.delete_notification(notification_id, current_user, delete_for_all)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Notificação não encontrada ou sem permissão para deletar"
        )
    
    return {"message": "Notificação deletada com sucesso"}


@router.post("/mark-read")
async def mark_notifications_as_read(
    data: NotificationMarkAsRead,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Marca notificações como lidas.
    """
    service = NotificationService(db)
    
    count = await service.mark_as_read(data.notification_ids, current_user)
    
    return {
        "message": f"{count} notificações marcadas como lidas",
        "count": count
    }


@router.post("/bulk-action")
async def bulk_action(
    data: NotificationBulkAction,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Executa ação em lote nas notificações.
    """
    service = NotificationService(db)
    
    if data.action == "mark_read":
        count = await service.mark_as_read(data.notification_ids, current_user)
        return {"message": f"{count} notificações marcadas como lidas", "count": count}
    
    elif data.action == "delete":
        count = 0
        for notification_id in data.notification_ids:
            success = await service.delete_notification(
                notification_id, current_user, data.delete_for_all
            )
            if success:
                count += 1
        
        return {"message": f"{count} notificações deletadas", "count": count}
    
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Ação não suportada: {data.action}"
        )


@router.post("/{notification_id}/click")
async def register_click(
    notification_id: UUID,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Registra um clique na notificação.
    """
    service = NotificationService(db)
    
    success = await service.increment_click_count(notification_id, current_user)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Notificação não encontrada"
        )
    
    return {"message": "Clique registrado com sucesso"}


@router.get("/{notification_id}/preview")
async def get_delivery_preview(
    notification_id: UUID,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Obtém preview da entrega da notificação.
    
    Mostra quantos usuários receberão a notificação.
    """
    service = NotificationService(db)
    delivery_service = NotificationDeliveryService(db)
    
    notification = await service.get_notification(notification_id, current_user)
    if not notification:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Notificação não encontrada"
        )
    
    preview = await delivery_service.get_delivery_preview(notification)
    
    return preview


# Endpoints administrativos
@router.get("/admin/all", response_model=NotificationListResponse)
async def admin_list_all_notifications(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    sender_type: Optional[str] = Query(None),
    target_type: Optional[str] = Query(None),
    tenant_id: Optional[UUID] = Query(None),
    search: Optional[str] = Query(None),
    current_user: User = Depends(require_admin_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Lista todas as notificações do sistema (apenas admin).
    """
    service = NotificationService(db)
    
    filters = NotificationFilters(
        status=status,
        sender_type=sender_type,
        target_type=target_type,
        tenant_id=tenant_id,
        search=search
    )
    
    notifications, total = await service.get_notifications(
        current_user, filters, page, per_page
    )
    
    return NotificationListResponse(
        notifications=notifications,
        total=total,
        page=page,
        per_page=per_page,
        has_next=(page * per_page) < total,
        has_prev=page > 1
    )
