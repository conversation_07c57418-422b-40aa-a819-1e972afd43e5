import logging
from typing import Optional, Sequence, List, Dict, Any
from sqlalchemy import select, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.exc import IntegrityError
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status
import uuid
from decimal import Decimal
from datetime import datetime

# Import models
from app.modules.core.eshop.models.product import Product, ApprovalStatus
from app.modules.core.eshop.models.product_category import ProductCategory
from app.core.enums import MarketType

# Import schemas
from app.modules.core.eshop.schemas.product import (
    ProductCreate,
    ProductUpdate,
    ProductResponse,
    ProductListResponse,
    ProductDetailResponse,
)
from app.modules.core.eshop.schemas.eshop_product import eshopProductCreate, eshopProductUpdate

# Import WebSocket utility
from app.websockets.manager import emit_to_tenant

# Import related services
from app.modules.core.eshop.services.category_service import CategoryService

logger = logging.getLogger(__name__)


class ProductService:
    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session
        self.category_service = CategoryService(db_session)

    async def create_product(
        self, product_in: eshopProductCreate, vendor_id: uuid.UUID, tenant_id: Optional[uuid.UUID]
    ) -> Product:
        product_data = product_in.dict()
        product = Product(**product_data, vendor_id=vendor_id, tenant_id=tenant_id)
        self.db_session.add(product)
        await self.db_session.commit()
        await self.db_session.refresh(product)
        return product

    async def get_product(self, product_id: uuid.UUID) -> Product | None:
        result = await self.db_session.execute(
            select(Product)
            .options(
                selectinload(Product.variant_groups).selectinload('*'),
                selectinload(Product.modifier_groups).selectinload('*'),
                selectinload(Product.optional_groups).selectinload('*'),
                joinedload(Product.category),
                joinedload(Product.vendor),
            )
            .filter(Product.id == product_id)
        )
        return result.scalars().first()

    async def get_all_products(self) -> list[Product]:
        result = await self.db_session.execute(select(Product).order_by(Product.name))
        return result.scalars().all()
    
    async def get_products_by_category(self, category_id: uuid.UUID) -> list[Product]:
        result = await self.db_session.execute(
            select(Product)
            .filter(Product.category_id == category_id)
            .order_by(Product.name)
        )
        return result.scalars().all()

    async def update_product(
        self,
        product_id: uuid.UUID,
        product_in: eshopProductUpdate,
        current_user_id: uuid.UUID,
        is_admin: bool,
    ) -> Product | None:
        product = await self.get_product(product_id)
        if not product:
            return None  # Product not found

        # Permission check: only admin or product's vendor can update
        if not is_admin and product.vendor_id != current_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You do not have permission to update this product.",
            )

        update_data = product_in.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(product, key, value)
        
        product.updated_at = datetime.utcnow()
        await self.db_session.commit()
        await self.db_session.refresh(product)
        return product

    async def delete_product(
        self, product_id: uuid.UUID, current_user_id: uuid.UUID, is_admin: bool
    ) -> bool:
        product = await self.get_product(product_id)
        if not product:
            return False

        # Permission check: only admin or product's vendor can delete
        if not is_admin and product.vendor_id != current_user_id:
            # Optionally raise HTTPException here or just return False
            logger.warning(
                f"Permission denied: User {current_user_id} attempted to delete product {product_id} owned by {product.vendor_id}"
            )
            return False

        await self.db_session.delete(product)
        await self.db_session.commit()
        return True

    async def get_products(
        self,
        tenant_id: uuid.UUID,
        market_type: Optional[MarketType] = None,
        category_slug: Optional[str] = None,
        status: Optional[ApprovalStatus] = None,
        min_price: Optional[float] = None,
        max_price: Optional[float] = None,
        search: Optional[str] = None,
        is_public_request: bool = False,
        page: int = 1,
        page_size: int = 100,
    ) -> Sequence[Product]:
        """Gets a list of products with filtering and sorting options."""
        query = (
            select(Product)
            .options(joinedload(Product.vendor), joinedload(Product.category))
            .order_by(Product.created_at.desc())
        )

        # Base filters
        filters = [or_(Product.tenant_id == tenant_id, Product.tenant_id.is_(None))]

        if market_type:
            filters.append(Product.market_type == market_type)
        
        if status:
            filters.append(Product.approval_status == status)

        if category_slug:
            # Subquery to find category_id from slug
            category_query = select(ProductCategory.id).where(ProductCategory.slug == category_slug)
            if tenant_id:
                category_query = category_query.where(ProductCategory.tenant_id == tenant_id)
            
            category_result = await self.db_session.execute(category_query)
            category_id = category_result.scalar_one_or_none()

            if category_id:
                filters.append(Product.category_id == category_id)
            else:
                # If category slug is provided but not found, return no products
                return []
        
        if search:
            search_filter = or_(
                Product.name.ilike(f"%{search}%"),
                Product.description.ilike(f"%{search}%"),
            )
            filters.append(search_filter)

        if min_price is not None:
            filters.append(Product.base_price >= min_price)

        if max_price is not None:
            filters.append(Product.base_price <= max_price)

        if filters:
            query = query.where(and_(*filters))

        # Apply pagination
        offset = (page - 1) * page_size
        query = query.offset(offset).limit(page_size)

        result = await self.db_session.execute(query)
        products = result.scalars().all()

        return products

    async def update_stock(
        self,
        product_id: uuid.UUID,
        quantity_change: int,
        current_user_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID] = None
    ) -> Optional[Product]:
        """Updates product stock quantity."""
        db_product = await self.get_product(product_id)
        if not db_product:
            return None

        # Validate vendor permissions
        if db_product.vendor_id != current_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You can only update stock for your own products.",
            )

        try:
            new_quantity = db_product.stock_quantity + quantity_change
            if new_quantity < 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Stock quantity cannot be negative.",
                )

            db_product.stock_quantity = new_quantity

            # Update status based on stock
            if new_quantity == 0 and db_product.manage_stock:
                db_product.status = "out_of_stock"
            elif db_product.status == "out_of_stock" and new_quantity > 0:
                db_product.status = "active"

            await self.db_session.commit()
            await self.db_session.refresh(db_product)

            # Emit WebSocket notification for low stock
            if new_quantity <= db_product.low_stock_threshold and db_product.tenant_id:
                await emit_to_tenant(
                    db_product.tenant_id,
                    "product_low_stock",
                    {
                        "product_id": str(db_product.id),
                        "product_name": db_product.name,
                        "stock_quantity": new_quantity,
                        "threshold": db_product.low_stock_threshold
                    }
                )

            logger.info(f"Product stock updated: {product_id}, new quantity: {new_quantity}")
            return db_product

        except Exception as e:
            await self.db_session.rollback()
            logger.exception(f"Error updating product stock: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal server error: {str(e)}",
            )

    # Fusion methods for product approval workflow
    
    async def approve_product(
        self,
        product_id: uuid.UUID,
        approver_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID] = None
    ) -> Optional[Product]:
        """Approves a product for marketplace listing."""
        db_product = await self.get_product(product_id)
        if not db_product:
            return None

        # Update approval status
        db_product.approval_status = ApprovalStatus.APPROVED
        db_product.approved_at = datetime.utcnow()
        db_product.approved_by = approver_id
        db_product.rejection_reason = None  # Clear any previous rejection reason

        try:
            await self.db_session.commit()
            await self.db_session.refresh(db_product)

            # Emit WebSocket notification to vendor
            if db_product.tenant_id:
                await emit_to_tenant(
                    db_product.tenant_id,
                    "product_approved",
                    {
                        "product_id": str(db_product.id),
                        "product_name": db_product.name,
                        "approved_at": db_product.approved_at.isoformat(),
                        "approver_id": str(approver_id)
                    }
                )

            logger.info(f"Product approved: {product_id} by user {approver_id}")
            return db_product

        except Exception as e:
            await self.db_session.rollback()
            logger.exception(f"Error approving product: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error approving product: {str(e)}",
            )

    async def reject_product(
        self,
        product_id: uuid.UUID,
        rejector_id: uuid.UUID,
        rejection_reason: str,
        tenant_id: Optional[uuid.UUID] = None
    ) -> Optional[Product]:
        """Rejects a product with reason."""
        db_product = await self.get_product(product_id)
        if not db_product:
            return None

        # Update approval status
        db_product.approval_status = ApprovalStatus.REJECTED
        db_product.approved_at = None
        db_product.approved_by = rejector_id  # Track who rejected
        db_product.rejection_reason = rejection_reason

        try:
            await self.db_session.commit()
            await self.db_session.refresh(db_product)

            # Emit WebSocket notification to vendor
            if db_product.tenant_id:
                await emit_to_tenant(
                    db_product.tenant_id,
                    "product_rejected",
                    {
                        "product_id": str(db_product.id),
                        "product_name": db_product.name,
                        "rejection_reason": rejection_reason,
                        "rejected_by": str(rejector_id)
                    }
                )

            logger.info(f"Product rejected: {product_id} by user {rejector_id}")
            return db_product

        except Exception as e:
            await self.db_session.rollback()
            logger.exception(f"Error rejecting product: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error rejecting product: {str(e)}",
            )

    async def get_products_pending_approval(
        self,
        tenant_id: Optional[uuid.UUID] = None,
        skip: int = 0,
        limit: int = 100
    ) -> Sequence[Product]:
        """Gets products pending approval."""
        query = select(Product).where(Product.approval_status == ApprovalStatus.PENDING)
        
        if tenant_id:
            query = query.where(or_(Product.tenant_id == tenant_id, Product.tenant_id.is_(None)))

        query = query.order_by(Product.created_at.asc()).offset(skip).limit(limit)
        
        # Load relationships
        query = query.options(
            joinedload(Product.category),
            joinedload(Product.vendor),
        )

        result = await self.db_session.execute(query)
        return result.scalars().all()

    async def get_products_by_approval_status(
        self,
        approval_status: ApprovalStatus,
        tenant_id: Optional[uuid.UUID] = None,
        skip: int = 0,
        limit: int = 100
    ) -> Sequence[Product]:
        """Gets products by approval status."""
        query = select(Product).where(Product.approval_status == approval_status)
        
        if tenant_id:
            query = query.where(or_(Product.tenant_id == tenant_id, Product.tenant_id.is_(None)))

        query = query.order_by(Product.created_at.desc()).offset(skip).limit(limit)
        
        # Load relationships
        query = query.options(
            joinedload(Product.category),
            joinedload(Product.vendor),
        )

        result = await self.db_session.execute(query)
        return result.scalars().all()
