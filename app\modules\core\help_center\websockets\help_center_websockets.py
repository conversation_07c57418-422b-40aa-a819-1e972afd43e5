"""
Help Center WebSocket Manager

Gerenciador de WebSocket para comunicação em tempo real do help center.
"""

import json
import logging
from datetime import datetime
from typing import Dict, Any, List, Set
from uuid import UUID

import socketio
from app.core.socketio_manager import sio_server as sio

logger = logging.getLogger(__name__)


class HelpCenterWebSocketManager:
    """WebSocket manager for help center real-time communication."""

    def __init__(self):
        self.namespace = "/help-center"
        
        # Salas ativas: ticket_id -> set of user_ids
        self.ticket_rooms: Dict[str, Set[str]] = {}
        
        # Usuários conectados: user_id -> connection_info
        self.connected_users: Dict[str, Dict[str, Any]] = {}
        
        # Admins conectados para notificações globais
        self.connected_admins: Set[str] = set()

    async def emit_ticket_created(self, ticket_data: Dict[str, Any]):
        """
        Emite evento quando um novo ticket é criado.
        
        Args:
            ticket_data: Dados do ticket criado
        """
        # Notifica todos os admins conectados
        await sio.emit(
            "ticket_created",
            {
                "type": "ticket_created",
                "ticket": ticket_data,
                "timestamp": datetime.utcnow().isoformat()
            },
            room="admin_help_center",
            namespace=self.namespace
        )
        
        logger.info(f"Evento ticket_created emitido para ticket {ticket_data.get('id')}")

    async def emit_message_sent(
        self, 
        ticket_id: UUID, 
        message_data: Dict[str, Any],
        sender_id: UUID
    ):
        """
        Emite evento quando uma mensagem é enviada.
        
        Args:
            ticket_id: ID do ticket
            message_data: Dados da mensagem
            sender_id: ID do remetente
        """
        ticket_room = f"ticket_{ticket_id}"
        
        await sio.emit(
            "message_sent",
            {
                "type": "message_sent",
                "ticket_id": str(ticket_id),
                "message": message_data,
                "sender_id": str(sender_id),
                "timestamp": datetime.utcnow().isoformat()
            },
            room=ticket_room,
            namespace=self.namespace
        )
        
        logger.info(f"Evento message_sent emitido para ticket {ticket_id}")

    async def emit_message_read(
        self, 
        ticket_id: UUID, 
        message_ids: List[UUID],
        reader_id: UUID
    ):
        """
        Emite evento quando mensagens são marcadas como lidas.
        
        Args:
            ticket_id: ID do ticket
            message_ids: IDs das mensagens lidas
            reader_id: ID de quem leu
        """
        ticket_room = f"ticket_{ticket_id}"
        
        await sio.emit(
            "messages_read",
            {
                "type": "messages_read",
                "ticket_id": str(ticket_id),
                "message_ids": [str(mid) for mid in message_ids],
                "reader_id": str(reader_id),
                "timestamp": datetime.utcnow().isoformat()
            },
            room=ticket_room,
            namespace=self.namespace
        )
        
        logger.info(f"Evento messages_read emitido para ticket {ticket_id}")

    async def emit_ticket_status_changed(
        self, 
        ticket_id: UUID, 
        old_status: str,
        new_status: str,
        changed_by: UUID
    ):
        """
        Emite evento quando status do ticket muda.
        
        Args:
            ticket_id: ID do ticket
            old_status: Status anterior
            new_status: Novo status
            changed_by: ID de quem alterou
        """
        ticket_room = f"ticket_{ticket_id}"
        
        await sio.emit(
            "ticket_status_changed",
            {
                "type": "ticket_status_changed",
                "ticket_id": str(ticket_id),
                "old_status": old_status,
                "new_status": new_status,
                "changed_by": str(changed_by),
                "timestamp": datetime.utcnow().isoformat()
            },
            room=ticket_room,
            namespace=self.namespace
        )
        
        logger.info(f"Evento ticket_status_changed emitido para ticket {ticket_id}")

    async def emit_ticket_assigned(
        self, 
        ticket_id: UUID, 
        admin_id: UUID,
        assigned_by: UUID
    ):
        """
        Emite evento quando ticket é atribuído a um admin.
        
        Args:
            ticket_id: ID do ticket
            admin_id: ID do admin atribuído
            assigned_by: ID de quem fez a atribuição
        """
        ticket_room = f"ticket_{ticket_id}"
        
        await sio.emit(
            "ticket_assigned",
            {
                "type": "ticket_assigned",
                "ticket_id": str(ticket_id),
                "admin_id": str(admin_id),
                "assigned_by": str(assigned_by),
                "timestamp": datetime.utcnow().isoformat()
            },
            room=ticket_room,
            namespace=self.namespace
        )
        
        # Também notifica o admin atribuído se estiver conectado
        admin_room = f"user_{admin_id}"
        await sio.emit(
            "ticket_assigned_to_you",
            {
                "type": "ticket_assigned_to_you",
                "ticket_id": str(ticket_id),
                "assigned_by": str(assigned_by),
                "timestamp": datetime.utcnow().isoformat()
            },
            room=admin_room,
            namespace=self.namespace
        )
        
        logger.info(f"Evento ticket_assigned emitido para ticket {ticket_id}")

    async def emit_typing_indicator(
        self, 
        ticket_id: UUID, 
        user_id: UUID,
        is_typing: bool
    ):
        """
        Emite indicador de digitação.
        
        Args:
            ticket_id: ID do ticket
            user_id: ID do usuário digitando
            is_typing: Se está digitando ou parou
        """
        ticket_room = f"ticket_{ticket_id}"
        
        await sio.emit(
            "typing_indicator",
            {
                "type": "typing_indicator",
                "ticket_id": str(ticket_id),
                "user_id": str(user_id),
                "is_typing": is_typing,
                "timestamp": datetime.utcnow().isoformat()
            },
            room=ticket_room,
            namespace=self.namespace
        )

    async def join_ticket_room(self, sid: str, ticket_id: UUID, user_id: UUID):
        """
        Adiciona usuário à sala do ticket.
        
        Args:
            sid: Session ID do WebSocket
            ticket_id: ID do ticket
            user_id: ID do usuário
        """
        ticket_room = f"ticket_{ticket_id}"
        await sio.enter_room(sid, ticket_room, namespace=self.namespace)
        
        # Registra na estrutura de dados
        ticket_key = str(ticket_id)
        if ticket_key not in self.ticket_rooms:
            self.ticket_rooms[ticket_key] = set()
        self.ticket_rooms[ticket_key].add(str(user_id))
        
        logger.info(f"Usuário {user_id} entrou na sala do ticket {ticket_id}")

    async def leave_ticket_room(self, sid: str, ticket_id: UUID, user_id: UUID):
        """
        Remove usuário da sala do ticket.
        
        Args:
            sid: Session ID do WebSocket
            ticket_id: ID do ticket
            user_id: ID do usuário
        """
        ticket_room = f"ticket_{ticket_id}"
        await sio.leave_room(sid, ticket_room, namespace=self.namespace)
        
        # Remove da estrutura de dados
        ticket_key = str(ticket_id)
        if ticket_key in self.ticket_rooms:
            self.ticket_rooms[ticket_key].discard(str(user_id))
            if not self.ticket_rooms[ticket_key]:
                del self.ticket_rooms[ticket_key]
        
        logger.info(f"Usuário {user_id} saiu da sala do ticket {ticket_id}")

    async def join_admin_room(self, sid: str, admin_id: UUID):
        """
        Adiciona admin à sala de notificações globais.
        
        Args:
            sid: Session ID do WebSocket
            admin_id: ID do admin
        """
        await sio.enter_room(sid, "admin_help_center", namespace=self.namespace)
        await sio.enter_room(sid, f"user_{admin_id}", namespace=self.namespace)
        
        self.connected_admins.add(str(admin_id))
        
        logger.info(f"Admin {admin_id} entrou na sala de notificações globais")

    async def leave_admin_room(self, sid: str, admin_id: UUID):
        """
        Remove admin da sala de notificações globais.
        
        Args:
            sid: Session ID do WebSocket
            admin_id: ID do admin
        """
        await sio.leave_room(sid, "admin_help_center", namespace=self.namespace)
        await sio.leave_room(sid, f"user_{admin_id}", namespace=self.namespace)
        
        self.connected_admins.discard(str(admin_id))
        
        logger.info(f"Admin {admin_id} saiu da sala de notificações globais")

    async def join_user_room(self, sid: str, user_id: UUID):
        """
        Adiciona usuário à sua sala pessoal.
        
        Args:
            sid: Session ID do WebSocket
            user_id: ID do usuário
        """
        user_room = f"user_{user_id}"
        await sio.enter_room(sid, user_room, namespace=self.namespace)
        
        self.connected_users[str(user_id)] = {
            "sid": sid,
            "connected_at": datetime.utcnow().isoformat()
        }
        
        logger.info(f"Usuário {user_id} entrou na sua sala pessoal")

    async def leave_user_room(self, sid: str, user_id: UUID):
        """
        Remove usuário da sua sala pessoal.
        
        Args:
            sid: Session ID do WebSocket
            user_id: ID do usuário
        """
        user_room = f"user_{user_id}"
        await sio.leave_room(sid, user_room, namespace=self.namespace)
        
        self.connected_users.pop(str(user_id), None)
        
        logger.info(f"Usuário {user_id} saiu da sua sala pessoal")


# Global instance
help_center_ws_manager = HelpCenterWebSocketManager()


def register_help_center_handlers(sio_server):
    """
    Registra todos os handlers de WebSocket do Help Center.

    Args:
        sio_server: Instância do servidor Socket.IO
    """

    @sio_server.event(namespace="/help-center")
    async def connect(sid, environ, auth):
        """Handle client connection."""
        logger.info(f"Help Center WebSocket client connected: {sid}")

        # Verificar autenticação se necessário
        if auth:
            user_id = auth.get("user_id")
            is_admin = auth.get("is_admin", False)

            if user_id:
                # Adicionar à sala pessoal
                await help_center_ws_manager.join_user_room(sid, UUID(user_id))

                # Se for admin, adicionar à sala de admins
                if is_admin:
                    await help_center_ws_manager.join_admin_room(sid, UUID(user_id))

                # Confirmar conexão
                await sio_server.emit(
                    "connected",
                    {
                        "message": "Conectado ao Help Center",
                        "user_id": user_id,
                        "is_admin": is_admin,
                        "timestamp": datetime.utcnow().isoformat()
                    },
                    room=sid,
                    namespace="/help-center"
                )

    @sio_server.event(namespace="/help-center")
    async def disconnect(sid):
        """Handle client disconnection."""
        logger.info(f"Help Center WebSocket client disconnected: {sid}")

        # Cleanup será feito automaticamente pelo Socket.IO
        # mas podemos adicionar lógica adicional se necessário

    @sio_server.event(namespace="/help-center")
    async def join_ticket(sid, data):
        """Handle joining ticket room."""
        try:
            ticket_id = UUID(data.get("ticket_id"))
            user_id = UUID(data.get("user_id"))

            await help_center_ws_manager.join_ticket_room(sid, ticket_id, user_id)

            await sio_server.emit(
                "joined_ticket",
                {
                    "ticket_id": str(ticket_id),
                    "message": "Entrou na conversa do ticket"
                },
                room=sid,
                namespace="/help-center"
            )

        except Exception as e:
            logger.error(f"Erro ao entrar na sala do ticket: {e}")
            await sio_server.emit(
                "error",
                {"message": "Erro ao entrar na conversa"},
                room=sid,
                namespace="/help-center"
            )

    @sio_server.event(namespace="/help-center")
    async def leave_ticket(sid, data):
        """Handle leaving ticket room."""
        try:
            ticket_id = UUID(data.get("ticket_id"))
            user_id = UUID(data.get("user_id"))

            await help_center_ws_manager.leave_ticket_room(sid, ticket_id, user_id)

            await sio_server.emit(
                "left_ticket",
                {
                    "ticket_id": str(ticket_id),
                    "message": "Saiu da conversa do ticket"
                },
                room=sid,
                namespace="/help-center"
            )

        except Exception as e:
            logger.error(f"Erro ao sair da sala do ticket: {e}")

    @sio_server.event(namespace="/help-center")
    async def typing(sid, data):
        """Handle typing indicator."""
        try:
            ticket_id = UUID(data.get("ticket_id"))
            user_id = UUID(data.get("user_id"))
            is_typing = data.get("is_typing", True)

            await help_center_ws_manager.emit_typing_indicator(
                ticket_id, user_id, is_typing
            )

        except Exception as e:
            logger.error(f"Erro ao processar indicador de digitação: {e}")

    @sio_server.event(namespace="/help-center")
    async def ping(sid, data):
        """Handle ping for connection testing."""
        await sio_server.emit(
            "pong",
            {"timestamp": datetime.utcnow().isoformat()},
            room=sid,
            namespace="/help-center"
        )

    logger.info("Help Center WebSocket handlers registered successfully")
