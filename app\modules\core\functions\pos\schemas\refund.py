import uuid
from typing import Optional
from decimal import Decimal
from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field

# --- Refund Schemas ---


# Base schema for refunds
class RefundBase(BaseModel):
    amount: Decimal = Field(..., max_digits=10, decimal_places=2, gt=0)
    reason: str = Field(..., min_length=3, max_length=500)


# Schema para criação de um estorno


class RefundCreate(RefundBase):
    """Schema for creating a refund."""

    pass


# Schema para atualização de um estorno (se necessário)


class RefundUpdate(BaseModel):
    reason: Optional[str] = Field(None, min_length=3, max_length=500)


# Schema base para estornos armazenados no banco


class RefundInDBBase(BaseModel):
    id: uuid.UUID
    transaction_id: uuid.UUID
    tenant_id: uuid.UUID
    amount: Decimal
    reason: str
    created_at: datetime
    created_by: Optional[uuid.UUID] = None

    model_config = ConfigDict(from_attributes=True)


# Schema para leitura de estornos (resposta da API)


class RefundRead(RefundInDBBase):
    pass
