from pydantic import <PERSON><PERSON>odel, constr, ConfigDict  # Adicionado ConfigDict
from typing import Optional
from uuid import UUID
from datetime import datetime

from app.modules.custom_domains.models import (
    VerificationStatus,
    ProxyConfigStatus,
    SslStatus,
)  # Importar os Enums

# from app.modules.custom_domains.models import FrontendType # Se for usar Enum para frontend_type


class CustomDomainBase(BaseModel):
    domain_name: constr(min_length=3, max_length=255)
    # frontend_type: FrontendType # Usar Enum real em fases futuras
    frontend_type: str  # Ex: 'ONLINE_STORE', 'DIGITAL_MENU'


class CustomDomainCreate(CustomDomainBase):
    domain_registration_id: Optional[UUID] = None
    use_existing_domain: bool = False


class CustomDomainUpdate(BaseModel):
    # Quais campos podem ser atualizados? Para o MVP, talvez nenhum diretamente pelo tenant após criação.  # noqa: E501
    # O verification_status e is_active seriam gerenciados internamente.
    # frontend_type: Optional[str] = None
    pass


class CustomDomainInDBBase(CustomDomainBase):
    id: UUID
    tenant_id: UUID
    domain_registration_id: Optional[UUID] = None  # Add this field
    cname_target: str  # Renomeado de trix_target_domain
    verification_status: VerificationStatus  # Renomeado de status e usando Enum
    is_active: bool
    proxy_config_status: ProxyConfigStatus  # Adicionado novo campo

    dns_validation_attempts: Optional[int] = None
    last_dns_validation_at: Optional[datetime] = None
    ssl_certificate_id: Optional[str] = None  # ID do certificado (ex: ID interno ou da CA)
    ssl_issued_at: Optional[datetime] = None
    ssl_expires_at: Optional[datetime] = None
    ssl_status: SslStatus  # Adicionado novo campo
    last_ssl_check_at: Optional[datetime] = None  # Adicionado novo campo
    ssl_error_details: Optional[str] = None  # Adicionado novo campo

    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Schema para retornar ao cliente


class CustomDomain(CustomDomainInDBBase):
    pass
