from typing import List, Optional
import uuid
from fastapi import HTT<PERSON>Ex<PERSON>, status
from sqlalchemy import select, delete
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from app.modules.shared.hr.core.models.employee import Employee, EmploymentStatus
from app.modules.shared.hr.core.schemas.employee import (
    EmployeeCreate,
    EmployeeUpdate,
    EmployeeRead,
    EmployeeWithUser,
)
from app.models.tenant_user_association import TenantUserAssociation  # noqa: E402
from app.core.exceptions import NotFoundError


class EmployeeService:
    """Service for employee operations."""

    @staticmethod
    async def create_employee(
        db: AsyncSession, tenant_id: uuid.UUID, employee_data: Employee<PERSON><PERSON>
    ) -> Employee:
        """Create a new employee."""
        # Verify that the tenant user association exists
        stmt = select(TenantUserAssociation).where(
            TenantUserAssociation.id == employee_data.user_tenant_association_id,
            TenantUserAssociation.tenant_id == tenant_id,
        )
        association = await db.execute(stmt)
        association = association.scalars().first()

        if not association:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant user association not found",
            )

        # Create employee
        employee = Employee(tenant_id=tenant_id, **employee_data.model_dump(exclude={"metadata"}))

        # Add metadata if provided
        if employee_data.metadata:
            employee.metadata = employee_data.metadata

        db.add(employee)
        await db.commit()
        await db.refresh(employee)
        return employee

    @staticmethod
    async def get_employee(
        db: AsyncSession, tenant_id: uuid.UUID, employee_id: uuid.UUID
    ) -> Employee:
        """Get an employee by ID."""
        stmt = select(Employee).where(Employee.id == employee_id, Employee.tenant_id == tenant_id)
        result = await db.execute(stmt)
        employee = result.scalars().first()

        if not employee:
            raise NotFoundError(f"Employee with id {employee_id} not found")

        return employee

    @staticmethod
    async def get_employee_with_user(
        db: AsyncSession, tenant_id: uuid.UUID, employee_id: uuid.UUID
    ) -> EmployeeWithUser:
        """Get an employee with user data."""
        stmt = (
            select(Employee)
            .options(joinedload(Employee.user_association).joinedload(TenantUserAssociation.user))
            .where(Employee.id == employee_id, Employee.tenant_id == tenant_id)
        )
        result = await db.execute(stmt)
        employee = result.scalars().first()

        if not employee:
            raise NotFoundError(f"Employee with id {employee_id} not found")

        # Convert to EmployeeWithUser schema
        user = employee.user_association.user
        employee_data = EmployeeRead.model_validate(employee)

        return EmployeeWithUser(
            **employee_data.model_dump(),
            user_id=user.id,
            user_full_name=f"{user.first_name} {user.last_name}",
            user_email=user.email,
        )

    @staticmethod
    async def get_employees(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        status: Optional[EmploymentStatus] = None,
        department: Optional[str] = None,
    ) -> List[Employee]:
        """Get all employees with optional filtering."""
        query = select(Employee).where(Employee.tenant_id == tenant_id)

        # Apply filters if provided
        if status:
            query = query.where(Employee.employment_status == status)

        if department:
            query = query.where(Employee.department == department)

        # Apply pagination
        query = query.offset(skip).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def update_employee(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        employee_id: uuid.UUID,
        employee_data: EmployeeUpdate,
    ) -> Employee:
        """Update an employee."""
        # Get the employee first to ensure it exists
        employee = await EmployeeService.get_employee(db, tenant_id, employee_id)

        # Update attributes
        update_data = employee_data.model_dump(exclude_unset=True)

        # Handle metadata update separately (merge instead of replace)
        metadata_update = update_data.pop("metadata", None)
        if metadata_update:
            employee.metadata = {**employee.metadata, **metadata_update}

        # Update the other fields
        for key, value in update_data.items():
            setattr(employee, key, value)

        await db.commit()
        await db.refresh(employee)
        return employee

    @staticmethod
    async def delete_employee(
        db: AsyncSession, tenant_id: uuid.UUID, employee_id: uuid.UUID
    ) -> bool:
        """Delete an employee."""
        stmt = (
            delete(Employee)
            .where(Employee.id == employee_id, Employee.tenant_id == tenant_id)
            .returning(Employee.id)
        )

        result = await db.execute(stmt)
        deleted_id = result.scalars().first()

        if not deleted_id:
            raise NotFoundError(f"Employee with id {employee_id} not found")

        await db.commit()
        return True

    @staticmethod
    async def get_by_user_association(
        db: AsyncSession, tenant_id: uuid.UUID, user_tenant_association_id: uuid.UUID
    ) -> Optional[Employee]:
        """Get an employee by user association ID."""
        stmt = select(Employee).where(
            Employee.user_tenant_association_id == user_tenant_association_id,
            Employee.tenant_id == tenant_id,
        )
        result = await db.execute(stmt)
        return result.scalars().first()

    @staticmethod
    async def get_employee_by_user_id(
        db: AsyncSession, tenant_id: uuid.UUID, user_id: uuid.UUID
    ) -> Optional[Employee]:
        """Get an employee by user ID."""
        # First get the association
        stmt = select(TenantUserAssociation.id).where(
            TenantUserAssociation.user_id == user_id,
            TenantUserAssociation.tenant_id == tenant_id,
        )
        result = await db.execute(stmt)
        association_id = result.scalars().first()

        if not association_id:
            return None

        # Then get the employee
        return await EmployeeService.get_by_user_association(db, tenant_id, association_id)


# Create a singleton instance
employee_service = EmployeeService()
