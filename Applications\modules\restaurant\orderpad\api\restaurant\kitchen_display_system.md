# Restaurant - Kitchen Display System

**Categoria:** Restaurant
**Módulo:** Kitchen Display System
**Total de Endpoints:** 4
**Gerado em:** 08/06/2025, 17:26:01

## 📋 Endpoints

- [GET /api/modules/restaurants/kds/orders/](#get-apimodulesrestaurantskdsorders) - Read Kitchen Orders
- [POST /api/modules/restaurants/kds/orders/](#post-apimodulesrestaurantskdsorders) - Create Kitchen Order
- [GET /api/modules/restaurants/kds/orders/{order_id}](#get-apimodulesrestaurantskdsordersorder-id) - Read Kitchen Order
- [PATCH /api/modules/restaurants/kds/orders/{order_id}/status](#patch-apimodulesrestaurantskdsordersorder-idstatus) - Update Kitchen Order Status

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### KitchenOrderCreate

**Descrição:** Schema para criar um novo KitchenOrder. tenant_id será adicionado no serviço.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `order_details` | object | ✅ | Detalhes do pedido (itens, quantidades, notas, etc.) |
| `status` | string | ❌ | Status atual do pedido (pending, preparing, ready, served) |
| `source_sale_id` | unknown | ❌ | ID opcional da transação de venda de origem (POS) |
| `source_online_order_id` | unknown | ❌ | ID opcional do pedido online de origem |
| `source_order_id` | unknown | ❌ | ID opcional do pedido compartilhado de origem |
| `creator_user_id` | unknown | ❌ | ID opcional do usuário que criou o pedido (ex: usuário do pedido online) |

### KitchenOrderRead

**Descrição:** Schema para retornar dados de KitchenOrder na API.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `order_details` | object | ✅ | Detalhes do pedido (itens, quantidades, notas, etc.) |
| `status` | string | ❌ | Status atual do pedido (pending, preparing, ready, served) |
| `source_sale_id` | unknown | ❌ | ID opcional da transação de venda de origem (POS) |
| `source_online_order_id` | unknown | ❌ | ID opcional do pedido online de origem |
| `source_order_id` | unknown | ❌ | ID opcional do pedido compartilhado de origem |
| `creator_user_id` | unknown | ❌ | ID opcional do usuário que criou o pedido (ex: usuário do pedido online) |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### KitchenOrderUpdate

**Descrição:** Schema para atualizar um KitchenOrder existente. Permite atualizar status e detalhes.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `status` | unknown | ❌ | Novo status do pedido (pending, preparing, ready, served) |
| `order_details` | unknown | ❌ | Detalhes atualizados do pedido (itens, notas, etc.) |

## 🔗 Endpoints Detalhados

### GET /api/modules/restaurants/kds/orders/ {#get-apimodulesrestaurantskdsorders}

**Resumo:** Read Kitchen Orders
**Descrição:** Recupera todos os pedidos da cozinha para o tenant atual, opcionalmente filtrados por status.
Requer papel de OWNER, MANAGER ou STAFF no tenant (customers não têm acesso).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `status` | string | query | ❌ | Filtrar por status (pending, preparing, ready, served) |
| `skip` | integer | query | ❌ | Número de registros para pular |
| `limit` | integer | query | ❌ | Número máximo de registros para retornar |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/kds/orders/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/restaurants/kds/orders/ {#post-apimodulesrestaurantskdsorders}

**Resumo:** Create Kitchen Order
**Descrição:** Cria um novo pedido na cozinha (KDS).
Requer papel de OWNER ou MANAGER no tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [KitchenOrderCreate](#kitchenordercreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [KitchenOrderRead](#kitchenorderread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/restaurants/kds/orders/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/restaurants/kds/orders/{order_id} {#get-apimodulesrestaurantskdsordersorder-id}

**Resumo:** Read Kitchen Order
**Descrição:** Recupera um pedido específico da cozinha pelo ID.
Requer papel de OWNER, MANAGER ou STAFF no tenant (customers não têm acesso).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `order_id` | string | path | ✅ | ID do pedido a ser recuperado |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [KitchenOrderRead](#kitchenorderread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/kds/orders/{order_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PATCH /api/modules/restaurants/kds/orders/{order_id}/status {#patch-apimodulesrestaurantskdsordersorder-idstatus}

**Resumo:** Update Kitchen Order Status
**Descrição:** Atualiza o status e/ou detalhes de um pedido da cozinha.
Requer papel de OWNER ou MANAGER no tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `order_id` | string | path | ✅ | ID do pedido a ser atualizado |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [KitchenOrderUpdate](#kitchenorderupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [KitchenOrderRead](#kitchenorderread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PATCH "http://localhost:8000/api/modules/restaurants/kds/orders/{order_id}/status" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
