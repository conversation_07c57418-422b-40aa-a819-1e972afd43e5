'use client';

import { useState, useMemo, useEffect } from 'react';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { Cog6ToothIcon, PlusIcon } from '@heroicons/react/24/outline';
import { useMenuContext } from '@/contexts/MenuContext';
import { useMenuPermissions } from '@/hooks/useMenuPermissions';
import { ItemModal } from './ItemModal';
import { CategoryModal } from './CategoryModal';
import { MenuSelector } from './MenuSelector';
import { useMenuEditorActions } from './MenuEditorActions';
import { useMenuFilters } from '@/hooks/useMenuFilters';
import { MenuEditorLayout } from './MenuEditorLayout';
import { menuCategoryService } from '@/services/api/MenuCategoryService';
import { MenuLoadingStates, LoadingOverlay } from './MenuLoadingStates';
import { PermissionFallback } from './PermissionFallback';
import { ReadOnlyMenuView } from './ReadOnlyMenuView';

export function MenuEditor() {
  const {
    digitalMenus,
    selectedMenu,
    categories,
    items,
    loading,
    error,
    selectDigitalMenu,
    createCategory,
    updateCategory,
    deleteCategory,
    createItem,
    updateItem,
    deleteItem,
    reorderCategories,
    reorderItems,
  } = useMenuContext();

  // Permissions hook
  const {
    canViewMenu,
    canViewDigitalMenus,
    canEditMenu,
    canCreateMenu,
    canDeleteMenu,
    canManageCategories,
    canManageItems,
    getAccessLevel,
    currentTenant,
  } = useMenuPermissions();

  // UI State
  const [showItemModal, setShowItemModal] = useState(false);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [editingCategory, setEditingCategory] = useState<any>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<any>(null);
  const [filterBy, setFilterBy] = useState<'category' | 'items'>('items');
  const [selectedSubcategoryId, setSelectedSubcategoryId] =
    useState<string | null>(null);
  const [selectedCategoryForView, setSelectedCategoryForViewState] = useState<string>('');

  // Advanced Filter State
  const [showFilters, setShowFilters] = useState(false);
  const [availabilityFilter, setAvailabilityFilter] = useState<'all' | 'available' | 'unavailable'>('all');
  const [typeFilter, setTypeFilter] = useState<'all' | 'combo' | 'individual'>('all');

  // Cache and Performance
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // Drag & Drop State
  const [activeId, setActiveId] = useState<string | null>(null);
  const [dragType, setDragType] = useState<'category' | 'item' | null>(null);

  // Use optimized filters hook
  const {
    filteredItems,
    filteredCategories,
    categoriesWithChildren,
    finalFilteredItems,
    selectedCategoryWithChildren,
  } = useMenuFilters({
    categories,
    items,
    selectedCategory,
    selectedSubcategoryId,
    searchQuery,
    filterBy,
    availabilityFilter,
    typeFilter,
  });

  // Note: All filter logic now consolidated in useMenuFilters hook

  // Clear selected category when menu changes (but not on initial load)
  const [previousMenuId, setPreviousMenuId] = useState<string | null>(null);

  useEffect(() => {
    if (selectedMenu?.id) {
      // Only clear if we're switching from one menu to another (not initial load)
      if (previousMenuId && previousMenuId !== selectedMenu.id) {
        setSelectedCategory(null);
        setSelectedSubcategoryId('');
        setSelectedCategoryForViewState(''); // CRITICAL: Clear visual state too
      }
      setPreviousMenuId(selectedMenu.id);
    }
  }, [selectedMenu?.id, previousMenuId]);

  // Sync selectedCategoryForView with selectedCategory
  useEffect(() => {
    setSelectedCategoryForViewState(selectedCategory?.id || '');
  }, [selectedCategory?.id]);

  // Initial load tracking
  useEffect(() => {
    if (selectedMenu && categories.length > 0 && items.length > 0 && isInitialLoad) {
      setIsInitialLoad(false);
    }
  }, [selectedMenu, categories.length, items.length, isInitialLoad]);

  // Use actions hook
  const {
    handleDragStart,
    handleDragEnd,
    handleCreateItem,
    handleEditItem,
    handleSaveItem,
    handleDeleteItem,
    handleCreateCategory,
    handleCreateSubcategory,
    handleCategorySelect,
    handleSubcategorySelect,
    handleEditCategory,
    handleSaveCategory,
    handleDeleteCategory,
    savingItem,
    savingCategory,
  } = useMenuEditorActions({
    displayCategories: filteredCategories,
    displayItems: items,
    categories,
    setActiveId,
    setDragType,
    setShowItemModal,
    setShowCategoryModal,
    setEditingItem,
    setEditingCategory,
    setSelectedCategoryForView: (id: string) => {
      setSelectedCategoryForViewState(id);
      const category = categories.find(c => c.id === id);
      setSelectedCategory(category || null);
    },
    setSelectedSubcategoryId,
    selectedCategoryId: selectedCategoryForView,
    reorderCategories,
    reorderItems,
    updateItem,
    createItem,
    deleteItem,
    createCategory,
    updateCategory,
    deleteCategory,
  });

  // Auto-select first non-default category when categories are loaded
  useEffect(() => {
    // Only auto-select if we have categories, a selected menu, and no category is currently selected
    if (selectedMenu && categories.length > 0 && !selectedCategory && !loading) {
      // Find first non-default category, fallback to first category if all are default
      const firstNonDefaultCategory = categories.find(cat =>
        !menuCategoryService.isDefaultCategory(cat)
      );

      const categoryToSelect = firstNonDefaultCategory || categories[0];

      if (categoryToSelect) {
        setSelectedCategory(categoryToSelect);
      }
    }
  }, [selectedMenu, categories, selectedCategory, loading]);

  // Force category selection after data is loaded (fallback)
  useEffect(() => {
    if (selectedMenu && categories.length > 0 && !selectedCategory && !loading) {
      // Small delay to ensure all state updates are complete
      const timer = setTimeout(() => {
        if (!selectedCategory && categories.length > 0) {
          const firstNonDefaultCategory = categories.find(cat =>
            !menuCategoryService.isDefaultCategory(cat)
          );
          const categoryToSelect = firstNonDefaultCategory || categories[0];

          if (categoryToSelect) {
            setSelectedCategory(categoryToSelect);
          }
        }
      }, 200);

      return () => clearTimeout(timer);
    }
  }, [selectedMenu, categories, selectedCategory, loading]);

  // Handle toggle category active/inactive
  const handleToggleCategoryActive = async (categoryId: string, isActive: boolean) => {
    try {
      const category = categories.find(cat => cat.id === categoryId);
      if (!category) return;

      await updateCategory(categoryId, {
        ...category,
        is_active: isActive
      });
    } catch (error) {
      console.error('Error toggling category active state:', error);
    }
  };

  // Handle toggle item active/inactive
  const handleToggleItemActive = async (itemId: string, isActive: boolean) => {
    try {
      const item = items.find(it => it.id === itemId);
      if (!item) {
        console.error('Item not found:', itemId);
        return;
      }

      await updateItem(itemId, {
        ...item,
        base_price: typeof item.base_price === 'string' ? parseFloat(item.base_price) : item.base_price,
        is_active: isActive
      });
    } catch (error) {
      console.error('Error toggling item active state:', error);
    }
  };

  // Handle subcategory actions
  const handleEditSubcategory = (subcategory: any) => {
    handleEditCategory(subcategory);
  };

  const handleDeleteSubcategory = (subcategoryId: string) => {
    handleDeleteCategory(subcategoryId);
  };

  const handleToggleSubcategoryActive = async (subcategoryId: string, isActive: boolean) => {
    await handleToggleCategoryActive(subcategoryId, isActive);
  };

  // Use real data only
  const displayMenu = selectedMenu;

  // Show loading state when switching menus to prevent showing wrong data
  const isMenuSwitching = useMemo(() => {
    // If we're loading and have a selected menu, we might be switching
    return loading && selectedMenu && (categories.length === 0 || items.length === 0);
  }, [loading, selectedMenu, categories.length, items.length]);

  // Don't render content if menu is switching (prevents flash of wrong data)
  if (isMenuSwitching) {
    return (
      <div className="min-h-[calc(100vh-200px)] flex flex-col
        bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20
        rounded-xl overflow-hidden"
      >
        <MenuLoadingStates
          type="menu"
          message="Carregando dados do menu..."
          className="flex-1"
        />
      </div>
    );
  }

  // Show loading state for initial load
  if (loading && isInitialLoad) {
    return (
      <div className="min-h-[calc(100vh-200px)] flex flex-col
        bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20
        rounded-xl overflow-hidden"
      >
        <MenuLoadingStates
          type="menu"
          message="Carregando editor de menu..."
          className="flex-1"
        />
      </div>
    );
  }

  // Permission checks - render appropriate fallback components
  if (!currentTenant) {
    return (
      <PermissionFallback
        accessLevel={getAccessLevel}
        currentTenant={currentTenant}
        type="no-tenant"
      />
    );
  }

  if (!canViewMenu) {
    return (
      <PermissionFallback
        accessLevel={getAccessLevel}
        currentTenant={currentTenant}
        type="no-access"
      />
    );
  }

  // Customer view - read-only interface
  if (getAccessLevel === 'customer') {
    return (
      <ReadOnlyMenuView
        displayMenu={displayMenu}
        categories={categories}
        items={items}
        currentTenant={currentTenant}
      />
    );
  }

  // Check if user can view digital menus (staff/owner level required)
  if (!canViewDigitalMenus) {
    return (
      <PermissionFallback
        accessLevel={getAccessLevel}
        currentTenant={currentTenant}
        type="customer-view"
      />
    );
  }

  // Debug logging removed to prevent excessive re-renders

  // Loading state
  if (loading && (!Array.isArray(digitalMenus) || digitalMenus.length === 0)) {
    return (
      <div className="flex flex-col items-center justify-center h-96 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
        <p className="text-gray-500">Carregando menus digitais...</p>
      </div>
    );
  }

  // No menus found state - show only create button
  if (Array.isArray(digitalMenus) && digitalMenus.length === 0 && !loading) {
    return (
      <div className="flex flex-col items-center justify-center h-96 text-center">
        <div className="mb-4">
          <Cog6ToothIcon className="h-16 w-16 text-gray-400 mx-auto" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Nenhum Menu Digital Encontrado
        </h3>
        <p className="text-gray-500 mb-6">
          {canCreateMenu
            ? 'Crie seu primeiro menu digital para começar a gerenciar categorias e itens.'
            : 'Não há menus digitais disponíveis no momento.'
          }
        </p>

        {/* Show only create button when no menus exist */}
        {canCreateMenu && (
          <button
            onClick={() => {
              // Trigger create menu modal from MenuSelector
              const createButton = document.querySelector('[data-create-menu]') as HTMLButtonElement;
              if (createButton) {
                createButton.click();
              }
            }}
            className="btn-primary flex items-center space-x-2"
          >
            <PlusIcon className="h-5 w-5" />
            <span>Criar Primeiro Menu</span>
          </button>
        )}

        {error && (
          <div className="mt-4 p-4 bg-red-50 rounded-lg border border-red-200">
            <p className="text-sm text-red-800">
              <strong>Erro:</strong> {error}
            </p>
          </div>
        )}

        {/* Hidden MenuSelector for modal functionality */}
        <div className="hidden">
          <MenuSelector />
        </div>
      </div>
    );
  }

  // Menus exist but none selected (this should not happen with auto-selection, but just in case)
  if (Array.isArray(digitalMenus) && digitalMenus.length > 0 && !selectedMenu && !loading) {
    return (
      <div className="flex flex-col items-center justify-center h-96 text-center">
        <div className="mb-4">
          <Cog6ToothIcon className="h-16 w-16 text-orange-400 mx-auto" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Aguardando Seleção de Menu
        </h3>
        <p className="text-gray-500 mb-6">
          Um menu será selecionado automaticamente em instantes...
        </p>
        <div className="animate-pulse bg-gray-200 h-10 w-64 rounded-lg"></div>
      </div>
    );
  }

  return (
    <DndContext
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="min-h-[calc(100vh-200px)] flex flex-col
        bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20
        rounded-xl"
      >
        
        {/* Error Display */}
        {error && (
          <div className="mx-6 mt-4 bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        <MenuEditorLayout
          displayMenu={displayMenu}
          displayCategories={filteredCategories}
          selectedCategory={selectedCategoryWithChildren}
          filteredItems={finalFilteredItems}
          allCategoryItems={filteredItems}
          items={items}
          categories={categories}
          viewMode={viewMode}
          searchTerm={searchQuery}
          selectedCategoryForView={selectedCategoryForView}
          selectedSubcategoryId={selectedSubcategoryId}
          activeId={activeId}
          dragType={dragType}
          savingItem={savingItem}
          handleCreateCategory={canManageCategories ? handleCreateCategory : undefined}
          handleCreateItem={canManageItems ? handleCreateItem : undefined}
          handleEditCategory={canManageCategories ? handleEditCategory : undefined}
          handleDeleteCategory={canDeleteMenu ? handleDeleteCategory : undefined}
          handleToggleCategoryActive={canManageCategories ? handleToggleCategoryActive : undefined}
          handleEditItem={canManageItems ? handleEditItem : undefined}
          handleDeleteItem={canManageItems ? handleDeleteItem : undefined}
          handleToggleItemActive={canManageItems ? handleToggleItemActive : undefined}
          handleCategorySelect={handleCategorySelect}
          handleCreateSubcategory={canManageCategories ? handleCreateSubcategory : undefined}
          handleEditSubcategory={canManageCategories ? handleEditSubcategory : undefined}
          handleDeleteSubcategory={canDeleteMenu ? handleDeleteSubcategory : undefined}
          handleToggleSubcategoryActive={canManageCategories ? handleToggleSubcategoryActive : undefined}
          handleSubcategorySelect={handleSubcategorySelect}
          setViewMode={setViewMode}
          setSearchTerm={setSearchQuery}
          permissions={{
            canEdit: canEditMenu,
            canCreate: canCreateMenu,
            canDelete: canDeleteMenu,
            canManageCategories,
            canManageItems,
            accessLevel: getAccessLevel,
          }}
        />

        {/* Loading Overlays */}
        <LoadingOverlay
          isVisible={savingItem}
          type="saving"
          message="Salvando item do menu..."
        />
        <LoadingOverlay
          isVisible={savingCategory}
          type="saving"
          message="Salvando categoria..."
        />

        {/* Modals */}
        <ItemModal
          isOpen={showItemModal}
          onClose={() => {
            setShowItemModal(false);
            setEditingItem(null);
          }}
          onSave={handleSaveItem}
          item={editingItem}
          categories={categories}
          defaultCategoryId={
            selectedCategory?.id ||
            categories.find(cat => !menuCategoryService.isDefaultCategory(cat))?.id ||
            categories[0]?.id ||
            ''
          }
          loading={loading || savingItem}
          digitalMenuId={selectedMenu?.id}
        />

        <CategoryModal
          isOpen={showCategoryModal}
          onClose={() => {
            setShowCategoryModal(false);
            setEditingCategory(null);
          }}
          onSave={handleSaveCategory}
          category={editingCategory}
          categories={categories}
          loading={loading || savingCategory}
        />
      </div>
    </DndContext>
  );
}
