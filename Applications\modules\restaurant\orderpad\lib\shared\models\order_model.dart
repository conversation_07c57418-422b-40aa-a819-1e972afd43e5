import 'package:uuid/uuid.dart';
import 'menu_item_model.dart';

class OrderModel {
  final String id;
  final String orderNumber;
  final String type; // 'dine_in', 'delivery', 'takeaway'
  final String status; // 'pending', 'preparing', 'ready', 'delivered', 'cancelled'
  final String? tableId;
  final String? tableName;
  final String waiterId;
  final String waiterName;
  final String? customerId;
  final String? customerName;
  final String? customerPhone;
  final String? customerAddress;
  final List<OrderItem> items;
  final double subtotal;
  final double taxAmount;
  final double discountAmount;
  final double deliveryFee;
  final double totalAmount;
  final String? paymentMethod;
  final String? paymentStatus; // 'pending', 'paid', 'refunded'
  final String? notes;
  final String? specialInstructions;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? preparedAt;
  final DateTime? deliveredAt;
  final DateTime? cancelledAt;
  final String? cancellationReason;
  final int estimatedPreparationTime; // in minutes
  final Map<String, dynamic>? metadata;

  OrderModel({
    required this.id,
    required this.orderNumber,
    required this.type,
    required this.status,
    this.tableId,
    this.tableName,
    required this.waiterId,
    required this.waiterName,
    this.customerId,
    this.customerName,
    this.customerPhone,
    this.customerAddress,
    this.items = const [],
    this.subtotal = 0.0,
    this.taxAmount = 0.0,
    this.discountAmount = 0.0,
    this.deliveryFee = 0.0,
    this.totalAmount = 0.0,
    this.paymentMethod,
    this.paymentStatus = 'pending',
    this.notes,
    this.specialInstructions,
    required this.createdAt,
    required this.updatedAt,
    this.preparedAt,
    this.deliveredAt,
    this.cancelledAt,
    this.cancellationReason,
    this.estimatedPreparationTime = 0,
    this.metadata,
  });

  factory OrderModel.create({
    required String type,
    required String waiterId,
    required String waiterName,
    String? tableId,
    String? tableName,
    String? customerId,
    String? customerName,
    String? customerPhone,
    String? customerAddress,
    String? notes,
    String? specialInstructions,
  }) {
    final now = DateTime.now();
    final orderNumber = _generateOrderNumber();
    
    return OrderModel(
      id: const Uuid().v4(),
      orderNumber: orderNumber,
      type: type,
      status: 'pending',
      tableId: tableId,
      tableName: tableName,
      waiterId: waiterId,
      waiterName: waiterName,
      customerId: customerId,
      customerName: customerName,
      customerPhone: customerPhone,
      customerAddress: customerAddress,
      notes: notes,
      specialInstructions: specialInstructions,
      createdAt: now,
      updatedAt: now,
    );
  }

  static String _generateOrderNumber() {
    final now = DateTime.now();
    final timestamp = now.millisecondsSinceEpoch.toString().substring(8);
    return '#${timestamp}';
  }

  factory OrderModel.fromJson(Map<String, dynamic> json) {
    return OrderModel(
      id: json['id'] ?? '',
      orderNumber: json['orderNumber'] ?? '',
      type: json['type'] ?? 'dine_in',
      status: json['status'] ?? 'pending',
      tableId: json['tableId'],
      tableName: json['tableName'],
      waiterId: json['waiterId'] ?? '',
      waiterName: json['waiterName'] ?? '',
      customerId: json['customerId'],
      customerName: json['customerName'],
      customerPhone: json['customerPhone'],
      customerAddress: json['customerAddress'],
      items: (json['items'] as List<dynamic>? ?? [])
          .map((item) => OrderItem.fromJson(item))
          .toList(),
      subtotal: (json['subtotal'] ?? 0.0).toDouble(),
      taxAmount: (json['taxAmount'] ?? 0.0).toDouble(),
      discountAmount: (json['discountAmount'] ?? 0.0).toDouble(),
      deliveryFee: (json['deliveryFee'] ?? 0.0).toDouble(),
      totalAmount: (json['totalAmount'] ?? 0.0).toDouble(),
      paymentMethod: json['paymentMethod'],
      paymentStatus: json['paymentStatus'] ?? 'pending',
      notes: json['notes'],
      specialInstructions: json['specialInstructions'],
      createdAt: DateTime.parse(
          json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(
          json['updatedAt'] ?? DateTime.now().toIso8601String()),
      preparedAt: json['preparedAt'] != null
          ? DateTime.parse(json['preparedAt'])
          : null,
      deliveredAt: json['deliveredAt'] != null
          ? DateTime.parse(json['deliveredAt'])
          : null,
      cancelledAt: json['cancelledAt'] != null
          ? DateTime.parse(json['cancelledAt'])
          : null,
      cancellationReason: json['cancellationReason'],
      estimatedPreparationTime: json['estimatedPreparationTime'] ?? 0,
      metadata: json['metadata'] != null
          ? Map<String, dynamic>.from(json['metadata'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'orderNumber': orderNumber,
      'type': type,
      'status': status,
      'tableId': tableId,
      'tableName': tableName,
      'waiterId': waiterId,
      'waiterName': waiterName,
      'customerId': customerId,
      'customerName': customerName,
      'customerPhone': customerPhone,
      'customerAddress': customerAddress,
      'items': items.map((item) => item.toJson()).toList(),
      'subtotal': subtotal,
      'taxAmount': taxAmount,
      'discountAmount': discountAmount,
      'deliveryFee': deliveryFee,
      'totalAmount': totalAmount,
      'paymentMethod': paymentMethod,
      'paymentStatus': paymentStatus,
      'notes': notes,
      'specialInstructions': specialInstructions,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'preparedAt': preparedAt?.toIso8601String(),
      'deliveredAt': deliveredAt?.toIso8601String(),
      'cancelledAt': cancelledAt?.toIso8601String(),
      'cancellationReason': cancellationReason,
      'estimatedPreparationTime': estimatedPreparationTime,
      'metadata': metadata,
    };
  }

  OrderModel copyWith({
    String? id,
    String? orderNumber,
    String? type,
    String? status,
    String? tableId,
    String? tableName,
    String? waiterId,
    String? waiterName,
    String? customerId,
    String? customerName,
    String? customerPhone,
    String? customerAddress,
    List<OrderItem>? items,
    double? subtotal,
    double? taxAmount,
    double? discountAmount,
    double? deliveryFee,
    double? totalAmount,
    String? paymentMethod,
    String? paymentStatus,
    String? notes,
    String? specialInstructions,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? preparedAt,
    DateTime? deliveredAt,
    DateTime? cancelledAt,
    String? cancellationReason,
    int? estimatedPreparationTime,
    Map<String, dynamic>? metadata,
  }) {
    return OrderModel(
      id: id ?? this.id,
      orderNumber: orderNumber ?? this.orderNumber,
      type: type ?? this.type,
      status: status ?? this.status,
      tableId: tableId ?? this.tableId,
      tableName: tableName ?? this.tableName,
      waiterId: waiterId ?? this.waiterId,
      waiterName: waiterName ?? this.waiterName,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      customerAddress: customerAddress ?? this.customerAddress,
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      taxAmount: taxAmount ?? this.taxAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      deliveryFee: deliveryFee ?? this.deliveryFee,
      totalAmount: totalAmount ?? this.totalAmount,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      notes: notes ?? this.notes,
      specialInstructions: specialInstructions ?? this.specialInstructions,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      preparedAt: preparedAt ?? this.preparedAt,
      deliveredAt: deliveredAt ?? this.deliveredAt,
      cancelledAt: cancelledAt ?? this.cancelledAt,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      estimatedPreparationTime: estimatedPreparationTime ?? this.estimatedPreparationTime,
      metadata: metadata ?? this.metadata,
    );
  }

  // Helper methods
  bool get isPending => status == OrderStatus.pending;
  bool get isPreparing => status == OrderStatus.preparing;
  bool get isReady => status == OrderStatus.ready;
  bool get isDelivered => status == OrderStatus.delivered;
  bool get isCancelled => status == OrderStatus.cancelled;
  bool get isPaid => paymentStatus == 'paid';
  bool get isDineIn => type == OrderType.dineIn;
  bool get isDelivery => type == OrderType.delivery;
  bool get isTakeaway => type == OrderType.takeaway;
  bool get hasTable => tableId != null && tableId!.isNotEmpty;
  bool get hasCustomer => customerId != null && customerId!.isNotEmpty;
  
  int get itemCount => items.fold(0, (sum, item) => sum + item.quantity);
  
  Duration? get preparationDuration {
    if (preparedAt == null) return null;
    return preparedAt!.difference(createdAt);
  }
  
  Duration get orderAge => DateTime.now().difference(createdAt);
  
  String get typeDisplay {
    switch (type) {
      case OrderType.dineIn:
        return 'Balcão';
      case OrderType.delivery:
        return 'Delivery';
      case OrderType.takeaway:
        return 'Retirada';
      default:
        return type;
    }
  }
  
  String get statusDisplay {
    switch (status) {
      case OrderStatus.pending:
        return 'Pendente';
      case OrderStatus.preparing:
        return 'Preparando';
      case OrderStatus.ready:
        return 'Pronto';
      case OrderStatus.delivered:
        return 'Entregue';
      case OrderStatus.cancelled:
        return 'Cancelado';
      default:
        return status;
    }
  }

  // Calculate totals
  OrderModel recalculateTotals({double taxRate = 0.1}) {
    final newSubtotal = items.fold(0.0, (sum, item) => sum + item.totalPrice);
    final newTaxAmount = newSubtotal * taxRate;
    final newTotalAmount = newSubtotal + newTaxAmount + deliveryFee - discountAmount;
    final newEstimatedTime = items.fold(0, (max, item) => 
        item.menuItem.preparationTime > max ? item.menuItem.preparationTime : max);
    
    return copyWith(
      subtotal: newSubtotal,
      taxAmount: newTaxAmount,
      totalAmount: newTotalAmount,
      estimatedPreparationTime: newEstimatedTime,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OrderModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'OrderModel(id: $id, orderNumber: $orderNumber, status: $status, total: $totalAmount)';
  }
}

class OrderItem {
  final String id;
  final MenuItemModel menuItem;
  final int quantity;
  final double unitPrice;
  final double totalPrice;
  final List<SelectedModifier> selectedModifiers;
  final String? notes;
  final DateTime addedAt;

  OrderItem({
    required this.id,
    required this.menuItem,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
    this.selectedModifiers = const [],
    this.notes,
    required this.addedAt,
  });

  factory OrderItem.create({
    required MenuItemModel menuItem,
    int quantity = 1,
    List<SelectedModifier> selectedModifiers = const [],
    String? notes,
  }) {
    final modifierPrice = selectedModifiers.fold(0.0, 
        (sum, modifier) => sum + modifier.priceModifier);
    final unitPrice = menuItem.price + modifierPrice;
    final totalPrice = unitPrice * quantity;
    
    return OrderItem(
      id: const Uuid().v4(),
      menuItem: menuItem,
      quantity: quantity,
      unitPrice: unitPrice,
      totalPrice: totalPrice,
      selectedModifiers: selectedModifiers,
      notes: notes,
      addedAt: DateTime.now(),
    );
  }

  factory OrderItem.fromJson(Map<String, dynamic> json) {
    return OrderItem(
      id: json['id'] ?? '',
      menuItem: MenuItemModel.fromJson(json['menuItem']),
      quantity: json['quantity'] ?? 1,
      unitPrice: (json['unitPrice'] ?? 0.0).toDouble(),
      totalPrice: (json['totalPrice'] ?? 0.0).toDouble(),
      selectedModifiers: (json['selectedModifiers'] as List<dynamic>? ?? [])
          .map((modifier) => SelectedModifier.fromJson(modifier))
          .toList(),
      notes: json['notes'],
      addedAt: DateTime.parse(
          json['addedAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'menuItem': menuItem.toJson(),
      'quantity': quantity,
      'unitPrice': unitPrice,
      'totalPrice': totalPrice,
      'selectedModifiers': selectedModifiers.map((m) => m.toJson()).toList(),
      'notes': notes,
      'addedAt': addedAt.toIso8601String(),
    };
  }

  OrderItem copyWith({
    String? id,
    MenuItemModel? menuItem,
    int? quantity,
    double? unitPrice,
    double? totalPrice,
    List<SelectedModifier>? selectedModifiers,
    String? notes,
    DateTime? addedAt,
  }) {
    return OrderItem(
      id: id ?? this.id,
      menuItem: menuItem ?? this.menuItem,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalPrice: totalPrice ?? this.totalPrice,
      selectedModifiers: selectedModifiers ?? this.selectedModifiers,
      notes: notes ?? this.notes,
      addedAt: addedAt ?? this.addedAt,
    );
  }
}

class SelectedModifier {
  final String modifierId;
  final String modifierName;
  final String optionId;
  final String optionName;
  final double priceModifier;
  final int quantity;

  SelectedModifier({
    required this.modifierId,
    required this.modifierName,
    required this.optionId,
    required this.optionName,
    required this.priceModifier,
    this.quantity = 1,
  });

  factory SelectedModifier.fromJson(Map<String, dynamic> json) {
    return SelectedModifier(
      modifierId: json['modifierId'] ?? '',
      modifierName: json['modifierName'] ?? '',
      optionId: json['optionId'] ?? '',
      optionName: json['optionName'] ?? '',
      priceModifier: (json['priceModifier'] ?? 0.0).toDouble(),
      quantity: json['quantity'] ?? 1,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'modifierId': modifierId,
      'modifierName': modifierName,
      'optionId': optionId,
      'optionName': optionName,
      'priceModifier': priceModifier,
      'quantity': quantity,
    };
  }
}

// Order status constants
class OrderStatus {
  static const String pending = 'pending';
  static const String preparing = 'preparing';
  static const String ready = 'ready';
  static const String delivered = 'delivered';
  static const String cancelled = 'cancelled';

  static List<String> get all => [pending, preparing, ready, delivered, cancelled];
}

// Order type constants
class OrderType {
  static const String dineIn = 'dine_in';
  static const String delivery = 'delivery';
  static const String takeaway = 'takeaway';

  static List<String> get all => [dineIn, delivery, takeaway];
}

// Payment methods
class PaymentMethod {
  static const String cash = 'cash';
  static const String card = 'card';
  static const String pix = 'pix';

  static List<String> get all => [cash, card, pix];
  
  static String getDisplayName(String method) {
    switch (method) {
      case cash:
        return 'Dinheiro';
      case card:
        return 'Cartão';
      case pix:
        return 'PIX';
      default:
        return method;
    }
  }
}