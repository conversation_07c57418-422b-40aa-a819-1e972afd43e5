from typing import Optional
from pydantic import BaseModel, Field
from uuid import UUID
from decimal import Decimal


class ProductVariantOptionBase(BaseModel):
    """Base schema for product variant options."""
    name: str = Field(..., min_length=1, max_length=100, description="Variant option name")
    description: Optional[str] = Field(None, max_length=255, description="Variant option description")
    value: Optional[str] = Field(None, max_length=100, description="Machine-readable value")
    price_adjustment: Decimal = Field(0.0, description="Price adjustment from base price")
    price_adjustment_type: str = Field("fixed", pattern="^(fixed|percentage)$", description="Price adjustment type")
    stock_quantity: Optional[int] = Field(None, ge=0, description="Stock quantity for this variant")
    sku_suffix: Optional[str] = Field(None, max_length=50, description="SKU suffix for this variant")
    display_order: int = Field(0, description="Display order within the group")
    is_default: bool = Field(False, description="Whether this is the default selection")
    is_active: bool = Field(True, description="Whether this variant option is active")
    color_code: Optional[str] = Field(None, pattern="^#[0-9A-Fa-f]{6}$", description="Hex color code")
    image_url: Optional[str] = Field(None, max_length=500, description="Image URL for this variant")


class ProductVariantOptionCreate(ProductVariantOptionBase):
    """Schema for creating a new product variant option."""
    tenant_id: Optional[UUID] = Field(None, description="Tenant ID (null for global options)")
    variant_group_id: UUID = Field(..., description="Parent variant group ID")


class ProductVariantOptionUpdate(BaseModel):
    """Schema for updating a product variant option."""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="Variant option name")
    description: Optional[str] = Field(None, max_length=255, description="Variant option description")
    value: Optional[str] = Field(None, max_length=100, description="Machine-readable value")
    price_adjustment: Optional[Decimal] = Field(None, description="Price adjustment from base price")
    price_adjustment_type: Optional[str] = Field(None, pattern="^(fixed|percentage)$", description="Price adjustment type")
    stock_quantity: Optional[int] = Field(None, ge=0, description="Stock quantity for this variant")
    sku_suffix: Optional[str] = Field(None, max_length=50, description="SKU suffix for this variant")
    display_order: Optional[int] = Field(None, description="Display order within the group")
    is_default: Optional[bool] = Field(None, description="Whether this is the default selection")
    is_active: Optional[bool] = Field(None, description="Whether this variant option is active")
    color_code: Optional[str] = Field(None, pattern="^#[0-9A-Fa-f]{6}$", description="Hex color code")
    image_url: Optional[str] = Field(None, max_length=500, description="Image URL for this variant")


class ProductVariantOptionResponse(ProductVariantOptionBase):
    """Schema for product variant option responses."""
    id: UUID
    tenant_id: Optional[UUID]
    variant_group_id: UUID
    
    class Config:
        from_attributes = True
