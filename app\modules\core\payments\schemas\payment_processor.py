"""Schemas for payment processors."""

import uuid  # noqa: E402
from typing import Optional, Dict, Any
from pydantic import BaseModel, ConfigDict, Field

from app.modules.core.payments.models.payment_processor import PaymentProcessorType  # noqa: E402

# --- PaymentProcessor Schemas ---

# Base schema for payment processors


class PaymentProcessorBase(BaseModel):
    """Base schema for payment processors."""

    name: str = Field(..., min_length=1, max_length=100)
    processor_type: PaymentProcessorType
    is_active: bool = True
    is_default: bool = False
    sandbox_mode: bool = True
    webhook_url: Optional[str] = None
    additional_config: Optional[Dict[str, Any]] = None


# Schema for creating a new payment processor


class PaymentProcessorCreate(PaymentProcessorBase):
    """Schema for creating a new payment processor."""

    api_key: Optional[str] = None
    api_secret: Optional[str] = None
    webhook_secret: Optional[str] = None


# Schema for updating an existing payment processor


class PaymentProcessorUpdate(BaseModel):
    """Schema for updating an existing payment processor."""

    name: Optional[str] = Field(None, min_length=1, max_length=100)
    processor_type: Optional[PaymentProcessorType] = None
    is_active: Optional[bool] = None
    is_default: Optional[bool] = None
    sandbox_mode: Optional[bool] = None
    api_key: Optional[str] = None
    api_secret: Optional[str] = None
    webhook_url: Optional[str] = None
    webhook_secret: Optional[str] = None
    additional_config: Optional[Dict[str, Any]] = None


# Schema for reading a payment processor


class PaymentProcessorRead(PaymentProcessorBase):
    """Schema for reading a payment processor."""

    id: uuid.UUID
    tenant_id: uuid.UUID

    model_config = ConfigDict(from_attributes=True)


# Schema for reading a payment processor with sensitive information


class PaymentProcessorReadAdmin(PaymentProcessorRead):
    """Schema for reading a payment processor with sensitive information."""

    api_key: Optional[str] = None
    webhook_secret: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)
