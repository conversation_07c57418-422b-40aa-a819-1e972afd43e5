import logging
from typing import Optional, Sequence
from sqlalchemy import select, and_, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.exc import IntegrityError
from fastapi import HTTPException, status
import uuid
from decimal import Decimal

# Import models
from app.modules.core.eshop.models.product_review import ProductReview
from app.modules.core.eshop.models.product import Product

# Import schemas
from app.modules.core.eshop.schemas.product_review import (
    ProductReviewCreate,
    ProductReviewUpdate,
    ProductReviewAdminUpdate,
    ProductReviewVote,
)

# Import WebSocket utility
from app.websockets.manager import emit_to_tenant

logger = logging.getLogger(__name__)


class ProductReviewService:
    def __init__(self, db_session: AsyncSession):
        self.db = db_session

    async def create_review(
        self, 
        review_in: ProductReviewCreate,
        current_user_id: uuid.UUID
    ) -> ProductReview:
        """Creates a new product review."""
        logger.info(f"Creating review for product: {review_in.product_id} by user {current_user_id}")

        # Check if product exists
        product_query = select(Product).where(Product.id == review_in.product_id)
        result = await self.db.execute(product_query)
        product = result.scalars().first()
        
        if not product:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Product not found.",
            )

        # Check if user already reviewed this product
        existing_review_query = select(ProductReview).where(
            and_(
                ProductReview.product_id == review_in.product_id,
                ProductReview.user_id == current_user_id
            )
        )
        result = await self.db.execute(existing_review_query)
        existing_review = result.scalars().first()
        
        if existing_review:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="You have already reviewed this product.",
            )

        try:
            review_data = review_in.model_dump()
            review_data["user_id"] = current_user_id
            
            db_review = ProductReview(**review_data)
            self.db.add(db_review)
            await self.db.flush()

            # Update product rating statistics
            await self._update_product_rating_stats(review_in.product_id)

            await self.db.commit()
            await self.db.refresh(db_review)

            # Emit WebSocket notification
            if product.tenant_id:
                await emit_to_tenant(
                    product.tenant_id,
                    "product_review_created",
                    {
                        "product_id": str(review_in.product_id),
                        "review_id": str(db_review.id),
                        "rating": review_in.rating
                    }
                )

            logger.info(f"Product review created: {db_review.id}")
            return db_review

        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"IntegrityError creating review: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Error creating review. Database constraint violation: {str(e)}",
            )
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Unexpected error creating review: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal server error: {str(e)}",
            )

    async def get_review(
        self, 
        review_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID] = None
    ) -> Optional[ProductReview]:
        """Gets a specific product review by ID."""
        query = select(ProductReview).where(ProductReview.id == review_id)
        
        if tenant_id:
            query = query.where(ProductReview.tenant_id == tenant_id)

        query = query.options(
            joinedload(ProductReview.user),
            joinedload(ProductReview.product)
        )

        result = await self.db.execute(query)
        review = result.scalars().first()

        if not review:
            logger.warning(f"Review {review_id} not found")
            return None

        return review

    async def get_reviews(
        self,
        product_id: Optional[uuid.UUID] = None,
        user_id: Optional[uuid.UUID] = None,
        tenant_id: Optional[uuid.UUID] = None,
        rating: Optional[int] = None,
        is_approved: Optional[bool] = None,
        is_featured: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100,
        order_by: str = "created_at",
        order_direction: str = "desc",
    ) -> Sequence[ProductReview]:
        """Gets a list of product reviews with filtering options."""
        query = select(ProductReview)

        # Apply filters
        filters = []
        
        if product_id:
            filters.append(ProductReview.product_id == product_id)
        
        if user_id:
            filters.append(ProductReview.user_id == user_id)
            
        if tenant_id:
            filters.append(ProductReview.tenant_id == tenant_id)
            
        if rating is not None:
            filters.append(ProductReview.rating == rating)
            
        if is_approved is not None:
            filters.append(ProductReview.is_approved == is_approved)
            
        if is_featured is not None:
            filters.append(ProductReview.is_featured == is_featured)

        if filters:
            query = query.where(and_(*filters))

        # Apply ordering
        if order_direction.lower() == "desc":
            if order_by == "rating":
                query = query.order_by(ProductReview.rating.desc())
            elif order_by == "helpful_count":
                query = query.order_by(ProductReview.helpful_count.desc())
            else:  # created_at
                query = query.order_by(ProductReview.created_at.desc())
        else:
            if order_by == "rating":
                query = query.order_by(ProductReview.rating.asc())
            elif order_by == "helpful_count":
                query = query.order_by(ProductReview.helpful_count.asc())
            else:  # created_at
                query = query.order_by(ProductReview.created_at.asc())

        # Apply pagination
        query = query.offset(skip).limit(limit)

        # Load relationships
        query = query.options(
            joinedload(ProductReview.user),
            joinedload(ProductReview.product)
        )

        result = await self.db.execute(query)
        reviews = result.scalars().all()

        return reviews

    async def update_review(
        self, 
        review_id: uuid.UUID, 
        review_in: ProductReviewUpdate,
        current_user_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID] = None
    ) -> Optional[ProductReview]:
        """Updates an existing product review."""
        db_review = await self.get_review(review_id, tenant_id)
        if not db_review:
            return None

        # Validate user permissions (users can only update their own reviews)
        if db_review.user_id != current_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You can only update your own reviews.",
            )

        try:
            update_data = review_in.model_dump(exclude_unset=True)
            old_rating = db_review.rating

            # Apply updates
            for field, value in update_data.items():
                setattr(db_review, field, value)

            await self.db.flush()

            # Update product rating statistics if rating changed
            if "rating" in update_data and update_data["rating"] != old_rating:
                await self._update_product_rating_stats(db_review.product_id)

            await self.db.commit()
            await self.db.refresh(db_review)

            logger.info(f"Product review updated: {db_review.id}")
            return db_review

        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Error updating review: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal server error: {str(e)}",
            )

    async def admin_update_review(
        self, 
        review_id: uuid.UUID, 
        review_in: ProductReviewAdminUpdate,
        current_user_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID] = None
    ) -> Optional[ProductReview]:
        """Admin updates to product review (approval, featured status)."""
        db_review = await self.get_review(review_id, tenant_id)
        if not db_review:
            return None

        try:
            update_data = review_in.model_dump(exclude_unset=True)

            # Apply updates
            for field, value in update_data.items():
                setattr(db_review, field, value)

            await self.db.commit()
            await self.db.refresh(db_review)

            logger.info(f"Product review admin updated: {db_review.id}")
            return db_review

        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Error admin updating review: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal server error: {str(e)}",
            )

    async def delete_review(
        self, 
        review_id: uuid.UUID,
        current_user_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID] = None
    ) -> bool:
        """Deletes a product review."""
        db_review = await self.get_review(review_id, tenant_id)
        if not db_review:
            return False

        # Validate user permissions (users can only delete their own reviews)
        if db_review.user_id != current_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You can only delete your own reviews.",
            )

        try:
            product_id = db_review.product_id
            await self.db.delete(db_review)
            await self.db.flush()

            # Update product rating statistics
            await self._update_product_rating_stats(product_id)

            await self.db.commit()

            logger.info(f"Product review deleted: {review_id}")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Error deleting review: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal server error: {str(e)}",
            )

    async def _update_product_rating_stats(self, product_id: uuid.UUID):
        """Updates product rating statistics."""
        # Calculate new average rating and review count
        stats_query = select(
            func.avg(ProductReview.rating).label("avg_rating"),
            func.count(ProductReview.id).label("review_count")
        ).where(
            and_(
                ProductReview.product_id == product_id,
                ProductReview.is_approved == True
            )
        )
        
        result = await self.db.execute(stats_query)
        stats = result.first()
        
        avg_rating = float(stats.avg_rating) if stats.avg_rating else 0.0
        review_count = stats.review_count or 0

        # Update product
        product_query = select(Product).where(Product.id == product_id)
        result = await self.db.execute(product_query)
        product = result.scalars().first()
        
        if product:
            product.average_rating = Decimal(str(round(avg_rating, 2)))
            product.review_count = review_count
