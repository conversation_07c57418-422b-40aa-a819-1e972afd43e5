import uuid
import enum
from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON>ey,
    Numeric,
    Boolean,
    Enum as SQLAlchemyEnum,
    Text,
    DateTime
)
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.orm import relationship, Mapped, mapped_column
from typing import TYPE_CHECKING

from app.db.base import Base
from app.modules.core.functions.orders.models.order import Order

if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant


class CarrierType(str, enum.Enum):
    INTERNAL = "internal"
    EXTERNAL = "external"


class ShipmentStatus(str, enum.Enum):
    PENDING = "pending"
    IN_TRANSIT = "in_transit"
    DELIVERED = "delivered"
    FAILED = "failed"
    RETURNED = "returned"


class CarrierServiceability(enum.Enum):
    LOCAL = "local"
    NATIONAL = "national"
    INTERNATIONAL = "international"


class ShippingCarrier(Base):
    __tablename__ = "shipping_carriers"

    id: Mapped[uuid.UUID] = mapped_column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id: Mapped[uuid.UUID] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    carrier_type: Mapped[CarrierType] = mapped_column(SQLAlchemyEnum(CarrierType), nullable=False, default=CarrierType.EXTERNAL)
    tracking_url_template: Mapped[str | None] = mapped_column(String(255))
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    service_area: Mapped[CarrierServiceability] = mapped_column(SQLAlchemyEnum(CarrierServiceability), nullable=False)
    logo_url: Mapped[str | None] = mapped_column(String(255))
    website: Mapped[str | None] = mapped_column(String(255))

    tenant: Mapped["Tenant"] = relationship("Tenant")


class ShippingMethod(Base):
    __tablename__ = "shipping_methods"

    id: Mapped[uuid.UUID] = mapped_column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id: Mapped[uuid.UUID] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    carrier_id: Mapped[uuid.UUID | None] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("shipping_carriers.id"))

    name: Mapped[str] = mapped_column(String(100), nullable=False)
    description: Mapped[str | None] = mapped_column(Text)
    base_cost: Mapped[float] = mapped_column(Numeric(10, 2), default=0.00, nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    
    tenant: Mapped["Tenant"] = relationship("Tenant")
    carrier: Mapped["ShippingCarrier"] = relationship("ShippingCarrier")


class ShippingZone(Base):
    __tablename__ = "shipping_zones"

    id: Mapped[uuid.UUID] = mapped_column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id: Mapped[uuid.UUID] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    name: Mapped[str] = mapped_column(String(100), nullable=False, unique=True)
    countries: Mapped[str] = mapped_column(Text)
    provinces: Mapped[str] = mapped_column(Text)
    postal_codes: Mapped[str] = mapped_column(Text)

    tenant: Mapped["Tenant"] = relationship("Tenant")


class ShippingRate(Base):
    __tablename__ = "shipping_rates"

    id: Mapped[uuid.UUID] = mapped_column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id: Mapped[uuid.UUID] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    carrier_id: Mapped[uuid.UUID] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("shipping_carriers.id"))
    zone_id: Mapped[uuid.UUID] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("shipping_zones.id"))
    min_weight: Mapped[float] = mapped_column(Numeric(10, 2), default=0.00)
    max_weight: Mapped[float] = mapped_column(Numeric(10, 2), default=9999.99)
    rate: Mapped[float] = mapped_column(Numeric(10, 2), nullable=False)
    
    tenant: Mapped["Tenant"] = relationship("Tenant")
    carrier: Mapped["ShippingCarrier"] = relationship("ShippingCarrier")
    zone: Mapped["ShippingZone"] = relationship("ShippingZone")


class Shipment(Base):
    __tablename__ = "shipments"

    id: Mapped[uuid.UUID] = mapped_column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id: Mapped[uuid.UUID] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    order_id: Mapped[uuid.UUID] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("orders.id"), nullable=False, index=True)
    shipping_method_id: Mapped[uuid.UUID] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("shipping_methods.id"), nullable=False, index=True)

    tracking_number: Mapped[str | None] = mapped_column(String(100), index=True)
    status: Mapped[ShipmentStatus] = mapped_column(SQLAlchemyEnum(ShipmentStatus), nullable=False, default=ShipmentStatus.PENDING, index=True)
    shipping_cost: Mapped[float] = mapped_column(Numeric(10, 2), nullable=False)
    notes: Mapped[str | None] = mapped_column(Text)
    shipped_at: Mapped[DateTime | None] = mapped_column(DateTime)
    delivered_at: Mapped[DateTime | None] = mapped_column(DateTime)

    tenant: Mapped["Tenant"] = relationship("Tenant")
    order: Mapped["Order"] = relationship("Order", back_populates="shipments")
    shipping_method: Mapped["ShippingMethod"] = relationship("ShippingMethod")