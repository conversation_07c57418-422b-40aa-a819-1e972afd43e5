import logging
from typing import Optional, Sequence
from sqlalchemy.ext.asyncio import AsyncSession
import uuid

# Import service classes
from app.modules.tenants.restaurants.menu.services.menu_category_service import (
    MenuCategoryService,
)
from app.modules.tenants.restaurants.menu.services.menu_item_service import (  # noqa: E402
    MenuItemService,
)
from app.modules.tenants.restaurants.menu.services.menu_variant_service import (  # noqa: E402
    MenuVariantService,
)
from app.modules.tenants.restaurants.menu.services.menu_modifier_service import (  # noqa: E402
    MenuModifierService,
)

# Import models
from app.modules.tenants.restaurants.menu.models.menu_category import (  # noqa: E402
    MenuCategory,
)
from app.modules.tenants.restaurants.menu.models.menu_item import MenuItem  # noqa: E402
from app.modules.core.functions.customizations.models.variant_group import (
    VariantGroup,
)

# Import schemas
from app.modules.tenants.restaurants.menu.schemas.menu_category import (  # noqa: E402
    MenuCategoryCreate,
    MenuCategoryUpdate,
)
from app.modules.tenants.restaurants.menu.schemas.menu_item import (  # noqa: E402
    MenuItemCreate,
    MenuItemUpdate,
)
from app.modules.tenants.restaurants.menu.schemas.variant_group import (  # noqa: E402
    VariantGroupCreate,
)

logger = logging.getLogger(__name__)


class MenuService:
    """
    Facade service that delegates to specialized services for menu operations.
    This service provides a unified interface for all menu-related operations.
    """

    def __init__(self, db_session: AsyncSession):
        self.db = db_session
        self.category_service = MenuCategoryService(db_session)
        self.item_service = MenuItemService(db_session)
        self.variant_service = MenuVariantService(db_session)
        self.modifier_service = MenuModifierService(db_session)

    # --- Menu Category Operations ---

    async def create_category(
        self, category_in: MenuCategoryCreate, tenant_id: uuid.UUID
    ) -> MenuCategory:
        """Creates a new menu category for the given tenant, with optional parent_id for subcategories."""  # noqa: E501
        return await self.category_service.create_category(category_in, tenant_id)

    async def get_category(
        self,
        category_id: uuid.UUID,
        tenant_id: uuid.UUID,
        include_children: bool = True,
    ) -> Optional[MenuCategory]:
        """
        Gets a specific active menu category by ID for the given tenant.
        Optionally includes its direct children.
        """
        return await self.category_service.get_category(category_id, tenant_id, include_children)

    async def get_categories(
        self,
        tenant_id: uuid.UUID,
        parent_id: Optional[uuid.UUID] = None,
        digital_menu_id: Optional[uuid.UUID] = None,
        include_children: bool = True,
        include_items: bool = False,
        only_top_level: bool = False,
        skip: int = 0,
        limit: int = 100,
    ) -> Sequence[MenuCategory]:
        """
        Gets a list of active menu categories for the given tenant.
        Can filter by parent_id, digital_menu_id, or fetch only top-level categories.
        """
        return await self.category_service.get_categories(
            tenant_id=tenant_id,
            parent_id=parent_id,
            digital_menu_id=digital_menu_id,
            include_children=include_children,
            include_items=include_items,
            only_top_level=only_top_level,
            skip=skip,
            limit=limit,
        )

    async def _is_descendant(
        self,
        potential_descendant_id: uuid.UUID,
        ancestor_id: uuid.UUID,
        tenant_id: uuid.UUID,
    ) -> bool:
        """
        Check if potential_descendant_id is a descendant of ancestor_id in the category hierarchy.
        This prevents cycles when setting a category's parent.
        """
        return await self.category_service._is_descendant(
            potential_descendant_id, ancestor_id, tenant_id
        )

    async def update_category(
        self,
        category_id: uuid.UUID,
        category_in: MenuCategoryUpdate,
        tenant_id: uuid.UUID,
    ) -> Optional[MenuCategory]:
        """Updates an existing menu category, including parent_id."""
        return await self.category_service.update_category(category_id, category_in, tenant_id)

    async def _deactivate_category_and_children_recursive(
        self, category_id: uuid.UUID, tenant_id: uuid.UUID
    ):
        """
        Helper to recursively deactivate a category and all its children.
        """
        return await self.category_service._deactivate_category_and_children_recursive(
            category_id, tenant_id
        )

    async def delete_category(
        self, category_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Optional[MenuCategory]:
        """
        Soft deletes a menu category and all its active children recursively for the given tenant.

        Returns:
            The deactivated category with updated is_active=False if successful, None if not found
        """
        return await self.category_service.delete_category(category_id, tenant_id)

    # --- Menu Item Operations ---

    async def create_item(self, item_in: MenuItemCreate, tenant_id: uuid.UUID) -> MenuItem:
        """Creates a new menu item, potentially with nested groups/options."""
        return await self.item_service.create_item(item_in, tenant_id)

    async def get_item(
        self, item_id: uuid.UUID, tenant_id: uuid.UUID, include_details: bool = True
    ) -> Optional[MenuItem]:
        """Gets a specific menu item by ID for the given tenant, optionally including full details."""  # noqa: E501
        return await self.item_service.get_item(item_id, tenant_id, include_details)

    async def get_items(
        self,
        tenant_id: uuid.UUID,
        digital_menu_id: Optional[uuid.UUID] = None,
        category_id: Optional[uuid.UUID] = None,
        skip: int = 0,
        limit: int = 100,
        include_details: bool = False,  # Default to false for list view performance
    ) -> Sequence[MenuItem]:
        """Gets a list of menu items for the tenant, optionally filtered by digital menu and/or category."""
        return await self.item_service.get_items(
            tenant_id, digital_menu_id, category_id, skip, limit, include_details
        )

    async def update_item(
        self, item_id: uuid.UUID, item_in: MenuItemUpdate, tenant_id: uuid.UUID
    ) -> Optional[MenuItem]:
        """Updates an existing menu item."""
        return await self.item_service.update_item(item_id, item_in, tenant_id)

    async def delete_item(self, item_id: uuid.UUID, tenant_id: uuid.UUID) -> bool:
        """Deletes a menu item (soft delete preferred)."""
        return await self.item_service.delete_item(item_id, tenant_id)

    # --- Variant Group Operations ---

    async def create_variant_group(
        self,
        menu_item_id: uuid.UUID,
        group_in: VariantGroupCreate,
        tenant_id: uuid.UUID,
    ) -> VariantGroup:
        """Creates a new variant group for a menu item."""
        return await self.variant_service.create_variant_group(menu_item_id, group_in, tenant_id)

    async def get_variant_group(
        self, group_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Optional[VariantGroup]:
        """Gets a variant group by ID for the given tenant."""
        return await self.variant_service.get_variant_group(group_id, tenant_id)

    # --- Modifier Group Operations ---
    # These would delegate to the menu_modifier_service.py
