'use client';

import { useState } from 'react';
import { MapPinIcon, GlobeAltIcon, BuildingOfficeIcon } from '@heroicons/react/24/outline';
import AddressForm from './AddressForm';
import MapSelector from './MapSelector';

interface Address {
  street: string;
  number?: string;
  complement?: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  latitude?: number;
  longitude?: number;
  phone?: string;
  phone_secondary?: string;
  fax?: string;
  email?: string;
  website?: string;
}

interface LocationTabProps {
  address?: Address;
  tenantSlug?: string;
  onUpdate?: (field: string, value: any) => void;
}

const DEFAULT_ADDRESS: Address = {
  street: '',
  city: '',
  state: '',
  zipCode: '',
  country: 'US'
};

const COUNTRIES = [
  { code: 'US', name: 'United States', flag: '🇺🇸' },
  { code: 'BR', name: 'Brazil', flag: '🇧🇷' },
  { code: 'CA', name: 'Canada', flag: '🇨🇦' },
  { code: 'GB', name: 'United Kingdom', flag: '🇬🇧' },
  { code: 'DE', name: 'Germany', flag: '🇩🇪' },
  { code: 'FR', name: 'France', flag: '🇫🇷' },
  { code: 'IT', name: 'Italy', flag: '🇮🇹' },
  { code: 'ES', name: 'Spain', flag: '🇪🇸' },
  { code: 'AU', name: 'Australia', flag: '🇦🇺' },
  { code: 'JP', name: 'Japan', flag: '🇯🇵' }
];

export default function LocationTab({
  address = DEFAULT_ADDRESS,
  tenantSlug = '',
  onUpdate
}: LocationTabProps) {
  const [currentAddress, setCurrentAddress] = useState<Address>(address);
  const [currentSlug, setCurrentSlug] = useState(tenantSlug);
  const [showMap, setShowMap] = useState(false);

  const handleAddressUpdate = (field: keyof Address, value: any) => {
    const newAddress = { ...currentAddress, [field]: value };
    setCurrentAddress(newAddress);
    onUpdate?.('address', newAddress);
  };

  const handleSlugUpdate = (slug: string) => {
    // Clean slug: lowercase, no spaces, alphanumeric and hyphens only
    const cleanSlug = slug.toLowerCase().replace(/[^a-z0-9-]/g, '').replace(/--+/g, '-');
    setCurrentSlug(cleanSlug);
    onUpdate?.('tenantSlug', cleanSlug);
  };

  const handleLocationSelect = (lat: number, lng: number, addressData?: Partial<Address>) => {
    const newAddress = {
      ...currentAddress,
      latitude: lat,
      longitude: lng,
      ...addressData
    };
    setCurrentAddress(newAddress);
    onUpdate?.('address', newAddress);
  };

  const selectedCountry = COUNTRIES.find(c => c.code === currentAddress.country);

  return (
    <div className="space-y-6">
      {/* Tenant Configuration */}
      <div className="card">
        <div className="flex items-center space-x-2 mb-4">
          <GlobeAltIcon className="h-5 w-5 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900">Tenant Configuration</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Country
            </label>
            <select
              value={currentAddress.country}
              onChange={(e) => handleAddressUpdate('country', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            >
              {COUNTRIES.map(country => (
                <option key={country.code} value={country.code}>
                  {country.flag} {country.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tenant Slug
            </label>
            <div className="flex">
              <span className="inline-flex items-center px-3 py-2 border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm rounded-l-md">
                {selectedCountry?.code.toLowerCase()}.trix.com/
              </span>
              <input
                type="text"
                value={currentSlug}
                onChange={(e) => handleSlugUpdate(e.target.value)}
                placeholder="your-restaurant"
                className="flex-1 px-3 py-2 border border-gray-300 rounded-r-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
            <p className="text-xs text-gray-500 mt-1">
              This will be your restaurant's URL. Only lowercase letters, numbers, and hyphens allowed.
            </p>
          </div>
        </div>

        {currentSlug && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <p className="text-sm text-blue-700">
              <strong>Your restaurant URL:</strong> https://{selectedCountry?.code.toLowerCase()}.trix.com/{currentSlug}
            </p>
          </div>
        )}
      </div>

      {/* Address Information */}
      <div className="card">
        <div className="flex items-center space-x-2 mb-4">
          <BuildingOfficeIcon className="h-5 w-5 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900">Business Address</h3>
        </div>

        <AddressForm
          address={currentAddress}
          onUpdate={handleAddressUpdate}
        />
      </div>



      {/* Information Panel */}
      <div className="card">
        <div className="p-4 bg-green-50 border border-green-200 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <MapPinIcon className="h-5 w-5 text-green-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800">
                Location Benefits
              </h3>
              <div className="mt-2 text-sm text-green-700">
                <ul className="list-disc list-inside space-y-1">
                  <li>Accurate delivery radius calculation</li>
                  <li>Better customer experience with precise location</li>
                  <li>Integration with mapping services</li>
                  <li>Improved SEO with local business listings</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
