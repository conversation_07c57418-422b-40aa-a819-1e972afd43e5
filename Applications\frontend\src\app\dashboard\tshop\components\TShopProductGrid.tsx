'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  MagnifyingGlassIcon,
  FunnelIcon,
  ShoppingCartIcon,
  BuildingOfficeIcon,
  StarIcon,
  TruckIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';
import { useTShopCart } from '@/contexts/TShopCartContext';
import { useTShopProducts } from '@/hooks/useTShopProducts';
import { formatCurrency } from '@/lib/utils';
import { toast } from 'sonner';

interface TShopProduct {
  id: string;
  name: string;
  description?: string;
  b2b_price: number;
  b2c_price: number;
  stock_quantity: number;
  minimum_order_quantity: number;
  bulk_discount_tiers?: Array<{
    min_quantity: number;
    discount_percentage: number;
  }>;
  featured_image_url?: string;
  supplier_id: string;
  supplier_name: string;
  supplier_rating: number;
  estimated_delivery_days: number;
  category_name: string;
  market_type: 'b2b' | 'b2c' | 'both';
  tags?: string[];
}

export function TShopProductGrid() {
  const { products, isLoading, error } = useTShopProducts();
  const { addToCart, isLoading: cartLoading } = useTShopCart();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedSupplier, setSelectedSupplier] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [quantities, setQuantities] = useState<Record<string, number>>({});

  // Filter and sort products
  const filteredProducts = (products as any[])
    .filter((product: any) => {
      const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           product.description?.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedCategory === 'all' || product.category_name === selectedCategory;
      const matchesSupplier = selectedSupplier === 'all' || product.supplier_name === selectedSupplier;
      
      return matchesSearch && matchesCategory && matchesSupplier;
    })
    .sort((a: any, b: any) => {
      switch (sortBy) {
        case 'price_low':
          return (a.b2b_price || a.base_price || 0) - (b.b2b_price || b.base_price || 0);
        case 'price_high':
          return (b.b2b_price || b.base_price || 0) - (a.b2b_price || a.base_price || 0);
        case 'rating':
          return (b.supplier_rating || 0) - (a.supplier_rating || 0);
        case 'delivery':
          return (a.estimated_delivery_days || 0) - (b.estimated_delivery_days || 0);
        default:
          return a.name.localeCompare(b.name);
      }
    });

  // Get unique categories and suppliers
  const categories = Array.from(new Set((products as any[]).map((p: any) => p.category_name).filter(Boolean)));
  const suppliers = Array.from(new Set((products as any[]).map((p: any) => p.supplier_name).filter(Boolean)));

  const handleAddToCart = async (product: any) => {
    const quantity = quantities[product.id] || product.minimum_order_quantity || 1;

    if (quantity < (product.minimum_order_quantity || 1)) {
      toast.error(`Quantidade mínima: ${product.minimum_order_quantity}`);
      return;
    }

    if (quantity > (product.stock_quantity || 0)) {
      toast.error(`Estoque disponível: ${product.stock_quantity || 0}`);
      return;
    }

    await addToCart(product.id, quantity, product.supplier_id);
  };

  const handleQuantityChange = (productId: string, quantity: number) => {
    setQuantities(prev => ({
      ...prev,
      [productId]: quantity
    }));
  };

  const calculateBulkDiscount = (product: any, quantity: number) => {
    if (!product.bulk_discount_tiers) return 0;

    const applicableTier = product.bulk_discount_tiers
      .filter((tier: any) => quantity >= tier.min_quantity)
      .sort((a: any, b: any) => b.discount_percentage - a.discount_percentage)[0];

    return applicableTier ? applicableTier.discount_percentage : 0;
  };

  const getDiscountedPrice = (product: any, quantity: number) => {
    const discount = calculateBulkDiscount(product, quantity);
    return (product.b2b_price || product.base_price || 0) * (1 - discount / 100);
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <div className="h-48 bg-gray-200 rounded-t-lg"></div>
            <CardContent className="p-4">
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-3 bg-gray-200 rounded mb-4"></div>
              <div className="h-8 bg-gray-200 rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <p className="text-red-600">{error}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FunnelIcon className="h-5 w-5 mr-2" />
            Filtros e Busca
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="relative">
              <MagnifyingGlassIcon className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
              <Input
                placeholder="Buscar produtos..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger>
                <SelectValue placeholder="Categoria" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas as categorias</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={selectedSupplier} onValueChange={setSelectedSupplier}>
              <SelectTrigger>
                <SelectValue placeholder="Fornecedor" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os fornecedores</SelectItem>
                {suppliers.map(supplier => (
                  <SelectItem key={supplier} value={supplier}>
                    {supplier}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger>
                <SelectValue placeholder="Ordenar por" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name">Nome</SelectItem>
                <SelectItem value="price_low">Menor preço</SelectItem>
                <SelectItem value="price_high">Maior preço</SelectItem>
                <SelectItem value="rating">Avaliação</SelectItem>
                <SelectItem value="delivery">Entrega mais rápida</SelectItem>
              </SelectContent>
            </Select>
            
            <div className="text-sm text-gray-600 flex items-center">
              {filteredProducts.length} produtos encontrados
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Products Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredProducts.map((product: any) => {
          const quantity = quantities[product.id] || product.minimum_order_quantity || 1;
          const discountedPrice = getDiscountedPrice(product, quantity);
          const discount = calculateBulkDiscount(product, quantity);
          
          return (
            <Card key={product.id} className="overflow-hidden hover:shadow-lg transition-shadow">
              {/* Product Image */}
              <div className="relative h-48 bg-gray-100">
                {product.featured_image_url ? (
                  <img
                    src={product.featured_image_url}
                    alt={product.name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <BuildingOfficeIcon className="h-12 w-12 text-gray-400" />
                  </div>
                )}
                
                {/* B2B Badge */}
                <Badge className="absolute top-2 left-2 bg-indigo-600">
                  B2B
                </Badge>
                
                {/* Discount Badge */}
                {discount > 0 && (
                  <Badge className="absolute top-2 right-2 bg-green-600">
                    -{discount}%
                  </Badge>
                )}
              </div>

              <CardContent className="p-4">
                {/* Product Info */}
                <div className="space-y-2 mb-4">
                  <h3 className="font-semibold text-lg line-clamp-2">{product.name}</h3>
                  <p className="text-sm text-gray-600 line-clamp-2">{product.description}</p>
                  
                  {/* Supplier Info */}
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center">
                      <BuildingOfficeIcon className="h-4 w-4 mr-1 text-gray-400" />
                      <span className="text-gray-600">{product.supplier_name || 'N/A'}</span>
                    </div>
                    <div className="flex items-center">
                      <StarIcon className="h-4 w-4 mr-1 text-yellow-500" />
                      <span>{(product.supplier_rating || 0).toFixed(1)}</span>
                    </div>
                  </div>

                  {/* Delivery Info */}
                  <div className="flex items-center text-sm text-gray-600">
                    <TruckIcon className="h-4 w-4 mr-1" />
                    <span>Entrega em {product.estimated_delivery_days || 0} dias</span>
                  </div>
                </div>

                {/* Pricing */}
                <div className="space-y-2 mb-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Preço B2B:</span>
                    <div className="text-right">
                      {discount > 0 && (
                        <span className="text-sm text-gray-400 line-through">
                          {formatCurrency(product.b2b_price || product.base_price || 0)}
                        </span>
                      )}
                      <div className="font-bold text-lg text-green-600">
                        {formatCurrency(discountedPrice)}
                      </div>
                    </div>
                  </div>

                  <div className="text-xs text-gray-500">
                    Mín: {product.minimum_order_quantity || 1} un. | Estoque: {product.stock_quantity || 0}
                  </div>
                </div>

                {/* Quantity and Add to Cart */}
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <label className="text-sm font-medium">Qtd:</label>
                    <Input
                      type="number"
                      min={product.minimum_order_quantity || 1}
                      max={product.stock_quantity}
                      value={quantity}
                      onChange={(e) => handleQuantityChange(product.id, parseInt(e.target.value) || 1)}
                      className="w-20"
                    />
                  </div>
                  
                  <Button
                    onClick={() => handleAddToCart(product)}
                    disabled={cartLoading || product.stock_quantity === 0}
                    className="w-full"
                  >
                    <ShoppingCartIcon className="h-4 w-4 mr-2" />
                    {product.stock_quantity === 0 ? 'Sem Estoque' : 'Adicionar ao Carrinho'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {filteredProducts.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <BuildingOfficeIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Nenhum produto encontrado com os filtros selecionados.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
