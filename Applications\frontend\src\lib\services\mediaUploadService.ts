import apiClient from '@/lib/api/client';

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface MediaUploadResponse {
  id: string;
  filename: string;
  media_type: string;
  file_size: number;
  display_order: number;
  is_primary: boolean;
  file_url: string;
  thumbnail_url?: string;
  context_type?: string;
  context_id?: string;
  created_at: string;
}

export interface MediaListResponse {
  menu_item_id: string;
  media_count: number;
  media_items: MediaUploadResponse[];
}

export interface UploadApiResponse {
  success: boolean;
  message?: string;
  uploaded_files?: number;
  failed_files?: number;
  total_size_mb?: number;
  files?: Array<{
    success: boolean;
    upload_id?: string;
    filename?: string;
    final_size?: number;
    compression_ratio?: number;
    file_url?: string;
    thumbnail_url?: string;
    context_type?: string;
    context_id?: string;
    metadata?: any;
    error?: string;
  }>;
  uploads?: MediaUploadResponse[]; // Para compatibilidade com código existente
  error?: string;
}

export interface MediaUploadOptions {
  onProgress?: (progress: UploadProgress) => void;
  onSuccess?: (response: UploadApiResponse) => void;
  onError?: (error: Error) => void;
}

class MediaUploadService {
  /**
   * Upload multiple files to the media system for user context (temporary storage)
   */
  async uploadUserMedia(
    files: File[],
    options?: MediaUploadOptions
  ): Promise<UploadApiResponse> {
    try {
      // Validate files
      files.forEach(file => this.validateFile(file));

      // Upload files one by one to the media system
      const uploadResults: any[] = [];
      let uploadedFiles = 0;
      let failedFiles = 0;
      let totalSizeMb = 0;

      for (const file of files) {
        try {
          // Create FormData for single file
          const formData = new FormData();
          formData.append('file', file);
          formData.append('context_type', 'user');
          // No context_id for user uploads

          // Upload with progress tracking
                const response = await apiClient.post(
        `/modules/core/media/upload`,
            formData,
            {
              onUploadProgress: (progressEvent: { loaded: number; total?: number }) => {
                if (progressEvent.total && options?.onProgress) {
                  const progress: UploadProgress = {
                    loaded: progressEvent.loaded,
                    total: progressEvent.total,
                    percentage: Math.round((progressEvent.loaded * 100) / progressEvent.total),
                  };
                  options.onProgress(progress);
                }
              },
            }
          );

          // Verificar se response e response.data existem
          if (response && response.data && response.data.success) {
            uploadResults.push({
              success: true,
              upload_id: response.data.upload_id,
              filename: file.name,
              final_size: file.size,
              file_url: response.data.file_url,
              thumbnail_url: response.data.thumbnail_url,
              context_type: 'user',
              context_id: null
            });
            uploadedFiles++;
            totalSizeMb += file.size / (1024 * 1024);
          } else {
            throw new Error('Upload failed');
          }
        } catch (error) {
          uploadResults.push({
            success: false,
            filename: file.name,
            error: error instanceof Error ? error.message : 'Upload failed'
          });
          failedFiles++;
        }
      }

      // Transform response to maintain compatibility
      const transformedResponse: UploadApiResponse = {
        success: uploadedFiles > 0,
        uploaded_files: uploadedFiles,
        failed_files: failedFiles,
        total_size_mb: totalSizeMb,
        files: uploadResults
      };

      // Adicionar uploads para compatibilidade com código existente
      (transformedResponse as any).uploads = uploadResults.filter(f => f.success).map(file => ({
        id: file.upload_id || '',
        filename: file.filename || '',
        media_type: 'image',
        file_size: file.final_size || 0,
        display_order: 0,
        is_primary: false,
        file_url: file.file_url || '',
        thumbnail_url: file.thumbnail_url,
        context_type: file.context_type,
        context_id: file.context_id,
        created_at: new Date().toISOString()
      }));

      // Call success callback
      if (options?.onSuccess) {
        options.onSuccess(transformedResponse);
      }

      return transformedResponse;
    } catch (error) {
      console.error('Failed to upload user media:', error);
      if (options?.onError) {
        options.onError(error as Error);
      }
      throw error;
    }
  }

  /**
   * Upload files temporarily to tenant context (for new items before creation)
   */
  async uploadTempMedia(
    files: File[],
    options?: MediaUploadOptions
  ): Promise<UploadApiResponse> {
    try {
      // Validate files
      files.forEach(file => this.validateFile(file));

      // Upload files one by one to the media system (tenant context)
      const uploadResults: any[] = [];
      let uploadedFiles = 0;
      let failedFiles = 0;
      let totalSizeMb = 0;

      for (const file of files) {
        try {
          const formData = new FormData();
          formData.append('file', file);
          formData.append('context_type', 'tenant');

          // Get tenant ID from localStorage or headers
          const tenantId = localStorage.getItem('tenant_id') ||
                          document.cookie.split('; ').find(row => row.startsWith('tenant_id='))?.split('=')[1];
          if (tenantId) {
            formData.append('context_id', tenantId);
          }

          const response = await apiClient.post('/api/modules/core/media/upload', formData, {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
            onUploadProgress: (progressEvent) => {
              if (progressEvent.total) {
                const progress: UploadProgress = {
                  loaded: progressEvent.loaded,
                  total: progressEvent.total,
                  percentage: Math.round((progressEvent.loaded * 100) / progressEvent.total)
                };
                if (options?.onProgress) {
                  options.onProgress(progress);
                }
              }
            }
          });

          if (response.data.success) {
            uploadResults.push({
              filename: file.name,
              url: response.data.url,
              upload_id: response.data.upload_id,
              success: true
            });
            uploadedFiles++;
            totalSizeMb += file.size / (1024 * 1024);
          } else {
            uploadResults.push({
              filename: file.name,
              success: false,
              error: response.data.message || 'Upload failed'
            });
            failedFiles++;
          }
        } catch (error) {
          console.error(`Failed to upload ${file.name}:`, error);
          uploadResults.push({
            filename: file.name,
            success: false,
            error: error instanceof Error ? error.message : 'Upload failed'
          });
          failedFiles++;
        }
      }

      const transformedResponse: UploadApiResponse = {
        success: uploadedFiles > 0,
        message: `${uploadedFiles} arquivo(s) enviado(s) com sucesso${failedFiles > 0 ? `, ${failedFiles} falharam` : ''}`,
        uploads: uploadResults,
        uploaded_files: uploadedFiles,
        failed_files: failedFiles,
        total_size_mb: Math.round(totalSizeMb * 100) / 100
      };

      // Call success callback
      if (options?.onSuccess) {
        options.onSuccess(transformedResponse);
      }

      return transformedResponse;
    } catch (error) {
      console.error('Failed to upload temp media:', error);
      if (options?.onError) {
        options.onError(error as Error);
      }
      throw error;
    }
  }

  /**
   * Upload multiple files to the media system for a specific menu item
   */
  async uploadMenuItemMedia(
    menuItemId: string,
    files: File[],
    options?: MediaUploadOptions
  ): Promise<UploadApiResponse> {
    try {
      // Validate files
      files.forEach(file => this.validateFile(file));

      // Upload files one by one to the media system
      const uploadResults: any[] = [];
      let uploadedFiles = 0;
      let failedFiles = 0;
      let totalSizeMb = 0;

      for (const file of files) {
        try {
          // Create FormData for single file
          const formData = new FormData();
          formData.append('file', file);

          // Upload with progress tracking using menu item specific endpoint
          const response = await apiClient.post(
            `/modules/core/media/menu-items/${menuItemId}/upload`,
            formData,
            {
              onUploadProgress: (progressEvent) => {
                if (progressEvent.total && options?.onProgress) {
                  const progress: UploadProgress = {
                    loaded: progressEvent.loaded,
                    total: progressEvent.total,
                    percentage: Math.round((progressEvent.loaded * 100) / progressEvent.total),
                  };
                  options.onProgress(progress);
                }
              },
            }
          );

          // Verificar se response existe - response pode ser direto ou ter .data
          console.log('🔍 Upload Response Full:', response);
          console.log('🔍 Upload Response Data:', response?.data);
          console.log('🔍 Upload Response Type:', typeof response);

          // Handle both response.data and direct response formats
          const responseData = response?.data || response;
          console.log('🔍 Upload ResponseData:', responseData);
          console.log('🔍 Upload ResponseData Success:', responseData?.success);
          console.log('🔍 Upload ResponseData Type:', typeof responseData?.success);

          if (responseData) {
            // More flexible success check - API might return different success indicators
            const isSuccess = responseData.success === true ||
                             responseData.upload_id ||
                             responseData.file_url;

            console.log('🔍 Upload Success Check Result:', isSuccess);

            if (isSuccess) {
              console.log('🔍 Upload SUCCESS - Adding to results');
              uploadResults.push({
                success: true,
                upload_id: responseData.upload_id,
                filename: file.name,
                final_size: file.size,
                file_url: responseData.file_url,
                thumbnail_url: responseData.thumbnail_url,
                context_type: 'menu_item',
                context_id: menuItemId
              });
              uploadedFiles++;
              totalSizeMb += file.size / (1024 * 1024);
              console.log('🔍 Upload Files Count:', uploadedFiles);
            } else {
              console.log('🔍 Upload FAILED - Success check failed');
              throw new Error('Upload failed - no success indicator');
            }
          } else {
            console.log('🔍 Upload FAILED - No response data');
            throw new Error('Upload failed - no response data');
          }
        } catch (error) {
          console.log('🔍 Upload ERROR for file:', file.name, error);
          uploadResults.push({
            success: false,
            filename: file.name,
            error: error instanceof Error ? error.message : 'Upload failed'
          });
          failedFiles++;

          // Call error callback immediately for this file
          if (options?.onError) {
            options.onError(error instanceof Error ? error : new Error('Upload failed'));
          }
        }
      }

      // Transform response to maintain compatibility
      console.log('🔍 Final Upload Stats:', {
        uploadedFiles,
        failedFiles,
        totalSizeMb,
        uploadResults
      });

      const transformedResponse: UploadApiResponse = {
        success: uploadedFiles > 0,
        uploaded_files: uploadedFiles,
        failed_files: failedFiles,
        total_size_mb: totalSizeMb,
        files: uploadResults
      };

      console.log('🔍 Transformed Response:', transformedResponse);

      // Adicionar uploads para compatibilidade com código existente
      (transformedResponse as any).uploads = uploadResults.filter(f => f.success).map(file => ({
        id: file.upload_id || '',
        filename: file.filename || '',
        media_type: 'image',
        file_size: file.final_size || 0,
        display_order: 0,
        is_primary: false,
        file_url: file.file_url || '',
        thumbnail_url: file.thumbnail_url,
        context_type: file.context_type,
        context_id: file.context_id,
        created_at: new Date().toISOString()
      }));

      // Call success callback
      if (options?.onSuccess) {
        options.onSuccess(transformedResponse);
      }

      return transformedResponse;
    } catch (error) {
      const uploadError = error instanceof Error ? error : new Error('Upload failed');

      // Call error callback
      if (options?.onError) {
        options.onError(uploadError);
      }

      throw uploadError;
    }
  }

  /**
   * Clear cached data for a menu item
   */
  private clearMenuItemCache(menuItemId: string): void {
    try {
      // Clear localStorage cache
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.includes(`media_cache_`) && key.includes(menuItemId)) {
          localStorage.removeItem(key);
          console.log('🔍 Cleared cache key:', key);
        }
      });
    } catch (error) {
      console.warn('Failed to clear menu item cache:', error);
    }
  }

  /**
   * Get media files for a specific menu item
   */
  async getMenuItemMedia(menuItemId: string): Promise<MediaUploadResponse[]> {
    try {
      console.log('🔍 mediaUploadService.getMenuItemMedia - Starting for:', menuItemId);

      // Clear any cached data for this item to ensure fresh data
      this.clearMenuItemCache(menuItemId);

      // Use media system endpoint directly since FTP system was disabled in backend
      const response = await apiClient.get(
        `/modules/core/media/menu-items/${menuItemId}/media`
      );

      console.log('🔍 mediaUploadService.getMenuItemMedia - Raw response:', response);
      console.log('🔍 mediaUploadService.getMenuItemMedia - Response.data:', response?.data);
      console.log('🔍 mediaUploadService.getMenuItemMedia - Response type:', typeof response);
      console.log('🔍 mediaUploadService.getMenuItemMedia - Response status:', response?.status);
      console.log('🔍 mediaUploadService.getMenuItemMedia - Is response array?', Array.isArray(response?.data));

      // O apiClient retorna a response completa do axios, então os dados estão em response.data
      const responseData = response?.data;

      console.log('🔍 mediaUploadService.getMenuItemMedia - Using responseData:', responseData);
      console.log('🔍 mediaUploadService.getMenuItemMedia - ResponseData type:', typeof responseData);
      console.log('🔍 mediaUploadService.getMenuItemMedia - Is responseData array?', Array.isArray(responseData));

      // Verificar se temos dados válidos
      if (!responseData) {
        console.warn('🔍 mediaUploadService.getMenuItemMedia - No response data');
        return [];
      }

      // Verificar se é um array
      if (!Array.isArray(responseData)) {
        console.warn('🔍 mediaUploadService.getMenuItemMedia - Response data is not an array:', responseData);
        return [];
      }

      // Transform response to match expected format
      console.log('🔍 mediaUploadService.getMenuItemMedia - Transforming data, length:', responseData.length);
      const transformedData = responseData.map((item: any, index: number) => {
        console.log('🔍 mediaUploadService.getMenuItemMedia - Processing item:', item);
        console.log('🔍 Item ID for download URL:', item.id);
        console.log('🔍 Item filename:', item.filename);

        // Generate the correct download URL
        const downloadUrl = `/api/modules/core/media/download/${item.id}`;
        console.log('🔍 Generated download URL:', downloadUrl);
        console.log('🔍 Item structure:', JSON.stringify(item, null, 2));

        return {
          id: item.id,
          filename: item.filename,
          media_type: item.media_type,
          file_size: item.file_size,
          display_order: item.display_order || index,
          is_primary: item.is_primary || false, // Use real value from backend
          file_url: downloadUrl,
          thumbnail_url: item.thumbnail_path ? `${downloadUrl}?thumbnail=true` : undefined,
          context_type: item.context_type,
          context_id: item.context_id,
          created_at: item.created_at
        };
      });

      console.log('🔍 mediaUploadService.getMenuItemMedia - Final transformed data:', transformedData);
      return transformedData;
    } catch (error) {
      console.error('Failed to get menu item media:', error);
      // Retornar array vazio em caso de erro para não quebrar a interface
      return [];
    }
  }

  /**
   * Link temporary uploads to a menu item after creation
   */
  async linkUploadsToMenuItem(menuItemId: string, uploadIds: string[]): Promise<void> {
    try {
      for (const uploadId of uploadIds) {
        await apiClient.post(`/modules/core/media/menu-items/${menuItemId}/link`, {
          upload_id: uploadId
        });
      }
    } catch (error) {
      console.error('Failed to link uploads to menu item:', error);
      throw error;
    }
  }

  /**
   * Delete a media file
   */
  async deleteMedia(menuItemId: string, uploadId: string): Promise<void> {
    try {
      await apiClient.delete(`/modules/core/media/uploads/${uploadId}`);
    } catch (error) {
      console.error('Failed to delete media:', error);
      throw error;
    }
  }

  /**
   * Update media order for a menu item
   */
  async updateMediaOrder(menuItemId: string, mediaOrder: { id: string; display_order: number }[]): Promise<void> {
    try {
      await apiClient.put(`/modules/core/media/menu-items/${menuItemId}/order`, {
        media_order: mediaOrder
      });
    } catch (error) {
      console.error('Failed to update media order:', error);
      throw error;
    }
  }

  /**
   * Set primary/default media for a menu item
   */
  async setPrimaryMedia(menuItemId: string, uploadId: string): Promise<void> {
    try {
      await apiClient.put(`/modules/core/media/menu-items/${menuItemId}/primary`, {
        upload_id: uploadId
      });
    } catch (error) {
      console.error('Failed to set primary media:', error);
      throw error;
    }
  }

  /**
   * Get media file URL for display using upload ID
   */
  getMediaUrl(uploadId: string): string {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';
    return `${baseUrl}/modules/core/media/download/${uploadId}`;
  }

  /**
   * Get thumbnail URL for display using upload ID
   */
  getThumbnailUrl(uploadId: string): string {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';
    return `${baseUrl}/modules/core/media/download/${uploadId}?thumbnail=true`;
  }

  /**
   * Get compressed media URL for display using upload ID
   */
  getCompressedUrl(uploadId: string): string {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';
    return `${baseUrl}/modules/core/media/download/${uploadId}?compressed=true`;
  }

  /**
   * Validate file before upload
   */
  private validateFile(file: File): void {
    // Check file size (max 50MB for videos, 10MB for images)
    const isVideo = file.type.startsWith('video/');
    const maxSize = isVideo ? 50 * 1024 * 1024 : 10 * 1024 * 1024; // 50MB for videos, 10MB for images

    if (file.size > maxSize) {
      const maxSizeText = isVideo ? '50MB' : '10MB';
      throw new Error(`File size exceeds ${maxSizeText} limit`);
    }

    // Check file type (images and videos)
    const allowedImageTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml'
    ];

    const allowedVideoTypes = [
      'video/mp4',
      'video/webm',
      'video/ogg',
      'video/avi',
      'video/mov',
      'video/wmv'
    ];

    const allowedTypes = [...allowedImageTypes, ...allowedVideoTypes];

    if (!allowedTypes.includes(file.type)) {
      throw new Error('Only image files (JPEG, PNG, GIF, WebP, SVG) and video files (MP4, WebM, OGG, AVI, MOV, WMV) are allowed');
    }
  }

  /**
   * Create a preview URL for a file before upload
   */
  createPreviewUrl(file: File): string {
    return URL.createObjectURL(file);
  }

  /**
   * Revoke a preview URL to free memory
   */
  revokePreviewUrl(url: string): void {
    URL.revokeObjectURL(url);
  }

  /**
   * Transform FTP system response to match expected format
   */
  private transformFTPResponse(ftpData: any, menuItemId: string): MediaUploadResponse[] {
    console.log('🔍 mediaUploadService.transformFTPResponse - FTP data:', ftpData);

    if (!ftpData || !Array.isArray(ftpData)) {
      console.log('🔍 mediaUploadService.transformFTPResponse - Invalid FTP data');
      return [];
    }

    return ftpData.map((item: any, index: number) => {
      console.log('🔍 mediaUploadService.transformFTPResponse - Processing FTP item:', item);

      // FTP system uses different URL structure
      const ftpUrl = `/api/modules/core/ftp_system/files/${item.folder_uuid}/${item.filename}`;
      console.log('🔍 mediaUploadService.transformFTPResponse - Generated FTP URL:', ftpUrl);

      return {
        id: item.id,
        filename: item.filename,
        media_type: item.media_type || 'image',
        file_size: item.file_size,
        display_order: index,
        is_primary: index === 0,
        file_url: ftpUrl,
        thumbnail_url: undefined, // FTP system doesn't have thumbnails
        context_type: 'menu_item',
        context_id: menuItemId,
        created_at: item.created_at
      };
    });
  }
}

// Export singleton instance
export const mediaUploadService = new MediaUploadService();
export default mediaUploadService;
