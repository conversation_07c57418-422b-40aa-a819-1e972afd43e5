"""
B2B Authorization Schemas
========================

Schemas Pydantic para o sistema de autorização B2B.
"""

import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any
from decimal import Decimal
from enum import Enum

from pydantic import BaseModel, Field, EmailStr, validator


class AuthorizationStatus(str, Enum):
    """Status do processo de autorização."""
    PENDING = "PENDING"
    UNDER_REVIEW = "UNDER_REVIEW"
    APPROVED = "APPROVED"
    REJECTED = "REJECTED"
    SUSPENDED = "SUSPENDED"
    EXPIRED = "EXPIRED"


class DocumentType(str, Enum):
    """Tipos de documentos para verificação."""
    BUSINESS_LICENSE = "BUSINESS_LICENSE"
    TAX_CERTIFICATE = "TAX_CERTIFICATE"
    BANK_STATEMENT = "BANK_STATEMENT"
    IDENTITY_DOCUMENT = "IDENTITY_DOCUMENT"
    PROOF_OF_ADDRESS = "PROOF_OF_ADDRESS"
    FINANCIAL_STATEMENT = "FINANCIAL_STATEMENT"
    TRADE_REFERENCE = "TRADE_REFERENCE"
    INSURANCE_CERTIFICATE = "INSURANCE_CERTIFICATE"


class ApprovalType(str, Enum):
    """Tipo de aprovação."""
    AUTOMATIC = "AUTOMATIC"
    MANUAL = "MANUAL"
    CONDITIONAL = "CONDITIONAL"


class EntityType(str, Enum):
    """Tipo de entidade B2B."""
    TCOSTUMER = "tcostumer"
    TVENDOR = "tvendor"


# Base Schemas
class AddressSchema(BaseModel):
    """Schema para endereços."""
    street: str = Field(..., max_length=200)
    number: str = Field(..., max_length=20)
    complement: Optional[str] = Field(None, max_length=100)
    neighborhood: str = Field(..., max_length=100)
    city: str = Field(..., max_length=100)
    state: str = Field(..., max_length=50)
    country: str = Field(..., max_length=50)
    postal_code: str = Field(..., max_length=20)


# Request Schemas
class B2BAuthorizationRequestCreate(BaseModel):
    """Schema para criação de solicitação de autorização."""
    entity_type: EntityType
    company_name: str = Field(..., max_length=200)
    tax_id: str = Field(..., max_length=50)
    business_type: Optional[str] = Field(None, max_length=100)
    
    contact_person: str = Field(..., max_length=200)
    contact_email: EmailStr
    contact_phone: Optional[str] = Field(None, max_length=50)
    
    business_address: AddressSchema
    billing_address: Optional[AddressSchema] = None
    
    # Informações financeiras solicitadas
    requested_credit_limit: Optional[Decimal] = Field(None, ge=0, le=*********.99)
    requested_payment_terms: Optional[str] = Field(None, max_length=50)
    
    # Para TVendorSupplier
    requested_commission_rate: Optional[Decimal] = Field(None, ge=0, le=100)
    supplier_type: Optional[str] = Field(None, max_length=50)
    
    additional_info: Optional[Dict[str, Any]] = None
    
    @validator('billing_address', pre=True, always=True)
    def set_billing_address(cls, v, values):
        """Se billing_address não fornecido, usa business_address."""
        if v is None and 'business_address' in values:
            return values['business_address']
        return v


class B2BAuthorizationRequestUpdate(BaseModel):
    """Schema para atualização de solicitação."""
    company_name: Optional[str] = Field(None, max_length=200)
    business_type: Optional[str] = Field(None, max_length=100)
    contact_person: Optional[str] = Field(None, max_length=200)
    contact_email: Optional[EmailStr] = None
    contact_phone: Optional[str] = Field(None, max_length=50)
    business_address: Optional[AddressSchema] = None
    billing_address: Optional[AddressSchema] = None
    requested_credit_limit: Optional[Decimal] = Field(None, ge=0, le=*********.99)
    requested_payment_terms: Optional[str] = Field(None, max_length=50)
    requested_commission_rate: Optional[Decimal] = Field(None, ge=0, le=100)
    supplier_type: Optional[str] = Field(None, max_length=50)
    additional_info: Optional[Dict[str, Any]] = None


class B2BAuthorizationApproval(BaseModel):
    """Schema para aprovação de solicitação."""
    approved_credit_limit: Optional[Decimal] = Field(None, ge=0, le=*********.99)
    approved_payment_terms: Optional[str] = Field(None, max_length=50)
    approved_commission_rate: Optional[Decimal] = Field(None, ge=0, le=100)
    internal_notes: Optional[str] = None


class B2BAuthorizationRejection(BaseModel):
    """Schema para rejeição de solicitação."""
    rejection_reason: str = Field(..., max_length=500)
    rejection_notes: Optional[str] = None


# Document Schemas
class B2BAuthorizationDocumentUpload(BaseModel):
    """Schema para upload de documento."""
    document_type: DocumentType
    document_name: str = Field(..., max_length=255)


class B2BAuthorizationDocumentVerification(BaseModel):
    """Schema para verificação de documento."""
    is_verified: bool
    verification_notes: Optional[str] = None


# Response Schemas
class B2BAuthorizationDocumentResponse(BaseModel):
    """Schema de resposta para documento."""
    id: uuid.UUID
    document_type: DocumentType
    document_name: str
    original_filename: str
    file_size: int
    file_type: str
    is_verified: bool
    verified_at: Optional[datetime] = None
    verification_notes: Optional[str] = None
    uploaded_at: datetime
    
    class Config:
        from_attributes = True


class B2BAuthorizationHistoryResponse(BaseModel):
    """Schema de resposta para histórico."""
    id: uuid.UUID
    action: str
    previous_status: Optional[str] = None
    new_status: Optional[str] = None
    performed_by: uuid.UUID
    performed_at: datetime
    notes: Optional[str] = None
    changes: Optional[Dict[str, Any]] = None
    
    class Config:
        from_attributes = True


class B2BAuthorizationRequestResponse(BaseModel):
    """Schema de resposta para solicitação de autorização."""
    id: uuid.UUID
    tenant_id: uuid.UUID
    user_id: uuid.UUID
    entity_type: EntityType
    entity_id: Optional[uuid.UUID] = None
    
    status: AuthorizationStatus
    approval_type: ApprovalType
    
    company_name: str
    tax_id: str
    business_type: Optional[str] = None
    
    contact_person: str
    contact_email: str
    contact_phone: Optional[str] = None
    
    business_address: Optional[Dict[str, Any]] = None
    billing_address: Optional[Dict[str, Any]] = None
    
    requested_credit_limit: Optional[Decimal] = None
    requested_payment_terms: Optional[str] = None
    requested_commission_rate: Optional[Decimal] = None
    supplier_type: Optional[str] = None
    
    approved_by: Optional[uuid.UUID] = None
    approved_at: Optional[datetime] = None
    approved_credit_limit: Optional[Decimal] = None
    approved_payment_terms: Optional[str] = None
    approved_commission_rate: Optional[Decimal] = None
    
    rejection_reason: Optional[str] = None
    rejection_notes: Optional[str] = None
    
    created_at: datetime
    updated_at: datetime
    expires_at: Optional[datetime] = None
    
    additional_info: Optional[Dict[str, Any]] = None
    internal_notes: Optional[str] = None
    
    # Relacionamentos
    documents: List[B2BAuthorizationDocumentResponse] = []
    history: List[B2BAuthorizationHistoryResponse] = []
    
    class Config:
        from_attributes = True


class B2BAuthorizationRequestListResponse(BaseModel):
    """Schema de resposta para listagem de solicitações."""
    items: List[B2BAuthorizationRequestResponse]
    total: int
    page: int
    per_page: int
    pages: int


# Settings Schemas
class B2BAuthorizationSettingsUpdate(BaseModel):
    """Schema para atualização de configurações."""
    auto_approve_tcostumer: Optional[bool] = None
    auto_approve_tvendor: Optional[bool] = None
    max_auto_credit_limit: Optional[Decimal] = Field(None, ge=0, le=*********.99)
    max_auto_commission_rate: Optional[Decimal] = Field(None, ge=0, le=100)
    request_expiry_days: Optional[int] = Field(None, ge=1, le=365)
    document_retention_days: Optional[int] = Field(None, ge=30, le=3650)
    notify_on_new_request: Optional[bool] = None
    notify_on_approval: Optional[bool] = None
    notify_on_rejection: Optional[bool] = None
    notification_emails: Optional[List[EmailStr]] = None
    required_documents_tcostumer: Optional[List[DocumentType]] = None
    required_documents_tvendor: Optional[List[DocumentType]] = None


class B2BAuthorizationSettingsResponse(BaseModel):
    """Schema de resposta para configurações."""
    id: uuid.UUID
    tenant_id: uuid.UUID
    auto_approve_tcostumer: bool
    auto_approve_tvendor: bool
    max_auto_credit_limit: Decimal
    max_auto_commission_rate: Decimal
    request_expiry_days: int
    document_retention_days: int
    notify_on_new_request: bool
    notify_on_approval: bool
    notify_on_rejection: bool
    notification_emails: Optional[List[str]] = None
    required_documents_tcostumer: Optional[List[str]] = None
    required_documents_tvendor: Optional[List[str]] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Statistics Schemas
class B2BAuthorizationStats(BaseModel):
    """Schema para estatísticas de autorização."""
    total_requests: int
    pending_requests: int
    approved_requests: int
    rejected_requests: int
    expired_requests: int
    
    # Por tipo de entidade
    tcostumer_requests: int
    tvendor_requests: int
    
    # Tempos médios
    avg_approval_time_hours: Optional[float] = None
    avg_document_verification_time_hours: Optional[float] = None
    
    # Aprovações automáticas vs manuais
    automatic_approvals: int
    manual_approvals: int
    
    # Documentos
    total_documents: int
    verified_documents: int
    pending_verification: int


# Filter Schemas
class B2BAuthorizationRequestFilter(BaseModel):
    """Schema para filtros de busca."""
    status: Optional[List[AuthorizationStatus]] = None
    entity_type: Optional[List[EntityType]] = None
    approval_type: Optional[List[ApprovalType]] = None
    company_name: Optional[str] = None
    tax_id: Optional[str] = None
    contact_email: Optional[str] = None
    created_from: Optional[datetime] = None
    created_to: Optional[datetime] = None
    approved_from: Optional[datetime] = None
    approved_to: Optional[datetime] = None
    expires_from: Optional[datetime] = None
    expires_to: Optional[datetime] = None
    has_documents: Optional[bool] = None
    is_expired: Optional[bool] = None
