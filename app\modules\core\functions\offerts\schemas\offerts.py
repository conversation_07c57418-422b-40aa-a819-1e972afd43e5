import uuid
from pydantic import BaseModel, validator
from typing import Optional, List
from datetime import datetime
from app.modules.core.functions.offerts.models.offerts import DiscountType

# --- DiscountCoupon Schemas ---
class DiscountCouponBase(BaseModel):
    code: str
    description: Optional[str] = None
    discount_type: DiscountType
    value: float
    max_uses: Optional[int] = None
    max_uses_per_user: Optional[int] = None
    valid_from: Optional[datetime] = None
    valid_until: Optional[datetime] = None
    is_active: bool = True
    tenant_id: Optional[uuid.UUID] = None

    @validator('value')
    def validate_value(cls, v, values):
        if 'discount_type' in values:
            if values['discount_type'] == DiscountType.PERCENTAGE and not (0 <= v <= 100):
                raise ValueError('Percentage value must be between 0 and 100')
            elif values['discount_type'] == DiscountType.FIXED_AMOUNT and v <= 0:
                raise ValueError('Fixed amount value must be positive')
        return v

class DiscountCouponCreate(DiscountCouponBase):
    pass

class DiscountCouponUpdate(BaseModel):
    description: Optional[str] = None
    discount_type: Optional[DiscountType] = None
    value: Optional[float] = None
    max_uses: Optional[int] = None
    max_uses_per_user: Optional[int] = None
    valid_from: Optional[datetime] = None
    valid_until: Optional[datetime] = None
    is_active: Optional[bool] = None
    tenant_id: Optional[uuid.UUID] = None # Allow updating tenant_id or making it global

    @validator('value')
    def validate_value_update(cls, v, values):
        # This validation might be tricky if discount_type is not also being updated
        # For simplicity, we might require discount_type if value is updated, or fetch current type
        if v is not None and 'discount_type' in values and values['discount_type'] is not None:
            if values['discount_type'] == DiscountType.PERCENTAGE and not (0 <= v <= 100):
                raise ValueError('Percentage value must be between 0 and 100')
            elif values['discount_type'] == DiscountType.FIXED_AMOUNT and v <= 0:
                raise ValueError('Fixed amount value must be positive')
        return v

class DiscountCoupon(DiscountCouponBase):
    id: int
    uses_count: int
    valid_from: datetime # Make non-optional for response model

    class Config:
        from_attributes = True

# --- AppliedCoupon Schemas ---
class AppliedCouponBase(BaseModel):
    coupon_id: int
    order_id: uuid.UUID
    user_id: uuid.UUID
    discount_amount: float

class AppliedCouponCreate(AppliedCouponBase):
    pass

class AppliedCoupon(AppliedCouponBase):
    id: int
    applied_at: datetime

    class Config:
        from_attributes = True

# --- Offer Schemas ---
class OfferBase(BaseModel):
    """Base schema for an offer."""
    title: str
    description: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    is_active: bool = True

    class Config:
        from_attributes = True

class OfferRead(OfferBase):
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class OfferCreate(OfferBase):
    pass

class OfferUpdate(OfferBase):
    """Schema for updating an offer."""
    pass