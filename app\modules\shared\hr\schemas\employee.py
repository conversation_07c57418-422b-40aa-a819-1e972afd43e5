from typing import Optional, Dict, Any
import uuid
from datetime import date
from pydantic import BaseModel, ConfigDict

from app.modules.shared.hr.core.models.employee import EmploymentStatus, EmploymentType

# Base Employee Schema


class EmployeeBase(BaseModel):
    """Base schema for Employee."""

    employee_code: Optional[str] = None
    job_title: str
    department: Optional[str] = None
    employment_status: EmploymentStatus = EmploymentStatus.ACTIVE
    employment_type: EmploymentType = EmploymentType.FULL_TIME
    hire_date: date
    termination_date: Optional[date] = None
    work_email: Optional[str] = None
    work_phone: Optional[str] = None
    emergency_contact_name: Optional[str] = None
    emergency_contact_phone: Optional[str] = None
    emergency_contact_relationship: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


# Schema for creating a new employee


class EmployeeCreate(EmployeeBase):
    """Schema for creating a new Employee."""

    user_tenant_association_id: uuid.UUID


# Schema for updating an employee


class EmployeeUpdate(BaseModel):
    """Schema for updating an Employee."""

    employee_code: Optional[str] = None
    job_title: Optional[str] = None
    department: Optional[str] = None
    employment_status: Optional[EmploymentStatus] = None
    employment_type: Optional[EmploymentType] = None
    hire_date: Optional[date] = None
    termination_date: Optional[date] = None
    work_email: Optional[str] = None
    work_phone: Optional[str] = None
    emergency_contact_name: Optional[str] = None
    emergency_contact_phone: Optional[str] = None
    emergency_contact_relationship: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


# Schema for reading an employee


class EmployeeRead(EmployeeBase):
    """Schema for reading an Employee."""

    id: uuid.UUID
    user_tenant_association_id: uuid.UUID
    tenant_id: uuid.UUID

    model_config = ConfigDict(from_attributes=True)


# Schema for employee with associated user information


class EmployeeWithUser(EmployeeRead):
    """Schema for Employee with associated User data."""

    user_id: uuid.UUID
    user_full_name: str
    user_email: str

    model_config = ConfigDict(from_attributes=True)
