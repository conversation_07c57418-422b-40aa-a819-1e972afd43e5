'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import SupplierHeader from './components/SupplierHeader';
import SupplierStats from './components/SupplierStats';
import SupplierFilters from './components/SupplierFilters';
import SupplierItemsList from './components/SupplierItemsList';
import { useSupplierData } from './hooks/useSupplierData';
import { PriorityFilter } from './types/supplier';

interface ShoppingListItem {
  id: string;
  name: string;
  quantity: number;
  unit: string | null;
  priority: 'low' | 'medium' | 'high';
  estimated_price: string | null;
  purchased: boolean;
  notes: string | null;
  inventory_item_id: string | null;
  shopping_list_id: string;
  created_at: string;
  updated_at: string;
}

interface ShoppingList {
  id: string;
  name: string;
  description: string | null;
  is_active: boolean;
  tenant_id: string;
  created_at: string;
  updated_at: string;
  items: ShoppingListItem[];
}

// Temporary mock user and API client until proper integration
const mockUser = {
  email: '<EMAIL>',
  full_name: 'Supplier User'
};

const mockApiClient = {
  get: async (url: string) => {
    // Mock API responses for development
    if (url === '/modules/shopping-list/lists') {
      return { data: [] };
    }
    return { data: { items: [] } };
  }
};

export default function SupplierPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPriority, setSelectedPriority] = useState<PriorityFilter>('all');

  // Using custom hook for data management
  const {
    shoppingLists,
    tenants,
    loading,
    selectedTenantId,
    setSelectedTenantId
  } = useSupplierData();

  // Using mock user for now - will be replaced with proper auth integration
  const user = mockUser;



  const handleLogout = () => {
    // Mock logout - will be replaced with proper auth integration
    router.push('/auth');
  };

  const filteredItems = shoppingLists.flatMap(list => 
    list.items.filter(item => {
      const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesPriority = selectedPriority === 'all' || item.priority === selectedPriority;
      const notPurchased = !item.purchased; // Mostrar apenas itens não comprados
      return matchesSearch && matchesPriority && notPurchased;
    })
  );

  const totalItems = filteredItems.length;
  const highPriorityItems = filteredItems.filter(item => item.priority === 'high').length;
  const estimatedTotal = filteredItems.reduce((sum, item) => {
    const price = parseFloat(item.estimated_price || '0');
    return sum + (price * item.quantity);
  }, 0);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <SupplierHeader
        userName={user.full_name}
        userEmail={user.email}
        onLogout={handleLogout}
        tenants={tenants}
        selectedTenantId={selectedTenantId}
        onTenantChange={setSelectedTenantId}
      />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <SupplierStats
          totalItems={totalItems}
          highPriorityItems={highPriorityItems}
          estimatedTotal={estimatedTotal}
        />

        <SupplierFilters
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          selectedPriority={selectedPriority}
          onPriorityChange={(priority: string) => setSelectedPriority(priority as PriorityFilter)}
        />

        <SupplierItemsList
          items={filteredItems}
          loading={loading}
        />
      </main>
    </div>
  );
}
