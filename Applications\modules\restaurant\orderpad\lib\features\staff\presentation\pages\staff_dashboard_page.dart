import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/providers/auth_provider.dart';
import '../../../../core/providers/orders_provider.dart';
import '../../../../core/providers/tables_provider.dart';
import '../../../../core/router/app_router.dart';
import '../widgets/dashboard_stats_card.dart';
import '../widgets/quick_action_card.dart';
import '../widgets/recent_orders_list.dart';
import '../widgets/table_status_overview.dart';

class StaffDashboardPage extends ConsumerStatefulWidget {
  const StaffDashboardPage({super.key});

  @override
  ConsumerState<StaffDashboardPage> createState() => _StaffDashboardPageState();
}

class _StaffDashboardPageState extends ConsumerState<StaffDashboardPage>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadData();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  void _loadData() {
    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(ordersProvider.notifier).loadOrders();
      ref.read(tablesProvider.notifier).loadTables();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final user = ref.watch(currentUserProvider);
    final ordersState = ref.watch(ordersProvider);
    final tablesState = ref.watch(tablesProvider);
    
    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: _buildAppBar(theme, user),
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: RefreshIndicator(
                onRefresh: _handleRefresh,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Welcome section
                      _buildWelcomeSection(theme, user),
                      
                      const SizedBox(height: 24),
                      
                      // Stats cards
                      _buildStatsSection(ordersState, tablesState),
                      
                      const SizedBox(height: 24),
                      
                      // Quick actions
                      _buildQuickActionsSection(theme),
                      
                      const SizedBox(height: 24),
                      
                      // Table status overview
                      _buildTableStatusSection(),
                      
                      const SizedBox(height: 24),
                      
                      // Recent orders
                      _buildRecentOrdersSection(),
                      
                      const SizedBox(height: 80), // Bottom padding for FAB
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
      floatingActionButton: _buildFloatingActionButton(theme),
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeData theme, user) {
    return AppBar(
      backgroundColor: theme.colorScheme.primary,
      foregroundColor: Colors.white,
      elevation: 0,
      title: Row(
        children: [
          const Icon(
            Icons.restaurant_menu,
            color: Colors.white,
            size: 28,
          ),
          const SizedBox(width: 12),
          Text(
            'OrderPad',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
      actions: [
        // Notifications
        IconButton(
          onPressed: () {
            // TODO: Show notifications
          },
          icon: Stack(
            children: [
              const Icon(Icons.notifications_outlined),
              Positioned(
                right: 0,
                top: 0,
                child: Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.error,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ],
          ),
        ),
        
        // Profile menu
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          icon: CircleAvatar(
            radius: 16,
            backgroundColor: Colors.white.withValues(alpha: 0.2),
            child: Text(
              user?.name.substring(0, 1).toUpperCase() ?? 'U',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'profile',
              child: Row(
                children: [
                  Icon(Icons.person_outline),
                  SizedBox(width: 12),
                  Text('Perfil'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'settings',
              child: Row(
                children: [
                  Icon(Icons.settings_outlined),
                  SizedBox(width: 12),
                  Text('Configurações'),
                ],
              ),
            ),
            const PopupMenuDivider(),
            PopupMenuItem(
              value: 'logout',
              child: Row(
                children: [
                  Icon(Icons.logout, color: theme.colorScheme.error),
                  const SizedBox(width: 12),
                  Text(
                    'Sair',
                    style: TextStyle(color: theme.colorScheme.error),
                  ),
                ],
              ),
            ),
          ],
        ),
        
        const SizedBox(width: 8),
      ],
    );
  }

  Widget _buildWelcomeSection(ThemeData theme, user) {
    final now = DateTime.now();
    final hour = now.hour;
    String greeting;
    
    if (hour < 12) {
      greeting = 'Bom dia';
    } else if (hour < 18) {
      greeting = 'Boa tarde';
    } else {
      greeting = 'Boa noite';
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.primary.withValues(alpha: 0.1),
            theme.colorScheme.secondary.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.primary.withValues(alpha: 0.1),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '$greeting, ${user?.name ?? 'Usuário'}!',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Pronto para mais um dia de trabalho?',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
          Icon(
            _getGreetingIcon(hour),
            size: 48,
            color: theme.colorScheme.primary.withValues(alpha: 0.7),
          ),
        ],
      ),
    );
  }

  IconData _getGreetingIcon(int hour) {
    if (hour < 12) {
      return Icons.wb_sunny;
    } else if (hour < 18) {
      return Icons.wb_cloudy;
    } else {
      return Icons.nightlight_round;
    }
  }

  Widget _buildStatsSection(ordersState, tablesState) {
    final pendingOrders = ordersState.pendingOrders.length;
    final preparingOrders = ordersState.preparingOrders.length;

    return Row(
      children: [
        Expanded(
          child: DashboardStatsCard(
            title: 'Pedidos Pendentes',
            value: pendingOrders.toString(),
            icon: Icons.pending_actions,
            color: Colors.orange,
            onTap: () => context.go(AppRoutes.orders),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: DashboardStatsCard(
            title: 'Em Preparo',
            value: preparingOrders.toString(),
            icon: Icons.restaurant,
            color: Colors.blue,
            onTap: () => context.go(AppRoutes.orders),
          ),
        ),
      ],
    );
  }

  Widget _buildQuickActionsSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Ações Rápidas',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          mainAxisSpacing: 12,
          crossAxisSpacing: 12,
          childAspectRatio: 1.5,
          children: [
            QuickActionCard(
              title: 'Novo Pedido',
              icon: Icons.add_shopping_cart,
              color: theme.colorScheme.primary,
              onTap: () => context.go(AppRoutes.createOrder),
            ),
            QuickActionCard(
              title: 'Ver Mesas',
              icon: Icons.table_restaurant,
              color: Colors.green,
              onTap: () => context.go(AppRoutes.tables),
            ),
            QuickActionCard(
              title: 'Pedidos',
              icon: Icons.receipt_long,
              color: Colors.blue,
              onTap: () => context.go(AppRoutes.orders),
            ),
            QuickActionCard(
              title: 'Delivery',
              icon: Icons.delivery_dining,
              color: Colors.purple,
              onTap: () => context.go(AppRoutes.delivery),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTableStatusSection() {
    return const TableStatusOverview();
  }

  Widget _buildRecentOrdersSection() {
    return const RecentOrdersList();
  }

  Widget _buildFloatingActionButton(ThemeData theme) {
    return FloatingActionButton.extended(
      onPressed: () => context.go(AppRoutes.createOrder),
      backgroundColor: theme.colorScheme.primary,
      foregroundColor: Colors.white,
      icon: const Icon(Icons.add),
      label: const Text('Novo Pedido'),
    );
  }

  Future<void> _handleRefresh() async {
    await Future.wait([
      ref.read(ordersProvider.notifier).loadOrders(),
      ref.read(tablesProvider.notifier).loadTables(),
    ]);
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'profile':
        // TODO: Navigate to profile page
        break;
      case 'settings':
        // TODO: Navigate to settings page
        break;
      case 'logout':
        _handleLogout();
        break;
    }
  }

  void _handleLogout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmar Saída'),
        content: const Text('Tem certeza que deseja sair?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(authProvider.notifier).logout();
              context.go(AppRoutes.login);
            },
            child: const Text('Sair'),
          ),
        ],
      ),
    );
  }
}