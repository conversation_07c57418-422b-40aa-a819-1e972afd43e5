"""
Service for managing translation changes.
"""

import logging
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, or_

from app.modules.core.i18n.models.translation_change import TranslationChange, ChangeType
from app.modules.core.i18n.models.language import Language
from app.modules.core.i18n.models.translation import Translation
from app.modules.core.i18n.schemas import translation_change as schemas
from app.modules.core.i18n.services.language_service import LanguageService

# Set up logger
logger = logging.getLogger(__name__)


class TranslationChangeService:
    """Service for managing translation changes."""

    async def record_change(
        self,
        db: AsyncSession,
        key: str,
        sector: str,
        language_id: str,
        change_type: ChangeType,
        new_text: Optional[str] = None,
        previous_text: Optional[str] = None,
        changed_by_id: Optional[str] = None,
        version_code: str = None,
    ) -> TranslationChange:
        """
        Record a translation change.

        Args:
            db: Database session
            key: Translation key
            sector: Sector/namespace
            language_id: Language ID
            change_type: Type of change (added, updated, deleted)
            new_text: New text value (None if deleted)
            previous_text: Previous text value (None if added)
            changed_by_id: ID of the user who made the change
            version_code: Version code after this change

        Returns:
            The created TranslationChange object
        """
        # If no version code is provided, get the current one from the language
        if not version_code:
            language_result = await db.execute(select(Language).where(Language.id == language_id))
            language = language_result.scalar_one_or_none()
            if not language:
                raise ValueError(f"Language with ID {language_id} not found")
            version_code = language.version_code

        # Create the change record
        change = TranslationChange(
            key=key,
            sector=sector,
            language_id=language_id,
            change_type=change_type,
            new_text=new_text,
            previous_text=previous_text,
            changed_by_id=changed_by_id,
            version_code=version_code,
        )

        db.add(change)
        await db.flush()
        await db.refresh(change)

        return change

    async def get_changes_since_version(
        self,
        db: AsyncSession,
        language_code: str,
        since_version: str,
        sector: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Get all translation changes since a specific version.

        Args:
            db: Database session
            language_code: Language code
            since_version: Get changes since this version
            sector: Optional sector to filter by

        Returns:
            Dictionary with language changes information
        """
        # Get the language
        language_service = LanguageService()
        language = await language_service.get_language_by_code(db, language_code)

        if not language:
            raise ValueError(f"Language with code {language_code} not found")

        # If the versions are the same, no changes
        if language.version_code == since_version:
            return {
                "language_code": language_code,
                "current_version_code": language.version_code,
                "has_changes": False,
                "sectors": [],
            }

        # Build the query
        query = select(TranslationChange).where(
            and_(
                TranslationChange.language_id == language.id,
                TranslationChange.version_code != since_version,
            )
        )

        # Add sector filter if provided
        if sector:
            query = query.where(TranslationChange.sector == sector)

        # Execute the query
        result = await db.execute(query)
        changes = result.scalars().all()

        # Group changes by sector
        sectors_dict = {}
        for change in changes:
            if change.sector not in sectors_dict:
                sectors_dict[change.sector] = []

            sectors_dict[change.sector].append(change)

        # Format the response
        sectors_list = []
        for sector_name, sector_changes in sectors_dict.items():
            sectors_list.append({"sector": sector_name, "changes": sector_changes})

        return {
            "language_code": language_code,
            "current_version_code": language.version_code,
            "has_changes": len(changes) > 0,
            "sectors": sectors_list,
        }

    async def get_translations_by_sector(
        self,
        db: AsyncSession,
        language_code: str,
        sector: Optional[str] = None,
    ) -> List[Dict[str, Any]]:
        """
        Get all translations for a language, grouped by sector.

        Args:
            db: Database session
            language_code: Language code
            sector: Optional sector to filter by

        Returns:
            List of dictionaries with sector and translations
        """
        # Get the language
        language_service = LanguageService()
        language = await language_service.get_language_by_code(db, language_code)

        if not language:
            raise ValueError(f"Language with code {language_code} not found")

        # Build the query
        query = select(Translation).where(Translation.language_id == language.id)

        # Add sector filter if provided
        if sector:
            query = query.where(Translation.sector == sector)

        # Execute the query
        result = await db.execute(query)
        translations = result.scalars().all()

        # Group translations by sector
        sectors_dict = {}
        for translation in translations:
            if translation.sector not in sectors_dict:
                sectors_dict[translation.sector] = []

            sectors_dict[translation.sector].append(translation)

        # Format the response
        sectors_list = []
        for sector_name, sector_translations in sectors_dict.items():
            sectors_list.append({"sector": sector_name, "translations": sector_translations})

        return sectors_list
