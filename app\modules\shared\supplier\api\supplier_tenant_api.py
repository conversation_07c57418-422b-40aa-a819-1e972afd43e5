from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi import status as http_status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from typing import List
import uuid

from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.auth_dependencies import get_current_user

router = APIRouter()


@router.get(
    "/supplier/tenants",
    response_model=List[dict],
    summary="Listar Tenants do Supplier",
    description="Lista todos os tenants onde o usuário atual é supplier.",
)
async def get_supplier_tenants(
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user),
):
    """
    Lista tenants onde o usuário atual tem role de supplier.
    """
    try:
        # Buscar associações do usuário com role supplier usando SQL direto
        result = await db.execute(
            text("""
                SELECT
                    tua.id as association_id,
                    tua.role,
                    t.id as tenant_id,
                    t.name,
                    t.tenant_type as type,
                    t.is_active
                FROM tenant_user_associations tua
                JOIN tenants t ON tua.tenant_id = t.id
                WHERE tua.user_id = :user_id AND tua.role = 'supplier'
            """),
            {"user_id": current_user.id}
        )

        associations = result.fetchall()

        tenants = []
        for row in associations:
            tenants.append({
                "id": str(row.tenant_id),
                "name": row.name,
                "description": None,  # Tenant model doesn't have description
                "type": row.type,
                "is_active": row.is_active,
                "association_id": str(row.association_id),
                "role": row.role
            })

        return tenants

    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch supplier tenants: {str(e)}"
        )


@router.get(
    "/supplier/shopping-lists",
    response_model=List[dict],
    summary="Listar Shopping Lists para Supplier",
    description="Lista shopping lists baseado no tenant selecionado ou todos os tenants.",
)
async def get_supplier_shopping_lists(
    tenant_id: str = Query(None, description="ID do tenant específico (opcional)"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user),
):
    """
    Lista shopping lists para o supplier.
    Se tenant_id for fornecido, filtra por esse tenant.
    Se não, retorna de todos os tenants onde o usuário é supplier.
    """
    try:
        # Primeiro, verificar quais tenants o usuário é supplier
        supplier_tenants_query = select(TenantUserAssociation.tenant_id).where(
            and_(
                TenantUserAssociation.user_id == current_user.id,
                TenantUserAssociation.role == "supplier"
            )
        )
        
        if tenant_id:
            # Verificar se o usuário é supplier neste tenant específico
            supplier_tenants_query = supplier_tenants_query.where(
                TenantUserAssociation.tenant_id == uuid.UUID(tenant_id)
            )
        
        supplier_tenants_result = await db.execute(supplier_tenants_query)
        allowed_tenant_ids = [row[0] for row in supplier_tenants_result.all()]
        
        if not allowed_tenant_ids:
            return []
        
        # Buscar shopping lists dos tenants permitidos
        from app.modules.shared.shopping_list.models.shopping_list import ShoppingList
        
        shopping_lists_query = select(ShoppingList).where(
            and_(
                ShoppingList.tenant_id.in_(allowed_tenant_ids),
                ShoppingList.is_active == True
            )
        )
        
        shopping_lists_result = await db.execute(shopping_lists_query)
        shopping_lists = shopping_lists_result.scalars().all()
        
        # Buscar informações do tenant para cada lista
        result = []
        for shopping_list in shopping_lists:
            tenant_result = await db.execute(
                select(Tenant).where(Tenant.id == shopping_list.tenant_id)
            )
            tenant = tenant_result.scalar_one_or_none()
            
            result.append({
                "id": str(shopping_list.id),
                "name": shopping_list.name,
                "description": shopping_list.description,
                "is_active": shopping_list.is_active,
                "tenant_id": str(shopping_list.tenant_id),
                "tenant_name": tenant.name if tenant else "Unknown",
                "created_at": shopping_list.created_at.isoformat(),
                "updated_at": shopping_list.updated_at.isoformat(),
            })
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch supplier shopping lists"
        )


@router.get(
    "/supplier/shopping-lists/{list_id}/items",
    response_model=List[dict],
    summary="Listar Itens da Shopping List para Supplier",
    description="Lista itens de uma shopping list específica se o supplier tem acesso.",
)
async def get_supplier_shopping_list_items(
    list_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user),
):
    """
    Lista itens de uma shopping list específica.
    Verifica se o supplier tem acesso ao tenant da lista.
    """
    try:
        from app.modules.shared.shopping_list.models.shopping_list import (
            ShoppingList, 
            ShoppingListItem
        )
        
        # Buscar a shopping list
        shopping_list_result = await db.execute(
            select(ShoppingList).where(ShoppingList.id == list_id)
        )
        shopping_list = shopping_list_result.scalar_one_or_none()
        
        if not shopping_list:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Shopping list not found"
            )
        
        # Verificar se o usuário é supplier neste tenant
        supplier_check = await db.execute(
            select(TenantUserAssociation).where(
                and_(
                    TenantUserAssociation.user_id == current_user.id,
                    TenantUserAssociation.tenant_id == shopping_list.tenant_id,
                    TenantUserAssociation.role == "supplier"
                )
            )
        )
        
        if not supplier_check.scalar_one_or_none():
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="Access denied: not a supplier for this tenant"
            )
        
        # Buscar itens da lista
        items_result = await db.execute(
            select(ShoppingListItem).where(
                ShoppingListItem.shopping_list_id == list_id
            )
        )
        items = items_result.scalars().all()
        
        result = []
        for item in items:
            result.append({
                "id": str(item.id),
                "name": item.name,
                "quantity": item.quantity,
                "unit": item.unit,
                "priority": item.priority.value if item.priority else "low",
                "estimated_price": str(item.estimated_price) if item.estimated_price else None,
                "purchased": item.purchased,
                "notes": item.notes,
                "inventory_item_id": str(item.inventory_item_id) if item.inventory_item_id else None,
                "shopping_list_id": str(item.shopping_list_id),
                "created_at": item.created_at.isoformat(),
                "updated_at": item.updated_at.isoformat(),
            })
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch shopping list items"
        )


@router.get(
    "/supplier/purchase-orders",
    response_model=List[dict],
    summary="Listar Purchase Orders para Supplier",
    description="Lista purchase orders do supplier baseado no tenant selecionado.",
)
async def get_supplier_purchase_orders(
    tenant_id: str = Query(None, description="ID do tenant específico (opcional)"),
    status: str = Query(None, description="Status do pedido (opcional)"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user),
):
    """
    Lista purchase orders para o supplier.
    Se tenant_id for fornecido, filtra por esse tenant.
    """
    try:
        from app.modules.shared.supplier.services.purchase_order_service import (
            purchase_order_service
        )

        # Convert tenant_id to UUID if provided
        tenant_uuid = uuid.UUID(tenant_id) if tenant_id else None

        # Convert status string to enum if provided
        order_status = None
        if status:
            from app.modules.shared.supplier.models.purchase_order import PurchaseOrderStatus
            try:
                order_status = PurchaseOrderStatus(status)
            except ValueError:
                raise HTTPException(
                    status_code=http_status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid status: {status}"
                )

        purchase_orders = await purchase_order_service.get_supplier_purchase_orders(
            db=db,
            supplier_user_id=current_user.id,
            tenant_id=tenant_uuid,
            status=order_status
        )

        # Convert to dict format with tenant info
        result = []
        for order in purchase_orders:
            # Get tenant name
            tenant_result = await db.execute(
                text("SELECT name FROM tenants WHERE id = :tenant_id"),
                {"tenant_id": order.tenant_id}
            )
            tenant_name = tenant_result.scalar_one_or_none() or "Unknown"

            # Get items count
            items_result = await db.execute(
                text("SELECT COUNT(*) FROM purchase_order_items WHERE purchase_order_id = :order_id"),
                {"order_id": order.id}
            )
            items_count = items_result.scalar_one() or 0

            result.append({
                "id": str(order.id),
                "order_number": order.order_number,
                "status": order.status,
                "order_date": order.order_date.isoformat(),
                "requested_delivery_date": order.requested_delivery_date.isoformat() if order.requested_delivery_date else None,
                "confirmed_delivery_date": order.confirmed_delivery_date.isoformat() if order.confirmed_delivery_date else None,
                "tenant_id": str(order.tenant_id),
                "tenant_name": tenant_name,
                "total_amount": str(order.total_amount),
                "items_count": items_count,
                "delivery_address": order.delivery_address,
                "delivery_notes": order.delivery_notes,
                "notes": order.notes,
                "supplier_invoice_number": order.supplier_invoice_number,
                "supplier_delivery_note": order.supplier_delivery_note,
                "created_at": order.created_at.isoformat(),
                "updated_at": order.updated_at.isoformat(),
            })

        return result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch supplier purchase orders: {str(e)}"
        )


@router.get(
    "/supplier/purchase-orders/{order_id}/items",
    response_model=List[dict],
    summary="Listar Itens do Purchase Order",
    description="Lista itens de um purchase order específico se o supplier tem acesso.",
)
async def get_supplier_purchase_order_items(
    order_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user),
):
    """
    Lista itens de um purchase order específico.
    Verifica se o supplier tem acesso ao pedido.
    """
    try:
        from app.modules.shared.supplier.services.purchase_order_service import (
            purchase_order_service
        )

        # Get purchase order and verify access
        purchase_order = await purchase_order_service.get_purchase_order_by_id(
            db=db, order_id=order_id
        )

        if not purchase_order:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Purchase order not found"
            )

        # Verify supplier access
        access_check = await db.execute(
            text("""
                SELECT 1 FROM tenant_user_associations tua
                JOIN suppliers s ON s.tenant_id = tua.tenant_id
                WHERE tua.user_id = :user_id
                AND tua.role = 'supplier'
                AND s.id = :supplier_id
            """),
            {"user_id": current_user.id, "supplier_id": purchase_order.supplier_id}
        )

        if not access_check.scalar_one_or_none():
            raise HTTPException(
                status_code=http_status.HTTP_403_FORBIDDEN,
                detail="Access denied: not authorized for this supplier"
            )

        # Get items
        items = await purchase_order_service.get_purchase_order_items(
            db=db, order_id=order_id
        )

        result = []
        for item in items:
            result.append({
                "id": str(item.id),
                "purchase_order_id": str(item.purchase_order_id),
                "name": item.name,
                "description": item.description,
                "quantity_requested": str(item.quantity_requested),
                "quantity_confirmed": str(item.quantity_confirmed) if item.quantity_confirmed else None,
                "quantity_delivered": str(item.quantity_delivered) if item.quantity_delivered else None,
                "unit": item.unit,
                "unit_price": str(item.unit_price),
                "total_price": str(item.total_price),
                "status": item.status,
                "product_code": item.product_code,
                "notes": item.notes,
                "supplier_notes": item.supplier_notes,
                "inventory_item_id": str(item.inventory_item_id) if item.inventory_item_id else None,
                "shopping_list_item_id": str(item.shopping_list_item_id) if item.shopping_list_item_id else None,
                "sort_order": item.sort_order,
            })

        return result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch purchase order items: {str(e)}"
        )


@router.put(
    "/supplier/purchase-orders/{order_id}/items/{item_id}",
    response_model=dict,
    summary="Atualizar Item do Purchase Order",
    description="Permite ao supplier atualizar status, quantidade e notas de um item.",
)
async def update_supplier_purchase_order_item(
    order_id: uuid.UUID,
    item_id: uuid.UUID,
    item_data: dict,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user),
):
    """
    Atualiza item do purchase order pelo supplier.
    Permite confirmar disponibilidade, quantidade e adicionar notas.
    """
    try:
        from app.modules.shared.supplier.services.purchase_order_service import (
            purchase_order_service
        )
        from app.modules.shared.supplier.schemas.purchase_order import (
            PurchaseOrderItemSupplierUpdate
        )
        from app.modules.shared.supplier.models.purchase_order import (
            PurchaseOrderItemStatus
        )

        # Validate and convert item_data
        try:
            # Convert status string to enum if provided
            if "status" in item_data:
                item_data["status"] = PurchaseOrderItemStatus(item_data["status"])

            update_data = PurchaseOrderItemSupplierUpdate(**item_data)
        except ValueError as e:
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid data: {str(e)}"
            )

        updated_item = await purchase_order_service.supplier_update_purchase_order_item(
            db=db,
            item_id=item_id,
            item_data=update_data,
            supplier_user_id=current_user.id
        )

        return {
            "id": str(updated_item.id),
            "purchase_order_id": str(updated_item.purchase_order_id),
            "name": updated_item.name,
            "quantity_requested": str(updated_item.quantity_requested),
            "quantity_confirmed": str(updated_item.quantity_confirmed) if updated_item.quantity_confirmed else None,
            "quantity_delivered": str(updated_item.quantity_delivered) if updated_item.quantity_delivered else None,
            "unit_price": str(updated_item.unit_price),
            "total_price": str(updated_item.total_price),
            "status": updated_item.status,
            "supplier_notes": updated_item.supplier_notes,
            "message": "Item updated successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update purchase order item: {str(e)}"
        )


@router.put(
    "/supplier/purchase-orders/{order_id}",
    response_model=dict,
    summary="Atualizar Purchase Order",
    description="Permite ao supplier atualizar status, datas e informações de entrega.",
)
async def update_supplier_purchase_order(
    order_id: uuid.UUID,
    order_data: dict,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user),
):
    """
    Atualiza purchase order pelo supplier.
    Permite confirmar datas, adicionar número de fatura e nota de entrega.
    """
    try:
        from app.modules.shared.supplier.services.purchase_order_service import (
            purchase_order_service
        )
        from app.modules.shared.supplier.schemas.purchase_order import (
            PurchaseOrderSupplierUpdate
        )
        from app.modules.shared.supplier.models.purchase_order import (
            PurchaseOrderStatus
        )

        # Validate and convert order_data
        try:
            # Convert status string to enum if provided
            if "status" in order_data:
                order_data["status"] = PurchaseOrderStatus(order_data["status"])

            # Convert date strings to date objects if provided
            for date_field in ["confirmed_delivery_date", "actual_delivery_date"]:
                if date_field in order_data and order_data[date_field]:
                    from datetime import datetime
                    order_data[date_field] = datetime.fromisoformat(order_data[date_field]).date()

            update_data = PurchaseOrderSupplierUpdate(**order_data)
        except ValueError as e:
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid data: {str(e)}"
            )

        updated_order = await purchase_order_service.supplier_update_purchase_order(
            db=db,
            order_id=order_id,
            order_data=update_data,
            supplier_user_id=current_user.id
        )

        return {
            "id": str(updated_order.id),
            "order_number": updated_order.order_number,
            "status": updated_order.status,
            "confirmed_delivery_date": updated_order.confirmed_delivery_date.isoformat() if updated_order.confirmed_delivery_date else None,
            "actual_delivery_date": updated_order.actual_delivery_date.isoformat() if updated_order.actual_delivery_date else None,
            "supplier_invoice_number": updated_order.supplier_invoice_number,
            "supplier_delivery_note": updated_order.supplier_delivery_note,
            "total_amount": str(updated_order.total_amount),
            "message": "Purchase order updated successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update purchase order: {str(e)}"
        )
