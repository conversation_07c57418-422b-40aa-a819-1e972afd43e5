"""
Review Metrics Models

Sistema de métricas e estatísticas para reviews.
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional
from uuid import UUID, uuid4

from sqlalchemy import Column, String, DateTime, Integer, Numeric, ForeignKey
from sqlalchemy.dialects.postgresql import UUID as PostgreSQLUUID
from sqlalchemy.orm import relationship

from app.db.base import Base


class ProductReviewMetrics(Base):
    """
    Modelo para métricas agregadas de reviews por produto.
    
    Mantém estatísticas pré-calculadas para performance.
    """
    __tablename__ = "eshop_product_review_metrics"

    id: UUID = Column(
        PostgreSQLUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        index=True
    )
    
    # Relacionamento
    product_id: UUID = Column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("eshop_products.id", ondelete="CASCADE"),
        nullable=False,
        unique=True,
        index=True
    )
    
    # Métricas de rating
    total_reviews: int = Column(Integer, default=0, nullable=False)
    average_rating: Decimal = Column(Numeric(3, 2), default=0.00, nullable=False)
    
    # Distribuição por estrelas
    rating_1_count: int = Column(Integer, default=0, nullable=False)
    rating_2_count: int = Column(Integer, default=0, nullable=False)
    rating_3_count: int = Column(Integer, default=0, nullable=False)
    rating_4_count: int = Column(Integer, default=0, nullable=False)
    rating_5_count: int = Column(Integer, default=0, nullable=False)
    
    # Métricas de qualidade
    verified_purchases_count: int = Column(Integer, default=0, nullable=False)
    total_helpful_votes: int = Column(Integer, default=0, nullable=False)
    total_not_helpful_votes: int = Column(Integer, default=0, nullable=False)
    
    # Métricas de moderação
    hidden_reviews_count: int = Column(Integer, default=0, nullable=False)
    reported_reviews_count: int = Column(Integer, default=0, nullable=False)
    
    # Timestamps
    last_updated: datetime = Column(DateTime, default=datetime.utcnow, nullable=False)
    created_at: datetime = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relacionamento SQLAlchemy
    product = relationship("app.modules.core.eshop.models.product.Product", back_populates="review_metrics")
    
    def __repr__(self) -> str:
        return f"<ProductReviewMetrics(product_id={self.product_id}, avg_rating={self.average_rating})>"
    
    @property
    def verified_purchase_percentage(self) -> float:
        """Percentual de reviews de compras verificadas"""
        if self.total_reviews == 0:
            return 0.0
        return (self.verified_purchases_count / self.total_reviews) * 100
    
    @property
    def helpfulness_ratio(self) -> float:
        """Proporção de votos úteis"""
        total_votes = self.total_helpful_votes + self.total_not_helpful_votes
        if total_votes == 0:
            return 0.0
        return self.total_helpful_votes / total_votes
    
    @property
    def quality_score(self) -> float:
        """Score de qualidade das reviews (0-100)"""
        # Combina múltiplos fatores para um score de qualidade
        if self.total_reviews == 0:
            return 0.0
        
        # Fatores: verificação de compra, utilidade, moderação
        verification_factor = self.verified_purchase_percentage / 100
        helpfulness_factor = self.helpfulness_ratio
        moderation_factor = 1 - (self.hidden_reviews_count / self.total_reviews)
        
        # Peso balanceado
        quality = (verification_factor * 0.4 + 
                  helpfulness_factor * 0.4 + 
                  moderation_factor * 0.2)
        
        return round(quality * 100, 2)
    
    def get_rating_distribution(self) -> dict:
        """Retorna distribuição de ratings"""
        return {
            1: self.rating_1_count,
            2: self.rating_2_count,
            3: self.rating_3_count,
            4: self.rating_4_count,
            5: self.rating_5_count
        }
    
    def update_metrics(self, reviews_data: dict) -> None:
        """Atualiza métricas baseado nos dados das reviews"""
        self.total_reviews = reviews_data.get('total_reviews', 0)
        self.average_rating = Decimal(str(reviews_data.get('average_rating', 0.0)))
        
        # Distribuição por estrelas
        distribution = reviews_data.get('rating_distribution', {})
        self.rating_1_count = distribution.get(1, 0)
        self.rating_2_count = distribution.get(2, 0)
        self.rating_3_count = distribution.get(3, 0)
        self.rating_4_count = distribution.get(4, 0)
        self.rating_5_count = distribution.get(5, 0)
        
        # Outras métricas
        self.verified_purchases_count = reviews_data.get('verified_purchases', 0)
        self.total_helpful_votes = reviews_data.get('helpful_votes', 0)
        self.total_not_helpful_votes = reviews_data.get('not_helpful_votes', 0)
        self.hidden_reviews_count = reviews_data.get('hidden_reviews', 0)
        self.reported_reviews_count = reviews_data.get('reported_reviews', 0)
        
        self.last_updated = datetime.utcnow()


class ReviewerMetrics(Base):
    """
    Modelo para métricas de reviewers.
    
    Rastreia estatísticas dos usuários que fazem reviews.
    """
    __tablename__ = "eshop_reviewer_metrics"

    id: UUID = Column(
        PostgreSQLUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        index=True
    )
    
    # Relacionamento
    user_id: UUID = Column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        unique=True,
        index=True
    )
    
    # Métricas de atividade
    total_reviews: int = Column(Integer, default=0, nullable=False)
    verified_reviews: int = Column(Integer, default=0, nullable=False)
    average_rating_given: Decimal = Column(Numeric(3, 2), default=0.00, nullable=False)
    
    # Métricas de qualidade
    helpful_votes_received: int = Column(Integer, default=0, nullable=False)
    not_helpful_votes_received: int = Column(Integer, default=0, nullable=False)
    reviews_reported: int = Column(Integer, default=0, nullable=False)
    reviews_hidden: int = Column(Integer, default=0, nullable=False)
    
    # Ranking
    reviewer_rank: Optional[str] = Column(String(20), nullable=True)  # novice, regular, expert, top
    
    # Timestamps
    first_review_date: Optional[datetime] = Column(DateTime, nullable=True)
    last_review_date: Optional[datetime] = Column(DateTime, nullable=True)
    last_updated: datetime = Column(DateTime, default=datetime.utcnow, nullable=False)
    created_at: datetime = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relacionamento SQLAlchemy
    user = relationship("User")
    
    def __repr__(self) -> str:
        return f"<ReviewerMetrics(user_id={self.user_id}, total_reviews={self.total_reviews})>"
    
    @property
    def verification_rate(self) -> float:
        """Taxa de reviews verificadas"""
        if self.total_reviews == 0:
            return 0.0
        return (self.verified_reviews / self.total_reviews) * 100
    
    @property
    def helpfulness_ratio(self) -> float:
        """Proporção de votos úteis recebidos"""
        total_votes = self.helpful_votes_received + self.not_helpful_votes_received
        if total_votes == 0:
            return 0.0
        return self.helpful_votes_received / total_votes
    
    @property
    def reputation_score(self) -> int:
        """Score de reputação do reviewer (0-1000)"""
        if self.total_reviews == 0:
            return 0
        
        # Fatores para cálculo da reputação
        activity_factor = min(self.total_reviews / 50, 1.0)  # Max 50 reviews
        verification_factor = self.verification_rate / 100
        helpfulness_factor = self.helpfulness_ratio
        quality_factor = 1 - (self.reviews_hidden / max(self.total_reviews, 1))
        
        # Peso balanceado
        reputation = (activity_factor * 0.3 + 
                     verification_factor * 0.3 + 
                     helpfulness_factor * 0.3 + 
                     quality_factor * 0.1)
        
        return int(reputation * 1000)
    
    def calculate_rank(self) -> str:
        """Calcula o rank do reviewer baseado na reputação"""
        score = self.reputation_score
        
        if score >= 800:
            return "top"
        elif score >= 600:
            return "expert"
        elif score >= 300:
            return "regular"
        else:
            return "novice"
    
    def update_metrics(self, reviews_data: dict) -> None:
        """Atualiza métricas do reviewer"""
        self.total_reviews = reviews_data.get('total_reviews', 0)
        self.verified_reviews = reviews_data.get('verified_reviews', 0)
        self.average_rating_given = Decimal(str(reviews_data.get('average_rating', 0.0)))
        
        self.helpful_votes_received = reviews_data.get('helpful_votes', 0)
        self.not_helpful_votes_received = reviews_data.get('not_helpful_votes', 0)
        self.reviews_reported = reviews_data.get('reported_reviews', 0)
        self.reviews_hidden = reviews_data.get('hidden_reviews', 0)
        
        self.first_review_date = reviews_data.get('first_review_date')
        self.last_review_date = reviews_data.get('last_review_date')
        
        # Atualizar rank
        self.reviewer_rank = self.calculate_rank()
        self.last_updated = datetime.utcnow()