"""
Blog Comment Models

Comment system for blog posts with moderation and threading support.
"""

import uuid
from datetime import datetime

from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    ForeignKey,
    Integer,
    String,
    Text,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base


class BlogComment(Base):
    """
    Blog comment model with threading support.

    Supports nested comments (replies) and moderation features.
    """

    __tablename__ = "blog_comments"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Foreign keys
    post_id = Column(UUID(as_uuid=True), ForeignKey("blog_posts.id"),
                    nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"),
                    nullable=True)  # Nullable for guest comments
    parent_id = Column(UUID(as_uuid=True), ForeignKey("blog_comments.id"),
                      nullable=True)  # For threaded comments

    # Comment content
    content = Column(Text, nullable=False)

    # Guest comment information (when user_id is null)
    guest_name = Column(String(100), nullable=True)
    guest_email = Column(String(255), nullable=True)
    guest_website = Column(String(500), nullable=True)

    # Moderation
    status = Column(String(20), default="pending", nullable=False)
    # pending, approved, rejected, spam
    is_pinned = Column(Boolean, default=False)

    # Engagement
    like_count = Column(Integer, default=0)
    reply_count = Column(Integer, default=0)

    # Technical
    ip_address = Column(String(45), nullable=True)  # IPv6 support
    user_agent = Column(String(500), nullable=True)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow,
                       onupdate=datetime.utcnow, nullable=False)

    # Relationships
    post = relationship("BlogPost", back_populates="comments")
    user = relationship("User", backref="blog_comments")
    parent = relationship("BlogComment", remote_side=[id],
                         back_populates="replies")
    replies = relationship("BlogComment", back_populates="parent")

    def __repr__(self):
        return f"<BlogComment(id={self.id}, post_id={self.post_id})>"
