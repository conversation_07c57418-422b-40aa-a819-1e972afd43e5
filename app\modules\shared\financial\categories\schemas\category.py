"""Financial category schemas."""

import uuid
from typing import Optional, List
from pydantic import BaseModel, Field, validator

from ..models.category import CategoryType


class FinancialCategoryBase(BaseModel):
    """Base schema for financial categories."""
    
    name: str = Field(
        ..., 
        min_length=1, 
        max_length=100,
        description="Category name"
    )
    description: Optional[str] = Field(
        None,
        max_length=500,
        description="Category description"
    )
    category_type: CategoryType = Field(
        ...,
        description="Category type (income or expense)"
    )
    parent_id: Optional[uuid.UUID] = Field(
        None,
        description="Parent category ID for hierarchical structure"
    )
    display_order: int = Field(
        default=0,
        ge=0,
        description="Display order for sorting"
    )
    is_active: bool = Field(
        default=True,
        description="Whether the category is active"
    )
    color: Optional[str] = Field(
        None,
        pattern=r'^#[0-9A-Fa-f]{6}$',
        description="Hex color code for the category"
    )
    icon: Optional[str] = Field(
        None,
        max_length=100,
        description="Icon class or URL for the category"
    )


class FinancialCategoryCreate(FinancialCategoryBase):
    """Schema for creating a financial category."""
    
    @validator('name')
    def validate_name(cls, v):
        """Validate category name."""
        if not v.strip():
            raise ValueError('Category name cannot be empty')
        return v.strip()


class FinancialCategoryUpdate(BaseModel):
    """Schema for updating a financial category."""
    
    name: Optional[str] = Field(
        None,
        min_length=1,
        max_length=100,
        description="Category name"
    )
    description: Optional[str] = Field(
        None,
        max_length=500,
        description="Category description"
    )
    category_type: Optional[CategoryType] = Field(
        None,
        description="Category type (income or expense)"
    )
    parent_id: Optional[uuid.UUID] = Field(
        None,
        description="Parent category ID for hierarchical structure"
    )
    display_order: Optional[int] = Field(
        None,
        ge=0,
        description="Display order for sorting"
    )
    is_active: Optional[bool] = Field(
        None,
        description="Whether the category is active"
    )
    color: Optional[str] = Field(
        None,
        pattern=r'^#[0-9A-Fa-f]{6}$',
        description="Hex color code for the category"
    )
    icon: Optional[str] = Field(
        None,
        max_length=100,
        description="Icon class or URL for the category"
    )

    @validator('name')
    def validate_name(cls, v):
        """Validate category name."""
        if v is not None and not v.strip():
            raise ValueError('Category name cannot be empty')
        return v.strip() if v else v


class FinancialCategoryRead(FinancialCategoryBase):
    """Schema for reading a financial category."""
    
    id: uuid.UUID = Field(..., description="Category ID")
    tenant_id: uuid.UUID = Field(..., description="Tenant ID")
    is_default: bool = Field(default=False, description="Whether this is a default category")
    
    # Hierarchical data
    parent_name: Optional[str] = Field(None, description="Parent category name")
    children_count: int = Field(default=0, description="Number of child categories")
    
    # Usage statistics
    transactions_count: int = Field(default=0, description="Number of transactions in this category")
    total_income: Optional[float] = Field(None, description="Total income in this category")
    total_expense: Optional[float] = Field(None, description="Total expense in this category")
    
    class Config:
        from_attributes = True


class FinancialCategoryTree(FinancialCategoryRead):
    """Schema for hierarchical category tree."""
    
    children: List['FinancialCategoryTree'] = Field(
        default_factory=list,
        description="Child categories"
    )


class FinancialCategoryListResponse(BaseModel):
    """Schema for paginated category list response."""
    
    categories: List[FinancialCategoryRead] = Field(
        default_factory=list,
        description="List of categories"
    )
    total: int = Field(
        default=0,
        description="Total number of categories"
    )
    page: int = Field(
        default=1,
        description="Current page number"
    )
    per_page: int = Field(
        default=20,
        description="Items per page"
    )
    pages: int = Field(
        default=0,
        description="Total number of pages"
    )


class FinancialCategoryFilter(BaseModel):
    """Schema for filtering financial categories."""
    
    category_type: Optional[CategoryType] = Field(
        None,
        description="Filter by category type"
    )
    parent_id: Optional[uuid.UUID] = Field(
        None,
        description="Filter by parent category ID"
    )
    is_active: Optional[bool] = Field(
        None,
        description="Filter by active status"
    )
    search: Optional[str] = Field(
        None,
        max_length=100,
        description="Search in name and description"
    )


# Update forward references
FinancialCategoryTree.model_rebuild()
