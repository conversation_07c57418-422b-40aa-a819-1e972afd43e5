import uuid
from sqlalchemy import Column, String, ForeignKey, Index, Boolean, Integer, Text, Numeric, DateTime, Enum
from sqlalchemy.dialects.postgresql import UUID as PG_UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum

from app.db.base import Base
from app.modules.core.tenants.models.tenant import Tenant
from app.modules.core.users.models.user import User
from app.core.enums import MarketType


class ProductType(enum.Enum):
    PHYSICAL = "physical"
    DIGITAL = "digital"


class ProductStatus(enum.Enum):
    DRAFT = "draft"
    ACTIVE = "active"
    INACTIVE = "inactive"
    OUT_OF_STOCK = "out_of_stock"


# MarketType agora importado de app.core.enums


class ApprovalStatus(enum.Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"


class Product(Base):
    """
    Represents a product in the eshop marketplace.
    Can be physical or digital, with variants, modifiers, and optionals.
    """

    __tablename__ = "eshop_products"

    # Primary key
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Tenant and vendor information
    tenant_id = Column(PG_UUID(as_uuid=True), ForeignKey(Tenant.id), nullable=True, index=True)  # Can be global
    vendor_id = Column(PG_UUID(as_uuid=True), ForeignKey(User.id), nullable=False, index=True)  # TVendorSupplier
    
    # Category
    category_id = Column(PG_UUID(as_uuid=True), ForeignKey("eshop_product_categories.id"), nullable=False, index=True)
    
    # Basic product information
    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    short_description = Column(String(500), nullable=True)
    slug = Column(String(250), nullable=False, index=True)  # URL-friendly name
    sku = Column(String(100), nullable=True, index=True)  # Stock Keeping Unit
    
    # Product type and status
    product_type = Column(Enum(ProductType), nullable=False, default=ProductType.PHYSICAL)
    status = Column(Enum(ProductStatus), nullable=False, default=ProductStatus.DRAFT)
    
    # Pricing
    base_price = Column(Numeric(10, 2), nullable=False)
    sale_price = Column(Numeric(10, 2), nullable=True)  # Discounted price
    cost_price = Column(Numeric(10, 2), nullable=True)  # Cost for profit calculation
    
    # Stock management (for physical products)
    stock_quantity = Column(Integer, default=0, nullable=False)
    low_stock_threshold = Column(Integer, default=5, nullable=False)
    manage_stock = Column(Boolean, default=True, nullable=False)
    allow_backorders = Column(Boolean, default=False, nullable=False)
    
    # Digital product settings
    digital_file_url = Column(String(500), nullable=True)  # For digital products
    download_limit = Column(Integer, nullable=True)  # Max downloads per purchase
    download_expiry_days = Column(Integer, nullable=True)  # Days until download expires
    
    # Shipping and delivery
    weight = Column(Numeric(8, 3), nullable=True)  # In kg
    dimensions = Column(JSONB, nullable=True)  # {"length": 10, "width": 5, "height": 3}
    shipping_required = Column(Boolean, default=True, nullable=False)
    shipping_class = Column(String(50), nullable=True)  # Standard, Express, etc.
    
    # SEO and metadata
    meta_title = Column(String(200), nullable=True)
    meta_description = Column(Text, nullable=True)
    featured_image_url = Column(String(500), nullable=True)
    gallery_images = Column(JSONB, nullable=True)  # Array of image URLs
    
    # Display and organization
    display_order = Column(Integer, default=0, nullable=False)
    is_featured = Column(Boolean, default=False, nullable=False)
    is_virtual = Column(Boolean, default=False, nullable=False)  # No shipping needed
    
    # Ratings and reviews
    average_rating = Column(Numeric(3, 2), default=0.0, nullable=False)
    review_count = Column(Integer, default=0, nullable=False)
    
    # Additional product data
    attributes = Column(JSONB, nullable=True)  # Custom attributes
    tags = Column(JSONB, nullable=True)  # Array of tags
    
    # Fusion fields for Cuponic-EShop integration
    market_type = Column(Enum(MarketType), nullable=False, default=MarketType.B2C)
    approval_status = Column(Enum(ApprovalStatus), nullable=False, default=ApprovalStatus.PENDING)
    commission_rate = Column(Numeric(5, 2), nullable=True)  # Percentage (0.00-99.99)
    legacy_cuponic_id = Column(Integer, nullable=True, index=True)  # For backward compatibility
    approved_at = Column(DateTime(timezone=True), nullable=True)
    approved_by = Column(PG_UUID(as_uuid=True), ForeignKey(User.id), nullable=True)
    rejection_reason = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    tenant = relationship("Tenant")
    vendor = relationship("User", foreign_keys=[vendor_id])  # back_populates="eshop_products"
    approver = relationship("User", foreign_keys=[approved_by])
    category = relationship("app.modules.core.eshop.models.product_category.ProductCategory", back_populates="products")
    reviews = relationship("app.modules.core.functions.reviews.models.review.Review", back_populates="product", cascade="all, delete-orphan")
    review_metrics = relationship("app.modules.core.functions.reviews.models.review_metrics.ProductReviewMetrics", back_populates="product", cascade="all, delete-orphan", uselist=False)
    approval_history = relationship("app.modules.core.eshop.models.product_approval.ProductApprovalHistory", back_populates="product", cascade="all, delete-orphan")
    
    # Variant, modifier, and optional groups (many-to-many relationships)
    variant_groups = relationship(
        "app.modules.core.eshop.models.product_variant_group.ProductVariantGroup",
        secondary="eshop_product_variant_groups_association",
        back_populates="products"
    )
    modifier_groups = relationship(
        "app.modules.core.eshop.models.product_modifier_group.ProductModifierGroup",
        secondary="eshop_product_modifier_groups_association",
        back_populates="products"
    )
    optional_groups = relationship(
        "app.modules.core.eshop.models.product_optional_group.ProductOptionalGroup",
        secondary="eshop_product_optional_groups_association",
        back_populates="products"
    )

    __table_args__ = (
        Index("ix_eshop_products_tenant_id", "tenant_id"),
        Index("ix_eshop_products_vendor_id", "vendor_id"),
        Index("ix_eshop_products_category_id", "category_id"),
        Index("ix_eshop_products_slug", "slug"),
        Index("ix_eshop_products_sku", "sku"),
        Index("ix_eshop_products_status", "status"),
        Index("ix_eshop_products_product_type", "product_type"),
        Index("ix_eshop_products_tenant_status", "tenant_id", "status"),
        Index("ix_eshop_products_vendor_status", "vendor_id", "status"),
        # Fusion-specific indexes for performance
        Index("ix_eshop_products_market_type", "market_type"),
        Index("ix_eshop_products_approval_status", "approval_status"),
        Index("ix_eshop_products_legacy_cuponic_id", "legacy_cuponic_id"),
        Index("ix_eshop_products_vendor_approval", "vendor_id", "approval_status"),
        Index("ix_eshop_products_market_approval", "market_type", "approval_status"),
        Index("ix_eshop_products_tenant_market", "tenant_id", "market_type"),
    )

    def __repr__(self):
        return f"<Product(id={self.id}, name='{self.name}', vendor_id={self.vendor_id})>"
