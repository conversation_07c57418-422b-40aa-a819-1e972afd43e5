"""
Media WebSockets

WebSockets para notificações em tempo real do sistema de mídia.
"""

import json
import logging
from typing import Dict, List, Optional, Set
from uuid import UUID

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.modules.core.auth.dependencies import get_current_user_websocket
from app.modules.core.users.models import User

from ..models import MediaContextType, MediaUploadStatus

logger = logging.getLogger(__name__)

router = APIRouter()


class MediaWebSocketManager:
    """Gerenciador de WebSockets para o sistema de mídia."""

    def __init__(self):
        # Conexões ativas: {user_id: {websocket, context_subscriptions}}
        self.active_connections: Dict[UUID, Dict] = {}
        
        # Assinantes por contexto: {context_id: {user_ids}}
        self.context_subscribers: Dict[UUID, Set[UUID]] = {}

    async def connect(self, websocket: WebSocket, user: User):
        """
        Conecta um usuário via WebSocket.
        
        Args:
            websocket: Conexão WebSocket
            user: Usu<PERSON>rio conectado
        """
        await websocket.accept()
        
        self.active_connections[user.id] = {
            "websocket": websocket,
            "context_subscriptions": set()
        }
        
        logger.info(f"Usuário conectado via WebSocket: {user.id}")
        
        # Envia mensagem de boas-vindas
        await self.send_personal_message(user.id, {
            "type": "connection_established",
            "message": "Conectado ao sistema de mídia",
            "user_id": str(user.id)
        })

    def disconnect(self, user_id: UUID):
        """
        Desconecta um usuário.
        
        Args:
            user_id: ID do usuário
        """
        if user_id in self.active_connections:
            # Remove das assinaturas de contexto
            subscriptions = self.active_connections[user_id]["context_subscriptions"]
            for context_id in subscriptions:
                if context_id in self.context_subscribers:
                    self.context_subscribers[context_id].discard(user_id)
                    if not self.context_subscribers[context_id]:
                        del self.context_subscribers[context_id]
            
            # Remove conexão
            del self.active_connections[user_id]
            logger.info(f"Usuário desconectado: {user_id}")

    async def subscribe_to_context(self, user_id: UUID, context_id: UUID):
        """
        Inscreve usuário para receber notificações de um contexto.
        
        Args:
            user_id: ID do usuário
            context_id: ID do contexto
        """
        if user_id not in self.active_connections:
            return

        # Adiciona à assinatura do usuário
        self.active_connections[user_id]["context_subscriptions"].add(context_id)
        
        # Adiciona aos assinantes do contexto
        if context_id not in self.context_subscribers:
            self.context_subscribers[context_id] = set()
        self.context_subscribers[context_id].add(user_id)
        
        logger.info(f"Usuário {user_id} inscrito no contexto {context_id}")
        
        await self.send_personal_message(user_id, {
            "type": "context_subscribed",
            "context_id": str(context_id),
            "message": "Inscrito para receber notificações do contexto"
        })

    async def unsubscribe_from_context(self, user_id: UUID, context_id: UUID):
        """
        Remove inscrição de usuário de um contexto.
        
        Args:
            user_id: ID do usuário
            context_id: ID do contexto
        """
        if user_id not in self.active_connections:
            return

        # Remove da assinatura do usuário
        self.active_connections[user_id]["context_subscriptions"].discard(context_id)
        
        # Remove dos assinantes do contexto
        if context_id in self.context_subscribers:
            self.context_subscribers[context_id].discard(user_id)
            if not self.context_subscribers[context_id]:
                del self.context_subscribers[context_id]
        
        logger.info(f"Usuário {user_id} desinscrito do contexto {context_id}")
        
        await self.send_personal_message(user_id, {
            "type": "context_unsubscribed",
            "context_id": str(context_id),
            "message": "Desinscrito das notificações do contexto"
        })

    async def send_personal_message(self, user_id: UUID, message: dict):
        """
        Envia mensagem para um usuário específico.
        
        Args:
            user_id: ID do usuário
            message: Mensagem a enviar
        """
        if user_id in self.active_connections:
            try:
                websocket = self.active_connections[user_id]["websocket"]
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Erro ao enviar mensagem para {user_id}: {e}")
                self.disconnect(user_id)

    async def broadcast_to_context(self, context_id: UUID, message: dict):
        """
        Envia mensagem para todos os assinantes de um contexto.
        
        Args:
            context_id: ID do contexto
            message: Mensagem a enviar
        """
        if context_id not in self.context_subscribers:
            return

        subscribers = self.context_subscribers[context_id].copy()
        for user_id in subscribers:
            await self.send_personal_message(user_id, message)

    async def notify_upload_progress(
        self,
        context_id: UUID,
        upload_id: UUID,
        status: MediaUploadStatus,
        progress: Optional[int] = None,
        message: Optional[str] = None
    ):
        """
        Notifica progresso de upload.
        
        Args:
            context_id: ID do contexto
            upload_id: ID do upload
            status: Status do upload
            progress: Progresso em porcentagem (0-100)
            message: Mensagem adicional
        """
        notification = {
            "type": "upload_progress",
            "upload_id": str(upload_id),
            "context_id": str(context_id),
            "status": status.value,
            "progress": progress,
            "message": message,
            "timestamp": str(UUID)  # TODO: usar timestamp real
        }
        
        await self.broadcast_to_context(context_id, notification)

    async def notify_upload_completed(
        self,
        context_id: UUID,
        upload_id: UUID,
        file_url: str,
        thumbnail_url: Optional[str] = None
    ):
        """
        Notifica conclusão de upload.
        
        Args:
            context_id: ID do contexto
            upload_id: ID do upload
            file_url: URL do arquivo
            thumbnail_url: URL do thumbnail (opcional)
        """
        notification = {
            "type": "upload_completed",
            "upload_id": str(upload_id),
            "context_id": str(context_id),
            "file_url": file_url,
            "thumbnail_url": thumbnail_url,
            "message": "Upload concluído com sucesso"
        }
        
        await self.broadcast_to_context(context_id, notification)

    async def notify_upload_failed(
        self,
        context_id: UUID,
        upload_id: UUID,
        error_message: str
    ):
        """
        Notifica falha no upload.
        
        Args:
            context_id: ID do contexto
            upload_id: ID do upload
            error_message: Mensagem de erro
        """
        notification = {
            "type": "upload_failed",
            "upload_id": str(upload_id),
            "context_id": str(context_id),
            "error": error_message,
            "message": "Falha no upload"
        }
        
        await self.broadcast_to_context(context_id, notification)

    async def notify_quota_warning(
        self,
        context_id: UUID,
        usage_percentage: float,
        available_space_mb: int
    ):
        """
        Notifica aviso de quota.
        
        Args:
            context_id: ID do contexto
            usage_percentage: Porcentagem de uso
            available_space_mb: Espaço disponível em MB
        """
        notification = {
            "type": "quota_warning",
            "context_id": str(context_id),
            "usage_percentage": usage_percentage,
            "available_space_mb": available_space_mb,
            "message": f"Quota em {usage_percentage:.1f}% de uso"
        }
        
        await self.broadcast_to_context(context_id, notification)

    def get_connection_stats(self) -> dict:
        """
        Obtém estatísticas das conexões.
        
        Returns:
            dict: Estatísticas das conexões
        """
        return {
            "active_connections": len(self.active_connections),
            "context_subscriptions": len(self.context_subscribers),
            "total_subscriptions": sum(
                len(subscribers) for subscribers in self.context_subscribers.values()
            )
        }


# Instância global do gerenciador
media_ws_manager = MediaWebSocketManager()


@router.websocket("/ws/media")
async def websocket_endpoint(
    websocket: WebSocket,
    user: User = Depends(get_current_user_websocket),
    db: Session = Depends(get_db)
):
    """
    Endpoint WebSocket para o sistema de mídia.
    
    Args:
        websocket: Conexão WebSocket
        user: Usuário autenticado
        db: Sessão do banco de dados
    """
    await media_ws_manager.connect(websocket, user)
    
    try:
        while True:
            # Recebe mensagens do cliente
            data = await websocket.receive_text()
            message = json.loads(data)
            
            message_type = message.get("type")
            
            if message_type == "subscribe_context":
                context_id = UUID(message.get("context_id"))
                await media_ws_manager.subscribe_to_context(user.id, context_id)
                
            elif message_type == "unsubscribe_context":
                context_id = UUID(message.get("context_id"))
                await media_ws_manager.unsubscribe_from_context(user.id, context_id)
                
            elif message_type == "ping":
                await media_ws_manager.send_personal_message(user.id, {
                    "type": "pong",
                    "message": "WebSocket ativo"
                })
                
            else:
                await media_ws_manager.send_personal_message(user.id, {
                    "type": "error",
                    "message": f"Tipo de mensagem não reconhecido: {message_type}"
                })
                
    except WebSocketDisconnect:
        media_ws_manager.disconnect(user.id)
    except Exception as e:
        logger.error(f"Erro no WebSocket para usuário {user.id}: {e}")
        media_ws_manager.disconnect(user.id)
