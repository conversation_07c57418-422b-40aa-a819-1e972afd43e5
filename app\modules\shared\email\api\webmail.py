"""Webmail API endpoints for Email module."""

import logging  # noqa: E402
import uuid
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, status, Query, File, UploadFile  # noqa: E402
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_current_user, get_db  # noqa: E402
from app.models.user import User

from app.modules.shared.email.models import EmailAccount, EmailMetadata  # noqa: E402
from app.modules.shared.email.api.schemas import (
    EmailMetadataRead,
    EmailContent,
    EmailSend,
    EmailMove,
    EmailRead,
    EmailFlag,
    MailboxCreate,
)
from app.modules.shared.email.services.email_actions_service import (
    EmailActionsService,
)  # noqa: E402
from app.modules.shared.email.services.quota_service import QuotaService
from app.modules.shared.email.services.auth_service import EmailAuthService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/webmail", tags=["Webmail"])

# Initialize services
email_actions_service = EmailActionsService()
quota_service = QuotaService()
auth_service = EmailAuthService()


# Helper function to get the user's email account
async def get_user_email_account(
    db: AsyncSession, current_user: User, account_id: Optional[uuid.UUID] = None
) -> EmailAccount:
    """Get the user's email account.

    If account_id is provided, checks if the user has access to that account.
    If account_id is not provided, returns the user's primary email account.
    """
    if account_id:
        account = await db.get(EmailAccount, account_id)
        if not account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Email account not found",
            )

        # Check if the user has access to this account
        if account.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to access this email account",
            )

        return account
    else:
        # Get the user's primary email account
        # This is a simplified approach - in a real implementation, you might
        # want to allow the user to select their "active" email account
        accounts = await db.execute(
            EmailAccount.select().where(EmailAccount.user_id == current_user.id)
        )
        account = accounts.scalars().first()

        if not account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No email account found for this user",
            )

        return account


@router.get("/mailboxes", response_model=List[str])
async def list_mailboxes(
    account_id: Optional[uuid.UUID] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """List mailboxes for the authenticated user."""
    account = await get_user_email_account(db, current_user, account_id)
    return await email_actions_service.get_mailboxes(db, account_id=account.id)


@router.post("/mailboxes", status_code=status.HTTP_201_CREATED)
async def create_mailbox(
    mailbox_in: MailboxCreate,
    account_id: Optional[uuid.UUID] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Create a new mailbox."""
    account = await get_user_email_account(db, current_user, account_id)
    await email_actions_service.create_mailbox(
        db, account_id=account.id, mailbox_name=mailbox_in.name
    )
    return {"message": f"Mailbox '{mailbox_in.name}' created successfully"}


@router.delete("/mailboxes/{mailbox_name}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_mailbox(
    mailbox_name: str,
    account_id: Optional[uuid.UUID] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Delete a mailbox."""
    account = await get_user_email_account(db, current_user, account_id)
    await email_actions_service.delete_mailbox(db, account_id=account.id, mailbox_name=mailbox_name)
    return None


@router.get("/emails", response_model=List[EmailMetadataRead])
async def list_emails(
    mailbox: str = "INBOX",
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    unread_only: bool = False,
    flagged_only: bool = False,
    search: Optional[str] = None,
    account_id: Optional[uuid.UUID] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """List emails with pagination and filtering."""
    account = await get_user_email_account(db, current_user, account_id)

    filters = {}
    if unread_only:
        filters["is_read"] = False
    if flagged_only:
        filters["is_flagged"] = True
    if search:
        filters["search"] = search

    return await email_actions_service.get_emails(
        db,
        account_id=account.id,
        mailbox=mailbox,
        page=page,
        per_page=per_page,
        filters=filters,
    )


@router.get("/emails/{email_id}", response_model=EmailContent)
async def get_email(
    email_id: uuid.UUID,
    account_id: Optional[uuid.UUID] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get a specific email with content."""
    account = await get_user_email_account(db, current_user, account_id)

    # Get the email metadata
    email_metadata = await db.get(EmailMetadata, email_id)
    if not email_metadata or email_metadata.email_account_id != account.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email not found",
        )

    # Get the email content
    return await email_actions_service.get_email(db, email_id=email_id)


@router.post("/emails", status_code=status.HTTP_201_CREATED)
async def send_email(
    email_in: EmailSend,
    account_id: Optional[uuid.UUID] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Send a new email."""
    account = await get_user_email_account(db, current_user, account_id)

    # Check quota before sending
    if not await quota_service.check_quota(
        db,
        account_id=account.id,
        size_to_add=len(email_in.html_body or "") + len(email_in.text_body or ""),
    ):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Quota exceeded",
        )

    await email_actions_service.send_email(
        db,
        from_account_id=account.id,
        to_addresses=email_in.to_addresses,
        cc_addresses=email_in.cc_addresses,
        bcc_addresses=email_in.bcc_addresses,
        subject=email_in.subject,
        html_body=email_in.html_body,
        text_body=email_in.text_body,
        attachments=email_in.attachments,
    )

    return {"message": "Email sent successfully"}


@router.put("/emails/{email_id}/read", status_code=status.HTTP_204_NO_CONTENT)
async def mark_email_read(
    email_id: uuid.UUID,
    read_status: EmailRead,
    account_id: Optional[uuid.UUID] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Mark email as read/unread."""
    account = await get_user_email_account(db, current_user, account_id)

    # Check if the email belongs to this account
    email_metadata = await db.get(EmailMetadata, email_id)
    if not email_metadata or email_metadata.email_account_id != account.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email not found",
        )

    await email_actions_service.mark_as_read(db, email_id=email_id, is_read=read_status.is_read)
    return None


@router.put("/emails/{email_id}/move", status_code=status.HTTP_204_NO_CONTENT)
async def move_email(
    email_id: uuid.UUID,
    move_to: EmailMove,
    account_id: Optional[uuid.UUID] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Move email to another mailbox."""
    account = await get_user_email_account(db, current_user, account_id)

    # Check if the email belongs to this account
    email_metadata = await db.get(EmailMetadata, email_id)
    if not email_metadata or email_metadata.email_account_id != account.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email not found",
        )

    await email_actions_service.move_email(
        db, email_id=email_id, target_mailbox=move_to.target_mailbox
    )
    return None


@router.delete("/emails/{email_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_email(
    email_id: uuid.UUID,
    permanent: bool = False,
    account_id: Optional[uuid.UUID] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Delete an email."""
    account = await get_user_email_account(db, current_user, account_id)

    # Check if the email belongs to this account
    email_metadata = await db.get(EmailMetadata, email_id)
    if not email_metadata or email_metadata.email_account_id != account.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Email not found",
        )

    await email_actions_service.delete_email(db, email_id=email_id, permanent=permanent)
    return None
