'use client';

import { useMemo } from 'react';
import useSWR from 'swr';
import { api } from '@/lib/apiClient';
import { z } from 'zod';
// Product type enum for EShop products
export type ProductType = 'physical' | 'digital' | 'service';

// Schema para os dados que o formulário de criação/edição validará
export const productFormSchema = z.object({
    name: z.string().min(3, "O nome deve ter pelo menos 3 caracteres."),
    description_short: z.string().optional(),
    description_long: z.string().optional(),
    base_price: z.number().min(0.01, "O preço deve ser maior que zero."),
    category_id: z.string().uuid("Selecione uma categoria válida."),
    supplier_id: z.string().uuid().optional(),
    is_active: z.boolean().default(true),
    product_type: z.enum(['physical', 'digital', 'service']).default('physical'),
    stock_quantity: z.number().min(0, "A quantidade em estoque deve ser maior ou igual a zero.").optional(),
    sku: z.string().optional(),
    weight: z.number().min(0).optional(),
    dimensions: z.object({
        length: z.number().min(0).optional(),
        width: z.number().min(0).optional(),
        height: z.number().min(0).optional(),
    }).optional(),
    tags: z.array(z.string()).optional(),
    images: z.array(z.string().url()).optional(),
});

// Transform schema to handle empty strings and convert to appropriate types
export const productFormSchemaTransformed = productFormSchema.transform((data) => ({
    ...data,
    supplier_id: data.supplier_id === '' ? null : data.supplier_id,
    stock_quantity: data.stock_quantity || 0,
}));

// Tipagem para o formulário
export type ProductFormData = z.infer<typeof productFormSchema>;

// Tipagem para os dados enviados para a API
export type ProductCreatePayload = z.infer<typeof productFormSchemaTransformed>;
export type ProductUpdatePayload = ProductCreatePayload;

// Schema de validação para um único produto, alinhado com o backend
const productSchema = z.object({
  id: z.string().uuid(),
  name: z.string(),
  description_short: z.string().nullable().optional(),
  description_long: z.string().nullable().optional(),
  base_price: z.number(),
  product_type: z.enum(['physical', 'digital', 'service']),
  is_active: z.boolean(),
  category_id: z.string().uuid(),
  supplier_id: z.string().uuid().nullable().optional(),
  variant_groups: z.array(z.any()).optional(),
  modifier_groups: z.array(z.any()).optional(),
  optional_groups: z.array(z.any()).optional(),
  // Campos opcionais que podem não estar presentes
  stock_quantity: z.number().optional(),
  sku: z.string().nullable().optional(),
  weight: z.number().nullable().optional(),
  dimensions: z.object({
    length: z.number().nullable().optional(),
    width: z.number().nullable().optional(),
    height: z.number().nullable().optional(),
  }).nullable().optional(),
  tags: z.array(z.string()).optional(),
  images: z.array(z.string()).optional(),
  rating: z.number().nullable().optional(),
  review_count: z.number().optional(),
  created_at: z.string().optional(),
  updated_at: z.string().optional(),
  // Dados relacionados
  category_name: z.string().optional(),
  vendor_name: z.string().optional(),
});

// Schema para a resposta da API (uma lista de produtos)
const productsResponseSchema = z.object({
  items: z.array(productSchema),
  total: z.number(),
  page: z.number(),
  size: z.number(),
  pages: z.number(),
});

// Tipagem inferida a partir do schema
export interface EshopProduct {
    id: string;
    name: string;
    description_short?: string | null;
    description_long?: string | null;
    base_price: number;
    product_type: 'physical' | 'digital' | 'service';
    is_active: boolean;
    category_id: string;
    supplier_id?: string | null;
    variant_groups?: any[];
    modifier_groups?: any[];
    optional_groups?: any[];
    stock_quantity?: number;
    sku?: string | null;
    weight?: number | null;
    dimensions?: {
        length?: number | null;
        width?: number | null;
        height?: number | null;
    } | null;
    tags?: string[];
    images?: string[];
    rating?: number | null;
    review_count?: number;
    created_at?: string;
    updated_at?: string;
    // Dados relacionados
    category_name?: string;
    vendor_name?: string;
}

export interface ProductsResponse {
    items: EshopProduct[];
    total: number;
    page: number;
    size: number;
    pages: number;
}

// O fetcher que o SWR usará
const fetcher = (url: string): Promise<ProductsResponse> => {
  const apiWithoutTenant = {
    get: (url: string) => {
      return fetch(`${process.env.NEXT_PUBLIC_API_URL}${url}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }).then(res => {
        if (!res.ok) {
          throw new Error(`HTTP error! status: ${res.status}`);
        }
        return res.json();
      });
    }
  };
  
  return apiWithoutTenant.get(url).then(res => productsResponseSchema.parse(res));
};

export function useUniversalEshopProducts(
  page: number = 1,
  size: number = 20,
  search?: string,
  categoryId?: string,
  sortBy?: string
) {
  // Constrói a URL com filtros
  const params = new URLSearchParams();
  
  params.append('page', page.toString());
  params.append('size', size.toString());
  
  if (search) {
    params.append('search', search);
  }
  
  if (categoryId && categoryId !== 'all') {
    params.append('category_id', categoryId);
  }
  
  if (sortBy) {
    params.append('sort_by', sortBy);
  }
  
  const url = `/eshop/products/universal?${params.toString()}`;
    
  const { data, error, isLoading, mutate } = useSWR(url, fetcher);

  const createProduct = async (productData: ProductCreatePayload) => {
    try {
        const newProduct = await api.post('/eshop/products/universal', productData);
        // Atualiza o cache local do SWR com os novos dados
        if (data) {
            const updatedData = {
                ...data,
                items: [newProduct, ...data.items],
                total: data.total + 1
            };
            mutate(updatedData, false);
        }
        return newProduct;
    } catch (error) {
        console.error("Failed to create product:", error);
        throw error;
    }
  };

  const updateProduct = async (productId: string, productData: ProductUpdatePayload) => {
    try {
        const updatedProduct = await api.put(`/eshop/products/universal/${productId}`, productData);
        // Atualiza o cache local do SWR
        if (data) {
            const updatedData = {
                ...data,
                items: data.items.map(product => 
                    product.id === productId ? updatedProduct : product
                )
            };
            mutate(updatedData, false);
        }
        return updatedProduct;
    } catch (error) {
        console.error("Failed to update product:", error);
        throw error;
    }
  };

  const deleteProduct = async (productId: string) => {
    try {
        await api.delete(`/eshop/products/universal/${productId}`);
        // Remove o produto do cache local
        if (data) {
            const updatedData = {
                ...data,
                items: data.items.filter(product => product.id !== productId),
                total: data.total - 1
            };
            mutate(updatedData, false);
        }
    } catch (error) {
        console.error("Failed to delete product:", error);
        throw error;
    }
  };

  const toggleProductStatus = async (productId: string, isActive: boolean) => {
    try {
        const updatedProduct = await api.patch(`/eshop/products/universal/${productId}/status`, {
            is_active: isActive
        });
        // Atualiza o cache local do SWR
        if (data) {
            const updatedData = {
                ...data,
                items: data.items.map(product => 
                    product.id === productId ? { ...product, is_active: isActive } : product
                )
            };
            mutate(updatedData, false);
        }
        return updatedProduct;
    } catch (error) {
        console.error("Failed to toggle product status:", error);
        throw error;
    }
  };

  return {
    products: data?.items || [],
    total: data?.total || 0,
    page: data?.page || 1,
    size: data?.size || 20,
    pages: data?.pages || 0,
    isLoading,
    isError: error,
    mutateProducts: mutate,
    createProduct,
    updateProduct,
    deleteProduct,
    toggleProductStatus,
  };
}