from typing import List, Optional, Union, Dict
from uuid import UUID
import json
import hashlib

from sqlalchemy.orm import joinedload
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.modules.i18n import models, schemas


class TranslationService:
    # Cache TTL in seconds (1 hour)
    CACHE_TTL = 3600

    def __init__(self, db: AsyncSession = None):
        """Initialize the service with an optional database session."""
        self.db = db
        # Import Redis client here to avoid circular imports
        try:
            from app.modules.i18n.router import redis_client

            self.redis_client = redis_client
        except (ImportError, AttributeError):
            self.redis_client = None

    async def get_translation(
        self, db: AsyncSession, translation_id: int
    ) -> Optional[models.Translation]:
        """Get a translation by ID."""
        return await db.get(models.Translation, translation_id)

    async def get_translation_by_key_and_lang(
        self, db: AsyncSession, key_id: int, language_id: int
    ) -> Optional[models.Translation]:
        """Get a translation by key ID and language ID."""
        query = select(models.Translation).where(
            models.Translation.key_id == key_id, models.Translation.language_id == language_id
        )

        result = await db.execute(query)
        return result.scalar_one_or_none()

    async def create_translation(
        self, db: AsyncSession, translation: schemas.TranslationCreate
    ) -> models.Translation:
        """Create a new translation and invalidate cache."""
        db_translation = models.Translation(
            translated_text=translation.translated_text,
            is_approved=translation.is_approved,
            version=translation.version,
            key_id=translation.key_id,
            language_id=translation.language_id,
            # Set last_updated_by_user_id from the input schema
            last_updated_by_user_id=translation.last_updated_by_user_id,
        )

        # The unique constraint on the model will handle conflicts.
        db.add(db_translation)
        await db.commit()
        await db.refresh(db_translation)

        # Invalidate cache for this language
        await self._invalidate_language_cache(db, db_translation.language_id)

        return db_translation

    async def update_translation(
        self,
        db: AsyncSession,
        translation_db: models.Translation,
        translation_in: schemas.TranslationUpdate,
        user_id: Optional[UUID] = None,
    ) -> models.Translation:
        """Update a translation and invalidate cache if changed."""
        update_data = translation_in.model_dump(exclude_unset=True)

        changed = False
        for field, value in update_data.items():
            if getattr(translation_db, field) != value:
                setattr(translation_db, field, value)
                changed = True

        if user_id:
            if translation_db.last_updated_by_user_id != user_id:
                translation_db.last_updated_by_user_id = user_id
                changed = True  # Mesmo que o user_id seja o único campo alterado

        if changed:  # Incrementa a versão apenas se algo mudou
            translation_db.version += 1
            # translation_db.updated_at = datetime.utcnow() # O TimestampMixin deve cuidar disso

            db.add(translation_db)
            await db.commit()
            await db.refresh(translation_db)

            # Invalidate cache for this language
            await self._invalidate_language_cache(db, translation_db.language_id)

        return translation_db

    async def delete_translation(
        self, db: AsyncSession, translation_id: int
    ) -> Optional[models.Translation]:
        """Delete a translation and invalidate cache."""
        db_translation = await self.get_translation(db, translation_id)
        if db_translation:
            language_id = db_translation.language_id
            await db.delete(db_translation)
            await db.commit()

            # Invalidate cache for this language
            await self._invalidate_language_cache(db, language_id)

        return db_translation

    async def get_translations_for_language_code(
        self,
        db: AsyncSession,
        lang_code: str,
        modules: Optional[List[str]] = None,
        for_caching: bool = False,
    ) -> Union[List[models.Translation], Dict[str, str]]:
        """
        Get translations for a language code, optionally filtered by modules.

        Args:
            db: Database session
            lang_code: Language code
            modules: Optional list of modules to filter by
            for_caching: If True, return a dict for caching, otherwise return a list of models

        Returns:
            Either a dict of key_string -> translated_text (if for_caching=True)
            or a list of Translation models
        """
        query = (
            select(models.Translation)
            .join(models.Language, models.Translation.language_id == models.Language.id)
            .join(models.TranslationKey, models.Translation.key_id == models.TranslationKey.id)
            .where(models.Language.code == lang_code)
            .where(models.Translation.is_approved == True)  # Only approved translations
            .options(
                joinedload(models.Translation.translation_key)  # Eager load key for key_string
            )
        )

        if modules:
            query = query.where(models.TranslationKey.module.in_(modules))

        result = await db.execute(query)
        translations = result.scalars().all()

        if for_caching:  # Return dict for caching as used by router
            return {t.translation_key.key_string: t.translated_text for t in translations}
        return list(translations)  # Return list of models for other uses

    async def get_all_for_language(self, language_id: int) -> List[models.Translation]:
        """
        Get all translations for a language, with Redis caching.

        Args:
            language_id: The language ID

        Returns:
            List of Translation models
        """
        if not self.db:
            raise ValueError("Database session is required for this operation")

        # Check cache first if Redis is available
        if self.redis_client:
            cache_key = f"translations:{language_id}"
            cached_data = await self.redis_client.get(cache_key)

            if cached_data:
                # Parse cached data and return
                try:
                    translations_dict = json.loads(cached_data)
                    # Convert back to models (simplified for now)
                    # In a real implementation, you might want to reconstruct full models
                    return await self._get_translations_from_db(language_id)
                except json.JSONDecodeError:
                    # Invalid cache data, continue to DB query
                    pass

        # Get from database
        translations = await self._get_translations_from_db(language_id)

        # Cache the result if Redis is available
        if self.redis_client and translations:
            cache_key = f"translations:{language_id}"
            translations_dict = {
                t.translation_key.key_string: t.translated_text for t in translations
            }
            await self.redis_client.set(cache_key, json.dumps(translations_dict), ex=self.CACHE_TTL)

        return translations

    async def _get_translations_from_db(self, language_id: int) -> List[models.Translation]:
        """Get all translations for a language from the database."""
        query = (
            select(models.Translation)
            .filter(models.Translation.language_id == language_id)
            .filter(models.Translation.is_approved == True)
            .options(joinedload(models.Translation.translation_key))
        )
        result = await self.db.execute(query)
        return list(result.scalars().all())

    async def _invalidate_language_cache(self, db: AsyncSession, language_id: int) -> None:
        """Invalidate cache for a language."""
        if self.redis_client:
            cache_key = f"translations:{language_id}"
            await self.redis_client.delete(cache_key)

    async def get_etag_for_language(self, language_id: int) -> str:
        """
        Generate an ETag for a language's translations.

        Args:
            language_id: The language ID

        Returns:
            ETag string in the format "hash"
        """
        if not self.db:
            raise ValueError("Database session is required for this operation")

        translations = await self.get_all_for_language(language_id)
        translations_dict = {t.translation_key.key_string: t.translated_text for t in translations}

        # Generate hash from sorted JSON
        hash_value = hashlib.md5(json.dumps(translations_dict, sort_keys=True).encode()).hexdigest()
        return f'"{hash_value}"'  # ETags should be quoted
