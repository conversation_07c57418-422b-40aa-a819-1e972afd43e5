"""Pricing schemas for CRM module."""

import uuid  # noqa: E402
from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field, ConfigDict

from app.modules.shared.crm.models.pricing import PricingRuleType  # noqa: E402


# Base PricingTier Schema
class PricingTierBase(BaseModel):
    """Base schema for PricingTier."""

    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    is_active: bool = True
    default_discount_percentage: float = 0.0


# Schema for creating a new pricing tier
class PricingTierCreate(PricingTierBase):
    """Schema for creating a new PricingTier."""


# Schema for updating a pricing tier
class PricingTierUpdate(BaseModel):
    """Schema for updating a PricingTier."""

    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    is_active: Optional[bool] = None
    default_discount_percentage: Optional[float] = None


# Schema for reading a pricing tier
class PricingTierRead(PricingTierBase):
    """Schema for reading a PricingTier."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Base CustomerPricingAssignment Schema
class CustomerPricingAssignmentBase(BaseModel):
    """Base schema for CustomerPricingAssignment."""

    start_date: datetime = Field(default_factory=datetime.utcnow)
    end_date: Optional[datetime] = None
    is_active: bool = True
    custom_discount_percentage: Optional[float] = None
    notes: Optional[str] = None


# Schema for creating a new customer pricing assignment
class CustomerPricingAssignmentCreate(CustomerPricingAssignmentBase):
    """Schema for creating a new CustomerPricingAssignment."""

    pricing_tier_id: uuid.UUID
    account_id: uuid.UUID


# Schema for updating a customer pricing assignment
class CustomerPricingAssignmentUpdate(BaseModel):
    """Schema for updating a CustomerPricingAssignment."""

    end_date: Optional[datetime] = None
    is_active: Optional[bool] = None
    custom_discount_percentage: Optional[float] = None
    notes: Optional[str] = None


# Schema for reading a customer pricing assignment
class CustomerPricingAssignmentRead(CustomerPricingAssignmentBase):
    """Schema for reading a CustomerPricingAssignment."""

    id: uuid.UUID
    pricing_tier_id: uuid.UUID
    account_id: uuid.UUID
    tenant_id: uuid.UUID
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Base PricingRule Schema
class PricingRuleBase(BaseModel):
    """Base schema for PricingRule."""

    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    rule_type: PricingRuleType

    # Target
    product_category: Optional[str] = None
    product_id: Optional[str] = None

    # Rule parameters
    discount_percentage: Optional[float] = None
    discount_amount: Optional[float] = None
    custom_price: Optional[float] = None
    min_quantity: Optional[int] = None

    # Validity
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None

    # Status
    is_active: bool = True

    # Additional conditions
    conditions: Optional[Dict[str, Any]] = None


# Schema for creating a new pricing rule
class PricingRuleCreate(PricingRuleBase):
    """Schema for creating a new PricingRule."""

    pricing_tier_id: uuid.UUID


# Schema for updating a pricing rule
class PricingRuleUpdate(BaseModel):
    """Schema for updating a PricingRule."""

    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    rule_type: Optional[PricingRuleType] = None

    # Target
    product_category: Optional[str] = None
    product_id: Optional[str] = None

    # Rule parameters
    discount_percentage: Optional[float] = None
    discount_amount: Optional[float] = None
    custom_price: Optional[float] = None
    min_quantity: Optional[int] = None

    # Validity
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None

    # Status
    is_active: Optional[bool] = None

    # Additional conditions
    conditions: Optional[Dict[str, Any]] = None


# Schema for reading a pricing rule
class PricingRuleRead(PricingRuleBase):
    """Schema for reading a PricingRule."""

    id: uuid.UUID
    pricing_tier_id: uuid.UUID
    tenant_id: uuid.UUID
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)
