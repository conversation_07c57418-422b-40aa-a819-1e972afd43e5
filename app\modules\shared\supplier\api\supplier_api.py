from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi import status as http_status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, TYPE_CHECKING
import uuid

from app.core.db_dependencies import get_db
from app.modules.core.tenants.models.tenant import Tenant
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    get_current_tenant,
    require_tenant_role
)
from app.modules.core.roles.models.roles import RolePermissions, TenantRole

if TYPE_CHECKING:
    from app.modules.core.users.models.user import User

from app.modules.shared.supplier.schemas.supplier import (
    SupplierCreate,
    SupplierUpdate,
    SupplierRead,
    SupplierWithProducts,
    ProductSupplierCreate,
    ProductSupplierUpdate,
    ProductSupplierRead,
    ProductSupplierWithDetails,
    SupplierPriceHistoryCreate,
    SupplierPriceHistoryRead,
    CompetitivePricingView,
    AutoReplenishmentSuggestion
)
from pydantic import BaseModel, Field
from decimal import Decimal
from datetime import date
from typing import List
from app.modules.shared.supplier.services.supplier_service import (
    supplier_service,
    product_supplier_service
)
from app.modules.shared.shopping_list.services.financial_integration_service import (
    financial_integration_service
)
from app.modules.shared.supplier.models.supplier import SupplierStatus
from app.core.exceptions import NotFoundError

router = APIRouter()


# === Schemas for Invoice Registration ===

class SupplierInvoiceCreate(BaseModel):
    """Schema for supplier to register an invoice."""
    shopping_list_items: List[uuid.UUID] = Field(..., description="Shopping list items delivered")
    invoice_number: str = Field(..., max_length=50, description="Invoice number")
    invoice_date: date = Field(..., description="Invoice date")
    total_amount: Decimal = Field(..., gt=0, description="Total invoice amount")
    delivery_note_number: Optional[str] = Field(None, max_length=50, description="Delivery note number")
    due_date: Optional[date] = Field(None, description="Payment due date")
    notes: Optional[str] = Field(None, description="Additional notes")


class SupplierInvoiceRead(BaseModel):
    """Schema for reading supplier invoice transactions."""
    id: uuid.UUID
    tenant_id: uuid.UUID
    amount: Decimal
    description: str
    transaction_date: date
    reference_number: str
    notes: Optional[str] = None
    payment_method_id: Optional[uuid.UUID] = None
    is_paid: bool = Field(default=False, description="Whether invoice is paid")

    class Config:
        from_attributes = True


# === Supplier Endpoints ===

@router.get(
    "/suppliers",
    response_model=List[SupplierRead],
    summary="Listar Fornecedores",
    description="Lista todos os fornecedores do tenant.",
)
async def list_suppliers(
    status: Optional[SupplierStatus] = Query(None, description="Filtrar por status"),
    skip: int = Query(0, ge=0, description="Número de registros para pular"),
    limit: int = Query(100, ge=1, le=1000, description="Limite de registros"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
):
    """
    Lista fornecedores do tenant.
    Requer role STAFF, MANAGER ou OWNER.
    """
    try:
        suppliers = await supplier_service.get_suppliers(
            db=db,
            tenant_id=current_tenant.id,
            status=status,
            skip=skip,
            limit=limit
        )
        return suppliers
    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch suppliers"
        )


@router.post(
    "/suppliers",
    response_model=SupplierRead,
    status_code=http_status.HTTP_201_CREATED,
    summary="Criar Fornecedor",
    description="Cria um novo fornecedor.",
)
async def create_supplier(
    supplier_data: SupplierCreate,
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.ADMIN_ROLES, tenant_id_source="header")
    ),
):
    """
    Cria novo fornecedor.
    Requer role MANAGER ou OWNER.
    """
    try:
        supplier = await supplier_service.create_supplier(
            db=db,
            supplier_data=supplier_data,
            tenant_id=current_tenant.id
        )
        return supplier
    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create supplier"
        )


@router.get(
    "/suppliers/{supplier_id}",
    response_model=SupplierRead,
    summary="Obter Fornecedor",
    description="Obtém detalhes de um fornecedor específico.",
)
async def get_supplier(
    supplier_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
):
    """
    Obtém fornecedor por ID.
    Requer role STAFF, MANAGER ou OWNER.
    """
    try:
        supplier = await supplier_service.get_supplier_by_id(
            db=db,
            supplier_id=supplier_id,
            tenant_id=current_tenant.id
        )
        if not supplier:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Supplier not found"
            )
        return supplier
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch supplier"
        )


@router.put(
    "/suppliers/{supplier_id}",
    response_model=SupplierRead,
    summary="Atualizar Fornecedor",
    description="Atualiza dados de um fornecedor.",
)
async def update_supplier(
    supplier_id: uuid.UUID,
    supplier_data: SupplierUpdate,
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.ADMIN_ROLES, tenant_id_source="header")
    ),
):
    """
    Atualiza fornecedor.
    Requer role MANAGER ou OWNER.
    """
    try:
        supplier = await supplier_service.update_supplier(
            db=db,
            supplier_id=supplier_id,
            supplier_data=supplier_data,
            tenant_id=current_tenant.id
        )
        return supplier
    except NotFoundError as e:
        raise HTTPException(status_code=http_status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update supplier"
        )


@router.delete(
    "/suppliers/{supplier_id}",
    response_model=SupplierRead,
    summary="Excluir Fornecedor",
    description="Exclui um fornecedor (soft delete).",
)
async def delete_supplier(
    supplier_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role([TenantRole.OWNER.value], tenant_id_source="header")
    ),
):
    """
    Exclui fornecedor (soft delete).
    Requer role OWNER.
    """
    try:
        supplier = await supplier_service.delete_supplier(
            db=db,
            supplier_id=supplier_id,
            tenant_id=current_tenant.id
        )
        return supplier
    except NotFoundError as e:
        raise HTTPException(status_code=http_status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete supplier"
        )


# === Product Supplier Endpoints ===

@router.get(
    "/product-suppliers",
    response_model=List[ProductSupplierRead],
    summary="Listar Associações Produto-Fornecedor",
    description="Lista associações entre produtos e fornecedores.",
)
async def list_product_suppliers(
    inventory_item_id: Optional[uuid.UUID] = Query(None, description="Filtrar por produto"),
    supplier_id: Optional[uuid.UUID] = Query(None, description="Filtrar por fornecedor"),
    active_only: bool = Query(True, description="Apenas associações ativas"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
):
    """
    Lista associações produto-fornecedor.
    Requer role STAFF, MANAGER ou OWNER.
    """
    try:
        product_suppliers = await product_supplier_service.get_product_suppliers(
            db=db,
            tenant_id=current_tenant.id,
            inventory_item_id=inventory_item_id,
            supplier_id=supplier_id,
            active_only=active_only
        )
        return product_suppliers
    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch product suppliers"
        )


@router.post(
    "/product-suppliers",
    response_model=ProductSupplierRead,
    status_code=http_status.HTTP_201_CREATED,
    summary="Criar Associação Produto-Fornecedor",
    description="Cria associação entre produto e fornecedor.",
)
async def create_product_supplier(
    product_supplier_data: ProductSupplierCreate,
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.ADMIN_ROLES, tenant_id_source="header")
    ),
):
    """
    Cria associação produto-fornecedor.
    Requer role MANAGER ou OWNER.
    """
    try:
        product_supplier = await product_supplier_service.create_product_supplier(
            db=db,
            product_supplier_data=product_supplier_data,
            tenant_id=current_tenant.id
        )
        return product_supplier
    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create product supplier association"
        )


# === Competitive Pricing Endpoints ===

@router.get(
    "/competitive-pricing/{inventory_item_id}",
    response_model=CompetitivePricingView,
    summary="Obter Preços Competitivos",
    description="Obtém visão de preços competitivos para um produto.",
)
async def get_competitive_pricing(
    inventory_item_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
):
    """
    Obtém preços competitivos para um produto.
    Requer role STAFF, MANAGER ou OWNER.
    """
    try:
        pricing_view = await product_supplier_service.get_competitive_pricing(
            db=db,
            inventory_item_id=inventory_item_id,
            tenant_id=current_tenant.id
        )
        return pricing_view
    except NotFoundError as e:
        raise HTTPException(status_code=http_status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch competitive pricing"
        )


@router.get(
    "/auto-replenishment/suggestions",
    response_model=List[AutoReplenishmentSuggestion],
    summary="Obter Sugestões de Reposição Automática",
    description="Obtém sugestões de reposição automática para itens com estoque baixo.",
)
async def get_auto_replenishment_suggestions(
    low_stock_threshold: int = Query(10, ge=1, description="Limite de estoque baixo"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
):
    """
    Obtém sugestões de reposição automática.
    Requer role STAFF, MANAGER ou OWNER.
    """
    try:
        suggestions = await product_supplier_service.get_auto_replenishment_suggestions(
            db=db,
            tenant_id=current_tenant.id,
            low_stock_threshold=low_stock_threshold
        )
        return suggestions
    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch auto replenishment suggestions"
        )


# === Supplier Invoice Endpoints ===

@router.post(
    "/suppliers/{supplier_id}/invoices",
    response_model=SupplierInvoiceRead,
    status_code=http_status.HTTP_201_CREATED,
    summary="Registrar Fatura do Fornecedor",
    description="Permite ao fornecedor registrar uma fatura para itens entregues.",
)
async def register_supplier_invoice(
    supplier_id: uuid.UUID,
    invoice_data: SupplierInvoiceCreate,
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role([TenantRole.SUPPLIER.value], tenant_id_source="header")
    ),
):
    """
    Registra fatura do fornecedor no sistema financeiro.
    Requer role SUPPLIER.
    """
    try:
        # Verify supplier belongs to current user
        supplier = await supplier_service.get_supplier_by_id(
            db=db,
            supplier_id=supplier_id,
            tenant_id=current_tenant.id
        )

        if not supplier:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Supplier not found"
            )

        # Register invoice as financial transaction
        transaction = await financial_integration_service.register_supplier_invoice(
            db=db,
            shopping_list_items=invoice_data.shopping_list_items,
            supplier_id=supplier_id,
            tenant_id=current_tenant.id,
            created_by=current_user.id,
            invoice_number=invoice_data.invoice_number,
            invoice_date=invoice_data.invoice_date,
            total_amount=invoice_data.total_amount,
            delivery_note_number=invoice_data.delivery_note_number,
            due_date=invoice_data.due_date,
            notes=invoice_data.notes
        )

        # Convert to response format
        return SupplierInvoiceRead(
            id=transaction.id,
            tenant_id=transaction.tenant_id,
            amount=transaction.amount,
            description=transaction.description,
            transaction_date=transaction.transaction_date,
            reference_number=transaction.reference_number,
            notes=transaction.notes,
            payment_method_id=transaction.payment_method_id,
            is_paid=transaction.payment_method_id is not None
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to register supplier invoice: {str(e)}"
        )


@router.get(
    "/suppliers/{supplier_id}/invoices",
    response_model=List[SupplierInvoiceRead],
    summary="Listar Faturas do Fornecedor",
    description="Lista faturas registradas pelo fornecedor.",
)
async def list_supplier_invoices(
    supplier_id: uuid.UUID,
    skip: int = Query(0, ge=0, description="Número de registros para pular"),
    limit: int = Query(100, ge=1, le=1000, description="Limite de registros"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
):
    """
    Lista faturas do fornecedor.
    Requer role STAFF, MANAGER ou OWNER.
    """
    try:
        # Get pending invoices for this supplier
        transactions = await financial_integration_service.get_pending_supplier_invoices(
            db=db,
            tenant_id=current_tenant.id,
            supplier_id=supplier_id,
            skip=skip,
            limit=limit
        )

        # Convert to response format
        invoices = []
        for transaction in transactions:
            invoice = SupplierInvoiceRead(
                id=transaction.id,
                tenant_id=transaction.tenant_id,
                amount=transaction.amount,
                description=transaction.description,
                transaction_date=transaction.transaction_date,
                reference_number=transaction.reference_number,
                notes=transaction.notes,
                payment_method_id=transaction.payment_method_id,
                is_paid=transaction.payment_method_id is not None
            )
            invoices.append(invoice)

        return invoices

    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch supplier invoices: {str(e)}"
        )
