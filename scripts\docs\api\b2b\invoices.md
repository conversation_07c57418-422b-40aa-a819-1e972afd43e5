# B2B - Invoices

**Categoria:** B2B
**Módulo:** Invoices
**Total de Endpoints:** 7
**Gerado em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [GET /api/financial/invoices/b2b-invoices/](#get-apifinancialinvoicesb2b-invoices) - List B2B Invoices
- [POST /api/financial/invoices/b2b-invoices/](#post-apifinancialinvoicesb2b-invoices) - Create B2B Invoice
- [GET /api/financial/invoices/b2b-invoices/stats/summary](#get-apifinancialinvoicesb2b-invoicesstatssummary) - Get B2B Invoice Stats
- [GET /api/financial/invoices/b2b-invoices/{invoice_id}](#get-apifinancialinvoicesb2b-invoicesinvoice-id) - Get B2B Invoice
- [POST /api/financial/invoices/b2b-invoices/{invoice_id}/download-token](#post-apifinancialinvoicesb2b-invoicesinvoice-iddownload-token) - Generate B2B Download Token
- [POST /api/financial/invoices/b2b-invoices/{invoice_id}/send](#post-apifinancialinvoicesb2b-invoicesinvoice-idsend) - Send B2B Invoice
- [POST /api/financial/invoices/b2b-invoices/{invoice_id}/upload](#post-apifinancialinvoicesb2b-invoicesinvoice-idupload) - Upload B2B Invoice File

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### B2BInvoiceCreate

**Descrição:** Schema for creating B2B invoices.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `invoice_type` | InvoiceType | ❌ | Must be B2B type |
| `customer_name` | string | ✅ | Customer name |
| `customer_email` | unknown | ❌ | Customer email |
| `customer_phone` | unknown | ❌ | Customer phone |
| `customer_address` | unknown | ❌ | Customer address |
| `customer_tax_id` | unknown | ❌ | Customer tax ID |
| `issue_date` | string | ✅ | Invoice issue date |
| `due_date` | unknown | ❌ | Payment due date |
| `tax_rate` | unknown | ❌ | Default tax rate percentage |
| `discount_amount` | unknown | ❌ | Discount amount |
| `payment_method` | unknown | ❌ | Payment method |
| `notes` | unknown | ❌ | Invoice notes |
| `terms_and_conditions` | unknown | ❌ | Terms and conditions |
| `order_id` | unknown | ❌ | Related order ID |
| `vendor_id` | string | ✅ | B2B vendor ID |
| `customer_b2b_id` | string | ✅ | B2B customer ID |
| `notify_on_view` | boolean | ❌ | Notify when invoice is viewed |
| `notify_on_due` | boolean | ❌ | Notify when invoice is due |
| `items` | Array[InvoiceItemCreate] | ❌ | Invoice items |

### B2BInvoiceDownloadRequest

**Descrição:** Schema for requesting B2B invoice download.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `expires_hours` | integer | ❌ | Token expiration in hours |

### B2BInvoiceListResponse

**Descrição:** Schema for B2B invoice list response.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `invoices` | Array[object] | ❌ | Invoice summaries |
| `total` | integer | ❌ | Total invoices |
| `page` | integer | ❌ | Current page |
| `limit` | integer | ❌ | Items per page |
| `total_pages` | integer | ❌ | Total pages |
| `stats` | unknown | ❌ | Summary statistics |

### B2BInvoiceResponse

**Descrição:** Schema for B2B invoice API responses.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `success` | boolean | ✅ | Operation success |
| `message` | string | ✅ | Response message |
| `invoice` | unknown | ❌ | Invoice data |
| `access_token` | unknown | ❌ | Download access token |
| `stats` | unknown | ❌ | Statistics data |

### B2BInvoiceStats

**Descrição:** Schema for B2B invoice statistics.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `total_invoices` | integer | ❌ | Total number of invoices |
| `total_amount` | string | ❌ | Total amount |
| `paid_amount` | string | ❌ | Total paid amount |
| `outstanding_amount` | string | ❌ | Total outstanding amount |
| `draft_count` | integer | ❌ | Draft invoices |
| `pending_count` | integer | ❌ | Pending invoices |
| `sent_count` | integer | ❌ | Sent invoices |
| `viewed_count` | integer | ❌ | Viewed invoices |
| `paid_count` | integer | ❌ | Paid invoices |
| `overdue_count` | integer | ❌ | Overdue invoices |
| `cancelled_count` | integer | ❌ | Cancelled invoices |
| `disputed_count` | integer | ❌ | Disputed invoices |
| `this_month_amount` | string | ❌ | This month amount |
| `last_month_amount` | string | ❌ | Last month amount |
| `avg_payment_days` | number | ❌ | Average payment days |

### Body_upload_b2b_invoice_file_api_financial_invoices_b2b_invoices__invoice_id__upload_post

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `file` | string | ✅ | - |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/financial/invoices/b2b-invoices/ {#get-apifinancialinvoicesb2b-invoices}

**Resumo:** List B2B Invoices
**Descrição:** Lista faturas B2B com filtros.

Usuários veem apenas suas faturas (como vendor ou customer).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `vendor_id` | string | query | ❌ | Filter by vendor ID |
| `customer_id` | string | query | ❌ | Filter by customer ID |
| `status` | string | query | ❌ | Filter by status |
| `overdue_only` | boolean | query | ❌ | Show only overdue invoices |
| `page` | integer | query | ❌ | Page number |
| `limit` | integer | query | ❌ | Items per page |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [B2BInvoiceListResponse](#b2binvoicelistresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/invoices/b2b-invoices/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/financial/invoices/b2b-invoices/ {#post-apifinancialinvoicesb2b-invoices}

**Resumo:** Create B2B Invoice
**Descrição:** Cria nova fatura B2B.

Apenas TVendorSupplier pode criar faturas B2B.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [B2BInvoiceCreate](#b2binvoicecreate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [B2BInvoiceResponse](#b2binvoiceresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/financial/invoices/b2b-invoices/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/financial/invoices/b2b-invoices/stats/summary {#get-apifinancialinvoicesb2b-invoicesstatssummary}

**Resumo:** Get B2B Invoice Stats
**Descrição:** Estatísticas de faturas B2B.

Filtros aplicados baseados no role do usuário.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `vendor_id` | string | query | ❌ | Filter by vendor ID |
| `customer_id` | string | query | ❌ | Filter by customer ID |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [B2BInvoiceStats](#b2binvoicestats)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/invoices/b2b-invoices/stats/summary" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/financial/invoices/b2b-invoices/{invoice_id} {#get-apifinancialinvoicesb2b-invoicesinvoice-id}

**Resumo:** Get B2B Invoice
**Descrição:** Busca fatura B2B por ID.

Apenas vendor ou customer podem acessar.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `invoice_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [B2BInvoiceResponse](#b2binvoiceresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/invoices/b2b-invoices/{invoice_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/financial/invoices/b2b-invoices/{invoice_id}/download-token {#post-apifinancialinvoicesb2b-invoicesinvoice-iddownload-token}

**Resumo:** Generate B2B Download Token
**Descrição:** Gera token para download da fatura B2B.

Vendor ou customer podem gerar token.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `invoice_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [B2BInvoiceDownloadRequest](#b2binvoicedownloadrequest)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [B2BInvoiceResponse](#b2binvoiceresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/financial/invoices/b2b-invoices/{invoice_id}/download-token" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/financial/invoices/b2b-invoices/{invoice_id}/send {#post-apifinancialinvoicesb2b-invoicesinvoice-idsend}

**Resumo:** Send B2B Invoice
**Descrição:** Envia fatura B2B para cliente.

Apenas TVendorSupplier proprietário pode enviar.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `invoice_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [B2BInvoiceResponse](#b2binvoiceresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/financial/invoices/b2b-invoices/{invoice_id}/send" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/financial/invoices/b2b-invoices/{invoice_id}/upload {#post-apifinancialinvoicesb2b-invoicesinvoice-idupload}

**Resumo:** Upload B2B Invoice File
**Descrição:** Faz upload do arquivo da fatura B2B.

Apenas o TVendorSupplier proprietário pode fazer upload.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `invoice_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `multipart/form-data`
**Schema:** [Body_upload_b2b_invoice_file_api_financial_invoices_b2b_invoices__invoice_id__upload_post](#body_upload_b2b_invoice_file_api_financial_invoices_b2b_invoices__invoice_id__upload_post)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [B2BInvoiceResponse](#b2binvoiceresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/financial/invoices/b2b-invoices/{invoice_id}/upload" \
  -H "Content-Type: multipart/form-data" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
