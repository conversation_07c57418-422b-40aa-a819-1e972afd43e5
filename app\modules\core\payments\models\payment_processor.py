"""Payment processor models for core payments module."""

import uuid  # noqa: E402
import enum
from sqlalchemy import Column, String, <PERSON>olean, ForeignKey, JSON, Enum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base  # noqa: E402


class PaymentProcessorType(str, enum.Enum):
    """Enum for payment processor types."""

    STRIPE = "stripe"
    PAYPAL = "paypal"
    MERCADO_PAGO = "mercado_pago"
    SUMUP = "sumup"
    PAGSEGURO = "pagseguro"
    MANUAL = "manual"  # For manual payment processing (e.g., bank transfers)
    OTHER = "other"


class PaymentProcessor(Base):
    """
    Model for payment processors available to tenants.

    This represents a payment processor that can be used to process payments.
    """

    __tablename__ = "payment_processors"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)

    name = Column(String, nullable=False)
    processor_type = Column(Enum(PaymentProcessorType), nullable=False)
    is_active = Column(Boolean, default=True)
    is_default = Column(Boolean, default=False)

    # Configuration for the payment processor
    api_key = Column(String, nullable=True)  # Encrypted API key
    api_secret = Column(String, nullable=True)  # Encrypted API secret
    sandbox_mode = Column(Boolean, default=True)  # Whether to use sandbox/test mode
    webhook_url = Column(String, nullable=True)  # Webhook URL for the processor
    webhook_secret = Column(String, nullable=True)  # Webhook secret for the processor
    additional_config = Column(JSON, nullable=True)  # Additional configuration options

    # Relationships
    tenant = relationship("Tenant")
    payment_methods = relationship(
        "PaymentMethod", back_populates="processor", cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<PaymentProcessor(id={self.id}, name='{self.name}', type='{self.processor_type}')>"
