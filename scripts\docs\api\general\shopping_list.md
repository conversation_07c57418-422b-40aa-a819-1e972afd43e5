# General - Shopping List

**Categoria:** General
**Mó<PERSON><PERSON>:** Shopping List
**Total de Endpoints:** 16
**Gerado em:** 27/06/2025, 20:20:27

## 📋 Endpoints

- [GET /api/modules/shopping-list/shopping-lists/](#get-apimodulesshopping-listshopping-lists) - List Shopping Lists
- [POST /api/modules/shopping-list/shopping-lists/auto-generate](#post-apimodulesshopping-listshopping-listsauto-generate) - Auto-Generate Shopping List
- [GET /api/modules/shopping-list/shopping-lists/categories](#get-apimodulesshopping-listshopping-listscategories) - List Shopping List Categories
- [POST /api/modules/shopping-list/shopping-lists/categories](#post-apimodulesshopping-listshopping-listscategories) - Create Shopping List Category
- [DELETE /api/modules/shopping-list/shopping-lists/categories/{category_id}](#delete-apimodulesshopping-listshopping-listscategoriescategory-id) - Delete Shopping List Category
- [GET /api/modules/shopping-list/shopping-lists/categories/{category_id}](#get-apimodulesshopping-listshopping-listscategoriescategory-id) - Get Shopping List Category
- [PUT /api/modules/shopping-list/shopping-lists/categories/{category_id}](#put-apimodulesshopping-listshopping-listscategoriescategory-id) - Update Shopping List Category
- [DELETE /api/modules/shopping-list/shopping-lists/items/{item_id}](#delete-apimodulesshopping-listshopping-listsitemsitem-id) - Delete Shopping List Item
- [GET /api/modules/shopping-list/shopping-lists/items/{item_id}](#get-apimodulesshopping-listshopping-listsitemsitem-id) - Get Shopping List Item
- [PUT /api/modules/shopping-list/shopping-lists/items/{item_id}](#put-apimodulesshopping-listshopping-listsitemsitem-id) - Update Shopping List Item
- [GET /api/modules/shopping-list/shopping-lists/low-stock-items](#get-apimodulesshopping-listshopping-listslow-stock-items) - Get Low Stock Items
- [POST /api/modules/shopping-list/shopping-lists/test-auto-add-low-stock](#post-apimodulesshopping-listshopping-liststest-auto-add-low-stock) - Test Auto Add Low Stock Items
- [POST /api/modules/shopping-list/shopping-lists/test-integration](#post-apimodulesshopping-listshopping-liststest-integration) - Test Integration
- [GET /api/modules/shopping-list/shopping-lists/{list_id}](#get-apimodulesshopping-listshopping-listslist-id) - Get Shopping List
- [GET /api/modules/shopping-list/shopping-lists/{list_id}/items](#get-apimodulesshopping-listshopping-listslist-iditems) - Get Shopping List Items
- [POST /api/modules/shopping-list/shopping-lists/{list_id}/items](#post-apimodulesshopping-listshopping-listslist-iditems) - Create Shopping List Item

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### AutoGenerateShoppingListRequest

**Descrição:** Schema for auto-generating shopping list from low stock items.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Shopping list name |
| `description` | unknown | ❌ | Shopping list description |
| `target_date` | unknown | ❌ | Target completion date |
| `include_categories` | unknown | ❌ | Categories to include |
| `exclude_categories` | unknown | ❌ | Categories to exclude |
| `minimum_stock_threshold` | unknown | ❌ | Minimum stock threshold |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### ShoppingListCategoryCreate

**Descrição:** Schema para criação de categoria de lista de compras.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `description` | unknown | ❌ | - |
| `display_order` | integer | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `color` | unknown | ❌ | Hex color code for the category |

### ShoppingListCategoryRead

**Descrição:** Schema para leitura de categoria de lista de compras.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `description` | unknown | ❌ | - |
| `display_order` | integer | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `color` | unknown | ❌ | Hex color code for the category |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### ShoppingListCategoryUpdate

**Descrição:** Schema para atualização de categoria de lista de compras.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `display_order` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `color` | unknown | ❌ | Hex color code for the category |

### ShoppingListItemCreate

**Descrição:** Schema para criar um novo item de lista de compras.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Nome do item. |
| `quantity` | integer | ❌ | Quantidade do item. |
| `unit` | unknown | ❌ | Unidade de medida (kg, units, etc). |
| `priority` | Priority | ❌ | Prioridade do item. |
| `estimated_price` | unknown | ❌ | Preço estimado do item. |
| `purchased` | boolean | ❌ | Se o item foi comprado. |
| `notes` | unknown | ❌ | Observações sobre o item. |
| `inventory_item_id` | unknown | ❌ | ID do item de inventário relacionado. |
| `category_id` | unknown | ❌ | ID da categoria do item. |

### ShoppingListItemRead

**Descrição:** Schema para retornar dados de um item de lista ao cliente.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Nome do item. |
| `quantity` | integer | ❌ | Quantidade do item. |
| `unit` | unknown | ❌ | Unidade de medida (kg, units, etc). |
| `priority` | Priority | ❌ | Prioridade do item. |
| `estimated_price` | unknown | ❌ | Preço estimado do item. |
| `purchased` | boolean | ❌ | Se o item foi comprado. |
| `notes` | unknown | ❌ | Observações sobre o item. |
| `inventory_item_id` | unknown | ❌ | ID do item de inventário relacionado. |
| `category_id` | unknown | ❌ | ID da categoria do item. |
| `id` | string | ✅ | - |
| `shopping_list_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `category` | unknown | ❌ | - |

### ShoppingListItemUpdate

**Descrição:** Schema para atualizar um item de lista de compras.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | Novo nome do item. |
| `quantity` | unknown | ❌ | Nova quantidade. |
| `unit` | unknown | ❌ | Nova unidade de medida. |
| `priority` | unknown | ❌ | Nova prioridade. |
| `estimated_price` | unknown | ❌ | Novo preço estimado. |
| `purchased` | unknown | ❌ | Novo status de compra. |
| `notes` | unknown | ❌ | Novas observações. |
| `inventory_item_id` | unknown | ❌ | Novo ID do item de inventário. |
| `category_id` | unknown | ❌ | Novo ID da categoria do item. |

### ShoppingListRead

**Descrição:** Schema para retornar dados de uma lista de compras ao cliente.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Nome da lista de compras. |
| `description` | unknown | ❌ | Descrição da lista. |
| `is_active` | boolean | ❌ | Se a lista está ativa. |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### ShoppingListWithItems

**Descrição:** Schema para retornar lista com seus itens.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Nome da lista de compras. |
| `description` | unknown | ❌ | Descrição da lista. |
| `is_active` | boolean | ❌ | Se a lista está ativa. |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `items` | Array[ShoppingListItemRead] | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/modules/shopping-list/shopping-lists/ {#get-apimodulesshopping-listshopping-lists}

**Resumo:** List Shopping Lists
**Descrição:** Get all shopping lists for the current tenant with summary information.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `status` | string | query | ❌ | Filter by status |
| `auto_generated` | string | query | ❌ | Filter by auto-generated |
| `skip` | integer | query | ❌ | Number of records to skip |
| `limit` | integer | query | ❌ | Number of records to return |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/shopping-list/shopping-lists/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/shopping-list/shopping-lists/auto-generate {#post-apimodulesshopping-listshopping-listsauto-generate}

**Resumo:** Auto-Generate Shopping List
**Descrição:** Auto-generate shopping list from low stock inventory items.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [AutoGenerateShoppingListRequest](#autogenerateshoppinglistrequest)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ShoppingListRead](#shoppinglistread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/shopping-list/shopping-lists/auto-generate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/shopping-list/shopping-lists/categories {#get-apimodulesshopping-listshopping-listscategories}

**Resumo:** List Shopping List Categories
**Descrição:** Get all shopping list categories for the current tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `active_only` | boolean | query | ❌ | Only active categories |
| `skip` | integer | query | ❌ | Number of records to skip |
| `limit` | integer | query | ❌ | Number of records to return |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/shopping-list/shopping-lists/categories" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/shopping-list/shopping-lists/categories {#post-apimodulesshopping-listshopping-listscategories}

**Resumo:** Create Shopping List Category
**Descrição:** Create a new shopping list category.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ShoppingListCategoryCreate](#shoppinglistcategorycreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ShoppingListCategoryRead](#shoppinglistcategoryread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/shopping-list/shopping-lists/categories" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/shopping-list/shopping-lists/categories/{category_id} {#delete-apimodulesshopping-listshopping-listscategoriescategory-id}

**Resumo:** Delete Shopping List Category
**Descrição:** Delete a shopping list category (soft delete).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ShoppingListCategoryRead](#shoppinglistcategoryread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/shopping-list/shopping-lists/categories/{category_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/shopping-list/shopping-lists/categories/{category_id} {#get-apimodulesshopping-listshopping-listscategoriescategory-id}

**Resumo:** Get Shopping List Category
**Descrição:** Get details of a specific shopping list category.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ShoppingListCategoryRead](#shoppinglistcategoryread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/shopping-list/shopping-lists/categories/{category_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/shopping-list/shopping-lists/categories/{category_id} {#put-apimodulesshopping-listshopping-listscategoriescategory-id}

**Resumo:** Update Shopping List Category
**Descrição:** Update a shopping list category.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ShoppingListCategoryUpdate](#shoppinglistcategoryupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ShoppingListCategoryRead](#shoppinglistcategoryread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/shopping-list/shopping-lists/categories/{category_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/shopping-list/shopping-lists/items/{item_id} {#delete-apimodulesshopping-listshopping-listsitemsitem-id}

**Resumo:** Delete Shopping List Item
**Descrição:** Delete a shopping list item.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `item_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/shopping-list/shopping-lists/items/{item_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/shopping-list/shopping-lists/items/{item_id} {#get-apimodulesshopping-listshopping-listsitemsitem-id}

**Resumo:** Get Shopping List Item
**Descrição:** Get a specific shopping list item by ID.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `item_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ShoppingListItemRead](#shoppinglistitemread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/shopping-list/shopping-lists/items/{item_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/shopping-list/shopping-lists/items/{item_id} {#put-apimodulesshopping-listshopping-listsitemsitem-id}

**Resumo:** Update Shopping List Item
**Descrição:** Update a shopping list item.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `item_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ShoppingListItemUpdate](#shoppinglistitemupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ShoppingListItemRead](#shoppinglistitemread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/shopping-list/shopping-lists/items/{item_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/shopping-list/shopping-lists/low-stock-items {#get-apimodulesshopping-listshopping-listslow-stock-items}

**Resumo:** Get Low Stock Items
**Descrição:** Get inventory items that are below their minimum stock threshold.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `include_categories` | string | query | ❌ | Categories to include |
| `exclude_categories` | string | query | ❌ | Categories to exclude |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/shopping-list/shopping-lists/low-stock-items" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/shopping-list/shopping-lists/test-auto-add-low-stock {#post-apimodulesshopping-listshopping-liststest-auto-add-low-stock}

**Resumo:** Test Auto Add Low Stock Items
**Descrição:** Test automatic addition of low stock items to shopping list.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `threshold` | integer | query | ❌ | Stock threshold |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/shopping-list/shopping-lists/test-auto-add-low-stock" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/shopping-list/shopping-lists/test-integration {#post-apimodulesshopping-listshopping-liststest-integration}

**Resumo:** Test Integration
**Descrição:** Test inventory-shopping list integration status.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/shopping-list/shopping-lists/test-integration" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/shopping-list/shopping-lists/{list_id} {#get-apimodulesshopping-listshopping-listslist-id}

**Resumo:** Get Shopping List
**Descrição:** Get a specific shopping list with all its items.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `list_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ShoppingListWithItems](#shoppinglistwithitems)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/shopping-list/shopping-lists/{list_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/shopping-list/shopping-lists/{list_id}/items {#get-apimodulesshopping-listshopping-listslist-iditems}

**Resumo:** Get Shopping List Items
**Descrição:** Get all items for a specific shopping list.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `list_id` | string | path | ✅ | - |
| `status` | string | query | ❌ | Filter by status |
| `priority` | string | query | ❌ | Filter by priority |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/shopping-list/shopping-lists/{list_id}/items" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/shopping-list/shopping-lists/{list_id}/items {#post-apimodulesshopping-listshopping-listslist-iditems}

**Resumo:** Create Shopping List Item
**Descrição:** Create a new item in a shopping list.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `list_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ShoppingListItemCreate](#shoppinglistitemcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ShoppingListItemRead](#shoppinglistitemread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/shopping-list/shopping-lists/{list_id}/items" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
