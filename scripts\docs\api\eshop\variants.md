# Eshop - Variants

**Categoria:** Eshop
**Módulo:** Variants
**Total de Endpoints:** 6
**Gerado em:** 27/06/2025, 20:20:26

## 📋 Endpoints

- [GET /api/eshop/variants/groups/](#get-apieshopvariantsgroups) - Read Variant Groups
- [POST /api/eshop/variants/groups/](#post-apieshopvariantsgroups) - Create Variant Group
- [GET /api/eshop/variants/groups/{group_id}](#get-apieshopvariantsgroupsgroup-id) - Read Variant Group
- [GET /api/eshop/variants/options/](#get-apieshopvariantsoptions) - Read Variant Options
- [POST /api/eshop/variants/options/](#post-apieshopvariantsoptions) - Create Variant Option
- [GET /api/eshop/variants/options/{option_id}](#get-apieshopvariantsoptionsoption-id) - Read Variant Option

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### ProductVariantGroupCreate

**Descrição:** Schema for creating a new product variant group.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Variant group name |
| `description` | unknown | ❌ | Variant group description |
| `min_selection` | integer | ❌ | Minimum number of selections required |
| `max_selection` | integer | ❌ | Maximum number of selections allowed |
| `display_order` | integer | ❌ | Display order within the product |
| `is_required` | boolean | ❌ | Whether this variant group is required |
| `is_active` | boolean | ❌ | Whether this variant group is active |
| `requires_default_selection` | boolean | ❌ | Whether a default selection is required |
| `is_template` | boolean | ❌ | Whether this is a template group |
| `template_id` | unknown | ❌ | Reference to template group |
| `tenant_id` | unknown | ❌ | Tenant ID (null for global groups) |

### ProductVariantGroupResponse

**Descrição:** Schema for product variant group responses.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Variant group name |
| `description` | unknown | ❌ | Variant group description |
| `min_selection` | integer | ❌ | Minimum number of selections required |
| `max_selection` | integer | ❌ | Maximum number of selections allowed |
| `display_order` | integer | ❌ | Display order within the product |
| `is_required` | boolean | ❌ | Whether this variant group is required |
| `is_active` | boolean | ❌ | Whether this variant group is active |
| `requires_default_selection` | boolean | ❌ | Whether a default selection is required |
| `is_template` | boolean | ❌ | Whether this is a template group |
| `template_id` | unknown | ❌ | Reference to template group |
| `id` | string | ✅ | - |
| `tenant_id` | unknown | ✅ | - |
| `options` | Array[ProductVariantOptionResponse] | ❌ | Variant options |

### ProductVariantOptionCreate

**Descrição:** Schema for creating a new product variant option.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Variant option name |
| `description` | unknown | ❌ | Variant option description |
| `value` | unknown | ❌ | Machine-readable value |
| `price_adjustment` | unknown | ❌ | Price adjustment from base price |
| `price_adjustment_type` | string | ❌ | Price adjustment type |
| `stock_quantity` | unknown | ❌ | Stock quantity for this variant |
| `sku_suffix` | unknown | ❌ | SKU suffix for this variant |
| `display_order` | integer | ❌ | Display order within the group |
| `is_default` | boolean | ❌ | Whether this is the default selection |
| `is_active` | boolean | ❌ | Whether this variant option is active |
| `color_code` | unknown | ❌ | Hex color code |
| `image_url` | unknown | ❌ | Image URL for this variant |
| `tenant_id` | unknown | ❌ | Tenant ID (null for global options) |
| `variant_group_id` | string | ✅ | Parent variant group ID |

### ProductVariantOptionResponse

**Descrição:** Schema for product variant option responses.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Variant option name |
| `description` | unknown | ❌ | Variant option description |
| `value` | unknown | ❌ | Machine-readable value |
| `price_adjustment` | string | ❌ | Price adjustment from base price |
| `price_adjustment_type` | string | ❌ | Price adjustment type |
| `stock_quantity` | unknown | ❌ | Stock quantity for this variant |
| `sku_suffix` | unknown | ❌ | SKU suffix for this variant |
| `display_order` | integer | ❌ | Display order within the group |
| `is_default` | boolean | ❌ | Whether this is the default selection |
| `is_active` | boolean | ❌ | Whether this variant option is active |
| `color_code` | unknown | ❌ | Hex color code |
| `image_url` | unknown | ❌ | Image URL for this variant |
| `id` | string | ✅ | - |
| `tenant_id` | unknown | ✅ | - |
| `variant_group_id` | string | ✅ | - |

## 🔗 Endpoints Detalhados

### GET /api/eshop/variants/groups/ {#get-apieshopvariantsgroups}

**Resumo:** Read Variant Groups
**Descrição:** Retrieve variant groups.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `is_active` | string | query | ❌ | - |
| `is_template` | string | query | ❌ | - |
| `include_options` | boolean | query | ❌ | - |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/variants/groups/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/eshop/variants/groups/ {#post-apieshopvariantsgroups}

**Resumo:** Create Variant Group
**Descrição:** Create a new variant group.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ProductVariantGroupCreate](#productvariantgroupcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductVariantGroupResponse](#productvariantgroupresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/eshop/variants/groups/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/eshop/variants/groups/{group_id} {#get-apieshopvariantsgroupsgroup-id}

**Resumo:** Read Variant Group
**Descrição:** Retrieve a specific variant group.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `group_id` | string | path | ✅ | - |
| `include_options` | boolean | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductVariantGroupResponse](#productvariantgroupresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/variants/groups/{group_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/eshop/variants/options/ {#get-apieshopvariantsoptions}

**Resumo:** Read Variant Options
**Descrição:** Retrieve variant options.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `variant_group_id` | string | query | ❌ | - |
| `is_active` | string | query | ❌ | - |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/variants/options/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/eshop/variants/options/ {#post-apieshopvariantsoptions}

**Resumo:** Create Variant Option
**Descrição:** Create a new variant option.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ProductVariantOptionCreate](#productvariantoptioncreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductVariantOptionResponse](#productvariantoptionresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/eshop/variants/options/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/eshop/variants/options/{option_id} {#get-apieshopvariantsoptionsoption-id}

**Resumo:** Read Variant Option
**Descrição:** Retrieve a specific variant option.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `option_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductVariantOptionResponse](#productvariantoptionresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/variants/options/{option_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
