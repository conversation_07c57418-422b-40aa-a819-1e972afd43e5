import 'package:uuid/uuid.dart';

class UserModel {
  final String id;
  final String name;
  final String email;
  final String role; // 'owner' or 'staff'
  final String? phone;
  final String? avatar;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? lastLoginAt;
  final Map<String, dynamic>? permissions;

  UserModel({
    required this.id,
    required this.name,
    required this.email,
    required this.role,
    this.phone,
    this.avatar,
    this.isActive = true,
    required this.createdAt,
    this.lastLoginAt,
    this.permissions,
  });

  factory UserModel.create({
    required String name,
    required String email,
    required String role,
    String? phone,
    String? avatar,
    Map<String, dynamic>? permissions,
  }) {
    return UserModel(
      id: const Uuid().v4(),
      name: name,
      email: email,
      role: role,
      phone: phone,
      avatar: avatar,
      createdAt: DateTime.now(),
      permissions: permissions,
    );
  }

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      role: json['role'] ?? 'staff',
      phone: json['phone'],
      avatar: json['avatar'],
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      lastLoginAt: json['lastLoginAt'] != null 
          ? DateTime.parse(json['lastLoginAt']) 
          : null,
      permissions: json['permissions'] != null 
          ? Map<String, dynamic>.from(json['permissions']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'role': role,
      'phone': phone,
      'avatar': avatar,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'lastLoginAt': lastLoginAt?.toIso8601String(),
      'permissions': permissions,
    };
  }

  UserModel copyWith({
    String? id,
    String? name,
    String? email,
    String? role,
    String? phone,
    String? avatar,
    bool? isActive,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    Map<String, dynamic>? permissions,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      role: role ?? this.role,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      permissions: permissions ?? this.permissions,
    );
  }

  bool get isOwner => role == 'owner';
  bool get isStaff => role == 'staff';

  bool hasPermission(String permission) {
    if (isOwner) return true; // Owners have all permissions
    return permissions?[permission] == true;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'UserModel(id: $id, name: $name, email: $email, role: $role)';
  }
}

// Default permissions for staff
class UserPermissions {
  static const String viewOrders = 'view_orders';
  static const String createOrders = 'create_orders';
  static const String editOrders = 'edit_orders';
  static const String deleteOrders = 'delete_orders';
  static const String viewTables = 'view_tables';
  static const String manageTables = 'manage_tables';
  static const String viewMenu = 'view_menu';
  static const String manageMenu = 'manage_menu';
  static const String viewReports = 'view_reports';
  static const String manageStaff = 'manage_staff';
  static const String manageSettings = 'manage_settings';
  static const String processPayments = 'process_payments';
  static const String printReceipts = 'print_receipts';
  static const String manageDelivery = 'manage_delivery';

  static Map<String, dynamic> get defaultStaffPermissions => {
    viewOrders: true,
    createOrders: true,
    editOrders: true,
    deleteOrders: false,
    viewTables: true,
    manageTables: true,
    viewMenu: true,
    manageMenu: false,
    viewReports: false,
    manageStaff: false,
    manageSettings: false,
    processPayments: true,
    printReceipts: true,
    manageDelivery: true,
  };

  static Map<String, dynamic> get allPermissions => {
    viewOrders: true,
    createOrders: true,
    editOrders: true,
    deleteOrders: true,
    viewTables: true,
    manageTables: true,
    viewMenu: true,
    manageMenu: true,
    viewReports: true,
    manageStaff: true,
    manageSettings: true,
    processPayments: true,
    printReceipts: true,
    manageDelivery: true,
  };
}