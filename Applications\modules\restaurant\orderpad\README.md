# OrderPad - Sistema de Pedidos para Restaurantes

## 📱 Visão Geral

OrderPad é um aplicativo Flutter completo para gerenciamento de pedidos em restaurantes, desenvolvido para garçons e proprietários. O sistema oferece duas interfaces distintas baseadas no papel do usuário:

- **Staff (Garçons)**: Interface otimizada para tomada de pedidos, gestão de mesas e delivery
- **Owner (Proprietários)**: Dashboard administrativo completo para gestão do restaurante

## 🎯 Objetivos do Projeto

### Funcionalidades Principais

#### Para Garçons (Staff)
- ✅ Gestão de mesas com layout visual
- ✅ Tomada de pedidos offline/online
- ✅ Cardápio digital com imagens
- ✅ Modificadores e pedidos especiais
- ✅ Impressão de comandas (KOT)
- ✅ Status de pedidos em tempo real
- ✅ Gestão de delivery e takeaway
- ✅ Divisão e junção de contas
- ✅ Aplicação de descontos
- ✅ Histórico de pedidos

#### Para Proprietários (Owner)
- ✅ Dashboard com métricas e KPIs
- ✅ Gestão de funcionários
- ✅ Relatórios de vendas
- ✅ Gestão de cardápio
- ✅ Configurações do sistema
- ✅ Análise de performance
- ✅ Gestão de estoque básica
- ✅ Configuração de mesas

## 🏗️ Arquitetura Técnica

### Stack Tecnológica
- **Framework**: Flutter 3.16+
- **Linguagem**: Dart 3.0+
- **Gerenciamento de Estado**: Riverpod 2.4+
- **Banco Local**: Hive/SQLite
- **Navegação**: GoRouter
- **UI**: Material Design 3
- **Autenticação**: Local (mock data)

### Padrão de Arquitetura
```
lib/
├── core/
│   ├── constants/
│   ├── theme/
│   ├── utils/
│   └── services/
├── features/
│   ├── auth/
│   ├── staff/
│   │   ├── orders/
│   │   ├── tables/
│   │   └── delivery/
│   └── admin/
│       ├── dashboard/
│       ├── staff_management/
│       └── reports/
├── shared/
│   ├── models/
│   ├── widgets/
│   └── providers/
└── main.dart
```

## 🎨 Design System

### Paleta de Cores
- **Primária**: #2E7D32 (Verde escuro)
- **Secundária**: #4CAF50 (Verde claro)
- **Accent**: #FF9800 (Laranja)
- **Background**: #FAFAFA (Cinza muito claro)
- **Surface**: #FFFFFF (Branco)
- **Error**: #F44336 (Vermelho)

### Tipografia
- **Fonte Principal**: Roboto
- **Títulos**: Roboto Medium
- **Corpo**: Roboto Regular
- **Botões**: Roboto Medium

### Princípios de Design
- Interface limpa e minimalista
- Navegação intuitiva
- Feedback visual claro
- Acessibilidade
- Responsividade

## 📋 Roadmap de Desenvolvimento

Ver arquivo `ROADMAP.md` para detalhes completos do cronograma de desenvolvimento.

## 🚀 Como Executar

### Pré-requisitos
- Flutter SDK 3.16+
- Dart SDK 3.0+
- Android Studio / VS Code
- Emulador Android/iOS ou dispositivo físico

### Instalação
```bash
# Clone o repositório
git clone <repository-url>
cd orderpad

# Instale as dependências
flutter pub get

# Execute o app
flutter run
```

### Credenciais de Teste
```
# Proprietário
Email: <EMAIL>
Senha: owner123

# Garçom
Email: <EMAIL>
Senha: waiter123
```

## 📱 Screenshots

*Screenshots serão adicionados conforme o desenvolvimento*

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.

## 📞 Suporte

Para suporte, entre em contato através do email: <EMAIL>

---

**Desenvolvido com ❤️ usando Flutter**