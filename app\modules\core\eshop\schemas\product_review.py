from typing import Optional
from pydantic import BaseModel, Field, validator
from uuid import UUID
from datetime import datetime


class ProductReviewBase(BaseModel):
    """Base schema for product reviews."""
    rating: int = Field(..., ge=1, le=5, description="Rating from 1 to 5 stars")
    title: Optional[str] = Field(None, max_length=200, description="Review title")
    comment: Optional[str] = Field(None, description="Review comment")


class ProductReviewCreate(ProductReviewBase):
    """Schema for creating a new product review."""
    product_id: UUID = Field(..., description="Product ID being reviewed")
    tenant_id: Optional[UUID] = Field(None, description="Tenant ID (null for global reviews)")


class ProductReviewUpdate(BaseModel):
    """Schema for updating a product review."""
    rating: Optional[int] = Field(None, ge=1, le=5, description="Rating from 1 to 5 stars")
    title: Optional[str] = Field(None, max_length=200, description="Review title")
    comment: Optional[str] = Field(None, description="Review comment")


class ProductReviewResponse(ProductReviewBase):
    """Schema for product review responses."""
    id: UUID
    product_id: UUID
    user_id: UUID
    tenant_id: Optional[UUID]
    is_verified_purchase: bool
    is_approved: bool
    is_featured: bool
    helpful_count: int
    total_votes: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class ProductReviewAdminUpdate(BaseModel):
    """Schema for admin updates to product reviews."""
    is_approved: Optional[bool] = Field(None, description="Whether review is approved")
    is_featured: Optional[bool] = Field(None, description="Whether review is featured")


class ProductReviewVote(BaseModel):
    """Schema for voting on review helpfulness."""
    is_helpful: bool = Field(..., description="Whether the vote is helpful or not")
