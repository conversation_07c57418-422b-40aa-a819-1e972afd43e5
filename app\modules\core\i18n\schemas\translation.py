"""
Translation schemas for the i18n module.
"""

import uuid
from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, List


class TranslationBase(BaseModel):
    """Base schema for Translation."""

    key: str = Field(..., min_length=1, max_length=255, description="Translation key")
    text: str = Field(..., description="Translated text")
    language_id: uuid.UUID = Field(..., description="ID of the language")
    sector: str = Field(
        "common",
        min_length=1,
        max_length=50,
        description="Sector/namespace for the translation (e.g., 'menu', 'auth', 'common')",
    )


class TranslationCreate(TranslationBase):
    """Schema for creating a new Translation."""

    pass


class TranslationUpdate(BaseModel):
    """Schema for updating an existing Translation."""

    key: Optional[str] = Field(None, min_length=1, max_length=255)
    text: Optional[str] = None
    language_id: Optional[uuid.UUID] = None
    sector: Optional[str] = Field(None, min_length=1, max_length=50)


class TranslationRead(TranslationBase):
    """Schema for reading a Translation."""

    id: uuid.UUID
    last_updated_by_id: Optional[uuid.UUID] = None

    model_config = ConfigDict(from_attributes=True)


class TranslationBySectorRead(BaseModel):
    """Schema for reading translations grouped by sector."""

    sector: str
    translations: List[TranslationRead]

    model_config = ConfigDict(from_attributes=True)


class TranslationChangeRead(BaseModel):
    """Schema for reading a translation change."""

    key: str
    sector: str
    change_type: str  # "added", "updated", "deleted"
    new_text: Optional[str] = None
    previous_text: Optional[str] = None
    changed_at: str

    model_config = ConfigDict(from_attributes=True)


class SectorChangesRead(BaseModel):
    """Schema for reading changes in a specific sector."""

    sector: str
    changes: List[TranslationChangeRead]

    model_config = ConfigDict(from_attributes=True)


class LanguageChangesRead(BaseModel):
    """Schema for reading all changes for a language since a specific version."""

    language_code: str
    current_version_code: str
    sectors: List[SectorChangesRead]

    model_config = ConfigDict(from_attributes=True)
