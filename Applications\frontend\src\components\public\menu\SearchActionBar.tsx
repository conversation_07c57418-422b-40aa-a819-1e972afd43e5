'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { createPortal } from 'react-dom';
import {
  MagnifyingGlassIcon,
  PhoneIcon,
  CalendarIcon,
  MapPinIcon,
  XMarkIcon,
  WifiIcon
} from '@heroicons/react/24/outline';
import { ReservationModal } from '../reservation/ReservationModal';

interface SearchActionBarProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
  restaurantInfo?: {
    phone?: string;
    address?: string;
    wifi?: {
      name: string;
      password: string;
    };
  };
}

export function SearchActionBar({
  searchTerm,
  onSearchChange,
  restaurantInfo
}: SearchActionBarProps) {
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);
  const [isWifiModalOpen, setIsWifiModalOpen] = useState(false);
  const [isReservationModalOpen, setIsReservationModalOpen] = useState(false);
  const searchRef = useRef<HTMLDivElement>(null);
  const wifiModalRef = useRef<HTMLDivElement>(null);

  const handleSearchClick = () => {
    setIsSearchExpanded(true);
  };

  const handleCloseSearch = useCallback(() => {
    setIsSearchExpanded(false);
    onSearchChange('');
  }, [onSearchChange]);

  // Close search when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        handleCloseSearch();
      }
      if (wifiModalRef.current && !wifiModalRef.current.contains(event.target as Node)) {
        handleCloseWifiModal();
      }
    };

    if (isSearchExpanded || isWifiModalOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isSearchExpanded, isWifiModalOpen, handleCloseSearch]);

  // Cleanup scroll when component unmounts or modal closes
  useEffect(() => {
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, []);

  // Handle scroll disable/enable for WiFi modal
  useEffect(() => {
    if (isWifiModalOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
  }, [isWifiModalOpen]);

  const handleCall = () => {
    if (restaurantInfo?.phone) {
      window.open(`tel:${restaurantInfo.phone}`, '_self');
    }
  };

  const handleReservation = () => {
    setIsReservationModalOpen(true);
  };

  const handleMap = () => {
    if (restaurantInfo?.address) {
      const encodedAddress = encodeURIComponent(restaurantInfo.address);
      window.open(`https://maps.google.com?q=${encodedAddress}`, '_blank');
    }
  };

  const handleWifi = () => {
    setIsWifiModalOpen(true);
    // Disable scroll when modal opens
    document.body.style.overflow = 'hidden';
  };

  const handleCloseWifiModal = () => {
    setIsWifiModalOpen(false);
    // Re-enable scroll when modal closes
    document.body.style.overflow = 'unset';
  };

  const handleConnectWifi = () => {
    if (restaurantInfo?.wifi) {
      // Create WiFi connection URL (works on some devices)
      const wifiUrl = `wifi:T:WPA;S:${restaurantInfo.wifi.name};P:${restaurantInfo.wifi.password};;`;

      // Try to open WiFi connection
      try {
        window.location.href = wifiUrl;
      } catch (error) {
        // Fallback: copy password to clipboard
        if (restaurantInfo.wifi?.password) {
          navigator.clipboard.writeText(restaurantInfo.wifi.password).then(() => {
            alert('Senha copiada para a área de transferência!');
          }).catch(() => {
            alert(`Senha do WiFi: ${restaurantInfo.wifi?.password || 'Não disponível'}`);
          });
        }
      }
    }
  };

  if (isSearchExpanded) {
    return (
      <div ref={searchRef} className="relative z-20">
        <div className="max-w-6xl mx-auto px-6 py-5">
          {/* Logo spacer to avoid overlap */}
          <div className="flex items-center gap-4">
            <div className="w-32 sm:w-40 flex-shrink-0"></div>

            {/* Full width search when expanded */}
            <div className="relative flex-1">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-orange-400" />
              </div>
              <input
                type="text"
                placeholder="Buscar pratos, bebidas, ingredientes..."
                value={searchTerm}
                onChange={(e) => onSearchChange(e.target.value)}
                className="w-full pl-12 pr-12 py-3 bg-gradient-to-r from-orange-50/50 to-amber-50/50
                         border border-orange-200/50 rounded-full
                         focus:ring-2 focus:ring-orange-300 focus:border-orange-300 focus:bg-white
                         transition-all duration-300 text-gray-700 placeholder-gray-500
                         shadow-sm hover:shadow-md h-12"
                autoFocus
              />
              <button
                onClick={handleCloseSearch}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 p-2 rounded-full
                         hover:bg-orange-100 transition-colors text-orange-500"
              >
                <XMarkIcon className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative z-10">
      <div className="max-w-6xl mx-auto px-6 py-5">
        {/* Spacer for logo - push content to the right */}
        <div className="flex items-center gap-4">
          {/* Logo spacer - invisible div to reserve space */}
          <div className="w-32 sm:w-40 flex-shrink-0"></div>

          {/* Content area */}
          <div className="flex items-center justify-end gap-4 flex-1">
            {/* Action Buttons - Left side */}
            <div className="flex items-center gap-3">
              {/* Call Button - Always show for testing */}
              <button
                onClick={handleCall}
                className="flex items-center gap-2 px-4 py-3 bg-green-500 hover:bg-green-600
                         text-white rounded-full transition-colors duration-300 border border-transparent
                         shadow-sm hover:shadow-md"
                title={`Ligar${restaurantInfo?.phone ? ` para ${restaurantInfo.phone}` : ''}`}
              >
                <PhoneIcon className="h-5 w-5" />
                <span className="hidden sm:inline text-sm font-medium">Ligar</span>
              </button>

              {/* Reservation Button */}
              <button
                onClick={handleReservation}
                className="flex items-center gap-2 px-4 py-3 bg-blue-500 hover:bg-blue-600
                         text-white rounded-full transition-colors duration-300 border border-transparent
                         shadow-sm hover:shadow-md"
                title="Fazer reserva"
              >
                <CalendarIcon className="h-5 w-5" />
                <span className="hidden sm:inline text-sm font-medium">Reservar</span>
              </button>

              {/* Map Button - Always show for testing */}
              <button
                onClick={handleMap}
                className="flex items-center gap-2 px-4 py-3 bg-orange-500 hover:bg-orange-600
                         text-white rounded-full transition-colors duration-300 border border-transparent
                         shadow-sm hover:shadow-md"
                title={`Ver no mapa${restaurantInfo?.address ? `: ${restaurantInfo.address}` : ''}`}
              >
                <MapPinIcon className="h-5 w-5" />
                <span className="hidden sm:inline text-sm font-medium">Mapa</span>
              </button>

              {/* WiFi Button */}
              <button
                onClick={handleWifi}
                className="flex items-center gap-2 px-4 py-3 bg-purple-500 hover:bg-purple-600
                         text-white rounded-full transition-colors duration-300 border border-transparent
                         shadow-sm hover:shadow-md"
                title="Informações do WiFi"
              >
                <WifiIcon className="h-5 w-5" />
                <span className="hidden sm:inline text-sm font-medium">WiFi</span>
              </button>

              {/* Search Icon - Right side */}
              <button
                onClick={handleSearchClick}
                className="flex items-center justify-center w-12 h-12 bg-orange-50 hover:bg-orange-100
                         border border-orange-200 rounded-full transition-colors duration-300
                         shadow-sm hover:shadow-md ml-2"
                title="Buscar no menu"
              >
                <MagnifyingGlassIcon className="h-6 w-6 text-orange-600" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* WiFi Modal - Rendered via Portal */}
      {isWifiModalOpen && typeof document !== 'undefined' && createPortal(
        <div
          className="fixed inset-0 bg-black/70 flex items-center justify-center p-4 overflow-hidden"
          style={{ zIndex: 2147483647 }}
        >
          <div
            ref={wifiModalRef}
            className="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden relative"
            style={{ zIndex: 2147483647 }}
          >
            {/* Modal Header */}
            <div className="bg-gradient-to-r from-purple-500 to-purple-600 px-6 py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <WifiIcon className="h-6 w-6 text-white" />
                  <h3 className="text-lg font-semibold text-white">WiFi do Restaurante</h3>
                </div>
                <button
                  onClick={handleCloseWifiModal}
                  className="p-1 rounded-full hover:bg-white/20 transition-colors"
                >
                  <XMarkIcon className="h-5 w-5 text-white" />
                </button>
              </div>
            </div>

            {/* Modal Content */}
            <div className="p-6">
              {restaurantInfo?.wifi ? (
                <div className="space-y-4">
                  {/* Network Name */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Nome da Rede
                    </label>
                    <div className="bg-gray-50 rounded-lg p-3 border">
                      <span className="font-mono text-gray-900">{restaurantInfo.wifi.name}</span>
                    </div>
                  </div>

                  {/* Password */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Senha
                    </label>
                    <div className="bg-gray-50 rounded-lg p-3 border">
                      <span className="font-mono text-gray-900">{restaurantInfo.wifi.password}</span>
                    </div>
                  </div>

                  {/* Connect Button */}
                  <button
                    onClick={handleConnectWifi}
                    className="w-full bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700
                             text-white font-medium py-3 px-4 rounded-lg transition-all duration-300
                             shadow-lg hover:shadow-xl transform hover:scale-105"
                  >
                    Conectar Automaticamente
                  </button>

                  <p className="text-xs text-gray-500 text-center">
                    Se a conexão automática não funcionar, use as informações acima para conectar manualmente.
                  </p>
                </div>
              ) : (
                <div className="text-center py-8">
                  <WifiIcon className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">Informações do WiFi não disponíveis</p>
                </div>
              )}
            </div>
          </div>
        </div>,
        document.body
      )}

      {/* Reservation Modal */}
      <ReservationModal
        isOpen={isReservationModalOpen}
        onClose={() => setIsReservationModalOpen(false)}
        restaurantInfo={{
          name: "Test Restaurant",
          phone: restaurantInfo?.phone,
          address: restaurantInfo?.address
        }}
      />
    </div>
  );
}
