import useSWR from 'swr';
import { useMemo } from 'react';
import { api } from '@/lib/apiClient';
import { z } from 'zod';
// Category types for EShop categories

// Schema para os dados que o formulário de criação validará
export const categoryFormSchema = z.object({
    name: z.string().min(3, "O nome deve ter pelo menos 3 caracteres."),
    description: z.string().optional(),
    parent_id: z.string().uuid().nullable().optional().or(z.literal(null)),
});

// Transform schema to handle empty strings and convert to null
export const categoryFormSchemaTransformed = categoryFormSchema.transform((data) => ({
    ...data,
    parent_id: data.parent_id === '' ? null : data.parent_id,
    description: data.description === '' ? undefined : data.description,
}));

// Tipagem para o formulário
export type CategoryFormData = z.infer<typeof categoryFormSchema>;

// Tipagem para os dados enviados para a API (incluindo o slug)
export type CategoryCreatePayload = z.infer<typeof categoryFormSchemaTransformed>;
export type CategoryUpdatePayload = CategoryCreatePayload;

// Schema de validação para uma única categoria, alinhado com o backend
const categorySchema: z.ZodType<EshopCategory> = z.object({
  id: z.string().uuid(),
  name: z.string(),
  description: z.string().nullable().optional(),
  parent_id: z.string().uuid().nullable().optional(),
  image_url: z.string().url().nullable().optional(),
  icon: z.string().nullable().optional(),
  children: z.array(z.lazy(() => categorySchema)).optional(), // Para a estrutura em árvore
});

// Schema para a resposta da API (uma lista de categorias)
const categoriesResponseSchema = z.array(categorySchema);

// Tipagem inferida a partir do schema
export interface EshopCategory {
    id: string;
    name: string;
    description?: string | null;
    parent_id?: string | null;
    image_url?: string | null;
    icon?: string | null;
    children?: EshopCategory[];
}

// O fetcher que o SWR usará - sem tenant_id
const fetcher = (url: string): Promise<EshopCategory[]> => {
  // Cria uma instância do api sem o header X-Tenant-ID
  const apiWithoutTenant = {
    get: (url: string) => {
      return fetch(`${process.env.NEXT_PUBLIC_API_URL}${url}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          // Não inclui X-Tenant-ID para categorias universais
        },
        credentials: 'include',
      }).then(res => {
        if (!res.ok) {
          throw new Error(`HTTP error! status: ${res.status}`);
        }
        return res.json();
      });
    }
  };
  
  return apiWithoutTenant.get(url).then(res => categoriesResponseSchema.parse(res));
};

// Função para construir a árvore de categorias a partir de uma lista plana
const buildCategoryTree = (categories: EshopCategory[]): EshopCategory[] => {
  const topLevelCategories = categories.filter(cat => !cat.parent_id);
  
  const buildTree = (category: EshopCategory): EshopCategory => {
    const children = categories.filter(cat => cat.parent_id === category.id);
    return {
      ...category,
      children: children.length > 0 ? children.map(buildTree) : undefined
    };
  };
  
  return topLevelCategories.map(buildTree);
};

export function useUniversalEshopCategories() {
  // URL do endpoint universal de categorias
  const url = '/eshop/categories/universal';
    
  const { data, error, isLoading, mutate } = useSWR(url, fetcher);
  
  // Constrói a árvore de categorias a partir dos dados planos
  const categoriesTree = useMemo(() => {
    if (!data) return [];
    return buildCategoryTree(data);
  }, [data]);

  const createCategory = async (categoryData: CategoryCreatePayload) => {
    try {
        // Mapeia o market_type para os valores esperados pela API
        const marketTypeMapping = {
            'PUBLIC': 'public',
            'B2B': 'b2b',
            'ALL': 'all'
        };
        
        // Adiciona o market_type ao payload se fornecido
        const payload = {
            ...categoryData,
            market_type: marketTypeMapping[marketType as keyof typeof marketTypeMapping] || 'public'
        };
        
        const newCategory = await api.post('/eshop/categories/universal', payload);
        // Atualiza o cache local do SWR com os novos dados
        if (data) {
            mutate([...data, newCategory], false);
        }
        return newCategory;
    } catch (error) {
        // Lançar o erro para ser tratado no formulário
        console.error("Failed to create category:", error);
        throw error;
    }
  };

  const updateCategory = async (categoryId: string, categoryData: CategoryUpdatePayload) => {
    try {
        // Mapeia o market_type para os valores esperados pela API
        const payload = {
            ...categoryData,
            market_type: categoryData.market_type?.toUpperCase() || 'PUBLIC'
        };
        
        const updatedCategory = await api.put(`/eshop/categories/universal/${categoryId}`, payload);
        // Atualiza o cache local do SWR
        if (data) {
            const updatedData = data.map(cat => 
                cat.id === categoryId ? updatedCategory : cat
            );
            mutate(updatedData, false);
        }
        return updatedCategory;
    } catch (error) {
        console.error("Failed to update category:", error);
        throw error;
    }
  };

  const deleteCategory = async (categoryId: string) => {
    try {
        await api.delete(`/eshop/categories/universal/${categoryId}`);
        // Remove a categoria e seus filhos do cache local (cascade)
        if (data) {
            // Função para encontrar recursivamente todos os filhos de uma categoria
            const findAllChildren = (parentId: string, categories: EshopCategory[]): string[] => {
                const children = categories.filter(cat => cat.parent_id === parentId);
                const allChildrenIds = children.map(child => child.id);
                
                // Recursivamente encontra filhos dos filhos
                children.forEach(child => {
                    allChildrenIds.push(...findAllChildren(child.id, categories));
                });
                
                return allChildrenIds;
            };
            
            const childrenIds = findAllChildren(categoryId, data);
            const idsToRemove = [categoryId, ...childrenIds];
            
            const filteredData = data.filter(cat => !idsToRemove.includes(cat.id));
            mutate(filteredData, false);
        }
    } catch (error) {
        console.error("Failed to delete category:", error);
        throw error;
    }
  };

  return {
    categories: categoriesTree,
    isLoading,
    isError: error,
    mutateCategories: mutate,
    createCategory,
    updateCategory,
    deleteCategory,
  };
}