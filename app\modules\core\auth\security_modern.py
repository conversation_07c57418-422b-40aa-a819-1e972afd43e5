"""
Módulo de segurança moderno para hashing de senhas.
Substitui passlib por implementação direta usando cryptography + argon2-cffi.
Resolve problemas de compatibilidade com bcrypt 4.x.
"""

import os
import base64
import logging
from typing import Union, <PERSON><PERSON>, Optional
from argon2 import PasswordHasher, exceptions as argon2_exceptions
from argon2.low_level import Type
import bcrypt

logger = logging.getLogger(__name__)

# Configurações do Argon2id (algoritmo moderno recomendado)
ARGON2_CONFIG = {
    "time_cost": 3,      # Número de iterações
    "memory_cost": 65536, # Memória em KB (64MB)
    "parallelism": 1,     # Número de threads paralelas
    "hash_len": 32,       # Tamanho do hash em bytes
    "salt_len": 16,       # Tamanho do salt em bytes
}

# Instância global do hasher Argon2
argon2_hasher = PasswordHasher(
    time_cost=ARGON2_CONFIG["time_cost"],
    memory_cost=ARGON2_CONFIG["memory_cost"],
    parallelism=ARGON2_CONFIG["parallelism"],
    hash_len=ARGON2_CONFIG["hash_len"],
    salt_len=ARGON2_CONFIG["salt_len"],
    type=Type.ID  # Argon2id (mais seguro)
)


class ModernPasswordHasher:
    """
    Hasher moderno de senhas que suporta:
    - Argon2id (padrão para novas senhas)
    - bcrypt (compatibilidade com senhas existentes)
    - Migração automática durante verificação
    """
    
    def __init__(self):
        self.argon2_hasher = argon2_hasher
        
    def hash_password(self, password: str) -> str:
        """
        Gera hash de senha usando Argon2id (algoritmo moderno).
        
        Args:
            password: Senha em texto plano
            
        Returns:
            Hash da senha no formato Argon2id
        """
        try:
            if isinstance(password, str):
                password = password.encode('utf-8')
            elif isinstance(password, bytes):
                pass  # Já está em bytes
            else:
                raise ValueError("Password must be string or bytes")
                
            hash_result = self.argon2_hasher.hash(password)
            logger.debug("Password hashed successfully with Argon2id")
            return hash_result
            
        except Exception as e:
            logger.error(f"Error hashing password with Argon2id: {e}")
            raise
    
    def verify_password(self, password: str, hashed_password: str) -> Tuple[bool, Optional[str]]:
        """
        Verifica senha contra hash existente.
        Suporta tanto Argon2id quanto bcrypt para compatibilidade.
        
        Args:
            password: Senha em texto plano
            hashed_password: Hash armazenado
            
        Returns:
            Tuple (is_valid, new_hash_if_migrated)
            - is_valid: True se senha está correta
            - new_hash_if_migrated: Novo hash Argon2id se migração foi feita
        """
        try:
            if isinstance(password, str):
                password = password.encode('utf-8')
                
            # Detectar tipo de hash
            if hashed_password.startswith('$argon2'):
                # Hash Argon2 - verificação direta
                return self._verify_argon2(password, hashed_password)
                
            elif hashed_password.startswith('$2b$') or hashed_password.startswith('$2a$'):
                # Hash bcrypt - verificar e migrar
                return self._verify_bcrypt_and_migrate(password, hashed_password)
                
            else:
                logger.warning(f"Unknown hash format: {hashed_password[:10]}...")
                return False, None
                
        except Exception as e:
            logger.error(f"Error verifying password: {e}")
            return False, None
    
    def _verify_argon2(self, password: bytes, hashed_password: str) -> Tuple[bool, None]:
        """Verifica senha contra hash Argon2."""
        try:
            self.argon2_hasher.verify(hashed_password, password)
            logger.debug("Password verified successfully with Argon2id")
            return True, None
            
        except argon2_exceptions.VerifyMismatchError:
            logger.debug("Password verification failed with Argon2id")
            return False, None
            
        except Exception as e:
            logger.error(f"Error verifying Argon2 password: {e}")
            return False, None
    
    def _verify_bcrypt_and_migrate(self, password: bytes, hashed_password: str) -> Tuple[bool, Optional[str]]:
        """
        Verifica senha contra hash bcrypt e migra para Argon2id se válida.
        """
        try:
            # Verificar com bcrypt (usando biblioteca diretamente)
            hashed_bytes = hashed_password.encode('utf-8')
            is_valid = bcrypt.checkpw(password, hashed_bytes)
            
            if is_valid:
                logger.info("Password verified with bcrypt - migrating to Argon2id")
                # Migrar para Argon2id
                new_hash = self.hash_password(password.decode('utf-8'))
                return True, new_hash
            else:
                logger.debug("Password verification failed with bcrypt")
                return False, None
                
        except Exception as e:
            logger.error(f"Error verifying bcrypt password: {e}")
            return False, None
    
    def needs_rehash(self, hashed_password: str) -> bool:
        """
        Verifica se o hash precisa ser atualizado.
        
        Args:
            hashed_password: Hash atual
            
        Returns:
            True se precisa ser rehashed (bcrypt ou Argon2 com parâmetros antigos)
        """
        try:
            # Se é bcrypt, precisa migrar
            if hashed_password.startswith('$2b$') or hashed_password.startswith('$2a$'):
                return True
                
            # Se é Argon2, verificar se parâmetros estão atualizados
            if hashed_password.startswith('$argon2'):
                try:
                    # Verificar se precisa rehash com parâmetros atuais
                    return self.argon2_hasher.check_needs_rehash(hashed_password)
                except:
                    # Se der erro, é melhor rehash
                    return True
                    
            return False
            
        except Exception as e:
            logger.error(f"Error checking if rehash needed: {e}")
            return False


# Instância global para uso em toda aplicação
modern_password_hasher = ModernPasswordHasher()

# Funções de conveniência para compatibilidade com código existente
def get_password_hash(password: str) -> str:
    """Gera hash de senha (compatibilidade com código existente)."""
    return modern_password_hasher.hash_password(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verifica senha (compatibilidade com código existente)."""
    is_valid, _ = modern_password_hasher.verify_password(plain_password, hashed_password)
    return is_valid

def verify_password_with_migration(plain_password: str, hashed_password: str) -> Tuple[bool, Optional[str]]:
    """
    Verifica senha e retorna novo hash se migração foi feita.
    
    Returns:
        Tuple (is_valid, new_hash_if_migrated)
    """
    return modern_password_hasher.verify_password(plain_password, hashed_password)
