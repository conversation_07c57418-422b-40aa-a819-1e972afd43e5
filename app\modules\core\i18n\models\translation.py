"""
Translation model for the i18n module.
"""

import uuid
from sqlalchemy import Column, String, Text, ForeignKey, Index, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base


class Translation(Base):
    """
    Model for storing translations of text strings.
    """

    __tablename__ = "translations"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    key = Column(String(255), nullable=False, index=True)
    text = Column(Text, nullable=False)

    # The sector/namespace this translation belongs to (e.g., "menu", "auth", "common")
    sector = Column(String(50), nullable=False, default="common", index=True)

    # Foreign key to TranslationKey
    translation_key_id = Column(Integer, ForeignKey("i18n_translation_keys.id"), nullable=True)

    language_id = Column(UUID(as_uuid=True), ForeignKey("languages.id"), nullable=False)

    # Foreign keys
    last_updated_by_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # Relationships
    language = relationship("Language", backref="translations")
    last_updated_by = relationship(
        "User", foreign_keys=[last_updated_by_id], back_populates="updated_translations"
    )
    key_obj = relationship(
        "TranslationKey", foreign_keys=[translation_key_id], back_populates="translations"
    )

    __table_args__ = (
        Index("ix_translations_key_language_id", "key", "language_id", unique=True),
        Index("ix_translations_sector_language_id", "sector", "language_id"),
    )

    def __repr__(self):
        return f"<Translation(id={self.id}, key='{self.key}', sector='{self.sector}', language_id='{self.language_id}')>"
