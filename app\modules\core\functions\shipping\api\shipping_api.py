import uuid
from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.dependencies import get_db
from app.modules.core.functions.shipping.services.shipping_service import ShippingService
from app.modules.core.functions.shipping.schemas.shipping import ShipmentCreate, ShipmentRead

router = APIRouter(
    prefix="/shipping",
    tags=["Shipping"]
)

@router.get("/cost", response_model=float)
async def get_shipping_cost(
    weight: float = Query(..., gt=0, description="Weight of the package in kg"),
    distance: float = Query(..., gt=0, description="Distance for delivery in km"),
    db: AsyncSession = Depends(get_db)
):
    """
    Calculates a simulated shipping cost.
    """
    service = ShippingService(db)
    cost = await service.calculate_shipping_cost(weight=weight, distance=distance)
    return cost

@router.post("/shipments", response_model=ShipmentRead)
async def create_shipment(
    shipment_in: ShipmentCreate,
    db: AsyncSession = Depends(get_db),
    # tenant: Tenant = Depends(get_current_tenant), # Placeholder for auth
):
    """
    Creates a new shipment record.
    """
    # This is a placeholder tenant_id - replace with actual tenant from auth
    tenant_id = uuid.uuid4() 
    service = ShippingService(db)
    shipment = await service.create_shipment(shipment_in=shipment_in, tenant_id=tenant_id)
    return shipment