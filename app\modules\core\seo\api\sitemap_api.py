"""
Sitemap API

REST API endpoints for sitemap generation and management.
"""

import uuid
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Response
from fastapi.responses import PlainTextResponse
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.users.dependencies import get_current_user
from app.modules.core.users.models.user import User
from app.modules.core.seo.services.sitemap_service import SitemapService
from app.modules.core.seo.schemas.sitemap_schemas import (
    SitemapEntryCreate, SitemapEntryUpdate, SitemapEntryResponse,
    SitemapResponse, SitemapIndexResponse, SEOSitemapStats
)

router = APIRouter()

# Initialize service
sitemap_service = SitemapService()


@router.post("/entries", response_model=SitemapEntryResponse)
async def create_sitemap_entry(
    entry_data: SitemapEntryCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new sitemap entry."""
    try:
        sitemap_entry = await sitemap_service.create_sitemap_entry(db, entry_data, current_user.id)
        return sitemap_entry
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/entries/{content_type}/{content_id}/{language_code}", response_model=SitemapEntryResponse)
async def get_sitemap_entry(
    content_type: str,
    content_id: uuid.UUID,
    language_code: str,
    db: AsyncSession = Depends(get_db)
):
    """Get sitemap entry for specific content and language."""
    sitemap_entry = await sitemap_service.get_sitemap_entry(db, content_type, content_id, language_code)
    if not sitemap_entry:
        raise HTTPException(status_code=404, detail="Sitemap entry not found")
    return sitemap_entry


@router.put("/entries/{entry_id}", response_model=SitemapEntryResponse)
async def update_sitemap_entry(
    entry_id: uuid.UUID,
    entry_data: SitemapEntryUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update sitemap entry."""
    sitemap_entry = await sitemap_service.update_sitemap_entry(db, entry_id, entry_data)
    if not sitemap_entry:
        raise HTTPException(status_code=404, detail="Sitemap entry not found")
    return sitemap_entry


@router.get("/sitemap.xml")
async def get_sitemap_xml(
    language_code: Optional[str] = Query(None, description="Filter by language code"),
    content_type: Optional[str] = Query(None, description="Filter by content type"),
    base_url: str = Query("https://example.com", description="Base URL for sitemap"),
    db: AsyncSession = Depends(get_db)
):
    """Generate XML sitemap."""
    try:
        sitemap_xml = await sitemap_service.generate_sitemap_xml(
            db, language_code, content_type, base_url
        )
        return PlainTextResponse(
            content=sitemap_xml,
            media_type="application/xml",
            headers={"Content-Disposition": "inline; filename=sitemap.xml"}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sitemap-{language_code}.xml")
async def get_language_sitemap_xml(
    language_code: str,
    base_url: str = Query("https://example.com", description="Base URL for sitemap"),
    db: AsyncSession = Depends(get_db)
):
    """Generate XML sitemap for specific language."""
    try:
        sitemap_xml = await sitemap_service.generate_sitemap_xml(
            db, language_code=language_code, base_url=base_url
        )
        return PlainTextResponse(
            content=sitemap_xml,
            media_type="application/xml",
            headers={"Content-Disposition": f"inline; filename=sitemap-{language_code}.xml"}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sitemap-{content_type}.xml")
async def get_content_type_sitemap_xml(
    content_type: str,
    base_url: str = Query("https://example.com", description="Base URL for sitemap"),
    db: AsyncSession = Depends(get_db)
):
    """Generate XML sitemap for specific content type."""
    try:
        sitemap_xml = await sitemap_service.generate_sitemap_xml(
            db, content_type=content_type, base_url=base_url
        )
        return PlainTextResponse(
            content=sitemap_xml,
            media_type="application/xml",
            headers={"Content-Disposition": f"inline; filename=sitemap-{content_type}.xml"}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sitemapindex.xml")
async def get_sitemap_index_xml(
    base_url: str = Query("https://example.com", description="Base URL for sitemap"),
    db: AsyncSession = Depends(get_db)
):
    """Generate XML sitemap index."""
    try:
        sitemap_index_xml = await sitemap_service.generate_sitemap_index_xml(db, base_url)
        return PlainTextResponse(
            content=sitemap_index_xml,
            media_type="application/xml",
            headers={"Content-Disposition": "inline; filename=sitemapindex.xml"}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/sync/{content_type}/{content_id}")
async def sync_sitemap_from_slugs(
    content_type: str,
    content_id: uuid.UUID,
    base_url: str = Query("https://example.com", description="Base URL for sitemap"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Sync sitemap entries from URL slugs."""
    try:
        await sitemap_service.sync_sitemap_from_slugs(db, content_type, content_id, base_url)
        return {"message": "Sitemap entries synced successfully"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/stats", response_model=SEOSitemapStats)
async def get_sitemap_stats(
    db: AsyncSession = Depends(get_db)
):
    """Get sitemap statistics."""
    try:
        stats = await sitemap_service.get_sitemap_stats(db)
        return SEOSitemapStats(**stats)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
