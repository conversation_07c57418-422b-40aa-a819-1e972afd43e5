"""Stripe payment processor implementation."""

import logging  # noqa: E402
from typing import Dict, Any, Optional
from decimal import Decimal

import stripe  # noqa: E402
from stripe.error import StripeError

from app.modules.core.payments.processors.base import PaymentProcessorInterface  # noqa: E402
from app.modules.core.payments.models.payment_transaction import PaymentStatus

logger = logging.getLogger(__name__)


class StripeProcessor(PaymentProcessorInterface):
    """
    Stripe payment processor implementation.
    """

    def __init__(self, processor_config: Dict[str, Any]):
        """
        Initialize the Stripe processor with the given configuration.

        Args:
            processor_config: A dictionary containing the processor configuration.
                This should include API keys, secrets, and other settings.
        """
        super().__init__(processor_config)

        # Set the API key based on sandbox mode
        if self.sandbox_mode:
            api_key = self.config.get("test_api_key")
        else:
            api_key = self.config.get("api_key")

        if not api_key:
            raise ValueError("Stripe API key is required")

        # Initialize the Stripe client
        stripe.api_key = api_key

        # Set the API version (optional)
        api_version = self.config.get("api_version")
        if api_version:
            stripe.api_version = api_version

    async def process_payment(
        self,
        amount: Decimal,
        currency: str,
        payment_method_id: Optional[str] = None,
        customer_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Process a payment using Stripe.

        Args:
            amount: The amount to charge.
            currency: The currency code (e.g., 'USD', 'BRL').
            payment_method_id: An optional ID of the payment method to use.
            customer_id: An optional ID of the customer in Stripe.
            metadata: Optional additional data to include with the payment.

        Returns:
            A dictionary containing the payment details, including:
                - external_id: The ID of the payment in Stripe.
                - status: The status of the payment.
                - error: An error message if the payment failed.
        """
        try:
            # Convert amount to cents (Stripe requires integer amounts)
            amount_cents = int(amount * 100)

            # Create payment intent
            payment_intent_params = {
                "amount": amount_cents,
                "currency": currency.lower(),
                "metadata": metadata or {},
            }

            if payment_method_id:
                payment_intent_params["payment_method"] = payment_method_id
                payment_intent_params["confirm"] = True
                payment_intent_params["return_url"] = self.config.get(
                    "return_url", "https://example.com/return"
                )

            if customer_id:
                payment_intent_params["customer"] = customer_id

            # Create the payment intent
            payment_intent = stripe.PaymentIntent.create(**payment_intent_params)

            # Map the status
            status = self.map_status_to_internal(payment_intent.status)

            return {
                "external_id": payment_intent.id,
                "status": status,
                "client_secret": payment_intent.client_secret,
                "next_action": payment_intent.get("next_action"),
            }

        except StripeError as e:
            logger.error(f"Stripe payment error: {str(e)}")
            return {
                "status": PaymentStatus.FAILED,
                "error": str(e),
            }
        except Exception as e:
            logger.error(f"Unexpected error processing Stripe payment: {str(e)}")
            return {
                "status": PaymentStatus.FAILED,
                "error": f"Unexpected error: {str(e)}",
            }

    async def process_refund(
        self,
        payment_id: str,
        amount: Optional[Decimal] = None,
        reason: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Process a refund for a payment using Stripe.

        Args:
            payment_id: The ID of the payment in Stripe.
            amount: The amount to refund. If None, refund the full amount.
            reason: An optional reason for the refund.
            metadata: Optional additional data to include with the refund.

        Returns:
            A dictionary containing the refund details, including:
                - external_id: The ID of the refund in Stripe.
                - status: The status of the refund.
                - error: An error message if the refund failed.
        """
        try:
            refund_params = {
                "payment_intent": payment_id,
                "metadata": metadata or {},
            }

            if amount:
                # Convert amount to cents (Stripe requires integer amounts)
                amount_cents = int(amount * 100)
                refund_params["amount"] = amount_cents

            if reason:
                refund_params["reason"] = reason

            # Create the refund
            refund = stripe.Refund.create(**refund_params)

            # Map the status
            status = self.map_status_to_internal(refund.status)

            return {
                "external_id": refund.id,
                "status": status,
            }

        except StripeError as e:
            logger.error(f"Stripe refund error: {str(e)}")
            return {
                "status": PaymentStatus.FAILED,
                "error": str(e),
            }
        except Exception as e:
            logger.error(f"Unexpected error processing Stripe refund: {str(e)}")
            return {
                "status": PaymentStatus.FAILED,
                "error": f"Unexpected error: {str(e)}",
            }

    async def get_payment_status(self, payment_id: str) -> Dict[str, Any]:
        """
        Get the status of a payment from Stripe.

        Args:
            payment_id: The ID of the payment in Stripe.

        Returns:
            A dictionary containing the payment details, including:
                - status: The status of the payment.
                - error: An error message if the status check failed.
        """
        try:
            # Retrieve the payment intent
            payment_intent = stripe.PaymentIntent.retrieve(payment_id)

            # Map the status
            status = self.map_status_to_internal(payment_intent.status)

            return {
                "status": status,
                "external_id": payment_intent.id,
                "amount": Decimal(payment_intent.amount) / 100,  # Convert cents to decimal
                "currency": payment_intent.currency,
                "metadata": payment_intent.metadata,
            }

        except StripeError as e:
            logger.error(f"Stripe payment status error: {str(e)}")
            return {
                "status": PaymentStatus.FAILED,
                "error": str(e),
            }
        except Exception as e:
            logger.error(f"Unexpected error getting Stripe payment status: {str(e)}")
            return {
                "status": PaymentStatus.FAILED,
                "error": f"Unexpected error: {str(e)}",
            }

    async def get_refund_status(self, refund_id: str) -> Dict[str, Any]:
        """
        Get the status of a refund from Stripe.

        Args:
            refund_id: The ID of the refund in Stripe.

        Returns:
            A dictionary containing the refund details, including:
                - status: The status of the refund.
                - error: An error message if the status check failed.
        """
        try:
            # Retrieve the refund
            refund = stripe.Refund.retrieve(refund_id)

            # Map the status
            status = self.map_status_to_internal(refund.status)

            return {
                "status": status,
                "external_id": refund.id,
                "amount": Decimal(refund.amount) / 100,  # Convert cents to decimal
                "currency": refund.currency,
                "metadata": refund.metadata,
            }

        except StripeError as e:
            logger.error(f"Stripe refund status error: {str(e)}")
            return {
                "status": PaymentStatus.FAILED,
                "error": str(e),
            }
        except Exception as e:
            logger.error(f"Unexpected error getting Stripe refund status: {str(e)}")
            return {
                "status": PaymentStatus.FAILED,
                "error": f"Unexpected error: {str(e)}",
            }

    async def create_payment_method(
        self,
        method_type: str,
        method_details: Dict[str, Any],
        customer_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Create a payment method in Stripe.

        Args:
            method_type: The type of payment method (e.g., 'card', 'bank_account').
            method_details: Details of the payment method.
            customer_id: An optional ID of the customer to associate with the method.

        Returns:
            A dictionary containing the payment method details, including:
                - external_id: The ID of the payment method in Stripe.
                - error: An error message if the creation failed.
        """
        try:
            # Create the payment method
            payment_method = stripe.PaymentMethod.create(
                type=method_type, **{method_type: method_details}
            )

            # If customer_id is provided, attach the payment method to the customer
            if customer_id:
                stripe.PaymentMethod.attach(payment_method.id, customer=customer_id)

            return {
                "external_id": payment_method.id,
                "type": payment_method.type,
                "details": payment_method[payment_method.type],
            }

        except StripeError as e:
            logger.error(f"Stripe payment method creation error: {str(e)}")
            return {
                "error": str(e),
            }
        except Exception as e:
            logger.error(f"Unexpected error creating Stripe payment method: {str(e)}")
            return {
                "error": f"Unexpected error: {str(e)}",
            }

    async def delete_payment_method(self, method_id: str) -> Dict[str, Any]:
        """
        Delete a payment method from Stripe.

        Args:
            method_id: The ID of the payment method in Stripe.

        Returns:
            A dictionary containing the result, including:
                - success: Whether the deletion was successful.
                - error: An error message if the deletion failed.
        """
        try:
            # Detach the payment method (this is how you "delete" a payment method in Stripe)
            payment_method = stripe.PaymentMethod.detach(method_id)

            return {
                "success": True,
                "external_id": payment_method.id,
            }

        except StripeError as e:
            logger.error(f"Stripe payment method deletion error: {str(e)}")
            return {
                "success": False,
                "error": str(e),
            }
        except Exception as e:
            logger.error(f"Unexpected error deleting Stripe payment method: {str(e)}")
            return {
                "success": False,
                "error": f"Unexpected error: {str(e)}",
            }

    async def create_customer(self, customer_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a customer in Stripe.

        Args:
            customer_details: Details of the customer.

        Returns:
            A dictionary containing the customer details, including:
                - external_id: The ID of the customer in Stripe.
                - error: An error message if the creation failed.
        """
        try:
            # Create the customer
            customer = stripe.Customer.create(**customer_details)

            return {
                "external_id": customer.id,
                "email": customer.email,
                "name": customer.name,
            }

        except StripeError as e:
            logger.error(f"Stripe customer creation error: {str(e)}")
            return {
                "error": str(e),
            }
        except Exception as e:
            logger.error(f"Unexpected error creating Stripe customer: {str(e)}")
            return {
                "error": f"Unexpected error: {str(e)}",
            }

    async def update_customer(
        self, customer_id: str, customer_details: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Update a customer in Stripe.

        Args:
            customer_id: The ID of the customer in Stripe.
            customer_details: Updated details of the customer.

        Returns:
            A dictionary containing the customer details, including:
                - external_id: The ID of the customer in Stripe.
                - error: An error message if the update failed.
        """
        try:
            # Update the customer
            customer = stripe.Customer.modify(customer_id, **customer_details)

            return {
                "external_id": customer.id,
                "email": customer.email,
                "name": customer.name,
            }

        except StripeError as e:
            logger.error(f"Stripe customer update error: {str(e)}")
            return {
                "error": str(e),
            }
        except Exception as e:
            logger.error(f"Unexpected error updating Stripe customer: {str(e)}")
            return {
                "error": f"Unexpected error: {str(e)}",
            }

    async def delete_customer(self, customer_id: str) -> Dict[str, Any]:
        """
        Delete a customer from Stripe.

        Args:
            customer_id: The ID of the customer in Stripe.

        Returns:
            A dictionary containing the result, including:
                - success: Whether the deletion was successful.
                - error: An error message if the deletion failed.
        """
        try:
            # Delete the customer
            customer = stripe.Customer.delete(customer_id)

            return {
                "success": customer.deleted,
                "external_id": customer_id,
            }

        except StripeError as e:
            logger.error(f"Stripe customer deletion error: {str(e)}")
            return {
                "success": False,
                "error": str(e),
            }
        except Exception as e:
            logger.error(f"Unexpected error deleting Stripe customer: {str(e)}")
            return {
                "success": False,
                "error": f"Unexpected error: {str(e)}",
            }

    @staticmethod
    def map_status_to_internal(processor_status: str) -> PaymentStatus:
        """
        Map a Stripe-specific status to an internal PaymentStatus.

        Args:
            processor_status: The status from Stripe.

        Returns:
            The corresponding internal PaymentStatus.
        """
        status_map = {
            "succeeded": PaymentStatus.COMPLETED,
            "requires_payment_method": PaymentStatus.PENDING,
            "requires_confirmation": PaymentStatus.PENDING,
            "requires_action": PaymentStatus.PENDING,
            "processing": PaymentStatus.PROCESSING,
            "requires_capture": PaymentStatus.PROCESSING,
            "canceled": PaymentStatus.CANCELLED,
            "failed": PaymentStatus.FAILED,
        }
        return status_map.get(processor_status.lower(), PaymentStatus.PENDING)
