import logging
import uuid  # Import uuid if Tenant ID is UUID
from typing import (
    List,
    Optional,
    Annotated,
    Any,
    Union,
)  # Import Annotated and Any  # noqa: E402

from fastapi import (
    APIRouter,
    Depends,
    HTTPException,
    status,
    Query,
    Path,
)  # Import Path

# Set up logger
logger = logging.getLogger(__name__)

# from app import models # Removido - Importar User especificamente
from app.modules.core.users.models.user import User  # Importar User diretamente  # noqa: E402

# Import services and schemas
from app.modules.tenants.restaurants.menu.services.menu_service import (  # noqa: E402
    MenuService,
)

# Import correct dependencies
from app.modules.core.auth.dependencies.auth_dependencies import (  # noqa: E402
    get_current_active_user,
)
from app.modules.core.tenants.dependencies.tenant_dependencies import (  # noqa: E402
    get_current_tenant_from_header,  # Nova dependência de tenant
    require_tenant_role,  # Nova dependência de role
)

# Mantendo get_menu_service se for específico de dependencies
from app.core.dependencies import get_menu_service  # noqa: E402
from app.modules.core.tenants.models.tenant import Tenant  # Import Tenant model
from app.modules.core.roles.models.roles import RolePermissions  # Import the role enums

from app.modules.tenants.restaurants.menu.schemas.menu_category import (  # noqa: E402
    MenuCategoryCreate,
    MenuCategoryUpdate,
    MenuCategoryRead,
    MenuCategoryReadSimple,
    MenuCategoryReadDetailed,  # Import the detailed schema
)
from app.modules.tenants.restaurants.menu.schemas.menu_item import (  # noqa: E402
    MenuItemCreate,
    MenuItemUpdate,
    MenuItemRead,
)

# Import other schemas if needed for more specific endpoints (variants/modifiers)
# from app.modules.tenants.restaurants.menu_control.schemas.variant_group import (
#     VariantGroupCreate,
#     VariantGroupRead,
# )
# from app.modules.tenants.restaurants.menu_control.schemas.modifier_group import (
#     ModifierGroupCreate,
#     ModifierGroupRead,
# )

# Router setup
router = APIRouter(
    # prefix="/menu", # Removido pois já está definido no include_router em app/api/api.py
    tags=["Restaurant - Menu Management"],
    # Apply authentication globally or per-endpoint as needed
)

# Include groups router
from .groups_api import router as groups_router
router.include_router(groups_router)

# --- Menu Category Endpoints ---

# Define required roles for modification endpoints
write_roles = RolePermissions.ADMIN_ROLES


@router.post("/categories/", response_model=MenuCategoryReadSimple, status_code=status.HTTP_201_CREATED)
async def create_menu_category(
    category_in: MenuCategoryCreate,
    # Use Annotated for dependencies
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)],  # Atualizado
    menu_service: Annotated[MenuService, Depends(get_menu_service)],
    # Require specific roles for creating
    current_user_for_action: Annotated[
        User, Depends(get_current_active_user)
    ],  # Usuário para auditoria, etc.
    _: Annotated[
        Any, Depends(require_tenant_role(required_roles=write_roles))
    ],  # Aplica a verificação de role
):
    """
    Create a new menu category for the current tenant.
    Requires OWNER or MANAGER tenant role.
    """
    # Pass tenant ID from the dependency
    created_category = await menu_service.create_category(category_in=category_in, tenant_id=current_tenant.id)
    # Return simple schema to avoid greenlet issues with lazy-loaded relationships
    return MenuCategoryReadSimple.model_validate(created_category)


@router.get("/categories/", response_model=List[Union[MenuCategoryRead, MenuCategoryReadSimple, MenuCategoryReadDetailed]])
async def read_menu_categories(
    # Dependencies first (reordered to satisfy linter)
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)],  # Atualizado
    menu_service: Annotated[MenuService, Depends(get_menu_service)],
    current_user_for_action: Annotated[User, Depends(get_current_active_user)],
    # Aplica a verificação de role
    _: Annotated[Any, Depends(require_tenant_role(required_roles=RolePermissions.VIEW_ROLES))],
    # Query parameters next
    digital_menu_id: Optional[uuid.UUID] = Query(None, description="Filter by digital menu ID"),
    parent_id: Optional[uuid.UUID] = Query(None, description="Filter categories by parent ID"),
    only_top_level: bool = Query(False, description="Only return top-level categories (no parent)"),
    include_children: bool = Query(True, description="Include children in the response"),
    include_items: bool = Query(True, description="Include menu items in the response"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=200, description="Maximum number of records to return"),
):
    """
    Retrieve active menu categories for the current tenant.

    - Use `digital_menu_id` to filter categories by digital menu
    - Use `parent_id` to filter categories by their parent
    - Use `only_top_level=true` to get only top-level categories (no parent)
    - Use `include_children=false` to exclude children from the response

    Requires at least costumer role in the tenant.
    """
    try:
        categories = await menu_service.get_categories(
            tenant_id=current_tenant.id,
            parent_id=parent_id,
            digital_menu_id=digital_menu_id,
            only_top_level=only_top_level,
            include_children=include_children,
            include_items=include_items,
            skip=skip,
            limit=limit,
        )

        # Choose appropriate schema based on parameters to avoid MissingGreenlet error
        if not include_children and not include_items:
            # Use simple schema without relationships
            return [MenuCategoryReadSimple.model_validate(category) for category in categories]
        elif include_children and include_items:
            # Use detailed schema with guaranteed relationships
            return [MenuCategoryReadDetailed.model_validate(category) for category in categories]
        elif include_items and not include_children:
            # Use standard schema with items but no children
            return [MenuCategoryRead.model_validate(category) for category in categories]
        else:
            # include_children=true but include_items=false - use simple schema to avoid greenlet error
            return [MenuCategoryReadSimple.model_validate(category) for category in categories]

    except HTTPException as e:
        # Re-raise any HTTPExceptions from the service
        raise e
    except Exception as e:
        # Log unexpected errors and return a generic error
        logger.exception(f"Unexpected error retrieving categories: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while retrieving categories",
        )


@router.get("/categories/{category_id}", response_model=MenuCategoryRead)
async def read_menu_category(
    # Path parameter first
    category_id: Annotated[uuid.UUID, Path(..., description="The ID of the category to retrieve")],
    # Dependencies after path parameter
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)],  # Atualizado
    menu_service: Annotated[MenuService, Depends(get_menu_service)],
    current_user_for_action: Annotated[User, Depends(get_current_active_user)],
    # Aplica a verificação de role
    _: Annotated[Any, Depends(require_tenant_role(required_roles=RolePermissions.VIEW_ROLES))],
):
    """
    Retrieve a specific active menu category by ID for the current tenant.
    Requires at least costumer role in the tenant.
    """
    category = await menu_service.get_category(category_id=category_id, tenant_id=current_tenant.id)
    if category is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Menu category not found or not active",
        )
    return category


@router.put("/categories/{category_id}", response_model=MenuCategoryRead)
async def update_menu_category(
    # Explicit Path
    category_id: Annotated[uuid.UUID, Path(..., description="The ID of the category to update")],
    category_in: MenuCategoryUpdate,  # Body parameter
    # Dependencies
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)],  # Atualizado
    menu_service: Annotated[MenuService, Depends(get_menu_service)],
    current_user_for_action: Annotated[User, Depends(get_current_active_user)],
    # Aplica a verificação de role
    _: Annotated[Any, Depends(require_tenant_role(required_roles=write_roles))],
):
    """
    Update a specific menu category by ID for the current tenant.

    - Can update name, description, display_order, is_active
    - Can change parent_id to move the category in the hierarchy
    - Cannot create cycles in the category hierarchy

    Requires OWNER or MANAGER tenant role.
    """
    try:
        logger.info(f"Updating category {category_id} for tenant {current_tenant.id}")

        # The service now raises HTTPException directly with appropriate status codes
        updated_category = await menu_service.update_category(
            category_id=category_id,
            category_in=category_in,
            tenant_id=current_tenant.id,
        )

        logger.info(f"Successfully updated category {category_id}")
        return updated_category

    except HTTPException as e:
        # Re-raise any HTTPExceptions from the service
        logger.warning(f"Error updating category {category_id}: {e.detail}")
        raise e
    except Exception as e:
        # Log unexpected errors and return a generic error
        logger.exception(f"Unexpected error updating category {category_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while updating the category",
        )


@router.delete("/categories/{category_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_menu_category(
    # Explicit Path
    category_id: Annotated[
        uuid.UUID, Path(..., description="The ID of the category to deactivate")
    ],
    # Dependencies
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)],  # Atualizado
    menu_service: Annotated[MenuService, Depends(get_menu_service)],
    current_user_for_action: Annotated[User, Depends(get_current_active_user)],
    # Aplica a verificação de role
    _: Annotated[Any, Depends(require_tenant_role(required_roles=write_roles))],
):
    """
    Deactivate (soft delete) a menu category by ID for the current tenant.
    This will also deactivate all child categories recursively.
    Requires OWNER or MANAGER tenant role.
    """
    try:
        logger.info(f"Deleting category {category_id} for tenant {current_tenant.id}")

        # The service now raises HTTPException directly with appropriate status codes
        await menu_service.delete_category(category_id=category_id, tenant_id=current_tenant.id)

        # If we get here, the deletion was successful
        logger.info(f"Successfully deleted category {category_id}")
        return None  # 204 No Content response

    except HTTPException as e:
        # Re-raise any HTTPExceptions from the service
        logger.warning(f"Error deleting category {category_id}: {e.detail}")
        raise e
    except Exception as e:
        # Log unexpected errors and return a generic error
        logger.exception(f"Unexpected error deleting category {category_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while deleting the category",
        )


# --- Menu Item Endpoints ---


@router.post("/items/", status_code=status.HTTP_201_CREATED)
async def create_menu_item(
    item_in: MenuItemCreate,
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)],  # Atualizado
    menu_service: Annotated[MenuService, Depends(get_menu_service)],
    current_user_for_action: Annotated[User, Depends(get_current_active_user)],
    # Aplica a verificação de role
    _: Annotated[Any, Depends(require_tenant_role(required_roles=write_roles))],
):
    """
    Create a new menu item for the current tenant.
    Supports creating nested variant and modifier groups/options.
    Requires OWNER or MANAGER tenant role.
    """
    # Service layer handles category validation and potential errors
    created_item = await menu_service.create_item(item_in=item_in, tenant_id=current_tenant.id)

    # Use jsonable_encoder to handle SQLAlchemy serialization
    from fastapi.encoders import jsonable_encoder
    from fastapi.responses import JSONResponse
    return JSONResponse(content=jsonable_encoder(created_item), status_code=status.HTTP_201_CREATED)


@router.get("/items/")
async def read_menu_items(
    # Dependencies first (reordered to satisfy linter)
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)],  # Atualizado
    menu_service: Annotated[MenuService, Depends(get_menu_service)],
    current_user_for_action: Annotated[User, Depends(get_current_active_user)],
    # Aplica a verificação de role
    _: Annotated[Any, Depends(require_tenant_role(required_roles=RolePermissions.VIEW_ROLES))],
    # Query parameters next
    digital_menu_id: Optional[uuid.UUID] = Query(
        None, description="Filter items by digital menu ID"
    ),
    category_id: Optional[uuid.UUID] = Query(
        None, description="Filter items by category ID"
    ),  # Assuming UUID
    include_details: bool = Query(
        False,
        description="Include full details like variants and modifiers in the list",
    ),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=200),
):
    """
    Retrieve menu items for the current tenant, optionally filtered by digital menu and/or category.
    By default, returns only active items; set 'include_inactive=true' to include soft deleted items.
    By default, returns a simplified list; set 'include_details=true' for full data.
    Requires at least costumer role in the tenant.
    """
    items = await menu_service.get_items(
        tenant_id=current_tenant.id,
        digital_menu_id=digital_menu_id,
        category_id=category_id,
        skip=skip,
        limit=limit,
        include_details=include_details,
    )

    # Return items directly without Pydantic validation to avoid serialization issues
    return items


@router.get("/items/{item_id}")
async def read_menu_item(
    # Path parameter first
    item_id: Annotated[uuid.UUID, Path(..., description="The ID of the item to retrieve")],
    # Dependencies after path parameter
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)],  # Atualizado
    menu_service: Annotated[MenuService, Depends(get_menu_service)],
    current_user_for_action: Annotated[User, Depends(get_current_active_user)],
    # Aplica a verificação de role
    _: Annotated[Any, Depends(require_tenant_role(required_roles=RolePermissions.VIEW_ROLES))],
):
    """
    Retrieve a specific active menu item by ID, including its category, variants, and modifiers.
    Requires at least costumer role in the tenant.
    """
    item = await menu_service.get_item(
        item_id=item_id, tenant_id=current_tenant.id, include_details=True
    )
    if item is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Menu item not found or not active",
        )

    # Use jsonable_encoder to handle SQLAlchemy serialization
    from fastapi.encoders import jsonable_encoder
    from fastapi.responses import JSONResponse
    return JSONResponse(content=jsonable_encoder(item))


@router.put("/items/{item_id}")
async def update_menu_item(
    # Explicit Path
    item_id: Annotated[uuid.UUID, Path(..., description="The ID of the item to update")],
    item_in: MenuItemUpdate,  # Body parameter
    # Dependencies
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)],  # Atualizado
    menu_service: Annotated[MenuService, Depends(get_menu_service)],
    current_user_for_action: Annotated[User, Depends(get_current_active_user)],
    # Aplica a verificação de role
    _: Annotated[Any, Depends(require_tenant_role(required_roles=write_roles))],
):
    """
    Update a specific menu item by ID for the current tenant.
    Note: This currently updates only the base fields of the item.
    Requires OWNER or MANAGER tenant role.
    """
    updated_item = await menu_service.update_item(
        item_id=item_id, item_in=item_in, tenant_id=current_tenant.id
    )
    if updated_item is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Menu item not found or not active",
        )

    # Use jsonable_encoder to handle SQLAlchemy serialization
    from fastapi.encoders import jsonable_encoder
    from fastapi.responses import JSONResponse
    return JSONResponse(content=jsonable_encoder(updated_item))


@router.delete("/items/{item_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_menu_item(
    # Explicit Path
    item_id: Annotated[uuid.UUID, Path(..., description="The ID of the item to permanently delete")],
    # Dependencies
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)],  # Atualizado
    menu_service: Annotated[MenuService, Depends(get_menu_service)],
    current_user_for_action: Annotated[User, Depends(get_current_active_user)],
    # Aplica a verificação de role
    _: Annotated[Any, Depends(require_tenant_role(required_roles=write_roles))],
):
    """
    Permanently delete a menu item by ID for the current tenant.
    WARNING: This action cannot be undone. The item will be completely removed from the database.
    Requires OWNER or MANAGER tenant role.
    """
    success = await menu_service.delete_item(item_id=item_id, tenant_id=current_tenant.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Menu item not found or not active",
        )
    # 204 No Content response (success=True)


# --- Drag & Drop Reordering Endpoints ---

@router.put("/categories/reorder", response_model=List[MenuCategoryRead])
async def reorder_categories(
    category_orders: List[dict],  # [{"id": "uuid", "display_order": int}, ...]
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)],
    menu_service: Annotated[MenuService, Depends(get_menu_service)],
    current_user_for_action: Annotated[User, Depends(get_current_active_user)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=write_roles))],
):
    """
    Reorder menu categories by updating their display_order.
    Expects a list of objects with 'id' and 'display_order' fields.
    Requires OWNER or MANAGER tenant role.
    """
    try:
        updated_categories = []

        for category_order in category_orders:
            category_id = uuid.UUID(category_order["id"])
            display_order = category_order["display_order"]

            updated_category = await menu_service.update_category(
                category_id=category_id,
                category_in=MenuCategoryUpdate(display_order=display_order),
                tenant_id=current_tenant.id
            )

            if updated_category:
                updated_categories.append(updated_category)

        return updated_categories
    except Exception as e:
        logger.error(f"Error reordering categories for tenant {current_tenant.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error reordering categories"
        )


@router.put("/items/reorder")
async def reorder_items(
    item_orders: List[dict],  # [{"id": "uuid", "display_order": int}, ...]
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)],
    menu_service: Annotated[MenuService, Depends(get_menu_service)],
    current_user_for_action: Annotated[User, Depends(get_current_active_user)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=write_roles))],
):
    """
    Reorder menu items by updating their display_order.
    Expects a list of objects with 'id' and 'display_order' fields.
    Requires OWNER or MANAGER tenant role.
    """
    try:
        updated_items = []

        for item_order in item_orders:
            item_id = uuid.UUID(item_order["id"])
            display_order = item_order["display_order"]

            updated_item = await menu_service.update_item(
                item_id=item_id,
                item_in=MenuItemUpdate(display_order=display_order),
                tenant_id=current_tenant.id
            )

            if updated_item:
                # Use jsonable_encoder to handle SQLAlchemy serialization
                from fastapi.encoders import jsonable_encoder
                updated_items.append(jsonable_encoder(updated_item))

        return updated_items
    except Exception as e:
        logger.error(f"Error reordering items for tenant {current_tenant.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error reordering items"
        )
