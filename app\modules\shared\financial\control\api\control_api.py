"""Financial Control API Endpoints."""

from typing import List, Optional
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.auth_dependencies import get_current_active_user
from app.modules.core.tenants.dependencies.tenant_dependencies import get_current_tenant
from app.core.pagination import PaginationParams
from app.core.exceptions import NotFoundError, ValidationError
from app.modules.core.users.models.user import User
from app.modules.core.tenants.models.tenant import Tenant

from ..services.control_service import ControlService
from ..schemas.control_entry_schemas import (
    ControlEntryCreate, ControlEntryUpdate, ControlEntryResponse,
    ControlEntryList, ControlEntryFilters, ControlEntryBulkUpdate
)

router = APIRouter(prefix="/control", tags=["Financial Control"])


@router.post("/entries", response_model=ControlEntryResponse)
async def create_control_entry(
    entry_data: ControlEntryCreate,
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new financial control entry."""
    
    service = ControlService(db)
    
    try:
        entry = await service.create_entry(entry_data, tenant.id, current_user.id)
        return ControlEntryResponse.model_validate(entry)
    except ValidationError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get("/entries", response_model=ControlEntryList)
async def list_control_entries(
    tenant: Tenant = Depends(get_current_tenant),
    pagination: PaginationParams = Depends(),
    db: AsyncSession = Depends(get_db),
    # Filter parameters
    entry_type: Optional[str] = Query(None),
    status: Optional[List[str]] = Query(None),
    category_id: Optional[UUID] = Query(None),
    supplier_id: Optional[UUID] = Query(None),
    date_from: Optional[str] = Query(None),
    date_to: Optional[str] = Query(None),
    amount_min: Optional[float] = Query(None),
    amount_max: Optional[float] = Query(None),
    search: Optional[str] = Query(None),
    is_overdue: Optional[bool] = Query(None),
    sort_by: Optional[str] = Query("entry_date"),
    sort_order: Optional[str] = Query("desc")
):
    """List financial control entries with filtering and pagination."""
    
    # Build filters
    filters = ControlEntryFilters(
        entry_type=entry_type,
        status=status,
        category_id=category_id,
        supplier_id=supplier_id,
        date_from=date_from,
        date_to=date_to,
        amount_min=amount_min,
        amount_max=amount_max,
        search=search,
        is_overdue=is_overdue,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    service = ControlService(db)
    result = await service.list_entries(tenant.id, filters, pagination)

    return ControlEntryList(
        items=[ControlEntryResponse.model_validate(entry) for entry in result["items"]],
        total=result["total"],
        page=result["page"],
        size=result["size"],
        pages=result["pages"]
    )


@router.get("/entries/{entry_id}", response_model=ControlEntryResponse)
async def get_control_entry(
    entry_id: UUID,
    tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """Get a financial control entry by ID."""
    
    service = ControlService(db)
    
    try:
        entry = await service.get_entry(entry_id, tenant.id)
        return ControlEntryResponse.model_validate(entry)
    except NotFoundError:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Entry not found")


@router.put("/entries/{entry_id}", response_model=ControlEntryResponse)
async def update_control_entry(
    entry_id: UUID,
    entry_data: ControlEntryUpdate,
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Update a financial control entry."""
    
    service = ControlService(db)
    
    try:
        entry = await service.update_entry(entry_id, entry_data, tenant.id, current_user.id)
        return ControlEntryResponse.model_validate(entry)
    except NotFoundError:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Entry not found")
    except ValidationError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.delete("/entries/{entry_id}")
async def delete_control_entry(
    entry_id: UUID,
    tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """Delete a financial control entry."""
    
    service = ControlService(db)
    
    try:
        await service.delete_entry(entry_id, tenant.id)
        return {"message": "Entry deleted successfully"}
    except NotFoundError:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Entry not found")


@router.post("/entries/bulk-update")
async def bulk_update_entries(
    bulk_data: ControlEntryBulkUpdate,
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Bulk update financial control entries."""
    
    service = ControlService(db)
    
    try:
        entries = await service.bulk_update_entries(bulk_data, tenant.id, current_user.id)
        return {
            "message": f"Updated {len(entries)} entries successfully",
            "updated_entries": [str(entry.id) for entry in entries]
        }
    except ValidationError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.patch("/entries/{entry_id}/archive", response_model=ControlEntryResponse)
async def archive_entry(
    entry_id: UUID,
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Archive a financial control entry."""
    
    service = ControlService(db)
    
    try:
        entry = await service.archive_entry(entry_id, tenant.id, current_user.id)
        return ControlEntryResponse.model_validate(entry)
    except NotFoundError:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Entry not found")


@router.patch("/entries/{entry_id}/mark-paid", response_model=ControlEntryResponse)
async def mark_entry_as_paid(
    entry_id: UUID,
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
    payment_date: Optional[str] = Query(None),
    payment_method: Optional[str] = Query(None),
    payment_reference: Optional[str] = Query(None)
):
    """Mark an entry as paid."""
    
    service = ControlService(db)
    
    try:
        # Parse payment_date if provided
        parsed_payment_date = None
        if payment_date:
            from datetime import datetime
            parsed_payment_date = datetime.strptime(payment_date, "%Y-%m-%d").date()
        
        entry = await service.mark_as_paid(
            entry_id, tenant.id, current_user.id,
            payment_date=parsed_payment_date,
            payment_method=payment_method,
            payment_reference=payment_reference
        )
        return ControlEntryResponse.model_validate(entry)
    except NotFoundError:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Entry not found")
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Invalid date format: {e}")


# Health check endpoint
@router.get("/health")
async def health_check():
    """Health check for financial control module."""
    return {"status": "healthy", "module": "financial_control"}
