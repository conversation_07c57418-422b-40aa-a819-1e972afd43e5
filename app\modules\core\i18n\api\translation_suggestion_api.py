"""
API endpoints for translation suggestion management.
"""

import logging
from typing import List, Annotated, Any, Optional, TYPE_CHECKING

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.user_auth import get_current_active_user
from app.modules.core.auth.dependencies.role_auth import require_system_role
from app.modules.core.roles.models.roles import SystemRole

if TYPE_CHECKING:
    from app.modules.core.users.models.user import User

from app.modules.core.i18n.schemas.translation_suggestion import (
    TranslationSuggestionCreate,
    TranslationSuggestionUpdate,
    TranslationSuggestionRead,
)
from app.modules.core.i18n.services.translation_suggestion_service import (
    TranslationSuggestionService,
)

# Set up logger
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# Define required roles
admin_roles = [SystemRole.ADMIN]


@router.post("/", response_model=TranslationSuggestionRead, status_code=status.HTTP_201_CREATED)
async def create_translation_suggestion(
    suggestion_in: TranslationSuggestionCreate,
    db: Annotated[AsyncSession, Depends(get_db)],
    current_user: Annotated["User", Depends(get_current_active_user)],
):
    """
    Create a new translation suggestion.
    Any authenticated user can create a suggestion.
    """
    suggestion_service = TranslationSuggestionService()
    try:
        return await suggestion_service.create_translation_suggestion(
            db, suggestion_in, current_user.id
        )
    except Exception as e:
        logger.error(f"Error creating translation suggestion: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Error creating translation suggestion",
        )


@router.get("/", response_model=List[TranslationSuggestionRead])
async def read_translation_suggestions(
    db: Annotated[AsyncSession, Depends(get_db)],
    current_user: Annotated["User", Depends(get_current_active_user)],
    _: Annotated[Any, Depends(require_system_role(required_roles=admin_roles))],
    language_id: Optional[int] = Query(None, description="Filter by language ID"),
    key: Optional[str] = Query(None, description="Filter by translation key"),
    status: Optional[str] = Query(
        None, description="Filter by status (pending, approved, rejected)"
    ),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=200),
):
    """
    Retrieve all translation suggestions with pagination and optional filtering.
    Requires ADMIN system role.
    """
    suggestion_service = TranslationSuggestionService()
    return await suggestion_service.get_translation_suggestions(
        db, language_id=language_id, key=key, status=status, skip=skip, limit=limit
    )


@router.get("/my", response_model=List[TranslationSuggestionRead])
async def read_my_translation_suggestions(
    db: Annotated[AsyncSession, Depends(get_db)],
    current_user: Annotated["User", Depends(get_current_active_user)],
    language_id: Optional[int] = Query(None, description="Filter by language ID"),
    key: Optional[str] = Query(None, description="Filter by translation key"),
    status: Optional[str] = Query(
        None, description="Filter by status (pending, approved, rejected)"
    ),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=200),
):
    """
    Retrieve all translation suggestions made by the current user.
    """
    suggestion_service = TranslationSuggestionService()
    return await suggestion_service.get_user_translation_suggestions(
        db,
        user_id=current_user.id,
        language_id=language_id,
        key=key,
        status=status,
        skip=skip,
        limit=limit,
    )


@router.get("/{suggestion_id}", response_model=TranslationSuggestionRead)
async def read_translation_suggestion(
    suggestion_id: Annotated[int, Path(..., description="The ID of the suggestion to retrieve")],
    db: Annotated[AsyncSession, Depends(get_db)],
    current_user: Annotated["User", Depends(get_current_active_user)],
):
    """
    Retrieve a specific translation suggestion by ID.
    Users can only view their own suggestions unless they are admins.
    """
    suggestion_service = TranslationSuggestionService()
    suggestion = await suggestion_service.get_translation_suggestion(db, suggestion_id)

    if not suggestion:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Translation suggestion not found",
        )

    # Check if user is admin or the suggestion owner
    is_admin = SystemRole.ADMIN in [role.name for role in current_user.roles]
    if not is_admin and suggestion.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this suggestion",
        )

    return suggestion


@router.put("/{suggestion_id}", response_model=TranslationSuggestionRead)
async def update_translation_suggestion(
    suggestion_id: Annotated[int, Path(..., description="The ID of the suggestion to update")],
    suggestion_in: TranslationSuggestionUpdate,
    db: Annotated[AsyncSession, Depends(get_db)],
    current_user: Annotated["User", Depends(get_current_active_user)],
):
    """
    Update a translation suggestion.
    Users can only update their own pending suggestions.
    Admins can update any suggestion, including changing the status.
    """
    suggestion_service = TranslationSuggestionService()

    # Get the existing suggestion
    existing_suggestion = await suggestion_service.get_translation_suggestion(db, suggestion_id)
    if not existing_suggestion:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Translation suggestion not found",
        )

    # Check permissions
    is_admin = SystemRole.ADMIN in [role.name for role in current_user.roles]
    is_owner = existing_suggestion.user_id == current_user.id

    # Regular users can only update their own pending suggestions
    if not is_admin and (not is_owner or existing_suggestion.status != "pending"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update this suggestion",
        )

    # Regular users cannot change the status
    if not is_admin and "status" in suggestion_in.model_dump(exclude_unset=True):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Regular users cannot change suggestion status",
        )

    # Perform the update
    updated_suggestion = await suggestion_service.update_translation_suggestion(
        db, suggestion_id, suggestion_in
    )

    return updated_suggestion


@router.delete("/{suggestion_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_translation_suggestion(
    suggestion_id: Annotated[int, Path(..., description="The ID of the suggestion to delete")],
    db: Annotated[AsyncSession, Depends(get_db)],
    current_user: Annotated["User", Depends(get_current_active_user)],
):
    """
    Delete a translation suggestion.
    Users can only delete their own pending suggestions.
    Admins can delete any suggestion.
    """
    suggestion_service = TranslationSuggestionService()

    # Get the existing suggestion
    existing_suggestion = await suggestion_service.get_translation_suggestion(db, suggestion_id)
    if not existing_suggestion:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Translation suggestion not found",
        )

    # Check permissions
    is_admin = SystemRole.ADMIN in [role.name for role in current_user.roles]
    is_owner = existing_suggestion.user_id == current_user.id

    # Regular users can only delete their own pending suggestions
    if not is_admin and (not is_owner or existing_suggestion.status != "pending"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to delete this suggestion",
        )

    # Perform the deletion
    result = await suggestion_service.delete_translation_suggestion(db, suggestion_id)

    return None


@router.post("/{suggestion_id}/approve", response_model=TranslationSuggestionRead)
async def approve_translation_suggestion(
    suggestion_id: Annotated[int, Path(..., description="The ID of the suggestion to approve")],
    db: Annotated[AsyncSession, Depends(get_db)],
    current_user: Annotated["User", Depends(get_current_active_user)],
    _: Annotated[Any, Depends(require_system_role(required_roles=admin_roles))],
):
    """
    Approve a translation suggestion and create a translation from it.
    Requires ADMIN system role.
    """
    suggestion_service = TranslationSuggestionService()

    try:
        approved_suggestion = await suggestion_service.approve_translation_suggestion(
            db, suggestion_id, current_user.id
        )
        if not approved_suggestion:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Translation suggestion not found",
            )
        return approved_suggestion
    except Exception as e:
        logger.error(f"Error approving translation suggestion: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )


@router.post("/{suggestion_id}/reject", response_model=TranslationSuggestionRead)
async def reject_translation_suggestion(
    suggestion_id: Annotated[int, Path(..., description="The ID of the suggestion to reject")],
    db: Annotated[AsyncSession, Depends(get_db)],
    current_user: Annotated["User", Depends(get_current_active_user)],
    _: Annotated[Any, Depends(require_system_role(required_roles=admin_roles))],
):
    """
    Reject a translation suggestion.
    Requires ADMIN system role.
    """
    suggestion_service = TranslationSuggestionService()

    rejected_suggestion = await suggestion_service.reject_translation_suggestion(db, suggestion_id)
    if not rejected_suggestion:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Translation suggestion not found",
        )
    return rejected_suggestion
