"""
API endpoints for Digital Menu management.
Handles multi-menu functionality with time-based scheduling.
"""

import logging
import uuid
from typing import List, Optional, Annotated
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path

# Import dependencies
from app.modules.core.users.models.user import User
from app.modules.core.auth.dependencies.auth_dependencies import get_current_active_user
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    get_current_tenant_from_header,
    require_tenant_role,
)
from app.modules.core.tenants.models.tenant import Tenant
from app.modules.core.roles.models.roles import RolePermissions, TenantRole
from app.core.dependencies import get_digital_menu_service

# Import services
from app.modules.tenants.restaurants.menu.services.digital_menu_service import DigitalMenuService

# Import schemas
from app.modules.tenants.restaurants.menu.schemas.digital_menu import (
    DigitalMenuCreate,
    DigitalMenuUpdate,
    DigitalMenuRead,
    DigitalMenuWithCategories,
)

logger = logging.getLogger(__name__)

# Router setup
router = APIRouter(
    prefix="/digital-menus",
    tags=["Restaurant - Digital Menu Management"],
)


@router.post("/", response_model=DigitalMenuRead, status_code=status.HTTP_201_CREATED)
async def create_digital_menu(
    menu_in: DigitalMenuCreate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)],
    _: Annotated[None, Depends(require_tenant_role([TenantRole.OWNER, TenantRole.STAFF]))],
    menu_service: Annotated[DigitalMenuService, Depends(get_digital_menu_service)],
):
    """
    Create a new digital menu.
    Requires tenant owner or staff permissions.
    """
    try:
        return await menu_service.create_digital_menu(menu_in, current_tenant.id)
    except Exception as e:
        logger.error(f"Error creating digital menu for tenant {current_tenant.id}: {e}")
        raise


@router.get("/", response_model=List[DigitalMenuRead])
async def get_digital_menus(
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)],
    _: Annotated[None, Depends(require_tenant_role([TenantRole.OWNER, TenantRole.STAFF]))],
    menu_service: Annotated[DigitalMenuService, Depends(get_digital_menu_service)],
    include_categories: bool = Query(False, description="Include categories in response"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
):
    """
    Get all digital menus for the current tenant.
    Requires tenant owner or staff permissions.
    """
    try:
        return await menu_service.get_digital_menus(
            tenant_id=current_tenant.id,
            include_categories=include_categories,
            skip=skip,
            limit=limit
        )
    except Exception as e:
        logger.error(f"Error retrieving digital menus for tenant {current_tenant.id}: {e}")
        raise


@router.get("/current", response_model=DigitalMenuWithCategories)
async def get_current_digital_menu(
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)],
    _: Annotated[None, Depends(require_tenant_role([TenantRole.OWNER, TenantRole.STAFF]))],
    menu_service: Annotated[DigitalMenuService, Depends(get_digital_menu_service)],
    timezone: str = Query("UTC", description="Timezone for schedule checking"),
):
    """
    Get the currently active digital menu based on time scheduling.
    Requires tenant owner or staff permissions.
    """
    try:
        current_menu = await menu_service.get_active_menu_for_time(
            tenant_id=current_tenant.id,
            timezone_str=timezone
        )

        if not current_menu:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No active menu found"
            )

        return current_menu
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving current digital menu for tenant {current_tenant.id}: {e}")
        raise


@router.get("/{menu_id}", response_model=DigitalMenuWithCategories)
async def get_digital_menu(
    menu_id: uuid.UUID,
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)],
    _: Annotated[None, Depends(require_tenant_role([TenantRole.OWNER, TenantRole.STAFF]))],
    menu_service: Annotated[DigitalMenuService, Depends(get_digital_menu_service)],
    include_categories: bool = Query(True, description="Include categories in response"),
):
    """
    Get a specific digital menu by ID.
    Requires tenant owner or staff permissions.
    """
    try:
        menu = await menu_service.get_digital_menu(
            menu_id=menu_id,
            tenant_id=current_tenant.id,
            include_categories=include_categories
        )

        if not menu:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Digital menu with ID {menu_id} not found"
            )

        return menu
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving digital menu {menu_id} for tenant {current_tenant.id}: {e}")
        raise


@router.put("/{menu_id}", response_model=DigitalMenuRead)
async def update_digital_menu(
    menu_id: uuid.UUID,
    menu_in: DigitalMenuUpdate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)],
    _: Annotated[None, Depends(require_tenant_role([TenantRole.OWNER, TenantRole.STAFF]))],
    menu_service: Annotated[DigitalMenuService, Depends(get_digital_menu_service)],
):
    """
    Update a digital menu.
    Requires tenant owner or staff permissions.
    """
    try:
        updated_menu = await menu_service.update_digital_menu(
            menu_id=menu_id,
            menu_in=menu_in,
            tenant_id=current_tenant.id
        )

        if not updated_menu:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Digital menu with ID {menu_id} not found"
            )

        return updated_menu
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating digital menu {menu_id} for tenant {current_tenant.id}: {e}")
        raise


@router.delete("/{menu_id}", response_model=DigitalMenuRead)
async def delete_digital_menu(
    menu_id: uuid.UUID,
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)],
    _: Annotated[None, Depends(require_tenant_role([TenantRole.OWNER, TenantRole.STAFF]))],
    menu_service: Annotated[DigitalMenuService, Depends(get_digital_menu_service)],
):
    """
    Hard delete a digital menu and all related data (categories, items, variants, modifiers, optionals).
    This action is irreversible. Requires tenant owner or staff permissions.
    """
    try:
        deleted_menu = await menu_service.delete_digital_menu(
            menu_id=menu_id,
            tenant_id=current_tenant.id
        )

        return deleted_menu
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting digital menu {menu_id} for tenant {current_tenant.id}: {e}")
        raise


# Endpoint for reordering digital menus
@router.put("/reorder", response_model=List[DigitalMenuRead])
async def reorder_digital_menus(
    menu_orders: List[dict],  # [{"id": "uuid", "display_order": int}, ...]
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)],
    _: Annotated[None, Depends(require_tenant_role([TenantRole.OWNER, TenantRole.STAFF]))],
    menu_service: Annotated[DigitalMenuService, Depends(get_digital_menu_service)],
):
    """
    Reorder digital menus by updating their display_order.
    Requires tenant owner or staff permissions.
    """
    try:
        updated_menus = []

        for menu_order in menu_orders:
            menu_id = uuid.UUID(menu_order["id"])
            display_order = menu_order["display_order"]

            updated_menu = await menu_service.update_digital_menu(
                menu_id=menu_id,
                menu_in=DigitalMenuUpdate(display_order=display_order),
                tenant_id=current_tenant.id
            )

            if updated_menu:
                updated_menus.append(updated_menu)

        return updated_menus
    except Exception as e:
        logger.error(f"Error reordering digital menus for tenant {current_tenant.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error reordering digital menus"
        )
