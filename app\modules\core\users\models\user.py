from typing import List, TYPE_CHECKING, Optional
import uuid

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON>n, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, Mapped

from app.db.base import Base
from app.modules.core.roles.models.roles import SystemRole

# Use TYPE_CHECKING to handle circular dependencies for type hinting
if TYPE_CHECKING:
    from app.modules.core.i18n.models.translation import Translation
    from app.modules.core.i18n.models.translation_suggestion import TranslationSuggestion
    from app.modules.shared.email.models.email_account import EmailAccount
    from app.modules.shared.domain_rent.models.domain_registration import DomainRegistration
    from app.modules.core.functions.orders.models.order import Order
    from app.modules.core.functions.pos.models.sale_transaction import SaleTransaction
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.core.users.models.tenant_user_association import TenantUserAssociation
    from app.modules.shared.crm.models.account import Account
    from app.modules.core.eshop.models.product import Product


class User(Base):
    """
    User model representing individuals with system-level access.

    The User model has a system_role attribute that determines global permissions:
    - SystemRole.ADMIN: System administrator with full access
    - SystemRole.USER: Regular user with tenant-specific permissions

    User permissions within tenants are defined through TenantUserAssociation with roles:
    - "owner": Tenant owner with administrative access
    - "manager": Tenant manager with administrative access to specific areas
    - "staff": Tenant staff with operational permissions
    - "customer": Customer/client with limited access

    See app.modules.core.roles.models.roles for full role definitions and hierarchy.
    """

    __tablename__ = "users"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    full_name = Column(String, index=True, nullable=True)
    phone_number = Column(String, unique=True, index=True, nullable=True)
    is_active = Column(Boolean, default=True)
    is_subscriber = Column(Boolean, default=False, nullable=False)  # For blog access control
    system_role = Column(String, nullable=False, default=SystemRole.USER.value, index=True)

    # Relationship with the association table
    tenant_associations = relationship(
        "TenantUserAssociation",
        foreign_keys="[TenantUserAssociation.user_id]",
        back_populates="user",
        cascade="all, delete-orphan",
    )
    tenants = relationship(
        "Tenant",
        secondary="tenant_user_associations",
        primaryjoin="and_(User.id == TenantUserAssociation.user_id)",
        secondaryjoin="and_(Tenant.id == TenantUserAssociation.tenant_id)",
        viewonly=True,
    )



    # Relationship with orders
    orders: Mapped[list["Order"]] = relationship(
        "app.modules.core.functions.orders.models.order.Order",
        back_populates="user",
        cascade="all, delete-orphan",
    )

    # Data sharing consent (for customers)
    # Whether user consents to share data between tenants
    # TODO: Add migration to create this column
    # data_sharing_consent = Column(Boolean(), default=False)

    # Relationship with OnlineOrder - Commented out until the module is implemented
    # online_orders = relationship(
    #     "app.modules.tenants.restaurants.models.online_order.OnlineOrder",
    #     back_populates="user",
    #     cascade="all, delete-orphan",
    # )

    # Relationships for I18n
    updated_translations = relationship(
        "app.modules.core.i18n.models.translation.Translation",
        foreign_keys="app.modules.core.i18n.models.translation.Translation.last_updated_by_id",
        back_populates="last_updated_by",
    )
    translation_suggestions = relationship(
        "app.modules.core.i18n.models.translation_suggestion.TranslationSuggestion",
        back_populates="user",
    )

    # Relationship with EmailAccount - Commented out until the module is implemented
    # email_accounts = relationship(
    #     "app.modules.shared.email.models.email_account.EmailAccount",
    #     back_populates="user",
    #     cascade="all, delete-orphan",
    # )

    # CRM relationships
    accounts: Mapped[List["Account"]] = relationship(
        "Account", back_populates="user"
    )

    # POS relationships
    sale_transactions: Mapped[List["SaleTransaction"]] = relationship(
        "SaleTransaction", back_populates="customer"
    )

    # Relationship with DomainRegistration - Commented out until the module is implemented
    # domain_registrations = relationship(
    #     "app.modules.shared.domain_rent.models.domain_registration.DomainRegistration",
    #     back_populates="user",
    #     cascade="all, delete-orphan",
    # )

    # Relacionamento com FinancialTransaction (um para muitos)

    # Help Center relationships
    help_tickets = relationship(
        "app.modules.core.help_center.models.Ticket",
        foreign_keys="app.modules.core.help_center.models.Ticket.user_id",
        back_populates="user",
        cascade="all, delete-orphan"
    )

    # eshop relationships
    # eshop_products: Mapped[list["Product"]] = relationship(
    #     "app.modules.core.eshop.models.product.Product",
    #     back_populates="vendor",
    #     foreign_keys="[app.modules.core.eshop.models.product.Product.vendor_id]"
    # )
    # eshop_reviews: Reviews are now handled by the generic reviews system
    # Use app.modules.core.functions.reviews for all review functionality

    def __repr__(self):
        return f"<User {self.email}>"
