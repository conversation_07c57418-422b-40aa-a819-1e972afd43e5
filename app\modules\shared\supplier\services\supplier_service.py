import uuid
from typing import Optional, List, Sequence
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from sqlalchemy import and_, desc, func
from decimal import Decimal

from app.modules.shared.supplier.models.supplier import (
    Supplier,
    ProductSupplier,
    SupplierPriceHistory,
    SupplierStatus,
    PriorityLevel
)
from app.modules.shared.supplier.schemas.supplier import (
    SupplierCreate,
    SupplierUpdate,
    ProductSupplierCreate,
    ProductSupplierUpdate,
    SupplierPriceHistoryCreate,
    CompetitivePricingView,
    AutoReplenishmentSuggestion
)
from app.modules.core.functions.inventory.models.inventory_item import InventoryItem
from app.core.exceptions import NotFoundError


class SupplierService:
    """Serviço para gerenciamento de fornecedores."""

    async def get_suppliers(
        self, 
        db: AsyncSession, 
        *, 
        tenant_id: uuid.UUID,
        status: Optional[SupplierStatus] = None,
        skip: int = 0,
        limit: int = 100
    ) -> Sequence[Supplier]:
        """Lista fornecedores do tenant."""
        query = select(Supplier).where(Supplier.tenant_id == tenant_id)
        
        if status:
            query = query.where(Supplier.status == status)
            
        query = query.offset(skip).limit(limit).order_by(Supplier.name)
        result = await db.execute(query)
        return result.scalars().all()

    async def get_supplier_by_id(
        self, 
        db: AsyncSession, 
        *, 
        supplier_id: uuid.UUID, 
        tenant_id: uuid.UUID
    ) -> Optional[Supplier]:
        """Obtém fornecedor por ID."""
        result = await db.execute(
            select(Supplier).where(
                and_(
                    Supplier.id == supplier_id,
                    Supplier.tenant_id == tenant_id
                )
            )
        )
        return result.scalars().first()

    async def create_supplier(
        self, 
        db: AsyncSession, 
        *, 
        supplier_data: SupplierCreate, 
        tenant_id: uuid.UUID
    ) -> Supplier:
        """Cria novo fornecedor."""
        db_supplier = Supplier(
            tenant_id=tenant_id,
            **supplier_data.model_dump()
        )
        db.add(db_supplier)
        await db.commit()
        await db.refresh(db_supplier)
        return db_supplier

    async def update_supplier(
        self, 
        db: AsyncSession, 
        *, 
        supplier_id: uuid.UUID, 
        supplier_data: SupplierUpdate, 
        tenant_id: uuid.UUID
    ) -> Supplier:
        """Atualiza fornecedor."""
        db_supplier = await self.get_supplier_by_id(
            db=db, supplier_id=supplier_id, tenant_id=tenant_id
        )
        if not db_supplier:
            raise NotFoundError(f"Supplier with id {supplier_id} not found")

        update_data = supplier_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_supplier, field, value)

        await db.commit()
        await db.refresh(db_supplier)
        return db_supplier

    async def delete_supplier(
        self, 
        db: AsyncSession, 
        *, 
        supplier_id: uuid.UUID, 
        tenant_id: uuid.UUID
    ) -> Supplier:
        """Exclui fornecedor (soft delete)."""
        db_supplier = await self.get_supplier_by_id(
            db=db, supplier_id=supplier_id, tenant_id=tenant_id
        )
        if not db_supplier:
            raise NotFoundError(f"Supplier with id {supplier_id} not found")

        db_supplier.status = SupplierStatus.INACTIVE
        await db.commit()
        await db.refresh(db_supplier)
        return db_supplier


class ProductSupplierService:
    """Serviço para associações produto-fornecedor."""

    async def get_product_suppliers(
        self, 
        db: AsyncSession, 
        *, 
        tenant_id: uuid.UUID,
        inventory_item_id: Optional[uuid.UUID] = None,
        supplier_id: Optional[uuid.UUID] = None,
        active_only: bool = True
    ) -> Sequence[ProductSupplier]:
        """Lista associações produto-fornecedor."""
        query = select(ProductSupplier).where(ProductSupplier.tenant_id == tenant_id)
        
        if inventory_item_id:
            query = query.where(ProductSupplier.inventory_item_id == inventory_item_id)
        if supplier_id:
            query = query.where(ProductSupplier.supplier_id == supplier_id)
        if active_only:
            query = query.where(ProductSupplier.is_active == True)
            
        query = query.order_by(
            ProductSupplier.priority_level,
            ProductSupplier.priority_order
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def create_product_supplier(
        self, 
        db: AsyncSession, 
        *, 
        product_supplier_data: ProductSupplierCreate, 
        tenant_id: uuid.UUID
    ) -> ProductSupplier:
        """Cria associação produto-fornecedor."""
        db_product_supplier = ProductSupplier(
            tenant_id=tenant_id,
            **product_supplier_data.model_dump()
        )
        db.add(db_product_supplier)
        await db.commit()
        await db.refresh(db_product_supplier)
        return db_product_supplier

    async def get_competitive_pricing(
        self, 
        db: AsyncSession, 
        *, 
        inventory_item_id: uuid.UUID, 
        tenant_id: uuid.UUID
    ) -> CompetitivePricingView:
        """Obtém visão de preços competitivos para um produto."""
        result = await db.execute(
            select(ProductSupplier, Supplier, InventoryItem)
            .join(Supplier, ProductSupplier.supplier_id == Supplier.id)
            .join(InventoryItem, ProductSupplier.inventory_item_id == InventoryItem.id)
            .where(
                and_(
                    ProductSupplier.tenant_id == tenant_id,
                    ProductSupplier.inventory_item_id == inventory_item_id,
                    ProductSupplier.is_active == True,
                    Supplier.status == SupplierStatus.ACTIVE,
                    ProductSupplier.current_price.isnot(None)
                )
            )
            .order_by(ProductSupplier.current_price)
        )
        
        rows = result.all()
        if not rows:
            raise NotFoundError(f"No suppliers found for inventory item {inventory_item_id}")

        suppliers_data = []
        prices = []
        
        for product_supplier, supplier, inventory_item in rows:
            suppliers_data.append({
                "supplier_id": str(supplier.id),
                "supplier_name": supplier.name,
                "price": float(product_supplier.current_price),
                "priority_level": product_supplier.priority_level,
                "lead_time_days": product_supplier.lead_time_days,
                "minimum_order_quantity": product_supplier.minimum_order_quantity
            })
            prices.append(product_supplier.current_price)

        lowest_price = min(prices) if prices else None
        highest_price = max(prices) if prices else None
        price_range_percentage = None
        
        if lowest_price and highest_price and lowest_price > 0:
            price_range_percentage = ((highest_price - lowest_price) / lowest_price) * 100

        return CompetitivePricingView(
            inventory_item_id=inventory_item_id,
            inventory_item_name=rows[0][2].name,  # inventory_item.name
            suppliers=suppliers_data,
            lowest_price=lowest_price,
            highest_price=highest_price,
            price_range_percentage=price_range_percentage
        )

    async def get_auto_replenishment_suggestions(
        self, 
        db: AsyncSession, 
        *, 
        tenant_id: uuid.UUID,
        low_stock_threshold: int = 10
    ) -> List[AutoReplenishmentSuggestion]:
        """Obtém sugestões de reposição automática."""
        result = await db.execute(
            select(InventoryItem, ProductSupplier, Supplier)
            .join(ProductSupplier, InventoryItem.id == ProductSupplier.inventory_item_id)
            .join(Supplier, ProductSupplier.supplier_id == Supplier.id)
            .where(
                and_(
                    InventoryItem.tenant_id == tenant_id,
                    InventoryItem.quantity <= low_stock_threshold,
                    ProductSupplier.is_active == True,
                    ProductSupplier.auto_replenishment_authorized == True,
                    Supplier.status == SupplierStatus.ACTIVE,
                    ProductSupplier.current_price.isnot(None)
                )
            )
            .order_by(
                InventoryItem.quantity,
                ProductSupplier.priority_level,
                ProductSupplier.priority_order,
                ProductSupplier.current_price
            )
        )
        
        suggestions = []
        processed_items = set()
        
        for inventory_item, product_supplier, supplier in result.all():
            # Only one suggestion per inventory item (best supplier)
            if inventory_item.id in processed_items:
                continue
                
            processed_items.add(inventory_item.id)
            
            # Calculate suggested quantity (double current stock or minimum order)
            suggested_quantity = max(
                inventory_item.quantity * 2,
                product_supplier.minimum_order_quantity or 1
            )
            
            suggestions.append(AutoReplenishmentSuggestion(
                inventory_item_id=inventory_item.id,
                inventory_item_name=inventory_item.name,
                current_stock=inventory_item.quantity,
                suggested_quantity=suggested_quantity,
                recommended_supplier_id=supplier.id,
                recommended_supplier_name=supplier.name,
                estimated_cost=product_supplier.current_price * suggested_quantity,
                lead_time_days=product_supplier.lead_time_days or 1
            ))
        
        return suggestions


# Service instances
supplier_service = SupplierService()
product_supplier_service = ProductSupplierService()
