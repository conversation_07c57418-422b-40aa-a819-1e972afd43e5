"""Financial Control Category Schemas."""

from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, Field
from uuid import UUID


class ControlCategoryBase(BaseModel):
    """Base schema for control categories."""
    
    name: str = Field(..., min_length=1, max_length=100, description="Category name")
    description: Optional[str] = Field(None, description="Category description")
    code: Optional[str] = Field(None, max_length=20, description="Short code")
    color: Optional[str] = Field(None, max_length=7, description="Hex color")
    icon: Optional[str] = Field(None, max_length=50, description="Icon name")
    
    parent_id: Optional[UUID] = Field(None, description="Parent category ID")
    sort_order: int = Field(0, description="Sort order")
    
    is_active: bool = Field(True, description="Is category active")
    is_tax_category: bool = Field(False, description="Is tax category")
    
    monthly_budget_limit: Optional[str] = Field(None, description="Monthly budget limit (JSON)")
    annual_budget_limit: Optional[str] = Field(None, description="Annual budget limit (JSON)")
    alert_threshold: Optional[int] = Field(None, ge=0, le=100, description="Alert threshold percentage")


class ControlCategoryCreate(ControlCategoryBase):
    """Schema for creating control categories."""
    pass


class ControlCategoryUpdate(BaseModel):
    """Schema for updating control categories."""
    
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    code: Optional[str] = Field(None, max_length=20)
    color: Optional[str] = Field(None, max_length=7)
    icon: Optional[str] = Field(None, max_length=50)
    
    parent_id: Optional[UUID] = None
    sort_order: Optional[int] = None
    
    is_active: Optional[bool] = None
    is_tax_category: Optional[bool] = None
    
    monthly_budget_limit: Optional[str] = None
    annual_budget_limit: Optional[str] = None
    alert_threshold: Optional[int] = Field(None, ge=0, le=100)


class ControlCategoryResponse(ControlCategoryBase):
    """Schema for control category responses."""
    
    id: UUID
    tenant_id: UUID
    level: int
    is_system: bool
    full_path: str
    has_children: bool
    
    created_by: UUID
    updated_by: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime
    
    # Related data
    entries_count: int = 0
    total_amount: float = 0.0
    
    class Config:
        from_attributes = True


class ControlCategoryList(BaseModel):
    """Schema for category lists."""
    
    items: List[ControlCategoryResponse]
    total: int
