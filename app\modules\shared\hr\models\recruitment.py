import uuid
import enum
from datetime import datetime
from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    String,
    <PERSON><PERSON><PERSON>,
    <PERSON>ole<PERSON>,
    DateTime,
    Integer,
    Enum,
    Text,
    Float,
)
from sqlalchemy.dialects.postgresql import UUID, JSONB, ARRAY  # noqa: E402
from sqlalchemy.orm import relationship

from app.db.base import Base  # noqa: E402
from app.core.tenant_mixin import TenantMixin


class JobStatus(str, enum.Enum):
    """Job status enum."""

    DRAFT = "draft"
    OPEN = "open"
    CLOSED = "closed"
    ON_HOLD = "on_hold"
    FILLED = "filled"


class JobType(str, enum.Enum):
    """Job type enum."""

    FULL_TIME = "full_time"
    PART_TIME = "part_time"
    CONTRACT = "contract"
    TEMPORARY = "temporary"
    INTERNSHIP = "internship"
    VOLUNTEER = "volunteer"


class CandidateStatus(str, enum.Enum):
    """Candidate status enum."""

    NEW = "new"
    SCREENING = "screening"
    INTERVIEW = "interview"
    ASSESSMENT = "assessment"
    REFERENCE_CHECK = "reference_check"
    OFFER = "offer"
    HIRED = "hired"
    REJECTED = "rejected"
    WITHDRAWN = "withdrawn"


class InterviewStatus(str, enum.Enum):
    """Interview status enum."""

    SCHEDULED = "scheduled"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    NO_SHOW = "no_show"
    RESCHEDULED = "rescheduled"


class AssessmentType(str, enum.Enum):
    """Assessment type enum."""

    TECHNICAL = "technical"
    PERSONALITY = "personality"
    SKILLS = "skills"
    COGNITIVE = "cognitive"
    LANGUAGE = "language"
    CULTURE_FIT = "culture_fit"
    OTHER = "other"


class Job(Base, TenantMixin):
    """Job model for recruitment.

    This represents a job opening.
    """

    __tablename__ = "hr_jobs"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Job details
    title = Column(String, nullable=False)
    description = Column(Text, nullable=False)
    department = Column(String, nullable=True)
    location = Column(String, nullable=True)
    job_type = Column(Enum(JobType), nullable=False)
    status = Column(Enum(JobStatus), nullable=False, default=JobStatus.DRAFT)

    # Salary information
    salary_min = Column(Float, nullable=True)
    salary_max = Column(Float, nullable=True)
    salary_currency = Column(String, nullable=True)
    salary_is_public = Column(Boolean, default=False)

    # Dates
    posting_date = Column(DateTime, nullable=True)
    closing_date = Column(DateTime, nullable=True)

    # Requirements
    required_skills = Column(ARRAY(String), nullable=True)
    required_experience = Column(String, nullable=True)
    required_education = Column(String, nullable=True)

    # Additional information
    benefits = Column(Text, nullable=True)
    remote_allowed = Column(Boolean, default=False)
    number_of_openings = Column(Integer, default=1)

    # Internal tracking
    hiring_manager_id = Column(UUID(as_uuid=True), ForeignKey("hr_employees.id"), nullable=True)
    recruiter_id = Column(UUID(as_uuid=True), ForeignKey("hr_employees.id"), nullable=True)
    internal_notes = Column(Text, nullable=True)

    # Metadata
    metadata = Column(JSONB, default={})

    # Relationships
    hiring_manager = relationship("Employee", foreign_keys=[hiring_manager_id])
    recruiter = relationship("Employee", foreign_keys=[recruiter_id])
    candidates = relationship("Candidate", back_populates="job", cascade="all, delete-orphan")


class Candidate(Base, TenantMixin):
    """Candidate model for recruitment.

    This represents a job candidate.
    """

    __tablename__ = "hr_candidates"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    job_id = Column(UUID(as_uuid=True), ForeignKey("hr_jobs.id"), nullable=False)

    # Basic information
    first_name = Column(String, nullable=False)
    last_name = Column(String, nullable=False)
    email = Column(String, nullable=False)
    phone = Column(String, nullable=True)

    # Resume and cover letter
    resume_path = Column(String, nullable=True)
    cover_letter_path = Column(String, nullable=True)

    # Status and tracking
    status = Column(Enum(CandidateStatus), nullable=False, default=CandidateStatus.NEW)
    application_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    # Where the candidate came from (e.g., job board, referral)
    source = Column(String, nullable=True)

    # Evaluation
    rating = Column(Integer, nullable=True)  # 1-5 rating
    notes = Column(Text, nullable=True)

    # Metadata
    metadata = Column(JSONB, default={})

    # Relationships
    job = relationship("Job", back_populates="candidates")
    interviews = relationship("Interview", back_populates="candidate", cascade="all, delete-orphan")
    assessments = relationship(
        "Assessment", back_populates="candidate", cascade="all, delete-orphan"
    )


class Interview(Base, TenantMixin):
    """Interview model for recruitment.

    This represents an interview with a candidate.
    """

    __tablename__ = "hr_interviews"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    candidate_id = Column(UUID(as_uuid=True), ForeignKey("hr_candidates.id"), nullable=False)

    # Interview details
    title = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    interview_type = Column(String, nullable=False)  # e.g., phone, video, in-person

    # Scheduling
    scheduled_start = Column(DateTime, nullable=False)
    scheduled_end = Column(DateTime, nullable=False)
    location = Column(String, nullable=True)

    # Status
    status = Column(Enum(InterviewStatus), nullable=False, default=InterviewStatus.SCHEDULED)

    # Interviewers
    interviewers = Column(ARRAY(UUID(as_uuid=True)), nullable=True)

    # Feedback
    feedback = Column(Text, nullable=True)
    rating = Column(Integer, nullable=True)  # 1-5 rating

    # Metadata
    metadata = Column(JSONB, default={})

    # Relationships
    candidate = relationship("Candidate", back_populates="interviews")


class Assessment(Base, TenantMixin):
    """Assessment model for recruitment.

    This represents an assessment given to a candidate.
    """

    __tablename__ = "hr_assessments"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    candidate_id = Column(UUID(as_uuid=True), ForeignKey("hr_candidates.id"), nullable=False)

    # Assessment details
    title = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    assessment_type = Column(Enum(AssessmentType), nullable=False)

    # Scheduling
    assigned_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    due_date = Column(DateTime, nullable=True)
    completed_date = Column(DateTime, nullable=True)

    # Status
    is_completed = Column(Boolean, default=False)

    # Results
    score = Column(Float, nullable=True)
    max_score = Column(Float, nullable=True)
    passed = Column(Boolean, nullable=True)
    feedback = Column(Text, nullable=True)

    # Metadata
    metadata = Column(JSONB, default={})

    # Relationships
    candidate = relationship("Candidate", back_populates="assessments")
