# Blog - Tags

**Categoria:** Blog
**Módulo:** Tags
**Total de Endpoints:** 4
**Gerado em:** 08/06/2025, 17:26:01

## 📋 Endpoints

- [GET /api/modules/core/blog/tags/](#get-apimodulescoreblogtags) - Get Tags
- [POST /api/modules/core/blog/tags/](#post-apimodulescoreblogtags) - Create Tag
- [GET /api/modules/core/blog/tags/cloud](#get-apimodulescoreblogtagscloud) - Get Tag Cloud
- [GET /api/modules/core/blog/tags/{tag_id}](#get-apimodulescoreblogtagstag-id) - Get Tag

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### BlogTagCreate

**Descrição:** Schema for creating blog tags.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `slug` | string | ✅ | - |
| `color` | unknown | ❌ | - |
| `translations` | Array[BlogTagTranslationCreate] | ✅ | - |

### BlogTagRead

**Descrição:** Schema for reading blog tags.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `slug` | string | ✅ | - |
| `color` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `translations` | Array[BlogTagTranslationRead] | ❌ | - |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/modules/core/blog/tags/ {#get-apimodulescoreblogtags}

**Resumo:** Get Tags
**Descrição:** Get blog tags with filtering and pagination.

Results include post counts for each tag.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | Number of tags to skip |
| `limit` | integer | query | ❌ | Number of tags to return |
| `language` | string | query | ❌ | Language code |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/tags/"
```

---

### POST /api/modules/core/blog/tags/ {#post-apimodulescoreblogtags}

**Resumo:** Create Tag
**Descrição:** Create a new blog tag.

Requires authentication.
Must include at least one translation.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [BlogTagCreate](#blogtagcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogTagRead](#blogtagread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/blog/tags/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/blog/tags/cloud {#get-apimodulescoreblogtagscloud}

**Resumo:** Get Tag Cloud
**Descrição:** Get tag cloud data.

Returns tags with calculated weights for tag cloud display.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `limit` | integer | query | ❌ | Number of tags to return |
| `language` | string | query | ❌ | Language code |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/tags/cloud"
```

---

### GET /api/modules/core/blog/tags/{tag_id} {#get-apimodulescoreblogtagstag-id}

**Resumo:** Get Tag
**Descrição:** Get a specific blog tag by ID.

Optionally filter translations to a specific language.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tag_id` | string | path | ✅ | - |
| `language` | string | query | ❌ | Language code |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogTagRead](#blogtagread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/tags/{tag_id}"
```

---
