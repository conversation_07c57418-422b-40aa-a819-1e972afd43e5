# Restaurant - Menu Management

**Categoria:** Restaurant
**Módulo:** Menu Management
**Total de Endpoints:** 12
**Gerado em:** 27/06/2025, 20:20:27

## 📋 Endpoints

- [GET /api/modules/restaurants/menu/categories/](#get-apimodulesrestaurantsmenucategories) - Read Menu Categories
- [POST /api/modules/restaurants/menu/categories/](#post-apimodulesrestaurantsmenucategories) - Create Menu Category
- [PUT /api/modules/restaurants/menu/categories/reorder](#put-apimodulesrestaurantsmenucategoriesreorder) - Reorder Categories
- [DELETE /api/modules/restaurants/menu/categories/{category_id}](#delete-apimodulesrestaurantsmenucategoriescategory-id) - Delete Menu Category
- [GET /api/modules/restaurants/menu/categories/{category_id}](#get-apimodulesrestaurantsmenucategoriescategory-id) - Read Menu Category
- [PUT /api/modules/restaurants/menu/categories/{category_id}](#put-apimodulesrestaurantsmenucategoriescategory-id) - Update Menu Category
- [GET /api/modules/restaurants/menu/items/](#get-apimodulesrestaurantsmenuitems) - Read Menu Items
- [POST /api/modules/restaurants/menu/items/](#post-apimodulesrestaurantsmenuitems) - Create Menu Item
- [PUT /api/modules/restaurants/menu/items/reorder](#put-apimodulesrestaurantsmenuitemsreorder) - Reorder Items
- [DELETE /api/modules/restaurants/menu/items/{item_id}](#delete-apimodulesrestaurantsmenuitemsitem-id) - Delete Menu Item
- [GET /api/modules/restaurants/menu/items/{item_id}](#get-apimodulesrestaurantsmenuitemsitem-id) - Read Menu Item
- [PUT /api/modules/restaurants/menu/items/{item_id}](#put-apimodulesrestaurantsmenuitemsitem-id) - Update Menu Item

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### MenuCategoryCreate

**Descrição:** Schema for creating a new Menu Category.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Name of the menu category (e.g., Appetizers) |
| `description` | unknown | ❌ | Optional description for the category |
| `display_order` | integer | ❌ | Order in which the category should be displayed |
| `is_active` | boolean | ❌ | Whether the category is currently active |
| `is_default` | unknown | ❌ | Whether this is the default 'Sem Categoria' category |
| `parent_id` | unknown | ❌ | ID of the parent category, if this is a subcategory |
| `digital_menu_id` | unknown | ❌ | ID of the digital menu this category belongs to |

### MenuCategoryRead

**Descrição:** Schema for reading a Menu Category, includes the ID and optional relationships.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Name of the menu category (e.g., Appetizers) |
| `description` | unknown | ❌ | Optional description for the category |
| `display_order` | integer | ❌ | Order in which the category should be displayed |
| `is_active` | boolean | ❌ | Whether the category is currently active |
| `is_default` | unknown | ❌ | Whether this is the default 'Sem Categoria' category |
| `parent_id` | unknown | ❌ | ID of the parent category, if this is a subcategory |
| `digital_menu_id` | unknown | ❌ | ID of the digital menu this category belongs to |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `menu_items` | unknown | ❌ | - |
| `children` | unknown | ❌ | - |

### MenuCategoryReadSimple

**Descrição:** Simplified schema for Menu Category without children to avoid lazy loading issues.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `id` | string | ✅ | - |
| `name` | string | ✅ | - |
| `description` | unknown | ❌ | - |
| `display_order` | integer | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `is_default` | unknown | ❌ | - |
| `parent_id` | unknown | ❌ | - |
| `digital_menu_id` | unknown | ❌ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### MenuCategoryUpdate

**Descrição:** Schema for updating an existing Menu Category. All fields are optional.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `display_order` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `is_default` | unknown | ❌ | - |
| `parent_id` | unknown | ❌ | ID of the parent category to move this category under. Use null to make it a top-level category. |
| `digital_menu_id` | unknown | ❌ | ID of the digital menu to assign this category to |

### MenuItemCreate

**Descrição:** Schema for creating a new Menu Item, including category and potentially nested groups.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Name of the menu item (e.g., Classic Burger) |
| `description` | unknown | ❌ | Detailed description of the item |
| `base_price` | unknown | ✅ | Base price of the item before variants/modifiers |
| `image_url` | unknown | ❌ | URL for the item's primary image |
| `allergen_ids` | Array[string] | ❌ | List of allergen IDs associated with this item |
| `is_available` | boolean | ❌ | Is the item currently available for ordering? |
| `is_active` | boolean | ❌ | Is the item active in the system (for soft delete)? |
| `is_combo` | boolean | ❌ | Is this item a combo meal? |
| `discount_percentage` | unknown | ❌ | Optional discount percentage (e.g., 10.5 for 10.5%) |
| `display_order` | integer | ❌ | Order within the menu category |
| `category_id` | string | ✅ | ID of the category this item belongs to |
| `variant_groups` | Array[VariantGroupCreate] | ❌ | Variant groups (e.g., Size) and their options |
| `modifier_groups` | Array[ModifierGroupCreate] | ❌ | Modifier groups (e.g., Add-ons) and their options |
| `optional_groups` | Array[OptionalGroupCreate] | ❌ | Optional groups (e.g., Sides, Drinks) and their options |

### MenuItemUpdate

**Descrição:** Schema for updating an existing Menu Item. All fields optional.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `base_price` | unknown | ❌ | - |
| `image_url` | unknown | ❌ | - |
| `allergen_ids` | unknown | ❌ | - |
| `is_available` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `is_combo` | unknown | ❌ | - |
| `discount_percentage` | unknown | ❌ | - |
| `display_order` | unknown | ❌ | - |
| `category_id` | unknown | ❌ | - |
| `allergens` | unknown | ❌ | - |
| `variant_groups` | unknown | ❌ | Variant groups to replace existing ones |
| `modifier_groups` | unknown | ❌ | Modifier groups to replace existing ones |
| `optional_groups` | unknown | ❌ | Optional groups to replace existing ones |

## 🔗 Endpoints Detalhados

### GET /api/modules/restaurants/menu/categories/ {#get-apimodulesrestaurantsmenucategories}

**Resumo:** Read Menu Categories
**Descrição:** Retrieve active menu categories for the current tenant.

- Use `digital_menu_id` to filter categories by digital menu
- Use `parent_id` to filter categories by their parent
- Use `only_top_level=true` to get only top-level categories (no parent)
- Use `include_children=false` to exclude children from the response

Requires at least costumer role in the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `digital_menu_id` | string | query | ❌ | Filter by digital menu ID |
| `parent_id` | string | query | ❌ | Filter categories by parent ID |
| `only_top_level` | boolean | query | ❌ | Only return top-level categories (no parent) |
| `include_children` | boolean | query | ❌ | Include children in the response |
| `include_items` | boolean | query | ❌ | Include menu items in the response |
| `skip` | integer | query | ❌ | Number of records to skip |
| `limit` | integer | query | ❌ | Maximum number of records to return |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/menu/categories/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/restaurants/menu/categories/ {#post-apimodulesrestaurantsmenucategories}

**Resumo:** Create Menu Category
**Descrição:** Create a new menu category for the current tenant.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [MenuCategoryCreate](#menucategorycreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [MenuCategoryReadSimple](#menucategoryreadsimple)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/restaurants/menu/categories/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/modules/restaurants/menu/categories/reorder {#put-apimodulesrestaurantsmenucategoriesreorder}

**Resumo:** Reorder Categories
**Descrição:** Reorder menu categories by updating their display_order.
Expects a list of objects with 'id' and 'display_order' fields.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** {'type': 'array', 'items': {'type': 'object', 'additionalProperties': True}, 'title': 'Category Orders'}

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/menu/categories/reorder" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/restaurants/menu/categories/{category_id} {#delete-apimodulesrestaurantsmenucategoriescategory-id}

**Resumo:** Delete Menu Category
**Descrição:** Deactivate (soft delete) a menu category by ID for the current tenant.
This will also deactivate all child categories recursively.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | The ID of the category to deactivate |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/restaurants/menu/categories/{category_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/menu/categories/{category_id} {#get-apimodulesrestaurantsmenucategoriescategory-id}

**Resumo:** Read Menu Category
**Descrição:** Retrieve a specific active menu category by ID for the current tenant.
Requires at least costumer role in the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | The ID of the category to retrieve |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [MenuCategoryRead](#menucategoryread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/menu/categories/{category_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/restaurants/menu/categories/{category_id} {#put-apimodulesrestaurantsmenucategoriescategory-id}

**Resumo:** Update Menu Category
**Descrição:** Update a specific menu category by ID for the current tenant.

- Can update name, description, display_order, is_active
- Can change parent_id to move the category in the hierarchy
- Cannot create cycles in the category hierarchy

Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | The ID of the category to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [MenuCategoryUpdate](#menucategoryupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [MenuCategoryRead](#menucategoryread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/menu/categories/{category_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/restaurants/menu/items/ {#get-apimodulesrestaurantsmenuitems}

**Resumo:** Read Menu Items
**Descrição:** Retrieve menu items for the current tenant, optionally filtered by digital menu and/or category.
By default, returns only active items; set 'include_inactive=true' to include soft deleted items.
By default, returns a simplified list; set 'include_details=true' for full data.
Requires at least costumer role in the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `digital_menu_id` | string | query | ❌ | Filter items by digital menu ID |
| `category_id` | string | query | ❌ | Filter items by category ID |
| `include_details` | boolean | query | ❌ | Include full details like variants and modifiers in the list |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/menu/items/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/restaurants/menu/items/ {#post-apimodulesrestaurantsmenuitems}

**Resumo:** Create Menu Item
**Descrição:** Create a new menu item for the current tenant.
Supports creating nested variant and modifier groups/options.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [MenuItemCreate](#menuitemcreate)

**📥 Respostas:**

**201:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/restaurants/menu/items/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/modules/restaurants/menu/items/reorder {#put-apimodulesrestaurantsmenuitemsreorder}

**Resumo:** Reorder Items
**Descrição:** Reorder menu items by updating their display_order.
Expects a list of objects with 'id' and 'display_order' fields.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** {'type': 'array', 'items': {'type': 'object', 'additionalProperties': True}, 'title': 'Item Orders'}

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/menu/items/reorder" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/restaurants/menu/items/{item_id} {#delete-apimodulesrestaurantsmenuitemsitem-id}

**Resumo:** Delete Menu Item
**Descrição:** Permanently delete a menu item by ID for the current tenant.
WARNING: This action cannot be undone. The item will be completely removed from the database.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `item_id` | string | path | ✅ | The ID of the item to permanently delete |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/restaurants/menu/items/{item_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/menu/items/{item_id} {#get-apimodulesrestaurantsmenuitemsitem-id}

**Resumo:** Read Menu Item
**Descrição:** Retrieve a specific active menu item by ID, including its category, variants, and modifiers.
Requires at least costumer role in the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `item_id` | string | path | ✅ | The ID of the item to retrieve |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/menu/items/{item_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/restaurants/menu/items/{item_id} {#put-apimodulesrestaurantsmenuitemsitem-id}

**Resumo:** Update Menu Item
**Descrição:** Update a specific menu item by ID for the current tenant.
Note: This currently updates only the base fields of the item.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `item_id` | string | path | ✅ | The ID of the item to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [MenuItemUpdate](#menuitemupdate)

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/menu/items/{item_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
