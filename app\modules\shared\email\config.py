"""Configuration for Email module."""

import os  # noqa: E402
from typing import Dict, Any

# SMTP configuration
SMTP_HOST = os.getenv("SMTP_HOST", "localhost")
SMTP_PORT = int(os.getenv("SMTP_PORT", "587"))
SMTP_USE_TLS = os.getenv("SMTP_USE_TLS", "True").lower() in ("true", "1", "t", "yes")
SMTP_USERNAME = os.getenv("SMTP_USERNAME", "")
SMTP_PASSWORD = os.getenv("SMTP_PASSWORD", "")

# IMAP configuration
IMAP_HOST = os.getenv("IMAP_HOST", "localhost")
IMAP_PORT = int(os.getenv("IMAP_PORT", "143"))
IMAP_USE_SSL = os.getenv("IMAP_USE_SSL", "False").lower() in ("true", "1", "t", "yes")

# Maildir configuration
MAILDIR_BASE_PATH = os.getenv("MAILDIR_BASE_PATH", "/var/vmail")

# Quota configuration
DEFAULT_QUOTA_MB = int(os.getenv("DEFAULT_QUOTA_MB", "1024"))  # 1GB

# DKIM configuration
DKIM_SELECTOR = os.getenv("DKIM_SELECTOR", "mail")
DKIM_PRIVATE_KEY_PATH = os.getenv("DKIM_PRIVATE_KEY_PATH", "/etc/opendkim/keys")

# DNS configuration
DEFAULT_MX_RECORDS = [
    os.getenv("PRIMARY_MX_RECORD", "10 mail.{domain}"),
    os.getenv("SECONDARY_MX_RECORD", "20 mail2.{domain}"),
]
DEFAULT_SPF_RECORD = os.getenv("DEFAULT_SPF_RECORD", "v=spf1 mx a:{domain} ~all")
DEFAULT_DMARC_RECORD = os.getenv(
    "DEFAULT_DMARC_RECORD", "v=DMARC1; p=none; sp=none; rua=mailto:dmarc@{domain}"
)

# Email server configuration
EMAIL_SERVER_HOSTNAME = os.getenv("EMAIL_SERVER_HOSTNAME", "mail.example.com")


def get_config() -> Dict[str, Any]:
    """Get the configuration as a dictionary."""
    return {
        "smtp": {
            "host": SMTP_HOST,
            "port": SMTP_PORT,
            "use_tls": SMTP_USE_TLS,
            "username": SMTP_USERNAME,
            "password": SMTP_PASSWORD,
        },
        "imap": {
            "host": IMAP_HOST,
            "port": IMAP_PORT,
            "use_ssl": IMAP_USE_SSL,
        },
        "maildir": {
            "base_path": MAILDIR_BASE_PATH,
        },
        "quota": {
            "default_quota_mb": DEFAULT_QUOTA_MB,
        },
        "dkim": {
            "selector": DKIM_SELECTOR,
            "private_key_path": DKIM_PRIVATE_KEY_PATH,
        },
        "dns": {
            "mx_records": DEFAULT_MX_RECORDS,
            "spf_record": DEFAULT_SPF_RECORD,
            "dmarc_record": DEFAULT_DMARC_RECORD,
        },
        "server": {
            "hostname": EMAIL_SERVER_HOSTNAME,
        },
    }
