"""Service layer for financial transactions."""

from typing import List, Optional
from uuid import UUID
from sqlalchemy.orm import Session, joinedload
from sqlalchemy.future import select

from app.modules.shared.financial.transactions.models.transaction import FinancialTransaction
from app.modules.shared.financial.transactions.schemas.transaction import (
    FinancialTransactionCreate,
    FinancialTransactionUpdate,
)
from app.modules.core.functions.media_system.models import MediaUpload


class FinancialTransactionService:
    def __init__(self, db: Session):
        self.db = db

    async def get_transaction_by_id(self, transaction_id: UUID, tenant_id: UUID) -> Optional[FinancialTransaction]:
        stmt = (
            select(FinancialTransaction)
            .where(FinancialTransaction.id == transaction_id, FinancialTransaction.tenant_id == tenant_id)
            .options(joinedload(FinancialTransaction.media_uploads))
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    async def get_all_transactions(self, tenant_id: UUID, skip: int = 0, limit: int = 100) -> List[FinancialTransaction]:
        stmt = (
            select(FinancialTransaction)
            .where(FinancialTransaction.tenant_id == tenant_id)
            .offset(skip)
            .limit(limit)
            .options(joinedload(FinancialTransaction.media_uploads))
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def create_transaction(
        self,
        transaction_data: FinancialTransactionCreate,
        tenant_id: UUID,
        user_id: UUID,
    ) -> FinancialTransaction:
        db_transaction = FinancialTransaction(
            **transaction_data.dict(exclude={"media_upload_ids"}),
            tenant_id=tenant_id,
            created_by=user_id
        )

        if transaction_data.media_upload_ids:
            media_uploads = await self.db.execute(
                select(MediaUpload).where(MediaUpload.id.in_(transaction_data.media_upload_ids))
            )
            db_transaction.media_uploads = media_uploads.scalars().all()

        self.db.add(db_transaction)
        await self.db.commit()
        await self.db.refresh(db_transaction)
        return db_transaction

    async def update_transaction(
        self,
        transaction_id: UUID,
        transaction_data: FinancialTransactionUpdate,
        tenant_id: UUID,
    ) -> Optional[FinancialTransaction]:
        db_transaction = await self.get_transaction_by_id(transaction_id, tenant_id)
        if not db_transaction:
            return None

        for key, value in transaction_data.dict(exclude_unset=True, exclude={"media_upload_ids"}).items():
            setattr(db_transaction, key, value)

        if transaction_data.media_upload_ids is not None:
            media_uploads = await self.db.execute(
                select(MediaUpload).where(MediaUpload.id.in_(transaction_data.media_upload_ids))
            )
            db_transaction.media_uploads = media_uploads.scalars().all()

        await self.db.commit()
        await self.db.refresh(db_transaction)
        return db_transaction

    async def delete_transaction(self, transaction_id: UUID, tenant_id: UUID) -> bool:
        db_transaction = await self.get_transaction_by_id(transaction_id, tenant_id)
        if not db_transaction:
            return False
        
        await self.db.delete(db_transaction)
        await self.db.commit()
        return True
