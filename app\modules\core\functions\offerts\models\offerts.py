import uuid
from sqlalchemy import Column, String, Float, <PERSON>olean, Foreign<PERSON>ey, DateTime, Enum, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.db.base import Base
import enum
from sqlalchemy.sql import func

class DiscountType(enum.Enum):
    PERCENTAGE = "percentage"
    FIXED_AMOUNT = "fixed_amount"

class DiscountCoupon(Base):
    __tablename__ = "discount_coupons"

    id = Column(Integer, primary_key=True, index=True)
    code = Column(String(50), nullable=False, unique=True, index=True)
    description = Column(String(255), nullable=True)
    discount_type = Column(Enum(DiscountType), nullable=False)
    value = Column(Float, nullable=False)  # Percentage (e.g., 10 for 10%) or fixed amount
    max_uses = Column(Integer, nullable=True) # Max number of times this coupon can be used in total
    uses_count = Column(Integer, default=0) # How many times this coupon has been used
    max_uses_per_user = Column(Integer, nullable=True) # Max times a single user can use this coupon
    valid_from = Column(DateTime(timezone=True), server_default=func.now())
    valid_until = Column(DateTime(timezone=True), nullable=True)
    is_active = Column(Boolean, default=True)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True) # Optional: coupon can be global or tenant-specific

    # Relationship to track which orders used this coupon
    applied_to_orders = relationship("AppliedCoupon", back_populates="coupon")

class AppliedCoupon(Base):
    __tablename__ = "applied_coupons"

    id = Column(Integer, primary_key=True, index=True)
    coupon_id = Column(Integer, ForeignKey("discount_coupons.id"), nullable=False)
    order_id = Column(UUID(as_uuid=True), ForeignKey("orders.id"), nullable=False) # Assuming orders.id is UUID
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False) # Assuming users.id is UUID
    applied_at = Column(DateTime(timezone=True), server_default=func.now())
    discount_amount = Column(Float, nullable=False) # The actual amount discounted for this order

    coupon = relationship("DiscountCoupon", back_populates="applied_to_orders")
    # order = relationship("Order") # Define if Order model exists and is needed
    # user = relationship("User") # Define if User model exists and is needed