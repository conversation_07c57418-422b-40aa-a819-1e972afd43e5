import uuid
from typing import TYPE_CHECKING
from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String, ForeignKey, Index, Boolean
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.orm import relationship, Mapped, mapped_column

from app.db.base import Base

if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from .optional_option import OptionalOption


class OptionalGroup(Base):
    """
    Represents a group of optional add-ons for a product (e.g., Sides, Drinks).
    This is a tenant-scoped, reusable entity.
    """
    __tablename__ = "optional_groups"

    id: Mapped[uuid.UUID] = mapped_column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    tenant_id: Mapped[uuid.UUID] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)

    name: Mapped[str] = mapped_column(String(100), nullable=False)
    description: Mapped[str | None] = mapped_column(String(255))
    min_selection: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    max_selection: Mapped[int] = mapped_column(Integer, default=5, nullable=False)
    display_order: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    is_required: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)

    # Relationships
    tenant: Mapped["Tenant"] = relationship("Tenant")
    options: Mapped[list["OptionalOption"]] = relationship(
        "OptionalOption", back_populates="optional_group", cascade="all, delete-orphan"
    )

    __table_args__ = (
        Index("ix_optional_groups_tenant_id", "tenant_id"),
        Index("ix_optional_groups_tenant_id_name", "tenant_id", "name"),
    )

    def __repr__(self) -> str:
        return f"<OptionalGroup(id={self.id}, name='{self.name}', tenant_id={self.tenant_id})>" 