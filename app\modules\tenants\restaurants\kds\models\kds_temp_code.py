"""
Modelo para códigos temporários de autenticação do KDS.
"""
import uuid
from datetime import datetime, timezone, timedelta
from sqlalchemy import Column, String, DateTime, Boolean, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base


class KDSTempCode(Base):
    """
    Modelo para códigos temporários de autenticação do KDS.

    Permite que tenant owners gerem códigos de 6 dígitos temporários
    para autenticação de apps KDS usando UUID do restaurante + código.
    """
    __tablename__ = "kds_temp_codes"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Relacionamento com tenant (restaurante)
    tenant_id = Column(
        UUID(as_uuid=True),
        ForeignKey("tenants.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )

    # Código de 6 dígitos
    code = Column(String(6), nullable=False, index=True)

    # Timestamps
    created_at = Column(
        DateTime(timezone=True),
        default=lambda: datetime.now(timezone.utc),
        nullable=False
    )
    expires_at = Column(DateTime(timezone=True), nullable=False)

    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    used_at = Column(DateTime(timezone=True), nullable=True)

    # Relacionamentos
    tenant = relationship("Tenant", back_populates="kds_temp_codes")

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Define expiração padrão de 24 horas se não especificada
        if not self.expires_at:
            self.expires_at = datetime.now(timezone.utc) + timedelta(hours=24)

    @property
    def is_expired(self) -> bool:
        """Verifica se o código está expirado."""
        return datetime.now(timezone.utc) > self.expires_at

    @property
    def is_valid(self) -> bool:
        """Verifica se o código é válido (ativo e não expirado)."""
        return self.is_active and not self.is_expired and not self.used_at

    def mark_as_used(self):
        """Marca o código como usado."""
        self.used_at = datetime.now(timezone.utc)
        self.is_active = False

    def __repr__(self):
        return f"<KDSTempCode(id={self.id}, tenant_id={self.tenant_id}, code={self.code}, expires_at={self.expires_at})>"
