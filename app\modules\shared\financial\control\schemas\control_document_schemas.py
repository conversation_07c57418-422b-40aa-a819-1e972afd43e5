"""Financial Control Document Schemas."""

from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, Field
from uuid import UUID

from ..models.control_document import DocumentType


class ControlDocumentBase(BaseModel):
    """Base schema for control documents."""
    
    document_type: DocumentType
    title: str = Field(..., min_length=1, max_length=255, description="Document title")
    description: Optional[str] = Field(None, description="Document description")
    document_number: Optional[str] = Field(None, max_length=100, description="Document number")
    
    is_original: bool = Field(True, description="Is original document")
    is_required: bool = Field(False, description="Is required document")
    
    document_date: Optional[datetime] = Field(None, description="Date on document")
    expiry_date: Optional[datetime] = Field(None, description="Document expiry date")
    
    tags: Optional[List[str]] = Field(None, description="Document tags")


class ControlDocumentCreate(ControlDocumentBase):
    """Schema for creating control documents."""
    
    media_upload_id: UUID = Field(..., description="Media upload ID")


class ControlDocumentUpdate(BaseModel):
    """Schema for updating control documents."""
    
    document_type: Optional[DocumentType] = None
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    document_number: Optional[str] = Field(None, max_length=100)
    
    is_original: Optional[bool] = None
    is_required: Optional[bool] = None
    
    document_date: Optional[datetime] = None
    expiry_date: Optional[datetime] = None
    
    tags: Optional[List[str]] = None


class ControlDocumentResponse(ControlDocumentBase):
    """Schema for control document responses."""
    
    id: UUID
    tenant_id: UUID
    media_upload_id: UUID
    
    file_name: str
    file_size: Optional[int] = None
    mime_type: Optional[str] = None
    file_hash: Optional[str] = None
    file_url: str
    
    is_verified: bool
    is_image: bool
    is_pdf: bool
    
    verified_by: Optional[UUID] = None
    verified_at: Optional[datetime] = None
    verification_notes: Optional[str] = None
    
    uploaded_by: UUID
    updated_by: Optional[UUID] = None
    uploaded_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class ControlDocumentList(BaseModel):
    """Schema for document lists."""
    
    items: List[ControlDocumentResponse]
    total: int
