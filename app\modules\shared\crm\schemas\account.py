"""Account schemas for CRM module."""

import uuid  # noqa: E402
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, ConfigDict

from app.modules.shared.crm.models.account import AccountType, AccountStatus  # noqa: E402


# Base Account Schema
class AccountBase(BaseModel):
    """Base schema for Account."""

    name: str = Field(..., min_length=1, max_length=255)
    account_type: AccountType = Field(default=AccountType.INDIVIDUAL)
    status: AccountStatus = Field(default=AccountStatus.LEAD)

    # Contact information
    email: Optional[str] = None
    phone: Optional[str] = None
    website: Optional[str] = None

    # Address
    address_line1: Optional[str] = None
    address_line2: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None

    # Business information
    tax_id: Optional[str] = None
    industry: Optional[str] = None
    annual_revenue: Optional[float] = None
    number_of_employees: Optional[int] = None

    # Additional information
    description: Optional[str] = None
    notes: Optional[str] = None


# Schema for creating a new account
class AccountCreate(AccountBase):
    """Schema for creating a new Account."""

    # Optional fields for linking to existing user
    user_tenant_association_id: Optional[uuid.UUID] = None

    # Optional dates
    acquisition_date: Optional[datetime] = None


# Schema for updating an account
class AccountUpdate(BaseModel):
    """Schema for updating an Account."""

    name: Optional[str] = Field(None, min_length=1, max_length=255)
    account_type: Optional[AccountType] = None
    status: Optional[AccountStatus] = None

    # Contact information
    email: Optional[str] = None
    phone: Optional[str] = None
    website: Optional[str] = None

    # Address
    address_line1: Optional[str] = None
    address_line2: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None

    # Business information
    tax_id: Optional[str] = None
    industry: Optional[str] = None
    annual_revenue: Optional[float] = None
    number_of_employees: Optional[int] = None

    # Dates
    acquisition_date: Optional[datetime] = None
    last_contact_date: Optional[datetime] = None

    # Additional information
    description: Optional[str] = None
    notes: Optional[str] = None


# Schema for reading an account
class AccountRead(AccountBase):
    """Schema for reading an Account."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    user_tenant_association_id: Optional[uuid.UUID] = None

    # Dates
    acquisition_date: Optional[datetime] = None
    last_contact_date: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Schema for reading an account with details
class AccountReadWithDetails(AccountRead):
    """Schema for reading an Account with details."""

    # These will be populated when implemented
    # contacts: List["ContactRead"] = []
    # interactions: List["InteractionRead"] = []
    # loyalty_memberships: List["LoyaltyMembershipRead"] = []
    # pricing_assignments: List["CustomerPricingAssignmentRead"] = []

    model_config = ConfigDict(from_attributes=True)
