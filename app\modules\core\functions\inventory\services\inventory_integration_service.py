"""Consolidated Inventory Integration service for automatic shopping list generation."""

import uuid
from datetime import date, datetime
from decimal import Decimal
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, or_, func, text

from app.modules.core.functions.inventory.models.inventory_item import InventoryItem
from app.modules.shared.shopping_list.models.shopping_list import (
    ShoppingList,
    ShoppingListItem,
    ShoppingListStatus,
    ShoppingListItemStatus,
    ShoppingListItemPriority
)
from app.modules.shared.shopping_list.services.shopping_list_service import (
    shopping_list_service
)
from app.modules.shared.shopping_list.schemas.shopping_list import (
    Priority,
    AutoGenerateShoppingListRequest
)
from app.core.exceptions import NotFoundError


class InventoryIntegrationService:
    """Consolidated service for integrating shopping lists with inventory management."""

    async def get_low_stock_items(
        self,
        db: AsyncSession,
        *,
        tenant_id: uuid.UUID,
        include_categories: Optional[List[str]] = None,
        exclude_categories: Optional[List[str]] = None,
        threshold: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Get inventory items that are below their minimum stock threshold."""

        # Build query to find low stock items
        if threshold is not None:
            # Use simple threshold-based query for backward compatibility
            query = text("""
                SELECT
                    ii.id,
                    ii.name,
                    ii.description,
                    ii.quantity as current_quantity,
                    :threshold as minimum_quantity,
                    'unidades' as unit,
                    'General' as category,
                    ii.unit_cost as average_cost,
                    ii.unit_cost as last_purchase_cost,
                    GREATEST(:threshold - ii.quantity + :threshold, 5) as suggested_quantity
                FROM inventory_items ii
                WHERE ii.tenant_id = :tenant_id
                AND ii.quantity <= :threshold
            """)
            params = {"tenant_id": tenant_id, "threshold": threshold}
        else:
            # Use advanced query with minimum_quantity field
            query = text("""
                SELECT
                    ii.id,
                    ii.name,
                    ii.description,
                    COALESCE(ii.current_quantity, ii.quantity) as current_quantity,
                    COALESCE(ii.minimum_quantity, 10) as minimum_quantity,
                    COALESCE(ii.unit, 'unidades') as unit,
                    COALESCE(ii.category, 'General') as category,
                    COALESCE(ii.average_cost, ii.unit_cost) as average_cost,
                    COALESCE(ii.last_purchase_cost, ii.unit_cost) as last_purchase_cost,
                    (COALESCE(ii.minimum_quantity, 10) - COALESCE(ii.current_quantity, ii.quantity) +
                     COALESCE(ii.reorder_quantity, ii.minimum_quantity, 10)) as suggested_quantity
                FROM inventory_items ii
                WHERE ii.tenant_id = :tenant_id
                AND COALESCE(ii.current_quantity, ii.quantity) <= COALESCE(ii.minimum_quantity, 10)
                AND ii.id NOT IN (
                    SELECT DISTINCT sli.inventory_item_id
                    FROM shopping_list_items sli
                    JOIN shopping_lists sl ON sli.shopping_list_id = sl.id
                    WHERE sl.tenant_id = :tenant_id
                    AND sl.status IN ('draft', 'active')
                    AND sli.inventory_item_id IS NOT NULL
                    AND COALESCE(sli.status, 'pending') IN ('pending', 'ordered')
                )
            """)
            params = {"tenant_id": tenant_id}

        # Add category filters if provided
        if include_categories:
            query = text(str(query) + " AND COALESCE(ii.category, 'General') = ANY(:include_categories)")
            params["include_categories"] = include_categories

        if exclude_categories:
            query = text(str(query) + " AND COALESCE(ii.category, 'General') != ALL(:exclude_categories)")
            params["exclude_categories"] = exclude_categories

        query = text(str(query) + " ORDER BY COALESCE(ii.category, 'General'), ii.name")

        result = await db.execute(query, params)
        rows = result.fetchall()

        low_stock_items = []
        for row in rows:
            # Determine priority based on how low the stock is
            current_qty = float(row.current_quantity) if row.current_quantity else 0
            min_qty = float(row.minimum_quantity) if row.minimum_quantity else 10
            stock_ratio = current_qty / min_qty if min_qty > 0 else 0

            if stock_ratio <= 0.2:  # 20% or less of minimum
                priority = ShoppingListItemPriority.URGENT
            elif stock_ratio <= 0.5:  # 50% or less of minimum
                priority = ShoppingListItemPriority.HIGH
            elif stock_ratio <= 0.8:  # 80% or less of minimum
                priority = ShoppingListItemPriority.MEDIUM
            else:
                priority = ShoppingListItemPriority.LOW

            # Use last purchase cost if available, otherwise average cost
            estimated_cost = row.last_purchase_cost or row.average_cost or Decimal('0.00')

            low_stock_items.append({
                "inventory_item_id": row.id,
                "name": row.name,
                "description": row.description,
                "current_quantity": current_qty,
                "minimum_quantity": min_qty,
                "suggested_quantity": max(row.suggested_quantity, 1),
                "unit": row.unit,
                "category": row.category,
                "estimated_unit_cost": estimated_cost,
                "priority": priority,
                "stock_ratio": stock_ratio
            })

        return low_stock_items

    async def auto_add_low_stock_items(
        self,
        db: AsyncSession,
        *,
        tenant_id: uuid.UUID,
        threshold: int = 10,
        shopping_list_name: str = "Lista de Compras Principal"
    ) -> List[ShoppingListItem]:
        """
        Automaticamente adiciona itens com estoque baixo à lista de compras.

        Args:
            db: Sessão do banco de dados
            tenant_id: ID do tenant
            threshold: Limite de estoque baixo
            shopping_list_name: Nome da lista de compras padrão

        Returns:
            Lista de itens adicionados à lista de compras
        """
        # 1. Buscar ou criar lista de compras padrão
        shopping_list = await self._get_or_create_default_shopping_list(
            db=db, tenant_id=tenant_id, list_name=shopping_list_name
        )

        # 2. Buscar itens com estoque baixo usando o método consolidado
        low_stock_data = await self.get_low_stock_items(
            db=db, tenant_id=tenant_id, threshold=threshold
        )

        # 3. Converter para InventoryItem objects para compatibilidade
        low_stock_items = []
        for item_data in low_stock_data:
            # Get the actual inventory item
            result = await db.execute(
                select(InventoryItem).where(
                    and_(
                        InventoryItem.id == item_data["inventory_item_id"],
                        InventoryItem.tenant_id == tenant_id
                    )
                )
            )
            inventory_item = result.scalar_one_or_none()
            if inventory_item:
                low_stock_items.append(inventory_item)

        # 4. Filtrar itens que já estão na lista de compras
        items_to_add = await self._filter_existing_items(
            db=db,
            shopping_list_id=shopping_list.id,
            inventory_items=low_stock_items
        )

        # 5. Adicionar itens à lista de compras
        added_items = []
        for inventory_item in items_to_add:
            shopping_item = await self._create_shopping_item_from_inventory(
                db=db,
                shopping_list_id=shopping_list.id,
                inventory_item=inventory_item
            )
            added_items.append(shopping_item)

        return added_items

    async def update_inventory_from_shopping_list(
        self,
        db: AsyncSession,
        *,
        item_id: uuid.UUID,
        quantity_received: Decimal,
        actual_unit_cost: Optional[Decimal] = None,
        tenant_id: uuid.UUID
    ) -> bool:
        """Update inventory when shopping list items are received."""

        # Get shopping list item with inventory reference
        result = await db.execute(
            select(ShoppingListItem, ShoppingList)
            .join(ShoppingList)
            .where(
                and_(
                    ShoppingListItem.id == item_id,
                    ShoppingList.tenant_id == tenant_id,
                    ShoppingListItem.inventory_item_id.isnot(None)
                )
            )
        )

        row = result.first()
        if not row:
            return False

        shopping_item, shopping_list = row

        # Update inventory item stock
        inventory_update_query = text("""
            UPDATE inventory_items
            SET
                quantity = quantity + :quantity_received,
                unit_cost = COALESCE(:actual_unit_cost, unit_cost),
                updated_at = CURRENT_TIMESTAMP
            WHERE id = :inventory_item_id AND tenant_id = :tenant_id
        """)

        await db.execute(inventory_update_query, {
            "quantity_received": quantity_received,
            "actual_unit_cost": actual_unit_cost,
            "inventory_item_id": shopping_item.inventory_item_id,
            "tenant_id": tenant_id
        })

        # Update shopping list item
        shopping_item.quantity_received = (shopping_item.quantity_received or Decimal('0')) + quantity_received
        shopping_item.status = ShoppingListItemStatus.RECEIVED

        if actual_unit_cost:
            shopping_item.actual_unit_cost = actual_unit_cost
            shopping_item.actual_total_cost = shopping_item.quantity_received * actual_unit_cost

        # Update shopping list totals
        if shopping_item.actual_total_cost:
            shopping_list.actual_total += shopping_item.actual_total_cost

        shopping_list.updated_at = datetime.utcnow()

        await db.commit()
        return True

    async def check_and_auto_generate_shopping_lists(
        self,
        db: AsyncSession,
        *,
        tenant_id: uuid.UUID,
        created_by: uuid.UUID,
        auto_generate_threshold: int = 5  # Minimum number of low stock items to auto-generate
    ) -> Optional[ShoppingList]:
        """Check for low stock items and auto-generate shopping list if needed."""

        # Check if there's already an active auto-generated shopping list
        existing_list_query = select(ShoppingList).where(
            and_(
                ShoppingList.tenant_id == tenant_id,
                ShoppingList.auto_generated == True,
                ShoppingList.status.in_([ShoppingListStatus.DRAFT, ShoppingListStatus.ACTIVE])
            )
        )

        result = await db.execute(existing_list_query)
        existing_list = result.scalar_one_or_none()

        if existing_list:
            # Don't create another auto-generated list if one already exists
            return None

        # Get low stock items
        low_stock_items = await self.get_low_stock_items(db=db, tenant_id=tenant_id)

        # Only auto-generate if we have enough items
        if len(low_stock_items) < auto_generate_threshold:
            return None

        # Create shopping list
        estimated_total = sum(
            item["suggested_quantity"] * item["estimated_unit_cost"]
            for item in low_stock_items
        )

        shopping_list = ShoppingList(
            tenant_id=tenant_id,
            name=f"Auto-Generated Shopping List - {date.today().strftime('%Y-%m-%d')}",
            description=f"Automatically generated from {len(low_stock_items)} low stock items",
            target_date=None,
            estimated_total=estimated_total,
            auto_generated=True,
            created_by=created_by
        )

        db.add(shopping_list)
        await db.flush()  # Get the ID

        # Create shopping list items
        for i, item_data in enumerate(low_stock_items):
            estimated_total_cost = item_data["suggested_quantity"] * item_data["estimated_unit_cost"]

            shopping_item = ShoppingListItem(
                shopping_list_id=shopping_list.id,
                name=item_data["name"],
                description=item_data["description"],
                quantity_needed=item_data["suggested_quantity"],
                unit=item_data["unit"],
                estimated_unit_cost=item_data["estimated_unit_cost"],
                estimated_total_cost=estimated_total_cost,
                priority=item_data["priority"],
                inventory_item_id=item_data["inventory_item_id"],
                auto_generated=True,
                low_stock_threshold=item_data["minimum_quantity"],
                current_stock_level=item_data["current_quantity"],
                sort_order=i,
                notes=f"Auto-added: Stock {item_data['current_quantity']}/{item_data['minimum_quantity']} {item_data['unit']}"
            )
            db.add(shopping_item)

        await db.commit()
        await db.refresh(shopping_list)
        return shopping_list

    async def auto_generate_shopping_list(
        self,
        db: AsyncSession,
        *,
        request: AutoGenerateShoppingListRequest,
        tenant_id: uuid.UUID,
        created_by: uuid.UUID
    ) -> ShoppingList:
        """Auto-generate shopping list from request parameters."""
        return await self.check_and_auto_generate_shopping_lists(
            db=db,
            tenant_id=tenant_id,
            created_by=created_by,
            auto_generate_threshold=getattr(request, 'min_items_threshold', 1)
        )

    async def _get_or_create_default_shopping_list(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID,
        list_name: str
    ) -> ShoppingList:
        """Busca ou cria a lista de compras padrão."""
        # Buscar lista existente
        result = await db.execute(
            select(ShoppingList).where(
                and_(
                    ShoppingList.tenant_id == tenant_id,
                    ShoppingList.name == list_name,
                    ShoppingList.status.in_([ShoppingListStatus.DRAFT, ShoppingListStatus.ACTIVE])
                )
            )
        )
        shopping_list = result.scalars().first()

        if not shopping_list:
            # Criar nova lista
            from app.modules.shared.shopping_list.schemas.shopping_list import (
                ShoppingListCreate
            )
            list_data = ShoppingListCreate(
                name=list_name,
                description="Lista principal de compras do restaurante"
            )
            shopping_list = await shopping_list_service.create_list(
                db=db, list_in=list_data, tenant_id=tenant_id
            )

        return shopping_list

    async def _filter_existing_items(
        self,
        db: AsyncSession,
        shopping_list_id: uuid.UUID,
        inventory_items: List[InventoryItem]
    ) -> List[InventoryItem]:
        """Filtra itens que já estão na lista de compras."""
        if not inventory_items:
            return []

        # Buscar itens já existentes na lista
        inventory_ids = [item.id for item in inventory_items]
        result = await db.execute(
            select(ShoppingListItem.inventory_item_id).where(
                and_(
                    ShoppingListItem.shopping_list_id == shopping_list_id,
                    ShoppingListItem.inventory_item_id.in_(inventory_ids),
                    or_(
                        ShoppingListItem.purchased == False,
                        ShoppingListItem.status.in_([
                            ShoppingListItemStatus.PENDING,
                            ShoppingListItemStatus.ORDERED
                        ])
                    )
                )
            )
        )
        existing_ids = {row[0] for row in result.all()}

        # Retornar apenas itens que não estão na lista
        return [item for item in inventory_items if item.id not in existing_ids]

    async def _create_shopping_item_from_inventory(
        self,
        db: AsyncSession,
        shopping_list_id: uuid.UUID,
        inventory_item: InventoryItem
    ) -> ShoppingListItem:
        """Cria um item na lista de compras baseado no item do inventário."""
        # Determinar prioridade baseada no estoque
        if inventory_item.quantity <= 2:
            priority = Priority.HIGH
        elif inventory_item.quantity <= 5:
            priority = Priority.MEDIUM
        else:
            priority = Priority.LOW

        # Calcular quantidade sugerida (dobrar o estoque atual, mínimo 5)
        suggested_quantity = max(inventory_item.quantity * 2, 5)

        shopping_item = ShoppingListItem(
            shopping_list_id=shopping_list_id,
            inventory_item_id=inventory_item.id,
            name=inventory_item.name,
            quantity=suggested_quantity,
            unit="unidades",
            priority=priority,
            estimated_price=inventory_item.unit_cost,
            purchased=False,
            notes=f"Auto-adicionado: estoque atual {inventory_item.quantity} unidades"
        )

        db.add(shopping_item)
        await db.commit()
        await db.refresh(shopping_item)
        return shopping_item

    async def sync_inventory_to_shopping_list(
        self,
        db: AsyncSession,
        *,
        tenant_id: uuid.UUID,
        threshold: int = 10
    ) -> dict:
        """
        Sincroniza automaticamente itens com estoque baixo para a lista de compras.

        Returns:
            Dicionário com estatísticas da sincronização
        """
        try:
            added_items = await self.auto_add_low_stock_items(
                db=db, tenant_id=tenant_id, threshold=threshold
            )

            return {
                "success": True,
                "items_added": len(added_items),
                "threshold_used": threshold,
                "message": f"Successfully added {len(added_items)} items to shopping list"
            }
        except Exception as e:
            return {
                "success": False,
                "items_added": 0,
                "threshold_used": threshold,
                "error": str(e),
                "message": "Failed to sync inventory to shopping list"
            }


# Consolidated service instance
inventory_integration_service = InventoryIntegrationService()
