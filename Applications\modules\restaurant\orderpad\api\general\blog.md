# General - Blog

**Categoria:** General
**Mó<PERSON><PERSON>:** Blog
**Total de Endpoints:** 29
**Gerado em:** 08/06/2025, 17:26:01

## 📋 Endpoints

- [GET /api/modules/core/blog/](#get-apimodulescoreblog) - Blog System Status
- [GET /api/modules/core/blog/authors/](#get-apimodulescoreblogauthors) - Get Authors
- [POST /api/modules/core/blog/authors/](#post-apimodulescoreblogauthors) - Create Author
- [GET /api/modules/core/blog/authors/slug/{slug}](#get-apimodulescoreblogauthorsslugslug) - Get Author By Slug
- [GET /api/modules/core/blog/authors/{author_id}](#get-apimodulescoreblogauthorsauthor-id) - Get Author
- [PUT /api/modules/core/blog/authors/{author_id}/link-user/{user_id}](#put-apimodulescoreblogauthorsauthor-idlink-useruser-id) - Link User To Author
- [DELETE /api/modules/core/blog/authors/{author_id}/unlink-user](#delete-apimodulescoreblogauthorsauthor-idunlink-user) - Unlink User From Author
- [GET /api/modules/core/blog/categories/](#get-apimodulescoreblogcategories) - Get Categories
- [POST /api/modules/core/blog/categories/](#post-apimodulescoreblogcategories) - Create Category
- [GET /api/modules/core/blog/categories/tree](#get-apimodulescoreblogcategoriestree) - Get Category Tree
- [DELETE /api/modules/core/blog/categories/{category_id}](#delete-apimodulescoreblogcategoriescategory-id) - Delete Category
- [GET /api/modules/core/blog/categories/{category_id}](#get-apimodulescoreblogcategoriescategory-id) - Get Category
- [PUT /api/modules/core/blog/categories/{category_id}](#put-apimodulescoreblogcategoriescategory-id) - Update Category
- [GET /api/modules/core/blog/comments/](#get-apimodulescoreblogcomments) - Get Comments
- [POST /api/modules/core/blog/comments/](#post-apimodulescoreblogcomments) - Create Comment
- [POST /api/modules/core/blog/comments/moderate](#post-apimodulescoreblogcommentsmoderate) - Moderate Comments
- [GET /api/modules/core/blog/comments/post/{post_id}/tree](#get-apimodulescoreblogcommentspostpost-idtree) - Get Post Comments Tree
- [GET /api/modules/core/blog/comments/stats](#get-apimodulescoreblogcommentsstats) - Get Comment Stats
- [GET /api/modules/core/blog/posts/](#get-apimodulescoreblogposts) - Get Posts
- [POST /api/modules/core/blog/posts/](#post-apimodulescoreblogposts) - Create Post
- [GET /api/modules/core/blog/posts/slug/{slug}](#get-apimodulescoreblogpostsslugslug) - Get Post By Slug
- [DELETE /api/modules/core/blog/posts/{post_id}](#delete-apimodulescoreblogpostspost-id) - Delete Post
- [GET /api/modules/core/blog/posts/{post_id}](#get-apimodulescoreblogpostspost-id) - Get Post
- [PUT /api/modules/core/blog/posts/{post_id}](#put-apimodulescoreblogpostspost-id) - Update Post
- [GET /api/modules/core/blog/posts/{post_id}/related](#get-apimodulescoreblogpostspost-idrelated) - Get Related Posts
- [GET /api/modules/core/blog/tags/](#get-apimodulescoreblogtags) - Get Tags
- [POST /api/modules/core/blog/tags/](#post-apimodulescoreblogtags) - Create Tag
- [GET /api/modules/core/blog/tags/cloud](#get-apimodulescoreblogtagscloud) - Get Tag Cloud
- [GET /api/modules/core/blog/tags/{tag_id}](#get-apimodulescoreblogtagstag-id) - Get Tag

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### BlogAuthorCreate

**Descrição:** Schema for creating blog authors.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `slug` | string | ✅ | - |
| `display_name` | string | ✅ | - |
| `email` | unknown | ❌ | - |
| `bio` | unknown | ❌ | - |
| `avatar_url` | unknown | ❌ | - |
| `website_url` | unknown | ❌ | - |
| `twitter_handle` | unknown | ❌ | - |
| `linkedin_url` | unknown | ❌ | - |
| `github_url` | unknown | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `is_featured` | boolean | ❌ | - |
| `user_id` | unknown | ❌ | - |

### BlogAuthorProfile

**Descrição:** Schema for public author profile.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `slug` | string | ✅ | - |
| `display_name` | string | ✅ | - |
| `bio` | unknown | ✅ | - |
| `avatar_url` | unknown | ✅ | - |
| `website_url` | unknown | ✅ | - |
| `twitter_handle` | unknown | ✅ | - |
| `linkedin_url` | unknown | ✅ | - |
| `github_url` | unknown | ✅ | - |
| `post_count` | integer | ❌ | - |
| `total_views` | integer | ❌ | - |

### BlogAuthorRead

**Descrição:** Schema for reading blog authors.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `slug` | string | ✅ | - |
| `display_name` | string | ✅ | - |
| `email` | unknown | ❌ | - |
| `bio` | unknown | ❌ | - |
| `avatar_url` | unknown | ❌ | - |
| `website_url` | unknown | ❌ | - |
| `twitter_handle` | unknown | ❌ | - |
| `linkedin_url` | unknown | ❌ | - |
| `github_url` | unknown | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `is_featured` | boolean | ❌ | - |
| `id` | string | ✅ | - |
| `user_id` | unknown | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### BlogCategoryCreate

**Descrição:** Schema for creating blog categories.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `slug` | string | ✅ | - |
| `parent_id` | unknown | ❌ | - |
| `sort_order` | integer | ❌ | - |
| `color` | unknown | ❌ | - |
| `icon` | unknown | ❌ | - |
| `translations` | Array[BlogCategoryTranslationCreate] | ✅ | - |

### BlogCategoryRead

**Descrição:** Schema for reading blog categories.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `slug` | string | ✅ | - |
| `parent_id` | unknown | ❌ | - |
| `sort_order` | integer | ❌ | - |
| `color` | unknown | ❌ | - |
| `icon` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `parent` | unknown | ❌ | - |
| `children` | Array[BlogCategoryRead] | ❌ | - |
| `translations` | Array[BlogCategoryTranslationRead] | ❌ | - |

### BlogCategoryUpdate

**Descrição:** Schema for updating blog categories.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `slug` | unknown | ❌ | - |
| `parent_id` | unknown | ❌ | - |
| `sort_order` | unknown | ❌ | - |
| `color` | unknown | ❌ | - |
| `icon` | unknown | ❌ | - |

### BlogCommentCreate

**Descrição:** Schema for creating blog comments.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `content` | string | ✅ | - |
| `guest_name` | unknown | ❌ | - |
| `guest_email` | unknown | ❌ | - |
| `guest_website` | unknown | ❌ | - |
| `post_id` | string | ✅ | - |
| `parent_id` | unknown | ❌ | - |

### BlogCommentModeration

**Descrição:** Schema for comment moderation actions.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `comment_ids` | Array[string] | ✅ | - |
| `action` | string | ✅ | - |

### BlogCommentRead

**Descrição:** Schema for reading blog comments.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `content` | string | ✅ | - |
| `guest_name` | unknown | ❌ | - |
| `guest_email` | unknown | ❌ | - |
| `guest_website` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `post_id` | string | ✅ | - |
| `user_id` | unknown | ✅ | - |
| `parent_id` | unknown | ✅ | - |
| `status` | string | ✅ | - |
| `is_pinned` | boolean | ✅ | - |
| `like_count` | integer | ✅ | - |
| `reply_count` | integer | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `user_display_name` | unknown | ❌ | - |
| `user_avatar_url` | unknown | ❌ | - |

### BlogCommentStats

**Descrição:** Schema for comment statistics.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `total_comments` | integer | ✅ | - |
| `pending_comments` | integer | ✅ | - |
| `approved_comments` | integer | ✅ | - |
| `rejected_comments` | integer | ✅ | - |
| `spam_comments` | integer | ✅ | - |

### BlogPostCreate

**Descrição:** Schema for creating blog posts.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `slug` | string | ✅ | - |
| `author_id` | string | ✅ | - |
| `category_id` | unknown | ❌ | - |
| `status` | string | ❌ | - |
| `visibility` | string | ❌ | - |
| `is_featured` | boolean | ❌ | - |
| `published_at` | unknown | ❌ | - |
| `scheduled_at` | unknown | ❌ | - |
| `featured_image_url` | unknown | ❌ | - |
| `featured_image_alt` | unknown | ❌ | - |
| `translations` | Array[BlogPostTranslationCreate] | ✅ | - |
| `tag_ids` | unknown | ❌ | - |

### BlogPostRead

**Descrição:** Schema for reading blog posts.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `slug` | string | ✅ | - |
| `author_id` | string | ✅ | - |
| `category_id` | unknown | ❌ | - |
| `status` | string | ❌ | - |
| `visibility` | string | ❌ | - |
| `is_featured` | boolean | ❌ | - |
| `published_at` | unknown | ❌ | - |
| `scheduled_at` | unknown | ❌ | - |
| `featured_image_url` | unknown | ❌ | - |
| `featured_image_alt` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `view_count` | integer | ✅ | - |
| `like_count` | integer | ✅ | - |
| `comment_count` | integer | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `author` | unknown | ❌ | - |
| `category` | unknown | ❌ | - |
| `tags` | Array[BlogTagRead] | ❌ | - |
| `translations` | Array[BlogPostTranslationRead] | ❌ | - |
| `seo` | unknown | ❌ | - |

### BlogPostUpdate

**Descrição:** Schema for updating blog posts.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `slug` | unknown | ❌ | - |
| `category_id` | unknown | ❌ | - |
| `status` | unknown | ❌ | - |
| `visibility` | unknown | ❌ | - |
| `is_featured` | unknown | ❌ | - |
| `published_at` | unknown | ❌ | - |
| `scheduled_at` | unknown | ❌ | - |
| `featured_image_url` | unknown | ❌ | - |
| `featured_image_alt` | unknown | ❌ | - |
| `tag_ids` | unknown | ❌ | - |

### BlogTagCreate

**Descrição:** Schema for creating blog tags.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `slug` | string | ✅ | - |
| `color` | unknown | ❌ | - |
| `translations` | Array[BlogTagTranslationCreate] | ✅ | - |

### BlogTagRead

**Descrição:** Schema for reading blog tags.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `slug` | string | ✅ | - |
| `color` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `translations` | Array[BlogTagTranslationRead] | ❌ | - |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/modules/core/blog/ {#get-apimodulescoreblog}

**Resumo:** Blog System Status
**Descrição:** Get blog system status and information.

Returns basic information about the blog system including
available features and API version.

**🔐 Autenticação:** Não requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/"
```

---

### GET /api/modules/core/blog/authors/ {#get-apimodulescoreblogauthors}

**Resumo:** Get Authors
**Descrição:** Get blog authors with filtering and pagination.

Results include statistics like post count and total views.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | Number of authors to skip |
| `limit` | integer | query | ❌ | Number of authors to return |
| `is_active` | string | query | ❌ | Filter by active status |
| `is_featured` | string | query | ❌ | Filter featured authors |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/authors/"
```

---

### POST /api/modules/core/blog/authors/ {#post-apimodulescoreblogauthors}

**Resumo:** Create Author
**Descrição:** Create a new blog author.

Requires authentication and admin privileges.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `authorization` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [BlogAuthorCreate](#blogauthorcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogAuthorRead](#blogauthorread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/blog/authors/" \
  -H "Content-Type: application/json" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/blog/authors/slug/{slug} {#get-apimodulescoreblogauthorsslugslug}

**Resumo:** Get Author By Slug
**Descrição:** Get a blog author's public profile by slug.

Returns public information suitable for author profile pages.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `slug` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogAuthorProfile](#blogauthorprofile)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/authors/slug/{slug}"
```

---

### GET /api/modules/core/blog/authors/{author_id} {#get-apimodulescoreblogauthorsauthor-id}

**Resumo:** Get Author
**Descrição:** Get a specific blog author by ID.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `author_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogAuthorRead](#blogauthorread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/authors/{author_id}"
```

---

### PUT /api/modules/core/blog/authors/{author_id}/link-user/{user_id} {#put-apimodulescoreblogauthorsauthor-idlink-useruser-id}

**Resumo:** Link User To Author
**Descrição:** Link a system user to an existing blog author.

Requires admin privileges. This allows the user to be associated with the author profile.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `author_id` | string | path | ✅ | - |
| `user_id` | string | path | ✅ | - |
| `authorization` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogAuthorRead](#blogauthorread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/core/blog/authors/{author_id}/link-user/{user_id}"
```

---

### DELETE /api/modules/core/blog/authors/{author_id}/unlink-user {#delete-apimodulescoreblogauthorsauthor-idunlink-user}

**Resumo:** Unlink User From Author
**Descrição:** Unlink a system user from a blog author.

Requires admin privileges. This removes the association between the user and author.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `author_id` | string | path | ✅ | - |
| `authorization` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogAuthorRead](#blogauthorread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/core/blog/authors/{author_id}/unlink-user"
```

---

### GET /api/modules/core/blog/categories/ {#get-apimodulescoreblogcategories}

**Resumo:** Get Categories
**Descrição:** Get blog categories with filtering and pagination.

Supports filtering by parent category and language.
Results include post counts for each category.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | Number of categories to skip |
| `limit` | integer | query | ❌ | Number of categories to return |
| `language` | string | query | ❌ | Language code |
| `parent_id` | string | query | ❌ | Filter by parent category |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/categories/"
```

---

### POST /api/modules/core/blog/categories/ {#post-apimodulescoreblogcategories}

**Resumo:** Create Category
**Descrição:** Create a new blog category.

Requires authentication and admin privileges.
Must include at least one translation.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [BlogCategoryCreate](#blogcategorycreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogCategoryRead](#blogcategoryread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/blog/categories/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/blog/categories/tree {#get-apimodulescoreblogcategoriestree}

**Resumo:** Get Category Tree
**Descrição:** Get hierarchical category tree.

Returns categories organized in a tree structure with children.
Includes post counts for each category.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `language` | string | query | ❌ | Language code |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/categories/tree"
```

---

### DELETE /api/modules/core/blog/categories/{category_id} {#delete-apimodulescoreblogcategoriescategory-id}

**Resumo:** Delete Category
**Descrição:** Delete a blog category.

Requires authentication and admin privileges.
Cannot delete categories that have posts.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/core/blog/categories/{category_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/blog/categories/{category_id} {#get-apimodulescoreblogcategoriescategory-id}

**Resumo:** Get Category
**Descrição:** Get a specific blog category by ID.

Optionally filter translations to a specific language.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | - |
| `language` | string | query | ❌ | Language code |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogCategoryRead](#blogcategoryread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/categories/{category_id}"
```

---

### PUT /api/modules/core/blog/categories/{category_id} {#put-apimodulescoreblogcategoriescategory-id}

**Resumo:** Update Category
**Descrição:** Update a blog category.

Requires authentication and admin privileges.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [BlogCategoryUpdate](#blogcategoryupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogCategoryRead](#blogcategoryread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/core/blog/categories/{category_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/blog/comments/ {#get-apimodulescoreblogcomments}

**Resumo:** Get Comments
**Descrição:** Get blog comments with filtering and pagination.

Supports filtering by post and moderation status.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | Number of comments to skip |
| `limit` | integer | query | ❌ | Number of comments to return |
| `post_id` | string | query | ❌ | Filter by post |
| `status` | string | query | ❌ | Filter by status |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/comments/"
```

---

### POST /api/modules/core/blog/comments/ {#post-apimodulescoreblogcomments}

**Resumo:** Create Comment
**Descrição:** Create a new blog comment.

Can be created by authenticated users or guests.
Guest comments require name and email.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [BlogCommentCreate](#blogcommentcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogCommentRead](#blogcommentread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/blog/comments/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/core/blog/comments/moderate {#post-apimodulescoreblogcommentsmoderate}

**Resumo:** Moderate Comments
**Descrição:** Moderate blog comments (approve, reject, mark as spam, delete).

Requires authentication and admin privileges.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [BlogCommentModeration](#blogcommentmoderation)

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/blog/comments/moderate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/blog/comments/post/{post_id}/tree {#get-apimodulescoreblogcommentspostpost-idtree}

**Resumo:** Get Post Comments Tree
**Descrição:** Get comments for a post in threaded tree format.

Returns comments organized hierarchically with replies.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `post_id` | string | path | ✅ | - |
| `status` | string | query | ❌ | Comment status filter |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/comments/post/{post_id}/tree"
```

---

### GET /api/modules/core/blog/comments/stats {#get-apimodulescoreblogcommentsstats}

**Resumo:** Get Comment Stats
**Descrição:** Get comment statistics.

Requires authentication. Returns moderation statistics.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `post_id` | string | query | ❌ | Filter by post |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogCommentStats](#blogcommentstats)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/comments/stats" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/blog/posts/ {#get-apimodulescoreblogposts}

**Resumo:** Get Posts
**Descrição:** Get blog posts with filtering and pagination.

Supports filtering by status, category, tag, author, language, and featured status.
Results are paginated and can be ordered by various fields.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | Number of posts to skip |
| `limit` | integer | query | ❌ | Number of posts to return |
| `status` | string | query | ❌ | Filter by post status |
| `category_id` | string | query | ❌ | Filter by category |
| `tag_id` | string | query | ❌ | Filter by tag |
| `author_id` | string | query | ❌ | Filter by author |
| `language` | string | query | ❌ | Language code |
| `is_featured` | string | query | ❌ | Filter featured posts |
| `order_by` | string | query | ❌ | Field to order by |
| `order_direction` | string | query | ❌ | - |
| `authorization` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/posts/"
```

---

### POST /api/modules/core/blog/posts/ {#post-apimodulescoreblogposts}

**Resumo:** Create Post
**Descrição:** Create a new blog post.

Requires authentication. The post will be created with the specified author.
Must include at least one translation.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `authorization` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [BlogPostCreate](#blogpostcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogPostRead](#blogpostread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/blog/posts/" \
  -H "Content-Type: application/json" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/blog/posts/slug/{slug} {#get-apimodulescoreblogpostsslugslug}

**Resumo:** Get Post By Slug
**Descrição:** Get a specific blog post by slug.

Optionally filter translations to a specific language.
Automatically increments view count.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `slug` | string | path | ✅ | - |
| `language` | string | query | ❌ | Language code |
| `authorization` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogPostRead](#blogpostread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/posts/slug/{slug}"
```

---

### DELETE /api/modules/core/blog/posts/{post_id} {#delete-apimodulescoreblogpostspost-id}

**Resumo:** Delete Post
**Descrição:** Delete a blog post.

Requires authentication. Only the author or admin can delete a post.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `post_id` | string | path | ✅ | - |
| `authorization` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/core/blog/posts/{post_id}"
```

---

### GET /api/modules/core/blog/posts/{post_id} {#get-apimodulescoreblogpostspost-id}

**Resumo:** Get Post
**Descrição:** Get a specific blog post by ID.

Optionally filter translations to a specific language.
Automatically increments view count.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `post_id` | string | path | ✅ | - |
| `language` | string | query | ❌ | Language code |
| `authorization` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogPostRead](#blogpostread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/posts/{post_id}"
```

---

### PUT /api/modules/core/blog/posts/{post_id} {#put-apimodulescoreblogpostspost-id}

**Resumo:** Update Post
**Descrição:** Update a blog post.

Requires authentication. Only the author or admin can update a post.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `post_id` | string | path | ✅ | - |
| `authorization` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [BlogPostUpdate](#blogpostupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogPostRead](#blogpostread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/core/blog/posts/{post_id}" \
  -H "Content-Type: application/json" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/blog/posts/{post_id}/related {#get-apimodulescoreblogpostspost-idrelated}

**Resumo:** Get Related Posts
**Descrição:** Get related blog posts.

Returns posts that share tags or category with the specified post.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `post_id` | string | path | ✅ | - |
| `limit` | integer | query | ❌ | Number of related posts |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/posts/{post_id}/related"
```

---

### GET /api/modules/core/blog/tags/ {#get-apimodulescoreblogtags}

**Resumo:** Get Tags
**Descrição:** Get blog tags with filtering and pagination.

Results include post counts for each tag.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | Number of tags to skip |
| `limit` | integer | query | ❌ | Number of tags to return |
| `language` | string | query | ❌ | Language code |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/tags/"
```

---

### POST /api/modules/core/blog/tags/ {#post-apimodulescoreblogtags}

**Resumo:** Create Tag
**Descrição:** Create a new blog tag.

Requires authentication.
Must include at least one translation.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [BlogTagCreate](#blogtagcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogTagRead](#blogtagread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/blog/tags/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/blog/tags/cloud {#get-apimodulescoreblogtagscloud}

**Resumo:** Get Tag Cloud
**Descrição:** Get tag cloud data.

Returns tags with calculated weights for tag cloud display.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `limit` | integer | query | ❌ | Number of tags to return |
| `language` | string | query | ❌ | Language code |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/tags/cloud"
```

---

### GET /api/modules/core/blog/tags/{tag_id} {#get-apimodulescoreblogtagstag-id}

**Resumo:** Get Tag
**Descrição:** Get a specific blog tag by ID.

Optionally filter translations to a specific language.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tag_id` | string | path | ✅ | - |
| `language` | string | query | ❌ | Language code |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogTagRead](#blogtagread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/tags/{tag_id}"
```

---
