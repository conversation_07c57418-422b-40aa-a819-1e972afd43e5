"""
Language specific schemas for tenant settings.
"""

from typing import Optional, List

from pydantic import BaseModel, Field


class LanguageSettingsUpdate(BaseModel):
    """Schema for updating language settings."""
    
    multi_language_enabled: Optional[bool] = Field(
        None, 
        description="Enable multi-language support"
    )
    available_languages: Optional[List[str]] = Field(
        None, 
        description="List of available language codes"
    )
    default_language: Optional[str] = Field(
        None, 
        max_length=5, 
        description="Default language code"
    )
