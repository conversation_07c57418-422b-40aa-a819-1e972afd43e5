# General - Eshop

**Categoria:** General
**<PERSON><PERSON><PERSON><PERSON>:** Eshop
**Total de Endpoints:** 34
**Gerado em:** 27/06/2025, 20:20:26

## 📋 Endpoints

- [GET /api/eshop/categories/](#get-apieshopcategories) - List Categories
- [POST /api/eshop/categories/](#post-apieshopcategories) - Create Category
- [GET /api/eshop/categories/tree](#get-apieshopcategoriestree) - Read Category Tree
- [GET /api/eshop/categories/universal](#get-apieshopcategoriesuniversal) - List Universal Categories
- [POST /api/eshop/categories/universal](#post-apieshopcategoriesuniversal) - Create Universal Category
- [DELETE /api/eshop/categories/universal/{category_id}](#delete-apieshopcategoriesuniversalcategory-id) - Delete Universal Category
- [PUT /api/eshop/categories/universal/{category_id}](#put-apieshopcategoriesuniversalcategory-id) - Update Universal Category
- [DELETE /api/eshop/categories/{category_id}](#delete-apieshopcategoriescategory-id) - Delete Category
- [GET /api/eshop/categories/{category_id}](#get-apieshopcategoriescategory-id) - Read Category
- [PUT /api/eshop/categories/{category_id}](#put-apieshopcategoriescategory-id) - Update Category
- [GET /api/eshop/modifiers/groups/](#get-apieshopmodifiersgroups) - Read Modifier Groups
- [POST /api/eshop/modifiers/groups/](#post-apieshopmodifiersgroups) - Create Modifier Group
- [GET /api/eshop/modifiers/groups/{group_id}](#get-apieshopmodifiersgroupsgroup-id) - Read Modifier Group
- [GET /api/eshop/modifiers/options/](#get-apieshopmodifiersoptions) - Read Modifier Options
- [POST /api/eshop/modifiers/options/](#post-apieshopmodifiersoptions) - Create Modifier Option
- [GET /api/eshop/modifiers/options/{option_id}](#get-apieshopmodifiersoptionsoption-id) - Read Modifier Option
- [GET /api/eshop/optionals/groups/](#get-apieshopoptionalsgroups) - Read Optional Groups
- [POST /api/eshop/optionals/groups/](#post-apieshopoptionalsgroups) - Create Optional Group
- [GET /api/eshop/optionals/groups/{group_id}](#get-apieshopoptionalsgroupsgroup-id) - Read Optional Group
- [GET /api/eshop/optionals/options/](#get-apieshopoptionalsoptions) - Read Optional Options
- [POST /api/eshop/optionals/options/](#post-apieshopoptionalsoptions) - Create Optional Option
- [GET /api/eshop/optionals/options/{option_id}](#get-apieshopoptionalsoptionsoption-id) - Read Optional Option
- [GET /api/eshop/products/](#get-apieshopproducts) - List Products
- [POST /api/eshop/products/](#post-apieshopproducts) - Create Product
- [DELETE /api/eshop/products/{product_id}](#delete-apieshopproductsproduct-id) - Delete Product
- [GET /api/eshop/products/{product_id}](#get-apieshopproductsproduct-id) - Get Product
- [PUT /api/eshop/products/{product_id}](#put-apieshopproductsproduct-id) - Update Product
- [PATCH /api/eshop/products/{product_id}/stock](#patch-apieshopproductsproduct-idstock) - Update Product Stock
- [GET /api/eshop/variants/groups/](#get-apieshopvariantsgroups) - Read Variant Groups
- [POST /api/eshop/variants/groups/](#post-apieshopvariantsgroups) - Create Variant Group
- [GET /api/eshop/variants/groups/{group_id}](#get-apieshopvariantsgroupsgroup-id) - Read Variant Group
- [GET /api/eshop/variants/options/](#get-apieshopvariantsoptions) - Read Variant Options
- [POST /api/eshop/variants/options/](#post-apieshopvariantsoptions) - Create Variant Option
- [GET /api/eshop/variants/options/{option_id}](#get-apieshopvariantsoptionsoption-id) - Read Variant Option

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### ProductCategoryResponse

**Descrição:** Schema for product category responses.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Category name |
| `description` | unknown | ❌ | Category description |
| `slug` | string | ✅ | URL-friendly category name |
| `parent_id` | unknown | ❌ | Parent category ID for subcategories |
| `display_order` | integer | ❌ | Display order |
| `is_active` | boolean | ❌ | Whether category is active |
| `is_featured` | boolean | ❌ | Whether category is featured |
| `meta_title` | unknown | ❌ | SEO meta title |
| `meta_description` | unknown | ❌ | SEO meta description |
| `image_url` | unknown | ❌ | Category image URL |
| `icon` | unknown | ❌ | Category icon |
| `id` | string | ✅ | - |
| `tenant_id` | unknown | ✅ | - |
| `product_count` | unknown | ❌ | Number of products in this category |

### ProductCategoryUpdate

**Descrição:** Schema for updating a product category.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | Category name |
| `description` | unknown | ❌ | Category description |
| `slug` | unknown | ❌ | URL-friendly category name |
| `parent_id` | unknown | ❌ | Parent category ID for subcategories |
| `display_order` | unknown | ❌ | Display order |
| `is_active` | unknown | ❌ | Whether category is active |
| `is_featured` | unknown | ❌ | Whether category is featured |
| `meta_title` | unknown | ❌ | SEO meta title |
| `meta_description` | unknown | ❌ | SEO meta description |
| `image_url` | unknown | ❌ | Category image URL |
| `icon` | unknown | ❌ | Category icon |

### ProductModifierGroupCreate

**Descrição:** Schema for creating a new product modifier group.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Modifier group name |
| `description` | unknown | ❌ | Modifier group description |
| `min_selection` | integer | ❌ | Minimum number of selections required |
| `max_selection` | integer | ❌ | Maximum number of selections allowed |
| `display_order` | integer | ❌ | Display order within the product |
| `is_required` | boolean | ❌ | Whether this modifier group is required |
| `is_active` | boolean | ❌ | Whether this modifier group is active |
| `is_template` | boolean | ❌ | Whether this is a template group |
| `template_id` | unknown | ❌ | Reference to template group |
| `tenant_id` | unknown | ❌ | Tenant ID (null for global groups) |

### ProductModifierGroupResponse

**Descrição:** Schema for product modifier group responses.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Modifier group name |
| `description` | unknown | ❌ | Modifier group description |
| `min_selection` | integer | ❌ | Minimum number of selections required |
| `max_selection` | integer | ❌ | Maximum number of selections allowed |
| `display_order` | integer | ❌ | Display order within the product |
| `is_required` | boolean | ❌ | Whether this modifier group is required |
| `is_active` | boolean | ❌ | Whether this modifier group is active |
| `is_template` | boolean | ❌ | Whether this is a template group |
| `template_id` | unknown | ❌ | Reference to template group |
| `id` | string | ✅ | - |
| `tenant_id` | unknown | ✅ | - |
| `options` | Array[ProductModifierOptionResponse] | ❌ | Modifier options |

### ProductModifierOptionCreate

**Descrição:** Schema for creating a new product modifier option.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Modifier option name |
| `description` | unknown | ❌ | Modifier option description |
| `price_adjustment` | unknown | ❌ | Additional cost for this modifier |
| `price_adjustment_type` | string | ❌ | Price adjustment type |
| `display_order` | integer | ❌ | Display order within the group |
| `is_default` | boolean | ❌ | Whether this is the default selection |
| `is_active` | boolean | ❌ | Whether this modifier option is active |
| `stock_quantity` | unknown | ❌ | Limited availability for this modifier |
| `image_url` | unknown | ❌ | Image URL for this modifier |
| `tenant_id` | unknown | ❌ | Tenant ID (null for global options) |
| `modifier_group_id` | string | ✅ | Parent modifier group ID |

### ProductModifierOptionResponse

**Descrição:** Schema for product modifier option responses.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Modifier option name |
| `description` | unknown | ❌ | Modifier option description |
| `price_adjustment` | string | ❌ | Additional cost for this modifier |
| `price_adjustment_type` | string | ❌ | Price adjustment type |
| `display_order` | integer | ❌ | Display order within the group |
| `is_default` | boolean | ❌ | Whether this is the default selection |
| `is_active` | boolean | ❌ | Whether this modifier option is active |
| `stock_quantity` | unknown | ❌ | Limited availability for this modifier |
| `image_url` | unknown | ❌ | Image URL for this modifier |
| `id` | string | ✅ | - |
| `tenant_id` | unknown | ✅ | - |
| `modifier_group_id` | string | ✅ | - |

### ProductOptionalGroupCreate

**Descrição:** Schema for creating a new product optional group.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Optional group name |
| `description` | unknown | ❌ | Optional group description |
| `min_selection` | integer | ❌ | Minimum number of selections required |
| `max_selection` | integer | ❌ | Maximum number of selections allowed |
| `display_order` | integer | ❌ | Display order within the product |
| `is_required` | boolean | ❌ | Whether this optional group is required |
| `is_active` | boolean | ❌ | Whether this optional group is active |
| `is_template` | boolean | ❌ | Whether this is a template group |
| `template_id` | unknown | ❌ | Reference to template group |
| `tenant_id` | unknown | ❌ | Tenant ID (null for global groups) |

### ProductOptionalGroupResponse

**Descrição:** Schema for product optional group responses.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Optional group name |
| `description` | unknown | ❌ | Optional group description |
| `min_selection` | integer | ❌ | Minimum number of selections required |
| `max_selection` | integer | ❌ | Maximum number of selections allowed |
| `display_order` | integer | ❌ | Display order within the product |
| `is_required` | boolean | ❌ | Whether this optional group is required |
| `is_active` | boolean | ❌ | Whether this optional group is active |
| `is_template` | boolean | ❌ | Whether this is a template group |
| `template_id` | unknown | ❌ | Reference to template group |
| `id` | string | ✅ | - |
| `tenant_id` | unknown | ✅ | - |
| `options` | Array[ProductOptionalOptionResponse] | ❌ | Optional options |

### ProductOptionalOptionCreate

**Descrição:** Schema for creating a new product optional option.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Optional option name |
| `description` | unknown | ❌ | Optional option description |
| `price_adjustment` | unknown | ❌ | Additional cost for this optional |
| `price_adjustment_type` | string | ❌ | Price adjustment type |
| `display_order` | integer | ❌ | Display order within the group |
| `is_default` | boolean | ❌ | Whether this is the default selection |
| `is_active` | boolean | ❌ | Whether this optional option is active |
| `stock_quantity` | unknown | ❌ | Limited availability for this optional |
| `sku` | unknown | ❌ | Separate SKU for this optional item |
| `image_url` | unknown | ❌ | Image URL for this optional |
| `related_product_id` | unknown | ❌ | Related product ID if this optional is another product |
| `tenant_id` | unknown | ❌ | Tenant ID (null for global options) |
| `optional_group_id` | string | ✅ | Parent optional group ID |

### ProductOptionalOptionResponse

**Descrição:** Schema for product optional option responses.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Optional option name |
| `description` | unknown | ❌ | Optional option description |
| `price_adjustment` | string | ❌ | Additional cost for this optional |
| `price_adjustment_type` | string | ❌ | Price adjustment type |
| `display_order` | integer | ❌ | Display order within the group |
| `is_default` | boolean | ❌ | Whether this is the default selection |
| `is_active` | boolean | ❌ | Whether this optional option is active |
| `stock_quantity` | unknown | ❌ | Limited availability for this optional |
| `sku` | unknown | ❌ | Separate SKU for this optional item |
| `image_url` | unknown | ❌ | Image URL for this optional |
| `related_product_id` | unknown | ❌ | Related product ID if this optional is another product |
| `id` | string | ✅ | - |
| `tenant_id` | unknown | ✅ | - |
| `optional_group_id` | string | ✅ | - |

### ProductResponse

**Descrição:** Schema for product responses.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Product name |
| `description` | unknown | ❌ | Product description |
| `short_description` | unknown | ❌ | Short product description |
| `slug` | string | ✅ | URL-friendly product name |
| `sku` | unknown | ❌ | Stock Keeping Unit |
| `category_id` | string | ✅ | Product category ID |
| `product_type` | ProductType | ❌ | Product type |
| `status` | ProductStatus | ❌ | Product status |
| `base_price` | string | ✅ | Base product price |
| `sale_price` | unknown | ❌ | Sale price (discounted) |
| `cost_price` | unknown | ❌ | Cost price for profit calculation |
| `stock_quantity` | integer | ❌ | Current stock quantity |
| `low_stock_threshold` | integer | ❌ | Low stock alert threshold |
| `manage_stock` | boolean | ❌ | Whether to manage stock for this product |
| `allow_backorders` | boolean | ❌ | Allow orders when out of stock |
| `digital_file_url` | unknown | ❌ | Digital file URL |
| `download_limit` | unknown | ❌ | Maximum downloads per purchase |
| `download_expiry_days` | unknown | ❌ | Days until download expires |
| `weight` | unknown | ❌ | Product weight in kg |
| `dimensions` | unknown | ❌ | Product dimensions |
| `shipping_required` | boolean | ❌ | Whether shipping is required |
| `shipping_class` | unknown | ❌ | Shipping class |
| `meta_title` | unknown | ❌ | SEO meta title |
| `meta_description` | unknown | ❌ | SEO meta description |
| `featured_image_url` | unknown | ❌ | Featured image URL |
| `gallery_images` | unknown | ❌ | Gallery image URLs |
| `display_order` | integer | ❌ | Display order |
| `is_featured` | boolean | ❌ | Whether product is featured |
| `is_virtual` | boolean | ❌ | Whether product is virtual (no shipping) |
| `attributes` | unknown | ❌ | Custom product attributes |
| `tags` | unknown | ❌ | Product tags |
| `market_type` | MarketType | ❌ | Market type for B2B/B2C operations |
| `approval_status` | ApprovalStatus | ❌ | Product approval status |
| `commission_rate` | unknown | ❌ | Commission rate percentage |
| `legacy_cuponic_id` | unknown | ❌ | Legacy Cuponic ID for backward compatibility |
| `id` | string | ✅ | - |
| `tenant_id` | unknown | ✅ | - |
| `vendor_id` | string | ✅ | - |
| `average_rating` | string | ✅ | - |
| `review_count` | integer | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `approved_at` | unknown | ❌ | Approval timestamp |
| `approved_by` | unknown | ❌ | Approver user ID |
| `rejection_reason` | unknown | ❌ | Reason for rejection |

### ProductVariantGroupCreate

**Descrição:** Schema for creating a new product variant group.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Variant group name |
| `description` | unknown | ❌ | Variant group description |
| `min_selection` | integer | ❌ | Minimum number of selections required |
| `max_selection` | integer | ❌ | Maximum number of selections allowed |
| `display_order` | integer | ❌ | Display order within the product |
| `is_required` | boolean | ❌ | Whether this variant group is required |
| `is_active` | boolean | ❌ | Whether this variant group is active |
| `requires_default_selection` | boolean | ❌ | Whether a default selection is required |
| `is_template` | boolean | ❌ | Whether this is a template group |
| `template_id` | unknown | ❌ | Reference to template group |
| `tenant_id` | unknown | ❌ | Tenant ID (null for global groups) |

### ProductVariantGroupResponse

**Descrição:** Schema for product variant group responses.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Variant group name |
| `description` | unknown | ❌ | Variant group description |
| `min_selection` | integer | ❌ | Minimum number of selections required |
| `max_selection` | integer | ❌ | Maximum number of selections allowed |
| `display_order` | integer | ❌ | Display order within the product |
| `is_required` | boolean | ❌ | Whether this variant group is required |
| `is_active` | boolean | ❌ | Whether this variant group is active |
| `requires_default_selection` | boolean | ❌ | Whether a default selection is required |
| `is_template` | boolean | ❌ | Whether this is a template group |
| `template_id` | unknown | ❌ | Reference to template group |
| `id` | string | ✅ | - |
| `tenant_id` | unknown | ✅ | - |
| `options` | Array[ProductVariantOptionResponse] | ❌ | Variant options |

### ProductVariantOptionCreate

**Descrição:** Schema for creating a new product variant option.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Variant option name |
| `description` | unknown | ❌ | Variant option description |
| `value` | unknown | ❌ | Machine-readable value |
| `price_adjustment` | unknown | ❌ | Price adjustment from base price |
| `price_adjustment_type` | string | ❌ | Price adjustment type |
| `stock_quantity` | unknown | ❌ | Stock quantity for this variant |
| `sku_suffix` | unknown | ❌ | SKU suffix for this variant |
| `display_order` | integer | ❌ | Display order within the group |
| `is_default` | boolean | ❌ | Whether this is the default selection |
| `is_active` | boolean | ❌ | Whether this variant option is active |
| `color_code` | unknown | ❌ | Hex color code |
| `image_url` | unknown | ❌ | Image URL for this variant |
| `tenant_id` | unknown | ❌ | Tenant ID (null for global options) |
| `variant_group_id` | string | ✅ | Parent variant group ID |

### ProductVariantOptionResponse

**Descrição:** Schema for product variant option responses.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Variant option name |
| `description` | unknown | ❌ | Variant option description |
| `value` | unknown | ❌ | Machine-readable value |
| `price_adjustment` | string | ❌ | Price adjustment from base price |
| `price_adjustment_type` | string | ❌ | Price adjustment type |
| `stock_quantity` | unknown | ❌ | Stock quantity for this variant |
| `sku_suffix` | unknown | ❌ | SKU suffix for this variant |
| `display_order` | integer | ❌ | Display order within the group |
| `is_default` | boolean | ❌ | Whether this is the default selection |
| `is_active` | boolean | ❌ | Whether this variant option is active |
| `color_code` | unknown | ❌ | Hex color code |
| `image_url` | unknown | ❌ | Image URL for this variant |
| `id` | string | ✅ | - |
| `tenant_id` | unknown | ✅ | - |
| `variant_group_id` | string | ✅ | - |

### eshopCategoryCreate

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `slug` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `parent_id` | unknown | ❌ | - |
| `market_type` | MarketType | ❌ | - |

### eshopCategoryRead

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `slug` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `parent_id` | unknown | ❌ | - |
| `market_type` | MarketType | ❌ | - |
| `id` | string | ✅ | - |

### eshopCategoryUpdate

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | - |
| `slug` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `parent_id` | unknown | ❌ | - |

### eshopProductCreate

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `description_short` | unknown | ❌ | - |
| `description_long` | unknown | ❌ | - |
| `base_price` | number | ✅ | - |
| `product_type` | ProductType | ✅ | - |
| `is_active` | boolean | ❌ | - |
| `category_id` | unknown | ❌ | - |
| `supplier_id` | unknown | ❌ | - |

### eshopProductRead

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `description_short` | unknown | ❌ | - |
| `description_long` | unknown | ❌ | - |
| `base_price` | number | ✅ | - |
| `product_type` | ProductType | ✅ | - |
| `is_active` | boolean | ❌ | - |
| `category_id` | unknown | ❌ | - |
| `supplier_id` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `variant_groups` | Array[app__modules__core__eshop__schemas__eshop_product__VariantGroupRead] | ❌ | - |
| `modifier_groups` | Array[app__modules__core__eshop__schemas__eshop_product__ModifierGroupRead] | ❌ | - |
| `optional_groups` | Array[app__modules__core__eshop__schemas__eshop_product__OptionalGroupRead] | ❌ | - |

### eshopProductUpdate

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | - |
| `description_short` | unknown | ❌ | - |
| `description_long` | unknown | ❌ | - |
| `base_price` | unknown | ❌ | - |
| `product_type` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `category_id` | unknown | ❌ | - |
| `supplier_id` | unknown | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/eshop/categories/ {#get-apieshopcategories}

**Resumo:** List Categories
**Descrição:** List all categories, optionally filtered by market type

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `market_type` | string | query | ❌ | Filter categories by market type |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/categories/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/eshop/categories/ {#post-apieshopcategories}

**Resumo:** Create Category

**🔐 Autenticação:** Não requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [eshopCategoryCreate](#eshopcategorycreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [eshopCategoryRead](#eshopcategoryread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/eshop/categories/" \
  -H "Content-Type: application/json" \
  -d '{"example": "data"}'
```

---

### GET /api/eshop/categories/tree {#get-apieshopcategoriestree}

**Resumo:** Read Category Tree
**Descrição:** Retrieve the complete category tree structure.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `is_active` | string | query | ❌ | Filter by active status |
| `market_type` | string | query | ❌ | Filter categories by market type |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/categories/tree" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/eshop/categories/universal {#get-apieshopcategoriesuniversal}

**Resumo:** List Universal Categories
**Descrição:** List all universal categories for admin purposes, optionally filtered by market type

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `market_type` | string | query | ❌ | Filter categories by market type |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/categories/universal"
```

---

### POST /api/eshop/categories/universal {#post-apieshopcategoriesuniversal}

**Resumo:** Create Universal Category
**Descrição:** Create a universal category for admin purposes

**🔐 Autenticação:** Não requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [eshopCategoryCreate](#eshopcategorycreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [eshopCategoryRead](#eshopcategoryread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/eshop/categories/universal" \
  -H "Content-Type: application/json" \
  -d '{"example": "data"}'
```

---

### DELETE /api/eshop/categories/universal/{category_id} {#delete-apieshopcategoriesuniversalcategory-id}

**Resumo:** Delete Universal Category
**Descrição:** Delete a universal category for admin purposes (cascade delete for children)

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/eshop/categories/universal/{category_id}"
```

---

### PUT /api/eshop/categories/universal/{category_id} {#put-apieshopcategoriesuniversalcategory-id}

**Resumo:** Update Universal Category
**Descrição:** Update a universal category for admin purposes

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [eshopCategoryUpdate](#eshopcategoryupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [eshopCategoryRead](#eshopcategoryread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/eshop/categories/universal/{category_id}" \
  -H "Content-Type: application/json" \
  -d '{"example": "data"}'
```

---

### DELETE /api/eshop/categories/{category_id} {#delete-apieshopcategoriescategory-id}

**Resumo:** Delete Category
**Descrição:** Delete a specific category by ID.
Requires admin role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | The ID of the category to delete |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/eshop/categories/{category_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/eshop/categories/{category_id} {#get-apieshopcategoriescategory-id}

**Resumo:** Read Category
**Descrição:** Retrieve a specific category by ID.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | The ID of the category to retrieve |
| `include_children` | boolean | query | ❌ | Include child categories |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductCategoryResponse](#productcategoryresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/categories/{category_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/eshop/categories/{category_id} {#put-apieshopcategoriescategory-id}

**Resumo:** Update Category
**Descrição:** Update a specific category by ID.
Requires admin role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | The ID of the category to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ProductCategoryUpdate](#productcategoryupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductCategoryResponse](#productcategoryresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/eshop/categories/{category_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/eshop/modifiers/groups/ {#get-apieshopmodifiersgroups}

**Resumo:** Read Modifier Groups
**Descrição:** Retrieve modifier groups.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `is_active` | string | query | ❌ | - |
| `include_options` | boolean | query | ❌ | - |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/modifiers/groups/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/eshop/modifiers/groups/ {#post-apieshopmodifiersgroups}

**Resumo:** Create Modifier Group
**Descrição:** Create a new modifier group.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ProductModifierGroupCreate](#productmodifiergroupcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductModifierGroupResponse](#productmodifiergroupresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/eshop/modifiers/groups/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/eshop/modifiers/groups/{group_id} {#get-apieshopmodifiersgroupsgroup-id}

**Resumo:** Read Modifier Group
**Descrição:** Retrieve a specific modifier group.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `group_id` | string | path | ✅ | - |
| `include_options` | boolean | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductModifierGroupResponse](#productmodifiergroupresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/modifiers/groups/{group_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/eshop/modifiers/options/ {#get-apieshopmodifiersoptions}

**Resumo:** Read Modifier Options
**Descrição:** Retrieve modifier options.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `modifier_group_id` | string | query | ❌ | - |
| `is_active` | string | query | ❌ | - |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/modifiers/options/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/eshop/modifiers/options/ {#post-apieshopmodifiersoptions}

**Resumo:** Create Modifier Option
**Descrição:** Create a new modifier option.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ProductModifierOptionCreate](#productmodifieroptioncreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductModifierOptionResponse](#productmodifieroptionresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/eshop/modifiers/options/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/eshop/modifiers/options/{option_id} {#get-apieshopmodifiersoptionsoption-id}

**Resumo:** Read Modifier Option
**Descrição:** Retrieve a specific modifier option.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `option_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductModifierOptionResponse](#productmodifieroptionresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/modifiers/options/{option_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/eshop/optionals/groups/ {#get-apieshopoptionalsgroups}

**Resumo:** Read Optional Groups
**Descrição:** Retrieve optional groups.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `is_active` | string | query | ❌ | - |
| `include_options` | boolean | query | ❌ | - |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/optionals/groups/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/eshop/optionals/groups/ {#post-apieshopoptionalsgroups}

**Resumo:** Create Optional Group
**Descrição:** Create a new optional group.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ProductOptionalGroupCreate](#productoptionalgroupcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductOptionalGroupResponse](#productoptionalgroupresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/eshop/optionals/groups/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/eshop/optionals/groups/{group_id} {#get-apieshopoptionalsgroupsgroup-id}

**Resumo:** Read Optional Group
**Descrição:** Retrieve a specific optional group.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `group_id` | string | path | ✅ | - |
| `include_options` | boolean | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductOptionalGroupResponse](#productoptionalgroupresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/optionals/groups/{group_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/eshop/optionals/options/ {#get-apieshopoptionalsoptions}

**Resumo:** Read Optional Options
**Descrição:** Retrieve optional options.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `optional_group_id` | string | query | ❌ | - |
| `is_active` | string | query | ❌ | - |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/optionals/options/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/eshop/optionals/options/ {#post-apieshopoptionalsoptions}

**Resumo:** Create Optional Option
**Descrição:** Create a new optional option.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ProductOptionalOptionCreate](#productoptionaloptioncreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductOptionalOptionResponse](#productoptionaloptionresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/eshop/optionals/options/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/eshop/optionals/options/{option_id} {#get-apieshopoptionalsoptionsoption-id}

**Resumo:** Read Optional Option
**Descrição:** Retrieve a specific optional option.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `option_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductOptionalOptionResponse](#productoptionaloptionresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/optionals/options/{option_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/eshop/products/ {#get-apieshopproducts}

**Resumo:** List Products
**Descrição:** List products for the current tenant, with optional filters.
Requires 'owner', 'admin', or 'staff' role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `market_type` | string | query | ❌ | - |
| `category_slug` | string | query | ❌ | - |
| `status` | string | query | ❌ | - |
| `min_price` | string | query | ❌ | - |
| `max_price` | string | query | ❌ | - |
| `search` | string | query | ❌ | - |
| `page` | integer | query | ❌ | - |
| `page_size` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/products/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/eshop/products/ {#post-apieshopproducts}

**Resumo:** Create Product
**Descrição:** Creates a new product.

Only users with an **Admin** role or a **TVendorSupplier** association can create products.
The `vendor_id` is automatically set to the current user's ID.
If a tenant is present in the header, the product is associated with that tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [eshopProductCreate](#eshopproductcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [eshopProductRead](#eshopproductread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/eshop/products/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/eshop/products/{product_id} {#delete-apieshopproductsproduct-id}

**Resumo:** Delete Product
**Descrição:** Deletes a product.

Only the product's original vendor or an Admin can delete it.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `product_id` | string | path | ✅ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/eshop/products/{product_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/eshop/products/{product_id} {#get-apieshopproductsproduct-id}

**Resumo:** Get Product

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `product_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [eshopProductRead](#eshopproductread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/products/{product_id}"
```

---

### PUT /api/eshop/products/{product_id} {#put-apieshopproductsproduct-id}

**Resumo:** Update Product
**Descrição:** Updates a product.

Only the product's original vendor or an Admin can update it.
This check is performed within the service layer.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `product_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [eshopProductUpdate](#eshopproductupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [eshopProductRead](#eshopproductread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/eshop/products/{product_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PATCH /api/eshop/products/{product_id}/stock {#patch-apieshopproductsproduct-idstock}

**Resumo:** Update Product Stock
**Descrição:** Update product stock quantity.
Only the product owner (vendor) or admin can update stock.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `product_id` | string | path | ✅ | The ID of the product to update stock |
| `quantity_change` | integer | query | ✅ | Quantity change (positive or negative) |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductResponse](#productresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PATCH "http://localhost:8000/api/eshop/products/{product_id}/stock" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/eshop/variants/groups/ {#get-apieshopvariantsgroups}

**Resumo:** Read Variant Groups
**Descrição:** Retrieve variant groups.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `is_active` | string | query | ❌ | - |
| `is_template` | string | query | ❌ | - |
| `include_options` | boolean | query | ❌ | - |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/variants/groups/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/eshop/variants/groups/ {#post-apieshopvariantsgroups}

**Resumo:** Create Variant Group
**Descrição:** Create a new variant group.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ProductVariantGroupCreate](#productvariantgroupcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductVariantGroupResponse](#productvariantgroupresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/eshop/variants/groups/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/eshop/variants/groups/{group_id} {#get-apieshopvariantsgroupsgroup-id}

**Resumo:** Read Variant Group
**Descrição:** Retrieve a specific variant group.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `group_id` | string | path | ✅ | - |
| `include_options` | boolean | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductVariantGroupResponse](#productvariantgroupresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/variants/groups/{group_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/eshop/variants/options/ {#get-apieshopvariantsoptions}

**Resumo:** Read Variant Options
**Descrição:** Retrieve variant options.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `variant_group_id` | string | query | ❌ | - |
| `is_active` | string | query | ❌ | - |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/variants/options/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/eshop/variants/options/ {#post-apieshopvariantsoptions}

**Resumo:** Create Variant Option
**Descrição:** Create a new variant option.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ProductVariantOptionCreate](#productvariantoptioncreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductVariantOptionResponse](#productvariantoptionresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/eshop/variants/options/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/eshop/variants/options/{option_id} {#get-apieshopvariantsoptionsoption-id}

**Resumo:** Read Variant Option
**Descrição:** Retrieve a specific variant option.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `option_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductVariantOptionResponse](#productvariantoptionresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/variants/options/{option_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
