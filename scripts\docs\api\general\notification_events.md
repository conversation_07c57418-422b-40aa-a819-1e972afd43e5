# General - Notification Events

**Categoria:** General
**Mó<PERSON><PERSON>:** Notification Events
**Total de Endpoints:** 9
**Gerado em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [POST /api/modules/core/notifications/events/auctions/end](#post-apimodulescorenotificationseventsauctionsend) - Trigger Auction End
- [POST /api/modules/core/notifications/events/auctions/new-bid](#post-apimodulescorenotificationseventsauctionsnew-bid) - Trigger Auction Bid
- [POST /api/modules/core/notifications/events/b2b/approval-request](#post-apimodulescorenotificationseventsb2bapproval-request) - Trigger B2B Approval Request
- [POST /api/modules/core/notifications/events/b2b/approval-status](#post-apimodulescorenotificationseventsb2bapproval-status) - Trigger B2B Approval Status
- [POST /api/modules/core/notifications/events/email/test](#post-apimodulescorenotificationseventsemailtest) - Send Test Email
- [POST /api/modules/core/notifications/events/invoices/generated](#post-apimodulescorenotificationseventsinvoicesgenerated) - Trigger Invoice Generated
- [POST /api/modules/core/notifications/events/orders/new-order](#post-apimodulescorenotificationseventsordersnew-order) - Trigger New Order
- [POST /api/modules/core/notifications/events/orders/status-change](#post-apimodulescorenotificationseventsordersstatus-change) - Trigger Order Status Change
- [POST /api/modules/core/notifications/events/system/maintenance](#post-apimodulescorenotificationseventssystemmaintenance) - Trigger System Maintenance

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

## 🔗 Endpoints Detalhados

### POST /api/modules/core/notifications/events/auctions/end {#post-apimodulescorenotificationseventsauctionsend}

**Resumo:** Trigger Auction End
**Descrição:** Dispara notificação de fim de leilão.

Args:
    auction_id: ID do leilão
    winner_id: ID do vencedor
    winning_bid: Lance vencedor

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `auction_id` | string | query | ✅ | - |
| `winner_id` | string | query | ❌ | - |
| `winning_bid` | string | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/events/auctions/end" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/notifications/events/auctions/new-bid {#post-apimodulescorenotificationseventsauctionsnew-bid}

**Resumo:** Trigger Auction Bid
**Descrição:** Dispara notificação de novo lance em leilão.

Args:
    auction_id: ID do leilão
    bidder_id: ID do licitante
    bid_amount: Valor do lance
    previous_bidder_id: ID do licitante anterior

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `auction_id` | string | query | ✅ | - |
| `bidder_id` | string | query | ✅ | - |
| `bid_amount` | number | query | ✅ | - |
| `previous_bidder_id` | string | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/events/auctions/new-bid" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/notifications/events/b2b/approval-request {#post-apimodulescorenotificationseventsb2bapproval-request}

**Resumo:** Trigger B2B Approval Request
**Descrição:** Dispara notificação de solicitação de aprovação B2B.

Args:
    user_id: ID do usuário solicitante
    user_type: Tipo de aprovação (tcustomer/tvendor_supplier)
    tenant_id: ID do tenant (opcional)

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `user_id` | string | query | ✅ | - |
| `user_type` | string | query | ✅ | - |
| `tenant_id` | string | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/events/b2b/approval-request" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/notifications/events/b2b/approval-status {#post-apimodulescorenotificationseventsb2bapproval-status}

**Resumo:** Trigger B2B Approval Status
**Descrição:** Dispara notificação de mudança de status de aprovação B2B.

Args:
    user_id: ID do usuário
    user_type: Tipo de aprovação
    approval_status: Status (approved/rejected)
    reason: Motivo (se rejeitado)

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `user_id` | string | query | ✅ | - |
| `user_type` | string | query | ✅ | - |
| `approval_status` | string | query | ✅ | - |
| `reason` | string | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/events/b2b/approval-status" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/notifications/events/email/test {#post-apimodulescorenotificationseventsemailtest}

**Resumo:** Send Test Email
**Descrição:** Envia email de teste.

Args:
    to_email: Email de destino

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `to_email` | string | query | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/events/email/test" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/notifications/events/invoices/generated {#post-apimodulescorenotificationseventsinvoicesgenerated}

**Resumo:** Trigger Invoice Generated
**Descrição:** Dispara notificação de fatura gerada.

Args:
    invoice_id: ID da fatura
    customer_id: ID do cliente
    tenant_id: ID do tenant
    invoice_total: Valor da fatura

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `invoice_id` | string | query | ✅ | - |
| `customer_id` | string | query | ✅ | - |
| `tenant_id` | string | query | ✅ | - |
| `invoice_total` | number | query | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/events/invoices/generated" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/notifications/events/orders/new-order {#post-apimodulescorenotificationseventsordersnew-order}

**Resumo:** Trigger New Order
**Descrição:** Dispara notificação de novo pedido.

Args:
    order_id: ID do pedido
    customer_id: ID do cliente
    tenant_id: ID do tenant
    order_total: Valor total do pedido

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `order_id` | string | query | ✅ | - |
| `customer_id` | string | query | ✅ | - |
| `tenant_id` | string | query | ✅ | - |
| `order_total` | number | query | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/events/orders/new-order" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/notifications/events/orders/status-change {#post-apimodulescorenotificationseventsordersstatus-change}

**Resumo:** Trigger Order Status Change
**Descrição:** Dispara notificação de mudança de status de pedido.

Args:
    order_id: ID do pedido
    customer_id: ID do cliente
    old_status: Status anterior
    new_status: Novo status
    tenant_id: ID do tenant

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `order_id` | string | query | ✅ | - |
| `customer_id` | string | query | ✅ | - |
| `old_status` | string | query | ✅ | - |
| `new_status` | string | query | ✅ | - |
| `tenant_id` | string | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/events/orders/status-change" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/notifications/events/system/maintenance {#post-apimodulescorenotificationseventssystemmaintenance}

**Resumo:** Trigger System Maintenance
**Descrição:** Dispara notificação de manutenção do sistema.

Args:
    title: Título da manutenção
    message: Mensagem detalhada
    start_time: Horário de início
    end_time: Horário de fim

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `title` | string | query | ✅ | - |
| `message` | string | query | ✅ | - |
| `start_time` | string | query | ✅ | - |
| `end_time` | string | query | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/events/system/maintenance" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
