from __future__ import annotations
import uuid
from typing import TYPE_CHECKING, Any, cast, List, Optional
from sqlalchemy import <PERSON>umn, String, ForeignKey, JSON, DateTime, func, Index
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.orm.attributes import flag_modified

from app.db.base import Base

if TYPE_CHECKING:
    from app.modules.core.users.models.user import User
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.shared.financial.orders.models.order import Order


class KitchenOrder(Base):
    __tablename__ = "kitchen_orders"

    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    order_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("orders.id", ondelete="CASCADE"), unique=True)
    tenant_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), <PERSON><PERSON><PERSON>("tenants.id", ondelete="CASCADE"))
    creator_user_id: Mapped[Optional[uuid.UUID]] = mapped_column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    order_details: Mapped[dict[str, Any]] = mapped_column(JSON, nullable=False)
    status: Mapped[str] = mapped_column(String, default="pending", nullable=False)
    source_sale_id: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    source_online_order_id: Mapped[Optional[uuid.UUID]] = mapped_column(UUID(as_uuid=True), ForeignKey("online_orders.id", name="fk_kitchen_order_online_order", use_alter=True), nullable=True)
    source_order_id: Mapped[Optional[uuid.UUID]] = mapped_column(UUID(as_uuid=True), ForeignKey("orders.id", name="fk_kitchen_order_order", use_alter=True), nullable=True)
    created_at: Mapped[DateTime] = mapped_column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at: Mapped[DateTime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    tenant: Mapped["Tenant"] = relationship(back_populates="kitchen_orders")
    creator_user: Mapped[Optional["User"]] = relationship("User")
    order: Mapped["Order"] = relationship("Order", foreign_keys=[order_id], back_populates="kitchen_order")

    __table_args__ = (
        Index("ix_kitchen_orders_tenant_id", "tenant_id"),
        Index("ix_kitchen_orders_status", "status"),
        Index("ix_kitchen_orders_source_sale_id", "source_sale_id"),
        Index("ix_kitchen_orders_source_online_order_id", "source_online_order_id"),
        Index("ix_kitchen_orders_source_order_id", "source_order_id"),
        Index("ix_kitchen_orders_creator_user_id", "creator_user_id"),
    )

    def __repr__(self) -> str:
        return f"<KitchenOrder(id={self.id}, tenant_id={self.tenant_id}, status='{self.status}')>"

    @property
    def items(self) -> list[dict[str, Any]]:
        return cast(list[dict[str, Any]], self.order_details.get("items", []))

    def update_item_status(self, item_id: str, done: bool) -> bool:
        items = self.items
        item_found = False
        for item in items:
            if item.get("id") == item_id:
                item["done"] = done
                item_found = True
                break
        if item_found:
            self.order_details["items"] = items
            flag_modified(self, "order_details")
            return True
        return False
