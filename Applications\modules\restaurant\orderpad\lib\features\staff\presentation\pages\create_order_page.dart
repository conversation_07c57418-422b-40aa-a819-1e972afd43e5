import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/models/menu_model.dart';
import '../../../../core/models/menu_item_model.dart';
import '../../../../core/models/order_model.dart';
import '../../../../core/models/table_model.dart';
import '../../../../core/providers/menu_provider.dart';
import '../../../../core/providers/orders_provider.dart';
import '../../../../core/providers/tables_provider.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../../../shared/widgets/loading_overlay.dart';

class CreateOrderPage extends ConsumerStatefulWidget {
  final String? tableId;
  final String? orderType;
  
  const CreateOrderPage({
    super.key,
    this.tableId,
    this.orderType,
  });

  @override
  ConsumerState<CreateOrderPage> createState() => _CreateOrderPageState();
}

class _CreateOrderPageState extends ConsumerState<CreateOrderPage>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  final _formKey = GlobalKey<FormState>();
  final _customerNameController = TextEditingController();
  final _customerPhoneController = TextEditingController();
  final _notesController = TextEditingController();
  final _searchController = TextEditingController();
  
  String _orderType = OrderType.dineIn.toString().split('.').last;
  String? _selectedTableId;
  String _selectedCategory = 'Todos';
  String _paymentMethod = 'cash';
  
  final List<OrderItem> _orderItems = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _selectedTableId = widget.tableId;
    if (widget.orderType != null) {
      _orderType = widget.orderType!;
    }
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _customerNameController.dispose();
    _customerPhoneController.dispose();
    _notesController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.colorScheme.background,
      appBar: AppBar(
        title: const Text('Novo Pedido'),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        actions: [
          if (_orderItems.isNotEmpty)
            TextButton.icon(
              onPressed: _clearOrder,
              icon: const Icon(Icons.clear_all),
              label: const Text('Limpar'),
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: Column(
                  children: [
                    Expanded(
                      child: Row(
                        children: [
                          // Menu items section
                          Expanded(
                            flex: 3,
                            child: _buildMenuSection(theme),
                          ),
                          
                          // Order summary section
                          Container(
                            width: 400,
                            decoration: BoxDecoration(
                              color: theme.colorScheme.surface,
                              boxShadow: [
                                BoxShadow(
                                  color: theme.colorScheme.shadow.withValues(alpha: 0.1),
                                  blurRadius: 8,
                                  offset: const Offset(-2, 0),
                                ),
                              ],
                            ),
                            child: _buildOrderSummarySection(theme),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildMenuSection(ThemeData theme) {
    final menuState = ref.watch(menuProvider);
    
    return Column(
      children: [
        // Search and filters
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            boxShadow: [
              BoxShadow(
                color: theme.colorScheme.shadow.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              SearchTextField(
                controller: _searchController,
                hint: 'Buscar itens do menu...',
                onChanged: (value) => setState(() {}),
              ),
              
              const SizedBox(height: 12),
              
              // Category filter
              SizedBox(
                height: 40,
                child: ListView(
                  scrollDirection: Axis.horizontal,
                  children: [
                    _buildCategoryChip('Todos', theme),
                    ...menuState.categories.map(
                      (category) => _buildCategoryChip(category, theme),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        
        // Menu items grid
        Expanded(
          child: menuState.isLoading
              ? const Center(child: CircularProgressIndicator())
              : Consumer(
                  builder: (context, ref, child) {
                    final filteredItems = ref.watch(filteredMenuItemsProvider);
                    return _buildMenuGrid(filteredItems, theme);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildCategoryChip(String category, ThemeData theme) {
    final isSelected = _selectedCategory == category;
    
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(category),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _selectedCategory = category;
          });
        },
        backgroundColor: theme.colorScheme.surface,
        selectedColor: theme.colorScheme.primary.withValues(alpha: 0.2),
        checkmarkColor: theme.colorScheme.primary,
        labelStyle: TextStyle(
          color: isSelected
              ? theme.colorScheme.primary
              : theme.colorScheme.onSurface,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
    );
  }

  Widget _buildMenuGrid(List<MenuItemModel> items, ThemeData theme) {
    final filteredItems = _getFilteredItems(items);
    
    if (filteredItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            const SizedBox(height: 16),
            Text(
              'Nenhum item encontrado',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
          ],
        ),
      );
    }
    
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 0.8,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: filteredItems.length,
      itemBuilder: (context, index) {
        final item = filteredItems[index];
        return _buildMenuItemCard(item, theme);
      },
    );
  }

  Widget _buildMenuItemCard(MenuItemModel item, ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: item.isAvailable ? () => _addItemToOrder(item) : null,
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Item image placeholder
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: item.isAvailable
                      ? theme.colorScheme.primaryContainer.withValues(alpha: 0.3)
                      : theme.colorScheme.onSurface.withValues(alpha: 0.1),
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(12),
                  ),
                ),
                child: Stack(
                  children: [
                    Center(
                      child: Icon(
                        Icons.restaurant,
                        size: 48,
                        color: item.isAvailable
                            ? theme.colorScheme.primary.withValues(alpha: 0.6)
                            : theme.colorScheme.onSurface.withValues(alpha: 0.3),
                      ),
                    ),
                    
                    if (!item.isAvailable)
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            'Indisponível',
                            style: theme.textTheme.labelSmall?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            
            // Item details
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.name,
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: item.isAvailable
                            ? theme.colorScheme.onSurface
                            : theme.colorScheme.onSurface.withValues(alpha: 0.5),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const SizedBox(height: 4),
                    
                    Text(
                      'R\$ ${item.price.toStringAsFixed(2)}',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: item.isAvailable
                            ? theme.colorScheme.primary
                            : theme.colorScheme.onSurface.withValues(alpha: 0.5),
                      ),
                    ),
                    
                    if (item.description.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        item.description,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderSummarySection(ThemeData theme) {
    return Column(
      children: [
        // Order type and table selection
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: theme.colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Tipo do Pedido',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),
              
              _buildOrderTypeSelector(theme),
              
              if (_orderType == OrderType.dineIn) ...[
                const SizedBox(height: 16),
                _buildTableSelector(theme),
              ],
            ],
          ),
        ),
        
        // Order items
        Expanded(
          child: _orderItems.isEmpty
              ? _buildEmptyOrderState(theme)
              : _buildOrderItemsList(theme),
        ),
        
        // Order total and actions
        _buildOrderFooter(theme),
      ],
    );
  }

  Widget _buildOrderTypeSelector(ThemeData theme) {
    return Row(
      children: [
        Expanded(
          child: _buildOrderTypeChip(
            OrderType.dineIn.toString().split('.').last,
            'Mesa',
            Icons.restaurant,
            theme,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildOrderTypeChip(
            OrderType.takeaway.toString().split('.').last,
            'Retirada',
            Icons.shopping_bag,
            theme,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildOrderTypeChip(
            OrderType.delivery.toString().split('.').last,
            'Entrega',
            Icons.delivery_dining,
            theme,
          ),
        ),
      ],
    );
  }

  Widget _buildOrderTypeChip(
    String type,
    String label,
    IconData icon,
    ThemeData theme,
  ) {
    final isSelected = _orderType == type;
    
    return GestureDetector(
      onTap: () => setState(() => _orderType = type),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 8,
        ),
        decoration: BoxDecoration(
          color: isSelected
              ? theme.colorScheme.primary
              : theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? theme.colorScheme.primary
                : theme.colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 20,
              color: isSelected
                  ? theme.colorScheme.onPrimary
                  : theme.colorScheme.onSurface,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: theme.textTheme.labelSmall?.copyWith(
                color: isSelected
                    ? theme.colorScheme.onPrimary
                    : theme.colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTableSelector(ThemeData theme) {
    final tablesState = ref.watch(tablesProvider);
    final availableTables = tablesState.tables
        .where((table) => table.status == 'available')
        .toList();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Mesa',
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        
        DropdownButtonFormField<String>(
          value: _selectedTableId,
          decoration: InputDecoration(
            hintText: 'Selecione uma mesa',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 8,
            ),
          ),
          items: availableTables.map((table) {
            return DropdownMenuItem(
              value: table.id,
              child: Text('Mesa ${table.number} - ${table.name}'),
            );
          }).toList(),
          onChanged: (value) => setState(() => _selectedTableId = value),
        ),
      ],
    );
  }

  Widget _buildEmptyOrderState(ThemeData theme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.shopping_cart_outlined,
              size: 64,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            const SizedBox(height: 16),
            Text(
              'Nenhum item adicionado',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Selecione itens do menu para adicionar ao pedido',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderItemsList(ThemeData theme) {
    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: _orderItems.length,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        final item = _orderItems[index];
        return _buildOrderItemCard(item, index, theme);
      },
    );
  }

  Widget _buildOrderItemCard(OrderItem item, int index, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.background,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  item.menuItem.name,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close, size: 20),
                onPressed: () => _removeItemFromOrder(index),
                color: theme.colorScheme.error,
                constraints: const BoxConstraints(
                  minWidth: 32,
                  minHeight: 32,
                ),
                padding: EdgeInsets.zero,
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          Row(
            children: [
              // Quantity controls
              Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: theme.colorScheme.outline.withValues(alpha: 0.3),
                  ),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.remove, size: 16),
                      onPressed: item.quantity > 1
                          ? () => _updateItemQuantity(index, item.quantity - 1)
                          : null,
                      constraints: const BoxConstraints(
                        minWidth: 32,
                        minHeight: 32,
                      ),
                      padding: EdgeInsets.zero,
                    ),
                    Container(
                      width: 40,
                      alignment: Alignment.center,
                      child: Text(
                        '${item.quantity}',
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.add, size: 16),
                      onPressed: () => _updateItemQuantity(index, item.quantity + 1),
                      constraints: const BoxConstraints(
                        minWidth: 32,
                        minHeight: 32,
                      ),
                      padding: EdgeInsets.zero,
                    ),
                  ],
                ),
              ),
              
              const Spacer(),
              
              // Price
              Text(
                'R\$ ${item.totalPrice.toStringAsFixed(2)}',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOrderFooter(ThemeData theme) {
    final subtotal = _calculateSubtotal();
    final taxAmount = subtotal * 0.1; // 10% tax
    final total = subtotal + taxAmount;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Column(
        children: [
          // Order summary
          Row(
            children: [
              Text(
                'Subtotal',
                style: theme.textTheme.bodyMedium,
              ),
              const Spacer(),
              Text(
                'R\$ ${subtotal.toStringAsFixed(2)}',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 4),
          
          Row(
            children: [
              Text(
                'Taxas',
                style: theme.textTheme.bodyMedium,
              ),
              const Spacer(),
              Text(
                'R\$ ${taxAmount.toStringAsFixed(2)}',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          
          const Divider(height: 16),
          
          Row(
            children: [
              Text(
                'Total',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Text(
                'R\$ ${total.toStringAsFixed(2)}',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Action buttons
          Row(
            children: [
              Expanded(
                child: OutlineButton(
                  onPressed: _orderItems.isNotEmpty ? _showOrderDetailsDialog : null,
                  text: 'Detalhes',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                flex: 2,
                child: PrimaryButton(
                  onPressed: _orderItems.isNotEmpty ? _createOrder : null,
                  text: 'Criar Pedido',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Helper methods
  List<MenuItemModel> _getFilteredItems(List<MenuItemModel> items) {
    var filtered = items.where((item) => item.isAvailable).toList();
    
    // Filter by category
    if (_selectedCategory != 'Todos') {
      filtered = filtered.where((item) => item.category == _selectedCategory).toList();
    }
    
    // Filter by search
    if (_searchController.text.isNotEmpty) {
      final searchTerm = _searchController.text.toLowerCase();
      filtered = filtered.where((item) {
        return item.name.toLowerCase().contains(searchTerm) ||
               item.description.toLowerCase().contains(searchTerm);
      }).toList();
    }
    
    return filtered;
  }

  double _calculateSubtotal() {
    return _orderItems.fold(0.0, (sum, item) => sum + item.totalPrice);
  }

  // Action methods
  void _addItemToOrder(MenuItemModel menuItem) {
    final existingIndex = _orderItems.indexWhere(
      (item) => item.menuItemId == menuItem.id,
    );
    
    if (existingIndex >= 0) {
      _updateItemQuantity(existingIndex, _orderItems[existingIndex].quantity + 1);
    } else {
      setState(() {
        _orderItems.add(OrderItem(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: menuItem.name,
          price: menuItem.price,
          quantity: 1,
          menuItemId: menuItem.id,
          unitPrice: menuItem.price,
          selectedModifiers: [],
        ));
      });
    }
  }

  void _removeItemFromOrder(int index) {
    setState(() {
      _orderItems.removeAt(index);
    });
  }

  void _updateItemQuantity(int index, int newQuantity) {
    if (newQuantity <= 0) {
      _removeItemFromOrder(index);
      return;
    }
    
    setState(() {
      _orderItems[index] = _orderItems[index].copyWith(quantity: newQuantity);
    });
  }

  void _clearOrder() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Limpar Pedido'),
        content: const Text(
          'Tem certeza que deseja remover todos os itens do pedido?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancelar'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _orderItems.clear();
              });
              Navigator.pop(context);
            },
            child: Text(
              'Limpar',
              style: TextStyle(
                color: Theme.of(context).colorScheme.error,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showOrderDetailsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Detalhes do Pedido'),
        content: SizedBox(
          width: 400,
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (_orderType != OrderType.dineIn.toString().split('.').last) ...[
                  CustomTextField(
                    controller: _customerNameController,
                    label: 'Nome do Cliente',
                    validator: (value) {
                      if (_orderType != OrderType.dineIn.toString().split('.').last && 
                          (value?.isEmpty ?? true)) {
                        return 'Nome é obrigatório';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  PhoneTextField(
                    controller: _customerPhoneController,
                    label: 'Telefone do Cliente',
                    validator: (value) {
                      if (_orderType == OrderType.delivery.toString().split('.').last && 
                          (value?.isEmpty ?? true)) {
                        return 'Telefone é obrigatório para entrega';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                ],
                
                CustomTextField(
                  controller: _notesController,
                  label: 'Observações',
                  maxLines: 3,
                ),
                
                const SizedBox(height: 16),
                
                DropdownButtonFormField<String>(
                  value: _paymentMethod,
                  decoration: const InputDecoration(
                    labelText: 'Método de Pagamento',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(
                      value: 'cash',
                      child: Text('Dinheiro'),
                    ),
                    DropdownMenuItem(
                      value: 'credit_card',
                      child: Text('Cartão de Crédito'),
                    ),
                    DropdownMenuItem(
                      value: 'debit_card',
                      child: Text('Cartão de Débito'),
                    ),
                    DropdownMenuItem(
                      value: 'pix',
                      child: Text('PIX'),
                    ),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      setState(() => _paymentMethod = value);
                    }
                  },
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancelar'),
          ),
          PrimaryButton(
            onPressed: () {
              Navigator.pop(context);
              _createOrder();
            },
            text: 'Criar Pedido',
          ),
        ],
      ),
    );
  }

  Future<void> _createOrder() async {
    if (_orderType == OrderType.dineIn.toString().split('.').last && _selectedTableId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Selecione uma mesa para pedidos no local'),
        ),
      );
      return;
    }
    
    setState(() => _isLoading = true);
    
    try {
      final subtotal = _calculateSubtotal();
      final taxAmount = subtotal * 0.1;
      final total = subtotal + taxAmount;
      
      await ref.read(ordersProvider.notifier).createOrder(
        orderType: _orderType,
        waiterId: 'current_waiter_id', // TODO: Get from auth
        waiterName: 'Garçom Atual', // TODO: Get from auth
        tableId: _selectedTableId,
        tableName: _selectedTableId != null
            ? ref.read(tablesProvider).tables
                .firstWhere((t) => t.id == _selectedTableId)
                .name
            : null,
        customerName: _customerNameController.text.isNotEmpty
            ? _customerNameController.text
            : null,
        notes: _notesController.text.isNotEmpty
            ? _notesController.text
            : null,
      );
      
      // Update table status if dine-in
      if (_orderType == OrderType.dineIn.toString().split('.').last && _selectedTableId != null) {
        await ref.read(tablesProvider.notifier).updateTableStatus(
          _selectedTableId!,
          'occupied',
        );
      }
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Pedido criado com sucesso!'),
            backgroundColor: Colors.green,
          ),
        );
        
        context.pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao criar pedido: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}