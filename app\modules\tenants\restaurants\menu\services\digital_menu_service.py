"""
Service for Digital Menu operations.
Handles multi-menu functionality with time-based scheduling.
"""

import logging
from typing import Optional, Sequence
from sqlalchemy import select, text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from sqlalchemy.exc import IntegrityError
from fastapi import HTTPException, status
import uuid
from datetime import datetime, time
from zoneinfo import ZoneInfo

# Import models
from app.modules.tenants.restaurants.menu.models.digital_menu import DigitalMenu
from app.modules.tenants.restaurants.menu.models.menu_category import MenuCategory

# Import schemas
from app.modules.tenants.restaurants.menu.schemas.digital_menu import (
    DigitalMenuCreate,
    DigitalMenuUpdate,
    MenuScheduleConfig,
)

logger = logging.getLogger(__name__)


class DigitalMenuService:
    def __init__(self, db_session: AsyncSession):
        self.db = db_session

    async def create_digital_menu(
        self, menu_in: DigitalMenuCreate, tenant_id: uuid.UUID
    ) -> DigitalMenu:
        """
        Creates a new digital menu for the given tenant.
        """
        try:
            db_menu = DigitalMenu(**menu_in.model_dump(), tenant_id=tenant_id)
            self.db.add(db_menu)
            await self.db.commit()
            await self.db.refresh(db_menu)
            logger.info(f"Digital menu created: {db_menu.id} for tenant {tenant_id}")
            return db_menu
        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"Error creating digital menu for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Error creating digital menu.",
            )
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Unexpected error creating digital menu for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def get_digital_menu(
        self,
        menu_id: uuid.UUID,
        tenant_id: uuid.UUID,
        include_categories: bool = True,
    ) -> Optional[DigitalMenu]:
        """
        Gets a specific digital menu by ID for the given tenant.
        """
        stmt = select(DigitalMenu).where(
            DigitalMenu.id == menu_id,
            DigitalMenu.tenant_id == tenant_id,
            DigitalMenu.is_active,
        )

        if include_categories:
            stmt = stmt.options(
                selectinload(DigitalMenu.categories)
                .selectinload(MenuCategory.children)
            )

        result = await self.db.execute(stmt)
        return result.scalars().first()

    async def get_digital_menus(
        self,
        tenant_id: uuid.UUID,
        include_categories: bool = False,
        skip: int = 0,
        limit: int = 100,
    ) -> Sequence[DigitalMenu]:
        """
        Gets a list of digital menus for the given tenant.
        """
        stmt = select(DigitalMenu).where(
            DigitalMenu.tenant_id == tenant_id,
            DigitalMenu.is_active,
        ).order_by(DigitalMenu.display_order, DigitalMenu.name).offset(skip).limit(limit)

        if include_categories:
            stmt = stmt.options(
                selectinload(DigitalMenu.categories)
                .selectinload(MenuCategory.children)
            )

        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_active_menu_for_time(
        self,
        tenant_id: uuid.UUID,
        current_time: Optional[datetime] = None,
        timezone_str: str = "UTC",
    ) -> Optional[DigitalMenu]:
        """
        Gets the active menu for the current time based on scheduling.
        If no scheduled menu is active, returns the default menu (first active menu).
        """
        if current_time is None:
            current_time = datetime.utcnow()

        # Convert to specified timezone
        try:
            tz = ZoneInfo(timezone_str)
            local_time = current_time.replace(tzinfo=ZoneInfo('UTC')).astimezone(tz)
        except Exception:
            # Fallback to UTC if timezone is invalid
            local_time = current_time.replace(tzinfo=ZoneInfo('UTC'))

        # Get all active menus with scheduling enabled
        stmt = select(DigitalMenu).where(
            DigitalMenu.tenant_id == tenant_id,
            DigitalMenu.is_active,
            DigitalMenu.schedule_enabled,
        ).order_by(DigitalMenu.display_order)

        result = await self.db.execute(stmt)
        scheduled_menus = result.scalars().all()

        # Check each menu's schedule
        for menu in scheduled_menus:
            if await self._is_menu_active_at_time(menu, local_time):
                return menu

        # If no scheduled menu is active, return the default menu
        default_stmt = select(DigitalMenu).where(
            DigitalMenu.tenant_id == tenant_id,
            DigitalMenu.is_active,
        ).order_by(DigitalMenu.display_order).limit(1)

        default_result = await self.db.execute(default_stmt)
        return default_result.scalars().first()

    async def _is_menu_active_at_time(self, menu: DigitalMenu, local_time: datetime) -> bool:
        """
        Checks if a menu is active at the given local time based on its schedule.
        """
        if not menu.schedule_enabled or not menu.schedule_config:
            return False

        schedule = menu.schedule_config
        current_day = local_time.strftime('%A').lower()
        current_time = local_time.time()

        # Find the day configuration
        for day_config in schedule.get('days', []):
            if day_config.get('day', '').lower() == current_day and day_config.get('enabled', False):
                # Check time ranges for this day
                for time_range in day_config.get('time_ranges', []):
                    start_time = time.fromisoformat(time_range.get('start_time', '00:00'))
                    end_time = time.fromisoformat(time_range.get('end_time', '23:59'))

                    if start_time <= current_time <= end_time:
                        return True

        return False

    async def update_digital_menu(
        self,
        menu_id: uuid.UUID,
        menu_in: DigitalMenuUpdate,
        tenant_id: uuid.UUID,
    ) -> Optional[DigitalMenu]:
        """
        Updates an existing digital menu.
        """
        db_menu = await self.get_digital_menu(menu_id, tenant_id, include_categories=False)
        if not db_menu:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Digital menu with ID {menu_id} not found",
            )

        update_data = menu_in.model_dump(exclude_unset=True)
        if not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No update data provided",
            )

        try:
            for key, value in update_data.items():
                setattr(db_menu, key, value)

            self.db.add(db_menu)
            await self.db.commit()
            await self.db.refresh(db_menu)
            logger.info(f"Digital menu updated: {menu_id} for tenant {tenant_id}")
            return db_menu

        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"Error updating digital menu {menu_id} for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Error updating digital menu.",
            )
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Unexpected error updating digital menu {menu_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def delete_digital_menu(self, menu_id: uuid.UUID, tenant_id: uuid.UUID) -> DigitalMenu:
        """
        Hard deletes a digital menu and all related data via cascade.
        This will delete all categories, items, variants, modifiers, and optionals.
        """
        db_menu = await self.get_digital_menu(menu_id, tenant_id, include_categories=False)
        if not db_menu:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Digital menu with ID {menu_id} not found",
            )

        try:
            # Store menu data for return before deletion
            menu_data = DigitalMenu(
                id=db_menu.id,
                tenant_id=db_menu.tenant_id,
                name=db_menu.name,
                description=db_menu.description,
                is_active=db_menu.is_active,
                display_order=db_menu.display_order,
                schedule_enabled=db_menu.schedule_enabled,
                schedule_config=db_menu.schedule_config,
                created_at=db_menu.created_at,
                updated_at=db_menu.updated_at
            )

            # Hard delete - this will trigger cascade delete for all related data
            await self.db.delete(db_menu)

            # Clean up orphaned groups that are no longer associated with any menu items
            await self._cleanup_orphaned_groups(tenant_id)

            await self.db.commit()
            logger.info(f"Digital menu hard deleted with cascade and orphaned groups cleaned: {menu_id} for tenant {tenant_id}")
            return menu_data

        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Error hard deleting digital menu {menu_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def _cleanup_orphaned_groups(self, tenant_id: uuid.UUID) -> None:
        """
        Clean up orphaned variant, modifier, and optional groups that are no longer
        associated with any menu items for the given tenant.
        """
        try:
            # Use direct SQL for efficiency - delete options first, then groups

            # Step 1: Delete orphaned variant options (from groups that have no associations)
            await self.db.execute(text("""
                DELETE FROM variant_options
                WHERE variant_group_id IN (
                    SELECT id FROM variant_groups
                    WHERE tenant_id = :tenant_id
                    AND id NOT IN (
                        SELECT DISTINCT variant_group_id
                        FROM menu_item_variant_groups
                        WHERE variant_group_id IS NOT NULL
                    )
                )
            """), {"tenant_id": tenant_id})

            # Step 2: Delete orphaned modifier options
            await self.db.execute(text("""
                DELETE FROM modifier_options
                WHERE modifier_group_id IN (
                    SELECT id FROM modifier_groups
                    WHERE tenant_id = :tenant_id
                    AND id NOT IN (
                        SELECT DISTINCT modifier_group_id
                        FROM menu_item_modifier_groups
                        WHERE modifier_group_id IS NOT NULL
                    )
                )
            """), {"tenant_id": tenant_id})

            # Step 3: Delete orphaned optional options
            await self.db.execute(text("""
                DELETE FROM optional_options
                WHERE optional_group_id IN (
                    SELECT id FROM optional_groups
                    WHERE tenant_id = :tenant_id
                    AND id NOT IN (
                        SELECT DISTINCT optional_group_id
                        FROM menu_item_optional_groups
                        WHERE optional_group_id IS NOT NULL
                    )
                )
            """), {"tenant_id": tenant_id})

            # Step 4: Now delete the orphaned groups (after their options are gone)
            await self.db.execute(text("""
                DELETE FROM variant_groups
                WHERE tenant_id = :tenant_id
                AND id NOT IN (
                    SELECT DISTINCT variant_group_id
                    FROM menu_item_variant_groups
                    WHERE variant_group_id IS NOT NULL
                )
            """), {"tenant_id": tenant_id})

            await self.db.execute(text("""
                DELETE FROM modifier_groups
                WHERE tenant_id = :tenant_id
                AND id NOT IN (
                    SELECT DISTINCT modifier_group_id
                    FROM menu_item_modifier_groups
                    WHERE modifier_group_id IS NOT NULL
                )
            """), {"tenant_id": tenant_id})

            await self.db.execute(text("""
                DELETE FROM optional_groups
                WHERE tenant_id = :tenant_id
                AND id NOT IN (
                    SELECT DISTINCT optional_group_id
                    FROM menu_item_optional_groups
                    WHERE optional_group_id IS NOT NULL
                )
            """), {"tenant_id": tenant_id})

            logger.info(f"Cleaned up orphaned groups for tenant {tenant_id}")

        except Exception as e:
            logger.exception(f"Error cleaning up orphaned groups for tenant {tenant_id}: {e}")
            # Don't raise here as this is cleanup - the main delete should still succeed
