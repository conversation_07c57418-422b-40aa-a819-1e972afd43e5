"""
Restaurant business specific schemas for tenant settings.
"""

from typing import Optional

from pydantic import BaseModel, Field


class RestaurantBusinessSettingsUpdate(BaseModel):
    """Schema for updating restaurant business settings."""
    
    tenant_slug: Optional[str] = Field(
        None,
        max_length=63,
        pattern=r"^[a-z0-9](?:[a-z0-9\-]{0,61}[a-z0-9])?$",
        description="Unique slug for tenant identification in public URLs"
    )
