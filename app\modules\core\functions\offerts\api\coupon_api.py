import uuid
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List

from app.core.dependencies import get_db
from app.modules.core.functions.offerts.services.coupon_service import CouponService
from app.modules.core.functions.offerts.schemas.coupon import CouponCreate, CouponRead, CouponUpdate

router = APIRouter()

@router.post("/", response_model=CouponRead, status_code=201)
async def create_coupon(
    coupon_in: CouponCreate,
    db: AsyncSession = Depends(get_db),
    # current_user: User = Depends(get_current_active_tenant_owner) # Placeholder
):
    """
    Create a new coupon.
    (Requires Tenant Owner authentication)
    """
    service = CouponService(db)
    # TODO: Check if tenant_id in coupon_in belongs to the authenticated user
    coupon = await service.create_coupon(coupon_in=coupon_in)
    return coupon

@router.get("/{code}", response_model=CouponRead)
async def get_coupon(code: str, db: AsyncSession = Depends(get_db)):
    """
    Get a coupon by its code.
    """
    service = CouponService(db)
    coupon = await service.get_coupon_by_code(code)
    if not coupon:
        raise HTTPException(status_code=404, detail="Coupon not found")
    return coupon

@router.get("/{code}/validate", response_model=bool)
async def validate_coupon(code: str, db: AsyncSession = Depends(get_db)):
    """
    Validate a coupon code. Returns true if valid, false otherwise.
    """
    service = CouponService(db)
    is_valid = await service.validate_coupon(code)
    return is_valid

@router.patch("/{code}", response_model=CouponRead)
async def update_coupon(
    code: str,
    coupon_in: CouponUpdate,
    db: AsyncSession = Depends(get_db),
    # current_user: User = Depends(get_current_active_tenant_owner) # Placeholder
):
    """
    Update a coupon.
    (Requires Tenant Owner authentication)
    """
    service = CouponService(db)
    # TODO: Add authorization check
    coupon = await service.update_coupon(code=code, coupon_in=coupon_in)
    if not coupon:
        raise HTTPException(status_code=404, detail="Coupon not found")
    return coupon

@router.delete("/{code}", status_code=204)
async def delete_coupon(
    code: str,
    db: AsyncSession = Depends(get_db),
    # current_user: User = Depends(get_current_active_tenant_owner) # Placeholder
):
    """
    Delete a coupon.
    (Requires Tenant Owner authentication)
    """
    service = CouponService(db)
    # TODO: Add authorization check
    if not await service.delete_coupon(code=code):
        raise HTTPException(status_code=404, detail="Coupon not found")
    return {"ok": True} # FastAPI requires a return value for 204 