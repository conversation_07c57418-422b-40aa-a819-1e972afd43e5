from __future__ import annotations
import uuid
from typing import TYPE_CHECKING, List, Optional
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, Mapped, mapped_column
from app.db.base import Base
from app.modules.core.roles.models.roles import TenantType

# Use TYPE_CHECKING to handle circular dependencies for type hinting
if TYPE_CHECKING:
    from app.modules.core.users.models.user import User
    from app.modules.core.users.models.tenant_user_association import TenantUserAssociation
    from app.modules.core.functions.orders.models.order import Order
    from app.modules.core.functions.pos.models.sale_transaction import SaleTransaction
    from app.modules.core.functions.pos.models.cash_register import CashRegister
    from app.modules.tenants.restaurants.menu.models.digital_menu import DigitalMenu
    from app.modules.tenants.restaurants.menu.models.menu_category import MenuCategory
    from app.modules.tenants.restaurants.menu.models.menu_item import MenuItem
    from app.modules.tenants.restaurants.kds.models.kitchen_order import KitchenOrder
    from app.modules.tenants.restaurants.models.online_order import OnlineOrder
    from app.modules.shared.financial.transactions.models.transaction import FinancialTransaction
    from app.modules.shared.financial.invoices.models.invoice import Invoice
    from app.modules.shared.crm.models.account import Account
    from app.modules.shared.crm.models.interaction import Interaction
    from app.modules.shared.crm.models.loyalty import LoyaltyProgram, LoyaltyMembership, LoyaltyTransaction
    from app.modules.shared.crm.models.pricing import PricingTier, CustomerPricingAssignment, PricingRule
    from app.modules.core.functions.media_system.models import MediaFTPUser
    from app.modules.tenants.restaurants.kds.models.kds_temp_code import KDSTempCode
    from app.modules.core.functions.inventory.models.inventory_item import InventoryItem, InventoryCategory
    from app.modules.core.functions.shipping.models.shipping import ShippingMethod, ShippingCarrier, Shipment
    from app.modules.core.functions.offerts.models.coupon import Coupon
    from app.modules.core.eshop.models.product_category import ProductCategory
    # from app.modules.shared.couponic.models.couponic_offer import CouponicOffer  # Migrado para eshop
    from app.modules.tenants.restaurants.table_reservation.models.reservation import Reservation


class Tenant(Base):
    __tablename__ = "tenants"

    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)

    # Relationships
    user_associations: Mapped[List["TenantUserAssociation"]] = relationship(back_populates="tenant", cascade="all, delete-orphan")
    digital_menus: Mapped[List["DigitalMenu"]] = relationship(back_populates="tenant")
    menu_categories: Mapped[List["MenuCategory"]] = relationship(back_populates="tenant")
    menu_items: Mapped[List["MenuItem"]] = relationship(back_populates="tenant")
    kitchen_orders: Mapped[List["KitchenOrder"]] = relationship("KitchenOrder", back_populates="tenant")
    orders: Mapped[List["Order"]] = relationship("Order", back_populates="tenant")
    online_orders: Mapped[List["OnlineOrder"]] = relationship("OnlineOrder", back_populates="tenant")
    transactions: Mapped[List["FinancialTransaction"]] = relationship(
        "FinancialTransaction", back_populates="tenant"
    )
    invoices: Mapped[List["Invoice"]] = relationship("Invoice", back_populates="tenant")
    crm_accounts: Mapped[List["Account"]] = relationship("Account", back_populates="tenant")
    crm_interactions: Mapped[List["Interaction"]] = relationship(
        "Interaction", back_populates="tenant"
    )
    crm_loyalty_programs: Mapped[List["LoyaltyProgram"]] = relationship(
        "LoyaltyProgram", back_populates="tenant"
    )
    crm_loyalty_memberships: Mapped[List["LoyaltyMembership"]] = relationship(
        "LoyaltyMembership", back_populates="tenant"
    )
    crm_loyalty_transactions: Mapped[List["LoyaltyTransaction"]] = relationship(
        "LoyaltyTransaction", back_populates="tenant"
    )
    crm_pricing_tiers: Mapped[List["PricingTier"]] = relationship(
        "PricingTier", back_populates="tenant"
    )
    crm_customer_pricing_assignments: Mapped[List["CustomerPricingAssignment"]] = relationship(
        "CustomerPricingAssignment", back_populates="tenant"
    )
    crm_pricing_rules: Mapped[List["PricingRule"]] = relationship(
        "PricingRule", back_populates="tenant"
    )
    kds_temp_codes: Mapped[List["KDSTempCode"]] = relationship(
        "KDSTempCode", back_populates="tenant"
    )
    cash_registers: Mapped[List["CashRegister"]] = relationship(
        "CashRegister", back_populates="tenant"
    )
    sale_transactions: Mapped[List["SaleTransaction"]] = relationship(
        "SaleTransaction", back_populates="tenant"
    )
    reservations: Mapped[List["Reservation"]] = relationship(
        "Reservation", back_populates="tenant"
    )
    inventory_items: Mapped[List["InventoryItem"]] = relationship(
        "InventoryItem", back_populates="tenant"
    )
    inventory_categories: Mapped[List["InventoryCategory"]] = relationship(
        "InventoryCategory", back_populates="tenant"
    )
    shipping_methods: Mapped[List["ShippingMethod"]] = relationship("ShippingMethod", back_populates="tenant")
    shipping_carriers: Mapped[List["ShippingCarrier"]] = relationship("ShippingCarrier", back_populates="tenant")
    shipments: Mapped[List["Shipment"]] = relationship("Shipment", back_populates="tenant")
    coupons: Mapped[List["Coupon"]] = relationship("Coupon", back_populates="tenant")
    # couponic_offers migrado para eshop - removido para evitar erro SQLAlchemy
    # eshop_products: Mapped[List["Product"]] = relationship("Product", back_populates="tenant")
    # product_categories: Mapped[List["ProductCategory"]] = relationship("ProductCategory", back_populates="tenant")

    # Tenant Settings relationship
    settings: Mapped[Optional["TenantSettings"]] = relationship(
        "app.modules.core.tenants.models.tenant_settings.TenantSettings",
        back_populates="tenant",
        uselist=False,
        cascade="all, delete-orphan"
    )

    # Restaurant-specific settings relationship (conditional on tenant_type)
    restaurant_settings: Mapped[Optional["RestaurantTenantSettings"]] = relationship(
        "app.modules.tenants.restaurants.tenant_settings.models.restaurant_tenant_settings.RestaurantTenantSettings",
        back_populates="tenant",
        uselist=False,
        cascade="all, delete-orphan"
    )

    def __repr__(self) -> str:
        return f"<Tenant(id={self.id}, name='{self.name}', tenant_slug='{self.tenant_slug}')>"
