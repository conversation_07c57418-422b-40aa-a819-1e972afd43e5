"""
Product Approval Models
======================

Modelos para histórico e controle de aprovação de produtos no EShop.
"""

import uuid
import enum
from datetime import datetime
from typing import TYPE_CHECKING, Optional

from sqlalchemy import (
    Column, String, ForeignKey, Text, Enum, DateTime, 
    Boolean, Index, func
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base

# Use TYPE_CHECKING to handle circular dependencies
if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.core.users.models.user import User
    from .product import Product


class ProductApprovalAction(str, enum.Enum):
    """Enum para ações de aprovação de produto."""
    
    SUBMITTED = "submitted"
    APPROVED = "approved"
    REJECTED = "rejected"
    REVISION_REQUESTED = "revision_requested"
    AUTO_APPROVED = "auto_approved"


class ProductApprovalReason(str, enum.Enum):
    """Enum para razões de aprovação/rejeição."""
    
    # Aprovação
    MEETS_STANDARDS = "meets_standards"
    AUTO_MIGRATION = "auto_migration"
    TRUSTED_VENDOR = "trusted_vendor"
    
    # Rejeição
    INCOMPLETE_INFO = "incomplete_info"
    INAPPROPRIATE_CONTENT = "inappropriate_content"
    DUPLICATE_PRODUCT = "duplicate_product"
    PRICING_ISSUES = "pricing_issues"
    CATEGORY_MISMATCH = "category_mismatch"
    QUALITY_CONCERNS = "quality_concerns"
    POLICY_VIOLATION = "policy_violation"


class ProductApprovalHistory(Base):
    """
    Modelo para histórico de aprovação de produtos.
    
    Rastreia todas as ações de aprovação, rejeição e revisão
    de produtos no sistema EShop com auditoria completa.
    """
    
    __tablename__ = "eshop_product_approval_history"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("tenants.id"), 
        nullable=False, 
        index=True
    )
    
    # Produto relacionado
    product_id = Column(
        UUID(as_uuid=True),
        ForeignKey("eshop_products.id"),
        nullable=False,
        index=True
    )
    
    # Ação de aprovação
    action = Column(Enum(ProductApprovalAction), nullable=False, index=True)
    reason = Column(Enum(ProductApprovalReason), nullable=True)
    
    # Status anterior e novo
    previous_status = Column(String(20), nullable=True)
    new_status = Column(String(20), nullable=False)
    
    # Detalhes da ação
    comments = Column(Text, nullable=True)
    internal_notes = Column(Text, nullable=True)  # Notas internas (não visíveis ao vendor)
    rejection_reason = Column(Text, nullable=True)  # Razão detalhada de rejeição
    
    # Usuário responsável pela ação
    performed_by = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        index=True
    )
    performed_by_role = Column(String(50), nullable=True)  # ADMIN, MODERATOR, SYSTEM
    
    # Automação
    is_automated = Column(Boolean, default=False)
    automation_rule = Column(String(100), nullable=True)  # Regra que triggou automação
    
    # Dados do produto no momento da aprovação (snapshot)
    product_snapshot = Column(Text, nullable=True)  # JSON com dados do produto
    
    # Métricas de tempo
    processing_time_seconds = Column(String(10), nullable=True)  # Tempo para processar
    
    # Timestamps
    created_at = Column(DateTime, nullable=False, default=func.now(), index=True)
    
    # Relacionamentos
    tenant = relationship("app.modules.core.tenants.models.tenant.Tenant", viewonly=True)
    product = relationship("app.modules.core.eshop.models.product.Product", back_populates="approval_history")
    performed_by_user = relationship(
        "app.modules.core.users.models.user.User", 
        foreign_keys=[performed_by],
        viewonly=True
    )
    
    # Índices para performance
    __table_args__ = (
        Index("ix_approval_history_tenant_product", "tenant_id", "product_id"),
        Index("ix_approval_history_tenant_action", "tenant_id", "action"),
        Index("ix_approval_history_tenant_date", "tenant_id", "created_at"),
        Index("ix_approval_history_product_date", "product_id", "created_at"),
        Index("ix_approval_history_performer", "performed_by", "created_at"),
    )
    
    def __repr__(self):
        return (
            f"<ProductApprovalHistory(id={self.id}, "
            f"product_id={self.product_id}, "
            f"action='{self.action}', "
            f"performed_by={self.performed_by})>"
        )
    
    @property
    def is_approval_action(self) -> bool:
        """Verifica se é uma ação de aprovação."""
        return self.action in [
            ProductApprovalAction.APPROVED,
            ProductApprovalAction.AUTO_APPROVED
        ]
    
    @property
    def is_rejection_action(self) -> bool:
        """Verifica se é uma ação de rejeição."""
        return self.action == ProductApprovalAction.REJECTED
    
    @property
    def requires_vendor_action(self) -> bool:
        """Verifica se requer ação do vendor."""
        return self.action == ProductApprovalAction.REVISION_REQUESTED


class ProductApprovalSettings(Base):
    """
    Configurações de aprovação por tenant.
    
    Define regras e configurações específicas para
    aprovação de produtos por tenant.
    """
    
    __tablename__ = "eshop_product_approval_settings"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("tenants.id"), 
        nullable=False, 
        unique=True,
        index=True
    )
    
    # Configurações de aprovação automática
    auto_approve_trusted_vendors = Column(Boolean, default=False)
    auto_approve_b2c_products = Column(Boolean, default=True)
    auto_approve_b2b_products = Column(Boolean, default=False)
    
    # Limites para aprovação automática
    max_auto_approve_price = Column(String(10), nullable=True)  # Decimal como string
    min_vendor_rating = Column(String(3), nullable=True)  # Rating mínimo do vendor
    
    # Configurações de notificação
    notify_admins_on_submission = Column(Boolean, default=True)
    notify_vendor_on_approval = Column(Boolean, default=True)
    notify_vendor_on_rejection = Column(Boolean, default=True)
    
    # Configurações de tempo
    approval_deadline_hours = Column(String(3), default="72")  # 72 horas padrão
    auto_reject_after_days = Column(String(2), nullable=True)  # Auto-rejeitar após X dias
    
    # Lista de usuários aprovadores (JSON)
    approver_user_ids = Column(Text, nullable=True)  # JSON array de UUIDs
    
    # Configurações de moderação
    require_approval_for_edits = Column(Boolean, default=False)
    require_approval_for_price_changes = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime, nullable=False, default=func.now())
    updated_at = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now())
    
    # Relacionamento
    tenant = relationship("app.modules.core.tenants.models.tenant.Tenant", viewonly=True)
    
    def __repr__(self):
        return (
            f"<ProductApprovalSettings(tenant_id={self.tenant_id}, "
            f"auto_approve_b2c={self.auto_approve_b2c_products})>"
        ) 