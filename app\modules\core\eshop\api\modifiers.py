import logging
import uuid
from typing import List, Optional, Annotated, Any

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession

logger = logging.getLogger(__name__)

from app.modules.core.users.models.user import User
from app.modules.core.auth.dependencies.auth_dependencies import get_current_active_user
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    get_current_tenant_from_header,
    require_tenant_role,
)
from app.modules.core.tenants.models.tenant import Tenant
from app.modules.core.roles.models.roles import RolePermissions
from app.core.dependencies import get_db

from app.modules.core.eshop.services.product_modifier_service import ProductModifierService
from app.modules.core.eshop.schemas.product_modifier_group import (
    ProductModifierGroupCreate,
    ProductModifierGroupUpdate,
    ProductModifierGroupResponse,
)
from app.modules.core.eshop.schemas.product_modifier_option import (
    ProductModifierOptionCreate,
    ProductModifierOptionUpdate,
    ProductModifierOptionResponse,
)

async def get_modifier_service(db_session: AsyncSession = Depends(get_db)) -> ProductModifierService:
    return ProductModifierService(db_session)

router = APIRouter(prefix="/modifiers", tags=["eshop - Modifiers"])

write_roles = RolePermissions.ADMIN_ROLES + ["TVendorSupplier"]
view_roles = RolePermissions.VIEW_ROLES + ["TCostumer"]


# Modifier Groups
@router.post("/groups/", response_model=ProductModifierGroupResponse, status_code=status.HTTP_201_CREATED)
async def create_modifier_group(
    group_in: ProductModifierGroupCreate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Optional[Tenant], Depends(get_current_tenant_from_header)],
    modifier_service: Annotated[ProductModifierService, Depends(get_modifier_service)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=write_roles))],
):
    """Create a new modifier group."""
    try:
        if current_tenant:
            group_in.tenant_id = current_tenant.id

        created_group = await modifier_service.create_modifier_group(
            group_in=group_in,
            current_user_id=current_user.id
        )
        
        return ProductModifierGroupResponse.model_validate(created_group)
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"Error creating modifier group: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/groups/", response_model=List[ProductModifierGroupResponse])
async def read_modifier_groups(
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Optional[Tenant], Depends(get_current_tenant_from_header)],
    modifier_service: Annotated[ProductModifierService, Depends(get_modifier_service)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))],
    is_active: Optional[bool] = Query(None),
    include_options: bool = Query(False),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=200),
):
    """Retrieve modifier groups."""
    try:
        tenant_id = current_tenant.id if current_tenant else None
        
        groups = await modifier_service.get_modifier_groups(
            tenant_id=tenant_id,
            is_active=is_active,
            include_options=include_options,
            skip=skip,
            limit=limit,
        )

        return [ProductModifierGroupResponse.model_validate(group) for group in groups]
    except Exception as e:
        logger.exception(f"Error retrieving modifier groups: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/groups/{group_id}", response_model=ProductModifierGroupResponse)
async def read_modifier_group(
    group_id: Annotated[uuid.UUID, Path(...)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Optional[Tenant], Depends(get_current_tenant_from_header)],
    modifier_service: Annotated[ProductModifierService, Depends(get_modifier_service)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))],
    include_options: bool = Query(False),
):
    """Retrieve a specific modifier group."""
    try:
        tenant_id = current_tenant.id if current_tenant else None
        
        group = await modifier_service.get_modifier_group(
            group_id=group_id,
            tenant_id=tenant_id,
            include_options=include_options
        )
        
        if group is None:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Modifier group not found")

        return ProductModifierGroupResponse.model_validate(group)
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"Error retrieving modifier group {group_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


# Modifier Options
@router.post("/options/", response_model=ProductModifierOptionResponse, status_code=status.HTTP_201_CREATED)
async def create_modifier_option(
    option_in: ProductModifierOptionCreate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Optional[Tenant], Depends(get_current_tenant_from_header)],
    modifier_service: Annotated[ProductModifierService, Depends(get_modifier_service)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=write_roles))],
):
    """Create a new modifier option."""
    try:
        if current_tenant:
            option_in.tenant_id = current_tenant.id

        created_option = await modifier_service.create_modifier_option(
            option_in=option_in,
            current_user_id=current_user.id
        )
        
        return ProductModifierOptionResponse.model_validate(created_option)
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"Error creating modifier option: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/options/", response_model=List[ProductModifierOptionResponse])
async def read_modifier_options(
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Optional[Tenant], Depends(get_current_tenant_from_header)],
    modifier_service: Annotated[ProductModifierService, Depends(get_modifier_service)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))],
    modifier_group_id: Optional[uuid.UUID] = Query(None),
    is_active: Optional[bool] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=200),
):
    """Retrieve modifier options."""
    try:
        tenant_id = current_tenant.id if current_tenant else None
        
        options = await modifier_service.get_modifier_options(
            modifier_group_id=modifier_group_id,
            tenant_id=tenant_id,
            is_active=is_active,
            skip=skip,
            limit=limit,
        )

        return [ProductModifierOptionResponse.model_validate(option) for option in options]
    except Exception as e:
        logger.exception(f"Error retrieving modifier options: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/options/{option_id}", response_model=ProductModifierOptionResponse)
async def read_modifier_option(
    option_id: Annotated[uuid.UUID, Path(...)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Optional[Tenant], Depends(get_current_tenant_from_header)],
    modifier_service: Annotated[ProductModifierService, Depends(get_modifier_service)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))],
):
    """Retrieve a specific modifier option."""
    try:
        tenant_id = current_tenant.id if current_tenant else None
        
        option = await modifier_service.get_modifier_option(option_id=option_id, tenant_id=tenant_id)
        
        if option is None:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Modifier option not found")

        return ProductModifierOptionResponse.model_validate(option)
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"Error retrieving modifier option {option_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
