from typing import TYPE_CHECKING
from sqlalchemy import (
    <PERSON>um<PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    DateT<PERSON>,
    <PERSON><PERSON><PERSON>,
    Enum as SAE<PERSON>,
    Integer,
)
from sqlalchemy.dialects.postgresql import UUID  # noqa: E402
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid
import enum  # Adicionado import para enum

from app.db.base import Base  # noqa: E402

if TYPE_CHECKING:
    from app.modules.shared.email.models.email_domain import EmailDomain  # noqa: E402

# O Enum FrontendType pode ser removido se não for usado em outros lugares ou se 'frontend_type' for sempre string.  # noqa: E501
# Por ora, vou mantê-lo comentado para referência, já que o campo frontend_type é uma string.
# class FrontendType(SAEnum):
#     ONLINE_STORE = "ONLINE_STORE"
#     DIGITAL_MENU = "DIGITAL_MENU"

# O Enum DomainStatus também pode ser removido ou renomeado se não for mais usado diretamente pelo campo 'status'  # noqa: E501
# que foi renomeado para 'verification_status'.
# class DomainStatus(SAEnum):
#     PENDING_VERIFICATION = "PENDING_VERIFICATION"
#     ACTIVE = "ACTIVE"
#     ERROR = "ERROR"
#     SSL_PENDING = "SSL_PENDING"


class VerificationStatus(str, enum.Enum):
    PENDING = "PENDING"
    VERIFIED = "VERIFIED"
    FAILED = "FAILED"


class ProxyConfigStatus(str, enum.Enum):
    NOT_CONFIGURED = "NOT_CONFIGURED"
    CONFIGURED = "CONFIGURED"
    ERROR_CONFIGURING = "ERROR_CONFIGURING"
    PENDING_REMOVAL = "PENDING_REMOVAL"
    REMOVED = "REMOVED"


class SslStatus(str, enum.Enum):
    PENDING_ISSUANCE = "PENDING_ISSUANCE"
    ACTIVE = "ACTIVE"
    ISSUANCE_FAILED = "ISSUANCE_FAILED"
    RENEWAL_FAILED = "RENEWAL_FAILED"
    DISABLED = "DISABLED"  # e.g., if subscription feature removed


class CustomDomain(Base):
    __tablename__ = "custom_domains"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)

    domain_name = Column(String, nullable=False, unique=True, index=True)
    # Renomeado de trix_target_domain para cname_target
    cname_target = Column(String, nullable=False)  # Ex: [tenant_id].trix-sites.com

    # Usando String para simplificar o MVP, pode evoluir para SAEnum se houver
    # um conjunto fixo de frontends
    frontend_type = Column(String, nullable=False)  # Ex: 'ONLINE_STORE', 'DIGITAL_MENU'

    # Renomeado de status para verification_status e usando o novo Enum
    verification_status = Column(
        SAEnum(
            VerificationStatus,
            name="verification_status_enum",
            create_constraint=True,
            validate_strings=True,
        ),
        nullable=False,
        default=VerificationStatus.PENDING,
        index=True,
    )

    # Ativado após validação DNS e SSL (fases futuras)
    is_active = Column(Boolean, default=False, nullable=False)
    proxy_config_status = Column(
        SAEnum(
            ProxyConfigStatus,
            name="proxy_config_status_enum",
            create_constraint=True,
            validate_strings=True,
        ),
        nullable=False,
        default=ProxyConfigStatus.NOT_CONFIGURED,
        index=True,
    )

    # Campos para fases futuras de automação mais detalhada, mas incluídos na estrutura
    dns_validation_attempts = Column(Integer, default=0)
    last_dns_validation_at = Column(DateTime(timezone=True), nullable=True)
    # ID do certificado (ex: ID interno ou da CA)
    ssl_certificate_id = Column(String, nullable=True)
    ssl_issued_at = Column(DateTime(timezone=True), nullable=True)
    ssl_expires_at = Column(DateTime(timezone=True), nullable=True)
    ssl_status = Column(
        SAEnum(
            SslStatus,
            name="ssl_status_enum",
            create_constraint=True,
            validate_strings=True,
        ),
        nullable=False,
        default=SslStatus.DISABLED,
        index=True,
    )
    # Timestamp da última verificação de SSL pelo backend
    last_ssl_check_at = Column(DateTime(timezone=True), nullable=True)
    # Detalhes em caso de falha na emissão/renovação
    ssl_error_details = Column(String, nullable=True)

    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )

    tenant = relationship("Tenant")

    # TODO: Re-enable when domain_registrations table is properly set up
    # Add relationship to DomainRegistration
    # domain_registration_id = Column(
    #     UUID(as_uuid=True),
    #     ForeignKey("domain_registrations.id"),
    #     nullable=True,
    #     index=True,
    # )

    # domain_registration = relationship(
    #     "DomainRegistration",
    #     back_populates="custom_domains",
    #     foreign_keys=[domain_registration_id],
    # )

    # Relationship to EmailDomain
    email_domain = relationship(
        "app.modules.shared.email.models.email_domain.EmailDomain",
        back_populates="custom_domain",
        primaryjoin="CustomDomain.domain_name == app.modules.shared.email.models.email_domain.EmailDomain.domain_name",  # noqa: E501
        uselist=False,
    )

    # Adicionar outros campos como dns_validation_attempts, last_dns_validation_at, etc. se necessário para esta fase,  # noqa: E501
    # mas a tarefa principal foca em verification_status e cname_target.
    # Os campos existentes dns_validation_attempts e last_dns_validation_at podem ser úteis.

    def __repr__(self):
        return f"<CustomDomain(id={self.id}, domain_name='{self.domain_name}', tenant_id='{self.tenant_id}')>"  # noqa: E501
