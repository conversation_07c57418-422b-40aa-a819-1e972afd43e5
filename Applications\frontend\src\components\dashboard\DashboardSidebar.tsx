'use client';

import { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname, useSearchParams } from 'next/navigation';
import {
  HomeIcon,
  BuildingStorefrontIcon,
  CubeIcon,
  UsersIcon,
  UserGroupIcon,
  Cog6ToothIcon,
  ChevronRightIcon,
  ChevronDownIcon,
  Bars3Icon,
  ShoppingBagIcon,
  ClipboardDocumentListIcon,
  ComputerDesktopIcon,
  CalendarDaysIcon,
  TruckIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  MegaphoneIcon,
  TvIcon,
  ShareIcon,
  TagIcon,
  MusicalNoteIcon,
  PhoneIcon,
  DevicePhoneMobileIcon,
  WrenchScrewdriverIcon,
  LifebuoyIcon,
  GlobeAltIcon,
  BookOpenIcon,
  BellIcon,
  QuestionMarkCircleIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '@/lib/auth/AuthProvider';
import { useTenant } from '@/lib/tenant/TenantProvider';
import { clsx } from 'clsx';
import { adminNavigation, tenantNavigation } from './sidebar/navigation';
import { motion, AnimatePresence } from 'framer-motion';

// Tooltip Component
const Tooltip = ({
  children,
  content,
  show
}: {
  children: React.ReactNode;
  content: string;
  show: boolean;
}) => {
  const tooltipRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const cachedPositionRef = useRef<{ left: number; top: number } | null>(null);

  // Calculate position dynamically (recalculates on every call to handle scroll)
  const calculatePosition = useCallback(() => {
    if (!containerRef.current) return null;

    // Find the actual clickable element - try multiple selectors
    let targetElement: Element = containerRef.current.querySelector('button') ||
                                containerRef.current.querySelector('a') ||
                                containerRef.current;

    const targetRect = targetElement.getBoundingClientRect();

    // Calculate position - center the tooltip with the visual icon card
    const leftPosition = targetRect.right + 12; // 12px margin

    // For collapsed sidebar, the visual card is 40px (w-10 h-10)
    // We need to align with the center of the visual card, not the HTML element
    // Move up by half the card height (20px) to center with the card
    const cardHeight = 40; // w-10 h-10 = 40px
    const iconCenterY = targetRect.top + (targetRect.height / 2) - (cardHeight / 2);

    return { left: leftPosition, top: iconCenterY };
  }, []);

  // Apply position to tooltip and handle scroll updates
  useEffect(() => {
    if (show && tooltipRef.current) {
      const updatePosition = () => {
        const position = calculatePosition();
        if (position && tooltipRef.current) {
          const tooltip = tooltipRef.current;
          tooltip.style.position = 'fixed';
          tooltip.style.left = `${position.left}px`;
          tooltip.style.top = `${position.top}px`;
          tooltip.style.transform = 'translateY(-50%)';
          tooltip.style.zIndex = '9999';
        }
      };

      // Initial positioning
      updatePosition();

      // Update position on scroll
      const handleScroll = () => {
        updatePosition();
      };

      window.addEventListener('scroll', handleScroll, { passive: true });

      return () => {
        window.removeEventListener('scroll', handleScroll);
      };
    }
  }, [show, calculatePosition]);

  return (
    <div className="relative" ref={containerRef}>
      {children}
      <AnimatePresence>
        {show && (
          <motion.div
            ref={tooltipRef}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.2 }}
            className="px-3 py-2 bg-gray-900 text-white text-sm rounded-lg shadow-lg whitespace-nowrap"
            style={{
              position: 'fixed',
              zIndex: 9999,
              // Remove any transform conflicts - positioning will be handled by useEffect
              transform: 'translateY(-50%)'
            }}
          >
            {content}
            {/* Arrow positioned to point to center of icon */}
            <div
              className="absolute right-full w-0 h-0 border-t-4 border-b-4 border-r-4 border-transparent border-r-gray-900"
              style={{
                top: '50%',
                transform: 'translateY(-50%)'
              }}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Dropdown Component for collapsed sidebar
const CollapsedDropdown = ({
  item,
  show,
  onClose,
  buildUrl,
  onMouseEnter,
  onMouseLeave
}: {
  item: NavigationItem;
  show: boolean;
  onClose: () => void;
  buildUrl: (href: string) => string;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
}) => {
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Calculate position dynamically (recalculates on every call to handle scroll)
  const calculatePosition = useCallback(() => {
    if (!dropdownRef.current) return null;

    const dropdown = dropdownRef.current;
    const parentRect = dropdown.parentElement?.getBoundingClientRect();
    if (!parentRect) return null;

    // Calculate initial position
    const iconCenterY = parentRect.top + (parentRect.height / 2);
    let leftPosition = parentRect.right + 12; // 12px margin
    let topPosition = parentRect.top - 20; // Move up by 20px (half of icon card height)

    // Force render to measure dropdown dimensions
    dropdown.style.visibility = 'hidden';
    dropdown.style.display = 'block';
    dropdown.style.position = 'fixed';
    dropdown.style.left = `${leftPosition}px`;
    dropdown.style.top = `${topPosition}px`;
    dropdown.style.zIndex = '9999';

    // Get dropdown dimensions
    const dropdownRect = dropdown.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Minimum margin from screen edges
    const minMargin = 10;
    // Extra margin for footer to ensure dropdown doesn't touch it
    const footerMargin = 40; // 10px base + 30px extra for footer

    // Check for bottom overflow - ensure dropdown stays above footer
    if (topPosition + dropdownRect.height > viewportHeight - footerMargin) {
      topPosition = viewportHeight - dropdownRect.height - footerMargin;
    }

    // Check for top overflow - ensure at least 10px margin from top
    if (topPosition < minMargin) {
      topPosition = minMargin;
    }

    // Check for right overflow - ensure at least 10px margin from right
    if (leftPosition + dropdownRect.width > viewportWidth - minMargin) {
      leftPosition = viewportWidth - dropdownRect.width - minMargin;
    }

    // Check for left overflow - ensure at least 10px margin from left
    if (leftPosition < minMargin) {
      leftPosition = minMargin;
    }

    // Calculate arrow position relative to the dropdown
    const arrowPosition = Math.max(8, Math.min(dropdownRect.height - 8, iconCenterY - topPosition));

    return { left: leftPosition, top: topPosition, arrowPosition };
  }, []);

  // Apply position to dropdown and handle scroll updates
  useEffect(() => {
    if (show && dropdownRef.current) {
      const updatePosition = () => {
        const position = calculatePosition();
        if (position && dropdownRef.current) {
          const dropdown = dropdownRef.current;
          dropdown.style.position = 'fixed';
          dropdown.style.left = `${position.left}px`;
          dropdown.style.top = `${position.top}px`;
          dropdown.style.setProperty('--arrow-position', `${position.arrowPosition}px`);
          dropdown.style.zIndex = '9999';
          dropdown.style.visibility = 'visible';
          dropdown.style.display = 'block';
        }
      };

      // Initial positioning
      updatePosition();

      // Update position on scroll
      const handleScroll = () => {
        updatePosition();
      };

      window.addEventListener('scroll', handleScroll, { passive: true });

      return () => {
        window.removeEventListener('scroll', handleScroll);
      };
    }
  }, [show, calculatePosition]);

  return (
    <AnimatePresence>
      {show && (
        <motion.div
          ref={dropdownRef}
          initial={{ opacity: 0, x: -10, scale: 0.9 }}
          animate={{ opacity: 1, x: 0, scale: 1 }}
          exit={{ opacity: 0, x: -10, scale: 0.9 }}
          transition={{ duration: 0.2 }}
          className="fixed z-[9999] min-w-48 bg-white rounded-xl shadow-2xl border border-gray-200/50 backdrop-blur-xl overflow-hidden"

          onMouseEnter={onMouseEnter}
          onMouseLeave={onMouseLeave}
        >
          {/* Dynamic arrow pointing to the icon */}
          <div
            className="absolute right-full w-0 h-0 border-t-4 border-b-4 border-r-4 border-transparent border-r-white"
            style={{
              top: 'var(--arrow-position, 50%)',
              transform: 'translateY(-50%)'
            }}
          />
        {/* Header with parent item */}
        {item.href && (
          <Link
            href={buildUrl(item.href)}
            className="flex items-center px-4 py-3 text-sm font-semibold text-gray-800 bg-gradient-to-r from-blue-50 to-purple-50 border-b border-gray-100 hover:from-blue-100 hover:to-purple-100 transition-all duration-200"
            onClick={onClose}
          >
            <item.icon className="h-4 w-4 mr-3 text-blue-600" />
            {item.name}
          </Link>
        )}

        {/* Children */}
        <div className="py-2">
          {item.children?.map((child) => (
            <Link
              key={child.name}
              href={buildUrl(child.href!)}
              className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-25 hover:text-gray-900 transition-all duration-200"
              onClick={onClose}
            >
              <child.icon className="h-4 w-4 mr-3 text-gray-500" />
              {child.name}
            </Link>
          ))}
        </div>
      </motion.div>
    )}
  </AnimatePresence>
  );
};

interface NavigationItem {
  name: string;
  href?: string;
  icon: any;
  children?: NavigationItem[];
  roles?: string[];
}

interface DashboardSidebarProps {
  collapsed?: boolean;
  onToggle?: () => void;
}

export function DashboardSidebar({ collapsed: collapsedProp, onToggle }: DashboardSidebarProps) {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { isAdmin, isTenantOwner, isTenantStaff, isTenantCustomer } = useAuth();
  const { currentTenant, isMultiTenant, isAdminViewMode } = useTenant();
  const [expandedItems, setExpandedItems] = useState<string[]>(['restaurant']);
  const [internalCollapsed, setInternalCollapsed] = useState(false);
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const [dropdownItem, setDropdownItem] = useState<string | null>(null);
  const [hoverTimeout, setHoverTimeout] = useState<NodeJS.Timeout | null>(null);
  const [dropdownTimeout, setDropdownTimeout] = useState<NodeJS.Timeout | null>(null);

  // Track if initial animation has been shown
  const [hasAnimated, setHasAnimated] = useState(() => {
    if (typeof window !== 'undefined') {
      return sessionStorage.getItem('sidebar-animated') === 'true';
    }
    return false;
  });

  // Use prop if provided, otherwise use internal state
  const collapsed = collapsedProp !== undefined ? collapsedProp : internalCollapsed;

  // Handle toggle - use prop callback if provided, otherwise use internal state
  const handleToggle = () => {
    if (onToggle) {
      onToggle();
    } else {
      setInternalCollapsed(!internalCollapsed);
    }
  };

  // Handle hover with delay
  const handleMouseEnter = (itemKey: string) => {
    // Clear any existing timeout
    if (hoverTimeout) {
      clearTimeout(hoverTimeout);
      setHoverTimeout(null);
    }
    if (dropdownTimeout) {
      clearTimeout(dropdownTimeout);
      setDropdownTimeout(null);
    }

    setHoveredItem(itemKey);
    if (collapsed) {
      setDropdownItem(itemKey);
    }
  };

  const handleMouseLeave = (itemKey: string) => {
    // Set timeout to hide tooltip after 0.5 seconds
    const timeout = setTimeout(() => {
      setHoveredItem(null);
    }, 500);
    setHoverTimeout(timeout);

    // Set timeout to hide dropdown after 0.5 seconds
    if (collapsed) {
      const dropTimeout = setTimeout(() => {
        setDropdownItem(null);
      }, 500);
      setDropdownTimeout(dropTimeout);
    }
  };

  const handleDropdownMouseEnter = () => {
    // Clear timeouts when mouse enters dropdown
    if (hoverTimeout) {
      clearTimeout(hoverTimeout);
      setHoverTimeout(null);
    }
    if (dropdownTimeout) {
      clearTimeout(dropdownTimeout);
      setDropdownTimeout(null);
    }
  };

  const handleDropdownMouseLeave = () => {
    // Hide dropdown immediately when mouse leaves dropdown area
    setDropdownItem(null);
    setHoveredItem(null);
  };

  // DEBUG LOGS
  useEffect(() => {
    console.log('[DashboardSidebar EFFECT] isAdmin:', isAdmin());
    console.log('[DashboardSidebar EFFECT] currentTenant:', currentTenant ? currentTenant.id : null);
  }, [isAdmin, currentTenant]);

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev =>
      prev.includes(itemName)
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    );
  };

  // Function to build URLs that preserve admin view parameters
  const buildUrl = (href: string): string => {
    if (!isAdminViewMode) {
      return href;
    }

    const adminView = searchParams.get('admin_view');
    const tenantId = searchParams.get('tenant_id');

    if (adminView === 'true' && tenantId) {
      const url = new URL(href, window.location.origin);
      url.searchParams.set('admin_view', 'true');
      url.searchParams.set('tenant_id', tenantId);
      return url.pathname + url.search;
    }

    return href;
  };



  const buildNavigation = useCallback((): NavigationItem[] => {
    console.log('[DashboardSidebar buildNavigation] currentTenant:', currentTenant ? currentTenant.id : null);
    console.log('[DashboardSidebar buildNavigation] isAdmin:', isAdmin());
    console.log('[DashboardSidebar buildNavigation] isAdminViewMode:', isAdminViewMode);
    console.log('[DashboardSidebar buildNavigation] Condition (isAdmin() || currentTenant):', isAdmin() || !!currentTenant);

    // Admin View Mode - use tenant navigation even if user is admin
    if (isAdminViewMode && currentTenant) {
      console.log('[DashboardSidebar buildNavigation] Using tenant navigation for admin view mode');
      return tenantNavigation;
    }

    // Admin Navigation (only when not in admin view mode)
    if (isAdmin() && !isAdminViewMode) {
      return adminNavigation;
    }

    // Tenant Navigation - only show if user has a tenant
    if (currentTenant) {
      return tenantNavigation;
    }

    // Default navigation for users without tenant
    return [
      {
        name: 'Dashboard',
        href: '/dashboard',
        icon: HomeIcon,
      },
    ];
  }, [currentTenant, isAdmin, isAdminViewMode]); // This closes the buildNavigation function

  const navigation = useMemo(() => buildNavigation(), [buildNavigation]);


  const hasAccess = (item: NavigationItem): boolean => {
    console.log(`[DashboardSidebar hasAccess] Checking access for: "${item.name}"`);
    console.log(`[DashboardSidebar hasAccess] Item roles:`, item.roles);
    console.log(`[DashboardSidebar hasAccess] isAdmin():`, isAdmin());
    console.log(`[DashboardSidebar hasAccess] currentTenant:`, currentTenant ? currentTenant.id : null);

    // Admins have access to everything
    if (isAdmin()) {
      console.log(`[DashboardSidebar hasAccess] Access granted to "${item.name}" (isAdmin)`);
      return true;
    }

    // Items without roles are accessible to everyone
    if (!item.roles) return true;

    // Need a current tenant for role-based access
    if (!currentTenant) {
      console.log(`[DashboardSidebar hasAccess] Access DENIED to "${item.name}" (no currentTenant for role check)`);
      return false;
    }

    const hasMatchingRole = item.roles.some(role => {
      let roleCheckResult = false;
      switch (role) {
        case 'admin':
          roleCheckResult = isAdmin();
          break;
        case 'owner':
          roleCheckResult = isTenantOwner(currentTenant.id);
          break;
        case 'staff':
          roleCheckResult = isTenantStaff(currentTenant.id) || isTenantOwner(currentTenant.id);
          break;
        case 'customer':
          roleCheckResult = isTenantCustomer(currentTenant.id);
          break;
        default:
          roleCheckResult = false;
      }
      console.log(`[DashboardSidebar hasAccess] Role check for "${item.name}": role "${role}" -> ${roleCheckResult}`);
      return roleCheckResult;
    });

    if (hasMatchingRole) {
      console.log(`[DashboardSidebar hasAccess] Access GRANTED to "${item.name}" (role match)`);
    } else {
      console.log(`[DashboardSidebar hasAccess] Access DENIED to "${item.name}" (no role match)`);
    }
    return hasMatchingRole;
  };

  const renderNavigationItem = (item: NavigationItem, level = 0) => {
    if (!hasAccess(item)) return null;

    const isExpanded = expandedItems.includes(item.name.toLowerCase());
    const hasChildren = item.children && item.children.length > 0;
    // Special case for Dashboard - only active when exactly on /dashboard
    const isActive = item.href ?
      (item.href === '/dashboard' ? pathname === '/dashboard' : pathname === item.href) :
      false;

    // Check if any child is active (for parent highlighting)
    const hasActiveChild = hasChildren && item.children?.some(child => {
      if (!child.href) return false;
      // Special case for Dashboard - only active when exactly on /dashboard
      if (child.href === '/dashboard') {
        return pathname === '/dashboard';
      }
      return pathname === child.href || pathname.startsWith(child.href + '/');
    });

    if (hasChildren) {
      const itemKey = `${item.name}-${level}`;

      return (
        <motion.div
          key={item.name}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: level * 0.1 }}
          className="relative"
        >
          <Tooltip content={item.name} show={collapsed && hoveredItem === itemKey}>
            <motion.button
              onClick={() => !collapsed && toggleExpanded(item.name.toLowerCase())}
              onMouseEnter={() => handleMouseEnter(itemKey)}
              onMouseLeave={() => handleMouseLeave(itemKey)}
              className={clsx(
                'group w-full text-left flex items-center transition-all duration-300 mx-1 my-1',
                hasActiveChild
                  ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-blue-700 shadow-lg border border-blue-200/50 backdrop-blur-sm'
                  : 'text-gray-700 hover:bg-gradient-to-r hover:from-gray-100/80 hover:to-gray-50/60 hover:text-gray-900 hover:shadow-md hover:backdrop-blur-sm',
                level > 0 && 'ml-4',
                collapsed ? 'justify-center items-center p-2 rounded-xl w-10 h-10 mx-auto' : 'px-4 py-3 rounded-xl text-sm font-medium'
              )}
              whileHover={{ scale: 1.02, x: collapsed ? 0 : 2 }}
              whileTap={{ scale: 0.98 }}
            >
              <item.icon className={clsx(
                'flex-shrink-0 transition-all duration-300',
                hasActiveChild ? 'text-blue-600' : 'text-gray-600 group-hover:text-gray-800',
                collapsed ? 'h-5 w-5' : 'h-5 w-5 mr-3'
              )} />
              {!collapsed && (
                <>
                  <span className="flex-1 font-medium">{item.name}</span>
                  <motion.div
                    animate={{ rotate: isExpanded ? 90 : 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <ChevronRightIcon className="ml-auto h-4 w-4 text-gray-400" />
                  </motion.div>
                </>
              )}
            </motion.button>
          </Tooltip>

          {/* Collapsed Dropdown */}
          {collapsed && (
            <CollapsedDropdown
              item={item}
              show={dropdownItem === itemKey}
              onClose={() => setDropdownItem(null)}
              buildUrl={buildUrl}
              onMouseEnter={handleDropdownMouseEnter}
              onMouseLeave={handleDropdownMouseLeave}
            />
          )}

          {/* Expanded Children */}
          <AnimatePresence>
            {!collapsed && isExpanded && item.children && (
              <motion.div
                className="mt-2 space-y-1 ml-4"
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
              >
                {item.children.map(child => renderNavigationItem(child, level + 1))}
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      );
    }

    // For child items, also check if the current path starts with the item href
    const isChildActive = item.href && (() => {
      // Special case for Dashboard - only active when exactly on /dashboard
      if (item.href === '/dashboard') {
        return pathname === '/dashboard';
      }
      return pathname === item.href || pathname.startsWith(item.href + '/');
    })();

    const itemKey = `${item.name}-${level}`;

    // If item has no href, render as a non-clickable element
    if (!item.href) {
      return (
        <motion.div
          key={item.name}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: level * 0.1 }}
          className="relative"
        >
          <Tooltip content={item.name} show={collapsed && hoveredItem === itemKey}>
            <div
              onMouseEnter={() => handleMouseEnter(itemKey)}
              onMouseLeave={() => handleMouseLeave(itemKey)}
              className={clsx(
                'group flex items-center transition-all duration-300 mx-1 my-1 cursor-default',
                'text-gray-700 hover:bg-gradient-to-r hover:from-gray-100/80 hover:to-gray-50/60 hover:text-gray-900 hover:shadow-md hover:backdrop-blur-sm',
                level > 0 && 'ml-4',
                collapsed ? 'justify-center items-center p-2 rounded-xl w-10 h-10 mx-auto' : 'px-4 py-3 rounded-xl text-sm font-medium'
              )}
            >
              <item.icon className={clsx(
                'flex-shrink-0 transition-all duration-300',
                'text-gray-600 group-hover:text-gray-800',
                collapsed ? 'h-5 w-5' : 'h-5 w-5 mr-3'
              )} />
              {!collapsed && (
                <motion.span
                  className="font-medium"
                  whileHover={{ x: 2 }}
                  transition={{ duration: 0.2 }}
                >
                  {item.name}
                </motion.span>
              )}
            </div>
          </Tooltip>
        </motion.div>
      );
    }

    return (
      <motion.div
        key={item.name}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: level * 0.1 }}
        className="relative"
      >
        <Tooltip content={item.name} show={collapsed && hoveredItem === itemKey}>
          <Link
            href={buildUrl(item.href)}
            onMouseEnter={() => handleMouseEnter(itemKey)}
            onMouseLeave={() => handleMouseLeave(itemKey)}
            className={clsx(
              'group flex items-center transition-all duration-300 mx-1 my-1',
              (isActive || isChildActive)
                ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-blue-700 shadow-lg border border-blue-200/50 backdrop-blur-sm'
                : 'text-gray-700 hover:bg-gradient-to-r hover:from-gray-100/80 hover:to-gray-50/60 hover:text-gray-900 hover:shadow-md hover:backdrop-blur-sm',
              level > 0 && 'ml-4',
              collapsed ? 'justify-center items-center p-2 rounded-xl w-10 h-10 mx-auto' : 'px-4 py-3 rounded-xl text-sm font-medium'
            )}
          >
            <item.icon className={clsx(
              'flex-shrink-0 transition-all duration-300',
              (isActive || isChildActive) ? 'text-blue-600' : 'text-gray-600 group-hover:text-gray-800',
              collapsed ? 'h-5 w-5' : 'h-5 w-5 mr-3'
            )} />
            {!collapsed && (
              <motion.span
                className="font-medium"
                whileHover={{ x: 2 }}
                transition={{ duration: 0.2 }}
              >
                {item.name}
              </motion.span>
            )}
          </Link>
        </Tooltip>
      </motion.div>
    );
  };

  // Auto-expand items that contain the current page
  useEffect(() => {
    const findParentWithActivePage = (items: NavigationItem[]): string[] => {
      const activeParents: string[] = [];

      items.forEach(item => {
        if (item.children) {
          const hasActivePage = item.children.some(child => {
            if (!child.href) return false;
            // Special case for Dashboard - only active when exactly on /dashboard
            if (child.href === '/dashboard') {
              return pathname === '/dashboard';
            }
            return pathname === child.href || pathname.startsWith(child.href + '/');
          });
          if (hasActivePage) {
            activeParents.push(item.name.toLowerCase());
          }
        }
      });

      return activeParents;
    };

    const activeParents = findParentWithActivePage(navigation);
    if (activeParents.length > 0) {
      setExpandedItems(prev => {
        const newExpanded = [...prev];
        activeParents.forEach(parent => {
          if (!newExpanded.includes(parent)) {
            newExpanded.push(parent);
          }
        });
        return newExpanded;
      });
    }
  }, [pathname, navigation]);

  // Mark animation as completed after first render
  useEffect(() => {
    if (!hasAnimated) {
      const timer = setTimeout(() => {
        setHasAnimated(true);
        if (typeof window !== 'undefined') {
          sessionStorage.setItem('sidebar-animated', 'true');
        }
      }, 600); // After animation completes (500ms + buffer)

      return () => clearTimeout(timer);
    }
  }, [hasAnimated]);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (hoverTimeout) {
        clearTimeout(hoverTimeout);
      }
      if (dropdownTimeout) {
        clearTimeout(dropdownTimeout);
      }
    };
  }, [hoverTimeout, dropdownTimeout]);

  return (
    <motion.div
      className={clsx(
        "hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:flex-col transition-all duration-500",
        collapsed ? "lg:w-16 overflow-visible" : "lg:w-72"
      )}
      initial={hasAnimated ? { x: 0, opacity: 1 } : { x: -100, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      transition={{ duration: hasAnimated ? 0 : 0.5, ease: "easeOut" }}
    >
      {/* Modern Glassmorphism Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/95 via-white/90 to-white/85 backdrop-blur-xl border-r border-white/20 shadow-2xl" />

      {/* Decorative Elements */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-3xl" />
      <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-green-400/10 to-blue-400/10 rounded-full blur-2xl" />

      <div className={clsx(
        "relative z-10 flex grow flex-col gap-y-6 pb-6 overflow-y-auto",
        collapsed ? "px-2 overflow-x-visible" : "px-6 overflow-x-hidden"
      )}>
        {/* Modern Header with Logo */}
        <motion.div
          className="flex h-20 shrink-0 items-center justify-center mt-4 relative"
          whileHover={{ scale: 1.02 }}
          transition={{ duration: 0.2 }}
        >
          {!collapsed ? (
            <Link href="/" className="flex items-center group">
              <motion.div
                whileHover={{ rotate: [0, -2, 2, 0] }}
                transition={{ duration: 0.3 }}
              >
                <Image
                  src="/logo_escuro.png"
                  alt="Trix"
                  width={240}
                  height={80}
                  className="h-16 w-auto transition-all duration-300 group-hover:brightness-110"
                  priority
                />
              </motion.div>
            </Link>
          ) : (
            <Link href="/" className="flex items-center justify-center group w-full">
              <Image
                src="/logo_escuro.png"
                alt="Trix"
                width={120}
                height={40}
                className="w-10 h-auto transition-all duration-300 group-hover:brightness-110"
                priority
              />
            </Link>
          )}


        </motion.div>

        <nav className="flex flex-1 flex-col">
          <motion.ul
            role="list"
            className="flex flex-1 flex-col gap-y-8"
            initial={hasAnimated ? { opacity: 1 } : { opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: hasAnimated ? 0 : 0.5, delay: hasAnimated ? 0 : 0.2 }}
          >
            <li>
              <motion.ul
                role="list"
                className={clsx(
                  collapsed ? "space-y-3 flex flex-col items-center" : "space-y-2"
                )}
                initial={hasAnimated ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: hasAnimated ? 0 : 0.5, delay: hasAnimated ? 0 : 0.3 }}
              >
                {navigation.map((item, index) => (
                  <motion.div
                    key={item.name}
                    initial={hasAnimated ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: hasAnimated ? 0 : 0.3, delay: hasAnimated ? 0 : 0.4 + (index * 0.1) }}
                  >
                    {renderNavigationItem(item)}
                  </motion.div>
                ))}
              </motion.ul>
            </li>

            {/* Modern Multi-tenant overview for admins */}
            {isAdmin() && isMultiTenant && (
              <motion.li
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.6 }}
              >
                {!collapsed && (
                  <motion.div
                    className="px-4 py-2 mb-4"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.7 }}
                  >
                    <div className="flex items-center gap-2 text-xs font-bold uppercase tracking-wider text-gray-500">
                      <div className="w-8 h-px bg-gradient-to-r from-gray-300 to-transparent" />
                      <span>System Administration</span>
                      <div className="flex-1 h-px bg-gradient-to-r from-transparent to-gray-300" />
                    </div>
                  </motion.div>
                )}
                <motion.ul
                  role="list"
                  className="space-y-2"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.8 }}
                >
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 0.9 }}
                  >
                    <Link
                      href={buildUrl("/dashboard/admin/tenants")}
                      className={clsx(
                        'group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 mx-1 my-1',
                        pathname === '/dashboard/admin/tenants'
                          ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-blue-700 shadow-lg border border-blue-200/50 backdrop-blur-sm'
                          : 'text-gray-700 hover:bg-gradient-to-r hover:from-gray-100/80 hover:to-gray-50/60 hover:text-gray-900 hover:shadow-md hover:backdrop-blur-sm',
                        collapsed && 'justify-center'
                      )}
                      title={collapsed ? "Tenants" : undefined}
                    >
                      <BuildingStorefrontIcon className={clsx(
                        'h-5 w-5 flex-shrink-0 transition-all duration-300',
                        pathname === '/dashboard/admin/tenants' ? 'text-blue-600' : 'text-gray-600 group-hover:text-gray-800',
                        collapsed ? 'mx-auto' : 'mr-3'
                      )} />
                      {!collapsed && (
                        <motion.span
                          className="font-medium"
                          whileHover={{ x: 2 }}
                          transition={{ duration: 0.2 }}
                        >
                          Tenants
                        </motion.span>
                      )}
                    </Link>
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 1.0 }}
                  >
                    <Link
                      href={buildUrl("/dashboard/admin/users")}
                      className={clsx(
                        'group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 mx-1 my-1',
                        pathname === '/dashboard/admin/users'
                          ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-blue-700 shadow-lg border border-blue-200/50 backdrop-blur-sm'
                          : 'text-gray-700 hover:bg-gradient-to-r hover:from-gray-100/80 hover:to-gray-50/60 hover:text-gray-900 hover:shadow-md hover:backdrop-blur-sm',
                        collapsed && 'justify-center'
                      )}
                      title={collapsed ? "Users" : undefined}
                    >
                      <UsersIcon className={clsx(
                        'h-5 w-5 flex-shrink-0 transition-all duration-300',
                        pathname === '/dashboard/admin/users' ? 'text-blue-600' : 'text-gray-600 group-hover:text-gray-800',
                        collapsed ? 'mx-auto' : 'mr-3'
                      )} />
                      {!collapsed && (
                        <motion.span
                          className="font-medium"
                          whileHover={{ x: 2 }}
                          transition={{ duration: 0.2 }}
                        >
                          Users
                        </motion.span>
                      )}
                    </Link>
                  </motion.div>
                </motion.ul>
              </motion.li>
            )}
          </motion.ul>
        </nav>
      </div>
    </motion.div>
  );
}
