"""
EShop Integration API

Placeholder for future integration endpoints.
"""

from fastapi import APIRouter

# Create integration router
router = APIRouter(prefix="/integration", tags=["EShop Integration"])


@router.get("/status")
async def integration_status():
    """
    Get integration system status.
    """
    return {
        "status": "placeholder",
        "message": "Integration API placeholder - not yet implemented"
    } 