"""
URL Slug Model

Multilingual URL slug management for SEO-friendly URLs.
"""

import uuid
from datetime import datetime
from typing import Optional

from sqlalchemy import Column, DateTime, ForeignKey, String, Boolean, Integer, Index
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base


class URLSlug(Base):
    """
    Multilingual URL slug model for SEO-friendly URLs.
    
    Stores URL slugs for different languages and content types,
    enabling multilingual SEO with proper hreflang support.
    """

    __tablename__ = "url_slugs"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Generic content reference
    content_type = Column(String(50), nullable=False, index=True)  # e.g., 'product', 'blog_post', 'category'
    content_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # Language support
    language_code = Column(String(10), ForeignKey("languages.code"), nullable=False, index=True)
    
    # Slug information
    slug = Column(String(255), nullable=False, index=True)
    full_path = Column(String(500), nullable=True)  # Full URL path including parent categories
    
    # Slug metadata
    is_primary = Column(Boolean, default=True, nullable=False)  # Primary slug for this language
    is_active = Column(Boolean, default=True, nullable=False)
    redirect_to_id = Column(UUID(as_uuid=True), ForeignKey("url_slugs.id"), nullable=True)  # For redirects
    
    # SEO properties
    priority = Column(Integer, default=50, nullable=False)  # 0-100 for sitemap priority
    change_frequency = Column(String(20), default="weekly", nullable=False)  # always, hourly, daily, weekly, monthly, yearly, never
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    last_accessed_at = Column(DateTime, nullable=True)  # For analytics
    
    # Foreign keys
    created_by_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # Relationships
    language = relationship("app.modules.core.i18n.models.language.Language", viewonly=True)
    created_by = relationship("app.modules.core.users.models.user.User", viewonly=True)
    redirect_to = relationship("URLSlug", remote_side=[id], viewonly=True)

    # Indexes for performance
    __table_args__ = (
        Index("ix_url_slugs_content", "content_type", "content_id"),
        Index("ix_url_slugs_slug_lang", "slug", "language_code"),
        Index("ix_url_slugs_active", "is_active", "is_primary"),
        {"schema": None},  # Use default schema
    )

    def __repr__(self):
        return f"<URLSlug(id={self.id}, slug='{self.slug}', content_type='{self.content_type}', language='{self.language_code}')>"

    @property
    def full_url(self) -> str:
        """Get the full URL path for this slug."""
        if self.full_path:
            return self.full_path
        return f"/{self.language_code}/{self.content_type}/{self.slug}"

    def is_valid_change_frequency(self, frequency: str) -> bool:
        """Validate change frequency for sitemap."""
        valid_frequencies = [
            "always", "hourly", "daily", "weekly", 
            "monthly", "yearly", "never"
        ]
        return frequency in valid_frequencies

    def get_sitemap_priority(self) -> float:
        """Get sitemap priority as float between 0.0 and 1.0."""
        return min(max(self.priority / 100.0, 0.0), 1.0)
