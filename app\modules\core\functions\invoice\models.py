"""
TvendorInvoice Model for B2B Invoice Management
==============================================

Modelo para gestão de faturas entre TVendorSupplier e TCostumer,
incluindo upload de arquivos, controle de acesso e rastreamento de status.
"""

import uuid
import enum
from datetime import datetime, timed<PERSON>ta
from typing import TYPE_CHECKING, Optional
from decimal import Decimal

from sqlalchemy import (
    Column, String, ForeignKey, Text, Enum, DateTime, 
    Boolean, Index, func, Numeric, JSON, Integer
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, Mapped, mapped_column

from app.db.base import Base

# Use TYPE_CHECKING to handle circular dependencies
if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.core.users.models.user import User
    from app.modules.core.eshop.models.tcostumer import TCostumer
    from app.modules.core.eshop.models.tvendor_supplier import TVendorSupplier


class InvoiceStatus(str, enum.Enum):
    """Status da fatura."""
    
    DRAFT = "draft"
    PENDING = "pending"
    SENT = "sent"
    VIEWED = "viewed"
    PAID = "paid"
    OVERDUE = "overdue"
    CANCELLED = "cancelled"
    DISPUTED = "disputed"


class InvoiceType(str, enum.Enum):
    """Tipo de fatura."""
    
    STANDARD = "standard"
    PROFORMA = "proforma"
    CREDIT_NOTE = "credit_note"
    DEBIT_NOTE = "debit_note"
    RECURRING = "recurring"


class TvendorInvoice(Base):
    """
    Modelo para faturas de fornecedores B2B.
    
    Gerencia upload, acesso e rastreamento de faturas entre
    TVendorSupplier e TCostumer com controle de segurança.
    """
    
    __tablename__ = "eshop_tvendor_invoices"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("tenants.id"), 
        nullable=False, 
        index=True
    )
    
    # Relacionamentos B2B
    vendor_id = Column(
        UUID(as_uuid=True),
        ForeignKey("eshop_tvendor_suppliers.id"),
        nullable=False,
        index=True
    )
    customer_id = Column(
        UUID(as_uuid=True),
        ForeignKey("eshop_tcostumers.id"),
        nullable=False,
        index=True
    )
    
    # Informações da fatura
    invoice_number = Column(String(100), nullable=False, index=True)
    invoice_type = Column(Enum(InvoiceType), default=InvoiceType.STANDARD, nullable=False)
    status = Column(Enum(InvoiceStatus), default=InvoiceStatus.DRAFT, nullable=False, index=True)
    
    # Datas importantes
    invoice_date = Column(DateTime, nullable=False)
    due_date = Column(DateTime, nullable=False, index=True)
    sent_date = Column(DateTime, nullable=True)
    viewed_date = Column(DateTime, nullable=True)
    paid_date = Column(DateTime, nullable=True)
    
    # Valores financeiros
    subtotal = Column(Numeric(12, 2), nullable=False)
    tax_amount = Column(Numeric(12, 2), default=0.00, nullable=False)
    discount_amount = Column(Numeric(12, 2), default=0.00, nullable=False)
    total_amount = Column(Numeric(12, 2), nullable=False)
    paid_amount = Column(Numeric(12, 2), default=0.00, nullable=False)
    
    # Informações do arquivo
    file_name = Column(String(255), nullable=False)
    file_original_name = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    file_type = Column(String(50), nullable=False)
    file_hash = Column(String(64), nullable=False)  # SHA-256 hash
    
    # Controle de acesso
    access_token = Column(String(128), nullable=True, unique=True)
    access_expires_at = Column(DateTime, nullable=True)
    download_count = Column(Integer, default=0, nullable=False)
    max_downloads = Column(Integer, default=10, nullable=False)
    
    # Metadados da fatura
    description = Column(Text, nullable=True)
    notes = Column(Text, nullable=True)
    payment_terms = Column(String(100), nullable=True)
    payment_method = Column(String(50), nullable=True)
    
    # Informações de cobrança
    billing_address = Column(JSON, nullable=True)
    shipping_address = Column(JSON, nullable=True)
    
    # Itens da fatura (JSON para flexibilidade)
    line_items = Column(JSON, nullable=True)
    
    # Configurações de notificação
    notify_on_view = Column(Boolean, default=True, nullable=False)
    notify_on_due = Column(Boolean, default=True, nullable=False)
    reminder_sent = Column(Boolean, default=False, nullable=False)
    
    # Metadados adicionais
    metadata = Column(JSON, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )
    deleted_at = Column(DateTime, nullable=True)  # Soft delete
    
    # Relacionamentos
    tenant = relationship("Tenant")
    vendor = relationship("TVendorSupplier")
    customer = relationship("TCostumer")
    
    # Índices para performance
    __table_args__ = (
        Index("ix_invoice_vendor_customer", "vendor_id", "customer_id"),
        Index("ix_invoice_status_tenant", "status", "tenant_id"),
        Index("ix_invoice_due_date_status", "due_date", "status"),
        Index("ix_invoice_number_tenant", "invoice_number", "tenant_id"),
        Index("ix_invoice_created", "created_at"),
        Index("ix_invoice_access_token", "access_token"),
    )
    
    def __repr__(self):
        return (
            f"<TvendorInvoice(id={self.id}, "
            f"invoice_number='{self.invoice_number}', "
            f"status='{self.status}', "
            f"total_amount={self.total_amount})>"
        )
    
    @property
    def is_overdue(self) -> bool:
        """Verifica se a fatura está vencida."""
        if self.status in [InvoiceStatus.PAID, InvoiceStatus.CANCELLED]:
            return False
        return datetime.utcnow() > self.due_date
    
    @property
    def days_until_due(self) -> int:
        """Calcula dias até o vencimento."""
        delta = self.due_date - datetime.utcnow()
        return delta.days
    
    @property
    def outstanding_amount(self) -> Decimal:
        """Calcula valor em aberto."""
        return self.total_amount - self.paid_amount
    
    @property
    def is_fully_paid(self) -> bool:
        """Verifica se está totalmente paga."""
        return self.paid_amount >= self.total_amount
    
    @property
    def access_expired(self) -> bool:
        """Verifica se o acesso expirou."""
        if not self.access_expires_at:
            return False
        return datetime.utcnow() > self.access_expires_at
    
    @property
    def can_download(self) -> bool:
        """Verifica se ainda pode fazer download."""
        if self.access_expired:
            return False
        return self.download_count < self.max_downloads
    
    def generate_access_token(self, expires_hours: int = 24) -> str:
        """
        Gera token de acesso para download.
        
        Args:
            expires_hours: Horas até expiração
            
        Returns:
            str: Token de acesso
        """
        import secrets
        
        self.access_token = secrets.token_urlsafe(64)
        self.access_expires_at = datetime.utcnow() + timedelta(hours=expires_hours)
        self.updated_at = datetime.utcnow()
        
        return self.access_token
    
    def mark_as_viewed(self, viewed_by_id: uuid.UUID):
        """
        Marca fatura como visualizada.
        
        Args:
            viewed_by_id: ID do usuário que visualizou
        """
        if self.status == InvoiceStatus.SENT:
            self.status = InvoiceStatus.VIEWED
            self.viewed_date = datetime.utcnow()
            self.updated_at = datetime.utcnow()
    
    def mark_as_paid(self, paid_amount: Decimal, payment_date: datetime = None):
        """
        Marca fatura como paga.
        
        Args:
            paid_amount: Valor pago
            payment_date: Data do pagamento
        """
        self.paid_amount += paid_amount
        
        if self.is_fully_paid:
            self.status = InvoiceStatus.PAID
            self.paid_date = payment_date or datetime.utcnow()
        
        self.updated_at = datetime.utcnow()
    
    def update_status_if_overdue(self):
        """Atualiza status se vencida."""
        if self.is_overdue and self.status not in [InvoiceStatus.PAID, InvoiceStatus.CANCELLED]:
            self.status = InvoiceStatus.OVERDUE
            self.updated_at = datetime.utcnow()
    
    def increment_download_count(self):
        """Incrementa contador de downloads."""
        self.download_count += 1
        self.updated_at = datetime.utcnow()
    
    def soft_delete(self):
        """Soft delete da fatura."""
        self.deleted_at = datetime.utcnow()
        self.status = InvoiceStatus.CANCELLED
        self.updated_at = datetime.utcnow()
