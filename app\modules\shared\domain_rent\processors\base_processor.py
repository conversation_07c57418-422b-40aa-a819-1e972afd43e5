"""Base processor for domain registrar APIs."""

import abc  # noqa: E402
from datetime import date
from typing import Dict, List, Optional, Any

from app.modules.shared.domain_rent.schemas.domain_schemas import (  # noqa: E402
    DomainAvailabilityResult,
    DomainPriceInfo,
    ContactInfo,
)


class BaseProcessor(abc.ABC):
    """Abstract base class for domain registrar processors.

    All registrar-specific processors must inherit from this class and implement
    its abstract methods.
    """

    @property
    @abc.abstractmethod
    def registrar_name(self) -> str:
        """Return the name of the registrar."""
        pass

    @abc.abstractmethod
    async def search_availability(
        self, domain_name: str, tlds: Optional[List[str]] = None
    ) -> List[DomainAvailabilityResult]:
        """Search for domain availability.

        Args:
            domain_name: Domain name without TLD
            tlds: List of TLDs to check (if None, check common TLDs)

        Returns:
            List of availability results for each domain/TLD combination
        """
        pass

    @abc.abstractmethod
    async def register_domain(
        self,
        domain_name: str,
        tld: str,
        period_years: int,
        contacts: Dict[str, ContactInfo],
        nameservers: Optional[List[str]] = None,
        whois_privacy: bool = False,
    ) -> Dict[str, Any]:
        """Register a new domain.

        Args:
            domain_name: Domain name without TLD
            tld: TLD to register
            period_years: Registration period in years
            contacts: Contact information for the domain
            nameservers: List of nameserver hostnames
            whois_privacy: Whether to enable WHOIS privacy protection

        Returns:
            Dictionary with registration details (registrar-specific)
        """
        pass

    @abc.abstractmethod
    async def renew_domain(
        self,
        domain_name: str,
        tld: str,
        period_years: int,
        current_expiry_date: date,
    ) -> Dict[str, Any]:
        """Renew an existing domain.

        Args:
            domain_name: Domain name without TLD
            tld: TLD of the domain
            period_years: Renewal period in years
            current_expiry_date: Current expiry date of the domain

        Returns:
            Dictionary with renewal details (registrar-specific)
        """
        pass

    @abc.abstractmethod
    async def transfer_domain(
        self,
        domain_name: str,
        tld: str,
        auth_code: str,
        contacts: Dict[str, ContactInfo],
        nameservers: Optional[List[str]] = None,
    ) -> Dict[str, Any]:
        """Transfer a domain from another registrar.

        Args:
            domain_name: Domain name without TLD
            tld: TLD of the domain
            auth_code: Authorization code for transfer
            contacts: Contact information for the domain
            nameservers: List of nameserver hostnames

        Returns:
            Dictionary with transfer details (registrar-specific)
        """
        pass

    @abc.abstractmethod
    async def get_domain_info(self, domain_name: str, tld: str) -> Dict[str, Any]:
        """Get information about a domain.

        Args:
            domain_name: Domain name without TLD
            tld: TLD of the domain

        Returns:
            Dictionary with domain information (registrar-specific)
        """
        pass

    @abc.abstractmethod
    async def get_nameservers(self, domain_name: str, tld: str) -> List[str]:
        """Get nameservers for a domain.

        Args:
            domain_name: Domain name without TLD
            tld: TLD of the domain

        Returns:
            List of nameserver hostnames
        """
        pass

    @abc.abstractmethod
    async def update_nameservers(self, domain_name: str, tld: str, nameservers: List[str]) -> bool:
        """Update nameservers for a domain.

        Args:
            domain_name: Domain name without TLD
            tld: TLD of the domain
            nameservers: List of nameserver hostnames

        Returns:
            True if successful, False otherwise
        """
        pass

    @abc.abstractmethod
    async def get_auth_code(self, domain_name: str, tld: str) -> str:
        """Get authorization code for domain transfer.

        Args:
            domain_name: Domain name without TLD
            tld: TLD of the domain

        Returns:
            Authorization code
        """
        pass

    @abc.abstractmethod
    async def toggle_whois_privacy(self, domain_name: str, tld: str, enable: bool) -> bool:
        """Enable or disable WHOIS privacy for a domain.

        Args:
            domain_name: Domain name without TLD
            tld: TLD of the domain
            enable: Whether to enable or disable WHOIS privacy

        Returns:
            True if successful, False otherwise
        """
        pass

    @abc.abstractmethod
    async def toggle_domain_lock(self, domain_name: str, tld: str, lock: bool) -> bool:
        """Lock or unlock a domain for transfer.

        Args:
            domain_name: Domain name without TLD
            tld: TLD of the domain
            lock: Whether to lock or unlock the domain

        Returns:
            True if successful, False otherwise
        """
        pass
