"""
Tax specific schemas for tenant settings.
"""

from typing import Optional
from decimal import Decimal

from pydantic import BaseModel, Field, validator


class TaxSettingsUpdate(BaseModel):
    """Schema for updating tax settings."""
    
    base_tax_rate: Optional[Decimal] = Field(
        None, 
        ge=0, 
        le=100, 
        max_digits=5, 
        decimal_places=4,
        description="Base tax rate percentage"
    )
    tax_calculation_method: Optional[str] = Field(
        None, 
        description="Tax calculation method: 'incremental' or 'inclusive'"
    )

    @validator('tax_calculation_method')
    def validate_tax_calculation_method(cls, v):
        """Validate tax calculation method."""
        if v is not None:
            allowed_methods = ['incremental', 'inclusive']
            if v not in allowed_methods:
                raise ValueError(f'Tax calculation method must be one of: {allowed_methods}')
        return v
