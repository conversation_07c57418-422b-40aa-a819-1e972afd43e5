"""Bank transfer payment processor implementation."""

import logging  # noqa: E402
import uuid
from typing import Dict, Any, Optional
from decimal import Decimal

from app.modules.core.payments.processors.base import PaymentProcessorInterface  # noqa: E402
from app.modules.core.payments.models.payment_transaction import PaymentStatus

logger = logging.getLogger(__name__)


class BankTransferProcessor(PaymentProcessorInterface):
    """
    Bank transfer payment processor implementation.

    This processor handles manual bank transfers where the tenant provides
    their bank account details for customers to make transfers.
    """

    def __init__(self, processor_config: Dict[str, Any]):
        """
        Initialize the bank transfer processor with the given configuration.

        Args:
            processor_config: A dictionary containing the processor configuration.
                This should include bank account details.
        """
        super().__init__(processor_config)

        # Bank account details
        self.bank_name = self.config.get("bank_name")
        self.account_holder = self.config.get("account_holder")
        self.account_number = self.config.get("account_number")
        self.branch_code = self.config.get("branch_code")
        self.swift_code = self.config.get("swift_code")
        self.iban = self.config.get("iban")
        self.pix_key = self.config.get("pix_key")  # For Brazilian PIX transfers

        # Validation
        if not self.bank_name or not self.account_holder or not self.account_number:
            raise ValueError("Bank name, account holder, and account number are required")

    async def process_payment(
        self,
        amount: Decimal,
        currency: str,
        payment_method_id: Optional[str] = None,
        customer_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Process a bank transfer payment.

        For bank transfers, this method generates a reference number and returns
        the bank account details for the customer to make the transfer.

        Args:
            amount: The amount to transfer.
            currency: The currency code (e.g., 'USD', 'BRL').
            payment_method_id: Not used for bank transfers.
            customer_id: Not used for bank transfers.
            metadata: Optional additional data to include with the payment.

        Returns:
            A dictionary containing the payment details, including:
                - external_id: A generated reference number for the transfer.
                - status: Always PENDING for bank transfers.
                - bank_details: The bank account details for the transfer.
        """
        try:
            # Generate a reference number
            reference = str(uuid.uuid4())[:8].upper()

            # Include any reference prefix from metadata
            if metadata and "reference_prefix" in metadata:
                reference = f"{metadata['reference_prefix']}-{reference}"

            # Prepare bank details
            bank_details = {
                "bank_name": self.bank_name,
                "account_holder": self.account_holder,
                "account_number": self.account_number,
                "branch_code": self.branch_code,
                "swift_code": self.swift_code,
                "iban": self.iban,
                "pix_key": self.pix_key,
                "reference": reference,
                "amount": str(amount),
                "currency": currency,
            }

            # Add any additional instructions
            if self.config.get("payment_instructions"):
                bank_details["payment_instructions"] = self.config.get("payment_instructions")

            return {
                "external_id": reference,
                "status": PaymentStatus.PENDING,
                "bank_details": bank_details,
            }

        except Exception as e:
            logger.error(f"Unexpected error processing bank transfer: {str(e)}")
            return {
                "status": PaymentStatus.FAILED,
                "error": f"Unexpected error: {str(e)}",
            }

    async def process_refund(
        self,
        payment_id: str,
        amount: Optional[Decimal] = None,
        reason: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Process a refund for a bank transfer.

        For bank transfers, this method generates a reference number for the refund
        but requires manual processing by the tenant.

        Args:
            payment_id: The reference number of the original payment.
            amount: The amount to refund. If None, refund the full amount.
            reason: An optional reason for the refund.
            metadata: Optional additional data to include with the refund.

        Returns:
            A dictionary containing the refund details, including:
                - external_id: A generated reference number for the refund.
                - status: Always PENDING for bank transfer refunds.
                - refund_details: Details for processing the refund manually.
        """
        try:
            # Generate a reference number for the refund
            refund_reference = f"REF-{str(uuid.uuid4())[:8].upper()}"

            # Prepare refund details
            refund_details = {
                "original_reference": payment_id,
                "refund_reference": refund_reference,
                "amount": str(amount) if amount else "Full amount",
                "reason": reason or "No reason provided",
                "instructions": "This refund requires manual processing. Please transfer the funds to the customer's bank account.",  # noqa: E501
            }

            # Add customer bank details if provided in metadata
            if metadata and "customer_bank_details" in metadata:
                refund_details["customer_bank_details"] = metadata["customer_bank_details"]

            return {
                "external_id": refund_reference,
                "status": PaymentStatus.PENDING,
                "refund_details": refund_details,
            }

        except Exception as e:
            logger.error(f"Unexpected error processing bank transfer refund: {str(e)}")
            return {
                "status": PaymentStatus.FAILED,
                "error": f"Unexpected error: {str(e)}",
            }

    async def get_payment_status(self, payment_id: str) -> Dict[str, Any]:
        """
        Get the status of a bank transfer payment.

        For bank transfers, the status must be updated manually by the tenant
        after confirming the transfer. This method always returns PENDING.

        Args:
            payment_id: The reference number of the payment.

        Returns:
            A dictionary containing the payment details, including:
                - status: Always PENDING for bank transfers until manually updated.
                - external_id: The reference number of the payment.
        """
        # Bank transfers require manual status updates
        return {
            "status": PaymentStatus.PENDING,
            "external_id": payment_id,
            "message": "Bank transfer status must be updated manually after confirming the transfer.",  # noqa: E501
        }

    async def get_refund_status(self, refund_id: str) -> Dict[str, Any]:
        """
        Get the status of a bank transfer refund.

        For bank transfers, the refund status must be updated manually by the tenant
        after processing the refund. This method always returns PENDING.

        Args:
            refund_id: The reference number of the refund.

        Returns:
            A dictionary containing the refund details, including:
                - status: Always PENDING for bank transfer refunds until manually updated.
                - external_id: The reference number of the refund.
        """
        # Bank transfer refunds require manual status updates
        return {
            "status": PaymentStatus.PENDING,
            "external_id": refund_id,
            "message": "Bank transfer refund status must be updated manually after processing the refund.",  # noqa: E501
        }

    async def create_payment_method(
        self,
        method_type: str,
        method_details: Dict[str, Any],
        customer_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Create a payment method for bank transfers.

        For bank transfers, this method is not applicable as the tenant's bank account
        details are used for all transfers.

        Args:
            method_type: Not used for bank transfers.
            method_details: Not used for bank transfers.
            customer_id: Not used for bank transfers.

        Returns:
            A dictionary containing an error message.
        """
        return {
            "error": "Bank transfers do not support creating payment methods. The tenant's bank account details are used for all transfers.",  # noqa: E501
        }

    async def delete_payment_method(self, method_id: str) -> Dict[str, Any]:
        """
        Delete a payment method for bank transfers.

        For bank transfers, this method is not applicable.

        Args:
            method_id: Not used for bank transfers.

        Returns:
            A dictionary containing an error message.
        """
        return {
            "success": False,
            "error": "Bank transfers do not support deleting payment methods.",
        }

    async def create_customer(self, customer_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a customer for bank transfers.

        For bank transfers, this method is not applicable as customer details
        are managed separately.

        Args:
            customer_details: Not used for bank transfers.

        Returns:
            A dictionary containing an error message.
        """
        return {
            "error": "Bank transfers do not support creating customers. Customer details should be managed separately.",  # noqa: E501
        }

    async def update_customer(
        self, customer_id: str, customer_details: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Update a customer for bank transfers.

        For bank transfers, this method is not applicable.

        Args:
            customer_id: Not used for bank transfers.
            customer_details: Not used for bank transfers.

        Returns:
            A dictionary containing an error message.
        """
        return {
            "error": "Bank transfers do not support updating customers.",
        }

    async def delete_customer(self, customer_id: str) -> Dict[str, Any]:
        """
        Delete a customer for bank transfers.

        For bank transfers, this method is not applicable.

        Args:
            customer_id: Not used for bank transfers.

        Returns:
            A dictionary containing an error message.
        """
        return {
            "success": False,
            "error": "Bank transfers do not support deleting customers.",
        }

    @staticmethod
    def map_status_to_internal(processor_status: str) -> PaymentStatus:
        """
        Map a bank transfer-specific status to an internal PaymentStatus.

        For bank transfers, the status is manually updated by the tenant.

        Args:
            processor_status: The status from the bank transfer processor.

        Returns:
            The corresponding internal PaymentStatus.
        """
        # Bank transfers use the same status names as the internal PaymentStatus
        try:
            return PaymentStatus(processor_status.lower())
        except ValueError:
            return PaymentStatus.PENDING
