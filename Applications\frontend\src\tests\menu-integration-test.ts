/**
 * Teste de integração completo do sistema de menu digital
 * Valida categorias, itens, drag & drop e isolamento de dados
 */

import { menuService } from '@/services/api/menuService';
import { authApi } from '@/lib/api/auth';
import { mediaUploadService } from '@/lib/services/mediaUploadService';
import Cookies from 'js-cookie';

export interface MenuTestResult {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

export class MenuIntegrationTest {
  /**
   * Testa listagem de categorias do menu
   */
  static async testGetMenuCategories(): Promise<MenuTestResult> {
    try {
      console.log('🔍 Testing get menu categories...');
      
      // Login como tenant owner
      const formData = new URLSearchParams();
      formData.append('email', '<EMAIL>');
      formData.append('password', 'password');

      const loginResponse = await authApi.login(formData);
      Cookies.set('access_token', loginResponse.access_token);
      Cookies.set('tenant_id', 'f19423fc-b0b0-45d5-a813-6f88ae2fb792');
      
      const categories = await menuService.getCategories({
        include_children: true,
        include_items: false
      });
      
      return {
        success: true,
        message: 'Get menu categories successful',
        data: {
          categoryCount: categories.length,
          categories: categories.map(cat => ({
            id: cat.id,
            name: cat.name,
            displayOrder: cat.display_order,
            hasParent: !!cat.parent_id,
            isActive: cat.is_active
          }))
        }
      };
    } catch (error: any) {
      return {
        success: false,
        message: 'Get menu categories failed',
        error: error.message
      };
    }
  }

  /**
   * Testa listagem de itens do menu
   */
  static async testGetMenuItems(): Promise<MenuTestResult> {
    try {
      console.log('🔍 Testing get menu items...');
      
      // Login como tenant owner
      const formData = new URLSearchParams();
      formData.append('email', '<EMAIL>');
      formData.append('password', 'password');

      const loginResponse = await authApi.login(formData);
      Cookies.set('access_token', loginResponse.access_token);
      Cookies.set('tenant_id', 'f19423fc-b0b0-45d5-a813-6f88ae2fb792');
      
      const items = await menuService.getItems({
        include_details: true
      });
      
      return {
        success: true,
        message: 'Get menu items successful',
        data: {
          itemCount: items.length,
          items: items.map(item => ({
            id: item.id,
            name: item.name,
            basePrice: item.base_price,
            categoryId: item.category_id,
            isActive: item.is_active,
            isAvailable: item.is_available
          }))
        }
      };
    } catch (error: any) {
      return {
        success: false,
        message: 'Get menu items failed',
        error: error.message
      };
    }
  }

  /**
   * Testa criação de categoria
   */
  static async testCreateCategory(): Promise<MenuTestResult> {
    try {
      console.log('🔍 Testing create category...');
      
      // Login como tenant owner
      const formData = new URLSearchParams();
      formData.append('email', '<EMAIL>');
      formData.append('password', 'password');

      const loginResponse = await authApi.login(formData);
      Cookies.set('access_token', loginResponse.access_token);
      Cookies.set('tenant_id', 'f19423fc-b0b0-45d5-a813-6f88ae2fb792');
      
      const categoryData = {
        name: 'Categoria Teste API',
        description: 'Categoria criada via teste de integração',
        display_order: 99
      };
      
      const newCategory = await menuService.createCategory(categoryData);
      
      return {
        success: true,
        message: 'Create category successful',
        data: {
          id: newCategory.id,
          name: newCategory.name,
          description: newCategory.description,
          displayOrder: newCategory.display_order,
          isActive: newCategory.is_active
        }
      };
    } catch (error: any) {
      return {
        success: false,
        message: 'Create category failed',
        error: error.message
      };
    }
  }

  /**
   * Testa criação de item do menu
   */
  static async testCreateMenuItem(): Promise<MenuTestResult> {
    try {
      console.log('🔍 Testing create menu item...');
      
      // Login como tenant owner
      const formData = new URLSearchParams();
      formData.append('email', '<EMAIL>');
      formData.append('password', 'password');

      const loginResponse = await authApi.login(formData);
      Cookies.set('access_token', loginResponse.access_token);
      Cookies.set('tenant_id', 'f19423fc-b0b0-45d5-a813-6f88ae2fb792');
      
      // Primeiro, obter uma categoria existente
      const categories = await menuService.getCategories();
      if (categories.length === 0) {
        throw new Error('No categories available for testing');
      }
      
      const itemData = {
        name: 'Item Teste API',
        description: 'Item criado via teste de integração',
        base_price: 15.99,
        category_id: categories[0].id,
        display_order: 99,
        preparation_time: 15
      };
      
      const newItem = await menuService.createItem(itemData);
      
      return {
        success: true,
        message: 'Create menu item successful',
        data: {
          id: newItem.id,
          name: newItem.name,
          description: newItem.description,
          basePrice: newItem.base_price,
          categoryId: newItem.category_id,
          isActive: newItem.is_active,
          isAvailable: newItem.is_available
        }
      };
    } catch (error: any) {
      return {
        success: false,
        message: 'Create menu item failed',
        error: error.message
      };
    }
  }

  /**
   * Testa upload de mídia para item do menu
   */
  static async testMenuMediaUpload(): Promise<MenuTestResult> {
    try {
      console.log('🔍 Testing menu media upload...');

      // Login como tenant owner
      const formData = new URLSearchParams();
      formData.append('email', '<EMAIL>');
      formData.append('password', 'password');

      const loginResponse = await authApi.login(formData);
      Cookies.set('access_token', loginResponse.access_token);
      Cookies.set('tenant_id', 'f19423fc-b0b0-45d5-a813-6f88ae2fb792');

      // Criar um arquivo de teste (1x1 pixel PNG)
      const testImageData = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAHGbKdMWwAAAABJRU5ErkJggg==';

      // Converter base64 para File
      const response = await fetch(testImageData);
      const blob = await response.blob();
      const testFile = new File([blob], 'test-image.png', { type: 'image/png' });

      // Obter um item existente para associar a mídia
      const items = await menuService.getItems();
      if (items.length === 0) {
        throw new Error('No menu items available for media upload test');
      }

      const testItem = items[0];

      // Fazer upload da mídia
      const uploadResult = await mediaUploadService.uploadMenuItemMedia(
        testItem.id,
        [testFile],
        {
          onProgress: (progress) => {
            console.log(`Upload progress: ${progress}%`);
          }
        }
      );

      return {
        success: uploadResult.success,
        message: uploadResult.success ? 'Menu media upload successful' : 'Menu media upload failed',
        data: {
          itemId: testItem.id,
          uploadedFiles: uploadResult.uploads?.length || 0,
          firstFileUrl: uploadResult.uploads?.[0]?.file_url,
          contextType: uploadResult.uploads?.[0]?.context_type,
          contextId: uploadResult.uploads?.[0]?.context_id
        },
        error: uploadResult.error
      };
    } catch (error: any) {
      return {
        success: false,
        message: 'Menu media upload test failed',
        error: error.message
      };
    }
  }

  /**
   * Testa isolamento de dados entre tenants
   */
  static async testMenuDataIsolation(): Promise<MenuTestResult> {
    try {
      console.log('🔍 Testing menu data isolation...');
      
      // Login como tenant owner
      const formData = new URLSearchParams();
      formData.append('email', '<EMAIL>');
      formData.append('password', 'password');

      const loginResponse = await authApi.login(formData);
      Cookies.set('access_token', loginResponse.access_token);
      Cookies.set('tenant_id', 'f19423fc-b0b0-45d5-a813-6f88ae2fb792');
      
      // Obter dados do próprio tenant
      const ownCategories = await menuService.getCategories();
      const ownItems = await menuService.getItems();
      
      // Verificar se os dados foram carregados corretamente
      const categoriesIsolated = ownCategories.length > 0;
      const itemsIsolated = ownItems.length > 0;
      
      return {
        success: true,
        message: 'Menu data isolation test completed',
        data: {
          ownCategoryCount: ownCategories.length,
          ownItemCount: ownItems.length,
          categoriesIsolated,
          itemsIsolated,
          isolationWorking: categoriesIsolated && itemsIsolated
        }
      };
    } catch (error: any) {
      return {
        success: false,
        message: 'Menu data isolation test failed',
        error: error.message
      };
    }
  }

  /**
   * Executa todos os testes de menu
   */
  static async runAllTests(): Promise<{
    getCategories: MenuTestResult;
    getItems: MenuTestResult;
    createCategory: MenuTestResult;
    createMenuItem: MenuTestResult;
    mediaUpload: MenuTestResult;
    dataIsolation: MenuTestResult;
    summary: {
      total: number;
      passed: number;
      failed: number;
    };
  }> {
    console.log('🚀 Starting Menu Integration Tests...');

    const getCategories = await this.testGetMenuCategories();
    const getItems = await this.testGetMenuItems();
    const createCategory = await this.testCreateCategory();
    const createMenuItem = await this.testCreateMenuItem();
    const mediaUpload = await this.testMenuMediaUpload();
    const dataIsolation = await this.testMenuDataIsolation();

    const results = [
      getCategories,
      getItems,
      createCategory,
      createMenuItem,
      mediaUpload,
      dataIsolation
    ];
    
    const passed = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;

    console.log('📊 Menu Test Results Summary:');
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📈 Total: ${results.length}`);

    // Limpar cookies de teste
    Cookies.remove('access_token');
    Cookies.remove('refresh_token');
    Cookies.remove('tenant_id');

    return {
      getCategories,
      getItems,
      createCategory,
      createMenuItem,
      mediaUpload,
      dataIsolation,
      summary: {
        total: results.length,
        passed,
        failed
      }
    };
  }
}

// Função para executar testes via console do navegador
export const runMenuTests = () => {
  return MenuIntegrationTest.runAllTests();
};

// Exportar para uso global no navegador (desenvolvimento)
if (typeof window !== 'undefined') {
  (window as any).runMenuTests = runMenuTests;
  (window as any).MenuIntegrationTest = MenuIntegrationTest;
}
