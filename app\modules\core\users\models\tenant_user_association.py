from sqlalchemy import <PERSON>um<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Text
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from typing import TYPE_CHECKING
import uuid
from datetime import datetime

from app.db.base import Base

if TYPE_CHECKING:
    pass


class TenantUserAssociation(Base):
    """
    Association table linking Users with Tenants and defining their role within the tenant.

    Roles in tenant_user_association determine what permissions a user has within a tenant:
    - TenantRole.OWNER: Tenant owner with administrative access
    - TenantRole.MANAGER: Tenant manager with administrative access to specific areas
    - TenantRole.STAFF: Tenant staff with operational permissions
    - TenantRole.TVENDOR: Authorized B2B seller with product management permissions
    - TenantRole.SUPPLIER: External supplier with access to supplier portal
    - TenantRole.TCOSTUMER: Authorized B2B buyer with bulk purchasing access
    - TenantRole.CUSTOMER: Tenant customer/client with limited access

    The role hierarchy is: OWNER > MANAGER > STAFF > TVENDOR > SUPPLIER > TCOSTUMER > CUSTOMER

    See app.modules.core.roles.models.roles for full role definitions and hierarchy.
    """

    __tablename__ = "tenant_user_associations"

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Foreign keys
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    tenant_id = Column(
        UUID(as_uuid=True),
        ForeignKey("tenants.id", ondelete="CASCADE"),
        nullable=False,
    )

    # User's role within this specific tenant
    # Possible values include: 'owner', 'manager', 'staff', 'tvendor', 'supplier', 'tcostumer', 'customer'
    role = Column(String, nullable=False)

    # Staff sub-role for more granular permissions (used when role is 'staff')
    staff_sub_role = Column(String, nullable=True)

    # Market context for B2B/B2C operations
    # Possible values: 'b2b', 'b2c', 'marketplace'
    market_context = Column(String, nullable=True, default='b2c')

    # B2B Authorization fields for TVENDOR and TCOSTUMER roles
    b2b_authorized = Column(Boolean, default=False, nullable=False)
    b2b_authorization_date = Column(DateTime, nullable=True)
    b2b_authorization_notes = Column(Text, nullable=True)
    b2b_authorized_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # Business verification fields for B2B roles
    business_name = Column(String, nullable=True)
    business_registration_number = Column(String, nullable=True)
    business_verification_status = Column(String, nullable=True)  # 'pending', 'verified', 'rejected'
    business_verification_date = Column(DateTime, nullable=True)

    # Pricing tier for B2B customers (TCOSTUMER role)
    pricing_tier = Column(String, nullable=True)  # 'standard', 'premium', 'enterprise'

    # Commission rate for B2B vendors (TVENDOR role)
    commission_rate = Column(sa.Numeric(5, 2), nullable=True)  # Percentage (e.g., 5.50 for 5.5%)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    user = relationship(
        "User", back_populates="tenant_associations", foreign_keys=[user_id]
    )
    tenant = relationship(
        "Tenant", back_populates="user_associations"
    )
    authorized_by_user = relationship(
        "User", foreign_keys=[b2b_authorized_by]
    )

    __table_args__ = (
        sa.Index("ix_tenant_user_association_role", "role"),
        sa.Index("ix_tenant_user_association_staff_sub_role", "staff_sub_role"),
        sa.Index("ix_tenant_user_association_market_context", "market_context"),
        sa.Index("ix_tenant_user_association_b2b_authorized", "b2b_authorized"),
        sa.Index("ix_tenant_user_association_business_verification", "business_verification_status"),
        sa.Index("ix_tenant_user_association_pricing_tier", "pricing_tier"),
        # Composite indexes for common queries
        sa.Index("ix_tenant_user_association_role_market", "role", "market_context"),
        sa.Index("ix_tenant_user_association_tenant_role", "tenant_id", "role"),
    )

    def __repr__(self):
        return (
            f"<TenantUserAssociation(user_id={self.user_id}, tenant_id={self.tenant_id}, "
            f"role='{self.role}', market_context='{self.market_context}', "
            f"b2b_authorized={self.b2b_authorized})>"
        )

    @property
    def is_b2b_role(self) -> bool:
        """Check if this association represents a B2B role."""
        return self.role in ['tvendor', 'tcostumer']

    @property
    def requires_business_verification(self) -> bool:
        """Check if this role requires business verification."""
        return self.is_b2b_role

    @property
    def is_authorized_for_b2b(self) -> bool:
        """Check if user is authorized for B2B operations."""
        if not self.is_b2b_role:
            return False
        return self.b2b_authorized and self.business_verification_status == 'verified'

    def can_access_market_context(self, context: str) -> bool:
        """
        Check if this association can access a specific market context.
        
        Args:
            context: Market context ('b2b', 'b2c', 'marketplace')
            
        Returns:
            bool: True if access is allowed
        """
        from app.modules.core.roles.models.roles import RolePermissions
        return RolePermissions.has_market_access(self.role, context)

    def get_effective_permissions(self) -> dict:
        """
        Get effective permissions for this association.
        
        Returns:
            dict: Dictionary containing permission information
        """
        from app.modules.core.roles.models.roles import (
            RolePermissions, TVendorPermissions, TCostumerPermissions
        )
        
        permissions = {
            'role': self.role,
            'market_contexts': RolePermissions.get_user_market_contexts(self.role),
            'is_b2b': self.is_b2b_role,
            'b2b_authorized': self.is_authorized_for_b2b,
            'hierarchy_level': RolePermissions.ROLE_HIERARCHY.get(self.role, 0),
        }
        
        # Add role-specific permissions
        if self.role == 'tvendor' and self.is_authorized_for_b2b:
            permissions['tvendor_permissions'] = [perm.value for perm in TVendorPermissions]
        elif self.role == 'tcostumer' and self.is_authorized_for_b2b:
            permissions['tcostumer_permissions'] = [perm.value for perm in TCostumerPermissions]
            permissions['pricing_tier'] = self.pricing_tier
            
        return permissions
