"""
Audit and Log Integration Service

This service demonstrates how the audit and logging systems work in synchronization
to provide comprehensive system observability and compliance tracking.
"""

import logging
import uuid
from typing import Any, Dict, Optional
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession

from app.modules.core.audit.services.audit_service import AuditService
from app.modules.core.audit.schemas.audit import (
    AuditAction, AuditResource, OperationType, AuditStatus, SecurityLevel
)
from app.modules.core.functions.logs.services.log_service import LogService
from app.modules.core.functions.logs.schemas.log_schemas import (
    LogLevel, LogCategory
)

logger = logging.getLogger(__name__)


class AuditLogIntegrationService:
    """
    Integration service that coordinates audit and logging systems.
    
    This service provides a unified interface for recording both audit trails
    and system logs for critical operations, ensuring complete observability
    and compliance tracking.
    """

    def __init__(self, db: AsyncSession):
        self.db = db
        self.audit_service = AuditService(db)
        self.log_service = LogService(db)

    async def record_operation(
        self,
        # Audit parameters
        user_id: str,
        action: AuditAction,
        resource: AuditResource,
        resource_id: str,
        tenant_id: Optional[str] = None,
        operation_type: OperationType = OperationType.EXECUTE,
        old_values: Optional[Dict[str, Any]] = None,
        new_values: Optional[Dict[str, Any]] = None,
        status: AuditStatus = AuditStatus.SUCCESS,
        security_level: SecurityLevel = SecurityLevel.NORMAL,
        
        # Log parameters
        log_level: LogLevel = LogLevel.INFO,
        log_category: LogCategory = LogCategory.BUSINESS,
        message: str = "",
        
        # Common context
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None,
        request_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        
        # Additional details
        details: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None,
        duration_ms: Optional[str] = None,
        business_process: Optional[str] = None,
        
        # Compliance and monitoring
        compliance_tags: Optional[list] = None,
    ) -> Dict[str, str]:
        """
        Record both audit and log entries for a system operation.
        
        This method ensures that critical operations are tracked in both
        the audit system (for compliance and security) and the logging
        system (for operational monitoring and debugging).
        
        Returns:
            Dictionary with audit_id and log_id
        """
        try:
            # Generate correlation ID if not provided
            if not correlation_id:
                correlation_id = str(uuid.uuid4())
            
            # Record audit entry first
            audit_id = await self.audit_service.log_action(
                user_id=user_id,
                action=action,
                resource=resource,
                resource_id=resource_id,
                tenant_id=tenant_id,
                operation_type=operation_type,
                old_values=old_values,
                new_values=new_values,
                details=details,
                ip_address=ip_address,
                user_agent=user_agent,
                session_id=session_id,
                request_id=request_id,
                correlation_id=correlation_id,
                security_level=security_level,
                compliance_tags=compliance_tags,
                status=status,
                error_message=error_message,
                duration_ms=duration_ms,
            )
            
            # Create comprehensive log message if not provided
            if not message:
                message = self._generate_log_message(action, resource, resource_id, status)
            
            # Record log entry with audit correlation
            log_id = await self.log_service.create_log(
                level=log_level,
                category=log_category,
                logger_name=f"audit_integration.{resource.value.lower()}",
                message=message,
                details=details,
                user_id=user_id,
                tenant_id=tenant_id,
                session_id=session_id,
                request_id=request_id,
                correlation_id=correlation_id,
                ip_address=ip_address,
                user_agent=user_agent,
                duration_ms=duration_ms,
                business_process=business_process,
                audit_log_id=audit_id,  # Link to audit entry
                tags=[
                    f"audit:{action.value}",
                    f"resource:{resource.value}",
                    f"security:{security_level.value}",
                ] + (compliance_tags or []),
            )
            
            return {
                "audit_id": audit_id,
                "log_id": log_id,
                "correlation_id": correlation_id,
            }
            
        except Exception as e:
            logger.error(f"Error recording integrated audit/log operation: {e}", exc_info=True)
            # Return empty IDs to avoid breaking the main operation
            return {
                "audit_id": str(uuid.uuid4()),
                "log_id": str(uuid.uuid4()),
                "correlation_id": correlation_id or str(uuid.uuid4()),
            }

    async def record_b2b_operation(
        self,
        user_id: str,
        action: AuditAction,
        resource_id: str,
        tenant_id: str,
        operation_details: Dict[str, Any],
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None,
        request_id: Optional[str] = None,
    ) -> Dict[str, str]:
        """
        Record B2B-specific operations with enhanced tracking.
        
        B2B operations require special attention due to their business impact
        and compliance requirements.
        """
        return await self.record_operation(
            user_id=user_id,
            action=action,
            resource=AuditResource.B2B_AUTHORIZATION,
            resource_id=resource_id,
            tenant_id=tenant_id,
            operation_type=OperationType.EXECUTE,
            new_values=operation_details,
            status=AuditStatus.SUCCESS,
            security_level=SecurityLevel.SENSITIVE,
            log_level=LogLevel.INFO,
            log_category=LogCategory.BUSINESS,
            message=f"B2B operation: {action.value} for resource {resource_id}",
            ip_address=ip_address,
            user_agent=user_agent,
            session_id=session_id,
            request_id=request_id,
            business_process="B2B_OPERATIONS",
            compliance_tags=["B2B", "BUSINESS_CRITICAL"],
            details=operation_details,
        )

    async def record_financial_operation(
        self,
        user_id: str,
        action: AuditAction,
        resource: AuditResource,
        resource_id: str,
        tenant_id: str,
        amount: float,
        currency: str,
        transaction_details: Dict[str, Any],
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None,
        request_id: Optional[str] = None,
    ) -> Dict[str, str]:
        """
        Record financial operations with enhanced security and compliance tracking.
        
        Financial operations require the highest level of audit and logging
        due to regulatory requirements and business impact.
        """
        enhanced_details = {
            **transaction_details,
            "amount": amount,
            "currency": currency,
            "financial_operation": True,
        }
        
        return await self.record_operation(
            user_id=user_id,
            action=action,
            resource=resource,
            resource_id=resource_id,
            tenant_id=tenant_id,
            operation_type=OperationType.EXECUTE,
            new_values=enhanced_details,
            status=AuditStatus.SUCCESS,
            security_level=SecurityLevel.CRITICAL,
            log_level=LogLevel.INFO,
            log_category=LogCategory.BUSINESS,
            message=f"Financial operation: {action.value} - {currency} {amount}",
            ip_address=ip_address,
            user_agent=user_agent,
            session_id=session_id,
            request_id=request_id,
            business_process="FINANCIAL_OPERATIONS",
            compliance_tags=["FINANCIAL", "PCI", "CRITICAL"],
            details=enhanced_details,
        )

    async def record_security_event(
        self,
        user_id: Optional[str],
        action: AuditAction,
        resource: AuditResource,
        resource_id: str,
        security_details: Dict[str, Any],
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None,
        request_id: Optional[str] = None,
        is_suspicious: bool = False,
    ) -> Dict[str, str]:
        """
        Record security-related events with enhanced monitoring.
        
        Security events require immediate attention and comprehensive logging
        for threat detection and compliance.
        """
        log_level = LogLevel.WARNING if is_suspicious else LogLevel.INFO
        security_level = SecurityLevel.CRITICAL if is_suspicious else SecurityLevel.SENSITIVE
        
        return await self.record_operation(
            user_id=user_id,
            action=action,
            resource=resource,
            resource_id=resource_id,
            operation_type=OperationType.EXECUTE,
            new_values=security_details,
            status=AuditStatus.SUCCESS,
            security_level=security_level,
            log_level=log_level,
            log_category=LogCategory.SECURITY,
            message=f"Security event: {action.value} - {'SUSPICIOUS' if is_suspicious else 'NORMAL'}",
            ip_address=ip_address,
            user_agent=user_agent,
            session_id=session_id,
            request_id=request_id,
            business_process="SECURITY_MONITORING",
            compliance_tags=["SECURITY", "GDPR"] + (["SUSPICIOUS"] if is_suspicious else []),
            details=security_details,
        )

    def _generate_log_message(
        self,
        action: AuditAction,
        resource: AuditResource,
        resource_id: str,
        status: AuditStatus,
    ) -> str:
        """Generate a descriptive log message for the operation."""
        status_text = "completed successfully" if status == AuditStatus.SUCCESS else "failed"
        return f"{action.value} operation on {resource.value} (ID: {resource_id}) {status_text}"

    async def get_operation_history(
        self,
        correlation_id: str,
    ) -> Dict[str, Any]:
        """
        Get complete operation history using correlation ID.
        
        This method retrieves both audit and log entries for a complete
        view of an operation's lifecycle.
        """
        try:
            # Get audit logs
            from app.modules.core.audit.schemas.audit import AuditLogFilter
            audit_filters = AuditLogFilter(
                correlation_id=correlation_id,
                skip=0,
                limit=100,
                sort_by="timestamp",
                sort_order="asc",
            )
            audit_logs = await self.audit_service.get_audit_logs(audit_filters)
            
            # Get system logs
            from app.modules.core.functions.logs.schemas.log_schemas import LogFilter
            log_filters = LogFilter(
                correlation_id=correlation_id,
                skip=0,
                limit=100,
                sort_by="timestamp",
                sort_order="asc",
            )
            system_logs = await self.log_service.get_logs(log_filters)
            
            return {
                "correlation_id": correlation_id,
                "audit_logs": audit_logs.dict(),
                "system_logs": system_logs.dict(),
                "total_entries": audit_logs.total + system_logs.total,
            }
            
        except Exception as e:
            logger.error(f"Error getting operation history: {e}", exc_info=True)
            return {
                "correlation_id": correlation_id,
                "audit_logs": {"items": [], "total": 0},
                "system_logs": {"items": [], "total": 0},
                "total_entries": 0,
                "error": str(e),
            }
