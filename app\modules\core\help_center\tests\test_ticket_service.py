"""
Testes para o TicketService.
"""

import pytest
from datetime import datetime, timezone
from uuid import uuid4

from app.modules.core.help_center.services.ticket_service import TicketService
from app.modules.core.help_center.schemas import TicketCreate, TicketUpdate
from app.modules.core.help_center.models import TicketStatus, TicketPriority, TicketCategory
from app.modules.core.users.models.user import User


@pytest.fixture
async def ticket_service(db_session):
    """Fixture para TicketService."""
    return TicketService(db_session)


@pytest.fixture
async def test_user(db_session):
    """Fixture para usuário de teste."""
    user = User(
        id=uuid4(),
        email="<EMAIL>",
        full_name="Test User",
        system_role="user",
        is_active=True
    )
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    return user


@pytest.fixture
async def admin_user(db_session):
    """Fixture para usuário admin."""
    user = User(
        id=uuid4(),
        email="<EMAIL>",
        full_name="Admin User",
        system_role="admin",
        is_active=True
    )
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    return user


class TestTicketService:
    """Testes para o TicketService."""

    async def test_create_ticket(self, ticket_service, test_user):
        """Testa criação de ticket."""
        ticket_data = TicketCreate(
            title="Problema de teste",
            description="Descrição do problema de teste",
            category=TicketCategory.PROBLEM
        )
        
        ticket = await ticket_service.create_ticket(ticket_data, test_user)
        
        assert ticket is not None
        assert ticket.title == "Problema de teste"
        assert ticket.description == "Descrição do problema de teste"
        assert ticket.category == TicketCategory.PROBLEM
        assert ticket.status == TicketStatus.NEW
        assert ticket.priority == TicketPriority.MEDIUM
        assert ticket.user_id == test_user.id
        assert ticket.is_read_by_user is True
        assert ticket.is_read_by_admin is False

    async def test_get_ticket_by_owner(self, ticket_service, test_user):
        """Testa obtenção de ticket pelo proprietário."""
        # Criar ticket
        ticket_data = TicketCreate(
            title="Teste get ticket",
            description="Descrição teste",
            category=TicketCategory.QUESTION
        )
        
        created_ticket = await ticket_service.create_ticket(ticket_data, test_user)
        
        # Buscar ticket
        retrieved_ticket = await ticket_service.get_ticket(created_ticket.id, test_user)
        
        assert retrieved_ticket is not None
        assert retrieved_ticket.id == created_ticket.id
        assert retrieved_ticket.title == "Teste get ticket"

    async def test_get_ticket_by_admin(self, ticket_service, test_user, admin_user):
        """Testa obtenção de ticket por admin."""
        # Criar ticket como usuário
        ticket_data = TicketCreate(
            title="Teste admin access",
            description="Descrição teste",
            category=TicketCategory.INCIDENT
        )
        
        created_ticket = await ticket_service.create_ticket(ticket_data, test_user)
        
        # Buscar ticket como admin
        retrieved_ticket = await ticket_service.get_ticket(created_ticket.id, admin_user)
        
        assert retrieved_ticket is not None
        assert retrieved_ticket.id == created_ticket.id
        assert retrieved_ticket.is_read_by_admin is True  # Deve marcar como lido

    async def test_update_ticket_by_admin(self, ticket_service, test_user, admin_user):
        """Testa atualização de ticket por admin."""
        # Criar ticket
        ticket_data = TicketCreate(
            title="Ticket para atualizar",
            description="Descrição original",
            category=TicketCategory.REQUEST
        )
        
        ticket = await ticket_service.create_ticket(ticket_data, test_user)
        
        # Atualizar como admin
        update_data = TicketUpdate(
            status=TicketStatus.OPEN,
            priority=TicketPriority.HIGH
        )
        
        updated_ticket = await ticket_service.update_ticket(ticket.id, update_data, admin_user)
        
        assert updated_ticket is not None
        assert updated_ticket.status == TicketStatus.OPEN
        assert updated_ticket.priority == TicketPriority.HIGH

    async def test_update_ticket_by_user(self, ticket_service, test_user):
        """Testa atualização de ticket por usuário."""
        # Criar ticket
        ticket_data = TicketCreate(
            title="Ticket original",
            description="Descrição original",
            category=TicketCategory.SUGGESTION
        )
        
        ticket = await ticket_service.create_ticket(ticket_data, test_user)
        
        # Atualizar como usuário (apenas título e descrição)
        update_data = TicketUpdate(
            title="Ticket atualizado",
            description="Nova descrição",
            status=TicketStatus.RESOLVED  # Usuário não pode alterar status
        )
        
        updated_ticket = await ticket_service.update_ticket(ticket.id, update_data, test_user)
        
        assert updated_ticket is not None
        assert updated_ticket.title == "Ticket atualizado"
        assert updated_ticket.description == "Nova descrição"
        assert updated_ticket.status == TicketStatus.NEW  # Status não deve mudar

    async def test_list_tickets_by_user(self, ticket_service, test_user):
        """Testa listagem de tickets por usuário."""
        # Criar alguns tickets
        for i in range(3):
            ticket_data = TicketCreate(
                title=f"Ticket {i}",
                description=f"Descrição {i}",
                category=TicketCategory.QUESTION
            )
            await ticket_service.create_ticket(ticket_data, test_user)
        
        # Listar tickets
        result = await ticket_service.list_tickets(test_user)
        
        assert result.total == 3
        assert len(result.tickets) == 3
        assert all(ticket.user_id == test_user.id for ticket in result.tickets)

    async def test_list_tickets_by_admin(self, ticket_service, test_user, admin_user):
        """Testa listagem de tickets por admin."""
        # Criar ticket como usuário
        ticket_data = TicketCreate(
            title="Ticket do usuário",
            description="Descrição",
            category=TicketCategory.PROBLEM
        )
        await ticket_service.create_ticket(ticket_data, test_user)
        
        # Listar como admin (deve ver todos)
        result = await ticket_service.list_tickets(admin_user)
        
        assert result.total >= 1
        # Admin deve ver tickets de todos os usuários

    async def test_delete_ticket_by_admin(self, ticket_service, test_user, admin_user):
        """Testa deleção de ticket por admin."""
        # Criar ticket
        ticket_data = TicketCreate(
            title="Ticket para deletar",
            description="Será deletado",
            category=TicketCategory.INCIDENT
        )
        
        ticket = await ticket_service.create_ticket(ticket_data, test_user)
        
        # Deletar como admin
        success = await ticket_service.delete_ticket(ticket.id, admin_user)
        
        assert success is True
        
        # Verificar que não existe mais
        deleted_ticket = await ticket_service.get_ticket(ticket.id, admin_user)
        assert deleted_ticket is None

    async def test_delete_ticket_by_user_fails(self, ticket_service, test_user):
        """Testa que usuário não pode deletar ticket."""
        # Criar ticket
        ticket_data = TicketCreate(
            title="Ticket protegido",
            description="Usuário não pode deletar",
            category=TicketCategory.REQUEST
        )
        
        ticket = await ticket_service.create_ticket(ticket_data, test_user)
        
        # Tentar deletar como usuário
        success = await ticket_service.delete_ticket(ticket.id, test_user)
        
        assert success is False
