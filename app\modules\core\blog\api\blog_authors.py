"""
Blog Authors API

REST API endpoints for blog author management.
"""

import uuid
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.auth_dependencies import get_current_user
from app.modules.core.users.models.user import User
from ..dependencies.access_control import require_admin_permission
from ..schemas.blog_author import (
    BlogAuthorCreate,
    BlogAuthorUpdate,
    BlogAuthorRead,
    BlogAuthorList,
    BlogAuthorProfile,
)
from ..services.blog_author_service import BlogAuthorService

router = APIRouter()
blog_author_service = BlogAuthorService()


@router.get("/", response_model=List[BlogAuthorList])
async def get_authors(
    skip: int = Query(0, ge=0, description="Number of authors to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of authors to return"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    is_featured: Optional[bool] = Query(None, description="Filter featured authors"),
    db: AsyncSession = Depends(get_db),
):
    """
    Get blog authors with filtering and pagination.

    Results include statistics like post count and total views.
    """
    authors = await blog_author_service.get_authors(
        db=db,
        skip=skip,
        limit=limit,
        is_active=is_active,
        is_featured=is_featured
    )
    return authors


@router.get("/{author_id}", response_model=BlogAuthorRead)
async def get_author(
    author_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
):
    """
    Get a specific blog author by ID.
    """
    author = await blog_author_service.get_author_by_id(db=db, author_id=author_id)

    if not author:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Blog author not found"
        )

    return author


@router.get("/slug/{slug}", response_model=BlogAuthorProfile)
async def get_author_by_slug(
    slug: str,
    db: AsyncSession = Depends(get_db),
):
    """
    Get a blog author's public profile by slug.

    Returns public information suitable for author profile pages.
    """
    author = await blog_author_service.get_author_by_slug(db=db, slug=slug)

    if not author:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Blog author not found"
        )

    return author


@router.post("/", response_model=BlogAuthorRead, status_code=status.HTTP_201_CREATED)
async def create_author(
    author_data: BlogAuthorCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(require_admin_permission),
):
    """
    Create a new blog author.

    Requires authentication and admin privileges.
    """
    try:
        author = await blog_author_service.create_author(db=db, author_data=author_data)
        return author
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create author: {str(e)}"
        )


@router.put("/{author_id}/link-user/{user_id}", response_model=BlogAuthorRead)
async def link_user_to_author(
    author_id: uuid.UUID,
    user_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(require_admin_permission),
):
    """
    Link a system user to an existing blog author.

    Requires admin privileges. This allows the user to be associated with the author profile.
    """
    author = await blog_author_service.link_user_to_author(
        db=db,
        author_id=author_id,
        user_id=user_id
    )

    if not author:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Author or user not found"
        )

    return author


@router.delete("/{author_id}/unlink-user", response_model=BlogAuthorRead)
async def unlink_user_from_author(
    author_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(require_admin_permission),
):
    """
    Unlink a system user from a blog author.

    Requires admin privileges. This removes the association between the user and author.
    """
    author = await blog_author_service.unlink_user_from_author(
        db=db,
        author_id=author_id
    )

    if not author:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Author not found"
        )

    return author
