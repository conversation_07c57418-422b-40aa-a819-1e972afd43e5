import uuid
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, ConfigDict

from app.modules.tenants.restaurants.table_management.models.table import TableStatus


class TableBase(BaseModel):
    """Base schema for table."""

    table_number: str = Field(..., description="Table number or identifier")
    name: Optional[str] = Field(None, description="Optional display name for the table")
    capacity: int = Field(..., ge=1, description="Number of people the table can accommodate")
    zone: Optional[str] = Field(
        None, max_length=100, description="Zone where the table is located"
    )
    status: TableStatus = Field(
        default=TableStatus.AVAILABLE, description="Current status of the table"
    )

    # Position in layout
    position_x: Optional[float] = Field(None, description="X coordinate in the layout")
    position_y: Optional[float] = Field(None, description="Y coordinate in the layout")
    width: Optional[float] = Field(None, description="Width of the table in the layout")
    height: Optional[float] = Field(None, description="Height of the table in the layout")

    # Additional information
    shape: Optional[str] = Field(None, description="Shape of the table (rectangle, circle, custom)")
    custom_shape_data: Optional[Dict[str, Any]] = Field(None, description="Data for custom shapes")
    is_active: bool = Field(default=True, description="Whether the table is active")
    notes: Optional[str] = Field(None, description="Additional notes about the table")

    # QR code information
    qrcode_enabled: bool = Field(
        default=True, description="Whether the QR code is enabled for this table"
    )


class TableCreate(TableBase):
    """Schema for creating a new table."""

    layout_id: Optional[uuid.UUID] = Field(
        None, description="ID of the layout this table belongs to"
    )


class TableUpdate(BaseModel):
    """Schema for updating an existing table."""

    table_number: Optional[str] = Field(None, description="Table number or identifier")
    name: Optional[str] = Field(None, description="Optional display name for the table")
    capacity: Optional[int] = Field(
        None, ge=1, description="Number of people the table can accommodate"
    )
    zone: Optional[str] = Field(
        None, max_length=100, description="Zone where the table is located"
    )
    status: Optional[TableStatus] = Field(None, description="Current status of the table")
    layout_id: Optional[uuid.UUID] = Field(
        None, description="ID of the layout this table belongs to"
    )

    # Position in layout
    position_x: Optional[float] = Field(None, description="X coordinate in the layout")
    position_y: Optional[float] = Field(None, description="Y coordinate in the layout")
    width: Optional[float] = Field(None, description="Width of the table in the layout")
    height: Optional[float] = Field(None, description="Height of the table in the layout")

    # Additional information
    shape: Optional[str] = Field(None, description="Shape of the table (rectangle, circle, custom)")
    custom_shape_data: Optional[Dict[str, Any]] = Field(None, description="Data for custom shapes")
    is_active: Optional[bool] = Field(None, description="Whether the table is active")
    notes: Optional[str] = Field(None, description="Additional notes about the table")

    # QR code information
    qrcode_enabled: Optional[bool] = Field(
        None, description="Whether the QR code is enabled for this table"
    )


class TableRead(TableBase):
    """Schema for reading a table."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    layout_id: Optional[uuid.UUID] = None

    # QR code information
    qrcode_id: Optional[str] = Field(None, description="Unique identifier for the QR code")

    # Include layout details in the response if needed
    layout: Optional["TableLayoutBase"] = None

    model_config = ConfigDict(from_attributes=True)


class TableLayoutBase(BaseModel):
    """Base schema for table layout."""

    name: str = Field(..., description="Name of the layout")
    description: Optional[str] = Field(None, description="Description of the layout")
    floor_number: Optional[int] = Field(None, description="Floor number")
    width: Optional[float] = Field(None, description="Width of the layout")
    height: Optional[float] = Field(None, description="Height of the layout")
    background_image_url: Optional[str] = Field(None, description="URL to background image")
    is_active: bool = Field(default=True, description="Whether the layout is active")
    layout_data: Optional[Dict[str, Any]] = Field(None, description="Additional layout information")


class TableLayoutCreate(TableLayoutBase):
    """Schema for creating a new table layout."""

    pass


class TableLayoutUpdate(BaseModel):
    """Schema for updating an existing table layout."""

    name: Optional[str] = Field(None, description="Name of the layout")
    description: Optional[str] = Field(None, description="Description of the layout")
    floor_number: Optional[int] = Field(None, description="Floor number")
    width: Optional[float] = Field(None, description="Width of the layout")
    height: Optional[float] = Field(None, description="Height of the layout")
    background_image_url: Optional[str] = Field(None, description="URL to background image")
    is_active: Optional[bool] = Field(None, description="Whether the layout is active")
    layout_data: Optional[Dict[str, Any]] = Field(None, description="Additional layout information")


class TableLayoutRead(TableLayoutBase):
    """Schema for reading a table layout."""

    id: uuid.UUID
    tenant_id: uuid.UUID

    # Include tables in the response if needed
    tables: Optional[List[TableRead]] = None

    model_config = ConfigDict(from_attributes=True)
