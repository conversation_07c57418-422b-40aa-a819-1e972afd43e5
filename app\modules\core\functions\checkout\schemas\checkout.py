"""
Checkout Schemas for EShop System
=================================

Schemas de validação para processo de checkout com suporte a
múltiplos métodos de pagamento e entrega.
"""

import uuid
from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any, List

from pydantic import BaseModel, Field, ConfigDict, validator

from app.modules.core.functions.checkout.models.checkout import (
    CheckoutStatus, PaymentMethod, ShippingMethod
)


class AddressSchema(BaseModel):
    """Schema para endereços de entrega e cobrança."""
    
    street: str = Field(..., max_length=255, description="Rua")
    number: str = Field(..., max_length=20, description="Número")
    complement: Optional[str] = Field(None, max_length=100, description="Complemento")
    neighborhood: str = Field(..., max_length=100, description="Bairro")
    city: str = Field(..., max_length=100, description="Cidade")
    state: str = Field(..., max_length=50, description="Estado")
    postal_code: str = Field(..., max_length=20, description="CEP")
    country: str = Field(default='BR', max_length=2, description="País")
    
    @validator('postal_code')
    def validate_postal_code(cls, v):
        """Valida formato do CEP brasileiro."""
        import re
        if not re.match(r'^\d{5}-?\d{3}$', v):
            raise ValueError('CEP deve estar no formato 00000-000 ou 00000000')
        return v.replace('-', '')


class CheckoutSessionBase(BaseModel):
    """Schema base para sessão de checkout."""
    
    market_context: str = Field(
        default='b2c', 
        description="Contexto do mercado (b2b/b2c)"
    )
    shipping_method: Optional[ShippingMethod] = Field(
        None, description="Método de entrega"
    )
    payment_method: Optional[PaymentMethod] = Field(
        None, description="Método de pagamento"
    )
    customer_notes: Optional[str] = Field(
        None, max_length=1000, description="Observações do cliente"
    )
    special_instructions: Optional[str] = Field(
        None, max_length=1000, description="Instruções especiais"
    )


class CheckoutInitiateRequest(BaseModel):
    """Schema para iniciar processo de checkout."""
    
    cart_id: uuid.UUID = Field(..., description="ID do carrinho")
    market_context: str = Field(
        default='b2c', 
        description="Contexto do mercado"
    )
    shipping_address: Optional[AddressSchema] = Field(
        None, description="Endereço de entrega"
    )
    billing_address: Optional[AddressSchema] = Field(
        None, description="Endereço de cobrança"
    )
    shipping_method: Optional[ShippingMethod] = Field(
        None, description="Método de entrega preferido"
    )


class CheckoutSessionCreate(CheckoutSessionBase):
    """Schema para criação de sessão de checkout."""
    
    cart_id: uuid.UUID = Field(..., description="ID do carrinho")
    shipping_address: Optional[Dict[str, Any]] = Field(
        None, description="Endereço de entrega"
    )
    billing_address: Optional[Dict[str, Any]] = Field(
        None, description="Endereço de cobrança"
    )


class CheckoutSessionUpdate(BaseModel):
    """Schema para atualização de sessão de checkout."""
    
    shipping_address: Optional[Dict[str, Any]] = Field(
        None, description="Endereço de entrega atualizado"
    )
    billing_address: Optional[Dict[str, Any]] = Field(
        None, description="Endereço de cobrança atualizado"
    )
    shipping_method: Optional[ShippingMethod] = Field(
        None, description="Método de entrega atualizado"
    )
    payment_method: Optional[PaymentMethod] = Field(
        None, description="Método de pagamento atualizado"
    )
    customer_notes: Optional[str] = Field(
        None, max_length=1000, description="Observações atualizadas"
    )
    special_instructions: Optional[str] = Field(
        None, max_length=1000, description="Instruções especiais atualizadas"
    )


class CheckoutSessionRead(CheckoutSessionBase):
    """Schema para leitura de sessão de checkout."""
    
    id: uuid.UUID
    tenant_id: uuid.UUID
    cart_id: uuid.UUID
    user_id: Optional[uuid.UUID]
    order_id: Optional[uuid.UUID]
    status: CheckoutStatus
    
    # Endereços
    shipping_address: Optional[Dict[str, Any]]
    billing_address: Optional[Dict[str, Any]]
    
    # Custos
    subtotal: Decimal
    tax_amount: Decimal
    discount_amount: Decimal
    shipping_cost: Decimal
    total_amount: Decimal
    currency: str
    
    # Informações de pagamento
    payment_provider: Optional[str]
    payment_external_id: Optional[str]
    payment_reference: Optional[str]
    
    # Timestamps
    created_at: datetime
    updated_at: datetime
    expires_at: Optional[datetime]
    payment_confirmed_at: Optional[datetime]
    completed_at: Optional[datetime]
    
    # Propriedades calculadas
    is_expired: bool = False
    is_payment_pending: bool = False
    is_completed: bool = False
    
    model_config = ConfigDict(from_attributes=True)


class CheckoutCompleteRequest(BaseModel):
    """Schema para completar checkout."""
    
    payment_method: PaymentMethod = Field(..., description="Método de pagamento")
    payment_provider: Optional[str] = Field(
        None, description="Provedor de pagamento"
    )
    payment_token: Optional[str] = Field(
        None, description="Token de pagamento"
    )
    save_payment_method: bool = Field(
        default=False, description="Salvar método de pagamento"
    )


class CheckoutSummary(BaseModel):
    """Schema para resumo do checkout."""
    
    id: uuid.UUID
    cart_id: uuid.UUID
    status: CheckoutStatus
    total_amount: Decimal
    currency: str
    payment_method: Optional[PaymentMethod]
    shipping_method: Optional[ShippingMethod]
    created_at: datetime
    expires_at: Optional[datetime]
    
    model_config = ConfigDict(from_attributes=True)


class CheckoutResponse(BaseModel):
    """Schema de resposta padrão para operações de checkout."""
    
    success: bool = Field(..., description="Sucesso da operação")
    message: str = Field(..., description="Mensagem de retorno")
    checkout_session: Optional[CheckoutSessionRead] = Field(
        None, description="Dados da sessão de checkout"
    )
    payment_url: Optional[str] = Field(
        None, description="URL para pagamento (se aplicável)"
    )
    order_id: Optional[uuid.UUID] = Field(
        None, description="ID do pedido criado (se completado)"
    )
    
    model_config = ConfigDict(from_attributes=True)


class CheckoutStats(BaseModel):
    """Schema para estatísticas de checkout."""
    
    total_sessions: int = Field(..., description="Total de sessões")
    initiated_sessions: int = Field(..., description="Sessões iniciadas")
    payment_pending_sessions: int = Field(..., description="Pagamentos pendentes")
    completed_sessions: int = Field(..., description="Sessões completadas")
    cancelled_sessions: int = Field(..., description="Sessões canceladas")
    expired_sessions: int = Field(..., description="Sessões expiradas")
    
    total_value: Decimal = Field(..., description="Valor total")
    average_session_value: Decimal = Field(..., description="Valor médio por sessão")
    completion_rate: float = Field(..., description="Taxa de conclusão (%)")
    abandonment_rate: float = Field(..., description="Taxa de abandono (%)")
    
    # Estatísticas por método de pagamento
    payment_methods_stats: Dict[str, int] = Field(
        default_factory=dict, description="Estatísticas por método de pagamento"
    )
    
    # Estatísticas por método de entrega
    shipping_methods_stats: Dict[str, int] = Field(
        default_factory=dict, description="Estatísticas por método de entrega"
    )
    
    model_config = ConfigDict(from_attributes=True)


class ShippingQuoteRequest(BaseModel):
    """Schema para solicitar cotação de frete."""
    
    destination_postal_code: str = Field(..., description="CEP de destino")
    items: List[Dict[str, Any]] = Field(..., description="Itens para cálculo")
    shipping_methods: Optional[List[ShippingMethod]] = Field(
        None, description="Métodos específicos para cotação"
    )


class ShippingQuoteResponse(BaseModel):
    """Schema para resposta de cotação de frete."""
    
    quotes: List[Dict[str, Any]] = Field(..., description="Cotações disponíveis")
    estimated_delivery_days: Dict[str, int] = Field(
        default_factory=dict, description="Dias estimados por método"
    )
    
    model_config = ConfigDict(from_attributes=True)
