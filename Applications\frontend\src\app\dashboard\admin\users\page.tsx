'use client';

import { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import {
  Plus,
  Search,
  Filter,
  Download,
  Users,
  UserCheck,
  UserX,
  Crown,
  Shield,
  TrendingUp,
  TrendingDown,
  Sparkles
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';

import { UsersList } from './components/UsersList';
import { UserModal } from './components/UserModal';
import { UserFilters } from './components/UserFilters';
import { UserStats } from './components/UserStats';
import { userApi } from '@/lib/api/userApi';
import { User, UserFilters as UserFiltersType, UserStats as UserStatsType } from '@/types/user';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 10,
    },
  },
};

export default function UsersAdministrationPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<UserStatsType | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isFiltersOpen, setIsFiltersOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [totalUsers, setTotalUsers] = useState(0);

  const [filters, setFilters] = useState<UserFiltersType>({
    search: '',
    role: undefined,
    is_active: undefined,
    is_verified: undefined,
    is_blacklisted: undefined,
    is_premium: undefined,
    page: 1,
    limit: 10,
  });

  // Load users data
  const loadUsers = useCallback(async () => {
    try {
      setLoading(true);
      const response = await userApi.getUsers({
        ...filters,
        search: searchTerm,
        page: currentPage,
        limit: itemsPerPage,
      });

      setUsers(response.users);
      setTotalPages(response.total_pages);
      setTotalUsers(response.total);
    } catch (error) {
      console.error('Error loading users:', error);
      toast.error('Erro ao carregar usuários');
    } finally {
      setLoading(false);
    }
  }, [filters, searchTerm, currentPage, itemsPerPage]);

  // Load stats
  const loadStats = async () => {
    try {
      const statsData = await userApi.getUserStats();
      setStats(statsData);
    } catch (error) {
      console.error('Error loading stats:', error);
    }
  };

  useEffect(() => {
    loadUsers();
  }, [currentPage, itemsPerPage, filters, loadUsers]);

  useEffect(() => {
    loadStats();
  }, []);

  // Handle search
  const handleSearch = () => {
    setFilters(prev => ({ ...prev, search: searchTerm }));
    setCurrentPage(1);
  };

  // Handle filter changes
  const handleFiltersChange = (newFilters: Partial<UserFiltersType>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(1);
  };

  // Handle user creation
  const handleUserCreated = (newUser: User) => {
    setUsers(prev => [newUser, ...prev]);
    setIsCreateModalOpen(false);
    loadStats();
    toast.success('Usuário criado com sucesso');
  };

  // Handle user update
  const handleUserUpdated = (updatedUser: User) => {
    setUsers(prev => prev.map(user => user.id === updatedUser.id ? updatedUser : user));
    setIsEditModalOpen(false);
    setSelectedUser(null);
    loadStats();
    toast.success('Usuário atualizado com sucesso');
  };

  // Handle user deletion
  const handleUserDeleted = (userId: string) => {
    setUsers(prev => prev.filter(user => user.id !== userId));
    loadStats();
    toast.success('Usuário removido com sucesso');
  };

  // Handle edit user
  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setIsEditModalOpen(true);
  };

  // Handle export
  const handleExport = async () => {
    try {
      const blob = await userApi.exportUsers(filters);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `users-export-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success('Exportação realizada com sucesso');
    } catch (error) {
      console.error('Error exporting users:', error);
      toast.error('Erro ao exportar usuários');
    }
  };

  return (
    <motion.div
      className="w-full space-y-8"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Modern Header with Glassmorphism */}
      <motion.div
        variants={itemVariants}
        className="relative overflow-hidden rounded-3xl bg-white/10 backdrop-blur-xl border border-white/20 p-8 shadow-xl"
      >
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-2xl"></div>

        <div className="relative z-10 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6">
          <div className="flex items-center gap-4">
            <motion.div
              className="p-3 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600 shadow-lg"
              whileHover={{
                rotate: [0, -10, 10, 0],
                scale: 1.1,
                transition: { duration: 0.3 }
              }}
            >
              <Users className="h-6 w-6 text-white" />
            </motion.div>
            <div>
              <h1 className="text-3xl font-black text-gray-900">
                Administração de Usuários
              </h1>
              <p className="text-gray-600 font-medium">
                Gerencie usuários, roles e associações do sistema
              </p>
            </div>
          </div>

          <div className="flex gap-3">
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                variant="outline"
                onClick={handleExport}
                className="flex items-center gap-2 bg-white/20 backdrop-blur-sm border-white/30 hover:bg-white/30 transition-all duration-300"
              >
                <Download className="h-4 w-4" />
                Exportar
              </Button>
            </motion.div>
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                onClick={() => setIsCreateModalOpen(true)}
                className="flex items-center gap-2 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 border-0 shadow-lg"
              >
                <Plus className="h-4 w-4" />
                Criar Usuário
              </Button>
            </motion.div>
          </div>
        </div>
      </motion.div>

        {/* Modern Stats Cards */}
        {stats && (
          <motion.div variants={itemVariants}>
            <UserStats stats={stats} />
          </motion.div>
        )}

        {/* Modern Search and Filters */}
        <motion.div
          variants={itemVariants}
          className="relative overflow-hidden rounded-3xl bg-white/10 backdrop-blur-xl border border-white/20 p-6 shadow-xl"
        >
          <div className="absolute top-0 left-0 w-32 h-32 bg-gradient-to-br from-green-400/20 to-blue-400/20 rounded-full blur-2xl"></div>

          <div className="relative z-10">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search */}
              <div className="flex-1 flex gap-3">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Buscar por nome ou email..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    className="pl-10 bg-white/20 backdrop-blur-sm border-white/30 focus:bg-white/30 transition-all duration-300"
                  />
                </div>
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    onClick={handleSearch}
                    className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 border-0"
                  >
                    Buscar
                  </Button>
                </motion.div>
              </div>

              {/* Items per page */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-700 font-medium">Mostrar:</span>
                <Select
                  value={itemsPerPage.toString()}
                  onValueChange={(value) => {
                    setItemsPerPage(parseInt(value));
                    setCurrentPage(1);
                  }}
                >
                  <SelectTrigger className="w-20 bg-white/20 backdrop-blur-sm border-white/30">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Filters Toggle */}
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  variant="outline"
                  onClick={() => setIsFiltersOpen(!isFiltersOpen)}
                  className="flex items-center gap-2 bg-white/20 backdrop-blur-sm border-white/30 hover:bg-white/30"
                >
                  <Filter className="h-4 w-4" />
                  Filtros
                </Button>
              </motion.div>
            </div>

            {/* Filters Panel */}
            {isFiltersOpen && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mt-6 pt-6 border-t border-white/20"
              >
                <UserFilters
                  filters={filters}
                  onFiltersChange={handleFiltersChange}
                  onClearFilters={() => {
                    setFilters({
                      search: '',
                      role: undefined,
                      is_active: undefined,
                      is_verified: undefined,
                      is_blacklisted: undefined,
                      is_premium: undefined,
                      page: 1,
                      limit: itemsPerPage,
                    });
                    setSearchTerm('');
                  }}
                />
              </motion.div>
            )}
          </div>
        </motion.div>

        {/* Users List with Animation */}
        <motion.div variants={itemVariants}>
          <UsersList
            users={users}
            loading={loading}
            currentPage={currentPage}
            totalPages={totalPages}
            totalUsers={totalUsers}
            onPageChange={setCurrentPage}
            onEditUser={handleEditUser}
            onDeleteUser={handleUserDeleted}
          />
        </motion.div>

        {/* Modals */}
        <UserModal
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
          onUserCreated={handleUserCreated}
          mode="create"
        />

        <UserModal
          isOpen={isEditModalOpen}
          onClose={() => {
            setIsEditModalOpen(false);
            setSelectedUser(null);
          }}
          onUserUpdated={handleUserUpdated}
          user={selectedUser}
          mode="edit"
        />
    </motion.div>
  );
}
