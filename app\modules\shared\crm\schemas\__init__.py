"""Schemas for CRM module."""

from .account import (  # noqa: E402
    AccountBase,
    AccountCreate,
    AccountUpdate,
    AccountRead,
    AccountReadWithDetails,
)
from .contact import ContactBase, ContactCreate, ContactUpdate, ContactRead  # noqa: E402
from .interaction import (
    InteractionBase,
    InteractionCreate,
    InteractionUpdate,
    InteractionRead,
)
from .loyalty import (  # noqa: E402
    LoyaltyProgramBase,
    LoyaltyProgramCreate,
    LoyaltyProgramUpdate,
    LoyaltyProgramRead,
    LoyaltyMembershipBase,
    LoyaltyMembershipCreate,
    LoyaltyMembershipUpdate,
    LoyaltyMembershipRead,
    LoyaltyTransactionBase,
    LoyaltyTransactionCreate,
    LoyaltyTransactionRead,
)
from .pricing import (  # noqa: E402
    PricingTierBase,
    PricingTierCreate,
    PricingTierUpdate,
    PricingTierRead,
    CustomerPricingAssignmentBase,
    CustomerPricingAssignmentCreate,
    CustomerPricingAssignmentUpdate,
    CustomerPricingAssignmentRead,
    P<PERSON>ingRuleBase,
    Pricing<PERSON>ule<PERSON>reate,
    PricingRuleUpdate,
    PricingRuleRead,
)

__all__ = [
    # Account
    "AccountBase",
    "AccountCreate",
    "AccountUpdate",
    "AccountRead",
    "AccountReadWithDetails",
    # Contact
    "ContactBase",
    "ContactCreate",
    "ContactUpdate",
    "ContactRead",
    # Interaction
    "InteractionBase",
    "InteractionCreate",
    "InteractionUpdate",
    "InteractionRead",
    # Loyalty
    "LoyaltyProgramBase",
    "LoyaltyProgramCreate",
    "LoyaltyProgramUpdate",
    "LoyaltyProgramRead",
    "LoyaltyMembershipBase",
    "LoyaltyMembershipCreate",
    "LoyaltyMembershipUpdate",
    "LoyaltyMembershipRead",
    "LoyaltyTransactionBase",
    "LoyaltyTransactionCreate",
    "LoyaltyTransactionRead",
    # Pricing
    "PricingTierBase",
    "PricingTierCreate",
    "PricingTierUpdate",
    "PricingTierRead",
    "CustomerPricingAssignmentBase",
    "CustomerPricingAssignmentCreate",
    "CustomerPricingAssignmentUpdate",
    "CustomerPricingAssignmentRead",
    "PricingRuleBase",
    "PricingRuleCreate",
    "PricingRuleUpdate",
    "PricingRuleRead",
]
