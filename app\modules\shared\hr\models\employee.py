import uuid
import enum
from sqlalchemy import Column, String, ForeignKey, Date, Enum
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship

from app.db.base import Base
from app.core.tenant_mixin import TenantMixin


class EmploymentStatus(str, enum.Enum):
    """Employment status enum."""

    ACTIVE = "active"
    INACTIVE = "inactive"
    ON_LEAVE = "on_leave"
    TERMINATED = "terminated"


class EmploymentType(str, enum.Enum):
    """Employment type enum."""

    FULL_TIME = "full_time"
    PART_TIME = "part_time"
    CONTRACT = "contract"
    TEMPORARY = "temporary"
    INTERN = "intern"


class Employee(Base, TenantMixin):
    """Employee model for HR module.

    This represents a staff member in a tenant. It is linked to the tenant_user_association
    table to connect with the User model.
    """

    __tablename__ = "hr_employees"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_tenant_association_id = Column(
        UUID(as_uuid=True), ForeignKey("tenant_user_associations.id"), nullable=False
    )

    # Basic information
    employee_code = Column(String, index=True)
    job_title = Column(String, nullable=False)
    department = Column(String)

    # Employment details
    employment_status = Column(
        Enum(EmploymentStatus), nullable=False, default=EmploymentStatus.ACTIVE
    )
    employment_type = Column(Enum(EmploymentType), nullable=False, default=EmploymentType.FULL_TIME)

    # Dates
    hire_date = Column(Date, nullable=False)
    termination_date = Column(Date, nullable=True)

    # Contact info
    work_email = Column(String)
    work_phone = Column(String)

    # Emergency contact
    emergency_contact_name = Column(String)
    emergency_contact_phone = Column(String)
    emergency_contact_relationship = Column(String)

    # Additional metadata
    metadata = Column(JSONB, default={})

    # Relationships
    # These would be defined once other models are created
    time_records = relationship(
        "TimeRecord", back_populates="employee", cascade="all, delete-orphan"
    )
    schedules = relationship(
        "WorkSchedule", back_populates="employee", cascade="all, delete-orphan"
    )
    leave_requests = relationship(
        "LeaveRequest", back_populates="employee", cascade="all, delete-orphan"
    )

    # Association to User through TenantUserAssociation
    user_association = relationship("TenantUserAssociation", backref="hr_employee")
