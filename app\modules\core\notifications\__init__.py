"""
Notifications Module

Sistema completo de notificações em tempo real para a plataforma Trix.
Suporta notificações bidirecionais, métricas avançadas e escalabilidade.
Integração com B2B, pedidos, leilões, faturas e eventos do sistema.
"""

from .api.router import router

from .models import (
    Notification,
    NotificationQueue,
    NotificationMetrics,
    NotificationTemplate,
    NotificationPriority,
    NotificationStatus,
    NotificationSenderType,
    NotificationTargetType
)

from .services import (
    NotificationService,
    NotificationQueueService,
    NotificationMetricsService,
    NotificationDeliveryService
)

from .services.system_notification_service import SystemNotificationService
from .services.email_notification_service import EmailNotificationService
from .services.notification_integration_service import NotificationIntegrationService

from .websockets.notification_websockets import (
    NotificationWebSocketManager,
    notification_ws_manager
)

__all__ = [
    "router",

    # Models
    "Notification",
    "NotificationQueue",
    "NotificationMetrics",
    "NotificationTemplate",
    "NotificationPriority",
    "NotificationStatus",
    "NotificationSenderType",
    "NotificationTargetType",

    # Services
    "NotificationService",
    "NotificationQueueService",
    "NotificationMetricsService",
    "NotificationDeliveryService",
    "SystemNotificationService",
    "EmailNotificationService",
    "NotificationIntegrationService",

    # WebSockets
    "NotificationWebSocketManager",
    "notification_ws_manager"
]
