import { useState, useEffect } from 'react';
import { tenantSettingsService } from '@/services/api/tenantSettingsService';

export interface CurrencyConfig {
  default_currency?: string;
  currency?: string; // Alternative field name
  currency_config?: {
    default_currency: string;
    enabled_currencies: string[];
    exchange_rates: Record<string, number>;
    auto_update_rates: boolean;
    rate_update_frequency: string;
    display_currency: string;
    currency_formatting: Record<string, {
      decimal_separator: string;
      thousands_separator: string;
      symbol_position: 'left' | 'right';
      symbol_spacing: boolean;
    }>;
  };
  currency_format?: {
    decimal_separator: string;
    thousands_separator: string;
    symbol_position: 'left' | 'right';
    symbol_spacing: boolean;
  };
}

export interface CurrencyFormatting {
  symbol: string;
  decimal_separator: string;
  thousands_separator: string;
  symbol_position: 'left' | 'right';
  symbol_spacing: boolean;
  decimal_places: number;
}

const DEFAULT_CURRENCY_SYMBOLS: Record<string, string> = {
  BRL: 'R$',
  USD: '$',
  EUR: '€',
  GBP: '£',
  JPY: '¥',
  CAD: 'C$',
  AUD: 'A$',
  CHF: 'CHF',
  CNY: '¥',
  SEK: 'kr',
  NZD: 'NZ$',
  MXN: '$',
  SGD: 'S$',
  HKD: 'HK$',
  NOK: 'kr',
  TRY: '₺',
  RUB: '₽',
  INR: '₹',
  KRW: '₩',
  PLN: 'zł',
  THB: '฿',
  IDR: 'Rp',
  HUF: 'Ft',
  CZK: 'Kč',
  ILS: '₪',
  CLP: '$',
  PHP: '₱',
  AED: 'د.إ',
  COP: '$',
  SAR: '﷼',
  MYR: 'RM',
  RON: 'lei',
  BGN: 'лв',
  HRK: 'kn',
  DKK: 'kr',
  ISK: 'kr',
};

const DEFAULT_FORMATTING: CurrencyFormatting = {
  symbol: 'R$',
  decimal_separator: ',',
  thousands_separator: '.',
  symbol_position: 'left',
  symbol_spacing: true,
  decimal_places: 2,
};

export function useCurrency() {
  const [currencyConfig, setCurrencyConfig] = useState<CurrencyConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadCurrencyConfig();
  }, []);

  const loadCurrencyConfig = async () => {
    try {
      setLoading(true);
      setError(null);

      const settings = await tenantSettingsService.getSettings();

      // Convert tenant settings to currency config format
      const currencyConfig: CurrencyConfig = {
        default_currency: settings.currency,
        currency: settings.currency,
        currency_format: settings.currency_format,
      };

      setCurrencyConfig(currencyConfig);

      // Save to localStorage for public access
      try {
        // Try to get tenant slug from various sources
        let tenantSlug = 'default';

        // Method 1: From URL path (for dashboard)
        const pathSegments = window.location.pathname.split('/').filter(Boolean);
        if (pathSegments.length > 1 && pathSegments[0] === 'dashboard') {
          // Dashboard URL, try to get tenant from other sources
          const urlParams = new URLSearchParams(window.location.search);
          tenantSlug = urlParams.get('tenant') || 'default';
        } else if (pathSegments.length > 0 && !['auth', 'admin', 'api'].includes(pathSegments[0])) {
          // Public URL, first segment is tenant
          tenantSlug = pathSegments[0];
        }

        // Prepare currency data for public access
        const currency = settings.currency || 'BRL';
        let currency_format = settings.currency_format;

        // Use default currency format if none provided
        if (!currency_format) {
          currency_format = {
            decimal_separator: ',',
            thousands_separator: '.',
            symbol_position: 'left',
            symbol_spacing: true
          };
        }

        // Fallback to default format
        if (!currency_format) {
          currency_format = {
            decimal_separator: ",",
            thousands_separator: ".",
            symbol_position: "left",
            symbol_spacing: true
          };
        }

        const currencyData = {
          currency: currency,
          currency_format: currency_format
        };

        const cacheKey = `tenant_currency_${tenantSlug}`;
        localStorage.setItem(cacheKey, JSON.stringify(currencyData));

        console.log('💾 useCurrency: Saved currency settings to localStorage:', {
          tenantSlug,
          cacheKey,
          currencyData
        });

      } catch (e) {
        console.warn('⚠️ useCurrency: Could not save to localStorage:', e);
      }
    } catch (err) {
      console.error('Error loading currency config:', err);
      setError('Failed to load currency configuration');
      // Use default config as fallback
      setCurrencyConfig({
        default_currency: 'BRL',
        currency: 'BRL',
        currency_format: {
          decimal_separator: ',',
          thousands_separator: '.',
          symbol_position: 'left',
          symbol_spacing: true,
        },
      });
    } finally {
      setLoading(false);
    }
  };

  const getCurrentCurrency = (): string => {
    return currencyConfig?.currency_config?.display_currency ||
           currencyConfig?.default_currency ||
           currencyConfig?.currency ||
           'BRL';
  };

  const getCurrencyFormatting = (currencyCode?: string): CurrencyFormatting => {
    const currency = currencyCode || getCurrentCurrency();

    // Try to get formatting from currency_config first
    const configFormatting = currencyConfig?.currency_config?.currency_formatting?.[currency];

    // Fallback to currency_format (simpler structure)
    const simpleFormatting = currencyConfig?.currency_format;

    return {
      symbol: DEFAULT_CURRENCY_SYMBOLS[currency] || currency,
      decimal_separator: configFormatting?.decimal_separator ||
                        simpleFormatting?.decimal_separator ||
                        DEFAULT_FORMATTING.decimal_separator,
      thousands_separator: configFormatting?.thousands_separator ||
                          simpleFormatting?.thousands_separator ||
                          DEFAULT_FORMATTING.thousands_separator,
      symbol_position: configFormatting?.symbol_position ||
                      simpleFormatting?.symbol_position ||
                      DEFAULT_FORMATTING.symbol_position,
      symbol_spacing: configFormatting?.symbol_spacing ??
                     simpleFormatting?.symbol_spacing ??
                     DEFAULT_FORMATTING.symbol_spacing,
      decimal_places: DEFAULT_FORMATTING.decimal_places,
    };
  };

  const formatCurrency = (amount: number, currencyCode?: string): string => {
    const formatting = getCurrencyFormatting(currencyCode);
    
    // Format the number with proper decimal places
    const formattedNumber = amount.toFixed(formatting.decimal_places);
    
    // Split into integer and decimal parts
    const [integerPart, decimalPart] = formattedNumber.split('.');
    
    // Add thousands separators
    const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, formatting.thousands_separator);
    
    // Combine with decimal separator
    const formattedAmount = decimalPart ? 
      `${formattedInteger}${formatting.decimal_separator}${decimalPart}` : 
      formattedInteger;
    
    // Add currency symbol
    const spacing = formatting.symbol_spacing ? ' ' : '';
    
    if (formatting.symbol_position === 'left') {
      return `${formatting.symbol}${spacing}${formattedAmount}`;
    } else {
      return `${formattedAmount}${spacing}${formatting.symbol}`;
    }
  };

  const parseCurrency = (value: string): number => {
    const formatting = getCurrencyFormatting();
    
    // Remove currency symbol and spaces
    let cleanValue = value.replace(new RegExp(`[${formatting.symbol}\\s]`, 'g'), '');
    
    // Replace thousands separator with empty string
    cleanValue = cleanValue.replace(new RegExp(`\\${formatting.thousands_separator}`, 'g'), '');
    
    // Replace decimal separator with dot
    cleanValue = cleanValue.replace(formatting.decimal_separator, '.');
    
    // Parse as float
    const parsed = parseFloat(cleanValue);
    return isNaN(parsed) ? 0 : parsed;
  };

  return {
    currencyConfig,
    loading,
    error,
    getCurrentCurrency,
    getCurrencyFormatting,
    formatCurrency,
    parseCurrency,
    reload: loadCurrencyConfig,
  };
}
