"""Service for managing payment methods."""

import uuid  # noqa: E402
from typing import List, Optional
from fastapi import HTT<PERSON>Ex<PERSON>, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.modules.core.functions.pos.models.payment_method import (  # noqa: E402
    POSPaymentMethod,
    TransactionPayment,
)
from app.modules.core.functions.pos.models.sale_transaction import SaleTransaction  # noqa: E402
from app.modules.core.functions.pos.schemas.payment_method import (
    PaymentMethodCreate,
    PaymentMethodUpdate,
    TransactionPaymentCreate,
)
from app.core.exceptions import BusinessLogicError  # noqa: E402


class PaymentMethodService:
    """Service for managing payment methods."""

    async def get(
        self, db: AsyncSession, id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Optional[POSPaymentMethod]:
        """
        Get a payment method by ID, ensuring it belongs to the specified tenant.
        """
        result = await db.execute(
            select(POSPaymentMethod).where(
                POSPaymentMethod.id == id, POSPaymentMethod.tenant_id == tenant_id
            )
        )
        return result.scalars().first()

    async def get_multi(
        self,
        db: AsyncSession,
        *,
        tenant_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        is_active: Optional[bool] = None,
    ) -> List[POSPaymentMethod]:
        """
        Get multiple payment methods with optional filtering.
        """
        query = select(POSPaymentMethod).where(POSPaymentMethod.tenant_id == tenant_id)

        if is_active is not None:
            query = query.where(POSPaymentMethod.is_active == is_active)

        query = query.order_by(POSPaymentMethod.name).offset(skip).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    async def create(
        self, db: AsyncSession, *, tenant_id: uuid.UUID, obj_in: PaymentMethodCreate
    ) -> POSPaymentMethod:
        """
        Create a new payment method.
        """
        db_obj = POSPaymentMethod(tenant_id=tenant_id, **obj_in.model_dump())

        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)

        return db_obj

    async def update(
        self,
        db: AsyncSession,
        *,
        id: uuid.UUID,
        tenant_id: uuid.UUID,
        obj_in: PaymentMethodUpdate,
    ) -> Optional[POSPaymentMethod]:
        """
        Update an existing payment method.
        """
        payment_method = await self.get(db, id, tenant_id)

        if not payment_method:
            return None

        update_data = obj_in.model_dump(exclude_unset=True)

        for field, value in update_data.items():
            setattr(payment_method, field, value)

        db.add(payment_method)
        await db.commit()
        await db.refresh(payment_method)

        return payment_method

    async def get_transaction_payments(
        self, db: AsyncSession, *, transaction_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> List[TransactionPayment]:
        """
        Get all payments for a specific transaction.
        """
        result = await db.execute(
            select(TransactionPayment).where(
                TransactionPayment.transaction_id == transaction_id,
                TransactionPayment.tenant_id == tenant_id,
            )
        )
        return result.scalars().all()

    async def add_transaction_payment(
        self,
        db: AsyncSession,
        *,
        transaction_id: uuid.UUID,
        tenant_id: uuid.UUID,
        obj_in: TransactionPaymentCreate,
    ) -> TransactionPayment:
        """
        Add a payment to a transaction.

        Validates:
        - The transaction exists and belongs to the tenant
        - The payment method exists and is active
        """
        # Check if transaction exists
        result = await db.execute(
            select(SaleTransaction).where(
                SaleTransaction.id == transaction_id,
                SaleTransaction.tenant_id == tenant_id,
            )
        )
        transaction = result.scalars().first()

        if not transaction:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Transaction with id {transaction_id} not found or does not belong to the tenant.",  # noqa: E501
            )

        # Check if payment method exists and is active
        result = await db.execute(
            select(POSPaymentMethod).where(
                POSPaymentMethod.id == obj_in.payment_method_id,
                POSPaymentMethod.tenant_id == tenant_id,
            )
        )
        payment_method = result.scalars().first()

        if not payment_method:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Payment method with id {obj_in.payment_method_id} not found or does not belong to the tenant.",  # noqa: E501
            )

        if not payment_method.is_active:
            raise BusinessLogicError(
                f"Payment method with id {obj_in.payment_method_id} is not active."
            )

        # Create payment
        db_obj = TransactionPayment(
            transaction_id=transaction_id, tenant_id=tenant_id, **obj_in.model_dump()
        )

        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)

        return db_obj


# Instance of the service to be used in endpoints
payment_method_service = PaymentMethodService()
