"""
Analytics Schemas

Schemas Pydantic para validação de dados de analytics e métricas.
"""

from datetime import datetime, date
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class HelpCenterMetricsResponse(BaseModel):
    # Métricas básicas
    total_tickets: int
    open_tickets: int
    resolved_tickets: int
    closed_tickets: int
    pending_tickets: int
    
    # Métricas por prioridade
    urgent_tickets: int
    high_priority_tickets: int
    medium_priority_tickets: int
    low_priority_tickets: int
    
    # Métricas de tempo
    average_response_time_hours: float
    average_resolution_time_hours: float
    
    # Base de conhecimento
    total_kb_articles: int
    total_kb_views: int
    average_kb_helpfulness: float
    
    # Métricas diárias
    tickets_created_today: int
    tickets_resolved_today: int
    
    # Métricas semanais
    tickets_created_this_week: int
    tickets_resolved_this_week: int
    
    # Taxas
    resolution_rate: float
    first_response_rate: float


class AdminDashboardMetrics(BaseModel):
    # Métricas gerais (overview)
    overview: HelpCenterMetricsResponse

    # Distribuição por status
    tickets_by_status: Dict[str, int]

    # Distribuição por categoria
    tickets_by_category: Dict[str, int]

    # Performance dos admins
    admin_performance: List[Dict[str, Any]]

    # Tendências diárias
    daily_ticket_trend: List[Dict[str, Any]]

    # Tendências semanais de resolução
    weekly_resolution_trend: List[Dict[str, Any]]


class TicketMetrics(BaseModel):
    ticket_id: str
    created_at: datetime
    first_response_at: Optional[datetime] = None
    resolved_at: Optional[datetime] = None
    closed_at: Optional[datetime] = None
    response_time_hours: Optional[float] = None
    resolution_time_hours: Optional[float] = None
    message_count: int
    status_changes: int


class AgentMetrics(BaseModel):
    agent_id: str
    agent_name: str
    tickets_assigned: int
    tickets_resolved: int
    avg_response_time_hours: float
    avg_resolution_time_hours: float
    customer_satisfaction: Optional[float] = None


class CategoryMetrics(BaseModel):
    category: str
    ticket_count: int
    avg_resolution_time_hours: float
    resolution_rate: float


class TimeRangeMetrics(BaseModel):
    start_date: date
    end_date: date
    tickets_created: int
    tickets_resolved: int
    avg_response_time_hours: float
    avg_resolution_time_hours: float


class KnowledgeBaseMetrics(BaseModel):
    total_articles: int
    total_views: int
    total_votes: int
    helpful_votes: int
    not_helpful_votes: int
    helpfulness_ratio: float
    most_viewed_articles: List[Dict[str, Any]]
    most_helpful_articles: List[Dict[str, Any]]


class CustomerSatisfactionMetrics(BaseModel):
    total_responses: int
    average_rating: float
    rating_distribution: Dict[str, int]
    satisfaction_trend: List[Dict[str, Any]]


class PerformanceMetrics(BaseModel):
    sla_compliance: float
    first_response_sla: float
    resolution_sla: float
    escalation_rate: float
    reopened_tickets_rate: float


class ReportRequest(BaseModel):
    start_date: date
    end_date: date
    metrics: List[str] = Field(..., description="Lista de métricas a incluir no relatório")
    format: str = Field("json", description="Formato do relatório (json, csv, pdf)")
    filters: Optional[Dict[str, Any]] = None


class ReportResponse(BaseModel):
    report_id: str
    generated_at: datetime
    start_date: date
    end_date: date
    metrics: Dict[str, Any]
    download_url: Optional[str] = None
