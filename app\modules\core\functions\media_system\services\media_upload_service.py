"""
Media Upload Service

Serviço para gerenciamento de uploads de mídia.
"""

import logging
import os
import shutil
from pathlib import Path
from typing import Optional, List, BinaryIO
from uuid import UUID

from fastapi import UploadFile
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

from ..models import MediaUpload, MediaContext, MediaUploadStatus, MediaFileType
from ..schemas import (
    MediaUploadCreate,
    MediaUploadUpdate,
    MediaUploadRead,
    MediaUploadResponse
)
from .media_context_service import MediaContextService

logger = logging.getLogger(__name__)


class MediaUploadService:
    """Serviço para gerenciamento de uploads de mídia."""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.context_service = MediaContextService(db)
        self.base_upload_path = Path("/media_data")

    def _get_media_type_from_mime(self, mime_type: str) -> MediaFileType:
        """
        Determina o tipo de mídia baseado no MIME type.
        
        Args:
            mime_type: MIME type do arquivo
            
        Returns:
            MediaType: Tipo de mídia
        """
        mime_lower = mime_type.lower()
        
        if mime_lower.startswith('image/'):
            return MediaFileType.IMAGE
        elif mime_lower.startswith('video/'):
            return MediaFileType.VIDEO
        elif mime_lower.startswith('audio/'):
            return MediaFileType.AUDIO
        elif mime_lower in ['application/pdf', 'text/plain', 'application/msword',
                           'application/vnd.openxmlformats-officedocument.wordprocessingml.document']:
            return MediaFileType.DOCUMENT
        elif mime_lower in ['application/zip', 'application/x-rar-compressed',
                           'application/x-tar', 'application/gzip']:
            return MediaFileType.ARCHIVE
        elif mime_lower.startswith('text/'):
            return MediaFileType.TEXT
        else:
            return MediaFileType.OTHER

    def _generate_unique_filename(self, original_filename: str, upload_id: UUID) -> str:
        """
        Gera um nome único para o arquivo.
        
        Args:
            original_filename: Nome original do arquivo
            upload_id: ID do upload
            
        Returns:
            str: Nome único do arquivo
        """
        file_extension = Path(original_filename).suffix
        return f"{upload_id}{file_extension}"

    def _ensure_directory_exists(self, directory_path: Path) -> None:
        """
        Garante que o diretório existe.
        
        Args:
            directory_path: Caminho do diretório
        """
        directory_path.mkdir(parents=True, exist_ok=True)

    async def upload_file(
        self,
        file: UploadFile,
        context: MediaContext,
        uploaded_by: UUID,
        directory_id: Optional[UUID] = None
    ) -> MediaUploadResponse:
        """
        Faz upload de um arquivo.
        
        Args:
            file: Arquivo para upload
            context: Contexto de mídia
            uploaded_by: ID do usuário que fez upload
            directory_id: ID do diretório (opcional)
            
        Returns:
            MediaUploadResponse: Resposta do upload
        """
        try:
            # Verifica tamanho do arquivo
            file_size = 0
            content = await file.read()
            file_size = len(content)
            file_size_mb = file_size / (1024 * 1024)

            # Verifica quota
            quota_check = await self.context_service.check_quota(context.id, int(file_size_mb))
            if not quota_check["can_upload"]:
                return MediaUploadResponse(
                    success=False,
                    message=f"Upload negado: {quota_check['reason']}"
                )

            # Cria registro de upload
            media_type = self._get_media_type_from_mime(file.content_type or "application/octet-stream")
            
            upload_data = MediaUploadCreate(
                context_id=context.id,
                uploaded_by=uploaded_by,
                filename="",  # Será definido após gerar nome único
                original_filename=file.filename or "unknown",
                file_path="",  # Será definido após salvar arquivo
                file_size=file_size,
                mime_type=file.content_type or "application/octet-stream",
                media_type=media_type,
                directory_id=directory_id
            )

            upload = MediaUpload(**upload_data.model_dump())
            self.db.add(upload)
            await self.db.flush()  # Para obter o ID

            # Gera nome único e caminho
            unique_filename = self._generate_unique_filename(upload.original_filename, upload.id)
            storage_path = Path(context.get_storage_path())
            file_path = storage_path / unique_filename

            # Atualiza registro com informações do arquivo
            upload.filename = unique_filename
            upload.file_path = str(file_path)
            upload.upload_status = MediaUploadStatus.UPLOADING

            # Garante que o diretório existe
            self._ensure_directory_exists(storage_path)

            # Salva arquivo no disco
            with open(file_path, "wb") as buffer:
                buffer.write(content)

            # Atualiza status para concluído
            upload.upload_status = MediaUploadStatus.COMPLETED
            
            # Atualiza espaço usado no contexto
            await self.context_service.update_used_space(context.id, int(file_size_mb))

            await self.db.commit()
            await self.db.refresh(upload)

            logger.info(f"Upload concluído: {upload.id} - {upload.filename}")

            return MediaUploadResponse(
                success=True,
                message="Upload realizado com sucesso",
                upload_id=upload.id,
                file_url=f"/api/modules/core/media/download/{upload.id}"
            )

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Erro no upload: {e}")
            
            # Marca upload como falhou se foi criado
            if 'upload' in locals():
                upload.upload_status = MediaUploadStatus.FAILED
                await self.db.commit()

            return MediaUploadResponse(
                success=False,
                message=f"Erro no upload: {str(e)}"
            )

    async def get_upload(self, upload_id: UUID) -> Optional[MediaUpload]:
        """
        Obtém um upload por ID.

        Args:
            upload_id: ID do upload

        Returns:
            MediaUpload ou None
        """
        stmt = select(MediaUpload).where(MediaUpload.id == upload_id)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    async def get_uploads_by_context(
        self,
        context_id: UUID,
        skip: int = 0,
        limit: int = 100,
        media_type: Optional[MediaFileType] = None
    ) -> List[MediaUpload]:
        """
        Lista uploads por contexto.

        Args:
            context_id: ID do contexto
            skip: Número de registros para pular
            limit: Limite de registros
            media_type: Filtro por tipo de mídia (opcional)

        Returns:
            List[MediaUpload]: Lista de uploads
        """
        stmt = select(MediaUpload).where(MediaUpload.context_id == context_id)

        if media_type:
            stmt = stmt.where(MediaUpload.media_type == media_type)

        stmt = stmt.order_by(MediaUpload.created_at.desc()).offset(skip).limit(limit)
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def update_upload(
        self,
        upload_id: UUID,
        upload_data: MediaUploadUpdate
    ) -> Optional[MediaUpload]:
        """
        Atualiza um upload.
        
        Args:
            upload_id: ID do upload
            upload_data: Dados para atualização
            
        Returns:
            MediaUpload atualizado ou None
        """
        try:
            upload = await self.get_upload(upload_id)
            if not upload:
                return None

            update_data = upload_data.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                setattr(upload, field, value)

            await self.db.commit()
            await self.db.refresh(upload)
            
            logger.info(f"Upload atualizado: {upload_id}")
            return upload

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Erro ao atualizar upload: {e}")
            raise

    async def delete_upload(self, upload_id: UUID) -> bool:
        """
        Remove um upload e seu arquivo.
        
        Args:
            upload_id: ID do upload
            
        Returns:
            bool: True se removido com sucesso
        """
        try:
            upload = await self.get_upload(upload_id)
            if not upload:
                return False

            # Remove arquivo do disco
            file_path = Path(upload.file_path)
            if file_path.exists():
                file_path.unlink()

            # Remove thumbnail se existir
            if upload.thumbnail_path:
                thumb_path = Path(upload.thumbnail_path)
                if thumb_path.exists():
                    thumb_path.unlink()

            # Remove arquivo comprimido se existir
            if upload.compressed_path:
                comp_path = Path(upload.compressed_path)
                if comp_path.exists():
                    comp_path.unlink()

            # Atualiza espaço usado no contexto
            file_size_mb = int(upload.file_size / (1024 * 1024))
            await self.context_service.update_used_space(upload.context_id, -file_size_mb)

            # Remove registro do banco
            await self.db.delete(upload)
            await self.db.commit()
            
            logger.info(f"Upload removido: {upload_id}")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Erro ao remover upload: {e}")
            return False

    async def get_file_content(self, upload_id: UUID) -> Optional[bytes]:
        """
        Obtém o conteúdo de um arquivo.
        
        Args:
            upload_id: ID do upload
            
        Returns:
            bytes: Conteúdo do arquivo ou None
        """
        upload = await self.get_upload(upload_id)
        if not upload:
            return None

        file_path = Path(upload.file_path)
        if not file_path.exists():
            return None

        try:
            with open(file_path, "rb") as f:
                return f.read()
        except Exception as e:
            logger.error(f"Erro ao ler arquivo {upload_id}: {e}")
            return None

    async def update_display_order(self, upload_id: UUID, display_order: int) -> bool:
        """
        Atualiza a ordem de exibição de um upload.

        Args:
            upload_id: ID do upload
            display_order: Nova ordem de exibição

        Returns:
            bool: True se atualizado com sucesso
        """
        try:
            upload = await self.get_upload(upload_id)
            if not upload:
                return False

            upload.display_order = display_order
            await self.db.commit()

            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Erro ao atualizar ordem do upload {upload_id}: {e}")
            return False

    async def set_primary_media(self, menu_item_id: UUID, upload_id: UUID) -> bool:
        """
        Define uma mídia como primária para um item do menu.
        A mídia primária sempre será movida para display_order = 0 (primeira posição).

        Args:
            menu_item_id: ID do item do menu
            upload_id: ID do upload a ser definido como primário

        Returns:
            bool: True se definido com sucesso
        """
        try:
            logger.info(f"🔄 Setting primary media: menu_item_id={menu_item_id}, upload_id={upload_id}")

            # Get all associations for this menu item
            from ..models import MediaMenuItemMedia
            associations_stmt = select(MediaMenuItemMedia).where(
                MediaMenuItemMedia.menu_item_id == menu_item_id
            ).order_by(MediaMenuItemMedia.display_order)

            associations_result = await self.db.execute(associations_stmt)
            associations = associations_result.scalars().all()

            logger.info(f"🔍 Found {len(associations)} associations for menu item {menu_item_id}")
            for i, assoc in enumerate(associations):
                logger.info(f"  - Association {i}: upload_id={assoc.media_upload_id}, is_primary={assoc.is_primary}, display_order={assoc.display_order}")

            # Find the association to be set as primary
            primary_association = None
            for assoc in associations:
                if assoc.media_upload_id == upload_id:
                    primary_association = assoc
                    break

            if not primary_association:
                logger.error(f"Association for upload {upload_id} not found for menu item {menu_item_id}")
                return False

            # Remove the primary association from its current position
            associations = [a for a in associations if a.media_upload_id != upload_id]

            # Insert the primary association at the beginning
            associations.insert(0, primary_association)

            # First, remove is_primary from all associations to avoid constraint violation
            for assoc in associations:
                assoc.is_primary = False
            await self.db.flush()  # Flush to database but don't commit yet

            # Then update display_order and set the first one as primary
            for index, assoc in enumerate(associations):
                assoc.display_order = index
                assoc.is_primary = (index == 0)  # Only first is primary

            await self.db.commit()
            logger.info(f"Set media {upload_id} as primary (first position) for menu item {menu_item_id}")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Erro ao definir mídia primária {upload_id} para item {menu_item_id}: {e}")
            return False

    async def get_menu_item_media(self, menu_item_id: UUID) -> List[dict]:
        """
        Busca mídia de um item do menu com informações de is_primary e display_order.

        Args:
            menu_item_id: ID do item do menu

        Returns:
            List[dict]: Lista de mídias com informações completas
        """
        try:
            # Buscar na tabela media_menu_item_media que tem is_primary e display_order
            from ..models import MediaMenuItemMedia

            stmt = select(MediaMenuItemMedia, MediaUpload).join(
                MediaUpload, MediaMenuItemMedia.media_upload_id == MediaUpload.id
            ).where(
                MediaMenuItemMedia.menu_item_id == menu_item_id
            ).order_by(MediaMenuItemMedia.display_order)

            result = await self.db.execute(stmt)
            rows = result.all()

            media_list = []
            for media_menu_item_media, media_upload in rows:
                media_dict = {
                    'id': str(media_upload.id),
                    'filename': media_upload.filename,
                    'media_type': media_upload.media_type.value if media_upload.media_type else 'image',
                    'file_size': media_upload.file_size,
                    'display_order': media_menu_item_media.display_order,
                    'is_primary': media_menu_item_media.is_primary,
                    'file_url': f"/api/modules/core/media/download/{media_upload.id}",
                    'thumbnail_url': f"/api/modules/core/media/download/{media_upload.id}?thumbnail=true" if media_upload.thumbnail_path else None,
                    'context_type': 'menu_item',
                    'context_id': str(menu_item_id),
                    'created_at': media_upload.created_at.isoformat() if media_upload.created_at else None
                }
                media_list.append(media_dict)

            logger.info(f"Found {len(media_list)} media items for menu item {menu_item_id}")
            return media_list

        except Exception as e:
            logger.error(f"Erro ao buscar mídia do item do menu {menu_item_id}: {e}")
            return []

    async def create_menu_item_association(self, menu_item_id: UUID, upload_id: UUID):
        """Cria associação entre upload e item do menu."""
        try:
            from ..models import MediaMenuItemMedia

            # Verificar se já existe associação
            existing = await self.db.execute(
                select(MediaMenuItemMedia).where(
                    MediaMenuItemMedia.menu_item_id == menu_item_id,
                    MediaMenuItemMedia.media_upload_id == upload_id
                )
            )

            if existing.scalars().first():
                logger.info(f"Association already exists: {menu_item_id} -> {upload_id}")
                return

            # Verificar quantas imagens já existem
            count_result = await self.db.execute(
                select(MediaMenuItemMedia).where(
                    MediaMenuItemMedia.menu_item_id == menu_item_id
                )
            )
            existing_associations = count_result.scalars().all()
            existing_count = len(existing_associations)

            # Se é a primeira imagem, será primária
            # Se não é a primeira, será adicionada no final sem ser primária
            is_primary = existing_count == 0

            # Criar associação
            association = MediaMenuItemMedia(
                menu_item_id=menu_item_id,
                media_upload_id=upload_id,
                display_order=existing_count,  # Adicionar no final
                is_primary=is_primary
            )

            self.db.add(association)
            await self.db.commit()

            logger.info(f"Created menu item association: {menu_item_id} -> {upload_id} (primary: {is_primary})")

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Erro ao criar associação menu item: {e}")
            raise
