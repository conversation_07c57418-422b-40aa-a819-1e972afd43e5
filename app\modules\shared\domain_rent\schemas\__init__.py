"""Schemas for Domain Rent module."""

from app.modules.shared.domain_rent.schemas.domain_schemas import (  # noqa: E402
    DomainRegistrationBase,
    DomainRegistrationCreate,
    DomainRegistrationUpdate,
    DomainRegistrationRead,
    DomainAvailabilityRequest,
    DomainAvailabilityResult,
    DomainRegistrationRequest,
    DomainTransferRequest,
    DomainRenewalRequest,
    NameserverUpdateRequest,
    WhoisPrivacyRequest,
    AutoRenewRequest,
)
from app.modules.shared.domain_rent.schemas.contact_schemas import (  # noqa: E402
    DomainContactBase,
    DomainContactCreate,
    DomainContactUpdate,
    DomainContactRead,
    ContactUpdateRequest,
)
from app.modules.shared.domain_rent.schemas.config_schemas import (  # noqa: E402
    RegistrarConfigBase,
    RegistrarConfigCreate,
    RegistrarConfigUpdate,
    RegistrarConfigRead,
)

__all__ = [
    "DomainRegistrationBase",
    "DomainRegistrationCreate",
    "DomainRegistrationUpdate",
    "DomainRegistrationRead",
    "DomainAvailabilityRequest",
    "DomainAvailabilityResult",
    "DomainRegistrationRequest",
    "DomainTransferRequest",
    "DomainRenewalRequest",
    "NameserverUpdateRequest",
    "WhoisPrivacyRequest",
    "AutoRenewRequest",
    "DomainContactBase",
    "DomainContactCreate",
    "DomainContactUpdate",
    "DomainContactRead",
    "ContactUpdateRequest",
    "RegistrarConfigBase",
    "RegistrarConfigCreate",
    "RegistrarConfigUpdate",
    "RegistrarConfigRead",
]
