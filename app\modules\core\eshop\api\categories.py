import logging
import uuid
from typing import List, Optional, Annotated, Any

from fastapi import (
    APIRouter,
    Depends,
    HTTPException,
    status,
    Query,
    Path,
)
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError
from typing import List, Optional

# Set up logger
logger = logging.getLogger(__name__)

# Import models and dependencies
from app.modules.core.users.models.user import User
from app.modules.core.auth.dependencies.auth_dependencies import get_current_active_user, get_current_user
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    get_current_tenant_from_header,
    require_tenant_role,
)
from app.modules.core.tenants.models.tenant import Tenant
from app.modules.core.roles.models.roles import RolePermissions

# Import eshop services and schemas
from app.modules.core.eshop.services.product_category_service import ProductCategoryService
from app.modules.core.eshop.schemas.product_category import (
    ProductCategoryCreate,
    ProductCategoryUpdate,
    ProductCategoryResponse,
    ProductCategoryTreeResponse,
)
from app.core.dependencies import get_db
from app.modules.core.eshop.services.category_service import CategoryService
from app.modules.core.eshop.schemas.eshop_category import (
    eshopCategoryCreate,
    eshopCategoryRead,
    eshopCategoryUpdate,
)
from app.core.enums import MarketType

# Dependency to get category service
async def get_category_service(db_session=Depends(lambda: None)) -> ProductCategoryService:
    # This should be implemented properly with database session
    pass

# Router setup
router = APIRouter(
    prefix="/categories",
    tags=["eshop - Categories"],
)

# Define required roles
write_roles = RolePermissions.ADMIN_ROLES
view_roles = RolePermissions.VIEW_ROLES + ["TCostumer"]


@router.post("/", response_model=eshopCategoryRead, status_code=status.HTTP_201_CREATED)
async def create_category(
    category_in: eshopCategoryCreate,
    db: AsyncSession = Depends(get_db),
    # current_user: User = Depends(get_current_admin_user), # Placeholder
):
    service = CategoryService(db)
    return await service.create_category(category_in=category_in)


@router.get("/", response_model=List[eshopCategoryRead])
async def list_categories(
    market_type: Optional[MarketType] = Query(None, description="Filter categories by market type"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant_from_header)
):
    """List all categories, optionally filtered by market type"""
    service = CategoryService(db)
    categories = await service.get_all_categories(current_tenant.id, market_type)
    return categories


@router.get("/universal", response_model=List[eshopCategoryRead])
async def list_universal_categories(
    market_type: Optional[MarketType] = Query(None, description="Filter categories by market type"),
    db: AsyncSession = Depends(get_db)
):
    """List all universal categories for admin purposes, optionally filtered by market type"""
    service = CategoryService(db)
    categories = await service.get_all_universal_categories(market_type)
    return categories


@router.post("/universal", response_model=eshopCategoryRead, status_code=status.HTTP_201_CREATED)
async def create_universal_category(
    category_in: eshopCategoryCreate,
    db: AsyncSession = Depends(get_db)
):
    """Create a universal category for admin purposes"""
    service = CategoryService(db)
    try:
        return await service.create_category(category_in=category_in)
    except IntegrityError as e:
        if "duplicate key value violates unique constraint" in str(e):
            if "slug" in str(e):
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=f"Category with slug '{category_in.slug}' already exists"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail="Category with this data already exists"
                )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to create category due to data constraint violation"
        )


@router.put("/universal/{category_id}", response_model=eshopCategoryRead)
async def update_universal_category(
    category_id: uuid.UUID,
    category_in: eshopCategoryUpdate,
    db: AsyncSession = Depends(get_db)
):
    """Update a universal category for admin purposes"""
    service = CategoryService(db)
    try:
        category = await service.update_category(category_id=category_id, category_in=category_in)
        if not category:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Category not found")
        return category
    except IntegrityError as e:
        if "duplicate key value violates unique constraint" in str(e):
            if "slug" in str(e):
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=f"Category with slug '{category_in.slug}' already exists"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail="Category with this data already exists"
                )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to update category due to data constraint violation"
        )


@router.delete("/universal/{category_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_universal_category(
    category_id: uuid.UUID,
    db: AsyncSession = Depends(get_db)
):
    """Delete a universal category for admin purposes (cascade delete for children)"""
    service = CategoryService(db)
    if not await service.delete_category(category_id):
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Category not found")
    return


@router.get("/{category_id}", response_model=eshopCategoryRead)
async def get_category(category_id: uuid.UUID, db: AsyncSession = Depends(get_db)):
    service = CategoryService(db)
    category = await service.get_category(category_id)
    if not category:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Category not found")
    return category


@router.put("/{category_id}", response_model=eshopCategoryRead)
async def update_category(
    category_id: uuid.UUID,
    category_in: eshopCategoryUpdate,
    db: AsyncSession = Depends(get_db),
    # current_user: User = Depends(get_current_admin_user), # Placeholder
):
    service = CategoryService(db)
    category = await service.update_category(category_id=category_id, category_in=category_in)
    if not category:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Category not found")
    return category


@router.delete("/{category_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_category(
    category_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    # current_user: User = Depends(get_current_admin_user), # Placeholder
):
    service = CategoryService(db)
    if not await service.delete_category(category_id):
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Category not found")
    return


@router.get("/tree", response_model=List[ProductCategoryTreeResponse])
async def read_category_tree(
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Optional[Tenant], Depends(get_current_tenant_from_header)],
    category_service: Annotated[ProductCategoryService, Depends(get_category_service)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))],
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    market_type: Optional[MarketType] = Query(None, description="Filter categories by market type"),
):
    """
    Retrieve the complete category tree structure.
    """
    try:
        tenant_id = current_tenant.id if current_tenant else None
        
        categories = await category_service.get_category_tree(
            tenant_id=tenant_id,
            is_active=is_active,
            market_type=market_type,
        )

        return [ProductCategoryTreeResponse.model_validate(category) for category in categories]
    
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"Unexpected error retrieving category tree: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while retrieving category tree",
        )


@router.get("/{category_id}", response_model=ProductCategoryResponse)
async def read_category(
    category_id: Annotated[uuid.UUID, Path(..., description="The ID of the category to retrieve")],
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Optional[Tenant], Depends(get_current_tenant_from_header)],
    category_service: Annotated[ProductCategoryService, Depends(get_category_service)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))],
    include_children: bool = Query(False, description="Include child categories"),
):
    """
    Retrieve a specific category by ID.
    """
    try:
        tenant_id = current_tenant.id if current_tenant else None
        
        category = await category_service.get_category(
            category_id=category_id,
            tenant_id=tenant_id,
            include_children=include_children
        )
        
        if category is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Category not found",
            )

        return ProductCategoryResponse.model_validate(category)
    
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"Unexpected error retrieving category {category_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while retrieving the category",
        )


@router.put("/{category_id}", response_model=ProductCategoryResponse)
async def update_category(
    category_id: Annotated[uuid.UUID, Path(..., description="The ID of the category to update")],
    category_in: ProductCategoryUpdate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Optional[Tenant], Depends(get_current_tenant_from_header)],
    category_service: Annotated[ProductCategoryService, Depends(get_category_service)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=write_roles))],
):
    """
    Update a specific category by ID.
    Requires admin role.
    """
    try:
        tenant_id = current_tenant.id if current_tenant else None
        
        updated_category = await category_service.update_category(
            category_id=category_id,
            category_in=category_in,
            current_user_id=current_user.id,
            tenant_id=tenant_id
        )
        
        if updated_category is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Category not found",
            )

        return ProductCategoryResponse.model_validate(updated_category)
    
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"Unexpected error updating category {category_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while updating the category",
        )


@router.delete("/{category_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_category(
    category_id: Annotated[uuid.UUID, Path(..., description="The ID of the category to delete")],
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Optional[Tenant], Depends(get_current_tenant_from_header)],
    category_service: Annotated[ProductCategoryService, Depends(get_category_service)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=write_roles))],
):
    """
    Delete a specific category by ID.
    Requires admin role.
    """
    try:
        tenant_id = current_tenant.id if current_tenant else None
        
        success = await category_service.delete_category(
            category_id=category_id,
            current_user_id=current_user.id,
            tenant_id=tenant_id
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Category not found",
            )

        return None  # 204 No Content response
    
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"Unexpected error deleting category {category_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while deleting the category",
        )
