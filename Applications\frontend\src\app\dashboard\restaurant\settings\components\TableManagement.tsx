'use client';

import React, { useState, useEffect } from 'react';
import { useTableManagement } from '@/hooks/useTableManagement';
import LayoutModeToggle from './shared/LayoutModeToggle';
import ListLayoutMode from './list/ListLayoutMode';
import VisualLayoutMode from './visual/VisualLayoutMode';
import TableFormModal from './shared/TableFormModal';
import ZoneFormModal from './shared/ZoneFormModal';
import { PlusIcon, MapPinIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

export default function TableManagement() {
  const {
    tables,
    zones,
    loading,
    error,
    loadTables,
    createTable,
    updateTable,
    deleteTable,
    filterByZone
  } = useTableManagement();

  const [viewMode, setViewMode] = useState<'list' | 'visual'>('list');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showZoneForm, setShowZoneForm] = useState(false);
  const [customZones, setCustomZones] = useState<string[]>([]);

  // Load tables on mount
  useEffect(() => {
    loadTables();
  }, [loadTables]);

  // Load custom zones from localStorage
  useEffect(() => {
    const savedZones = localStorage.getItem('customZones');
    if (savedZones) {
      try {
        setCustomZones(JSON.parse(savedZones));
      } catch (error) {
        console.error('Error loading custom zones:', error);
      }
    }
  }, []);

  // Combine zones from tables with custom zones
  const allZones = React.useMemo(() => {
    const tableZones = zones; // zones from useTableManagement (extracted from tables)
    const combined = Array.from(new Set([...tableZones, ...customZones]));
    return combined.sort();
  }, [zones, customZones]);

  // Get zone status (active if has tables, inactive if empty)
  const getZoneStatus = React.useCallback((zone: string) => {
    const tablesInZone = filterByZone(zone);
    return {
      hasTable: tablesInZone.length > 0,
      tableCount: tablesInZone.length,
      isCustom: customZones.includes(zone) && !zones.includes(zone)
    };
  }, [filterByZone, customZones, zones]);

  // Create zone as independent entity
  const handleCreateZone = async (zoneName: string) => {
    try {
      console.log('🏗️ Creating zone:', zoneName);

      // Add zone to custom zones
      const updatedCustomZones = [...customZones, zoneName];
      setCustomZones(updatedCustomZones);

      // Save to localStorage
      localStorage.setItem('customZones', JSON.stringify(updatedCustomZones));

      console.log('✅ Zone created successfully:', zoneName);
      toast.success(`Zona "${zoneName}" criada com sucesso!`);

    } catch (error) {
      console.error('❌ Error creating zone:', error);
      toast.error('Erro ao criar zona');
      throw error;
    }
  };

  // Modal handlers
  const handleTableFormSubmit = async (data: any) => {
    try {
      const result = await createTable(data);
      if (result) {
        toast.success('Mesa criada com sucesso!');
        setShowCreateForm(false);
      }
    } catch (error) {
      toast.error('Erro ao criar mesa');
    }
  };

  const handleZoneFormSubmit = async (zoneName: string) => {
    await handleCreateZone(zoneName);
    setShowZoneForm(false);
  };



  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">
            Gerenciamento de Mesas
          </h2>
          <p className="text-sm text-gray-600">
            Gerencie mesas e zonas do seu restaurante
          </p>
        </div>

        <div className="flex items-center space-x-4">
          {/* Action Buttons */}
          <div className="flex space-x-3">
            <button
              onClick={() => setShowZoneForm(true)}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
            >
              <MapPinIcon className="h-4 w-4" />
              <span>Nova Zona</span>
            </button>

            <button
              onClick={() => setShowCreateForm(true)}
              className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors flex items-center space-x-2"
            >
              <PlusIcon className="h-4 w-4" />
              <span>Nova Mesa</span>
            </button>
          </div>

          {/* View Mode Toggle */}
          <LayoutModeToggle
            viewMode={viewMode}
            onViewModeChange={setViewMode}
            disabled={loading}
          />
        </div>
      </div>

      {/* Content based on view mode */}
      {viewMode === 'list' ? (
        <ListLayoutMode
          tables={tables}
          zones={allZones}
          loading={loading}
          error={error}
          onCreateTable={createTable}
          onUpdateTable={updateTable}
          onDeleteTable={deleteTable}
          filterByZone={filterByZone}
          getZoneStatus={getZoneStatus}
        />
      ) : (
        <VisualLayoutMode
          tables={tables}
          zones={allZones}
          loading={loading}
          error={error}
          onCreateTable={createTable}
          onUpdateTable={updateTable}
          onDeleteTable={deleteTable}
          filterByZone={filterByZone}
          getZoneStatus={getZoneStatus}
          onViewModeChange={setViewMode}
        />
      )}

      {/* Table Form Modal */}
      <TableFormModal
        isOpen={showCreateForm}
        editingTable={null}
        zones={allZones}
        onClose={() => setShowCreateForm(false)}
        onSubmit={handleTableFormSubmit}
      />

      {/* Zone Form Modal */}
      <ZoneFormModal
        isOpen={showZoneForm}
        editingZone={null}
        existingZones={allZones}
        onClose={() => setShowZoneForm(false)}
        onSubmit={handleZoneFormSubmit}
      />
    </div>
  );
}
