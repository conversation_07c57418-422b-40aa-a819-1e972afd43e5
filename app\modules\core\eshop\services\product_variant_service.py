import logging
from typing import Optional, Sequence, List
from sqlalchemy import select, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.exc import IntegrityError
from fastapi import HTTPException, status
import uuid

# Import models
from app.modules.core.eshop.models.product_variant_group import ProductVariantGroup
from app.modules.core.eshop.models.product_variant_option import ProductVariantOption

# Import schemas
from app.modules.core.eshop.schemas.product_variant_group import (
    ProductVariantGroupCreate,
    ProductVariantGroupUpdate,
    ProductVariantGroupResponse,
)
from app.modules.core.eshop.schemas.product_variant_option import (
    ProductVariantOptionCreate,
    ProductVariantOptionUpdate,
    ProductVariantOptionResponse,
)

# Import WebSocket utility
from app.websockets.manager import emit_to_tenant

logger = logging.getLogger(__name__)


class ProductVariantService:
    def __init__(self, db_session: AsyncSession):
        self.db = db_session

    # Variant Group Methods
    async def create_variant_group(
        self, 
        group_in: ProductVariantGroupCreate,
        current_user_id: uuid.UUID
    ) -> ProductVariantGroup:
        """Creates a new product variant group."""
        logger.info(f"Creating product variant group: {group_in.name}")

        try:
            group_data = group_in.model_dump()
            db_group = ProductVariantGroup(**group_data)
            self.db.add(db_group)
            await self.db.commit()
            await self.db.refresh(db_group)

            # Emit WebSocket notification
            if group_in.tenant_id:
                await emit_to_tenant(
                    group_in.tenant_id,
                    "variant_group_created",
                    {"group_id": str(db_group.id), "group_name": db_group.name}
                )

            logger.info(f"Product variant group created: {db_group.id}")
            return db_group

        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"IntegrityError creating variant group: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Error creating variant group. Database constraint violation: {str(e)}",
            )
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Unexpected error creating variant group: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal server error: {str(e)}",
            )

    async def get_variant_group(
        self, 
        group_id: uuid.UUID, 
        tenant_id: Optional[uuid.UUID] = None,
        include_options: bool = False
    ) -> Optional[ProductVariantGroup]:
        """Gets a specific product variant group by ID."""
        query = select(ProductVariantGroup).where(ProductVariantGroup.id == group_id)
        
        if tenant_id:
            query = query.where(
                or_(ProductVariantGroup.tenant_id == tenant_id, ProductVariantGroup.tenant_id.is_(None))
            )

        if include_options:
            query = query.options(selectinload(ProductVariantGroup.options))

        result = await self.db.execute(query)
        group = result.scalars().first()

        if not group:
            logger.warning(f"Variant group {group_id} not found")
            return None

        return group

    async def get_variant_groups(
        self,
        tenant_id: Optional[uuid.UUID] = None,
        is_active: Optional[bool] = None,
        is_template: Optional[bool] = None,
        include_options: bool = False,
        skip: int = 0,
        limit: int = 100,
    ) -> Sequence[ProductVariantGroup]:
        """Gets a list of product variant groups with filtering options."""
        query = select(ProductVariantGroup)

        # Apply filters
        filters = []
        
        if tenant_id:
            filters.append(
                or_(ProductVariantGroup.tenant_id == tenant_id, ProductVariantGroup.tenant_id.is_(None))
            )
            
        if is_active is not None:
            filters.append(ProductVariantGroup.is_active == is_active)
            
        if is_template is not None:
            filters.append(ProductVariantGroup.is_template == is_template)

        if filters:
            query = query.where(and_(*filters))

        # Apply ordering and pagination
        query = query.order_by(ProductVariantGroup.display_order, ProductVariantGroup.name)
        query = query.offset(skip).limit(limit)

        # Load options if requested
        if include_options:
            query = query.options(selectinload(ProductVariantGroup.options))

        result = await self.db.execute(query)
        groups = result.scalars().all()

        return groups

    async def update_variant_group(
        self, 
        group_id: uuid.UUID, 
        group_in: ProductVariantGroupUpdate,
        current_user_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID] = None
    ) -> Optional[ProductVariantGroup]:
        """Updates an existing product variant group."""
        db_group = await self.get_variant_group(group_id, tenant_id)
        if not db_group:
            return None

        try:
            update_data = group_in.model_dump(exclude_unset=True)
            
            # Apply updates
            for field, value in update_data.items():
                setattr(db_group, field, value)

            await self.db.commit()
            await self.db.refresh(db_group)

            # Emit WebSocket notification
            if db_group.tenant_id:
                await emit_to_tenant(
                    db_group.tenant_id,
                    "variant_group_updated",
                    {"group_id": str(db_group.id), "group_name": db_group.name}
                )

            logger.info(f"Product variant group updated: {db_group.id}")
            return db_group

        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"IntegrityError updating variant group: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Error updating variant group. Database constraint violation: {str(e)}",
            )
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Unexpected error updating variant group: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal server error: {str(e)}",
            )

    async def delete_variant_group(
        self, 
        group_id: uuid.UUID,
        current_user_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID] = None
    ) -> bool:
        """Deletes a product variant group."""
        db_group = await self.get_variant_group(group_id, tenant_id, include_options=True)
        if not db_group:
            return False

        try:
            await self.db.delete(db_group)
            await self.db.commit()

            # Emit WebSocket notification
            if db_group.tenant_id:
                await emit_to_tenant(
                    db_group.tenant_id,
                    "variant_group_deleted",
                    {"group_id": str(group_id), "group_name": db_group.name}
                )

            logger.info(f"Product variant group deleted: {group_id}")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Error deleting variant group: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal server error: {str(e)}",
            )

    # Variant Option Methods
    async def create_variant_option(
        self, 
        option_in: ProductVariantOptionCreate,
        current_user_id: uuid.UUID
    ) -> ProductVariantOption:
        """Creates a new product variant option."""
        logger.info(f"Creating product variant option: {option_in.name}")

        # Check if variant group exists
        group = await self.get_variant_group(option_in.variant_group_id, option_in.tenant_id)
        if not group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Variant group not found.",
            )

        try:
            option_data = option_in.model_dump()
            db_option = ProductVariantOption(**option_data)
            self.db.add(db_option)
            await self.db.commit()
            await self.db.refresh(db_option)

            # Emit WebSocket notification
            if option_in.tenant_id:
                await emit_to_tenant(
                    option_in.tenant_id,
                    "variant_option_created",
                    {"option_id": str(db_option.id), "option_name": db_option.name}
                )

            logger.info(f"Product variant option created: {db_option.id}")
            return db_option

        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"IntegrityError creating variant option: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Error creating variant option. Database constraint violation: {str(e)}",
            )
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Unexpected error creating variant option: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal server error: {str(e)}",
            )

    async def get_variant_option(
        self, 
        option_id: uuid.UUID, 
        tenant_id: Optional[uuid.UUID] = None
    ) -> Optional[ProductVariantOption]:
        """Gets a specific product variant option by ID."""
        query = select(ProductVariantOption).where(ProductVariantOption.id == option_id)
        
        if tenant_id:
            query = query.where(
                or_(ProductVariantOption.tenant_id == tenant_id, ProductVariantOption.tenant_id.is_(None))
            )

        result = await self.db.execute(query)
        option = result.scalars().first()

        if not option:
            logger.warning(f"Variant option {option_id} not found")
            return None

        return option

    async def get_variant_options(
        self,
        variant_group_id: Optional[uuid.UUID] = None,
        tenant_id: Optional[uuid.UUID] = None,
        is_active: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> Sequence[ProductVariantOption]:
        """Gets a list of product variant options with filtering options."""
        query = select(ProductVariantOption)

        # Apply filters
        filters = []
        
        if variant_group_id:
            filters.append(ProductVariantOption.variant_group_id == variant_group_id)
        
        if tenant_id:
            filters.append(
                or_(ProductVariantOption.tenant_id == tenant_id, ProductVariantOption.tenant_id.is_(None))
            )
            
        if is_active is not None:
            filters.append(ProductVariantOption.is_active == is_active)

        if filters:
            query = query.where(and_(*filters))

        # Apply ordering and pagination
        query = query.order_by(ProductVariantOption.display_order, ProductVariantOption.name)
        query = query.offset(skip).limit(limit)

        result = await self.db.execute(query)
        options = result.scalars().all()

        return options

    async def update_variant_option(
        self, 
        option_id: uuid.UUID, 
        option_in: ProductVariantOptionUpdate,
        current_user_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID] = None
    ) -> Optional[ProductVariantOption]:
        """Updates an existing product variant option."""
        db_option = await self.get_variant_option(option_id, tenant_id)
        if not db_option:
            return None

        try:
            update_data = option_in.model_dump(exclude_unset=True)
            
            # Apply updates
            for field, value in update_data.items():
                setattr(db_option, field, value)

            await self.db.commit()
            await self.db.refresh(db_option)

            # Emit WebSocket notification
            if db_option.tenant_id:
                await emit_to_tenant(
                    db_option.tenant_id,
                    "variant_option_updated",
                    {"option_id": str(db_option.id), "option_name": db_option.name}
                )

            logger.info(f"Product variant option updated: {db_option.id}")
            return db_option

        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"IntegrityError updating variant option: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Error updating variant option. Database constraint violation: {str(e)}",
            )
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Unexpected error updating variant option: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal server error: {str(e)}",
            )

    async def delete_variant_option(
        self, 
        option_id: uuid.UUID,
        current_user_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID] = None
    ) -> bool:
        """Deletes a product variant option."""
        db_option = await self.get_variant_option(option_id, tenant_id)
        if not db_option:
            return False

        try:
            await self.db.delete(db_option)
            await self.db.commit()

            # Emit WebSocket notification
            if db_option.tenant_id:
                await emit_to_tenant(
                    db_option.tenant_id,
                    "variant_option_deleted",
                    {"option_id": str(option_id), "option_name": db_option.name}
                )

            logger.info(f"Product variant option deleted: {option_id}")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Error deleting variant option: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal server error: {str(e)}",
            ) 