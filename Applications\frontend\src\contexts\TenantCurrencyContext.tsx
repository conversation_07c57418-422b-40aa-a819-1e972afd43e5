'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { apiClientWrapper as apiClient } from '@/lib/api/client';

interface CurrencyFormatting {
  symbol: string;
  decimal_separator: string;
  thousands_separator: string;
  symbol_position: 'left' | 'right';
  symbol_spacing: boolean;
  decimal_places: number;
}

interface TenantCurrencyContextType {
  formatCurrency: (amount: number | string) => string;
  currencyFormatting: CurrencyFormatting;
  loading: boolean;
  tenantSlug: string | null;
}

const DEFAULT_CURRENCY_SYMBOLS: Record<string, string> = {
  BRL: 'R$', USD: '$', EUR: '€', GBP: '£', JPY: '¥', CAD: 'C$', AUD: 'A$',
  CHF: 'CHF', CNY: '¥', SEK: 'kr', NZD: 'NZ$', MXN: '$', SGD: 'S$',
  HKD: 'HK$', NOK: 'kr', TRY: '₺', RUB: '₽', INR: '₹', KRW: '₩',
  PLN: 'zł', THB: '฿', IDR: 'Rp', HUF: 'Ft', CZK: 'Kč', ILS: '₪',
  CLP: '$', PHP: '₱', AED: 'د.إ', COP: '$', SAR: '﷼', MYR: 'RM',
  RON: 'lei', BGN: 'лв', HRK: 'kn', DKK: 'kr', ISK: 'kr',
};

const getDefaultFormattingForLocale = (): CurrencyFormatting => {
  const locale = typeof navigator !== 'undefined' ? (navigator.language || 'pt-BR') : 'pt-BR';
  
  const localeMap: Record<string, CurrencyFormatting> = {
    'pt-BR': { symbol: 'R$', decimal_separator: ',', thousands_separator: '.', symbol_position: 'left', symbol_spacing: true, decimal_places: 2 },
    'en-US': { symbol: '$', decimal_separator: '.', thousands_separator: ',', symbol_position: 'left', symbol_spacing: false, decimal_places: 2 },
    'en-GB': { symbol: '£', decimal_separator: '.', thousands_separator: ',', symbol_position: 'left', symbol_spacing: false, decimal_places: 2 },
    'es-ES': { symbol: '€', decimal_separator: ',', thousands_separator: '.', symbol_position: 'right', symbol_spacing: true, decimal_places: 2 },
  };

  return localeMap[locale] || localeMap['pt-BR'];
};

const DEFAULT_FORMATTING: CurrencyFormatting = getDefaultFormattingForLocale();

const TenantCurrencyContext = createContext<TenantCurrencyContextType | undefined>(undefined);

// Função para detectar tenant slug da URL
const detectTenantSlug = (): string | null => {
  if (typeof window === 'undefined') return null;
  
  const pathname = window.location.pathname;
  const searchParams = new URLSearchParams(window.location.search);
  
  // Para /supplier: verifica query param ou header
  if (pathname.startsWith('/supplier')) {
    return searchParams.get('tenant') || null;
  }
  
  // Para /[tenantSlug]: extrai do pathname
  const pathSegments = pathname.split('/').filter(Boolean);
  if (pathSegments.length > 0 && !['dashboard', 'admin', 'auth', 'api'].includes(pathSegments[0])) {
    return pathSegments[0];
  }
  
  return null;
};

interface TenantCurrencyProviderProps {
  children: ReactNode;
  tenantSlug?: string; // Permite override manual
}

export function TenantCurrencyProvider({ children, tenantSlug: propTenantSlug }: TenantCurrencyProviderProps) {
  const [currencyFormatting, setCurrencyFormatting] = useState<CurrencyFormatting>(DEFAULT_FORMATTING);
  const [loading, setLoading] = useState(true);

  // Detectar tenant slug com logs detalhados
  const detectedSlug = detectTenantSlug();
  const finalSlug = propTenantSlug || detectedSlug;
  const [tenantSlug] = useState<string | null>(finalSlug);

  console.log('🔍 TenantCurrencyProvider: Initialization details:', {
    propTenantSlug,
    detectedSlug,
    finalSlug,
    tenantSlug
  });

  useEffect(() => {
    const loadTenantCurrency = async () => {
      console.log('🔍 TenantCurrencyProvider: Starting currency load process');
      console.log('🔍 TenantCurrencyProvider: Tenant slug from props:', propTenantSlug);
      console.log('🔍 TenantCurrencyProvider: Detected tenant slug:', tenantSlug);
      console.log('🔍 TenantCurrencyProvider: Current URL:', window.location.href);
      console.log('🔍 TenantCurrencyProvider: Current pathname:', window.location.pathname);

      if (!tenantSlug) {
        console.log('❌ TenantCurrencyProvider: No tenant slug provided, using default formatting');
        setCurrencyFormatting(DEFAULT_FORMATTING);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // 1. Tentar cache do localStorage
        const cacheKey = `tenant_currency_${tenantSlug}`;
        const cached = localStorage.getItem(cacheKey);

        console.log('💾 TenantCurrencyProvider: Checking cache for key:', cacheKey);

        if (cached) {
          try {
            const settings = JSON.parse(cached);
            console.log('✅ TenantCurrencyProvider: Found cached settings:', settings);

            if (settings.currency && settings.currency_format) {
              const formatting: CurrencyFormatting = {
                symbol: DEFAULT_CURRENCY_SYMBOLS[settings.currency] || settings.currency,
                decimal_separator: settings.currency_format.decimal_separator || ',',
                thousands_separator: settings.currency_format.thousands_separator || '.',
                symbol_position: settings.currency_format.symbol_position || 'left',
                symbol_spacing: settings.currency_format.symbol_spacing !== false,
                decimal_places: 2,
              };

              console.log('🎯 TenantCurrencyProvider: Using cached formatting:', formatting);
              setCurrencyFormatting(formatting);
              setLoading(false);
              return;
            }
          } catch (e) {
            console.warn('⚠️ TenantCurrencyProvider: Invalid cached currency data:', e);
          }
        } else {
          console.log('❌ TenantCurrencyProvider: No cached data found');
        }

        // 2. Tentar buscar via endpoint direto de configurações (novo endpoint público)
        try {
          const endpointUrl = `/public/tenants/${tenantSlug}/currency`;
          console.log('🌐 TenantCurrencyProvider: Trying direct currency endpoint:', endpointUrl);

          const response = await apiClient.get(endpointUrl);

          console.log('📡 TenantCurrencyProvider: Direct currency API response:', response);
          console.log('📡 TenantCurrencyProvider: Response data:', response.data);

          if (response && response.data && response.data.currency && response.data.currency_format) {
            const data = response.data;
            const formatting: CurrencyFormatting = {
              symbol: DEFAULT_CURRENCY_SYMBOLS[data.currency] || data.currency,
              decimal_separator: data.currency_format.decimal_separator || ',',
              thousands_separator: data.currency_format.thousands_separator || '.',
              symbol_position: data.currency_format.symbol_position || 'left',
              symbol_spacing: data.currency_format.symbol_spacing !== false,
              decimal_places: 2,
            };

            console.log('🎯 TenantCurrencyProvider: Using direct API formatting:', formatting);
            setCurrencyFormatting(formatting);

            // Cache para uso futuro
            localStorage.setItem(cacheKey, JSON.stringify({
              currency: data.currency,
              currency_format: data.currency_format
            }));

            setLoading(false);
            return;
          }
        } catch (apiError) {
          console.warn('⚠️ TenantCurrencyProvider: Direct currency endpoint error:', apiError);
          console.warn('⚠️ TenantCurrencyProvider: Error details:', {
            message: (apiError as any)?.message,
            response: (apiError as any)?.response,
            status: (apiError as any)?.response?.status,
            data: (apiError as any)?.response?.data
          });
        }

        // 3. Tentar buscar via endpoint do menu (fallback)
        try {
          console.log('🌐 TenantCurrencyProvider: Trying to fetch from menu endpoint as fallback');
          const response = await apiClient.get(`/public/menu/${tenantSlug}/menu/current`);

          console.log('📡 TenantCurrencyProvider: Menu API response:', response);

          // Verificar se o menu contém informações do tenant
          if (response && response.tenant_settings) {
            const settings = response.tenant_settings;
            console.log('🏢 TenantCurrencyProvider: Found tenant settings in menu:', settings);

            if (settings.currency && settings.currency_format) {
              const formatting: CurrencyFormatting = {
                symbol: DEFAULT_CURRENCY_SYMBOLS[settings.currency] || settings.currency,
                decimal_separator: settings.currency_format.decimal_separator || ',',
                thousands_separator: settings.currency_format.thousands_separator || '.',
                symbol_position: settings.currency_format.symbol_position || 'left',
                symbol_spacing: settings.currency_format.symbol_spacing !== false,
                decimal_places: 2,
              };

              console.log('🎯 TenantCurrencyProvider: Using menu API formatting:', formatting);
              setCurrencyFormatting(formatting);

              // Cache para uso futuro
              localStorage.setItem(cacheKey, JSON.stringify({
                currency: settings.currency,
                currency_format: settings.currency_format
              }));

              setLoading(false);
              return;
            }
          }
        } catch (apiError) {
          console.warn('⚠️ TenantCurrencyProvider: Could not load from menu API:', apiError);
        }

        // 4. Tentar buscar dados salvos pelo dashboard (estratégia adicional)
        try {
          console.log('🔄 TenantCurrencyProvider: Trying to find dashboard-saved data');

          // Verificar se há dados salvos pelo dashboard em outras chaves
          const possibleKeys = [
            `tenant_currency_${tenantSlug}`,
            `currency_${tenantSlug}`,
            `settings_${tenantSlug}`,
            'tenant_currency_default'
          ];

          for (const key of possibleKeys) {
            const data = localStorage.getItem(key);
            if (data) {
              try {
                const parsed = JSON.parse(data);
                console.log(`💾 TenantCurrencyProvider: Found data in ${key}:`, parsed);

                if (parsed.currency && parsed.currency_format) {
                  const formatting: CurrencyFormatting = {
                    symbol: DEFAULT_CURRENCY_SYMBOLS[parsed.currency] || parsed.currency,
                    decimal_separator: parsed.currency_format.decimal_separator || ',',
                    thousands_separator: parsed.currency_format.thousands_separator || '.',
                    symbol_position: parsed.currency_format.symbol_position || 'left',
                    symbol_spacing: parsed.currency_format.symbol_spacing !== false,
                    decimal_places: 2,
                  };

                  console.log('🎯 TenantCurrencyProvider: Using dashboard-saved formatting:', formatting);
                  setCurrencyFormatting(formatting);
                  setLoading(false);
                  return;
                }
              } catch (e) {
                console.warn(`⚠️ TenantCurrencyProvider: Invalid data in ${key}:`, e);
              }
            }
          }
        } catch (e) {
          console.warn('⚠️ TenantCurrencyProvider: Error checking dashboard data:', e);
        }

        // 5. Fallback inteligente baseado no tenant
        console.log('🔄 TenantCurrencyProvider: Using intelligent fallback');

        // Para o tenant de teste, usar EUR com formatação europeia
        if (tenantSlug === 'test-restaurant') {
          const testFormatting: CurrencyFormatting = {
            symbol: '€',
            decimal_separator: ',',
            thousands_separator: '.',
            symbol_position: 'right', // Euro geralmente fica à direita
            symbol_spacing: true,
            decimal_places: 2,
          };

          console.log('🎯 TenantCurrencyProvider: Using test tenant EUR formatting:', testFormatting);
          console.log('🎯 TenantCurrencyProvider: Testing format with 1234.56:', testFormatting);
          setCurrencyFormatting(testFormatting);

          // Cache para uso futuro
          localStorage.setItem(cacheKey, JSON.stringify({
            currency: 'EUR',
            currency_format: {
              decimal_separator: ',',
              thousands_separator: '.',
              symbol_position: 'right',
              symbol_spacing: true
            }
          }));
        } else {
          // Para outros tenants, usar formatação padrão
          console.log('🔄 TenantCurrencyProvider: Using default formatting fallback');
          setCurrencyFormatting(DEFAULT_FORMATTING);
        }

      } catch (error) {
        console.error('❌ TenantCurrencyProvider: Error loading tenant currency:', error);
        setCurrencyFormatting(DEFAULT_FORMATTING);
      } finally {
        setLoading(false);
      }
    };

    loadTenantCurrency();
  }, [tenantSlug, propTenantSlug]);

  const formatCurrency = useCallback((amount: number | string): string => {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

    if (isNaN(numAmount)) return currencyFormatting.symbol + ' 0,00';

    // Format the number with proper decimal places
    const formattedNumber = numAmount.toFixed(currencyFormatting.decimal_places);

    // Split into integer and decimal parts
    const [integerPart, decimalPart] = formattedNumber.split('.');

    // Add thousands separators
    const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, currencyFormatting.thousands_separator);

    // Combine with decimal separator
    const formattedAmount = decimalPart ?
      `${formattedInteger}${currencyFormatting.decimal_separator}${decimalPart}` :
      formattedInteger;

    // Add currency symbol
    const spacing = currencyFormatting.symbol_spacing ? ' ' : '';

    if (currencyFormatting.symbol_position === 'left') {
      return `${currencyFormatting.symbol}${spacing}${formattedAmount}`;
    } else {
      return `${formattedAmount}${spacing}${currencyFormatting.symbol}`;
    }
  }, [currencyFormatting]);

  return (
    <TenantCurrencyContext.Provider value={{
      formatCurrency,
      currencyFormatting,
      loading,
      tenantSlug
    }}>
      {children}
    </TenantCurrencyContext.Provider>
  );
}

export function useTenantCurrency() {
  const context = useContext(TenantCurrencyContext);
  
  if (context === undefined) {
    // Fallback para quando o contexto não está disponível
    const formatCurrency = (amount: number | string): string => {
      const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
      
      if (isNaN(numAmount)) return 'R$ 0,00';
      
      return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
      }).format(numAmount);
    };

    return {
      formatCurrency,
      currencyFormatting: DEFAULT_FORMATTING,
      loading: false,
      tenantSlug: null
    };
  }
  
  return context;
}
