"""Exceptions for Email module."""


class EmailError(Exception):
    """Base exception for Email module."""

    pass


class EmailDomainError(EmailError):
    """Exception for email domain operations."""

    pass


class EmailAccountError(EmailError):
    """Exception for email account operations."""

    pass


class EmailAliasError(EmailError):
    """Exception for email alias operations."""

    pass


class EmailQuotaError(EmailError):
    """Exception for email quota operations."""

    pass


class EmailAuthError(EmailError):
    """Exception for email authentication operations."""

    pass


class EmailSendError(EmailError):
    """Exception for email sending operations."""

    pass


class EmailReceiveError(EmailError):
    """Exception for email receiving operations."""

    pass
