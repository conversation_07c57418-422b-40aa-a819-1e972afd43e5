"""
Blog Tag Models

Tag management with multi-language support for content organization.
"""

import uuid
from datetime import datetime

from sqlalchemy import Column, DateTime, ForeignKey, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base


class BlogTag(Base):
    """
    Blog tag model for content organization.

    Tags provide flexible content categorization.
    Language-specific content is stored in BlogTagTranslation.
    """

    __tablename__ = "blog_tags"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Core identifiers
    slug = Column(String(255), unique=True, nullable=False, index=True)

    # Display
    color = Column(String(7), nullable=True)  # Hex color code

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow,
                       onupdate=datetime.utcnow, nullable=False)

    # Relationships
    posts = relationship("BlogPost", secondary="blog_post_tags",
                        back_populates="tags")
    translations = relationship("BlogTagTranslation", back_populates="tag",
                               cascade="all, delete-orphan")

    def __repr__(self):
        return f"<BlogTag(id={self.id}, slug='{self.slug}')>"


class BlogTagTranslation(Base):
    """
    Blog tag translations for multi-language support.

    Stores language-specific content for blog tags.
    """

    __tablename__ = "blog_tag_translations"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Foreign keys
    tag_id = Column(UUID(as_uuid=True), ForeignKey("blog_tags.id"),
                   nullable=False)
    language_code = Column(String(10), nullable=False)  # e.g., 'en', 'pt-BR'

    # Content
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow,
                       onupdate=datetime.utcnow, nullable=False)

    # Relationships
    tag = relationship("BlogTag", back_populates="translations")

    def __repr__(self):
        return f"<BlogTagTranslation(tag_id={self.tag_id}, " \
               f"language='{self.language_code}')>"
