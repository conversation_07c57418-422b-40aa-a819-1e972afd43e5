"""
Social Media specific schemas for tenant settings.
"""

import re
from typing import Optional, List

from pydantic import BaseModel, Field, validator


class SocialMediaLinkSchema(BaseModel):
    """Schema for individual social media links."""

    id: str = Field(..., description="Unique identifier for the social media link")
    platform: str = Field(..., max_length=50, description="Social media platform name")
    url: str = Field(..., max_length=500, description="URL to the social media profile/page")
    display_name: str = Field(..., max_length=100, description="Display name for the link")
    icon: str = Field(..., max_length=50, description="Icon identifier for the platform")
    is_active: bool = Field(True, description="Whether the link is active/visible")
    order: int = Field(0, ge=0, description="Display order (0-based)")

    # Additional fields for enhanced functionality
    description: Optional[str] = Field(None, max_length=200, description="Optional description of the social media link")
    follower_count: Optional[int] = Field(None, ge=0, description="Number of followers (optional)")
    verification_status: Optional[str] = Field(None, description="Verification status: verified, unverified, pending")
    last_updated: Optional[str] = Field(None, description="Last time the link was updated")

    @validator('url')
    def validate_url(cls, v):
        """Validate URL format."""
        if not v.startswith(('http://', 'https://')):
            raise ValueError('URL must start with http:// or https://')

        # Basic URL pattern validation
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)

        if not url_pattern.match(v):
            raise ValueError('Invalid URL format')

        return v

    @validator('platform')
    def validate_platform(cls, v):
        """Validate platform name."""
        # List of supported platforms (can be extended)
        supported_platforms = [
            'facebook', 'instagram', 'twitter', 'linkedin', 'youtube',
            'tiktok', 'whatsapp', 'telegram', 'pinterest', 'snapchat',
            'discord', 'twitch', 'reddit', 'custom'
        ]

        platform_lower = v.lower()
        if platform_lower not in supported_platforms:
            # Allow custom platforms but validate they're reasonable
            if len(v) < 2 or len(v) > 50:
                raise ValueError('Platform name must be between 2 and 50 characters')

        return platform_lower

    @validator('icon')
    def validate_icon(cls, v):
        """Validate icon identifier."""
        # Icon should match platform or be a valid icon name
        if len(v) < 2 or len(v) > 50:
            raise ValueError('Icon identifier must be between 2 and 50 characters')
        return v.lower()


class SocialMediaSettingsUpdate(BaseModel):
    """Schema for updating social media settings."""
    
    social_media_links: Optional[List[SocialMediaLinkSchema]] = Field(
        None,
        description="Social media platform links with icons"
    )
