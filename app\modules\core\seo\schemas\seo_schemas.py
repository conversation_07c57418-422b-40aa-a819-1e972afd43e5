"""
SEO Schemas

Pydantic models for SEO validation and serialization.
"""

import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional

from pydantic import BaseModel, Field, validator


class SEOMetaBase(BaseModel):
    """Base schema for SEO metadata."""
    content_type: str = Field(..., max_length=50, description="Type of content (e.g., 'product', 'blog_post')")
    content_id: uuid.UUID = Field(..., description="ID of the content")
    language_code: str = Field(..., max_length=10, description="Language code (e.g., 'en', 'pt-br')")
    
    # Basic SEO
    meta_title: Optional[str] = Field(None, max_length=200, description="SEO meta title")
    meta_description: Optional[str] = Field(None, description="SEO meta description")
    meta_keywords: Optional[str] = Field(None, description="SEO meta keywords (comma-separated)")
    canonical_url: Optional[str] = Field(None, max_length=500, description="Canonical URL")
    robots_directive: str = Field(default="index,follow", max_length=100, description="Robots directive")
    
    # Open Graph
    og_title: Optional[str] = Field(None, max_length=95, description="Open Graph title")
    og_description: Optional[str] = Field(None, max_length=300, description="Open Graph description")
    og_image_url: Optional[str] = Field(None, max_length=500, description="Open Graph image URL")
    og_image_alt: Optional[str] = Field(None, max_length=255, description="Open Graph image alt text")
    og_type: str = Field(default="website", max_length=50, description="Open Graph type")
    og_site_name: Optional[str] = Field(None, max_length=100, description="Open Graph site name")
    
    # Twitter Cards
    twitter_card_type: str = Field(default="summary_large_image", max_length=50, description="Twitter card type")
    twitter_title: Optional[str] = Field(None, max_length=70, description="Twitter title")
    twitter_description: Optional[str] = Field(None, max_length=200, description="Twitter description")
    twitter_image_url: Optional[str] = Field(None, max_length=500, description="Twitter image URL")
    twitter_image_alt: Optional[str] = Field(None, max_length=255, description="Twitter image alt text")
    twitter_creator: Optional[str] = Field(None, max_length=100, description="Twitter creator handle")
    twitter_site: Optional[str] = Field(None, max_length=100, description="Twitter site handle")
    
    # Structured Data
    structured_data: Optional[Dict[str, Any]] = Field(None, description="JSON-LD structured data")
    additional_meta_tags: Optional[Dict[str, str]] = Field(None, description="Additional meta tags")
    
    # SEO Analysis
    focus_keyword: Optional[str] = Field(None, max_length=100, description="Focus keyword for SEO")
    keyword_density: Optional[str] = Field(None, max_length=10, description="Keyword density percentage")
    readability_score: Optional[str] = Field(None, max_length=20, description="Readability score")
    seo_score: Optional[int] = Field(None, ge=0, le=100, description="SEO score (0-100)")
    
    # Status
    is_active: bool = Field(default=True, description="Is SEO metadata active")
    priority: int = Field(default=50, ge=0, le=100, description="Priority (0-100)")

    @validator('robots_directive')
    def validate_robots_directive(cls, v):
        allowed_directives = [
            'index,follow', 'index,nofollow', 'noindex,follow', 'noindex,nofollow',
            'index', 'noindex', 'follow', 'nofollow'
        ]
        if v not in allowed_directives:
            raise ValueError(f'Robots directive must be one of: {allowed_directives}')
        return v

    @validator('twitter_card_type')
    def validate_twitter_card_type(cls, v):
        allowed_types = ['summary', 'summary_large_image', 'app', 'player']
        if v not in allowed_types:
            raise ValueError(f'Twitter card type must be one of: {allowed_types}')
        return v


class SEOMetaCreate(SEOMetaBase):
    """Schema for creating SEO metadata."""
    pass


class SEOMetaUpdate(BaseModel):
    """Schema for updating SEO metadata."""
    meta_title: Optional[str] = Field(None, max_length=200)
    meta_description: Optional[str] = None
    meta_keywords: Optional[str] = None
    canonical_url: Optional[str] = Field(None, max_length=500)
    robots_directive: Optional[str] = Field(None, max_length=100)
    
    og_title: Optional[str] = Field(None, max_length=95)
    og_description: Optional[str] = Field(None, max_length=300)
    og_image_url: Optional[str] = Field(None, max_length=500)
    og_image_alt: Optional[str] = Field(None, max_length=255)
    og_type: Optional[str] = Field(None, max_length=50)
    og_site_name: Optional[str] = Field(None, max_length=100)
    
    twitter_card_type: Optional[str] = Field(None, max_length=50)
    twitter_title: Optional[str] = Field(None, max_length=70)
    twitter_description: Optional[str] = Field(None, max_length=200)
    twitter_image_url: Optional[str] = Field(None, max_length=500)
    twitter_image_alt: Optional[str] = Field(None, max_length=255)
    twitter_creator: Optional[str] = Field(None, max_length=100)
    twitter_site: Optional[str] = Field(None, max_length=100)
    
    structured_data: Optional[Dict[str, Any]] = None
    additional_meta_tags: Optional[Dict[str, str]] = None
    
    focus_keyword: Optional[str] = Field(None, max_length=100)
    keyword_density: Optional[str] = Field(None, max_length=10)
    readability_score: Optional[str] = Field(None, max_length=20)
    seo_score: Optional[int] = Field(None, ge=0, le=100)
    
    is_active: Optional[bool] = None
    priority: Optional[int] = Field(None, ge=0, le=100)


class SEOMetaResponse(SEOMetaBase):
    """Schema for SEO metadata response."""
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    created_by_id: Optional[uuid.UUID]
    updated_by_id: Optional[uuid.UUID]

    class Config:
        from_attributes = True


class URLSlugBase(BaseModel):
    """Base schema for URL slugs."""
    content_type: str = Field(..., max_length=50, description="Type of content")
    content_id: uuid.UUID = Field(..., description="ID of the content")
    language_code: str = Field(..., max_length=10, description="Language code")
    slug: str = Field(..., max_length=255, description="URL slug")
    full_path: Optional[str] = Field(None, max_length=500, description="Full URL path")
    is_primary: bool = Field(default=True, description="Is primary slug for this language")
    is_active: bool = Field(default=True, description="Is slug active")
    priority: int = Field(default=50, ge=0, le=100, description="Priority for sitemap")
    change_frequency: str = Field(default="weekly", description="Change frequency for sitemap")

    @validator('change_frequency')
    def validate_change_frequency(cls, v):
        valid_frequencies = ["always", "hourly", "daily", "weekly", "monthly", "yearly", "never"]
        if v not in valid_frequencies:
            raise ValueError(f'Change frequency must be one of: {valid_frequencies}')
        return v


class URLSlugCreate(URLSlugBase):
    """Schema for creating URL slugs."""
    pass


class URLSlugUpdate(BaseModel):
    """Schema for updating URL slugs."""
    slug: Optional[str] = Field(None, max_length=255)
    full_path: Optional[str] = Field(None, max_length=500)
    is_primary: Optional[bool] = None
    is_active: Optional[bool] = None
    priority: Optional[int] = Field(None, ge=0, le=100)
    change_frequency: Optional[str] = None


class URLSlugResponse(URLSlugBase):
    """Schema for URL slug response."""
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    last_accessed_at: Optional[datetime]
    created_by_id: Optional[uuid.UUID]

    class Config:
        from_attributes = True


class SEOAnalysisResponse(BaseModel):
    """Schema for SEO analysis results."""
    content_type: str
    content_id: uuid.UUID
    language_code: str
    
    # Analysis results
    seo_score: int = Field(..., ge=0, le=100)
    readability_score: int = Field(..., ge=0, le=100)
    
    # Recommendations
    recommendations: List[str] = []
    warnings: List[str] = []
    errors: List[str] = []
    
    # Metrics
    title_length: int
    description_length: int
    content_length: int
    keyword_density: float
    
    class Config:
        from_attributes = True


class MetaTagsResponse(BaseModel):
    """Schema for generated meta tags."""
    html_meta_tags: str
    json_ld: Optional[Dict[str, Any]]
    hreflang_tags: List[Dict[str, str]]
    
    class Config:
        from_attributes = True


class HreflangResponse(BaseModel):
    """Schema for hreflang data."""
    language_code: str
    url: str
    title: Optional[str]
    
    class Config:
        from_attributes = True
