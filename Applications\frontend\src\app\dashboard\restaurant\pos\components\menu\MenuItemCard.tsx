/**
 * Card de Item do Menu
 * Componente para exibir informações de um item do menu
 */

'use client';

import React from 'react';
import Image from 'next/image';
import { MenuItem } from '@/types/pos';
import { useCurrency } from '@/hooks/useCurrency';

interface MenuItemCardProps {
  item: MenuItem;
  onSelect: () => void;
  className?: string;
}

export const MenuItemCard: React.FC<MenuItemCardProps> = ({
  item,
  onSelect,
  className = '',
}) => {
  const { formatCurrency } = useCurrency();

  const formatPrepTime = (time?: number) => {
    if (!time) return null;
    return `${time} min`;
  };

  return (
    <button
      onClick={onSelect}
      className={`
        group relative bg-white rounded-lg border border-gray-200 
        hover:border-blue-300 hover:shadow-lg
        transition-all duration-200 transform hover:scale-105
        p-4 text-left w-full h-full
        focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
        ${!item.is_available ? 'opacity-50 cursor-not-allowed' : ''}
        ${className}
      `}
      disabled={!item.is_available}
    >
      {/* Imagem do item */}
      <div className="aspect-square bg-gray-100 rounded-lg mb-3 overflow-hidden relative">
        {item.image_url ? (
          <Image
            src={item.image_url}
            alt={item.name}
            fill
            className="object-cover group-hover:scale-110 transition-transform duration-200"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <svg
              className="w-8 h-8 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
          </div>
        )}
      </div>

      {/* Informações do item */}
      <div className="space-y-2">
        <h3 className="font-medium text-gray-900 text-sm line-clamp-2 leading-tight">
          {item.name}
        </h3>

        {item.description && (
          <p className="text-xs text-gray-500 line-clamp-2 leading-relaxed">
            {item.description}
          </p>
        )}

        <div className="flex items-center justify-between">
          <span className="text-blue-600 font-semibold text-sm">
            {formatCurrency(item.price)}
          </span>

          {item.preparation_time && (
            <span className="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded-full">
              {formatPrepTime(item.preparation_time)}
            </span>
          )}
        </div>

        {/* Indicadores adicionais */}
        <div className="flex items-center justify-between">
          {!item.is_available && (
            <span className="text-xs text-red-500 font-medium">
              Indisponível
            </span>
          )}

          {item.modifier_groups && item.modifier_groups.length > 0 && (
            <span className="text-xs text-blue-500 bg-blue-50 px-2 py-1 rounded-full">
              Personalizável
            </span>
          )}

          {item.variant_groups && item.variant_groups.length > 0 && (
            <span className="text-xs text-green-500 bg-green-50 px-2 py-1 rounded-full">
              Variações
            </span>
          )}
        </div>
      </div>

      {/* Botão de adição rápida */}
      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
        <div className="bg-blue-600 text-white rounded-full p-1.5 shadow-lg">
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 6v6m0 0v6m0-6h6m-6 0H6"
            />
          </svg>
        </div>
      </div>
    </button>
  );
};

export default MenuItemCard;