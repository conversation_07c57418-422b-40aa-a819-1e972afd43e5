"""
Review Metrics Schemas

Schemas para métricas e estatísticas de reviews.
"""

from datetime import datetime
from decimal import Decimal
from typing import Dict, Optional
from uuid import UUID

from pydantic import BaseModel, Field


class ProductReviewMetricsResponse(BaseModel):
    """Schema para métricas de reviews de um produto"""
    id: UUID
    product_id: UUID
    total_reviews: int
    average_rating: Decimal
    
    # Distribuição por estrelas
    rating_1_count: int
    rating_2_count: int
    rating_3_count: int
    rating_4_count: int
    rating_5_count: int
    
    # Métricas de qualidade
    verified_purchases_count: int
    total_helpful_votes: int
    total_not_helpful_votes: int
    
    # Métricas de moderação
    hidden_reviews_count: int
    reported_reviews_count: int
    
    # Timestamps
    last_updated: datetime
    created_at: datetime
    
    class Config:
        from_attributes = True
    
    @property
    def verified_purchase_percentage(self) -> float:
        """Percentual de reviews de compras verificadas"""
        if self.total_reviews == 0:
            return 0.0
        return (self.verified_purchases_count / self.total_reviews) * 100
    
    @property
    def helpfulness_ratio(self) -> float:
        """Proporção de votos úteis"""
        total_votes = self.total_helpful_votes + self.total_not_helpful_votes
        if total_votes == 0:
            return 0.0
        return self.total_helpful_votes / total_votes
    
    @property
    def quality_score(self) -> float:
        """Score de qualidade das reviews (0-100)"""
        if self.total_reviews == 0:
            return 0.0
        
        verification_factor = self.verified_purchase_percentage / 100
        helpfulness_factor = self.helpfulness_ratio
        moderation_factor = 1 - (self.hidden_reviews_count / self.total_reviews)
        
        quality = (verification_factor * 0.4 + 
                  helpfulness_factor * 0.4 + 
                  moderation_factor * 0.2)
        
        return round(quality * 100, 2)
    
    def get_rating_distribution(self) -> Dict[int, int]:
        """Retorna distribuição de ratings"""
        return {
            1: self.rating_1_count,
            2: self.rating_2_count,
            3: self.rating_3_count,
            4: self.rating_4_count,
            5: self.rating_5_count
        }


class ReviewerMetricsResponse(BaseModel):
    """Schema para métricas de um reviewer"""
    id: UUID
    user_id: UUID
    total_reviews: int
    verified_reviews: int
    average_rating_given: Decimal
    
    # Métricas de qualidade
    helpful_votes_received: int
    not_helpful_votes_received: int
    reviews_reported: int
    reviews_hidden: int
    
    # Ranking
    reviewer_rank: Optional[str]
    
    # Timestamps
    first_review_date: Optional[datetime]
    last_review_date: Optional[datetime]
    last_updated: datetime
    created_at: datetime
    
    class Config:
        from_attributes = True
    
    @property
    def verification_rate(self) -> float:
        """Taxa de reviews verificadas"""
        if self.total_reviews == 0:
            return 0.0
        return (self.verified_reviews / self.total_reviews) * 100
    
    @property
    def helpfulness_ratio(self) -> float:
        """Proporção de votos úteis recebidos"""
        total_votes = self.helpful_votes_received + self.not_helpful_votes_received
        if total_votes == 0:
            return 0.0
        return self.helpful_votes_received / total_votes
    
    @property
    def reputation_score(self) -> int:
        """Score de reputação do reviewer (0-1000)"""
        if self.total_reviews == 0:
            return 0
        
        activity_factor = min(self.total_reviews / 50, 1.0)
        verification_factor = self.verification_rate / 100
        helpfulness_factor = self.helpfulness_ratio
        quality_factor = 1 - (self.reviews_hidden / max(self.total_reviews, 1))
        
        reputation = (activity_factor * 0.3 + 
                     verification_factor * 0.3 + 
                     helpfulness_factor * 0.3 + 
                     quality_factor * 0.1)
        
        return int(reputation * 1000)


class ReviewDistributionResponse(BaseModel):
    """Schema para distribuição de reviews"""
    product_id: UUID
    rating_distribution: Dict[int, int] = Field(
        description="Distribuição por rating {1: count, 2: count, ...}"
    )
    total_reviews: int
    average_rating: Decimal
    
    # Distribuição por período
    reviews_last_30_days: int
    reviews_last_90_days: int
    reviews_last_year: int
    
    # Distribuição por tipo
    verified_purchases: int
    unverified_purchases: int
    
    class Config:
        from_attributes = True
    
    @property
    def verification_percentage(self) -> float:
        """Percentual de compras verificadas"""
        if self.total_reviews == 0:
            return 0.0
        return (self.verified_purchases / self.total_reviews) * 100


class ReviewTrendsResponse(BaseModel):
    """Schema para tendências de reviews"""
    product_id: UUID
    monthly_data: Dict[str, Dict] = Field(
        description="Dados mensais {YYYY-MM: {count, avg_rating, ...}}"
    )
    trend_direction: str = Field(
        description="Tendência: improving, declining, stable"
    )
    rating_trend: float = Field(
        description="Mudança na média de rating (últimos 3 meses)"
    )
    volume_trend: float = Field(
        description="Mudança no volume de reviews (últimos 3 meses)"
    )
    
    class Config:
        from_attributes = True


class ReviewComparisonResponse(BaseModel):
    """Schema para comparação de reviews entre produtos"""
    products: Dict[str, Dict] = Field(
        description="Dados por produto {product_id: {metrics}}"
    )
    comparison_metrics: Dict[str, float] = Field(
        description="Métricas comparativas"
    )
    
    class Config:
        from_attributes = True


class ReviewAnalyticsResponse(BaseModel):
    """Schema para analytics avançados de reviews"""
    product_id: UUID
    
    # Métricas básicas
    total_reviews: int
    average_rating: Decimal
    
    # Análise de sentimento (se implementado)
    sentiment_distribution: Optional[Dict[str, int]] = None
    
    # Palavras-chave mais mencionadas
    top_keywords: Optional[Dict[str, int]] = None
    
    # Análise de qualidade
    quality_indicators: Dict[str, float]
    
    # Recomendações
    recommendations: Dict[str, str]
    
    class Config:
        from_attributes = True