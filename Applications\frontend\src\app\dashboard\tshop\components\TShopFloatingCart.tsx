'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ShoppingCartIcon, BuildingOfficeIcon } from '@heroicons/react/24/outline';
import { useTShopCart } from '@/contexts/TShopCartContext';
import { TShopCartSidebar } from './TShopCartSidebar';
import { formatCurrency } from '@/lib/utils';

export function TShopFloatingCart() {
  const { cart } = useTShopCart();
  const [isCartOpen, setIsCartOpen] = useState(false);

  // Don't show if cart is empty
  if (!cart || cart.items.length === 0) {
    return null;
  }

  return (
    <>
      {/* Floating Cart Button */}
      <div className="fixed bottom-6 right-6 z-50">
        <Button
          onClick={() => setIsCartOpen(true)}
          className="relative h-14 w-14 rounded-full shadow-lg bg-indigo-600 hover:bg-indigo-700 transition-all duration-200 hover:scale-105"
          size="lg"
        >
          <ShoppingCartIcon className="h-6 w-6" />
          
          {/* Item Count Badge */}
          {cart.total_items > 0 && (
            <Badge 
              className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0 flex items-center justify-center bg-red-500 text-white text-xs font-bold animate-pulse"
            >
              {cart.total_items > 99 ? '99+' : cart.total_items}
            </Badge>
          )}
        </Button>

        {/* Cart Preview Tooltip */}
        <div className="absolute bottom-16 right-0 mb-2 opacity-0 hover:opacity-100 transition-opacity duration-200 pointer-events-none">
          <div className="bg-white rounded-lg shadow-lg border p-4 w-80 max-w-sm">
            {/* Header */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <BuildingOfficeIcon className="h-4 w-4 mr-2 text-indigo-600" />
                <span className="font-medium text-sm">Carrinho B2B</span>
              </div>
              <Badge variant="secondary" className="bg-indigo-100 text-indigo-800">
                {cart.total_items} itens
              </Badge>
            </div>

            {/* Quick Preview of Items */}
            <div className="space-y-2 mb-3 max-h-32 overflow-y-auto">
              {cart.items.slice(0, 3).map((item) => (
                <div key={item.id} className="flex items-center space-x-2 text-sm">
                  <div className="relative w-8 h-8 bg-gray-100 rounded flex-shrink-0">
                    {item.product_image ? (
                      <Image
                        src={item.product_image}
                        alt={item.product_name}
                        fill
                        className="object-cover rounded"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <BuildingOfficeIcon className="h-4 w-4 text-gray-400" />
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="truncate font-medium">{item.product_name}</p>
                    <p className="text-gray-500 text-xs">
                      {item.quantity}x {formatCurrency(item.unit_price)}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{formatCurrency(item.total_price)}</p>
                  </div>
                </div>
              ))}
              
              {cart.items.length > 3 && (
                <div className="text-center text-xs text-gray-500 py-1">
                  +{cart.items.length - 3} mais itens
                </div>
              )}
            </div>

            {/* Totals */}
            <div className="border-t pt-3 space-y-1">
              {cart.bulk_discount_amount > 0 && (
                <div className="flex justify-between text-xs text-green-600">
                  <span>Desconto em lote:</span>
                  <span>-{formatCurrency(cart.bulk_discount_amount)}</span>
                </div>
              )}
              
              {cart.discount_amount > 0 && (
                <div className="flex justify-between text-xs text-green-600">
                  <span>Desconto:</span>
                  <span>-{formatCurrency(cart.discount_amount)}</span>
                </div>
              )}
              
              <div className="flex justify-between font-bold text-sm">
                <span>Total:</span>
                <span className="text-indigo-600">{formatCurrency(cart.total_amount)}</span>
              </div>
            </div>

            {/* Warnings */}
            {cart.requires_approval && (
              <div className="mt-2 text-xs text-yellow-600 bg-yellow-50 rounded p-2">
                ⚠️ Requer aprovação
              </div>
            )}
            
            {!cart.credit_limit_check && (
              <div className="mt-2 text-xs text-red-600 bg-red-50 rounded p-2">
                ⚠️ Limite de crédito excedido
              </div>
            )}

            {/* Estimated Delivery */}
            {cart.estimated_delivery_date && (
              <div className="mt-2 text-xs text-blue-600 bg-blue-50 rounded p-2">
                📦 Entrega: {new Date(cart.estimated_delivery_date).toLocaleDateString('pt-BR')}
              </div>
            )}

            {/* Action Hint */}
            <div className="mt-3 text-center">
              <span className="text-xs text-gray-500">
                Clique no carrinho para ver detalhes
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Cart Sidebar */}
      <TShopCartSidebar 
        isOpen={isCartOpen} 
        onClose={() => setIsCartOpen(false)} 
      />
    </>
  );
}
