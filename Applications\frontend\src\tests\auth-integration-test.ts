/**
 * Teste de integração completo do sistema de autenticação
 * Valida fluxo completo: login, refresh token, logout, middleware
 */

import { authApi } from '@/lib/api/auth';
import Cookies from 'js-cookie';

export interface AuthTestResult {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

export class AuthIntegrationTest {
  /**
   * Testa login com usuário admin
   */
  static async testAdminLogin(): Promise<AuthTestResult> {
    try {
      console.log('🔍 Testing admin login...');
      
      const formData = new URLSearchParams();
      formData.append('email', '<EMAIL>');
      formData.append('password', 'password');

      const response = await authApi.login(formData);
      
      return {
        success: true,
        message: 'Admin login successful',
        data: {
          hasAccessToken: !!response.access_token,
          hasRefreshToken: !!response.refresh_token,
          tokenType: response.token_type
        }
      };
    } catch (error: any) {
      return {
        success: false,
        message: 'Admin login failed',
        error: error.message
      };
    }
  }

  /**
   * Testa login com usuário tenant owner
   */
  static async testTenantOwnerLogin(): Promise<AuthTestResult> {
    try {
      console.log('🔍 Testing tenant owner login...');
      
      const formData = new URLSearchParams();
      formData.append('email', '<EMAIL>');
      formData.append('password', 'password');

      const response = await authApi.login(formData);
      
      return {
        success: true,
        message: 'Tenant owner login successful',
        data: {
          hasAccessToken: !!response.access_token,
          hasRefreshToken: !!response.refresh_token,
          tokenType: response.token_type
        }
      };
    } catch (error: any) {
      return {
        success: false,
        message: 'Tenant owner login failed',
        error: error.message
      };
    }
  }

  /**
   * Testa obtenção de dados do usuário atual
   */
  static async testGetCurrentUser(): Promise<AuthTestResult> {
    try {
      console.log('🔍 Testing get current user...');
      
      // Primeiro fazer login para obter token
      const formData = new URLSearchParams();
      formData.append('email', '<EMAIL>');
      formData.append('password', 'password');

      const loginResponse = await authApi.login(formData);
      
      // Simular armazenamento do token (em teste real seria feito pelo AuthProvider)
      Cookies.set('access_token', loginResponse.access_token);
      
      const userResponse = await authApi.getCurrentUser();
      
      return {
        success: true,
        message: 'Get current user successful',
        data: {
          userId: userResponse.id,
          email: userResponse.email,
          systemRole: userResponse.system_role,
          isActive: userResponse.is_active
        }
      };
    } catch (error: any) {
      return {
        success: false,
        message: 'Get current user failed',
        error: error.message
      };
    }
  }

  /**
   * Testa obtenção de tenants do usuário
   */
  static async testGetUserTenants(): Promise<AuthTestResult> {
    try {
      console.log('🔍 Testing get user tenants...');
      
      // Login com usuário que tem tenant
      const formData = new URLSearchParams();
      formData.append('email', '<EMAIL>');
      formData.append('password', 'password');

      const loginResponse = await authApi.login(formData);
      Cookies.set('access_token', loginResponse.access_token);
      
      const tenantsResponse = await authApi.getUserTenants();
      
      return {
        success: true,
        message: 'Get user tenants successful',
        data: {
          tenantCount: tenantsResponse.length,
          tenants: tenantsResponse.map(tenant => ({
            id: tenant.id,
            name: tenant.name,
            role: 'user' // Default role since getUserTenants returns direct tenant objects
          }))
        }
      };
    } catch (error: any) {
      return {
        success: false,
        message: 'Get user tenants failed',
        error: error.message
      };
    }
  }

  /**
   * Testa refresh token
   */
  static async testRefreshToken(): Promise<AuthTestResult> {
    try {
      console.log('🔍 Testing refresh token...');
      
      // Primeiro fazer login para obter refresh token
      const formData = new URLSearchParams();
      formData.append('email', '<EMAIL>');
      formData.append('password', 'password');

      const loginResponse = await authApi.login(formData);
      
      if (!loginResponse.refresh_token) {
        throw new Error('No refresh token received');
      }
      
      const refreshResponse = await authApi.refresh(loginResponse.refresh_token);
      
      return {
        success: true,
        message: 'Refresh token successful',
        data: {
          hasNewAccessToken: !!refreshResponse.access_token,
          hasNewRefreshToken: !!refreshResponse.refresh_token,
          tokenType: refreshResponse.token_type
        }
      };
    } catch (error: any) {
      return {
        success: false,
        message: 'Refresh token failed',
        error: error.message
      };
    }
  }

  /**
   * Testa login com credenciais inválidas
   */
  static async testInvalidLogin(): Promise<AuthTestResult> {
    try {
      console.log('🔍 Testing invalid login...');
      
      const formData = new URLSearchParams();
      formData.append('email', '<EMAIL>');
      formData.append('password', 'wrongpassword');

      await authApi.login(formData);
      
      // Se chegou aqui, o teste falhou (deveria ter dado erro)
      return {
        success: false,
        message: 'Invalid login test failed - should have thrown error',
        error: 'Expected authentication error but login succeeded'
      };
    } catch (error: any) {
      // Erro esperado
      return {
        success: true,
        message: 'Invalid login correctly rejected',
        data: {
          errorMessage: error.message
        }
      };
    }
  }

  /**
   * Executa todos os testes de autenticação
   */
  static async runAllTests(): Promise<{
    adminLogin: AuthTestResult;
    tenantOwnerLogin: AuthTestResult;
    getCurrentUser: AuthTestResult;
    getUserTenants: AuthTestResult;
    refreshToken: AuthTestResult;
    invalidLogin: AuthTestResult;
    summary: {
      total: number;
      passed: number;
      failed: number;
    };
  }> {
    console.log('🚀 Starting Authentication Integration Tests...');
    
    const adminLogin = await this.testAdminLogin();
    const tenantOwnerLogin = await this.testTenantOwnerLogin();
    const getCurrentUser = await this.testGetCurrentUser();
    const getUserTenants = await this.testGetUserTenants();
    const refreshToken = await this.testRefreshToken();
    const invalidLogin = await this.testInvalidLogin();

    const results = [
      adminLogin,
      tenantOwnerLogin,
      getCurrentUser,
      getUserTenants,
      refreshToken,
      invalidLogin
    ];
    
    const passed = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;

    console.log('📊 Authentication Test Results Summary:');
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📈 Total: ${results.length}`);

    // Limpar cookies de teste
    Cookies.remove('access_token');
    Cookies.remove('refresh_token');
    Cookies.remove('tenant_id');

    return {
      adminLogin,
      tenantOwnerLogin,
      getCurrentUser,
      getUserTenants,
      refreshToken,
      invalidLogin,
      summary: {
        total: results.length,
        passed,
        failed
      }
    };
  }
}

// Função para executar testes via console do navegador
export const runAuthTests = () => {
  return AuthIntegrationTest.runAllTests();
};

// Exportar para uso global no navegador (desenvolvimento)
if (typeof window !== 'undefined') {
  (window as any).runAuthTests = runAuthTests;
  (window as any).AuthIntegrationTest = AuthIntegrationTest;
}
