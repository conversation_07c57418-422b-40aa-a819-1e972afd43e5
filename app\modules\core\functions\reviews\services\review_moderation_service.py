"""
Review Moderation Service

Serviço para moderação de reviews.
"""

from datetime import datetime
from typing import Dict, List, Optional, Tuple
from uuid import UUID

from sqlalchemy import and_, desc, func, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.modules.core.functions.reviews.models import (
    Review,
    ReviewStatus,
    ReviewReport,
    ReviewHelpfulness,
    ModerationAction,
    ReportReason,
    ReportStatus
)
from app.modules.core.functions.reviews.schemas import (
    ReviewReportCreate,
    ReviewReportResponse,
    ReviewHelpfulnessCreate,
    ReviewHelpfulnessResponse,
    ModerationActionCreate,
    ModerationActionResponse,
    ModerationQueueResponse,
    ModerationStatsResponse,
)


class ReviewModerationService:
    """Serviço para moderação de reviews"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def report_review(
        self,
        report_data: ReviewReportCreate,
        reporter_user_id: UUID,
        tenant_id: UUID
    ) -> ReviewReportResponse:
        """Reportar uma review"""
        
        # Verificar se a review existe
        review = await self._get_review(report_data.review_id, tenant_id)
        if not review:
            raise ValueError("Review not found")
        
        # Verificar se o usuário já reportou esta review
        existing_report = await self._get_user_review_report(
            reporter_user_id, report_data.review_id, tenant_id
        )
        if existing_report:
            raise ValueError("User already reported this review")
        
        # Criar report
        report = ReviewReport(
            review_id=report_data.review_id,
            reporter_user_id=reporter_user_id,
            tenant_id=tenant_id,
            reason=report_data.reason,
            description=report_data.description,
            status=ReportStatus.PENDING
        )
        
        self.db.add(report)
        await self.db.commit()
        await self.db.refresh(report)
        
        # Auto-moderação baseada em número de reports
        await self._check_auto_moderation(report_data.review_id, tenant_id)
        
        return ReviewReportResponse.from_orm(report)
    
    async def vote_helpfulness(
        self,
        vote_data: ReviewHelpfulnessCreate,
        voter_user_id: UUID,
        tenant_id: UUID
    ) -> ReviewHelpfulnessResponse:
        """Votar na utilidade de uma review"""
        
        # Verificar se a review existe
        review = await self._get_review(vote_data.review_id, tenant_id)
        if not review:
            raise ValueError("Review not found")
        
        # Verificar se o usuário já votou nesta review
        existing_vote = await self._get_user_helpfulness_vote(
            voter_user_id, vote_data.review_id, tenant_id
        )
        
        if existing_vote:
            # Atualizar voto existente
            existing_vote.is_helpful = vote_data.is_helpful
            existing_vote.updated_at = datetime.utcnow()
            await self.db.commit()
            return ReviewHelpfulnessResponse.from_orm(existing_vote)
        
        # Criar novo voto
        vote = ReviewHelpfulness(
            review_id=vote_data.review_id,
            user_id=voter_user_id,
            tenant_id=tenant_id,
            is_helpful=vote_data.is_helpful
        )
        
        self.db.add(vote)
        await self.db.commit()
        await self.db.refresh(vote)
        
        return ReviewHelpfulnessResponse.from_orm(vote)
    
    async def moderate_review(
        self,
        action_data: ModerationActionCreate,
        moderator_user_id: UUID,
        tenant_id: UUID
    ) -> ModerationActionResponse:
        """Executar ação de moderação em uma review"""
        
        # Verificar se a review existe
        review = await self._get_review(action_data.review_id, tenant_id)
        if not review:
            raise ValueError("Review not found")
        
        # Aplicar ação de moderação
        old_status = review.status
        review.status = action_data.new_status
        review.moderation_reason = action_data.reason
        review.moderated_at = datetime.utcnow()
        review.moderated_by = moderator_user_id
        
        # Criar registro da ação
        moderation_action = ModerationAction(
            review_id=action_data.review_id,
            moderator_user_id=moderator_user_id,
            tenant_id=tenant_id,
            action_type=action_data.action_type,
            old_status=old_status,
            new_status=action_data.new_status,
            reason=action_data.reason,
            notes=action_data.notes
        )
        
        self.db.add(moderation_action)
        
        # Atualizar status dos reports relacionados se aprovado/rejeitado
        if action_data.new_status in [ReviewStatus.HIDDEN_TEXT, ReviewStatus.HIDDEN_COMPLETE]:
            await self._update_related_reports(action_data.review_id, ReportStatus.APPROVED)
        elif action_data.new_status == ReviewStatus.ACTIVE:
            await self._update_related_reports(action_data.review_id, ReportStatus.REJECTED)
        
        await self.db.commit()
        await self.db.refresh(moderation_action)
        
        return ModerationActionResponse.from_orm(moderation_action)
    
    async def get_moderation_queue(
        self,
        tenant_id: UUID,
        # filters: ModerationFilterParams, # Temporarily removed
        page: int = 1,
        page_size: int = 20
    ) -> ModerationQueueResponse:
        """Obter fila de moderação"""
        
        # Query base para reviews que precisam de moderação
        query = select(Review).where(
            and_(
                Review.tenant_id == tenant_id,
                Review.status.in_([ReviewStatus.REPORTED, ReviewStatus.ACTIVE])
            )
        )
        
        # Aplicar filtros - Temporariamente desativado
        # if filters.status:
        #     query = query.where(Review.status == filters.status)
        
        # if filters.report_reason:
        #     # Filtrar por reviews que têm reports com motivo específico
        #     query = query.join(ReviewReport).where(
        #         ReviewReport.reason == filters.report_reason
        #     )
        
        # if filters.date_from:
        #     query = query.where(Review.created_at >= filters.date_from)
        
        # if filters.date_to:
        #     query = query.where(Review.created_at <= filters.date_to)
        
        # Ordenar por prioridade (mais reports primeiro)
        query = query.outerjoin(ReviewReport).group_by(Review.id).order_by(
            desc(func.count(ReviewReport.id)),
            desc(Review.created_at)
        )
        
        # Contar total
        count_query = select(func.count()).select_from(query.subquery())
        total_count = await self.db.scalar(count_query)
        
        # Aplicar paginação
        offset = (page - 1) * page_size
        query = query.offset(offset).limit(page_size)
        
        # Executar query com relacionamentos
        query = query.options(
            selectinload(Review.user),
            selectinload(Review.reports),
            selectinload(Review.moderation_actions)
        )
        
        result = await self.db.execute(query)
        reviews = result.scalars().all()
        
        # Calcular estatísticas da fila
        stats = await self._get_queue_stats(tenant_id)
        
        return ModerationQueueResponse(
            reviews=reviews,
            total_count=total_count,
            page=page,
            page_size=page_size,
            total_pages=(total_count + page_size - 1) // page_size,
            stats=stats
        )
    
    async def get_moderation_stats(
        self,
        tenant_id: UUID,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None
    ) -> ModerationStatsResponse:
        """Obter estatísticas de moderação"""
        
        # Query base
        base_query = select(func.count()).where(Review.tenant_id == tenant_id)
        
        if date_from:
            base_query = base_query.where(Review.created_at >= date_from)
        if date_to:
            base_query = base_query.where(Review.created_at <= date_to)
        
        # Total de reviews
        total_reviews = await self.db.scalar(base_query)
        
        # Reviews por status
        active_reviews = await self.db.scalar(
            base_query.where(Review.status == ReviewStatus.ACTIVE)
        )
        hidden_text_reviews = await self.db.scalar(
            base_query.where(Review.status == ReviewStatus.HIDDEN_TEXT)
        )
        hidden_complete_reviews = await self.db.scalar(
            base_query.where(Review.status == ReviewStatus.HIDDEN_COMPLETE)
        )
        reported_reviews = await self.db.scalar(
            base_query.where(Review.status == ReviewStatus.REPORTED)
        )
        
        # Reports por status
        reports_query = select(func.count()).select_from(ReviewReport).where(
            ReviewReport.tenant_id == tenant_id
        )
        if date_from:
            reports_query = reports_query.where(ReviewReport.created_at >= date_from)
        if date_to:
            reports_query = reports_query.where(ReviewReport.created_at <= date_to)
        
        total_reports = await self.db.scalar(reports_query)
        pending_reports = await self.db.scalar(
            reports_query.where(ReviewReport.status == ReportStatus.PENDING)
        )
        approved_reports = await self.db.scalar(
            reports_query.where(ReviewReport.status == ReportStatus.APPROVED)
        )
        rejected_reports = await self.db.scalar(
            reports_query.where(ReviewReport.status == ReportStatus.REJECTED)
        )
        
        # Ações de moderação
        actions_query = select(func.count()).select_from(ModerationAction).where(
            ModerationAction.tenant_id == tenant_id
        )
        if date_from:
            actions_query = actions_query.where(ModerationAction.created_at >= date_from)
        if date_to:
            actions_query = actions_query.where(ModerationAction.created_at <= date_to)
        
        total_moderation_actions = await self.db.scalar(actions_query)
        
        return ModerationStatsResponse(
            total_reviews=total_reviews or 0,
            active_reviews=active_reviews or 0,
            hidden_text_reviews=hidden_text_reviews or 0,
            hidden_complete_reviews=hidden_complete_reviews or 0,
            reported_reviews=reported_reviews or 0,
            total_reports=total_reports or 0,
            pending_reports=pending_reports or 0,
            approved_reports=approved_reports or 0,
            rejected_reports=rejected_reports or 0,
            total_moderation_actions=total_moderation_actions or 0,
            moderation_rate=(
                (total_moderation_actions / total_reviews * 100) 
                if total_reviews > 0 else 0.0
            )
        )
    
    # Métodos privados auxiliares
    
    async def _get_review(self, review_id: UUID, tenant_id: UUID) -> Optional[Review]:
        """Buscar review por ID"""
        query = select(Review).where(
            and_(
                Review.id == review_id,
                Review.tenant_id == tenant_id
            )
        )
        return await self.db.scalar(query)
    
    async def _get_user_review_report(
        self,
        user_id: UUID,
        review_id: UUID,
        tenant_id: UUID
    ) -> Optional[ReviewReport]:
        """Verificar se usuário já reportou a review"""
        query = select(ReviewReport).where(
            and_(
                ReviewReport.reporter_user_id == user_id,
                ReviewReport.review_id == review_id,
                ReviewReport.tenant_id == tenant_id
            )
        )
        return await self.db.scalar(query)
    
    async def _get_user_helpfulness_vote(
        self,
        user_id: UUID,
        review_id: UUID,
        tenant_id: UUID
    ) -> Optional[ReviewHelpfulness]:
        """Verificar se usuário já votou na review"""
        query = select(ReviewHelpfulness).where(
            and_(
                ReviewHelpfulness.user_id == user_id,
                ReviewHelpfulness.review_id == review_id,
                ReviewHelpfulness.tenant_id == tenant_id
            )
        )
        return await self.db.scalar(query)
    
    async def _check_auto_moderation(self, review_id: UUID, tenant_id: UUID):
        """Verificar se review deve ser auto-moderada"""
        
        # Contar reports pendentes para esta review
        reports_count = await self.db.scalar(
            select(func.count()).where(
                and_(
                    ReviewReport.review_id == review_id,
                    ReviewReport.tenant_id == tenant_id,
                    ReviewReport.status == ReportStatus.PENDING
                )
            )
        )
        
        # Auto-moderar se muitos reports (configurável)
        AUTO_MODERATE_THRESHOLD = 5
        
        if reports_count >= AUTO_MODERATE_THRESHOLD:
            # Marcar review como reportada para revisão manual
            await self.db.execute(
                update(Review)
                .where(
                    and_(
                        Review.id == review_id,
                        Review.tenant_id == tenant_id
                    )
                )
                .values(status=ReviewStatus.REPORTED)
            )
            await self.db.commit()
    
    async def _update_related_reports(
        self,
        review_id: UUID,
        new_status: ReportStatus
    ):
        """Atualizar status dos reports relacionados"""
        await self.db.execute(
            update(ReviewReport)
            .where(ReviewReport.review_id == review_id)
            .values(
                status=new_status,
                resolved_at=datetime.utcnow()
            )
        )
    
    async def _get_queue_stats(self, tenant_id: UUID) -> Dict:
        """Obter estatísticas da fila de moderação"""
        
        # Reviews aguardando moderação
        pending_count = await self.db.scalar(
            select(func.count()).where(
                and_(
                    Review.tenant_id == tenant_id,
                    Review.status == ReviewStatus.REPORTED
                )
            )
        )
        
        # Reports pendentes
        pending_reports = await self.db.scalar(
            select(func.count()).where(
                and_(
                    ReviewReport.tenant_id == tenant_id,
                    ReviewReport.status == ReportStatus.PENDING
                )
            )
        )
        
        return {
            "pending_reviews": pending_count or 0,
            "pending_reports": pending_reports or 0,
            "priority_reviews": 0  # TODO: Implementar lógica de prioridade
        }