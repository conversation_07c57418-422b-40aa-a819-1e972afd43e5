# General - Public Tenant Api

**Categoria:** General
**Módulo:** Public Tenant Api
**Total de Endpoints:** 1
**Gerado em:** 27/06/2025, 20:20:27

## 📋 Endpoints

- [GET /api/public/tenants/{tenant_slug}/currency](#get-apipublictenantstenant-slugcurrency) - Get Public Tenant Currency

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### PublicCurrencyResponse

**Descrição:** Response schema for public currency information.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `currency` | string | ✅ | - |
| `currency_format` | object | ✅ | - |

## 🔗 Endpoints Detalhados

### GET /api/public/tenants/{tenant_slug}/currency {#get-apipublictenantstenant-slugcurrency}

**Resumo:** Get Public Tenant Currency
**Descrição:** Get public currency configuration for a tenant.
Public endpoint - no authentication required.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_slug` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PublicCurrencyResponse](#publiccurrencyresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/public/tenants/{tenant_slug}/currency"
```

---
