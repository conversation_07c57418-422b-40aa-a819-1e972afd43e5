"""Schemas for HR module."""

from .employee import (  # noqa: E402
    EmployeeBase,
    EmployeeCreate,
    EmployeeUpdate,
    EmployeeRead,
    EmployeeReadWithDetails,
)
from .time_tracking import (  # noqa: E402
    TimeRecordBase,
    TimeRecordCreate,
    TimeRecordUpdate,
    TimeRecordRead,
    OvertimeRecordBase,
    OvertimeRecordCreate,
    OvertimeRecordUpdate,
    OvertimeRecordRead,
    TimeBankBase,
    TimeBankCreate,
    TimeBankUpdate,
    TimeBankRead,
    TimeBankTransactionBase,
    TimeBankTransactionCreate,
    TimeBankTransactionRead,
    ClockInRequest,
    ClockOutRequest,
    TimeCardSummary,
    GenerateTimeMirrorRequest,
    TimeMirrorBase,
    TimeMirrorCreate,
    TimeMirrorUpdate,
    TimeMirrorRead,
    TimeMirrorWithRecords,
    OvertimeRuleBase,
    OvertimeRuleCreate,
    OvertimeRuleUpdate,
    OvertimeRuleRead,
    PayrollIntegrationRequest,
)
from .work_schedule import (  # noqa: E402
    ScheduleTemplateBase,
    ScheduleTemplateCreate,
    ScheduleTemplateUpdate,
    ScheduleTemplateRead,
    ScheduleTemplateWithEntries,
    ScheduleTemplateEntryBase,
    ScheduleTemplateEntryCreate,
    ScheduleTemplateEntryUpdate,
    ScheduleTemplateEntryRead,
    WorkScheduleBase,
    WorkScheduleCreate,
    WorkScheduleUpdate,
    WorkScheduleRead,
    WorkScheduleWithShifts,
    WorkShiftBase,
    WorkShiftCreate,
    WorkShiftUpdate,
    WorkShiftRead,
    LeaveRequestBase,
    LeaveRequestCreate,
    LeaveRequestUpdate,
    LeaveRequestRead,
    GenerateScheduleRequest,
    LeaveBalanceResponse,
)
from .document_management import (  # noqa: E402
    DocumentBase,
    DocumentCreate,
    DocumentUpdate,
    DocumentRead,
    DocumentAccessBase,
    DocumentAccessCreate,
    DocumentAccessRead,
    DocumentSignatureBase,
    DocumentSignatureCreate,
    DocumentSignatureRead,
    DocumentTemplateBase,
    DocumentTemplateCreate,
    DocumentTemplateUpdate,
    DocumentTemplateRead,
    GenerateDocumentRequest,
)
from .recruitment import (  # noqa: E402
    JobBase,
    JobCreate,
    JobUpdate,
    JobRead,
    CandidateBase,
    CandidateCreate,
    CandidateUpdate,
    CandidateRead,
    InterviewBase,
    InterviewCreate,
    InterviewUpdate,
    InterviewRead,
    AssessmentBase,
    AssessmentCreate,
    AssessmentUpdate,
    AssessmentRead,
    JobApplicationCreate,
)

__all__ = [
    # Employee
    "EmployeeBase",
    "EmployeeCreate",
    "EmployeeUpdate",
    "EmployeeRead",
    "EmployeeReadWithDetails",
    # Time Tracking
    "TimeRecordBase",
    "TimeRecordCreate",
    "TimeRecordUpdate",
    "TimeRecordRead",
    "OvertimeRecordBase",
    "OvertimeRecordCreate",
    "OvertimeRecordUpdate",
    "OvertimeRecordRead",
    "TimeBankBase",
    "TimeBankCreate",
    "TimeBankUpdate",
    "TimeBankRead",
    "TimeBankTransactionBase",
    "TimeBankTransactionCreate",
    "TimeBankTransactionRead",
    "ClockInRequest",
    "ClockOutRequest",
    "TimeCardSummary",
    "GenerateTimeMirrorRequest",
    "TimeMirrorBase",
    "TimeMirrorCreate",
    "TimeMirrorUpdate",
    "TimeMirrorRead",
    "TimeMirrorWithRecords",
    "OvertimeRuleBase",
    "OvertimeRuleCreate",
    "OvertimeRuleUpdate",
    "OvertimeRuleRead",
    "PayrollIntegrationRequest",
    # Work Schedule
    "ScheduleTemplateBase",
    "ScheduleTemplateCreate",
    "ScheduleTemplateUpdate",
    "ScheduleTemplateRead",
    "ScheduleTemplateWithEntries",
    "ScheduleTemplateEntryBase",
    "ScheduleTemplateEntryCreate",
    "ScheduleTemplateEntryUpdate",
    "ScheduleTemplateEntryRead",
    "WorkScheduleBase",
    "WorkScheduleCreate",
    "WorkScheduleUpdate",
    "WorkScheduleRead",
    "WorkScheduleWithShifts",
    "WorkShiftBase",
    "WorkShiftCreate",
    "WorkShiftUpdate",
    "WorkShiftRead",
    "LeaveRequestBase",
    "LeaveRequestCreate",
    "LeaveRequestUpdate",
    "LeaveRequestRead",
    "GenerateScheduleRequest",
    "LeaveBalanceResponse",
    # Document Management
    "DocumentBase",
    "DocumentCreate",
    "DocumentUpdate",
    "DocumentRead",
    "DocumentAccessBase",
    "DocumentAccessCreate",
    "DocumentAccessRead",
    "DocumentSignatureBase",
    "DocumentSignatureCreate",
    "DocumentSignatureRead",
    "DocumentTemplateBase",
    "DocumentTemplateCreate",
    "DocumentTemplateUpdate",
    "DocumentTemplateRead",
    "GenerateDocumentRequest",
    # Recruitment
    "JobBase",
    "JobCreate",
    "JobUpdate",
    "JobRead",
    "CandidateBase",
    "CandidateCreate",
    "CandidateUpdate",
    "CandidateRead",
    "InterviewBase",
    "InterviewCreate",
    "InterviewUpdate",
    "InterviewRead",
    "AssessmentBase",
    "AssessmentCreate",
    "AssessmentUpdate",
    "AssessmentRead",
    "JobApplicationCreate",
]
