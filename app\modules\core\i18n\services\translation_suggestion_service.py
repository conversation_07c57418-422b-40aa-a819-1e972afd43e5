from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import delete, and_
from sqlalchemy.orm import joinedload
from uuid import UUID

from app.modules.core.i18n.models import (
    TranslationSuggestion,
    Translation,
    Language,
    TranslationKey,
)
from app.modules.core.i18n.models.translation_suggestion import TranslationSuggestionStatus
from app.modules.core.i18n import schemas
from app.modules.core.i18n.services.translation_service import TranslationService


class TranslationSuggestionService:
    async def create_suggestion(
        self,
        db: AsyncSession,
        suggestion_in: schemas.TranslationSuggestionCreate,
        suggested_by_id: UUID,
    ) -> TranslationSuggestion:
        """Create a new translation suggestion."""
        # Get the current translation text if it exists
        translation_result = await db.execute(
            select(Translation).filter(
                and_(
                    Translation.key_id == suggestion_in.key_id,
                    Translation.language_id == suggestion_in.language_id,
                )
            )
        )
        current_translation = translation_result.scalars().first()

        db_suggestion = TranslationSuggestion(
            suggested_text=suggestion_in.suggested_text,
            current_text_on_suggestion=(
                current_translation.translated_text if current_translation else None
            ),
            status=TranslationSuggestionStatus.PENDING,
            key_id=suggestion_in.key_id,
            language_id=suggestion_in.language_id,
            user_id=suggested_by_id,
        )
        db.add(db_suggestion)
        await db.flush()
        await db.refresh(db_suggestion)
        return db_suggestion

    async def create_suggestion_from_client(
        self,
        db: AsyncSession,
        suggestion_in: schemas.TranslationSuggestionCreate,
        suggested_by_id: UUID,
    ) -> TranslationSuggestion:
        """Create a new translation suggestion from client-side data."""
        return await self.create_suggestion(db, suggestion_in, suggested_by_id)

    async def get_suggestion(
        self, db: AsyncSession, suggestion_id: int
    ) -> Optional[TranslationSuggestion]:
        """Get a suggestion by ID."""
        result = await db.execute(
            select(TranslationSuggestion)
            .filter(TranslationSuggestion.id == suggestion_id)
            .options(
                joinedload(TranslationSuggestion.translation_key),
                joinedload(TranslationSuggestion.language),
                joinedload(TranslationSuggestion.user),
            )
        )
        return result.scalars().first()

    async def get_suggestions(
        self,
        db: AsyncSession,
        status: Optional[TranslationSuggestionStatus] = None,
        lang_code: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[TranslationSuggestion]:
        """Get suggestions with filters and pagination."""
        query = select(TranslationSuggestion)

        # Apply status filter if provided
        if status:
            query = query.filter(TranslationSuggestion.status == status)

        # Apply language filter if provided
        if lang_code:
            lang_result = await db.execute(select(Language).filter(Language.code == lang_code))
            language = lang_result.scalars().first()
            if language:
                query = query.filter(TranslationSuggestion.language_id == language.id)

        # Apply pagination
        query = query.offset(skip).limit(limit)

        # Load related entities
        query = query.options(
            joinedload(TranslationSuggestion.translation_key),
            joinedload(TranslationSuggestion.language),
            joinedload(TranslationSuggestion.user),
        )

        result = await db.execute(query)
        return result.scalars().all()

    async def approve_suggestion(
        self, db: AsyncSession, suggestion_id: int, approver_id: UUID
    ) -> Optional[TranslationSuggestion]:
        """Approve a suggestion and update the translation."""
        db_suggestion = await self.get_suggestion(db, suggestion_id)
        if not db_suggestion or db_suggestion.status != TranslationSuggestionStatus.PENDING:
            return None

        # Update the suggestion status
        db_suggestion.status = TranslationSuggestionStatus.APPROVED
        db_suggestion.reviewed_at = datetime.now()

        # Update or create the translation
        translation_service = TranslationService()
        translation_in = schemas.TranslationCreate(
            translated_text=db_suggestion.suggested_text,
            is_approved=True,
            key_id=db_suggestion.key_id,
            language_id=db_suggestion.language_id,
            last_updated_by_user_id=approver_id,
        )
        await translation_service.create_translation(db, translation_in)

        await db.flush()
        await db.refresh(db_suggestion)
        return db_suggestion

    async def reject_suggestion(
        self, db: AsyncSession, suggestion_id: int, reviewer_id: UUID
    ) -> Optional[TranslationSuggestion]:
        """Reject a suggestion."""
        db_suggestion = await self.get_suggestion(db, suggestion_id)
        if not db_suggestion or db_suggestion.status != TranslationSuggestionStatus.PENDING:
            return None

        # Update the suggestion status
        db_suggestion.status = TranslationSuggestionStatus.REJECTED
        db_suggestion.reviewed_at = datetime.now()

        await db.flush()
        await db.refresh(db_suggestion)
        return db_suggestion

    async def mark_as_manually_implemented(
        self, db: AsyncSession, suggestion_id: int, reviewer_id: UUID
    ) -> Optional[TranslationSuggestion]:
        """Mark a suggestion as manually implemented."""
        db_suggestion = await self.get_suggestion(db, suggestion_id)
        if not db_suggestion or db_suggestion.status != TranslationSuggestionStatus.PENDING:
            return None

        # Update the suggestion status
        db_suggestion.status = TranslationSuggestionStatus.IMPLEMENTED_MANUALLY
        db_suggestion.reviewed_at = datetime.now()

        await db.flush()
        await db.refresh(db_suggestion)
        return db_suggestion
