import 'package:hive/hive.dart';
import 'menu_item_model.dart';

part 'menu_model.g.dart';

@HiveType(typeId: 5)
class MenuModel extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String name;

  @HiveField(2)
  String description;

  @HiveField(3)
  List<MenuCategory> categories;

  @HiveField(4)
  bool isActive;

  @HiveField(5)
  DateTime createdAt;

  @HiveField(6)
  DateTime? updatedAt;

  MenuModel({
    required this.id,
    required this.name,
    required this.description,
    required this.categories,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
  });

  factory MenuModel.fromJson(Map<String, dynamic> json) {
    return MenuModel(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      categories: (json['categories'] as List)
          .map((category) => MenuCategory.fromJson(category))
          .toList(),
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'categories': categories.map((category) => category.toJson()).toList(),
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  List<MenuItemModel> get allItems {
    return categories
        .expand((category) => category.items)
        .toList();
  }

  List<MenuItemModel> getItemsByCategory(String categoryId) {
    final category = categories.firstWhere(
      (cat) => cat.id == categoryId,
      orElse: () => throw Exception('Category not found'),
    );
    return category.items;
  }
}

@HiveType(typeId: 6)
class MenuCategory extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String name;

  @HiveField(2)
  String description;

  @HiveField(3)
  List<MenuItemModel> items;

  @HiveField(4)
  int sortOrder;

  @HiveField(5)
  bool isActive;

  MenuCategory({
    required this.id,
    required this.name,
    required this.description,
    required this.items,
    this.sortOrder = 0,
    this.isActive = true,
  });

  factory MenuCategory.fromJson(Map<String, dynamic> json) {
    return MenuCategory(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      items: (json['items'] as List)
          .map((item) => MenuItemModel.fromJson(item))
          .toList(),
      sortOrder: json['sortOrder'] ?? 0,
      isActive: json['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'items': items.map((item) => item.toJson()).toList(),
      'sortOrder': sortOrder,
      'isActive': isActive,
    };
  }
}