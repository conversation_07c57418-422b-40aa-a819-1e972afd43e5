import uuid  # Import uuid
from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field
from typing import Optional, List

# Forward references might be needed for relationships, handled later if necessary
# from .menu_item import MenuItemReadSimple # Example if needed


class MenuCategoryBase(BaseModel):
    """Base schema for Menu Category, shared fields."""

    name: str = Field(
        ..., max_length=100, description="Name of the menu category (e.g., Appetizers)"
    )
    description: Optional[str] = Field(None, description="Optional description for the category")
    display_order: int = Field(0, description="Order in which the category should be displayed")
    is_active: bool = Field(True, description="Whether the category is currently active")
    is_default: Optional[bool] = Field(False, description="Whether this is the default 'Sem Categoria' category")
    parent_id: Optional[uuid.UUID] = Field(
        None, description="ID of the parent category, if this is a subcategory"
    )
    digital_menu_id: Optional[uuid.UUID] = Field(
        None, description="ID of the digital menu this category belongs to"
    )


class MenuCategoryCreate(MenuCategoryBase):
    """Schema for creating a new Menu Category."""

    # tenant_id will be added by the service based on the authenticated user


class MenuCategoryUpdate(MenuCategoryBase):
    """Schema for updating an existing Menu Category. All fields are optional."""

    name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = None
    display_order: Optional[int] = None
    is_active: Optional[bool] = None
    is_default: Optional[bool] = None
    parent_id: Optional[uuid.UUID] = Field(
        None,
        description="ID of the parent category to move this category under. Use null to make it a top-level category.",  # noqa: E501
    )
    digital_menu_id: Optional[uuid.UUID] = Field(
        None, description="ID of the digital menu to assign this category to"
    )


class MenuCategoryRead(MenuCategoryBase):
    """Schema for reading a Menu Category, includes the ID and optional relationships."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    # Tornar relacionamentos opcionais para evitar MissingGreenlet error
    menu_items: Optional[List["MenuItemRead"]] = None
    children: Optional[List["MenuCategoryRead"]] = None

    model_config = ConfigDict(from_attributes=True)


class MenuCategoryReadDetailed(MenuCategoryBase):
    """Schema for reading a Menu Category with guaranteed relationships loaded."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    menu_items: List["MenuItemRead"] = []  # Include menu items with full details
    children: List["MenuCategoryReadDetailed"] = []  # List of child categories

    model_config = ConfigDict(from_attributes=True)


# Simplified version to avoid greenlet issues
class MenuCategoryReadSimple(BaseModel):
    """Simplified schema for Menu Category without children to avoid lazy loading issues."""
    id: uuid.UUID
    name: str
    description: Optional[str] = None
    display_order: int = 0
    is_active: bool = True
    is_default: Optional[bool] = False
    parent_id: Optional[uuid.UUID] = None
    digital_menu_id: Optional[uuid.UUID] = None
    tenant_id: uuid.UUID
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Public API version without redundant category object in menu items
class MenuCategoryReadPublic(BaseModel):
    """Schema for reading a Menu Category in public API without redundant data."""
    id: uuid.UUID
    name: str
    description: Optional[str] = None
    display_order: int = 0
    is_active: bool = True
    is_default: Optional[bool] = False
    parent_id: Optional[uuid.UUID] = None
    digital_menu_id: Optional[uuid.UUID] = None
    tenant_id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    menu_items: List["MenuItemReadPublic"] = []  # Use public schema without category object
    children: List["MenuCategoryReadPublic"] = []  # Recursive for subcategories

    model_config = ConfigDict(from_attributes=True)


# Resolve forward references for MenuItemRead and recursive relationships
from .menu_item import MenuItemRead, MenuItemReadPublic  # noqa: E402
MenuCategoryRead.model_rebuild()
MenuCategoryReadDetailed.model_rebuild()
MenuCategoryReadPublic.model_rebuild()
