'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { 
  BellIcon, 
  PlusIcon, 
  FunnelIcon,
  EyeIcon,
  TrashIcon,
  CheckIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { BellIcon as BellSolidIcon } from '@heroicons/react/24/solid';

import { useAuth } from '@/lib/auth/AuthProvider';
import { apiClient } from '@/lib/api/client';
import NotificationsList from './components/NotificationsList';
import NotificationModal from './components/NotificationModal';
import NotificationFilters from './components/NotificationFilters';
import NotificationMetrics from './components/NotificationMetrics';

interface Notification {
  id: string;
  title: string;
  content: string;
  image_url?: string;
  action_url?: string;
  sender_id: string;
  sender_type: string;
  target_type: string;
  target_id?: string;
  tenant_id?: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  status: string;
  view_count: number;
  click_count: number;
  delivery_count: number;
  created_at: string;
  updated_at?: string;
  expires_at?: string;
  sent_at?: string;
  is_expired: boolean;
  is_read?: boolean;
  is_deleted?: boolean;
}

interface NotificationFiltersType {
  status?: string;
  priority?: string;
  is_read?: boolean;
  search?: string;
}

export default function NotificationsPage() {
  const { user, isAdmin, isTenantOwner, isTenantStaff } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [showMetrics, setShowMetrics] = useState(false);

  // Função para verificar se o usuário pode criar notificações
  const canCreateNotifications = () => {
    return isAdmin() || isTenantOwner() || isTenantStaff();
  };

  // Função para verificar se o usuário pode ver métricas
  const canViewMetrics = () => {
    return isAdmin() || isTenantOwner();
  };
  const [filters, setFilters] = useState<NotificationFiltersType>({});
  const [pagination, setPagination] = useState({
    page: 1,
    per_page: 20,
    total: 0,
    has_next: false,
    has_prev: false
  });

  // Carrega notificações
  const loadNotifications = useCallback(async (page = 1, currentFilters = filters) => {
    try {
      setLoading(true);
      const filteredParams = Object.fromEntries(
        Object.entries({
          page: page.toString(),
          per_page: pagination.per_page.toString(),
          ...currentFilters
        })
        .filter(([_, value]) => value !== undefined && value !== null && value !== '')
        .map(([key, value]) => [key, String(value)])
      );
      const params = new URLSearchParams(filteredParams);

      const response = await apiClient.get(`/modules/core/notifications/?${params}`);

      setNotifications(response.data.notifications);
      setPagination({
        page: response.data.page,
        per_page: response.data.per_page,
        total: response.data.total,
        has_next: response.data.has_next,
        has_prev: response.data.has_prev
      });
    } catch (err) {
      console.error('Erro ao carregar notificações:', err);
      setError('Erro ao carregar notificações');
    } finally {
      setLoading(false);
    }
  }, [filters, pagination.per_page]);

  // Carrega notificações na inicialização
  useEffect(() => {
    loadNotifications();
  }, [loadNotifications]);

  // Marca notificações como lidas
  const markAsRead = async (notificationIds: string[]) => {
    try {
      await apiClient.post('/modules/core/notifications/mark-read', {
        notification_ids: notificationIds
      });
      
      // Atualiza estado local
      setNotifications(prev => 
        prev.map(notification => 
          notificationIds.includes(notification.id)
            ? { ...notification, is_read: true }
            : notification
        )
      );
      
      setSelectedNotifications([]);
    } catch (err) {
      console.error('Erro ao marcar como lidas:', err);
      setError('Erro ao marcar notificações como lidas');
    }
  };

  // Deleta notificações
  const deleteNotifications = async (notificationIds: string[], deleteForAll = false) => {
    try {
      await apiClient.post('/modules/core/notifications/bulk-action', {
        notification_ids: notificationIds,
        action: 'delete',
        delete_for_all: deleteForAll
      });
      
      // Remove do estado local
      setNotifications(prev => 
        prev.filter(notification => !notificationIds.includes(notification.id))
      );
      
      setSelectedNotifications([]);
    } catch (err) {
      console.error('Erro ao deletar notificações:', err);
      setError('Erro ao deletar notificações');
    }
  };

  // Aplica filtros
  const applyFilters = (newFilters: NotificationFiltersType) => {
    setFilters(newFilters);
    loadNotifications(1, newFilters);
  };

  // Limpa filtros
  const clearFilters = () => {
    setFilters({});
    loadNotifications(1, {});
  };

  // Seleciona/deseleciona todas as notificações
  const toggleSelectAll = () => {
    if (selectedNotifications.length === notifications.length) {
      setSelectedNotifications([]);
    } else {
      setSelectedNotifications(notifications.map(n => n.id));
    }
  };

  // Conta notificações não lidas
  const unreadCount = notifications.filter(n => !n.is_read).length;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <BellSolidIcon className="h-8 w-8 text-blue-600" />
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Notificações</h1>
                <p className="text-gray-600">
                  {unreadCount > 0 ? `${unreadCount} não lidas` : 'Todas as notificações lidas'}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              {/* Botão de Métricas (apenas para admin/owner) */}
              {canViewMetrics() && (
                <button
                  onClick={() => setShowMetrics(!showMetrics)}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <EyeIcon className="h-4 w-4 mr-2" />
                  Métricas
                </button>
              )}
              
              {/* Botão de Filtros */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <FunnelIcon className="h-4 w-4 mr-2" />
                Filtros
              </button>
              
              {/* Botão de Criar (admin/owner/staff) */}
              {canCreateNotifications() && (
                <button
                  onClick={() => setShowCreateModal(true)}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Nova Notificação
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Métricas */}
        {showMetrics && (
          <div className="mb-6">
            <NotificationMetrics />
          </div>
        )}

        {/* Filtros */}
        {showFilters && (
          <div className="mb-6">
            <NotificationFilters
              filters={filters}
              onApplyFilters={applyFilters}
              onClearFilters={clearFilters}
            />
          </div>
        )}

        {/* Ações em lote */}
        {selectedNotifications.length > 0 && (
          <div className="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <span className="text-sm font-medium text-blue-900">
                  {selectedNotifications.length} notificação(ões) selecionada(s)
                </span>
              </div>
              
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => markAsRead(selectedNotifications)}
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200"
                >
                  <CheckIcon className="h-4 w-4 mr-1" />
                  Marcar como Lidas
                </button>
                
                <button
                  onClick={() => deleteNotifications(selectedNotifications)}
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200"
                >
                  <TrashIcon className="h-4 w-4 mr-1" />
                  Deletar
                </button>
                
                {canViewMetrics() && (
                  <button
                    onClick={() => deleteNotifications(selectedNotifications, true)}
                    className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200"
                  >
                    <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                    Deletar para Todos
                  </button>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Lista de Notificações */}
        <div className="bg-white shadow rounded-lg">
          {error && (
            <div className="p-4 bg-red-50 border-l-4 border-red-400">
              <div className="flex">
                <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                <div className="ml-3">
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              </div>
            </div>
          )}
          
          <NotificationsList
            notifications={notifications}
            loading={loading}
            selectedNotifications={selectedNotifications}
            onSelectionChange={setSelectedNotifications}
            onToggleSelectAll={toggleSelectAll}
            onMarkAsRead={markAsRead}
            onDelete={deleteNotifications}
            onRefresh={() => loadNotifications(pagination.page)}
          />
          
          {/* Paginação */}
          {pagination.total > pagination.per_page && (
            <div className="px-6 py-3 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-700">
                  Mostrando {((pagination.page - 1) * pagination.per_page) + 1} a{' '}
                  {Math.min(pagination.page * pagination.per_page, pagination.total)} de{' '}
                  {pagination.total} notificações
                </div>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => loadNotifications(pagination.page - 1)}
                    disabled={!pagination.has_prev}
                    className="px-3 py-1 text-sm border rounded disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Anterior
                  </button>
                  
                  <span className="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded">
                    {pagination.page}
                  </span>
                  
                  <button
                    onClick={() => loadNotifications(pagination.page + 1)}
                    disabled={!pagination.has_next}
                    className="px-3 py-1 text-sm border rounded disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Próxima
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Modal de Criação */}
      {showCreateModal && (
        <NotificationModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSuccess={() => {
            setShowCreateModal(false);
            loadNotifications();
          }}
        />
      )}
    </div>
  );
}
