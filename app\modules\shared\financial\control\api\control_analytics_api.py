"""Financial Control Analytics API Endpoints."""

from typing import Optional
from uuid import UUID
from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.tenants.dependencies.tenant_dependencies import get_current_tenant
from app.modules.core.tenants.models.tenant import Tenant

from ..services.control_analytics_service import ControlAnalyticsService
from ..schemas.control_analytics_schemas import (
    ControlMetrics, DashboardData, AnalyticsFilters,
    CategoryBreakdown, CashFlowData, TrendData, BudgetAnalysis
)

router = APIRouter(prefix="/control/analytics", tags=["Financial Control Analytics"])


@router.get("/dashboard", response_model=DashboardData)
async def get_dashboard_data(
    tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db),
    # Filter parameters
    date_from: Optional[str] = Query(None),
    date_to: Optional[str] = Query(None),
    period_type: Optional[str] = Query("month"),
    category_ids: Optional[str] = Query(None),  # Comma-separated UUIDs
    entry_types: Optional[str] = Query(None),   # Comma-separated types
    include_archived: bool = Query(False)
):
    """Get comprehensive dashboard data with analytics."""
    
    # Parse filters
    filters = None
    if any([date_from, date_to, category_ids, entry_types]):
        # Parse category_ids
        parsed_category_ids = None
        if category_ids:
            try:
                parsed_category_ids = [UUID(id.strip()) for id in category_ids.split(",")]
            except ValueError:
                parsed_category_ids = None
        
        # Parse entry_types
        parsed_entry_types = None
        if entry_types:
            parsed_entry_types = [t.strip() for t in entry_types.split(",")]
        
        # Parse dates
        parsed_date_from = None
        parsed_date_to = None
        if date_from:
            from datetime import datetime
            parsed_date_from = datetime.strptime(date_from, "%Y-%m-%d").date()
        if date_to:
            from datetime import datetime
            parsed_date_to = datetime.strptime(date_to, "%Y-%m-%d").date()
        
        filters = AnalyticsFilters(
            date_from=parsed_date_from,
            date_to=parsed_date_to,
            period_type=period_type,
            category_ids=parsed_category_ids,
            entry_types=parsed_entry_types,
            include_archived=include_archived
        )
    
    service = ControlAnalyticsService(db)
    return await service.get_dashboard_data(tenant.id, filters)


@router.get("/metrics", response_model=ControlMetrics)
async def get_control_metrics(
    tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db),
    # Filter parameters
    date_from: Optional[str] = Query(None),
    date_to: Optional[str] = Query(None),
    period_type: Optional[str] = Query("month"),
    category_ids: Optional[str] = Query(None),
    include_archived: bool = Query(False)
):
    """Get financial control metrics."""
    
    # Parse filters (similar to dashboard endpoint)
    filters = None
    if any([date_from, date_to, category_ids]):
        parsed_category_ids = None
        if category_ids:
            try:
                parsed_category_ids = [UUID(id.strip()) for id in category_ids.split(",")]
            except ValueError:
                parsed_category_ids = None
        
        parsed_date_from = None
        parsed_date_to = None
        if date_from:
            from datetime import datetime
            parsed_date_from = datetime.strptime(date_from, "%Y-%m-%d").date()
        if date_to:
            from datetime import datetime
            parsed_date_to = datetime.strptime(date_to, "%Y-%m-%d").date()
        
        filters = AnalyticsFilters(
            date_from=parsed_date_from,
            date_to=parsed_date_to,
            period_type=period_type,
            category_ids=parsed_category_ids,
            include_archived=include_archived
        )
    
    service = ControlAnalyticsService(db)
    return await service.get_control_metrics(tenant.id, filters)


@router.get("/category-breakdown")
async def get_category_breakdown(
    tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db),
    date_from: Optional[str] = Query(None),
    date_to: Optional[str] = Query(None),
    include_archived: bool = Query(False)
):
    """Get expense breakdown by category."""
    
    filters = None
    if date_from or date_to:
        parsed_date_from = None
        parsed_date_to = None
        if date_from:
            from datetime import datetime
            parsed_date_from = datetime.strptime(date_from, "%Y-%m-%d").date()
        if date_to:
            from datetime import datetime
            parsed_date_to = datetime.strptime(date_to, "%Y-%m-%d").date()
        
        filters = AnalyticsFilters(
            date_from=parsed_date_from,
            date_to=parsed_date_to,
            include_archived=include_archived
        )
    
    service = ControlAnalyticsService(db)
    return await service.get_category_breakdown(tenant.id, filters)


@router.get("/cash-flow")
async def get_cash_flow_data(
    tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db),
    date_from: Optional[str] = Query(None),
    date_to: Optional[str] = Query(None),
    period_type: Optional[str] = Query("month"),
    include_archived: bool = Query(False)
):
    """Get cash flow data over time."""
    
    filters = None
    if date_from or date_to or period_type != "month":
        parsed_date_from = None
        parsed_date_to = None
        if date_from:
            from datetime import datetime
            parsed_date_from = datetime.strptime(date_from, "%Y-%m-%d").date()
        if date_to:
            from datetime import datetime
            parsed_date_to = datetime.strptime(date_to, "%Y-%m-%d").date()
        
        filters = AnalyticsFilters(
            date_from=parsed_date_from,
            date_to=parsed_date_to,
            period_type=period_type,
            include_archived=include_archived
        )
    
    service = ControlAnalyticsService(db)
    return await service.get_cash_flow_data(tenant.id, filters)


@router.get("/trends")
async def get_trend_data(
    tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db),
    date_from: Optional[str] = Query(None),
    date_to: Optional[str] = Query(None),
    period_type: Optional[str] = Query("month")
):
    """Get trend analysis data."""
    
    filters = None
    if date_from or date_to:
        parsed_date_from = None
        parsed_date_to = None
        if date_from:
            from datetime import datetime
            parsed_date_from = datetime.strptime(date_from, "%Y-%m-%d").date()
        if date_to:
            from datetime import datetime
            parsed_date_to = datetime.strptime(date_to, "%Y-%m-%d").date()
        
        filters = AnalyticsFilters(
            date_from=parsed_date_from,
            date_to=parsed_date_to,
            period_type=period_type
        )
    
    service = ControlAnalyticsService(db)
    return await service.get_trend_data(tenant.id, filters)


@router.get("/budget-analysis")
async def get_budget_analysis(
    tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db),
    date_from: Optional[str] = Query(None),
    date_to: Optional[str] = Query(None)
):
    """Get budget vs actual analysis."""
    
    filters = None
    if date_from or date_to:
        parsed_date_from = None
        parsed_date_to = None
        if date_from:
            from datetime import datetime
            parsed_date_from = datetime.strptime(date_from, "%Y-%m-%d").date()
        if date_to:
            from datetime import datetime
            parsed_date_to = datetime.strptime(date_to, "%Y-%m-%d").date()
        
        filters = AnalyticsFilters(
            date_from=parsed_date_from,
            date_to=parsed_date_to
        )
    
    service = ControlAnalyticsService(db)
    return await service.get_budget_analysis(tenant.id, filters)


@router.get("/alerts")
async def get_alerts(
    tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """Get financial alerts and notifications."""
    
    service = ControlAnalyticsService(db)
    return await service.get_alerts(tenant.id)


# Health check endpoint
@router.get("/health")
async def analytics_health_check():
    """Health check for financial control analytics."""
    return {"status": "healthy", "module": "financial_control_analytics"}
