import logging
import uuid
from typing import Optional, Callable, Awaitable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

logger = logging.getLogger(__name__)


class TenantMiddleware(BaseHTTPMiddleware):
    """
    Middleware to handle tenant context in requests.

    This middleware extracts tenant information from headers or path parameters
    and makes it available in the request state for easier access in endpoints.
    """

    def __init__(
        self,
        app: ASGIApp,
        tenant_header_name: str = "X-Tenant-ID",
        tenant_path_param_name: str = "tenant_id",
    ):
        super().__init__(app)
        self.tenant_header_name = tenant_header_name
        self.tenant_path_param_name = tenant_path_param_name

    async def dispatch(
        self, request: Request, call_next: Callable[[Request], Awaitable[Response]]
    ) -> Response:
        """
        Process the request before it reaches the endpoint.

        Extracts tenant information from headers or path parameters and
        stores it in request.state for easy access in endpoints.
        """
        # Try to get tenant ID from header
        tenant_id_str = request.headers.get(self.tenant_header_name)
        tenant_id: Optional[uuid.UUID] = None

        # If tenant ID is in header, try to parse it
        if tenant_id_str:
            try:
                tenant_id = uuid.UUID(tenant_id_str)
                logger.debug(f"TenantMiddleware: Found tenant ID {tenant_id} in header")
            except ValueError:
                logger.warning(
                    f"TenantMiddleware: Invalid tenant ID format in header: {tenant_id_str}"
                )

        # If tenant ID is not in header or invalid, try to get it from path parameters
        if not tenant_id and self.tenant_path_param_name in request.path_params:
            path_tenant_id_str = request.path_params.get(self.tenant_path_param_name)
            if path_tenant_id_str:
                try:
                    tenant_id = uuid.UUID(path_tenant_id_str)
                    logger.debug(f"TenantMiddleware: Found tenant ID {tenant_id} in path parameter")
                except ValueError:
                    logger.warning(
                        f"TenantMiddleware: Invalid tenant ID format in path: {path_tenant_id_str}"
                    )

        # Store tenant ID in request state for easy access in endpoints
        request.state.tenant_id = tenant_id

        # Continue processing the request
        response = await call_next(request)
        return response
