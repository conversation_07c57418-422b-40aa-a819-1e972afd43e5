"""
API endpoints for translation management.
"""

import logging
from typing import List, Annotated, Any, Optional, TYPE_CHECKING

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.user_auth import get_current_active_user
from app.modules.core.auth.dependencies.role_auth import require_system_role
from app.modules.core.roles.models.roles import SystemRole

if TYPE_CHECKING:
    from app.modules.core.users.models.user import User

from app.modules.core.i18n.schemas.translation import (
    TranslationCreate,
    TranslationUpdate,
    TranslationRead,
)
from app.modules.core.i18n.services.translation_service import TranslationService

# Set up logger
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# Define required roles
admin_roles = [SystemRole.ADMIN]


@router.post("/", response_model=TranslationRead, status_code=status.HTTP_201_CREATED)
async def create_translation(
    translation_in: TranslationCreate,
    db: Annotated[AsyncSession, Depends(get_db)],
    current_user: Annotated["User", Depends(get_current_active_user)],
    _: Annotated[Any, Depends(require_system_role(required_roles=admin_roles))],
):
    """
    Create a new translation.
    Requires ADMIN system role.
    """
    translation_service = TranslationService()
    try:
        return await translation_service.create_translation(db, translation_in, current_user.id)
    except Exception as e:
        logger.error(f"Error creating translation: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Error creating translation",
        )


@router.get("/", response_model=List[TranslationRead])
async def read_translations(
    db: Annotated[AsyncSession, Depends(get_db)],
    language_id: Optional[int] = Query(None, description="Filter by language ID"),
    key: Optional[str] = Query(None, description="Filter by translation key"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=200),
):
    """
    Retrieve all translations with pagination and optional filtering.
    This endpoint is public and does not require authentication.
    """
    translation_service = TranslationService()
    return await translation_service.get_translations(
        db, language_id=language_id, key=key, skip=skip, limit=limit
    )


@router.get("/{translation_id}", response_model=TranslationRead)
async def read_translation(
    translation_id: Annotated[int, Path(..., description="The ID of the translation to retrieve")],
    db: Annotated[AsyncSession, Depends(get_db)],
):
    """
    Retrieve a specific translation by ID.
    This endpoint is public and does not require authentication.
    """
    translation_service = TranslationService()
    translation = await translation_service.get_translation(db, translation_id)
    if not translation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Translation not found",
        )
    return translation


@router.put("/{translation_id}", response_model=TranslationRead)
async def update_translation(
    translation_id: Annotated[int, Path(..., description="The ID of the translation to update")],
    translation_in: TranslationUpdate,
    db: Annotated[AsyncSession, Depends(get_db)],
    current_user: Annotated["User", Depends(get_current_active_user)],
    _: Annotated[Any, Depends(require_system_role(required_roles=admin_roles))],
):
    """
    Update a translation.
    Requires ADMIN system role.
    """
    translation_service = TranslationService()
    translation = await translation_service.update_translation(
        db, translation_id, translation_in, current_user.id
    )
    if not translation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Translation not found",
        )
    return translation


@router.delete("/{translation_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_translation(
    translation_id: Annotated[int, Path(..., description="The ID of the translation to delete")],
    db: Annotated[AsyncSession, Depends(get_db)],
    current_user: Annotated["User", Depends(get_current_active_user)],
    _: Annotated[Any, Depends(require_system_role(required_roles=admin_roles))],
):
    """
    Delete a translation.
    Requires ADMIN system role.
    """
    translation_service = TranslationService()
    result = await translation_service.delete_translation(db, translation_id)
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Translation not found",
        )
    return None
