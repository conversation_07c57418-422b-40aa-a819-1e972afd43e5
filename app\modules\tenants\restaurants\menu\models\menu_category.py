import uuid  # Import uuid
from sqlalchemy import <PERSON>umn, Inte<PERSON>, String, Text, ForeignKey, Boolean, Index
from sqlalchemy.dialects.postgresql import UUID  # Import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base
from app.modules.core.tenants.models.tenant import Tenant  # Assuming Tenant model exists


class MenuCategory(Base):
    """
    Represents a category in the digital menu (e.g., Appetizers, Main Courses).
    """

    __tablename__ = "menu_categories"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)  # Changed to UUID
    tenant_id = Column(
        UUID(as_uuid=True), ForeignKey(Tenant.id), nullable=False, index=True
    )  # Changed to UUID
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    display_order = Column(Integer, default=0, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)  # Added for soft delete/activation
    is_default = Column(Boolean, default=False, nullable=True)  # Added for default category identification

    # Parent category for subcategory support
    parent_id = Column(
        UUID(as_uuid=True), ForeignKey("menu_categories.id"), nullable=True, index=True
    )

    # Digital menu association for multi-menu support
    digital_menu_id = Column(
        UUID(as_uuid=True), ForeignKey("digital_menus.id"), nullable=True, index=True
    )

    # Relationships
    tenant = relationship("Tenant")
    menu_items = relationship("MenuItem", back_populates="category", cascade="all, delete-orphan")
    digital_menu = relationship("DigitalMenu", back_populates="categories")

    # Self-referential relationship for subcategories
    # Parent category (the category this one belongs to)
    parent = relationship(
        "MenuCategory",
        remote_side=[id],  # Points to the 'id' column of the parent MenuCategory
        back_populates="children",
        foreign_keys=[parent_id],  # Explicitly define foreign keys for SQLAlchemy
    )
    # Child categories (categories that belong to this one)
    children = relationship(
        "MenuCategory",
        back_populates="parent",
        cascade="all, delete-orphan",
        foreign_keys="[MenuCategory.parent_id]",  # Explicitly define foreign keys for SQLAlchemy
    )

    __table_args__ = (
        Index(
            "ix_menu_categories_tenant_id_name_parent",
            "tenant_id",
            "name",
            "parent_id",
            unique=True,
        ),  # Name should be unique within a tenant and parent
        Index("ix_menu_categories_tenant_id_display_order", "tenant_id", "display_order"),
        Index("ix_menu_categories_parent_id", "parent_id"),
    )

    def __repr__(self):
        return f"<MenuCategory(id={self.id}, name='{self.name}', tenant_id={self.tenant_id})>"
