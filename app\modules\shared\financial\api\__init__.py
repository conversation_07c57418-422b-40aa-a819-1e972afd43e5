"""API endpoints for Financial module."""

from fastapi import APIRouter

def register_financial_routes() -> APIRouter:
    """Creates and configures the API router for the financial module."""
    router = APIRouter()

    # Import and include routers from submodules
    from app.modules.shared.financial.transactions.api.transaction_api import router as transactions_router
    from app.modules.shared.financial.invoices.api.invoice_api import router as invoices_router
    from app.modules.shared.financial.invoices.api.b2b_invoice_api import router as b2b_invoices_router
    from app.modules.shared.financial.categories.api import router as categories_router
    from app.modules.core.functions.orders.api import orders_router
    # from .budgets.api import router as budgets_router
    # from .reports.api import router as reports_router
    # from .documents.api import router as documents_router
    # from .settings.api import router as settings_router

    router.include_router(transactions_router, prefix="/transactions", tags=["transactions"])
    router.include_router(invoices_router, prefix="/invoices", tags=["invoices"])
    router.include_router(b2b_invoices_router, prefix="/invoices", tags=["b2b-invoices"])
    router.include_router(categories_router, prefix="/categories", tags=["categories"])
    router.include_router(orders_router, prefix="/orders", tags=["orders"])
    # router.include_router(budgets_router, prefix="/budgets", tags=["budgets"])
    # router.include_router(reports_router, prefix="/reports", tags=["reports"])
    # router.include_router(documents_router, prefix="/documents", tags=["documents"])
    # router.include_router(settings_router, prefix="/settings", tags=["settings"])
    
    return router

# Create the router instance by calling the function
router = register_financial_routes()

__all__ = ["router"]
