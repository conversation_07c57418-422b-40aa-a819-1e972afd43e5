import uuid  # Import uuid
from pydantic import BaseModel, ConfigDict, <PERSON>
from typing import Optional, Union
from decimal import Decimal  # Use Decimal for price adjustments


class VariantOptionBase(BaseModel):
    """Base schema for Variant Option."""

    name: str = Field(
        ...,
        max_length=100,
        description="Name of the variant option (e.g., Medium, Well Done)",
    )
    price_adjustment: Decimal = Field(
        Decimal("0.00"), description="Price adjustment relative to item's base price"
    )
    display_order: int = Field(0, description="Order within the variant group")
    is_active: bool = Field(True, description="Whether the option is currently active")
    is_default: bool = Field(False, description="Whether this option is the default for the group")


class VariantOptionCreate(VariantOptionBase):
    """Schema for creating a new Variant Option."""

    id: Optional[Union[uuid.UUID, str]] = Field(None, description="Optional ID for existing options")
    # variant_group_id will be path parameter or part of a nested structure
    # tenant_id will be added by the service


class VariantOptionUpdate(VariantOptionBase):
    """Schema for updating an existing Variant Option. All fields optional."""

    name: Optional[str] = Field(None, max_length=100)
    price_adjustment: Optional[Decimal] = None
    display_order: Optional[int] = None
    is_active: Optional[bool] = None
    is_default: Optional[bool] = None


class VariantOptionRead(VariantOptionBase):
    """Schema for reading a Variant Option."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    variant_group_id: uuid.UUID

    model_config = ConfigDict(from_attributes=True)


# Optional simplified version if needed elsewhere
# class VariantOptionReadSimple(BaseModel):
#     id: int
#     name: str
#     price_adjustment: Decimal
#     model_config = ConfigDict(from_attributes=True)
