'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  BuildingOfficeIcon,
  StarIcon,
  MapPinIcon,
  PhoneIcon,
  EnvelopeIcon,
  TruckIcon,
  ShoppingBagIcon,
  MagnifyingGlassIcon,
  CheckBadgeIcon
} from '@heroicons/react/24/outline';
import { apiClient } from '@/lib/apiClient';
import { toast } from 'sonner';

interface Supplier {
  id: string;
  name: string;
  description?: string;
  logo_url?: string;
  rating: number;
  review_count: number;
  location: string;
  phone?: string;
  email?: string;
  website?: string;
  specialties: string[];
  delivery_areas: string[];
  average_delivery_days: number;
  minimum_order_value: number;
  is_verified: boolean;
  is_premium: boolean;
  total_products: number;
  total_orders: number;
  response_time_hours: number;
  certifications: string[];
}

export function SupplierDirectory() {
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLocation, setSelectedLocation] = useState('all');
  const [selectedSpecialty, setSelectedSpecialty] = useState('all');
  const [sortBy, setSortBy] = useState('rating');

  useEffect(() => {
    loadSuppliers();
  }, []);

  const loadSuppliers = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await apiClient('/tshop/suppliers', {
        headers: {
          'X-Market-Context': 'b2b',
        },
      });

      if (response && Array.isArray(response.items)) {
        setSuppliers(response.items);
      } else {
        setSuppliers([]);
      }
    } catch (err: any) {
      console.error('Failed to load suppliers:', err);
      const errorMessage = err.message || 'Falha ao carregar fornecedores';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Filter and sort suppliers
  const filteredSuppliers = suppliers
    .filter(supplier => {
      const matchesSearch = supplier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           supplier.description?.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesLocation = selectedLocation === 'all' || supplier.location === selectedLocation;
      const matchesSpecialty = selectedSpecialty === 'all' || 
                              supplier.specialties.some(s => s === selectedSpecialty);
      
      return matchesSearch && matchesLocation && matchesSpecialty;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'rating':
          return b.rating - a.rating;
        case 'orders':
          return b.total_orders - a.total_orders;
        case 'products':
          return b.total_products - a.total_products;
        case 'delivery':
          return a.average_delivery_days - b.average_delivery_days;
        case 'response':
          return a.response_time_hours - b.response_time_hours;
        default:
          return a.name.localeCompare(b.name);
      }
    });

  // Get unique locations and specialties
  const locations = Array.from(new Set(suppliers.map(s => s.location)));
  const specialties = Array.from(new Set(suppliers.flatMap(s => s.specialties)));

  const handleContactSupplier = (supplier: Supplier) => {
    if (supplier.email) {
      window.open(`mailto:${supplier.email}?subject=Interesse em parceria B2B`);
    } else if (supplier.phone) {
      window.open(`tel:${supplier.phone}`);
    } else {
      toast.info('Informações de contato não disponíveis');
    }
  };

  const handleViewProducts = (supplierId: string) => {
    // Navigate to supplier products
    window.location.href = `/dashboard/tshop/suppliers/${supplierId}/products`;
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-16 w-16 bg-gray-200 rounded-full mb-4"></div>
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-3 bg-gray-200 rounded mb-4"></div>
              <div className="h-8 bg-gray-200 rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <p className="text-red-600">{error}</p>
          <Button onClick={loadSuppliers} className="mt-4">
            Tentar novamente
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Diretório de Fornecedores B2B</CardTitle>
          <CardDescription>
            Encontre fornecedores verificados para suas necessidades empresariais
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="relative">
              <MagnifyingGlassIcon className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
              <Input
                placeholder="Buscar fornecedores..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={selectedLocation} onValueChange={setSelectedLocation}>
              <SelectTrigger>
                <SelectValue placeholder="Localização" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas as localizações</SelectItem>
                {locations.map(location => (
                  <SelectItem key={location} value={location}>
                    {location}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={selectedSpecialty} onValueChange={setSelectedSpecialty}>
              <SelectTrigger>
                <SelectValue placeholder="Especialidade" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas as especialidades</SelectItem>
                {specialties.map(specialty => (
                  <SelectItem key={specialty} value={specialty}>
                    {specialty}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger>
                <SelectValue placeholder="Ordenar por" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name">Nome</SelectItem>
                <SelectItem value="rating">Avaliação</SelectItem>
                <SelectItem value="orders">Pedidos realizados</SelectItem>
                <SelectItem value="products">Produtos disponíveis</SelectItem>
                <SelectItem value="delivery">Tempo de entrega</SelectItem>
                <SelectItem value="response">Tempo de resposta</SelectItem>
              </SelectContent>
            </Select>
            
            <div className="text-sm text-gray-600 flex items-center">
              {filteredSuppliers.length} fornecedores encontrados
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Suppliers Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredSuppliers.map((supplier) => (
          <Card key={supplier.id} className="overflow-hidden hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              {/* Header */}
              <div className="flex items-start space-x-4 mb-4">
                <div className="relative w-16 h-16 bg-gray-100 rounded-full flex-shrink-0 flex items-center justify-center">
                  {supplier.logo_url ? (
                    <Image
                      src={supplier.logo_url}
                      alt={supplier.name}
                      fill
                      className="object-cover rounded-full"
                    />
                  ) : (
                    <BuildingOfficeIcon className="h-8 w-8 text-gray-400" />
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <h3 className="font-semibold text-lg truncate">{supplier.name}</h3>
                    {supplier.is_verified && (
                      <CheckBadgeIcon className="h-5 w-5 text-blue-600" />
                    )}
                    {supplier.is_premium && (
                      <Badge className="bg-yellow-100 text-yellow-800">Premium</Badge>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-1 mb-2">
                    <StarIcon className="h-4 w-4 text-yellow-500" />
                    <span className="text-sm font-medium">{supplier.rating.toFixed(1)}</span>
                    <span className="text-sm text-gray-500">({supplier.review_count})</span>
                  </div>
                  
                  <div className="flex items-center text-sm text-gray-600">
                    <MapPinIcon className="h-4 w-4 mr-1" />
                    <span>{supplier.location}</span>
                  </div>
                </div>
              </div>

              {/* Description */}
              {supplier.description && (
                <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                  {supplier.description}
                </p>
              )}

              {/* Specialties */}
              <div className="mb-4">
                <div className="flex flex-wrap gap-1">
                  {supplier.specialties.slice(0, 3).map((specialty) => (
                    <Badge key={specialty} variant="outline" className="text-xs">
                      {specialty}
                    </Badge>
                  ))}
                  {supplier.specialties.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{supplier.specialties.length - 3}
                    </Badge>
                  )}
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                <div className="flex items-center">
                  <ShoppingBagIcon className="h-4 w-4 mr-2 text-gray-400" />
                  <span>{supplier.total_products} produtos</span>
                </div>
                <div className="flex items-center">
                  <TruckIcon className="h-4 w-4 mr-2 text-gray-400" />
                  <span>{supplier.average_delivery_days} dias</span>
                </div>
              </div>

              {/* Certifications */}
              {supplier.certifications.length > 0 && (
                <div className="mb-4">
                  <p className="text-xs text-gray-500 mb-1">Certificações:</p>
                  <div className="flex flex-wrap gap-1">
                    {supplier.certifications.slice(0, 2).map((cert) => (
                      <Badge key={cert} variant="secondary" className="text-xs">
                        {cert}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="space-y-2">
                <Button
                  onClick={() => handleViewProducts(supplier.id)}
                  className="w-full"
                >
                  <ShoppingBagIcon className="h-4 w-4 mr-2" />
                  Ver Produtos ({supplier.total_products})
                </Button>
                
                <Button
                  onClick={() => handleContactSupplier(supplier)}
                  variant="outline"
                  className="w-full"
                >
                  {supplier.email ? (
                    <EnvelopeIcon className="h-4 w-4 mr-2" />
                  ) : (
                    <PhoneIcon className="h-4 w-4 mr-2" />
                  )}
                  Entrar em Contato
                </Button>
              </div>

              {/* Response Time */}
              <div className="mt-3 text-xs text-gray-500 text-center">
                Responde em até {supplier.response_time_hours}h
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredSuppliers.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <BuildingOfficeIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Nenhum fornecedor encontrado com os filtros selecionados.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
