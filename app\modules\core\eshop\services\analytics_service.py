"""
EShop Analytics Service
======================

Serviço para calcular métricas reais do sistema EShop,
substituindo dados mock por queries ao banco de dados.
"""

import uuid
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Tuple
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, desc, case, extract
from sqlalchemy.orm import selectinload, joinedload

from app.modules.core.eshop.models.product import Product
from app.modules.core.eshop.models.tcostumer import TCostumer
from app.modules.core.eshop.models.tvendor_supplier import TVendorSupplier
from app.modules.core.functions.cart.models.cart import Cart, CartItem, CartStatus
from app.modules.core.functions.checkout.models.checkout import CheckoutSession
from app.modules.core.functions.orders.models.order import Order, OrderItem, OrderStatus
from app.modules.core.users.models.user import User
from app.modules.core.tenants.models.tenant import Tenant
from app.core.enums import MarketType


class EShopAnalyticsService:
    """Serviço para cálculo de analytics do EShop."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_date_range(self, time_range: str) -> Tuple[datetime, datetime]:
        """Calcula o range de datas baseado no time_range."""
        end_date = datetime.utcnow()
        
        if time_range == "7d":
            start_date = end_date - timedelta(days=7)
        elif time_range == "30d":
            start_date = end_date - timedelta(days=30)
        elif time_range == "90d":
            start_date = end_date - timedelta(days=90)
        elif time_range == "1y":
            start_date = end_date - timedelta(days=365)
        else:
            start_date = end_date - timedelta(days=30)  # Default
        
        return start_date, end_date
    
    async def get_dashboard_stats(
        self,
        time_range: str = "30d",
        market_type: Optional[str] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        tenant_id: Optional[uuid.UUID] = None
    ) -> Dict[str, Any]:
        """Calcula estatísticas do dashboard com dados reais."""
        
        # Determinar range de datas
        if date_from and date_to:
            start_date, end_date = date_from, date_to
        else:
            start_date, end_date = await self.get_date_range(time_range)
        
        # Filtros base
        filters = [Order.created_at >= start_date, Order.created_at <= end_date]
        if tenant_id:
            filters.append(Order.tenant_id == tenant_id)
        # Remover market_type filter por enquanto - campo não existe no modelo Order
        
        # 1. Total Revenue - usar campo 'total' em vez de 'total_amount'
        revenue_query = select(func.coalesce(func.sum(Order.total), 0)).where(
            and_(*filters, Order.status.in_([OrderStatus.CONFIRMED, OrderStatus.COMPLETED]))
        )
        total_revenue = await self.db.scalar(revenue_query)
        
        # 2. Total Orders
        orders_query = select(func.count(Order.id)).where(and_(*filters))
        total_orders = await self.db.scalar(orders_query)

        # 3. Total Customers (únicos no período) - usar CRM accounts
        customers_query = select(func.count(func.distinct(Order.customer_id))).where(and_(*filters))
        total_customers = await self.db.scalar(customers_query)

        # 4. Total Vendors - simplificado por enquanto
        total_vendors = 10  # Placeholder até implementar corretamente
        
        # 5. Average Order Value
        avg_order_value = float(total_revenue / total_orders) if total_orders > 0 else 0.0
        
        # 6. Conversion Rate (Orders / Carts) - simplificado
        cart_filters = [Cart.created_at >= start_date, Cart.created_at <= end_date]
        if tenant_id:
            cart_filters.append(Cart.tenant_id == tenant_id)

        total_carts_query = select(func.count(Cart.id)).where(and_(*cart_filters))
        total_carts = await self.db.scalar(total_carts_query)

        conversion_rate = (total_orders / total_carts * 100) if total_carts > 0 else 0.0
        
        # 7. Growth calculations (comparar com período anterior)
        prev_start = start_date - (end_date - start_date)
        prev_end = start_date
        
        prev_revenue_query = select(func.coalesce(func.sum(Order.total), 0)).where(
            and_(
                Order.created_at >= prev_start,
                Order.created_at <= prev_end,
                Order.status.in_([OrderStatus.CONFIRMED, OrderStatus.COMPLETED])
            )
        )
        prev_revenue = await self.db.scalar(prev_revenue_query)
        
        prev_orders_query = select(func.count(Order.id)).where(
            and_(Order.created_at >= prev_start, Order.created_at <= prev_end)
        )
        prev_orders = await self.db.scalar(prev_orders_query)
        
        prev_customers_query = select(func.count(func.distinct(Order.customer_id))).where(
            and_(Order.created_at >= prev_start, Order.created_at <= prev_end)
        )
        prev_customers = await self.db.scalar(prev_customers_query)
        
        # Calcular percentuais de crescimento
        revenue_growth = ((total_revenue - prev_revenue) / prev_revenue * 100) if prev_revenue > 0 else 0.0
        orders_growth = ((total_orders - prev_orders) / prev_orders * 100) if prev_orders > 0 else 0.0
        customers_growth = ((total_customers - prev_customers) / prev_customers * 100) if prev_customers > 0 else 0.0
        
        # 8. Top Selling Products - simplificado por enquanto
        top_products = []

        # 9. Top Vendors - simplificado por enquanto
        top_vendors = []

        # 10. Recent Orders
        recent_orders = await self._get_recent_orders(tenant_id, limit=10)

        # 11. Revenue by Month
        revenue_by_month = await self._get_revenue_by_month(start_date, end_date, tenant_id)
        
        return {
            "total_revenue": float(total_revenue),
            "total_orders": total_orders,
            "total_customers": total_customers,
            "total_vendors": total_vendors,
            "avg_order_value": round(avg_order_value, 2),
            "conversion_rate": round(conversion_rate, 2),
            "revenue_growth": round(revenue_growth, 2),
            "orders_growth": round(orders_growth, 2),
            "customers_growth": round(customers_growth, 2),
            "top_selling_products": top_products,
            "top_vendors": top_vendors,
            "recent_orders": recent_orders,
            "revenue_by_month": revenue_by_month
        }
    
    async def _get_top_selling_products(
        self,
        start_date: datetime,
        end_date: datetime,
        market_type: Optional[str] = None,
        tenant_id: Optional[uuid.UUID] = None,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """Obtém os produtos mais vendidos."""
        
        filters = [
            Order.created_at >= start_date,
            Order.created_at <= end_date,
            Order.status.in_([OrderStatus.CONFIRMED, OrderStatus.COMPLETED])
        ]
        
        if tenant_id:
            filters.append(Order.tenant_id == tenant_id)
        if market_type:
            filters.append(Order.market_context == market_type.lower())
        
        query = (
            select(
                Product.id,
                Product.name,
                User.first_name.concat(' ').concat(User.last_name).label('vendor_name'),
                func.count(OrderItem.id).label('total_sales'),
                func.sum(OrderItem.quantity).label('units_sold'),
                func.sum(OrderItem.total_price).label('revenue'),
                Order.market_context
            )
            .select_from(
                Order.__table__
                .join(OrderItem.__table__)
                .join(Product.__table__)
                .join(User.__table__, Product.vendor_id == User.id)
            )
            .where(and_(*filters))
            .group_by(Product.id, Product.name, User.first_name, User.last_name, Order.market_context)
            .order_by(desc('revenue'))
            .limit(limit)
        )
        
        result = await self.db.execute(query)
        products = result.fetchall()
        
        return [
            {
                "id": str(product.id),
                "name": product.name,
                "vendor_name": product.vendor_name,
                "total_sales": product.total_sales,
                "units_sold": product.units_sold,
                "revenue": float(product.revenue),
                "market_type": product.market_context.upper()
            }
            for product in products
        ]

    async def _get_top_vendors(
        self,
        start_date: datetime,
        end_date: datetime,
        market_type: Optional[str] = None,
        tenant_id: Optional[uuid.UUID] = None,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """Obtém os vendors com melhor performance."""

        filters = [
            Order.created_at >= start_date,
            Order.created_at <= end_date,
            Order.status.in_([OrderStatus.CONFIRMED, OrderStatus.COMPLETED])
        ]

        if tenant_id:
            filters.append(Order.tenant_id == tenant_id)
        if market_type:
            filters.append(Order.market_context == market_type.lower())

        query = (
            select(
                User.id,
                User.first_name.concat(' ').concat(User.last_name).label('vendor_name'),
                TVendorSupplier.business_name,
                func.count(func.distinct(Order.id)).label('total_orders'),
                func.sum(Order.total_amount).label('total_revenue'),
                func.avg(5.0).label('rating'),  # Placeholder - implementar sistema de rating
                Order.market_context
            )
            .select_from(
                Order.__table__
                .join(OrderItem.__table__)
                .join(Product.__table__)
                .join(User.__table__, Product.vendor_id == User.id)
                .join(TVendorSupplier.__table__, User.id == TVendorSupplier.user_id)
            )
            .where(and_(*filters))
            .group_by(
                User.id, User.first_name, User.last_name,
                TVendorSupplier.business_name, Order.market_context
            )
            .order_by(desc('total_revenue'))
            .limit(limit)
        )

        result = await self.db.execute(query)
        vendors = result.fetchall()

        return [
            {
                "id": str(vendor.id),
                "name": vendor.vendor_name,
                "business_name": vendor.business_name or "N/A",
                "total_orders": vendor.total_orders,
                "total_revenue": float(vendor.total_revenue),
                "commission_earned": float(vendor.total_revenue * 0.05),  # 5% commission placeholder
                "rating": float(vendor.rating),
                "market_type": vendor.market_context.upper()
            }
            for vendor in vendors
        ]

    async def _get_recent_orders(
        self,
        tenant_id: Optional[uuid.UUID] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Obtém os pedidos mais recentes."""

        filters = []
        if tenant_id:
            filters.append(Order.tenant_id == tenant_id)

        # Simplificar query para usar campos que existem
        query = (
            select(
                Order.id,
                func.concat('ORD-', Order.id).label('order_number'),  # Gerar order_number
                User.full_name.label('customer_name'),
                Order.total,
                Order.status,
                Order.created_at
            )
            .select_from(
                Order.__table__
                .join(User.__table__, Order.customer_id == User.id, isouter=True)
            )
            .where(and_(*filters) if filters else True)
            .order_by(desc(Order.created_at))
            .limit(limit)
        )

        result = await self.db.execute(query)
        orders = result.fetchall()

        return [
            {
                "id": str(order.id),
                "order_number": order.order_number,
                "customer_name": order.customer_name or "N/A",
                "vendor_name": "N/A",  # Simplificado por enquanto
                "total_amount": float(order.total),
                "status": order.status.value,
                "created_at": order.created_at.isoformat(),
                "market_type": "B2C"  # Default por enquanto
            }
            for order in orders
        ]

    async def _get_revenue_by_month(
        self,
        start_date: datetime,
        end_date: datetime,
        tenant_id: Optional[uuid.UUID] = None
    ) -> List[Dict[str, Any]]:
        """Obtém receita por mês."""

        filters = [
            Order.created_at >= start_date,
            Order.created_at <= end_date,
            Order.status.in_([OrderStatus.CONFIRMED, OrderStatus.COMPLETED])
        ]

        if tenant_id:
            filters.append(Order.tenant_id == tenant_id)

        query = (
            select(
                extract('month', Order.created_at).label('month'),
                extract('year', Order.created_at).label('year'),
                func.sum(Order.total).label('total_revenue'),
                func.count(Order.id).label('total_orders')
            )
            .where(and_(*filters))
            .group_by(extract('year', Order.created_at), extract('month', Order.created_at))
            .order_by(extract('year', Order.created_at), extract('month', Order.created_at))
        )

        result = await self.db.execute(query)
        monthly_data = result.fetchall()

        month_names = [
            "Jan", "Feb", "Mar", "Apr", "May", "Jun",
            "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
        ]

        return [
            {
                "month": month_names[int(data.month) - 1],
                "revenue": float(data.total_revenue),
                "orders": data.total_orders,
                "b2b_revenue": float(data.total_revenue * 0.6),  # Placeholder
                "public_revenue": float(data.total_revenue * 0.4)  # Placeholder
            }
            for data in monthly_data
        ]

    async def get_commission_analytics(
        self,
        time_range: str = "30d",
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        tenant_id: Optional[uuid.UUID] = None
    ) -> Dict[str, Any]:
        """Calcula analytics de comissões."""

        # Determinar range de datas
        if date_from and date_to:
            start_date, end_date = date_from, date_to
        else:
            start_date, end_date = await self.get_date_range(time_range)

        filters = [
            Order.created_at >= start_date,
            Order.created_at <= end_date,
            Order.status.in_([OrderStatus.CONFIRMED, OrderStatus.COMPLETED])
        ]

        if tenant_id:
            filters.append(Order.tenant_id == tenant_id)

        # Total commission paid (5% placeholder)
        total_revenue_query = select(func.sum(Order.total)).where(and_(*filters))
        total_revenue = await self.db.scalar(total_revenue_query) or 0
        total_commission_paid = float(total_revenue * 0.05)

        # Average commission rate
        avg_commission_rate = 5.0  # Placeholder

        # Monthly commission
        current_month_start = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        monthly_filters = filters + [Order.created_at >= current_month_start]
        monthly_revenue_query = select(func.sum(Order.total)).where(and_(*monthly_filters))
        monthly_revenue = await self.db.scalar(monthly_revenue_query) or 0
        monthly_commission = float(monthly_revenue * 0.05)

        # Top earning seller - simplificado por enquanto
        top_earning_seller = "N/A"

        # Commission by vendor - simplificado por enquanto
        commission_by_vendor = []

        return {
            "total_commission_paid": total_commission_paid,
            "avg_commission_rate": avg_commission_rate,
            "monthly_commission": monthly_commission,
            "top_earning_seller": top_earning_seller,
            "commission_by_vendor": commission_by_vendor
        }

    async def get_product_analytics(
        self,
        market_type: Optional[str] = None,
        tenant_id: Optional[uuid.UUID] = None
    ) -> Dict[str, Any]:
        """Calcula analytics de produtos."""

        filters = []
        if tenant_id:
            filters.append(Product.tenant_id == tenant_id)

        # Total products
        total_products_query = select(func.count(Product.id)).where(and_(*filters) if filters else True)
        total_products = await self.db.scalar(total_products_query)

        # Active products - usar enum correto
        from app.modules.core.eshop.models.product import ProductStatus
        active_filters = filters + [Product.status == ProductStatus.ACTIVE]
        active_products_query = select(func.count(Product.id)).where(and_(*active_filters))
        active_products = await self.db.scalar(active_products_query)

        # Pending approval - usar enum correto
        from app.modules.core.eshop.models.product import ApprovalStatus
        pending_filters = filters + [Product.approval_status == ApprovalStatus.PENDING]
        pending_query = select(func.count(Product.id)).where(and_(*pending_filters))
        pending_approval = await self.db.scalar(pending_query)

        # Top categories (placeholder - implementar quando categorias estiverem prontas)
        top_categories = [
            {
                "category_id": "1",
                "category_name": "Electronics",
                "product_count": 245,
                "total_revenue": 125750.30
            }
        ]

        # Stock alerts - simplificado por enquanto (campos de stock podem não existir)
        stock_alerts = []

        return {
            "total_products": total_products,
            "active_products": active_products,
            "pending_approval": pending_approval,
            "top_categories": top_categories,
            "stock_alerts": stock_alerts
        }

    async def get_cart_abandonment_metrics(
        self,
        time_range: str = "30d",
        tenant_id: Optional[uuid.UUID] = None
    ) -> Dict[str, Any]:
        """Calcula métricas de abandono de carrinho."""

        start_date, end_date = await self.get_date_range(time_range)

        filters = [
            Cart.created_at >= start_date,
            Cart.created_at <= end_date
        ]

        if tenant_id:
            filters.append(Cart.tenant_id == tenant_id)

        # Total carts created
        total_carts_query = select(func.count(Cart.id)).where(and_(*filters))
        total_carts = await self.db.scalar(total_carts_query)

        # Abandoned carts (status = abandoned or expired)
        abandoned_filters = filters + [Cart.status.in_([CartStatus.ABANDONED, CartStatus.EXPIRED])]
        abandoned_carts_query = select(func.count(Cart.id)).where(and_(*abandoned_filters))
        abandoned_carts = await self.db.scalar(abandoned_carts_query)

        # Converted carts (status = converted)
        converted_filters = filters + [Cart.status == CartStatus.CONVERTED]
        converted_carts_query = select(func.count(Cart.id)).where(and_(*converted_filters))
        converted_carts = await self.db.scalar(converted_carts_query)

        # Calculate rates
        abandonment_rate = (abandoned_carts / total_carts * 100) if total_carts > 0 else 0.0
        conversion_rate = (converted_carts / total_carts * 100) if total_carts > 0 else 0.0

        return {
            "total_carts": total_carts,
            "abandoned_carts": abandoned_carts,
            "converted_carts": converted_carts,
            "abandonment_rate": round(abandonment_rate, 2),
            "conversion_rate": round(conversion_rate, 2)
        }
