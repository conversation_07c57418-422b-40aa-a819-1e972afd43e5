"""
Schemas for translation keys.
"""

from typing import Optional
from pydantic import BaseModel, Field


class TranslationKeyBase(BaseModel):
    """Base schema for translation keys."""

    key_string: str = Field(..., description="Unique string identifier for the translation key")
    module: str = Field(..., description="Module or section the key belongs to")
    description: Optional[str] = Field(
        None, description="Optional description of the key's purpose"
    )


class TranslationKeyCreate(TranslationKeyBase):
    """Schema for creating a new translation key."""

    pass


class TranslationKeyUpdate(BaseModel):
    """Schema for updating an existing translation key."""

    key_string: Optional[str] = Field(
        None, description="Unique string identifier for the translation key"
    )
    module: Optional[str] = Field(None, description="Module or section the key belongs to")
    description: Optional[str] = Field(
        None, description="Optional description of the key's purpose"
    )


class TranslationKeyRead(TranslationKeyBase):
    """Schema for reading a translation key."""

    id: int

    class Config:
        from_attributes = True
