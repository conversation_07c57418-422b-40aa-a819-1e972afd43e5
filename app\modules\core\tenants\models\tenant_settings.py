"""
Tenant Settings model for comprehensive tenant configuration management.
"""

import uuid
from typing import TYPE_CHECKING, Optional, Dict, Any
from decimal import Decimal

from sqlalchemy import String, Boolean, JSON, ForeignKey, Numeric
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.db.base import Base

if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant


class TenantSettings(Base):
    """
    Model for common tenant settings and configuration.

    This model stores tenant-agnostic settings including business information,
    operating hours, language settings, loyalty system, location data,
    tax configuration, and subscription details.

    Restaurant-specific settings (WiFi networks, social media, tenant_slug)
    are handled by RestaurantTenantSettings in the restaurants module.
    """

    __tablename__ = "tenant_settings"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    tenant_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), 
        ForeignKey("tenants.id", ondelete="CASCADE"), 
        unique=True, 
        nullable=False,
        index=True
    )

    # Business Information
    business_name: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    business_type: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)

    # Currency Configuration (Multi-Currency System)
    default_currency: Mapped[str] = mapped_column(String(3), default="BRL", nullable=False)
    currency_config: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON, nullable=True
    )  # Multi-currency configuration with exchange rates and formatting

    timezone: Mapped[str] = mapped_column(
        String(50), default="America/Sao_Paulo", nullable=False
    )

    # Operating Hours (JSON field for flexibility)
    # Structure: {
    #   "monday": {
    #     "is_open": true,
    #     "service_hours": [{"id": "1", "open": "11:00", "close": "15:00", "type": "service", "label": "Lunch"}],
    #     "break_periods": [{"id": "2", "open": "15:00", "close": "18:00", "type": "break", "label": "Kitchen Break"}],
    #     "happy_hour": [{"id": "3", "open": "17:00", "close": "19:00", "type": "happy_hour", "label": "Happy Hour"}]
    #   }
    # }
    operating_hours: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON, nullable=True
    )

    # Multi-language Settings
    multi_language_enabled: Mapped[bool] = mapped_column(
        Boolean, default=False, nullable=False
    )
    available_languages: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON, nullable=True
    )  # Array of language codes and settings
    default_language: Mapped[str] = mapped_column(
        String(5), default="pt-BR", nullable=False
    )

    # Loyalty System Configuration
    loyalty_enabled: Mapped[bool] = mapped_column(
        Boolean, default=False, nullable=False
    )
    loyalty_config: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON, nullable=True
    )  # Points system, rewards, conversion rates

    # Location & Address Management
    country: Mapped[Optional[str]] = mapped_column(String(2), nullable=True)  # ISO country code
    address: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON, nullable=True
    )  # Full address with coordinates

    # Tax Configuration
    base_tax_rate: Mapped[Decimal] = mapped_column(
        Numeric(5, 4), default=Decimal("0.0000"), nullable=False
    )
    tax_calculation_method: Mapped[str] = mapped_column(
        String(20), default="incremental", nullable=False
    )  # "incremental" or "inclusive"

    # Social Media Links (Universal - all business types need marketing)
    social_media_links: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON, nullable=True
    )  # Array of social media platform links with icons

    # Subscription Information
    subscription_plan: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True
    )
    subscription_status: Mapped[str] = mapped_column(
        String(20), default="active", nullable=False
    )

    # Additional Settings (for future extensibility)
    additional_settings: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON, nullable=True
    )

    # Relationships
    tenant: Mapped["Tenant"] = relationship(
        "app.modules.core.tenants.models.tenant.Tenant",
        back_populates="settings",
        lazy="select"
    )

    def __repr__(self) -> str:
        return (
            f"<TenantSettings(id={self.id}, tenant_id={self.tenant_id}, "
            f"business_name='{self.business_name}', currency='{self.default_currency}')>"
        )

    @property
    def is_multi_language_enabled(self) -> bool:
        """Check if multi-language support is enabled."""
        return self.multi_language_enabled

    @property
    def is_loyalty_enabled(self) -> bool:
        """Check if loyalty system is enabled."""
        return self.loyalty_enabled

    @property
    def has_operating_hours(self) -> bool:
        """Check if operating hours are configured."""
        return self.operating_hours is not None and len(self.operating_hours) > 0

    @property
    def has_address(self) -> bool:
        """Check if address is configured."""
        return self.address is not None and len(self.address) > 0

    @property
    def has_social_media_links(self) -> bool:
        """Check if social media links are configured."""
        return self.social_media_links is not None and len(self.social_media_links) > 0


