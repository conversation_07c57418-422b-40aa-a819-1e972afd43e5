import uuid
from datetime import datetime
from enum import Enum
from typing import List, Optional
from sqlalchemy import Column, String, Integer, Numeric, Boolean, DateTime, Text, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, Mapped, mapped_column
from app.db.base import Base


class OrderStatus(str, Enum):
    PENDING = "pending"           # Pedido criado, aguardando confirmação
    CONFIRMED = "confirmed"       # Confirmado pelo supplier
    PARTIALLY_AVAILABLE = "partially_available"  # Alguns itens disponíveis
    SHIPPED = "shipped"           # Enviado
    DELIVERED = "delivered"       # Entregue
    CANCELLED = "cancelled"       # Cancelado


class ItemAvailability(str, Enum):
    AVAILABLE = "available"       # Item disponível
    UNAVAILABLE = "unavailable"   # Item não disponível
    PARTIAL = "partial"           # Parcialmente disponível


class SupplierOrder(Base):
    """
    Pedido feito para um supplier específico.
    """
    __tablename__ = "supplier_orders"

    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Relacionamentos
    tenant_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False)
    supplier_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("suppliers.id"), nullable=False)
    
    # Informações do pedido
    order_number: Mapped[str] = mapped_column(String(50), unique=True, nullable=False)
    status: Mapped[OrderStatus] = mapped_column(String(50), default=OrderStatus.PENDING, nullable=False)
    
    # Faturamento
    invoice_number: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    delivery_note: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)  # Albarán
    
    # Valores
    total_amount: Mapped[Optional[float]] = mapped_column(Numeric(10, 2), nullable=True)
    
    # Datas
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    confirmed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    shipped_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    delivered_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    
    # Observações
    notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Relacionamentos
    items: Mapped[List["SupplierOrderItem"]] = relationship(
        "SupplierOrderItem", 
        back_populates="order",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<SupplierOrder(id={self.id}, order_number='{self.order_number}', status='{self.status}')>"


class SupplierOrderItem(Base):
    """
    Item específico de um pedido para supplier.
    """
    __tablename__ = "supplier_order_items"

    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Relacionamentos
    order_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("supplier_orders.id"), nullable=False)
    shopping_list_item_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("shopping_list_items.id"), nullable=False)
    
    # Informações do item
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    quantity_requested: Mapped[int] = mapped_column(Integer, nullable=False)
    quantity_available: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    quantity_shipped: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    
    # Preços
    unit_price: Mapped[Optional[float]] = mapped_column(Numeric(10, 2), nullable=True)
    total_price: Mapped[Optional[float]] = mapped_column(Numeric(10, 2), nullable=True)
    
    # Disponibilidade
    availability: Mapped[Optional[ItemAvailability]] = mapped_column(String(50), nullable=True)
    
    # Observações
    notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Datas
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relacionamentos
    order: Mapped["SupplierOrder"] = relationship("SupplierOrder", back_populates="items")
    
    def __repr__(self) -> str:
        return f"<SupplierOrderItem(id={self.id}, name='{self.name}', quantity_requested={self.quantity_requested})>"


class SupplierOrderHistory(Base):
    """
    Histórico de mudanças de status dos pedidos.
    """
    __tablename__ = "supplier_order_history"

    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Relacionamentos
    order_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("supplier_orders.id"), nullable=False)
    
    # Informações da mudança
    previous_status: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    new_status: Mapped[str] = mapped_column(String(50), nullable=False)
    changed_by: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)  # Email do usuário
    notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Data
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    
    def __repr__(self) -> str:
        return f"<SupplierOrderHistory(id={self.id}, order_id={self.order_id}, status_change='{self.previous_status}' -> '{self.new_status}')>"
