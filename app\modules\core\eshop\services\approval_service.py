"""
Product Approval Service
=======================

Serviço para gerenciamento de aprovação de produtos no EShop.
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from uuid import UUID
from decimal import Decimal

from sqlalchemy import and_, desc, func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.modules.core.users.models.user import User
from app.modules.core.tenants.models.tenant import Tenant
from app.modules.core.notifications.services.notification_service import NotificationService
from app.modules.core.notifications.schemas import (
    NotificationCreate, NotificationTargetType, NotificationPriority
)
from app.core.enums import MarketType

from ..models import (
    Product, ApprovalStatus,
    ProductApprovalHistory, ProductApprovalSettings,
    ProductApprovalAction, ProductApprovalReason
)

logger = logging.getLogger(__name__)


class ProductApprovalService:
    """Serviço para gerenciamento de aprovação de produtos."""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.notification_service = NotificationService(db)

    async def submit_for_approval(
        self,
        product_id: UUID,
        submitted_by: User,
        comments: Optional[str] = None
    ) -> ProductApprovalHistory:
        """
        Submete produto para aprovação.
        
        Args:
            product_id: ID do produto
            submitted_by: Usuário que submeteu
            comments: Comentários opcionais
            
        Returns:
            Histórico de aprovação criado
        """
        # Buscar produto
        product = await self._get_product(product_id)
        if not product:
            raise ValueError(f"Produto {product_id} não encontrado")
        
        # Verificar se pode ser submetido
        if product.approval_status not in [ApprovalStatus.REJECTED, ApprovalStatus.PENDING]:
            if product.approval_status == ApprovalStatus.APPROVED:
                raise ValueError("Produto já está aprovado")
        
        # Obter configurações de aprovação
        settings = await self._get_approval_settings(product.tenant_id)
        
        # Verificar aprovação automática
        auto_approved = await self._check_auto_approval(product, settings)
        
        previous_status = product.approval_status.value
        
        if auto_approved:
            # Aprovação automática
            product.approval_status = ApprovalStatus.APPROVED
            product.approved_at = datetime.utcnow()
            product.approved_by = None  # Sistema automático
            
            action = ProductApprovalAction.AUTO_APPROVED
            reason = ProductApprovalReason.AUTO_MIGRATION
            new_status = ApprovalStatus.APPROVED.value
            
        else:
            # Aprovação manual necessária
            product.approval_status = ApprovalStatus.PENDING
            
            action = ProductApprovalAction.SUBMITTED
            reason = None
            new_status = ApprovalStatus.PENDING.value
        
        # Criar snapshot do produto
        product_snapshot = await self._create_product_snapshot(product)
        
        # Criar histórico
        approval_history = ProductApprovalHistory(
            tenant_id=product.tenant_id,
            product_id=product.id,
            action=action,
            reason=reason,
            previous_status=previous_status,
            new_status=new_status,
            comments=comments,
            performed_by=submitted_by.id,
            performed_by_role=submitted_by.system_role or "VENDOR",
            is_automated=auto_approved,
            automation_rule="auto_migration" if auto_approved else None,
            product_snapshot=product_snapshot
        )
        
        self.db.add(approval_history)
        await self.db.commit()
        await self.db.refresh(approval_history)
        
        # Enviar notificações
        await self._send_submission_notifications(product, approval_history, settings)
        
        logger.info(f"Produto {product_id} submetido para aprovação por {submitted_by.id}")
        return approval_history

    async def approve_product(
        self,
        product_id: UUID,
        approved_by: User,
        comments: Optional[str] = None,
        reason: ProductApprovalReason = ProductApprovalReason.MEETS_STANDARDS
    ) -> ProductApprovalHistory:
        """
        Aprova um produto.
        
        Args:
            product_id: ID do produto
            approved_by: Usuário que aprovou
            comments: Comentários da aprovação
            reason: Razão da aprovação
            
        Returns:
            Histórico de aprovação criado
        """
        # Buscar produto
        product = await self._get_product(product_id)
        if not product:
            raise ValueError(f"Produto {product_id} não encontrado")
        
        if product.approval_status == ApprovalStatus.APPROVED:
            raise ValueError("Produto já está aprovado")
        
        previous_status = product.approval_status.value
        
        # Atualizar produto
        product.approval_status = ApprovalStatus.APPROVED
        product.approved_at = datetime.utcnow()
        product.approved_by = approved_by.id
        product.rejection_reason = None  # Limpar razão de rejeição anterior
        
        # Criar snapshot do produto
        product_snapshot = await self._create_product_snapshot(product)
        
        # Criar histórico
        approval_history = ProductApprovalHistory(
            tenant_id=product.tenant_id,
            product_id=product.id,
            action=ProductApprovalAction.APPROVED,
            reason=reason,
            previous_status=previous_status,
            new_status=ApprovalStatus.APPROVED.value,
            comments=comments,
            performed_by=approved_by.id,
            performed_by_role=approved_by.system_role or "ADMIN",
            is_automated=False,
            product_snapshot=product_snapshot
        )
        
        self.db.add(approval_history)
        await self.db.commit()
        await self.db.refresh(approval_history)
        
        # Enviar notificações
        await self._send_approval_notifications(product, approval_history)
        
        logger.info(f"Produto {product_id} aprovado por {approved_by.id}")
        return approval_history

    async def reject_product(
        self,
        product_id: UUID,
        rejected_by: User,
        rejection_reason: str,
        reason: ProductApprovalReason = ProductApprovalReason.POLICY_VIOLATION,
        internal_notes: Optional[str] = None
    ) -> ProductApprovalHistory:
        """
        Rejeita um produto.
        
        Args:
            product_id: ID do produto
            rejected_by: Usuário que rejeitou
            rejection_reason: Razão da rejeição (visível ao vendor)
            reason: Categoria da rejeição
            internal_notes: Notas internas (não visíveis ao vendor)
            
        Returns:
            Histórico de aprovação criado
        """
        # Buscar produto
        product = await self._get_product(product_id)
        if not product:
            raise ValueError(f"Produto {product_id} não encontrado")
        
        if product.approval_status == ApprovalStatus.REJECTED:
            raise ValueError("Produto já está rejeitado")
        
        previous_status = product.approval_status.value
        
        # Atualizar produto
        product.approval_status = ApprovalStatus.REJECTED
        product.rejection_reason = rejection_reason
        product.rejected_at = datetime.utcnow()
        product.rejected_by = rejected_by.id
        
        # Criar snapshot do produto
        product_snapshot = await self._create_product_snapshot(product)
        
        # Criar histórico
        approval_history = ProductApprovalHistory(
            tenant_id=product.tenant_id,
            product_id=product.id,
            action=ProductApprovalAction.REJECTED,
            reason=reason,
            previous_status=previous_status,
            new_status=ApprovalStatus.REJECTED.value,
            comments=rejection_reason,
            internal_notes=internal_notes,
            rejection_reason=rejection_reason,
            performed_by=rejected_by.id,
            performed_by_role=rejected_by.system_role or "ADMIN",
            is_automated=False,
            product_snapshot=product_snapshot
        )
        
        self.db.add(approval_history)
        await self.db.commit()
        await self.db.refresh(approval_history)
        
        # Enviar notificações
        await self._send_rejection_notifications(product, approval_history)
        
        logger.info(f"Produto {product_id} rejeitado por {rejected_by.id}")
        return approval_history

    async def request_revision(
        self,
        product_id: UUID,
        requested_by: User,
        revision_notes: str,
        reason: ProductApprovalReason = ProductApprovalReason.INCOMPLETE_INFO
    ) -> ProductApprovalHistory:
        """
        Solicita revisão de um produto.
        
        Args:
            product_id: ID do produto
            requested_by: Usuário que solicitou revisão
            revision_notes: Notas sobre as revisões necessárias
            reason: Razão da solicitação de revisão
            
        Returns:
            Histórico de aprovação criado
        """
        # Buscar produto
        product = await self._get_product(product_id)
        if not product:
            raise ValueError(f"Produto {product_id} não encontrado")
        
        if product.approval_status == ApprovalStatus.APPROVED:
            raise ValueError("Produto aprovado não pode ser revisado")
        
        previous_status = product.approval_status.value
        
        # Atualizar produto
        product.approval_status = ApprovalStatus.REVISION_REQUESTED
        product.revision_notes = revision_notes
        product.revision_requested_at = datetime.utcnow()
        product.revision_requested_by = requested_by.id
        
        # Criar snapshot do produto
        product_snapshot = await self._create_product_snapshot(product)
        
        # Criar histórico
        approval_history = ProductApprovalHistory(
            tenant_id=product.tenant_id,
            product_id=product.id,
            action=ProductApprovalAction.REVISION_REQUESTED,
            reason=reason,
            previous_status=previous_status,
            new_status=ApprovalStatus.REVISION_REQUESTED.value,
            comments=revision_notes,
            performed_by=requested_by.id,
            performed_by_role=requested_by.system_role or "ADMIN",
            is_automated=False,
            product_snapshot=product_snapshot
        )
        
        self.db.add(approval_history)
        await self.db.commit()
        await self.db.refresh(approval_history)
        
        # Enviar notificações
        await self._send_revision_notifications(product, approval_history)
        
        logger.info(f"Revisão solicitada para produto {product_id} por {requested_by.id}")
        return approval_history

    async def get_products_pending_approval(
        self,
        tenant_id: Optional[UUID] = None,
        page: int = 1,
        per_page: int = 20
    ) -> Tuple[List[Product], int]:
        """
        Lista produtos pendentes de aprovação.
        
        Args:
            tenant_id: ID do tenant (opcional para admin global)
            page: Página atual
            per_page: Itens por página
            
        Returns:
            Tupla com (produtos, total)
        """
        stmt = select(Product).where(Product.approval_status == ApprovalStatus.PENDING)
        
        if tenant_id:
            stmt = stmt.where(Product.tenant_id == tenant_id)
        
        stmt = stmt.order_by(Product.created_at.asc())
        
        # Paginação
        offset = (page - 1) * per_page
        stmt = stmt.offset(offset).limit(per_page)
        
        result = await self.db.execute(stmt)
        products = result.scalars().all()
        
        # Contar total
        count_stmt = select(func.count(Product.id)).where(
            Product.approval_status == ApprovalStatus.PENDING
        )
        if tenant_id:
            count_stmt = count_stmt.where(Product.tenant_id == tenant_id)
        
        count_result = await self.db.execute(count_stmt)
        total = count_result.scalar()
        
        return products, total

    async def get_approval_history(
        self,
        product_id: UUID,
        page: int = 1,
        per_page: int = 10
    ) -> Tuple[List[ProductApprovalHistory], int]:
        """
        Obtém histórico de aprovação de um produto.
        
        Args:
            product_id: ID do produto
            page: Página atual
            per_page: Itens por página
            
        Returns:
            Tupla com (histórico, total)
        """
        stmt = select(ProductApprovalHistory).where(
            ProductApprovalHistory.product_id == product_id
        ).order_by(desc(ProductApprovalHistory.created_at))
        
        # Paginação
        offset = (page - 1) * per_page
        stmt = stmt.offset(offset).limit(per_page)
        
        result = await self.db.execute(stmt)
        history = result.scalars().all()
        
        # Contar total
        count_stmt = select(func.count(ProductApprovalHistory.id)).where(
            ProductApprovalHistory.product_id == product_id
        )
        count_result = await self.db.execute(count_stmt)
        total = count_result.scalar()
        
        return history, total

    async def _get_product(self, product_id: UUID) -> Optional[Product]:
        """Busca produto por ID."""
        stmt = select(Product).where(Product.id == product_id)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    async def _get_approval_settings(self, tenant_id: UUID) -> ProductApprovalSettings:
        """Obtém configurações de aprovação do tenant."""
        stmt = select(ProductApprovalSettings).where(
            ProductApprovalSettings.tenant_id == tenant_id
        )
        result = await self.db.execute(stmt)
        settings = result.scalar_one_or_none()
        
        if not settings:
            # Criar configurações padrão
            settings = ProductApprovalSettings(
                tenant_id=tenant_id,
                auto_approve_b2c_products=True,
                auto_approve_b2b_products=False,
                notify_admins_on_submission=True,
                notify_vendor_on_approval=True,
                notify_vendor_on_rejection=True
            )
            self.db.add(settings)
            await self.db.commit()
            await self.db.refresh(settings)
        
        return settings

    async def _check_auto_approval(
        self, 
        product: Product, 
        settings: ProductApprovalSettings
    ) -> bool:
        """Verifica se produto pode ser aprovado automaticamente."""
        # Produtos B2C podem ser auto-aprovados se configurado
        if (product.market_type == MarketType.B2C and 
            settings.auto_approve_b2c_products):
            return True
        
        # Produtos B2B podem ser auto-aprovados se configurado
        if (product.market_type == MarketType.B2B and 
            settings.auto_approve_b2b_products):
            return True
        
        # Verificar limite de preço para auto-aprovação
        if settings.max_auto_approve_price:
            max_price = Decimal(settings.max_auto_approve_price)
            if product.base_price <= max_price:
                return True
        
        # Verificar se é migração do Cuponic (auto-aprovação)
        if product.legacy_cuponic_id is not None:
            return True
        
        return False

    async def _create_product_snapshot(self, product: Product) -> str:
        """Cria snapshot JSON do produto para auditoria."""
        snapshot = {
            "id": str(product.id),
            "name": product.name,
            "description": product.description,
            "base_price": float(product.base_price),
            "sale_price": float(product.sale_price) if product.sale_price else None,
            "market_type": product.market_type.value,
            "approval_status": product.approval_status.value,
            "vendor_id": str(product.vendor_id),
            "category_id": str(product.category_id),
            "created_at": product.created_at.isoformat() if product.created_at else None,
            "updated_at": product.updated_at.isoformat() if product.updated_at else None
        }
        return json.dumps(snapshot)

    async def _send_submission_notifications(
        self,
        product: Product,
        history: ProductApprovalHistory,
        settings: ProductApprovalSettings
    ):
        """Envia notificações de submissão."""
        if not settings.notify_admins_on_submission:
            return
        
        # Notificar admins sobre nova submissão
        notification_data = NotificationCreate(
            title=f"Novo produto submetido para aprovação",
            content=f"O produto '{product.name}' foi submetido para aprovação pelo vendor.",
            target_type=NotificationTargetType.TENANT_STAFF,
            tenant_id=product.tenant_id,
            priority=NotificationPriority.NORMAL,
            action_url=f"/admin/products/{product.id}/approval"
        )
        
        # Buscar um admin para ser o sender (simplificado)
        admin_stmt = select(User).where(User.system_role == "admin").limit(1)
        admin_result = await self.db.execute(admin_stmt)
        admin = admin_result.scalar_one_or_none()
        
        if admin:
            await self.notification_service.create_notification(
                notification_data, admin
            )

    async def _send_approval_notifications(
        self,
        product: Product,
        history: ProductApprovalHistory
    ):
        """Envia notificações de aprovação."""
        # Notificar vendor sobre aprovação
        notification_data = NotificationCreate(
            title=f"Produto aprovado",
            content=f"Seu produto '{product.name}' foi aprovado e está disponível no marketplace.",
            target_type=NotificationTargetType.SPECIFIC_USER,
            target_id=product.vendor_id,
            tenant_id=product.tenant_id,
            priority=NotificationPriority.HIGH,
            action_url=f"/vendor/products/{product.id}"
        )
        
        # Buscar um admin para ser o sender
        admin_stmt = select(User).where(User.system_role == "admin").limit(1)
        admin_result = await self.db.execute(admin_stmt)
        admin = admin_result.scalar_one_or_none()
        
        if admin:
            await self.notification_service.create_notification(
                notification_data, admin
            )

    async def _send_rejection_notifications(
        self,
        product: Product,
        history: ProductApprovalHistory
    ):
        """Envia notificações de rejeição."""
        # Notificar vendor sobre rejeição
        notification_data = NotificationCreate(
            title=f"Produto rejeitado",
            content=f"Seu produto '{product.name}' foi rejeitado. Motivo: {history.rejection_reason}",
            target_type=NotificationTargetType.SPECIFIC_USER,
            target_id=product.vendor_id,
            tenant_id=product.tenant_id,
            priority=NotificationPriority.HIGH,
            action_url=f"/vendor/products/{product.id}/edit"
        )
        
        # Buscar um admin para ser o sender
        admin_stmt = select(User).where(User.system_role == "admin").limit(1)
        admin_result = await self.db.execute(admin_stmt)
        admin = admin_result.scalar_one_or_none()
        
        if admin:
            await self.notification_service.create_notification(
                notification_data, admin
            )

    async def _send_revision_notifications(
        self,
        product: Product,
        history: ProductApprovalHistory
    ):
        """Envia notificações de solicitação de revisão."""
        # Notificar vendor sobre necessidade de revisão
        notification_data = NotificationCreate(
            title=f"Revisão necessária",
            content=f"Seu produto '{product.name}' precisa de revisões. Detalhes: {history.comments}",
            target_type=NotificationTargetType.SPECIFIC_USER,
            target_id=product.vendor_id,
            tenant_id=product.tenant_id,
            priority=NotificationPriority.NORMAL,
            action_url=f"/vendor/products/{product.id}/edit"
        )
        
        # Buscar um admin para ser o sender
        admin_stmt = select(User).where(User.system_role == "admin").limit(1)
        admin_result = await self.db.execute(admin_stmt)
        admin = admin_result.scalar_one_or_none()
        
        if admin:
            await self.notification_service.create_notification(
                notification_data, admin
            )