"""
API endpoints for language management.
"""

import logging
from typing import List, Annotated, Any, TYPE_CHECKING

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.user_auth import get_current_active_user
from app.modules.core.auth.dependencies.role_auth import require_system_role
from app.modules.core.roles.models.roles import SystemRole

if TYPE_CHECKING:
    from app.modules.core.users.models.user import User

from app.modules.core.i18n.schemas.language import (
    LanguageCreate,
    LanguageUpdate,
    LanguageRead,
)
from app.modules.core.i18n.services.language_service import LanguageService

# Set up logger
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# Define required roles
admin_roles = [SystemRole.ADMIN]


@router.post("/", response_model=LanguageRead, status_code=status.HTTP_201_CREATED)
async def create_language(
    language_in: LanguageCreate,
    db: Annotated[AsyncSession, Depends(get_db)],
    current_user: Annotated["User", Depends(get_current_active_user)],
    _: Annotated[Any, Depends(require_system_role(required_roles=admin_roles))],
):
    """
    Create a new language.
    Requires ADMIN system role.
    """
    language_service = LanguageService()
    try:
        return await language_service.create_language(db, language_in)
    except Exception as e:
        logger.error(f"Error creating language: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Error creating language",
        )


@router.get("/", response_model=List[LanguageRead])
async def read_languages(
    db: Annotated[AsyncSession, Depends(get_db)],
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=200),
):
    """
    Retrieve all languages with pagination.
    This endpoint is public and does not require authentication.
    """
    language_service = LanguageService()
    return await language_service.get_languages(db, skip=skip, limit=limit)


@router.get("/{language_id}", response_model=LanguageRead)
async def read_language(
    language_id: Annotated[int, Path(..., description="The ID of the language to retrieve")],
    db: Annotated[AsyncSession, Depends(get_db)],
):
    """
    Retrieve a specific language by ID.
    This endpoint is public and does not require authentication.
    """
    language_service = LanguageService()
    language = await language_service.get_language(db, language_id)
    if not language:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Language not found",
        )
    return language


@router.put("/{language_id}", response_model=LanguageRead)
async def update_language(
    language_id: Annotated[int, Path(..., description="The ID of the language to update")],
    language_in: LanguageUpdate,
    db: Annotated[AsyncSession, Depends(get_db)],
    current_user: Annotated["User", Depends(get_current_active_user)],
    _: Annotated[Any, Depends(require_system_role(required_roles=admin_roles))],
):
    """
    Update a language.
    Requires ADMIN system role.
    """
    language_service = LanguageService()
    language = await language_service.update_language(db, language_id, language_in)
    if not language:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Language not found",
        )
    return language


@router.delete("/{language_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_language(
    language_id: Annotated[int, Path(..., description="The ID of the language to delete")],
    db: Annotated[AsyncSession, Depends(get_db)],
    current_user: Annotated["User", Depends(get_current_active_user)],
    _: Annotated[Any, Depends(require_system_role(required_roles=admin_roles))],
):
    """
    Delete a language.
    Requires ADMIN system role.
    """
    language_service = LanguageService()
    result = await language_service.delete_language(db, language_id)
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Language not found",
        )
    return None


@router.post("/{language_id}/set-default", response_model=LanguageRead)
async def set_default_language(
    language_id: Annotated[int, Path(..., description="The ID of the language to set as default")],
    db: Annotated[AsyncSession, Depends(get_db)],
    current_user: Annotated["User", Depends(get_current_active_user)],
    _: Annotated[Any, Depends(require_system_role(required_roles=admin_roles))],
):
    """
    Set a language as the default.
    Requires ADMIN system role.
    """
    language_service = LanguageService()
    language = await language_service.set_default_language(db, language_id)
    if not language:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Language not found",
        )
    return language


@router.get("/check-version/{language_code}")
async def check_language_version(
    language_code: Annotated[str, Path(..., description="The language code to check")],
    client_version: Annotated[str, Query(..., description="The client's current version code")],
    db: Annotated[AsyncSession, Depends(get_db)],
):
    """
    Check if a language version has changed.
    This endpoint is public and does not require authentication.

    Returns a JSON object with:
    - changed: boolean indicating if the version has changed
    - version_code: the current version code on the server
    """
    language_service = LanguageService()
    result = await language_service.check_version(db, language_code, client_version)

    if "error" in result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=result["error"],
        )

    return result
