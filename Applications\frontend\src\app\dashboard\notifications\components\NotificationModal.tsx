'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import {
  XMarkIcon,
  PhotoIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';

import { useAuth } from '@/lib/auth/AuthProvider';
import { apiClient } from '@/lib/api/client';

interface NotificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  notification?: any; // Para edição
}

interface FormData {
  title: string;
  content: string;
  target_type: string;
  target_id: string;
  tenant_id: string;
  priority: string;
  auto_expire_hours: number;
  image_url: string;
  action_url: string;
}

export default function NotificationModal({
  isOpen,
  onClose,
  onSuccess,
  notification
}: NotificationModalProps) {
  const { user, isAdmin, isTenantOwner, isTenantStaff } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [tenants, setTenants] = useState<any[]>([]);
  const [users, setUsers] = useState<any[]>([]);
  const [imageUploading, setImageUploading] = useState(false);
  
  const [formData, setFormData] = useState<FormData>({
    title: '',
    content: '',
    target_type: 'all_users',
    target_id: '',
    tenant_id: '',
    priority: 'normal',
    auto_expire_hours: 24,
    image_url: '',
    action_url: ''
  });

  // Carrega dados iniciais
  useEffect(() => {
    if (isOpen) {
      loadTenants();
      if (notification) {
        setFormData({
          title: notification.title || '',
          content: notification.content || '',
          target_type: notification.target_type || 'all_users',
          target_id: notification.target_id || '',
          tenant_id: notification.tenant_id || '',
          priority: notification.priority || 'normal',
          auto_expire_hours: notification.auto_expire_hours || 24,
          image_url: notification.image_url || '',
          action_url: notification.action_url || ''
        });
      }
    }
  }, [isOpen, notification]);

  // Carrega tenants disponíveis
  const loadTenants = async () => {
    try {
      const response = await apiClient.get('/modules/tenants/tenants/');
      setTenants(response.data.tenants || []);
    } catch (err) {
      console.error('Erro ao carregar tenants:', err);
    }
  };

  // Carrega usuários quando necessário
  const loadUsers = async (tenantId?: string) => {
    try {
      const params = tenantId ? `?tenant_id=${tenantId}` : '';
      const response = await apiClient.get(`/modules/core/auth/users/${params}`);
      setUsers(response.data.users || []);
    } catch (err) {
      console.error('Erro ao carregar usuários:', err);
    }
  };

  // Upload de imagem
  const handleImageUpload = async (file: File) => {
    if (!file) return;

    setImageUploading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('context', 'notification');

      const response = await apiClient.post('/modules/core/media_system/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      setFormData(prev => ({
        ...prev,
        image_url: response.data.url
      }));
    } catch (err) {
      console.error('Erro ao fazer upload da imagem:', err);
      setError('Erro ao fazer upload da imagem');
    } finally {
      setImageUploading(false);
    }
  };

  // Submete formulário
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const payload = {
        ...formData,
        target_id: formData.target_id || null,
        tenant_id: formData.tenant_id || null,
        image_url: formData.image_url || null,
        action_url: formData.action_url || null
      };

      if (notification) {
        // Edição
        await apiClient.put(`/modules/core/notifications/${notification.id}`, payload);
      } else {
        // Criação
        await apiClient.post('/modules/core/notifications/', payload);
      }

      onSuccess();
    } catch (err: any) {
      console.error('Erro ao salvar notificação:', err);
      setError(err.response?.data?.detail || 'Erro ao salvar notificação');
    } finally {
      setLoading(false);
    }
  };

  // Atualiza campo do formulário
  const updateField = (field: keyof FormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Carrega usuários quando target_type muda para specific_user
    if (field === 'target_type' && value === 'specific_user') {
      loadUsers(formData.tenant_id);
    }

    // Carrega usuários quando tenant_id muda e target_type é specific_user
    if (field === 'tenant_id' && formData.target_type === 'specific_user') {
      loadUsers(value);
    }
  };

  // Opções de destinatário baseadas no role do usuário
  const getTargetTypeOptions = () => {
    const options = [];

    if (isAdmin()) {
      options.push(
        { value: 'all_users', label: 'Todos os usuários' },
        { value: 'tenant_owners', label: 'Proprietários de restaurantes' },
        { value: 'tenant_users', label: 'Usuários de um restaurante' },
        { value: 'tenant_staff', label: 'Funcionários de um restaurante' },
        { value: 'tenant_customers', label: 'Clientes de um restaurante' },
        { value: 'specific_user', label: 'Usuário específico' }
      );
    } else if (isTenantOwner() || isTenantStaff()) {
      // Tenant owner ou staff
      options.push(
        { value: 'tenant_users', label: 'Todos os usuários do restaurante' },
        { value: 'tenant_staff', label: 'Funcionários' },
        { value: 'tenant_customers', label: 'Clientes' },
        { value: 'specific_user', label: 'Usuário específico' }
      );
    }

    return options;
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex items-center justify-between mb-6">
                  <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                    {notification ? 'Editar Notificação' : 'Nova Notificação'}
                  </Dialog.Title>
                  <button
                    onClick={onClose}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                {error && (
                  <div className="mb-4 p-4 bg-red-50 border-l-4 border-red-400">
                    <div className="flex">
                      <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                      <div className="ml-3">
                        <p className="text-sm text-red-700">{error}</p>
                      </div>
                    </div>
                  </div>
                )}

                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Título */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Título *
                    </label>
                    <input
                      type="text"
                      required
                      value={formData.title}
                      onChange={(e) => updateField('title', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Digite o título da notificação"
                    />
                  </div>

                  {/* Conteúdo */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Conteúdo *
                    </label>
                    <textarea
                      required
                      rows={4}
                      value={formData.content}
                      onChange={(e) => updateField('content', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Digite o conteúdo da notificação"
                    />
                  </div>

                  {/* Destinatário */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Destinatário *
                    </label>
                    <select
                      required
                      value={formData.target_type}
                      onChange={(e) => updateField('target_type', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      {getTargetTypeOptions().map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Tenant (se necessário) */}
                  {formData.target_type.startsWith('tenant_') && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Restaurante *
                      </label>
                      <select
                        required
                        value={formData.tenant_id}
                        onChange={(e) => updateField('tenant_id', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">Selecione um restaurante</option>
                        {tenants.map(tenant => (
                          <option key={tenant.id} value={tenant.id}>
                            {tenant.name}
                          </option>
                        ))}
                      </select>
                    </div>
                  )}

                  {/* Usuário específico (se necessário) */}
                  {formData.target_type === 'specific_user' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Usuário *
                      </label>
                      <select
                        required
                        value={formData.target_id}
                        onChange={(e) => updateField('target_id', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">Selecione um usuário</option>
                        {users.map(user => (
                          <option key={user.id} value={user.id}>
                            {user.email} ({user.name || 'Sem nome'})
                          </option>
                        ))}
                      </select>
                    </div>
                  )}

                  {/* Prioridade e Expiração */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Prioridade
                      </label>
                      <select
                        value={formData.priority}
                        onChange={(e) => updateField('priority', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="low">Baixa</option>
                        <option value="normal">Normal</option>
                        <option value="high">Alta</option>
                        <option value="urgent">Urgente</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Expirar em (horas)
                      </label>
                      <input
                        type="number"
                        min="1"
                        max="720"
                        value={formData.auto_expire_hours}
                        onChange={(e) => updateField('auto_expire_hours', parseInt(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  {/* Imagem */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Imagem (opcional)
                    </label>
                    <div className="flex items-center space-x-4">
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) handleImageUpload(file);
                        }}
                        className="hidden"
                        id="image-upload"
                      />
                      <label
                        htmlFor="image-upload"
                        className="cursor-pointer inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                      >
                        <PhotoIcon className="h-4 w-4 mr-2" />
                        {imageUploading ? 'Enviando...' : 'Escolher Imagem'}
                      </label>
                      {formData.image_url && (
                        <div className="h-12 w-12 relative">
                          <Image
                            src={formData.image_url}
                            alt="Preview"
                            fill
                            className="rounded object-cover"
                          />
                        </div>
                      )}
                    </div>
                  </div>

                  {/* URL de Ação */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      URL de Ação (opcional)
                    </label>
                    <input
                      type="url"
                      value={formData.action_url}
                      onChange={(e) => updateField('action_url', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="https://exemplo.com"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      URL que será aberta quando o usuário clicar na notificação
                    </p>
                  </div>

                  {/* Botões */}
                  <div className="flex items-center justify-end space-x-3 pt-6">
                    <button
                      type="button"
                      onClick={onClose}
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                    >
                      Cancelar
                    </button>
                    <button
                      type="submit"
                      disabled={loading}
                      className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50"
                    >
                      {loading ? 'Salvando...' : (notification ? 'Atualizar' : 'Criar')}
                    </button>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
