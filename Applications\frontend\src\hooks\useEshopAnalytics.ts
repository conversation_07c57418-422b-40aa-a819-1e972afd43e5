'use client';

import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { apiClient } from '@/lib/api/apiClient';

export interface DashboardStats {
  total_revenue: number;
  total_orders: number;
  total_customers: number;
  total_vendors: number;
  avg_order_value: number;
  conversion_rate: number;
  revenue_growth: number;
  orders_growth: number;
  customers_growth: number;
  top_selling_products: TopProduct[];
  top_vendors: TopVendor[];
  recent_orders: RecentOrder[];
  revenue_by_month: MonthlyRevenue[];
}

export interface TopProduct {
  id: string;
  name: string;
  vendor_name: string;
  total_sales: number;
  units_sold: number;
  revenue: number;
  market_type: 'B2B' | 'PUBLIC';
}

export interface TopVendor {
  id: string;
  name: string;
  business_name: string;
  total_orders: number;
  total_revenue: number;
  commission_earned: number;
  rating: number;
  market_type: 'B2B' | 'PUBLIC';
}

export interface RecentOrder {
  id: string;
  order_number: string;
  customer_name: string;
  vendor_name: string;
  total_amount: number;
  status: string;
  created_at: string;
  market_type: 'B2B' | 'PUBLIC';
}

export interface MonthlyRevenue {
  month: string;
  revenue: number;
  orders: number;
  b2b_revenue: number;
  public_revenue: number;
}

export interface AnalyticsFilters {
  timeRange?: '7d' | '30d' | '90d' | '1y';
  marketType?: 'B2B' | 'PUBLIC' | 'ALL';
  dateFrom?: string;
  dateTo?: string;
}

export interface CommissionAnalytics {
  total_commission_paid: number;
  avg_commission_rate: number;
  monthly_commission: number;
  top_earning_seller: string;
  commission_by_vendor: Array<{
    vendor_id: string;
    vendor_name: string;
    total_commission: number;
    commission_rate: number;
  }>;
}

export interface ProductAnalytics {
  total_products: number;
  active_products: number;
  pending_approval: number;
  top_categories: Array<{
    category_id: string;
    category_name: string;
    product_count: number;
    total_revenue: number;
  }>;
  stock_alerts: Array<{
    product_id: string;
    product_name: string;
    current_stock: number;
    min_stock: number;
  }>;
}

export function useEshopAnalytics() {
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [commissionAnalytics, setCommissionAnalytics] = useState<CommissionAnalytics | null>(null);
  const [productAnalytics, setProductAnalytics] = useState<ProductAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch dashboard statistics
  const fetchDashboardStats = useCallback(async (filters?: AnalyticsFilters) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams();
      if (filters?.timeRange) params.append('time_range', filters.timeRange);
      if (filters?.marketType && filters.marketType !== 'ALL') {
        params.append('market_type', filters.marketType);
      }
      if (filters?.dateFrom) params.append('date_from', filters.dateFrom);
      if (filters?.dateTo) params.append('date_to', filters.dateTo);

      const response = await apiClient.get<DashboardStats>(
        `/api/modules/core/eshop/analytics/dashboard?${params.toString()}`
      );
      
      setDashboardStats(response.data);
      return response.data;
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || 'Failed to fetch dashboard statistics';
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fetch commission analytics
  const fetchCommissionAnalytics = useCallback(async (filters?: AnalyticsFilters) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams();
      if (filters?.timeRange) params.append('time_range', filters.timeRange);
      if (filters?.dateFrom) params.append('date_from', filters.dateFrom);
      if (filters?.dateTo) params.append('date_to', filters.dateTo);

      const response = await apiClient.get<CommissionAnalytics>(
        `/api/modules/core/eshop/analytics/commissions?${params.toString()}`
      );
      
      setCommissionAnalytics(response.data);
      return response.data;
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || 'Failed to fetch commission analytics';
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fetch product analytics
  const fetchProductAnalytics = useCallback(async (filters?: AnalyticsFilters) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams();
      if (filters?.marketType && filters.marketType !== 'ALL') {
        params.append('market_type', filters.marketType);
      }

      const response = await apiClient.get<ProductAnalytics>(
        `/api/modules/core/eshop/analytics/products?${params.toString()}`
      );
      
      setProductAnalytics(response.data);
      return response.data;
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || 'Failed to fetch product analytics';
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Export analytics data
  const exportAnalytics = useCallback(async (
    type: 'dashboard' | 'commissions' | 'products',
    format: 'csv' | 'xlsx' | 'pdf',
    filters?: AnalyticsFilters
  ) => {
    try {
      const params = new URLSearchParams();
      params.append('format', format);
      if (filters?.timeRange) params.append('time_range', filters.timeRange);
      if (filters?.marketType && filters.marketType !== 'ALL') {
        params.append('market_type', filters.marketType);
      }
      if (filters?.dateFrom) params.append('date_from', filters.dateFrom);
      if (filters?.dateTo) params.append('date_to', filters.dateTo);

      const response = await apiClient.get(
        `/api/modules/core/eshop/analytics/export/${type}?${params.toString()}`,
        { responseType: 'blob' }
      );

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `eshop-${type}-analytics.${format}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      toast.success(`Analytics exported successfully as ${format.toUpperCase()}`);
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || 'Failed to export analytics';
      toast.error(errorMessage);
      throw err;
    }
  }, []);

  // Get real-time metrics
  const fetchRealTimeMetrics = useCallback(async () => {
    try {
      const response = await apiClient.get('/api/modules/core/eshop/analytics/realtime');
      return response.data;
    } catch (err: any) {
      console.error('Failed to fetch real-time metrics:', err);
      return null;
    }
  }, []);

  return {
    // State
    dashboardStats,
    commissionAnalytics,
    productAnalytics,
    isLoading,
    error,
    
    // Actions
    fetchDashboardStats,
    fetchCommissionAnalytics,
    fetchProductAnalytics,
    exportAnalytics,
    fetchRealTimeMetrics,
    
    // Utilities
    clearError: () => setError(null),
    refresh: (filters?: AnalyticsFilters) => {
      return Promise.all([
        fetchDashboardStats(filters),
        fetchCommissionAnalytics(filters),
        fetchProductAnalytics(filters)
      ]);
    }
  };
}
