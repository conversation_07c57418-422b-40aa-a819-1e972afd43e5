'use client';

import { useState, useCallback, useEffect, useRef } from 'react';
import { BuildingOfficeIcon, CurrencyDollarIcon, ExclamationTriangleIcon, MapPinIcon, GlobeAltIcon, PhotoIcon } from '@heroicons/react/24/outline';
import CurrencyFormatSettings from '../shared/CurrencyFormatSettings';
import TimezoneSelector from '../shared/TimezoneSelector';
import AddressForm from '../location/AddressForm';
import MapSelector from '../location/MapSelector';
import ContactInfoTab from './ContactInfoTab';
import ImageUpload from './ImageUpload';
import { useDebouncedInput } from '@/hooks/useDebounce';
import toast from 'react-hot-toast';

interface Address {
  street: string;
  number?: string;
  complement?: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  latitude?: number;
  longitude?: number;
}

interface ContactInfo {
  phone?: string;
  phone_is_whatsapp?: boolean;
  phone_secondary?: string;
  phone_secondary_is_whatsapp?: boolean;
  fax?: string;
}

interface CurrencyFormat {
  decimal_separator: '.' | ',';
  thousands_separator: '.' | ',' | ' ' | '';
  symbol_position: 'left' | 'right';
  symbol_spacing: boolean;
}

interface BusinessInfoTabProps {
  settings: {
    business_name?: string;
    business_type?: string;
    currency: string;
    currency_format?: CurrencyFormat;
    timezone: string;
    subscription_plan?: string;
    subscription_status?: string;
    address?: Address;
    tenant_slug?: string;
    contact?: ContactInfo;
    header_image?: string;
    logo_image?: string;
  };
  onUpdate: (field: string, value: any) => void;
  savingField?: string | null;
}

interface ValidationErrors {
  business_name?: string;
  business_type?: string;
}

const BUSINESS_TYPES = [
  'Restaurant',
  'Fast Food',
  'Cafe',
  'Bar',
  'Bakery',
  'Food Truck',
  'Catering',
  'Other'
];

const DEFAULT_ADDRESS: Address = {
  street: '',
  number: '',
  complement: '',
  city: '',
  state: '',
  zipCode: '',
  country: 'US'
};

const DEFAULT_CONTACT: ContactInfo = {
  phone: '',
  phone_is_whatsapp: false,
  phone_secondary: '',
  phone_secondary_is_whatsapp: false,
  fax: ''
};

const COUNTRIES = [
  { code: 'US', name: 'United States', flag: '🇺🇸' },
  { code: 'BR', name: 'Brazil', flag: '🇧🇷' },
  { code: 'CA', name: 'Canada', flag: '🇨🇦' },
  { code: 'GB', name: 'United Kingdom', flag: '🇬🇧' },
  { code: 'DE', name: 'Germany', flag: '🇩🇪' },
  { code: 'FR', name: 'France', flag: '🇫🇷' },
  { code: 'IT', name: 'Italy', flag: '🇮🇹' },
  { code: 'ES', name: 'Spain', flag: '🇪🇸' },
  { code: 'AU', name: 'Australia', flag: '🇦🇺' },
  { code: 'JP', name: 'Japan', flag: '🇯🇵' }
];

export default function BusinessInfoTab({ settings, onUpdate, savingField }: BusinessInfoTabProps) {
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [showMap, setShowMap] = useState(false);
  const [currentAddress, setCurrentAddress] = useState<Address>(settings.address || DEFAULT_ADDRESS);

  // Use ref to track save timeout and latest address
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const latestAddressRef = useRef<Address>(currentAddress);

  const currentContact = settings.contact || DEFAULT_CONTACT;
  const currentSlug = settings.tenant_slug || '';

  // Update local address state when settings change
  useEffect(() => {
    const newAddress = settings.address || DEFAULT_ADDRESS;
    setCurrentAddress(newAddress);
    latestAddressRef.current = newAddress;
  }, [settings.address]);

  const validateField = (field: string, value: string): string | undefined => {
    switch (field) {
      case 'business_name':
        if (!value.trim()) return 'Business name is required';
        if (value.length < 2) return 'Business name must be at least 2 characters';
        if (value.length > 100) return 'Business name must be less than 100 characters';
        break;
      case 'business_type':
        if (!value) return 'Business type is required';
        break;
    }
    return undefined;
  };

  // Validation function that doesn't cause re-renders during save
  const validateAndSaveBusinessName = (value: string) => {
    const error = validateField('business_name', value);

    // Update errors state
    setErrors(prev => ({ ...prev, business_name: error }));

    // Only save if no error
    if (!error) {
      onUpdate('business_name', value);
    }
  };

  // Debounced input for business name (10 seconds inactivity delay)
  const businessNameInput = useDebouncedInput(
    settings.business_name || '',
    validateAndSaveBusinessName,
    10000 // 10 seconds
  );

  // Debounced input for tenant slug (10 seconds inactivity delay)
  const tenantSlugInput = useDebouncedInput(
    currentSlug,
    (value) => {
      // Clean slug: lowercase, no spaces, alphanumeric and hyphens only
      const cleanSlug = value.toLowerCase().replace(/[^a-z0-9-]/g, '').replace(/--+/g, '-');
      onUpdate('tenant_slug', cleanSlug);
    },
    10000 // 10 seconds
  );

  // Immediate update for selects and other non-text fields
  const handleImmediateUpdate = (field: string, value: any) => {
    if (typeof value === 'string' && field !== 'business_name') {
      const error = validateField(field, value);
      setErrors(prev => ({ ...prev, [field]: error }));
    }
    onUpdate(field, value);
  };

  // Handle blur events to force save
  const handleBlur = (inputHandler: { forceSave: () => void }) => {
    inputHandler.forceSave();
  };

  const handleAddressUpdate = (field: keyof Address, value: any) => {
    console.log('🏠 BusinessInfoTab: Address field updated (UI only):', { field, value });

    // Use the latest address from ref to avoid stale state
    const newAddress = { ...latestAddressRef.current, [field]: value };
    console.log('📍 New address after update (UI only):', newAddress);

    // Update both state and ref immediately (UI update only - no auto-save)
    setCurrentAddress(newAddress);
    latestAddressRef.current = newAddress;

    // If coordinates are being updated (from address validation), automatically show the map
    if (field === 'latitude' || field === 'longitude') {
      setShowMap(true);
    }

    console.log('ℹ️ BusinessInfoTab: Address updated in UI only. Use "Validate Address" button to save.');
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      const timeout = saveTimeoutRef.current;
      if (timeout) {
        clearTimeout(timeout);
      }
    };
  }, []);

  const handleContactUpdate = (field: keyof ContactInfo, value: any) => {
    const newContact = { ...currentContact, [field]: value };
    onUpdate('contact', newContact);
  };

  const handleSlugUpdate = (slug: string) => {
    // Clean slug: lowercase, no spaces, alphanumeric and hyphens only
    const cleanSlug = slug.toLowerCase().replace(/[^a-z0-9-]/g, '').replace(/--+/g, '-');
    onUpdate('tenant_slug', cleanSlug);
  };

  const handleLocationSelect = (lat: number, lng: number, addressData?: Partial<Address>) => {
    const newAddress = {
      ...currentAddress,
      latitude: lat,
      longitude: lng,
      ...addressData
    };
    onUpdate('address', newAddress);
  };

  // Image upload handlers
  const handleHeaderImageUpload = async (file: File) => {
    // TODO: Implement actual upload to server
    // For now, create a local URL for preview
    const imageUrl = URL.createObjectURL(file);
    onUpdate('header_image', imageUrl);
  };

  const handleLogoImageUpload = async (file: File) => {
    // TODO: Implement actual upload to server
    // For now, create a local URL for preview
    const imageUrl = URL.createObjectURL(file);
    onUpdate('logo_image', imageUrl);
  };

  const handleHeaderImageRemove = () => {
    onUpdate('header_image', '');
  };

  const handleLogoImageRemove = () => {
    onUpdate('logo_image', '');
  };

  const selectedCountry = COUNTRIES.find(c => c.code === currentAddress.country);

  return (
    <div className="space-y-6">
      {/* Business Information */}
      <div className="card">
        <div className="flex items-center space-x-2 mb-4">
          <BuildingOfficeIcon className="h-5 w-5 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900">Business Information</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Business Name *
            </label>
            <input
              type="text"
              value={businessNameInput.value}
              onChange={(e) => businessNameInput.setValue(e.target.value)}
              onBlur={() => handleBlur(businessNameInput)} // Salva ao clicar fora
              onKeyDown={(e) => e.key === 'Enter' && handleBlur(businessNameInput)} // Salva ao pressionar Enter
              placeholder="Enter your business name"
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 ${
                errors.business_name ? 'border-red-300' : 'border-gray-300'
              } ${businessNameInput.isChanged ? 'border-yellow-300 bg-yellow-50' : ''}`}
              title="Salva automaticamente: ao clicar fora, pressionar Enter ou após 10s de inatividade"
            />
            {errors.business_name && (
              <p className="mt-1 text-sm text-red-600 flex items-center">
                <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                {errors.business_name}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Business Type *
            </label>
            <select
              value={settings.business_type || ''}
              onChange={(e) => handleImmediateUpdate('business_type', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 ${
                errors.business_type ? 'border-red-300' : 'border-gray-300'
              }`}
            >
              <option value="">Select business type</option>
              {BUSINESS_TYPES.map(type => (
                <option key={type} value={type}>
                  {type}
                </option>
              ))}
            </select>
            {errors.business_type && (
              <p className="mt-1 text-sm text-red-600 flex items-center">
                <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                {errors.business_type}
              </p>
            )}
          </div>
        </div>

        {/* Restaurant URL Slug */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Restaurant URL Slug
          </label>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 items-start">
            {/* URL Input */}
            <div>
              <div className="flex max-w-md">
                <span className="inline-flex items-center px-3 py-2 border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm rounded-l-md">
                  trix.com/
                </span>
                <input
                  type="text"
                  value={tenantSlugInput.value}
                  onChange={(e) => tenantSlugInput.setValue(e.target.value)}
                  onBlur={() => handleBlur(tenantSlugInput)} // Salva ao clicar fora
                  onKeyDown={(e) => e.key === 'Enter' && handleBlur(tenantSlugInput)} // Salva ao pressionar Enter
                  placeholder="your-restaurant"
                  className={`flex-1 px-3 py-2 border border-gray-300 rounded-r-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 ${
                    tenantSlugInput.isChanged ? 'border-yellow-300 bg-yellow-50' : ''
                  }`}
                  title="Salva automaticamente: ao clicar fora, pressionar Enter ou após 10s de inatividade"
                />
              </div>
              <p className="text-xs text-gray-500 mt-1">
                This will be your restaurant's URL. Only lowercase letters, numbers, and hyphens allowed.
              </p>
            </div>

            {/* URL Preview */}
            {currentSlug && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-sm text-blue-700">
                  <strong>Your restaurant URL:</strong> https://trix.com/{currentSlug}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Brand Images */}
      <div className="card">
        <div className="flex items-center space-x-2 mb-6">
          <PhotoIcon className="h-5 w-5 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900">Brand Images</h3>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Header Image - Takes 2 columns */}
          <div className="lg:col-span-2">
            <ImageUpload
              label="Header Image"
              description=""
              currentImage={settings.header_image}
              onImageUpload={handleHeaderImageUpload}
              onImageRemove={handleHeaderImageRemove}
              aspectRatio="wide"
              maxSize={10}
            />

            {settings.header_image && (
              <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-sm text-blue-700">
                  <strong>Preview:</strong> This image will appear at the top of your restaurant page at{' '}
                  <span className="font-mono">trix.com/{currentSlug || 'your-restaurant'}</span>
                </p>
              </div>
            )}
          </div>

          {/* Logo Image - Takes 1 column */}
          <div className="lg:col-span-1">
            <ImageUpload
              label="Restaurant Logo"
              description=""
              currentImage={settings.logo_image}
              onImageUpload={handleLogoImageUpload}
              onImageRemove={handleLogoImageRemove}
              aspectRatio="square"
              maxSize={5}
            />

            {settings.logo_image && (
              <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-md">
                <p className="text-sm text-green-700">
                  <strong>Logo Preview:</strong> This logo will appear in your restaurant's header and represent your brand across the platform.
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Image Guidelines */}
        <div className="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-md">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Image Guidelines</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-700">
            <div>
              <h5 className="font-medium text-gray-900 mb-1">Header Image:</h5>
              <ul className="space-y-1">
                <li>• Recommended: 1200x400px (16:9 ratio)</li>
                <li>• Maximum file size: 10MB</li>
                <li>• Formats: JPEG, PNG, WebP</li>
                <li>• High-quality food or restaurant photos work best</li>
              </ul>
            </div>
            <div>
              <h5 className="font-medium text-gray-900 mb-1">Logo:</h5>
              <ul className="space-y-1">
                <li>• Recommended: Square format (200x200px to 300x300px)</li>
                <li>• Maximum file size: 5MB</li>
                <li>• Formats: JPEG, PNG, WebP</li>
                <li>• Transparent background (PNG) recommended</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Contact Information */}
      <div className="card">
        <ContactInfoTab
          contactInfo={currentContact}
          onUpdate={handleContactUpdate}
        />
      </div>

      {/* Regional Settings */}
      <div className="card">
        <div className="flex items-center space-x-2 mb-6">
          <CurrencyDollarIcon className="h-5 w-5 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900">Regional Settings</h3>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Currency Configuration */}
          <div>
            <CurrencyFormatSettings
              currency={settings.currency}
              format={settings.currency_format}
              onCurrencyChange={(currency) => onUpdate('currency', currency)}
              onFormatChange={(format) => onUpdate('currency_format', format)}
            />
          </div>

          {/* Timezone */}
          <div>
            <TimezoneSelector
              value={settings.timezone}
              onChange={(timezone) => onUpdate('timezone', timezone)}
              label="Business Timezone"
            />

            {/* Timezone Information */}
            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
              <h4 className="text-sm font-medium text-blue-900 mb-2">Timezone Information</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• <strong>Operating Hours:</strong> Displayed in this timezone</li>
                <li>• <strong>Reports:</strong> All timestamps use this timezone</li>
                <li>• <strong>Notifications:</strong> Scheduled based on this timezone</li>
                <li>• <strong>Customer Display:</strong> Times shown to customers</li>
              </ul>
            </div>
          </div>
        </div>
      </div>



      {/* Business Address */}
      <div className="card">
        <div className="flex items-center space-x-2 mb-4">
          <BuildingOfficeIcon className="h-5 w-5 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900">Business Address</h3>
        </div>

        <AddressForm
          address={currentAddress}
          onUpdate={handleAddressUpdate}
          onSave={async (address) => {
            console.log('💾 BusinessInfoTab: Saving address via validate button:', address);
            await onUpdate('address', address);
            console.log('✅ BusinessInfoTab: Address saved successfully via validate button');
          }}
        />


      </div>




    </div>
  );
}
