# Crm - Interactions

**Categoria:** Crm
**Mó<PERSON>lo:** Interactions
**Total de Endpoints:** 6
**Gerado em:** 08/06/2025, 17:26:01

## 📋 Endpoints

- [GET /api/modules/crm/crm/interactions/](#get-apimodulescrmcrminteractions) - Get Interactions
- [POST /api/modules/crm/crm/interactions/](#post-apimodulescrmcrminteractions) - Create Interaction
- [GET /api/modules/crm/crm/interactions/account/{account_id}](#get-apimodulescrmcrminteractionsaccountaccount-id) - Get Account Interactions
- [DELETE /api/modules/crm/crm/interactions/{interaction_id}](#delete-apimodulescrmcrminteractionsinteraction-id) - Delete Interaction
- [GET /api/modules/crm/crm/interactions/{interaction_id}](#get-apimodulescrmcrminteractionsinteraction-id) - Get Interaction
- [PUT /api/modules/crm/crm/interactions/{interaction_id}](#put-apimodulescrmcrminteractionsinteraction-id) - Update Interaction

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### InteractionCreate

**Descrição:** Schema for creating a new Interaction.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `interaction_type` | InteractionType | ✅ | - |
| `channel` | InteractionChannel | ✅ | - |
| `subject` | string | ✅ | - |
| `description` | unknown | ❌ | - |
| `interaction_date` | string | ❌ | - |
| `duration_minutes` | unknown | ❌ | - |
| `requires_followup` | boolean | ❌ | - |
| `followup_date` | unknown | ❌ | - |
| `followup_notes` | unknown | ❌ | - |
| `is_completed` | boolean | ❌ | - |
| `outcome` | unknown | ❌ | - |
| `metadata` | unknown | ❌ | - |
| `account_id` | string | ✅ | - |
| `contact_id` | unknown | ❌ | - |
| `created_by_user_id` | unknown | ❌ | - |

### InteractionRead

**Descrição:** Schema for reading an Interaction.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `interaction_type` | InteractionType | ✅ | - |
| `channel` | InteractionChannel | ✅ | - |
| `subject` | string | ✅ | - |
| `description` | unknown | ❌ | - |
| `interaction_date` | string | ❌ | - |
| `duration_minutes` | unknown | ❌ | - |
| `requires_followup` | boolean | ❌ | - |
| `followup_date` | unknown | ❌ | - |
| `followup_notes` | unknown | ❌ | - |
| `is_completed` | boolean | ❌ | - |
| `outcome` | unknown | ❌ | - |
| `metadata` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `account_id` | string | ✅ | - |
| `contact_id` | unknown | ❌ | - |
| `tenant_id` | string | ✅ | - |
| `created_by_user_id` | unknown | ❌ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### InteractionUpdate

**Descrição:** Schema for updating an Interaction.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `interaction_type` | unknown | ❌ | - |
| `channel` | unknown | ❌ | - |
| `subject` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `interaction_date` | unknown | ❌ | - |
| `duration_minutes` | unknown | ❌ | - |
| `requires_followup` | unknown | ❌ | - |
| `followup_date` | unknown | ❌ | - |
| `followup_notes` | unknown | ❌ | - |
| `is_completed` | unknown | ❌ | - |
| `outcome` | unknown | ❌ | - |
| `metadata` | unknown | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/modules/crm/crm/interactions/ {#get-apimodulescrmcrminteractions}

**Resumo:** Get Interactions
**Descrição:** Get all CRM interactions with optional filtering.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `account_id` | string | query | ❌ | Filter by account ID |
| `contact_id` | string | query | ❌ | Filter by contact ID |
| `skip` | integer | query | ❌ | Number of interactions to skip |
| `limit` | integer | query | ❌ | Maximum number of interactions to return |
| `interaction_type` | string | query | ❌ | Filter by interaction type |
| `requires_followup` | string | query | ❌ | Filter by requires_followup flag |
| `is_completed` | string | query | ❌ | Filter by is_completed flag |
| `start_date` | string | query | ❌ | Filter by start date (inclusive) |
| `end_date` | string | query | ❌ | Filter by end date (inclusive) |
| `search` | string | query | ❌ | Search term for interaction subject or description |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/interactions/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/crm/crm/interactions/ {#post-apimodulescrmcrminteractions}

**Resumo:** Create Interaction
**Descrição:** Create a new CRM interaction.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [InteractionCreate](#interactioncreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [InteractionRead](#interactionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/crm/crm/interactions/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/crm/crm/interactions/account/{account_id} {#get-apimodulescrmcrminteractionsaccountaccount-id}

**Resumo:** Get Account Interactions
**Descrição:** Get all interactions for a specific account.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `account_id` | string | path | ✅ | The ID of the account to get interactions for |
| `skip` | integer | query | ❌ | Number of interactions to skip |
| `limit` | integer | query | ❌ | Maximum number of interactions to return |
| `interaction_type` | string | query | ❌ | Filter by interaction type |
| `requires_followup` | string | query | ❌ | Filter by requires_followup flag |
| `is_completed` | string | query | ❌ | Filter by is_completed flag |
| `start_date` | string | query | ❌ | Filter by start date (inclusive) |
| `end_date` | string | query | ❌ | Filter by end date (inclusive) |
| `search` | string | query | ❌ | Search term for interaction subject or description |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/interactions/account/{account_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### DELETE /api/modules/crm/crm/interactions/{interaction_id} {#delete-apimodulescrmcrminteractionsinteraction-id}

**Resumo:** Delete Interaction
**Descrição:** Delete a CRM interaction.

Requires tenant owner or manager role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `interaction_id` | string | path | ✅ | The ID of the interaction to delete |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/crm/crm/interactions/{interaction_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/crm/crm/interactions/{interaction_id} {#get-apimodulescrmcrminteractionsinteraction-id}

**Resumo:** Get Interaction
**Descrição:** Get a CRM interaction by ID.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `interaction_id` | string | path | ✅ | The ID of the interaction to retrieve |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [InteractionRead](#interactionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/interactions/{interaction_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/crm/crm/interactions/{interaction_id} {#put-apimodulescrmcrminteractionsinteraction-id}

**Resumo:** Update Interaction
**Descrição:** Update a CRM interaction.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `interaction_id` | string | path | ✅ | The ID of the interaction to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [InteractionUpdate](#interactionupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [InteractionRead](#interactionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/crm/crm/interactions/{interaction_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
