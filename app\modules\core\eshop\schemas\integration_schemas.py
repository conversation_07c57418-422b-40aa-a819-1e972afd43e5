"""
Integration Schemas - Pydantic models for EShop integration APIs.

Defines request and response schemas for all integration endpoints
between EShop and other core e-commerce modules.
"""

import uuid
from typing import Dict, List, Optional, Any, Union
from decimal import Decimal
from datetime import datetime
from pydantic import BaseModel, Field, validator


# =====================================================================
# BASE SCHEMAS
# =====================================================================

class BaseIntegrationRequest(BaseModel):
    """Base schema for integration requests."""
    
    class Config:
        from_attributes = True
        json_encoders = {
            uuid.UUID: str,
            Decimal: float,
            datetime: lambda v: v.isoformat()
        }


class BaseIntegrationResponse(BaseModel):
    """Base schema for integration responses."""
    
    status: str = Field(..., description="Operation status")
    
    class Config:
        from_attributes = True
        json_encoders = {
            uuid.UUID: str,
            Decimal: float,
            datetime: lambda v: v.isoformat()
        }


# =====================================================================
# INVENTORY INTEGRATION SCHEMAS
# =====================================================================

class ProductSyncRequest(BaseIntegrationRequest):
    """Request schema for product inventory sync."""
    
    product_id: uuid.UUID = Field(..., description="Product ID to sync")
    force_update: bool = Field(
        default=False, 
        description="Force update even if already synced"
    )


class ProductSyncResponse(BaseIntegrationResponse):
    """Response schema for product inventory sync."""
    
    inventory_item_id: uuid.UUID = Field(..., description="Inventory item ID")
    current_stock: int = Field(..., description="Current stock level")
    low_stock_threshold: int = Field(..., description="Low stock threshold")
    last_updated: datetime = Field(
        default_factory=datetime.utcnow,
        description="Last sync timestamp"
    )


# =====================================================================
# ORDERS INTEGRATION SCHEMAS
# =====================================================================

class OrderProductData(BaseModel):
    """Product data for order creation."""
    
    product_id: uuid.UUID = Field(..., description="Product ID")
    quantity: int = Field(..., gt=0, description="Quantity to order")
    special_instructions: Optional[str] = Field(
        None, description="Special instructions for this item"
    )


class OrderCreateRequest(BaseIntegrationRequest):
    """Request schema for creating EShop orders."""
    
    customer_id: uuid.UUID = Field(..., description="Customer ID")
    product_data: List[OrderProductData] = Field(
        ..., min_items=1, description="Products to order"
    )
    order_metadata: Optional[Dict[str, Any]] = Field(
        None, description="Additional order metadata"
    )
    delivery_address: Optional[Dict[str, str]] = Field(
        None, description="Delivery address"
    )


class OrderCreateResponse(BaseIntegrationResponse):
    """Response schema for order creation."""
    
    order_id: uuid.UUID = Field(..., description="Created order ID")
    order_number: str = Field(..., description="Order number")
    total: float = Field(..., description="Order total amount")
    estimated_delivery: Optional[str] = Field(
        None, description="Estimated delivery time"
    )


# =====================================================================
# FINANCIAL INTEGRATION SCHEMAS
# =====================================================================

class CommissionCalculationResponse(BaseIntegrationResponse):
    """Response schema for commission calculation."""
    
    product_id: uuid.UUID = Field(..., description="Product ID")
    sale_amount: float = Field(..., description="Sale amount")
    commission_rate: float = Field(..., description="Commission rate applied")
    commission_amount: float = Field(..., description="Commission amount")
    market_type: str = Field(..., description="Product market type")
    calculation_details: Optional[Dict[str, Any]] = Field(
        None, description="Detailed calculation breakdown"
    )


# =====================================================================
# CRM INTEGRATION SCHEMAS
# =====================================================================

class CustomerSyncResponse(BaseIntegrationResponse):
    """Response schema for customer sync."""
    
    customer_id: uuid.UUID = Field(..., description="Customer ID")
    total_orders: int = Field(..., description="Total number of orders")
    total_spent: float = Field(..., description="Total amount spent")
    avg_order_value: float = Field(..., description="Average order value")
    customer_status: str = Field(..., description="Customer status")
    last_order_date: Optional[datetime] = Field(
        None, description="Last order date"
    )


# =====================================================================
# POS INTEGRATION SCHEMAS
# =====================================================================

class POSEnableRequest(BaseIntegrationRequest):
    """Request schema for enabling product in POS."""
    
    product_id: uuid.UUID = Field(..., description="Product ID")
    pos_settings: Optional[Dict[str, Any]] = Field(
        None, description="Custom POS settings"
    )


class POSEnableResponse(BaseIntegrationResponse):
    """Response schema for POS enablement."""
    
    product_id: uuid.UUID = Field(..., description="Product ID")
    pos_enabled: bool = Field(..., description="POS enabled status")
    settings: Dict[str, Any] = Field(..., description="POS settings applied")
    barcode: Optional[str] = Field(None, description="Product barcode")


# =====================================================================
# INTEGRATION STATUS SCHEMAS
# =====================================================================

class IntegrationModuleStatus(BaseModel):
    """Status of individual integration module."""
    
    enabled: bool = Field(..., description="Module enabled status")
    last_sync: Optional[datetime] = Field(
        None, description="Last sync timestamp"
    )
    status: str = Field(..., description="Module status")
    details: Optional[Dict[str, Any]] = Field(
        None, description="Additional module details"
    )


class IntegrationStatusResponse(BaseIntegrationResponse):
    """Response schema for integration status."""
    
    product_id: uuid.UUID = Field(..., description="Product ID")
    product_name: str = Field(..., description="Product name")
    approval_status: str = Field(..., description="Product approval status")
    market_type: str = Field(..., description="Product market type")
    integrations: Dict[str, IntegrationModuleStatus] = Field(
        ..., description="Integration modules status"
    )


# =====================================================================
# BULK OPERATIONS SCHEMAS
# =====================================================================

class BulkSyncRequest(BaseIntegrationRequest):
    """Request schema for bulk sync operations."""
    
    limit: Optional[int] = Field(
        100, gt=0, le=1000, description="Maximum products to sync"
    )
    filter_criteria: Optional[Dict[str, Any]] = Field(
        None, description="Filter criteria for products"
    )
    sync_modules: Optional[List[str]] = Field(
        None, description="Specific modules to sync"
    )


class BulkSyncError(BaseModel):
    """Error details for bulk sync operations."""
    
    product_id: str = Field(..., description="Product ID that failed")
    error: str = Field(..., description="Error message")
    module: Optional[str] = Field(None, description="Module where error occurred")


class BulkSyncResponse(BaseIntegrationResponse):
    """Response schema for bulk sync operations."""
    
    synced_count: int = Field(..., description="Number of products synced")
    total_products: int = Field(..., description="Total products processed")
    errors: List[BulkSyncError] = Field(
        default_factory=list, description="Sync errors"
    )
    message: Optional[str] = Field(None, description="Additional message")
    estimated_completion: Optional[datetime] = Field(
        None, description="Estimated completion time for background tasks"
    )


# =====================================================================
# SHIPPING INTEGRATION SCHEMAS
# =====================================================================

class ShippingCalculationRequest(BaseIntegrationRequest):
    """Request schema for shipping calculation."""
    
    product_ids: List[uuid.UUID] = Field(
        ..., min_items=1, description="Product IDs"
    )
    destination_address: Dict[str, str] = Field(
        ..., description="Destination address"
    )
    shipping_method: Optional[str] = Field(
        None, description="Preferred shipping method"
    )


class ShippingCalculationResponse(BaseIntegrationResponse):
    """Response schema for shipping calculation."""
    
    shipping_cost: float = Field(..., description="Calculated shipping cost")
    estimated_delivery: str = Field(..., description="Estimated delivery time")
    carrier: str = Field(..., description="Shipping carrier")
    breakdown: Dict[str, float] = Field(
        ..., description="Cost breakdown"
    )
    available_methods: Optional[List[Dict[str, Any]]] = Field(
        None, description="Available shipping methods"
    )


# =====================================================================
# REVIEWS INTEGRATION SCHEMAS
# =====================================================================

class ProductReview(BaseModel):
    """Individual product review."""
    
    id: uuid.UUID = Field(..., description="Review ID")
    customer_name: str = Field(..., description="Customer name")
    rating: float = Field(..., ge=1, le=5, description="Rating (1-5)")
    comment: Optional[str] = Field(None, description="Review comment")
    created_at: datetime = Field(..., description="Review date")
    verified_purchase: bool = Field(
        default=False, description="Verified purchase status"
    )


class ProductReviewsResponse(BaseIntegrationResponse):
    """Response schema for product reviews."""
    
    product_id: uuid.UUID = Field(..., description="Product ID")
    total_reviews: int = Field(..., description="Total number of reviews")
    average_rating: float = Field(..., description="Average rating")
    reviews: List[ProductReview] = Field(
        default_factory=list, description="Product reviews"
    )
    rating_distribution: Optional[Dict[str, int]] = Field(
        None, description="Rating distribution (1-5 stars)"
    )


# =====================================================================
# SEO INTEGRATION SCHEMAS
# =====================================================================

class SEOOptimizationResponse(BaseIntegrationResponse):
    """Response schema for SEO optimization."""
    
    product_id: uuid.UUID = Field(..., description="Product ID")
    seo_score: int = Field(..., ge=0, le=100, description="SEO score (0-100)")
    optimizations: List[str] = Field(
        default_factory=list, description="Applied optimizations"
    )
    recommendations: Optional[List[str]] = Field(
        None, description="Additional recommendations"
    )
    meta_data: Optional[Dict[str, str]] = Field(
        None, description="Generated meta data"
    )


# =====================================================================
# WEBHOOK SCHEMAS
# =====================================================================

class WebhookEvent(BaseModel):
    """Webhook event schema."""
    
    event_type: str = Field(..., description="Event type")
    tenant_id: uuid.UUID = Field(..., description="Tenant ID")
    product_id: Optional[uuid.UUID] = Field(None, description="Product ID")
    order_id: Optional[uuid.UUID] = Field(None, description="Order ID")
    data: Dict[str, Any] = Field(..., description="Event data")
    timestamp: datetime = Field(
        default_factory=datetime.utcnow, description="Event timestamp"
    )


# =====================================================================
# ANALYTICS SCHEMAS
# =====================================================================

class IntegrationAnalytics(BaseModel):
    """Integration analytics data."""
    
    total_synced_products: int = Field(..., description="Total synced products")
    sync_success_rate: float = Field(..., description="Sync success rate")
    average_sync_time: float = Field(..., description="Average sync time (seconds)")
    active_integrations: List[str] = Field(
        ..., description="Active integration modules"
    )
    last_24h_syncs: int = Field(..., description="Syncs in last 24 hours")
    error_rate: float = Field(..., description="Error rate percentage")


class IntegrationAnalyticsResponse(BaseIntegrationResponse):
    """Response schema for integration analytics."""
    
    tenant_id: uuid.UUID = Field(..., description="Tenant ID")
    period: str = Field(..., description="Analytics period")
    analytics: IntegrationAnalytics = Field(..., description="Analytics data")
    trends: Optional[Dict[str, List[float]]] = Field(
        None, description="Trend data"
    )


# =====================================================================
# VALIDATION HELPERS
# =====================================================================

class IntegrationValidationError(BaseModel):
    """Integration validation error."""
    
    field: str = Field(..., description="Field with error")
    message: str = Field(..., description="Error message")
    code: str = Field(..., description="Error code")


class IntegrationValidationResponse(BaseIntegrationResponse):
    """Response schema for validation errors."""
    
    errors: List[IntegrationValidationError] = Field(
        ..., description="Validation errors"
    )
    
    @validator('status')
    def status_must_be_error(cls, v):
        if v != 'error':
            raise ValueError('status must be "error" for validation responses')
        return v