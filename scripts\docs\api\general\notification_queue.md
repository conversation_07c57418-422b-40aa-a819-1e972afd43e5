# General - Notification Queue

**Categoria:** General
**Mó<PERSON><PERSON>:** Notification Queue
**Total de Endpoints:** 6
**Gerado em:** 27/06/2025, 20:20:27

## 📋 Endpoints

- [POST /api/modules/core/notifications/queue/cleanup](#post-apimodulescorenotificationsqueuecleanup) - Cleanup Old Entries
- [GET /api/modules/core/notifications/queue/entries](#get-apimodulescorenotificationsqueueentries) - List Queue Entries
- [GET /api/modules/core/notifications/queue/health](#get-apimodulescorenotificationsqueuehealth) - Get Queue Health
- [POST /api/modules/core/notifications/queue/process-batch](#post-apimodulescorenotificationsqueueprocess-batch) - Process Batch
- [POST /api/modules/core/notifications/queue/retry-failed](#post-apimodulescorenotificationsqueueretry-failed) - Retry Failed Notifications
- [GET /api/modules/core/notifications/queue/stats](#get-apimodulescorenotificationsqueuestats) - Get Queue Stats

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

## 🔗 Endpoints Detalhados

### POST /api/modules/core/notifications/queue/cleanup {#post-apimodulescorenotificationsqueuecleanup}

**Resumo:** Cleanup Old Entries
**Descrição:** Remove entradas antigas da fila.

Apenas para administradores.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `days_old` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/queue/cleanup" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/notifications/queue/entries {#get-apimodulescorenotificationsqueueentries}

**Resumo:** List Queue Entries
**Descrição:** Lista entradas da fila de notificações.

Apenas para administradores.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `status` | string | query | ❌ | - |
| `limit` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/notifications/queue/entries" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/notifications/queue/health {#get-apimodulescorenotificationsqueuehealth}

**Resumo:** Get Queue Health
**Descrição:** Verifica a saúde da fila de notificações.

Apenas para administradores.

**🔐 Autenticação:** Requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/notifications/queue/health" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/notifications/queue/process-batch {#post-apimodulescorenotificationsqueueprocess-batch}

**Resumo:** Process Batch
**Descrição:** Processa um lote de notificações da fila.

Apenas para administradores.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `batch_size` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/queue/process-batch" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/notifications/queue/retry-failed {#post-apimodulescorenotificationsqueueretry-failed}

**Resumo:** Retry Failed Notifications
**Descrição:** Recoloca notificações falhadas na fila para retry.

Apenas para administradores.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `max_age_hours` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/queue/retry-failed" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/notifications/queue/stats {#get-apimodulescorenotificationsqueuestats}

**Resumo:** Get Queue Stats
**Descrição:** Obtém estatísticas da fila de notificações.

Apenas para administradores.

**🔐 Autenticação:** Requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/notifications/queue/stats" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
