"""Schemas for Shopping List module."""

from .shopping_list_category import (
    ShoppingListCategoryBase,
    ShoppingListCategoryCreate,
    ShoppingListCategoryUpdate,
    ShoppingListCategoryRead,
)

from .shopping_list import (
    Priority,
    ShoppingListBase,
    ShoppingListCreate,
    Shopping<PERSON>istUpdate,
    ShoppingListRead,
    ShoppingListWithItems,
    ShoppingListItemBase,
    ShoppingListItemCreate,
    ShoppingListItemUpdate,
    ShoppingListItemRead,
    ShoppingListItemTogglePurchased,
    ShoppingListSummary,
    AutoGenerateShoppingListRequest,
)

__all__ = [
    # Category schemas
    "ShoppingListCategoryBase",
    "ShoppingListCategoryCreate",
    "ShoppingListCategoryUpdate",
    "ShoppingListCategoryRead",
    # Shopping list schemas
    "Priority",
    "ShoppingListBase",
    "ShoppingListCreate",
    "ShoppingListUpdate",
    "ShoppingListRead",
    "ShoppingListWithItems",
    "ShoppingListItemBase",
    "ShoppingListItemCreate",
    "ShoppingListItemUpdate",
    "ShoppingList<PERSON>temRead",
    "ShoppingListItemTogglePurchased",
    "ShoppingListSummary",
    "AutoGenerateShoppingListRequest",
]

# Resolve forward references
from .resolve_refs import resolve_shopping_list_refs
resolve_shopping_list_refs()
