"""
Unit tests for RoleManagementService.

Tests B2B role management functionality including:
- TVendor and TCostumer authorization workflow
- Market context switching
- Permission validation
- Business verification
- Role-specific operations
"""

import pytest
import pytest_asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
from uuid import uuid4

from app.modules.core.eshop.services.role_service import RoleManagementService
from app.modules.core.users.models.tenant_user_association import TenantUserAssociation
from app.modules.core.roles.models.roles import TenantRole, MarketContext


class TestRoleManagementService:
    """Unit tests for RoleManagementService."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        session = Mock()
        session.add = Mock()
        session.commit = AsyncMock()
        session.refresh = AsyncMock()
        session.execute = AsyncMock()
        session.scalar = AsyncMock()
        return session

    @pytest.fixture
    def mock_notification_service(self):
        """Mock notification service."""
        service = Mock()
        service.send_role_notification = AsyncMock()
        return service

    @pytest.fixture
    def role_service(self, mock_db_session, mock_notification_service):
        """Create RoleManagementService instance with mocks."""
        return RoleManagementService(
            db_session=mock_db_session,
            notification_service=mock_notification_service
        )

    @pytest.fixture
    def sample_user_association(self):
        """Sample user-tenant association."""
        association = Mock(spec=TenantUserAssociation)
        association.id = str(uuid4())
        association.user_id = str(uuid4())
        association.tenant_id = str(uuid4())
        association.role = TenantRole.CUSTOMER
        association.market_context = MarketContext.B2C
        association.b2b_authorized = False
        association.business_verification_status = "pending"
        return association

    @pytest_asyncio.async_test
    async def test_request_b2b_authorization_success(
        self,
        role_service,
        mock_db_session,
        mock_notification_service,
        sample_user_association
    ):
        """Test successful B2B authorization request."""
        # Arrange
        mock_db_session.scalar.return_value = sample_user_association
        
        request_data = {
            "requested_role": "TVENDOR",
            "business_name": "Test Business Inc.",
            "business_registration_number": "BRN123456789",
            "business_description": "E-commerce vendor specializing in electronics",
            "expected_monthly_volume": 50000.00
        }

        # Act
        result = await role_service.request_b2b_authorization(
            sample_user_association.user_id,
            sample_user_association.tenant_id,
            request_data
        )

        # Assert
        assert result.success is True
        assert result.status == "pending"
        assert sample_user_association.business_name == "Test Business Inc."
        assert sample_user_association.business_registration_number == "BRN123456789"
        mock_db_session.commit.assert_called()
        mock_notification_service.send_role_notification.assert_called()

    @pytest_asyncio.async_test
    async def test_request_b2b_authorization_already_authorized(
        self,
        role_service,
        sample_user_association
    ):
        """Test B2B authorization request for already authorized user."""
        # Arrange
        sample_user_association.b2b_authorized = True
        
        request_data = {
            "requested_role": "TVENDOR",
            "business_name": "Test Business Inc."
        }

        # Act & Assert
        with pytest.raises(ValueError, match="already authorized for B2B"):
            await role_service.request_b2b_authorization(
                sample_user_association.user_id,
                sample_user_association.tenant_id,
                request_data
            )

    @pytest_asyncio.async_test
    async def test_approve_b2b_authorization_success(
        self,
        role_service,
        mock_db_session,
        mock_notification_service,
        sample_user_association
    ):
        """Test successful B2B authorization approval."""
        # Arrange
        sample_user_association.business_verification_status = "pending"
        sample_user_association.staff_sub_role = "TVENDOR"
        mock_db_session.scalar.return_value = sample_user_association
        
        admin_user_id = str(uuid4())
        approval_data = {
            "admin_notes": "Business verification completed successfully",
            "approved_role": "TVENDOR",
            "commission_rate": 0.12,
            "pricing_tier": "standard"
        }

        # Act
        result = await role_service.approve_b2b_authorization(
            sample_user_association.user_id,
            sample_user_association.tenant_id,
            approval_data,
            admin_user_id
        )

        # Assert
        assert result.success is True
        assert sample_user_association.b2b_authorized is True
        assert sample_user_association.role == TenantRole.TVENDOR
        assert sample_user_association.business_verification_status == "approved"
        assert sample_user_association.b2b_authorized_by == admin_user_id
        assert sample_user_association.commission_rate == 0.12
        mock_db_session.commit.assert_called()
        mock_notification_service.send_role_notification.assert_called()

    @pytest_asyncio.async_test
    async def test_reject_b2b_authorization_success(
        self,
        role_service,
        mock_db_session,
        mock_notification_service,
        sample_user_association
    ):
        """Test successful B2B authorization rejection."""
        # Arrange
        sample_user_association.business_verification_status = "pending"
        mock_db_session.scalar.return_value = sample_user_association
        
        admin_user_id = str(uuid4())
        rejection_data = {
            "admin_notes": "Business documentation incomplete",
            "rejection_reason": "insufficient_documentation"
        }

        # Act
        result = await role_service.reject_b2b_authorization(
            sample_user_association.user_id,
            sample_user_association.tenant_id,
            rejection_data,
            admin_user_id
        )

        # Assert
        assert result.success is True
        assert sample_user_association.business_verification_status == "rejected"
        assert sample_user_association.b2b_authorized is False
        assert sample_user_association.b2b_authorized_by == admin_user_id
        mock_db_session.commit.assert_called()
        mock_notification_service.send_role_notification.assert_called()

    @pytest_asyncio.async_test
    async def test_switch_market_context_success(
        self,
        role_service,
        mock_db_session,
        sample_user_association
    ):
        """Test successful market context switching."""
        # Arrange
        sample_user_association.b2b_authorized = True
        sample_user_association.role = TenantRole.TVENDOR
        sample_user_association.market_context = MarketContext.B2C
        mock_db_session.scalar.return_value = sample_user_association

        # Act
        result = await role_service.switch_market_context(
            sample_user_association.user_id,
            sample_user_association.tenant_id,
            MarketContext.B2B
        )

        # Assert
        assert result.success is True
        assert sample_user_association.market_context == MarketContext.B2B
        mock_db_session.commit.assert_called()

    @pytest_asyncio.async_test
    async def test_switch_market_context_unauthorized(
        self,
        role_service,
        sample_user_association
    ):
        """Test market context switching for unauthorized user."""
        # Arrange
        sample_user_association.b2b_authorized = False
        sample_user_association.role = TenantRole.CUSTOMER

        # Act & Assert
        with pytest.raises(ValueError, match="not authorized for B2B"):
            await role_service.switch_market_context(
                sample_user_association.user_id,
                sample_user_association.tenant_id,
                MarketContext.B2B
            )

    @pytest_asyncio.async_test
    async def test_validate_tvendor_permissions_success(
        self,
        role_service,
        sample_user_association
    ):
        """Test TVendor permission validation."""
        # Arrange
        sample_user_association.role = TenantRole.TVENDOR
        sample_user_association.b2b_authorized = True
        sample_user_association.market_context = MarketContext.B2B

        # Act
        result = role_service.validate_tvendor_permissions(
            sample_user_association,
            "create_product"
        )

        # Assert
        assert result is True

    @pytest_asyncio.async_test
    async def test_validate_tvendor_permissions_failure(
        self,
        role_service,
        sample_user_association
    ):
        """Test TVendor permission validation failure."""
        # Arrange
        sample_user_association.role = TenantRole.CUSTOMER
        sample_user_association.b2b_authorized = False

        # Act
        result = role_service.validate_tvendor_permissions(
            sample_user_association,
            "create_product"
        )

        # Assert
        assert result is False

    @pytest_asyncio.async_test
    async def test_validate_tcostumer_permissions_success(
        self,
        role_service,
        sample_user_association
    ):
        """Test TCostumer permission validation."""
        # Arrange
        sample_user_association.role = TenantRole.TCOSTUMER
        sample_user_association.b2b_authorized = True
        sample_user_association.market_context = MarketContext.B2B

        # Act
        result = role_service.validate_tcostumer_permissions(
            sample_user_association,
            "bulk_purchase"
        )

        # Assert
        assert result is True

    @pytest_asyncio.async_test
    async def test_get_pending_b2b_requests(
        self,
        role_service,
        mock_db_session
    ):
        """Test retrieving pending B2B authorization requests."""
        # Arrange
        mock_requests = [
            Mock(
                user_id=str(uuid4()),
                business_name="Business 1",
                staff_sub_role="TVENDOR",
                business_verification_status="pending",
                created_at=datetime.now()
            ),
            Mock(
                user_id=str(uuid4()),
                business_name="Business 2",
                staff_sub_role="TCOSTUMER",
                business_verification_status="pending",
                created_at=datetime.now()
            )
        ]
        
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = mock_requests
        mock_db_session.execute.return_value = mock_result

        # Act
        requests = await role_service.get_pending_b2b_requests(
            tenant_id=str(uuid4()),
            role_filter="all"
        )

        # Assert
        assert len(requests) == 2
        assert requests[0].staff_sub_role == "TVENDOR"
        assert requests[1].staff_sub_role == "TCOSTUMER"

    @pytest_asyncio.async_test
    async def test_get_b2b_users_with_filters(
        self,
        role_service,
        mock_db_session
    ):
        """Test retrieving B2B users with filters."""
        # Arrange
        mock_users = [
            Mock(
                user_id=str(uuid4()),
                role=TenantRole.TVENDOR,
                business_name="Vendor Corp",
                b2b_authorized=True,
                commission_rate=0.15
            ),
            Mock(
                user_id=str(uuid4()),
                role=TenantRole.TCOSTUMER,
                business_name="Customer LLC",
                b2b_authorized=True,
                pricing_tier="premium"
            )
        ]
        
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = mock_users
        mock_db_session.execute.return_value = mock_result

        # Act
        users = await role_service.get_b2b_users(
            tenant_id=str(uuid4()),
            role_filter="TVENDOR",
            verification_status="approved"
        )

        # Assert
        assert len(users) == 2
        mock_db_session.execute.assert_called()

    @pytest_asyncio.async_test
    async def test_update_business_verification_status(
        self,
        role_service,
        mock_db_session,
        sample_user_association
    ):
        """Test updating business verification status."""
        # Arrange
        mock_db_session.scalar.return_value = sample_user_association
        
        verification_data = {
            "verification_status": "verified",
            "verification_notes": "All documents verified",
            "verification_date": datetime.now(),
            "verified_by": str(uuid4())
        }

        # Act
        result = await role_service.update_business_verification(
            sample_user_association.user_id,
            sample_user_association.tenant_id,
            verification_data
        )

        # Assert
        assert result.success is True
        assert sample_user_association.business_verification_status == "verified"
        mock_db_session.commit.assert_called()

    @pytest_asyncio.async_test
    async def test_bulk_role_assignment(
        self,
        role_service,
        mock_db_session,
        mock_notification_service
    ):
        """Test bulk role assignment for multiple users."""
        # Arrange
        user_ids = [str(uuid4()), str(uuid4()), str(uuid4())]
        tenant_id = str(uuid4())
        admin_user_id = str(uuid4())
        
        mock_associations = []
        for uid in user_ids:
            association = Mock()
            association.user_id = uid
            association.tenant_id = tenant_id
            association.b2b_authorized = False
            mock_associations.append(association)
        
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = mock_associations
        mock_db_session.execute.return_value = mock_result

        assignment_data = {
            "role": "TCOSTUMER",
            "commission_rate": 0.10,
            "pricing_tier": "standard",
            "admin_notes": "Bulk assignment for corporate customers"
        }

        # Act
        result = await role_service.bulk_assign_b2b_roles(
            user_ids,
            tenant_id,
            assignment_data,
            admin_user_id
        )

        # Assert
        assert result.success is True
        assert result.assigned_count == 3
        for association in mock_associations:
            assert association.role == TenantRole.TCOSTUMER
            assert association.b2b_authorized is True
        mock_db_session.commit.assert_called()

    @pytest_asyncio.async_test
    async def test_role_hierarchy_validation(
        self,
        role_service,
        sample_user_association
    ):
        """Test role hierarchy validation."""
        # Arrange
        sample_user_association.role = TenantRole.MANAGER

        # Act & Assert
        # Manager can assign TVENDOR role
        assert role_service.can_assign_role(
            sample_user_association.role, 
            TenantRole.TVENDOR
        ) is True
        
        # TVENDOR cannot assign MANAGER role
        sample_user_association.role = TenantRole.TVENDOR
        assert role_service.can_assign_role(
            sample_user_association.role,
            TenantRole.MANAGER
        ) is False

    @pytest_asyncio.async_test
    async def test_commission_rate_validation(
        self,
        role_service
    ):
        """Test commission rate validation for different roles."""
        # Valid rates
        assert role_service.validate_commission_rate(0.15, "TVENDOR") is True
        assert role_service.validate_commission_rate(0.05, "TCOSTUMER") is True
        
        # Invalid rates
        assert role_service.validate_commission_rate(0.50, "TVENDOR") is False  # Too high
        assert role_service.validate_commission_rate(-0.01, "TCOSTUMER") is False  # Negative

    @pytest_asyncio.async_test
    async def test_pricing_tier_management(
        self,
        role_service,
        mock_db_session,
        sample_user_association
    ):
        """Test pricing tier management for TCostumer."""
        # Arrange
        sample_user_association.role = TenantRole.TCOSTUMER
        sample_user_association.pricing_tier = "standard"
        mock_db_session.scalar.return_value = sample_user_association

        # Act
        result = await role_service.update_pricing_tier(
            sample_user_association.user_id,
            sample_user_association.tenant_id,
            "premium"
        )

        # Assert
        assert result.success is True
        assert sample_user_association.pricing_tier == "premium"
        mock_db_session.commit.assert_called()

    def test_market_context_validation(self, role_service):
        """Test market context validation."""
        # Valid contexts
        assert role_service.is_valid_market_context("B2B") is True
        assert role_service.is_valid_market_context("B2C") is True
        assert role_service.is_valid_market_context("MARKETPLACE") is True
        
        # Invalid contexts
        assert role_service.is_valid_market_context("INVALID") is False
        assert role_service.is_valid_market_context("") is False

    @pytest_asyncio.async_test
    async def test_role_statistics_generation(
        self,
        role_service,
        mock_db_session
    ):
        """Test generation of role statistics."""
        # Arrange
        tenant_id = str(uuid4())
        
        mock_stats = Mock()
        mock_stats.total_b2b_users = 25
        mock_stats.tvendor_count = 15
        mock_stats.tcostumer_count = 10
        mock_stats.pending_requests = 5
        mock_db_session.scalar.return_value = mock_stats

        # Act
        stats = await role_service.get_role_statistics(tenant_id)

        # Assert
        assert stats.total_b2b_users == 25
        assert stats.tvendor_count == 15
        assert stats.tcostumer_count == 10
        assert stats.pending_requests == 5 