"""
Commission Settings Models
=========================

Modelos para configuração de comissões do sistema EShop.
"""

import uuid
import enum
from datetime import datetime
from typing import TYPE_CHECKING, Optional

from sqlalchemy import (
    Column, String, ForeignKey, Text, Enum, DateTime, 
    Boolean, Index, func, Numeric
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base
from app.core.enums import MarketType

# Use TYPE_CHECKING to handle circular dependencies
if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.core.users.models.user import User


class CommissionType(str, enum.Enum):
    """Enum para tipos de comissão."""
    
    PERCENTAGE = "percentage"
    FIXED = "fixed"
    TIERED = "tiered"


# MarketType agora importado de app.core.enums


class CommissionSettings(Base):
    """
    Configurações de comissão para vendedores no EShop.
    
    Define taxas de comissão personalizadas por vendedor,
    categoria ou tipo de mercado.
    """
    
    __tablename__ = "eshop_commission_settings"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("tenants.id"), 
        nullable=False, 
        index=True
    )
    
    # Vendedor ou configuração global
    vendor_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=True,  # NULL = configuração global
        index=True
    )
    
    # Configurações de comissão
    commission_type = Column(Enum(CommissionType), nullable=False, default=CommissionType.PERCENTAGE)
    commission_rate = Column(Numeric(5, 2), nullable=False)  # 0.00-99.99%
    fixed_amount = Column(Numeric(10, 2), nullable=True)  # Para comissão fixa
    
    # Configurações por tipo de mercado
    market_type = Column(Enum(MarketType), nullable=True)  # NULL = todos os tipos
    
    # Configurações por categoria
    category_id = Column(
        UUID(as_uuid=True),
        ForeignKey("eshop_product_categories.id", use_alter=True, name="fk_commission_settings_category_id"),
        nullable=True,  # NULL = todas as categorias
        index=True
    )
    
    # Limites e condições
    min_order_value = Column(Numeric(10, 2), nullable=True)  # Valor mínimo do pedido
    max_commission_amount = Column(Numeric(10, 2), nullable=True)  # Comissão máxima
    
    # Configurações de tempo
    valid_from = Column(DateTime(timezone=True), nullable=True)
    valid_until = Column(DateTime(timezone=True), nullable=True)
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    is_default = Column(Boolean, default=False, nullable=False)  # Configuração padrão
    
    # Metadados
    description = Column(Text, nullable=True)
    internal_notes = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Relacionamentos
    tenant = relationship("app.modules.core.tenants.models.tenant.Tenant", viewonly=True)
    vendor = relationship("app.modules.core.users.models.user.User", foreign_keys=[vendor_id], viewonly=True)
    category = relationship("app.modules.core.eshop.models.product_category.ProductCategory", viewonly=True)
    creator = relationship("app.modules.core.users.models.user.User", foreign_keys=[created_by], viewonly=True)
    
    # Índices para performance
    __table_args__ = (
        Index("ix_commission_settings_tenant_vendor", "tenant_id", "vendor_id"),
        Index("ix_commission_settings_tenant_market", "tenant_id", "market_type"),
        Index("ix_commission_settings_tenant_category", "tenant_id", "category_id"),
        Index("ix_commission_settings_active", "is_active", "valid_from", "valid_until"),
        Index("ix_commission_settings_default", "tenant_id", "is_default"),
    )
    
    def __repr__(self):
        return (
            f"<CommissionSettings(id={self.id}, "
            f"vendor_id={self.vendor_id}, "
            f"rate={self.commission_rate}%)>"
        )
    
    @property
    def is_currently_valid(self) -> bool:
        """Verifica se a configuração está válida no momento atual."""
        now = datetime.utcnow()
        
        if not self.is_active:
            return False
            
        if self.valid_from and now < self.valid_from:
            return False
            
        if self.valid_until and now > self.valid_until:
            return False
            
        return True
    
    def calculate_commission(self, sale_amount: float) -> float:
        """Calcula a comissão baseada no valor da venda."""
        if not self.is_currently_valid:
            return 0.0
            
        if self.min_order_value and sale_amount < float(self.min_order_value):
            return 0.0
            
        if self.commission_type == CommissionType.PERCENTAGE:
            commission = sale_amount * (float(self.commission_rate) / 100)
        elif self.commission_type == CommissionType.FIXED:
            commission = float(self.fixed_amount or 0)
        else:  # TIERED - implementar lógica específica se necessário
            commission = sale_amount * (float(self.commission_rate) / 100)
            
        # Aplicar limite máximo se definido
        if self.max_commission_amount:
            commission = min(commission, float(self.max_commission_amount))
            
        return commission


class CommissionTransaction(Base):
    """
    Registro de transações de comissão.
    
    Armazena o histórico de comissões calculadas e pagas
    para auditoria e controle financeiro.
    """
    
    __tablename__ = "eshop_commission_transactions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("tenants.id"), 
        nullable=False, 
        index=True
    )
    
    # Referências
    order_id = Column(
        UUID(as_uuid=True),
        ForeignKey("orders.id"),
        nullable=False,
        index=True
    )
    product_id = Column(
        UUID(as_uuid=True),
        ForeignKey("eshop_products.id"),
        nullable=False,
        index=True
    )
    vendor_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        index=True
    )
    commission_settings_id = Column(
        UUID(as_uuid=True),
        ForeignKey("eshop_commission_settings.id"),
        nullable=False,
        index=True
    )
    
    # Valores da transação
    sale_amount = Column(Numeric(10, 2), nullable=False)
    commission_rate = Column(Numeric(5, 2), nullable=False)
    commission_amount = Column(Numeric(10, 2), nullable=False)
    
    # Status do pagamento
    payment_status = Column(String(20), default="pending", nullable=False)  # pending, paid, cancelled
    payment_date = Column(DateTime(timezone=True), nullable=True)
    payment_reference = Column(String(100), nullable=True)
    
    # Metadados
    calculation_details = Column(Text, nullable=True)  # JSON com detalhes do cálculo
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relacionamentos
    tenant = relationship("app.modules.core.tenants.models.tenant.Tenant", viewonly=True)
    order = relationship("app.modules.core.functions.orders.models.order.Order", viewonly=True)
    product = relationship("app.modules.core.eshop.models.product.Product", viewonly=True)
    vendor = relationship("app.modules.core.users.models.user.User", viewonly=True)
    commission_settings = relationship("CommissionSettings", viewonly=True)
    
    # Índices para performance
    __table_args__ = (
        Index("ix_commission_transactions_tenant_vendor", "tenant_id", "vendor_id"),
        Index("ix_commission_transactions_order", "order_id"),
        Index("ix_commission_transactions_payment_status", "payment_status", "created_at"),
        Index("ix_commission_transactions_vendor_status", "vendor_id", "payment_status"),
    )
    
    def __repr__(self):
        return (
            f"<CommissionTransaction(id={self.id}, "
            f"vendor_id={self.vendor_id}, "
            f"amount={self.commission_amount})>"
        )