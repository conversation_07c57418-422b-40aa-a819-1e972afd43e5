# Blog - Posts

**Categoria:** Blog
**Mó<PERSON><PERSON>:** Posts
**Total de Endpoints:** 7
**Gerado em:** 27/06/2025, 20:20:26

## 📋 Endpoints

- [GET /api/modules/core/blog/posts/](#get-apimodulescoreblogposts) - Get Posts
- [POST /api/modules/core/blog/posts/](#post-apimodulescoreblogposts) - Create Post
- [GET /api/modules/core/blog/posts/slug/{slug}](#get-apimodulescoreblogpostsslugslug) - Get Post By Slug
- [DELETE /api/modules/core/blog/posts/{post_id}](#delete-apimodulescoreblogpostspost-id) - Delete Post
- [GET /api/modules/core/blog/posts/{post_id}](#get-apimodulescoreblogpostspost-id) - Get Post
- [PUT /api/modules/core/blog/posts/{post_id}](#put-apimodulescoreblogpostspost-id) - Update Post
- [GET /api/modules/core/blog/posts/{post_id}/related](#get-apimodulescoreblogpostspost-idrelated) - Get Related Posts

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### BlogPostCreate

**Descrição:** Schema for creating blog posts.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `slug` | string | ✅ | - |
| `author_id` | string | ✅ | - |
| `category_id` | unknown | ❌ | - |
| `featured_image_id` | unknown | ❌ | - |
| `status` | string | ❌ | - |
| `visibility` | string | ❌ | - |
| `is_featured` | boolean | ❌ | - |
| `published_at` | unknown | ❌ | - |
| `scheduled_at` | unknown | ❌ | - |
| `tags` | unknown | ❌ | - |
| `translations` | Array[BlogPostTranslationCreate] | ✅ | - |

### BlogPostRead

**Descrição:** Schema for reading blog posts.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `slug` | string | ✅ | - |
| `author_id` | string | ✅ | - |
| `category_id` | unknown | ❌ | - |
| `featured_image_id` | unknown | ❌ | - |
| `status` | string | ❌ | - |
| `visibility` | string | ❌ | - |
| `is_featured` | boolean | ❌ | - |
| `published_at` | unknown | ❌ | - |
| `scheduled_at` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `view_count` | integer | ✅ | - |
| `like_count` | integer | ✅ | - |
| `comment_count` | integer | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `author` | unknown | ❌ | - |
| `category` | unknown | ❌ | - |
| `tags` | Array[BlogTagRead] | ❌ | - |
| `translations` | Array[BlogPostTranslationRead] | ❌ | - |
| `seo` | unknown | ❌ | - |
| `comments` | Array[BlogCommentRead] | ❌ | - |
| `content` | unknown | ❌ | - |

### BlogPostUpdate

**Descrição:** Schema for updating blog posts.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `slug` | unknown | ❌ | - |
| `author_id` | unknown | ❌ | - |
| `category_id` | unknown | ❌ | - |
| `featured_image_id` | unknown | ❌ | - |
| `status` | unknown | ❌ | - |
| `is_featured` | unknown | ❌ | - |
| `visibility` | unknown | ❌ | - |
| `published_at` | unknown | ❌ | - |
| `scheduled_at` | unknown | ❌ | - |
| `tags` | unknown | ❌ | - |
| `translations` | unknown | ❌ | - |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/modules/core/blog/posts/ {#get-apimodulescoreblogposts}

**Resumo:** Get Posts
**Descrição:** Get blog posts with filtering and pagination.

Supports filtering by status, category, tag, author, language, and featured status.
Results are paginated and can be ordered by various fields.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | Number of posts to skip |
| `limit` | integer | query | ❌ | Number of posts to return |
| `status` | string | query | ❌ | Filter by post status |
| `category_id` | string | query | ❌ | Filter by category |
| `tag_id` | string | query | ❌ | Filter by tag |
| `author_id` | string | query | ❌ | Filter by author |
| `language` | string | query | ❌ | Language code |
| `is_featured` | string | query | ❌ | Filter featured posts |
| `order_by` | string | query | ❌ | Field to order by |
| `order_direction` | string | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/posts/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/blog/posts/ {#post-apimodulescoreblogposts}

**Resumo:** Create Post
**Descrição:** Create a new blog post.

Requires authentication. The post will be created with the specified author.
Must include at least one translation.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [BlogPostCreate](#blogpostcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogPostRead](#blogpostread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/blog/posts/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/blog/posts/slug/{slug} {#get-apimodulescoreblogpostsslugslug}

**Resumo:** Get Post By Slug
**Descrição:** Get a specific blog post by slug.

Optionally filter translations to a specific language.
Automatically increments view count.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `slug` | string | path | ✅ | - |
| `language` | string | query | ❌ | Language code |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogPostRead](#blogpostread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/posts/slug/{slug}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### DELETE /api/modules/core/blog/posts/{post_id} {#delete-apimodulescoreblogpostspost-id}

**Resumo:** Delete Post
**Descrição:** Delete a blog post.

Requires authentication. Only the author or admin can delete a post.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `post_id` | string | path | ✅ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/core/blog/posts/{post_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/blog/posts/{post_id} {#get-apimodulescoreblogpostspost-id}

**Resumo:** Get Post
**Descrição:** Get a specific blog post by ID.

Optionally filter translations to a specific language.
Automatically increments view count.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `post_id` | string | path | ✅ | - |
| `language` | string | query | ❌ | Language code |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogPostRead](#blogpostread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/posts/{post_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/core/blog/posts/{post_id} {#put-apimodulescoreblogpostspost-id}

**Resumo:** Update Post
**Descrição:** Update a blog post.

Requires authentication. Only the author or admin can update a post.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `post_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [BlogPostUpdate](#blogpostupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogPostRead](#blogpostread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/core/blog/posts/{post_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/blog/posts/{post_id}/related {#get-apimodulescoreblogpostspost-idrelated}

**Resumo:** Get Related Posts
**Descrição:** Get related blog posts.

Returns posts that share tags or category with the specified post.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `post_id` | string | path | ✅ | - |
| `limit` | integer | query | ❌ | Number of related posts |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/posts/{post_id}/related"
```

---
