import uuid
import logging
from typing import Optional, Annotated

from fastapi import Depends, HTTPException, Request, status, Header
from sqlalchemy.ext.asyncio import AsyncSession

from app.modules.core.tenants.models.tenant import Tenant
from app.core.db_dependencies import get_db
from app.modules.core.tenants.services.tenant_service import tenant_service

logger = logging.getLogger(__name__)


async def get_current_tenant(request: Request, db: AsyncSession = Depends(get_db)) -> Tenant:
    """
    FastAPI dependency to get the current tenant from the X-Tenant-ID header.

    Raises HTTPException if the header is missing, invalid, or if the tenant
    is not found or inactive.

    Args:
        request: The current request object.
        db: The async database session.

    Returns:
        The Tenant object corresponding to the ID in the header.

    Raises:
        HTTPException:
            - 400 Bad Request: If the X-Tenant-ID header is missing or invalid.
            - 404 Not Found: If the tenant is not found or not active.
    """
    tenant_id_str = request.headers.get("X-Tenant-ID")
    if not tenant_id_str:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="X-Tenant-ID header missing",
        )

    try:
        tenant_uuid = uuid.UUID(tenant_id_str)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid X-Tenant-ID format",
        )

    tenant = await tenant_service.get_tenant(db=db, tenant_id=tenant_uuid)

    if not tenant or not tenant.is_active:  # Ensure tenant exists AND is active
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tenant not found or not active",
        )

    return tenant


async def get_current_tenant_from_header(
    x_tenant_id: Annotated[Optional[str], Header(alias="X-Tenant-ID")] = None,
    db: Annotated[AsyncSession, Depends(get_db)] = None,
) -> Optional[Tenant]:
    """
    FastAPI dependency to get the current tenant from the X-Tenant-ID header.
    Used in routes that operate in the context of a specific tenant for an authenticated user.
    Raises HTTPException 400 if X-Tenant-ID is missing or malformed.
    Raises HTTPException 404 if the tenant is not found or inactive.
    """
    if not x_tenant_id:
        logger.warning("get_current_tenant_from_header: X-Tenant-ID header missing.")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="X-Tenant-ID header is required.",
        )

    try:
        tenant_uuid = uuid.UUID(x_tenant_id)
    except ValueError:
        logger.warning(
            f"get_current_tenant_from_header: X-Tenant-ID '{x_tenant_id}' is not a valid UUID."
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid X-Tenant-ID.",
        )

    # db will not be None here since x_tenant_id is not None
    tenant = await tenant_service.get_tenant(db, tenant_id=tenant_uuid)

    if not tenant:
        logger.warning(
            f"get_current_tenant_from_header: Tenant with ID '{tenant_uuid}' " f"not found."
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tenant not found.",
        )

    if not tenant.is_active:
        logger.warning(
            f"get_current_tenant_from_header: Tenant '{tenant_uuid}' "
            f"(ID: {tenant.id}) is inactive."
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Tenant is inactive.",
        )

    return tenant


async def get_current_tenant_by_slug_or_id(
    tenant_id_or_slug: str, db: Annotated[AsyncSession, Depends(get_db)]
) -> Tenant:
    """
    FastAPI dependency to get the current tenant from a slug or ID.

    Validates if the tenant exists and is active.
    Raises HTTPException 404 if the tenant is not found or inactive.
    """
    tenant = await tenant_service.get_tenant_by_slug_or_id(db, tenant_id_or_slug)

    if not tenant:
        logger.warning(
            f"get_current_tenant_by_slug_or_id: Tenant with slug/ID '{tenant_id_or_slug}' "
            f"not found."
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tenant not found",
        )

    if not tenant.is_active:
        logger.warning(
            f"get_current_tenant_by_slug_or_id: Tenant '{tenant_id_or_slug}' "
            f"(ID: {tenant.id}) is inactive."
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,  # Or 404 to not reveal existence
            detail="Tenant inactive",
        )

    logger.info(
        f"get_current_tenant_by_slug_or_id: Tenant '{tenant_id_or_slug}' "
        f"(ID: {tenant.id}) validated."
    )
    return tenant
