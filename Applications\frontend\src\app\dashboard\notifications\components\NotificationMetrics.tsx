'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  ChartBarIcon,
  EyeIcon,
  CursorArrowRaysIcon,
  PaperAirplaneIcon,
  ExclamationTriangleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

import { useAuth } from '@/lib/auth/AuthProvider';
import { apiClient } from '@/lib/api/client';

interface MetricsData {
  total_sent: number;
  total_delivered: number;
  total_read: number;
  total_clicked: number;
  delivery_rate: number;
  open_rate: number;
  click_through_rate: number;
  engagement_score?: number;
}

interface SystemMetrics {
  total_notifications: number;
  active_notifications: number;
  queue_size: number;
  failed_notifications: number;
  error_rate: number;
}

export default function NotificationMetrics() {
  const { user } = useAuth();
  const [metrics, setMetrics] = useState<MetricsData | null>(null);
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [period, setPeriod] = useState(30);

  const loadMetrics = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      if (user?.system_role === 'admin') {
        // Carrega métricas do sistema para admin
        const [metricsResponse, systemResponse] = await Promise.all([
          apiClient.get(`/modules/core/notifications/metrics/my-metrics?period_days=${period}`),
          apiClient.get('/modules/core/notifications/metrics/system')
        ]);
        
        setMetrics(metricsResponse.data.sent);
        setSystemMetrics(systemResponse.data);
      } else {
        // Carrega métricas do usuário/tenant
        const response = await apiClient.get(`/modules/core/notifications/metrics/my-metrics?period_days=${period}`);
        setMetrics(response.data.sent);
      }
    } catch (err) {
      console.error('Erro ao carregar métricas:', err);
      setError('Erro ao carregar métricas');
    } finally {
      setLoading(false);
    }
  }, [user, period]);

  useEffect(() => {
    loadMetrics();
  }, [loadMetrics]);

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatPercentage = (num: number) => {
    return `${num.toFixed(1)}%`;
  };

  if (loading) {
    return (
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="flex items-center text-red-600">
          <ExclamationTriangleIcon className="h-5 w-5 mr-2" />
          <span>{error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <ChartBarIcon className="h-5 w-5 text-gray-500" />
          <h3 className="text-lg font-medium text-gray-900">
            {user?.system_role === 'admin' ? 'Métricas do Sistema' : 'Minhas Métricas'}
          </h3>
        </div>
        
        <div className="flex items-center space-x-2">
          <label className="text-sm text-gray-600">Período:</label>
          <select
            value={period}
            onChange={(e) => setPeriod(parseInt(e.target.value))}
            className="text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value={7}>7 dias</option>
            <option value={30}>30 dias</option>
            <option value={90}>90 dias</option>
          </select>
        </div>
      </div>

      {/* Métricas do Sistema (apenas admin) */}
      {user?.system_role === 'admin' && systemMetrics && (
        <div className="mb-6">
          <h4 className="text-md font-medium text-gray-800 mb-3">Sistema</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <PaperAirplaneIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-blue-900">Total</p>
                  <p className="text-lg font-semibold text-blue-900">
                    {formatNumber(systemMetrics.total_notifications)}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <EyeIcon className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-green-900">Ativas</p>
                  <p className="text-lg font-semibold text-green-900">
                    {formatNumber(systemMetrics.active_notifications)}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ClockIcon className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-yellow-900">Na Fila</p>
                  <p className="text-lg font-semibold text-yellow-900">
                    {formatNumber(systemMetrics.queue_size)}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-red-900">Falhas</p>
                  <p className="text-lg font-semibold text-red-900">
                    {formatNumber(systemMetrics.failed_notifications)}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ChartBarIcon className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-purple-900">Taxa de Erro</p>
                  <p className="text-lg font-semibold text-purple-900">
                    {formatPercentage(systemMetrics.error_rate)}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Métricas de Engajamento */}
      {metrics && (
        <div>
          <h4 className="text-md font-medium text-gray-800 mb-3">
            {user?.system_role === 'admin' ? 'Minhas Notificações' : 'Engajamento'}
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <PaperAirplaneIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-blue-900">Enviadas</p>
                  <p className="text-lg font-semibold text-blue-900">
                    {formatNumber(metrics.total_sent)}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <EyeIcon className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-green-900">Visualizações</p>
                  <p className="text-lg font-semibold text-green-900">
                    {formatNumber(metrics.total_read)}
                  </p>
                  <p className="text-xs text-green-700">
                    {formatPercentage(metrics.open_rate)} taxa de abertura
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CursorArrowRaysIcon className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-purple-900">Cliques</p>
                  <p className="text-lg font-semibold text-purple-900">
                    {formatNumber(metrics.total_clicked)}
                  </p>
                  <p className="text-xs text-purple-700">
                    {formatPercentage(metrics.click_through_rate)} CTR
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ChartBarIcon className="h-6 w-6 text-orange-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-orange-900">Entrega</p>
                  <p className="text-lg font-semibold text-orange-900">
                    {formatNumber(metrics.total_delivered)}
                  </p>
                  <p className="text-xs text-orange-700">
                    {formatPercentage(metrics.delivery_rate)} taxa de entrega
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Score de Engajamento (se disponível) */}
          {metrics.engagement_score !== undefined && (
            <div className="mt-4 p-4 bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h5 className="text-sm font-medium text-gray-900">Score de Engajamento</h5>
                  <p className="text-xs text-gray-600">
                    Baseado na taxa de abertura e cliques
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold text-blue-600">
                    {metrics.engagement_score.toFixed(1)}
                  </p>
                  <p className="text-xs text-gray-600">de 100</p>
                </div>
              </div>
              
              {/* Barra de progresso */}
              <div className="mt-3">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${Math.min(metrics.engagement_score, 100)}%` }}
                  ></div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Botão de Atualizar */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <button
          onClick={loadMetrics}
          className="text-sm text-blue-600 hover:text-blue-800 flex items-center space-x-1"
        >
          <span>Atualizar Métricas</span>
        </button>
      </div>
    </div>
  );
}
