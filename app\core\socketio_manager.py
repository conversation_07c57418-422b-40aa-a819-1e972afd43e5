"""
Gerenciador centralizado para Socket.IO.

Este módulo fornece uma interface unificada para gerenciar conexões Socket.IO,
registrar namespaces e emitir eventos.
"""

import logging  # noqa: E402
import socketio
from typing import Any, Optional, Union
from uuid import UUID

# Configuração de logging
logger = logging.getLogger(__name__)

# Criar a instância do servidor Socket.IO assíncrono
sio_server = socketio.AsyncServer(
    async_mode="asgi",
    cors_allowed_origins="*",  # Permitir todas as origens para desenvolvimento
    logger=True,
    engineio_logger=True
)

# Criar a aplicação ASGI que envolve o servidor Socket.IO
sio_app = socketio.ASGIApp(socketio_server=sio_server, socketio_path="socket.io")

# Dicionário para armazenar namespaces registrados
registered_namespaces = {}


class SocketIOManager:
    """
    Gerenciador para operações Socket.IO.
    """

    @staticmethod
    def register_namespace(namespace_class, namespace_path):
        """
        Registra um namespace no servidor Socket.IO.

        Args:
            namespace_class: A classe do namespace a ser registrada
            namespace_path: O caminho do namespace (ex: '/kds')
        """
        if namespace_path not in registered_namespaces:
            sio_server.register_namespace(namespace_class(namespace_path))
            registered_namespaces[namespace_path] = namespace_class
            logger.info(f"Namespace {namespace_path} registrado com sucesso")
        else:
            logger.warning(f"Namespace {namespace_path} já está registrado")

    @staticmethod
    async def emit_to_room(room: str, event: str, data: Any, namespace: Optional[str] = None):
        """
        Emite um evento para uma sala específica.

        Args:
            room: O nome da sala
            event: O nome do evento
            data: Os dados a serem enviados
            namespace: O namespace (opcional)
        """
        try:
            await sio_server.emit(event, data, room=room, namespace=namespace)
            namespace_info = f" no namespace {namespace}" if namespace else ""
            logger.debug(f"Evento '{event}' emitido para sala '{room}'{namespace_info}")
            return True
        except Exception as e:
            logger.error(f"Erro ao emitir evento '{event}' para sala '{room}': {e}")
            return False

    @staticmethod
    async def emit_to_tenant(
        tenant_id: Union[str, UUID],
        event: str,
        data: Any,
        namespace: Optional[str] = None,
    ):
        """
        Emite um evento para todos os clientes conectados na sala de um tenant específico.

        Args:
            tenant_id: O ID do tenant
            event: O nome do evento
            data: Os dados a serem enviados
            namespace: O namespace (opcional)
        """
        tenant_room = f"tenant_{tenant_id}"
        return await SocketIOManager.emit_to_room(tenant_room, event, data, namespace)

    @staticmethod
    async def emit_to_user(
        user_id: Union[str, UUID],
        event: str,
        data: Any,
        namespace: Optional[str] = None,
    ):
        """
        Emite um evento para um usuário específico.

        Args:
            user_id: O ID do usuário
            event: O nome do evento
            data: Os dados a serem enviados
            namespace: O namespace (opcional)
        """
        user_room = f"user_{user_id}"
        return await SocketIOManager.emit_to_room(user_room, event, data, namespace)

    @staticmethod
    async def emit_to_delivery_boy(delivery_boy_id: Union[str, UUID], event: str, data: Any):
        """
        Emite um evento para um entregador específico no namespace /delivery.

        Args:
            delivery_boy_id: O ID do entregador
            event: O nome do evento
            data: Os dados a serem enviados
        """
        delivery_boy_room = f"delivery_boy_{delivery_boy_id}"
        return await SocketIOManager.emit_to_room(
            delivery_boy_room, event, data, namespace="/delivery"
        )

    @staticmethod
    async def emit_to_kds(tenant_id: Union[str, UUID], event: str, data: Any):
        """
        Emite um evento para o KDS de um tenant específico.

        Args:
            tenant_id: O ID do tenant
            event: O nome do evento
            data: Os dados a serem enviados
        """
        kds_room = f"kds_{tenant_id}"
        return await SocketIOManager.emit_to_room(kds_room, event, data, namespace="/kds")


# Exportar instâncias e classes
__all__ = ["sio_server", "sio_app", "SocketIOManager"]
