"""
Invoice API Endpoints for B2B Invoice Management
===============================================

Endpoints para gestão de faturas entre TVendorSupplier e TCostumer.
"""

import uuid
from typing import Optional, List
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Query
from fastapi.responses import FileResponse
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_db
from app.core.auth import get_current_user
from app.modules.core.users.models.user import User
from .service import InvoiceService
from .schemas import (
    InvoiceCreate, InvoiceUpdate, InvoicePaymentUpdate,
    InvoiceRead, InvoiceResponse, InvoiceListResponse,
    InvoiceStats, InvoiceDownloadRequest
)
from .models import InvoiceStatus

router = APIRouter(prefix="/invoices", tags=["invoices"])


@router.post("/", response_model=InvoiceResponse)
async def create_invoice(
    invoice_data: InvoiceCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Cria nova fatura.
    
    Apenas TVendorSupplier pode criar faturas.
    """
    service = InvoiceService(db)
    
    # TODO: Verificar se usuário é TVendorSupplier
    vendor_id = str(current_user.id)  # Simplificado por enquanto
    tenant_id = str(current_user.tenant_id)
    
    try:
        invoice = await service.create_invoice(
            invoice_data=invoice_data,
            vendor_id=vendor_id,
            tenant_id=tenant_id
        )
        
        return InvoiceResponse(
            success=True,
            message="Invoice created successfully",
            invoice=InvoiceRead.from_orm(invoice)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/{invoice_id}/upload", response_model=InvoiceResponse)
async def upload_invoice_file(
    invoice_id: str,
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Faz upload do arquivo da fatura.
    
    Apenas o TVendorSupplier proprietário pode fazer upload.
    """
    service = InvoiceService(db)
    
    # TODO: Verificar se usuário é TVendorSupplier
    vendor_id = str(current_user.id)
    
    try:
        invoice = await service.upload_invoice_file(
            invoice_id=invoice_id,
            file=file,
            vendor_id=vendor_id
        )
        
        return InvoiceResponse(
            success=True,
            message="File uploaded successfully",
            invoice=InvoiceRead.from_orm(invoice)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/", response_model=InvoiceListResponse)
async def list_invoices(
    vendor_id: Optional[str] = Query(None, description="Filter by vendor ID"),
    customer_id: Optional[str] = Query(None, description="Filter by customer ID"),
    status: Optional[InvoiceStatus] = Query(None, description="Filter by status"),
    overdue_only: bool = Query(False, description="Show only overdue invoices"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Lista faturas com filtros.
    
    Usuários veem apenas suas faturas (como vendor ou customer).
    """
    service = InvoiceService(db)
    
    # TODO: Aplicar filtros baseados no role do usuário
    # Por enquanto, usar user_id como vendor_id ou customer_id
    
    try:
        invoices, total = await service.list_invoices(
            vendor_id=vendor_id,
            customer_id=customer_id,
            status=status,
            overdue_only=overdue_only,
            page=page,
            limit=limit
        )
        
        # Converter para summary
        invoice_summaries = []
        for invoice in invoices:
            summary = {
                "id": invoice.id,
                "invoice_number": invoice.invoice_number,
                "invoice_type": invoice.invoice_type,
                "status": invoice.status,
                "invoice_date": invoice.invoice_date,
                "due_date": invoice.due_date,
                "total_amount": invoice.total_amount,
                "paid_amount": invoice.paid_amount,
                "outstanding_amount": invoice.outstanding_amount,
                "is_overdue": invoice.is_overdue,
                "days_until_due": invoice.days_until_due,
                # TODO: Carregar nomes das empresas
                "vendor_company_name": None,
                "customer_company_name": None
            }
            invoice_summaries.append(summary)
        
        total_pages = (total + limit - 1) // limit
        
        return InvoiceListResponse(
            invoices=invoice_summaries,
            total=total,
            page=page,
            limit=limit,
            total_pages=total_pages
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/{invoice_id}", response_model=InvoiceResponse)
async def get_invoice(
    invoice_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Busca fatura por ID.
    
    Apenas vendor ou customer podem acessar.
    """
    service = InvoiceService(db)
    
    try:
        invoice = await service.get_invoice(
            invoice_id=invoice_id,
            user_id=str(current_user.id)
        )
        
        if not invoice:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invoice not found"
            )
        
        # Marcar como visualizada se for customer
        # TODO: Implementar lógica de role
        
        return InvoiceResponse(
            success=True,
            message="Invoice retrieved successfully",
            invoice=InvoiceRead.from_orm(invoice)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.put("/{invoice_id}", response_model=InvoiceResponse)
async def update_invoice(
    invoice_id: str,
    update_data: InvoiceUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Atualiza fatura.
    
    Apenas TVendorSupplier proprietário pode atualizar.
    """
    service = InvoiceService(db)
    
    # TODO: Verificar se usuário é TVendorSupplier
    vendor_id = str(current_user.id)
    
    try:
        invoice = await service.update_invoice(
            invoice_id=invoice_id,
            update_data=update_data,
            vendor_id=vendor_id
        )
        
        return InvoiceResponse(
            success=True,
            message="Invoice updated successfully",
            invoice=InvoiceRead.from_orm(invoice)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/{invoice_id}/send", response_model=InvoiceResponse)
async def send_invoice(
    invoice_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Envia fatura para cliente.
    
    Apenas TVendorSupplier proprietário pode enviar.
    """
    service = InvoiceService(db)
    
    # TODO: Verificar se usuário é TVendorSupplier
    vendor_id = str(current_user.id)
    
    try:
        invoice = await service.send_invoice(
            invoice_id=invoice_id,
            vendor_id=vendor_id
        )
        
        return InvoiceResponse(
            success=True,
            message="Invoice sent successfully",
            invoice=InvoiceRead.from_orm(invoice)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/{invoice_id}/payment", response_model=InvoiceResponse)
async def mark_invoice_paid(
    invoice_id: str,
    payment_data: InvoicePaymentUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Marca fatura como paga.
    
    TCostumer ou TVendorSupplier podem marcar como paga.
    """
    service = InvoiceService(db)
    
    try:
        invoice = await service.mark_as_paid(
            invoice_id=invoice_id,
            payment_data=payment_data,
            user_id=str(current_user.id)
        )
        
        return InvoiceResponse(
            success=True,
            message="Payment recorded successfully",
            invoice=InvoiceRead.from_orm(invoice)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/{invoice_id}/download-token", response_model=InvoiceResponse)
async def generate_download_token(
    invoice_id: str,
    request: InvoiceDownloadRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Gera token para download da fatura.
    
    Vendor ou customer podem gerar token.
    """
    service = InvoiceService(db)
    
    try:
        token = await service.generate_download_token(
            invoice_id=invoice_id,
            user_id=str(current_user.id),
            expires_hours=request.expires_hours
        )
        
        return InvoiceResponse(
            success=True,
            message="Download token generated successfully",
            access_token=token
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/{invoice_id}/download")
async def download_invoice(
    invoice_id: str,
    token: str = Query(..., description="Access token"),
    db: AsyncSession = Depends(get_db)
):
    """
    Download do arquivo da fatura.
    
    Requer token de acesso válido.
    """
    service = InvoiceService(db)
    
    try:
        # TODO: Implementar verificação de token e download
        # Por enquanto, retornar erro
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Download functionality not implemented yet"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/{invoice_id}", response_model=InvoiceResponse)
async def delete_invoice(
    invoice_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Soft delete de fatura.
    
    Apenas TVendorSupplier proprietário pode deletar.
    """
    service = InvoiceService(db)
    
    # TODO: Verificar se usuário é TVendorSupplier
    vendor_id = str(current_user.id)
    
    try:
        success = await service.delete_invoice(
            invoice_id=invoice_id,
            vendor_id=vendor_id
        )
        
        return InvoiceResponse(
            success=success,
            message="Invoice deleted successfully"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/stats/summary", response_model=InvoiceStats)
async def get_invoice_stats(
    vendor_id: Optional[str] = Query(None, description="Filter by vendor ID"),
    customer_id: Optional[str] = Query(None, description="Filter by customer ID"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Estatísticas de faturas.
    
    Filtros aplicados baseados no role do usuário.
    """
    service = InvoiceService(db)
    
    try:
        stats = await service.get_invoice_stats(
            vendor_id=vendor_id,
            customer_id=customer_id,
            tenant_id=str(current_user.tenant_id)
        )
        
        return stats
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
