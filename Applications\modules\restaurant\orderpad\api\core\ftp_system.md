# Core - Ftp System

**Categoria:** Core
**Módulo:** Ftp System
**Total de Endpoints:** 27
**Gerado em:** 08/06/2025, 17:26:01

## 📋 Endpoints

- [GET /api/modules/core/ftp_system/directories](#get-apimodulescoreftp-systemdirectories) - List Ftp Directories
- [POST /api/modules/core/ftp_system/directories](#post-apimodulescoreftp-systemdirectories) - Create Ftp Directory
- [POST /api/modules/core/ftp_system/directories/create-defaults](#post-apimodulescoreftp-systemdirectoriescreate-defaults) - Create Default Directories
- [GET /api/modules/core/ftp_system/directories/tree](#get-apimodulescoreftp-systemdirectoriestree) - Get Directory Tree
- [DELETE /api/modules/core/ftp_system/directories/{directory_id}](#delete-apimodulescoreftp-systemdirectoriesdirectory-id) - Delete Ftp Directory
- [GET /api/modules/core/ftp_system/files/{context_type}/{context_id}/{subfolder}/{filename}](#get-apimodulescoreftp-systemfilescontext-typecontext-idsubfolderfilename) - Serve Context Media File
- [GET /api/modules/core/ftp_system/files/{folder_uuid}/{filename}](#get-apimodulescoreftp-systemfilesfolder-uuidfilename) - Serve Media File
- [GET /api/modules/core/ftp_system/health](#get-apimodulescoreftp-systemhealth) - Health Check
- [GET /api/modules/core/ftp_system/products/{menu_item_id}/media](#get-apimodulescoreftp-systemproductsmenu-item-idmedia) - Get Product Media
- [POST /api/modules/core/ftp_system/products/{menu_item_id}/media](#post-apimodulescoreftp-systemproductsmenu-item-idmedia) - Upload Product Media
- [DELETE /api/modules/core/ftp_system/products/{menu_item_id}/media/{upload_id}](#delete-apimodulescoreftp-systemproductsmenu-item-idmediaupload-id) - Delete Product Media
- [GET /api/modules/core/ftp_system/quota/stats](#get-apimodulescoreftp-systemquotastats) - Get All Quota Stats
- [GET /api/modules/core/ftp_system/stats](#get-apimodulescoreftp-systemstats) - Get Tenant Stats
- [GET /api/modules/core/ftp_system/uploads](#get-apimodulescoreftp-systemuploads) - List Ftp Uploads
- [GET /api/modules/core/ftp_system/uploads/search](#get-apimodulescoreftp-systemuploadssearch) - Search Uploads
- [DELETE /api/modules/core/ftp_system/uploads/{upload_id}](#delete-apimodulescoreftp-systemuploadsupload-id) - Delete Ftp Upload
- [GET /api/modules/core/ftp_system/uploads/{upload_id}](#get-apimodulescoreftp-systemuploadsupload-id) - Get Ftp Upload
- [PUT /api/modules/core/ftp_system/uploads/{upload_id}](#put-apimodulescoreftp-systemuploadsupload-id) - Update Ftp Upload
- [GET /api/modules/core/ftp_system/users](#get-apimodulescoreftp-systemusers) - List Ftp Users
- [POST /api/modules/core/ftp_system/users](#post-apimodulescoreftp-systemusers) - Create Ftp User
- [DELETE /api/modules/core/ftp_system/users/{user_id}](#delete-apimodulescoreftp-systemusersuser-id) - Delete Ftp User
- [GET /api/modules/core/ftp_system/users/{user_id}](#get-apimodulescoreftp-systemusersuser-id) - Get Ftp User
- [PUT /api/modules/core/ftp_system/users/{user_id}](#put-apimodulescoreftp-systemusersuser-id) - Update Ftp User
- [GET /api/modules/core/ftp_system/users/{user_id}/quota](#get-apimodulescoreftp-systemusersuser-idquota) - Get User Quota Stats
- [PUT /api/modules/core/ftp_system/users/{user_id}/quota](#put-apimodulescoreftp-systemusersuser-idquota) - Update User Quota
- [POST /api/modules/core/ftp_system/users/{user_id}/quota/check](#post-apimodulescoreftp-systemusersuser-idquotacheck) - Check Quota Before Upload
- [GET /api/modules/core/ftp_system/users/{user_id}/stats](#get-apimodulescoreftp-systemusersuser-idstats) - Get Ftp User Stats

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### Body_upload_product_media_api_modules_core_ftp_system_products__menu_item_id__media_post

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `files` | Array[string] | ✅ | - |
| `crop_data` | unknown | ❌ | - |

### FTPDirectoryCreate

**Descrição:** Schema para criação de diretório FTP.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Nome do diretório |
| `path` | string | ✅ | Caminho completo do diretório |
| `parent_id` | unknown | ❌ | ID do diretório pai |

### FTPDirectoryRead

**Descrição:** Schema para leitura de diretório FTP.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Nome do diretório |
| `path` | string | ✅ | Caminho completo do diretório |
| `parent_id` | unknown | ❌ | ID do diretório pai |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `children` | Array[FTPDirectoryRead] | ❌ | - |

### FTPQuotaCheckResponse

**Descrição:** Schema de resposta para verificação de quota.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `can_upload` | boolean | ✅ | - |
| `reason` | unknown | ❌ | - |
| `quota_limit_mb` | unknown | ❌ | - |
| `used_space_mb` | unknown | ❌ | - |
| `available_space_mb` | unknown | ❌ | - |
| `file_size_mb` | unknown | ❌ | - |

### FTPQuotaStatsResponse

**Descrição:** Schema de resposta para estatísticas de quota.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `user_id` | string | ✅ | - |
| `username` | string | ✅ | - |
| `quota_limit_mb` | integer | ✅ | - |
| `used_space_mb` | integer | ✅ | - |
| `available_space_mb` | integer | ✅ | - |
| `quota_usage_percentage` | number | ✅ | - |
| `is_quota_enabled` | boolean | ✅ | - |
| `is_quota_exceeded` | boolean | ✅ | - |
| `folder_uuid` | string | ✅ | - |
| `folder_path` | unknown | ✅ | - |

### FTPUploadRead

**Descrição:** Schema para leitura de upload FTP.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `filename` | string | ✅ | Nome do arquivo |
| `original_filename` | string | ✅ | Nome original do arquivo |
| `file_path` | string | ✅ | Caminho do arquivo |
| `file_size` | integer | ✅ | Tamanho do arquivo em bytes |
| `mime_type` | string | ✅ | Tipo MIME do arquivo |
| `media_type` | MediaType | ✅ | Tipo de mídia |
| `context_type` | FTPContextType | ✅ | - |
| `context_id` | unknown | ✅ | - |
| `directory_id` | unknown | ❌ | ID do diretório |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `ftp_user_id` | string | ✅ | - |
| `upload_status` | UploadStatus | ✅ | - |
| `thumbnail_path` | unknown | ✅ | - |
| `file_metadata` | unknown | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | unknown | ✅ | - |

### FTPUploadUpdate

**Descrição:** Schema para atualização de upload FTP.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `upload_status` | unknown | ❌ | - |
| `thumbnail_path` | unknown | ❌ | - |
| `file_metadata` | unknown | ❌ | - |
| `context_type` | unknown | ❌ | - |
| `context_id` | unknown | ❌ | - |

### FTPUserCreate

**Descrição:** Schema para criação de usuário FTP.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `username` | string | ✅ | Nome de usuário FTP |
| `home_directory` | unknown | ❌ | Diretório home do usuário |
| `is_active` | boolean | ❌ | Se o usuário está ativo |
| `quota_limit_mb` | integer | ❌ | Limite de quota em MB |
| `is_quota_enabled` | boolean | ❌ | Se a quota está habilitada |
| `password` | string | ✅ | Senha do usuário FTP |

### FTPUserQuotaUpdate

**Descrição:** Schema para atualização apenas de quota.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `quota_limit_mb` | unknown | ❌ | Limite de quota em MB |
| `is_quota_enabled` | unknown | ❌ | Habilitar/desabilitar quota |

### FTPUserRead

**Descrição:** Schema para leitura de usuário FTP.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `username` | string | ✅ | Nome de usuário FTP |
| `home_directory` | unknown | ❌ | Diretório home do usuário |
| `is_active` | boolean | ❌ | Se o usuário está ativo |
| `quota_limit_mb` | integer | ❌ | Limite de quota em MB |
| `is_quota_enabled` | boolean | ❌ | Se a quota está habilitada |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `folder_uuid` | string | ✅ | - |
| `folder_path` | unknown | ✅ | - |
| `used_space_mb` | integer | ✅ | - |
| `last_login` | unknown | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | unknown | ✅ | - |
| `quota_usage_percentage` | unknown | ❌ | - |
| `available_space_mb` | unknown | ❌ | - |
| `is_quota_exceeded` | unknown | ❌ | - |

### FTPUserStatsResponse

**Descrição:** Schema de resposta para estatísticas de usuário FTP.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `total_uploads` | integer | ✅ | - |
| `total_size` | integer | ✅ | - |
| `active_uploads` | integer | ✅ | - |
| `failed_uploads` | integer | ✅ | - |

### FTPUserUpdate

**Descrição:** Schema para atualização de usuário FTP.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `password` | unknown | ❌ | - |
| `home_directory` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `quota_limit_mb` | unknown | ❌ | Limite de quota em MB |
| `is_quota_enabled` | unknown | ❌ | Habilitar/desabilitar quota |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/modules/core/ftp_system/directories {#get-apimodulescoreftp-systemdirectories}

**Resumo:** List Ftp Directories
**Descrição:** Lista diretórios FTP do tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/ftp_system/directories" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/ftp_system/directories {#post-apimodulescoreftp-systemdirectories}

**Resumo:** Create Ftp Directory
**Descrição:** Cria um novo diretório FTP.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [FTPDirectoryCreate](#ftpdirectorycreate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [FTPDirectoryRead](#ftpdirectoryread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/ftp_system/directories" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/core/ftp_system/directories/create-defaults {#post-apimodulescoreftp-systemdirectoriescreate-defaults}

**Resumo:** Create Default Directories
**Descrição:** Cria diretórios padrão para o tenant (apenas OWNER).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/ftp_system/directories/create-defaults" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/ftp_system/directories/tree {#get-apimodulescoreftp-systemdirectoriestree}

**Resumo:** Get Directory Tree
**Descrição:** Obtém árvore de diretórios.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/ftp_system/directories/tree" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### DELETE /api/modules/core/ftp_system/directories/{directory_id} {#delete-apimodulescoreftp-systemdirectoriesdirectory-id}

**Resumo:** Delete Ftp Directory
**Descrição:** Remove diretório FTP (apenas OWNER).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `directory_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/core/ftp_system/directories/{directory_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/ftp_system/files/{context_type}/{context_id}/{subfolder}/{filename} {#get-apimodulescoreftp-systemfilescontext-typecontext-idsubfolderfilename}

**Resumo:** Serve Context Media File
**Descrição:** Serve arquivos de mídia usando nova estrutura de contexto.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `context_type` | string | path | ✅ | - |
| `context_id` | string | path | ✅ | - |
| `subfolder` | string | path | ✅ | - |
| `filename` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/ftp_system/files/{context_type}/{context_id}/{subfolder}/{filename}"
```

---

### GET /api/modules/core/ftp_system/files/{folder_uuid}/{filename} {#get-apimodulescoreftp-systemfilesfolder-uuidfilename}

**Resumo:** Serve Media File
**Descrição:** Serve arquivos de mídia publicamente via UUID (compatibilidade).

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `folder_uuid` | string | path | ✅ | - |
| `filename` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/ftp_system/files/{folder_uuid}/{filename}"
```

---

### GET /api/modules/core/ftp_system/health {#get-apimodulescoreftp-systemhealth}

**Resumo:** Health Check
**Descrição:** Health check para o sistema FTP.

**🔐 Autenticação:** Não requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/ftp_system/health"
```

---

### GET /api/modules/core/ftp_system/products/{menu_item_id}/media {#get-apimodulescoreftp-systemproductsmenu-item-idmedia}

**Resumo:** Get Product Media
**Descrição:** Obtém mídia de um produto.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `menu_item_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/ftp_system/products/{menu_item_id}/media" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/ftp_system/products/{menu_item_id}/media {#post-apimodulescoreftp-systemproductsmenu-item-idmedia}

**Resumo:** Upload Product Media
**Descrição:** Upload de mídia para produtos (até 9 imagens + 1 vídeo).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `menu_item_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `multipart/form-data`
**Schema:** [Body_upload_product_media_api_modules_core_ftp_system_products__menu_item_id__media_post](#body_upload_product_media_api_modules_core_ftp_system_products__menu_item_id__media_post)

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/ftp_system/products/{menu_item_id}/media" \
  -H "Content-Type: multipart/form-data" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/core/ftp_system/products/{menu_item_id}/media/{upload_id} {#delete-apimodulescoreftp-systemproductsmenu-item-idmediaupload-id}

**Resumo:** Delete Product Media
**Descrição:** Remove mídia de um produto (apenas admin).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `menu_item_id` | string | path | ✅ | - |
| `upload_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/core/ftp_system/products/{menu_item_id}/media/{upload_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/ftp_system/quota/stats {#get-apimodulescoreftp-systemquotastats}

**Resumo:** Get All Quota Stats
**Descrição:** Obtém estatísticas de quota de todos os usuários do tenant (apenas OWNER).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/ftp_system/quota/stats" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/ftp_system/stats {#get-apimodulescoreftp-systemstats}

**Resumo:** Get Tenant Stats
**Descrição:** Obtém estatísticas de uploads do tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/ftp_system/stats" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/ftp_system/uploads {#get-apimodulescoreftp-systemuploads}

**Resumo:** List Ftp Uploads
**Descrição:** Lista uploads FTP do tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `status` | string | query | ❌ | - |
| `media_type` | string | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `offset` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/ftp_system/uploads" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/ftp_system/uploads/search {#get-apimodulescoreftp-systemuploadssearch}

**Resumo:** Search Uploads
**Descrição:** Busca uploads por nome de arquivo.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `q` | string | query | ✅ | - |
| `media_type` | string | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/ftp_system/uploads/search" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### DELETE /api/modules/core/ftp_system/uploads/{upload_id} {#delete-apimodulescoreftp-systemuploadsupload-id}

**Resumo:** Delete Ftp Upload
**Descrição:** Remove upload FTP (apenas OWNER).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `upload_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/core/ftp_system/uploads/{upload_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/ftp_system/uploads/{upload_id} {#get-apimodulescoreftp-systemuploadsupload-id}

**Resumo:** Get Ftp Upload
**Descrição:** Obtém upload FTP por ID.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `upload_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [FTPUploadRead](#ftpuploadread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/ftp_system/uploads/{upload_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/core/ftp_system/uploads/{upload_id} {#put-apimodulescoreftp-systemuploadsupload-id}

**Resumo:** Update Ftp Upload
**Descrição:** Atualiza upload FTP.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `upload_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [FTPUploadUpdate](#ftpuploadupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [FTPUploadRead](#ftpuploadread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/core/ftp_system/uploads/{upload_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/ftp_system/users {#get-apimodulescoreftp-systemusers}

**Resumo:** List Ftp Users
**Descrição:** Lista usuários FTP do tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/ftp_system/users" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/ftp_system/users {#post-apimodulescoreftp-systemusers}

**Resumo:** Create Ftp User
**Descrição:** Cria um novo usuário FTP (apenas OWNER).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [FTPUserCreate](#ftpusercreate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [FTPUserRead](#ftpuserread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/ftp_system/users" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/core/ftp_system/users/{user_id} {#delete-apimodulescoreftp-systemusersuser-id}

**Resumo:** Delete Ftp User
**Descrição:** Remove usuário FTP (apenas OWNER).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `user_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/core/ftp_system/users/{user_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/ftp_system/users/{user_id} {#get-apimodulescoreftp-systemusersuser-id}

**Resumo:** Get Ftp User
**Descrição:** Obtém usuário FTP por ID.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `user_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [FTPUserRead](#ftpuserread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/ftp_system/users/{user_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/core/ftp_system/users/{user_id} {#put-apimodulescoreftp-systemusersuser-id}

**Resumo:** Update Ftp User
**Descrição:** Atualiza usuário FTP (apenas OWNER).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `user_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [FTPUserUpdate](#ftpuserupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [FTPUserRead](#ftpuserread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/core/ftp_system/users/{user_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/ftp_system/users/{user_id}/quota {#get-apimodulescoreftp-systemusersuser-idquota}

**Resumo:** Get User Quota Stats
**Descrição:** Obtém estatísticas de quota de um usuário.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `user_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [FTPQuotaStatsResponse](#ftpquotastatsresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/ftp_system/users/{user_id}/quota" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/core/ftp_system/users/{user_id}/quota {#put-apimodulescoreftp-systemusersuser-idquota}

**Resumo:** Update User Quota
**Descrição:** Atualiza configurações de quota de um usuário (apenas OWNER).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `user_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [FTPUserQuotaUpdate](#ftpuserquotaupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [FTPUserRead](#ftpuserread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/core/ftp_system/users/{user_id}/quota" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/core/ftp_system/users/{user_id}/quota/check {#post-apimodulescoreftp-systemusersuser-idquotacheck}

**Resumo:** Check Quota Before Upload
**Descrição:** Verifica se o usuário pode fazer upload de um arquivo.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `user_id` | string | path | ✅ | - |
| `file_size_bytes` | integer | query | ✅ | Tamanho do arquivo em bytes |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [FTPQuotaCheckResponse](#ftpquotacheckresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/ftp_system/users/{user_id}/quota/check" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/ftp_system/users/{user_id}/stats {#get-apimodulescoreftp-systemusersuser-idstats}

**Resumo:** Get Ftp User Stats
**Descrição:** Obtém estatísticas do usuário FTP.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `user_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [FTPUserStatsResponse](#ftpuserstatsresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/ftp_system/users/{user_id}/stats" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
