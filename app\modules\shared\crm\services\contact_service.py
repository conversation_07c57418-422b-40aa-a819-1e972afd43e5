"""Contact service for CRM module."""

import uuid  # noqa: E402
import logging
from typing import List, Optional
from sqlalchemy import select, update, delete
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError

from app.modules.shared.crm.models.contact import Contact, ContactStatus  # noqa: E402
from app.modules.shared.crm.schemas.contact import ContactCreate, ContactUpdate

logger = logging.getLogger(__name__)


class ContactService:
    """Service for managing CRM contacts."""

    @staticmethod
    async def create_contact(
        db: AsyncSession, tenant_id: uuid.UUID, contact_in: ContactCreate
    ) -> Contact:
        """Create a new contact."""
        try:
            contact_data = contact_in.model_dump(exclude_unset=True)

            # Generate full_name if not provided
            if not contact_data.get("full_name"):
                contact_data["full_name"] = (
                    f"{contact_data['first_name']} {contact_data['last_name']}".strip()
                )

            db_contact = Contact(tenant_id=tenant_id, **contact_data)

            db.add(db_contact)
            await db.commit()
            await db.refresh(db_contact)

            return db_contact
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(f"Error creating contact: {e}")
            raise

    @staticmethod
    async def get_contact(
        db: AsyncSession, tenant_id: uuid.UUID, contact_id: uuid.UUID
    ) -> Optional[Contact]:
        """Get a contact by ID."""
        try:
            query = select(Contact).where(Contact.tenant_id == tenant_id, Contact.id == contact_id)
            result = await db.execute(query)
            return result.scalars().first()
        except SQLAlchemyError as e:
            logger.error(f"Error getting contact {contact_id}: {e}")
            raise

    @staticmethod
    async def get_contacts(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        account_id: Optional[uuid.UUID] = None,
        skip: int = 0,
        limit: int = 100,
        status: Optional[ContactStatus] = None,
        contact_type: Optional[str] = None,
        search: Optional[str] = None,
    ) -> List[Contact]:
        """Get all contacts with optional filtering."""
        try:
            query = select(Contact).where(Contact.tenant_id == tenant_id)

            # Filter by account if provided
            if account_id:
                query = query.where(Contact.account_id == account_id)

            # Apply filters if provided
            if status:
                query = query.where(Contact.status == status)

            if contact_type:
                query = query.where(Contact.contact_type == contact_type)

            if search:
                search_term = f"%{search}%"
                query = query.where(
                    Contact.first_name.ilike(search_term)
                    | Contact.last_name.ilike(search_term)
                    | Contact.full_name.ilike(search_term)
                    | Contact.email.ilike(search_term)
                    | Contact.phone.ilike(search_term)
                )

            # Apply pagination
            query = query.offset(skip).limit(limit)

            result = await db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            logger.error(f"Error getting contacts: {e}")
            raise

    @staticmethod
    async def update_contact(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        contact_id: uuid.UUID,
        contact_in: ContactUpdate,
    ) -> Optional[Contact]:
        """Update a contact."""
        try:
            # Get the contact first to ensure it exists and belongs to the tenant
            db_contact = await ContactService.get_contact(db, tenant_id, contact_id)
            if not db_contact:
                return None

            # Update the contact
            contact_data = contact_in.model_dump(exclude_unset=True)

            # Update full_name if first_name or last_name is provided
            if "first_name" in contact_data or "last_name" in contact_data:
                first_name = contact_data.get("first_name", db_contact.first_name)
                last_name = contact_data.get("last_name", db_contact.last_name)
                contact_data["full_name"] = f"{first_name} {last_name}".strip()

            # Update the contact in the database
            query = (
                update(Contact)
                .where(Contact.id == contact_id, Contact.tenant_id == tenant_id)
                .values(**contact_data)
                .execution_options(synchronize_session="fetch")
            )
            await db.execute(query)
            await db.commit()

            # Refresh and return the updated contact
            return await ContactService.get_contact(db, tenant_id, contact_id)
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(f"Error updating contact {contact_id}: {e}")
            raise

    @staticmethod
    async def delete_contact(db: AsyncSession, tenant_id: uuid.UUID, contact_id: uuid.UUID) -> bool:
        """Delete a contact."""
        try:
            # Get the contact first to ensure it exists and belongs to the tenant
            db_contact = await ContactService.get_contact(db, tenant_id, contact_id)
            if not db_contact:
                return False

            # Delete the contact
            query = (
                delete(Contact)
                .where(Contact.id == contact_id, Contact.tenant_id == tenant_id)
                .execution_options(synchronize_session="fetch")
            )
            await db.execute(query)
            await db.commit()

            return True
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(f"Error deleting contact {contact_id}: {e}")
            raise


# Create a singleton instance
contact_service = ContactService()
