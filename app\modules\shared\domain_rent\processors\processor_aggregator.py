"""Processor aggregator for domain registrar APIs."""

import logging  # noqa: E402
from typing import Dict, List, Optional, Any, Type

from app.modules.shared.domain_rent.processors.base_processor import BaseProcessor  # noqa: E402
from app.modules.shared.domain_rent.schemas.domain_schemas import (
    DomainAvailabilityResult,
    ContactInfo,
)
from app.modules.shared.domain_rent.exceptions import (  # noqa: E402
    ProcessorNotFoundError,
    RegistrarApiError,
)


logger = logging.getLogger(__name__)


class ProcessorAggregator:
    """Aggregator for domain registrar processors.

    This class manages multiple registrar processors and provides methods to
    perform operations across all of them.
    """

    def __init__(self, processors: Optional[Dict[str, BaseProcessor]] = None):
        """Initialize processor aggregator.

        Args:
            processors: Dictionary of registrar processors (name -> processor)
        """
        self.processors = processors or {}

    def add_processor(self, processor: BaseProcessor) -> None:
        """Add a processor to the aggregator.

        Args:
            processor: Processor to add
        """
        self.processors[processor.registrar_name] = processor

    def get_processor(self, registrar: str) -> BaseProcessor:
        """Get a processor by registrar name.

        Args:
            registrar: Registrar name

        Returns:
            Processor for the specified registrar

        Raises:
            ProcessorNotFoundError: If no processor is found for the registrar
        """
        processor = self.processors.get(registrar)
        if not processor:
            raise ProcessorNotFoundError(f"No processor found for registrar: {registrar}")
        return processor

    def get_active_processors(self) -> List[BaseProcessor]:
        """Get all active processors.

        Returns:
            List of active processors
        """
        return list(self.processors.values())

    async def search_availability(
        self,
        domain_name: str,
        tlds: Optional[List[str]] = None,
        registrars: Optional[List[str]] = None,
    ) -> List[DomainAvailabilityResult]:
        """Search for domain availability across all processors.

        Args:
            domain_name: Domain name without TLD
            tlds: List of TLDs to check (if None, check common TLDs)
            registrars: List of registrars to check (if None, check all)

        Returns:
            List of availability results for each domain/TLD combination
        """
        results = []

        # Get processors to use
        processors_to_use = []
        if registrars:
            for registrar in registrars:
                try:
                    processors_to_use.append(self.get_processor(registrar))
                except ProcessorNotFoundError:
                    logger.warning(f"Skipping unknown registrar: {registrar}")
        else:
            processors_to_use = self.get_active_processors()

        # Check availability with each processor
        for processor in processors_to_use:
            try:
                processor_results = await processor.search_availability(domain_name, tlds)
                results.extend(processor_results)
            except Exception as e:
                logger.error(
                    f"Error checking availability with {processor.registrar_name}: {str(e)}"
                )

        return results

    def aggregate_availability_results(
        self, results: List[DomainAvailabilityResult]
    ) -> Dict[str, List[DomainAvailabilityResult]]:
        """Aggregate availability results by domain.

        Args:
            results: List of availability results

        Returns:
            Dictionary of domain -> list of results
        """
        aggregated = {}

        for result in results:
            domain_key = result.domain_name
            if domain_key not in aggregated:
                aggregated[domain_key] = []
            aggregated[domain_key].append(result)

        return aggregated

    def get_best_registrar(self, results: List[DomainAvailabilityResult]) -> Optional[str]:
        """Get the best registrar for a domain based on availability and price.

        Args:
            results: List of availability results for a domain

        Returns:
            Name of the best registrar, or None if no registrar is available
        """
        available_results = [r for r in results if r.available]
        if not available_results:
            return None

        # Sort by price (if available)
        def get_price(result):
            if not result.price_info:
                return float("inf")
            registration = result.price_info.registration
            if not registration:
                return float("inf")
            # Get price for 1 year registration
            return registration.get("1", float("inf"))

        available_results.sort(key=get_price)
        return available_results[0].registrar
