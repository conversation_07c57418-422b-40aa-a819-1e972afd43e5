import enum
import uuid
from typing import Optional, List
from sqlalchemy import <PERSON>um<PERSON>, String, ForeignKey, Index, Boolean, Integer, Text, Enum as SAEnum, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.sql import text

from app.db.base import Base
from app.modules.core.tenants.models.tenant import Tenant
from app.core.enums import MarketType


class ProductCategory(Base):
    """
    Represents a category for eshop products with unlimited subcategory support.
    Similar to menu categories but for marketplace products.
    """

    __tablename__ = "eshop_product_categories"

    # Primary key
    id: Mapped[uuid.UUID] = mapped_column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Tenant isolation - categories are global but can be tenant-specific
    tenant_id: Mapped[uuid.UUID] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    
    # Category properties
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    slug: Mapped[str] = mapped_column(String(120), nullable=False, index=True)
    market_type: Mapped[MarketType] = mapped_column(SAEnum(MarketType, name='markettype', create_type=False), nullable=False, server_default=text("'PUBLIC'"))
    description: Mapped[Optional[str]] = mapped_column(Text)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    
    # Hierarchical structure - unlimited subcategories
    parent_id: Mapped[Optional[uuid.UUID]] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("eshop_product_categories.id"), nullable=True, index=True)
    
    # Display and status
    display_order: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    is_featured: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)  # Featured categories
    
    # SEO and metadata
    meta_title: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    meta_description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Image for category
    image_url: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    icon: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)  # Icon class or name
    
    # Relationships
    # tenant: Mapped['Tenant'] = relationship(back_populates='product_categories')
    parent: Mapped[Optional['ProductCategory']] = relationship(remote_side=[id], back_populates='children', lazy='joined')
    children: Mapped[List['ProductCategory']] = relationship(back_populates='parent', lazy='joined', cascade="all, delete-orphan")
    products: Mapped[List['Product']] = relationship('app.modules.core.eshop.models.product.Product', back_populates='category')

    __table_args__ = (
        Index("ix_eshop_product_categories_tenant_id", "tenant_id"),
        Index("ix_eshop_product_categories_slug", "slug"),
        Index("ix_eshop_product_categories_parent_id", "parent_id"),
        UniqueConstraint('tenant_id', 'slug', name='_tenant_slug_uc'),
    )

    def __repr__(self) -> str:
        return f"ProductCategory(id={self.id}, name='{self.name}', tenant_id={self.tenant_id})"
