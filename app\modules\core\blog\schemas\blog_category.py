"""
Blog Category Schemas

Pydantic models for blog category validation and serialization.
"""

import uuid
from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field


class BlogCategoryTranslationBase(BaseModel):
    """Base schema for blog category translations."""
    language_code: str = Field(..., max_length=10)
    name: str = Field(..., max_length=100)
    description: Optional[str] = None
    meta_title: Optional[str] = Field(None, max_length=60)
    meta_description: Optional[str] = Field(None, max_length=160)


class BlogCategoryTranslationCreate(BlogCategoryTranslationBase):
    """Schema for creating blog category translations."""
    pass


class BlogCategoryTranslationUpdate(BaseModel):
    """Schema for updating blog category translations."""
    name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = None
    meta_title: Optional[str] = Field(None, max_length=60)
    meta_description: Optional[str] = Field(None, max_length=160)


class BlogCategoryTranslationRead(BlogCategoryTranslationBase):
    """Schema for reading blog category translations."""
    id: uuid.UUID
    category_id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class BlogCategoryBase(BaseModel):
    """Base schema for blog categories."""
    slug: str = Field(..., max_length=255)
    parent_id: Optional[uuid.UUID] = None
    sort_order: int = 0
    color: Optional[str] = Field(None, max_length=7)  # Hex color
    icon: Optional[str] = Field(None, max_length=100)


class BlogCategoryCreate(BlogCategoryBase):
    """Schema for creating blog categories."""
    translations: List[BlogCategoryTranslationCreate] = Field(..., min_items=1)


class BlogCategoryUpdate(BaseModel):
    """Schema for updating blog categories."""
    slug: Optional[str] = Field(None, max_length=255)
    parent_id: Optional[uuid.UUID] = None
    sort_order: Optional[int] = None
    color: Optional[str] = Field(None, max_length=7)
    icon: Optional[str] = Field(None, max_length=100)


class BlogCategoryRead(BlogCategoryBase):
    """Schema for reading blog categories."""
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    
    # Relationships
    parent: Optional["BlogCategoryRead"] = None
    children: List["BlogCategoryRead"] = []
    translations: List[BlogCategoryTranslationRead] = []
    
    class Config:
        from_attributes = True


class BlogCategoryList(BaseModel):
    """Schema for blog category list items (simplified)."""
    id: uuid.UUID
    slug: str
    parent_id: Optional[uuid.UUID]
    sort_order: int
    color: Optional[str]
    icon: Optional[str]
    created_at: datetime
    
    # Primary translation (usually in default language)
    name: Optional[str] = None
    description: Optional[str] = None
    
    # Post count
    post_count: int = 0
    
    class Config:
        from_attributes = True


class BlogCategoryTree(BaseModel):
    """Schema for hierarchical category tree."""
    id: uuid.UUID
    slug: str
    name: str
    color: Optional[str]
    icon: Optional[str]
    post_count: int = 0
    children: List["BlogCategoryTree"] = []
    
    class Config:
        from_attributes = True


# Update forward references
BlogCategoryRead.model_rebuild()
BlogCategoryTree.model_rebuild()
