from typing import List, Optional, <PERSON><PERSON>
import uuid
from datetime import datetime, date, timedelta
from sqlalchemy import select, delete
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from app.modules.shared.hr.core.models.time_tracking import (
    TimeRecord,
    TimeRecordType,
    OvertimeRecord,
    TimeBank,
    TimeBankTransaction,
    TimeBankTransactionType,
    TimeMirror,
    TimeMirrorStatus,
    OvertimeRule,
)
from app.modules.shared.hr.core.models.employee import Employee  # noqa: E402
from app.modules.shared.hr.core.schemas.time_tracking import (
    TimeRecordCreate,
    TimeRecordUpdate,
    OvertimeRecordCreate,
    OvertimeRecordUpdate,
    TimeBankTransactionCreate,
    ClockInRequest,
    ClockOutRequest,
    TimeCardSummary,
    GenerateTimeMirrorRequest,
    OvertimeRuleCreate,
    OvertimeRuleUpdate,
)
from app.core.exceptions import NotFoundError, ValidationError  # noqa: E402


class TimeTrackingService:
    """Service for time tracking operations."""

    @staticmethod
    async def create_time_record(
        db: AsyncSession, tenant_id: uuid.UUID, record_data: TimeRecordCreate
    ) -> TimeRecord:
        """Create a new time record."""
        # Verify that the employee exists
        stmt = select(Employee).where(
            Employee.id == record_data.employee_id, Employee.tenant_id == tenant_id
        )
        result = await db.execute(stmt)
        employee = result.scalars().first()

        if not employee:
            raise NotFoundError(f"Employee with id {record_data.employee_id} not found")

        # Create time record
        time_record = TimeRecord(tenant_id=tenant_id, **record_data.model_dump())

        db.add(time_record)
        await db.commit()
        await db.refresh(time_record)
        return time_record

    @staticmethod
    async def get_time_record(
        db: AsyncSession, tenant_id: uuid.UUID, record_id: uuid.UUID
    ) -> TimeRecord:
        """Get a time record by ID."""
        stmt = select(TimeRecord).where(
            TimeRecord.id == record_id, TimeRecord.tenant_id == tenant_id
        )
        result = await db.execute(stmt)
        record = result.scalars().first()

        if not record:
            raise NotFoundError(f"Time record with id {record_id} not found")

        return record

    @staticmethod
    async def get_time_records(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        employee_id: Optional[uuid.UUID] = None,
        record_type: Optional[TimeRecordType] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[TimeRecord]:
        """Get time records with optional filtering."""
        query = select(TimeRecord).where(TimeRecord.tenant_id == tenant_id)

        # Apply filters if provided
        if employee_id:
            query = query.where(TimeRecord.employee_id == employee_id)

        if record_type:
            query = query.where(TimeRecord.record_type == record_type)

        if start_date:
            query = query.where(TimeRecord.record_time >= start_date)

        if end_date:
            query = query.where(TimeRecord.record_time <= end_date)

        # Apply pagination and ordering
        query = query.order_by(TimeRecord.record_time.desc()).offset(skip).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def update_time_record(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        record_id: uuid.UUID,
        record_data: TimeRecordUpdate,
    ) -> TimeRecord:
        """Update a time record."""
        # Get the record first to ensure it exists
        time_record = await TimeTrackingService.get_time_record(db, tenant_id, record_id)

        # Update attributes
        update_data = record_data.model_dump(exclude_unset=True)

        # Handle metadata update separately (merge instead of replace)
        metadata_update = update_data.pop("metadata", None)
        if metadata_update:
            time_record.metadata = {**time_record.metadata, **metadata_update}

        # Update the other fields
        for key, value in update_data.items():
            setattr(time_record, key, value)

        await db.commit()
        await db.refresh(time_record)
        return time_record

    @staticmethod
    async def delete_time_record(
        db: AsyncSession, tenant_id: uuid.UUID, record_id: uuid.UUID
    ) -> bool:
        """Delete a time record."""
        stmt = (
            delete(TimeRecord)
            .where(TimeRecord.id == record_id, TimeRecord.tenant_id == tenant_id)
            .returning(TimeRecord.id)
        )

        result = await db.execute(stmt)
        deleted_id = result.scalars().first()

        if not deleted_id:
            raise NotFoundError(f"Time record with id {record_id} not found")

        await db.commit()
        return True

    @staticmethod
    async def clock_in(
        db: AsyncSession, tenant_id: uuid.UUID, clock_in_data: ClockInRequest
    ) -> TimeRecord:
        """Clock in an employee."""
        # Verify that the employee exists
        stmt = select(Employee).where(
            Employee.id == clock_in_data.employee_id, Employee.tenant_id == tenant_id
        )
        result = await db.execute(stmt)
        employee = result.scalars().first()

        if not employee:
            raise NotFoundError(f"Employee with id {clock_in_data.employee_id} not found")

        # Check if the employee is already clocked in
        today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        today_end = today_start + timedelta(days=1)

        # Find the latest check-in without a corresponding check-out
        stmt = (
            select(TimeRecord)
            .where(
                TimeRecord.employee_id == clock_in_data.employee_id,
                TimeRecord.tenant_id == tenant_id,
                TimeRecord.record_time.between(today_start, today_end),
                TimeRecord.record_type == TimeRecordType.CHECK_IN,
            )
            .order_by(TimeRecord.record_time.desc())
        )

        result = await db.execute(stmt)
        latest_check_in = result.scalars().first()

        if latest_check_in:
            # Check if there's a check-out after this check-in
            stmt = select(TimeRecord).where(
                TimeRecord.employee_id == clock_in_data.employee_id,
                TimeRecord.tenant_id == tenant_id,
                TimeRecord.record_time > latest_check_in.record_time,
                TimeRecord.record_type == TimeRecordType.CHECK_OUT,
            )

            result = await db.execute(stmt)
            check_out = result.scalars().first()

            if not check_out:
                raise ValidationError("Employee is already clocked in")

        # Create time record for clock-in
        clock_in_record = TimeRecord(
            tenant_id=tenant_id,
            employee_id=clock_in_data.employee_id,
            record_type=TimeRecordType.CHECK_IN,
            record_time=datetime.now(),
            source=clock_in_data.source,
            latitude=clock_in_data.latitude,
            longitude=clock_in_data.longitude,
            location_name=clock_in_data.location_name,
            notes=clock_in_data.notes,
        )

        db.add(clock_in_record)
        await db.commit()
        await db.refresh(clock_in_record)
        return clock_in_record

    @staticmethod
    async def clock_out(
        db: AsyncSession, tenant_id: uuid.UUID, clock_out_data: ClockOutRequest
    ) -> TimeRecord:
        """Clock out an employee."""
        # Verify that the employee exists
        stmt = select(Employee).where(
            Employee.id == clock_out_data.employee_id, Employee.tenant_id == tenant_id
        )
        result = await db.execute(stmt)
        employee = result.scalars().first()

        if not employee:
            raise NotFoundError(f"Employee with id {clock_out_data.employee_id} not found")

        # Check if the employee is clocked in
        today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        today_end = today_start + timedelta(days=1)

        # Find the latest check-in
        stmt = (
            select(TimeRecord)
            .where(
                TimeRecord.employee_id == clock_out_data.employee_id,
                TimeRecord.tenant_id == tenant_id,
                TimeRecord.record_time.between(today_start, today_end),
                TimeRecord.record_type == TimeRecordType.CHECK_IN,
            )
            .order_by(TimeRecord.record_time.desc())
        )

        result = await db.execute(stmt)
        latest_check_in = result.scalars().first()

        if not latest_check_in:
            raise ValidationError("Employee is not clocked in")

        # Check if there's already a check-out after this check-in
        stmt = select(TimeRecord).where(
            TimeRecord.employee_id == clock_out_data.employee_id,
            TimeRecord.tenant_id == tenant_id,
            TimeRecord.record_time > latest_check_in.record_time,
            TimeRecord.record_type == TimeRecordType.CHECK_OUT,
        )

        result = await db.execute(stmt)
        check_out = result.scalars().first()

        if check_out:
            raise ValidationError("Employee is already clocked out")

        # Create time record for clock-out
        clock_out_record = TimeRecord(
            tenant_id=tenant_id,
            employee_id=clock_out_data.employee_id,
            record_type=TimeRecordType.CHECK_OUT,
            record_time=datetime.now(),
            source=clock_out_data.source,
            latitude=clock_out_data.latitude,
            longitude=clock_out_data.longitude,
            location_name=clock_out_data.location_name,
            notes=clock_out_data.notes,
        )

        db.add(clock_out_record)
        await db.commit()
        await db.refresh(clock_out_record)
        return clock_out_record

    @staticmethod
    async def get_time_card(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        employee_id: uuid.UUID,
        for_date: Optional[date] = None,
    ) -> TimeCardSummary:
        """Get a time card summary for an employee for a specific date."""
        # Default to today if date not provided
        if not for_date:
            for_date = date.today()

        # Get the employee
        from app.models.tenant_user_association import TenantUserAssociation  # noqa: E402

        stmt = (
            select(Employee)
            .options(joinedload(Employee.user_association).joinedload(TenantUserAssociation.user))
            .where(Employee.id == employee_id, Employee.tenant_id == tenant_id)
        )
        result = await db.execute(stmt)
        employee = result.scalars().first()

        if not employee:
            raise NotFoundError(f"Employee with id {employee_id} not found")

        # Get the employee name from the user
        user = employee.user_association.user
        employee_name = f"{user.first_name} {user.last_name}"

        # Get time records for the date
        date_start = datetime.combine(for_date, datetime.min.time())
        date_end = datetime.combine(for_date, datetime.max.time())

        stmt = (
            select(TimeRecord)
            .where(
                TimeRecord.employee_id == employee_id,
                TimeRecord.tenant_id == tenant_id,
                TimeRecord.record_time.between(date_start, date_end),
            )
            .order_by(TimeRecord.record_time)
        )

        result = await db.execute(stmt)
        records = result.scalars().all()

        # Process records
        clock_in_time = None
        clock_out_time = None
        break_minutes = 0
        total_hours = 0.0
        is_complete = False

        # Find the first check-in and last check-out
        check_ins = [r for r in records if r.record_type == TimeRecordType.CHECK_IN]
        check_outs = [r for r in records if r.record_type == TimeRecordType.CHECK_OUT]

        if check_ins:
            clock_in_time = check_ins[0].record_time

        if check_outs:
            clock_out_time = check_outs[-1].record_time

        # Calculate breaks
        breaks = []
        break_start = None

        for record in records:
            if record.record_type in [
                TimeRecordType.BREAK_START,
                TimeRecordType.LUNCH_START,
            ]:
                break_start = record.record_time
            elif (
                record.record_type in [TimeRecordType.BREAK_END, TimeRecordType.LUNCH_END]
                and break_start
            ):
                break_duration = (record.record_time - break_start).total_seconds() / 60
                breaks.append(break_duration)
                break_start = None

        break_minutes = sum(breaks)

        # Calculate total hours
        if clock_in_time and clock_out_time:
            total_minutes = (clock_out_time - clock_in_time).total_seconds() / 60 - break_minutes
            total_hours = total_minutes / 60
            is_complete = True

        return TimeCardSummary(
            employee_id=employee_id,
            employee_name=employee_name,
            date=date_start,
            clock_in_time=clock_in_time,
            clock_out_time=clock_out_time,
            break_minutes=break_minutes,
            total_hours=total_hours,
            is_complete=is_complete,
        )

    @staticmethod
    async def create_overtime_record(
        db: AsyncSession, tenant_id: uuid.UUID, overtime_data: OvertimeRecordCreate
    ) -> OvertimeRecord:
        """Create a new overtime record."""
        # Verify that the employee exists
        stmt = select(Employee).where(
            Employee.id == overtime_data.employee_id, Employee.tenant_id == tenant_id
        )
        result = await db.execute(stmt)
        employee = result.scalars().first()

        if not employee:
            raise NotFoundError(f"Employee with id {overtime_data.employee_id} not found")

        # Create overtime record
        overtime_record = OvertimeRecord(tenant_id=tenant_id, **overtime_data.model_dump())

        db.add(overtime_record)
        await db.commit()
        await db.refresh(overtime_record)

        # If this is banked overtime, create or update the time bank
        if overtime_record.is_banked:
            await TimeTrackingService.add_overtime_to_timebank(
                db,
                tenant_id,
                overtime_record.employee_id,
                overtime_record.id,
                overtime_record.duration_minutes,
            )

        return overtime_record

    @staticmethod
    async def get_overtime_record(
        db: AsyncSession, tenant_id: uuid.UUID, record_id: uuid.UUID
    ) -> OvertimeRecord:
        """Get an overtime record by ID."""
        stmt = select(OvertimeRecord).where(
            OvertimeRecord.id == record_id, OvertimeRecord.tenant_id == tenant_id
        )
        result = await db.execute(stmt)
        record = result.scalars().first()

        if not record:
            raise NotFoundError(f"Overtime record with id {record_id} not found")

        return record

    @staticmethod
    async def get_overtime_records(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        employee_id: Optional[uuid.UUID] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        is_approved: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[OvertimeRecord]:
        """Get overtime records with optional filtering."""
        query = select(OvertimeRecord).where(OvertimeRecord.tenant_id == tenant_id)

        # Apply filters if provided
        if employee_id:
            query = query.where(OvertimeRecord.employee_id == employee_id)

        if start_date:
            query = query.where(OvertimeRecord.date >= start_date)

        if end_date:
            query = query.where(OvertimeRecord.date <= end_date)

        if is_approved is not None:
            query = query.where(OvertimeRecord.is_approved == is_approved)

        # Apply pagination and ordering
        query = query.order_by(OvertimeRecord.date.desc()).offset(skip).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def update_overtime_record(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        record_id: uuid.UUID,
        record_data: OvertimeRecordUpdate,
    ) -> OvertimeRecord:
        """Update an overtime record."""
        # Get the record first to ensure it exists
        overtime_record = await TimeTrackingService.get_overtime_record(db, tenant_id, record_id)

        # Keep track of old values
        old_is_banked = overtime_record.is_banked
        old_duration = overtime_record.duration_minutes

        # Update attributes
        update_data = record_data.model_dump(exclude_unset=True)

        # Update the fields
        for key, value in update_data.items():
            setattr(overtime_record, key, value)

        await db.commit()
        await db.refresh(overtime_record)

        # Handle updates to time bank if banked status or duration changes
        if "is_banked" in update_data or "duration_minutes" in update_data:
            # Remove from time bank if no longer banked
            if old_is_banked and not overtime_record.is_banked:
                await TimeTrackingService.remove_overtime_from_timebank(
                    db,
                    tenant_id,
                    overtime_record.employee_id,
                    overtime_record.id,
                    old_duration,
                )
            # Add to time bank if newly banked
            elif not old_is_banked and overtime_record.is_banked:
                await TimeTrackingService.add_overtime_to_timebank(
                    db,
                    tenant_id,
                    overtime_record.employee_id,
                    overtime_record.id,
                    overtime_record.duration_minutes,
                )
            # Update time bank if duration changed
            elif (
                old_is_banked
                and overtime_record.is_banked
                and old_duration != overtime_record.duration_minutes
            ):
                # Remove old, add new
                await TimeTrackingService.remove_overtime_from_timebank(
                    db,
                    tenant_id,
                    overtime_record.employee_id,
                    overtime_record.id,
                    old_duration,
                )
                await TimeTrackingService.add_overtime_to_timebank(
                    db,
                    tenant_id,
                    overtime_record.employee_id,
                    overtime_record.id,
                    overtime_record.duration_minutes,
                )

        return overtime_record

    @staticmethod
    async def delete_overtime_record(
        db: AsyncSession, tenant_id: uuid.UUID, record_id: uuid.UUID
    ) -> bool:
        """Delete an overtime record."""
        # Get the record first to handle time bank updates
        overtime_record = await TimeTrackingService.get_overtime_record(db, tenant_id, record_id)

        # Remove from time bank if banked
        if overtime_record.is_banked:
            await TimeTrackingService.remove_overtime_from_timebank(
                db,
                tenant_id,
                overtime_record.employee_id,
                overtime_record.id,
                overtime_record.duration_minutes,
            )

        # Delete the record
        stmt = (
            delete(OvertimeRecord)
            .where(OvertimeRecord.id == record_id, OvertimeRecord.tenant_id == tenant_id)
            .returning(OvertimeRecord.id)
        )

        result = await db.execute(stmt)
        deleted_id = result.scalars().first()

        if not deleted_id:
            raise NotFoundError(f"Overtime record with id {record_id} not found")

        await db.commit()
        return True

    # Time Bank Methods
    @staticmethod
    async def get_or_create_timebank(
        db: AsyncSession, tenant_id: uuid.UUID, employee_id: uuid.UUID
    ) -> TimeBank:
        """Get or create a time bank for an employee."""
        # Try to get existing time bank
        stmt = select(TimeBank).where(
            TimeBank.employee_id == employee_id, TimeBank.tenant_id == tenant_id
        )
        result = await db.execute(stmt)
        time_bank = result.scalars().first()

        if time_bank:
            return time_bank

        # Create new time bank
        time_bank = TimeBank(
            tenant_id=tenant_id,
            employee_id=employee_id,
            balance_minutes=0,
            last_updated=datetime.now(),
        )

        db.add(time_bank)
        await db.commit()
        await db.refresh(time_bank)
        return time_bank

    @staticmethod
    async def add_overtime_to_timebank(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        employee_id: uuid.UUID,
        overtime_id: uuid.UUID,
        minutes: int,
    ) -> Tuple[TimeBank, TimeBankTransaction]:
        """Add overtime to an employee's time bank."""
        # Get or create time bank
        time_bank = await TimeTrackingService.get_or_create_timebank(db, tenant_id, employee_id)

        # Create transaction
        transaction = TimeBankTransaction(
            tenant_id=tenant_id,
            time_bank_id=time_bank.id,
            transaction_type=TimeBankTransactionType.DEPOSIT,
            minutes=minutes,
            reference_id=overtime_id,
            reference_type="overtime",
            description=f"Overtime {minutes} minutes added to time bank",
        )

        db.add(transaction)

        # Update time bank balance
        time_bank.balance_minutes += minutes
        time_bank.last_updated = datetime.now()

        await db.commit()
        await db.refresh(time_bank)
        await db.refresh(transaction)

        return time_bank, transaction

    @staticmethod
    async def remove_overtime_from_timebank(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        employee_id: uuid.UUID,
        overtime_id: uuid.UUID,
        minutes: int,
    ) -> TimeBank:
        """Remove overtime from an employee's time bank."""
        # Get time bank
        stmt = select(TimeBank).where(
            TimeBank.employee_id == employee_id, TimeBank.tenant_id == tenant_id
        )
        result = await db.execute(stmt)
        time_bank = result.scalars().first()

        if not time_bank:
            raise NotFoundError(f"Time bank not found for employee {employee_id}")

        # Find existing transaction for this overtime
        stmt = select(TimeBankTransaction).where(
            TimeBankTransaction.time_bank_id == time_bank.id,
            TimeBankTransaction.reference_id == overtime_id,
            TimeBankTransaction.reference_type == "overtime",
            TimeBankTransaction.transaction_type == TimeBankTransactionType.DEPOSIT,
        )
        result = await db.execute(stmt)
        transaction = result.scalars().first()

        if not transaction:
            # This overtime was not in the timebank
            return time_bank

        # Update time bank balance
        time_bank.balance_minutes -= transaction.minutes
        time_bank.last_updated = datetime.now()

        # Delete the transaction
        await db.delete(transaction)

        await db.commit()
        await db.refresh(time_bank)

        return time_bank

    @staticmethod
    async def add_transaction_to_timebank(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        transaction_data: TimeBankTransactionCreate,
    ) -> Tuple[TimeBank, TimeBankTransaction]:
        """Add a transaction to a time bank."""
        # Verify that the time bank exists
        stmt = select(TimeBank).where(
            TimeBank.id == transaction_data.time_bank_id,
            TimeBank.tenant_id == tenant_id,
        )
        result = await db.execute(stmt)
        time_bank = result.scalars().first()

        if not time_bank:
            raise NotFoundError(f"Time bank with id {transaction_data.time_bank_id} not found")

        # Create transaction
        transaction = TimeBankTransaction(tenant_id=tenant_id, **transaction_data.model_dump())

        # Update time bank balance
        time_bank.balance_minutes += transaction.minutes
        time_bank.last_updated = datetime.utcnow()

        db.add(transaction)
        await db.commit()
        await db.refresh(time_bank)
        await db.refresh(transaction)
        return time_bank, transaction

    # --- Time Mirror (Espelho de Ponto) ---
    @staticmethod
    async def generate_time_mirror(
        db: AsyncSession, tenant_id: uuid.UUID, request_data: GenerateTimeMirrorRequest
    ) -> TimeMirror:
        """
        Generates a time mirror (espelho de ponto) for an employee for a given period.
        """
        # Verify that the employee exists
        employee = await db.get(Employee, request_data.employee_id)
        if not employee or employee.tenant_id != tenant_id:
            raise NotFoundError(
                f"Employee with id {request_data.employee_id} not found in this tenant."
            )

        # Ensure start_date is before end_date
        if request_data.start_date >= request_data.end_date:
            raise ValidationError("Start date must be before end date.")

        # Convert date to datetime for querying
        start_datetime = datetime.combine(request_data.start_date, datetime.min.time())
        end_datetime = datetime.combine(request_data.end_date, datetime.max.time())

        # Check if a time mirror already exists for this period and employee
        existing_mirror_stmt = select(TimeMirror).where(
            TimeMirror.employee_id == request_data.employee_id,
            TimeMirror.tenant_id == tenant_id,
            TimeMirror.start_date == start_datetime,
            TimeMirror.end_date == end_datetime,
            TimeMirror.status != TimeMirrorStatus.REJECTED,  # Allow regeneration if rejected
        )
        existing_mirror_result = await db.execute(existing_mirror_stmt)
        existing_mirror = existing_mirror_result.scalars().first()

        if existing_mirror and existing_mirror.status not in [
            TimeMirrorStatus.DRAFT,
            TimeMirrorStatus.REJECTED,
        ]:
            raise ValidationError(
                f"A time mirror for employee {request_data.employee_id} "
                f"for the period {request_data.start_date} to {request_data.end_date} "
                f"already exists with status {existing_mirror.status}."
            )

        if existing_mirror and existing_mirror.status == TimeMirrorStatus.DRAFT:
            # Delete existing draft mirror and its records to regenerate
            await db.execute(delete(TimeMirror).where(TimeMirror.id == existing_mirror.id))
            # Note: Associations in hr_time_mirror_records might need explicit deletion
            # depending on cascade settings, but SQLAlchemy often handles this.

        # Fetch all relevant time records for the employee within the given date range
        time_records_stmt = (
            select(TimeRecord)
            .where(
                TimeRecord.employee_id == request_data.employee_id,
                TimeRecord.tenant_id == tenant_id,
                TimeRecord.record_time >= start_datetime,
                TimeRecord.record_time <= end_datetime,
            )
            .order_by(TimeRecord.record_time)
        )

        time_records_result = await db.execute(time_records_stmt)
        all_period_records = time_records_result.scalars().all()

        total_worked_minutes = 0
        # More sophisticated logic needed here to pair check-ins/outs and handle breaks
        # For now, a simplified calculation:
        daily_worked_minutes = {}  # date -> minutes

        for i in range(len(all_period_records) - 1):
            record1 = all_period_records[i]
            all_period_records[i + 1]

            current_date_key = record1.record_time.date()
            if current_date_key not in daily_worked_minutes:
                daily_worked_minutes[current_date_key] = {
                    "worked": 0,
                    "last_check_in": None,
                    "break": 0,
                }

            if record1.record_type == TimeRecordType.CHECK_IN:
                daily_worked_minutes[current_date_key]["last_check_in"] = record1.record_time
            elif (
                record1.record_type == TimeRecordType.CHECK_OUT
                and daily_worked_minutes[current_date_key]["last_check_in"]
            ):
                duration = (
                    record1.record_time - daily_worked_minutes[current_date_key]["last_check_in"]
                ).total_seconds() / 60
                daily_worked_minutes[current_date_key]["worked"] += duration
                # Reset for next check-in
                daily_worked_minutes[current_date_key]["last_check_in"] = None
            # Break must be within a work period
            elif (
                record1.record_type == TimeRecordType.BREAK_START
                and daily_worked_minutes[current_date_key]["last_check_in"]
            ):
                # Find corresponding BREAK_END
                for j in range(i + 1, len(all_period_records)):
                    next_rec = all_period_records[j]
                    if (
                        next_rec.record_type == TimeRecordType.BREAK_END
                        and next_rec.record_time.date() == current_date_key
                    ):
                        break_duration = (
                            next_rec.record_time - record1.record_time
                        ).total_seconds() / 60
                        daily_worked_minutes[current_date_key]["break"] += break_duration
                        i = j  # Skip the BREAK_END record in the outer loop
                        break

        for day_data in daily_worked_minutes.values():
            total_worked_minutes += day_data["worked"] - day_data["break"]

        total_worked_minutes = max(0, int(total_worked_minutes))  # Ensure non-negative

        # Fetch approved overtime records for the period
        overtime_records_stmt = select(OvertimeRecord).where(
            OvertimeRecord.employee_id == request_data.employee_id,
            OvertimeRecord.tenant_id == tenant_id,
            OvertimeRecord.date >= start_datetime,  # Assuming OvertimeRecord.date is a DateTime
            OvertimeRecord.date <= end_datetime,
            OvertimeRecord.is_approved,
        )
        overtime_records_result = await db.execute(overtime_records_stmt)
        approved_overtime_records = overtime_records_result.scalars().all()

        total_overtime_minutes = sum(ot.duration_minutes for ot in approved_overtime_records)

        # Placeholder for absence minutes - requires leave module integration
        total_absence_minutes = 0

        # Create TimeMirror instance
        new_mirror = TimeMirror(
            tenant_id=tenant_id,
            employee_id=request_data.employee_id,
            start_date=start_datetime,
            end_date=end_datetime,
            generation_date=datetime.utcnow(),
            total_worked_minutes=total_worked_minutes,
            total_overtime_minutes=total_overtime_minutes,
            total_absence_minutes=total_absence_minutes,  # Placeholder
            status=(
                TimeMirrorStatus.APPROVED if request_data.auto_approve else TimeMirrorStatus.DRAFT
            ),
            notes="Generated automatically.",
        )

        if request_data.auto_approve:
            new_mirror.approved_at = datetime.utcnow()
            # new_mirror.approved_by = current_user.id # Assuming a current_user is available

        db.add(new_mirror)
        await db.flush()  # Flush to get the new_mirror.id for associations

        # Associate time records with the mirror
        # This assumes that the secondary table `hr_time_mirror_records` is set up correctly
        # and SQLAlchemy can handle the association through the relationship.
        # If direct insertion into the association table is needed, it would be:
        # for record in all_period_records:
        #     assoc = TimeMirrorRecord(time_mirror_id=new_mirror.id, time_record_id=record.id, tenant_id=tenant_id)  # noqa: E501
        #     db.add(assoc)
        new_mirror.time_records.extend(all_period_records)

        await db.commit()
        await db.refresh(new_mirror, ["time_records"])  # Refresh to load associated records

        return new_mirror

    @staticmethod
    async def get_time_mirror(
        db: AsyncSession, tenant_id: uuid.UUID, mirror_id: uuid.UUID
    ) -> Optional[TimeMirror]:
        stmt = (
            select(TimeMirror)
            .options(joinedload(TimeMirror.time_records))
            .where(TimeMirror.id == mirror_id, TimeMirror.tenant_id == tenant_id)
        )
        result = await db.execute(stmt)
        return result.scalars().first()

    @staticmethod
    async def list_time_mirrors(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        employee_id: Optional[uuid.UUID] = None,
        status: Optional[TimeMirrorStatus] = None,
        start_date_filter: Optional[date] = None,
        end_date_filter: Optional[date] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[TimeMirror]:
        query = select(TimeMirror).where(TimeMirror.tenant_id == tenant_id)
        if employee_id:
            query = query.where(TimeMirror.employee_id == employee_id)
        if status:
            query = query.where(TimeMirror.status == status)
        if start_date_filter:
            query = query.where(
                TimeMirror.start_date >= datetime.combine(start_date_filter, datetime.min.time())
            )
        if end_date_filter:
            query = query.where(
                TimeMirror.end_date <= datetime.combine(end_date_filter, datetime.max.time())
            )

        query = query.order_by(TimeMirror.start_date.desc()).offset(skip).limit(limit)
        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def update_time_mirror_status(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        mirror_id: uuid.UUID,
        new_status: TimeMirrorStatus,
        user_id: Optional[uuid.UUID] = None,  # User performing the action
        notes: Optional[str] = None,
    ) -> TimeMirror:
        mirror = await TimeTrackingService.get_time_mirror(db, tenant_id, mirror_id)
        if not mirror:
            raise NotFoundError(f"Time mirror with id {mirror_id} not found.")

        mirror.status = new_status
        now = datetime.utcnow()

        if new_status == TimeMirrorStatus.SUBMITTED:
            mirror.submitted_at = now
        elif new_status == TimeMirrorStatus.APPROVED:
            mirror.approved_at = now
            mirror.approved_by = user_id
        elif new_status == TimeMirrorStatus.REJECTED:
            # Potentially clear approval fields if previously approved then rejected
            mirror.approved_at = None
            mirror.approved_by = None
        elif new_status == TimeMirrorStatus.FINALIZED:
            mirror.finalized_at = now

        if notes:
            mirror.notes = notes if not mirror.notes else f"{mirror.notes}\\n{notes}"

        await db.commit()
        await db.refresh(mirror)
        return mirror

    # --- Overtime Rules ---
    @staticmethod
    async def create_overtime_rule(
        db: AsyncSession, tenant_id: uuid.UUID, rule_data: OvertimeRuleCreate
    ) -> OvertimeRule:
        new_rule = OvertimeRule(tenant_id=tenant_id, **rule_data.model_dump())
        db.add(new_rule)
        await db.commit()
        await db.refresh(new_rule)
        return new_rule

    @staticmethod
    async def get_overtime_rule(
        db: AsyncSession, tenant_id: uuid.UUID, rule_id: uuid.UUID
    ) -> Optional[OvertimeRule]:
        stmt = select(OvertimeRule).where(
            OvertimeRule.id == rule_id, OvertimeRule.tenant_id == tenant_id
        )
        result = await db.execute(stmt)
        return result.scalars().first()

    @staticmethod
    async def list_overtime_rules(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        is_active: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[OvertimeRule]:
        query = select(OvertimeRule).where(OvertimeRule.tenant_id == tenant_id)
        if is_active is not None:
            query = query.where(OvertimeRule.is_active == is_active)

        query = query.order_by(OvertimeRule.name).offset(skip).limit(limit)
        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def update_overtime_rule(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        rule_id: uuid.UUID,
        rule_data: OvertimeRuleUpdate,
    ) -> OvertimeRule:
        rule = await TimeTrackingService.get_overtime_rule(db, tenant_id, rule_id)
        if not rule:
            raise NotFoundError(f"Overtime rule with id {rule_id} not found.")

        update_data = rule_data.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(rule, key, value)

        await db.commit()
        await db.refresh(rule)
        return rule

    @staticmethod
    async def delete_overtime_rule(
        db: AsyncSession, tenant_id: uuid.UUID, rule_id: uuid.UUID
    ) -> bool:
        stmt = (
            delete(OvertimeRule)
            .where(OvertimeRule.id == rule_id, OvertimeRule.tenant_id == tenant_id)
            .returning(OvertimeRule.id)
        )

        result = await db.execute(stmt)
        deleted_id = result.scalars().first()

        if not deleted_id:
            raise NotFoundError(f"Overtime rule with id {rule_id} not found")

        await db.commit()
        return True


# Create a singleton instance
time_tracking_service = TimeTrackingService()
