from typing import AsyncGenerator, AsyncContextManager
from sqlalchemy.ext.asyncio import AsyncSession
from app.db.session import AsyncSessionLocal


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    FastAPI dependency that provides an asynchronous database session per request,
    managed within a transaction context.
    """
    async with AsyncSessionLocal() as session:
        yield session
        # The 'async with' context manager handles commit/rollback/close automatically.
        # Commit is done if the 'with' block exits without exceptions.
        # Rollback is done if an exception occurs within the 'with' block.
        # The session is closed when exiting the 'with' block.


def get_async_session() -> AsyncContextManager[AsyncSession]:
    """
    Returns an async context manager for database sessions.
    This is used for WebSocket handlers and other non-request contexts.

    Usage:
        async with get_async_session() as session:
            # Use session here
    """
    return AsyncSessionLocal()
