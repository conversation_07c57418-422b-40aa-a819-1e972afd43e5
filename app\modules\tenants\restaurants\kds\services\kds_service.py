import uuid
from typing import Optional, Sequence, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.modules.tenants.restaurants.kds.models.kitchen_order import KitchenOrder
from app.modules.tenants.restaurants.kds.schemas.kitchen_order import (
    KitchenOrderCreate,
    KitchenOrderRead,
)

# Importar funções de emissão de WebSocket
from app.modules.tenants.restaurants.kds.websockets.kds_websockets import (  # noqa: E402
    emit_new_order,
    emit_order_update,
)


class KdsService:
    """Serviço para gerenciar Pedidos da Cozinha (KDS)."""

    async def get_order_by_id(
        self, db: AsyncSession, *, order_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Optional[KitchenOrder]:
        """Busca um pedido específico pelo ID, garantindo que pertence ao tenant."""
        statement = select(KitchenOrder).where(
            KitchenOrder.id == order_id, KitchenOrder.tenant_id == tenant_id
        )
        result = await db.execute(statement)
        return result.scalar_one_or_none()

    async def get_orders(
        self,
        db: AsyncSession,
        *,
        tenant_id: uuid.UUID,
        status: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> Sequence[KitchenOrder]:
        """Busca pedidos da cozinha para o tenant, opcionalmente filtrados por status."""
        statement = select(KitchenOrder).where(KitchenOrder.tenant_id == tenant_id)

        if status:
            statement = statement.where(KitchenOrder.status == status)

        # Ordenar por data de criação (mais recentes primeiro)
        statement = statement.order_by(KitchenOrder.created_at.desc())
        statement = statement.offset(skip).limit(limit)

        result = await db.execute(statement)
        return result.scalars().all()

    async def create_order(
        self, db: AsyncSession, *, order_in: KitchenOrderCreate, tenant_id: uuid.UUID
    ) -> KitchenOrder:
        """Cria um novo pedido na cozinha para o tenant especificado."""
        db_obj = KitchenOrder(**order_in.model_dump(), tenant_id=tenant_id)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)

        # Emitir evento WebSocket após criar o pedido
        await emit_new_order(
            tenant_id=tenant_id,
            order=KitchenOrderRead.model_validate(db_obj),
        )

        return db_obj

    async def update_order_status(
        self,
        db: AsyncSession,
        *,
        order_id: uuid.UUID,
        new_status: str,
        tenant_id: uuid.UUID,
    ) -> Optional[KitchenOrder]:
        """Atualiza o status de um pedido da cozinha."""
        # Verificar se o pedido existe e pertence ao tenant
        db_obj = await self.get_order_by_id(db, order_id=order_id, tenant_id=tenant_id)
        if not db_obj:
            return None

        # Atualizar o status
        db_obj.status = new_status
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)

        # Emitir evento WebSocket após atualizar o pedido
        await emit_order_update(
            tenant_id=tenant_id,
            order=KitchenOrderRead.model_validate(db_obj),
        )

        return db_obj

    async def update_order(
        self,
        db: AsyncSession,
        *,
        order_id: uuid.UUID,
        tenant_id: uuid.UUID,
        new_status: Optional[str] = None,
        new_order_details: Optional[Dict[str, Any]] = None,
    ) -> Optional[KitchenOrder]:
        """Atualiza um pedido da cozinha (status e/ou detalhes)."""
        # Verificar se o pedido existe e pertence ao tenant
        db_obj = await self.get_order_by_id(db, order_id=order_id, tenant_id=tenant_id)
        if not db_obj:
            return None

        # Atualizar campos se fornecidos
        if new_status is not None:
            db_obj.status = new_status

        if new_order_details is not None:
            db_obj.order_details = new_order_details

            # Verificar se todos os itens estão concluídos e mudar status automaticamente
            items = new_order_details.get('items', [])
            if items and all(item.get('done', False) for item in items):
                # Se todos os itens estão concluídos e o pedido está em preparo, mover para ready
                if db_obj.status == 'preparing':
                    db_obj.status = 'ready'

        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)

        # Emitir evento WebSocket após atualizar o pedido
        await emit_order_update(
            tenant_id=tenant_id,
            order=KitchenOrderRead.model_validate(db_obj),
        )

        return db_obj


# Instância global do serviço para uso em endpoints
kds_service = KdsService()
