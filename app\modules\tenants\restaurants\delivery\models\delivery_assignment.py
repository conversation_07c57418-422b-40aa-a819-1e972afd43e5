import uuid
from datetime import datetime
from sqlalchemy import Column, ForeignKey, DateTime, String, JSON, Enum as SAEnum
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from app.db.base import Base
from app.db.base import TimestampMixin  # Corrected import path
from app.modules.tenants.restaurants.delivery.enums import DeliveryAssignmentStatus


class DeliveryAssignment(Base, TimestampMixin):
    __tablename__ = "delivery_assignments"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    online_order_id = Column(
        UUID(as_uuid=True),
        ForeignKey("online_orders.id"),
        nullable=False,
        unique=True,
        index=True,
    )
    delivery_boy_id = Column(
        UUID(as_uuid=True), ForeignKey("delivery_boys.id"), nullable=False, index=True
    )

    assignment_status = Column(
        SAEnum(DeliveryAssignmentStatus, name="delivery_assignment_status_enum"),
        # Removed create_type=False
        nullable=False,
        default=DeliveryAssignmentStatus.PENDING_ACCEPTANCE,
        # server_default=DeliveryAssignmentStatus.PENDING_ACCEPTANCE.value, # Optional
    )

    assigned_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    accepted_at = Column(DateTime, nullable=True)
    picked_up_at = Column(DateTime, nullable=True)
    delivered_at = Column(DateTime, nullable=True)
    cancelled_at = Column(DateTime, nullable=True)

    estimated_delivery_time = Column(DateTime, nullable=True)
    actual_delivery_time = Column(DateTime, nullable=True)

    delivery_address_snapshot = Column(JSON, nullable=False)
    notes = Column(String, nullable=True)

    # Relationships
    # Commented out until the module is implemented
    # online_order = relationship(
    #     "app.modules.tenants.restaurants.models.online_order.RestaurantOnlineOrder"
    # )
    delivery_boy = relationship("DeliveryBoy", back_populates="assigned_orders")
    tenant = relationship("Tenant")

    def __repr__(self):
        return f"<DeliveryAssignment(id={self.id}, order_id={self.online_order_id}, boy_id={self.delivery_boy_id})>"  # noqa: E501
