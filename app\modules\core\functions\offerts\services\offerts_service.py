from sqlalchemy.orm import Session
from typing import List, Optional
from app.modules.core.functions.offerts.models import offerts as offerts_models
from app.modules.core.functions.offerts.schemas import offerts as offerts_schemas
from app.modules.core.functions.orders.models.order import Order
from datetime import datetime
import uuid

class OffertsService:
    def __init__(self, db: Session):
        self.db = db

    def create_coupon(self, coupon: offerts_schemas.DiscountCouponCreate) -> offerts_models.DiscountCoupon:
        db_coupon = offerts_models.DiscountCoupon(**coupon.dict())
        self.db.add(db_coupon)
        self.db.commit()
        self.db.refresh(db_coupon)
        return db_coupon

    def get_coupons(self, skip: int = 0, limit: int = 100) -> List[offerts_models.DiscountCoupon]:
        return self.db.query(offerts_models.DiscountCoupon).offset(skip).limit(limit).all()

    def get_coupon_by_code(self, code: str) -> Optional[offerts_models.DiscountCoupon]:
        return self.db.query(offerts_models.DiscountCoupon).filter(offerts_models.DiscountCoupon.code == code).first()

    def apply_coupon(self, order_id: uuid.UUID, code: str, user_id: uuid.UUID) -> offerts_models.AppliedCoupon:
        coupon = self.get_coupon_by_code(code)
        if not coupon:
            raise ValueError("Invalid coupon code")

        if not coupon.is_active:
            raise ValueError("Coupon is not active")

        if coupon.valid_until and coupon.valid_until < datetime.utcnow().replace(tzinfo=None):
            raise ValueError("Coupon has expired")

        if coupon.max_uses is not None and coupon.uses_count >= coupon.max_uses:
            raise ValueError("Coupon has reached its maximum usage limit")

        # Check max uses per user
        user_uses = self.db.query(offerts_models.AppliedCoupon).filter(
            offerts_models.AppliedCoupon.coupon_id == coupon.id,
            offerts_models.AppliedCoupon.user_id == user_id
        ).count()
        if coupon.max_uses_per_user is not None and user_uses >= coupon.max_uses_per_user:
            raise ValueError("You have already used this coupon the maximum number of times")

        order = self.db.query(Order).filter(Order.id == order_id).first()
        if not order:
            raise ValueError("Order not found")

        # Calculate discount
        discount_amount = 0
        if coupon.discount_type == offerts_models.DiscountType.PERCENTAGE:
            discount_amount = (order.total * coupon.value) / 100
        elif coupon.discount_type == offerts_models.DiscountType.FIXED_AMOUNT:
            discount_amount = coupon.value

        # Ensure discount doesn't exceed order total
        discount_amount = min(discount_amount, order.total)
        
        # Apply discount to order (this assumes the Order model has a discount field)
        # order.total -= discount_amount

        # Record the applied coupon
        applied_coupon = offerts_models.AppliedCoupon(
            coupon_id=coupon.id,
            order_id=order_id,
            user_id=user_id,
            discount_amount=discount_amount
        )
        self.db.add(applied_coupon)

        # Increment usage count
        coupon.uses_count += 1

        self.db.commit()
        self.db.refresh(applied_coupon)

        return applied_coupon