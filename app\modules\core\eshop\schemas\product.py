from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, validator
from uuid import UUID
from decimal import Decimal
from datetime import datetime
from enum import Enum

from .product_category import ProductCategoryResponse
from .product_variant_group import ProductVariantGroupResponse
from .product_modifier_group import ProductModifierGroupResponse
from .product_optional_group import ProductOptionalGroupResponse
from .product_review import ProductReviewResponse
from app.core.enums import MarketType


class ProductType(str, Enum):
    PHYSICAL = "physical"
    DIGITAL = "digital"


class ProductStatus(str, Enum):
    DRAFT = "draft"
    ACTIVE = "active"
    INACTIVE = "inactive"
    OUT_OF_STOCK = "out_of_stock"


# MarketType agora importado de app.core.enums


class ApprovalStatus(str, Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"


class ProductBase(BaseModel):
    """Base schema for products."""
    name: str = Field(..., min_length=1, max_length=200, description="Product name")
    description: Optional[str] = Field(None, description="Product description")
    short_description: Optional[str] = Field(None, max_length=500, description="Short product description")
    slug: str = Field(..., min_length=1, max_length=250, description="URL-friendly product name")
    sku: Optional[str] = Field(None, max_length=100, description="Stock Keeping Unit")
    category_id: UUID = Field(..., description="Product category ID")
    
    # Product type and status
    product_type: ProductType = Field(ProductType.PHYSICAL, description="Product type")
    status: ProductStatus = Field(ProductStatus.DRAFT, description="Product status")
    
    # Pricing
    base_price: Decimal = Field(..., gt=0, description="Base product price")
    sale_price: Optional[Decimal] = Field(None, gt=0, description="Sale price (discounted)")
    cost_price: Optional[Decimal] = Field(None, gt=0, description="Cost price for profit calculation")
    
    # Stock management
    stock_quantity: int = Field(0, ge=0, description="Current stock quantity")
    low_stock_threshold: int = Field(5, ge=0, description="Low stock alert threshold")
    manage_stock: bool = Field(True, description="Whether to manage stock for this product")
    allow_backorders: bool = Field(False, description="Allow orders when out of stock")
    
    # Digital product settings
    digital_file_url: Optional[str] = Field(None, max_length=500, description="Digital file URL")
    download_limit: Optional[int] = Field(None, ge=1, description="Maximum downloads per purchase")
    download_expiry_days: Optional[int] = Field(None, ge=1, description="Days until download expires")
    
    # Shipping and delivery
    weight: Optional[Decimal] = Field(None, gt=0, description="Product weight in kg")
    dimensions: Optional[Dict[str, Any]] = Field(None, description="Product dimensions")
    shipping_required: bool = Field(True, description="Whether shipping is required")
    shipping_class: Optional[str] = Field(None, max_length=50, description="Shipping class")
    
    # SEO and metadata
    meta_title: Optional[str] = Field(None, max_length=200, description="SEO meta title")
    meta_description: Optional[str] = Field(None, description="SEO meta description")
    featured_image_url: Optional[str] = Field(None, max_length=500, description="Featured image URL")
    gallery_images: Optional[List[str]] = Field(None, description="Gallery image URLs")
    
    # Display and organization
    display_order: int = Field(0, description="Display order")
    is_featured: bool = Field(False, description="Whether product is featured")
    is_virtual: bool = Field(False, description="Whether product is virtual (no shipping)")
    
    # Additional data
    attributes: Optional[Dict[str, Any]] = Field(None, description="Custom product attributes")
    tags: Optional[List[str]] = Field(None, description="Product tags")
    
    # Fusion fields for Cuponic-EShop integration
    market_type: MarketType = Field(MarketType.B2C, description="Market type for B2B/B2C operations")
    approval_status: ApprovalStatus = Field(ApprovalStatus.PENDING, description="Product approval status")
    commission_rate: Optional[Decimal] = Field(None, ge=0, le=99.99, description="Commission rate percentage")
    legacy_cuponic_id: Optional[int] = Field(None, description="Legacy Cuponic ID for backward compatibility")

    @validator('sale_price')
    def validate_sale_price(cls, v, values):
        if v is not None and 'base_price' in values and v >= values['base_price']:
            raise ValueError('Sale price must be less than base price')
        return v

    @validator('digital_file_url')
    def validate_digital_file_url(cls, v, values):
        if values.get('product_type') == ProductType.DIGITAL and not v:
            raise ValueError('Digital products must have a digital file URL')
        return v


class ProductCreate(ProductBase):
    """Schema for creating a new product."""
    tenant_id: Optional[UUID] = Field(None, description="Tenant ID (null for global products)")
    vendor_id: UUID = Field(..., description="Vendor (TVendorSupplier) ID")


class ProductUpdate(BaseModel):
    """Schema for updating a product."""
    name: Optional[str] = Field(None, min_length=1, max_length=200, description="Product name")
    description: Optional[str] = Field(None, description="Product description")
    short_description: Optional[str] = Field(None, max_length=500, description="Short product description")
    slug: Optional[str] = Field(None, min_length=1, max_length=250, description="URL-friendly product name")
    sku: Optional[str] = Field(None, max_length=100, description="Stock Keeping Unit")
    category_id: Optional[UUID] = Field(None, description="Product category ID")
    
    # Product type and status
    product_type: Optional[ProductType] = Field(None, description="Product type")
    status: Optional[ProductStatus] = Field(None, description="Product status")
    
    # Pricing
    base_price: Optional[Decimal] = Field(None, gt=0, description="Base product price")
    sale_price: Optional[Decimal] = Field(None, gt=0, description="Sale price (discounted)")
    cost_price: Optional[Decimal] = Field(None, gt=0, description="Cost price for profit calculation")
    
    # Stock management
    stock_quantity: Optional[int] = Field(None, ge=0, description="Current stock quantity")
    low_stock_threshold: Optional[int] = Field(None, ge=0, description="Low stock alert threshold")
    manage_stock: Optional[bool] = Field(None, description="Whether to manage stock for this product")
    allow_backorders: Optional[bool] = Field(None, description="Allow orders when out of stock")
    
    # Digital product settings
    digital_file_url: Optional[str] = Field(None, max_length=500, description="Digital file URL")
    download_limit: Optional[int] = Field(None, ge=1, description="Maximum downloads per purchase")
    download_expiry_days: Optional[int] = Field(None, ge=1, description="Days until download expires")
    
    # Shipping and delivery
    weight: Optional[Decimal] = Field(None, gt=0, description="Product weight in kg")
    dimensions: Optional[Dict[str, Any]] = Field(None, description="Product dimensions")
    shipping_required: Optional[bool] = Field(None, description="Whether shipping is required")
    shipping_class: Optional[str] = Field(None, max_length=50, description="Shipping class")
    
    # SEO and metadata
    meta_title: Optional[str] = Field(None, max_length=200, description="SEO meta title")
    meta_description: Optional[str] = Field(None, description="SEO meta description")
    featured_image_url: Optional[str] = Field(None, max_length=500, description="Featured image URL")
    gallery_images: Optional[List[str]] = Field(None, description="Gallery image URLs")
    
    # Display and organization
    display_order: Optional[int] = Field(None, description="Display order")
    is_featured: Optional[bool] = Field(None, description="Whether product is featured")
    is_virtual: Optional[bool] = Field(None, description="Whether product is virtual (no shipping)")
    
    # Additional data
    attributes: Optional[Dict[str, Any]] = Field(None, description="Custom product attributes")
    tags: Optional[List[str]] = Field(None, description="Product tags")
    
    # Fusion fields for Cuponic-EShop integration
    market_type: Optional[MarketType] = Field(None, description="Market type for B2B/B2C operations")
    approval_status: Optional[ApprovalStatus] = Field(None, description="Product approval status")
    commission_rate: Optional[Decimal] = Field(None, ge=0, le=99.99, description="Commission rate percentage")
    legacy_cuponic_id: Optional[int] = Field(None, description="Legacy Cuponic ID for backward compatibility")


class ProductResponse(ProductBase):
    """Schema for product responses."""
    id: UUID
    tenant_id: Optional[UUID]
    vendor_id: UUID
    average_rating: Decimal
    review_count: int
    created_at: datetime
    updated_at: datetime
    
    # Additional approval metadata for responses
    approved_at: Optional[datetime] = Field(None, description="Approval timestamp")
    approved_by: Optional[UUID] = Field(None, description="Approver user ID")
    rejection_reason: Optional[str] = Field(None, description="Reason for rejection")
    
    class Config:
        from_attributes = True


class ProductListResponse(BaseModel):
    """Schema for product list responses."""
    id: UUID
    name: str
    slug: str
    base_price: Decimal
    sale_price: Optional[Decimal]
    featured_image_url: Optional[str]
    average_rating: Decimal
    review_count: int
    status: ProductStatus
    is_featured: bool
    stock_quantity: int
    
    # Fusion fields for filtering and display
    market_type: MarketType
    approval_status: ApprovalStatus
    
    class Config:
        from_attributes = True


class ProductDetailResponse(ProductResponse):
    """Schema for detailed product responses."""
    category: Optional[ProductCategoryResponse]
    variant_groups: List[ProductVariantGroupResponse] = []
    modifier_groups: List[ProductModifierGroupResponse] = []
    optional_groups: List[ProductOptionalGroupResponse] = []
    reviews: List[ProductReviewResponse] = []
    
    class Config:
        from_attributes = True
