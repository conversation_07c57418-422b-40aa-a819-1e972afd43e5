import uuid
from typing import Optional, Sequence
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_

# from app.modules.shared.inventory import models, schemas #
# Removido - Importar específicos abaixo
from app.modules.core.functions.inventory.models.inventory_item import (
    InventoryItem,
    InventoryCategory
)
from app.modules.core.functions.inventory.schemas.inventory_item import (
    InventoryItemCreate,
    InventoryItemUpdate,
    InventoryCategoryCreate,
    InventoryCategoryUpdate,
)

# Supondo que existam exceções customizadas
from app.core.exceptions import NotFoundError  # noqa: E402


class InventoryService:
    """Serviço para operações CRUD de itens de inventário, com escopo de tenant."""

    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session

    async def create_item(
        # Removido schemas.
        self,
        db: AsyncSession,
        *,
        item_in: InventoryItemCreate,
        tenant_id: uuid.UUID,
    ) -> InventoryItem:
        """
        Cria um novo item de inventário associado a um tenant específico.
        """
        db_item = InventoryItem(**item_in.model_dump(), tenant_id=tenant_id)  # Corrigido
        db.add(db_item)
        await db.commit()
        await db.refresh(db_item)
        return db_item

    async def get_item(
        self, db: AsyncSession, *, item_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Optional[InventoryItem]:  # Corrigido
        """
        Obtém um item de inventário específico pelo ID, dentro do escopo do tenant.
        """
        result = await db.execute(
            select(InventoryItem).where(  # Corrigido
                InventoryItem.id == item_id, InventoryItem.tenant_id == tenant_id
            )  # Corrigido
        )
        return result.scalars().first()

    async def get_item_or_404(
        self, db: AsyncSession, *, item_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> InventoryItem:  # Corrigido
        """
        Obtém um item de inventário específico ou levanta NotFoundError se não encontrado
        para o tenant especificado.
        """
        db_item = await self.get_item(db=db, item_id=item_id, tenant_id=tenant_id)
        if not db_item:
            raise NotFoundError(f"Inventory item with id {item_id} not found for this tenant.")
        return db_item

    async def get_items(
        self,
        db: AsyncSession,
        *,
        tenant_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        name: Optional[str] = None,
        sku: Optional[str] = None,
    ) -> Sequence[InventoryItem]:  # Corrigido
        """
        Lista itens de inventário para um tenant específico, com filtros opcionais e paginação.
        """
        query = select(InventoryItem).where(InventoryItem.tenant_id == tenant_id)  # Corrigido

        if name:
            # Usando ilike para busca case-insensitive
            query = query.where(InventoryItem.name.ilike(f"%{name}%"))  # Corrigido
        if sku:
            query = query.where(InventoryItem.sku == sku)  # Corrigido

        query = (
            query.offset(skip).limit(limit).order_by(InventoryItem.name)
        )  # Corrigido # Ordena por nome por padrão

        result = await db.execute(query)
        return result.scalars().all()

    async def update_item(
        self,
        db: AsyncSession,
        *,
        item_id: uuid.UUID,
        item_in: InventoryItemUpdate,  # Corrigido
        tenant_id: uuid.UUID,
    ) -> InventoryItem:  # Corrigido
        """
        Atualiza um item de inventário existente para o tenant especificado.
        A quantidade é atualizada separadamente via adjust_stock.
        """
        db_item = await self.get_item_or_404(db=db, item_id=item_id, tenant_id=tenant_id)

        update_data = item_in.model_dump(exclude_unset=True)  # Pega apenas os campos enviados

        for field, value in update_data.items():
            setattr(db_item, field, value)

        db.add(db_item)
        await db.commit()
        await db.refresh(db_item)
        return db_item

    async def delete_item(
        self, db: AsyncSession, *, item_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> InventoryItem:  # Corrigido
        """
        Exclui um item de inventário para o tenant especificado.
        """
        db_item = await self.get_item_or_404(db=db, item_id=item_id, tenant_id=tenant_id)
        await db.delete(db_item)
        await db.commit()
        # Retorna o objeto deletado (sem refresh, pois não existe mais no DB)
        # Se precisar retornar apenas um status, pode mudar o tipo de retorno
        return db_item

    async def adjust_stock(
        self, db: AsyncSession, *, item_id: uuid.UUID, tenant_id: uuid.UUID, change: int
    ) -> InventoryItem:  # Corrigido
        """
        Ajusta a quantidade em estoque de um item específico para o tenant.
        Permite valores positivos (adicionar) e negativos (remover).
        Garante que a quantidade não fique negativa.
        """
        db_item = await self.get_item_or_404(db=db, item_id=item_id, tenant_id=tenant_id)

        new_quantity = db_item.quantity + change

        if new_quantity < 0:
            raise ValueError("Stock quantity cannot be negative.")

        db_item.quantity = new_quantity
        db.add(db_item)
        await db.commit()
        await db.refresh(db_item)
        
        await self.check_stock_level(db=db, item_id=db_item.id)

        return db_item

    async def get_item_by_product(
        self, db: AsyncSession, *, tenant_id: uuid.UUID, product_id: uuid.UUID, variant_id: Optional[uuid.UUID] = None
    ) -> Optional[InventoryItem]:
        """
        Obtém um item de inventário pelo ID do produto/variante.
        """
        query = select(InventoryItem).where(
            InventoryItem.tenant_id == tenant_id,
            InventoryItem.product_id == product_id
        )
        if variant_id:
            query = query.where(InventoryItem.variant_id == variant_id)
        else:
            query = query.where(InventoryItem.variant_id.is_(None))
        
        result = await db.execute(query)
        return result.scalars().first()

    async def decrease_stock_on_order(self, db: AsyncSession, *, tenant_id: uuid.UUID, product_id: uuid.UUID, quantity: int, variant_id: Optional[uuid.UUID] = None):
        """
        Decrements the stock for a product/variant when an order is placed.
        """
        item = await self.get_item_by_product(db=db, tenant_id=tenant_id, product_id=product_id, variant_id=variant_id)
        if not item:
            # Decide if we should create an inventory item on the fly or raise an error
            raise NotFoundError(f"Inventory item for product {product_id} not found.")

        await self.adjust_stock(db=db, item_id=item.id, tenant_id=tenant_id, change=-quantity)

    async def increase_stock_on_cancellation(self, db: AsyncSession, *, tenant_id: uuid.UUID, product_id: uuid.UUID, quantity: int, variant_id: Optional[uuid.UUID] = None):
        """
        Increments the stock for a product/variant when an order is cancelled.
        """
        item = await self.get_item_by_product(db=db, tenant_id=tenant_id, product_id=product_id, variant_id=variant_id)
        if not item:
            raise NotFoundError(f"Inventory item for product {product_id} not found.")
            
        await self.adjust_stock(db=db, item_id=item.id, tenant_id=tenant_id, change=quantity)

    async def check_stock_level(self, db: AsyncSession, *, item_id: uuid.UUID):
        """
        Checks if the stock has fallen below the threshold and sends a notification.
        """
        result = await db.execute(select(InventoryItem).where(InventoryItem.id == item_id))
        item = result.scalars().first()

        if item and item.quantity <= item.low_stock_threshold:
            print(f"ALERT: Stock for item {item.name} (ID: {item.id}) is low! Current: {item.quantity}, Threshold: {item.low_stock_threshold}")
            # TODO: Integrate with core/notifications service to send an alert
            # notification_service = NotificationService(db)
            # await notification_service.create_low_stock_notification(tenant_id=item.tenant_id, item_id=item.id)
        pass


class InventoryCategoryService:
    """Serviço para gerenciamento de categorias de inventário."""

    async def get_categories(
        self,
        db: AsyncSession,
        *,
        tenant_id: uuid.UUID,
        active_only: bool = True
    ) -> Sequence[InventoryCategory]:
        """Lista categorias do tenant."""
        query = select(InventoryCategory).where(InventoryCategory.tenant_id == tenant_id)

        if active_only:
            query = query.where(InventoryCategory.is_active == True)

        query = query.order_by(InventoryCategory.display_order, InventoryCategory.name)
        result = await db.execute(query)
        return result.scalars().all()

    async def get_category_by_id(
        self,
        db: AsyncSession,
        *,
        category_id: uuid.UUID,
        tenant_id: uuid.UUID
    ) -> Optional[InventoryCategory]:
        """Obtém categoria por ID."""
        result = await db.execute(
            select(InventoryCategory).where(
                InventoryCategory.id == category_id,
                InventoryCategory.tenant_id == tenant_id
            )
        )
        return result.scalars().first()

    async def create_category(
        self,
        db: AsyncSession,
        *,
        category_data: InventoryCategoryCreate,
        tenant_id: uuid.UUID
    ) -> InventoryCategory:
        """Cria nova categoria."""
        db_category = InventoryCategory(
            tenant_id=tenant_id,
            **category_data.model_dump()
        )
        db.add(db_category)
        await db.commit()
        await db.refresh(db_category)
        return db_category

    async def update_category(
        self,
        db: AsyncSession,
        *,
        category_id: uuid.UUID,
        category_data: InventoryCategoryUpdate,
        tenant_id: uuid.UUID
    ) -> InventoryCategory:
        """Atualiza categoria."""
        db_category = await self.get_category_by_id(
            db=db, category_id=category_id, tenant_id=tenant_id
        )
        if not db_category:
            raise NotFoundError(f"Category with id {category_id} not found")

        update_data = category_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_category, field, value)

        await db.commit()
        await db.refresh(db_category)
        return db_category

    async def delete_category(
        self,
        db: AsyncSession,
        *,
        category_id: uuid.UUID,
        tenant_id: uuid.UUID
    ) -> InventoryCategory:
        """Exclui categoria (soft delete)."""
        db_category = await self.get_category_by_id(
            db=db, category_id=category_id, tenant_id=tenant_id
        )
        if not db_category:
            raise NotFoundError(f"Category with id {category_id} not found")

        db_category.is_active = False
        await db.commit()
        await db.refresh(db_category)
        return db_category


# Serviços disponíveis para importação
# As instâncias devem ser criadas conforme necessário nos endpoints/dependências
__all__ = ["InventoryService", "InventoryCategoryService"]
