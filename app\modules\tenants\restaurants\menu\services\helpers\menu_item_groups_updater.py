import logging
from typing import Optional, List
from sqlalchemy import delete
from sqlalchemy.ext.asyncio import AsyncSession
import uuid

# Import models
from app.modules.tenants.restaurants.menu.models.menu_item import MenuItem
from app.modules.core.functions.customizations.models.variant_group import VariantGroup
from app.modules.core.functions.customizations.models.variant_option import VariantOption
from app.modules.core.functions.customizations.models.modifier_group import ModifierGroup
from app.modules.core.functions.customizations.models.modifier_option import ModifierOption
from app.modules.core.functions.customizations.models.optional_group import OptionalGroup
from app.modules.core.functions.customizations.models.optional_option import OptionalOption

logger = logging.getLogger(__name__)


class MenuItemGroupsUpdater:
    def __init__(self, db_session: AsyncSession):
        self.db = db_session

    async def update_all_groups(self, db_item: MenuItem, variant_groups_data: Optional[List],
                               modifier_groups_data: Optional[List], optional_groups_data: Optional[List],
                               tenant_id: uuid.UUID):
        """Update all groups for a menu item."""
        try:
            if variant_groups_data is not None:
                await self._update_variant_groups(db_item, variant_groups_data, tenant_id)
            
            if modifier_groups_data is not None:
                await self._update_modifier_groups(db_item, modifier_groups_data, tenant_id)
            
            if optional_groups_data is not None:
                await self._update_optional_groups(db_item, optional_groups_data, tenant_id)

            logger.info(f"Updated groups for item {db_item.id}")

        except Exception as e:
            logger.error(f"Error updating groups for item {db_item.id}: {e}")
            raise

    async def _update_variant_groups(self, db_item: MenuItem, variant_groups_data: List, tenant_id: uuid.UUID):
        """Update variant groups for a menu item."""
        from app.db.base import menu_item_variant_groups

        # Remove existing associations
        await self.db.execute(
            menu_item_variant_groups.delete().where(
                menu_item_variant_groups.c.menu_item_id == db_item.id
            )
        )
        await self.db.flush()

        # Process each variant group
        for idx, group_data in enumerate(variant_groups_data):
            await self._process_variant_group(db_item, group_data, idx, tenant_id)

    async def _process_variant_group(self, db_item: MenuItem, group_data: dict, idx: int, tenant_id: uuid.UUID):
        """Process a single variant group update."""
        group_id = group_data.get('id')
        is_required = group_data.get('is_required', False)
        is_active = group_data.get('is_active', True)

        if group_id and not str(group_id).startswith('temp_'):
            # Existing group - update options if provided
            await self._update_existing_variant_group(group_id, group_data, tenant_id)
            await self._create_variant_group_association(db_item.id, group_id, tenant_id, idx, is_required, is_active)
        else:
            # New group - create group and association
            new_group = await self._create_new_variant_group(group_data, tenant_id)
            await self._create_variant_group_association(db_item.id, new_group.id, tenant_id, idx, is_required, is_active)

    async def _update_existing_variant_group(self, group_id: uuid.UUID, group_data: dict, tenant_id: uuid.UUID):
        """Update an existing variant group's options."""
        options_data = group_data.get('options', [])
        if options_data:
            # Remove existing options for this group
            await self.db.execute(
                delete(VariantOption).where(
                    VariantOption.variant_group_id == group_id,
                    VariantOption.tenant_id == tenant_id
                )
            )
            await self.db.flush()

            # Ensure there's always a default option
            has_default = any(opt.get('is_default', False) for opt in options_data)
            if not has_default and options_data:
                options_data[0]['is_default'] = True

            # Create updated options
            for option_data in options_data:
                option = VariantOption(
                    tenant_id=tenant_id,
                    variant_group_id=group_id,
                    name=option_data.get('name', ''),
                    price_adjustment=option_data.get('price_adjustment', 0),
                    is_active=option_data.get('is_active', True),
                    is_default=option_data.get('is_default', False),
                    display_order=option_data.get('display_order', 0)
                )
                self.db.add(option)

    async def _create_new_variant_group(self, group_data: dict, tenant_id: uuid.UUID) -> VariantGroup:
        """Create a new variant group with options."""
        new_group = VariantGroup(
            tenant_id=tenant_id,
            name=group_data.get('name', ''),
            description=group_data.get('description'),
            min_selection=group_data.get('min_selection', 1),
            max_selection=group_data.get('max_selection', 1),
            requires_default_selection=group_data.get('requires_default_selection', False)
        )
        self.db.add(new_group)
        await self.db.flush()

        # Create options for the new group
        options_data = group_data.get('options', [])
        
        # Ensure default option if required
        if group_data.get('requires_default_selection', False) and options_data:
            has_default = any(opt.get('is_default', False) for opt in options_data)
            if not has_default:
                options_data[0]['is_default'] = True

        for option_data in options_data:
            option = VariantOption(
                tenant_id=tenant_id,
                variant_group_id=new_group.id,
                name=option_data.get('name', ''),
                price_adjustment=option_data.get('price_adjustment', 0),
                is_active=option_data.get('is_active', True),
                is_default=option_data.get('is_default', False),
                display_order=option_data.get('display_order', 0)
            )
            self.db.add(option)

        return new_group

    async def _create_variant_group_association(self, item_id: uuid.UUID, group_id: uuid.UUID,
                                              tenant_id: uuid.UUID, display_order: int,
                                              is_required: bool, is_active: bool):
        """Create association between menu item and variant group."""
        from app.db.base import menu_item_variant_groups
        
        await self.db.execute(
            menu_item_variant_groups.insert().values(
                menu_item_id=item_id,
                variant_group_id=group_id,
                tenant_id=tenant_id,
                display_order=display_order,
                is_required=is_required,
                is_active=is_active
            )
        )

    async def _update_modifier_groups(self, db_item: MenuItem, modifier_groups_data: List, tenant_id: uuid.UUID):
        """Update modifier groups for a menu item."""
        from app.db.base import menu_item_modifier_groups

        # Remove existing associations
        await self.db.execute(
            menu_item_modifier_groups.delete().where(
                menu_item_modifier_groups.c.menu_item_id == db_item.id
            )
        )

        # Process each modifier group
        for idx, group_data in enumerate(modifier_groups_data):
            await self._process_modifier_group(db_item, group_data, idx, tenant_id)

    async def _process_modifier_group(self, db_item: MenuItem, group_data: dict, idx: int, tenant_id: uuid.UUID):
        """Process a single modifier group update."""
        group_id = group_data.get('id')
        is_required = group_data.get('is_required', False)
        is_active = group_data.get('is_active', True)

        logger.info(f"Processing modifier group {idx}: id={group_id}, name={group_data.get('name')}")

        if group_id and not str(group_id).startswith('temp_'):
            # Existing group - just create association
            await self._create_modifier_group_association(db_item.id, group_id, tenant_id, idx, is_required, is_active)
        else:
            # New group - create group and association
            new_group = await self._create_new_modifier_group(group_data, tenant_id)
            await self._create_modifier_group_association(db_item.id, new_group.id, tenant_id, idx, is_required, is_active)

    async def _create_new_modifier_group(self, group_data: dict, tenant_id: uuid.UUID) -> ModifierGroup:
        """Create a new modifier group with options."""
        new_group = ModifierGroup(
            tenant_id=tenant_id,
            name=group_data.get('name', ''),
            description=group_data.get('description', ''),
            min_selection=group_data.get('min_selection', 0),
            max_selection=group_data.get('max_selection', 1),
            display_order=group_data.get('display_order', 0)
        )
        self.db.add(new_group)
        await self.db.flush()

        # Create options for the new group
        options_data = group_data.get('options', [])
        for option_data in options_data:
            option = ModifierOption(
                tenant_id=tenant_id,
                modifier_group_id=new_group.id,
                name=option_data.get('name', ''),
                price_adjustment=option_data.get('price_adjustment', 0),
                is_default=option_data.get('is_default', False),
                is_active=option_data.get('is_active', True)
            )
            self.db.add(option)

        return new_group

    async def _create_modifier_group_association(self, item_id: uuid.UUID, group_id: uuid.UUID,
                                               tenant_id: uuid.UUID, display_order: int,
                                               is_required: bool, is_active: bool):
        """Create association between menu item and modifier group."""
        from app.db.base import menu_item_modifier_groups
        
        await self.db.execute(
            menu_item_modifier_groups.insert().values(
                menu_item_id=item_id,
                modifier_group_id=group_id,
                tenant_id=tenant_id,
                display_order=display_order,
                is_required=is_required,
                is_active=is_active
            )
        )

    async def _update_optional_groups(self, db_item: MenuItem, optional_groups_data: List, tenant_id: uuid.UUID):
        """Update optional groups for a menu item."""
        from app.db.base import menu_item_optional_groups

        # Remove existing associations
        await self.db.execute(
            menu_item_optional_groups.delete().where(
                menu_item_optional_groups.c.menu_item_id == db_item.id
            )
        )

        # Process each optional group
        for idx, group_data in enumerate(optional_groups_data):
            await self._process_optional_group(db_item, group_data, idx, tenant_id)

    async def _process_optional_group(self, db_item: MenuItem, group_data: dict, idx: int, tenant_id: uuid.UUID):
        """Process a single optional group update."""
        group_id = group_data.get('id')
        is_required = group_data.get('is_required', False)
        is_active = group_data.get('is_active', True)

        logger.info(f"Processing optional group {idx}: id={group_id}, name={group_data.get('name')}")

        if group_id and not str(group_id).startswith('temp_'):
            # Existing group - just create association
            await self._create_optional_group_association(db_item.id, group_id, tenant_id, idx, is_required, is_active)
        else:
            # New group - create group and association
            new_group = await self._create_new_optional_group(group_data, tenant_id)
            await self._create_optional_group_association(db_item.id, new_group.id, tenant_id, idx, is_required, is_active)

    async def _create_new_optional_group(self, group_data: dict, tenant_id: uuid.UUID) -> OptionalGroup:
        """Create a new optional group with options."""
        new_group = OptionalGroup(
            tenant_id=tenant_id,
            name=group_data.get('name', ''),
            description=group_data.get('description', ''),
            min_selection=group_data.get('min_selection', 0),
            max_selection=group_data.get('max_selection', 5),
            display_order=group_data.get('display_order', 0),
            is_active=group_data.get('is_active', True)
        )
        self.db.add(new_group)
        await self.db.flush()

        # Create options for the new group
        options_data = group_data.get('options', [])
        for option_data in options_data:
            option = OptionalOption(
                tenant_id=tenant_id,
                optional_group_id=new_group.id,
                name=option_data.get('name', ''),
                price_adjustment=option_data.get('price_adjustment', 0),
                is_active=option_data.get('is_active', True)
            )
            self.db.add(option)

        return new_group

    async def _create_optional_group_association(self, item_id: uuid.UUID, group_id: uuid.UUID,
                                               tenant_id: uuid.UUID, display_order: int,
                                               is_required: bool, is_active: bool):
        """Create association between menu item and optional group."""
        from app.db.base import menu_item_optional_groups
        
        await self.db.execute(
            menu_item_optional_groups.insert().values(
                menu_item_id=item_id,
                optional_group_id=group_id,
                tenant_id=tenant_id,
                display_order=display_order,
                is_required=is_required,
                is_active=is_active
            )
        )