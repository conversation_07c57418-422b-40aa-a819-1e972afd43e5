import uuid
from pydantic import BaseModel, ConfigDict, Field
from typing import Optional, Union
from decimal import Decimal


class OptionalOptionBase(BaseModel):
    """Base schema for Optional Option."""

    name: str = Field(
        ...,
        max_length=100,
        description="Name of the optional option (e.g., French Fries, Coca-Cola)",
    )
    price_adjustment: Decimal = Field(
        Decimal("0.00"), description="Price adjustment for selecting this option"
    )
    display_order: int = Field(0, description="Order within the optional group")
    is_active: bool = Field(True, description="Whether the option is currently active")


class OptionalOptionCreate(OptionalOptionBase):
    """Schema for creating a new Optional Option."""

    id: Optional[Union[uuid.UUID, str]] = Field(None, description="Optional ID for existing options")
    # optional_group_id will be path parameter or part of a nested structure
    # tenant_id will be added by the service


class OptionalOptionUpdate(OptionalOptionBase):
    """Schema for updating an existing Optional Option. All fields optional."""

    name: Optional[str] = Field(None, max_length=100)
    price_adjustment: Optional[Decimal] = None
    display_order: Optional[int] = None
    is_active: Optional[bool] = None


class OptionalOptionRead(OptionalOptionBase):
    """Schema for reading an Optional Option."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    optional_group_id: uuid.UUID

    model_config = ConfigDict(from_attributes=True)
