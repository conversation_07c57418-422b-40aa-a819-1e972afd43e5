"""Models for Email module."""

from app.modules.shared.email.models.email_domain import EmailDomain  # noqa: E402

# Commented out until the module is implemented
# from app.modules.shared.email.models.email_account import EmailAccount
from app.modules.shared.email.models.email_alias import Email<PERSON>lias
from app.modules.shared.email.models.email_metadata import EmailMetadata

__all__ = [
    "EmailDomain",
    # "EmailAccount",  # Commented out until the module is implemented
    "EmailAlias",
    "EmailMetadata",
]
