'use client';

import { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import { PhoneIcon, ExclamationTriangleIcon, CheckCircleIcon, ChevronDownIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { useDebouncedInput } from '@/hooks/useDebounce';

interface ContactInfo {
  phone?: string;
  phone_is_whatsapp?: boolean;
  phone_secondary?: string;
  phone_secondary_is_whatsapp?: boolean;
  fax?: string;
}

// Import countries data
import { Country, allCountries, getCountryByDialCode, popularCountries } from '@/data/countries';

interface ContactInfoTabProps {
  contactInfo: ContactInfo;
  onUpdate: (field: keyof ContactInfo, value: any) => void;
}

interface ValidationErrors {
  phone?: string;
  phone_secondary?: string;
  fax?: string;
}

interface CountrySelectorProps {
  selectedCountry: Country;
  onCountryChange: (country: Country) => void;
}

function CountrySelector({ selectedCountry, onCountryChange }: CountrySelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm('');
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const filteredCountries = allCountries.filter(country =>
    country.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    country.dialCode.includes(searchTerm) ||
    country.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleCountrySelect = (country: Country) => {
    onCountryChange(country);
    setIsOpen(false);
    setSearchTerm('');
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-l-md bg-gray-50 hover:bg-gray-100 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 h-10"
      >
        <div className="relative w-5 h-4">
          <Image
            src={selectedCountry.flagPath}
            alt={`${selectedCountry.name} flag`}
            fill
            className="object-cover rounded-sm"
          />
        </div>
        <span className="text-sm font-medium text-gray-700">{selectedCountry.dialCode}</span>
        <ChevronDownIcon className="h-4 w-4 text-gray-400" />
      </button>

      {isOpen && (
        <div className="absolute z-10 mt-1 w-80 bg-white border border-gray-300 rounded-md shadow-lg">
          {/* Search */}
          <div className="p-3 border-b border-gray-200">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search countries..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>

          {/* Countries List */}
          <div className="max-h-60 overflow-y-auto">
            {filteredCountries.map((country) => (
              <button
                key={country.code}
                type="button"
                onClick={() => handleCountrySelect(country)}
                className={`w-full flex items-center space-x-3 px-3 py-2 text-left hover:bg-gray-50 ${
                  selectedCountry.code === country.code ? 'bg-primary-50 text-primary-700' : 'text-gray-700'
                }`}
              >
                <div className="relative w-5 h-4">
                  <Image
                    src={country.flagPath}
                    alt={`${country.name} flag`}
                    fill
                    className="object-cover rounded-sm"
                  />
                </div>
                <span className="font-medium">{country.dialCode}</span>
                <span className="flex-1">{country.name}</span>
              </button>
            ))}
            {filteredCountries.length === 0 && (
              <div className="px-3 py-2 text-sm text-gray-500">
                No countries found
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export default function ContactInfoTab({ contactInfo, onUpdate }: ContactInfoTabProps) {
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [primaryCountry, setPrimaryCountry] = useState<Country>(popularCountries[0]);
  const [secondaryCountry, setSecondaryCountry] = useState<Country>(popularCountries[0]);
  const [faxCountry, setFaxCountry] = useState<Country>(popularCountries[0]);

  // Extract country code from existing phone numbers
  useEffect(() => {
    if (contactInfo.phone) {
      const country = getCountryByDialCode(contactInfo.phone.split(' ')[0]);
      if (country) setPrimaryCountry(country);
    }
    if (contactInfo.phone_secondary) {
      const country = getCountryByDialCode(contactInfo.phone_secondary.split(' ')[0]);
      if (country) setSecondaryCountry(country);
    }
    if (contactInfo.fax) {
      const country = getCountryByDialCode(contactInfo.fax.split(' ')[0]);
      if (country) setFaxCountry(country);
    }
  }, [contactInfo]);

  const validatePhoneNumber = (value: string): string | undefined => {
    if (!value || !value.trim()) return undefined; // Empty is valid (optional)

    // Remove formatting characters for validation
    const cleaned = value.replace(/[\s\-\(\)\+]/g, '');

    if (!cleaned.match(/^\d{8,15}$/)) {
      return 'Phone number must be between 8 and 15 digits';
    }

    return undefined;
  };

  // Validation function that doesn't cause re-renders during save
  const validateAndSave = (field: 'phone' | 'phone_secondary' | 'fax', value: string, country: Country) => {
    // Combine country code with phone number
    const fullNumber = value.startsWith(country.dialCode) ? value : `${country.dialCode} ${value}`;
    const error = validatePhoneNumber(fullNumber);

    // Update errors state
    setErrors(prev => ({ ...prev, [field]: error }));

    // Only save if no error
    if (!error) {
      onUpdate(field, fullNumber);
    }
  };

  // Handle country change
  const handleCountryChange = (field: 'phone' | 'phone_secondary' | 'fax', country: Country) => {
    if (field === 'phone') {
      setPrimaryCountry(country);
      if (phoneInput.value) {
        const numberWithoutCode = phoneInput.value.replace(/^\+\d+\s*/, '');
        const newValue = `${country.dialCode} ${numberWithoutCode}`;
        phoneInput.setValue(newValue);
        validateAndSave('phone', newValue, country);
      }
    } else if (field === 'phone_secondary') {
      setSecondaryCountry(country);
      if (phoneSecondaryInput.value) {
        const numberWithoutCode = phoneSecondaryInput.value.replace(/^\+\d+\s*/, '');
        const newValue = `${country.dialCode} ${numberWithoutCode}`;
        phoneSecondaryInput.setValue(newValue);
        validateAndSave('phone_secondary', newValue, country);
      }
    } else if (field === 'fax') {
      setFaxCountry(country);
      if (faxInput.value) {
        const numberWithoutCode = faxInput.value.replace(/^\+\d+\s*/, '');
        const newValue = `${country.dialCode} ${numberWithoutCode}`;
        faxInput.setValue(newValue);
        validateAndSave('fax', newValue, country);
      }
    }
  };

  // Debounced inputs for text fields (10 seconds inactivity delay)
  const phoneInput = useDebouncedInput(
    contactInfo.phone || '',
    (value) => validateAndSave('phone', value, primaryCountry),
    10000 // 10 seconds
  );

  const phoneSecondaryInput = useDebouncedInput(
    contactInfo.phone_secondary || '',
    (value) => validateAndSave('phone_secondary', value, secondaryCountry),
    10000 // 10 seconds
  );

  const faxInput = useDebouncedInput(
    contactInfo.fax || '',
    (value) => validateAndSave('fax', value, faxCountry),
    10000 // 10 seconds
  );

  // Immediate update for checkboxes (no debounce needed)
  const handleCheckboxUpdate = (field: keyof ContactInfo, value: any) => {
    onUpdate(field, value);
  };

  // Handle blur events to force save
  const handleBlur = (inputHandler: { forceSave: () => void }) => {
    inputHandler.forceSave();
  };

  const formatPhoneNumber = (value: string) => {
    // Simple formatting for display
    const cleaned = value.replace(/\D/g, '');
    if (cleaned.length >= 10) {
      return cleaned.replace(/(\d{2})(\d{4,5})(\d{4})/, '+$1 $2-$3');
    }
    return value;
  };

  const getFieldStyle = (field: keyof ValidationErrors) => {
    return errors[field]
      ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
      : 'border-gray-300 focus:ring-primary-500 focus:border-primary-500';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-2">
        <PhoneIcon className="h-5 w-5 text-gray-400" />
        <h3 className="text-lg font-medium text-gray-900">Contact Information</h3>
      </div>

      {/* Contact Fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Primary Phone */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Primary Phone
          </label>
          <div className="flex">
            <CountrySelector
              selectedCountry={primaryCountry}
              onCountryChange={(country) => handleCountryChange('phone', country)}
            />
            <input
              type="tel"
              value={phoneInput.value.replace(/^\+\d+\s*/, '')} // Remove country code from display
              onChange={(e) => {
                const fullValue = `${primaryCountry.dialCode} ${e.target.value}`;
                phoneInput.setValue(fullValue);
              }}
              onBlur={() => handleBlur(phoneInput)} // Salva ao clicar fora
              onKeyDown={(e) => e.key === 'Enter' && handleBlur(phoneInput)} // Salva ao pressionar Enter
              placeholder="11 99999-9999"
              className={`flex-1 px-3 py-2 border border-l-0 rounded-r-md focus:outline-none h-10 ${getFieldStyle('phone')} ${
                phoneInput.isChanged ? 'border-yellow-300 bg-yellow-50' : ''
              }`}
              title="Salva automaticamente: ao clicar fora, pressionar Enter ou após 10s de inatividade"
            />
          </div>
          {errors.phone && (
            <p className="mt-1 text-sm text-red-600 flex items-center">
              <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
              {errors.phone}
            </p>
          )}
          
          {/* WhatsApp Toggle */}
          <div className="mt-3 flex items-center">
            <input
              type="checkbox"
              id="whatsapp-toggle"
              checked={contactInfo.phone_is_whatsapp || false}
              onChange={(e) => handleCheckboxUpdate('phone_is_whatsapp', e.target.checked)}
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label htmlFor="whatsapp-toggle" className="ml-2 text-sm text-gray-700">
              This number is also WhatsApp
            </label>
            {contactInfo.phone_is_whatsapp && (
              <span className="ml-2 text-green-600 text-lg">
                📱
              </span>
            )}
          </div>
          
          <p className="mt-2 text-xs text-gray-500">
            Main contact number for customers and deliveries
          </p>
        </div>

        {/* Secondary Phone */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Secondary Phone
          </label>
          <div className="flex">
            <CountrySelector
              selectedCountry={secondaryCountry}
              onCountryChange={(country) => handleCountryChange('phone_secondary', country)}
            />
            <input
              type="tel"
              value={phoneSecondaryInput.value.replace(/^\+\d+\s*/, '')} // Remove country code from display
              onChange={(e) => {
                const fullValue = `${secondaryCountry.dialCode} ${e.target.value}`;
                phoneSecondaryInput.setValue(fullValue);
              }}
              onBlur={() => handleBlur(phoneSecondaryInput)} // Salva ao clicar fora
              onKeyDown={(e) => e.key === 'Enter' && handleBlur(phoneSecondaryInput)} // Salva ao pressionar Enter
              placeholder="11 3333-3333"
              className={`flex-1 px-3 py-2 border border-l-0 rounded-r-md focus:outline-none h-10 ${getFieldStyle('phone_secondary')} ${
                phoneSecondaryInput.isChanged ? 'border-yellow-300 bg-yellow-50' : ''
              }`}
              title="Salva automaticamente: ao clicar fora, pressionar Enter ou após 10s de inatividade"
            />
          </div>
          {errors.phone_secondary && (
            <p className="mt-1 text-sm text-red-600 flex items-center">
              <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
              {errors.phone_secondary}
            </p>
          )}

          {/* WhatsApp Toggle for Secondary Phone */}
          {contactInfo.phone_secondary && contactInfo.phone_secondary.trim() && (
            <div className="mt-3 flex items-center">
              <input
                type="checkbox"
                id="whatsapp-secondary-toggle"
                checked={contactInfo.phone_secondary_is_whatsapp || false}
                onChange={(e) => handleCheckboxUpdate('phone_secondary_is_whatsapp', e.target.checked)}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label htmlFor="whatsapp-secondary-toggle" className="ml-2 text-sm text-gray-700">
                This number is also WhatsApp
              </label>
              {contactInfo.phone_secondary_is_whatsapp && (
                <span className="ml-2 text-green-600 text-lg">
                  📱
                </span>
              )}
            </div>
          )}

          <p className="mt-2 text-xs text-gray-500">
            Optional: landline, office, or alternative contact
          </p>
        </div>

        {/* Fax Number */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Fax Number
          </label>
          <div className="flex">
            <CountrySelector
              selectedCountry={faxCountry}
              onCountryChange={(country) => handleCountryChange('fax', country)}
            />
            <input
              type="tel"
              value={faxInput.value.replace(/^\+\d+\s*/, '')} // Remove country code from display
              onChange={(e) => {
                const fullValue = `${faxCountry.dialCode} ${e.target.value}`;
                faxInput.setValue(fullValue);
              }}
              onBlur={() => handleBlur(faxInput)} // Salva ao clicar fora
              onKeyDown={(e) => e.key === 'Enter' && handleBlur(faxInput)} // Salva ao pressionar Enter
              placeholder="11 3333-3334"
              className={`flex-1 px-3 py-2 border border-l-0 rounded-r-md focus:outline-none h-10 ${getFieldStyle('fax')} ${
                faxInput.isChanged ? 'border-yellow-300 bg-yellow-50' : ''
              }`}
              title="Salva automaticamente: ao clicar fora, pressionar Enter ou após 10s de inatividade"
            />
          </div>
          {errors.fax && (
            <p className="mt-1 text-sm text-red-600 flex items-center">
              <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
              {errors.fax}
            </p>
          )}
          <p className="mt-2 text-xs text-gray-500">
            Optional: for businesses that still use fax communication
          </p>
        </div>
      </div>

      {/* Contact Preview */}
      {(contactInfo.phone || contactInfo.phone_secondary || contactInfo.fax) && (
        <div className="p-4 bg-gray-50 border border-gray-200 rounded-md">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Contact Information Preview:</h4>
          <div className="space-y-2 text-sm text-gray-700">
            {contactInfo.phone && (
              <div className="flex items-center space-x-2">
                <span>📞</span>
                <span>{contactInfo.phone}</span>
                {contactInfo.phone_is_whatsapp && (
                  <span className="text-green-600 text-xs bg-green-50 px-2 py-1 rounded-full">
                    📱 WhatsApp
                  </span>
                )}
                <span className="text-xs text-gray-500">(Primary)</span>
              </div>
            )}
            {contactInfo.phone_secondary && (
              <div className="flex items-center space-x-2">
                <span>📞</span>
                <span>{contactInfo.phone_secondary}</span>
                {contactInfo.phone_secondary_is_whatsapp && (
                  <span className="text-green-600 text-xs bg-green-50 px-2 py-1 rounded-full">
                    📱 WhatsApp
                  </span>
                )}
                <span className="text-xs text-gray-500">(Secondary)</span>
              </div>
            )}
            {contactInfo.fax && (
              <div className="flex items-center space-x-2">
                <span>📠</span>
                <span>{contactInfo.fax}</span>
                <span className="text-xs text-gray-500">(Fax)</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Information Panel */}
      <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
        <h4 className="text-sm font-medium text-blue-900 mb-2">Contact Information Tips</h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• <strong>Primary Phone:</strong> Main number customers will use to contact you (optional)</li>
          <li>• <strong>WhatsApp:</strong> Enable if customers can message you on WhatsApp</li>
          <li>• <strong>Secondary Phone:</strong> Backup number or dedicated line (reception, kitchen)</li>
          <li>• <strong>Secondary WhatsApp:</strong> Both phones can have WhatsApp enabled independently</li>
          <li>• <strong>Fax:</strong> For businesses that need to receive documents via fax</li>
          <li>• <strong>All fields are optional:</strong> Add only the contact methods you use</li>
        </ul>
      </div>
    </div>
  );
}
