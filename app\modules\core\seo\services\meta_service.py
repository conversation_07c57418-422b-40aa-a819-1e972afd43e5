"""
Meta Service

Business logic for generating HTML meta tags and structured data.
"""

import uuid
import json
from typing import Dict, List, Optional, Any
from sqlalchemy.ext.asyncio import AsyncSession

from app.modules.core.seo.models.seo_meta import SEOMeta
from app.modules.core.seo.models.url_slug import URLSlug
from app.modules.core.seo.services.seo_service import SEOService
from app.modules.core.seo.services.slug_service import SlugService
from app.modules.core.seo.schemas.seo_schemas import MetaTagsResponse, HreflangResponse


class MetaService:
    """Service for generating HTML meta tags and structured data."""

    def __init__(self):
        self.seo_service = SEOService()
        self.slug_service = SlugService()

    async def generate_meta_tags(
        self,
        db: AsyncSession,
        content_type: str,
        content_id: uuid.UUID,
        language_code: str,
        base_url: str = "https://example.com",
        fallback_data: Optional[Dict[str, Any]] = None
    ) -> MetaTagsResponse:
        """Generate complete HTML meta tags for content."""
        # Get SEO metadata
        seo_meta = await self.seo_service.get_seo_meta(
            db, content_type, content_id, language_code
        )
        
        # Get hreflang data
        hreflang_data = await self.slug_service.get_hreflang_data(
            db, content_type, content_id, base_url
        )
        
        # Generate HTML meta tags
        html_meta_tags = self._generate_html_meta_tags(seo_meta, fallback_data)
        
        # Generate JSON-LD structured data
        json_ld = self._generate_json_ld(seo_meta, fallback_data, base_url)
        
        # Format hreflang data
        hreflang_tags = [
            {"rel": "alternate", "hreflang": item["hreflang"], "href": item["href"]}
            for item in hreflang_data
        ]
        
        return MetaTagsResponse(
            html_meta_tags=html_meta_tags,
            json_ld=json_ld,
            hreflang_tags=hreflang_tags
        )

    async def generate_hreflang_tags(
        self,
        db: AsyncSession,
        content_type: str,
        content_id: uuid.UUID,
        base_url: str = "https://example.com"
    ) -> List[HreflangResponse]:
        """Generate hreflang tags for content."""
        hreflang_data = await self.slug_service.get_hreflang_data(
            db, content_type, content_id, base_url
        )
        
        return [
            HreflangResponse(
                language_code=item["hreflang"],
                url=item["href"],
                title=item.get("title")
            )
            for item in hreflang_data
        ]

    def _generate_html_meta_tags(
        self,
        seo_meta: Optional[SEOMeta],
        fallback_data: Optional[Dict[str, Any]] = None
    ) -> str:
        """Generate HTML meta tags string."""
        tags = []
        
        # Get data with fallbacks
        title = self._get_value(seo_meta, "meta_title", fallback_data, "title")
        description = self._get_value(seo_meta, "meta_description", fallback_data, "description")
        keywords = self._get_value(seo_meta, "meta_keywords", fallback_data, "keywords")
        canonical_url = self._get_value(seo_meta, "canonical_url", fallback_data, "canonical_url")
        robots = self._get_value(seo_meta, "robots_directive", fallback_data, "robots", "index,follow")
        
        # Basic meta tags
        if title:
            tags.append(f'<title>{self._escape_html(title)}</title>')
            tags.append(f'<meta name="title" content="{self._escape_html(title)}">')
        
        if description:
            tags.append(f'<meta name="description" content="{self._escape_html(description)}">')
        
        if keywords:
            tags.append(f'<meta name="keywords" content="{self._escape_html(keywords)}">')
        
        if canonical_url:
            tags.append(f'<link rel="canonical" href="{canonical_url}">')
        
        tags.append(f'<meta name="robots" content="{robots}">')
        
        # Open Graph tags
        og_title = self._get_value(seo_meta, "og_title", fallback_data, "og_title", title)
        og_description = self._get_value(seo_meta, "og_description", fallback_data, "og_description", description)
        og_image = self._get_value(seo_meta, "og_image_url", fallback_data, "og_image_url")
        og_image_alt = self._get_value(seo_meta, "og_image_alt", fallback_data, "og_image_alt")
        og_type = self._get_value(seo_meta, "og_type", fallback_data, "og_type", "website")
        og_site_name = self._get_value(seo_meta, "og_site_name", fallback_data, "og_site_name")
        
        if og_title:
            tags.append(f'<meta property="og:title" content="{self._escape_html(og_title)}">')
        
        if og_description:
            tags.append(f'<meta property="og:description" content="{self._escape_html(og_description)}">')
        
        if og_image:
            tags.append(f'<meta property="og:image" content="{og_image}">')
            if og_image_alt:
                tags.append(f'<meta property="og:image:alt" content="{self._escape_html(og_image_alt)}">')
        
        tags.append(f'<meta property="og:type" content="{og_type}">')
        
        if og_site_name:
            tags.append(f'<meta property="og:site_name" content="{self._escape_html(og_site_name)}">')
        
        # Twitter Card tags
        twitter_card = self._get_value(seo_meta, "twitter_card_type", fallback_data, "twitter_card_type", "summary_large_image")
        twitter_title = self._get_value(seo_meta, "twitter_title", fallback_data, "twitter_title", title)
        twitter_description = self._get_value(seo_meta, "twitter_description", fallback_data, "twitter_description", description)
        twitter_image = self._get_value(seo_meta, "twitter_image_url", fallback_data, "twitter_image_url", og_image)
        twitter_image_alt = self._get_value(seo_meta, "twitter_image_alt", fallback_data, "twitter_image_alt", og_image_alt)
        twitter_creator = self._get_value(seo_meta, "twitter_creator", fallback_data, "twitter_creator")
        twitter_site = self._get_value(seo_meta, "twitter_site", fallback_data, "twitter_site")
        
        tags.append(f'<meta name="twitter:card" content="{twitter_card}">')
        
        if twitter_title:
            tags.append(f'<meta name="twitter:title" content="{self._escape_html(twitter_title)}">')
        
        if twitter_description:
            tags.append(f'<meta name="twitter:description" content="{self._escape_html(twitter_description)}">')
        
        if twitter_image:
            tags.append(f'<meta name="twitter:image" content="{twitter_image}">')
            if twitter_image_alt:
                tags.append(f'<meta name="twitter:image:alt" content="{self._escape_html(twitter_image_alt)}">')
        
        if twitter_creator:
            tags.append(f'<meta name="twitter:creator" content="{twitter_creator}">')
        
        if twitter_site:
            tags.append(f'<meta name="twitter:site" content="{twitter_site}">')
        
        # Additional meta tags
        if seo_meta and seo_meta.additional_meta_tags:
            for name, content in seo_meta.additional_meta_tags.items():
                tags.append(f'<meta name="{self._escape_html(name)}" content="{self._escape_html(content)}">')
        
        return "\n".join(tags)

    def _generate_json_ld(
        self,
        seo_meta: Optional[SEOMeta],
        fallback_data: Optional[Dict[str, Any]] = None,
        base_url: str = "https://example.com"
    ) -> Optional[Dict[str, Any]]:
        """Generate JSON-LD structured data."""
        if seo_meta and seo_meta.structured_data:
            return seo_meta.structured_data
        
        # Generate basic structured data
        title = self._get_value(seo_meta, "meta_title", fallback_data, "title")
        description = self._get_value(seo_meta, "meta_description", fallback_data, "description")
        image = self._get_value(seo_meta, "og_image_url", fallback_data, "og_image_url")
        
        if not title:
            return None
        
        json_ld = {
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": title,
            "url": base_url
        }
        
        if description:
            json_ld["description"] = description
        
        if image:
            json_ld["image"] = image
        
        return json_ld

    def _get_value(
        self,
        seo_meta: Optional[SEOMeta],
        seo_field: str,
        fallback_data: Optional[Dict[str, Any]],
        fallback_field: str,
        default: Optional[str] = None
    ) -> Optional[str]:
        """Get value with fallback chain."""
        # Try SEO meta first
        if seo_meta:
            value = getattr(seo_meta, seo_field, None)
            if value:
                return value
        
        # Try fallback data
        if fallback_data and fallback_field in fallback_data:
            value = fallback_data[fallback_field]
            if value:
                return value
        
        # Return default
        return default

    def _escape_html(self, text: str) -> str:
        """Escape HTML special characters."""
        if not text:
            return ""
        
        return (
            text.replace("&", "&amp;")
            .replace("<", "&lt;")
            .replace(">", "&gt;")
            .replace('"', "&quot;")
            .replace("'", "&#x27;")
        )
