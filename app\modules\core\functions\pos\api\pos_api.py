import uuid
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

# Adicionado require_tenant_role
from app.core.db_dependencies import get_db
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    get_current_tenant,
    require_tenant_role,
)
from app.modules.core.roles.models.roles import RolePermissions, TenantRole  # Adicionado para roles
from app.core.exceptions import (
    BusinessLogicError,
)  # Adicionado para tratamento de erros
from app.modules.core.tenants.models.tenant import Tenant  # noqa: E402
from app.modules.core.users.models.user import User  # <PERSON><PERSON> importado, será usado para current_user

from app.modules.core.functions.pos.schemas.cash_register import (  # noqa: E402
    CashRegisterCreate,
    CashRegisterRead,
    CashRegisterUpdate,
)
from app.modules.core.functions.pos.schemas.sale_transaction import (  # noqa: E402
    SaleTransactionCreate,
    SaleTransactionRead,
    SaleTransactionUpdate,
)
from app.modules.core.functions.pos.schemas.refund import RefundCreate, RefundRead  # noqa: E402
from app.modules.core.functions.pos.schemas.cash_register_session import (
    CashRegisterSessionOpen,
    CashRegisterSessionRead,
    CashRegisterSessionClose,
)
from app.modules.core.functions.pos.schemas.payment_method import (  # noqa: E402
    PaymentMethodCreate,
    PaymentMethodRead,
    PaymentMethodUpdate,
    TransactionPaymentCreate,
    TransactionPaymentRead,
    TransactionPaymentWithMethodRead,
)
from app.modules.core.functions.pos.services.cash_register_service import (
    cash_register_service,
)
from app.modules.core.functions.pos.services.sale_transaction_service import (
    sale_transaction_service,
)
from app.modules.core.functions.pos.services.cash_register_session_service import (  # noqa: E402
    cash_register_session_service,
)
from app.modules.core.functions.pos.services.payment_method_service import (  # noqa: E402
    payment_method_service,
)

router = APIRouter()

# --- Cash Register Endpoints ---


@router.post(
    "/cash-registers",
    response_model=CashRegisterRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create Cash Register",
    description="Create a new cash register for the current tenant.",
)
async def create_cash_register(
    *,
    db: AsyncSession = Depends(get_db),
    cash_register_in: CashRegisterCreate,
    current_tenant: Tenant = Depends(get_current_tenant),  # Mantido para obter tenant.id
    current_user: User = Depends(
        require_tenant_role(RolePermissions.ADMIN_ROLES, tenant_id_source="header")
    ),
) -> CashRegisterRead:
    """
    Creates a new cash register associated with the currently authenticated tenant.
    Requires OWNER or MANAGER role.
    """
    cash_register = await cash_register_service.create(
        db=db, obj_in=cash_register_in, tenant_id=current_tenant.id
    )
    return cash_register


@router.get(
    "/cash-registers",
    response_model=List[CashRegisterRead],
    summary="List Cash Registers",
    description="List all cash registers for the current tenant.",
)
async def list_cash_registers(
    *,
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),  # Mantido para obter tenant.id
    current_user: User = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
    skip: int = 0,
    limit: int = 100,
) -> List[CashRegisterRead]:
    """
    Retrieves a list of cash registers belonging to the currently authenticated tenant.
    Requires STAFF, MANAGER, or OWNER role.
    """
    cash_registers = await cash_register_service.get_multi(
        db=db, tenant_id=current_tenant.id, skip=skip, limit=limit
    )
    return cash_registers


# Adicionar endpoints GET (by id), PUT, DELETE para CashRegister se necessário no futuro

# --- Sale Transaction Endpoints ---


@router.post(
    "/cash-registers/{cash_register_id}/transactions",
    response_model=SaleTransactionRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create Sale Transaction",
    description="Register a new sale transaction for a specific cash register of the current tenant.",  # noqa: E501
)
async def create_sale_transaction(
    *,
    db: AsyncSession = Depends(get_db),
    cash_register_id: uuid.UUID,
    transaction_in: SaleTransactionCreate,
    current_tenant: Tenant = Depends(get_current_tenant),  # Mantido para obter tenant.id
    current_user: User = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
) -> SaleTransactionRead:
    """
    Registers a new sale transaction.
    Requires STAFF, MANAGER, or OWNER role.
    - Validates that the specified cash register belongs to the current tenant.
    - Associates the transaction with the tenant and cash register.
    - Optionally associates with a customer (user) if `customer_id` is provided and valid.
    """
    # O serviço `create_transaction` já valida se o cash_register pertence ao tenant
    try:
        transaction = await sale_transaction_service.create_transaction(
            db=db,
            obj_in=transaction_in,
            tenant_id=current_tenant.id,
            cash_register_id=cash_register_id,
        )
        return transaction
    except HTTPException as e:
        # Re-raise a exceção vinda do serviço (ex: caixa não encontrado, cliente não encontrado)
        raise e
    except Exception:
        # Logar erro inesperado
        # logger.error(f"Unexpected error creating transaction: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while creating the transaction.",
        )


@router.get(
    "/transactions",
    response_model=List[SaleTransactionRead],
    summary="List Sale Transactions",
    description="List sale transactions for the current tenant, with optional filters.",
)
async def list_sale_transactions(
    *,
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),  # Mantido para obter tenant.id
    current_user: User = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
    cash_register_id: Optional[uuid.UUID] = Query(None, description="Filter by cash register ID"),
    customer_id: Optional[uuid.UUID] = Query(None, description="Filter by customer ID"),
    # Adicionar filtros de data aqui (start_date: Optional[date] =
    # Query(None), end_date: Optional[date] = Query(None))
    skip: int = 0,
    limit: int = 100,
) -> List[SaleTransactionRead]:
    """
    Retrieves a list of sale transactions for the currently authenticated tenant.
    Allows filtering by cash register and customer.
    Requires STAFF, MANAGER, or OWNER role.
    """
    # Validação adicional: Se cash_register_id for fornecido, verificar se pertence ao tenant?
    # O serviço get_multi já filtra pelo tenant_id principal.
    # Se quisermos garantir que o cash_register_id *também* pertence ao tenant
    # (redundante mas seguro):
    if cash_register_id:
        cash_register = await cash_register_service.get(
            db=db, id=cash_register_id, tenant_id=current_tenant.id
        )
        if not cash_register:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cash Register with id {cash_register_id} not found or does not belong to the tenant.",  # noqa: E501
            )

    transactions = await sale_transaction_service.get_multi(
        db=db,
        tenant_id=current_tenant.id,
        cash_register_id=cash_register_id,
        customer_id=customer_id,
        # Passar filtros de data aqui
        skip=skip,
        limit=limit,
    )
    return transactions


# Endpoint para obter uma transação específica


@router.get(
    "/transactions/{transaction_id}",
    response_model=SaleTransactionRead,
    summary="Get Transaction",
    description="Get details of a specific sale transaction.",
)
async def get_sale_transaction(
    *,
    db: AsyncSession = Depends(get_db),
    transaction_id: uuid.UUID,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
) -> SaleTransactionRead:
    """
    Retrieves details of a specific sale transaction.
    Requires STAFF, MANAGER, or OWNER role.
    """
    transaction = await sale_transaction_service.get(
        db=db, id=transaction_id, tenant_id=current_tenant.id, include_refunds=True
    )

    if not transaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Transaction with id {transaction_id} not found",
        )

    # Calcular o valor total estornado e o valor restante disponível para estorno
    refunded_amount = await sale_transaction_service.calculate_refunded_amount(
        db=db, transaction_id=transaction_id, tenant_id=current_tenant.id
    )

    # Criar o objeto de resposta com informações adicionais
    response = SaleTransactionRead.model_validate(transaction)
    response.refunded_amount = refunded_amount
    response.remaining_amount = transaction.total_amount - refunded_amount

    return response


# Endpoint para processar um estorno


@router.post(
    "/transactions/{transaction_id}/refund",
    response_model=SaleTransactionRead,
    status_code=status.HTTP_200_OK,
    summary="Refund Transaction",
    description="Process a refund for a sale transaction.",
)
async def refund_transaction(
    *,
    db: AsyncSession = Depends(get_db),
    transaction_id: uuid.UUID,
    refund_data: RefundCreate,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role(RolePermissions.ADMIN_ROLES, tenant_id_source="header")
    ),
) -> SaleTransactionRead:
    """
    Processes a refund for a sale transaction.
    Requires MANAGER or OWNER role.
    """
    try:
        # Processar o estorno
        await sale_transaction_service.create_refund(
            db=db,
            transaction_id=transaction_id,
            tenant_id=current_tenant.id,
            refund_data=refund_data,
            user_id=current_user.id,
        )

        # Obter a transação atualizada com os estornos
        transaction = await sale_transaction_service.get(
            db=db, id=transaction_id, tenant_id=current_tenant.id, include_refunds=True
        )

        if not transaction:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Transaction with id {transaction_id} not found",
            )

        # Calcular o valor total estornado e o valor restante disponível para estorno
        refunded_amount = await sale_transaction_service.calculate_refunded_amount(
            db=db, transaction_id=transaction_id, tenant_id=current_tenant.id
        )

        # Criar o objeto de resposta com informações adicionais
        response = SaleTransactionRead.model_validate(transaction)
        response.refunded_amount = refunded_amount
        response.remaining_amount = transaction.total_amount - refunded_amount

        return response
    except BusinessLogicError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


# Endpoint para obter o histórico de estornos de uma transação


@router.get(
    "/transactions/{transaction_id}/refunds",
    response_model=List[RefundRead],
    summary="Get Transaction Refunds",
    description="Get refund history for a specific sale transaction.",
)
async def get_transaction_refunds(
    *,
    db: AsyncSession = Depends(get_db),
    transaction_id: uuid.UUID,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
) -> List[RefundRead]:
    """
    Retrieves the refund history for a specific sale transaction.
    Requires STAFF, MANAGER, or OWNER role.
    """
    # Verificar se a transação existe e pertence ao tenant
    transaction = await sale_transaction_service.get(
        db=db, id=transaction_id, tenant_id=current_tenant.id
    )

    if not transaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Transaction with id {transaction_id} not found",
        )

    # Obter os estornos da transação
    refunds = await sale_transaction_service.get_transaction_refunds(
        db=db, transaction_id=transaction_id, tenant_id=current_tenant.id
    )

    return refunds


# --- Cash Register Session Endpoints ---


@router.post(
    "/cash-registers/{cash_register_id}/sessions",
    response_model=CashRegisterSessionRead,
    status_code=status.HTTP_201_CREATED,
    summary="Open Cash Register Session",
    description="Open a new session for a specific cash register.",
)
async def open_cash_register_session(
    *,
    db: AsyncSession = Depends(get_db),
    cash_register_id: uuid.UUID,
    session_data: CashRegisterSessionOpen,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
) -> CashRegisterSessionRead:
    """
    Opens a new session for a cash register.
    Requires STAFF, MANAGER, or OWNER role.
    """
    try:
        session = await cash_register_session_service.open_session(
            db=db,
            cash_register_id=cash_register_id,
            operator_id=current_user.id,
            tenant_id=current_tenant.id,
            session_data=session_data,
        )
        return session
    except BusinessLogicError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )


@router.put(
    "/cash-registers/sessions/{session_id}/close",
    response_model=CashRegisterSessionRead,
    status_code=status.HTTP_200_OK,
    summary="Close Cash Register Session",
    description="Close an active cash register session.",
)
async def close_cash_register_session(
    *,
    db: AsyncSession = Depends(get_db),
    session_id: uuid.UUID,
    session_data: CashRegisterSessionClose,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
) -> CashRegisterSessionRead:
    """
    Closes an active cash register session.
    Requires STAFF, MANAGER, or OWNER role.
    """
    try:
        session = await cash_register_session_service.close_session(
            db=db,
            session_id=session_id,
            tenant_id=current_tenant.id,
            session_data=session_data,
        )
        return session
    except BusinessLogicError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )


@router.get(
    "/cash-registers/{cash_register_id}/sessions",
    response_model=List[CashRegisterSessionRead],
    status_code=status.HTTP_200_OK,
    summary="List Cash Register Sessions",
    description="List all sessions for a specific cash register.",
)
async def list_cash_register_sessions(
    *,
    db: AsyncSession = Depends(get_db),
    cash_register_id: uuid.UUID,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
    is_open: Optional[bool] = Query(None, description="Filter by session status (open/closed)"),
    skip: int = 0,
    limit: int = 100,
) -> List[CashRegisterSessionRead]:
    """
    Lists all sessions for a specific cash register.
    Requires STAFF, MANAGER, or OWNER role.
    """
    sessions = await cash_register_session_service.get_multi(
        db=db,
        tenant_id=current_tenant.id,
        cash_register_id=cash_register_id,
        is_open=is_open,
        skip=skip,
        limit=limit,
    )
    return sessions


@router.get(
    "/cash-registers/sessions/{session_id}",
    response_model=CashRegisterSessionRead,
    status_code=status.HTTP_200_OK,
    summary="Get Cash Register Session",
    description="Get details of a specific cash register session.",
)
async def get_cash_register_session(
    *,
    db: AsyncSession = Depends(get_db),
    session_id: uuid.UUID,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
) -> CashRegisterSessionRead:
    """
    Gets details of a specific cash register session.
    Requires STAFF, MANAGER, or OWNER role.
    """
    session = await cash_register_session_service.get(
        db=db, id=session_id, tenant_id=current_tenant.id
    )

    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cash register session with id {session_id} not found or does not belong to the tenant.",  # noqa: E501
        )

    return session


@router.get(
    "/cash-registers/{cash_register_id}/active-session",
    response_model=CashRegisterSessionRead,
    status_code=status.HTTP_200_OK,
    summary="Get Active Cash Register Session",
    description="Get the active session for a specific cash register, if any.",
)
async def get_active_cash_register_session(
    *,
    db: AsyncSession = Depends(get_db),
    cash_register_id: uuid.UUID,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
) -> CashRegisterSessionRead:
    """
    Gets the active session for a specific cash register, if any.
    Requires STAFF, MANAGER, or OWNER role.
    """
    session = await cash_register_session_service.get_active_session(
        db=db, cash_register_id=cash_register_id, tenant_id=current_tenant.id
    )

    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"No active session found for cash register with id {cash_register_id}.",
        )

    return session


# --- Payment Method Endpoints ---


@router.post(
    "/payment-methods",
    response_model=PaymentMethodRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create Payment Method",
    description="Create a new payment method for the tenant.",
)
async def create_payment_method(
    *,
    db: AsyncSession = Depends(get_db),
    payment_method_in: PaymentMethodCreate,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role(RolePermissions.ADMIN_ROLES, tenant_id_source="header")
    ),
) -> PaymentMethodRead:
    """
    Creates a new payment method for the tenant.
    Requires MANAGER or OWNER role.
    """
    payment_method = await payment_method_service.create(
        db=db, tenant_id=current_tenant.id, obj_in=payment_method_in
    )
    return payment_method


@router.get(
    "/payment-methods",
    response_model=List[PaymentMethodRead],
    status_code=status.HTTP_200_OK,
    summary="List Payment Methods",
    description="List all payment methods for the tenant.",
)
async def list_payment_methods(
    *,
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    skip: int = 0,
    limit: int = 100,
) -> List[PaymentMethodRead]:
    """
    Lists all payment methods for the tenant.
    Requires STAFF, MANAGER, or OWNER role.
    """
    payment_methods = await payment_method_service.get_multi(
        db=db, tenant_id=current_tenant.id, is_active=is_active, skip=skip, limit=limit
    )
    return payment_methods


@router.get(
    "/payment-methods/{payment_method_id}",
    response_model=PaymentMethodRead,
    status_code=status.HTTP_200_OK,
    summary="Get Payment Method",
    description="Get details of a specific payment method.",
)
async def get_payment_method(
    *,
    db: AsyncSession = Depends(get_db),
    payment_method_id: uuid.UUID,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
) -> PaymentMethodRead:
    """
    Gets details of a specific payment method.
    Requires STAFF, MANAGER, or OWNER role.
    """
    payment_method = await payment_method_service.get(
        db=db, id=payment_method_id, tenant_id=current_tenant.id
    )

    if not payment_method:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Payment method with id {payment_method_id} not found or does not belong to the tenant.",  # noqa: E501
        )

    return payment_method


@router.put(
    "/payment-methods/{payment_method_id}",
    response_model=PaymentMethodRead,
    status_code=status.HTTP_200_OK,
    summary="Update Payment Method",
    description="Update an existing payment method.",
)
async def update_payment_method(
    *,
    db: AsyncSession = Depends(get_db),
    payment_method_id: uuid.UUID,
    payment_method_in: PaymentMethodUpdate,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role(RolePermissions.ADMIN_ROLES, tenant_id_source="header")
    ),
) -> PaymentMethodRead:
    """
    Updates an existing payment method.
    Requires MANAGER or OWNER role.
    """
    payment_method = await payment_method_service.update(
        db=db,
        id=payment_method_id,
        tenant_id=current_tenant.id,
        obj_in=payment_method_in,
    )

    if not payment_method:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Payment method with id {payment_method_id} not found or does not belong to the tenant.",  # noqa: E501
        )

    return payment_method


@router.post(
    "/transactions/{transaction_id}/payments",
    response_model=TransactionPaymentRead,
    status_code=status.HTTP_201_CREATED,
    summary="Add Transaction Payment",
    description="Add a payment to a transaction.",
)
async def add_transaction_payment(
    *,
    db: AsyncSession = Depends(get_db),
    transaction_id: uuid.UUID,
    payment_in: TransactionPaymentCreate,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
) -> TransactionPaymentRead:
    """
    Adds a payment to a transaction.
    Requires STAFF, MANAGER, or OWNER role.
    """
    try:
        payment = await payment_method_service.add_transaction_payment(
            db=db,
            transaction_id=transaction_id,
            tenant_id=current_tenant.id,
            obj_in=payment_in,
        )
        return payment
    except BusinessLogicError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )


@router.get(
    "/transactions/{transaction_id}/payments",
    response_model=List[TransactionPaymentWithMethodRead],
    status_code=status.HTTP_200_OK,
    summary="List Transaction Payments",
    description="List all payments for a transaction.",
)
async def list_transaction_payments(
    *,
    db: AsyncSession = Depends(get_db),
    transaction_id: uuid.UUID,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
) -> List[TransactionPaymentWithMethodRead]:
    """
    Lists all payments for a transaction.
    Requires STAFF, MANAGER, or OWNER role.
    """
    payments = await payment_method_service.get_transaction_payments(
        db=db, transaction_id=transaction_id, tenant_id=current_tenant.id
    )
    return payments
