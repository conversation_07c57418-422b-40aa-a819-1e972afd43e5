"""
Blog Tag Schemas

Pydantic models for blog tag validation and serialization.
"""

import uuid
from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field


class BlogTagTranslationBase(BaseModel):
    """Base schema for blog tag translations."""
    language_code: str = Field(..., max_length=10)
    name: str = Field(..., max_length=100)
    description: Optional[str] = None


class BlogTagTranslationCreate(BlogTagTranslationBase):
    """Schema for creating blog tag translations."""
    pass


class BlogTagTranslationUpdate(BaseModel):
    """Schema for updating blog tag translations."""
    name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = None


class BlogTagTranslationRead(BlogTagTranslationBase):
    """Schema for reading blog tag translations."""
    id: uuid.UUID
    tag_id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class BlogTagBase(BaseModel):
    """Base schema for blog tags."""
    slug: str = Field(..., max_length=255)
    color: Optional[str] = Field(None, max_length=7)  # Hex color


class BlogTagCreate(BlogTagBase):
    """Schema for creating blog tags."""
    translations: List[BlogTagTranslationCreate] = Field(..., min_items=1)


class BlogTagUpdate(BaseModel):
    """Schema for updating blog tags."""
    slug: Optional[str] = Field(None, max_length=255)
    color: Optional[str] = Field(None, max_length=7)


class BlogTagRead(BlogTagBase):
    """Schema for reading blog tags."""
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    
    # Relationships
    translations: List[BlogTagTranslationRead] = []
    
    class Config:
        from_attributes = True


class BlogTagList(BaseModel):
    """Schema for blog tag list items (simplified)."""
    id: uuid.UUID
    slug: str
    color: Optional[str]
    created_at: datetime
    
    # Primary translation (usually in default language)
    name: Optional[str] = None
    description: Optional[str] = None
    
    # Post count
    post_count: int = 0
    
    class Config:
        from_attributes = True


class BlogTagCloud(BaseModel):
    """Schema for tag cloud display."""
    id: uuid.UUID
    slug: str
    name: str
    color: Optional[str]
    post_count: int
    weight: float  # Calculated weight for tag cloud display
    
    class Config:
        from_attributes = True
