"""
Serviço de limpeza automática de mídia não vinculada.

Este serviço remove automaticamente uploads que não estão vinculados a nenhum
contexto específico (como menu items) após um período determinado.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Optional

from sqlalchemy import select, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_db
from app.modules.core.functions.media_system.models import (
    MediaUpload, 
    MediaMenuItemMedia,
    MediaUploadStatus
)

logger = logging.getLogger(__name__)


class MediaCleanupService:
    """Serviço para limpeza automática de mídia não vinculada."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.cleanup_threshold_days = 1  # Limpar após 1 dia sem vínculo
    
    async def cleanup_unlinked_media(self) -> dict:
        """
        Remove uploads que não estão vinculados há mais de X dias.
        
        Returns:
            dict: Estatísticas da limpeza realizada
        """
        try:
            logger.info("🧹 Iniciando limpeza de mídia não vinculada")
            
            # Data limite para considerar mídia órfã
            threshold_date = datetime.utcnow() - timedelta(days=self.cleanup_threshold_days)
            
            # Buscar uploads antigos que não estão vinculados a menu items
            unlinked_uploads = await self._find_unlinked_uploads(threshold_date)
            
            cleanup_stats = {
                "total_found": len(unlinked_uploads),
                "files_deleted": 0,
                "db_records_deleted": 0,
                "errors": 0,
                "freed_space_mb": 0
            }
            
            for upload in unlinked_uploads:
                try:
                    # Calcular espaço liberado
                    file_size_mb = upload.file_size / (1024 * 1024) if upload.file_size else 0
                    
                    # Remover arquivo físico
                    if await self._delete_physical_file(upload):
                        cleanup_stats["files_deleted"] += 1
                        cleanup_stats["freed_space_mb"] += file_size_mb
                    
                    # Remover registro do banco
                    await self.db.delete(upload)
                    cleanup_stats["db_records_deleted"] += 1
                    
                    logger.info(f"🗑️ Removido upload órfão: {upload.id} ({upload.filename})")
                    
                except Exception as e:
                    logger.error(f"❌ Erro ao remover upload {upload.id}: {e}")
                    cleanup_stats["errors"] += 1
            
            # Commit das mudanças
            await self.db.commit()
            
            logger.info(f"✅ Limpeza concluída: {cleanup_stats}")
            return cleanup_stats
            
        except Exception as e:
            logger.error(f"❌ Erro na limpeza de mídia: {e}")
            await self.db.rollback()
            raise
    
    async def _find_unlinked_uploads(self, threshold_date: datetime) -> List[MediaUpload]:
        """
        Encontra uploads que não estão vinculados a menu items e são antigos.
        
        Args:
            threshold_date: Data limite para considerar upload órfão
            
        Returns:
            List[MediaUpload]: Lista de uploads não vinculados
        """
        # Query para encontrar uploads que:
        # 1. São mais antigos que o threshold
        # 2. Não estão vinculados a menu items
        # 3. Têm status de sucesso (não são uploads em andamento)
        stmt = select(MediaUpload).where(
            and_(
                MediaUpload.created_at < threshold_date,
                MediaUpload.upload_status == MediaUploadStatus.COMPLETED,
                ~MediaUpload.id.in_(
                    select(MediaMenuItemMedia.media_upload_id)
                )
            )
        )
        
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def _delete_physical_file(self, upload: MediaUpload) -> bool:
        """
        Remove arquivo físico do disco.
        
        Args:
            upload: Upload a ser removido
            
        Returns:
            bool: True se arquivo foi removido com sucesso
        """
        try:
            if not upload.file_path:
                logger.warning(f"Upload {upload.id} não tem file_path definido")
                return False
            
            file_path = Path(upload.file_path)
            
            # Remover arquivo principal
            if file_path.exists():
                file_path.unlink()
                logger.debug(f"Removido arquivo: {file_path}")
            
            # Remover thumbnail se existir
            if upload.thumbnail_path:
                thumb_path = Path(upload.thumbnail_path)
                if thumb_path.exists():
                    thumb_path.unlink()
                    logger.debug(f"Removido thumbnail: {thumb_path}")
            
            # Remover versão comprimida se existir
            if upload.compressed_path:
                compressed_path = Path(upload.compressed_path)
                if compressed_path.exists():
                    compressed_path.unlink()
                    logger.debug(f"Removido arquivo comprimido: {compressed_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"Erro ao remover arquivo físico {upload.file_path}: {e}")
            return False
    
    async def get_cleanup_candidates(self) -> List[dict]:
        """
        Retorna lista de uploads candidatos à limpeza (para preview).
        
        Returns:
            List[dict]: Lista de uploads que seriam removidos
        """
        threshold_date = datetime.utcnow() - timedelta(days=self.cleanup_threshold_days)
        unlinked_uploads = await self._find_unlinked_uploads(threshold_date)
        
        candidates = []
        for upload in unlinked_uploads:
            file_size_mb = upload.file_size / (1024 * 1024) if upload.file_size else 0
            candidates.append({
                "id": str(upload.id),
                "filename": upload.filename,
                "file_size_mb": round(file_size_mb, 2),
                "created_at": upload.created_at.isoformat(),
                "days_old": (datetime.utcnow() - upload.created_at).days
            })
        
        return candidates


async def run_media_cleanup() -> dict:
    """
    Função utilitária para executar limpeza de mídia.
    
    Returns:
        dict: Estatísticas da limpeza
    """
    async for db in get_db():
        try:
            cleanup_service = MediaCleanupService(db)
            return await cleanup_service.cleanup_unlinked_media()
        finally:
            await db.close()


if __name__ == "__main__":
    # Permite executar limpeza diretamente
    asyncio.run(run_media_cleanup())
