"""
Comprehensive Audit Service for logging all critical system operations.
Provides complete audit trail with database persistence and advanced querying.
"""

import uuid
import logging
import time
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import and_, or_, func, desc, asc, text
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

from app.modules.core.audit.models.audit_log import AuditLog
from app.modules.core.audit.schemas.audit import (
    AuditAction, AuditResource, OperationType, AuditStatus, SecurityLevel,
    AuditLogCreate, AuditLogUpdate, AuditLogRead, AuditLogFilter,
    AuditLogListResponse, AuditLogStats, ComplianceReport
)

logger = logging.getLogger(__name__)


class AuditService:
    """
    Comprehensive audit service for logging all critical system operations.

    This service provides:
    - Complete audit trail with database persistence
    - Advanced querying and filtering capabilities
    - Security classification and compliance tracking
    - Performance monitoring and statistics
    - Export capabilities for compliance reporting
    """

    def __init__(self, db: AsyncSession):
        self.db = db

    async def log_action(
        self,
        user_id: Optional[str] = None,
        action: AuditAction = None,
        resource: AuditResource = None,
        resource_id: Optional[str] = None,
        tenant_id: Optional[str] = None,
        operation_type: OperationType = OperationType.EXECUTE,
        old_values: Optional[Dict[str, Any]] = None,
        new_values: Optional[Dict[str, Any]] = None,
        details: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None,
        request_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        security_level: SecurityLevel = SecurityLevel.NORMAL,
        compliance_tags: Optional[List[str]] = None,
        status: AuditStatus = AuditStatus.SUCCESS,
        error_message: Optional[str] = None,
        duration_ms: Optional[str] = None,
    ) -> str:
        """
        Log a comprehensive audit entry for any system operation.

        Args:
            user_id: ID of the user performing the action
            action: Type of action being performed
            resource: Type of resource being acted upon
            resource_id: ID of the specific resource
            tenant_id: Tenant context for multi-tenant operations
            operation_type: Database operation type (CREATE, READ, UPDATE, DELETE, EXECUTE)
            old_values: Previous state of the resource (for updates/deletes)
            new_values: New state of the resource (for creates/updates)
            details: Additional operation details
            ip_address: IP address of the user
            user_agent: User agent string
            session_id: Session identifier
            request_id: Request identifier for tracing
            correlation_id: Correlation identifier for workflow tracing
            security_level: Security classification level
            compliance_tags: Tags for compliance requirements
            status: Operation status
            error_message: Error details if operation failed
            duration_ms: Operation duration in milliseconds

        Returns:
            Audit log entry ID
        """
        try:
            # Create audit log entry in database
            audit_log = AuditLog(
                user_id=user_id,
                action=action.value if action else None,
                resource=resource.value if resource else None,
                resource_id=resource_id,
                tenant_id=tenant_id,
                operation_type=operation_type.value,
                status=status.value,
                old_values=old_values,
                new_values=new_values,
                details=details,
                ip_address=ip_address,
                user_agent=user_agent,
                session_id=session_id,
                request_id=request_id,
                correlation_id=correlation_id,
                security_level=security_level.value,
                compliance_tags=compliance_tags,
                error_message=error_message,
                duration_ms=duration_ms,
            )

            self.db.add(audit_log)
            await self.db.commit()
            await self.db.refresh(audit_log)

            # Also log to application logger for immediate visibility
            log_level = logging.ERROR if status == AuditStatus.FAILURE else logging.INFO
            logger.log(
                log_level,
                f"AUDIT: {action.value.upper() if action else 'UNKNOWN'} "
                f"{resource.value if resource else 'UNKNOWN'} "
                f"by user {user_id} "
                f"(resource_id: {resource_id}) "
                f"- Status: {status.value} "
                f"- Details: {details}",
                extra={
                    "audit_id": str(audit_log.id),
                    "user_id": user_id,
                    "action": action.value if action else None,
                    "resource": resource.value if resource else None,
                    "resource_id": resource_id,
                    "tenant_id": tenant_id,
                    "status": status.value,
                    "security_level": security_level.value,
                }
            )

            return str(audit_log.id)

        except Exception as e:
            logger.error(f"Error logging audit action: {e}", exc_info=True)
            # Don't raise exception to avoid breaking the main operation
            return str(uuid.uuid4())

    async def get_audit_logs(
        self,
        filters: AuditLogFilter
    ) -> AuditLogListResponse:
        """
        Get audit log entries with comprehensive filtering and pagination.

        Args:
            filters: Filter criteria for audit log query

        Returns:
            Paginated list of audit log entries
        """
        try:
            # Build base query
            query = select(AuditLog)

            # Apply filters
            conditions = []

            if filters.user_id:
                conditions.append(AuditLog.user_id == filters.user_id)

            if filters.tenant_id:
                conditions.append(AuditLog.tenant_id == filters.tenant_id)

            if filters.action:
                conditions.append(AuditLog.action == filters.action.value)

            if filters.resource:
                conditions.append(AuditLog.resource == filters.resource.value)

            if filters.resource_id:
                conditions.append(AuditLog.resource_id == filters.resource_id)

            if filters.operation_type:
                conditions.append(AuditLog.operation_type == filters.operation_type.value)

            if filters.status:
                conditions.append(AuditLog.status == filters.status.value)

            if filters.security_level:
                conditions.append(AuditLog.security_level == filters.security_level.value)

            if filters.start_date:
                conditions.append(AuditLog.timestamp >= filters.start_date)

            if filters.end_date:
                conditions.append(AuditLog.timestamp <= filters.end_date)

            if filters.ip_address:
                conditions.append(AuditLog.ip_address == filters.ip_address)

            if filters.search_term:
                search_conditions = [
                    AuditLog.details.astext.ilike(f"%{filters.search_term}%"),
                    AuditLog.error_message.ilike(f"%{filters.search_term}%"),
                    AuditLog.resource_id.ilike(f"%{filters.search_term}%"),
                ]
                conditions.append(or_(*search_conditions))

            if conditions:
                query = query.where(and_(*conditions))

            # Apply sorting
            if filters.sort_order == "desc":
                query = query.order_by(desc(getattr(AuditLog, filters.sort_by)))
            else:
                query = query.order_by(asc(getattr(AuditLog, filters.sort_by)))

            # Get total count
            count_query = select(func.count()).select_from(query.subquery())
            total_result = await self.db.execute(count_query)
            total = total_result.scalar()

            # Apply pagination
            query = query.offset(filters.skip).limit(filters.limit)

            # Execute query
            result = await self.db.execute(query)
            audit_logs = result.scalars().all()

            # Convert to response format
            items = [AuditLogRead.from_orm(log) for log in audit_logs]

            return AuditLogListResponse(
                items=items,
                total=total,
                skip=filters.skip,
                limit=filters.limit,
                has_more=(filters.skip + len(items)) < total
            )

        except Exception as e:
            logger.error(f"Error getting audit logs: {e}", exc_info=True)
            return AuditLogListResponse(
                items=[],
                total=0,
                skip=filters.skip,
                limit=filters.limit,
                has_more=False
            )

    async def get_audit_stats(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        tenant_id: Optional[str] = None,
    ) -> AuditLogStats:
        """
        Get comprehensive audit statistics.

        Args:
            start_date: Start date for statistics
            end_date: End date for statistics
            tenant_id: Filter by tenant ID

        Returns:
            Audit statistics
        """
        try:
            # Build base query
            query = select(AuditLog)
            conditions = []

            if start_date:
                conditions.append(AuditLog.timestamp >= start_date)
            if end_date:
                conditions.append(AuditLog.timestamp <= end_date)
            if tenant_id:
                conditions.append(AuditLog.tenant_id == tenant_id)

            if conditions:
                query = query.where(and_(*conditions))

            # Get total entries
            total_result = await self.db.execute(select(func.count()).select_from(query.subquery()))
            total_entries = total_result.scalar()

            # Get status breakdown
            status_query = select(
                AuditLog.status,
                func.count(AuditLog.id).label('count')
            ).group_by(AuditLog.status)

            if conditions:
                status_query = status_query.where(and_(*conditions))

            status_result = await self.db.execute(status_query)
            status_breakdown = {row.status: row.count for row in status_result}

            # Get resource breakdown
            resource_query = select(
                AuditLog.resource,
                func.count(AuditLog.id).label('count')
            ).group_by(AuditLog.resource)

            if conditions:
                resource_query = resource_query.where(and_(*conditions))

            resource_result = await self.db.execute(resource_query)
            resource_breakdown = {row.resource: row.count for row in resource_result}

            # Get action breakdown
            action_query = select(
                AuditLog.action,
                func.count(AuditLog.id).label('count')
            ).group_by(AuditLog.action)

            if conditions:
                action_query = action_query.where(and_(*conditions))

            action_result = await self.db.execute(action_query)
            action_breakdown = {row.action: row.count for row in action_result}

            # Get recent activity (last 24 hours)
            recent_date = datetime.utcnow() - timedelta(hours=24)
            recent_conditions = conditions + [AuditLog.timestamp >= recent_date]
            recent_query = select(func.count()).select_from(
                select(AuditLog).where(and_(*recent_conditions)).subquery()
            )
            recent_result = await self.db.execute(recent_query)
            recent_activity_count = recent_result.scalar()

            # Get sensitive and critical operations count
            sensitive_conditions = conditions + [AuditLog.security_level.in_(['SENSITIVE', 'CRITICAL'])]
            sensitive_query = select(func.count()).select_from(
                select(AuditLog).where(and_(*sensitive_conditions)).subquery()
            )
            sensitive_result = await self.db.execute(sensitive_query)
            sensitive_operations_count = sensitive_result.scalar()

            critical_conditions = conditions + [AuditLog.security_level == 'CRITICAL']
            critical_query = select(func.count()).select_from(
                select(AuditLog).where(and_(*critical_conditions)).subquery()
            )
            critical_result = await self.db.execute(critical_query)
            critical_operations_count = critical_result.scalar()

            return AuditLogStats(
                total_entries=total_entries,
                success_count=status_breakdown.get('SUCCESS', 0),
                failure_count=status_breakdown.get('FAILURE', 0),
                pending_count=status_breakdown.get('PENDING', 0),
                resource_breakdown=resource_breakdown,
                action_breakdown=action_breakdown,
                recent_activity_count=recent_activity_count,
                sensitive_operations_count=sensitive_operations_count,
                critical_operations_count=critical_operations_count,
            )

        except Exception as e:
            logger.error(f"Error getting audit stats: {e}", exc_info=True)
            return AuditLogStats(
                total_entries=0,
                success_count=0,
                failure_count=0,
                pending_count=0,
                resource_breakdown={},
                action_breakdown={},
                recent_activity_count=0,
                sensitive_operations_count=0,
                critical_operations_count=0,
            )

    async def get_user_audit_logs(
        self,
        user_id: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        action: Optional[AuditAction] = None,
        resource: Optional[AuditResource] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> AuditLogListResponse:
        """
        Get audit logs for a specific user.

        Args:
            user_id: User ID to filter by
            start_date: Start date for filtering
            end_date: End date for filtering
            action: Filter by action type
            resource: Filter by resource type
            skip: Number of entries to skip
            limit: Maximum number of entries to return

        Returns:
            Paginated list of user audit log entries
        """
        filters = AuditLogFilter(
            user_id=user_id,
            start_date=start_date,
            end_date=end_date,
            action=action,
            resource=resource,
            skip=skip,
            limit=limit,
            sort_by="timestamp",
            sort_order="desc",
        )

        return await self.get_audit_logs(filters)

    async def get_resource_audit_logs(
        self,
        resource: AuditResource,
        resource_id: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        action: Optional[AuditAction] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> AuditLogListResponse:
        """
        Get audit logs for a specific resource.

        Args:
            resource: Resource type
            resource_id: Resource ID to filter by
            start_date: Start date for filtering
            end_date: End date for filtering
            action: Filter by action type
            skip: Number of entries to skip
            limit: Maximum number of entries to return

        Returns:
            Paginated list of resource audit log entries
        """
        filters = AuditLogFilter(
            resource=resource,
            resource_id=resource_id,
            start_date=start_date,
            end_date=end_date,
            action=action,
            skip=skip,
            limit=limit,
            sort_by="timestamp",
            sort_order="desc",
        )

        return await self.get_audit_logs(filters)

    async def get_user_audit_log(
        self,
        db: AsyncSession,
        user_id: str,
        skip: int = 0,
        limit: int = 100,
    ) -> List[Dict[str, Any]]:
        """
        Get audit log entries for a specific user.
        
        Args:
            db: Database session
            user_id: User ID to filter by
            skip: Number of entries to skip
            limit: Maximum number of entries to return
            
        Returns:
            List of audit log entries for the user
        """
        return await self.get_audit_log(
            db=db,
            user_id=user_id,
            skip=skip,
            limit=limit
        )

    async def get_resource_audit_log(
        self,
        db: AsyncSession,
        resource: AuditResource,
        resource_id: str,
        skip: int = 0,
        limit: int = 100,
    ) -> List[Dict[str, Any]]:
        """
        Get audit log entries for a specific resource.
        
        Args:
            db: Database session
            resource: Resource type
            resource_id: Resource ID to filter by
            skip: Number of entries to skip
            limit: Maximum number of entries to return
            
        Returns:
            List of audit log entries for the resource
        """
        return await self.get_audit_log(
            db=db,
            resource=resource,
            resource_id=resource_id,
            skip=skip,
            limit=limit
        )
