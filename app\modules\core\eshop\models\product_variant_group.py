import uuid
from sqlalchemy import <PERSON>um<PERSON>, <PERSON>te<PERSON>, String, ForeignKey, Index, Boolean, Table
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.orm import relationship

from app.db.base import Base
from app.modules.core.tenants.models.tenant import Tenant


# Association table for Product-VariantGroup many-to-many relationship
eshop_product_variant_groups_association = Table(
    'eshop_product_variant_groups_association',
    Base.metadata,
    Column('product_id', PG_UUID(as_uuid=True), ForeignKey('eshop_products.id'), primary_key=True),
    Column('variant_group_id', PG_UUID(as_uuid=True), ForeignKey('eshop_product_variant_groups.id'), primary_key=True),
    Column('tenant_id', PG_UUID(as_uuid=True), ForeignKey('tenants.id'), nullable=True),  # For tenant isolation
    Index('ix_eshop_product_variant_groups_assoc_product_id', 'product_id'),
    Index('ix_eshop_product_variant_groups_assoc_variant_group_id', 'variant_group_id'),
    Index('ix_eshop_product_variant_groups_assoc_tenant_id', 'tenant_id'),
)


class ProductVariantGroup(Base):
    """
    Represents a group of choices for a product variant (e.g., Size, Color, Material).
    Similar to menu variant groups but for marketplace products.
    """

    __tablename__ = "eshop_product_variant_groups"

    # Primary key
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Tenant isolation - can be global or tenant-specific
    tenant_id = Column(PG_UUID(as_uuid=True), ForeignKey(Tenant.id), nullable=True, index=True)
    
    # Group properties
    name = Column(String(100), nullable=False)  # e.g., "Size", "Color", "Material"
    description = Column(String(255), nullable=True)  # Optional description
    min_selection = Column(Integer, default=1, nullable=False)
    max_selection = Column(Integer, default=1, nullable=False)
    display_order = Column(Integer, default=0, nullable=False)  # Order within the product
    
    # Group status
    is_required = Column(Boolean, default=False, nullable=False)  # Is this group required for the product?
    is_active = Column(Boolean, default=True, nullable=False)  # Is this group active?
    requires_default_selection = Column(Boolean, default=True, nullable=False)  # Requires one option to always be selected as default
    
    # Template system (similar to menu system)
    is_template = Column(Boolean, default=False, nullable=False)  # Is this a template group?
    template_id = Column(PG_UUID(as_uuid=True), ForeignKey("eshop_product_variant_groups.id"), nullable=True)  # Reference to template
    
    # Relationships
    tenant = relationship("Tenant")
    template = relationship("ProductVariantGroup", remote_side=[id])
    
    # Many-to-many relationship with Product
    products = relationship(
        "app.modules.core.eshop.models.product.Product",
        secondary=eshop_product_variant_groups_association,
        back_populates="variant_groups"
    )
    
    options = relationship(
        "ProductVariantOption", 
        back_populates="variant_group", 
        cascade="all, delete-orphan"
    )

    __table_args__ = (
        Index("ix_eshop_variant_groups_tenant_id", "tenant_id"),
        Index("ix_eshop_variant_groups_tenant_id_name", "tenant_id", "name"),
        Index("ix_eshop_variant_groups_template_id", "template_id"),
    )

    def __repr__(self):
        return f"<ProductVariantGroup(id={self.id}, name='{self.name}', tenant_id={self.tenant_id})>"
