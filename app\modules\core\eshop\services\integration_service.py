"""
EShop Integration Service - Central integration point for all e-commerce modules.

This service handles the integration between the unified EShop module and
13 existing core e-commerce modules, maintaining data consistency and
business logic across the entire e-commerce ecosystem.
"""

import uuid
import logging
from typing import Dict, List, Optional, Any, Tuple
from decimal import Decimal
from datetime import datetime

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.db.session import get_db
from app.core.exceptions import ValidationError, NotFoundError
from app.modules.core.eshop.models.product import Product
from app.modules.core.eshop.models.product_approval import (
    ProductApprovalHistory, ProductApprovalSettings
)
from app.modules.core.functions.inventory.models.inventory_item import InventoryItem
from app.modules.core.functions.orders.models.order import Order, OrderItem
from app.modules.core.functions.shipping.models.shipping import Shipment
from app.modules.shared.crm.models.account import Account
from app.modules.core.functions.pos.models.sale_transaction import SaleTransaction
from app.modules.core.notifications.services.notification_service import (
    NotificationService
)

logger = logging.getLogger(__name__)


class EShopIntegrationService:
    """Central service for EShop module integrations."""

    def __init__(self, db: Session):
        self.db = db
        self.notification_service = NotificationService(db)

    # =====================================================================
    # INVENTORY INTEGRATION
    # =====================================================================

    async def sync_product_inventory(
        self, product_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Dict[str, Any]:
        """Sync EShop product with inventory system."""
        try:
            # Get EShop product
            product = self.db.query(Product).filter(
                and_(Product.id == product_id, Product.tenant_id == tenant_id)
            ).first()
            
            if not product:
                raise NotFoundError(f"Product {product_id} not found")

            # Check existing inventory item
            inventory_item = self.db.query(InventoryItem).filter(
                and_(
                    InventoryItem.product_id == product_id,
                    InventoryItem.tenant_id == tenant_id
                )
            ).first()

            if not inventory_item:
                # Create new inventory item
                inventory_item = InventoryItem(
                    tenant_id=tenant_id,
                    product_id=product_id,
                    name=product.name,
                    description=product.description,
                    sku=product.sku,
                    quantity=0,  # Initial stock
                    low_stock_threshold=product.minimum_order_quantity or 5,
                    unit_cost=product.cost_price
                )
                self.db.add(inventory_item)
            else:
                # Update existing inventory item
                inventory_item.name = product.name
                inventory_item.description = product.description
                inventory_item.sku = product.sku
                inventory_item.unit_cost = product.cost_price

            self.db.commit()
            
            logger.info(f"Product {product_id} synced with inventory")
            return {
                "status": "success",
                "inventory_item_id": inventory_item.id,
                "current_stock": inventory_item.quantity
            }

        except Exception as e:
            self.db.rollback()
            logger.error(f"Error syncing product inventory: {str(e)}")
            raise

    async def update_stock_from_order(
        self, order_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Dict[str, Any]:
        """Update inventory stock when order is placed."""
        try:
            order = self.db.query(Order).filter(
                and_(Order.id == order_id, Order.tenant_id == tenant_id)
            ).first()
            
            if not order:
                raise NotFoundError(f"Order {order_id} not found")

            stock_updates = []
            for order_item in order.items:
                inventory_item = self.db.query(InventoryItem).filter(
                    and_(
                        InventoryItem.product_id == order_item.menu_item_id,
                        InventoryItem.tenant_id == tenant_id
                    )
                ).first()

                if inventory_item:
                    # Reduce stock
                    inventory_item.quantity -= order_item.quantity
                    stock_updates.append({
                        "product_id": order_item.menu_item_id,
                        "old_quantity": inventory_item.quantity + order_item.quantity,
                        "new_quantity": inventory_item.quantity,
                        "reduced_by": order_item.quantity
                    })

            self.db.commit()
            return {"status": "success", "stock_updates": stock_updates}

        except Exception as e:
            self.db.rollback()
            logger.error(f"Error updating stock from order: {str(e)}")
            raise

    # =====================================================================
    # ORDERS INTEGRATION
    # =====================================================================

    async def create_eshop_order(
        self, 
        product_data: List[Dict], 
        customer_id: uuid.UUID,
        tenant_id: uuid.UUID,
        order_metadata: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """Create order with EShop products."""
        try:
            # Calculate totals
            subtotal = Decimal('0.00')
            order_items_data = []

            for item in product_data:
                product = self.db.query(Product).filter(
                    and_(
                        Product.id == item['product_id'],
                        Product.tenant_id == tenant_id
                    )
                ).first()

                if not product:
                    raise NotFoundError(f"Product {item['product_id']} not found")

                if product.approval_status != 'approved':
                    raise ValidationError(
                        f"Product {product.name} is not approved for sale"
                    )

                quantity = item['quantity']
                unit_price = product.price
                total_price = unit_price * quantity
                subtotal += total_price

                order_items_data.append({
                    'product': product,
                    'quantity': quantity,
                    'unit_price': unit_price,
                    'total_price': total_price
                })

            # Calculate tax and total
            tax_rate = Decimal('0.10')  # 10% tax
            tax = subtotal * tax_rate
            total = subtotal + tax

            # Create order
            order = Order(
                tenant_id=tenant_id,
                customer_id=customer_id,
                order_number=f"ESH-{datetime.now().strftime('%Y%m%d')}-{uuid.uuid4().hex[:8].upper()}",
                order_type="eshop",
                subtotal=subtotal,
                tax=tax,
                total=total,
                order_metadata=order_metadata or {}
            )
            self.db.add(order)
            self.db.flush()  # Get order ID

            # Create order items
            for item_data in order_items_data:
                order_item = OrderItem(
                    order_id=order.id,
                    menu_item_id=item_data['product'].id,
                    name=item_data['product'].name,
                    quantity=item_data['quantity'],
                    unit_price=item_data['unit_price'],
                    total_price=item_data['total_price']
                )
                self.db.add(order_item)

            self.db.commit()

            # Update inventory
            await self.update_stock_from_order(order.id, tenant_id)

            # Send notifications
            await self.notification_service.send_order_confirmation(
                order.id, customer_id, tenant_id
            )

            return {
                "status": "success",
                "order_id": order.id,
                "order_number": order.order_number,
                "total": float(total)
            }

        except Exception as e:
            self.db.rollback()
            logger.error(f"Error creating EShop order: {str(e)}")
            raise

    # =====================================================================
    # FINANCIAL INTEGRATION
    # =====================================================================

    async def calculate_commission(
        self, product_id: uuid.UUID, sale_amount: Decimal, tenant_id: uuid.UUID
    ) -> Dict[str, Any]:
        """Calculate commission for EShop product sale."""
        try:
            product = self.db.query(Product).filter(
                and_(Product.id == product_id, Product.tenant_id == tenant_id)
            ).first()

            if not product:
                raise NotFoundError(f"Product {product_id} not found")

            # Get commission rate from product or default
            commission_rate = product.commission_rate or Decimal('0.05')  # 5% default
            commission_amount = sale_amount * commission_rate

            # Different rates for different market types
            if product.market_type == 'b2b':
                commission_rate *= Decimal('0.8')  # 20% reduction for B2B
            elif product.market_type == 'cuponic_migrated':
                commission_rate *= Decimal('1.2')  # 20% increase for Cuponic

            commission_amount = sale_amount * commission_rate

            return {
                "product_id": product_id,
                "sale_amount": float(sale_amount),
                "commission_rate": float(commission_rate),
                "commission_amount": float(commission_amount),
                "market_type": product.market_type
            }

        except Exception as e:
            logger.error(f"Error calculating commission: {str(e)}")
            raise

    # =====================================================================
    # CRM INTEGRATION
    # =====================================================================

    async def sync_customer_purchase_history(
        self, customer_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Dict[str, Any]:
        """Sync customer purchase history with CRM."""
        try:
            # Get customer account
            account = self.db.query(Account).filter(
                and_(Account.id == customer_id, Account.tenant_id == tenant_id)
            ).first()

            if not account:
                raise NotFoundError(f"Customer {customer_id} not found")

            # Get EShop orders for this customer
            eshop_orders = self.db.query(Order).filter(
                and_(
                    Order.customer_id == customer_id,
                    Order.tenant_id == tenant_id,
                    Order.order_type == "eshop"
                )
            ).all()

            # Calculate customer metrics
            total_orders = len(eshop_orders)
            total_spent = sum(order.total for order in eshop_orders)
            avg_order_value = total_spent / total_orders if total_orders > 0 else 0

            # Update account status based on purchase history
            if total_orders > 0:
                if account.status == 'lead' or account.status == 'prospect':
                    account.status = 'customer'
                    account.acquisition_date = min(order.created_at for order in eshop_orders)

            account.last_contact_date = datetime.utcnow()
            self.db.commit()

            return {
                "customer_id": customer_id,
                "total_orders": total_orders,
                "total_spent": float(total_spent),
                "avg_order_value": float(avg_order_value),
                "status": account.status
            }

        except Exception as e:
            self.db.rollback()
            logger.error(f"Error syncing customer history: {str(e)}")
            raise

    # =====================================================================
    # POS INTEGRATION
    # =====================================================================

    async def enable_product_in_pos(
        self, product_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Dict[str, Any]:
        """Enable EShop product for POS sales."""
        try:
            product = self.db.query(Product).filter(
                and_(Product.id == product_id, Product.tenant_id == tenant_id)
            ).first()

            if not product:
                raise NotFoundError(f"Product {product_id} not found")

            # Update product metadata to include POS settings
            pos_settings = {
                "pos_enabled": True,
                "pos_category": product.category_id,
                "pos_display_name": product.name,
                "pos_barcode": product.sku,
                "pos_quick_select": product.market_type == 'b2c'
            }

            if not product.metadata:
                product.metadata = {}
            
            product.metadata.update(pos_settings)
            self.db.commit()

            return {
                "status": "success",
                "product_id": product_id,
                "pos_enabled": True,
                "settings": pos_settings
            }

        except Exception as e:
            self.db.rollback()
            logger.error(f"Error enabling product in POS: {str(e)}")
            raise

    # =====================================================================
    # UTILITY METHODS
    # =====================================================================

    async def get_integration_status(
        self, product_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Dict[str, Any]:
        """Get comprehensive integration status for a product."""
        try:
            product = self.db.query(Product).filter(
                and_(Product.id == product_id, Product.tenant_id == tenant_id)
            ).first()

            if not product:
                raise NotFoundError(f"Product {product_id} not found")

            # Check inventory integration
            inventory_item = self.db.query(InventoryItem).filter(
                and_(
                    InventoryItem.product_id == product_id,
                    InventoryItem.tenant_id == tenant_id
                )
            ).first()

            # Check POS integration
            pos_enabled = bool(
                product.metadata and 
                product.metadata.get('pos_enabled', False)
            )

            return {
                "product_id": product_id,
                "product_name": product.name,
                "approval_status": product.approval_status,
                "market_type": product.market_type,
                "integrations": {
                    "inventory": {
                        "enabled": inventory_item is not None,
                        "stock_level": inventory_item.quantity if inventory_item else 0
                    },
                    "pos": {
                        "enabled": pos_enabled
                    },
                    "orders": {
                        "enabled": True  # Always enabled for approved products
                    },
                    "financial": {
                        "commission_rate": float(product.commission_rate or 0.05)
                    }
                }
            }

        except Exception as e:
            logger.error(f"Error getting integration status: {str(e)}")
            raise

    async def bulk_sync_products(
        self, tenant_id: uuid.UUID, limit: int = 100
    ) -> Dict[str, Any]:
        """Bulk sync EShop products with all integrated modules."""
        try:
            products = self.db.query(Product).filter(
                and_(
                    Product.tenant_id == tenant_id,
                    Product.approval_status == 'approved'
                )
            ).limit(limit).all()

            synced_count = 0
            errors = []

            for product in products:
                try:
                    # Sync with inventory
                    await self.sync_product_inventory(product.id, tenant_id)
                    
                    # Enable in POS if B2C
                    if product.market_type == 'b2c':
                        await self.enable_product_in_pos(product.id, tenant_id)
                    
                    synced_count += 1

                except Exception as e:
                    errors.append({
                        "product_id": str(product.id),
                        "error": str(e)
                    })

            return {
                "status": "completed",
                "synced_count": synced_count,
                "total_products": len(products),
                "errors": errors
            }

        except Exception as e:
            logger.error(f"Error in bulk sync: {str(e)}")
            raise