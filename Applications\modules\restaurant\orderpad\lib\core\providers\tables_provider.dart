import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../models/table_model.dart';
import '../models/order_model.dart';
import '../services/storage_service.dart';
import '../constants/app_constants.dart';

// Tables state
class TablesState {
  final List<TableModel> tables;
  final bool isLoading;
  final String? error;
  final TableModel? selectedTable;
  final String selectedArea;

  const TablesState({
    this.tables = const [],
    this.isLoading = false,
    this.error,
    this.selectedTable,
    this.selectedArea = 'all',
  });

  TablesState copyWith({
    List<TableModel>? tables,
    bool? isLoading,
    String? error,
    TableModel? selectedTable,
    String? selectedArea,
  }) {
    return TablesState(
      tables: tables ?? this.tables,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      selectedTable: selectedTable ?? this.selectedTable,
      selectedArea: selectedArea ?? this.selectedArea,
    );
  }

  List<TableModel> get freeTables => tables.where((t) => t.isAvailable).toList();
  List<TableModel> get occupiedTables => tables.where((t) => t.isOccupied).toList();
  List<TableModel> get reservedTables => tables.where((t) => t.isReserved).toList();
  List<TableModel> get cleaningTables => tables.where((t) => t.isCleaning).toList();
  
  Map<String, int> get statistics => {
    'total': tables.length,
    'free': freeTables.length,
    'occupied': occupiedTables.length,
    'reserved': reservedTables.length,
    'cleaning': cleaningTables.length,
  };
}

// Tables notifier
class TablesNotifier extends StateNotifier<TablesState> {
  TablesNotifier() : super(const TablesState()) {
    loadTables();
  }

  final _uuid = const Uuid();

  Future<void> loadTables() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final tablesData = await StorageService.getAllTables();
      
      // If no tables exist, create mock tables
      if (tablesData.isEmpty) {
        await _createMockTables();
        final mockTablesData = await StorageService.getAllTables();
        final mockTables = mockTablesData.map((data) => TableModel.fromJson(data)).toList();
        state = state.copyWith(
          tables: mockTables,
          isLoading: false,
        );
      } else {
        final tables = tablesData.map((data) => TableModel.fromJson(data)).toList();
        state = state.copyWith(
          tables: tables,
          isLoading: false,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Erro ao carregar mesas: ${e.toString()}',
      );
    }
  }

  Future<void> _createMockTables() async {
    final mockTables = [
      // Indoor tables
      TableModel(
        id: _uuid.v4(),
        number: '1',
        name: 'Mesa 1',
        capacity: 4,
        status: 'available',
        area: TableArea.indoor.toString().split('.').last,
        position: const {'x': 100, 'y': 100},
        shape: TableShape.round,
      ),
      TableModel(
        id: _uuid.v4(),
        number: '2',
        name: 'Mesa 2',
        capacity: 2,
        status: 'occupied',
        area: TableArea.indoor.toString().split('.').last,
        position: const {'x': 200, 'y': 100},
        shape: TableShape.square,
        currentOrderId: 'mock-order-1',
        assignedWaiterId: 'staff-001',
        occupiedAt: DateTime.now().subtract(const Duration(minutes: 30)),
      ),
      TableModel(
        id: _uuid.v4(),
        number: '3',
        name: 'Mesa 3',
        capacity: 6,
        status: 'reserved',
        area: TableArea.indoor.toString().split('.').last,
        position: const {'x': 300, 'y': 100},
        shape: TableShape.rectangular,
        reservedAt: DateTime.now().add(const Duration(hours: 1)),
      ),
      TableModel(
        id: _uuid.v4(),
        number: '4',
        name: 'Mesa 4',
        capacity: 4,
        status: 'cleaning',
        area: TableArea.indoor.toString().split('.').last,
        position: const {'x': 100, 'y': 200},
        shape: TableShape.round,
      ),
      
      // Outdoor tables
      TableModel(
        id: _uuid.v4(),
        number: '5',
        name: 'Mesa 5 - Terraço',
        capacity: 4,
        status: 'available',
        area: TableArea.outdoor.toString().split('.').last,
        position: const {'x': 150, 'y': 300},
        shape: TableShape.round,
      ),
      TableModel(
        id: _uuid.v4(),
        number: '6',
        name: 'Mesa 6 - Terraço',
        capacity: 2,
        status: 'occupied',
        area: TableArea.outdoor.toString().split('.').last,
        position: const {'x': 250, 'y': 300},
        shape: TableShape.square,
        currentOrderId: 'mock-order-2',
        assignedWaiterId: 'staff-001',
        occupiedAt: DateTime.now().subtract(const Duration(minutes: 15)),
      ),
      
      // VIP tables
      TableModel(
        id: _uuid.v4(),
        number: '7',
        name: 'Mesa VIP 1',
        capacity: 8,
        status: 'available',
        area: TableArea.vip.toString().split('.').last,
        position: const {'x': 400, 'y': 200},
        shape: TableShape.rectangular,
      ),
      
      // Bar tables
      TableModel(
        id: _uuid.v4(),
        number: '8',
        name: 'Balcão 1',
        capacity: 2,
        status: 'available',
        area: TableArea.bar.toString().split('.').last,
        position: const {'x': 500, 'y': 100},
        shape: TableShape.square,
      ),
      TableModel(
        id: _uuid.v4(),
        number: '9',
        name: 'Balcão 2',
        capacity: 2,
        status: 'occupied',
        area: TableArea.bar.toString().split('.').last,
        position: const {'x': 500, 'y': 150},
        shape: TableShape.square,
        currentOrderId: 'mock-order-3',
        assignedWaiterId: 'staff-001',
        occupiedAt: DateTime.now().subtract(const Duration(minutes: 45)),
      ),
    ];

    for (final table in mockTables) {
      await StorageService.saveTable(table);
    }
  }

  Future<bool> createTable({
    required int number,
    required String name,
    required int capacity,
    required String area,
    required String shape,
    Map<String, double>? position,
    String? notes,
  }) async {
    try {
      final table = TableModel(
        id: _uuid.v4(),
        number: number.toString(),
        name: name,
        capacity: capacity,
        status: 'available',
        area: area,
        position: position ?? const {'x': 0, 'y': 0},
        shape: TableShape.values.byName(shape),
        notes: notes,
      );

      await StorageService.saveTable(table);
      
      final updatedTables = [...state.tables, table];
      state = state.copyWith(tables: updatedTables);
      
      return true;
    } catch (e) {
      state = state.copyWith(
        error: 'Erro ao criar mesa: ${e.toString()}',
      );
      return false;
    }
  }

  Future<bool> updateTableStatus(String tableId, String newStatus, 
      {String? orderId, String? waiterId}) async {
    try {
      final tableIndex = state.tables.indexWhere((t) => t.id == tableId);
      if (tableIndex == -1) return false;

      final table = state.tables[tableIndex];
      final now = DateTime.now();
      
      TableModel updatedTable;
      
      switch (newStatus) {
        case 'occupied':
          updatedTable = table.copyWith(
            status: newStatus,
            currentOrderId: orderId,
            assignedWaiterId: waiterId,
            occupiedAt: now,
            reservedAt: null,
          );
          break;
        case 'reserved':
          updatedTable = table.copyWith(
            status: newStatus,
            reservedAt: now.add(const Duration(hours: 1)), // Default 1 hour reservation
          );
          break;
        case 'available':
          updatedTable = table.copyWith(
            status: newStatus,
            currentOrderId: null,
            assignedWaiterId: null,
            occupiedAt: null,
            reservedAt: null,
          );
          break;
        default:
          updatedTable = table.copyWith(status: newStatus);
      }

      await StorageService.saveTable(updatedTable);
      
      final updatedTables = [...state.tables];
      updatedTables[tableIndex] = updatedTable;
      
      state = state.copyWith(tables: updatedTables);
      return true;
    } catch (e) {
      state = state.copyWith(
        error: 'Erro ao atualizar status da mesa: ${e.toString()}',
      );
      return false;
    }
  }

  Future<bool> assignWaiterToTable(String tableId, String waiterId) async {
    try {
      final tableIndex = state.tables.indexWhere((t) => t.id == tableId);
      if (tableIndex == -1) return false;

      final table = state.tables[tableIndex];
      final updatedTable = table.copyWith(assignedWaiterId: waiterId);

      await StorageService.saveTable(updatedTable);
      
      final updatedTables = [...state.tables];
      updatedTables[tableIndex] = updatedTable;
      
      state = state.copyWith(tables: updatedTables);
      return true;
    } catch (e) {
      state = state.copyWith(
        error: 'Erro ao atribuir garçom: ${e.toString()}',
      );
      return false;
    }
  }

  Future<bool> updateTablePosition(String tableId, Map<String, double> position) async {
    try {
      final tableIndex = state.tables.indexWhere((t) => t.id == tableId);
      if (tableIndex == -1) return false;

      final table = state.tables[tableIndex];
      final updatedTable = table.copyWith(position: position);

      await StorageService.saveTable(updatedTable);
      
      final updatedTables = [...state.tables];
      updatedTables[tableIndex] = updatedTable;
      
      state = state.copyWith(tables: updatedTables);
      return true;
    } catch (e) {
      state = state.copyWith(
        error: 'Erro ao atualizar posição da mesa: ${e.toString()}',
      );
      return false;
    }
  }

  Future<bool> deleteTable(String tableId) async {
    try {
      final table = state.tables.firstWhere((t) => t.id == tableId);
      
      // Don't allow deletion of occupied tables
      if (table.status == 'occupied') {
        state = state.copyWith(
          error: 'Não é possível excluir mesa ocupada',
        );
        return false;
      }

      await StorageService.deleteTable(tableId);
      
      final updatedTables = state.tables.where((t) => t.id != tableId).toList();
      state = state.copyWith(tables: updatedTables);
      
      return true;
    } catch (e) {
      state = state.copyWith(
        error: 'Erro ao excluir mesa: ${e.toString()}',
      );
      return false;
    }
  }

  void selectTable(TableModel? table) {
    state = state.copyWith(selectedTable: table);
  }

  void setSelectedArea(String area) {
    state = state.copyWith(selectedArea: area);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  // Getters for filtered tables
  List<TableModel> get filteredTables {
    if (state.selectedArea == 'all') {
      return state.tables;
    }
    return state.tables.where((t) => t.area == state.selectedArea).toList();
  }

  List<TableModel> get freeTables => 
      state.tables.where((t) => t.status == 'available').toList();
  
  List<TableModel> get occupiedTables => 
      state.tables.where((t) => t.status == 'occupied').toList();
  
  List<TableModel> get reservedTables => 
      state.tables.where((t) => t.status == 'reserved').toList();
  
  List<TableModel> get cleaningTables => 
      state.tables.where((t) => t.status == 'cleaning').toList();

  List<TableModel> getTablesByArea(String area) {
    return state.tables.where((t) => t.area == area).toList();
  }

  List<TableModel> getTablesByWaiter(String waiterId) {
    return state.tables.where((t) => t.assignedWaiterId == waiterId).toList();
  }

  TableModel? getTableById(String tableId) {
    try {
      return state.tables.firstWhere((t) => t.id == tableId);
    } catch (e) {
      return null;
    }
  }

  // Statistics
  int get totalTables => state.tables.length;
  int get availableTables => freeTables.length;
  int get occupiedTablesCount => occupiedTables.length;
  double get occupancyRate => 
      totalTables > 0 ? (occupiedTablesCount / totalTables) * 100 : 0;
}

// Tables provider
final tablesProvider = StateNotifierProvider<TablesNotifier, TablesState>((ref) {
  return TablesNotifier();
});

// Convenience providers
final filteredTablesProvider = Provider<List<TableModel>>((ref) {
  return ref.watch(tablesProvider.notifier).filteredTables;
});

final freeTablesProvider = Provider<List<TableModel>>((ref) {
  return ref.watch(tablesProvider.notifier).freeTables;
});

final occupiedTablesProvider = Provider<List<TableModel>>((ref) {
  return ref.watch(tablesProvider.notifier).occupiedTables;
});

final reservedTablesProvider = Provider<List<TableModel>>((ref) {
  return ref.watch(tablesProvider.notifier).reservedTables;
});

final selectedTableProvider = Provider<TableModel?>((ref) {
  return ref.watch(tablesProvider).selectedTable;
});

final tableStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final notifier = ref.watch(tablesProvider.notifier);
  return {
    'total': notifier.totalTables,
    'available': notifier.availableTables,
    'occupied': notifier.occupiedTablesCount,
    'occupancyRate': notifier.occupancyRate,
  };
});