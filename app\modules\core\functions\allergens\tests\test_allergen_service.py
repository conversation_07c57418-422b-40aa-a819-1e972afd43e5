"""
Unit tests for AllergenService
"""

import pytest
import uuid
from unittest.mock import Mock, AsyncMock
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError
from fastapi import HTTPException

from app.modules.core.functions.allergens.models.allergen import Allergen
from app.modules.core.functions.allergens.schemas.allergen import AllergenCreate, AllergenUpdate
from app.modules.core.functions.allergens.services.allergen_service import AllergenService


@pytest.fixture
def mock_db():
    """Mock database session"""
    return Mock(spec=AsyncSession)


@pytest.fixture
def allergen_service(mock_db):
    """AllergenService instance with mocked database"""
    return AllergenService(mock_db)


@pytest.fixture
def sample_allergen():
    """Sample allergen for testing"""
    return Allergen(
        id=uuid.uuid4(),
        name="Glú<PERSON>",
        icon="🌾",
        description="Proteína encontrada no trigo, centeio, cevada e aveia",
        is_active=True
    )


@pytest.fixture
def sample_allergen_create():
    """Sample allergen create data"""
    return AllergenCreate(
        name="Teste Alérgeno",
        icon="🧪",
        description="Alérgeno de teste",
        is_active=True
    )


class TestAllergenServiceGet:
    """Tests for allergen retrieval operations"""
    
    @pytest.mark.asyncio
    async def test_get_allergens_success(self, allergen_service, mock_db, sample_allergen):
        """Test successful allergen retrieval"""
        # Mock database response
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = [sample_allergen]
        mock_db.execute.return_value = mock_result
        
        result = await allergen_service.get_allergens()
        
        assert len(result) == 1
        assert result[0] == sample_allergen
        mock_db.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_allergens_with_pagination(self, allergen_service, mock_db):
        """Test allergen retrieval with pagination"""
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = []
        mock_db.execute.return_value = mock_result
        
        await allergen_service.get_allergens(skip=10, limit=5)
        
        # Verify that execute was called (pagination parameters are in the query)
        mock_db.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_allergens_active_only(self, allergen_service, mock_db):
        """Test retrieving only active allergens"""
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = []
        mock_db.execute.return_value = mock_result
        
        await allergen_service.get_allergens(active_only=True)
        
        mock_db.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_allergen_by_id_success(self, allergen_service, mock_db, sample_allergen):
        """Test successful allergen retrieval by ID"""
        mock_result = Mock()
        mock_result.scalars.return_value.first.return_value = sample_allergen
        mock_db.execute.return_value = mock_result
        
        result = await allergen_service.get_allergen(sample_allergen.id)
        
        assert result == sample_allergen
        mock_db.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_allergen_by_id_not_found(self, allergen_service, mock_db):
        """Test allergen retrieval when not found"""
        mock_result = Mock()
        mock_result.scalars.return_value.first.return_value = None
        mock_db.execute.return_value = mock_result
        
        result = await allergen_service.get_allergen(uuid.uuid4())
        
        assert result is None
        mock_db.execute.assert_called_once()


class TestAllergenServiceCreate:
    """Tests for allergen creation operations"""
    
    @pytest.mark.asyncio
    async def test_create_allergen_success(self, allergen_service, mock_db, sample_allergen_create):
        """Test successful allergen creation"""
        # Mock successful creation
        mock_db.add = Mock()
        mock_db.commit = AsyncMock()
        mock_db.refresh = AsyncMock()
        
        # Mock the created allergen
        created_allergen = Allergen(
            id=uuid.uuid4(),
            **sample_allergen_create.model_dump()
        )
        
        # Mock refresh to set the created allergen
        async def mock_refresh(obj):
            for key, value in created_allergen.__dict__.items():
                if not key.startswith('_'):
                    setattr(obj, key, value)
        
        mock_db.refresh.side_effect = mock_refresh
        
        result = await allergen_service.create_allergen(sample_allergen_create)
        
        assert result.name == sample_allergen_create.name
        assert result.icon == sample_allergen_create.icon
        assert result.description == sample_allergen_create.description
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_allergen_integrity_error(self, allergen_service, mock_db, sample_allergen_create):
        """Test allergen creation with integrity error"""
        mock_db.add = Mock()
        mock_db.commit = AsyncMock(side_effect=IntegrityError("", "", ""))
        mock_db.rollback = AsyncMock()
        
        with pytest.raises(HTTPException) as exc_info:
            await allergen_service.create_allergen(sample_allergen_create)
        
        assert exc_info.value.status_code == 400
        mock_db.rollback.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_allergen_unexpected_error(self, allergen_service, mock_db, sample_allergen_create):
        """Test allergen creation with unexpected error"""
        mock_db.add = Mock()
        mock_db.commit = AsyncMock(side_effect=Exception("Unexpected error"))
        mock_db.rollback = AsyncMock()
        
        with pytest.raises(HTTPException) as exc_info:
            await allergen_service.create_allergen(sample_allergen_create)
        
        assert exc_info.value.status_code == 500
        mock_db.rollback.assert_called_once()


class TestAllergenServiceUpdate:
    """Tests for allergen update operations"""
    
    @pytest.mark.asyncio
    async def test_update_allergen_success(self, allergen_service, mock_db, sample_allergen):
        """Test successful allergen update"""
        # Mock finding the allergen
        mock_result = Mock()
        mock_result.scalars.return_value.first.return_value = sample_allergen
        mock_db.execute.return_value = mock_result
        
        mock_db.add = Mock()
        mock_db.commit = AsyncMock()
        mock_db.refresh = AsyncMock()
        
        update_data = AllergenUpdate(name="Updated Name")
        result = await allergen_service.update_allergen(sample_allergen.id, update_data)
        
        assert result.name == "Updated Name"
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_update_allergen_not_found(self, allergen_service, mock_db):
        """Test updating non-existent allergen"""
        mock_result = Mock()
        mock_result.scalars.return_value.first.return_value = None
        mock_db.execute.return_value = mock_result
        
        update_data = AllergenUpdate(name="Updated Name")
        result = await allergen_service.update_allergen(uuid.uuid4(), update_data)
        
        assert result is None
    
    @pytest.mark.asyncio
    async def test_update_allergen_no_changes(self, allergen_service, mock_db, sample_allergen):
        """Test updating allergen with no actual changes"""
        mock_result = Mock()
        mock_result.scalars.return_value.first.return_value = sample_allergen
        mock_db.execute.return_value = mock_result
        
        update_data = AllergenUpdate()  # No fields to update
        
        with pytest.raises(HTTPException) as exc_info:
            await allergen_service.update_allergen(sample_allergen.id, update_data)
        
        assert exc_info.value.status_code == 400
        assert "No fields provided" in str(exc_info.value.detail)


class TestAllergenServiceDelete:
    """Tests for allergen deletion operations"""
    
    @pytest.mark.asyncio
    async def test_delete_allergen_success(self, allergen_service, mock_db, sample_allergen):
        """Test successful allergen deletion"""
        # Mock finding the allergen
        mock_result = Mock()
        mock_result.scalars.return_value.first.return_value = sample_allergen
        mock_db.execute.return_value = mock_result
        
        mock_db.delete = Mock()
        mock_db.commit = AsyncMock()
        
        result = await allergen_service.delete_allergen(sample_allergen.id)
        
        assert result is True
        mock_db.delete.assert_called_once_with(sample_allergen)
        mock_db.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_delete_allergen_not_found(self, allergen_service, mock_db):
        """Test deleting non-existent allergen"""
        mock_result = Mock()
        mock_result.scalars.return_value.first.return_value = None
        mock_db.execute.return_value = mock_result
        
        result = await allergen_service.delete_allergen(uuid.uuid4())
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_delete_allergen_database_error(self, allergen_service, mock_db, sample_allergen):
        """Test allergen deletion with database error"""
        mock_result = Mock()
        mock_result.scalars.return_value.first.return_value = sample_allergen
        mock_db.execute.return_value = mock_result
        
        mock_db.delete = Mock()
        mock_db.commit = AsyncMock(side_effect=Exception("Database error"))
        mock_db.rollback = AsyncMock()
        
        with pytest.raises(HTTPException) as exc_info:
            await allergen_service.delete_allergen(sample_allergen.id)
        
        assert exc_info.value.status_code == 500
        mock_db.rollback.assert_called_once()
