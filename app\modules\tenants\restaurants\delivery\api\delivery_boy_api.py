from typing import List, Optional, Annotated, Any  # Adicionar Annotated, Any
import uuid
from fastapi import APIRouter, Depends, HTTPException, status, Query

from sqlalchemy.ext.asyncio import AsyncSession

# Atualizar importações
from app.db.session import get_db as get_async_db  # Usar get_db consistentemente
from app.core.security import (
    get_current_active_user,
    get_current_tenant_from_header,  # Usar esta para obter o tenant
    require_tenant_role,  # Usaremos esta para OWNER e MANAGER
    # Removed unused import: require_tenant_sub_role
)
from app.models.user import User  # noqa: E402
from app.models.tenant import Tenant as TenantModel  # Importar TenantModel
from app.core.roles import TenantRole  # Importar roles necessários

# Removed unused imports: RolePermissions, TenantStaffSubRole
# Removed unused import: from app.models.tenant import TenantType

from app.modules.tenants.restaurants.delivery.schemas.delivery_boy import (  # noqa: E402
    DeliveryBoyCreate,
    DeliveryBoyRead,
    DeliveryBoyUpdate,
)
from app.modules.tenants.restaurants.delivery.schemas.delivery_boy_location import (  # Added  # noqa: E402
    DeliveryBoyLocationCreate,  # Added
    DeliveryBoyLocationRead,  # Added
)  # Added
from app.modules.tenants.restaurants.delivery.services.delivery_boy_service import (  # noqa: E402
    DeliveryBoyService,
)
from app.core.exceptions import NotFoundError, BusinessLogicError  # Corrigido  # noqa: E402


router = APIRouter(prefix="/restaurants/delivery/boys", tags=["Restaurant - Delivery Boys"])


@router.post(
    "/",
    response_model=DeliveryBoyRead,
    status_code=status.HTTP_201_CREATED,
    # dependencies=[Depends(require_tenant_role([TenantRole.MANAGER,
    # TenantRole.STAFF]))] # Substituído abaixo
)
async def create_delivery_boy_endpoint(
    delivery_boy_data: DeliveryBoyCreate,
    db: Annotated[AsyncSession, Depends(get_async_db)],
    current_tenant: Annotated[TenantModel, Depends(get_current_tenant_from_header)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    _: Annotated[
        Any,
        Depends(require_tenant_role(required_roles=[TenantRole.OWNER, TenantRole.MANAGER])),
    ],
):
    """
    Create a new delivery boy for the current tenant.
    Requires MANAGER or STAFF role.
    """
    service = DeliveryBoyService()
    # Ensure the tenant_id in the payload matches the current_tenant.id from context
    if delivery_boy_data.tenant_id != current_tenant.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Tenant ID in payload does not match current tenant context.",
        )
    # O serviço já deve usar o tenant_id do contexto (current_tenant.id) para consistência.
    # No entanto, a validação acima é uma boa prática.
    # A criação de DeliveryBoy também deve associar o user_id ao TenantUserAssociation com sub-papel DELIVERY_PERSON  # noqa: E501
    # Isso é feito no serviço DeliveryBoyService.create_delivery_boy
    try:
        # Passar current_tenant.id explicitamente se o serviço não o obtiver do contexto automaticamente  # noqa: E501
        # ou se delivery_boy_data não for atualizado para usar o current_tenant.id
        # O serviço create_delivery_boy já espera delivery_boy_data.tenant_id, que validamos.
        return await service.create_delivery_boy(db, delivery_boy_data)
    except BusinessLogicError as e:  # Corrigido
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get(
    "/{delivery_boy_id}",
    response_model=DeliveryBoyRead,
    # dependencies=[Depends(require_tenant_role([TenantRole.MANAGER,
    # TenantRole.STAFF]))] # Substituído
)
async def get_delivery_boy_endpoint(
    delivery_boy_id: uuid.UUID,
    db: Annotated[AsyncSession, Depends(get_async_db)],
    current_tenant: Annotated[TenantModel, Depends(get_current_tenant_from_header)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    _: Annotated[
        Any,
        Depends(
            require_tenant_role(
                # Apenas OWNER/MANAGER podem ver detalhes de um entregador
                required_roles=[TenantRole.OWNER, TenantRole.MANAGER]
            )
        ),
    ],
):
    """
    Get a specific delivery boy by ID for the current tenant.
    Requires MANAGER or STAFF role.
    """
    service = DeliveryBoyService()
    db_delivery_boy = await service.get_delivery_boy_by_id(db, delivery_boy_id, current_tenant.id)
    if not db_delivery_boy:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Delivery boy not found")
    return db_delivery_boy


@router.get(
    "/",
    response_model=List[DeliveryBoyRead],
    # dependencies=[Depends(require_tenant_role([TenantRole.MANAGER,
    # TenantRole.STAFF]))] # Substituído
)
async def list_delivery_boys_endpoint(
    # Reordenar para que Depends venham antes de Query
    db: Annotated[AsyncSession, Depends(get_async_db)],
    current_tenant: Annotated[TenantModel, Depends(get_current_tenant_from_header)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    _: Annotated[
        Any,
        Depends(
            require_tenant_role(
                # Apenas OWNER/MANAGER podem listar todos os entregadores
                required_roles=[TenantRole.OWNER, TenantRole.MANAGER]
            )
        ),
    ],
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=200),
):
    """
    List all delivery boys for the current tenant.
    Requires MANAGER or STAFF role.
    """
    service = DeliveryBoyService()
    return await service.get_delivery_boys_by_tenant(db, current_tenant.id, skip=skip, limit=limit)


@router.put(
    "/{delivery_boy_id}",
    response_model=DeliveryBoyRead,
    # dependencies=[Depends(require_tenant_role([TenantRole.MANAGER,
    # TenantRole.STAFF]))] # Substituído
)
async def update_delivery_boy_endpoint(
    delivery_boy_id: uuid.UUID,
    update_data: DeliveryBoyUpdate,
    db: Annotated[AsyncSession, Depends(get_async_db)],
    current_tenant: Annotated[TenantModel, Depends(get_current_tenant_from_header)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    _: Annotated[
        Any,
        Depends(require_tenant_role(required_roles=[TenantRole.OWNER, TenantRole.MANAGER])),
    ],
):
    """
    Update a delivery boy's details for the current tenant.
    Requires MANAGER or STAFF role.
    """
    service = DeliveryBoyService()
    updated_boy = await service.update_delivery_boy(
        db, delivery_boy_id, current_tenant.id, update_data
    )
    if not updated_boy:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Delivery boy not found")
    return updated_boy


@router.patch(
    "/{delivery_boy_id}/set-active/{is_active}",
    response_model=DeliveryBoyRead,
    # dependencies=[Depends(require_tenant_role([TenantRole.MANAGER,
    # TenantRole.STAFF]))] # Substituído
)
async def set_delivery_boy_active_status_endpoint(
    delivery_boy_id: uuid.UUID,
    is_active: bool,
    db: Annotated[AsyncSession, Depends(get_async_db)],
    current_tenant: Annotated[TenantModel, Depends(get_current_tenant_from_header)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    _: Annotated[
        Any,
        Depends(require_tenant_role(required_roles=[TenantRole.OWNER, TenantRole.MANAGER])),
    ],
):
    """
    Activate or deactivate a delivery boy for the current tenant.
    Requires MANAGER or STAFF role.
    """
    service = DeliveryBoyService()
    try:
        boy = await service.set_delivery_boy_active_status(
            db, delivery_boy_id, current_tenant.id, is_active
        )
        return boy
    except NotFoundError as e:  # Corrigido
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))


@router.post(
    "/{delivery_boy_id}/location",
    response_model=DeliveryBoyLocationRead,
    status_code=status.HTTP_201_CREATED,
)
async def update_delivery_boy_location_endpoint(
    delivery_boy_id: uuid.UUID,
    location_data: DeliveryBoyLocationCreate,
    db: Annotated[AsyncSession, Depends(get_async_db)],
    current_tenant: Annotated[TenantModel, Depends(get_current_tenant_from_header)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    # This endpoint should be protected so only the delivery boy themselves can update their location.  # noqa: E501
    # We need to check if current_user.id matches the user_id of the delivery_boy_id.
    # Or, allow OWNER/MANAGER to also update, but the primary use case is self-update.
    # For now, let's restrict to the delivery boy themselves or OWNER/MANAGER.
):
    """
    Update the location of a specific delivery boy.
    Only the delivery boy themselves or an OWNER/MANAGER of the tenant can perform this action.
    """
    service = DeliveryBoyService()

    # Fetch the delivery boy to check ownership/permissions
    target_delivery_boy = await service.get_delivery_boy_by_id(
        db, delivery_boy_id, current_tenant.id
    )
    if not target_delivery_boy:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Delivery boy not found")

    # Permission check:
    # 1. Is the current user the delivery boy?
    is_self_update = target_delivery_boy.user_id == current_user.id

    # 2. Is the current user an OWNER or MANAGER of this tenant?
    # We need to get the current user's association with this tenant.
    # The require_tenant_role dependency doesn't easily give us the roles for this check.
    # We can manually check or adapt the require_tenant_role logic.
    # For simplicity, we'll use a helper or re-fetch association if not readily available.

    # Let's assume get_current_active_user also loads roles or we have a way to check.
    # A simpler check for now: if not self_update, then require OWNER/MANAGER
    if not is_self_update:
        # This is a coarse check. Ideally, require_tenant_role would be more flexible
        # or we'd have a specific "is_owner_or_manager" check.
        # For now, we'll re-use require_tenant_role (it will raise if not owner/manager)
        # but this means an extra DB call if it wasn't already made.
        # A better approach would be to have the roles available from current_user.
        # For this specific task, let's ensure the current_user is at least STAFF.
        # The prompt says "Protegido para que apenas o próprio entregador possa atualizar sua localização."  # noqa: E501
        # We will stick to this for now, and allow OWNER/MANAGER as an administrative override.

        from sqlalchemy import select  # noqa: E402
        from app.models.tenant_user_association import TenantUserAssociation

        user_association = await db.execute(
            select(TenantUserAssociation).where(
                TenantUserAssociation.tenant_id == current_tenant.id,
                TenantUserAssociation.user_id == current_user.id,
            )
        )
        user_assoc = user_association.scalars().first()

        is_owner_or_manager = user_assoc and user_assoc.role in [
            TenantRole.OWNER,
            TenantRole.MANAGER,
        ]

        if not is_owner_or_manager:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to update this delivery boy's location.",
            )

    try:
        updated_location = await service.update_delivery_boy_location(
            db,
            tenant_id=current_tenant.id,
            delivery_boy_id=delivery_boy_id,
            location_data=location_data,
        )
        return updated_location
    except NotFoundError as e:  # Corrigido
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except BusinessLogicError as e:  # Corrigido
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


# It might also be useful to have an endpoint to get the last known location


@router.get(
    "/{delivery_boy_id}/location/last",
    response_model=Optional[DeliveryBoyLocationRead],
    # dependencies=[Depends(require_tenant_role([TenantRole.OWNER,
    # TenantRole.MANAGER]))] # Allow staff too
)
async def get_last_known_delivery_boy_location_endpoint(
    delivery_boy_id: uuid.UUID,
    db: Annotated[AsyncSession, Depends(get_async_db)],
    current_tenant: Annotated[TenantModel, Depends(get_current_tenant_from_header)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    _: Annotated[
        Any,
        Depends(
            require_tenant_role(  # OWNER, MANAGER, or STAFF can see last location
                required_roles=[TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF]
            )
        ),
    ],
):
    """
    Get the last known location of a specific delivery boy.
    Requires OWNER, MANAGER, or STAFF role.
    """
    service = DeliveryBoyService()
    location = await service.get_last_known_location(
        db, tenant_id=current_tenant.id, delivery_boy_id=delivery_boy_id
    )
    if not location:
        # It's not an error if no location is found, just return None (as per
        # Optional response_model)
        return None
    return location
