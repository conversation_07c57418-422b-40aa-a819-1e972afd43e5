"""
Blog Post Service

Business logic for blog post management with SEO and multi-language support.
"""

import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any, TYPE_CHECKING
from sqlalchemy import and_, or_, desc, asc, func, select, delete, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.sql import text

from ..models.blog_post import BlogPost, BlogPostTranslation, PostVisibility
from ..models.blog_tag import BlogTag
from ..schemas.blog_post import (
    BlogPostCreate,
    BlogPostUpdate,
    BlogPostList,
    BlogPostSearchResult,
)
from ..dependencies.access_control import get_accessible_visibility_levels, BlogAccessControl

if TYPE_CHECKING:
    from app.modules.core.users.models.user import User


class BlogPostService:
    """Service for blog post operations."""

    async def create_post(
        self,
        db: AsyncSession,
        post_data: BlogPostCreate,
        current_user_id: uuid.UUID,
    ) -> BlogPost:
        """
        Create a new blog post with translations.

        Args:
            db: Database session
            post_data: Blog post creation data
            current_user_id: ID of the user creating the post

        Returns:
            Created blog post
        """
        # Validate slug
        await self.validate_slug(db, post_data.slug)

        # Create BlogPost instance without tags
        post_dict = post_data.dict(exclude={'tags', 'translations'})
        db_post = BlogPost(**post_dict)

        # Handle tags
        if post_data.tags:
            tags = await db.execute(
                select(BlogTag).filter(BlogTag.id.in_(post_data.tags))
            )
            db_post.tags = tags.scalars().all()

        # Handle translations
        for trans_data in post_data.translations:
            translation = BlogPostTranslation(**trans_data.dict())
            db_post.translations.append(translation)

        db.add(db_post)
        await db.commit()
        await db.refresh(db_post)

        # Eager load relationships for the response
        await self.eager_load_post_for_read(db, db_post)

        return db_post

    async def get_post_by_id(
        self,
        db: AsyncSession,
        post_id: uuid.UUID,
        language_code: Optional[str] = None
    ) -> Optional[BlogPost]:
        """
        Get a blog post by ID with optional language filtering.

        Args:
            db: Database session
            post_id: Post ID
            language_code: Optional language code for translation filtering

        Returns:
            Blog post or None if not found
        """
        query = select(BlogPost).filter(BlogPost.id == post_id)
        query = query.options(
            selectinload(BlogPost.author),
            selectinload(BlogPost.category),
            selectinload(BlogPost.tags),
            selectinload(BlogPost.translations),
            selectinload(BlogPost.comments),
            selectinload(BlogPost.seo),
            selectinload(BlogPost.featured_image)  # Eager load featured image
        )
        result = await db.execute(query)
        post = result.scalars().one_or_none()

        if post and post.translations:
            if language_code:
                translation = next(
                    (t for t in post.translations if t.language_code == language_code),
                    post.translations[0],
                )
            else:
                translation = post.translations[0]

            post.title = translation.title
            post.content = translation.content
            post.excerpt = translation.excerpt

        return post

    async def get_post_by_slug(
        self,
        db: AsyncSession,
        slug: str,
        language_code: Optional[str] = None
    ) -> Optional[BlogPost]:
        """
        Get a blog post by slug with optional language filtering.

        Args:
            db: Database session
            slug: Post slug
            language_code: Optional language code for translation filtering

        Returns:
            Blog post or None if not found
        """
        query = select(BlogPost).filter(BlogPost.slug == slug)
        query = query.options(
            selectinload(BlogPost.author),
            selectinload(BlogPost.category),
            selectinload(BlogPost.tags),
            selectinload(BlogPost.translations),
            selectinload(BlogPost.comments),
            selectinload(BlogPost.seo),
            selectinload(BlogPost.featured_image)  # Eager load featured image
        )
        result = await db.execute(query)
        post = result.scalars().one_or_none()

        if post and post.translations:
            if language_code:
                translation = next(
                    (t for t in post.translations if t.language_code == language_code),
                    post.translations[0],
                )
            else:
                translation = post.translations[0]

            post.title = translation.title
            post.content = translation.content
            post.excerpt = translation.excerpt

        return post

    async def get_posts(
        self,
        db: AsyncSession,
        skip: int = 0,
        limit: int = 20,
        status: Optional[str] = "published",
        category_id: Optional[uuid.UUID] = None,
        tag_id: Optional[uuid.UUID] = None,
        author_id: Optional[uuid.UUID] = None,
        language_code: Optional[str] = None,
        is_featured: Optional[bool] = None,
        order_by: str = "published_at",
        order_direction: str = "desc",
        current_user: Optional["User"] = None
    ) -> List[BlogPost]:
        """
        Get blog posts with filtering and pagination.

        Args:
            db: Database session
            skip: Number of posts to skip
            limit: Maximum number of posts to return
            status: Filter by post status
            category_id: Filter by category
            tag_id: Filter by tag
            author_id: Filter by author
            language_code: Filter by language
            is_featured: Filter by featured status
            order_by: Field to order by
            order_direction: Order direction (asc/desc)

        Returns:
            List of blog posts
        """
        query = select(BlogPost)

        # Apply visibility filters
        access_control = BlogAccessControl(current_user)
        query = access_control.apply_visibility_filter(query)

        if status:
            query = query.filter(BlogPost.status == status)

        if category_id:
            query = query.filter(BlogPost.category_id == category_id)

        if author_id:
            query = query.filter(BlogPost.author_id == author_id)

        if tag_id:
            query = query.join(BlogPost.tags).filter(BlogTag.id == tag_id)

        if is_featured is not None:
            query = query.filter(BlogPost.is_featured == is_featured)

        # Eager load relationships
        query = query.options(
            selectinload(BlogPost.author),
            selectinload(BlogPost.category),
            selectinload(BlogPost.tags),
            selectinload(BlogPost.translations),
            selectinload(BlogPost.featured_image)  # Eager load featured image
        )

        if hasattr(BlogPost, order_by):
            order_column = getattr(BlogPost, order_by)
        else:
            order_column = BlogPost.published_at

        # Apply ordering
        if order_direction.lower() == "desc":
            query = query.order_by(desc(order_column))
        else:
            query = query.order_by(order_column)

        # Apply pagination
        query = query.offset(skip).limit(limit)

        result = await db.execute(query)
        posts = result.scalars().unique().all()

        # Map title and excerpt from translations
        for post in posts:
            try:
                if post.translations:
                    if language_code:
                        translation = next(
                            (t for t in post.translations if t.language_code == language_code),
                            post.translations[0],  # Fallback to first
                        )
                    else:
                        translation = post.translations[0]
                    post.title = translation.title
                    post.excerpt = translation.excerpt
            except IndexError:
                post.title = "Title not available"
                post.excerpt = "Excerpt not available"

        return posts

    async def update_post(
        self,
        db: AsyncSession,
        post_id: uuid.UUID,
        post_data: BlogPostUpdate,
    ) -> Optional[BlogPost]:
        """
        Update a blog post with translations.

        Args:
            db: Database session
            post_id: ID of the post to update
            post_data: Updated blog post data

        Returns:
            Updated blog post or None if not found
        """
        # Fetch the existing post
        db_post = await self.get_post_by_id(db, post_id)
        if not db_post:
            return None

        # Update post fields from post_data
        post_update_data = post_data.dict(exclude_unset=True)
        for key, value in post_update_data.items():
            if hasattr(db_post, key):
                setattr(db_post, key, value)

        # Handle tags update
        if 'tags' in post_update_data and post_update_data['tags'] is not None:
            tags = await db.execute(
                select(BlogTag).filter(BlogTag.id.in_(post_update_data['tags']))
            )
            db_post.tags = tags.scalars().all()

        # Handle translations update
        if 'translations' in post_update_data:
            # Create a dictionary of existing translations by language code
            existing_translations = {
                trans.language_code: trans for trans in db_post.translations
            }
            for trans_data in post_update_data['translations']:
                if trans_data.language_code in existing_translations:
                    # Update existing translation
                    existing_trans = existing_translations[trans_data.language_code]
                    for key, value in trans_data.dict(exclude_unset=True).items():
                        setattr(existing_trans, key, value)
                else:
                    # Add new translation
                    new_trans = BlogPostTranslation(
                        post_id=db_post.id, **trans_data.dict()
                    )
                    db.add(new_trans)
                    db_post.translations.append(new_trans)

        # Set updated_at timestamp
        db_post.updated_at = datetime.utcnow()

        await db.commit()
        await db.refresh(db_post)

        # Eager load relationships for the response
        await self.eager_load_post_for_read(db, db_post)

        return db_post

    async def delete_post(
        self,
        db: AsyncSession,
        post_id: uuid.UUID
    ) -> bool:
        """
        Delete a blog post.

        Args:
            db: Database session
            post_id: Post ID to delete

        Returns:
            True if deleted, False if not found
        """
        query = select(BlogPost).where(BlogPost.id == post_id)
        result = await db.execute(query)
        db_post = result.scalar_one_or_none()

        if not db_post:
            return False

        await db.delete(db_post)
        await db.commit()

        return True

    async def increment_view_count(
        self,
        db: AsyncSession,
        post_id: uuid.UUID
    ) -> bool:
        """
        Increment the view count for a blog post.

        Args:
            db: Database session
            post_id: Post ID

        Returns:
            True if successful, False if post not found
        """
        query = select(BlogPost).where(BlogPost.id == post_id)
        result = await db.execute(query)
        db_post = result.scalar_one_or_none()

        if not db_post:
            return False

        db_post.view_count += 1
        await db.commit()

        return True

    async def get_related_posts(
        self,
        db: AsyncSession,
        post_id: uuid.UUID,
        limit: int = 5
    ) -> List[BlogPost]:
        """
        Get related posts based on tags and category.

        Args:
            db: Database session
            post_id: Current post ID
            limit: Maximum number of related posts

        Returns:
            List of related blog posts
        """
        post = await self.get_post_by_id(db, post_id)
        if not post or not post.tags:
            return []

        # Base query
        query = select(BlogPost).filter(
            BlogPost.id != post_id,
            BlogPost.status == "published"
        ).options(
            selectinload(BlogPost.translations),
            selectinload(BlogPost.featured_image)
        )

        # Logic to find related posts (e.g., by tags)
        tag_ids = [tag.id for tag in post.tags]
        query = query.join(BlogPost.tags).filter(BlogTag.id.in_(tag_ids))
        
        query = query.group_by(BlogPost.id).order_by(desc(func.count(BlogTag.id))).limit(limit)

        result = await db.execute(query)
        related_posts = result.scalars().unique().all()
        return related_posts

    async def eager_load_post_for_read(self, db: AsyncSession, post: BlogPost) -> None:
        """
        Eagerly load all relationships required for the BlogPostRead schema.
        """
        await db.refresh(post, attribute_names=[
            "author", "category", "tags", "translations", "comments", "seo", "featured_image"
        ])

    async def validate_slug(
        self, db: AsyncSession, slug: str, post_id: Optional[uuid.UUID] = None
    ):
        """
        Validate that the slug is unique.

        Args:
            db: Database session
            slug: Post slug
            post_id: Optional post ID to exclude from the check

        Raises:
            ValueError: If the slug already exists
        """
        query = select(BlogPost).filter(BlogPost.slug == slug)
        if post_id:
            query = query.filter(BlogPost.id != post_id)
        existing_post = await db.execute(query)
        existing_post = existing_post.scalar_one_or_none()

        if existing_post:
            raise ValueError(f"Slug '{slug}' already exists.")

    async def search_posts(
        self,
        db: AsyncSession,
        search_query: str,
        limit: int = 10
    ):
        """
        Search for blog posts based on a query string.
        """
        # A simple search implementation
        query = select(BlogPost).join(BlogPost.translations).filter(
            BlogPost.status == "published",
            BlogPostTranslation.title.ilike(f"%{search_query}%")
        ).options(selectinload(BlogPost.translations), selectinload(BlogPost.featured_image))

        result = await db.execute(query.limit(limit))
        return result.scalars().unique().all()
