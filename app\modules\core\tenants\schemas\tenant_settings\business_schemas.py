"""
Business specific schemas for tenant settings.
"""

from typing import Optional

from pydantic import BaseModel, Field


class BusinessSettingsUpdate(BaseModel):
    """Schema for updating common business settings."""

    business_name: Optional[str] = Field(
        None,
        max_length=200,
        description="Business name"
    )
    business_type: Optional[str] = Field(
        None,
        max_length=100,
        description="Type of business"
    )
