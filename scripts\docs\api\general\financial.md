# General - Financial

**Categoria:** General
**Módulo:** Financial
**Total de Endpoints:** 32
**Gerado em:** 27/06/2025, 20:20:26

## 📋 Endpoints

- [GET /api/financial/categories/](#get-apifinancialcategories) - List financial categories
- [POST /api/financial/categories/](#post-apifinancialcategories) - Create financial category
- [POST /api/financial/categories/defaults](#post-apifinancialcategoriesdefaults) - Create default categories
- [GET /api/financial/categories/tree](#get-apifinancialcategoriestree) - Get category tree
- [DELETE /api/financial/categories/{category_id}](#delete-apifinancialcategoriescategory-id) - Delete financial category
- [GET /api/financial/categories/{category_id}](#get-apifinancialcategoriescategory-id) - Get financial category
- [PUT /api/financial/categories/{category_id}](#put-apifinancialcategoriescategory-id) - Update financial category
- [GET /api/financial/invoices/](#get-apifinancialinvoices) - Get Invoices
- [POST /api/financial/invoices/](#post-apifinancialinvoices) - Create Invoice
- [GET /api/financial/invoices/b2b-invoices/](#get-apifinancialinvoicesb2b-invoices) - List B2B Invoices
- [POST /api/financial/invoices/b2b-invoices/](#post-apifinancialinvoicesb2b-invoices) - Create B2B Invoice
- [GET /api/financial/invoices/b2b-invoices/stats/summary](#get-apifinancialinvoicesb2b-invoicesstatssummary) - Get B2B Invoice Stats
- [GET /api/financial/invoices/b2b-invoices/{invoice_id}](#get-apifinancialinvoicesb2b-invoicesinvoice-id) - Get B2B Invoice
- [POST /api/financial/invoices/b2b-invoices/{invoice_id}/download-token](#post-apifinancialinvoicesb2b-invoicesinvoice-iddownload-token) - Generate B2B Download Token
- [POST /api/financial/invoices/b2b-invoices/{invoice_id}/send](#post-apifinancialinvoicesb2b-invoicesinvoice-idsend) - Send B2B Invoice
- [POST /api/financial/invoices/b2b-invoices/{invoice_id}/upload](#post-apifinancialinvoicesb2b-invoicesinvoice-idupload) - Upload B2B Invoice File
- [DELETE /api/financial/invoices/{invoice_id}](#delete-apifinancialinvoicesinvoice-id) - Delete Invoice
- [GET /api/financial/invoices/{invoice_id}](#get-apifinancialinvoicesinvoice-id) - Get Invoice
- [PUT /api/financial/invoices/{invoice_id}](#put-apifinancialinvoicesinvoice-id) - Update Invoice
- [POST /api/financial/invoices/{invoice_id}/generate-pdf](#post-apifinancialinvoicesinvoice-idgenerate-pdf) - Generate Invoice Pdf
- [GET /api/financial/orders/](#get-apifinancialorders) - Read Orders
- [POST /api/financial/orders/](#post-apifinancialorders) - Create Order
- [GET /api/financial/orders/stats](#get-apifinancialordersstats) - Get Order Stats
- [GET /api/financial/orders/{order_id}](#get-apifinancialordersorder-id) - Read Order
- [PATCH /api/financial/orders/{order_id}/status](#patch-apifinancialordersorder-idstatus) - Update Order Status
- [GET /api/financial/transactions/](#get-apifinancialtransactions) - Get All Transactions
- [POST /api/financial/transactions/](#post-apifinancialtransactions) - Create Transaction
- [GET /api/financial/transactions/supplier-invoices](#get-apifinancialtransactionssupplier-invoices) - Listar Faturas de Fornecedores
- [DELETE /api/financial/transactions/{transaction_id}](#delete-apifinancialtransactionstransaction-id) - Delete Transaction
- [GET /api/financial/transactions/{transaction_id}](#get-apifinancialtransactionstransaction-id) - Get Transaction
- [PUT /api/financial/transactions/{transaction_id}](#put-apifinancialtransactionstransaction-id) - Update Transaction
- [POST /api/financial/transactions/{transaction_id}/payment](#post-apifinancialtransactionstransaction-idpayment) - Registrar Pagamento de Fatura

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### B2BInvoiceCreate

**Descrição:** Schema for creating B2B invoices.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `invoice_type` | InvoiceType | ❌ | Must be B2B type |
| `customer_name` | string | ✅ | Customer name |
| `customer_email` | unknown | ❌ | Customer email |
| `customer_phone` | unknown | ❌ | Customer phone |
| `customer_address` | unknown | ❌ | Customer address |
| `customer_tax_id` | unknown | ❌ | Customer tax ID |
| `issue_date` | string | ✅ | Invoice issue date |
| `due_date` | unknown | ❌ | Payment due date |
| `tax_rate` | unknown | ❌ | Default tax rate percentage |
| `discount_amount` | unknown | ❌ | Discount amount |
| `payment_method` | unknown | ❌ | Payment method |
| `notes` | unknown | ❌ | Invoice notes |
| `terms_and_conditions` | unknown | ❌ | Terms and conditions |
| `order_id` | unknown | ❌ | Related order ID |
| `vendor_id` | string | ✅ | B2B vendor ID |
| `customer_b2b_id` | string | ✅ | B2B customer ID |
| `notify_on_view` | boolean | ❌ | Notify when invoice is viewed |
| `notify_on_due` | boolean | ❌ | Notify when invoice is due |
| `items` | Array[InvoiceItemCreate] | ❌ | Invoice items |

### B2BInvoiceDownloadRequest

**Descrição:** Schema for requesting B2B invoice download.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `expires_hours` | integer | ❌ | Token expiration in hours |

### B2BInvoiceListResponse

**Descrição:** Schema for B2B invoice list response.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `invoices` | Array[object] | ❌ | Invoice summaries |
| `total` | integer | ❌ | Total invoices |
| `page` | integer | ❌ | Current page |
| `limit` | integer | ❌ | Items per page |
| `total_pages` | integer | ❌ | Total pages |
| `stats` | unknown | ❌ | Summary statistics |

### B2BInvoiceResponse

**Descrição:** Schema for B2B invoice API responses.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `success` | boolean | ✅ | Operation success |
| `message` | string | ✅ | Response message |
| `invoice` | unknown | ❌ | Invoice data |
| `access_token` | unknown | ❌ | Download access token |
| `stats` | unknown | ❌ | Statistics data |

### B2BInvoiceStats

**Descrição:** Schema for B2B invoice statistics.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `total_invoices` | integer | ❌ | Total number of invoices |
| `total_amount` | string | ❌ | Total amount |
| `paid_amount` | string | ❌ | Total paid amount |
| `outstanding_amount` | string | ❌ | Total outstanding amount |
| `draft_count` | integer | ❌ | Draft invoices |
| `pending_count` | integer | ❌ | Pending invoices |
| `sent_count` | integer | ❌ | Sent invoices |
| `viewed_count` | integer | ❌ | Viewed invoices |
| `paid_count` | integer | ❌ | Paid invoices |
| `overdue_count` | integer | ❌ | Overdue invoices |
| `cancelled_count` | integer | ❌ | Cancelled invoices |
| `disputed_count` | integer | ❌ | Disputed invoices |
| `this_month_amount` | string | ❌ | This month amount |
| `last_month_amount` | string | ❌ | Last month amount |
| `avg_payment_days` | number | ❌ | Average payment days |

### Body_upload_b2b_invoice_file_api_financial_invoices_b2b_invoices__invoice_id__upload_post

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `file` | string | ✅ | - |

### FinancialCategoryCreate

**Descrição:** Schema for creating a financial category.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Category name |
| `description` | unknown | ❌ | Category description |
| `category_type` | CategoryType | ✅ | Category type (income or expense) |
| `parent_id` | unknown | ❌ | Parent category ID for hierarchical structure |
| `display_order` | integer | ❌ | Display order for sorting |
| `is_active` | boolean | ❌ | Whether the category is active |
| `color` | unknown | ❌ | Hex color code for the category |
| `icon` | unknown | ❌ | Icon class or URL for the category |

### FinancialCategoryListResponse

**Descrição:** Schema for paginated category list response.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `categories` | Array[FinancialCategoryRead] | ❌ | List of categories |
| `total` | integer | ❌ | Total number of categories |
| `page` | integer | ❌ | Current page number |
| `per_page` | integer | ❌ | Items per page |
| `pages` | integer | ❌ | Total number of pages |

### FinancialCategoryRead

**Descrição:** Schema for reading a financial category.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Category name |
| `description` | unknown | ❌ | Category description |
| `category_type` | CategoryType | ✅ | Category type (income or expense) |
| `parent_id` | unknown | ❌ | Parent category ID for hierarchical structure |
| `display_order` | integer | ❌ | Display order for sorting |
| `is_active` | boolean | ❌ | Whether the category is active |
| `color` | unknown | ❌ | Hex color code for the category |
| `icon` | unknown | ❌ | Icon class or URL for the category |
| `id` | string | ✅ | Category ID |
| `tenant_id` | string | ✅ | Tenant ID |
| `is_default` | boolean | ❌ | Whether this is a default category |
| `parent_name` | unknown | ❌ | Parent category name |
| `children_count` | integer | ❌ | Number of child categories |
| `transactions_count` | integer | ❌ | Number of transactions in this category |
| `total_income` | unknown | ❌ | Total income in this category |
| `total_expense` | unknown | ❌ | Total expense in this category |

### FinancialCategoryUpdate

**Descrição:** Schema for updating a financial category.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | Category name |
| `description` | unknown | ❌ | Category description |
| `category_type` | unknown | ❌ | Category type (income or expense) |
| `parent_id` | unknown | ❌ | Parent category ID for hierarchical structure |
| `display_order` | unknown | ❌ | Display order for sorting |
| `is_active` | unknown | ❌ | Whether the category is active |
| `color` | unknown | ❌ | Hex color code for the category |
| `icon` | unknown | ❌ | Icon class or URL for the category |

### FinancialTransactionCreate

**Descrição:** Schema for creating a financial transaction.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `transaction_type` | TransactionType | ✅ | Type of transaction (income or expense) |
| `amount` | unknown | ✅ | Transaction amount |
| `description` | string | ✅ | Transaction description |
| `transaction_date` | string | ✅ | Date when the transaction occurred |
| `payment_method_id` | unknown | ❌ | ID of the payment method used |
| `category_id` | unknown | ❌ | ID of the financial category |
| `reference_number` | unknown | ❌ | Reference number (invoice, receipt, etc.) |
| `notes` | unknown | ❌ | Additional notes |
| `media_upload_ids` | unknown | ❌ | - |

### FinancialTransactionInDB

**Descrição:** Schema for representing a transaction in a response.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `transaction_type` | TransactionType | ✅ | Type of transaction (income or expense) |
| `amount` | string | ✅ | Transaction amount |
| `description` | string | ✅ | Transaction description |
| `transaction_date` | string | ✅ | Date when the transaction occurred |
| `payment_method_id` | unknown | ❌ | ID of the payment method used |
| `category_id` | unknown | ❌ | ID of the financial category |
| `reference_number` | unknown | ❌ | Reference number (invoice, receipt, etc.) |
| `notes` | unknown | ❌ | Additional notes |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_by` | string | ✅ | - |
| `media_uploads` | Array[MediaUploadInfo] | ❌ | - |

### FinancialTransactionUpdate

**Descrição:** Schema for updating a financial transaction.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `transaction_type` | unknown | ❌ | Type of transaction (income or expense) |
| `amount` | unknown | ❌ | Transaction amount |
| `description` | unknown | ❌ | Transaction description |
| `transaction_date` | unknown | ❌ | Date when the transaction occurred |
| `payment_method_id` | unknown | ❌ | ID of the payment method used |
| `category_id` | unknown | ❌ | ID of the financial category |
| `reference_number` | unknown | ❌ | Reference number (invoice, receipt, etc.) |
| `notes` | unknown | ❌ | Additional notes |
| `media_upload_ids` | unknown | ❌ | - |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### InvoiceCreate

**Descrição:** Schema for creating invoices.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `invoice_type` | InvoiceType | ✅ | Type of invoice |
| `customer_name` | string | ✅ | Customer name |
| `customer_email` | unknown | ❌ | Customer email |
| `customer_phone` | unknown | ❌ | Customer phone |
| `customer_address` | unknown | ❌ | Customer address |
| `customer_tax_id` | unknown | ❌ | Customer tax ID |
| `issue_date` | string | ✅ | Invoice issue date |
| `due_date` | unknown | ❌ | Payment due date |
| `tax_rate` | unknown | ❌ | Default tax rate percentage |
| `discount_amount` | unknown | ❌ | Discount amount |
| `payment_method` | unknown | ❌ | Payment method |
| `notes` | unknown | ❌ | Invoice notes |
| `terms_and_conditions` | unknown | ❌ | Terms and conditions |
| `order_id` | unknown | ❌ | Related order ID |
| `vendor_id` | unknown | ❌ | B2B vendor ID |
| `customer_b2b_id` | unknown | ❌ | B2B customer ID |
| `notify_on_view` | boolean | ❌ | Notify when invoice is viewed |
| `notify_on_due` | boolean | ❌ | Notify when invoice is due |
| `items` | Array[InvoiceItemCreate] | ❌ | Invoice items |

### InvoicePaymentCreate

**Descrição:** Schema for registering payment of a supplier invoice.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `payment_method_id` | string | ✅ | Payment method used |
| `payment_date` | string | ✅ | Date of payment |
| `payment_amount` | unknown | ✅ | Amount paid |
| `payment_reference` | unknown | ❌ | Payment reference |
| `payment_receipt_file` | unknown | ❌ | Receipt file path |
| `notes` | unknown | ❌ | Additional payment notes |

### InvoicePaymentRead

**Descrição:** Schema for reading invoice payment information.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `amount` | string | ✅ | - |
| `description` | string | ✅ | - |
| `transaction_date` | string | ✅ | - |
| `reference_number` | string | ✅ | - |
| `payment_method_id` | unknown | ❌ | - |
| `notes` | unknown | ❌ | - |
| `is_paid` | boolean | ❌ | Whether invoice is paid |

### InvoiceRead

**Descrição:** Schema for reading invoices.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `invoice_type` | InvoiceType | ✅ | Type of invoice |
| `customer_name` | string | ✅ | Customer name |
| `customer_email` | unknown | ❌ | Customer email |
| `customer_phone` | unknown | ❌ | Customer phone |
| `customer_address` | unknown | ❌ | Customer address |
| `customer_tax_id` | unknown | ❌ | Customer tax ID |
| `issue_date` | string | ✅ | Invoice issue date |
| `due_date` | unknown | ❌ | Payment due date |
| `tax_rate` | string | ❌ | Default tax rate percentage |
| `discount_amount` | string | ❌ | Discount amount |
| `payment_method` | unknown | ❌ | Payment method |
| `notes` | unknown | ❌ | Invoice notes |
| `terms_and_conditions` | unknown | ❌ | Terms and conditions |
| `order_id` | unknown | ❌ | Related order ID |
| `vendor_id` | unknown | ❌ | B2B vendor ID |
| `customer_b2b_id` | unknown | ❌ | B2B customer ID |
| `notify_on_view` | boolean | ❌ | Notify when invoice is viewed |
| `notify_on_due` | boolean | ❌ | Notify when invoice is due |
| `id` | string | ✅ | Invoice ID |
| `tenant_id` | string | ✅ | Tenant ID |
| `invoice_number` | string | ✅ | Invoice number |
| `status` | InvoiceStatus | ✅ | Invoice status |
| `created_by` | string | ✅ | Created by user ID |
| `subtotal` | string | ✅ | Subtotal amount |
| `tax_amount` | string | ✅ | Tax amount |
| `total_amount` | string | ✅ | Total amount |
| `paid_date` | unknown | ❌ | Payment date |
| `payment_reference` | unknown | ❌ | Payment reference |
| `created_by_name` | unknown | ❌ | Created by user name |
| `order_number` | unknown | ❌ | Related order number |
| `pdf_generated_at` | unknown | ❌ | PDF generation timestamp |
| `pdf_available` | boolean | ❌ | Whether PDF is available |
| `items` | Array[InvoiceItemRead] | ❌ | Invoice items |

### InvoiceUpdate

**Descrição:** Schema for updating invoices.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `invoice_type` | unknown | ❌ | Type of invoice |
| `status` | unknown | ❌ | Invoice status |
| `customer_name` | unknown | ❌ | Customer name |
| `customer_email` | unknown | ❌ | Customer email |
| `customer_phone` | unknown | ❌ | Customer phone |
| `customer_address` | unknown | ❌ | Customer address |
| `customer_tax_id` | unknown | ❌ | Customer tax ID |
| `issue_date` | unknown | ❌ | Invoice issue date |
| `due_date` | unknown | ❌ | Payment due date |
| `paid_date` | unknown | ❌ | Payment date |
| `tax_rate` | unknown | ❌ | Default tax rate percentage |
| `discount_amount` | unknown | ❌ | Discount amount |
| `payment_method` | unknown | ❌ | Payment method |
| `payment_reference` | unknown | ❌ | Payment reference |
| `notes` | unknown | ❌ | Invoice notes |
| `terms_and_conditions` | unknown | ❌ | Terms and conditions |

### OrderCreate

**Descrição:** Schema for creating a new order.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `order_type` | string | ✅ | Type of order (dine-in, takeout, delivery, etc.) |
| `table_number` | unknown | ❌ | Table number for dine-in orders |
| `subtotal` | unknown | ✅ | Subtotal amount before tax and discounts |
| `tax` | unknown | ✅ | Tax amount |
| `discount` | unknown | ❌ | Discount amount |
| `total` | unknown | ✅ | Total amount after tax and discounts |
| `notes` | unknown | ❌ | Special instructions for the order |
| `metadata` | unknown | ❌ | Additional metadata |
| `customer_id` | unknown | ❌ | ID of the customer |
| `items` | Array[OrderItemCreate] | ✅ | Items in the order |

### OrderListResponse

**Descrição:** Schema for paginated order list response.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `orders` | Array[OrderRead] | ✅ | List of orders |
| `total` | integer | ✅ | Total number of orders |
| `page` | integer | ✅ | Current page number |
| `per_page` | integer | ✅ | Number of orders per page |
| `total_pages` | integer | ✅ | Total number of pages |

### OrderRead

**Descrição:** Schema for reading an order.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `order_type` | string | ✅ | Type of order (dine-in, takeout, delivery, etc.) |
| `table_number` | unknown | ❌ | Table number for dine-in orders |
| `subtotal` | string | ✅ | Subtotal amount before tax and discounts |
| `tax` | string | ✅ | Tax amount |
| `discount` | string | ❌ | Discount amount |
| `total` | string | ✅ | Total amount after tax and discounts |
| `notes` | unknown | ❌ | Special instructions for the order |
| `metadata` | unknown | ❌ | Additional metadata |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `customer_id` | unknown | ✅ | - |
| `user_id` | unknown | ✅ | - |
| `order_number` | string | ✅ | - |
| `status` | OrderStatus | ✅ | - |
| `items` | Array[OrderItemRead] | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `customer` | unknown | ❌ | - |
| `user` | unknown | ❌ | - |
| `kitchen_order` | unknown | ❌ | - |

### OrderStats

**Descrição:** Schema for order statistics.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `total_orders` | integer | ✅ | Total number of orders |
| `pending_orders` | integer | ✅ | Number of pending orders |
| `confirmed_orders` | integer | ✅ | Number of confirmed orders |
| `delivered_orders` | integer | ✅ | Number of delivered orders |
| `cancelled_orders` | integer | ✅ | Number of cancelled orders |
| `total_revenue` | string | ✅ | Total revenue from all orders |
| `average_order_value` | string | ✅ | Average order value |

### OrderUpdate

**Descrição:** Schema for updating an existing order.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `status` | unknown | ❌ | New status of the order |
| `notes` | unknown | ❌ | Updated special instructions |
| `metadata` | unknown | ❌ | Updated metadata |

## 🔗 Endpoints Detalhados

### GET /api/financial/categories/ {#get-apifinancialcategories}

**Resumo:** List financial categories
**Descrição:** Get paginated list of financial categories with optional filters.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `page` | integer | query | ❌ | Page number |
| `per_page` | integer | query | ❌ | Items per page |
| `order_by` | string | query | ❌ | Order by field |
| `order_direction` | string | query | ❌ | Order direction |
| `category_type` | string | query | ❌ | Filter by category type |
| `parent_id` | string | query | ❌ | Filter by parent category ID |
| `is_active` | string | query | ❌ | Filter by active status |
| `search` | string | query | ❌ | Search in name and description |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [FinancialCategoryListResponse](#financialcategorylistresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/categories/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/financial/categories/ {#post-apifinancialcategories}

**Resumo:** Create financial category
**Descrição:** Create a new financial category for the current tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [FinancialCategoryCreate](#financialcategorycreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [FinancialCategoryRead](#financialcategoryread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/financial/categories/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/financial/categories/defaults {#post-apifinancialcategoriesdefaults}

**Resumo:** Create default categories
**Descrição:** Create default financial categories for the current tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**201:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/financial/categories/defaults" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/financial/categories/tree {#get-apifinancialcategoriestree}

**Resumo:** Get category tree
**Descrição:** Get hierarchical tree of financial categories.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_type` | string | query | ❌ | Filter by category type |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/categories/tree" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### DELETE /api/financial/categories/{category_id} {#delete-apifinancialcategoriescategory-id}

**Resumo:** Delete financial category
**Descrição:** Delete a specific financial category.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/financial/categories/{category_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/financial/categories/{category_id} {#get-apifinancialcategoriescategory-id}

**Resumo:** Get financial category
**Descrição:** Get a specific financial category by ID.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [FinancialCategoryRead](#financialcategoryread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/categories/{category_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/financial/categories/{category_id} {#put-apifinancialcategoriescategory-id}

**Resumo:** Update financial category
**Descrição:** Update a specific financial category.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [FinancialCategoryUpdate](#financialcategoryupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [FinancialCategoryRead](#financialcategoryread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/financial/categories/{category_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/financial/invoices/ {#get-apifinancialinvoices}

**Resumo:** Get Invoices

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `status` | string | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/invoices/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/financial/invoices/ {#post-apifinancialinvoices}

**Resumo:** Create Invoice

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [InvoiceCreate](#invoicecreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [InvoiceRead](#invoiceread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/financial/invoices/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/financial/invoices/b2b-invoices/ {#get-apifinancialinvoicesb2b-invoices}

**Resumo:** List B2B Invoices
**Descrição:** Lista faturas B2B com filtros.

Usuários veem apenas suas faturas (como vendor ou customer).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `vendor_id` | string | query | ❌ | Filter by vendor ID |
| `customer_id` | string | query | ❌ | Filter by customer ID |
| `status` | string | query | ❌ | Filter by status |
| `overdue_only` | boolean | query | ❌ | Show only overdue invoices |
| `page` | integer | query | ❌ | Page number |
| `limit` | integer | query | ❌ | Items per page |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [B2BInvoiceListResponse](#b2binvoicelistresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/invoices/b2b-invoices/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/financial/invoices/b2b-invoices/ {#post-apifinancialinvoicesb2b-invoices}

**Resumo:** Create B2B Invoice
**Descrição:** Cria nova fatura B2B.

Apenas TVendorSupplier pode criar faturas B2B.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [B2BInvoiceCreate](#b2binvoicecreate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [B2BInvoiceResponse](#b2binvoiceresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/financial/invoices/b2b-invoices/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/financial/invoices/b2b-invoices/stats/summary {#get-apifinancialinvoicesb2b-invoicesstatssummary}

**Resumo:** Get B2B Invoice Stats
**Descrição:** Estatísticas de faturas B2B.

Filtros aplicados baseados no role do usuário.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `vendor_id` | string | query | ❌ | Filter by vendor ID |
| `customer_id` | string | query | ❌ | Filter by customer ID |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [B2BInvoiceStats](#b2binvoicestats)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/invoices/b2b-invoices/stats/summary" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/financial/invoices/b2b-invoices/{invoice_id} {#get-apifinancialinvoicesb2b-invoicesinvoice-id}

**Resumo:** Get B2B Invoice
**Descrição:** Busca fatura B2B por ID.

Apenas vendor ou customer podem acessar.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `invoice_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [B2BInvoiceResponse](#b2binvoiceresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/invoices/b2b-invoices/{invoice_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/financial/invoices/b2b-invoices/{invoice_id}/download-token {#post-apifinancialinvoicesb2b-invoicesinvoice-iddownload-token}

**Resumo:** Generate B2B Download Token
**Descrição:** Gera token para download da fatura B2B.

Vendor ou customer podem gerar token.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `invoice_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [B2BInvoiceDownloadRequest](#b2binvoicedownloadrequest)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [B2BInvoiceResponse](#b2binvoiceresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/financial/invoices/b2b-invoices/{invoice_id}/download-token" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/financial/invoices/b2b-invoices/{invoice_id}/send {#post-apifinancialinvoicesb2b-invoicesinvoice-idsend}

**Resumo:** Send B2B Invoice
**Descrição:** Envia fatura B2B para cliente.

Apenas TVendorSupplier proprietário pode enviar.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `invoice_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [B2BInvoiceResponse](#b2binvoiceresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/financial/invoices/b2b-invoices/{invoice_id}/send" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/financial/invoices/b2b-invoices/{invoice_id}/upload {#post-apifinancialinvoicesb2b-invoicesinvoice-idupload}

**Resumo:** Upload B2B Invoice File
**Descrição:** Faz upload do arquivo da fatura B2B.

Apenas o TVendorSupplier proprietário pode fazer upload.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `invoice_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `multipart/form-data`
**Schema:** [Body_upload_b2b_invoice_file_api_financial_invoices_b2b_invoices__invoice_id__upload_post](#body_upload_b2b_invoice_file_api_financial_invoices_b2b_invoices__invoice_id__upload_post)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [B2BInvoiceResponse](#b2binvoiceresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/financial/invoices/b2b-invoices/{invoice_id}/upload" \
  -H "Content-Type: multipart/form-data" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/financial/invoices/{invoice_id} {#delete-apifinancialinvoicesinvoice-id}

**Resumo:** Delete Invoice

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `invoice_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/financial/invoices/{invoice_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/financial/invoices/{invoice_id} {#get-apifinancialinvoicesinvoice-id}

**Resumo:** Get Invoice

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `invoice_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [InvoiceRead](#invoiceread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/invoices/{invoice_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/financial/invoices/{invoice_id} {#put-apifinancialinvoicesinvoice-id}

**Resumo:** Update Invoice

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `invoice_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [InvoiceUpdate](#invoiceupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [InvoiceRead](#invoiceread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/financial/invoices/{invoice_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/financial/invoices/{invoice_id}/generate-pdf {#post-apifinancialinvoicesinvoice-idgenerate-pdf}

**Resumo:** Generate Invoice Pdf

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `invoice_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/financial/invoices/{invoice_id}/generate-pdf" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/financial/orders/ {#get-apifinancialorders}

**Resumo:** Read Orders
**Descrição:** Retrieve orders for the current tenant, with optional filters.
Requires at least costumer role in the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `status` | string | query | ❌ | Filter by order status |
| `order_type` | string | query | ❌ | Filter by order type (dine-in, takeout, delivery, etc.) |
| `customer_id` | string | query | ❌ | Filter by customer ID |
| `customer_name` | string | query | ❌ | Filter by customer name |
| `customer_email` | string | query | ❌ | Filter by customer email |
| `order_number` | string | query | ❌ | Filter by order number |
| `date_from` | string | query | ❌ | Filter orders from date (YYYY-MM-DD) |
| `date_to` | string | query | ❌ | Filter orders to date (YYYY-MM-DD) |
| `min_amount` | string | query | ❌ | Filter by minimum order amount |
| `max_amount` | string | query | ❌ | Filter by maximum order amount |
| `page` | integer | query | ❌ | Page number |
| `per_page` | integer | query | ❌ | Number of records per page |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [OrderListResponse](#orderlistresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/orders/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/financial/orders/ {#post-apifinancialorders}

**Resumo:** Create Order
**Descrição:** Create a new order for the current tenant.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [OrderCreate](#ordercreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [OrderRead](#orderread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/financial/orders/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/financial/orders/stats {#get-apifinancialordersstats}

**Resumo:** Get Order Stats
**Descrição:** Get order statistics for the current tenant.
Requires at least customer role in the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [OrderStats](#orderstats)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/orders/stats" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/financial/orders/{order_id} {#get-apifinancialordersorder-id}

**Resumo:** Read Order
**Descrição:** Retrieve a specific order by ID.
Requires at least costumer role in the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `order_id` | string | path | ✅ | ID of the order to retrieve |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [OrderRead](#orderread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/orders/{order_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PATCH /api/financial/orders/{order_id}/status {#patch-apifinancialordersorder-idstatus}

**Resumo:** Update Order Status
**Descrição:** Update the status of an order.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `order_id` | string | path | ✅ | ID of the order to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [OrderUpdate](#orderupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [OrderRead](#orderread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PATCH "http://localhost:8000/api/financial/orders/{order_id}/status" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/financial/transactions/ {#get-apifinancialtransactions}

**Resumo:** Get All Transactions
**Descrição:** Retrieve all transactions for the tenant.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/transactions/"
```

---

### POST /api/financial/transactions/ {#post-apifinancialtransactions}

**Resumo:** Create Transaction
**Descrição:** Create a new financial transaction.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [FinancialTransactionCreate](#financialtransactioncreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [FinancialTransactionInDB](#financialtransactionindb)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/financial/transactions/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/financial/transactions/supplier-invoices {#get-apifinancialtransactionssupplier-invoices}

**Resumo:** Listar Faturas de Fornecedores
**Descrição:** Lista faturas de fornecedores pendentes de pagamento.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/transactions/supplier-invoices" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### DELETE /api/financial/transactions/{transaction_id} {#delete-apifinancialtransactionstransaction-id}

**Resumo:** Delete Transaction
**Descrição:** Delete a financial transaction.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `transaction_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/financial/transactions/{transaction_id}"
```

---

### GET /api/financial/transactions/{transaction_id} {#get-apifinancialtransactionstransaction-id}

**Resumo:** Get Transaction
**Descrição:** Retrieve a specific transaction by its ID.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `transaction_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [FinancialTransactionInDB](#financialtransactionindb)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/transactions/{transaction_id}"
```

---

### PUT /api/financial/transactions/{transaction_id} {#put-apifinancialtransactionstransaction-id}

**Resumo:** Update Transaction
**Descrição:** Update a financial transaction.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `transaction_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [FinancialTransactionUpdate](#financialtransactionupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [FinancialTransactionInDB](#financialtransactionindb)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/financial/transactions/{transaction_id}" \
  -H "Content-Type: application/json" \
  -d '{"example": "data"}'
```

---

### POST /api/financial/transactions/{transaction_id}/payment {#post-apifinancialtransactionstransaction-idpayment}

**Resumo:** Registrar Pagamento de Fatura
**Descrição:** Registra pagamento de uma fatura de fornecedor.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `transaction_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [InvoicePaymentCreate](#invoicepaymentcreate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [InvoicePaymentRead](#invoicepaymentread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/financial/transactions/{transaction_id}/payment" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
