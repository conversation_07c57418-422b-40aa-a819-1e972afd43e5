import 'package:hive/hive.dart';

part 'table_model.g.dart';

@HiveType(typeId: 4)
class TableModel extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String number;

  @HiveField(2)
  int capacity;

  @HiveField(3)
  String status; // available, occupied, reserved, cleaning

  @HiveField(4)
  String? currentOrderId;

  @HiveField(5)
  DateTime? reservationTime;

  @HiveField(6)
  String? reservationName;

  @HiveField(7)
  String? reservationPhone;

  @HiveField(8)
  String location; // indoor, outdoor, terrace, etc.

  @HiveField(9)
  bool isActive;

  @HiveField(10)
  DateTime createdAt;

  @HiveField(11)
  DateTime? updatedAt;

  TableModel({
    required this.id,
    required this.number,
    required this.capacity,
    this.status = 'available',
    this.currentOrderId,
    this.reservationTime,
    this.reservationName,
    this.reservationPhone,
    this.location = 'indoor',
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
  });

  factory TableModel.fromJson(Map<String, dynamic> json) {
    return TableModel(
      id: json['id'],
      number: json['number'],
      capacity: json['capacity'],
      status: json['status'] ?? 'available',
      currentOrderId: json['currentOrderId'],
      reservationTime: json['reservationTime'] != null 
          ? DateTime.parse(json['reservationTime']) 
          : null,
      reservationName: json['reservationName'],
      reservationPhone: json['reservationPhone'],
      location: json['location'] ?? 'indoor',
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'number': number,
      'capacity': capacity,
      'status': status,
      'currentOrderId': currentOrderId,
      'reservationTime': reservationTime?.toIso8601String(),
      'reservationName': reservationName,
      'reservationPhone': reservationPhone,
      'location': location,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  bool get isAvailable => status == 'available' && isActive;
  bool get isOccupied => status == 'occupied';
  bool get isReserved => status == 'reserved';
  bool get isCleaning => status == 'cleaning';
}