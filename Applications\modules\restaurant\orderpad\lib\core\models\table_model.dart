import 'package:hive/hive.dart';

part 'table_model.g.dart';

@HiveType(typeId: 5)
enum TableStatus {
  @HiveField(0)
  free('available'),
  
  @HiveField(1)
  occupied('occupied'),
  
  @HiveField(2)
  reserved('reserved'),
  
  @HiveField(3)
  cleaning('cleaning');
  
  const TableStatus(this.value);
  final String value;
  
  static TableStatus fromString(String value) {
    switch (value) {
      case 'available':
        return TableStatus.free;
      case 'occupied':
        return TableStatus.occupied;
      case 'reserved':
        return TableStatus.reserved;
      case 'cleaning':
        return TableStatus.cleaning;
      default:
        return TableStatus.free;
    }
  }
}

enum TableShape {
  round,
  square,
  rectangular,
  oval,
}

enum TableArea {
  indoor,
  outdoor,
  terrace,
  bar,
  vip,
}

@HiveType(typeId: 4)
class TableModel extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String number;

  @HiveField(2)
  int capacity;

  @HiveField(3)
  String status; // available, occupied, reserved, cleaning

  @HiveField(4)
  String? currentOrderId;

  @HiveField(5)
  DateTime? reservationTime;

  @HiveField(6)
  String? reservationName;

  @HiveField(7)
  String? reservationPhone;

  @HiveField(8)
  String location; // indoor, outdoor, terrace, etc.

  @HiveField(9)
  bool isActive;

  @HiveField(10)
  DateTime createdAt;

  @HiveField(11)
  DateTime? updatedAt;

  @HiveField(12)
  String? notes;

  @HiveField(13)
  String? name;

  @HiveField(14)
  String? area;

  @HiveField(15)
  TableShape? shape;

  @HiveField(16)
  DateTime? occupiedAt;

  @HiveField(17)
  DateTime? reservedAt;

  @HiveField(18)
  Map<String, double>? position;

  TableModel({
    required this.id,
    required this.number,
    required this.capacity,
    this.status = 'available',
    this.currentOrderId,
    this.reservationTime,
    this.reservationName,
    this.reservationPhone,
    this.location = 'indoor',
    this.isActive = true,
    DateTime? createdAt,
    this.updatedAt,
    this.notes,
    this.name,
    this.area,
    this.shape,
    this.occupiedAt,
    this.reservedAt,
    this.position,
    String? assignedWaiterId,
  }) : createdAt = createdAt ?? DateTime.now(),
       _assignedWaiterId = assignedWaiterId;

  factory TableModel.fromJson(Map<String, dynamic> json) {
    return TableModel(
      id: json['id'],
      number: json['number'],
      capacity: json['capacity'],
      status: json['status'] ?? 'available',
      currentOrderId: json['currentOrderId'],
      reservationTime: json['reservationTime'] != null 
          ? DateTime.parse(json['reservationTime']) 
          : null,
      reservationName: json['reservationName'],
      reservationPhone: json['reservationPhone'],
      location: json['location'] ?? 'indoor',
      isActive: json['isActive'] ?? true,
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : null,
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
      notes: json['notes'],
      name: json['name'],
      area: json['area'],
      shape: json['shape'] != null 
          ? TableShape.values.byName(json['shape']) 
          : null,
      occupiedAt: json['occupiedAt'] != null 
          ? DateTime.parse(json['occupiedAt']) 
          : null,
      reservedAt: json['reservedAt'] != null 
          ? DateTime.parse(json['reservedAt']) 
          : null,
      position: json['position'] != null 
          ? Map<String, double>.from(json['position']) 
          : null,
      assignedWaiterId: json['assignedWaiterId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'number': number,
      'capacity': capacity,
      'status': status,
      'currentOrderId': currentOrderId,
      'reservationTime': reservationTime?.toIso8601String(),
      'reservationName': reservationName,
      'reservationPhone': reservationPhone,
      'location': location,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'notes': notes,
      'name': name,
      'area': area,
      'shape': shape?.name,
      'occupiedAt': occupiedAt?.toIso8601String(),
      'reservedAt': reservedAt?.toIso8601String(),
      'position': position,
    };
  }

  bool get isAvailable => status == 'available' && isActive;
  bool get isOccupied => status == 'occupied';
  bool get isReserved => status == 'reserved';
  bool get isCleaning => status == 'cleaning';

  String get displayName => 'Mesa $number';
  String get areaDisplayName => location ?? 'Área Principal';
  String get assignedWaiter => 'Garçom não atribuído';
  
  String get statusDisplayName {
    switch (status) {
      case 'available':
        return 'Disponível';
      case 'occupied':
        return 'Ocupada';
      case 'reserved':
        return 'Reservada';
      case 'cleaning':
        return 'Limpeza';
      default:
        return 'Desconhecido';
    }
  }
  
  String get shapeDisplayName => 'Retangular';
  
  // Campos adicionais para funcionalidades específicas
  String? _assignedWaiterId;
  
  String? get assignedWaiterId => _assignedWaiterId;
  
  TableModel copyWith({
    String? id,
    String? number,
    int? capacity,
    String? status,
    String? currentOrderId,
    DateTime? reservationTime,
    String? reservationName,
    String? reservationPhone,
    String? location,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? notes,
    String? name,
    String? area,
    TableShape? shape,
    DateTime? occupiedAt,
    DateTime? reservedAt,
    String? assignedWaiterId,
    Map<String, double>? position,
  }) {
    final copy = TableModel(
      id: id ?? this.id,
      number: number ?? this.number,
      capacity: capacity ?? this.capacity,
      status: status ?? this.status,
      currentOrderId: currentOrderId ?? this.currentOrderId,
      reservationTime: reservationTime ?? this.reservationTime,
      reservationName: reservationName ?? this.reservationName,
      reservationPhone: reservationPhone ?? this.reservationPhone,
      location: location ?? this.location,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      notes: notes ?? this.notes,
      name: name ?? this.name,
      area: area ?? this.area,
      shape: shape ?? this.shape,
      occupiedAt: occupiedAt ?? this.occupiedAt,
      reservedAt: reservedAt ?? this.reservedAt,
      position: position ?? this.position,
    );
    copy._assignedWaiterId = assignedWaiterId ?? this._assignedWaiterId;
    return copy;
  }
}