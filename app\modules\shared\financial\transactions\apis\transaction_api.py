"""Financial transaction API endpoints."""

import uuid
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_db
from app.modules.core.auth.dependencies.auth import get_current_user
from app.modules.core.tenants.dependencies.tenant import get_current_tenant
from app.modules.core.users.models.user import User
from app.modules.core.tenants.models.tenant import Tenant

from ..schemas.transaction import (
    FinancialTransactionCreate,
    FinancialTransactionUpdate,
    FinancialTransactionRead,
    FinancialTransactionFilter,
    FinancialTransactionListResponse,
)
from ..services.transaction_service import FinancialTransactionService

router = APIRouter(prefix="/transactions", tags=["Financial Transactions"])


@router.post(
    "/",
    response_model=FinancialTransactionRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create financial transaction",
    description="Create a new financial transaction for the current tenant.",
)
async def create_transaction(
    transaction_data: FinancialTransactionCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
):
    """Create a new financial transaction."""
    service = FinancialTransactionService(db)
    
    transaction = await service.create_transaction(
        transaction_data=transaction_data,
        tenant_id=current_tenant.id,
        created_by=current_user.id,
    )
    
    await db.commit()
    
    # Return the created transaction
    created_transaction = await service.get_transaction(
        transaction.id, current_tenant.id
    )
    
    return service._transaction_to_read_schema(created_transaction)


@router.get(
    "/",
    response_model=FinancialTransactionListResponse,
    summary="List financial transactions",
    description="Get paginated list of financial transactions with optional filters.",
)
async def list_transactions(
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    order_by: str = Query("transaction_date", description="Order by field"),
    order_direction: str = Query("desc", regex="^(asc|desc)$", description="Order direction"),
    
    # Filters
    transaction_type: Optional[str] = Query(None, description="Filter by transaction type"),
    category_id: Optional[uuid.UUID] = Query(None, description="Filter by category ID"),
    payment_method_id: Optional[uuid.UUID] = Query(None, description="Filter by payment method ID"),
    date_from: Optional[str] = Query(None, description="Filter from date (YYYY-MM-DD)"),
    date_to: Optional[str] = Query(None, description="Filter to date (YYYY-MM-DD)"),
    amount_min: Optional[float] = Query(None, ge=0, description="Minimum amount filter"),
    amount_max: Optional[float] = Query(None, ge=0, description="Maximum amount filter"),
    search: Optional[str] = Query(None, description="Search in description and reference"),
    
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
):
    """Get paginated list of financial transactions."""
    service = FinancialTransactionService(db)
    
    # Build filters
    filters = FinancialTransactionFilter()
    if transaction_type:
        filters.transaction_type = transaction_type
    if category_id:
        filters.category_id = category_id
    if payment_method_id:
        filters.payment_method_id = payment_method_id
    if date_from:
        try:
            from datetime import datetime
            filters.date_from = datetime.strptime(date_from, "%Y-%m-%d").date()
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid date_from format. Use YYYY-MM-DD"
            )
    if date_to:
        try:
            from datetime import datetime
            filters.date_to = datetime.strptime(date_to, "%Y-%m-%d").date()
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid date_to format. Use YYYY-MM-DD"
            )
    if amount_min is not None:
        from decimal import Decimal
        filters.amount_min = Decimal(str(amount_min))
    if amount_max is not None:
        from decimal import Decimal
        filters.amount_max = Decimal(str(amount_max))
    if search:
        filters.search = search
    
    return await service.get_transactions(
        tenant_id=current_tenant.id,
        filters=filters,
        page=page,
        per_page=per_page,
        order_by=order_by,
        order_direction=order_direction,
    )


@router.get(
    "/{transaction_id}",
    response_model=FinancialTransactionRead,
    summary="Get financial transaction",
    description="Get a specific financial transaction by ID.",
)
async def get_transaction(
    transaction_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
):
    """Get a specific financial transaction."""
    service = FinancialTransactionService(db)
    
    transaction = await service.get_transaction(transaction_id, current_tenant.id)
    if not transaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Financial transaction not found"
        )
    
    return service._transaction_to_read_schema(transaction)


@router.put(
    "/{transaction_id}",
    response_model=FinancialTransactionRead,
    summary="Update financial transaction",
    description="Update a specific financial transaction.",
)
async def update_transaction(
    transaction_id: uuid.UUID,
    transaction_data: FinancialTransactionUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
):
    """Update a specific financial transaction."""
    service = FinancialTransactionService(db)
    
    transaction = await service.update_transaction(
        transaction_id=transaction_id,
        tenant_id=current_tenant.id,
        transaction_data=transaction_data,
    )
    
    if not transaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Financial transaction not found"
        )
    
    await db.commit()
    
    return service._transaction_to_read_schema(transaction)


@router.delete(
    "/{transaction_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete financial transaction",
    description="Delete a specific financial transaction.",
)
async def delete_transaction(
    transaction_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
):
    """Delete a specific financial transaction."""
    service = FinancialTransactionService(db)
    
    success = await service.delete_transaction(transaction_id, current_tenant.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Financial transaction not found"
        )
    
    await db.commit()


@router.get(
    "/summary/stats",
    response_model=dict,
    summary="Get transaction summary",
    description="Get summary statistics for financial transactions.",
)
async def get_transaction_summary(
    # Filters for summary
    transaction_type: Optional[str] = Query(None, description="Filter by transaction type"),
    category_id: Optional[uuid.UUID] = Query(None, description="Filter by category ID"),
    date_from: Optional[str] = Query(None, description="Filter from date (YYYY-MM-DD)"),
    date_to: Optional[str] = Query(None, description="Filter to date (YYYY-MM-DD)"),
    
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
):
    """Get summary statistics for financial transactions."""
    service = FinancialTransactionService(db)
    
    # Build filters
    filters = FinancialTransactionFilter()
    if transaction_type:
        filters.transaction_type = transaction_type
    if category_id:
        filters.category_id = category_id
    if date_from:
        try:
            from datetime import datetime
            filters.date_from = datetime.strptime(date_from, "%Y-%m-%d").date()
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid date_from format. Use YYYY-MM-DD"
            )
    if date_to:
        try:
            from datetime import datetime
            filters.date_to = datetime.strptime(date_to, "%Y-%m-%d").date()
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid date_to format. Use YYYY-MM-DD"
            )
    
    summary = await service._calculate_summary(current_tenant.id, filters)
    
    return {
        "summary": summary,
        "tenant_id": current_tenant.id,
        "filters_applied": filters.model_dump(exclude_unset=True),
    }
