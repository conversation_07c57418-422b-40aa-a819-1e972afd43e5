"""
SEO Meta Model

Generic SEO metadata model that can be attached to any content type.
Supports multilingual SEO with integration to the i18n system.
"""

import uuid
from datetime import datetime
from typing import Optional

from sqlalchemy import Column, DateTime, ForeignKey, String, Text, JSON, Boolean, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base


class SEOMeta(Base):
    """
    Generic SEO metadata model for any content type.
    
    This model stores SEO-specific data including meta tags, Open Graph,
    Twitter Cards, and structured data for any entity in the system.
    """

    __tablename__ = "seo_meta"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Generic content reference
    content_type = Column(String(50), nullable=False, index=True)  # e.g., 'product', 'blog_post', 'category'
    content_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # Language support
    language_code = Column(String(10), ForeignKey("languages.code"), nullable=False, index=True)
    
    # Basic SEO
    meta_title = Column(String(200), nullable=True)
    meta_description = Column(Text, nullable=True)
    meta_keywords = Column(Text, nullable=True)  # Comma-separated keywords
    canonical_url = Column(String(500), nullable=True)
    robots_directive = Column(String(100), default="index,follow", nullable=False)
    
    # Open Graph (Facebook, LinkedIn, etc.)
    og_title = Column(String(95), nullable=True)  # Max 95 chars for OG
    og_description = Column(String(300), nullable=True)  # Max 300 chars for OG
    og_image_url = Column(String(500), nullable=True)
    og_image_alt = Column(String(255), nullable=True)
    og_type = Column(String(50), default="website", nullable=False)
    og_site_name = Column(String(100), nullable=True)
    
    # Twitter Cards
    twitter_card_type = Column(String(50), default="summary_large_image", nullable=False)
    twitter_title = Column(String(70), nullable=True)  # Max 70 chars for Twitter
    twitter_description = Column(String(200), nullable=True)  # Max 200 chars
    twitter_image_url = Column(String(500), nullable=True)
    twitter_image_alt = Column(String(255), nullable=True)
    twitter_creator = Column(String(100), nullable=True)  # @username
    twitter_site = Column(String(100), nullable=True)  # @site
    
    # Structured Data (JSON-LD)
    structured_data = Column(JSON, nullable=True)  # Store JSON-LD markup
    
    # Additional meta tags
    additional_meta_tags = Column(JSON, nullable=True)  # Custom meta tags
    
    # SEO Analysis
    focus_keyword = Column(String(100), nullable=True)
    keyword_density = Column(String(10), nullable=True)  # e.g., "2.5%"
    readability_score = Column(String(20), nullable=True)  # e.g., "Good"
    seo_score = Column(Integer, nullable=True)  # 0-100
    
    # Status and priority
    is_active = Column(Boolean, default=True, nullable=False)
    priority = Column(Integer, default=50, nullable=False)  # 0-100, higher = more important
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Foreign keys
    created_by_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    updated_by_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # Relationships
    language = relationship("app.modules.core.i18n.models.language.Language", viewonly=True)
    created_by = relationship("app.modules.core.users.models.user.User", foreign_keys=[created_by_id], viewonly=True)
    updated_by = relationship("app.modules.core.users.models.user.User", foreign_keys=[updated_by_id], viewonly=True)

    # Composite index for efficient lookups
    __table_args__ = (
        {"schema": None},  # Use default schema
    )

    def __repr__(self):
        return f"<SEOMeta(id={self.id}, content_type='{self.content_type}', content_id={self.content_id}, language='{self.language_code}')>"

    @property
    def effective_title(self) -> Optional[str]:
        """Get the most appropriate title for this content."""
        return self.meta_title or self.og_title or self.twitter_title

    @property
    def effective_description(self) -> Optional[str]:
        """Get the most appropriate description for this content."""
        return self.meta_description or self.og_description or self.twitter_description

    @property
    def effective_image(self) -> Optional[str]:
        """Get the most appropriate image for this content."""
        return self.og_image_url or self.twitter_image_url
