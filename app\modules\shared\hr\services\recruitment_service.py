from typing import List, Optional
import uuid
import os
import shutil
from fastapi import UploadFile
from sqlalchemy import select, or_
from sqlalchemy.ext.asyncio import AsyncSession

from app.modules.shared.hr.models.recruitment import (
    Job,
    Candidate,
    Interview,
    Assessment,
    JobStatus,
    CandidateStatus,
)
from app.modules.shared.hr.models.employee import Employee  # noqa: E402
from app.modules.shared.hr.schemas.recruitment import (
    JobCreate,
    JobUpdate,
    CandidateCreate,
    InterviewCreate,
    AssessmentCreate,
    JobApplicationCreate,
)
from app.core.exceptions import NotFoundError  # noqa: E402
from app.core.config import settings


class RecruitmentService:
    """Service for recruitment operations."""

    @staticmethod
    async def create_job(db: AsyncSession, tenant_id: uuid.UUID, job_data: JobCreate) -> Job:
        """Create a new job."""
        # Verify that the hiring manager exists if provided
        if job_data.hiring_manager_id:
            stmt = select(Employee).where(
                Employee.id == job_data.hiring_manager_id,
                Employee.tenant_id == tenant_id,
            )
            result = await db.execute(stmt)
            hiring_manager = result.scalars().first()

            if not hiring_manager:
                raise NotFoundError(
                    f"Hiring manager with id {job_data.hiring_manager_id} not found"
                )

        # Verify that the recruiter exists if provided
        if job_data.recruiter_id:
            stmt = select(Employee).where(
                Employee.id == job_data.recruiter_id, Employee.tenant_id == tenant_id
            )
            result = await db.execute(stmt)
            recruiter = result.scalars().first()

            if not recruiter:
                raise NotFoundError(f"Recruiter with id {job_data.recruiter_id} not found")

        # Create job
        job = Job(tenant_id=tenant_id, **job_data.model_dump(exclude={"metadata"}))

        # Add metadata if provided
        if job_data.metadata:
            job.metadata = job_data.metadata

        db.add(job)
        await db.commit()
        await db.refresh(job)
        return job

    @staticmethod
    async def get_job(db: AsyncSession, tenant_id: uuid.UUID, job_id: uuid.UUID) -> Job:
        """Get a job by ID."""
        stmt = select(Job).where(Job.id == job_id, Job.tenant_id == tenant_id)
        result = await db.execute(stmt)
        job = result.scalars().first()

        if not job:
            raise NotFoundError(f"Job with id {job_id} not found")

        return job

    @staticmethod
    async def get_jobs(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        status: Optional[JobStatus] = None,
        department: Optional[str] = None,
        search: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[Job]:
        """Get all jobs with optional filtering."""
        query = select(Job).where(Job.tenant_id == tenant_id)

        # Apply filters if provided
        if status:
            query = query.where(Job.status == status)

        if department:
            query = query.where(Job.department == department)

        if search:
            query = query.where(
                or_(
                    Job.title.ilike(f"%{search}%"),
                    Job.description.ilike(f"%{search}%"),
                    Job.location.ilike(f"%{search}%"),
                )
            )

        # Apply pagination
        query = query.offset(skip).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def update_job(
        db: AsyncSession, tenant_id: uuid.UUID, job_id: uuid.UUID, job_data: JobUpdate
    ) -> Job:
        """Update a job."""
        # Get the job first to ensure it exists
        job = await RecruitmentService.get_job(db, tenant_id, job_id)

        # Verify that the hiring manager exists if provided
        if job_data.hiring_manager_id:
            stmt = select(Employee).where(
                Employee.id == job_data.hiring_manager_id,
                Employee.tenant_id == tenant_id,
            )
            result = await db.execute(stmt)
            hiring_manager = result.scalars().first()

            if not hiring_manager:
                raise NotFoundError(
                    f"Hiring manager with id {job_data.hiring_manager_id} not found"
                )

        # Verify that the recruiter exists if provided
        if job_data.recruiter_id:
            stmt = select(Employee).where(
                Employee.id == job_data.recruiter_id, Employee.tenant_id == tenant_id
            )
            result = await db.execute(stmt)
            recruiter = result.scalars().first()

            if not recruiter:
                raise NotFoundError(f"Recruiter with id {job_data.recruiter_id} not found")

        # Update job fields
        for key, value in job_data.model_dump(exclude_unset=True).items():
            setattr(job, key, value)

        await db.commit()
        await db.refresh(job)
        return job

    @staticmethod
    async def delete_job(db: AsyncSession, tenant_id: uuid.UUID, job_id: uuid.UUID) -> None:
        """Delete a job."""
        job = await RecruitmentService.get_job(db, tenant_id, job_id)

        # Delete the job
        await db.delete(job)
        await db.commit()

    @staticmethod
    async def create_candidate(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        candidate_data: CandidateCreate,
        resume_file: Optional[UploadFile] = None,
        cover_letter_file: Optional[UploadFile] = None,
    ) -> Candidate:
        """Create a new candidate."""
        # Verify that the job exists
        stmt = select(Job).where(Job.id == candidate_data.job_id, Job.tenant_id == tenant_id)
        result = await db.execute(stmt)
        job = result.scalars().first()

        if not job:
            raise NotFoundError(f"Job with id {candidate_data.job_id} not found")

        # Handle file uploads if provided
        resume_path = candidate_data.resume_path
        cover_letter_path = candidate_data.cover_letter_path

        if resume_file:
            # Create directory if it doesn't exist
            upload_dir = os.path.join(settings.MEDIA_ROOT, "resumes", str(tenant_id))
            os.makedirs(upload_dir, exist_ok=True)

            # Generate unique filename
            file_extension = os.path.splitext(resume_file.filename)[1]
            unique_filename = f"{uuid.uuid4()}{file_extension}"
            resume_path = os.path.join(upload_dir, unique_filename)

            # Save file
            with open(resume_path, "wb") as buffer:
                shutil.copyfileobj(resume_file.file, buffer)

        if cover_letter_file:
            # Create directory if it doesn't exist
            upload_dir = os.path.join(settings.MEDIA_ROOT, "cover_letters", str(tenant_id))
            os.makedirs(upload_dir, exist_ok=True)

            # Generate unique filename
            file_extension = os.path.splitext(cover_letter_file.filename)[1]
            unique_filename = f"{uuid.uuid4()}{file_extension}"
            cover_letter_path = os.path.join(upload_dir, unique_filename)

            # Save file
            with open(cover_letter_path, "wb") as buffer:
                shutil.copyfileobj(cover_letter_file.file, buffer)

        # Create candidate
        candidate = Candidate(
            tenant_id=tenant_id,
            resume_path=resume_path,
            cover_letter_path=cover_letter_path,
            **candidate_data.model_dump(exclude={"resume_path", "cover_letter_path", "metadata"}),
        )

        # Add metadata if provided
        if candidate_data.metadata:
            candidate.metadata = candidate_data.metadata

        db.add(candidate)
        await db.commit()
        await db.refresh(candidate)
        return candidate

    @staticmethod
    async def process_job_application(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        application_data: JobApplicationCreate,
        resume_file: Optional[UploadFile] = None,
        cover_letter_file: Optional[UploadFile] = None,
    ) -> Candidate:
        """Process a job application and create a candidate."""
        # Verify that the job exists and is open
        stmt = select(Job).where(
            Job.id == application_data.job_id,
            Job.tenant_id == tenant_id,
            Job.status == JobStatus.OPEN,
        )
        result = await db.execute(stmt)
        job = result.scalars().first()

        if not job:
            raise NotFoundError(f"Open job with id {application_data.job_id} not found")

        # Create candidate data from application
        candidate_data = CandidateCreate(
            job_id=application_data.job_id,
            first_name=application_data.first_name,
            last_name=application_data.last_name,
            email=application_data.email,
            phone=application_data.phone,
            source=application_data.source,
            status=CandidateStatus.NEW,
            notes=application_data.cover_letter,
            metadata=application_data.metadata,
        )

        # Create the candidate
        return await RecruitmentService.create_candidate(
            db=db,
            tenant_id=tenant_id,
            candidate_data=candidate_data,
            resume_file=resume_file,
            cover_letter_file=cover_letter_file,
        )

    @staticmethod
    async def create_interview(
        db: AsyncSession, tenant_id: uuid.UUID, interview_data: InterviewCreate
    ) -> Interview:
        """Create a new interview."""
        # Verify that the candidate exists
        stmt = select(Candidate).where(
            Candidate.id == interview_data.candidate_id,
            Candidate.tenant_id == tenant_id,
        )
        result = await db.execute(stmt)
        candidate = result.scalars().first()

        if not candidate:
            raise NotFoundError(f"Candidate with id {interview_data.candidate_id} not found")

        # Create interview
        interview = Interview(
            tenant_id=tenant_id, **interview_data.model_dump(exclude={"metadata"})
        )

        # Add metadata if provided
        if interview_data.metadata:
            interview.metadata = interview_data.metadata

        db.add(interview)
        await db.commit()
        await db.refresh(interview)

        # Update candidate status if needed
        if candidate.status in [CandidateStatus.NEW, CandidateStatus.SCREENING]:
            candidate.status = CandidateStatus.INTERVIEW
            await db.commit()

        return interview

    @staticmethod
    async def create_assessment(
        db: AsyncSession, tenant_id: uuid.UUID, assessment_data: AssessmentCreate
    ) -> Assessment:
        """Create a new assessment."""
        # Verify that the candidate exists
        stmt = select(Candidate).where(
            Candidate.id == assessment_data.candidate_id,
            Candidate.tenant_id == tenant_id,
        )
        result = await db.execute(stmt)
        candidate = result.scalars().first()

        if not candidate:
            raise NotFoundError(f"Candidate with id {assessment_data.candidate_id} not found")

        # Create assessment
        assessment = Assessment(
            tenant_id=tenant_id, **assessment_data.model_dump(exclude={"metadata"})
        )

        # Add metadata if provided
        if assessment_data.metadata:
            assessment.metadata = assessment_data.metadata

        db.add(assessment)
        await db.commit()
        await db.refresh(assessment)

        # Update candidate status if needed
        if candidate.status in [
            CandidateStatus.NEW,
            CandidateStatus.SCREENING,
            CandidateStatus.INTERVIEW,
        ]:
            candidate.status = CandidateStatus.ASSESSMENT
            await db.commit()

        return assessment


# Create a singleton instance
recruitment_service = RecruitmentService()
