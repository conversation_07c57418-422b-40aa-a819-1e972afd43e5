# Restaurant - Tenant Settings

**Categoria:** Restaurant
**Módulo:** Tenant Settings
**Total de Endpoints:** 11
**Gerado em:** 27/06/2025, 20:20:27

## 📋 Endpoints

- [GET /api/modules/restaurants/settings/](#get-apimodulesrestaurantssettings) - Get Restaurant Settings
- [PUT /api/modules/restaurants/settings/](#put-apimodulesrestaurantssettings) - Update Restaurant Settings
- [PUT /api/modules/restaurants/settings/business/](#put-apimodulesrestaurantssettingsbusiness) - Update Restaurant Business Settings
- [PUT /api/modules/restaurants/settings/location/](#put-apimodulesrestaurantssettingslocation) - Update Restaurant Location Settings
- [PUT /api/modules/restaurants/settings/operating-hours/](#put-apimodulesrestaurantssettingsoperating-hours) - Update Operating Hours
- [PUT /api/modules/restaurants/settings/social-media/](#put-apimodulesrestaurantssettingssocial-media) - Update Restaurant Social Media Settings
- [PUT /api/modules/restaurants/settings/special-calendar/](#put-apimodulesrestaurantssettingsspecial-calendar) - Update Special Calendar
- [PUT /api/modules/restaurants/settings/wifi/](#put-apimodulesrestaurantssettingswifi) - Update Restaurant Wifi Settings
- [GET /api/modules/restaurants/settings/zones/](#get-apimodulesrestaurantssettingszones) - Get Restaurant Zones
- [POST /api/modules/restaurants/settings/zones/](#post-apimodulesrestaurantssettingszones) - Add Custom Zone
- [DELETE /api/modules/restaurants/settings/zones/{zone_name}](#delete-apimodulesrestaurantssettingszoneszone-name) - Remove Custom Zone

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### RestaurantBusinessSettingsUpdate

**Descrição:** Schema for updating restaurant business settings.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `tenant_slug` | unknown | ❌ | Unique slug for tenant identification in public URLs |

### RestaurantLocationSettingsUpdate

**Descrição:** Schema for updating restaurant location settings.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `address_extensions` | unknown | ❌ | Restaurant-specific address extensions |

### RestaurantOperatingHoursUpdate

**Descrição:** Schema for updating restaurant operating hours.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `operating_hours` | unknown | ❌ | Operating hours configuration by day of week |

### RestaurantSocialMediaSettingsUpdate

**Descrição:** Schema for updating restaurant social media settings.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `social_media_links` | unknown | ❌ | Social media platform links with icons |

### RestaurantSpecialCalendarUpdate

**Descrição:** Schema for updating restaurant special calendar.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `special_calendar` | unknown | ❌ | Special calendar events by date |

### RestaurantTenantSettingsRead

**Descrição:** Schema for reading restaurant tenant settings.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `tenant_slug` | unknown | ❌ | Unique slug for tenant identification in public URLs |
| `wifi_networks` | unknown | ❌ | WiFi networks configuration by zone |
| `social_media_links` | unknown | ❌ | Social media platform links with icons |
| `address_extensions` | unknown | ❌ | Restaurant-specific address extensions |
| `operating_hours` | unknown | ❌ | Restaurant operating hours with service, break, and happy hour periods |
| `special_calendar` | unknown | ❌ | Special calendar events with custom hours or closures |
| `additional_restaurant_settings` | unknown | ❌ | Additional restaurant-specific settings |
| `id` | string | ✅ | Unique identifier for the restaurant settings |
| `tenant_id` | string | ✅ | ID of the tenant these settings belong to |
| `has_tenant_slug` | boolean | ✅ | Whether tenant slug is configured |
| `has_wifi_networks` | boolean | ✅ | Whether WiFi networks are configured |
| `has_social_media_links` | boolean | ✅ | Whether social media links are configured |
| `has_address_extensions` | boolean | ✅ | Whether address extensions are configured |
| `delivery_radius_km` | unknown | ❌ | Delivery radius in kilometers |
| `phone_is_whatsapp` | boolean | ❌ | Whether primary phone is WhatsApp |
| `has_operating_hours` | boolean | ❌ | Whether operating hours are configured |
| `has_special_calendar` | boolean | ❌ | Whether special calendar events are configured |
| `is_open_today` | boolean | ❌ | Whether restaurant is open today |

### RestaurantTenantSettingsUpdate

**Descrição:** Schema for updating restaurant tenant settings.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `tenant_slug` | unknown | ❌ | Unique slug for tenant identification in public URLs |
| `wifi_networks` | unknown | ❌ | WiFi networks configuration by zone |
| `social_media_links` | unknown | ❌ | Social media platform links with icons |
| `address_extensions` | unknown | ❌ | Restaurant-specific address extensions |
| `operating_hours` | unknown | ❌ | Restaurant operating hours with service, break, and happy hour periods |
| `special_calendar` | unknown | ❌ | Special calendar events with custom hours or closures |
| `additional_restaurant_settings` | unknown | ❌ | Additional restaurant-specific settings |

### RestaurantWiFiSettingsUpdate

**Descrição:** Schema for updating restaurant WiFi settings.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `wifi_networks` | unknown | ❌ | WiFi networks configuration by zone |

## 🔗 Endpoints Detalhados

### GET /api/modules/restaurants/settings/ {#get-apimodulesrestaurantssettings}

**Resumo:** Get Restaurant Settings
**Descrição:** Retrieve restaurant-specific tenant settings including tenant_slug, WiFi networks, social media links, and address extensions.

**🔐 Autenticação:** Requerida

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [RestaurantTenantSettingsRead](#restauranttenantsettingsread)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/settings/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/restaurants/settings/ {#put-apimodulesrestaurantssettings}

**Resumo:** Update Restaurant Settings
**Descrição:** Update restaurant-specific tenant settings with comprehensive validation.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [RestaurantTenantSettingsUpdate](#restauranttenantsettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [RestaurantTenantSettingsRead](#restauranttenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/settings/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/modules/restaurants/settings/business/ {#put-apimodulesrestaurantssettingsbusiness}

**Resumo:** Update Restaurant Business Settings
**Descrição:** Update restaurant business settings (tenant_slug).

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [RestaurantBusinessSettingsUpdate](#restaurantbusinesssettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [RestaurantTenantSettingsRead](#restauranttenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/settings/business/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/modules/restaurants/settings/location/ {#put-apimodulesrestaurantssettingslocation}

**Resumo:** Update Restaurant Location Settings
**Descrição:** Update restaurant location settings.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [RestaurantLocationSettingsUpdate](#restaurantlocationsettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [RestaurantTenantSettingsRead](#restauranttenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/settings/location/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/modules/restaurants/settings/operating-hours/ {#put-apimodulesrestaurantssettingsoperating-hours}

**Resumo:** Update Operating Hours
**Descrição:** Update restaurant operating hours with service hours, break periods, and happy hour.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [RestaurantOperatingHoursUpdate](#restaurantoperatinghoursupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [RestaurantTenantSettingsRead](#restauranttenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/settings/operating-hours/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/modules/restaurants/settings/social-media/ {#put-apimodulesrestaurantssettingssocial-media}

**Resumo:** Update Restaurant Social Media Settings
**Descrição:** Update restaurant social media settings.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [RestaurantSocialMediaSettingsUpdate](#restaurantsocialmediasettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [RestaurantTenantSettingsRead](#restauranttenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/settings/social-media/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/modules/restaurants/settings/special-calendar/ {#put-apimodulesrestaurantssettingsspecial-calendar}

**Resumo:** Update Special Calendar
**Descrição:** Update restaurant special calendar with holidays and special events.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [RestaurantSpecialCalendarUpdate](#restaurantspecialcalendarupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [RestaurantTenantSettingsRead](#restauranttenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/settings/special-calendar/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/modules/restaurants/settings/wifi/ {#put-apimodulesrestaurantssettingswifi}

**Resumo:** Update Restaurant Wifi Settings
**Descrição:** Update restaurant WiFi settings.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [RestaurantWiFiSettingsUpdate](#restaurantwifisettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [RestaurantTenantSettingsRead](#restauranttenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/settings/wifi/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/restaurants/settings/zones/ {#get-apimodulesrestaurantssettingszones}

**Resumo:** Get Restaurant Zones
**Descrição:** Get all available zones for restaurant including zones from tables and custom zones.

**🔐 Autenticação:** Requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/settings/zones/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/restaurants/settings/zones/ {#post-apimodulesrestaurantssettingszones}

**Resumo:** Add Custom Zone
**Descrição:** Add a custom zone to restaurant.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** {'additionalProperties': {'type': 'string'}, 'type': 'object', 'title': 'Zone Data'}

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [RestaurantTenantSettingsRead](#restauranttenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/restaurants/settings/zones/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/restaurants/settings/zones/{zone_name} {#delete-apimodulesrestaurantssettingszoneszone-name}

**Resumo:** Remove Custom Zone
**Descrição:** Remove a custom zone from restaurant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `zone_name` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [RestaurantTenantSettingsRead](#restauranttenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/restaurants/settings/zones/{zone_name}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
