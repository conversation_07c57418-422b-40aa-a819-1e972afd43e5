"""
Validation service for restaurant tenant settings.
"""

import re
from typing import Dict, Any, List

from app.core.exceptions import ValidationError


class RestaurantSettingsValidationService:
    """Service for validating restaurant-specific settings."""

    def validate_operating_hours(self, operating_hours: Dict[str, Any]) -> bool:
        """Validate operating hours structure."""
        if not operating_hours:
            return True
            
        valid_days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
        
        for day, schedule in operating_hours.items():
            if day not in valid_days:
                raise ValidationError(f"Invalid day: {day}")
            
            if not isinstance(schedule, dict):
                raise ValidationError(f"Schedule for {day} must be a dictionary")
            
            # Validate required fields
            if 'is_open' not in schedule:
                raise ValidationError(f"Missing 'is_open' field for {day}")
            
            if schedule.get('is_open', False):
                # Validate time slots if restaurant is open
                for slot_type in ['service_hours', 'break_periods', 'happy_hour']:
                    slots = schedule.get(slot_type, [])
                    if slots:
                        self.validate_time_slots(slots, slot_type)
        
        return True

    def validate_time_slots(self, slots: List[Dict[str, Any]], slot_type: str) -> bool:
        """Validate time slots structure."""
        for slot in slots:
            # Validate required fields
            required_fields = ['id', 'open', 'close', 'type']
            for field in required_fields:
                if field not in slot:
                    raise ValidationError(f"Missing '{field}' in {slot_type} slot")
            
            # Validate time format
            time_pattern = r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$'
            
            for time_field in ['open', 'close']:
                if not re.match(time_pattern, slot[time_field]):
                    raise ValidationError(f"Invalid time format for {time_field}: {slot[time_field]}")
            
            # Validate slot type
            valid_types = ['service', 'break', 'happy_hour']
            if slot['type'] not in valid_types:
                raise ValidationError(f"Invalid slot type: {slot['type']}")
        
        # Validate no overlapping slots
        self.validate_no_overlapping_slots(slots, slot_type)
        
        return True

    def validate_no_overlapping_slots(self, slots: List[Dict[str, Any]], slot_type: str) -> bool:
        """Validate that time slots don't overlap."""
        if len(slots) <= 1:
            return True
        
        # Sort slots by open time
        sorted_slots = sorted(slots, key=lambda x: x['open'])
        
        for i in range(len(sorted_slots) - 1):
            current_close = sorted_slots[i]['close']
            next_open = sorted_slots[i + 1]['open']
            
            # Handle overnight hours (e.g., 22:00 - 02:00)
            if current_close > next_open and next_open > "06:00":
                raise ValidationError(f'Overlapping time slots in {slot_type}')
        
        return True

    def validate_special_calendar(self, special_calendar: Dict[str, Any]) -> bool:
        """Validate special calendar structure."""
        if not special_calendar:
            return True
            
        date_pattern = r'^\d{4}-\d{2}-\d{2}$'
        
        for date_str, event in special_calendar.items():
            # Validate date format
            if not re.match(date_pattern, date_str):
                raise ValidationError(f"Invalid date format: {date_str}. Use YYYY-MM-DD")
            
            # Validate date is valid
            try:
                from datetime import datetime
                datetime.strptime(date_str, '%Y-%m-%d')
            except ValueError:
                raise ValidationError(f"Invalid date: {date_str}")
            
            # Validate event structure
            if not isinstance(event, dict):
                raise ValidationError(f"Event for {date_str} must be a dictionary")
            
            # Validate required reason field
            if 'reason' not in event:
                raise ValidationError(f"Missing 'reason' field for event on {date_str}")
            
            # Validate reason length
            if len(event['reason']) > 200:
                raise ValidationError(f"Reason too long for event on {date_str}")
            
            # Validate special hours if provided
            if 'special_hours' in event:
                self.validate_special_hours(event['special_hours'], date_str)
            
            # Validate note length if provided
            if 'note' in event and len(event['note']) > 500:
                raise ValidationError(f"Note too long for event on {date_str}")
        
        return True

    def validate_special_hours(self, special_hours: Dict[str, str], date_str: str) -> bool:
        """Validate special hours structure."""
        if not isinstance(special_hours, dict):
            raise ValidationError(f"Special hours for {date_str} must be a dictionary")
        
        required_fields = ['open', 'close']
        for field in required_fields:
            if field not in special_hours:
                raise ValidationError(f"Missing '{field}' in special hours for {date_str}")
            
            # Validate time format
            time_pattern = r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$'
            if not re.match(time_pattern, special_hours[field]):
                raise ValidationError(f"Invalid time format for {field}: {special_hours[field]}")
        
        return True

    def validate_tenant_slug_format(self, tenant_slug: str) -> bool:
        """Validate tenant slug format."""
        if not tenant_slug or not tenant_slug.strip():
            return False
            
        # Validate slug format (alphanumeric, hyphens, no spaces)
        if not re.match(r"^[a-z0-9](?:[a-z0-9\-]{0,61}[a-z0-9])?$", tenant_slug):
            return False
            
        # Additional validations
        if len(tenant_slug) < 3:
            raise ValidationError("Tenant slug must be at least 3 characters long")
        
        if len(tenant_slug) > 63:
            raise ValidationError("Tenant slug must be at most 63 characters long")
        
        # Check for reserved words
        reserved_words = [
            'admin', 'api', 'www', 'mail', 'ftp', 'localhost', 'test', 'staging',
            'production', 'dev', 'development', 'app', 'application', 'system',
            'root', 'user', 'users', 'account', 'accounts', 'login', 'logout',
            'register', 'signup', 'signin', 'auth', 'authentication', 'dashboard',
            'panel', 'control', 'config', 'configuration', 'settings', 'setup'
        ]
        
        if tenant_slug.lower() in reserved_words:
            raise ValidationError(f"'{tenant_slug}' is a reserved word and cannot be used")
        
        return True

    def validate_comprehensive_settings(self, settings_data: Dict[str, Any]) -> bool:
        """Validate all restaurant settings comprehensively."""
        # Validate operating hours if provided
        if 'operating_hours' in settings_data and settings_data['operating_hours']:
            self.validate_operating_hours(settings_data['operating_hours'])
        
        # Validate special calendar if provided
        if 'special_calendar' in settings_data and settings_data['special_calendar']:
            self.validate_special_calendar(settings_data['special_calendar'])
        
        # Validate tenant slug if provided
        if 'tenant_slug' in settings_data and settings_data['tenant_slug']:
            self.validate_tenant_slug_format(settings_data['tenant_slug'])
        
        return True


# Create validation service instance
restaurant_validation_service = RestaurantSettingsValidationService()
