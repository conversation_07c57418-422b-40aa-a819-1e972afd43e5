'use client';

import { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import {
  ChartBarIcon,
  CurrencyDollarIcon,
  ShoppingBagIcon,
  UserGroupIcon,
  CalendarIcon,
  ClockIcon,
  StarIcon,
  EyeIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '@heroicons/react/24/outline';
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { toast } from 'sonner';
import { useEshopAnalytics, type DashboardStats, type AnalyticsFilters } from '@/hooks/useEshopAnalytics';

const mockStats: DashboardStats = {
  total_revenue: 2847650.75,
  total_orders: 15847,
  total_customers: 3421,
  total_vendors: 287,
  avg_order_value: 179.65,
  conversion_rate: 3.2,
  revenue_growth: 12.5,
  orders_growth: 8.3,
  customers_growth: 15.7,
  top_selling_products: [
    {
      id: '1',
      name: 'Premium Coffee Blend',
      vendor_name: 'João Silva Coffee',
      total_sales: 1247,
      units_sold: 3891,
      revenue: 48750.30,
      market_type: 'B2B'
    },
    {
      id: '2',
      name: 'Organic Tea Collection',
      vendor_name: 'Maria Santos Tea',
      total_sales: 892,
      units_sold: 2156,
      revenue: 35680.50,
      market_type: 'PUBLIC'
    },
    {
      id: '3',
      name: 'Kitchen Pro Utensils',
      vendor_name: 'Carlos Kitchen',
      total_sales: 654,
      units_sold: 1789,
      revenue: 28950.75,
      market_type: 'B2B'
    },
    {
      id: '4',
      name: 'Artisan Bread Mix',
      vendor_name: 'Ana Bakery',
      total_sales: 543,
      units_sold: 1432,
      revenue: 21650.25,
      market_type: 'PUBLIC'
    },
    {
      id: '5',
      name: 'Gourmet Spice Set',
      vendor_name: 'Pedro Spices',
      total_sales: 432,
      units_sold: 987,
      revenue: 18750.80,
      market_type: 'B2B'
    }
  ],
  top_vendors: [
    {
      id: '1',
      name: 'João Silva',
      business_name: 'Coffee Store Premium',
      total_orders: 2847,
      total_revenue: 485750.30,
      commission_earned: 24287.52,
      rating: 4.8,
      market_type: 'B2B'
    },
    {
      id: '2',
      name: 'Maria Santos',
      business_name: 'Organic Tea Collection',
      total_orders: 1956,
      total_revenue: 356800.50,
      commission_earned: 17840.03,
      rating: 4.9,
      market_type: 'PUBLIC'
    },
    {
      id: '3',
      name: 'Carlos Oliveira',
      business_name: 'Kitchen Pro Equipment',
      total_orders: 1543,
      total_revenue: 289500.75,
      commission_earned: 14475.04,
      rating: 4.7,
      market_type: 'B2B'
    }
  ],
  recent_orders: [
    {
      id: '1',
      order_number: 'ORD-2024-001547',
      customer_name: 'Restaurant ABC',
      vendor_name: 'João Silva Coffee',
      total_amount: 1250.75,
      status: 'confirmed',
      created_at: '2024-12-27T10:30:00Z',
      market_type: 'B2B'
    },
    {
      id: '2',
      order_number: 'ORD-2024-001546',
      customer_name: 'Ana Costa',
      vendor_name: 'Maria Santos Tea',
      total_amount: 89.50,
      status: 'delivered',
      created_at: '2024-12-27T09:15:00Z',
      market_type: 'PUBLIC'
    }
  ],
  revenue_by_month: [
    { month: 'Jan', revenue: 185000, orders: 1250, b2b_revenue: 110000, public_revenue: 75000 },
    { month: 'Feb', revenue: 195000, orders: 1320, b2b_revenue: 115000, public_revenue: 80000 },
    { month: 'Mar', revenue: 210000, orders: 1450, b2b_revenue: 125000, public_revenue: 85000 },
    { month: 'Apr', revenue: 225000, orders: 1580, b2b_revenue: 135000, public_revenue: 90000 },
    { month: 'May', revenue: 240000, orders: 1650, b2b_revenue: 145000, public_revenue: 95000 },
    { month: 'Jun', revenue: 255000, orders: 1720, b2b_revenue: 155000, public_revenue: 100000 }
  ]
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending': return 'bg-yellow-100 text-yellow-800';
    case 'confirmed': return 'bg-blue-100 text-blue-800';
    case 'delivered': return 'bg-green-100 text-green-800';
    case 'cancelled': return 'bg-red-100 text-red-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getMarketTypeColor = (type: string) => {
  switch (type) {
    case 'B2B': return 'bg-blue-100 text-blue-800';
    case 'PUBLIC': return 'bg-green-100 text-green-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

export default function EShopStatsPage() {
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [marketType, setMarketType] = useState<'B2B' | 'PUBLIC' | 'ALL'>('ALL');

  const {
    dashboardStats,
    commissionAnalytics,
    productAnalytics,
    isLoading,
    error,
    fetchDashboardStats,
    exportAnalytics,
    clearError
  } = useEshopAnalytics();

  // Use mock data as fallback when API is not available
  const stats = dashboardStats || mockStats;

  const loadStats = useCallback(async () => {
    clearError();
    const filters: AnalyticsFilters = {
      timeRange,
      marketType: marketType !== 'ALL' ? marketType : undefined
    };

    try {
      await fetchDashboardStats(filters);
    } catch (error) {
      // Fallback to mock data if API fails
      console.warn('Using mock data as fallback');
    }
  }, [timeRange, marketType, fetchDashboardStats, clearError]);

  const handleExport = async (format: 'csv' | 'xlsx' | 'pdf') => {
    const filters: AnalyticsFilters = {
      timeRange,
      marketType: marketType !== 'ALL' ? marketType : undefined
    };

    try {
      await exportAnalytics('dashboard', format, filters);
    } catch (error) {
      // Error already handled in hook
    }
  };

  useEffect(() => {
    loadStats();
  }, [timeRange, marketType, loadStats]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="border-b border-gray-200 pb-4">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Analytics & Reports</h1>
            <p className="text-gray-600 mt-1">
              Comprehensive insights for B2B and Public marketplace performance
            </p>
            {error && (
              <div className="mt-2 text-sm text-red-600 bg-red-50 px-3 py-2 rounded-md">
                {error} - Using cached data
              </div>
            )}
          </div>
          <div className="flex items-center space-x-3">
            <Select value={marketType} onValueChange={(value: 'B2B' | 'PUBLIC' | 'ALL') => setMarketType(value)}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Market Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Markets</SelectItem>
                <SelectItem value="B2B">B2B Only</SelectItem>
                <SelectItem value="PUBLIC">Public Only</SelectItem>
              </SelectContent>
            </Select>

            <Select value={timeRange} onValueChange={(value: '7d' | '30d' | '90d' | '1y') => setTimeRange(value)}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Select time range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
                <SelectItem value="1y">Last year</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleExport('csv')}
                disabled={isLoading}
              >
                Export CSV
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleExport('xlsx')}
                disabled={isLoading}
              >
                Export Excel
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={loadStats}
                disabled={isLoading}
              >
                {isLoading ? 'Loading...' : 'Refresh'}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white p-6 rounded-lg shadow border border-gray-200"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900">
                R$ {stats.total_revenue.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
              </p>
              <div className="flex items-center mt-2">
                {stats.revenue_growth >= 0 ? (
                  <ArrowUpIcon className="h-4 w-4 text-green-600 mr-1" />
                ) : (
                  <ArrowDownIcon className="h-4 w-4 text-red-600 mr-1" />
                )}
                <span className={`text-sm ${stats.revenue_growth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {Math.abs(stats.revenue_growth)}%
                </span>
                <span className="text-sm text-gray-500 ml-1">vs last period</span>
              </div>
            </div>
            <CurrencyDollarIcon className="h-8 w-8 text-green-600" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white p-6 rounded-lg shadow border border-gray-200"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Orders</p>
              <p className="text-2xl font-bold text-gray-900">{stats.total_orders.toLocaleString()}</p>
              <div className="flex items-center mt-2">
                {stats.orders_growth >= 0 ? (
                  <ArrowUpIcon className="h-4 w-4 text-green-600 mr-1" />
                ) : (
                  <ArrowDownIcon className="h-4 w-4 text-red-600 mr-1" />
                )}
                <span className={`text-sm ${stats.orders_growth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {Math.abs(stats.orders_growth)}%
                </span>
                <span className="text-sm text-gray-500 ml-1">vs last period</span>
              </div>
            </div>
            <ShoppingBagIcon className="h-8 w-8 text-blue-600" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white p-6 rounded-lg shadow border border-gray-200"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Customers</p>
              <p className="text-2xl font-bold text-gray-900">{stats.total_customers.toLocaleString()}</p>
              <div className="flex items-center mt-2">
                {stats.customers_growth >= 0 ? (
                  <ArrowUpIcon className="h-4 w-4 text-green-600 mr-1" />
                ) : (
                  <ArrowDownIcon className="h-4 w-4 text-red-600 mr-1" />
                )}
                <span className={`text-sm ${stats.customers_growth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {Math.abs(stats.customers_growth)}%
                </span>
                <span className="text-sm text-gray-500 ml-1">vs last period</span>
              </div>
            </div>
            <UserGroupIcon className="h-8 w-8 text-purple-600" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white p-6 rounded-lg shadow border border-gray-200"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Order Value</p>
              <p className="text-2xl font-bold text-gray-900">
                R$ {stats.avg_order_value.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
              </p>
              <div className="flex items-center mt-2">
                <span className="text-sm text-gray-600">Conversion Rate:</span>
                <span className="text-sm font-medium text-gray-900 ml-1">{stats.conversion_rate}%</span>
              </div>
            </div>
            <ChartBarIcon className="h-8 w-8 text-orange-600" />
          </div>
        </motion.div>
      </div>

      {/* Charts and Tables Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Selling Products */}
        <Card>
          <CardHeader>
            <CardTitle>Top Selling Products</CardTitle>
            <CardDescription>Best performing products across both markets</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats.top_selling_products.map((product, index) => (
                <div key={product.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-gray-600">#{index + 1}</span>
                      </div>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">{product.name}</p>
                      <p className="text-sm text-gray-500">{product.vendor_name}</p>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge className={getMarketTypeColor(product.market_type)}>
                          {product.market_type}
                        </Badge>
                        <span className="text-xs text-gray-500">{product.units_sold} units sold</span>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">
                      R$ {product.revenue.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                    </p>
                    <p className="text-xs text-gray-500">{product.total_sales} sales</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Top Vendors */}
        <Card>
          <CardHeader>
            <CardTitle>Top Performing Vendors</CardTitle>
            <CardDescription>Highest revenue generating vendors</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats.top_vendors.map((vendor, index) => (
                <div key={vendor.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-gray-600">#{index + 1}</span>
                      </div>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">{vendor.business_name}</p>
                      <p className="text-sm text-gray-500">{vendor.name}</p>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge className={getMarketTypeColor(vendor.market_type)}>
                          {vendor.market_type}
                        </Badge>
                        <div className="flex items-center">
                          <StarIcon className="h-3 w-3 text-yellow-400 mr-1" />
                          <span className="text-xs text-gray-500">{vendor.rating}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">
                      R$ {vendor.total_revenue.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                    </p>
                    <p className="text-xs text-gray-500">{vendor.total_orders} orders</p>
                    <p className="text-xs text-green-600">
                      Commission: R$ {vendor.commission_earned.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Revenue Trends and Recent Orders */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Monthly Revenue Chart */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Revenue Trends</CardTitle>
            <CardDescription>Monthly revenue breakdown by market type</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats.revenue_by_month.map((month) => (
                <div key={month.month} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-900">{month.month}</span>
                    <span className="text-sm font-medium text-gray-900">
                      R$ {month.revenue.toLocaleString('pt-BR')}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="flex h-2 rounded-full overflow-hidden">
                      <div
                        className="bg-blue-500"
                        style={{ width: `${(month.b2b_revenue / month.revenue) * 100}%` }}
                      ></div>
                      <div
                        className="bg-green-500"
                        style={{ width: `${(month.public_revenue / month.revenue) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>B2B: R$ {month.b2b_revenue.toLocaleString('pt-BR')}</span>
                    <span>Public: R$ {month.public_revenue.toLocaleString('pt-BR')}</span>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-6 flex items-center justify-center space-x-6">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                <span className="text-sm text-gray-600">B2B Revenue</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                <span className="text-sm text-gray-600">Public Revenue</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recent Orders */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Orders</CardTitle>
            <CardDescription>Latest order activity</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats.recent_orders.map((order) => (
                <div key={order.id} className="p-3 border rounded-lg">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <p className="text-sm font-medium text-gray-900">{order.order_number}</p>
                      <p className="text-xs text-gray-500">{order.customer_name}</p>
                    </div>
                    <Badge className={getStatusColor(order.status)}>
                      {order.status}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-xs text-gray-500">{order.vendor_name}</p>
                      <Badge className={getMarketTypeColor(order.market_type)}>
                        {order.market_type}
                      </Badge>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">
                        R$ {order.total_amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                      </p>
                      <p className="text-xs text-gray-500">
                        {new Date(order.created_at).toLocaleDateString('pt-BR')}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4">
              <Button variant="outline" className="w-full">
                <EyeIcon className="h-4 w-4 mr-2" />
                View All Orders
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Additional Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Market Distribution</CardTitle>
            <CardDescription>Revenue split by market type</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                  <span className="text-sm text-gray-600">B2B Market</span>
                </div>
                <span className="text-sm font-medium text-gray-900">65%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-blue-500 h-2 rounded-full" style={{ width: '65%' }}></div>
              </div>

              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                  <span className="text-sm text-gray-600">Public Market</span>
                </div>
                <span className="text-sm font-medium text-gray-900">35%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-green-500 h-2 rounded-full" style={{ width: '35%' }}></div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Vendor Performance</CardTitle>
            <CardDescription>Active vendor statistics</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Total Vendors</span>
                <span className="text-sm font-medium text-gray-900">{stats.total_vendors}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Active This Month</span>
                <span className="text-sm font-medium text-gray-900">{Math.floor(stats.total_vendors * 0.85)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Avg Rating</span>
                <div className="flex items-center">
                  <StarIcon className="h-4 w-4 text-yellow-400 mr-1" />
                  <span className="text-sm font-medium text-gray-900">4.7</span>
                </div>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">New This Month</span>
                <span className="text-sm font-medium text-green-600">+12</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>System Health</CardTitle>
            <CardDescription>Platform performance metrics</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Uptime</span>
                <span className="text-sm font-medium text-green-600">99.9%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Avg Response Time</span>
                <span className="text-sm font-medium text-gray-900">245ms</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Error Rate</span>
                <span className="text-sm font-medium text-gray-900">0.1%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Active Sessions</span>
                <span className="text-sm font-medium text-gray-900">1,247</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}