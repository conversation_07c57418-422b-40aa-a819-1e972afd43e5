"""
API endpoints for translation change management.
"""

import logging
from typing import List, Annotated, Any, Optional

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.user_auth import get_current_active_user
from app.modules.core.auth.dependencies.role_auth import require_system_role
# from app.modules.core.users.models.user import User  # Removido para evitar import circular
from app.modules.core.roles.models.roles import SystemRole

from app.modules.core.i18n.schemas.translation_change import (
    TranslationChangeFilter,
    LanguageChanges,
)
from app.modules.core.i18n.services.translation_change_service import TranslationChangeService

# Set up logger
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# Define required roles
admin_roles = [SystemRole.ADMIN]


@router.get("/changes", response_model=LanguageChanges)
async def get_translation_changes(
    language_code: str = Query(..., description="Language code"),
    since_version: str = Query(
        ..., min_length=6, max_length=6, description="Get changes since this version"
    ),
    sector: Optional[str] = Query(None, description="Filter by sector"),
    db: AsyncSession = Depends(get_db),
):
    """
    Get all translation changes for a language since a specific version.
    This endpoint is public and does not require authentication.

    Returns a JSON object with:
    - language_code: The language code
    - current_version_code: The current version code on the server
    - has_changes: Boolean indicating if there are changes
    - sectors: List of sectors with their changes
    """
    change_service = TranslationChangeService()

    try:
        result = await change_service.get_changes_since_version(
            db, language_code, since_version, sector
        )
        return result
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"Error getting translation changes: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error getting translation changes",
        )


@router.get("/by-sector", response_model=List[dict])
async def get_translations_by_sector(
    language_code: str = Query(..., description="Language code"),
    sector: Optional[str] = Query(None, description="Filter by sector"),
    db: AsyncSession = Depends(get_db),
):
    """
    Get all translations for a language, grouped by sector.
    This endpoint is public and does not require authentication.

    Returns a list of sectors with their translations.
    """
    change_service = TranslationChangeService()

    try:
        result = await change_service.get_translations_by_sector(db, language_code, sector)
        return result
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"Error getting translations by sector: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error getting translations by sector",
        )
