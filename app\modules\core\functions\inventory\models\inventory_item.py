import uuid
from sqlalchemy import (
    Column,
    String,
    Integer,
    Numeric,
    ForeignKey,
    Index,
    Boolean,
    Text
)  # Removed unused Table
from sqlalchemy.orm import relationship  # noqa: E402
from sqlalchemy.dialects.postgresql import UUID

from typing import TYPE_CHECKING  # noqa: E402

from app.db.base import (  # noqa: E402
    Base,
    menu_item_inventory_association,
    modifier_option_inventory_association,
)

# Use TYPE_CHECKING to avoid circular import for type hints
if TYPE_CHECKING:
    from app.models.tenant import Tenant  # noqa: F401  # noqa: E402


class InventoryCategory(Base):
    """Modelo para categorias de inventário."""
    __tablename__ = "inventory_categories"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False)

    # Category info
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    display_order = Column(Integer, nullable=False, default=1)
    is_active = Column(Boolean, default=True, nullable=False)

    # Relationships
    tenant = relationship("Tenant", back_populates="inventory_categories")
    inventory_items = relationship("InventoryItem", back_populates="category")

    __table_args__ = (
        Index("ix_inventory_categories_tenant_id", "tenant_id"),
        Index("ix_inventory_categories_name", "name"),
        Index("ix_inventory_categories_display_order", "display_order"),
    )


class InventoryItem(Base):  # TimestampMixin já está em Base
    __tablename__ = "inventory_items"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    # index=True removido, definido em __table_args__
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False)
    category_id = Column(UUID(as_uuid=True), ForeignKey("inventory_categories.id"), nullable=True)
    
    product_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    variant_id = Column(UUID(as_uuid=True), nullable=True, index=True)

    name = Column(String, nullable=False)
    description = Column(String, nullable=True)
    # Stock Keeping Unit, index=True removido, definido em __table_args__
    sku = Column(String, nullable=True)
    quantity = Column(Integer, nullable=False, default=0)
    low_stock_threshold = Column(Integer, nullable=False, default=0)
    unit_cost = Column(Numeric(10, 2), nullable=True)  # Example precision

    tenant = relationship("Tenant", back_populates="inventory_items")
    category = relationship("InventoryCategory", back_populates="inventory_items")

    # Many-to-Many relationship with MenuItem
    menu_items = relationship(
        "MenuItem",
        secondary=menu_item_inventory_association,
        back_populates="inventory_items",
        # Updated joins for UUID
        primaryjoin="and_(InventoryItem.id == menu_item_inventory_association.c.inventory_item_id, InventoryItem.tenant_id == menu_item_inventory_association.c.tenant_id)",  # noqa: E501
        secondaryjoin="and_(MenuItem.id == menu_item_inventory_association.c.menu_item_id, MenuItem.tenant_id == menu_item_inventory_association.c.tenant_id)",  # noqa: E501
        viewonly=True,  # Usually managed elsewhere
    )

    # Many-to-Many relationship with ModifierOption
    modifier_options = relationship(
        "ModifierOption",
        secondary=modifier_option_inventory_association,
        back_populates="inventory_items",
        # Updated joins for UUID
        primaryjoin="and_(InventoryItem.id == modifier_option_inventory_association.c.inventory_item_id, InventoryItem.tenant_id == modifier_option_inventory_association.c.tenant_id)",  # noqa: E501
        secondaryjoin="and_(ModifierOption.id == modifier_option_inventory_association.c.modifier_option_id, ModifierOption.tenant_id == modifier_option_inventory_association.c.tenant_id)",  # noqa: E501
        viewonly=True,  # Usually managed elsewhere
    )

    __table_args__ = (
        Index("ix_inventory_items_tenant_id", "tenant_id"),
        Index("ix_inventory_items_category_id", "category_id"),
        Index("ix_inventory_items_sku", "sku"),
    )
