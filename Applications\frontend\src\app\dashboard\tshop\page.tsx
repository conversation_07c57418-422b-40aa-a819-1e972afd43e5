'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/lib/auth/AuthProvider';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ShoppingCartIcon, 
  BuildingOfficeIcon, 
  CurrencyDollarIcon,
  ChartBarIcon,
  DocumentTextIcon,
  UserGroupIcon,
  StarIcon,
  TruckIcon
} from '@heroicons/react/24/outline';
import { TShopProductGrid } from './components/TShopProductGrid';
import { TShopFloatingCart } from './components/TShopFloatingCart';
import { TShopCartSidebar } from './components/TShopCartSidebar';
import { SupplierDirectory } from './components/SupplierDirectory';
import { BulkOrderModal } from './components/BulkOrderModal';
import { OrderHistory } from './components/OrderHistory';
import { InvoiceViewer } from './components/InvoiceViewer';
import { TShopCartProvider } from '@/contexts/TShopCartContext';
import { useTenant } from '@/contexts/TenantContext';
import { toast } from 'sonner';

export default function TShopPage() {
  const { user, isAuthenticated, hasRole } = useAuth();
  const { tenant } = useTenant();
  const [activeTab, setActiveTab] = useState('products');
  const [showBulkOrder, setShowBulkOrder] = useState(false);

  // Check if user has B2B access
  const hasB2BAccess = hasRole('tcostumer') && isAuthenticated;

  useEffect(() => {
    if (!hasB2BAccess) {
      toast.error('Acesso negado. Você precisa ser um cliente B2B autorizado.');
    }
  }, [hasB2BAccess]);

  if (!hasB2BAccess) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <BuildingOfficeIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <CardTitle>Acesso Restrito</CardTitle>
            <CardDescription>
              Esta área é exclusiva para clientes B2B autorizados.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-sm text-gray-600 mb-4">
              Para acessar o TShop, você precisa:
            </p>
            <ul className="text-sm text-gray-600 text-left space-y-2 mb-6">
              <li>• Ser proprietário de um tenant</li>
              <li>• Ter autorização B2B aprovada</li>
              <li>• Possuir role TCostumer ativo</li>
            </ul>
            <Button 
              onClick={() => window.location.href = '/dashboard'}
              className="w-full"
            >
              Voltar ao Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <TShopCartProvider>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <BuildingOfficeIcon className="h-8 w-8 text-indigo-600" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">TShop B2B</h1>
                  <p className="text-sm text-gray-600">
                    Marketplace exclusivo para proprietários de tenants
                  </p>
                </div>
                <Badge variant="secondary" className="bg-indigo-100 text-indigo-800">
                  B2B Autorizado
                </Badge>
              </div>
              
              <div className="flex items-center space-x-4">
                <Button
                  variant="outline"
                  onClick={() => setShowBulkOrder(true)}
                  className="hidden sm:flex"
                >
                  <DocumentTextIcon className="h-4 w-4 mr-2" />
                  Pedido em Lote
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <CurrencyDollarIcon className="h-8 w-8 text-green-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Economia Total</p>
                    <p className="text-2xl font-bold text-gray-900">R$ 12.450</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <ShoppingCartIcon className="h-8 w-8 text-blue-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Pedidos Este Mês</p>
                    <p className="text-2xl font-bold text-gray-900">24</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <UserGroupIcon className="h-8 w-8 text-purple-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Fornecedores</p>
                    <p className="text-2xl font-bold text-gray-900">18</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <StarIcon className="h-8 w-8 text-yellow-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Avaliação Média</p>
                    <p className="text-2xl font-bold text-gray-900">4.8</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="products">Produtos</TabsTrigger>
              <TabsTrigger value="suppliers">Fornecedores</TabsTrigger>
              <TabsTrigger value="orders">Pedidos</TabsTrigger>
              <TabsTrigger value="invoices">Faturas</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </TabsList>

            <TabsContent value="products" className="space-y-6">
              <TShopProductGrid />
            </TabsContent>

            <TabsContent value="suppliers" className="space-y-6">
              <SupplierDirectory />
            </TabsContent>

            <TabsContent value="orders" className="space-y-6">
              <OrderHistory />
            </TabsContent>

            <TabsContent value="invoices" className="space-y-6">
              <InvoiceViewer />
            </TabsContent>

            <TabsContent value="analytics" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <ChartBarIcon className="h-5 w-5 mr-2" />
                    Analytics B2B
                  </CardTitle>
                  <CardDescription>
                    Análise detalhada dos seus pedidos e economia
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12">
                    <ChartBarIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">Analytics em desenvolvimento</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Cart Components */}
        <TShopCartSidebar />
        <TShopFloatingCart />

        {/* Modals */}
        {showBulkOrder && (
          <BulkOrderModal 
            isOpen={showBulkOrder}
            onClose={() => setShowBulkOrder(false)}
          />
        )}
      </div>
    </TShopCartProvider>
  );
}
