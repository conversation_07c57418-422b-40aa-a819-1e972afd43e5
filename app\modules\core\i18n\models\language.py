"""
Language model for the i18n module.
"""

import uuid
import random
import string
from sqlalchemy import Column, String, Boolean, Index
from sqlalchemy.dialects.postgresql import UUID

from app.db.base import Base


def generate_version_code():
    """
    Gera um código de versão aleatório de 6 caracteres (letras e números).
    """
    chars = string.ascii_lowercase + string.digits
    return "".join(random.choice(chars) for _ in range(6))


class Language(Base):
    """
    Model for storing supported languages.
    """

    __tablename__ = "languages"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    code = Column(String(10), nullable=False, unique=True, index=True)
    name = Column(String(100), nullable=False)
    native_name = Column(String(100), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_default = Column(Boolean, default=False, nullable=False)
    version_code = Column(String(6), nullable=False, default=generate_version_code)

    __table_args__ = (Index("ix_languages_code", "code"),)

    def __repr__(self):
        return f"<Language(id={self.id}, code='{self.code}', name='{self.name}', version_code='{self.version_code}')>"
