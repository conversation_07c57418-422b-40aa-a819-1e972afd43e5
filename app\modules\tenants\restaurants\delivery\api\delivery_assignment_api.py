from typing import List, Optional, Dict, Any, Annotated  # Adicionar Annotated
import uuid
from fastapi import APIRouter, Depends, HTTPException, status, Query, Body
from pydantic import BaseModel  # Importar BaseModel

from sqlalchemy.ext.asyncio import AsyncSession

# Atualizar importações
from app.db.session import get_db as get_async_db  # Usar get_db consistentemente
from app.core.security import (
    get_current_active_user,
    get_current_tenant_from_header,  # Usar esta para obter o tenant
    require_tenant_role,
    require_tenant_sub_role,  # Nova dependência
)
from app.models.user import User  # noqa: E402
from app.models.tenant import Tenant as TenantModel
from app.models.tenant_user_association import (
    TenantUserAssociation as TenantUserAssociationModel,
)  # Adicionado
from app.core.roles import TenantRole, TenantStaffSubRole, TenantType  # noqa: E402
from app.modules.tenants.restaurants.delivery.models.delivery_boy import (
    DeliveryBoy,
)  # Adicionado

from app.modules.tenants.restaurants.delivery.schemas.delivery_assignment import (  # noqa: E402
    # Removed unused import: DeliveryAssignmentCreate,
    DeliveryAssignmentRead,
    # Removed unused import: DeliveryAssignmentUpdate,
)
from app.modules.tenants.restaurants.delivery.services.delivery_assignment_service import (  # noqa: E402
    DeliveryAssignmentService,
)
from app.modules.tenants.restaurants.delivery.enums import DeliveryAssignmentStatus  # noqa: E402
from app.core.exceptions import NotFoundError, BusinessLogicError  # Corrigido
from app.modules.tenants.restaurants.services.online_order_service import (
    online_order_service,
)  # For available orders

# For response model of available orders
from app.modules.tenants.restaurants.schemas.online_order import OnlineOrderRead  # noqa: E402

# For delivery boy specific checks
from app.modules.tenants.restaurants.delivery.services.delivery_boy_service import (  # noqa: E402
    DeliveryBoyService,
)


router = APIRouter(
    prefix="/restaurants/delivery/assignments",
    tags=["Restaurant - Delivery Assignments"],
)

# Placeholder for a Pydantic model for status update if more fields are needed


class DeliveryAssignmentStatusUpdate(BaseModel):
    status: DeliveryAssignmentStatus
    # user_id: Optional[uuid.UUID] = None # Could be passed if needed for audit


@router.post(
    "/",
    response_model=DeliveryAssignmentRead,
    status_code=status.HTTP_201_CREATED,
    # dependencies=[Depends(require_tenant_role([TenantRole.MANAGER,
    # TenantRole.STAFF]))] # Substituído
)
async def assign_order_to_delivery_boy_endpoint(
    # Parâmetros do corpo da requisição
    online_order_id: Annotated[uuid.UUID, Body(...)],
    delivery_boy_id: Annotated[uuid.UUID, Body(...)],
    delivery_address_snapshot: Annotated[Dict[str, Any], Body(...)],
    # Dependências
    db: Annotated[AsyncSession, Depends(get_async_db)],
    current_tenant: Annotated[TenantModel, Depends(get_current_tenant_from_header)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    _: Annotated[
        Any,
        Depends(
            require_tenant_role(
                required_roles=[
                    TenantRole.OWNER,
                    TenantRole.MANAGER,
                ]  # Apenas OWNER/MANAGER podem atribuir
            )
        ),
    ],
):
    """
    Assign an online order to a delivery boy.
    Requires MANAGER or STAFF role.
    """
    service = DeliveryAssignmentService()
    try:
        # The DeliveryAssignmentCreate schema expects tenant_id, online_order_id, delivery_boy_id, delivery_address_snapshot  # noqa: E501
        # We construct it here or modify the service to accept these params directly.
        # For now, passing directly to service method as defined.
        assignment = await service.assign_order_to_delivery_boy(
            db,
            tenant_id=current_tenant.id,  # Usar ID do tenant obtido pela dependência
            online_order_id=online_order_id,
            delivery_boy_id=delivery_boy_id,
            delivery_address_snapshot=delivery_address_snapshot,
        )
        return assignment
    except BusinessLogicError as e:  # Corrigido
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except NotFoundError as e:  # Corrigido
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))


@router.put(
    "/{assignment_id}/status",
    response_model=DeliveryAssignmentRead,
    # Add custom dependency: Depends(require_tenant_role_or_self_for_delivery)
    # This dependency would check if user is manager/staff OR if current_user.id is linked to the delivery_boy_id of the assignment.  # noqa: E501
    # For now, let's assume a simpler permission for staff/manager or specific delivery boy role.
    # We'll use a placeholder for now, actual implementation of require_tenant_role_or_self_for_delivery is complex.  # noqa: E501
    # dependencies=[Depends(require_tenant_role([TenantRole.MANAGER, TenantRole.STAFF]))] # Removido, será mais granular  # noqa: E501
)
async def update_delivery_assignment_status_endpoint(
    assignment_id: uuid.UUID,
    status_update: DeliveryAssignmentStatusUpdate,
    db: Annotated[AsyncSession, Depends(get_async_db)],
    current_tenant: Annotated[TenantModel, Depends(get_current_tenant_from_header)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    # A permissão aqui é complexa: OWNER/MANAGER podem sempre,
    # ou o DELIVERY_PERSON *atribuído* pode.
    # Esta lógica será parcialmente na rota e parcialmente na dependência `require_tenant_sub_role`
    # Se o usuário for OWNER/MANAGER, a dependência de sub-role já permite.
    # Se for STAFF/DELIVERY_PERSON, precisamos verificar se é o entregador atribuído.
    # A dependência require_tenant_sub_role([TenantStaffSubRole.DELIVERY_PERSON], ...) não é suficiente sozinha.  # noqa: E501
    # Vamos simplificar por agora: OWNER, MANAGER ou STAFF(DELIVERY_PERSON) podem atualizar.
    # A lógica de "apenas o entregador atribuído" seria melhor no serviço ou uma dependência customizada.  # noqa: E501
    # _: Annotated[Any, Depends(require_tenant_sub_role(
    # required_sub_roles=[TenantStaffSubRole.DELIVERY_PERSON], # DELIVERY_PERSON pode atualizar seus status  # noqa: E501
    # tenant_type=TenantType.RESTAURANT
    # ))] # Esta é uma simplificação. A lógica real é mais complexa.
):
    """
    Update the status of a delivery assignment.
    Accessible by MANAGER, STAFF, or the assigned DELIVERY_PERSON (via custom permission logic).
    """
    service = DeliveryAssignmentService()

    # Permissão: OWNER, MANAGER ou o DELIVERY_PERSON atribuído.
    # A dependência require_tenant_sub_role com DELIVERY_PERSON já cobre OWNER/MANAGER.
    # A lógica para verificar se é o *entregador atribuído* precisa ser explícita.

    from sqlalchemy import select  # noqa: E402

    association = await db.execute(
        select(TenantUserAssociationModel).where(
            TenantUserAssociationModel.user_id == current_user.id,
            TenantUserAssociationModel.tenant_id == current_tenant.id,
        )
    )
    user_assoc = association.scalar_one_or_none()

    if not user_assoc:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User not associated with this tenant.",
        )

    is_owner_or_manager = user_assoc.role in [TenantRole.OWNER, TenantRole.MANAGER]
    is_delivery_person_staff = (
        user_assoc.role == TenantRole.STAFF
        and user_assoc.staff_sub_role == TenantStaffSubRole.DELIVERY_PERSON
    )

    can_update = False
    if is_owner_or_manager:
        can_update = True
    elif is_delivery_person_staff:
        # Verificar se este entregador é o atribuído à tarefa
        assignment_for_check = await service.get_assignment_by_id(
            db, assignment_id, current_tenant.id
        )
        if assignment_for_check and assignment_for_check.delivery_boy.user_id == current_user.id:
            can_update = True

    if not can_update:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update this assignment status.",
        )

    try:
        updated_assignment = await service.update_assignment_status(
            db,
            assignment_id=assignment_id,
            tenant_id=current_tenant.id,  # Usar ID do tenant obtido pela dependência
            new_status=status_update.status,
            user_id=current_user.id,  # For audit trail
        )
        return updated_assignment
    except NotFoundError as e:  # Corrigido
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except BusinessLogicError as e:  # Corrigido
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get(
    "/delivery-boy/{delivery_boy_id}",
    response_model=List[DeliveryAssignmentRead],
    # Add custom dependency: Depends(require_tenant_role_or_self_for_delivery_boy_listing)
    # Similar to above, manager/staff or the delivery_boy_id matches current_user's delivery_boy profile.  # noqa: E501
    # dependencies=[Depends(require_tenant_role([TenantRole.MANAGER, TenantRole.STAFF]))] # Simplified  # noqa: E501
)
async def get_assignments_for_delivery_boy_endpoint(
    delivery_boy_id: uuid.UUID,
    # Reordenar parâmetros
    db: Annotated[AsyncSession, Depends(get_async_db)],
    current_tenant: Annotated[TenantModel, Depends(get_current_tenant_from_header)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    # Query parameters
    status_filter: Optional[DeliveryAssignmentStatus] = Query(None, alias="status"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=200),
    # Permissão: OWNER, MANAGER, ou o próprio DELIVERY_PERSON.
):
    """
    Get all assignments for a specific delivery boy within the current tenant.
    Accessible by MANAGER, STAFF, or the delivery boy himself.
    """
    service = DeliveryAssignmentService()

    # Permissão: OWNER, MANAGER ou o próprio DELIVERY_PERSON.
    # Importar aqui para evitar import circular a nível de módulo
    # DeliveryBoy is already imported at the top of the file
    from sqlalchemy import select  # noqa: E402

    association = await db.execute(
        select(TenantUserAssociationModel).where(
            TenantUserAssociationModel.user_id == current_user.id,
            TenantUserAssociationModel.tenant_id == current_tenant.id,
        )
    )
    user_assoc = association.scalar_one_or_none()

    if not user_assoc:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User not associated with this tenant.",
        )

    is_owner_or_manager = user_assoc.role in [TenantRole.OWNER, TenantRole.MANAGER]

    can_view = False
    if is_owner_or_manager:
        can_view = True
    else:  # Check if STAFF and DELIVERY_PERSON and if it's their own assignments
        if (
            user_assoc.role == TenantRole.STAFF
            and user_assoc.staff_sub_role == TenantStaffSubRole.DELIVERY_PERSON
        ):
            # Check if the delivery_boy_id in path corresponds to the current_user
            delivery_boy_record = await db.execute(
                select(DeliveryBoy).where(
                    DeliveryBoy.id == delivery_boy_id,
                    DeliveryBoy.tenant_id == current_tenant.id,
                )
            )
            target_db_boy = delivery_boy_record.scalar_one_or_none()
            if target_db_boy and target_db_boy.user_id == current_user.id:
                can_view = True

    if not can_view:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to view these assignments.",
        )

    assignments = await service.get_assignments_for_delivery_boy(
        db,
        tenant_id=current_tenant.id,  # Usar ID do tenant obtido pela dependência
        delivery_boy_id=delivery_boy_id,
        status=status_filter,
        skip=skip,
        limit=limit,
    )
    return assignments


@router.get(
    "/order/{online_order_id}",
    response_model=DeliveryAssignmentRead,
    # dependencies=[Depends(require_tenant_role([TenantRole.MANAGER,
    # TenantRole.STAFF, TenantRole.OWNER]))] # Substituído
)
async def get_assignment_for_order_endpoint(
    online_order_id: uuid.UUID,
    db: Annotated[AsyncSession, Depends(get_async_db)],
    current_tenant: Annotated[TenantModel, Depends(get_current_tenant_from_header)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    _: Annotated[
        Any,
        Depends(
            require_tenant_role(
                required_roles=[
                    TenantRole.OWNER,
                    TenantRole.MANAGER,
                    TenantRole.STAFF,
                ]  # STAFF pode ver atribuição de pedido
            )
        ),
    ],
):
    """
    Get the delivery assignment details for a specific online order.
    Requires MANAGER, STAFF or OWNER role.
    """
    service = DeliveryAssignmentService()
    assignment = await service.get_assignment_by_order_id(db, current_tenant.id, online_order_id)
    if not assignment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Delivery assignment not found for this order",
        )
    return assignment


@router.get(
    "/available-orders",
    response_model=List[OnlineOrderRead],
    # dependencies=[Depends(require_tenant_role([TenantRole.STAFF],
    # sub_roles=[TenantStaffSubRole.DELIVERY_PERSON]))] # Substituído
)
async def list_available_orders_for_delivery(
    # Reordenar parâmetros
    db: Annotated[AsyncSession, Depends(get_async_db)],
    current_tenant: Annotated[TenantModel, Depends(get_current_tenant_from_header)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    _: Annotated[
        Any,
        Depends(
            require_tenant_sub_role(
                required_sub_roles=[TenantStaffSubRole.DELIVERY_PERSON],  # Apenas DELIVERY_PERSON
                tenant_type=TenantType.RESTAURANT,
            )
        ),
    ],
    # Query parameters
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=200),
):
    """
    List online orders available for delivery assignment.
    Accessible by users with DELIVERY_PERSON sub_role.
    """
    # Further check: ensure current_user is an active DeliveryBoy
    delivery_boy_service = DeliveryBoyService()
    # Use the new method to get delivery_boy by user_id
    delivery_boy_profile = await delivery_boy_service.get_delivery_boy_by_user_id(
        db, user_id=current_user.id, tenant_id=current_tenant.id
    )

    if (
        not delivery_boy_profile or not delivery_boy_profile.is_active
    ):  # is_active já está no modelo DeliveryBoy
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User is not an active delivery boy or profile not found.",
        )

    orders = await online_order_service.get_available_online_orders_for_delivery(
        db,
        tenant_id=current_tenant.id,
        skip=skip,
        limit=limit,  # Usar ID do tenant obtido pela dependência
    )
    return orders


@router.post(
    "/claim/{online_order_id}",
    response_model=DeliveryAssignmentRead,
    status_code=status.HTTP_201_CREATED,
    # dependencies=[Depends(require_tenant_role([TenantRole.STAFF],
    # sub_roles=[TenantStaffSubRole.DELIVERY_PERSON]))] # Substituído
)
async def claim_order_for_delivery_endpoint(
    online_order_id: uuid.UUID,
    db: Annotated[AsyncSession, Depends(get_async_db)],
    current_tenant: Annotated[TenantModel, Depends(get_current_tenant_from_header)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    _: Annotated[
        Any,
        Depends(
            require_tenant_sub_role(
                required_sub_roles=[TenantStaffSubRole.DELIVERY_PERSON],  # Apenas DELIVERY_PERSON
                tenant_type=TenantType.RESTAURANT,
            )
        ),
    ],
):
    """
    Allows an authenticated delivery boy to claim an available online order for delivery.
    Requires DELIVERY_PERSON sub_role.
    """
    service = DeliveryAssignmentService()
    delivery_boy_service = DeliveryBoyService()

    # Verify the current user is an active delivery boy
    delivery_boy_profile = await delivery_boy_service.get_delivery_boy_by_user_id(
        db, user_id=current_user.id, tenant_id=current_tenant.id
    )
    if (
        not delivery_boy_profile or not delivery_boy_profile.is_active
    ):  # is_active já está no modelo DeliveryBoy
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User is not an active delivery boy or profile not found.",
        )

    try:
        assignment = await service.claim_order_for_delivery(
            db=db,
            tenant_id=current_tenant.id,  # Usar ID do tenant obtido pela dependência
            online_order_id=online_order_id,
            delivery_boy_user_id=current_user.id,
        )
        return assignment
    except NotFoundError as e:  # Corrigido
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except BusinessLogicError as e:  # Corrigido
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
