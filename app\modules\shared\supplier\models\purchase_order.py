"""Purchase Order models for supplier system."""

import uuid
import enum
from datetime import date, datetime
from decimal import Decimal
from typing import TYPE_CHECKING, List, Optional
from sqlalchemy import (
    Column, String, ForeignKey, Text, Boolean, Index,
    DateTime, Date, Numeric, Integer
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base

# Use TYPE_CHECKING to handle circular dependencies for type hinting
if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.core.users.models.user import User
    from app.modules.shared.supplier.models.supplier import Supplier
    from app.modules.shared.shopping_list.models.shopping_list import ShoppingList
    from app.modules.shared.financial.invoices.models.invoice import Invoice


class PurchaseOrderStatus(str, enum.Enum):
    """Enum for purchase order status."""
    
    DRAFT = "draft"
    PENDING = "pending"
    SENT = "sent"
    CONFIRMED = "confirmed"
    PARTIALLY_DELIVERED = "partially_delivered"
    DELIVERED = "delivered"
    INVOICED = "invoiced"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class PurchaseOrderItemStatus(str, enum.Enum):
    """Enum for purchase order item status."""
    
    PENDING = "pending"
    CONFIRMED = "confirmed"
    UNAVAILABLE = "unavailable"
    DELIVERED = "delivered"
    CANCELLED = "cancelled"


class PurchaseOrder(Base):
    """
    Model for purchase orders sent to suppliers.
    
    Purchase orders are generated from shopping lists and sent to suppliers.
    Suppliers can confirm availability and delivery details.
    """
    
    __tablename__ = "purchase_orders"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("tenants.id"), 
        nullable=False, 
        index=True
    )
    
    # Purchase order identification
    order_number = Column(String(50), nullable=False, index=True)
    status = Column(
        String(30), 
        nullable=False, 
        default=PurchaseOrderStatus.DRAFT.value
    )
    
    # Supplier information
    supplier_id = Column(
        UUID(as_uuid=True),
        ForeignKey("suppliers.id"),
        nullable=False,
        index=True
    )
    
    # Shopping list reference
    shopping_list_id = Column(
        UUID(as_uuid=True),
        ForeignKey("shopping_lists.id"),
        nullable=True,
        index=True
    )
    
    # Dates
    order_date = Column(Date, nullable=False, default=date.today)
    requested_delivery_date = Column(Date, nullable=True)
    confirmed_delivery_date = Column(Date, nullable=True)
    actual_delivery_date = Column(Date, nullable=True)
    
    # Financial details
    subtotal = Column(
        Numeric(10, 2), 
        nullable=False, 
        default=Decimal('0.00')
    )
    tax_amount = Column(
        Numeric(10, 2), 
        nullable=False, 
        default=Decimal('0.00')
    )
    total_amount = Column(
        Numeric(10, 2), 
        nullable=False, 
        default=Decimal('0.00')
    )
    
    # Delivery information
    delivery_address = Column(Text, nullable=True)
    delivery_notes = Column(Text, nullable=True)
    
    # Invoice information
    supplier_invoice_number = Column(String(100), nullable=True)
    supplier_delivery_note = Column(String(100), nullable=True)
    
    # Additional information
    notes = Column(Text, nullable=True)
    internal_notes = Column(Text, nullable=True)
    
    # System fields
    created_by = Column(
        UUID(as_uuid=True), 
        ForeignKey("users.id"), 
        nullable=False
    )
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )
    
    # Relationships
    tenant = relationship(
        "app.modules.core.tenants.models.tenant.Tenant",
        viewonly=True
    )
    
    supplier = relationship(
        "app.modules.shared.supplier.models.supplier.Supplier"
    )
    
    shopping_list = relationship(
        "app.modules.shared.shopping_list.models.shopping_list.ShoppingList",
        viewonly=True
    )
    
    created_by_user = relationship(
        "app.modules.core.users.models.user.User",
        foreign_keys=[created_by],
        viewonly=True
    )
    
    # Purchase order items (one-to-many relationship)
    items = relationship(
        "PurchaseOrderItem",
        back_populates="purchase_order",
        cascade="all, delete-orphan"
    )
    
    # Table indexes for performance
    __table_args__ = (
        Index("ix_purchase_orders_tenant_number", "tenant_id", "order_number"),
        Index("ix_purchase_orders_tenant_status", "tenant_id", "status"),
        Index("ix_purchase_orders_tenant_supplier", "tenant_id", "supplier_id"),
        Index("ix_purchase_orders_tenant_date", "tenant_id", "order_date"),
        # Unique constraint for order number per tenant
        Index("uq_purchase_orders_tenant_number", "tenant_id", "order_number", unique=True),
    )
    
    def __repr__(self):
        return (
            f"<PurchaseOrder(id={self.id}, "
            f"number='{self.order_number}', "
            f"status='{self.status}', "
            f"total={self.total_amount})>"
        )


class PurchaseOrderItem(Base):
    """
    Model for purchase order line items.
    
    Each purchase order can have multiple items with quantity, price, availability status.
    """
    
    __tablename__ = "purchase_order_items"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    purchase_order_id = Column(
        UUID(as_uuid=True),
        ForeignKey("purchase_orders.id"),
        nullable=False,
        index=True
    )
    
    # Item details
    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    quantity_requested = Column(
        Numeric(10, 3), 
        nullable=False, 
        default=Decimal('1.000')
    )
    quantity_confirmed = Column(
        Numeric(10, 3), 
        nullable=True
    )
    quantity_delivered = Column(
        Numeric(10, 3), 
        nullable=True
    )
    
    unit = Column(String(20), nullable=True)  # e.g., "pcs", "kg", "liters"
    unit_price = Column(
        Numeric(10, 2), 
        nullable=False, 
        default=Decimal('0.00')
    )
    total_price = Column(
        Numeric(10, 2), 
        nullable=False, 
        default=Decimal('0.00')
    )
    
    # Status and availability
    status = Column(
        String(20), 
        nullable=False, 
        default=PurchaseOrderItemStatus.PENDING.value
    )
    
    # Optional fields
    product_code = Column(String(50), nullable=True)
    notes = Column(Text, nullable=True)
    supplier_notes = Column(Text, nullable=True)
    
    # Inventory item reference
    inventory_item_id = Column(
        UUID(as_uuid=True),
        nullable=True,
        index=True
    )
    
    # Shopping list item reference
    shopping_list_item_id = Column(
        UUID(as_uuid=True),
        nullable=True,
        index=True
    )
    
    # Display order
    sort_order = Column(Integer, nullable=False, default=0)
    
    # Relationships
    purchase_order = relationship(
        "PurchaseOrder",
        back_populates="items"
    )
    
    # Table indexes for performance
    __table_args__ = (
        Index("ix_purchase_order_items_order_sort", "purchase_order_id", "sort_order"),
        Index("ix_purchase_order_items_status", "status"),
        Index("ix_purchase_order_items_inventory", "inventory_item_id"),
    )
    
    def __repr__(self):
        return (
            f"<PurchaseOrderItem(id={self.id}, "
            f"name='{self.name}', "
            f"quantity={self.quantity_requested}, "
            f"status='{self.status}')>"
        )
