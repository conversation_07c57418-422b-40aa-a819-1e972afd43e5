"""Financial Control Document Models."""

import uuid
import enum
from typing import TYPE_CHECKING
from sqlalchemy import (
    Column, String, ForeignKey, Text, Enum, <PERSON><PERSON><PERSON>, Integer, 
    DateTime, func, Index
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base

if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.core.users.models.user import User
    from app.modules.core.functions.media_system.models import MediaUpload
    from .control_entry import FinancialControlEntry


class DocumentType(str, enum.Enum):
    """Enum for document types."""
    
    INVOICE = "invoice"                    # Original invoice
    RECEIPT = "receipt"                    # Payment receipt
    PAYMENT_PROOF = "payment_proof"        # Bank transfer proof
    CONTRACT = "contract"                  # Service contract
    QUOTE = "quote"                       # Price quote
    DELIVERY_NOTE = "delivery_note"       # Delivery confirmation
    TAX_DOCUMENT = "tax_document"         # Tax-related documents
    BANK_STATEMENT = "bank_statement"     # Bank statements
    OTHER = "other"                       # Other documents


class ControlDocument(Base):
    """
    Model for financial control documents.
    
    Manages document attachments for financial control entries,
    integrating with the media system for file storage and management.
    """
    
    __tablename__ = "financial_control_documents"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("tenants.id"), 
        nullable=False, 
        index=True
    )
    
    # Document details
    document_type = Column(Enum(DocumentType), nullable=False, index=True)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    document_number = Column(String(100), nullable=True)  # Invoice number, etc.
    
    # Media system integration
    media_upload_id = Column(
        UUID(as_uuid=True),
        ForeignKey("media_uploads.id"),
        nullable=False,
        index=True
    )
    
    # Document metadata
    file_name = Column(String(255), nullable=False)
    file_size = Column(Integer, nullable=True)
    mime_type = Column(String(100), nullable=True)
    file_hash = Column(String(64), nullable=True)  # For duplicate detection
    
    # Document properties
    is_original = Column(Boolean, nullable=False, default=True)
    is_verified = Column(Boolean, nullable=False, default=False)
    is_required = Column(Boolean, nullable=False, default=False)
    
    # Verification and approval
    verified_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    verified_at = Column(DateTime, nullable=True)
    verification_notes = Column(Text, nullable=True)
    
    # Document dates
    document_date = Column(DateTime, nullable=True)  # Date on the document
    expiry_date = Column(DateTime, nullable=True)    # Document expiry
    
    # Audit fields
    uploaded_by = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        index=True
    )
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    uploaded_at = Column(DateTime, nullable=False, default=func.now())
    updated_at = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now())
    
    # Additional metadata
    tags = Column(Text, nullable=True)  # JSON array of tags
    additional_data = Column(Text, nullable=True)  # JSON for additional data
    
    # Relationships
    tenant = relationship("app.modules.core.tenants.models.tenant.Tenant", viewonly=True)
    media_upload = relationship(
        "app.modules.core.functions.media_system.models.MediaUpload",
        viewonly=True
    )
    uploaded_by_user = relationship(
        "app.modules.core.users.models.user.User", 
        foreign_keys=[uploaded_by],
        viewonly=True
    )
    updated_by_user = relationship(
        "app.modules.core.users.models.user.User", 
        foreign_keys=[updated_by],
        viewonly=True
    )
    verified_by_user = relationship(
        "app.modules.core.users.models.user.User", 
        foreign_keys=[verified_by],
        viewonly=True
    )
    
    # Many-to-many relationship with control entries
    entries = relationship(
        "FinancialControlEntry",
        secondary="control_entry_documents",
        back_populates="documents"
    )
    
    # Table indexes for performance
    __table_args__ = (
        Index("ix_control_documents_tenant_type", "tenant_id", "document_type"),
        Index("ix_control_documents_tenant_verified", "tenant_id", "is_verified"),
        Index("ix_control_documents_document_number", "document_number"),
        Index("ix_control_documents_file_hash", "file_hash"),
        Index("ix_control_documents_document_date", "document_date"),
    )
    
    def __repr__(self):
        return (
            f"<ControlDocument(id={self.id}, "
            f"type='{self.document_type}', "
            f"title='{self.title}')>"
        )
    
    @property
    def file_url(self) -> str:
        """Get file URL from media upload."""
        if self.media_upload:
            return self.media_upload.file_url
        return ""
    
    @property
    def is_image(self) -> bool:
        """Check if document is an image."""
        if self.mime_type:
            return self.mime_type.startswith('image/')
        return False
    
    @property
    def is_pdf(self) -> bool:
        """Check if document is a PDF."""
        return self.mime_type == 'application/pdf'
