import uuid
from sqlalchemy import Column, String, ForeignKey, Index, Boolean, Integer, Numeric
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.orm import relationship

from app.db.base import Base
from app.modules.core.tenants.models.tenant import Tenant


class ProductModifierOption(Base):
    """
    Represents an individual option within a product modifier group (e.g., "Gift Wrapping", "Express Shipping").
    """

    __tablename__ = "eshop_product_modifier_options"

    # Primary key
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Tenant isolation
    tenant_id = Column(PG_UUID(as_uuid=True), ForeignKey(Tenant.id), nullable=True, index=True)
    
    # Parent modifier group
    modifier_group_id = Column(PG_UUID(as_uuid=True), ForeignKey("eshop_product_modifier_groups.id"), nullable=False, index=True)
    
    # Option properties
    name = Column(String(100), nullable=False)  # e.g., "Gift Wrapping", "Extended Warranty"
    description = Column(String(255), nullable=True)  # Optional description
    
    # Pricing
    price_adjustment = Column(Numeric(10, 2), default=0.0, nullable=False)  # Additional cost
    price_adjustment_type = Column(String(20), default="fixed", nullable=False)  # "fixed" or "percentage"
    
    # Display and status
    display_order = Column(Integer, default=0, nullable=False)
    is_default = Column(Boolean, default=False, nullable=False)  # Default selection for this group
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Stock and availability (if modifier affects availability)
    stock_quantity = Column(Integer, nullable=True)  # Limited availability for this modifier
    
    # Visual representation
    image_url = Column(String(500), nullable=True)  # Image for this modifier option
    
    # Relationships
    tenant = relationship("Tenant")
    modifier_group = relationship("ProductModifierGroup", back_populates="options")

    __table_args__ = (
        Index("ix_eshop_modifier_options_tenant_id", "tenant_id"),
        Index("ix_eshop_modifier_options_modifier_group_id", "modifier_group_id"),
        Index("ix_eshop_modifier_options_tenant_group", "tenant_id", "modifier_group_id"),
    )

    def __repr__(self):
        return f"<ProductModifierOption(id={self.id}, name='{self.name}', modifier_group_id={self.modifier_group_id})>"
