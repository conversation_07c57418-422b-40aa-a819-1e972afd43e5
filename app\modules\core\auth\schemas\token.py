from typing import Optional
from pydantic import BaseModel


class Token(BaseModel):
    """
    Schema for the token response returned by the login endpoint.
    """

    access_token: str
    refresh_token: str
    token_type: str


class TokenPayload(BaseModel):
    """
    Schema for the JWT token payload.
    """

    sub: Optional[str] = None
    exp: Optional[int] = None
    type: Optional[str] = None
