'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/lib/auth/AuthProvider';
import { useRouter } from 'next/navigation';
import SupplierHeader from '../components/SupplierHeader';
import InvoiceRegistrationForm from '../components/InvoiceRegistrationForm';
import InvoicesList from '../components/InvoicesList';
import { useSupplierInvoices } from '../hooks/useSupplierInvoices';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FileText, Plus, List } from 'lucide-react';

export default function SupplierInvoicesPage() {
  const { user, logout } = useAuth();
  const router = useRouter();
  const [selectedTenantId, setSelectedTenantId] = useState<string | null>(null);
  const [tenants, setTenants] = useState([]);
  const [activeTab, setActiveTab] = useState('list');

  const {
    invoices,
    loading,
    error,
    registerInvoice,
    refreshInvoices
  } = useSupplierInvoices(selectedTenantId);

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }

    // Fetch user tenants (supplier associations)
    fetchUserTenants();
  }, [user, router]);

  const fetchUserTenants = async () => {
    try {
      const response = await fetch('/api/auth/user-tenants', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        const supplierTenants = data.filter((t: any) => t.role === 'supplier');
        setTenants(supplierTenants);
        
        // Auto-select first tenant if only one
        if (supplierTenants.length === 1) {
          setSelectedTenantId(supplierTenants[0].id);
        }
      }
    } catch (error) {
      console.error('Error fetching tenants:', error);
    }
  };

  const handleLogout = () => {
    logout();
    router.push('/auth/login');
  };

  const handleTenantChange = (tenantId: string | null) => {
    setSelectedTenantId(tenantId);
  };

  const handleInvoiceRegistered = () => {
    refreshInvoices();
    setActiveTab('list');
  };

  if (!user) {
    return <div>Carregando...</div>;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <SupplierHeader
        userName={user.full_name || user.email || 'User'}
        userEmail={user.email}
        onLogout={handleLogout}
        tenants={tenants}
        selectedTenantId={selectedTenantId}
        onTenantChange={handleTenantChange}
      />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-2">
            <div className="p-2 bg-green-100 rounded-lg">
              <FileText className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Gestão de Faturas</h1>
              <p className="text-gray-600">Registre e acompanhe suas faturas de entrega</p>
            </div>
          </div>
        </div>

        {!selectedTenantId ? (
          <Card>
            <CardHeader>
              <CardTitle>Selecione um Tenant</CardTitle>
              <CardDescription>
                Escolha um tenant no seletor acima para visualizar e gerenciar faturas.
              </CardDescription>
            </CardHeader>
          </Card>
        ) : (
          <div className="space-y-6">
            {/* Navigation Buttons */}
            <div className="flex space-x-2">
              <Button
                variant={activeTab === 'list' ? 'default' : 'outline'}
                onClick={() => setActiveTab('list')}
                className="flex items-center space-x-2"
              >
                <List className="h-4 w-4" />
                <span>Lista de Faturas</span>
              </Button>
              <Button
                variant={activeTab === 'register' ? 'default' : 'outline'}
                onClick={() => setActiveTab('register')}
                className="flex items-center space-x-2"
              >
                <Plus className="h-4 w-4" />
                <span>Registrar Fatura</span>
              </Button>
            </div>

            {/* Content */}
            {activeTab === 'list' && (
              <InvoicesList
                invoices={invoices}
                loading={loading}
                error={error}
                onRefresh={refreshInvoices}
              />
            )}

            {activeTab === 'register' && (
              <InvoiceRegistrationForm
                tenantId={selectedTenantId}
                onInvoiceRegistered={handleInvoiceRegistered}
                onRegisterInvoice={registerInvoice}
                loading={loading}
              />
            )}
          </div>
        )}
      </main>
    </div>
  );
}
