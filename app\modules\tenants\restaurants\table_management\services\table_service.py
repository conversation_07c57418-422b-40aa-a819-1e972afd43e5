import uuid
import logging
from typing import List, Optional, Dict, Any
from sqlalchemy import select, update as sqlalchemy_update, delete
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.exc import IntegrityError
from fastapi import HTTPException, status

from app.modules.tenants.restaurants.table_management.models.table import (
    Table,
    TableLayout,
    TableStatus,
)
from app.modules.tenants.restaurants.table_management.schemas.table import (
    TableCreate,
    TableUpdate,
    TableLayoutCreate,
    TableLayoutUpdate,
)

# Import WebSocket utility for real-time updates
from app.websockets.manager import emit_to_tenant
from app.modules.tenants.restaurants.table_management.websockets.table_websockets import (
    emit_table_status_change,
)

logger = logging.getLogger(__name__)


class TableService:
    """Service for managing restaurant tables and layouts."""

    def __init__(self, db_session: Optional[AsyncSession] = None):
        """Initialize the service with an optional database session."""
        self.db = db_session

    # Table CRUD operations
    async def create_table(
        self,
        db: AsyncSession,
        table_in: TableCreate,
        tenant_id: uuid.UUID,
    ) -> Table:
        """Create a new table for the specified tenant."""
        try:
            # Create table object
            table_data = table_in.model_dump()
            db_table = Table(**table_data, tenant_id=tenant_id)

            # Add table to database
            db.add(db_table)
            await db.commit()
            await db.refresh(db_table)

            logger.info(f"Created table {db_table.table_number} for tenant {tenant_id}")
            return db_table

        except IntegrityError as e:
            await db.rollback()
            logger.error(f"Integrity error creating table for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Error creating table. Check input data.",
            )
        except Exception as e:
            await db.rollback()
            logger.exception(f"Unexpected error creating table for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def get_table(
        self,
        db: AsyncSession,
        table_id: uuid.UUID,
        tenant_id: uuid.UUID,
    ) -> Optional[Table]:
        """Get a table by ID for the specified tenant."""
        try:
            query = (
                select(Table)
                .options(selectinload(Table.layout))
                .where(Table.id == table_id, Table.tenant_id == tenant_id)
            )
            result = await db.execute(query)
            return result.scalars().first()
        except Exception as e:
            logger.exception(f"Error getting table {table_id} for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def get_tables(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID,
        layout_id: Optional[uuid.UUID] = None,
        status: Optional[TableStatus] = None,
        capacity_min: Optional[int] = None,
        capacity_max: Optional[int] = None,
        is_active: Optional[bool] = None,
        zone: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[Table]:
        """Get tables for the specified tenant with optional filters."""
        try:
            query = (
                select(Table)
                .options(selectinload(Table.layout))
                .where(Table.tenant_id == tenant_id)
                .offset(skip)
                .limit(limit)
            )

            # Apply filters if provided
            if layout_id:
                query = query.where(Table.layout_id == layout_id)
            if status:
                query = query.where(Table.status == status)
            if capacity_min is not None:
                query = query.where(Table.capacity >= capacity_min)
            if capacity_max is not None:
                query = query.where(Table.capacity <= capacity_max)
            if is_active is not None:
                query = query.where(Table.is_active == is_active)
            if zone is not None:
                query = query.where(Table.zone == zone)

            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.exception(f"Error getting tables for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def update_table(
        self,
        db: AsyncSession,
        table_id: uuid.UUID,
        table_in: TableUpdate,
        tenant_id: uuid.UUID,
    ) -> Optional[Table]:
        """Update a table for the specified tenant."""
        try:
            # Check if table exists and belongs to tenant
            db_table = await self.get_table(db, table_id, tenant_id)
            if not db_table:
                return None

            # Get update data
            update_data = table_in.model_dump(exclude_unset=True)

            # Update table
            query = (
                sqlalchemy_update(Table)
                .where(Table.id == table_id, Table.tenant_id == tenant_id)
                .values(**update_data)
                .execution_options(synchronize_session="fetch")
            )
            await db.execute(query)
            await db.commit()

            # Refresh table data
            db_table = await self.get_table(db, table_id, tenant_id)

            # Emit WebSocket event if status changed
            if "status" in update_data:
                try:
                    await emit_table_status_change(
                        tenant_id=tenant_id,
                        table_id=table_id,
                        new_status=update_data["status"],
                    )
                except Exception as ws_error:
                    logger.error(
                        f"Failed to emit WebSocket event for table status change: {ws_error}"
                    )

            logger.info(f"Updated table {table_id} for tenant {tenant_id}")
            return db_table

        except IntegrityError as e:
            await db.rollback()
            logger.error(f"Integrity error updating table {table_id} for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Error updating table. Check input data.",
            )
        except Exception as e:
            await db.rollback()
            logger.exception(
                f"Unexpected error updating table {table_id} for tenant {tenant_id}: {e}"
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def delete_table(
        self,
        db: AsyncSession,
        table_id: uuid.UUID,
        tenant_id: uuid.UUID,
    ) -> bool:
        """Delete a table for the specified tenant."""
        try:
            # Check if table exists and belongs to tenant
            db_table = await self.get_table(db, table_id, tenant_id)
            if not db_table:
                return False

            # Delete table
            query = (
                delete(Table)
                .where(Table.id == table_id, Table.tenant_id == tenant_id)
                .execution_options(synchronize_session="fetch")
            )
            await db.execute(query)
            await db.commit()

            logger.info(f"Deleted table {table_id} for tenant {tenant_id}")
            return True

        except Exception as e:
            await db.rollback()
            logger.exception(f"Error deleting table {table_id} for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    # Layout CRUD operations
    async def create_layout(
        self,
        db: AsyncSession,
        layout_in: TableLayoutCreate,
        tenant_id: uuid.UUID,
    ) -> TableLayout:
        """Create a new table layout for the specified tenant."""
        try:
            # Create layout object
            layout_data = layout_in.model_dump()
            db_layout = TableLayout(**layout_data, tenant_id=tenant_id)

            # Add layout to database
            db.add(db_layout)
            await db.commit()
            await db.refresh(db_layout)

            logger.info(f"Created table layout {db_layout.name} for tenant {tenant_id}")
            return db_layout

        except IntegrityError as e:
            await db.rollback()
            logger.error(f"Integrity error creating table layout for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Error creating table layout. Check input data.",
            )
        except Exception as e:
            await db.rollback()
            logger.exception(f"Unexpected error creating table layout for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def get_layout(
        self,
        db: AsyncSession,
        layout_id: uuid.UUID,
        tenant_id: uuid.UUID,
        include_tables: bool = False,
    ) -> Optional[TableLayout]:
        """Get a table layout by ID for the specified tenant."""
        try:
            query = select(TableLayout).where(
                TableLayout.id == layout_id, TableLayout.tenant_id == tenant_id
            )

            if include_tables:
                query = query.options(selectinload(TableLayout.tables))

            result = await db.execute(query)
            return result.scalars().first()
        except Exception as e:
            logger.exception(f"Error getting table layout {layout_id} for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def get_layouts(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID,
        is_active: Optional[bool] = None,
        include_tables: bool = False,
        skip: int = 0,
        limit: int = 100,
    ) -> List[TableLayout]:
        """Get table layouts for the specified tenant with optional filters."""
        try:
            query = (
                select(TableLayout)
                .where(TableLayout.tenant_id == tenant_id)
                .offset(skip)
                .limit(limit)
            )

            if is_active is not None:
                query = query.where(TableLayout.is_active == is_active)

            if include_tables:
                query = query.options(selectinload(TableLayout.tables))

            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.exception(f"Error getting table layouts for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def update_layout(
        self,
        db: AsyncSession,
        layout_id: uuid.UUID,
        layout_in: TableLayoutUpdate,
        tenant_id: uuid.UUID,
    ) -> Optional[TableLayout]:
        """Update a table layout for the specified tenant."""
        try:
            # Check if layout exists and belongs to tenant
            db_layout = await self.get_layout(db, layout_id, tenant_id)
            if not db_layout:
                return None

            # Get update data
            update_data = layout_in.model_dump(exclude_unset=True)

            # Update layout
            query = (
                sqlalchemy_update(TableLayout)
                .where(TableLayout.id == layout_id, TableLayout.tenant_id == tenant_id)
                .values(**update_data)
                .execution_options(synchronize_session="fetch")
            )
            await db.execute(query)
            await db.commit()

            # Refresh layout data
            db_layout = await self.get_layout(db, layout_id, tenant_id, include_tables=True)

            logger.info(f"Updated table layout {layout_id} for tenant {tenant_id}")
            return db_layout

        except IntegrityError as e:
            await db.rollback()
            logger.error(
                f"Integrity error updating table layout {layout_id} for tenant {tenant_id}: {e}"
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Error updating table layout. Check input data.",
            )
        except Exception as e:
            await db.rollback()
            logger.exception(
                f"Unexpected error updating table layout {layout_id} for tenant {tenant_id}: {e}"
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def delete_layout(
        self,
        db: AsyncSession,
        layout_id: uuid.UUID,
        tenant_id: uuid.UUID,
    ) -> bool:
        """Delete a table layout for the specified tenant."""
        try:
            # Check if layout exists and belongs to tenant
            db_layout = await self.get_layout(db, layout_id, tenant_id)
            if not db_layout:
                return False

            # Delete layout
            query = (
                delete(TableLayout)
                .where(TableLayout.id == layout_id, TableLayout.tenant_id == tenant_id)
                .execution_options(synchronize_session="fetch")
            )
            await db.execute(query)
            await db.commit()

            logger.info(f"Deleted table layout {layout_id} for tenant {tenant_id}")
            return True

        except Exception as e:
            await db.rollback()
            logger.exception(f"Error deleting table layout {layout_id} for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    # Utility methods
    async def get_available_tables(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID,
        capacity: Optional[int] = None,
        layout_id: Optional[uuid.UUID] = None,
        time_slot: Optional[Dict[str, Any]] = None,  # For future reservation checks
    ) -> List[Table]:
        """Get available tables for the specified tenant with optional filters."""
        try:
            query = select(Table).where(
                Table.tenant_id == tenant_id,
                Table.status == TableStatus.AVAILABLE,
                Table.is_active == True,
            )

            if capacity is not None:
                query = query.where(Table.capacity >= capacity)

            if layout_id is not None:
                query = query.where(Table.layout_id == layout_id)

            # TODO: Add time slot check for future reservations

            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.exception(f"Error getting available tables for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def update_table_status(
        self,
        db: AsyncSession,
        table_id: uuid.UUID,
        new_status: TableStatus,
        tenant_id: uuid.UUID,
    ) -> Optional[Table]:
        """Update the status of a table for the specified tenant."""
        try:
            # Check if table exists and belongs to tenant
            db_table = await self.get_table(db, table_id, tenant_id)
            if not db_table:
                return None

            # Update table status
            query = (
                sqlalchemy_update(Table)
                .where(Table.id == table_id, Table.tenant_id == tenant_id)
                .values(status=new_status)
                .execution_options(synchronize_session="fetch")
            )
            await db.execute(query)
            await db.commit()

            # Refresh table data
            db_table = await self.get_table(db, table_id, tenant_id)

            # Emit WebSocket event for status change
            try:
                await emit_table_status_change(
                    tenant_id=tenant_id,
                    table_id=table_id,
                    new_status=new_status,
                )
            except Exception as ws_error:
                logger.error(f"Failed to emit WebSocket event for table status change: {ws_error}")

            logger.info(f"Updated table {table_id} status to {new_status} for tenant {tenant_id}")
            return db_table

        except Exception as e:
            await db.rollback()
            logger.exception(f"Error updating table {table_id} status for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )


# Create a singleton instance
table_service = TableService()
