"""
Sitemap Entry Model

Model for managing sitemap entries and generation.
"""

import uuid
from datetime import datetime
from typing import Optional

from sqlalchemy import Column, DateTime, ForeignKey, String, Boolean, Integer, Text, Numeric
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base


class SitemapEntry(Base):
    """
    Sitemap entry model for XML sitemap generation.
    
    Stores sitemap entries for different content types and languages,
    enabling automatic sitemap generation with proper hreflang support.
    """

    __tablename__ = "sitemap_entries"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Content reference
    content_type = Column(String(50), nullable=False, index=True)
    content_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # Language support
    language_code = Column(String(10), ForeignKey("languages.code"), nullable=False, index=True)
    
    # Sitemap properties
    url = Column(String(500), nullable=False, index=True)
    priority = Column(Numeric(3, 2), default=0.5, nullable=False)  # 0.0 to 1.0
    change_frequency = Column(String(20), default="weekly", nullable=False)
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    is_indexed = Column(Boolean, default=True, nullable=False)  # Should be included in sitemap
    
    # Metadata
    title = Column(String(200), nullable=True)
    description = Column(Text, nullable=True)
    image_url = Column(String(500), nullable=True)
    
    # Timestamps
    last_modified = Column(DateTime, default=datetime.utcnow, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Analytics
    crawl_count = Column(Integer, default=0, nullable=False)
    last_crawled_at = Column(DateTime, nullable=True)
    
    # Foreign keys
    created_by_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # Relationships
    language = relationship("app.modules.core.i18n.models.language.Language", viewonly=True)
    created_by = relationship("app.modules.core.users.models.user.User", viewonly=True)

    # Indexes for performance
    __table_args__ = (
        {"schema": None},  # Use default schema
    )

    def __repr__(self):
        return f"<SitemapEntry(id={self.id}, url='{self.url}', language='{self.language_code}')>"

    @property
    def sitemap_priority(self) -> float:
        """Get priority as float for sitemap XML."""
        return float(self.priority)

    def is_valid_change_frequency(self, frequency: str) -> bool:
        """Validate change frequency for sitemap."""
        valid_frequencies = [
            "always", "hourly", "daily", "weekly", 
            "monthly", "yearly", "never"
        ]
        return frequency in valid_frequencies

    def update_crawl_stats(self):
        """Update crawl statistics."""
        self.crawl_count += 1
        self.last_crawled_at = datetime.utcnow()

    def get_hreflang_data(self) -> dict:
        """Get hreflang data for this entry."""
        return {
            "url": self.url,
            "language": self.language_code,
            "title": self.title
        }
