/**
 * Serviço Principal do Sistema POS
 * Responsável por integração com APIs do backend e gerenciamento de dados
 */

import {
  MenuItem,
  MenuCategory,
  POSOrder,
  Table,
  PaymentMethod,
  CartItem,
  ItemCustomization
} from '@/types/pos';
import Cookies from 'js-cookie';
import apiClient from '@/lib/api/client';

// Placeholder para apiClient - assumindo que existe baseado no padrão do projeto
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

class POSService {
  private baseUrl: string;
  private tenantId: string | null = null;

  constructor() {
    this.baseUrl = `${API_BASE_URL}/api`;
  }

  /**
   * Define o tenant ID para todas as requisições subsequentes
   */
  setTenantId(tenantId: string): void {
    this.tenantId = tenantId;
  }

  /**
   * Headers padrão para requisições autenticadas
   */
  private getHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    // Usar tenant ID do cookie (mesma abordagem do apiClient)
    const tenantId = Cookies.get('tenant_id');
    if (tenantId) {
      headers['X-Tenant-ID'] = tenantId;
    }

    // Adicionar token de autenticação do cookie (mesma abordagem do apiClient)
    const token = Cookies.get('access_token');
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return headers;
  }

  /**
   * Wrapper para requisições HTTP com tratamento de erro
   */
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;

    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          ...this.getHeaders(),
          ...options.headers,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      return result.data || result;
    } catch (error) {
      console.error(`API Error (${endpoint}):`, error);
      throw error;
    }
  }

  // ===============================
  // MENU MANAGEMENT
  // ===============================

  /**
   * Mapeia item do menu do formato backend para frontend
   */
  private mapMenuItemFromBackend(backendItem: any): MenuItem {
    const mappedItem = {
      id: backendItem.id,
      name: backendItem.name,
      description: backendItem.description || '',
      price: parseFloat(backendItem.base_price || '0'),
      category_id: backendItem.category_id,
      is_available: backendItem.is_available !== false,
      is_active: backendItem.is_active !== false,
      preparation_time: backendItem.preparation_time || 10,
      image_url: backendItem.image_url || null,
      variant_groups: this.mapVariantGroups(backendItem.variant_groups || []),
      modifier_groups: this.mapModifierGroups(backendItem.modifier_groups || []),
      optional_groups: this.mapOptionalGroups(backendItem.optional_groups || [])
    };

    console.log(`🍽️ POS: Mapped item "${mappedItem.name}" with category_id: "${mappedItem.category_id}"`);
    return mappedItem;
  }

  /**
   * Mapeia grupos de variantes do backend para frontend
   */
  private mapVariantGroups(backendGroups: any[]): any[] {
    return backendGroups.map(group => {
      console.log('🔍 POS: Mapping variant group:', group);
      const options = group.options || group.variants || [];
      console.log('🔍 POS: Variant options found:', options);

      return {
        id: group.id,
        name: group.name,
        is_required: group.is_required || false,
        max_selections: group.max_selections || group.max_selection || 1,
        min_selections: group.min_selections || group.min_selection || 0,
        requires_default_selection: group.requires_default_selection !== false, // Sempre true para variantes
        variants: options.map((variant: any) => ({
          id: variant.id,
          name: variant.name,
          price_adjustment: parseFloat(variant.price_adjustment || '0'),
          is_default: variant.is_default || false,
          is_available: variant.is_available !== false
        }))
      };
    });
  }

  /**
   * Mapeia grupos de modificadores do backend para frontend
   */
  private mapModifierGroups(backendGroups: any[]): any[] {
    return backendGroups.map(group => {
      console.log('🔍 POS: Mapping modifier group:', group);
      const options = group.options || group.modifiers || [];
      console.log('🔍 POS: Modifier options found:', options);

      return {
        id: group.id,
        name: group.name,
        is_required: group.is_required || false,
        max_selections: group.max_selections || group.max_selection || 999,
        min_selections: group.min_selections || group.min_selection || 0,
        modifiers: options.map((modifier: any) => ({
          id: modifier.id,
          name: modifier.name,
          price_adjustment: parseFloat(modifier.price_adjustment || '0'),
          is_available: modifier.is_available !== false
        }))
      };
    });
  }

  /**
   * Mapeia grupos de opcionais do backend para frontend
   */
  private mapOptionalGroups(backendGroups: any[]): any[] {
    return backendGroups.map(group => {
      console.log('🔍 POS: Mapping optional group:', group);
      const options = group.options || group.optionals || [];
      console.log('🔍 POS: Optional options found:', options);

      return {
        id: group.id,
        name: group.name,
        is_required: group.is_required || false,
        max_selections: group.max_selections || group.max_selection || 999,
        min_selections: group.min_selections || group.min_selection || 0,
        optionals: options.map((optional: any) => ({
          id: optional.id,
          name: optional.name,
          price_adjustment: parseFloat(optional.price_adjustment || '0'),
          is_available: optional.is_available !== false
        }))
      };
    });
  }

  /**
   * Busca todas as categorias do menu
   */
  async getMenuCategories(): Promise<MenuCategory[]> {
    try {
      console.log('🍽️ POS: Loading menu categories from API...');
      const response = await apiClient.get<any>('/modules/restaurants/menu/categories/');
      console.log('🍽️ POS: Raw categories response:', response);

      // Verificar se a resposta é um array ou objeto com propriedade data
      const categories = Array.isArray(response.data) ? response.data : (response.data?.data || response.data?.categories || []);
      console.log(`✅ POS: Loaded ${categories.length} categories from API`);

      // Verificar se categories é realmente um array
      if (!Array.isArray(categories)) {
        console.error('❌ POS: Categories is not an array:', categories);
        throw new Error('API response is not an array');
      }

      // Converter para o formato esperado pelo POS
      const mappedCategories = categories.map(cat => ({
        id: cat.id,
        name: cat.name,
        description: cat.description || '',
        display_order: cat.display_order || 0,
        is_active: cat.is_active,
        parent_id: cat.parent_id || null,
        children: cat.children || []
      }));

      console.log('🍽️ POS: Mapped categories:', mappedCategories.map(cat => ({ id: cat.id, name: cat.name })));
      return mappedCategories;
    } catch (error) {
      console.error('❌ POS: Error loading categories from API:', error);
      console.warn('⚠️ POS: Falling back to mock data');

      // Fallback para dados mock
      return [
        {
          id: 'cat-1',
          name: 'Pratos Principais',
          description: 'Pratos principais do restaurante',
          display_order: 1,
          is_active: true,
          parent_id: null,
          children: []
        },
        {
          id: 'cat-2',
          name: 'Bebidas',
          description: 'Bebidas e sucos',
          display_order: 2,
          is_active: true,
          parent_id: null,
          children: []
        },
        {
          id: 'cat-3',
          name: 'Sobremesas',
          description: 'Doces e sobremesas',
          display_order: 3,
          is_active: true,
          parent_id: null,
          children: []
        },
        {
          id: 'cat-4',
          name: 'Entradas',
          description: 'Aperitivos e entradas',
          display_order: 4,
          is_active: true,
          parent_id: null,
          children: []
        }
      ];
    }
  }

  /**
   * Busca todos os itens do menu
   */
  async getMenuItems(categoryId?: string): Promise<MenuItem[]> {
    try {
      // Construir URL com parâmetros necessários
      const params = new URLSearchParams();
      params.append('include_details', 'true'); // Incluir variants e modifiers

      if (categoryId) {
        params.append('category_id', categoryId);
        console.log(`🍽️ POS: Loading menu items for category ${categoryId}...`);
      } else {
        console.log('🍽️ POS: Loading all menu items...');
      }

      const endpoint = `/modules/restaurants/menu/items/?${params.toString()}`;
      console.log(`🍽️ POS: Making request to: ${endpoint}`);

      const response = await apiClient.get<any>(endpoint);
      console.log('🍽️ POS: Raw response:', response);

      // Verificar se a resposta é um array ou objeto com propriedade data
      const items = Array.isArray(response.data) ? response.data : (response.data?.data || response.data?.items || []);
      console.log(`✅ POS: Loaded ${items.length} menu items from API`);
      console.log('🍽️ POS: First item sample:', items[0]);

      // Verificar se items é realmente um array
      if (!Array.isArray(items)) {
        console.error('❌ POS: Items is not an array:', items);
        throw new Error('API response is not an array');
      }

      // Converter para o formato esperado pelo POS
      const mappedItems = items.map(item => this.mapMenuItemFromBackend(item));
      console.log(`🍽️ POS: Mapped ${mappedItems.length} items for frontend`);

      return mappedItems;
    } catch (error) {
      console.error('❌ POS: Error loading menu items from API:', error);
      console.warn('⚠️ POS: Falling back to mock data');

      // Fallback para dados mock
      const allItems: MenuItem[] = [
      {
        id: 'item-1',
        name: 'Hambúrguer Clássico',
        description: 'Hambúrguer com carne, queijo, alface e tomate',
        price: 25.90,
        category_id: 'cat-1',
        is_active: true,
        preparation_time: 15,
        image_url: null,
        variant_groups: [
          {
            id: 'vg-1',
            name: 'Ponto da Carne',
            is_required: true,
            max_selections: 1,
            requires_default_selection: false,
            variants: [
              { id: 'v-1', name: 'Mal Passado', price_adjustment: 0 },
              { id: 'v-2', name: 'Ao Ponto', price_adjustment: 0 },
              { id: 'v-3', name: 'Bem Passado', price_adjustment: 0 }
            ]
          }
        ],
        modifier_groups: [
          {
            id: 'mg-1',
            name: 'Adicionais',
            is_required: false,
            max_selections: 3,
            modifiers: [
              { id: 'm-1', name: 'Bacon', price_adjustment: 3.50 },
              { id: 'm-2', name: 'Queijo Extra', price_adjustment: 2.00 },
              { id: 'm-3', name: 'Cebola Caramelizada', price_adjustment: 1.50 }
            ]
          }
        ]
      },
      {
        id: 'item-2',
        name: 'Pizza Margherita',
        description: 'Pizza com molho de tomate, mussarela e manjericão',
        price: 32.90,
        category_id: 'cat-1',
        is_active: true,
        preparation_time: 20,
        image_url: null,
        variant_groups: [
          {
            id: 'vg-2',
            name: 'Tamanho',
            is_required: true,
            max_selections: 1,
            requires_default_selection: false,
            variants: [
              { id: 'v-4', name: 'Pequena', price_adjustment: -5.00 },
              { id: 'v-5', name: 'Média', price_adjustment: 0 },
              { id: 'v-6', name: 'Grande', price_adjustment: 8.00 }
            ]
          }
        ],
        modifier_groups: []
      },
      {
        id: 'item-3',
        name: 'Coca-Cola',
        description: 'Refrigerante Coca-Cola gelado',
        price: 5.50,
        category_id: 'cat-2',
        is_active: true,
        preparation_time: 2,
        image_url: null,
        variant_groups: [
          {
            id: 'vg-3',
            name: 'Tamanho',
            is_required: true,
            max_selections: 1,
            requires_default_selection: false,
            variants: [
              { id: 'v-7', name: '350ml', price_adjustment: 0 },
              { id: 'v-8', name: '600ml', price_adjustment: 2.50 },
              { id: 'v-9', name: '1L', price_adjustment: 4.00 }
            ]
          }
        ],
        modifier_groups: []
      },
      {
        id: 'item-4',
        name: 'Suco Natural',
        description: 'Suco natural da fruta',
        price: 8.90,
        category_id: 'cat-2',
        is_active: true,
        preparation_time: 5,
        image_url: null,
        variant_groups: [
          {
            id: 'vg-4',
            name: 'Sabor',
            is_required: true,
            max_selections: 1,
            requires_default_selection: false,
            variants: [
              { id: 'v-10', name: 'Laranja', price_adjustment: 0 },
              { id: 'v-11', name: 'Limão', price_adjustment: 0 },
              { id: 'v-12', name: 'Maracujá', price_adjustment: 1.00 },
              { id: 'v-13', name: 'Acerola', price_adjustment: 1.50 }
            ]
          }
        ],
        modifier_groups: []
      },
      {
        id: 'item-5',
        name: 'Pudim de Leite',
        description: 'Pudim caseiro com calda de caramelo',
        price: 12.90,
        category_id: 'cat-3',
        is_active: true,
        preparation_time: 3,
        image_url: null,
        variant_groups: [],
        modifier_groups: [
          {
            id: 'mg-2',
            name: 'Acompanhamentos',
            is_required: false,
            max_selections: 2,
            modifiers: [
              { id: 'm-4', name: 'Chantilly', price_adjustment: 2.00 },
              { id: 'm-5', name: 'Frutas Vermelhas', price_adjustment: 3.50 }
            ]
          }
        ]
      },
      {
        id: 'item-6',
        name: 'Batata Frita',
        description: 'Porção de batata frita crocante',
        price: 15.90,
        category_id: 'cat-4',
        is_active: true,
        preparation_time: 10,
        image_url: null,
        variant_groups: [
          {
            id: 'vg-5',
            name: 'Tamanho',
            is_required: true,
            max_selections: 1,
            requires_default_selection: false,
            variants: [
              { id: 'v-14', name: 'Pequena', price_adjustment: -3.00 },
              { id: 'v-15', name: 'Média', price_adjustment: 0 },
              { id: 'v-16', name: 'Grande', price_adjustment: 5.00 }
            ]
          }
        ],
        modifier_groups: [
          {
            id: 'mg-3',
            name: 'Molhos',
            is_required: false,
            max_selections: 2,
            modifiers: [
              { id: 'm-6', name: 'Ketchup', price_adjustment: 0 },
              { id: 'm-7', name: 'Maionese', price_adjustment: 0 },
              { id: 'm-8', name: 'Barbecue', price_adjustment: 1.00 }
            ]
          }
        ]
      }
      ];

      if (categoryId) {
        return allItems.filter(item => item.category_id === categoryId);
      }

      return allItems;
    }
  }

  /**
   * Busca item específico do menu com detalhes completos
   */
  async getMenuItem(itemId: string): Promise<MenuItem> {
    try {
      const item = await apiClient.get<any>(
        `/modules/restaurants/menu/items/${itemId}/?include_details=true`
      );
      return this.mapMenuItemFromBackend(item);
    } catch (error) {
      console.error(`Error loading menu item ${itemId} from API:`, error);
      throw error;
    }
  }

  // ===============================
  // ORDER MANAGEMENT
  // ===============================

  /**
   * Cria um novo pedido
   */
  async createOrder(orderData: Partial<POSOrder>): Promise<POSOrder> {
    try {
      console.log('🍽️ POS: Creating order...', orderData);
      const order = await apiClient.post<POSOrder>('/financial/orders/', orderData);
      console.log('✅ POS: Order created successfully:', order);
      return order.data;
    } catch (error) {
      console.error('❌ POS: Error creating order:', error);
      throw error;
    }
  }

  /**
   * Atualiza um pedido existente
   */
  async updateOrder(orderId: string, updates: Partial<POSOrder>): Promise<POSOrder> {
    return this.request<POSOrder>(`/modules/tenants/restaurants/orders/${orderId}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  /**
   * Adiciona item ao pedido
   */
  async addItemToOrder(
    orderId: string,
    customization: ItemCustomization
  ): Promise<CartItem> {
    return this.request<CartItem>(
      `/modules/tenants/restaurants/orders/${orderId}/items`,
      {
        method: 'POST',
        body: JSON.stringify(customization),
      }
    );
  }

  /**
   * Remove item do pedido
   */
  async removeItemFromOrder(orderId: string, itemId: string): Promise<void> {
    return this.request<void>(
      `/modules/tenants/restaurants/orders/${orderId}/items/${itemId}`,
      {
        method: 'DELETE',
      }
    );
  }

  /**
   * Envia pedido para a cozinha (KDS)
   */
  async sendOrderToKitchen(order: any): Promise<void> {
    // Converter order do POS para formato KDS
    const kdsOrderData = {
      order_details: {
        items: order.items.map((item: any) => ({
          name: item.name,
          quantity: item.quantity,
          price: item.unit_price,
          special_instructions: item.special_instructions || '',
          variant_options: item.selected_variants || [],
          modifier_options: item.selected_modifiers || []
        })),
        table_number: order.table_id ? `Mesa ${order.table_id}` : 'Balcão',
        order_type: order.table_id ? 'dine_in' : 'takeout',
        total_amount: order.total_amount,
        order_number: order.order_number
      },
      status: 'pending',
      source_order_id: order.id,
      creator_user_id: order.user_id
    };

    return this.request<void>(
      `/modules/restaurants/kds/orders/`,
      {
        method: 'POST',
        body: JSON.stringify(kdsOrderData)
      }
    );
  }

  // ===============================
  // TABLE MANAGEMENT
  // ===============================

  /**
   * Busca todas as mesas disponíveis
   */
  async getTables(): Promise<Table[]> {
    try {
      const tables = await this.request<any[]>('/modules/restaurants/tables/tables/');

      // Converter para o formato esperado pelo POS
      return tables.map(table => ({
        id: table.id,
        tenant_id: table.tenant_id || '',
        table_number: table.number?.toString() || table.table_number || '',
        number: table.number?.toString() || table.table_number || '',
        name: table.name || `Mesa ${table.number || table.table_number}`,
        capacity: table.capacity,
        status: this.mapTableStatus(table.status),
        position: table.position || { x: 0, y: 0, width: 100, height: 100 },
        is_active: table.is_active,
        qrcode_enabled: table.qrcode_enabled || false
      }));
    } catch (error) {
      console.error('Error loading tables from API, using mock data:', error);

      // Fallback para dados mock
      return [
        {
          id: 'table-1',
          tenant_id: '',
          table_number: '1',
          number: '1',
          name: 'Mesa 1',
          capacity: 4,
          status: 'available',
          position: { x: 0, y: 0, width: 100, height: 100 },
          is_active: true,
          qrcode_enabled: false
        },
        {
          id: 'table-2',
          tenant_id: '',
          table_number: '2',
          number: '2',
          name: 'Mesa 2',
          capacity: 2,
          status: 'occupied',
          position: { x: 120, y: 0, width: 100, height: 100 },
          is_active: true,
          qrcode_enabled: false
        },
        {
          id: 'table-3',
          tenant_id: '',
          table_number: '3',
          number: '3',
          name: 'Mesa 3',
          capacity: 6,
          status: 'available',
          position: { x: 240, y: 0, width: 100, height: 100 },
          is_active: true,
          qrcode_enabled: false
        },
        {
          id: 'table-4',
          tenant_id: '',
          table_number: '4',
          number: '4',
          name: 'Mesa 4',
          capacity: 2,
          status: 'reserved',
          position: { x: 0, y: 120, width: 100, height: 100 },
          is_active: true,
          qrcode_enabled: false
        }
      ];
    }
  }

  /**
   * Mapeia status da mesa do backend para frontend
   */
  private mapTableStatus(dbStatus: string): Table['status'] {
    const statusMap: Record<string, Table['status']> = {
      'AVAILABLE': 'available',
      'OCCUPIED': 'occupied',
      'RESERVED': 'reserved',
      'CLEANING': 'cleaning',
      'OUT_OF_ORDER': 'cleaning'
    };
    return statusMap[dbStatus] || 'available';
  }

  /**
   * Atualiza status de uma mesa
   */
  async updateTableStatus(
    tableId: string,
    status: Table['status']
  ): Promise<Table> {
    // Mapear status do frontend para backend
    const backendStatus = this.mapTableStatusToBackend(status);

    return this.request<Table>(
      `/modules/restaurants/tables/tables/${tableId}/status?status=${backendStatus}`,
      {
        method: 'PATCH'
      }
    );
  }

  /**
   * Mapeia status do frontend para backend
   */
  private mapTableStatusToBackend(frontendStatus: Table['status']): string {
    const statusMap: Record<Table['status'], string> = {
      'available': 'AVAILABLE',
      'occupied': 'OCCUPIED',
      'reserved': 'RESERVED',
      'cleaning': 'CLEANING'
    };
    return statusMap[frontendStatus] || 'AVAILABLE';
  }

  /**
   * Busca mesas disponíveis
   */
  async getAvailableTables(capacity?: number): Promise<Table[]> {
    try {
      const params = new URLSearchParams();
      if (capacity) {
        params.append('capacity', capacity.toString());
      }

      const queryString = params.toString();
      const url = `/modules/restaurants/tables/tables/available${queryString ? `?${queryString}` : ''}`;

      const tables = await this.request<any[]>(url);

      return tables.map(table => ({
        id: table.id,
        tenant_id: table.tenant_id || '',
        table_number: table.table_number || table.number?.toString() || '',
        number: table.number.toString(),
        name: table.name || `Mesa ${table.number}`,
        capacity: table.capacity,
        status: this.mapTableStatus(table.status),
        position: table.position || { x: 0, y: 0, width: 100, height: 100 },
        is_active: table.is_active,
        qrcode_enabled: table.qrcode_enabled || false
      }));
    } catch (error) {
      console.error('Error loading available tables:', error);
      // Fallback para todas as mesas disponíveis do mock
      const allTables = await this.getTables();
      return allTables.filter(table => table.status === 'available');
    }
  }

  /**
   * Associa mesa a um pedido
   */
  async assignTableToOrder(tableId: string, orderId: string): Promise<void> {
    return this.request<void>(
      `/modules/tenants/restaurants/tables/${tableId}/assign`,
      {
        method: 'POST',
        body: JSON.stringify({ order_id: orderId }),
      }
    );
  }

  // ===============================
  // PAYMENT PROCESSING
  // ===============================

  /**
   * Busca métodos de pagamento disponíveis
   */
  async getPaymentMethods(): Promise<PaymentMethod[]> {
    try {
      console.log('💳 POS: Loading payment methods from API...');
      const response = await apiClient.get<any>('/modules/pos/payment-methods');
      console.log('💳 POS: Raw payment methods response:', response);

      // Verificar se a resposta é um array ou objeto com propriedade data
      const methods = Array.isArray(response.data) ? response.data : (response.data?.methods || []);
      console.log(`✅ POS: Loaded ${methods.length} payment methods from API`);

      // Verificar se methods é realmente um array
      if (!Array.isArray(methods)) {
        console.error('❌ POS: Payment methods is not an array:', methods);
        throw new Error('API response is not an array');
      }

      // Converter para o formato esperado pelo POS
      return methods.map((method: any) => ({
        id: method.id,
        name: method.name,
        type: this.mapMethodType(method.method_type),
        is_active: method.is_active || method.is_enabled || true,
        icon: method.icon || this.getDefaultIcon(method.method_type),
        display_order: method.display_order || 0
      }));
    } catch (error) {
      console.error('❌ POS: Error loading payment methods from API:', error);
      console.warn('⚠️ POS: Falling back to defaults');

      // Fallback para dados mock
      return [
        {
          id: 'cash',
          name: 'Dinheiro',
          type: 'cash' as const,
          is_active: true,
          icon: '💵'
        },
        {
          id: 'credit_card',
          name: 'Cartão de Crédito',
          type: 'card' as const,
          is_active: true,
          icon: '💳'
        },
        {
          id: 'debit_card',
          name: 'Cartão de Débito',
          type: 'card' as const,
          is_active: true,
          icon: '💳'
        },
        {
          id: 'pix',
          name: 'PIX',
          type: 'pix' as const,
          is_active: true,
          icon: '📱'
        }
      ];
    }
  }

  /**
   * Mapeia tipos do banco para tipos do frontend
   */
  private mapMethodType(dbType: string): 'cash' | 'card' | 'pix' | 'digital_wallet' {
    const typeMap: Record<string, 'cash' | 'card' | 'pix' | 'digital_wallet'> = {
      'CASH': 'cash',
      'CREDIT_CARD': 'card',
      'DEBIT_CARD': 'card',
      'BANK_TRANSFER': 'digital_wallet',
      'MOBILE_PAYMENT': 'digital_wallet',
      'PIX': 'pix'
    };
    return typeMap[dbType] || 'cash';
  }

  /**
   * Retorna ícone padrão para tipo de pagamento
   */
  private getDefaultIcon(methodType: string): string {
    const icons: Record<string, string> = {
      'CASH': '💵',
      'CREDIT_CARD': '💳',
      'DEBIT_CARD': '💳',
      'PIX': '📱',
      'BANK_TRANSFER': '🏦',
      'MOBILE_PAYMENT': '📱'
    };
    return icons[methodType] || '💳';
  }

  /**
   * Processa pagamento de um pedido
   */
  async processPayment(
    orderId: string,
    paymentData: {
      method_id: string;
      amount: number;
      transaction_id?: string;
    }
  ): Promise<{ success: boolean; transaction_id: string }> {
    return this.request<{ success: boolean; transaction_id: string }>(
      `/modules/tenants/restaurants/orders/${orderId}/payment`,
      {
        method: 'POST',
        body: JSON.stringify(paymentData),
      }
    );
  }

  // ===============================
  // UTILITIES
  // ===============================

  /**
   * Valida conectividade com o servidor
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.request<{ status: string }>('/health');
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Busca configurações do POS para o tenant
   */
  async getPOSSettings(): Promise<{
    tax_rate: number;
    currency: string;
    auto_print_receipt: boolean;
    require_table_selection: boolean;
  }> {
    return this.request('/modules/tenants/restaurants/pos/settings');
  }

  /**
   * Sincroniza item offline com o servidor
   */
  async syncItem(item: any): Promise<void> {
    const endpoint = `/modules/tenants/restaurants/sync/${item.type}`;

    return this.request<void>(endpoint, {
      method: 'POST',
      body: JSON.stringify({
        action: item.action,
        data: item.data,
        offline_id: item.id
      }),
    });
  }
}

// Instância singleton do serviço
export const posService = new POSService();
export default posService;