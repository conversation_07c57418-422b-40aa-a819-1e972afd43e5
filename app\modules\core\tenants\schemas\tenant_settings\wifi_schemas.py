"""
WiFi specific schemas for tenant settings.
"""

from typing import Optional, List, Dict, Any

from pydantic import BaseModel, Field, validator


class WiFiNetworkSchema(BaseModel):
    """Schema for individual WiFi network configuration."""

    id: str = Field(..., description="Unique identifier for the WiFi network")
    name: str = Field(..., max_length=100, description="Network name (SSID)")
    password: str = Field(..., max_length=255, description="Network password")
    zone: Optional[str] = Field(None, max_length=100, description="Zone where this network is available")
    is_enabled: bool = Field(True, description="Whether this network is active")
    is_guest: bool = Field(False, description="Whether this is a guest network")
    bandwidth: Optional[str] = Field(None, max_length=50, description="Bandwidth limit (e.g., '50 Mbps')")
    max_devices: Optional[int] = Field(None, ge=0, description="Maximum number of devices allowed")
    description: Optional[str] = Field(None, max_length=200, description="Network description")
    security_type: Optional[str] = Field("WPA2", description="Security type (WPA2, WPA3, Open, etc.)")

    @validator('security_type')
    def validate_security_type(cls, v):
        """Validate security type."""
        if v is not None:
            valid_types = ["Open", "WEP", "WPA", "WPA2", "WPA3"]
            if v not in valid_types:
                raise ValueError(f'Security type must be one of: {valid_types}')
        return v


class WiFiSettingsUpdate(BaseModel):
    """Schema for updating WiFi settings."""

    wifi_networks: Optional[List[WiFiNetworkSchema]] = Field(
        None,
        description="WiFi networks configuration by zone"
    )
