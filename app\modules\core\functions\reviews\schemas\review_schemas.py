"""
Review Schemas

Schemas Pydantic para validação de dados de reviews.
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, List
from uuid import UUID

from pydantic import BaseModel, Field, validator

from app.modules.core.functions.reviews.models.review import ReviewStatus


class ReviewBase(BaseModel):
    """Schema base para reviews"""
    rating: int = Field(..., ge=1, le=5, description="Rating de 1 a 5 estrelas")
    comment: Optional[str] = Field(None, max_length=2000, description="Comentário da review")
    review_type: str = Field(..., description="Tipo da review: 'product', 'service', 'tenant', etc.")
    
    @validator('comment')
    def validate_comment(cls, v):
        if v is not None:
            v = v.strip()
            if len(v) == 0:
                return None
        return v
    
    @validator('review_type')
    def validate_review_type(cls, v):
        allowed_types = ['product', 'service', 'tenant', 'menu_item', 'general']
        if v not in allowed_types:
            raise ValueError(f'review_type deve ser um de: {allowed_types}')
        return v


class ReviewCreate(ReviewBase):
    """Schema para criação de review"""
    # Campos opcionais para associação genérica
    entity_type: Optional[str] = Field(None, description="Tipo da entidade: 'product', 'menu_item', etc.")
    entity_id: Optional[UUID] = Field(None, description="ID da entidade específica")
    tenant_id: Optional[UUID] = Field(None, description="ID do tenant (para reviews gerais)")
    order_id: Optional[UUID] = Field(None, description="ID do pedido (para compra verificada)")
    
    @validator('entity_type')
    def validate_entity_type(cls, v):
        if v is not None:
            allowed_types = ['product', 'menu_item', 'service']
            if v not in allowed_types:
                raise ValueError(f'entity_type deve ser um de: {allowed_types}')
        return v


class ReviewUpdate(BaseModel):
    """Schema para atualização de review (apenas moderação)"""
    status: ReviewStatus = Field(..., description="Novo status da review")
    moderation_reason: Optional[str] = Field(None, description="Motivo da moderação")


class ReviewResponse(ReviewBase):
    """Schema de resposta para review"""
    id: UUID
    user_id: UUID
    created_at: datetime
    updated_at: datetime
    
    # Campos genéricos
    entity_type: Optional[str] = None
    entity_id: Optional[UUID] = None
    tenant_id: Optional[UUID] = None
    
    # Campos opcionais
    status: Optional[ReviewStatus] = None
    verified_purchase: Optional[bool] = None
    helpful_count: Optional[int] = None
    not_helpful_count: Optional[int] = None
    
    # Dados do usuário (opcionais para privacidade)
    user_name: Optional[str] = None
    user_rank: Optional[str] = None
    
    # Moderação (apenas para admins)
    moderated_by: Optional[UUID] = None
    moderated_at: Optional[datetime] = None
    moderation_reason: Optional[str] = None
    
    class Config:
        from_attributes = True
    
    @property
    def helpfulness_ratio(self) -> float:
        """Calcula a proporção de utilidade"""
        if not self.helpful_count or not self.not_helpful_count:
            return 0.0
        total = self.helpful_count + self.not_helpful_count
        if total == 0:
            return 0.0
        return self.helpful_count / total
    
    @property
    def is_visible(self) -> bool:
        """Verifica se a review está visível"""
        if not self.status:
            return True
        return self.status == ReviewStatus.ACTIVE
    
    @property
    def text_visible(self) -> bool:
        """Verifica se o texto está visível"""
        if not self.status:
            return True
        return self.status in [ReviewStatus.ACTIVE, ReviewStatus.REPORTED]


class ReviewListResponse(BaseModel):
    """Schema para lista paginada de reviews"""
    reviews: List[ReviewResponse]
    total: int
    page: int
    per_page: int
    pages: int
    has_next: bool
    has_prev: bool
    
    class Config:
        from_attributes = True


class ReviewStatsResponse(BaseModel):
    """Schema para estatísticas de reviews genéricas"""
    entity_type: Optional[str] = None
    entity_id: Optional[UUID] = None
    tenant_id: Optional[UUID] = None
    total_reviews: int
    average_rating: Decimal
    
    # Distribuição por estrelas
    rating_distribution: dict = Field(
        default_factory=dict,
        description="Distribuição de ratings {1: count, 2: count, ...}"
    )
    
    # Métricas de qualidade (opcionais)
    verified_purchases_count: Optional[int] = None
    verified_purchase_percentage: Optional[float] = None
    
    # Métricas de utilidade (opcionais)
    total_helpful_votes: Optional[int] = None
    total_not_helpful_votes: Optional[int] = None
    helpfulness_ratio: Optional[float] = None
    
    # Métricas de moderação (opcionais)
    hidden_reviews_count: Optional[int] = None
    reported_reviews_count: Optional[int] = None
    
    # Score de qualidade geral (opcional)
    quality_score: Optional[float] = Field(
        None, description="Score de qualidade das reviews (0-100)"
    )
    
    class Config:
        from_attributes = True


class ReviewFilterParams(BaseModel):
    """Schema para parâmetros de filtro de reviews"""
    rating: Optional[int] = Field(None, ge=1, le=5, description="Filtrar por rating")
    verified_only: Optional[bool] = Field(False, description="Apenas compras verificadas")
    status: Optional[ReviewStatus] = Field(None, description="Filtrar por status")
    sort_by: Optional[str] = Field(
        "created_at", 
        description="Ordenar por: created_at, rating, helpful_count"
    )
    sort_order: Optional[str] = Field(
        "desc", 
        description="Ordem: asc, desc"
    )
    
    @validator('sort_by')
    def validate_sort_by(cls, v):
        allowed = ['created_at', 'rating', 'helpful_count']
        if v not in allowed:
            raise ValueError(f'sort_by deve ser um de: {allowed}')
        return v
    
    @validator('sort_order')
    def validate_sort_order(cls, v):
        if v not in ['asc', 'desc']:
            raise ValueError('sort_order deve ser "asc" ou "desc"')
        return v


class ReviewModerationRequest(BaseModel):
    """Schema para solicitação de moderação"""
    action: str = Field(..., description="Ação: hide_text, hide_complete, restore")
    reason: str = Field(..., min_length=10, description="Motivo da ação")
    
    @validator('action')
    def validate_action(cls, v):
        allowed = ['hide_text', 'hide_complete', 'restore']
        if v not in allowed:
            raise ValueError(f'action deve ser um de: {allowed}')
        return v