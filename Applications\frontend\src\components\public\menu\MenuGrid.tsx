'use client';

import React from 'react';
import Image from 'next/image';
import {
  ClockIcon,
  TagIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { PublicMenuItem, PublicMenuCategory } from '@/hooks/usePublicMenu';

interface MenuGridProps {
  items: PublicMenuItem[];
  categories: PublicMenuCategory[];
  selectedCategory: string | null;
  searchTerm: string;
  formatPrice: (price: string) => string;
  onItemClick: (item: PublicMenuItem) => void;
  onClearSearch: () => void;
}

export function MenuGrid({
  items,
  categories,
  selectedCategory,
  searchTerm,
  formatPrice,
  onItemClick,
  onClearSearch
}: MenuGridProps) {
  if (items.length === 0) {
    return (
      <div className="text-center py-20">
        <TagIcon className="h-16 w-16 mx-auto mb-4 text-gray-300" />
        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          {searchTerm ? 'Nenhum item encontrado' : 'Menu em construção'}
        </h3>
        <p className="text-gray-600">
          {searchTerm
            ? 'Tente buscar por outro termo ou navegue pelas categorias'
            : 'Este menu ainda não possui itens cadastrados.'
          }
        </p>
        {searchTerm && (
          <button
            onClick={onClearSearch}
            className="mt-6 px-6 py-3 bg-gray-900 text-white rounded-full hover:bg-gray-800 transition-colors font-medium"
          >
            Limpar busca
          </button>
        )}
      </div>
    );
  }

  // Simple grid - just show items without complex grouping
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {items.map((item, index) => (
        <ItemCard
          key={`item-${item.id}-${index}`}
          item={item}
          formatPrice={formatPrice}
          onItemClick={onItemClick}
        />
      ))}
    </div>
  );
}

// Component for individual item card
function ItemCard({ 
  item, 
  formatPrice, 
  onItemClick 
}: { 
  item: PublicMenuItem; 
  formatPrice: (price: string) => string; 
  onItemClick: (item: PublicMenuItem) => void; 
}) {
  return (
    <div
      className="group bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-xl hover:-translate-y-1 transition-all duration-300 cursor-pointer"
      onClick={() => onItemClick(item)}
    >
      {/* Imagem do Item */}
      <div className="aspect-[4/3] bg-gradient-to-br from-gray-100 to-gray-200 relative overflow-hidden">
        {item.image_url ? (
          <Image
            src={item.image_url.startsWith('http') ? item.image_url : `${process.env.NEXT_PUBLIC_API_URL?.replace(/\/api$/, '') || 'http://localhost:8000'}${item.image_url}`}
            alt={item.name}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <div className="text-6xl">🍽️</div>
          </div>
        )}

        {/* Badges sobre a imagem */}
        <div className="absolute top-3 left-3 flex gap-2">
          {item.is_featured && (
            <div className="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-bold shadow-lg">
              ⭐ Destaque
            </div>
          )}
          {item.is_popular && (
            <div className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold shadow-lg">
              🔥 Popular
            </div>
          )}
        </div>

        {/* Badge Personalizável - Canto inferior direito */}
        {((item.variant_groups?.length ?? 0) > 0 || (item.modifier_groups?.length ?? 0) > 0 || (item.optional_groups?.length ?? 0) > 0) && (
          <div className="absolute bottom-3 right-3">
            <div className="bg-purple-500/90 backdrop-blur-sm text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg border border-purple-400/50 flex items-center gap-1">
              <span>✨</span>
              <span>Personalizável</span>
            </div>
          </div>
        )}

        {/* Status de disponibilidade */}
        {!item.is_available && (
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
            <div className="bg-white text-gray-900 px-4 py-2 rounded-full font-semibold text-sm">
              Indisponível
            </div>
          </div>
        )}
      </div>

      {/* Conteúdo do Card */}
      <div className="p-5">
        <div className="mb-3">
          <h3 className="text-lg font-bold text-gray-900 mb-1 group-hover:text-gray-700 transition-colors">
            {item.name}
          </h3>
          {item.description && (
            <p className="text-gray-600 text-sm leading-relaxed line-clamp-2">
              {item.description}
            </p>
          )}
        </div>

        {/* Informações extras */}
        <div className="flex items-center gap-3 mb-4 text-xs text-gray-500">
          {item.preparation_time && (
            <div className="flex items-center gap-1">
              <ClockIcon className="h-3 w-3" />
              <span>{item.preparation_time}min</span>
            </div>
          )}

        </div>

        {/* Badges de características */}
        <div className="flex flex-wrap gap-2 mb-3">
          {item.is_featured && (
            <div className="flex items-center gap-1 text-amber-600 bg-amber-50 px-2 py-1 rounded-full text-xs">
              <span>⭐</span>
              <span>Destaque</span>
            </div>
          )}

          {item.is_popular && (
            <div className="flex items-center gap-1 text-red-600 bg-red-50 px-2 py-1 rounded-full text-xs">
              <span>🔥</span>
              <span>Popular</span>
            </div>
          )}

          {item.is_spicy && (
            <div className="flex items-center gap-1 text-red-600 bg-red-50 px-2 py-1 rounded-full text-xs">
              <span>🌶️</span>
              <span>Picante</span>
            </div>
          )}

          {item.is_combo && (
            <div className="flex items-center gap-1 text-green-600 bg-green-50 px-2 py-1 rounded-full text-xs">
              <span>🍽️</span>
              <span>Combo</span>
            </div>
          )}

          {item.discount_percentage && item.discount_percentage > 0 && (
            <div className="flex items-center gap-1 text-red-600 bg-red-50 px-2 py-1 rounded-full text-xs font-medium">
              <span>🏷️</span>
              <span>{item.discount_percentage}% OFF</span>
            </div>
          )}
        </div>

        {/* Preço e botão */}
        <div className="flex items-center justify-between">
          <div className="text-2xl font-bold text-gray-900">
            {formatPrice(item.base_price)}
          </div>
          <button className="bg-gray-900 text-white px-4 py-2 rounded-full text-sm font-medium hover:bg-gray-800 transition-colors group-hover:scale-105 transform duration-200">
            Ver mais
          </button>
        </div>
      </div>
    </div>
  );
}


