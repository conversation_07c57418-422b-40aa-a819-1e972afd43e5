# Restaurant Tenant Settings Module
from app.modules.tenants.restaurants.tenant_settings.api import settings_router
from app.modules.tenants.restaurants.tenant_settings.services import (
    RestaurantTenantSettingsService,
    restaurant_tenant_settings_service,
)
from app.modules.tenants.restaurants.tenant_settings.models import (
    RestaurantTenantSettings,
)
from app.modules.tenants.restaurants.tenant_settings.schemas import (
    RestaurantTenantSettingsBase,
    RestaurantTenantSettingsCreate,
    RestaurantTenantSettingsUpdate,
    RestaurantTenantSettingsRead,
    RestaurantBusinessSettingsUpdate,
    RestaurantWiFiSettingsUpdate,
    RestaurantSocialMediaSettingsUpdate,
    RestaurantLocationSettingsUpdate,
)

__all__ = [
    # API
    "settings_router",
    # Services
    "RestaurantTenantSettingsService",
    "restaurant_tenant_settings_service",
    # Models
    "RestaurantTenantSettings",
    # Schemas
    "RestaurantTenantSettingsBase",
    "RestaurantTenantSettingsCreate",
    "RestaurantTenantSettingsUpdate",
    "RestaurantTenantSettingsRead",
    "RestaurantBusinessSettingsUpdate",
    "RestaurantWiFiSettingsUpdate",
    "RestaurantSocialMediaSettingsUpdate",
    "RestaurantLocationSettingsUpdate",
]
