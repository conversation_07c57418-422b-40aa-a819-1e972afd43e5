import logging
import uuid
from typing import List, Optional, Annotated, Any

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession

logger = logging.getLogger(__name__)

from app.modules.core.users.models.user import User
from app.modules.core.auth.dependencies.auth_dependencies import get_current_active_user
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    get_current_tenant_from_header,
    require_tenant_role,
)
from app.modules.core.tenants.models.tenant import Tenant
from app.modules.core.roles.models.roles import RolePermissions
from app.core.dependencies import get_db

from app.modules.core.eshop.services.product_optional_service import ProductOptionalService
from app.modules.core.eshop.schemas.product_optional_group import (
    ProductOptionalGroupCreate,
    ProductOptionalGroupUpdate,
    ProductOptionalGroupResponse,
)
from app.modules.core.eshop.schemas.product_optional_option import (
    ProductOptionalOptionCreate,
    ProductOptionalOptionUpdate,
    ProductOptionalOptionResponse,
)

async def get_optional_service(db_session: AsyncSession = Depends(get_db)) -> ProductOptionalService:
    return ProductOptionalService(db_session)

router = APIRouter(prefix="/optionals", tags=["eshop - Optionals"])

write_roles = RolePermissions.ADMIN_ROLES + ["TVendorSupplier"]
view_roles = RolePermissions.VIEW_ROLES + ["TCostumer"]


# Optional Groups
@router.post("/groups/", response_model=ProductOptionalGroupResponse, status_code=status.HTTP_201_CREATED)
async def create_optional_group(
    group_in: ProductOptionalGroupCreate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Optional[Tenant], Depends(get_current_tenant_from_header)],
    optional_service: Annotated[ProductOptionalService, Depends(get_optional_service)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=write_roles))],
):
    """Create a new optional group."""
    try:
        if current_tenant:
            group_in.tenant_id = current_tenant.id

        created_group = await optional_service.create_optional_group(
            group_in=group_in,
            current_user_id=current_user.id
        )
        
        return ProductOptionalGroupResponse.model_validate(created_group)
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"Error creating optional group: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/groups/", response_model=List[ProductOptionalGroupResponse])
async def read_optional_groups(
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Optional[Tenant], Depends(get_current_tenant_from_header)],
    optional_service: Annotated[ProductOptionalService, Depends(get_optional_service)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))],
    is_active: Optional[bool] = Query(None),
    include_options: bool = Query(False),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=200),
):
    """Retrieve optional groups."""
    try:
        tenant_id = current_tenant.id if current_tenant else None
        
        groups = await optional_service.get_optional_groups(
            tenant_id=tenant_id,
            is_active=is_active,
            include_options=include_options,
            skip=skip,
            limit=limit,
        )

        return [ProductOptionalGroupResponse.model_validate(group) for group in groups]
    except Exception as e:
        logger.exception(f"Error retrieving optional groups: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/groups/{group_id}", response_model=ProductOptionalGroupResponse)
async def read_optional_group(
    group_id: Annotated[uuid.UUID, Path(...)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Optional[Tenant], Depends(get_current_tenant_from_header)],
    optional_service: Annotated[ProductOptionalService, Depends(get_optional_service)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))],
    include_options: bool = Query(False),
):
    """Retrieve a specific optional group."""
    try:
        tenant_id = current_tenant.id if current_tenant else None
        
        group = await optional_service.get_optional_group(
            group_id=group_id,
            tenant_id=tenant_id,
            include_options=include_options
        )
        
        if group is None:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Optional group not found")

        return ProductOptionalGroupResponse.model_validate(group)
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"Error retrieving optional group {group_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


# Optional Options
@router.post("/options/", response_model=ProductOptionalOptionResponse, status_code=status.HTTP_201_CREATED)
async def create_optional_option(
    option_in: ProductOptionalOptionCreate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Optional[Tenant], Depends(get_current_tenant_from_header)],
    optional_service: Annotated[ProductOptionalService, Depends(get_optional_service)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=write_roles))],
):
    """Create a new optional option."""
    try:
        if current_tenant:
            option_in.tenant_id = current_tenant.id

        created_option = await optional_service.create_optional_option(
            option_in=option_in,
            current_user_id=current_user.id
        )
        
        return ProductOptionalOptionResponse.model_validate(created_option)
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"Error creating optional option: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/options/", response_model=List[ProductOptionalOptionResponse])
async def read_optional_options(
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Optional[Tenant], Depends(get_current_tenant_from_header)],
    optional_service: Annotated[ProductOptionalService, Depends(get_optional_service)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))],
    optional_group_id: Optional[uuid.UUID] = Query(None),
    is_active: Optional[bool] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=200),
):
    """Retrieve optional options."""
    try:
        tenant_id = current_tenant.id if current_tenant else None
        
        options = await optional_service.get_optional_options(
            optional_group_id=optional_group_id,
            tenant_id=tenant_id,
            is_active=is_active,
            skip=skip,
            limit=limit,
        )

        return [ProductOptionalOptionResponse.model_validate(option) for option in options]
    except Exception as e:
        logger.exception(f"Error retrieving optional options: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/options/{option_id}", response_model=ProductOptionalOptionResponse)
async def read_optional_option(
    option_id: Annotated[uuid.UUID, Path(...)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Optional[Tenant], Depends(get_current_tenant_from_header)],
    optional_service: Annotated[ProductOptionalService, Depends(get_optional_service)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))],
):
    """Retrieve a specific optional option."""
    try:
        tenant_id = current_tenant.id if current_tenant else None
        
        option = await optional_service.get_optional_option(option_id=option_id, tenant_id=tenant_id)
        
        if option is None:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Optional option not found")

        return ProductOptionalOptionResponse.model_validate(option)
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"Error retrieving optional option {option_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
