# Eshop - Products

**Categoria:** Eshop
**Módulo:** Products
**Total de Endpoints:** 6
**Gerado em:** 27/06/2025, 20:20:26

## 📋 Endpoints

- [GET /api/eshop/products/](#get-apieshopproducts) - List Products
- [POST /api/eshop/products/](#post-apieshopproducts) - Create Product
- [DELETE /api/eshop/products/{product_id}](#delete-apieshopproductsproduct-id) - Delete Product
- [GET /api/eshop/products/{product_id}](#get-apieshopproductsproduct-id) - Get Product
- [PUT /api/eshop/products/{product_id}](#put-apieshopproductsproduct-id) - Update Product
- [PATCH /api/eshop/products/{product_id}/stock](#patch-apieshopproductsproduct-idstock) - Update Product Stock

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### ProductResponse

**Descrição:** Schema for product responses.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Product name |
| `description` | unknown | ❌ | Product description |
| `short_description` | unknown | ❌ | Short product description |
| `slug` | string | ✅ | URL-friendly product name |
| `sku` | unknown | ❌ | Stock Keeping Unit |
| `category_id` | string | ✅ | Product category ID |
| `product_type` | ProductType | ❌ | Product type |
| `status` | ProductStatus | ❌ | Product status |
| `base_price` | string | ✅ | Base product price |
| `sale_price` | unknown | ❌ | Sale price (discounted) |
| `cost_price` | unknown | ❌ | Cost price for profit calculation |
| `stock_quantity` | integer | ❌ | Current stock quantity |
| `low_stock_threshold` | integer | ❌ | Low stock alert threshold |
| `manage_stock` | boolean | ❌ | Whether to manage stock for this product |
| `allow_backorders` | boolean | ❌ | Allow orders when out of stock |
| `digital_file_url` | unknown | ❌ | Digital file URL |
| `download_limit` | unknown | ❌ | Maximum downloads per purchase |
| `download_expiry_days` | unknown | ❌ | Days until download expires |
| `weight` | unknown | ❌ | Product weight in kg |
| `dimensions` | unknown | ❌ | Product dimensions |
| `shipping_required` | boolean | ❌ | Whether shipping is required |
| `shipping_class` | unknown | ❌ | Shipping class |
| `meta_title` | unknown | ❌ | SEO meta title |
| `meta_description` | unknown | ❌ | SEO meta description |
| `featured_image_url` | unknown | ❌ | Featured image URL |
| `gallery_images` | unknown | ❌ | Gallery image URLs |
| `display_order` | integer | ❌ | Display order |
| `is_featured` | boolean | ❌ | Whether product is featured |
| `is_virtual` | boolean | ❌ | Whether product is virtual (no shipping) |
| `attributes` | unknown | ❌ | Custom product attributes |
| `tags` | unknown | ❌ | Product tags |
| `market_type` | MarketType | ❌ | Market type for B2B/B2C operations |
| `approval_status` | ApprovalStatus | ❌ | Product approval status |
| `commission_rate` | unknown | ❌ | Commission rate percentage |
| `legacy_cuponic_id` | unknown | ❌ | Legacy Cuponic ID for backward compatibility |
| `id` | string | ✅ | - |
| `tenant_id` | unknown | ✅ | - |
| `vendor_id` | string | ✅ | - |
| `average_rating` | string | ✅ | - |
| `review_count` | integer | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `approved_at` | unknown | ❌ | Approval timestamp |
| `approved_by` | unknown | ❌ | Approver user ID |
| `rejection_reason` | unknown | ❌ | Reason for rejection |

### eshopProductCreate

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `description_short` | unknown | ❌ | - |
| `description_long` | unknown | ❌ | - |
| `base_price` | number | ✅ | - |
| `product_type` | ProductType | ✅ | - |
| `is_active` | boolean | ❌ | - |
| `category_id` | unknown | ❌ | - |
| `supplier_id` | unknown | ❌ | - |

### eshopProductRead

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `description_short` | unknown | ❌ | - |
| `description_long` | unknown | ❌ | - |
| `base_price` | number | ✅ | - |
| `product_type` | ProductType | ✅ | - |
| `is_active` | boolean | ❌ | - |
| `category_id` | unknown | ❌ | - |
| `supplier_id` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `variant_groups` | Array[app__modules__core__eshop__schemas__eshop_product__VariantGroupRead] | ❌ | - |
| `modifier_groups` | Array[app__modules__core__eshop__schemas__eshop_product__ModifierGroupRead] | ❌ | - |
| `optional_groups` | Array[app__modules__core__eshop__schemas__eshop_product__OptionalGroupRead] | ❌ | - |

### eshopProductUpdate

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | - |
| `description_short` | unknown | ❌ | - |
| `description_long` | unknown | ❌ | - |
| `base_price` | unknown | ❌ | - |
| `product_type` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `category_id` | unknown | ❌ | - |
| `supplier_id` | unknown | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/eshop/products/ {#get-apieshopproducts}

**Resumo:** List Products
**Descrição:** List products for the current tenant, with optional filters.
Requires 'owner', 'admin', or 'staff' role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `market_type` | string | query | ❌ | - |
| `category_slug` | string | query | ❌ | - |
| `status` | string | query | ❌ | - |
| `min_price` | string | query | ❌ | - |
| `max_price` | string | query | ❌ | - |
| `search` | string | query | ❌ | - |
| `page` | integer | query | ❌ | - |
| `page_size` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/products/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/eshop/products/ {#post-apieshopproducts}

**Resumo:** Create Product
**Descrição:** Creates a new product.

Only users with an **Admin** role or a **TVendorSupplier** association can create products.
The `vendor_id` is automatically set to the current user's ID.
If a tenant is present in the header, the product is associated with that tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [eshopProductCreate](#eshopproductcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [eshopProductRead](#eshopproductread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/eshop/products/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/eshop/products/{product_id} {#delete-apieshopproductsproduct-id}

**Resumo:** Delete Product
**Descrição:** Deletes a product.

Only the product's original vendor or an Admin can delete it.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `product_id` | string | path | ✅ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/eshop/products/{product_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/eshop/products/{product_id} {#get-apieshopproductsproduct-id}

**Resumo:** Get Product

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `product_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [eshopProductRead](#eshopproductread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/products/{product_id}"
```

---

### PUT /api/eshop/products/{product_id} {#put-apieshopproductsproduct-id}

**Resumo:** Update Product
**Descrição:** Updates a product.

Only the product's original vendor or an Admin can update it.
This check is performed within the service layer.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `product_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [eshopProductUpdate](#eshopproductupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [eshopProductRead](#eshopproductread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/eshop/products/{product_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PATCH /api/eshop/products/{product_id}/stock {#patch-apieshopproductsproduct-idstock}

**Resumo:** Update Product Stock
**Descrição:** Update product stock quantity.
Only the product owner (vendor) or admin can update stock.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `product_id` | string | path | ✅ | The ID of the product to update stock |
| `quantity_change` | integer | query | ✅ | Quantity change (positive or negative) |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductResponse](#productresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PATCH "http://localhost:8000/api/eshop/products/{product_id}/stock" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
