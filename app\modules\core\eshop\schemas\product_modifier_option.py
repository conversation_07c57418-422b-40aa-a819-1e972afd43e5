from typing import Optional
from pydantic import BaseModel, Field
from uuid import UUID
from decimal import Decimal


class ProductModifierOptionBase(BaseModel):
    """Base schema for product modifier options."""
    name: str = Field(..., min_length=1, max_length=100, description="Modifier option name")
    description: Optional[str] = Field(None, max_length=255, description="Modifier option description")
    price_adjustment: Decimal = Field(0.0, description="Additional cost for this modifier")
    price_adjustment_type: str = Field("fixed", pattern="^(fixed|percentage)$", description="Price adjustment type")
    display_order: int = Field(0, description="Display order within the group")
    is_default: bool = Field(False, description="Whether this is the default selection")
    is_active: bool = Field(True, description="Whether this modifier option is active")
    stock_quantity: Optional[int] = Field(None, ge=0, description="Limited availability for this modifier")
    image_url: Optional[str] = Field(None, max_length=500, description="Image URL for this modifier")


class ProductModifierOptionCreate(ProductModifierOptionBase):
    """Schema for creating a new product modifier option."""
    tenant_id: Optional[UUID] = Field(None, description="Tenant ID (null for global options)")
    modifier_group_id: UUID = Field(..., description="Parent modifier group ID")


class ProductModifierOptionUpdate(BaseModel):
    """Schema for updating a product modifier option."""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="Modifier option name")
    description: Optional[str] = Field(None, max_length=255, description="Modifier option description")
    price_adjustment: Optional[Decimal] = Field(None, description="Additional cost for this modifier")
    price_adjustment_type: Optional[str] = Field(None, pattern="^(fixed|percentage)$", description="Price adjustment type")
    display_order: Optional[int] = Field(None, description="Display order within the group")
    is_default: Optional[bool] = Field(None, description="Whether this is the default selection")
    is_active: Optional[bool] = Field(None, description="Whether this modifier option is active")
    stock_quantity: Optional[int] = Field(None, ge=0, description="Limited availability for this modifier")
    image_url: Optional[str] = Field(None, max_length=500, description="Image URL for this modifier")


class ProductModifierOptionResponse(ProductModifierOptionBase):
    """Schema for product modifier option responses."""
    id: UUID
    tenant_id: Optional[UUID]
    modifier_group_id: UUID
    
    class Config:
        from_attributes = True
