# I18N - Translation Changes

**Categoria:** I18N
**Módulo:** Translation Changes
**Total de Endpoints:** 2
**Gerado em:** 08/06/2025, 17:26:01

## 📋 Endpoints

- [GET /api/i18n/translation-changes/by-sector](#get-apii18ntranslation-changesby-sector) - Get Translations By Sector
- [GET /api/i18n/translation-changes/changes](#get-apii18ntranslation-changeschanges) - Get Translation Changes

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### LanguageChanges

**Descrição:** Schema for all changes for a language since a specific version.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `language_code` | string | ✅ | - |
| `current_version_code` | string | ✅ | - |
| `has_changes` | boolean | ✅ | - |
| `sectors` | Array[SectorChanges] | ✅ | - |

## 🔗 Endpoints Detalhados

### GET /api/i18n/translation-changes/by-sector {#get-apii18ntranslation-changesby-sector}

**Resumo:** Get Translations By Sector
**Descrição:** Get all translations for a language, grouped by sector.
This endpoint is public and does not require authentication.

Returns a list of sectors with their translations.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `language_code` | string | query | ✅ | Language code |
| `sector` | string | query | ❌ | Filter by sector |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/i18n/translation-changes/by-sector"
```

---

### GET /api/i18n/translation-changes/changes {#get-apii18ntranslation-changeschanges}

**Resumo:** Get Translation Changes
**Descrição:** Get all translation changes for a language since a specific version.
This endpoint is public and does not require authentication.

Returns a JSON object with:
- language_code: The language code
- current_version_code: The current version code on the server
- has_changes: Boolean indicating if there are changes
- sectors: List of sectors with their changes

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `language_code` | string | query | ✅ | Language code |
| `since_version` | string | query | ✅ | Get changes since this version |
| `sector` | string | query | ❌ | Filter by sector |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LanguageChanges](#languagechanges)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/i18n/translation-changes/changes"
```

---
