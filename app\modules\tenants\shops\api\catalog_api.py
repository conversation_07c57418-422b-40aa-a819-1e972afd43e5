import uuid
from typing import List, Optional, Annotated  # Import Annotated
from fastapi import (
    APIRouter,
    Depends,
    HTTPException,
    status,
    Query,
    Path,
)  # Import Path

from sqlalchemy.ext.asyncio import AsyncSession  # noqa: E402

# Import correct dependencies
from app.core.db_dependencies import get_db  # noqa: E402
from app.modules.core.tenants.dependencies.tenant_dependencies import (  # noqa: E402
    get_current_tenant_from_header as get_current_tenant,
    require_tenant_role,
)
from app.core.dependencies import (
    get_product_service,
)  # Import the dependency function  # noqa: E402
from app.modules.core.roles.models.roles import (
    RolePermissions,
)  # Importar RolePermissions  # noqa: E402
from app.modules.core.tenants.models.tenant import Tenant
from app.modules.core.users.models.user import User  # Import User model
from app.modules.tenants.shops.schemas.product import (
    ProductCreate,
    ProductUpdate,
    ProductRead,
)
from app.modules.tenants.shops.services.product_service import (  # noqa: E402
    ProductService,
)  # Import the class

router = APIRouter(
    prefix="/catalog",  # More descriptive prefix
    tags=["Shops - Catalog Management"],
)

# Define roles
write_roles = RolePermissions.ADMIN_ROLES  # Usar RolePermissions


@router.post(
    "/products/",  # Added trailing slash for consistency
    response_model=ProductRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new product",
    description="Creates a new product associated with the current tenant.",
)
async def create_product(
    product_in: ProductCreate,  # Body parameter
    # Dependencies using Annotated
    db: Annotated[AsyncSession, Depends(get_db)],
    current_tenant: Annotated[Tenant, Depends(get_current_tenant)],
    product_service: Annotated[ProductService, Depends(get_product_service)],
    current_user: Annotated[
        User,
        Depends(require_tenant_role(required_roles=write_roles, tenant_id_source="header")),
    ],
):
    """
    Endpoint to create a new product for the logged-in tenant.
    Requires 'admin' or 'manager' role.
    """
    try:
        product = await product_service.create_product(
            db=db, product_in=product_in, tenant_id=current_tenant.id
        )
        return product
    except ValueError as e:
        # Handle validation errors from the service (e.g., invalid category/inventory item)
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception:
        # Log the exception e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create product.",
        )


@router.get(
    "/products/",  # Added trailing slash
    response_model=List[ProductRead],
    summary="List products",
    description="Retrieves a list of products for the current tenant, with optional filters.",
)
async def list_products(
    # Dependencies first (reordered to satisfy linter)
    db: Annotated[AsyncSession, Depends(get_db)],
    current_tenant: Annotated[Tenant, Depends(get_current_tenant)],
    product_service: Annotated[ProductService, Depends(get_product_service)],
    # Protegido por role
    current_user: Annotated[
        User,
        Depends(
            require_tenant_role(
                required_roles=RolePermissions.VIEW_ROLES,
                tenant_id_source="header",
            )
        ),
    ],
    # Query parameters next
    skip: int = Query(0, ge=0, description="Number of items to skip"),
    limit: int = Query(100, ge=1, le=200, description="Maximum number of items to return"),
    name: Optional[str] = Query(
        None, description="Filter products by name (case-insensitive, partial match)"
    ),
    sku: Optional[str] = Query(None, description="Filter products by SKU (exact match)"),
    category_id: Optional[uuid.UUID] = Query(None, description="Filter by category ID"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    is_featured: Optional[bool] = Query(None, description="Filter by featured status"),
):
    """
    Endpoint to list products belonging to the current tenant.
    Supports pagination and various filters. Requires at least costumer role in the tenant.
    """
    products = await product_service.get_products(
        db=db,
        tenant_id=current_tenant.id,
        skip=skip,
        limit=limit,
        name=name,
        sku=sku,
        category_id=category_id,
        is_active=is_active,
        is_featured=is_featured,
    )
    return products


@router.get(
    "/products/{product_id}",
    response_model=ProductRead,
    summary="Get product details",
    description="Retrieves details for a specific product belonging to the current tenant.",
)
async def get_product(
    # Path param
    product_id: Annotated[uuid.UUID, Path(..., description="ID of the product to retrieve")],
    # Dependencies using Annotated
    db: Annotated[AsyncSession, Depends(get_db)],
    current_tenant: Annotated[Tenant, Depends(get_current_tenant)],
    product_service: Annotated[ProductService, Depends(get_product_service)],
    # Protegido por role
    current_user: Annotated[
        User,
        Depends(
            require_tenant_role(
                required_roles=RolePermissions.VIEW_ROLES,
                tenant_id_source="header",
            )
        ),
    ],
):
    """
    Endpoint to get a specific product by its ID, ensuring it belongs to the tenant.
    Requires at least costumer role in the tenant.
    """
    product = await product_service.get_product(
        db=db, product_id=product_id, tenant_id=current_tenant.id
    )
    if not product:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Product not found or does not belong to this tenant.",
        )
    return product


@router.put(
    "/products/{product_id}",
    response_model=ProductRead,
    summary="Update a product",
    description="Updates an existing product belonging to the current tenant.",
)
async def update_product(
    # Path param
    product_id: Annotated[uuid.UUID, Path(..., description="ID of the product to update")],
    product_in: ProductUpdate,  # Body parameter
    # Dependencies using Annotated
    db: Annotated[AsyncSession, Depends(get_db)],
    current_tenant: Annotated[Tenant, Depends(get_current_tenant)],
    product_service: Annotated[ProductService, Depends(get_product_service)],
    current_user: Annotated[
        User,
        Depends(require_tenant_role(required_roles=write_roles, tenant_id_source="header")),
    ],
):
    """
    Endpoint to update a product. It first verifies ownership before updating.
    Requires 'admin' or 'manager' role.
    """
    db_product = await product_service.get_product(
        db=db, product_id=product_id, tenant_id=current_tenant.id
    )
    if not db_product:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Product not found or does not belong to this tenant.",
        )

    # Check if there's anything to update (moved check inside try-except)

    try:
        if not product_in.model_dump(exclude_unset=True):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No update data provided.",
            )
        updated_product = await product_service.update_product(
            db=db, db_obj=db_product, product_in=product_in, tenant_id=current_tenant.id
        )
        return updated_product
    except ValueError as e:
        # Handle validation errors from the service
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception:
        # Log the exception e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update product.",
        )


@router.delete(
    "/products/{product_id}",
    response_model=ProductRead,  # Or return status 204 and no body
    summary="Delete a product",
    description="Deletes a specific product belonging to the current tenant.",
)
async def delete_product(
    # Path param
    product_id: Annotated[uuid.UUID, Path(..., description="ID of the product to delete")],
    # Dependencies using Annotated
    db: Annotated[AsyncSession, Depends(get_db)],
    current_tenant: Annotated[Tenant, Depends(get_current_tenant)],
    product_service: Annotated[ProductService, Depends(get_product_service)],
    current_user: Annotated[
        User,
        Depends(require_tenant_role(required_roles=write_roles, tenant_id_source="header")),
    ],
):
    """
    Endpoint to delete a product, ensuring it belongs to the tenant.
    Returns status 204 on success. Requires 'admin' or 'manager' role.
    """
    deleted_product = await product_service.remove_product(
        db=db, product_id=product_id, tenant_id=current_tenant.id
    )
    if not deleted_product:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Product not found or does not belong to this tenant.",
        )
    # Return 204 No Content on successful deletion
    return None
