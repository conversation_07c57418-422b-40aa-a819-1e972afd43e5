"""Configuration service for Domain Rent module."""

import logging  # noqa: E402
import os
from typing import Dict, List, Optional, Any

from app.modules.shared.domain_rent.processors.base_processor import BaseProcessor  # noqa: E402
from app.modules.shared.domain_rent.processors.godaddy_processor import GoDaddyProcessor
from app.modules.shared.domain_rent.processors.namecheap_processor import (
    NamecheapProcessor,
)
from app.modules.shared.domain_rent.processors.processor_aggregator import (  # noqa: E402
    ProcessorAggregator,
)
from app.modules.shared.domain_rent.exceptions import ConfigurationError  # noqa: E402


logger = logging.getLogger(__name__)


class ConfigService:
    """Service for loading and managing registrar configurations."""

    def __init__(self):
        """Initialize config service."""
        self.processor_aggregator = ProcessorAggregator()
        self._load_registrar_configs()

    def _load_registrar_configs(self) -> None:
        """Load registrar configurations from environment variables."""
        # Load GoDaddy configuration
        if self._is_registrar_enabled("GODADDY"):
            try:
                godaddy_processor = GoDaddyProcessor(
                    api_key=os.environ.get("GODADDY_API_KEY", ""),
                    api_secret=os.environ.get("GODADDY_API_SECRET", ""),
                    api_url=os.environ.get("GODADDY_API_BASE_URL", "https://api.godaddy.com/"),
                    is_production=os.environ.get("GODADDY_USE_SANDBOX", "true").lower() != "true",
                )
                self.processor_aggregator.add_processor(godaddy_processor)
                logger.info("GoDaddy processor loaded")
            except Exception as e:
                logger.error(f"Error loading GoDaddy processor: {str(e)}")

        # Load Namecheap configuration
        if self._is_registrar_enabled("NAMECHEAP"):
            try:
                api_url = os.environ.get(
                    "NAMECHEAP_API_BASE_URL_PRODUCTION",
                    "https://api.namecheap.com/xml.response",
                )
                if os.environ.get("NAMECHEAP_USE_SANDBOX", "false").lower() == "true":
                    api_url = os.environ.get(
                        "NAMECHEAP_API_BASE_URL_SANDBOX",
                        "https://api.sandbox.namecheap.com/xml.response",
                    )

                namecheap_processor = NamecheapProcessor(
                    api_user=os.environ.get("NAMECHEAP_API_USER", ""),
                    api_key=os.environ.get("NAMECHEAP_API_KEY", ""),
                    client_ip=os.environ.get("NAMECHEAP_CLIENT_IP", ""),
                    api_url=api_url,
                    is_sandbox=os.environ.get("NAMECHEAP_USE_SANDBOX", "false").lower() == "true",
                )
                self.processor_aggregator.add_processor(namecheap_processor)
                logger.info("Namecheap processor loaded")
            except Exception as e:
                logger.error(f"Error loading Namecheap processor: {str(e)}")

    def _is_registrar_enabled(self, registrar: str) -> bool:
        """Check if a registrar is enabled in the environment.

        Args:
            registrar: Registrar name (uppercase)

        Returns:
            True if the registrar is enabled, False otherwise
        """
        enabled_var = f"{registrar}_API_ENABLED"
        return os.environ.get(enabled_var, "false").lower() == "true"

    def get_processor_aggregator(self) -> ProcessorAggregator:
        """Get the processor aggregator.

        Returns:
            Processor aggregator
        """
        return self.processor_aggregator

    def get_processor(self, registrar: str) -> BaseProcessor:
        """Get a processor by registrar name.

        Args:
            registrar: Registrar name

        Returns:
            Processor for the specified registrar
        """
        return self.processor_aggregator.get_processor(registrar)

    def get_active_processors(self) -> List[BaseProcessor]:
        """Get all active processors.

        Returns:
            List of active processors
        """
        return self.processor_aggregator.get_active_processors()

    def get_active_registrars(self) -> List[str]:
        """Get all active registrar names.

        Returns:
            List of active registrar names
        """
        return [p.registrar_name for p in self.get_active_processors()]
