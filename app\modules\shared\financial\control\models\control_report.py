"""Financial Control Report Models."""

import uuid
import enum
from typing import TYPE_CHECKING
from sqlalchemy import (
    Column, String, ForeignKey, Text, Enum, DateTime, 
    Boolean, func, Index
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base

if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.core.users.models.user import User


class ReportType(str, enum.Enum):
    """Enum for report types."""
    
    CASH_FLOW = "cash_flow"
    CATEGORY_BREAKDOWN = "category_breakdown"
    MONTHLY_SUMMARY = "monthly_summary"
    QUARTERLY_SUMMARY = "quarterly_summary"
    ANNUAL_SUMMARY = "annual_summary"
    COMPARATIVE = "comparative"
    TAX_REPORT = "tax_report"
    BUDGET_ANALYSIS = "budget_analysis"
    SUPPLIER_ANALYSIS = "supplier_analysis"
    CUSTOM = "custom"


class ReportStatus(str, enum.Enum):
    """Enum for report status."""
    
    GENERATING = "generating"
    COMPLETED = "completed"
    FAILED = "failed"
    EXPIRED = "expired"


class ControlReport(Base):
    """
    Model for financial control reports.
    
    Stores generated reports with metadata and provides
    access to report data and export capabilities.
    """
    
    __tablename__ = "financial_control_reports"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("tenants.id"), 
        nullable=False, 
        index=True
    )
    
    # Report details
    report_type = Column(Enum(ReportType), nullable=False, index=True)
    status = Column(Enum(ReportStatus), nullable=False, default=ReportStatus.GENERATING, index=True)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Report parameters
    parameters = Column(Text, nullable=True)  # JSON with report parameters
    filters = Column(Text, nullable=True)     # JSON with applied filters
    
    # Report period
    period_start = Column(DateTime, nullable=True, index=True)
    period_end = Column(DateTime, nullable=True, index=True)
    
    # Report data
    data = Column(Text, nullable=True)        # JSON with report data
    summary = Column(Text, nullable=True)     # JSON with summary metrics
    charts_data = Column(Text, nullable=True) # JSON with chart configurations
    
    # Report metadata
    total_records = Column(String, nullable=True)  # Number of records processed
    file_size = Column(String, nullable=True)      # Size of generated report
    format = Column(String(20), nullable=True)     # Export format (pdf, excel, csv)
    
    # File storage
    file_path = Column(String(500), nullable=True)  # Path to generated file
    file_url = Column(String(500), nullable=True)   # URL to access file
    
    # Report lifecycle
    expires_at = Column(DateTime, nullable=True, index=True)
    is_scheduled = Column(Boolean, nullable=False, default=False)
    schedule_config = Column(Text, nullable=True)  # JSON with schedule configuration
    
    # Error handling
    error_message = Column(Text, nullable=True)
    error_details = Column(Text, nullable=True)  # JSON with error details
    retry_count = Column(String, nullable=False, default=0)
    
    # Audit fields
    generated_by = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        index=True
    )
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    generated_at = Column(DateTime, nullable=False, default=func.now())
    completed_at = Column(DateTime, nullable=True)
    updated_at = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now())
    
    # Access control
    is_public = Column(Boolean, nullable=False, default=False)
    shared_with = Column(Text, nullable=True)  # JSON array of user IDs
    
    # Additional metadata
    tags = Column(Text, nullable=True)  # JSON array of tags
    additional_data = Column(Text, nullable=True)  # JSON for additional data
    
    # Relationships
    tenant = relationship("app.modules.core.tenants.models.tenant.Tenant", viewonly=True)
    generated_by_user = relationship(
        "app.modules.core.users.models.user.User", 
        foreign_keys=[generated_by],
        viewonly=True
    )
    updated_by_user = relationship(
        "app.modules.core.users.models.user.User", 
        foreign_keys=[updated_by],
        viewonly=True
    )
    
    # Table indexes for performance
    __table_args__ = (
        Index("ix_control_reports_tenant_type", "tenant_id", "report_type"),
        Index("ix_control_reports_tenant_status", "tenant_id", "status"),
        Index("ix_control_reports_tenant_period", "tenant_id", "period_start", "period_end"),
        Index("ix_control_reports_expires_at", "expires_at"),
        Index("ix_control_reports_scheduled", "is_scheduled"),
    )
    
    def __repr__(self):
        return (
            f"<ControlReport(id={self.id}, "
            f"type='{self.report_type}', "
            f"status='{self.status}')>"
        )
    
    @property
    def is_expired(self) -> bool:
        """Check if report is expired."""
        if not self.expires_at:
            return False
        from datetime import datetime
        return self.expires_at < datetime.utcnow()
    
    @property
    def duration_seconds(self) -> int:
        """Get report generation duration in seconds."""
        if self.completed_at and self.generated_at:
            delta = self.completed_at - self.generated_at
            return int(delta.total_seconds())
        return 0
