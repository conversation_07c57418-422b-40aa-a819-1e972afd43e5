import uuid
import logging
from typing import Optional, List, TYPE_CHECKING

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from sqlalchemy import delete

from app.modules.core.users.models.tenant_user_association import TenantUserAssociation
from app.modules.core.tenants.models.tenant import Tenant
from app.modules.core.tenants.services.tenant_service import tenant_service
from app.modules.core.roles.models.roles import TenantRole, TenantStaffSubRole, TenantType

# Use TYPE_CHECKING to avoid circular import
if TYPE_CHECKING:
    from app.modules.core.users.models.user import User

logger = logging.getLogger(__name__)


class TenantUserAssociationService:
    """
    Service for managing associations between Users and Tenants.
    """

    async def add_user_to_tenant(
        self,
        db: AsyncSession,
        *,
        user_id: uuid.UUID,
        tenant_id: uuid.UUID,
        role: TenantRole,
        staff_sub_role: Optional[TenantStaffSubRole] = None,
        data_sharing_consent: Optional[bool] = False,
    ) -> TenantUserAssociation:
        """
        Associates a user with a tenant with a specific role and, optionally, a staff sub-role.
        If the association already exists, updates the role.

        Args:
            db: The async database session.
            user_id: The user's ID.
            tenant_id: The tenant's ID.
            role: The role to assign to the user in the tenant.
            staff_sub_role: Optional sub-role for staff members.
            data_sharing_consent: Whether the user consents to share data with this tenant.

        Returns:
            The created or updated TenantUserAssociation object.

        Raises:
            ValueError: If the user or tenant doesn't exist, or if a database error occurs.
        """
        # Import here to avoid circular import
        from app.modules.core.users.models.user import User
        
        # 1. Check if the user exists and is active
        # We need to check if the user exists directly since we can't import user_service
        # due to circular imports
        stmt = select(User).where(User.id == user_id)
        result = await db.execute(stmt)
        user = result.scalars().first()

        if not user or not user.is_active:
            raise ValueError(f"User with ID {user_id} not found or inactive.")

        # 2. Check if the tenant exists and is active
        tenant = await tenant_service.get_tenant(db, tenant_id=tenant_id)
        if not tenant:  # get_tenant already checks is_active
            raise ValueError(f"Tenant with ID {tenant_id} not found or inactive.")

        # 3. Check if the association already exists
        existing_assoc = await self._get_association(db, user_id=user_id, tenant_id=tenant_id)

        # Validate sub_role: can only exist if role is STAFF and must be compatible with tenant_type
        # Note: tenant_type is now stored in TenantSettings, not Tenant model
        # For now, we allow all sub-roles since we're focusing on restaurant functionality
        if role == TenantRole.STAFF:
            if staff_sub_role:
                # Allow all sub-roles for now (tenant_type validation moved to TenantSettings)
                valid_sub_roles_for_type = [sub_role for sub_role in TenantStaffSubRole]

                if staff_sub_role not in valid_sub_roles_for_type:
                    raise ValueError(
                        f"Invalid staff_sub_role '{staff_sub_role.value}' for tenant type '{tenant.tenant_type}'. "
                        f"Allowed: {[r.value for r in valid_sub_roles_for_type]}"
                    )
        elif staff_sub_role is not None:
            # If the role is not STAFF, the sub_role must be None.
            raise ValueError("staff_sub_role can only be set if role is STAFF.")

        # 4. Create or update the association
        try:
            if existing_assoc:
                # Update existing association
                existing_assoc.role = role.value
                existing_assoc.staff_sub_role = staff_sub_role.value if staff_sub_role else None
                existing_assoc.data_sharing_consent = data_sharing_consent
                db.add(existing_assoc)
                await db.commit()
                await db.refresh(existing_assoc)
                logger.info(
                    f"Updated association: User {user_id} in Tenant {tenant_id} with role {role.value}"
                )
                return existing_assoc
            else:
                # Create new association
                new_assoc = TenantUserAssociation(
                    user_id=user_id,
                    tenant_id=tenant_id,
                    role=role.value,
                    staff_sub_role=staff_sub_role.value if staff_sub_role else None,
                    data_sharing_consent=data_sharing_consent,
                )
                db.add(new_assoc)
                await db.commit()
                await db.refresh(new_assoc)
                logger.info(
                    f"Created association: User {user_id} in Tenant {tenant_id} with role {role.value}"
                )
                return new_assoc
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(f"Database error managing user-tenant association: {e}", exc_info=True)
            raise ValueError(f"Could not manage user-tenant association: {e}")

    async def get_user_tenant_role(
        self, db: AsyncSession, *, user_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Optional[str]:
        """
        Gets the role of a user in a specific tenant.

        Args:
            db: The async database session.
            user_id: The user's ID.
            tenant_id: The tenant's ID.

        Returns:
            The role string if the association exists, otherwise None.
        """
        association = await self._get_association(db, user_id=user_id, tenant_id=tenant_id)
        return association.role if association else None

    async def _get_association(
        self, db: AsyncSession, *, user_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Optional[TenantUserAssociation]:
        """
        Gets the association between a user and a tenant.

        Args:
            db: The async database session.
            user_id: The user's ID.
            tenant_id: The tenant's ID.

        Returns:
            The TenantUserAssociation object if found, otherwise None.
        """
        stmt = select(TenantUserAssociation).where(
            TenantUserAssociation.user_id == user_id,
            TenantUserAssociation.tenant_id == tenant_id,
        )
        result = await db.execute(stmt)
        return result.scalars().first()

    # Alias for _get_association to maintain compatibility with dependencies
    async def get_association_by_user_and_tenant(
        self, db: AsyncSession, *, user_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Optional[TenantUserAssociation]:
        """Alias for _get_association to maintain compatibility with dependencies."""
        return await self._get_association(db, user_id=user_id, tenant_id=tenant_id)


# Create a singleton instance
tenant_user_association_service = TenantUserAssociationService()
