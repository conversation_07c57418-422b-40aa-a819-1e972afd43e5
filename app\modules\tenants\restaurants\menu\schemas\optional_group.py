import uuid
from pydantic import BaseModel, ConfigDict, Field, model_validator
from typing import Optional, List, Union
from .optional_option import OptionalOptionRead, OptionalOptionCreate


class OptionalGroupBase(BaseModel):
    """Base schema for Optional Group."""

    name: str = Field(
        ...,
        max_length=100,
        description="Name of the optional group (e.g., Sides, Drinks)",
    )
    description: Optional[str] = Field(None, max_length=255, description="Optional description")
    min_selection: int = Field(0, ge=0, description="Minimum selections (always 0 for optionals)")
    max_selection: int = Field(5, ge=1, description="Maximum selections allowed")
    display_order: int = Field(0, description="Order within the menu item")
    is_required: bool = Field(False, description="Is this group required for the item?")
    is_active: bool = Field(True, description="Is this group active for the item?")

    @model_validator(mode="after")
    def check_max_ge_min(self) -> "OptionalGroupBase":
        if self.max_selection < self.min_selection:
            raise ValueError("max_selection must be greater than or equal to min_selection")
        # Ensure min_selection is always 0 for optionals
        if self.min_selection != 0:
            raise ValueError("min_selection must be 0 for optional groups")
        return self


class OptionalGroupCreate(OptionalGroupBase):
    """Schema for creating a new Optional Group, including nested options."""

    id: Optional[Union[uuid.UUID, str]] = Field(None, description="Optional ID for existing groups")
    options: List[OptionalOptionCreate] = Field(
        [], description="Optional options within this group"
    )
    # menu_item_id will be path parameter or part of a nested structure
    # tenant_id will be added by the service


class OptionalGroupUpdate(OptionalGroupBase):
    """Schema for updating an existing Optional Group. All fields optional."""

    name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = Field(None, max_length=255)
    min_selection: Optional[int] = Field(None, ge=0)
    max_selection: Optional[int] = Field(None, ge=1)
    display_order: Optional[int] = None
    is_required: Optional[bool] = None
    is_active: Optional[bool] = None
    # Updating options might be handled by separate endpoints

    @model_validator(mode="after")
    def check_max_ge_min_update(self) -> "OptionalGroupUpdate":
        if self.min_selection is not None and self.max_selection is not None:
            if self.max_selection < self.min_selection:
                raise ValueError(
                    "max_selection must be greater than or equal to min_selection if both are provided"
                )
        # Ensure min_selection is always 0 for optionals if provided
        if self.min_selection is not None and self.min_selection != 0:
            raise ValueError("min_selection must be 0 for optional groups")
        return self


class OptionalGroupRead(OptionalGroupBase):
    """Schema for reading an Optional Group, including its options."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    menu_item_id: Optional[uuid.UUID] = None  # Nullable for template groups
    options: List[OptionalOptionRead] = []  # Include the options

    model_config = ConfigDict(from_attributes=True)


# Simplified version without options for list performance
class OptionalGroupReadSimple(BaseModel):
    """Schema for reading an Optional Group without options for list performance."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    menu_item_id: Optional[uuid.UUID] = None  # Nullable for template groups
    name: str
    description: Optional[str] = None
    min_selection: int
    max_selection: int
    display_order: int
    is_required: bool = False
    is_active: bool = True
    usage_count: Optional[int] = 0

    model_config = ConfigDict(from_attributes=True)


# Version with options for template functionality
class OptionalGroupReadWithOptions(BaseModel):
    """Schema for reading an Optional Group with options."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    menu_item_id: Optional[uuid.UUID] = None  # Nullable for template groups
    name: str
    description: Optional[str]
    min_selection: int
    max_selection: int
    display_order: int
    is_required: bool = False
    is_active: bool = True
    options: List[OptionalOptionRead] = []  # Include the options
    usage_count: Optional[int] = 0

    model_config = ConfigDict(from_attributes=True)


# No template-specific schemas needed anymore
# Groups are now shared directly without template/instance distinction
