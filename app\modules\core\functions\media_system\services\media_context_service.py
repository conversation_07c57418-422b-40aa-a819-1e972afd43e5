"""
Media Context Service

Serviço para gerenciamento de contextos de mídia.
"""

import logging
from typing import Optional, List
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError
from sqlalchemy import select, text

from ..models import MediaContext, MediaContextType
from ..schemas import (
    MediaContextCreate,
    MediaContextUpdate,
    MediaContextRead,
    MediaQuotaResponse
)

logger = logging.getLogger(__name__)


class MediaContextService:
    """Serviço para gerenciamento de contextos de mídia."""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_or_create_context(
        self,
        context_type: MediaContextType,
        context_id: UUID,
        uploaded_by: UUID = None
    ) -> MediaContext:
        """
        Obtém ou cria um contexto de mídia.
        
        Args:
            context_type: Tipo do contexto (USER ou TENANT)
            context_id: ID do contexto (user_id ou tenant_id)
            uploaded_by: ID do usuário que criou o contexto
            
        Returns:
            MediaContext: Contexto de mídia
        """
        try:
            # Tenta buscar contexto existente
            # Usa SQL direto para evitar problemas com enum
            context_type_str = context_type.value if hasattr(context_type, 'value') else str(context_type)
            stmt = text("""
                SELECT * FROM media_contexts
                WHERE context_type = :context_type AND context_id = :context_id
            """)
            result = await self.db.execute(stmt, {
                "context_type": context_type_str,
                "context_id": context_id
            })
            row = result.fetchone()

            if row:
                # Converte row para objeto MediaContext
                context = MediaContext(
                    id=row.id,
                    context_type=row.context_type,
                    context_id=row.context_id,
                    quota_limit_mb=row.quota_limit_mb,
                    used_space_mb=row.used_space_mb,
                    is_quota_enabled=row.is_quota_enabled,
                    created_at=row.created_at,
                    updated_at=row.updated_at,
                    uploaded_by=row.uploaded_by
                )
                return context


            # Cria novo contexto se não existir
            context_data = MediaContextCreate(
                context_type=context_type,
                context_id=context_id
            )
            
            return await self.create_context(context_data, uploaded_by)

        except Exception as e:
            logger.error(f"Erro ao obter/criar contexto: {e}")
            raise

    async def create_context(self, context_data: MediaContextCreate, uploaded_by: UUID = None) -> MediaContext:
        """
        Cria um novo contexto de mídia.
        
        Args:
            context_data: Dados do contexto
            uploaded_by: ID do usuário que criou o contexto
            
        Returns:
            MediaContext: Contexto criado
        """
        try:
            # Cria contexto usando SQL direto para evitar problemas com enum
            context_type_str = context_data.context_type.value if hasattr(context_data.context_type, 'value') else str(context_data.context_type)

            stmt = text("""
                INSERT INTO media_contexts (id, context_type, context_id, quota_limit_mb, used_space_mb, is_quota_enabled, uploaded_by, upload_source, filename)
                VALUES (gen_random_uuid(), :context_type, :context_id, :quota_limit_mb, 0, :is_quota_enabled, :uploaded_by, :upload_source, :filename)
                RETURNING *
            """)
            result = await self.db.execute(stmt, {
                "context_type": context_type_str,
                "context_id": context_data.context_id,
                "quota_limit_mb": context_data.quota_limit_mb,
                "is_quota_enabled": context_data.is_quota_enabled,
                "uploaded_by": uploaded_by,
                "upload_source": "DIRECT",
                "filename": "context_default"
            })
            await self.db.commit()

            row = result.fetchone()
            context = MediaContext(
                id=row.id,
                context_type=row.context_type,
                context_id=row.context_id,
                quota_limit_mb=row.quota_limit_mb,
                used_space_mb=row.used_space_mb,
                is_quota_enabled=row.is_quota_enabled,
                created_at=row.created_at,
                updated_at=row.updated_at,
                uploaded_by=row.uploaded_by
            )
            
            logger.info(f"Contexto criado: {context.id}")
            return context

        except IntegrityError:
            await self.db.rollback()
            # Se já existe, busca e retorna
            context_type_str = context_data.context_type.value if hasattr(context_data.context_type, 'value') else str(context_data.context_type)
            stmt = text("""
                SELECT * FROM media_contexts
                WHERE context_type = :context_type AND context_id = :context_id
            """)
            result = await self.db.execute(stmt, {
                "context_type": context_type_str,
                "context_id": context_data.context_id
            })
            row = result.fetchone()

            if row:
                existing = MediaContext(
                    id=row.id,
                    context_type=row.context_type,
                    context_id=row.context_id,
                    quota_limit_mb=row.quota_limit_mb,
                    used_space_mb=row.used_space_mb,
                    is_quota_enabled=row.is_quota_enabled,
                    created_at=row.created_at,
                    updated_at=row.updated_at,
                    uploaded_by=row.uploaded_by
                )
                return existing

            raise

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Erro ao criar contexto: {e}")
            raise

    async def get_context(self, context_id: UUID) -> Optional[MediaContext]:
        """
        Obtém um contexto por ID.

        Args:
            context_id: ID do contexto

        Returns:
            MediaContext ou None
        """
        stmt = select(MediaContext).where(MediaContext.id == context_id)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    def update_context(
        self,
        context_id: UUID,
        context_data: MediaContextUpdate
    ) -> Optional[MediaContext]:
        """
        Atualiza um contexto de mídia.
        
        Args:
            context_id: ID do contexto
            context_data: Dados para atualização
            
        Returns:
            MediaContext atualizado ou None
        """
        try:
            context = self.get_context(context_id)
            if not context:
                return None

            update_data = context_data.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                setattr(context, field, value)

            self.db.commit()
            self.db.refresh(context)
            
            logger.info(f"Contexto atualizado: {context_id}")
            return context

        except Exception as e:
            self.db.rollback()
            logger.error(f"Erro ao atualizar contexto: {e}")
            raise

    async def update_used_space(self, context_id: UUID, size_change_mb: int) -> bool:
        """
        Atualiza o espaço usado em um contexto.
        
        Args:
            context_id: ID do contexto
            size_change_mb: Mudança no tamanho (positivo para adicionar,
                           negativo para remover)
            
        Returns:
            bool: True se atualizado com sucesso
        """
        try:
            context = await self.get_context(context_id)
            if not context:
                return False

            new_used_space = max(0, context.used_space_mb + size_change_mb)
            context.used_space_mb = new_used_space

            await self.db.commit()
            logger.info(f"Espaço atualizado para contexto {context_id}: "
                       f"{size_change_mb}MB")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Erro ao atualizar espaço usado: {e}")
            return False

    async def check_quota(self, context_id: UUID, file_size_mb: int) -> dict:
        """
        Verifica se um arquivo pode ser enviado considerando a quota.
        
        Args:
            context_id: ID do contexto
            file_size_mb: Tamanho do arquivo em MB
            
        Returns:
            dict: Informações sobre a verificação de quota
        """
        context = await self.get_context(context_id)
        if not context:
            return {
                "can_upload": False,
                "reason": "Contexto não encontrado",
                "quota_limit_mb": None,
                "used_space_mb": None,
                "available_space_mb": None,
                "file_size_mb": file_size_mb
            }

        can_upload = context.can_upload(file_size_mb)
        reason = None
        
        if not can_upload:
            if not context.is_quota_enabled:
                reason = "Quota desabilitada"
            elif context.is_quota_exceeded:
                reason = "Quota já excedida"
            else:
                reason = f"Arquivo muito grande. Disponível: {context.available_space_mb}MB"

        return {
            "can_upload": can_upload,
            "reason": reason,
            "quota_limit_mb": context.quota_limit_mb,
            "used_space_mb": context.used_space_mb,
            "available_space_mb": context.available_space_mb,
            "file_size_mb": file_size_mb
        }

    async def get_quota_info(self, context_id: UUID) -> Optional[MediaQuotaResponse]:
        """
        Obtém informações de quota de um contexto.
        
        Args:
            context_id: ID do contexto
            
        Returns:
            MediaQuotaResponse ou None
        """
        context = await self.get_context(context_id)
        if not context:
            return None

        return MediaQuotaResponse(
            context_id=context.id,
            context_type=context.context_type,
            quota_limit_mb=context.quota_limit_mb,
            used_space_mb=context.used_space_mb,
            available_space_mb=context.available_space_mb,
            quota_usage_percentage=context.quota_usage_percentage,
            is_quota_enabled=context.is_quota_enabled,
            is_quota_exceeded=context.is_quota_exceeded
        )

    async def list_contexts_by_type(
        self,
        context_type: MediaContextType,
        skip: int = 0,
        limit: int = 100
    ) -> List[MediaContext]:
        """
        Lista contextos por tipo.
        
        Args:
            context_type: Tipo do contexto
            skip: Número de registros para pular
            limit: Limite de registros
            
        Returns:
            List[MediaContext]: Lista de contextos
        """
        context_type_str = context_type.value if hasattr(context_type, 'value') else str(context_type)
        stmt = text("""
            SELECT * FROM media_contexts
            WHERE context_type = :context_type
            OFFSET :skip LIMIT :limit
        """)
        result = await self.db.execute(stmt, {
            "context_type": context_type_str,
            "skip": skip,
            "limit": limit
        })
        rows = result.fetchall()

        contexts = []
        for row in rows:
            context = MediaContext(
                id=row.id,
                context_type=row.context_type,
                context_id=row.context_id,
                quota_limit_mb=row.quota_limit_mb,
                used_space_mb=row.used_space_mb,
                is_quota_enabled=row.is_quota_enabled,
                created_at=row.created_at,
                updated_at=row.updated_at
            )
            contexts.append(context)

        return contexts

    async def delete_context(self, context_id: UUID) -> bool:
        """
        Remove um contexto de mídia.

        Args:
            context_id: ID do contexto

        Returns:
            bool: True se removido com sucesso
        """
        try:
            context = await self.get_context(context_id)
            if not context:
                return False

            await self.db.delete(context)
            await self.db.commit()

            logger.info(f"Contexto removido: {context_id}")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Erro ao remover contexto: {e}")
            return False
