"""Exceptions for Domain Rent module."""


class DomainRentError(Exception):
    """Base exception for Domain Rent module."""

    pass


class RegistrarApiError(DomainRentError):
    """Exception raised when a registrar API request fails."""

    pass


class DomainNotAvailableError(DomainRentError):
    """Exception raised when a domain is not available for registration."""

    pass


class DomainTransferError(DomainRentError):
    """Exception raised when a domain transfer fails."""

    pass


class DomainRenewalError(DomainRentError):
    """Exception raised when a domain renewal fails."""

    pass


class ConfigurationError(DomainRentError):
    """Exception raised when there is a configuration error."""

    pass


class ProcessorNotFoundError(DomainRentError):
    """Exception raised when a processor is not found."""

    pass
