# Shared - Orders

**Categoria:** Shared
**Módulo:** Orders
**Total de Endpoints:** 10
**Gerado em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [GET /api/financial/orders/](#get-apifinancialorders) - Read Orders
- [POST /api/financial/orders/](#post-apifinancialorders) - Create Order
- [GET /api/financial/orders/stats](#get-apifinancialordersstats) - Get Order Stats
- [GET /api/financial/orders/{order_id}](#get-apifinancialordersorder-id) - Read Order
- [PATCH /api/financial/orders/{order_id}/status](#patch-apifinancialordersorder-idstatus) - Update Order Status
- [GET /api/orders/](#get-apiorders) - Read Orders
- [POST /api/orders/](#post-apiorders) - Create Order
- [GET /api/orders/stats](#get-apiordersstats) - Get Order Stats
- [GET /api/orders/{order_id}](#get-apiordersorder-id) - Read Order
- [PATCH /api/orders/{order_id}/status](#patch-apiordersorder-idstatus) - Update Order Status

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### OrderCreate

**Descrição:** Schema for creating a new order.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `order_type` | string | ✅ | Type of order (dine-in, takeout, delivery, etc.) |
| `table_number` | unknown | ❌ | Table number for dine-in orders |
| `subtotal` | unknown | ✅ | Subtotal amount before tax and discounts |
| `tax` | unknown | ✅ | Tax amount |
| `discount` | unknown | ❌ | Discount amount |
| `total` | unknown | ✅ | Total amount after tax and discounts |
| `notes` | unknown | ❌ | Special instructions for the order |
| `metadata` | unknown | ❌ | Additional metadata |
| `customer_id` | unknown | ❌ | ID of the customer |
| `items` | Array[OrderItemCreate] | ✅ | Items in the order |

### OrderListResponse

**Descrição:** Schema for paginated order list response.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `orders` | Array[OrderRead] | ✅ | List of orders |
| `total` | integer | ✅ | Total number of orders |
| `page` | integer | ✅ | Current page number |
| `per_page` | integer | ✅ | Number of orders per page |
| `total_pages` | integer | ✅ | Total number of pages |

### OrderRead

**Descrição:** Schema for reading an order.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `order_type` | string | ✅ | Type of order (dine-in, takeout, delivery, etc.) |
| `table_number` | unknown | ❌ | Table number for dine-in orders |
| `subtotal` | string | ✅ | Subtotal amount before tax and discounts |
| `tax` | string | ✅ | Tax amount |
| `discount` | string | ❌ | Discount amount |
| `total` | string | ✅ | Total amount after tax and discounts |
| `notes` | unknown | ❌ | Special instructions for the order |
| `metadata` | unknown | ❌ | Additional metadata |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `customer_id` | unknown | ✅ | - |
| `user_id` | unknown | ✅ | - |
| `order_number` | string | ✅ | - |
| `status` | OrderStatus | ✅ | - |
| `items` | Array[OrderItemRead] | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `customer` | unknown | ❌ | - |
| `user` | unknown | ❌ | - |
| `kitchen_order` | unknown | ❌ | - |

### OrderStats

**Descrição:** Schema for order statistics.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `total_orders` | integer | ✅ | Total number of orders |
| `pending_orders` | integer | ✅ | Number of pending orders |
| `confirmed_orders` | integer | ✅ | Number of confirmed orders |
| `delivered_orders` | integer | ✅ | Number of delivered orders |
| `cancelled_orders` | integer | ✅ | Number of cancelled orders |
| `total_revenue` | string | ✅ | Total revenue from all orders |
| `average_order_value` | string | ✅ | Average order value |

### OrderUpdate

**Descrição:** Schema for updating an existing order.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `status` | unknown | ❌ | New status of the order |
| `notes` | unknown | ❌ | Updated special instructions |
| `metadata` | unknown | ❌ | Updated metadata |

## 🔗 Endpoints Detalhados

### GET /api/financial/orders/ {#get-apifinancialorders}

**Resumo:** Read Orders
**Descrição:** Retrieve orders for the current tenant, with optional filters.
Requires at least costumer role in the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `status` | string | query | ❌ | Filter by order status |
| `order_type` | string | query | ❌ | Filter by order type (dine-in, takeout, delivery, etc.) |
| `customer_id` | string | query | ❌ | Filter by customer ID |
| `customer_name` | string | query | ❌ | Filter by customer name |
| `customer_email` | string | query | ❌ | Filter by customer email |
| `order_number` | string | query | ❌ | Filter by order number |
| `date_from` | string | query | ❌ | Filter orders from date (YYYY-MM-DD) |
| `date_to` | string | query | ❌ | Filter orders to date (YYYY-MM-DD) |
| `min_amount` | string | query | ❌ | Filter by minimum order amount |
| `max_amount` | string | query | ❌ | Filter by maximum order amount |
| `page` | integer | query | ❌ | Page number |
| `per_page` | integer | query | ❌ | Number of records per page |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [OrderListResponse](#orderlistresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/orders/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/financial/orders/ {#post-apifinancialorders}

**Resumo:** Create Order
**Descrição:** Create a new order for the current tenant.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [OrderCreate](#ordercreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [OrderRead](#orderread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/financial/orders/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/financial/orders/stats {#get-apifinancialordersstats}

**Resumo:** Get Order Stats
**Descrição:** Get order statistics for the current tenant.
Requires at least customer role in the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [OrderStats](#orderstats)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/orders/stats" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/financial/orders/{order_id} {#get-apifinancialordersorder-id}

**Resumo:** Read Order
**Descrição:** Retrieve a specific order by ID.
Requires at least costumer role in the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `order_id` | string | path | ✅ | ID of the order to retrieve |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [OrderRead](#orderread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/orders/{order_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PATCH /api/financial/orders/{order_id}/status {#patch-apifinancialordersorder-idstatus}

**Resumo:** Update Order Status
**Descrição:** Update the status of an order.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `order_id` | string | path | ✅ | ID of the order to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [OrderUpdate](#orderupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [OrderRead](#orderread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PATCH "http://localhost:8000/api/financial/orders/{order_id}/status" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/orders/ {#get-apiorders}

**Resumo:** Read Orders
**Descrição:** Retrieve orders for the current tenant, with optional filters.
Requires at least costumer role in the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `status` | string | query | ❌ | Filter by order status |
| `order_type` | string | query | ❌ | Filter by order type (dine-in, takeout, delivery, etc.) |
| `customer_id` | string | query | ❌ | Filter by customer ID |
| `customer_name` | string | query | ❌ | Filter by customer name |
| `customer_email` | string | query | ❌ | Filter by customer email |
| `order_number` | string | query | ❌ | Filter by order number |
| `date_from` | string | query | ❌ | Filter orders from date (YYYY-MM-DD) |
| `date_to` | string | query | ❌ | Filter orders to date (YYYY-MM-DD) |
| `min_amount` | string | query | ❌ | Filter by minimum order amount |
| `max_amount` | string | query | ❌ | Filter by maximum order amount |
| `page` | integer | query | ❌ | Page number |
| `per_page` | integer | query | ❌ | Number of records per page |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [OrderListResponse](#orderlistresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/orders/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/orders/ {#post-apiorders}

**Resumo:** Create Order
**Descrição:** Create a new order for the current tenant.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [OrderCreate](#ordercreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [OrderRead](#orderread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/orders/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/orders/stats {#get-apiordersstats}

**Resumo:** Get Order Stats
**Descrição:** Get order statistics for the current tenant.
Requires at least customer role in the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [OrderStats](#orderstats)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/orders/stats" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/orders/{order_id} {#get-apiordersorder-id}

**Resumo:** Read Order
**Descrição:** Retrieve a specific order by ID.
Requires at least costumer role in the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `order_id` | string | path | ✅ | ID of the order to retrieve |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [OrderRead](#orderread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/orders/{order_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PATCH /api/orders/{order_id}/status {#patch-apiordersorder-idstatus}

**Resumo:** Update Order Status
**Descrição:** Update the status of an order.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `order_id` | string | path | ✅ | ID of the order to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [OrderUpdate](#orderupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [OrderRead](#orderread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PATCH "http://localhost:8000/api/orders/{order_id}/status" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
