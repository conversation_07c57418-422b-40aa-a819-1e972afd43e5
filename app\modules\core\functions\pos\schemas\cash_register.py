import uuid
from typing import Optional
from pydantic import BaseModel, ConfigDict

# --- CashRegister Schemas ---

# Shared properties


class CashRegisterBase(BaseModel):
    name: str
    is_active: Optional[bool] = True


# Properties to receive via API on creation


class CashRegisterCreate(CashRegisterBase):
    pass  # tenant_id será adicionado pelo serviço/endpoint


# Properties to receive via API on update


class CashRegisterUpdate(BaseModel):
    name: Optional[str] = None
    is_active: Optional[bool] = None


# Properties stored in DB


class CashRegisterInDBBase(CashRegisterBase):
    id: uuid.UUID
    tenant_id: uuid.UUID
    model_config = ConfigDict(from_attributes=True)


# Properties to return to client


class CashRegisterRead(CashRegisterInDBBase):
    pass
