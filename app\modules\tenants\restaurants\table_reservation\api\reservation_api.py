from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from typing import List, Optional, Annotated, Any, Dict
import uuid
from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession
from app.core.db_dependencies import get_db

from app.modules.core.users.models.user import User
from app.modules.core.tenants.models.tenant import Tenant

from app.modules.tenants.restaurants.table_reservation.schemas.reservation import (
    ReservationCreate,
    ReservationUpdate,
    ReservationRead,
    CustomerBlacklistCreate,
    CustomerBlacklistUpdate,
    CustomerBlacklistRead,
)
from app.modules.tenants.restaurants.table_reservation.models.reservation import (
    ReservationStatus,
    BlacklistType,
)
from app.modules.tenants.restaurants.table_reservation.services.reservation_service import (
    ReservationService,
)
from app.modules.tenants.restaurants.table_reservation.services.blacklist_service import (
    BlacklistService,
)
from app.modules.tenants.restaurants.table_management.schemas.table import TableRead

from app.modules.core.auth.dependencies.auth_dependencies import (
    get_current_active_user,
)
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    get_current_tenant_from_header,
    require_tenant_role,
)
from app.modules.core.roles.models.roles import RolePermissions

router = APIRouter()

# Define required roles for different operations
view_roles = RolePermissions.VIEW_ROLES  # All roles that can view
write_roles = RolePermissions.ADMIN_ROLES  # Only admin roles can modify


# Reservation endpoints
@router.post(
    "/reservations/",
    response_model=ReservationRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new reservation",
)
async def create_reservation(
    reservation_in: ReservationCreate,
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated[User, Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))] = None,
    reservation_service: ReservationService = Depends(lambda: ReservationService()),
):
    """
    Create a new reservation for the current tenant.
    Requires at least STAFF tenant role.
    """
    return await reservation_service.create_reservation(
        db=db,
        reservation_in=reservation_in,
        tenant_id=current_tenant.id,
        user_id=current_user.id,
    )


@router.get(
    "/reservations/",
    response_model=List[ReservationRead],
    summary="Get all reservations",
)
async def get_reservations(
    table_id: Optional[uuid.UUID] = Query(None, description="Filter by table ID"),
    customer_id: Optional[uuid.UUID] = Query(None, description="Filter by customer ID"),
    status: Optional[ReservationStatus] = Query(None, description="Filter by reservation status"),
    date_from: Optional[datetime] = Query(None, description="Filter by reservation date (from)"),
    date_to: Optional[datetime] = Query(None, description="Filter by reservation date (to)"),
    skip: int = Query(0, ge=0, description="Number of reservations to skip"),
    limit: int = Query(100, ge=1, le=100, description="Maximum number of reservations to return"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated[User, Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))] = None,
    reservation_service: ReservationService = Depends(lambda: ReservationService()),
):
    """
    Get all reservations for the current tenant with optional filters.
    Requires at least STAFF tenant role.
    """
    return await reservation_service.get_reservations(
        db=db,
        tenant_id=current_tenant.id,
        table_id=table_id,
        customer_id=customer_id,
        status=status,
        date_from=date_from,
        date_to=date_to,
        skip=skip,
        limit=limit,
    )


@router.get(
    "/reservations/{reservation_id}",
    response_model=ReservationRead,
    summary="Get a reservation by ID",
)
async def get_reservation(
    reservation_id: uuid.UUID = Path(..., description="The ID of the reservation to get"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated[User, Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))] = None,
    reservation_service: ReservationService = Depends(lambda: ReservationService()),
):
    """
    Get a reservation by ID for the current tenant.
    Requires at least STAFF tenant role.
    """
    reservation = await reservation_service.get_reservation(
        db=db, reservation_id=reservation_id, tenant_id=current_tenant.id
    )
    if not reservation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Reservation not found",
        )
    return reservation


@router.put(
    "/reservations/{reservation_id}",
    response_model=ReservationRead,
    summary="Update a reservation",
)
async def update_reservation(
    reservation_id: uuid.UUID = Path(..., description="The ID of the reservation to update"),
    reservation_in: ReservationUpdate = None,
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated[User, Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))] = None,
    reservation_service: ReservationService = Depends(lambda: ReservationService()),
):
    """
    Update a reservation for the current tenant.
    Requires at least STAFF tenant role.
    """
    reservation = await reservation_service.update_reservation(
        db=db,
        reservation_id=reservation_id,
        reservation_in=reservation_in,
        tenant_id=current_tenant.id,
    )
    if not reservation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Reservation not found",
        )
    return reservation


@router.delete(
    "/reservations/{reservation_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete a reservation",
)
async def delete_reservation(
    reservation_id: uuid.UUID = Path(..., description="The ID of the reservation to delete"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated[User, Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=write_roles))] = None,
    reservation_service: ReservationService = Depends(lambda: ReservationService()),
):
    """
    Delete a reservation for the current tenant.
    Requires OWNER or MANAGER tenant role.
    """
    result = await reservation_service.delete_reservation(
        db=db, reservation_id=reservation_id, tenant_id=current_tenant.id
    )
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Reservation not found",
        )
    return None


@router.patch(
    "/reservations/{reservation_id}/status",
    response_model=ReservationRead,
    summary="Update reservation status",
)
async def update_reservation_status(
    reservation_id: uuid.UUID = Path(..., description="The ID of the reservation to update"),
    status: ReservationStatus = Query(..., description="The new status for the reservation"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated[User, Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))] = None,
    reservation_service: ReservationService = Depends(lambda: ReservationService()),
):
    """
    Update the status of a reservation for the current tenant.
    Requires at least STAFF tenant role.
    """
    reservation = await reservation_service.update_reservation_status(
        db=db, reservation_id=reservation_id, new_status=status, tenant_id=current_tenant.id
    )
    if not reservation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Reservation not found",
        )
    return reservation


@router.get(
    "/reservations/available-tables",
    response_model=List[TableRead],
    summary="Find available tables for a reservation",
)
async def find_available_tables(
    reservation_date: datetime = Query(..., description="Date and time of the reservation"),
    duration_minutes: int = Query(90, ge=30, description="Duration of the reservation in minutes"),
    party_size: int = Query(..., ge=1, description="Number of people in the party"),
    layout_id: Optional[uuid.UUID] = Query(None, description="Filter by layout ID"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated[User, Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))] = None,
    reservation_service: ReservationService = Depends(lambda: ReservationService()),
):
    """
    Find available tables for a reservation with the specified parameters.
    Requires at least STAFF tenant role.
    """
    return await reservation_service.find_available_tables(
        db=db,
        tenant_id=current_tenant.id,
        reservation_date=reservation_date,
        duration_minutes=duration_minutes,
        party_size=party_size,
        layout_id=layout_id,
    )


# Blacklist endpoints
@router.post(
    "/blacklist/",
    response_model=CustomerBlacklistRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new blacklist entry",
)
async def create_blacklist_entry(
    blacklist_in: CustomerBlacklistCreate,
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated[User, Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=write_roles))] = None,
    blacklist_service: BlacklistService = Depends(lambda: BlacklistService()),
):
    """
    Create a new blacklist entry for the current tenant.
    Requires OWNER or MANAGER tenant role.
    """
    return await blacklist_service.create_blacklist_entry(
        db=db, blacklist_in=blacklist_in, tenant_id=current_tenant.id
    )


@router.get(
    "/blacklist/",
    response_model=List[CustomerBlacklistRead],
    summary="Get all blacklist entries",
)
async def get_blacklist_entries(
    customer_id: Optional[uuid.UUID] = Query(None, description="Filter by customer ID"),
    blacklist_type: Optional[BlacklistType] = Query(None, description="Filter by blacklist type"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    skip: int = Query(0, ge=0, description="Number of entries to skip"),
    limit: int = Query(100, ge=1, le=100, description="Maximum number of entries to return"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated[User, Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))] = None,
    blacklist_service: BlacklistService = Depends(lambda: BlacklistService()),
):
    """
    Get all blacklist entries for the current tenant with optional filters.
    Requires at least STAFF tenant role.
    """
    return await blacklist_service.get_blacklist_entries(
        db=db,
        tenant_id=current_tenant.id,
        customer_id=customer_id,
        blacklist_type=blacklist_type,
        is_active=is_active,
        skip=skip,
        limit=limit,
    )


@router.get(
    "/blacklist/{blacklist_id}",
    response_model=CustomerBlacklistRead,
    summary="Get a blacklist entry by ID",
)
async def get_blacklist_entry(
    blacklist_id: uuid.UUID = Path(..., description="The ID of the blacklist entry to get"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated[User, Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))] = None,
    blacklist_service: BlacklistService = Depends(lambda: BlacklistService()),
):
    """
    Get a blacklist entry by ID for the current tenant.
    Requires at least STAFF tenant role.
    """
    blacklist_entry = await blacklist_service.get_blacklist_entry(
        db=db, blacklist_id=blacklist_id, tenant_id=current_tenant.id
    )
    if not blacklist_entry:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Blacklist entry not found",
        )
    return blacklist_entry


@router.put(
    "/blacklist/{blacklist_id}",
    response_model=CustomerBlacklistRead,
    summary="Update a blacklist entry",
)
async def update_blacklist_entry(
    blacklist_id: uuid.UUID = Path(..., description="The ID of the blacklist entry to update"),
    blacklist_in: CustomerBlacklistUpdate = None,
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated[User, Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=write_roles))] = None,
    blacklist_service: BlacklistService = Depends(lambda: BlacklistService()),
):
    """
    Update a blacklist entry for the current tenant.
    Requires OWNER or MANAGER tenant role.
    """
    blacklist_entry = await blacklist_service.update_blacklist_entry(
        db=db, blacklist_id=blacklist_id, blacklist_in=blacklist_in, tenant_id=current_tenant.id
    )
    if not blacklist_entry:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Blacklist entry not found",
        )
    return blacklist_entry


@router.delete(
    "/blacklist/{blacklist_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete a blacklist entry",
)
async def delete_blacklist_entry(
    blacklist_id: uuid.UUID = Path(..., description="The ID of the blacklist entry to delete"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated[User, Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=write_roles))] = None,
    blacklist_service: BlacklistService = Depends(lambda: BlacklistService()),
):
    """
    Delete a blacklist entry for the current tenant.
    Requires OWNER or MANAGER tenant role.
    """
    result = await blacklist_service.delete_blacklist_entry(
        db=db, blacklist_id=blacklist_id, tenant_id=current_tenant.id
    )
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Blacklist entry not found",
        )
    return None


@router.get(
    "/blacklist/check",
    response_model=Dict[str, Any],
    summary="Check if a customer is blacklisted",
)
async def check_blacklist(
    customer_id: Optional[uuid.UUID] = Query(None, description="Customer ID to check"),
    guest_email: Optional[str] = Query(None, description="Guest email to check"),
    guest_phone: Optional[str] = Query(None, description="Guest phone to check"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated[User, Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))] = None,
    blacklist_service: BlacklistService = Depends(lambda: BlacklistService()),
):
    """
    Check if a customer is blacklisted for the current tenant.
    Requires at least STAFF tenant role.
    """
    if not customer_id and not guest_email and not guest_phone:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="At least one of customer_id, guest_email, or guest_phone must be provided",
        )

    return await blacklist_service.check_customer_blacklist(
        db=db,
        tenant_id=current_tenant.id,
        customer_id=customer_id,
        guest_email=guest_email,
        guest_phone=guest_phone,
    )
