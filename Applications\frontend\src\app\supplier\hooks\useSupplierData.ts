import { useState, useEffect, useCallback } from 'react';

interface Tenant {
  id: string;
  name: string;
  description: string | null;
  type: string;
  is_active: boolean;
  association_id: string;
  role: string;
}

interface ShoppingListItem {
  id: string;
  name: string;
  quantity: number;
  unit: string | null;
  priority: 'low' | 'medium' | 'high';
  estimated_price: string | null;
  purchased: boolean;
  notes: string | null;
  inventory_item_id: string | null;
  shopping_list_id: string;
  created_at: string;
  updated_at: string;
}

interface ShoppingList {
  id: string;
  name: string;
  description: string | null;
  is_active: boolean;
  tenant_id: string;
  created_at: string;
  updated_at: string;
  items: ShoppingListItem[];
}

export function useSupplierData() {
  const [shoppingLists, seeshoppingLists] = useState<ShoppingList[]>([]);
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTenantId, setSelectedTenantId] = useState<string | null>(null);

  const fetchTenants = useCallback(async () => {
    try {
      // Mock tenants data - will be replaced with real API call
      const mockTenants: Tenant[] = [
        {
          id: '482a9ddc-b655-4cef-b16c-9574a0611e92',
          name: 'Test Restaurant',
          description: null,
          type: 'restaurant',
          is_active: true,
          association_id: '4b8deb19-57f7-42a5-92ad-fb97cbc46d4e',
          role: 'supplier'
        }
      ];

      setTenants(mockTenants);
    } catch (error) {
      console.error('Error fetching tenants:', error);
    }
  }, []);

  const fetchShoppingLists = useCallback(async () => {
    try {
      setLoading(true);

      // Mock data for development - will be replaced with real API calls
      // Filter by selected tenant if one is selected
      const mockShoppingLists: ShoppingList[] = [
        {
          id: '1',
          name: 'Lista Principal',
          description: 'Lista principal de compras',
          is_active: true,
          tenant_id: '1',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          items: [
            {
              id: '1',
              name: 'Chocolate em Pó',
              quantity: 10,
              unit: 'kg',
              priority: 'high',
              estimated_price: '25.00',
              purchased: false,
              notes: 'Auto-adicionado: estoque atual 2 unidades',
              inventory_item_id: '1',
              shopping_list_id: '1',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            },
            {
              id: '2',
              name: 'Salmão',
              quantity: 5,
              unit: 'kg',
              priority: 'medium',
              estimated_price: '45.00',
              purchased: false,
              notes: 'Produto premium para pratos especiais',
              inventory_item_id: '2',
              shopping_list_id: '1',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            },
            {
              id: '3',
              name: 'Azeite Extra Virgem',
              quantity: 3,
              unit: 'litros',
              priority: 'low',
              estimated_price: '28.00',
              purchased: false,
              notes: 'Para pratos especiais',
              inventory_item_id: '3',
              shopping_list_id: '1',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            },
            {
              id: '4',
              name: 'Farinha de Trigo',
              quantity: 20,
              unit: 'kg',
              priority: 'medium',
              estimated_price: '8.90',
              purchased: false,
              notes: 'Para massas e pães',
              inventory_item_id: '4',
              shopping_list_id: '1',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            },
            {
              id: '5',
              name: 'Tomates',
              quantity: 15,
              unit: 'kg',
              priority: 'high',
              estimated_price: '4.50',
              purchased: false,
              notes: 'Para molhos e saladas',
              inventory_item_id: '5',
              shopping_list_id: '1',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            }
          ]
        }
      ];
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      seeshoppingLists(mockShoppingLists);
    } catch (error) {
      console.error('Error fetching shopping lists:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchTenants();
  }, [fetchTenants]);

  useEffect(() => {
    fetchShoppingLists();
  }, [selectedTenantId, fetchShoppingLists]);

  return {
    shoppingLists,
    tenants,
    loading,
    selectedTenantId,
    setSelectedTenantId,
    refetch: fetchShoppingLists
  };
}
