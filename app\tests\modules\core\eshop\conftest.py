"""
EShop module test configuration and fixtures.

Provides specialized fixtures for EShop testing including:
- Service mocks and test data
- Authentication helpers
- Database session management
- Performance testing utilities
"""

import pytest
import pytest_asyncio
from unittest.mock import Mock, AsyncMock
from uuid import uuid4
from decimal import Decimal
import httpx


@pytest.fixture
def mock_eshop_db_session():
    """Mock database session for EShop tests."""
    session = Mock()
    session.add = Mock()
    session.commit = AsyncMock()
    session.refresh = AsyncMock()
    session.execute = AsyncMock()
    session.scalar = AsyncMock()
    session.query = Mock()
    return session


@pytest.fixture
def mock_notification_service():
    """Mock notification service for EShop tests."""
    service = Mock()
    service.send_approval_notification = AsyncMock()
    service.send_role_notification = AsyncMock()
    service.send_integration_notification = AsyncMock()
    return service


@pytest.fixture
def mock_celery_app():
    """Mock Celery app for background task testing."""
    app = Mock()
    app.send_task = Mock()
    app.delay = Mock()
    return app


@pytest.fixture
def sample_eshop_product():
    """Sample EShop product data."""
    return {
        "id": str(uuid4()),
        "name": "Test EShop Product",
        "description": "Sample product for testing",
        "price": Decimal("99.99"),
        "market_type": "B2C",
        "approval_status": "approved",
        "commission_rate": Decimal("0.10"),
        "inventory_quantity": 100,
        "minimum_stock_level": 10,
        "legacy_cuponic_id": None
    }


@pytest.fixture
def sample_b2b_vendor():
    """Sample B2B vendor data."""
    return {
        "id": str(uuid4()),
        "user_id": str(uuid4()),
        "tenant_id": str(uuid4()),
        "role": "TVENDOR",
        "b2b_authorized": True,
        "business_name": "Test Vendor Corp",
        "business_registration_number": "BRN123456",
        "commission_rate": Decimal("0.15"),
        "market_context": "B2B"
    }


@pytest.fixture
def sample_approval_request():
    """Sample product approval request."""
    return {
        "product_id": str(uuid4()),
        "vendor_notes": "Please approve this product for marketplace",
        "market_type": "B2B",
        "commission_rate": Decimal("0.12")
    }


@pytest.fixture
def sample_integration_request():
    """Sample integration request data."""
    return {
        "product_id": str(uuid4()),
        "inventory_quantity": 50,
        "sync_type": "real_time",
        "source_system": "eshop"
    }


@pytest.fixture
async def eshop_test_client():
    """HTTP client configured for EShop testing."""
    return httpx.AsyncClient(
        base_url="http://localhost:8000",
        timeout=30.0
    )


@pytest.fixture
async def eshop_admin_token(eshop_test_client):
    """Admin authentication token for EShop tests."""
    response = await eshop_test_client.post(
        "/api/auth/login",
        data={"username": "<EMAIL>", "password": "password"}
    )
    if response.status_code == 200:
        return response.json()["access_token"]
    return None


@pytest.fixture
async def eshop_admin_headers(eshop_admin_token):
    """Admin headers for EShop API calls."""
    if eshop_admin_token:
        return {"Authorization": f"Bearer {eshop_admin_token}"}
    return {}


@pytest.fixture
async def eshop_test_tenant(eshop_test_client, eshop_admin_headers):
    """Test tenant for EShop testing."""
    response = await eshop_test_client.get(
        "/api/tenants/",
        headers=eshop_admin_headers
    )
    if response.status_code == 200:
        tenants = response.json()
        return tenants[0] if tenants else None
    return None


class EShopTestHelpers:
    """Helper utilities for EShop testing."""
    
    @staticmethod
    def create_mock_product(**overrides):
        """Create mock product with optional overrides."""
        default_product = {
            "id": str(uuid4()),
            "name": "Mock Product",
            "description": "Mock product for testing",
            "price": Decimal("50.00"),
            "market_type": "B2C",
            "approval_status": "approved"
        }
        default_product.update(overrides)
        return Mock(**default_product)
    
    @staticmethod
    def create_mock_user_association(**overrides):
        """Create mock user-tenant association."""
        default_association = {
            "id": str(uuid4()),
            "user_id": str(uuid4()),
            "tenant_id": str(uuid4()),
            "role": "CUSTOMER",
            "b2b_authorized": False,
            "market_context": "B2C"
        }
        default_association.update(overrides)
        return Mock(**default_association)
    
    @staticmethod
    def assert_eshop_response_format(response_data, expected_fields):
        """Assert EShop API response format."""
        for field in expected_fields:
            assert field in response_data, f"Missing field: {field}"
    
    @staticmethod
    def assert_uuid_format(uuid_string):
        """Assert valid UUID format."""
        from uuid import UUID
        try:
            UUID(uuid_string)
            return True
        except (ValueError, TypeError):
            return False


@pytest.fixture
def eshop_test_helpers():
    """EShop test helper utilities."""
    return EShopTestHelpers()


# Performance testing fixtures
@pytest.fixture
def performance_config():
    """Configuration for performance tests."""
    return {
        "max_concurrent_users": 100,
        "products_per_batch": 50,
        "approval_batch_size": 25,
        "test_duration": 60,  # seconds
        "memory_limit_mb": 512,
        "response_time_limit": 2.0  # seconds
    }


@pytest.fixture
def migration_test_config():
    """Configuration for migration tests."""
    return {
        "batch_size": 100,
        "validation_enabled": True,
        "rollback_enabled": True,
        "integrity_threshold": 95.0,  # percentage
        "performance_threshold": 10.0  # products per second
    } 