"""Interaction service for CRM module."""

import uuid  # noqa: E402
import logging
from typing import List, Optional
from datetime import datetime
from sqlalchemy import select, update, delete
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError

from app.modules.shared.crm.models.interaction import Interaction, InteractionType  # noqa: E402
from app.modules.shared.crm.schemas.interaction import (
    InteractionCreate,
    InteractionUpdate,
)
from app.modules.shared.crm.services.account_service import account_service  # noqa: E402

logger = logging.getLogger(__name__)


class InteractionService:
    """Service for managing CRM interactions."""

    @staticmethod
    async def create_interaction(
        db: AsyncSession, tenant_id: uuid.UUID, interaction_in: InteractionCreate
    ) -> Interaction:
        """Create a new interaction."""
        try:
            interaction_data = interaction_in.model_dump(exclude_unset=True)
            db_interaction = Interaction(tenant_id=tenant_id, **interaction_data)

            db.add(db_interaction)
            await db.commit()
            await db.refresh(db_interaction)

            # Update the account's last_contact_date
            await account_service.update_last_contact_date(
                db,
                tenant_id,
                interaction_in.account_id,
                interaction_in.interaction_date,
            )

            return db_interaction
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(f"Error creating interaction: {e}")
            raise

    @staticmethod
    async def get_interaction(
        db: AsyncSession, tenant_id: uuid.UUID, interaction_id: uuid.UUID
    ) -> Optional[Interaction]:
        """Get an interaction by ID."""
        try:
            query = select(Interaction).where(
                Interaction.tenant_id == tenant_id, Interaction.id == interaction_id
            )
            result = await db.execute(query)
            return result.scalars().first()
        except SQLAlchemyError as e:
            logger.error(f"Error getting interaction {interaction_id}: {e}")
            raise

    @staticmethod
    async def get_interactions(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        account_id: Optional[uuid.UUID] = None,
        contact_id: Optional[uuid.UUID] = None,
        skip: int = 0,
        limit: int = 100,
        interaction_type: Optional[InteractionType] = None,
        requires_followup: Optional[bool] = None,
        is_completed: Optional[bool] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        search: Optional[str] = None,
    ) -> List[Interaction]:
        """Get all interactions with optional filtering."""
        try:
            query = select(Interaction).where(Interaction.tenant_id == tenant_id)

            # Filter by account if provided
            if account_id:
                query = query.where(Interaction.account_id == account_id)

            # Filter by contact if provided
            if contact_id:
                query = query.where(Interaction.contact_id == contact_id)

            # Apply filters if provided
            if interaction_type:
                query = query.where(Interaction.interaction_type == interaction_type)

            if requires_followup is not None:
                query = query.where(Interaction.requires_followup == requires_followup)

            if is_completed is not None:
                query = query.where(Interaction.is_completed == is_completed)

            if start_date:
                query = query.where(Interaction.interaction_date >= start_date)

            if end_date:
                query = query.where(Interaction.interaction_date <= end_date)

            if search:
                search_term = f"%{search}%"
                query = query.where(
                    Interaction.subject.ilike(search_term)
                    | Interaction.description.ilike(search_term)
                    | Interaction.outcome.ilike(search_term)
                )

            # Order by interaction date (most recent first)
            query = query.order_by(Interaction.interaction_date.desc())

            # Apply pagination
            query = query.offset(skip).limit(limit)

            result = await db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            logger.error(f"Error getting interactions: {e}")
            raise

    @staticmethod
    async def update_interaction(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        interaction_id: uuid.UUID,
        interaction_in: InteractionUpdate,
    ) -> Optional[Interaction]:
        """Update an interaction."""
        try:
            # Get the interaction first to ensure it exists and belongs to the tenant
            db_interaction = await InteractionService.get_interaction(db, tenant_id, interaction_id)
            if not db_interaction:
                return None

            # Update the interaction
            interaction_data = interaction_in.model_dump(exclude_unset=True)

            # Update the interaction in the database
            query = (
                update(Interaction)
                .where(Interaction.id == interaction_id, Interaction.tenant_id == tenant_id)
                .values(**interaction_data)
                .execution_options(synchronize_session="fetch")
            )
            await db.execute(query)
            await db.commit()

            # Refresh and return the updated interaction
            return await InteractionService.get_interaction(db, tenant_id, interaction_id)
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(f"Error updating interaction {interaction_id}: {e}")
            raise

    @staticmethod
    async def delete_interaction(
        db: AsyncSession, tenant_id: uuid.UUID, interaction_id: uuid.UUID
    ) -> bool:
        """Delete an interaction."""
        try:
            # Get the interaction first to ensure it exists and belongs to the tenant
            db_interaction = await InteractionService.get_interaction(db, tenant_id, interaction_id)
            if not db_interaction:
                return False

            # Delete the interaction
            query = (
                delete(Interaction)
                .where(Interaction.id == interaction_id, Interaction.tenant_id == tenant_id)
                .execution_options(synchronize_session="fetch")
            )
            await db.execute(query)
            await db.commit()

            return True
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(f"Error deleting interaction {interaction_id}: {e}")
            raise


# Create a singleton instance
interaction_service = InteractionService()
