"""
Location and Address specific schemas for tenant settings.
"""

from typing import Optional

from pydantic import BaseModel, Field, validator


class AddressSchema(BaseModel):
    """Schema for address information."""

    street: str = Field(..., max_length=200, description="Street name (without number)")
    number: Optional[str] = Field(None, max_length=20, description="House/building number")
    complement: Optional[str] = Field(None, max_length=100, description="Apartment, suite, floor, etc.")
    city: str = Field(..., max_length=100, description="City name")
    state: str = Field(..., max_length=100, description="State or province")
    zipCode: str = Field(..., max_length=20, description="ZIP or postal code")
    country: str = Field(..., min_length=2, max_length=2, description="Country code (ISO 3166-1 alpha-2)")
    latitude: Optional[float] = Field(None, ge=-90, le=90, description="Latitude coordinate")
    longitude: Optional[float] = Field(None, ge=-180, le=180, description="Longitude coordinate")

    # Contact Information
    phone: Optional[str] = Field(None, max_length=20, description="Primary phone number")
    phone_secondary: Optional[str] = Field(None, max_length=20, description="Secondary phone number")
    fax: Optional[str] = Field(None, max_length=20, description="Fax number")
    email: Optional[str] = Field(None, max_length=100, description="Business email address")
    website: Optional[str] = Field(None, max_length=200, description="Business website URL")

    # Business Hours Reference
    business_hours_note: Optional[str] = Field(None, max_length=200, description="Additional note about business hours")

    @validator('country')
    def validate_country_code(cls, v):
        """Validate country code format."""
        return v.upper() if v else v

    @validator('zipCode')
    def validate_zip_code(cls, v):
        """Validate ZIP code format."""
        if v and not v.replace(' ', '').replace('-', '').isalnum():
            raise ValueError('ZIP code must contain only alphanumeric characters, spaces, and hyphens')
        return v

    @validator('latitude')
    def validate_latitude(cls, v):
        """Validate latitude range."""
        if v is not None and not (-90 <= v <= 90):
            raise ValueError('Latitude must be between -90 and 90 degrees')
        return v

    @validator('longitude')
    def validate_longitude(cls, v):
        """Validate longitude range."""
        if v is not None and not (-180 <= v <= 180):
            raise ValueError('Longitude must be between -180 and 180 degrees')
        return v

    @validator('phone', 'phone_secondary', 'fax')
    def validate_phone_numbers(cls, v):
        """Validate phone number format."""
        if v is not None and v.strip():  # Only validate if not empty
            # Remove common formatting characters
            cleaned = v.replace(' ', '').replace('-', '').replace('(', '').replace(')', '').replace('+', '')
            if not cleaned.isdigit():
                raise ValueError('Phone number must contain only digits and common formatting characters')
            if len(cleaned) < 8 or len(cleaned) > 15:
                raise ValueError('Phone number must be between 8 and 15 digits')
        return v


class AddressUpdateSchema(BaseModel):
    """Schema for updating address information."""

    street: Optional[str] = Field(None, max_length=200, description="Street name (without number)")
    number: Optional[str] = Field(None, max_length=20, description="House/building number")
    complement: Optional[str] = Field(None, max_length=100, description="Apartment, suite, floor, etc.")
    city: Optional[str] = Field(None, max_length=100, description="City name")
    state: Optional[str] = Field(None, max_length=100, description="State or province")
    zipCode: Optional[str] = Field(None, max_length=20, description="ZIP or postal code")
    country: Optional[str] = Field(None, min_length=2, max_length=2, description="Country code (ISO 3166-1 alpha-2)")
    latitude: Optional[float] = Field(None, ge=-90, le=90, description="Latitude coordinate")
    longitude: Optional[float] = Field(None, ge=-180, le=180, description="Longitude coordinate")

    # Contact Information
    phone: Optional[str] = Field(None, max_length=20, description="Primary phone number")
    phone_secondary: Optional[str] = Field(None, max_length=20, description="Secondary phone number")
    fax: Optional[str] = Field(None, max_length=20, description="Fax number")
    email: Optional[str] = Field(None, max_length=100, description="Business email address")
    website: Optional[str] = Field(None, max_length=200, description="Business website URL")

    # Business Hours Reference
    business_hours_note: Optional[str] = Field(None, max_length=200, description="Additional note about business hours")

    @validator('country')
    def validate_country_code(cls, v):
        """Validate country code format."""
        return v.upper() if v else v

    @validator('zipCode')
    def validate_zip_code(cls, v):
        """Validate ZIP code format."""
        if v and not v.replace(' ', '').replace('-', '').isalnum():
            raise ValueError('ZIP code must contain only alphanumeric characters, spaces, and hyphens')
        return v

    @validator('latitude')
    def validate_latitude(cls, v):
        """Validate latitude range."""
        if v is not None and not (-90 <= v <= 90):
            raise ValueError('Latitude must be between -90 and 90 degrees')
        return v

    @validator('longitude')
    def validate_longitude(cls, v):
        """Validate longitude range."""
        if v is not None and not (-180 <= v <= 180):
            raise ValueError('Longitude must be between -180 and 180 degrees')
        return v

    @validator('phone', 'phone_secondary', 'fax')
    def validate_phone_numbers(cls, v):
        """Validate phone number format."""
        if v is not None and v.strip():  # Only validate if not empty
            # Remove common formatting characters
            cleaned = v.replace(' ', '').replace('-', '').replace('(', '').replace(')', '').replace('+', '')
            if not cleaned.isdigit():
                raise ValueError('Phone number must contain only digits and common formatting characters')
            if len(cleaned) < 8 or len(cleaned) > 15:
                raise ValueError('Phone number must be between 8 and 15 digits')
        return v


class LocationSettingsUpdate(BaseModel):
    """Schema for updating location settings."""
    
    country: Optional[str] = Field(
        None, 
        min_length=2, 
        max_length=2, 
        description="Country code (ISO 3166-1 alpha-2)"
    )
    address: Optional[AddressUpdateSchema] = Field(
        None,
        description="Complete address with coordinates"
    )
