from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional

from app.core.db_dependencies import get_db
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    get_current_tenant,
    require_tenant_role,
)
from app.modules.core.roles.models.roles import RolePermissions, TenantRole
from app.modules.core.users.models.user import User
from app.modules.core.tenants.models.tenant import Tenant

# Create router
router = APIRouter()


@router.get(
    "/",
    status_code=status.HTTP_200_OK,
    summary="Get Staff Management Module Status",
)
async def get_staff_management_status(
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role(RolePermissions.ADMIN_ROLES)),
):
    """
    Get the status of the Staff Management module.
    Requires OWNER or MANAGER role in the tenant.
    """
    return {
        "status": "active",
        "module": "staff_management",
        "version": "1.0.0",
        "tenant_id": str(current_tenant.id),
    }
