"""Email Metadata model for the Email module."""

import uuid  # noqa: E402
from datetime import datetime
from typing import TYPE_CHECKING, Optional

from sqlalchemy import (  # noqa: E402
    Column,
    String,
    Boolean,
    DateTime,
    ForeignKey,
    Index,
    Integer,
    Text,
)
from sqlalchemy.dialects.postgresql import UUID  # noqa: E402
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import Base  # noqa: E402

if TYPE_CHECKING:
    from app.modules.shared.email.models.email_account import EmailAccount  # noqa: E402


class EmailMetadata(Base):
    """Email Metadata model.

    Represents metadata for an email message.
    """

    __tablename__ = "email_metadata"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Email account association
    email_account_id = Column(
        UUID(as_uuid=True), ForeignKey("email_accounts.id"), nullable=False, index=True
    )

    # Email metadata
    message_id = Column(String, nullable=False, unique=True)  # Unique ID from email headers
    from_address = Column(String, nullable=False)
    to_addresses = Column(Text, nullable=False)  # Can be multiple addresses
    cc_addresses = Column(Text, nullable=True)  # Can be multiple addresses
    subject = Column(String, nullable=False)
    received_date = Column(DateTime(timezone=True), nullable=False)

    # Email status
    mailbox = Column(String, nullable=False, default="INBOX")  # e.g., "INBOX", "Sent", etc.
    is_read = Column(Boolean, default=False)
    is_flagged = Column(Boolean, default=False)
    is_deleted = Column(Boolean, default=False)

    # Email storage
    size_bytes = Column(Integer, nullable=False)
    path_to_file = Column(String, nullable=False)  # Path to the actual email file in Maildir

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )

    # Relationships
    # Commented out until the module is implemented
    # email_account = relationship("EmailAccount", back_populates="email_metadata")

    # Indexes
    __table_args__ = (
        Index("ix_email_metadata_email_account_id_mailbox", email_account_id, mailbox),
        Index(
            "ix_email_metadata_email_account_id_received_date",
            email_account_id,
            received_date,
        ),
    )

    def __repr__(self):
        return f"<EmailMetadata(id={self.id}, subject='{self.subject}')>"
