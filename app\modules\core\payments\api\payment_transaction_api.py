"""API endpoints for payment transactions."""

import uuid
from typing import List, Optional, TYPE_CHECKING

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.core.exceptions import BusinessLogicError
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    get_current_tenant,
    require_tenant_role,
)
from app.modules.core.roles.models.roles import TenantRole, RolePermissions

from app.modules.core.tenants.models.tenant import Tenant

# Use TYPE_CHECKING to avoid circular import
if TYPE_CHECKING:
    from app.modules.core.users.models.user import User

from app.modules.core.payments.schemas.payment_transaction import (  # noqa: E402
    PaymentTransactionCreate,
    PaymentTransactionUpdate,
    PaymentTransactionRead,
    PaymentTransactionWithRefundsRead,
    PaymentRefundCreate,
    PaymentRefundUpdate,
    PaymentRefundRead,
)
from app.modules.core.payments.services.payment_transaction_service import (  # noqa: E402
    payment_transaction_service,
)

router = APIRouter()


@router.post(
    "/transactions",
    response_model=PaymentTransactionRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create Payment Transaction",
    description="Create a new payment transaction for the tenant.",
)
async def create_payment_transaction(
    *,
    db: AsyncSession = Depends(get_db),
    transaction_in: PaymentTransactionCreate,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(required_roles=RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
) -> PaymentTransactionRead:
    """
    Creates a new payment transaction for the tenant.
    Requires STAFF, MANAGER, or OWNER role.
    """
    try:
        transaction = await payment_transaction_service.create(
            db=db, tenant_id=current_tenant.id, obj_in=transaction_in
        )
        return transaction
    except BusinessLogicError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get(
    "/transactions",
    response_model=List[PaymentTransactionRead],
    status_code=status.HTTP_200_OK,
    summary="List Payment Transactions",
    description="List all payment transactions for the tenant.",
)
async def list_payment_transactions(
    *,
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(required_roles=RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
    status_filter: Optional[str] = Query(None, description="Filter by transaction status"),
    skip: int = 0,
    limit: int = 100,
) -> List[PaymentTransactionRead]:
    """
    Lists all payment transactions for the tenant.
    Requires STAFF, MANAGER, or OWNER role.
    """
    transactions = await payment_transaction_service.get_multi(
        db=db, tenant_id=current_tenant.id, status_filter=status_filter, skip=skip, limit=limit
    )
    return transactions


@router.get(
    "/transactions/{transaction_id}",
    response_model=PaymentTransactionRead,
    status_code=status.HTTP_200_OK,
    summary="Get Payment Transaction",
    description="Get details of a specific payment transaction.",
)
async def get_payment_transaction(
    *,
    db: AsyncSession = Depends(get_db),
    transaction_id: uuid.UUID,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(required_roles=RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
) -> PaymentTransactionRead:
    """
    Gets details of a specific payment transaction.
    Requires STAFF, MANAGER, or OWNER role.
    """
    transaction = await payment_transaction_service.get(
        db=db, id=transaction_id, tenant_id=current_tenant.id
    )

    if not transaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Payment transaction with id {transaction_id} not found or does not belong to the tenant.",
        )

    return transaction


@router.put(
    "/transactions/{transaction_id}",
    response_model=PaymentTransactionRead,
    status_code=status.HTTP_200_OK,
    summary="Update Payment Transaction",
    description="Update an existing payment transaction.",
)
async def update_payment_transaction(
    *,
    db: AsyncSession = Depends(get_db),
    transaction_id: uuid.UUID,
    transaction_in: PaymentTransactionUpdate,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(required_roles=RolePermissions.ADMIN_ROLES, tenant_id_source="header")
    ),
) -> PaymentTransactionRead:
    """
    Updates an existing payment transaction.
    Requires MANAGER or OWNER role.
    """
    try:
        transaction = await payment_transaction_service.update(
            db=db, id=transaction_id, tenant_id=current_tenant.id, obj_in=transaction_in
        )

        if not transaction:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Payment transaction with id {transaction_id} not found or does not belong to the tenant.",
            )

        return transaction
    except BusinessLogicError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.post(
    "/transactions/{transaction_id}/refunds",
    response_model=PaymentRefundRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create Payment Refund",
    description="Create a new refund for a payment transaction.",
)
async def create_payment_refund(
    *,
    db: AsyncSession = Depends(get_db),
    transaction_id: uuid.UUID,
    refund_in: PaymentRefundCreate,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(required_roles=RolePermissions.ADMIN_ROLES, tenant_id_source="header")
    ),
) -> PaymentRefundRead:
    """
    Creates a new refund for a payment transaction.
    Requires MANAGER or OWNER role.
    """
    try:
        refund = await payment_transaction_service.create_refund(
            db=db,
            transaction_id=transaction_id,
            tenant_id=current_tenant.id,
            obj_in=refund_in,
            created_by=current_user.id,
        )
        return refund
    except BusinessLogicError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )


@router.put(
    "/refunds/{refund_id}",
    response_model=PaymentRefundRead,
    status_code=status.HTTP_200_OK,
    summary="Update Payment Refund",
    description="Update an existing payment refund.",
)
async def update_payment_refund(
    *,
    db: AsyncSession = Depends(get_db),
    refund_id: uuid.UUID,
    refund_in: PaymentRefundUpdate,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(required_roles=RolePermissions.ADMIN_ROLES, tenant_id_source="header")
    ),
) -> PaymentRefundRead:
    """
    Updates an existing payment refund.
    Requires MANAGER or OWNER role.
    """
    try:
        refund = await payment_transaction_service.update_refund(
            db=db, refund_id=refund_id, tenant_id=current_tenant.id, obj_in=refund_in
        )

        if not refund:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Payment refund with id {refund_id} not found or does not belong to the tenant.",  # noqa: E501
            )

        return refund
    except BusinessLogicError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
