"""Models for HR module."""

from .employee import Employee, EmploymentStatus, EmploymentType  # noqa: E402
from .time_tracking import (
    TimeRecord,
    TimeRecordType,
    TimeRecordSource,
    OvertimeRecord,
    TimeBank,
    TimeBankTransaction,
    TimeBankTransactionType,
    VerificationStatus,
    TimeMirror,
    TimeMirrorStatus,
    TimeMirrorRecord,
    OvertimeRule,
)
from .work_schedule import (  # noqa: E402
    Day,
    ScheduleType,
    ScheduleRecurrence,
    ScheduleTemplate,
    ScheduleTemplateEntry,
    WorkSchedule,
    WorkShift,
    LeaveType,
    LeaveRequestStatus,
    LeaveRequest,
)
from .document_management import (  # noqa: E402
    Document,
    DocumentType,
    DocumentStatus,
    DocumentAccess,
    DocumentSignature,
    DocumentTemplate,
)
from .recruitment import (  # noqa: E402
    Job,
    JobStatus,
    JobType,
    Candidate,
    CandidateStatus,
    Interview,
    InterviewStatus,
    Assessment,
    AssessmentType,
)

__all__ = [
    "Employee",
    "EmploymentStatus",
    "EmploymentType",
    "TimeRecord",
    "TimeRecordType",
    "TimeRecordSource",
    "OvertimeRecord",
    "TimeBank",
    "TimeBankTransaction",
    "TimeBankTransactionType",
    "VerificationStatus",
    "TimeMirror",
    "TimeMirrorStatus",
    "TimeMirrorRecord",
    "OvertimeRule",
    "Day",
    "ScheduleType",
    "ScheduleRecurrence",
    "ScheduleTemplate",
    "ScheduleTemplateEntry",
    "WorkSchedule",
    "WorkShift",
    "LeaveType",
    "LeaveRequestStatus",
    "LeaveRequest",
    "Document",
    "DocumentType",
    "DocumentStatus",
    "DocumentAccess",
    "DocumentSignature",
    "DocumentTemplate",
    "Job",
    "JobStatus",
    "JobType",
    "Candidate",
    "CandidateStatus",
    "Interview",
    "InterviewStatus",
    "Assessment",
    "AssessmentType",
]
