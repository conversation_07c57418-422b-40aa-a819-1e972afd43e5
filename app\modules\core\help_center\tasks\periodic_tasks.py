"""
Help Center Periodic Tasks

Tarefas periódicas para manutenção do sistema de help center.
"""

from celery import Celery
from celery.schedules import crontab

# Configuração das tarefas periódicas
HELP_CENTER_BEAT_SCHEDULE = {
    # Limpeza de mensagens expiradas - diariamente às 2:00
    'cleanup-expired-messages': {
        'task': 'app.modules.core.help_center.tasks.help_center_tasks.cleanup_expired_messages',
        'schedule': crontab(hour=2, minute=0),
        'options': {'queue': 'help_center_cleanup'}
    },
    
    # Limpeza de tickets expirados - semanalmente aos domingos às 3:00
    'cleanup-expired-tickets': {
        'task': 'app.modules.core.help_center.tasks.help_center_tasks.cleanup_expired_tickets',
        'schedule': crontab(hour=3, minute=0, day_of_week=0),
        'options': {'queue': 'help_center_cleanup'}
    },
    
    # Limpeza de arquivos órfãos - diariamente às 4:00
    'cleanup-orphaned-files': {
        'task': 'app.modules.core.help_center.tasks.help_center_tasks.cleanup_orphaned_files',
        'schedule': crontab(hour=4, minute=0),
        'options': {'queue': 'help_center_cleanup'}
    },
}

# Configuração das filas
HELP_CENTER_TASK_ROUTES = {
    'app.modules.core.help_center.tasks.help_center_tasks.process_ticket_notification': {
        'queue': 'help_center_notifications'
    },
    'app.modules.core.help_center.tasks.help_center_tasks.send_ticket_email_notification': {
        'queue': 'help_center_emails'
    },
    'app.modules.core.help_center.tasks.help_center_tasks.cleanup_expired_messages': {
        'queue': 'help_center_cleanup'
    },
    'app.modules.core.help_center.tasks.help_center_tasks.cleanup_expired_tickets': {
        'queue': 'help_center_cleanup'
    },
    'app.modules.core.help_center.tasks.help_center_tasks.generate_help_center_report': {
        'queue': 'help_center_reports'
    },
}


def configure_help_center_celery(app: Celery):
    """
    Configura o Celery para as tarefas do help center.
    
    Args:
        app: Instância do Celery
    """
    # Adicionar schedule das tarefas periódicas
    if not hasattr(app.conf, 'beat_schedule'):
        app.conf.beat_schedule = {}
    
    app.conf.beat_schedule.update(HELP_CENTER_BEAT_SCHEDULE)
    
    # Configurar roteamento de tarefas
    if not hasattr(app.conf, 'task_routes'):
        app.conf.task_routes = {}
    
    app.conf.task_routes.update(HELP_CENTER_TASK_ROUTES)
    
    # Configurações específicas do help center
    app.conf.update(
        # Configurações de retry
        task_default_retry_delay=60,
        task_max_retries=3,
        
        # Configurações de timeout
        task_soft_time_limit=300,  # 5 minutos
        task_time_limit=600,       # 10 minutos
        
        # Configurações de serialização
        task_serializer='json',
        result_serializer='json',
        accept_content=['json'],
        
        # Configurações de resultado
        result_expires=3600,  # 1 hora
        
        # Configurações de worker
        worker_prefetch_multiplier=1,
        worker_max_tasks_per_child=1000,
    )
