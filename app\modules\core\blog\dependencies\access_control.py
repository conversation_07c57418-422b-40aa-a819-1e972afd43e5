"""
Blog Access Control Dependencies

Functions and dependencies for controlling access to blog content based on
user authentication status, subscription status, and post visibility levels.
"""

from typing import Optional, TYPE_CHECKING
from fastapi import HTTPException, status, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.auth_dependencies import get_optional_current_user

if TYPE_CHECKING:
    from app.modules.core.users.models.user import User

from app.modules.core.roles.models.roles import SystemRole
from ..models.blog_post import PostVisibility, BlogPost


async def check_post_access_permission(
    post: BlogPost,
    current_user: Optional["User"] = None
) -> bool:
    """
    Check if a user has permission to access a blog post based on its visibility.
    
    Args:
        post: The blog post to check access for
        current_user: The current authenticated user (None if anonymous)
        
    Returns:
        bool: True if user has access, False otherwise
    """
    # Public posts are accessible to everyone
    if post.visibility == PostVisibility.PUBLIC.value:
        return True
    
    # Private posts require authentication
    if post.visibility == PostVisibility.PRIVATE.value:
        return current_user is not None
    
    # Member-only posts require authentication and subscriber status
    if post.visibility == PostVisibility.MEMBER_ONLY.value:
        return (current_user is not None and 
                getattr(current_user, 'is_subscriber', False))
    
    return False


async def check_admin_permission(current_user: "User") -> bool:
    """
    Check if a user has admin permissions for blog management.
    
    Args:
        current_user: The current authenticated user
        
    Returns:
        bool: True if user is admin, False otherwise
    """
    return current_user.system_role == SystemRole.ADMIN.value


async def require_admin_permission(
    current_user: "User" = Depends(get_optional_current_user)
) -> "User":
    """
    Dependency that requires admin permissions.
    
    Args:
        current_user: The current authenticated user
        
    Returns:
        User: The authenticated admin user
        
    Raises:
        HTTPException: If user is not authenticated or not admin
    """
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required"
        )
    
    if not await check_admin_permission(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required for blog management"
        )
    
    return current_user


async def check_comment_permission(
    post: BlogPost,
    current_user: Optional["User"] = None
) -> bool:
    """
    Check if a user has permission to comment on a blog post.
    
    Users can only comment on posts they have permission to view,
    and they must be authenticated (no anonymous comments).
    
    Args:
        post: The blog post to comment on
        current_user: The current authenticated user (None if anonymous)
        
    Returns:
        bool: True if user can comment, False otherwise
    """
    # Must be authenticated to comment
    if not current_user:
        return False
    
    # Must have permission to view the post
    return await check_post_access_permission(post, current_user)


async def require_comment_permission(
    post: BlogPost,
    current_user: "User" = Depends(get_optional_current_user)
) -> "User":
    """
    Dependency that requires permission to comment on a post.
    
    Args:
        post: The blog post to comment on
        current_user: The current authenticated user
        
    Returns:
        User: The authenticated user with comment permission
        
    Raises:
        HTTPException: If user cannot comment on this post
    """
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required to comment"
        )
    
    if not await check_comment_permission(post, current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have permission to comment on this post"
        )
    
    return current_user


def get_user_access_level(current_user: Optional["User"]) -> str:
    """
    Get the access level for a user.
    
    Args:
        current_user: The current user (None if anonymous)
        
    Returns:
        str: The access level ('anonymous', 'authenticated', 'subscriber', 'admin')
    """
    if not current_user:
        return 'anonymous'
    
    if current_user.system_role == SystemRole.ADMIN.value:
        return 'admin'
    
    if getattr(current_user, 'is_subscriber', False):
        return 'subscriber'
    
    return 'authenticated'


def get_accessible_visibility_levels(current_user: Optional["User"]) -> list:
    """
    Get the list of post visibility levels accessible to a user.
    
    Args:
        current_user: The current user (None if anonymous)
        
    Returns:
        list: List of accessible visibility levels
    """
    access_level = get_user_access_level(current_user)
    
    if access_level == 'anonymous':
        return [PostVisibility.PUBLIC.value]
    elif access_level == 'authenticated':
        return [PostVisibility.PUBLIC.value, PostVisibility.PRIVATE.value]
    elif access_level in ['subscriber', 'admin']:
        return [
            PostVisibility.PUBLIC.value,
            PostVisibility.PRIVATE.value,
            PostVisibility.MEMBER_ONLY.value
        ]
    
    return [PostVisibility.PUBLIC.value]


async def filter_posts_by_access(
    posts: list,
    current_user: Optional["User"] = None
) -> list:
    """
    Filter a list of posts based on user access permissions.
    
    Args:
        posts: List of blog posts to filter
        current_user: The current user (None if anonymous)
        
    Returns:
        list: Filtered list of posts the user can access
    """
    if not posts:
        return []
    
    accessible_posts = []
    for post in posts:
        if await check_post_access_permission(post, current_user):
            accessible_posts.append(post)
    
    return accessible_posts


class BlogAccessControl:
    """
    Utility class for blog access control operations.
    """
    
    @staticmethod
    async def can_view_post(post: BlogPost, user: Optional["User"] = None) -> bool:
        """Check if user can view a specific post."""
        return await check_post_access_permission(post, user)
    
    @staticmethod
    async def can_comment_on_post(post: BlogPost, user: Optional["User"] = None) -> bool:
        """Check if user can comment on a specific post."""
        return await check_comment_permission(post, user)
    
    @staticmethod
    async def can_manage_blog(user: Optional["User"] = None) -> bool:
        """Check if user can manage blog content (admin only)."""
        if not user:
            return False
        return await check_admin_permission(user)
    
    @staticmethod
    def get_user_level(user: Optional["User"] = None) -> str:
        """Get user access level."""
        return get_user_access_level(user)
    
    @staticmethod
    def get_accessible_levels(user: Optional["User"] = None) -> list:
        """Get list of visibility levels accessible to user."""
        return get_accessible_visibility_levels(user)


# Export commonly used functions
__all__ = [
    'check_post_access_permission',
    'check_admin_permission',
    'require_admin_permission',
    'check_comment_permission',
    'require_comment_permission',
    'get_user_access_level',
    'get_accessible_visibility_levels',
    'filter_posts_by_access',
    'BlogAccessControl'
]
