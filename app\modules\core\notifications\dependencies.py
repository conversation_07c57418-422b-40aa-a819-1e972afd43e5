"""
Notification Dependencies

Dependências para o sistema de notificações.
"""

from fastapi import Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.user_auth import get_current_active_user
from app.modules.core.users.models.user import User

from .services import (
    NotificationService,
    NotificationQueueService,
    NotificationMetricsService,
    NotificationDeliveryService
)


def get_notification_service(db: AsyncSession = Depends(get_db)) -> NotificationService:
    """
    Dependência para obter o serviço de notificações.
    
    Args:
        db: Sessão do banco de dados
        
    Returns:
        Instância do NotificationService
    """
    return NotificationService(db)


def get_notification_queue_service(db: AsyncSession = Depends(get_db)) -> NotificationQueueService:
    """
    Dependência para obter o serviço de fila de notificações.
    
    Args:
        db: Sessão do banco de dados
        
    Returns:
        Instância do NotificationQueueService
    """
    return NotificationQueueService(db)


def get_notification_metrics_service(db: AsyncSession = Depends(get_db)) -> NotificationMetricsService:
    """
    Dependência para obter o serviço de métricas de notificações.
    
    Args:
        db: Sessão do banco de dados
        
    Returns:
        Instância do NotificationMetricsService
    """
    return NotificationMetricsService(db)


def get_notification_delivery_service(db: AsyncSession = Depends(get_db)) -> NotificationDeliveryService:
    """
    Dependência para obter o serviço de entrega de notificações.
    
    Args:
        db: Sessão do banco de dados
        
    Returns:
        Instância do NotificationDeliveryService
    """
    return NotificationDeliveryService(db)


def require_notification_sender(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """
    Dependência que verifica se o usuário pode enviar notificações.
    
    Args:
        current_user: Usuário atual
        
    Returns:
        Usuário se autorizado
        
    Raises:
        HTTPException: Se o usuário não tem permissão
    """
    # Admin pode sempre enviar
    if current_user.system_role == "admin":
        return current_user
    
    # TODO: Verificar se é tenant owner
    # Por enquanto, permite todos os usuários autenticados
    return current_user


def require_notification_admin(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """
    Dependência que requer permissões de admin para notificações.
    
    Args:
        current_user: Usuário atual
        
    Returns:
        Usuário se for admin
        
    Raises:
        HTTPException: Se o usuário não é admin
    """
    if current_user.system_role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Acesso restrito a administradores"
        )
    
    return current_user


def require_notification_manager(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """
    Dependência que requer permissões de gerenciamento de notificações.
    
    Permite admin e tenant owners.
    
    Args:
        current_user: Usuário atual
        
    Returns:
        Usuário se autorizado
        
    Raises:
        HTTPException: Se o usuário não tem permissão
    """
    # Admin sempre pode gerenciar
    if current_user.system_role == "admin":
        return current_user
    
    # TODO: Verificar se é tenant owner
    # Por enquanto, permite apenas admin
    raise HTTPException(
        status_code=status.HTTP_403_FORBIDDEN,
        detail="Acesso restrito a administradores e proprietários de restaurantes"
    )


async def validate_notification_access(
    notification_id: str,
    current_user: User = Depends(get_current_active_user),
    service: NotificationService = Depends(get_notification_service)
):
    """
    Dependência que valida acesso a uma notificação específica.
    
    Args:
        notification_id: ID da notificação
        current_user: Usuário atual
        service: Serviço de notificações
        
    Returns:
        Notificação se o usuário tem acesso
        
    Raises:
        HTTPException: Se a notificação não existe ou usuário não tem acesso
    """
    from uuid import UUID
    
    try:
        notification_uuid = UUID(notification_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="ID de notificação inválido"
        )
    
    notification = await service.get_notification(notification_uuid, current_user)
    
    if not notification:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Notificação não encontrada ou sem acesso"
        )
    
    return notification


def validate_notification_filters(
    status: str = None,
    priority: str = None,
    sender_type: str = None,
    target_type: str = None,
    is_read: bool = None,
    search: str = None
):
    """
    Dependência que valida e normaliza filtros de notificação.
    
    Args:
        status: Status da notificação
        priority: Prioridade da notificação
        sender_type: Tipo de remetente
        target_type: Tipo de destinatário
        is_read: Status de leitura
        search: Termo de busca
        
    Returns:
        Dicionário com filtros validados
    """
    from .schemas import NotificationFilters
    from .models import NotificationStatus, NotificationPriority, NotificationSenderType, NotificationTargetType
    
    # Valida status
    if status and status not in [s.value for s in NotificationStatus]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Status inválido: {status}"
        )
    
    # Valida prioridade
    if priority and priority not in [p.value for p in NotificationPriority]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Prioridade inválida: {priority}"
        )
    
    # Valida sender_type
    if sender_type and sender_type not in [st.value for st in NotificationSenderType]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Tipo de remetente inválido: {sender_type}"
        )
    
    # Valida target_type
    if target_type and target_type not in [tt.value for tt in NotificationTargetType]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Tipo de destinatário inválido: {target_type}"
        )
    
    return NotificationFilters(
        status=status,
        priority=priority,
        sender_type=sender_type,
        target_type=target_type,
        is_read=is_read,
        search=search
    )


def validate_pagination(
    page: int = 1,
    per_page: int = 20
):
    """
    Dependência que valida parâmetros de paginação.
    
    Args:
        page: Número da página
        per_page: Itens por página
        
    Returns:
        Tupla com (page, per_page) validados
        
    Raises:
        HTTPException: Se os parâmetros são inválidos
    """
    if page < 1:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Número da página deve ser maior que 0"
        )
    
    if per_page < 1 or per_page > 100:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Itens por página deve estar entre 1 e 100"
        )
    
    return page, per_page


def validate_metrics_period(period_days: int = 30):
    """
    Dependência que valida período para métricas.
    
    Args:
        period_days: Período em dias
        
    Returns:
        Período validado
        
    Raises:
        HTTPException: Se o período é inválido
    """
    if period_days < 1 or period_days > 365:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Período deve estar entre 1 e 365 dias"
        )
    
    return period_days
