# Core - Logging System

**Categoria:** Core
**Módulo:** Logging System
**Total de Endpoints:** 5
**Gerado em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [GET /api/modules/core/logs/](#get-apimodulescorelogs) - Get Logs
- [GET /api/modules/core/logs/alerts](#get-apimodulescorelogsalerts) - Get Alert Logs
- [GET /api/modules/core/logs/errors](#get-apimodulescorelogserrors) - Get Error Logs
- [GET /api/modules/core/logs/performance](#get-apimodulescorelogsperformance) - Get Performance Logs
- [GET /api/modules/core/logs/security](#get-apimodulescorelogssecurity) - Get Security Logs

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### LogListResponse

**Descrição:** Schema for log list responses.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `items` | Array[LogRead] | ✅ | - |
| `total` | integer | ✅ | - |
| `skip` | integer | ✅ | - |
| `limit` | integer | ✅ | - |
| `has_more` | boolean | ✅ | - |

## 🔗 Endpoints Detalhados

### GET /api/modules/core/logs/ {#get-apimodulescorelogs}

**Resumo:** Get Logs
**Descrição:** Get system logs with comprehensive filtering.

Requires admin role or appropriate permissions.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `level` | string | query | ❌ | Filter by log level |
| `category` | string | query | ❌ | Filter by category |
| `logger_name` | string | query | ❌ | Filter by logger name |
| `module` | string | query | ❌ | Filter by module |
| `user_id` | string | query | ❌ | Filter by user ID |
| `tenant_id` | string | query | ❌ | Filter by tenant ID |
| `session_id` | string | query | ❌ | Filter by session ID |
| `request_id` | string | query | ❌ | Filter by request ID |
| `correlation_id` | string | query | ❌ | Filter by correlation ID |
| `business_process` | string | query | ❌ | Filter by business process |
| `external_service` | string | query | ❌ | Filter by external service |
| `is_alert` | string | query | ❌ | Filter by alert status |
| `alert_severity` | string | query | ❌ | Filter by alert severity |
| `is_resolved` | string | query | ❌ | Filter by resolution status |
| `start_date` | string | query | ❌ | Start date for filtering |
| `end_date` | string | query | ❌ | End date for filtering |
| `search_term` | string | query | ❌ | Search term |
| `exception_type` | string | query | ❌ | Filter by exception type |
| `min_duration_ms` | string | query | ❌ | Minimum duration in ms |
| `max_duration_ms` | string | query | ❌ | Maximum duration in ms |
| `skip` | integer | query | ❌ | Number of records to skip |
| `limit` | integer | query | ❌ | Number of records to return |
| `sort_by` | string | query | ❌ | Field to sort by |
| `sort_order` | string | query | ❌ | Sort order |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LogListResponse](#loglistresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/logs/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/logs/alerts {#get-apimodulescorelogsalerts}

**Resumo:** Get Alert Logs
**Descrição:** Get alert logs specifically.

Requires admin role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `alert_severity` | string | query | ❌ | Filter by alert severity |
| `is_resolved` | string | query | ❌ | Filter by resolution status |
| `start_date` | string | query | ❌ | Start date for filtering |
| `end_date` | string | query | ❌ | End date for filtering |
| `skip` | integer | query | ❌ | Number of records to skip |
| `limit` | integer | query | ❌ | Number of records to return |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LogListResponse](#loglistresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/logs/alerts" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/logs/errors {#get-apimodulescorelogserrors}

**Resumo:** Get Error Logs
**Descrição:** Get error logs specifically.

Requires admin role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `start_date` | string | query | ❌ | Start date for filtering |
| `end_date` | string | query | ❌ | End date for filtering |
| `exception_type` | string | query | ❌ | Filter by exception type |
| `search_term` | string | query | ❌ | Search term |
| `skip` | integer | query | ❌ | Number of records to skip |
| `limit` | integer | query | ❌ | Number of records to return |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LogListResponse](#loglistresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/logs/errors" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/logs/performance {#get-apimodulescorelogsperformance}

**Resumo:** Get Performance Logs
**Descrição:** Get performance-related logs.

Requires admin role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `min_duration_ms` | string | query | ❌ | Minimum duration in ms |
| `start_date` | string | query | ❌ | Start date for filtering |
| `end_date` | string | query | ❌ | End date for filtering |
| `module` | string | query | ❌ | Filter by module |
| `external_service` | string | query | ❌ | Filter by external service |
| `skip` | integer | query | ❌ | Number of records to skip |
| `limit` | integer | query | ❌ | Number of records to return |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LogListResponse](#loglistresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/logs/performance" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/logs/security {#get-apimodulescorelogssecurity}

**Resumo:** Get Security Logs
**Descrição:** Get security-related logs.

Requires admin role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `start_date` | string | query | ❌ | Start date for filtering |
| `end_date` | string | query | ❌ | End date for filtering |
| `user_id` | string | query | ❌ | Filter by user ID |
| `ip_address` | string | query | ❌ | Filter by IP address |
| `search_term` | string | query | ❌ | Search term |
| `skip` | integer | query | ❌ | Number of records to skip |
| `limit` | integer | query | ❌ | Number of records to return |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LogListResponse](#loglistresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/logs/security" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
