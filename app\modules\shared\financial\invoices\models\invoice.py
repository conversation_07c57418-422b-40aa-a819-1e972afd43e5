"""Invoice models for financial system."""

import uuid
import enum
from datetime import date, datetime
from decimal import Decimal
from typing import TYPE_CHECKING, List, Optional
from sqlalchemy import (
    Column, String, ForeignKey, Text, Boolean, Index,
    DateTime, Date, Numeric, Integer
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base
from app.modules.core.functions.orders.models.order import Order

# Use TYPE_CHECKING to handle circular dependencies for type hinting
if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.core.users.models.user import User


class InvoiceStatus(str, enum.Enum):
    """Enum for invoice status."""

    DRAFT = "draft"
    PENDING = "pending"
    SENT = "sent"
    VIEWED = "viewed"  # Added for B2B tracking
    PAID = "paid"
    OVERDUE = "overdue"
    CANCELLED = "cancelled"
    DISPUTED = "disputed"  # Added for B2B disputes


class InvoiceType(str, enum.Enum):
    """Enum for invoice type."""

    SALE = "sale"  # Invoice for sales
    SERVICE = "service"  # Invoice for services
    ORDER = "order"  # Invoice generated from order
    CUSTOM = "custom"  # Custom invoice
    B2B_VENDOR = "b2b_vendor"  # B2B vendor invoice
    B2B_CUSTOMER = "b2b_customer"  # B2B customer invoice


class Invoice(Base):
    """
    Model for invoices.
    
    Invoices are generated for orders or created manually for sales/services.
    They can be exported to PDF and sent to customers.
    """
    
    __tablename__ = "invoices"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("tenants.id"), 
        nullable=False, 
        index=True
    )
    
    # Invoice identification
    invoice_number = Column(String(50), nullable=False, index=True)
    invoice_type = Column(
        String(20), 
        nullable=False, 
        default=InvoiceType.CUSTOM.value
    )
    status = Column(
        String(20), 
        nullable=False, 
        default=InvoiceStatus.DRAFT.value
    )
    
    # Dates
    issue_date = Column(Date, nullable=False, default=date.today)
    due_date = Column(Date, nullable=True)
    paid_date = Column(Date, nullable=True)
    
    # Customer information
    customer_name = Column(String(200), nullable=False)
    customer_email = Column(String(200), nullable=True)
    customer_phone = Column(String(50), nullable=True)
    customer_address = Column(Text, nullable=True)
    customer_tax_id = Column(String(50), nullable=True)
    
    # Financial details
    subtotal = Column(
        Numeric(10, 2), 
        nullable=False, 
        default=Decimal('0.00')
    )
    tax_amount = Column(
        Numeric(10, 2), 
        nullable=False, 
        default=Decimal('0.00')
    )
    discount_amount = Column(
        Numeric(10, 2), 
        nullable=False, 
        default=Decimal('0.00')
    )
    total_amount = Column(
        Numeric(10, 2), 
        nullable=False, 
        default=Decimal('0.00')
    )
    
    # Tax information
    tax_rate = Column(
        Numeric(5, 2), 
        nullable=False, 
        default=Decimal('0.00')
    )  # Tax rate as percentage
    
    # Payment information
    payment_method = Column(String(50), nullable=True)
    payment_reference = Column(String(100), nullable=True)
    
    # Additional information
    notes = Column(Text, nullable=True)
    terms_and_conditions = Column(Text, nullable=True)
    
    # Order reference (if invoice is generated from order)
    order_id = Column(
        UUID(as_uuid=True),
        ForeignKey("orders.id"),
        nullable=True,
        index=True
    )

    # B2B relationships (optional - for B2B invoices)
    vendor_id = Column(
        UUID(as_uuid=True),
        ForeignKey("eshop_tvendor_suppliers.id"),
        nullable=True,
        index=True
    )
    customer_b2b_id = Column(
        UUID(as_uuid=True),
        ForeignKey("eshop_tcostumers.id"),
        nullable=True,
        index=True
    )

    # B2B specific fields
    viewed_date = Column(DateTime, nullable=True)
    sent_date = Column(DateTime, nullable=True)

    # File management for B2B invoices
    file_name = Column(String(255), nullable=True)
    file_original_name = Column(String(255), nullable=True)
    file_path = Column(String(500), nullable=True)
    file_size = Column(Integer, nullable=True)
    file_type = Column(String(50), nullable=True)
    file_hash = Column(String(64), nullable=True)  # SHA-256 hash

    # Access control for B2B
    access_token = Column(String(128), nullable=True, unique=True)
    access_expires_at = Column(DateTime, nullable=True)
    download_count = Column(Integer, default=0, nullable=False)
    max_downloads = Column(Integer, default=10, nullable=False)

    # B2B notification settings
    notify_on_view = Column(Boolean, default=True, nullable=False)
    notify_on_due = Column(Boolean, default=True, nullable=False)
    reminder_sent = Column(Boolean, default=False, nullable=False)
    
    # System fields
    created_by = Column(
        UUID(as_uuid=True), 
        ForeignKey("users.id"), 
        nullable=False
    )
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )
    
    # PDF generation tracking
    pdf_generated_at = Column(DateTime, nullable=True)
    pdf_file_path = Column(String(500), nullable=True)
    
    # Relationships
    tenant = relationship(
        "app.modules.core.tenants.models.tenant.Tenant",
        viewonly=True
    )
    
    created_by_user = relationship(
        "app.modules.core.users.models.user.User",
        foreign_keys=[created_by],
        viewonly=True
    )
    
    order = relationship(
        "app.modules.core.functions.orders.models.order.Order",
        back_populates="invoices",
        viewonly=True
    )

    # B2B relationships
    vendor = relationship(
        "app.modules.core.eshop.models.tvendor_supplier.TVendorSupplier",
        foreign_keys=[vendor_id],
        viewonly=True
    )

    customer_b2b = relationship(
        "app.modules.core.eshop.models.tcostumer.TCostumer",
        foreign_keys=[customer_b2b_id],
        viewonly=True
    )

    # Invoice items (one-to-many relationship)
    items = relationship(
        "InvoiceItem",
        back_populates="invoice",
        cascade="all, delete-orphan"
    )
    
    # Table indexes for performance
    __table_args__ = (
        Index("ix_invoices_tenant_number", "tenant_id", "invoice_number"),
        Index("ix_invoices_tenant_status", "tenant_id", "status"),
        Index("ix_invoices_tenant_date", "tenant_id", "issue_date"),
        Index("ix_invoices_tenant_customer", "tenant_id", "customer_email"),
        # B2B specific indexes
        Index("ix_invoices_vendor_customer", "vendor_id", "customer_b2b_id"),
        Index("ix_invoices_vendor_status", "vendor_id", "status"),
        Index("ix_invoices_customer_status", "customer_b2b_id", "status"),
        Index("ix_invoices_due_date_status", "due_date", "status"),
        Index("ix_invoices_access_token", "access_token"),
        # Unique constraint for invoice number per tenant
        Index("uq_invoices_tenant_number", "tenant_id", "invoice_number", unique=True),
    )
    
    def __repr__(self):
        return (
            f"<Invoice(id={self.id}, "
            f"number='{self.invoice_number}', "
            f"status='{self.status}', "
            f"total={self.total_amount})>"
        )

    # B2B specific properties and methods
    @property
    def is_b2b_invoice(self) -> bool:
        """Check if this is a B2B invoice."""
        return self.invoice_type in [InvoiceType.B2B_VENDOR, InvoiceType.B2B_CUSTOMER]

    @property
    def is_overdue(self) -> bool:
        """Check if invoice is overdue."""
        if self.status in [InvoiceStatus.PAID, InvoiceStatus.CANCELLED]:
            return False
        if not self.due_date:
            return False
        return datetime.utcnow().date() > self.due_date

    @property
    def days_until_due(self) -> int:
        """Calculate days until due date."""
        if not self.due_date:
            return 0
        delta = self.due_date - datetime.utcnow().date()
        return delta.days

    @property
    def outstanding_amount(self) -> Decimal:
        """Calculate outstanding amount (for partial payments)."""
        # This would need to be calculated from payment records
        # For now, return total_amount if not paid
        if self.status == InvoiceStatus.PAID:
            return Decimal('0.00')
        return self.total_amount

    @property
    def access_expired(self) -> bool:
        """Check if access token has expired."""
        if not self.access_expires_at:
            return False
        return datetime.utcnow() > self.access_expires_at

    @property
    def can_download(self) -> bool:
        """Check if file can still be downloaded."""
        if self.access_expired:
            return False
        return self.download_count < self.max_downloads

    def generate_access_token(self, expires_hours: int = 24) -> str:
        """Generate access token for file download."""
        import secrets
        from datetime import timedelta

        self.access_token = secrets.token_urlsafe(64)
        self.access_expires_at = datetime.utcnow() + timedelta(hours=expires_hours)

        return self.access_token

    def mark_as_viewed(self, viewed_by_id: uuid.UUID):
        """Mark invoice as viewed."""
        if self.status == InvoiceStatus.SENT:
            self.status = InvoiceStatus.VIEWED.value
            self.viewed_date = datetime.utcnow()

    def increment_download_count(self):
        """Increment download counter."""
        self.download_count += 1

    def update_status_if_overdue(self):
        """Update status to overdue if past due date."""
        if self.is_overdue and self.status not in [InvoiceStatus.PAID.value, InvoiceStatus.CANCELLED.value]:
            self.status = InvoiceStatus.OVERDUE.value


class InvoiceItem(Base):
    """
    Model for invoice line items.
    
    Each invoice can have multiple items with description, quantity, price, etc.
    """
    
    __tablename__ = "invoice_items"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    invoice_id = Column(
        UUID(as_uuid=True),
        ForeignKey("invoices.id"),
        nullable=False,
        index=True
    )
    
    # Item details
    description = Column(String(500), nullable=False)
    quantity = Column(
        Numeric(10, 3), 
        nullable=False, 
        default=Decimal('1.000')
    )
    unit_price = Column(
        Numeric(10, 2), 
        nullable=False, 
        default=Decimal('0.00')
    )
    total_price = Column(
        Numeric(10, 2), 
        nullable=False, 
        default=Decimal('0.00')
    )
    
    # Optional fields
    unit = Column(String(20), nullable=True)  # e.g., "pcs", "kg", "hours"
    product_code = Column(String(50), nullable=True)
    notes = Column(Text, nullable=True)
    
    # Tax information for this item
    tax_rate = Column(
        Numeric(5, 2), 
        nullable=False, 
        default=Decimal('0.00')
    )
    tax_amount = Column(
        Numeric(10, 2), 
        nullable=False, 
        default=Decimal('0.00')
    )
    
    # Display order
    sort_order = Column(Integer, nullable=False, default=0)
    
    # Relationships
    invoice = relationship(
        "Invoice",
        back_populates="items"
    )
    
    # Table indexes for performance
    __table_args__ = (
        Index("ix_invoice_items_invoice_order", "invoice_id", "sort_order"),
    )
    
    def __repr__(self):
        return (
            f"<InvoiceItem(id={self.id}, "
            f"description='{self.description}', "
            f"quantity={self.quantity}, "
            f"total={self.total_price})>"
        )
