"""Invoice models for financial system."""

import uuid
import enum
from datetime import date, datetime
from decimal import Decimal
from typing import TYPE_CHECKING, List, Optional
from sqlalchemy import (
    Column, String, ForeignKey, Text, Boolean, Index,
    DateTime, Date, Numeric, Integer
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base
from app.modules.core.functions.orders.models.order import Order

# Use TYPE_CHECKING to handle circular dependencies for type hinting
if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.core.users.models.user import User


class InvoiceStatus(str, enum.Enum):
    """Enum for invoice status."""
    
    DRAFT = "draft"
    PENDING = "pending"
    SENT = "sent"
    PAID = "paid"
    OVERDUE = "overdue"
    CANCELLED = "cancelled"


class InvoiceType(str, enum.Enum):
    """Enum for invoice type."""
    
    SALE = "sale"  # Invoice for sales
    SERVICE = "service"  # Invoice for services
    ORDER = "order"  # Invoice generated from order
    CUSTOM = "custom"  # Custom invoice


class Invoice(Base):
    """
    Model for invoices.
    
    Invoices are generated for orders or created manually for sales/services.
    They can be exported to PDF and sent to customers.
    """
    
    __tablename__ = "invoices"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("tenants.id"), 
        nullable=False, 
        index=True
    )
    
    # Invoice identification
    invoice_number = Column(String(50), nullable=False, index=True)
    invoice_type = Column(
        String(20), 
        nullable=False, 
        default=InvoiceType.CUSTOM.value
    )
    status = Column(
        String(20), 
        nullable=False, 
        default=InvoiceStatus.DRAFT.value
    )
    
    # Dates
    issue_date = Column(Date, nullable=False, default=date.today)
    due_date = Column(Date, nullable=True)
    paid_date = Column(Date, nullable=True)
    
    # Customer information
    customer_name = Column(String(200), nullable=False)
    customer_email = Column(String(200), nullable=True)
    customer_phone = Column(String(50), nullable=True)
    customer_address = Column(Text, nullable=True)
    customer_tax_id = Column(String(50), nullable=True)
    
    # Financial details
    subtotal = Column(
        Numeric(10, 2), 
        nullable=False, 
        default=Decimal('0.00')
    )
    tax_amount = Column(
        Numeric(10, 2), 
        nullable=False, 
        default=Decimal('0.00')
    )
    discount_amount = Column(
        Numeric(10, 2), 
        nullable=False, 
        default=Decimal('0.00')
    )
    total_amount = Column(
        Numeric(10, 2), 
        nullable=False, 
        default=Decimal('0.00')
    )
    
    # Tax information
    tax_rate = Column(
        Numeric(5, 2), 
        nullable=False, 
        default=Decimal('0.00')
    )  # Tax rate as percentage
    
    # Payment information
    payment_method = Column(String(50), nullable=True)
    payment_reference = Column(String(100), nullable=True)
    
    # Additional information
    notes = Column(Text, nullable=True)
    terms_and_conditions = Column(Text, nullable=True)
    
    # Order reference (if invoice is generated from order)
    order_id = Column(
        UUID(as_uuid=True),
        ForeignKey("orders.id"),
        nullable=True,
        index=True
    )
    
    # System fields
    created_by = Column(
        UUID(as_uuid=True), 
        ForeignKey("users.id"), 
        nullable=False
    )
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )
    
    # PDF generation tracking
    pdf_generated_at = Column(DateTime, nullable=True)
    pdf_file_path = Column(String(500), nullable=True)
    
    # Relationships
    tenant = relationship(
        "app.modules.core.tenants.models.tenant.Tenant",
        viewonly=True
    )
    
    created_by_user = relationship(
        "app.modules.core.users.models.user.User",
        foreign_keys=[created_by],
        viewonly=True
    )
    
    order = relationship(
        "app.modules.core.functions.orders.models.order.Order",
        back_populates="invoices",
        viewonly=True
    )
    
    # Invoice items (one-to-many relationship)
    items = relationship(
        "InvoiceItem",
        back_populates="invoice",
        cascade="all, delete-orphan"
    )
    
    # Table indexes for performance
    __table_args__ = (
        Index("ix_invoices_tenant_number", "tenant_id", "invoice_number"),
        Index("ix_invoices_tenant_status", "tenant_id", "status"),
        Index("ix_invoices_tenant_date", "tenant_id", "issue_date"),
        Index("ix_invoices_tenant_customer", "tenant_id", "customer_email"),
        # Unique constraint for invoice number per tenant
        Index("uq_invoices_tenant_number", "tenant_id", "invoice_number", unique=True),
    )
    
    def __repr__(self):
        return (
            f"<Invoice(id={self.id}, "
            f"number='{self.invoice_number}', "
            f"status='{self.status}', "
            f"total={self.total_amount})>"
        )


class InvoiceItem(Base):
    """
    Model for invoice line items.
    
    Each invoice can have multiple items with description, quantity, price, etc.
    """
    
    __tablename__ = "invoice_items"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    invoice_id = Column(
        UUID(as_uuid=True),
        ForeignKey("invoices.id"),
        nullable=False,
        index=True
    )
    
    # Item details
    description = Column(String(500), nullable=False)
    quantity = Column(
        Numeric(10, 3), 
        nullable=False, 
        default=Decimal('1.000')
    )
    unit_price = Column(
        Numeric(10, 2), 
        nullable=False, 
        default=Decimal('0.00')
    )
    total_price = Column(
        Numeric(10, 2), 
        nullable=False, 
        default=Decimal('0.00')
    )
    
    # Optional fields
    unit = Column(String(20), nullable=True)  # e.g., "pcs", "kg", "hours"
    product_code = Column(String(50), nullable=True)
    notes = Column(Text, nullable=True)
    
    # Tax information for this item
    tax_rate = Column(
        Numeric(5, 2), 
        nullable=False, 
        default=Decimal('0.00')
    )
    tax_amount = Column(
        Numeric(10, 2), 
        nullable=False, 
        default=Decimal('0.00')
    )
    
    # Display order
    sort_order = Column(Integer, nullable=False, default=0)
    
    # Relationships
    invoice = relationship(
        "Invoice",
        back_populates="items"
    )
    
    # Table indexes for performance
    __table_args__ = (
        Index("ix_invoice_items_invoice_order", "invoice_id", "sort_order"),
    )
    
    def __repr__(self):
        return (
            f"<InvoiceItem(id={self.id}, "
            f"description='{self.description}', "
            f"quantity={self.quantity}, "
            f"total={self.total_price})>"
        )
