import uuid
from sqlalchemy import Column, <PERSON>, <PERSON><PERSON><PERSON>, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base


class CashRegister(Base):  # TimestampMixin já está em Base
    __tablename__ = "cash_registers"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    name = Column(String, nullable=False)
    is_active = Column(Boolean, default=True)

    tenant = relationship(
        "app.modules.core.tenants.models.tenant.Tenant", back_populates="cash_registers"
    )
    # Relacionamento com SaleTransaction (um CashRegister pode ter várias SaleTransactions)
    transactions = relationship(
        "SaleTransaction", back_populates="cash_register", cascade="all, delete-orphan"
    )
    # Relacionamento com CashRegisterSession (um CashRegister pode ter várias sessões)
    sessions = relationship(
        "CashRegisterSession",
        back_populates="cash_register",
        cascade="all, delete-orphan",
    )
