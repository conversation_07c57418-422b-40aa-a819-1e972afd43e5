"""
Auction and Lottery Router
==========================

Main router for auction and lottery APIs.
"""

from fastapi import APIRouter

from app.modules.core.eshop.api.auction_lottery_api import router as auction_lottery_api_router

# Create the main router
router = APIRouter()

# Include the auction/lottery API router
router.include_router(
    auction_lottery_api_router,
    prefix="/auction-lottery",
    tags=["Auction & Lottery"]
)
