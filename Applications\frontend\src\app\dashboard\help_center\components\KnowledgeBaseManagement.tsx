'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/lib/auth/AuthProvider';
import { apiClient } from '@/lib/api/client';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { ArticleModal } from './ArticleModal';
import { 
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  MagnifyingGlassIcon,
  BookOpenIcon,
  TagIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';

interface KnowledgeBaseArticle {
  id: string;
  title: string;
  content: string;
  category?: string;
  tags?: string;
  view_count: number;
  helpful_count: number;
  not_helpful_count: number;
  helpfulness_ratio: number;
  is_published: boolean;
  created_at: string;
  updated_at?: string;
  created_by_name?: string;
}

interface CreateArticleData {
  title: string;
  content: string;
  category?: string;
  tags?: string;
  is_published: boolean;
}

export function KnowledgeBaseManagement() {
  const { user, isAdmin } = useAuth();
  const [articles, setArticles] = useState<KnowledgeBaseArticle[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingArticle, setEditingArticle] = useState<KnowledgeBaseArticle | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [categories, setCategories] = useState<string[]>([]);

  const fetchArticles = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams();
      if (searchQuery) params.append('search', searchQuery);
      if (selectedCategory) params.append('category', selectedCategory);

      // Para admin, não enviar X-Tenant-ID header
      const config = {
        headers: {
          'X-Tenant-ID': undefined // Remove o header para admin
        }
      };

      const response = await apiClient.get(
        `/modules/core/help-center/admin/kb/articles?${params.toString()}`,
        config
      );

      setArticles(response.data.articles || []);

      // Extract categories
      const uniqueCategories = Array.from(
        new Set(response.data.articles?.map((article: any) => article.category).filter(Boolean))
      ) as string[];
      setCategories(uniqueCategories);

    } catch (err: any) {
      console.error('Erro ao carregar artigos:', err);
      setError('Erro ao carregar artigos da base de conhecimento.');
    } finally {
      setIsLoading(false);
    }
  }, [searchQuery, selectedCategory]);

  useEffect(() => {
    if (isAdmin()) {
      fetchArticles();
    }
  }, [searchQuery, selectedCategory, fetchArticles, isAdmin]);

  const handleCreateArticle = async (articleData: CreateArticleData) => {
    try {
      // Para admin, não enviar X-Tenant-ID header
      const config = {
        headers: {
          'X-Tenant-ID': undefined // Remove o header para admin
        }
      };

      await apiClient.post('/modules/core/help-center/admin/kb/articles', articleData, config);
      setShowCreateModal(false);
      await fetchArticles();
    } catch (err: any) {
      console.error('Erro ao criar artigo:', err);
      throw new Error('Erro ao criar artigo.');
    }
  };

  const handleUpdateArticle = async (articleData: CreateArticleData) => {
    if (!editingArticle) return;

    try {
      // Para admin, não enviar X-Tenant-ID header
      const config = {
        headers: {
          'X-Tenant-ID': undefined // Remove o header para admin
        }
      };

      await apiClient.put(`/modules/core/help-center/admin/kb/articles/${editingArticle.id}`, articleData, config);
      setEditingArticle(null);
      await fetchArticles();
    } catch (err: any) {
      console.error('Erro ao atualizar artigo:', err);
      throw new Error('Erro ao atualizar artigo.');
    }
  };

  const handleDeleteArticle = async (articleId: string) => {
    if (!confirm('Tem certeza que deseja deletar este artigo?')) {
      return;
    }

    try {
      // Para admin, não enviar X-Tenant-ID header
      const config = {
        headers: {
          'X-Tenant-ID': undefined // Remove o header para admin
        }
      };

      await apiClient.delete(`/modules/core/help-center/admin/kb/articles/${articleId}`, config);
      await fetchArticles();
    } catch (err: any) {
      console.error('Erro ao deletar artigo:', err);
      setError('Erro ao deletar artigo.');
    }
  };

  const togglePublishStatus = async (article: KnowledgeBaseArticle) => {
    try {
      // Para admin, não enviar X-Tenant-ID header
      const config = {
        headers: {
          'X-Tenant-ID': undefined // Remove o header para admin
        }
      };

      await apiClient.put(`/modules/core/help-center/admin/kb/articles/${article.id}`, {
        is_published: !article.is_published
      }, config);
      await fetchArticles();
    } catch (err: any) {
      console.error('Erro ao alterar status:', err);
      setError('Erro ao alterar status de publicação.');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!isAdmin()) {
    return (
      <div className="text-center py-12">
        <BookOpenIcon className="mx-auto h-12 w-12 text-red-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">
          Acesso Negado
        </h3>
        <p className="mt-1 text-sm text-gray-500">
          Apenas administradores podem gerenciar a base de conhecimento.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">
            Gerenciar Base de Conhecimento
          </h2>
          <p className="text-sm text-gray-600">
            Crie e gerencie artigos para ajudar os usuários
          </p>
        </div>
        
        <button
          onClick={() => setShowCreateModal(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Novo Artigo
        </button>
      </div>

      {/* Filters */}
      <div className="glass rounded-lg p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Buscar
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Buscar artigos..."
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Categoria
            </label>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Todas as categorias</option>
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-sm text-red-600">{error}</div>
        </div>
      )}

      {/* Articles List */}
      <div className="glass rounded-lg overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        ) : articles.length === 0 ? (
          <div className="text-center py-12">
            <BookOpenIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              Nenhum artigo encontrado
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              Comece criando seu primeiro artigo da base de conhecimento.
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Artigo
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Métricas
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Criado
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ações
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {articles.map((article) => (
                  <tr key={article.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <div className="flex items-start space-x-3">
                        <BookOpenIcon className="h-5 w-5 text-blue-500 mt-0.5" />
                        <div className="flex-1 min-w-0">
                          <div className="text-sm font-medium text-gray-900 truncate">
                            {article.title}
                          </div>
                          {article.category && (
                            <div className="flex items-center mt-1">
                              <TagIcon className="h-3 w-3 text-gray-400 mr-1" />
                              <span className="text-xs text-gray-500">{article.category}</span>
                            </div>
                          )}
                          {article.tags && (
                            <div className="flex flex-wrap gap-1 mt-1">
                              {article.tags.split(',').slice(0, 3).map((tag, index) => (
                                <span
                                  key={index}
                                  className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800"
                                >
                                  {tag.trim()}
                                </span>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <button
                        onClick={() => togglePublishStatus(article)}
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          article.is_published
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}
                      >
                        {article.is_published ? 'Publicado' : 'Rascunho'}
                      </button>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <div className="flex items-center">
                          <EyeIcon className="h-4 w-4 mr-1" />
                          <span>{article.view_count}</span>
                        </div>
                        <div className="flex items-center">
                          <ChartBarIcon className="h-4 w-4 mr-1" />
                          <span>{Math.round(article.helpfulness_ratio * 100)}%</span>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-500">
                        {formatDate(article.created_at)}
                      </div>
                      {article.created_by_name && (
                        <div className="text-xs text-gray-400">
                          por {article.created_by_name}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => setEditingArticle(article)}
                          className="text-blue-600 hover:text-blue-900"
                          title="Editar"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteArticle(article.id)}
                          className="text-red-600 hover:text-red-900"
                          title="Deletar"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Create/Edit Modal */}
      <ArticleModal
        isOpen={showCreateModal || !!editingArticle}
        onClose={() => {
          setShowCreateModal(false);
          setEditingArticle(null);
        }}
        onSave={editingArticle ? handleUpdateArticle : handleCreateArticle}
        article={editingArticle}
        categories={categories}
      />
    </div>
  );
}
