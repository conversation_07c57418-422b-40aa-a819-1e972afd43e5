"""
Auction and Lottery WebSocket Manager
====================================

Real-time updates for auctions and lotteries.
"""

import json
from typing import Dict, Any, List, Optional
from uuid import UUID
from datetime import datetime

import socketio
from app.core.socketio_manager import sio_server as sio


class AuctionLotteryWebSocketManager:
    """WebSocket manager for auction and lottery real-time updates."""
    
    def __init__(self):
        self.namespace = "/auction-lottery"
        self.active_connections: Dict[str, Dict[str, Any]] = {}
    
    async def join_auction_room(self, sid: str, auction_id: UUID, user_id: UUID):
        """Join a user to an auction room for real-time updates."""
        room_name = f"auction_{auction_id}"
        
        await sio.enter_room(sid, room_name, namespace=self.namespace)
        
        # Track connection
        self.active_connections[sid] = {
            "user_id": str(user_id),
            "auction_id": str(auction_id),
            "room": room_name,
            "type": "auction",
            "joined_at": datetime.utcnow().isoformat()
        }
        
        # Send confirmation
        await sio.emit(
            "joined_auction",
            {
                "auction_id": str(auction_id),
                "message": "Successfully joined auction updates"
            },
            room=sid,
            namespace=self.namespace
        )
    
    async def join_lottery_room(self, sid: str, lottery_id: UUID, user_id: UUID):
        """Join a user to a lottery room for real-time updates."""
        room_name = f"lottery_{lottery_id}"
        
        await sio.enter_room(sid, room_name, namespace=self.namespace)
        
        # Track connection
        self.active_connections[sid] = {
            "user_id": str(user_id),
            "lottery_id": str(lottery_id),
            "room": room_name,
            "type": "lottery",
            "joined_at": datetime.utcnow().isoformat()
        }
        
        # Send confirmation
        await sio.emit(
            "joined_lottery",
            {
                "lottery_id": str(lottery_id),
                "message": "Successfully joined lottery updates"
            },
            room=sid,
            namespace=self.namespace
        )
    
    async def leave_room(self, sid: str):
        """Remove user from their current room."""
        if sid in self.active_connections:
            connection = self.active_connections[sid]
            room_name = connection.get("room")
            
            if room_name:
                await sio.leave_room(sid, room_name, namespace=self.namespace)
            
            del self.active_connections[sid]
    
    async def emit_new_bid(
        self,
        auction_id: UUID,
        bid_data: Dict[str, Any],
        time_remaining: Optional[int] = None
    ):
        """Emit new bid notification to auction room."""
        room_name = f"auction_{auction_id}"
        
        message = {
            "type": "new_bid",
            "auction_id": str(auction_id),
            "bid": bid_data,
            "time_remaining": time_remaining,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await sio.emit(
            "new_bid",
            message,
            room=room_name,
            namespace=self.namespace
        )
    
    async def emit_auction_extended(
        self,
        auction_id: UUID,
        new_end_time: datetime,
        extension_minutes: int,
        extensions_used: int,
        max_extensions: int
    ):
        """Emit auction extension notification."""
        room_name = f"auction_{auction_id}"
        
        message = {
            "type": "auction_extended",
            "auction_id": str(auction_id),
            "new_end_time": new_end_time.isoformat(),
            "extension_minutes": extension_minutes,
            "extensions_used": extensions_used,
            "max_extensions": max_extensions,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await sio.emit(
            "auction_extended",
            message,
            room=room_name,
            namespace=self.namespace
        )
    
    async def emit_auction_ended(
        self,
        auction_id: UUID,
        winner_id: Optional[UUID] = None,
        winning_bid: Optional[float] = None,
        reason: str = "time_expired"
    ):
        """Emit auction ended notification."""
        room_name = f"auction_{auction_id}"
        
        message = {
            "type": "auction_ended",
            "auction_id": str(auction_id),
            "winner_id": str(winner_id) if winner_id else None,
            "winning_bid": float(winning_bid) if winning_bid else None,
            "reason": reason,  # "time_expired", "buy_now", "cancelled"
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await sio.emit(
            "auction_ended",
            message,
            room=room_name,
            namespace=self.namespace
        )
    
    async def emit_buy_now_purchase(
        self,
        auction_id: UUID,
        buyer_id: UUID,
        purchase_price: float
    ):
        """Emit buy-now purchase notification."""
        room_name = f"auction_{auction_id}"
        
        message = {
            "type": "buy_now_purchase",
            "auction_id": str(auction_id),
            "buyer_id": str(buyer_id),
            "purchase_price": float(purchase_price),
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await sio.emit(
            "buy_now_purchase",
            message,
            room=room_name,
            namespace=self.namespace
        )
    
    async def emit_lottery_ticket_purchased(
        self,
        lottery_id: UUID,
        participant_id: UUID,
        tickets_purchased: int,
        total_tickets_sold: int
    ):
        """Emit lottery ticket purchase notification."""
        room_name = f"lottery_{lottery_id}"
        
        message = {
            "type": "tickets_purchased",
            "lottery_id": str(lottery_id),
            "participant_id": str(participant_id),
            "tickets_purchased": tickets_purchased,
            "total_tickets_sold": total_tickets_sold,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await sio.emit(
            "tickets_purchased",
            message,
            room=room_name,
            namespace=self.namespace
        )
    
    async def emit_lottery_winner_drawn(
        self,
        lottery_id: UUID,
        winner_id: UUID,
        winning_ticket_number: str,
        total_participants: int
    ):
        """Emit lottery winner announcement."""
        room_name = f"lottery_{lottery_id}"
        
        message = {
            "type": "winner_drawn",
            "lottery_id": str(lottery_id),
            "winner_id": str(winner_id),
            "winning_ticket_number": winning_ticket_number,
            "total_participants": total_participants,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await sio.emit(
            "winner_drawn",
            message,
            room=room_name,
            namespace=self.namespace
        )
    
    async def emit_time_update(self, auction_id: UUID, time_remaining: int):
        """Emit time remaining update for auction."""
        room_name = f"auction_{auction_id}"
        
        message = {
            "type": "time_update",
            "auction_id": str(auction_id),
            "time_remaining": time_remaining,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await sio.emit(
            "time_update",
            message,
            room=room_name,
            namespace=self.namespace
        )
    
    async def broadcast_auction_stats(self, tenant_id: UUID, stats: Dict[str, Any]):
        """Broadcast auction statistics to tenant room."""
        room_name = f"tenant_{tenant_id}_auctions"
        
        message = {
            "type": "auction_stats",
            "tenant_id": str(tenant_id),
            "stats": stats,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await sio.emit(
            "auction_stats",
            message,
            room=room_name,
            namespace=self.namespace
        )
    
    async def broadcast_lottery_stats(self, tenant_id: UUID, stats: Dict[str, Any]):
        """Broadcast lottery statistics to tenant room."""
        room_name = f"tenant_{tenant_id}_lotteries"
        
        message = {
            "type": "lottery_stats",
            "tenant_id": str(tenant_id),
            "stats": stats,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await sio.emit(
            "lottery_stats",
            message,
            room=room_name,
            namespace=self.namespace
        )
    
    def get_active_connections(self) -> Dict[str, Dict[str, Any]]:
        """Get all active connections."""
        return self.active_connections.copy()
    
    def get_room_participants(self, room_type: str, item_id: UUID) -> List[str]:
        """Get list of user IDs in a specific room."""
        room_name = f"{room_type}_{item_id}"
        participants = []
        
        for sid, connection in self.active_connections.items():
            if connection.get("room") == room_name:
                participants.append(connection.get("user_id"))
        
        return participants


# Global instance
auction_lottery_ws_manager = AuctionLotteryWebSocketManager()


# WebSocket event handlers
@sio.event(namespace="/auction-lottery")
async def connect(sid, environ, auth):
    """Handle WebSocket connection."""
    print(f"Client {sid} connected to auction-lottery namespace")
    
    await sio.emit(
        "connected",
        {"message": "Connected to auction-lottery updates"},
        room=sid,
        namespace="/auction-lottery"
    )


@sio.event(namespace="/auction-lottery")
async def disconnect(sid):
    """Handle WebSocket disconnection."""
    print(f"Client {sid} disconnected from auction-lottery namespace")
    
    # Clean up connection tracking
    await auction_lottery_ws_manager.leave_room(sid)


@sio.event(namespace="/auction-lottery")
async def join_auction(sid, data):
    """Handle joining an auction room."""
    try:
        auction_id = UUID(data.get("auction_id"))
        user_id = UUID(data.get("user_id"))
        
        await auction_lottery_ws_manager.join_auction_room(sid, auction_id, user_id)
        
    except (ValueError, TypeError) as e:
        await sio.emit(
            "error",
            {"message": f"Invalid auction join request: {str(e)}"},
            room=sid,
            namespace="/auction-lottery"
        )


@sio.event(namespace="/auction-lottery")
async def join_lottery(sid, data):
    """Handle joining a lottery room."""
    try:
        lottery_id = UUID(data.get("lottery_id"))
        user_id = UUID(data.get("user_id"))
        
        await auction_lottery_ws_manager.join_lottery_room(sid, lottery_id, user_id)
        
    except (ValueError, TypeError) as e:
        await sio.emit(
            "error",
            {"message": f"Invalid lottery join request: {str(e)}"},
            room=sid,
            namespace="/auction-lottery"
        )


@sio.event(namespace="/auction-lottery")
async def leave_room(sid, data):
    """Handle leaving current room."""
    await auction_lottery_ws_manager.leave_room(sid)
