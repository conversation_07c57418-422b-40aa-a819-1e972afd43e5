import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/orders_provider.dart';
import 'package:timeago/timeago.dart' as timeago;

class RecentOrdersList extends ConsumerWidget {
  const RecentOrdersList({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final ordersState = ref.watch(ordersProvider);

    if (ordersState.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (ordersState.error != null) {
      return Center(
        child: Text(
          'Erro: ${ordersState.error}',
          style: const TextStyle(color: Colors.red),
        ),
      );
    }

    final recentOrders = ordersState.orders
        .where((order) => order.status != 'completed')
        .take(10)
        .toList();

    if (recentOrders.isEmpty) {
      return const Center(
        child: Text('Nenhum pedido recente'),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: recentOrders.length,
      itemBuilder: (context, index) {
        final order = recentOrders[index];
        return Card(
          margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: _getStatusColor(order.status),
              child: Text(
                order.orderNumber,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ),
            title: Text(
              'Mesa ${order.tableNumber}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('${order.itemCount} itens • ${order.total}'),
                Text(
                  timeago.format(order.createdAt, locale: 'pt_BR'),
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            trailing: Chip(
              label: Text(
                order.statusDisplayName,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
              backgroundColor: _getStatusColor(order.status).withValues(alpha: 0.2),
              side: BorderSide(
                color: _getStatusColor(order.status),
                width: 1,
              ),
            ),
            onTap: () {
              // Navigate to order details
            },
          ),
        );
      },
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'preparing':
        return Colors.blue;
      case 'ready':
        return Colors.green;
      case 'delivered':
        return Colors.purple;
      case 'completed':
        return Colors.grey;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}