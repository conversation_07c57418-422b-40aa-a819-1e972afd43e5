import enum
from sqlalchemy import (
    Column,
    Integer,
    String,
    Numeric,
    DateTime,
    <PERSON>ole<PERSON>,
    Enum,
    <PERSON><PERSON>ey,
    Text,
)

# Adicionar importação para UUID
from sqlalchemy.dialects.postgresql import UUID  # noqa: E402
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from typing import TYPE_CHECKING

from app.db.base import Base  # Importar Base de app.db.base  # noqa: E402

# Importar Tenant corretamente
from app.modules.core.tenants.models.tenant import Tenant  # noqa: F401  # noqa: E402

# Para type hinting da relação inversa em Tenant
# Removed duplicate import of Tenant in TYPE_CHECKING block
if TYPE_CHECKING:
    pass  # Tenant is already imported above


class BillingPeriodEnum(str, enum.Enum):
    MONTHLY = "monthly"
    ANNUALLY = "annually"


class FeatureValueTypeEnum(str, enum.Enum):
    BOOLEAN = "BOOLEAN"
    NUMERIC = "NUMERIC"
    TEXT = "TEXT"


class SubscriptionStatusEnum(str, enum.Enum):
    ACTIVE = "ACTIVE"
    CANCELLED = "CANCELLED"
    EXPIRED = "EXPIRED"
    PENDING_PAYMENT = "PENDING_PAYMENT"
    IN_TRIAL = "IN_TRIAL"
    SUSPENDED = "SUSPENDED"


class Plan(Base):
    __tablename__ = "plans"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), unique=True, nullable=False, index=True)
    description = Column(Text, nullable=True)
    price = Column(Numeric(10, 2), nullable=False)
    currency = Column(String(10), nullable=False, default="BRL")
    billing_period = Column(Enum(BillingPeriodEnum), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    display_order = Column(Integer, default=0, nullable=False)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relacionamento com PlanFeatureLink
    features_links = relationship(
        "PlanFeatureLink", back_populates="plan", cascade="all, delete-orphan"
    )
    # Relacionamento com TenantSubscription - Comentado até que o módulo seja implementado
    # subscriptions = relationship("TenantSubscription", back_populates="plan")


class Feature(Base):
    __tablename__ = "features"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    key = Column(String(100), unique=True, nullable=False, index=True)
    description = Column(Text, nullable=True)
    value_type = Column(Enum(FeatureValueTypeEnum), nullable=False)
    unit = Column(String(50), nullable=True)  # Ex: "usuários", "GB", "/mês"

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relacionamento com PlanFeatureLink
    plans_links = relationship(
        "PlanFeatureLink", back_populates="feature", cascade="all, delete-orphan"
    )


class PlanFeatureLink(Base):
    __tablename__ = "plan_feature_links"

    plan_id = Column(Integer, ForeignKey("plans.id"), primary_key=True)
    feature_id = Column(Integer, ForeignKey("features.id"), primary_key=True)
    # Armazenado como string, parseado na lógica de negócios
    value = Column(String(255), nullable=False)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    plan = relationship("Plan", back_populates="features_links")
    feature = relationship("Feature", back_populates="plans_links")


class TenantSubscription(Base):
    __tablename__ = "tenant_subscriptions"

    id = Column(Integer, primary_key=True, index=True)
    # ForeignKey aponta para a tabela 'tenants', coluna 'id'
    tenant_id = Column(
        UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True
    )  # Ajustado para UUID se Tenant.id for UUID
    plan_id = Column(Integer, ForeignKey("plans.id"), nullable=False, index=True)

    start_date = Column(DateTime(timezone=True), nullable=False, default=func.now())
    end_date = Column(DateTime(timezone=True), nullable=True)
    next_billing_date = Column(DateTime(timezone=True), nullable=True)
    status = Column(
        Enum(SubscriptionStatusEnum),
        nullable=False,
        default=SubscriptionStatusEnum.IN_TRIAL,
    )

    payment_gateway_subscription_id = Column(String(255), nullable=True, index=True)
    trial_ends_at = Column(DateTime(timezone=True), nullable=True)

    cancelled_at = Column(DateTime(timezone=True), nullable=True)
    cancellation_reason = Column(Text, nullable=True)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Comentado até que o módulo seja implementado
    # plan = relationship("Plan", back_populates="subscriptions")
    # Relacionamento com Tenant - Comentado até que o módulo seja implementado
    # tenant = relationship("Tenant", back_populates="subscriptions")

    # Nota: O modelo Tenant em app/models/tenant.py precisará ter o relacionamento inverso:
    # subscriptions = relationship("TenantSubscription", back_populates="tenant", cascade="all, delete-orphan")  # noqa: E501
