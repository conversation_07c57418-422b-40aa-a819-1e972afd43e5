from datetime import datetime
from typing import List, Optional, Dict, Any
import uuid
from pydantic import BaseModel, Field, ConfigDict

from app.modules.shared.hr.models.document_management import (
    DocumentType,
    DocumentStatus,
)

# Base Document Schema


class DocumentBase(BaseModel):
    """Base schema for Document."""

    title: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    document_type: DocumentType
    file_name: str
    file_type: str
    is_confidential: bool = False
    access_level: Optional[str] = None
    expiry_date: Optional[datetime] = None
    tags: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None


# Schema for creating a new document


class DocumentCreate(DocumentBase):
    """Schema for creating a new Document."""

    employee_id: Optional[uuid.UUID] = None
    file_path: str
    file_size: int


# Schema for updating a document


class DocumentUpdate(BaseModel):
    """Schema for updating a Document."""

    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    document_type: Optional[DocumentType] = None
    status: Optional[DocumentStatus] = None
    is_confidential: Optional[bool] = None
    access_level: Optional[str] = None
    expiry_date: Optional[datetime] = None
    tags: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None


# Schema for reading a document


class DocumentRead(DocumentBase):
    """Schema for reading a Document."""

    id: uuid.UUID
    employee_id: Optional[uuid.UUID] = None
    status: DocumentStatus
    upload_date: datetime
    version: int
    is_latest_version: bool
    previous_version_id: Optional[uuid.UUID] = None
    file_path: str
    file_size: int
    tenant_id: uuid.UUID
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Schema for document access


class DocumentAccessBase(BaseModel):
    """Base schema for DocumentAccess."""

    document_id: uuid.UUID
    user_id: uuid.UUID
    access_type: str
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None


# Schema for creating a document access record


class DocumentAccessCreate(DocumentAccessBase):
    """Schema for creating a DocumentAccess."""


# Schema for reading a document access record


class DocumentAccessRead(DocumentAccessBase):
    """Schema for reading a DocumentAccess."""

    id: uuid.UUID
    access_time: datetime
    tenant_id: uuid.UUID

    model_config = ConfigDict(from_attributes=True)


# Schema for document signature


class DocumentSignatureBase(BaseModel):
    """Base schema for DocumentSignature."""

    document_id: uuid.UUID
    user_id: uuid.UUID
    signature_type: str
    signature_data: Optional[str] = None


# Schema for creating a document signature


class DocumentSignatureCreate(DocumentSignatureBase):
    """Schema for creating a DocumentSignature."""


# Schema for reading a document signature


class DocumentSignatureRead(DocumentSignatureBase):
    """Schema for reading a DocumentSignature."""

    id: uuid.UUID
    signature_time: datetime
    is_verified: bool
    verification_method: Optional[str] = None
    verification_time: Optional[datetime] = None
    tenant_id: uuid.UUID

    model_config = ConfigDict(from_attributes=True)


# Schema for document template


class DocumentTemplateBase(BaseModel):
    """Base schema for DocumentTemplate."""

    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    document_type: DocumentType
    variables: Optional[List[str]] = None
    content: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


# Schema for creating a document template


class DocumentTemplateCreate(DocumentTemplateBase):
    """Schema for creating a DocumentTemplate."""

    file_path: str


# Schema for updating a document template


class DocumentTemplateUpdate(BaseModel):
    """Schema for updating a DocumentTemplate."""

    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    document_type: Optional[DocumentType] = None
    variables: Optional[List[str]] = None
    content: Optional[str] = None
    is_active: Optional[bool] = None
    metadata: Optional[Dict[str, Any]] = None


# Schema for reading a document template


class DocumentTemplateRead(DocumentTemplateBase):
    """Schema for reading a DocumentTemplate."""

    id: uuid.UUID
    file_path: str
    is_active: bool
    created_at: datetime
    updated_at: datetime
    tenant_id: uuid.UUID

    model_config = ConfigDict(from_attributes=True)


# Schema for document generation from template


class GenerateDocumentRequest(BaseModel):
    """Schema for generating a document from a template."""

    template_id: uuid.UUID
    employee_id: Optional[uuid.UUID] = None
    variable_values: Dict[str, Any]
    title: Optional[str] = None
    description: Optional[str] = None
    is_confidential: bool = False
    access_level: Optional[str] = None
    expiry_date: Optional[datetime] = None
    tags: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None
