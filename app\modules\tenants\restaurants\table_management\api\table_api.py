from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from typing import List, Optional, Annotated, Any, TYPE_CHECKING
import uuid

from sqlalchemy.ext.asyncio import AsyncSession
from app.core.db_dependencies import get_db

# Use TYPE_CHECKING to avoid circular import
if TYPE_CHECKING:
    from app.modules.core.users.models.user import User
from app.modules.core.tenants.models.tenant import Tenant

from app.modules.tenants.restaurants.table_management.schemas.table import (
    TableCreate,
    TableUpdate,
    TableRead,
    TableLayoutCreate,
    TableLayoutUpdate,
    TableLayoutRead,
)
from app.modules.tenants.restaurants.table_management.models.table import TableStatus
from app.modules.tenants.restaurants.table_management.services.table_service import TableService

from app.modules.core.auth.dependencies.auth_dependencies import (
    get_current_active_user,
)
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    get_current_tenant_from_header,
    require_tenant_role,
)
from app.modules.core.roles.models.roles import RolePermissions

router = APIRouter()

# Define required roles for different operations
view_roles = RolePermissions.VIEW_ROLES  # All roles that can view
write_roles = RolePermissions.ADMIN_ROLES  # Only admin roles can modify


# Table endpoints
@router.post(
    "/tables/",
    response_model=TableRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new table",
)
async def create_table(
    table_in: TableCreate,
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=write_roles))] = None,
    table_service: TableService = Depends(lambda: TableService()),
):
    """
    Create a new table for the current tenant.
    Requires OWNER or MANAGER tenant role.
    """
    return await table_service.create_table(db=db, table_in=table_in, tenant_id=current_tenant.id)


@router.get(
    "/tables/",
    response_model=List[TableRead],
    summary="Get all tables",
)
async def get_tables(
    layout_id: Optional[uuid.UUID] = Query(None, description="Filter by layout ID"),
    status: Optional[TableStatus] = Query(None, description="Filter by table status"),
    capacity_min: Optional[int] = Query(None, ge=1, description="Minimum capacity"),
    capacity_max: Optional[int] = Query(None, ge=1, description="Maximum capacity"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    zone: Optional[str] = Query(None, description="Filter by zone"),
    skip: int = Query(0, ge=0, description="Number of tables to skip"),
    limit: int = Query(100, ge=1, le=100, description="Maximum number of tables to return"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))] = None,
    table_service: TableService = Depends(lambda: TableService()),
):
    """
    Get all tables for the current tenant with optional filters.
    Requires at least STAFF tenant role.
    """
    return await table_service.get_tables(
        db=db,
        tenant_id=current_tenant.id,
        layout_id=layout_id,
        status=status,
        capacity_min=capacity_min,
        capacity_max=capacity_max,
        is_active=is_active,
        zone=zone,
        skip=skip,
        limit=limit,
    )


@router.get(
    "/tables/{table_id}",
    response_model=TableRead,
    summary="Get a table by ID",
)
async def get_table(
    table_id: uuid.UUID = Path(..., description="The ID of the table to get"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))] = None,
    table_service: TableService = Depends(lambda: TableService()),
):
    """
    Get a table by ID for the current tenant.
    Requires at least STAFF tenant role.
    """
    table = await table_service.get_table(db=db, table_id=table_id, tenant_id=current_tenant.id)
    if not table:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Table not found",
        )
    return table


@router.put(
    "/tables/{table_id}",
    response_model=TableRead,
    summary="Update a table",
)
async def update_table(
    table_id: uuid.UUID = Path(..., description="The ID of the table to update"),
    table_in: TableUpdate = None,
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=write_roles))] = None,
    table_service: TableService = Depends(lambda: TableService()),
):
    """
    Update a table for the current tenant.
    Requires OWNER or MANAGER tenant role.
    """
    table = await table_service.update_table(
        db=db, table_id=table_id, table_in=table_in, tenant_id=current_tenant.id
    )
    if not table:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Table not found",
        )
    return table


@router.delete(
    "/tables/{table_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete a table",
)
async def delete_table(
    table_id: uuid.UUID = Path(..., description="The ID of the table to delete"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=write_roles))] = None,
    table_service: TableService = Depends(lambda: TableService()),
):
    """
    Delete a table for the current tenant.
    Requires OWNER or MANAGER tenant role.
    """
    result = await table_service.delete_table(db=db, table_id=table_id, tenant_id=current_tenant.id)
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Table not found",
        )
    return None


@router.patch(
    "/tables/{table_id}/status",
    response_model=TableRead,
    summary="Update table status",
)
async def update_table_status(
    table_id: uuid.UUID = Path(..., description="The ID of the table to update"),
    status: TableStatus = Query(..., description="The new status for the table"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))] = None,
    table_service: TableService = Depends(lambda: TableService()),
):
    """
    Update the status of a table for the current tenant.
    Requires at least STAFF tenant role.
    """
    table = await table_service.update_table_status(
        db=db, table_id=table_id, new_status=status, tenant_id=current_tenant.id
    )
    if not table:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Table not found",
        )
    return table


@router.get(
    "/tables/available",
    response_model=List[TableRead],
    summary="Get available tables",
)
async def get_available_tables(
    capacity: Optional[int] = Query(None, ge=1, description="Minimum capacity required"),
    layout_id: Optional[uuid.UUID] = Query(None, description="Filter by layout ID"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))] = None,
    table_service: TableService = Depends(lambda: TableService()),
):
    """
    Get all available tables for the current tenant.
    Requires at least STAFF tenant role.
    """
    return await table_service.get_available_tables(
        db=db,
        tenant_id=current_tenant.id,
        capacity=capacity,
        layout_id=layout_id,
    )


# Table Layout endpoints
@router.post(
    "/layouts/",
    response_model=TableLayoutRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new table layout",
)
async def create_layout(
    layout_in: TableLayoutCreate,
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=write_roles))] = None,
    table_service: TableService = Depends(lambda: TableService()),
):
    """
    Create a new table layout for the current tenant.
    Requires OWNER or MANAGER tenant role.
    """
    return await table_service.create_layout(db=db, layout_in=layout_in, tenant_id=current_tenant.id)


@router.get(
    "/layouts/",
    response_model=List[TableLayoutRead],
    summary="Get all table layouts",
)
async def get_layouts(
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    include_tables: bool = Query(False, description="Include tables in the response"),
    skip: int = Query(0, ge=0, description="Number of layouts to skip"),
    limit: int = Query(100, ge=1, le=100, description="Maximum number of layouts to return"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))] = None,
    table_service: TableService = Depends(lambda: TableService()),
):
    """
    Get all table layouts for the current tenant with optional filters.
    Requires at least STAFF tenant role.
    """
    return await table_service.get_layouts(
        db=db,
        tenant_id=current_tenant.id,
        is_active=is_active,
        include_tables=include_tables,
        skip=skip,
        limit=limit,
    )


@router.get(
    "/layouts/{layout_id}",
    response_model=TableLayoutRead,
    summary="Get a table layout by ID",
)
async def get_layout(
    layout_id: uuid.UUID = Path(..., description="The ID of the layout to get"),
    include_tables: bool = Query(False, description="Include tables in the response"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))] = None,
    table_service: TableService = Depends(lambda: TableService()),
):
    """
    Get a table layout by ID for the current tenant.
    Requires at least STAFF tenant role.
    """
    layout = await table_service.get_layout(
        db=db,
        layout_id=layout_id,
        tenant_id=current_tenant.id,
        include_tables=include_tables,
    )
    if not layout:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Table layout not found",
        )
    return layout


@router.put(
    "/layouts/{layout_id}",
    response_model=TableLayoutRead,
    summary="Update a table layout",
)
async def update_layout(
    layout_id: uuid.UUID = Path(..., description="The ID of the layout to update"),
    layout_in: TableLayoutUpdate = None,
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=write_roles))] = None,
    table_service: TableService = Depends(lambda: TableService()),
):
    """
    Update a table layout for the current tenant.
    Requires OWNER or MANAGER tenant role.
    """
    layout = await table_service.update_layout(
        db=db, layout_id=layout_id, layout_in=layout_in, tenant_id=current_tenant.id
    )
    if not layout:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Table layout not found",
        )
    return layout


@router.delete(
    "/layouts/{layout_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete a table layout",
)
async def delete_layout(
    layout_id: uuid.UUID = Path(..., description="The ID of the layout to delete"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=write_roles))] = None,
    table_service: TableService = Depends(lambda: TableService()),
):
    """
    Delete a table layout for the current tenant.
    Requires OWNER or MANAGER tenant role.
    """
    result = await table_service.delete_layout(db=db, layout_id=layout_id, tenant_id=current_tenant.id)
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Table layout not found",
        )
    return None
