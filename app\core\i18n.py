import gettext
from typing import Callable, Optional, Dict

from fastapi import Request, Depends
from babel.support import Translations

from app.core.config import settings

# Dicionário para armazenar instâncias de Translations carregadas
loaded_translations: Dict[str, Translations] = {}


def load_translations() -> None:
    """Carrega as traduções para todos os idiomas suportados."""
    global loaded_translations  # This is used to modify the global variable
    # Initialize loaded_translations if not already initialized
    if loaded_translations is None:
        loaded_translations = {}
    for lang in settings.SUPPORTED_LANGUAGES:
        try:
            # Tenta carregar o arquivo .mo compilado
            translation = Translations.load(settings.LOCALES_DIR, [lang], domain="messages")
            loaded_translations[lang] = translation
            print(f"Loaded translations for: {lang}")
        except Exception as e:
            print(f"Could not load translations for {lang}: {e}. Falling back to NullTranslations.")
            # Usa NullTranslations se o arquivo .mo não for encontrado ou houver erro
            loaded_translations[lang] = gettext.NullTranslations()


# Carrega as traduções quando o módulo é importado
load_translations()


def get_best_match_language(accept_language_header: Optional[str]) -> str:
    """
    Determina o melhor idioma suportado com base no cabeçalho Accept-Language.
    Retorna o idioma padrão se nenhuma correspondência for encontrada
    ou o cabeçalho estiver ausente.
    """
    if not accept_language_header:
        return settings.DEFAULT_LANGUAGE

    # Simple parsing of Accept-Language header
    # (can be improved with libraries like `babel.negotiate_locale`)
    # Example: "fr-CH, fr;q=0.9, en;q=0.8, de;q=0.7, *;q=0.5"
    languages = []
    for lang_part in accept_language_header.split(","):
        parts = lang_part.strip().split(";")
        lang = parts[0].strip()
        # Handle language codes like 'en-US' -> 'en' or 'pt-BR' -> 'pt_BR'
        # (matching our locale dir)
        lang_normalized = lang.replace("-", "_")
        if "_" in lang_normalized:  # pt_BR, en_US etc.
            languages.append(lang_normalized)
        # Add the base language as well (e.g., 'en' from 'en_US')
        base_lang = lang_normalized.split("_")[0]
        if base_lang != lang_normalized:
            languages.append(base_lang)
        # Add base lang if not already added (e.g. 'en')
        elif base_lang not in languages:
            languages.append(base_lang)

    for lang in languages:
        if lang in settings.SUPPORTED_LANGUAGES:
            return lang  # Retorna o primeiro idioma suportado encontrado na ordem de preferência

    return settings.DEFAULT_LANGUAGE  # Retorna o padrão se nenhum corresponder


async def get_locale(request: Request) -> str:
    """Dependência FastAPI para obter o locale preferido da requisição."""
    accept_language = request.headers.get("accept-language")
    return get_best_match_language(accept_language)


def get_translation_func(locale: str = Depends(get_locale)) -> Callable[[str], str]:
    """
    Dependência FastAPI que retorna a função gettext apropriada para o locale.
    """
    translations = loaded_translations.get(
        locale, loaded_translations.get(settings.DEFAULT_LANGUAGE)
    )
    # Garante que sempre retorne uma função gettext válida
    if translations:
        return translations.gettext
    else:
        # Fallback muito básico se tudo falhar
        return lambda text: text


# Alias comum para a função de tradução (pode ser usado diretamente nos endpoints/serviços)
# Exemplo de uso: _ = Depends(get_translation_func) ; translated_string = _("Hello")
# No entanto, injetar a dependência diretamente pode ser mais explícito:
# async def my_endpoint(translate: Callable[[str], str] = Depends(get_translation_func)):
#     message = translate("Some message")
#     ...
# # _ = Depends(get_translation_func)
# # Comentado para evitar TypeError: 'Depends' object is not callable


# Fornecer uma função _ padrão que pode ser importada para uso geral (e.g. em decoradores).
# Usará o idioma padrão se nenhuma tradução específica do request
# for injetada.
default_translations = loaded_translations.get(
    settings.DEFAULT_LANGUAGE, gettext.NullTranslations()
)
_ = default_translations.gettext

# Você precisará compilar os arquivos .po para .mo para que babel.support.Translations funcione.
# Comando típico: pybabel compile -d locales -D messages
