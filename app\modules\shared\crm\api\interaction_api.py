"""API endpoints for CRM interactions."""

import uuid  # noqa: E402
from typing import List, Optional, Annotated
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db  # noqa: E402
from app.modules.core.auth.dependencies.auth_dependencies import (
    get_current_active_user,
)  # noqa: E402
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    require_tenant_role,
)  # noqa: E402
from app.modules.core.roles.models.roles import TenantRole
from app.modules.core.users.models.user import User
from app.modules.shared.crm.models.interaction import InteractionType
from app.modules.shared.crm.schemas.interaction import (
    InteractionCreate,
    InteractionUpdate,
    InteractionRead,
)
from app.modules.shared.crm.services.interaction_service import interaction_service  # noqa: E402

router = APIRouter(prefix="/interactions", tags=["CRM - Interactions"])


@router.post(
    "/",
    response_model=InteractionRead,
    status_code=status.HTTP_201_CREATED,
    dependencies=[
        Depends(
            require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF], "header")
        )
    ],
)
async def create_interaction(
    interaction_in: InteractionCreate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    db: Annotated[AsyncSession, Depends(get_db)],
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Create a new CRM interaction.

    Requires tenant owner, manager, or staff role.
    """
    try:
        # Set the current user as the creator if not specified
        if not interaction_in.created_by_user_id:
            interaction_in.created_by_user_id = current_user.id

        db_interaction = await interaction_service.create_interaction(db, tenant_id, interaction_in)
        return db_interaction
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create interaction: {str(e)}",
        )


@router.get(
    "/{interaction_id}",
    response_model=InteractionRead,
    dependencies=[
        Depends(
            require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF], "header")
        )
    ],
)
async def get_interaction(
    interaction_id: uuid.UUID = Path(..., description="The ID of the interaction to retrieve"),
    db: Annotated[AsyncSession, Depends(get_db)] = None,
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Get a CRM interaction by ID.

    Requires tenant owner, manager, or staff role.
    """
    db_interaction = await interaction_service.get_interaction(db, tenant_id, interaction_id)
    if not db_interaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Interaction with ID {interaction_id} not found",
        )
    return db_interaction


@router.get(
    "/",
    response_model=List[InteractionRead],
    dependencies=[
        Depends(
            require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF], "header")
        )
    ],
)
async def get_interactions(
    account_id: Optional[uuid.UUID] = Query(None, description="Filter by account ID"),
    contact_id: Optional[uuid.UUID] = Query(None, description="Filter by contact ID"),
    skip: int = Query(0, ge=0, description="Number of interactions to skip"),
    limit: int = Query(100, ge=1, le=100, description="Maximum number of interactions to return"),
    interaction_type: Optional[InteractionType] = Query(
        None, description="Filter by interaction type"
    ),
    requires_followup: Optional[bool] = Query(None, description="Filter by requires_followup flag"),
    is_completed: Optional[bool] = Query(None, description="Filter by is_completed flag"),
    start_date: Optional[datetime] = Query(None, description="Filter by start date (inclusive)"),
    end_date: Optional[datetime] = Query(None, description="Filter by end date (inclusive)"),
    search: Optional[str] = Query(
        None, description="Search term for interaction subject or description"
    ),
    db: Annotated[AsyncSession, Depends(get_db)] = None,
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Get all CRM interactions with optional filtering.

    Requires tenant owner, manager, or staff role.
    """
    db_interactions = await interaction_service.get_interactions(
        db,
        tenant_id,
        account_id,
        contact_id,
        skip,
        limit,
        interaction_type,
        requires_followup,
        is_completed,
        start_date,
        end_date,
        search,
    )
    return db_interactions


@router.get(
    "/account/{account_id}",
    response_model=List[InteractionRead],
    dependencies=[
        Depends(
            require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF], "header")
        )
    ],
)
async def get_account_interactions(
    account_id: uuid.UUID = Path(..., description="The ID of the account to get interactions for"),
    skip: int = Query(0, ge=0, description="Number of interactions to skip"),
    limit: int = Query(100, ge=1, le=100, description="Maximum number of interactions to return"),
    interaction_type: Optional[InteractionType] = Query(
        None, description="Filter by interaction type"
    ),
    requires_followup: Optional[bool] = Query(None, description="Filter by requires_followup flag"),
    is_completed: Optional[bool] = Query(None, description="Filter by is_completed flag"),
    start_date: Optional[datetime] = Query(None, description="Filter by start date (inclusive)"),
    end_date: Optional[datetime] = Query(None, description="Filter by end date (inclusive)"),
    search: Optional[str] = Query(
        None, description="Search term for interaction subject or description"
    ),
    db: Annotated[AsyncSession, Depends(get_db)] = None,
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Get all interactions for a specific account.

    Requires tenant owner, manager, or staff role.
    """
    db_interactions = await interaction_service.get_interactions(
        db,
        tenant_id,
        account_id,
        None,
        skip,
        limit,
        interaction_type,
        requires_followup,
        is_completed,
        start_date,
        end_date,
        search,
    )
    return db_interactions


@router.put(
    "/{interaction_id}",
    response_model=InteractionRead,
    dependencies=[
        Depends(
            require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF], "header")
        )
    ],
)
async def update_interaction(
    interaction_in: InteractionUpdate,
    interaction_id: uuid.UUID = Path(..., description="The ID of the interaction to update"),
    db: Annotated[AsyncSession, Depends(get_db)] = None,
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Update a CRM interaction.

    Requires tenant owner, manager, or staff role.
    """
    db_interaction = await interaction_service.update_interaction(
        db, tenant_id, interaction_id, interaction_in
    )
    if not db_interaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Interaction with ID {interaction_id} not found",
        )
    return db_interaction


@router.delete(
    "/{interaction_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=[Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER], "header"))],
)
async def delete_interaction(
    interaction_id: uuid.UUID = Path(..., description="The ID of the interaction to delete"),
    db: Annotated[AsyncSession, Depends(get_db)] = None,
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Delete a CRM interaction.

    Requires tenant owner or manager role.
    """
    success = await interaction_service.delete_interaction(db, tenant_id, interaction_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Interaction with ID {interaction_id} not found",
        )
    return None
