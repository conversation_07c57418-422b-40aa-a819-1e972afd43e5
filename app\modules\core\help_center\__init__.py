"""
Help Center Module

Sistema completo de suporte ao cliente com tickets, mensagens em tempo real,
base de conhecimento e métricas para administradores.
"""

from .models import (
    Ticket,
    TicketMessage,
    KnowledgeBaseArticle,
    TicketStatus,
    TicketPriority,
    TicketCategory,
    MessageType
)

from .schemas import (
    TicketCreate,
    TicketUpdate,
    TicketResponse,
    TicketMessageCreate,
    TicketMessageResponse,
    KnowledgeBaseArticleCreate,
    KnowledgeBaseArticleResponse,
    HelpCenterMetricsResponse
)

__all__ = [
    # Models
    "Ticket",
    "TicketMessage", 
    "KnowledgeBaseArticle",
    "TicketStatus",
    "TicketPriority",
    "TicketCategory",
    "MessageType",
    
    # Schemas
    "TicketCreate",
    "TicketUpdate",
    "TicketResponse",
    "TicketMessageCreate",
    "TicketMessageResponse",
    "KnowledgeBaseArticleCreate",
    "KnowledgeBaseArticleResponse",
    "HelpCenterMetricsResponse"
]
