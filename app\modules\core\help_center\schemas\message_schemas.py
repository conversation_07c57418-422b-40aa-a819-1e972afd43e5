"""
Message Schemas

Schemas Pydantic para validação de dados de mensagens.
"""

from datetime import datetime
from typing import List, Optional
from uuid import UUID
from pydantic import BaseModel, Field
from enum import Enum


class MessageTypeEnum(str, Enum):
    TEXT = "text"
    IMAGE = "image"
    FILE = "file"
    SYSTEM = "system"


class TicketMessageCreate(BaseModel):
    ticket_id: Optional[UUID] = Field(None, description="ID do ticket (será definido pela URL)")
    message_content: str = Field(..., min_length=1, max_length=5000, description="Conteúdo da mensagem")
    message_type: MessageTypeEnum = Field(MessageTypeEnum.TEXT, description="Tipo da mensagem")
    file_path: Optional[str] = Field(None, description="Caminho do arquivo (se aplicável)")
    file_name: Optional[str] = Field(None, description="Nome do arquivo (se aplicável)")
    file_size: Optional[int] = Field(None, description="Tamanho do arquivo em bytes")
    mime_type: Optional[str] = Field(None, description="Tipo MIME do arquivo")


class TicketMessageResponse(BaseModel):
    id: UUID
    ticket_id: UUID
    sender_id: UUID
    message_content: str
    message_type: MessageTypeEnum
    file_path: Optional[str] = None
    file_name: Optional[str] = None
    file_size: Optional[int] = None
    mime_type: Optional[str] = None
    is_read: bool
    created_at: datetime
    expires_at: Optional[datetime] = None
    is_expired: bool
    
    # Campos adicionais para resposta
    sender_name: Optional[str] = None
    sender_email: Optional[str] = None
    is_admin: bool = False

    class Config:
        from_attributes = True


class TicketMessageListResponse(BaseModel):
    messages: List[TicketMessageResponse]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


class MessageMarkAsRead(BaseModel):
    message_ids: List[UUID] = Field(..., min_items=1, description="Lista de IDs das mensagens")


class MessageUpdate(BaseModel):
    message_content: Optional[str] = Field(None, min_length=1, max_length=5000)
    is_read: Optional[bool] = None


class MessageFileUpload(BaseModel):
    ticket_id: UUID
    file_name: str
    file_size: int
    mime_type: str
    file_path: str


class MessageReadReceipt(BaseModel):
    message_id: UUID
    ticket_id: UUID
    user_id: UUID
    read_at: datetime


class TypingIndicator(BaseModel):
    ticket_id: UUID
    user_id: UUID
    user_name: str
    is_typing: bool
