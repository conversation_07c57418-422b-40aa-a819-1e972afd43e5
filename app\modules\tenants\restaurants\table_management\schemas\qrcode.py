"""
Schemas for QR codes in the table management module.
"""

from typing import Optional, List
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, Field


class TableQRCodeBase(BaseModel):
    """Base schema for table QR code."""

    qrcode_enabled: bool = Field(True, description="Whether the QR code is enabled for this table")


class TableQRCodeCreate(TableQRCodeBase):
    """Schema for creating a new table QR code."""

    pass


class TableQRCodeUpdate(TableQRCodeBase):
    """Schema for updating a table QR code."""

    qrcode_enabled: Optional[bool] = Field(
        None, description="Whether the QR code is enabled for this table"
    )


class TableQRCodeRead(TableQRCodeBase):
    """Schema for reading a table QR code."""

    qrcode_id: str = Field(..., description="Unique identifier for the QR code")

    class Config:
        from_attributes = True


class UniversalQRCodeBase(BaseModel):
    """Base schema for universal QR code."""

    name: str = Field(..., description="Name of the QR code")
    description: Optional[str] = Field(None, description="Description of the QR code")
    requires_approval: bool = Field(
        True, description="Whether orders from this QR code require staff approval"
    )
    is_active: bool = Field(True, description="Whether the QR code is active")


class UniversalQRCodeCreate(UniversalQRCodeBase):
    """Schema for creating a new universal QR code."""

    pass


class UniversalQRCodeUpdate(BaseModel):
    """Schema for updating a universal QR code."""

    name: Optional[str] = Field(None, description="Name of the QR code")
    description: Optional[str] = Field(None, description="Description of the QR code")
    requires_approval: Optional[bool] = Field(
        None, description="Whether orders from this QR code require staff approval"
    )
    is_active: Optional[bool] = Field(None, description="Whether the QR code is active")


class UniversalQRCodeRead(UniversalQRCodeBase):
    """Schema for reading a universal QR code."""

    id: UUID = Field(..., description="Unique identifier for the QR code")
    qrcode_id: str = Field(..., description="Unique identifier for the QR code")
    created_at: datetime = Field(..., description="When the QR code was created")
    updated_at: datetime = Field(..., description="When the QR code was last updated")

    class Config:
        from_attributes = True


class QRCodeScanBase(BaseModel):
    """Base schema for QR code scan."""

    ip_address: Optional[str] = Field(None, description="IP address of the scanner")
    user_agent: Optional[str] = Field(None, description="User agent of the scanner")
    session_id: Optional[str] = Field(None, description="Session ID of the scanner")


class QRCodeScanCreate(QRCodeScanBase):
    """Schema for creating a new QR code scan."""

    table_id: Optional[UUID] = Field(None, description="ID of the table scanned")
    universal_qrcode_id: Optional[UUID] = Field(
        None, description="ID of the universal QR code scanned"
    )
    user_id: Optional[UUID] = Field(None, description="ID of the user who scanned")
    customer_id: Optional[UUID] = Field(None, description="ID of the customer who scanned")


class QRCodeScanUpdate(BaseModel):
    """Schema for updating a QR code scan."""

    resulted_in_order: Optional[bool] = Field(
        None, description="Whether the scan resulted in an order"
    )


class QRCodeScanRead(QRCodeScanBase):
    """Schema for reading a QR code scan."""

    id: UUID = Field(..., description="Unique identifier for the scan")
    table_id: Optional[UUID] = Field(None, description="ID of the table scanned")
    universal_qrcode_id: Optional[UUID] = Field(
        None, description="ID of the universal QR code scanned"
    )
    user_id: Optional[UUID] = Field(None, description="ID of the user who scanned")
    customer_id: Optional[UUID] = Field(None, description="ID of the customer who scanned")
    scan_time: datetime = Field(..., description="When the QR code was scanned")
    resulted_in_order: bool = Field(..., description="Whether the scan resulted in an order")

    class Config:
        from_attributes = True


class QRCodeScanStats(BaseModel):
    """Schema for QR code scan statistics."""

    total_scans: int = Field(..., description="Total number of scans")
    scans_with_orders: int = Field(..., description="Number of scans that resulted in orders")
    conversion_rate: float = Field(..., description="Percentage of scans that resulted in orders")
    scans_by_table: Optional[List[dict]] = Field(None, description="Scans grouped by table")
    scans_by_universal_qrcode: Optional[List[dict]] = Field(
        None, description="Scans grouped by universal QR code"
    )
    scans_by_day: Optional[List[dict]] = Field(None, description="Scans grouped by day")
    scans_by_hour: Optional[List[dict]] = Field(None, description="Scans grouped by hour")
