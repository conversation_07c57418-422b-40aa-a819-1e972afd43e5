"""Service for managing payment processors."""

import uuid  # noqa: E402
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update as sqlalchemy_update, and_

from app.modules.core.payments.models.payment_processor import PaymentProcessor  # noqa: E402
from app.modules.core.payments.schemas.payment_processor import (
    PaymentProcessorCreate,
    PaymentProcessorUpdate,
)


class PaymentProcessorService:
    """Service for managing payment processors."""

    async def get(
        self, db: AsyncSession, id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Optional[PaymentProcessor]:
        """
        Get a payment processor by ID, ensuring it belongs to the specified tenant.
        """
        result = await db.execute(
            select(PaymentProcessor).where(
                PaymentProcessor.id == id, PaymentProcessor.tenant_id == tenant_id
            )
        )
        return result.scalars().first()

    async def get_multi(
        self,
        db: AsyncSession,
        *,
        tenant_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        is_active: Optional[bool] = None
    ) -> List[PaymentProcessor]:
        """
        Get multiple payment processors with optional filtering.
        """
        query = select(PaymentProcessor).where(PaymentProcessor.tenant_id == tenant_id)

        if is_active is not None:
            query = query.where(PaymentProcessor.is_active == is_active)

        query = query.order_by(PaymentProcessor.name).offset(skip).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    async def create(
        self, db: AsyncSession, *, tenant_id: uuid.UUID, obj_in: PaymentProcessorCreate
    ) -> PaymentProcessor:
        """
        Create a new payment processor.

        If is_default is True, sets all other processors to is_default=False.
        """
        # Check if this is the default processor
        is_default = obj_in.is_default

        # If this is the default processor, unset default on all others
        if is_default:
            await self._unset_default_processors(db, tenant_id)

        # Create the processor
        db_obj = PaymentProcessor(tenant_id=tenant_id, **obj_in.model_dump())

        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)

        return db_obj

    async def update(
        self,
        db: AsyncSession,
        *,
        id: uuid.UUID,
        tenant_id: uuid.UUID,
        obj_in: PaymentProcessorUpdate
    ) -> Optional[PaymentProcessor]:
        """
        Update an existing payment processor.

        If is_default is True, sets all other processors to is_default=False.
        """
        processor = await self.get(db, id, tenant_id)

        if not processor:
            return None

        # Check if default status is changing
        update_data = obj_in.model_dump(exclude_unset=True)
        is_default = update_data.get("is_default")

        # If this is being set as the default processor, unset default on all others
        if is_default and not processor.is_default:
            await self._unset_default_processors(db, tenant_id)

        # Update the processor
        for field, value in update_data.items():
            setattr(processor, field, value)

        db.add(processor)
        await db.commit()
        await db.refresh(processor)

        return processor

    async def delete(self, db: AsyncSession, *, id: uuid.UUID, tenant_id: uuid.UUID) -> bool:
        """
        Delete a payment processor.

        Returns True if the processor was deleted, False if it was not found.
        """
        processor = await self.get(db, id, tenant_id)

        if not processor:
            return False

        await db.delete(processor)
        await db.commit()

        return True

    async def get_default_processor(
        self, db: AsyncSession, *, tenant_id: uuid.UUID
    ) -> Optional[PaymentProcessor]:
        """
        Get the default payment processor for a tenant.
        """
        result = await db.execute(
            select(PaymentProcessor).where(
                PaymentProcessor.tenant_id == tenant_id,
                PaymentProcessor.is_default,
                PaymentProcessor.is_active,
            )
        )
        return result.scalars().first()

    async def _unset_default_processors(self, db: AsyncSession, tenant_id: uuid.UUID) -> None:
        """
        Unset the default flag on all processors for a tenant.
        """
        await db.execute(
            sqlalchemy_update(PaymentProcessor)
            .where(and_(PaymentProcessor.tenant_id == tenant_id, PaymentProcessor.is_default))
            .values(is_default=False)
        )
        await db.commit()


# Instance of the service to be used in endpoints
payment_processor_service = PaymentProcessorService()
