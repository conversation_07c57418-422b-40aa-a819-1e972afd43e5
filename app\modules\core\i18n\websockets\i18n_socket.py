"""
WebSocket handlers for the i18n module.
"""

import logging
import json
from typing import Dict, Any, Optional

import socketio
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_async_session
from app.modules.core.i18n.services.language_service import LanguageService
from app.modules.core.i18n.services.translation_service import TranslationService

# Set up logger
logger = logging.getLogger(__name__)


async def handle_get_translations(
    sio: socketio.AsyncServer, sid: str, data: Dict[str, Any]
) -> None:
    """
    Handle WebSocket request to get translations for a specific language.

    Args:
        sio: The Socket.IO server instance
        sid: The session ID of the client
        data: The data sent by the client, should contain language_code
    """
    try:
        language_code = data.get("language_code")
        if not language_code:
            await sio.emit("translations_error", {"error": "Language code is required"}, room=sid)
            return

        # Get database session
        async with get_async_session() as db:
            # Get language by code
            language_service = LanguageService()
            language = await language_service.get_language_by_code(db, language_code)

            if not language:
                await sio.emit(
                    "translations_error",
                    {"error": f"Language with code '{language_code}' not found"},
                    room=sid,
                )
                return

            # Get translations for the language
            translation_service = TranslationService()
            translations = await translation_service.get_translations_by_language(db, language.id)

            # Convert translations to a dictionary format
            translations_dict = {t.key: t.text for t in translations}

            # Send translations to the client
            await sio.emit(
                "translations",
                {"language_code": language_code, "translations": translations_dict},
                room=sid,
            )

    except Exception as e:
        logger.error(f"Error handling get_translations: {e}")
        await sio.emit(
            "translations_error",
            {"error": "An error occurred while fetching translations"},
            room=sid,
        )


async def handle_check_version(sio: socketio.AsyncServer, sid: str, data: Dict[str, Any]) -> None:
    """
    Handle WebSocket request to check if a language version has changed.

    Args:
        sio: The Socket.IO server instance
        sid: The session ID of the client
        data: The data sent by the client, should contain language_code and version_code
    """
    try:
        language_code = data.get("language_code")
        client_version = data.get("version_code")

        if not language_code or not client_version:
            await sio.emit(
                "version_check_error",
                {"error": "Language code and version code are required"},
                room=sid,
            )
            return

        # Get database session
        async with get_async_session() as db:
            # Check version
            language_service = LanguageService()
            result = await language_service.check_version(db, language_code, client_version)

            # Send result to the client
            await sio.emit("version_check_result", result, room=sid)

    except Exception as e:
        logger.error(f"Error handling check_version: {e}")
        await sio.emit(
            "version_check_error", {"error": "An error occurred while checking version"}, room=sid
        )


async def handle_get_changes(sio: socketio.AsyncServer, sid: str, data: Dict[str, Any]) -> None:
    """
    Handle WebSocket request to get translation changes since a specific version.

    Args:
        sio: The Socket.IO server instance
        sid: The session ID of the client
        data: The data sent by the client, should contain language_code, since_version, and optional sector
    """
    try:
        language_code = data.get("language_code")
        since_version = data.get("since_version")
        sector = data.get("sector")  # Optional

        if not language_code or not since_version:
            await sio.emit(
                "translation_changes_error",
                {"error": "Language code and since_version are required"},
                room=sid,
            )
            return

        # Get database session
        async with get_async_session() as db:
            # Get changes
            from app.modules.core.i18n.services.translation_change_service import (
                TranslationChangeService,
            )

            change_service = TranslationChangeService()

            try:
                result = await change_service.get_changes_since_version(
                    db, language_code, since_version, sector
                )

                # Send result to the client
                await sio.emit("translation_changes", result, room=sid)
            except ValueError as e:
                await sio.emit("translation_changes_error", {"error": str(e)}, room=sid)

    except Exception as e:
        logger.error(f"Error handling get_changes: {e}")
        await sio.emit(
            "translation_changes_error",
            {"error": "An error occurred while getting translation changes"},
            room=sid,
        )


def register_i18n_handlers(sio: socketio.AsyncServer) -> None:
    """
    Register all i18n WebSocket event handlers.

    Args:
        sio: The Socket.IO server instance
    """

    @sio.on("get_translations")
    async def _handle_get_translations(sid: str, data: Dict[str, Any]) -> None:
        await handle_get_translations(sio, sid, data)

    @sio.on("check_version")
    async def _handle_check_version(sid: str, data: Dict[str, Any]) -> None:
        await handle_check_version(sio, sid, data)

    @sio.on("get_translation_changes")
    async def _handle_get_changes(sid: str, data: Dict[str, Any]) -> None:
        await handle_get_changes(sio, sid, data)

    logger.info("Registered i18n WebSocket handlers")
