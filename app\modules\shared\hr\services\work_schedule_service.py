from typing import List, Optional, <PERSON><PERSON>
import uuid
from datetime import datetime, date, timedelta
from sqlalchemy import select, delete, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from app.modules.shared.hr.core.models.work_schedule import (
    ScheduleTemplate,
    ScheduleTemplateEntry,
    WorkSchedule,
    WorkShift,
    LeaveRequest,
    LeaveRequestStatus,
    LeaveType,
    Day,
)
from app.modules.shared.hr.core.models.employee import Employee  # noqa: E402
from app.modules.shared.hr.core.schemas.work_schedule import (
    ScheduleTemplateCreate,
    ScheduleTemplateUpdate,
    ScheduleTemplateEntryCreate,
    ScheduleTemplateEntryUpdate,
    WorkScheduleCreate,
    WorkScheduleUpdate,
    WorkShiftCreate,
    WorkShiftUpdate,
    LeaveRequestCreate,
    LeaveRequestUpdate,
    GenerateScheduleRequest,
    LeaveBalanceResponse,
)
from app.core.exceptions import NotFoundError, ValidationError  # noqa: E402


class WorkScheduleService:
    """Service for work schedule operations."""

    # Schedule Template Methods
    @staticmethod
    async def create_schedule_template(
        db: AsyncSession, tenant_id: uuid.UUID, template_data: ScheduleTemplateCreate
    ) -> ScheduleTemplate:
        """Create a new schedule template."""
        # Extract entries from the data
        entries_data = template_data.entries
        template_data_dict = template_data.model_dump(exclude={"entries"})

        # Create template
        template = ScheduleTemplate(tenant_id=tenant_id, **template_data_dict)

        db.add(template)
        await db.commit()
        await db.refresh(template)

        # Create entries if provided
        if entries_data:
            for entry_data in entries_data:
                entry = ScheduleTemplateEntry(
                    tenant_id=tenant_id,
                    template_id=template.id,
                    **entry_data.model_dump(),
                )
                db.add(entry)

            await db.commit()
            await db.refresh(template)

        return template

    @staticmethod
    async def get_schedule_template(
        db: AsyncSession, tenant_id: uuid.UUID, template_id: uuid.UUID
    ) -> ScheduleTemplate:
        """Get a schedule template by ID."""
        stmt = select(ScheduleTemplate).where(
            ScheduleTemplate.id == template_id, ScheduleTemplate.tenant_id == tenant_id
        )
        result = await db.execute(stmt)
        template = result.scalars().first()

        if not template:
            raise NotFoundError(f"Schedule template with id {template_id} not found")

        return template

    @staticmethod
    async def get_schedule_template_with_entries(
        db: AsyncSession, tenant_id: uuid.UUID, template_id: uuid.UUID
    ) -> Tuple[ScheduleTemplate, List[ScheduleTemplateEntry]]:
        """Get a schedule template with its entries."""
        # Get template
        template = await WorkScheduleService.get_schedule_template(db, tenant_id, template_id)

        # Get entries
        stmt = (
            select(ScheduleTemplateEntry)
            .where(
                ScheduleTemplateEntry.template_id == template_id,
                ScheduleTemplateEntry.tenant_id == tenant_id,
            )
            .order_by(ScheduleTemplateEntry.day_of_week)
        )

        result = await db.execute(stmt)
        entries = result.scalars().all()

        return template, entries

    @staticmethod
    async def get_schedule_templates(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        is_active: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[ScheduleTemplate]:
        """Get all schedule templates with optional filtering."""
        query = select(ScheduleTemplate).where(ScheduleTemplate.tenant_id == tenant_id)

        # Apply filters if provided
        if is_active is not None:
            query = query.where(ScheduleTemplate.is_active == is_active)

        # Apply pagination
        query = query.offset(skip).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def update_schedule_template(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        template_id: uuid.UUID,
        template_data: ScheduleTemplateUpdate,
    ) -> ScheduleTemplate:
        """Update a schedule template."""
        # Get the template first to ensure it exists
        template = await WorkScheduleService.get_schedule_template(db, tenant_id, template_id)

        # Update attributes
        update_data = template_data.model_dump(exclude_unset=True)

        # Update the fields
        for key, value in update_data.items():
            setattr(template, key, value)

        await db.commit()
        await db.refresh(template)
        return template

    @staticmethod
    async def delete_schedule_template(
        db: AsyncSession, tenant_id: uuid.UUID, template_id: uuid.UUID
    ) -> bool:
        """Delete a schedule template."""
        stmt = (
            delete(ScheduleTemplate)
            .where(
                ScheduleTemplate.id == template_id,
                ScheduleTemplate.tenant_id == tenant_id,
            )
            .returning(ScheduleTemplate.id)
        )

        result = await db.execute(stmt)
        deleted_id = result.scalars().first()

        if not deleted_id:
            raise NotFoundError(f"Schedule template with id {template_id} not found")

        await db.commit()
        return True

    @staticmethod
    async def add_template_entry(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        template_id: uuid.UUID,
        entry_data: ScheduleTemplateEntryCreate,
    ) -> ScheduleTemplateEntry:
        """Add an entry to a schedule template."""
        # Verify that the template exists
        await WorkScheduleService.get_schedule_template(db, tenant_id, template_id)

        # Create entry
        entry = ScheduleTemplateEntry(
            tenant_id=tenant_id, template_id=template_id, **entry_data.model_dump()
        )

        db.add(entry)
        await db.commit()
        await db.refresh(entry)
        return entry

    @staticmethod
    async def update_template_entry(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        entry_id: uuid.UUID,
        entry_data: ScheduleTemplateEntryUpdate,
    ) -> ScheduleTemplateEntry:
        """Update a schedule template entry."""
        # Get the entry first
        stmt = select(ScheduleTemplateEntry).where(
            ScheduleTemplateEntry.id == entry_id,
            ScheduleTemplateEntry.tenant_id == tenant_id,
        )
        result = await db.execute(stmt)
        entry = result.scalars().first()

        if not entry:
            raise NotFoundError(f"Schedule template entry with id {entry_id} not found")

        # Update attributes
        update_data = entry_data.model_dump(exclude_unset=True)

        # Update the fields
        for key, value in update_data.items():
            setattr(entry, key, value)

        await db.commit()
        await db.refresh(entry)
        return entry

    @staticmethod
    async def delete_template_entry(
        db: AsyncSession, tenant_id: uuid.UUID, entry_id: uuid.UUID
    ) -> bool:
        """Delete a schedule template entry."""
        stmt = (
            delete(ScheduleTemplateEntry)
            .where(
                ScheduleTemplateEntry.id == entry_id,
                ScheduleTemplateEntry.tenant_id == tenant_id,
            )
            .returning(ScheduleTemplateEntry.id)
        )

        result = await db.execute(stmt)
        deleted_id = result.scalars().first()

        if not deleted_id:
            raise NotFoundError(f"Schedule template entry with id {entry_id} not found")

        await db.commit()
        return True

    # Work Schedule Methods
    @staticmethod
    async def create_work_schedule(
        db: AsyncSession, tenant_id: uuid.UUID, schedule_data: WorkScheduleCreate
    ) -> WorkSchedule:
        """Create a new work schedule."""
        # Verify that the employee exists
        stmt = select(Employee).where(
            Employee.id == schedule_data.employee_id, Employee.tenant_id == tenant_id
        )
        result = await db.execute(stmt)
        employee = result.scalars().first()

        if not employee:
            raise NotFoundError(f"Employee with id {schedule_data.employee_id} not found")

        # Verify that the template exists if provided
        if schedule_data.template_id:
            await WorkScheduleService.get_schedule_template(
                db, tenant_id, schedule_data.template_id
            )

        # Extract shifts from the data
        shifts_data = schedule_data.shifts
        schedule_data_dict = schedule_data.model_dump(exclude={"shifts"})

        # Create schedule
        schedule = WorkSchedule(tenant_id=tenant_id, **schedule_data_dict)

        db.add(schedule)
        await db.commit()
        await db.refresh(schedule)

        # Create shifts if provided
        if shifts_data:
            for shift_data in shifts_data:
                shift = WorkShift(
                    tenant_id=tenant_id,
                    schedule_id=schedule.id,
                    **shift_data.model_dump(),
                )
                db.add(shift)

            await db.commit()
            await db.refresh(schedule)

        return schedule

    @staticmethod
    async def get_work_schedule(
        db: AsyncSession, tenant_id: uuid.UUID, schedule_id: uuid.UUID
    ) -> WorkSchedule:
        """Get a work schedule by ID."""
        stmt = select(WorkSchedule).where(
            WorkSchedule.id == schedule_id, WorkSchedule.tenant_id == tenant_id
        )
        result = await db.execute(stmt)
        schedule = result.scalars().first()

        if not schedule:
            raise NotFoundError(f"Work schedule with id {schedule_id} not found")

        return schedule

    @staticmethod
    async def get_work_schedule_with_shifts(
        db: AsyncSession, tenant_id: uuid.UUID, schedule_id: uuid.UUID
    ) -> Tuple[WorkSchedule, List[WorkShift]]:
        """Get a work schedule with its shifts."""
        # Get schedule
        schedule = await WorkScheduleService.get_work_schedule(db, tenant_id, schedule_id)

        # Get shifts
        stmt = (
            select(WorkShift)
            .where(WorkShift.schedule_id == schedule_id, WorkShift.tenant_id == tenant_id)
            .order_by(WorkShift.date, WorkShift.start_time)
        )

        result = await db.execute(stmt)
        shifts = result.scalars().all()

        return schedule, shifts

    @staticmethod
    async def get_employee_schedules(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        employee_id: uuid.UUID,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        is_active: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[WorkSchedule]:
        """Get all work schedules for an employee with optional filtering."""
        query = select(WorkSchedule).where(
            WorkSchedule.tenant_id == tenant_id, WorkSchedule.employee_id == employee_id
        )

        # Apply filters if provided
        if is_active is not None:
            query = query.where(WorkSchedule.is_active == is_active)

        if start_date:
            # Schedules that end on or after start_date or have no end date
            query = query.where(
                or_(WorkSchedule.end_date >= start_date, WorkSchedule.end_date is None)
            )

        if end_date:
            # Schedules that start on or before end_date
            query = query.where(WorkSchedule.start_date <= end_date)

        # Apply pagination
        query = query.order_by(WorkSchedule.start_date.desc()).offset(skip).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def update_work_schedule(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        schedule_id: uuid.UUID,
        schedule_data: WorkScheduleUpdate,
    ) -> WorkSchedule:
        """Update a work schedule."""
        # Get the schedule first to ensure it exists
        schedule = await WorkScheduleService.get_work_schedule(db, tenant_id, schedule_id)

        # Update attributes
        update_data = schedule_data.model_dump(exclude_unset=True)

        # Update the fields
        for key, value in update_data.items():
            setattr(schedule, key, value)

        await db.commit()
        await db.refresh(schedule)
        return schedule

    @staticmethod
    async def delete_work_schedule(
        db: AsyncSession, tenant_id: uuid.UUID, schedule_id: uuid.UUID
    ) -> bool:
        """Delete a work schedule."""
        stmt = (
            delete(WorkSchedule)
            .where(WorkSchedule.id == schedule_id, WorkSchedule.tenant_id == tenant_id)
            .returning(WorkSchedule.id)
        )

        result = await db.execute(stmt)
        deleted_id = result.scalars().first()

        if not deleted_id:
            raise NotFoundError(f"Work schedule with id {schedule_id} not found")

        await db.commit()
        return True

    @staticmethod
    async def add_work_shift(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        schedule_id: uuid.UUID,
        shift_data: WorkShiftCreate,
    ) -> WorkShift:
        """Add a shift to a work schedule."""
        # Verify that the schedule exists
        await WorkScheduleService.get_work_schedule(db, tenant_id, schedule_id)

        # Create shift
        shift = WorkShift(tenant_id=tenant_id, schedule_id=schedule_id, **shift_data.model_dump())

        db.add(shift)
        await db.commit()
        await db.refresh(shift)
        return shift

    @staticmethod
    async def update_work_shift(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        shift_id: uuid.UUID,
        shift_data: WorkShiftUpdate,
    ) -> WorkShift:
        """Update a work shift."""
        # Get the shift first
        stmt = select(WorkShift).where(WorkShift.id == shift_id, WorkShift.tenant_id == tenant_id)
        result = await db.execute(stmt)
        shift = result.scalars().first()

        if not shift:
            raise NotFoundError(f"Work shift with id {shift_id} not found")

        # Update attributes
        update_data = shift_data.model_dump(exclude_unset=True)

        # Update the fields
        for key, value in update_data.items():
            setattr(shift, key, value)

        # Mark as modified if it's coming from a template
        shift.is_modified = True

        await db.commit()
        await db.refresh(shift)
        return shift

    @staticmethod
    async def delete_work_shift(
        db: AsyncSession, tenant_id: uuid.UUID, shift_id: uuid.UUID
    ) -> bool:
        """Delete a work shift."""
        stmt = (
            delete(WorkShift)
            .where(WorkShift.id == shift_id, WorkShift.tenant_id == tenant_id)
            .returning(WorkShift.id)
        )

        result = await db.execute(stmt)
        deleted_id = result.scalars().first()

        if not deleted_id:
            raise NotFoundError(f"Work shift with id {shift_id} not found")

        await db.commit()
        return True

    @staticmethod
    async def generate_schedule_from_template(
        db: AsyncSession, tenant_id: uuid.UUID, request_data: GenerateScheduleRequest
    ) -> WorkSchedule:
        """Generate a work schedule from a template."""
        # Verify that the employee exists
        stmt = select(Employee).where(
            Employee.id == request_data.employee_id, Employee.tenant_id == tenant_id
        )
        result = await db.execute(stmt)
        employee = result.scalars().first()

        if not employee:
            raise NotFoundError(f"Employee with id {request_data.employee_id} not found")

        # Get the template with entries
        template, entries = await WorkScheduleService.get_schedule_template_with_entries(
            db, tenant_id, request_data.template_id
        )

        if not entries:
            raise ValidationError("Template has no entries")

        # Create the schedule
        schedule = WorkSchedule(
            tenant_id=tenant_id,
            employee_id=request_data.employee_id,
            template_id=request_data.template_id,
            start_date=request_data.start_date,
            end_date=request_data.end_date,
            name=template.name,
        )

        db.add(schedule)
        await db.commit()
        await db.refresh(schedule)

        # Generate shifts based on the template
        current_date = request_data.start_date
        end_date = request_data.end_date or (current_date + timedelta(days=30))

        exclude_dates = request_data.exclude_dates or []

        while current_date <= end_date:
            # Get day of week
            day_of_week = Day(current_date.strftime("%A").lower())

            # Skip excluded dates
            if current_date in exclude_dates:
                current_date += timedelta(days=1)
                continue

            # Find entry for this day
            day_entry = next((e for e in entries if e.day_of_week == day_of_week), None)

            if day_entry and not day_entry.is_day_off:
                # Create shift for this day
                shift = WorkShift(
                    tenant_id=tenant_id,
                    schedule_id=schedule.id,
                    date=current_date,
                    start_time=day_entry.start_time,
                    end_time=day_entry.end_time,
                    break_duration_minutes=day_entry.break_duration_minutes,
                )

                db.add(shift)

            current_date += timedelta(days=1)

        await db.commit()
        await db.refresh(schedule)

        return schedule

    # Leave Request Methods
    @staticmethod
    async def create_leave_request(
        db: AsyncSession, tenant_id: uuid.UUID, request_data: LeaveRequestCreate
    ) -> LeaveRequest:
        """Create a new leave request."""
        # Verify that the employee exists
        stmt = select(Employee).where(
            Employee.id == request_data.employee_id, Employee.tenant_id == tenant_id
        )
        result = await db.execute(stmt)
        employee = result.scalars().first()

        if not employee:
            raise NotFoundError(f"Employee with id {request_data.employee_id} not found")

        # Create leave request
        leave_request = LeaveRequest(
            tenant_id=tenant_id,
            **request_data.model_dump(),
            request_date=datetime.now(),
            status=LeaveRequestStatus.PENDING,
        )

        db.add(leave_request)
        await db.commit()
        await db.refresh(leave_request)
        return leave_request

    @staticmethod
    async def get_leave_request(
        db: AsyncSession, tenant_id: uuid.UUID, request_id: uuid.UUID
    ) -> LeaveRequest:
        """Get a leave request by ID."""
        stmt = select(LeaveRequest).where(
            LeaveRequest.id == request_id, LeaveRequest.tenant_id == tenant_id
        )
        result = await db.execute(stmt)
        leave_request = result.scalars().first()

        if not leave_request:
            raise NotFoundError(f"Leave request with id {request_id} not found")

        return leave_request

    @staticmethod
    async def get_employee_leave_requests(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        employee_id: uuid.UUID,
        status: Optional[LeaveRequestStatus] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[LeaveRequest]:
        """Get all leave requests for an employee with optional filtering."""
        query = select(LeaveRequest).where(
            LeaveRequest.tenant_id == tenant_id, LeaveRequest.employee_id == employee_id
        )

        # Apply filters if provided
        if status:
            query = query.where(LeaveRequest.status == status)

        if start_date:
            # Requests that end on or after start_date
            query = query.where(LeaveRequest.end_date >= start_date)

        if end_date:
            # Requests that start on or before end_date
            query = query.where(LeaveRequest.start_date <= end_date)

        # Apply pagination
        query = query.order_by(LeaveRequest.start_date.desc()).offset(skip).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def update_leave_request(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        request_id: uuid.UUID,
        request_data: LeaveRequestUpdate,
    ) -> LeaveRequest:
        """Update a leave request."""
        # Get the request first to ensure it exists
        leave_request = await WorkScheduleService.get_leave_request(db, tenant_id, request_id)

        # Update attributes
        update_data = request_data.model_dump(exclude_unset=True)

        # Set review date when status changes
        if "status" in update_data and update_data["status"] != leave_request.status:
            update_data["review_date"] = datetime.now()

        # Update the fields
        for key, value in update_data.items():
            setattr(leave_request, key, value)

        await db.commit()
        await db.refresh(leave_request)
        return leave_request

    @staticmethod
    async def delete_leave_request(
        db: AsyncSession, tenant_id: uuid.UUID, request_id: uuid.UUID
    ) -> bool:
        """Delete a leave request."""
        stmt = (
            delete(LeaveRequest)
            .where(LeaveRequest.id == request_id, LeaveRequest.tenant_id == tenant_id)
            .returning(LeaveRequest.id)
        )

        result = await db.execute(stmt)
        deleted_id = result.scalars().first()

        if not deleted_id:
            raise NotFoundError(f"Leave request with id {request_id} not found")

        await db.commit()
        return True

    @staticmethod
    async def get_employee_leave_balance(
        db: AsyncSession, tenant_id: uuid.UUID, employee_id: uuid.UUID
    ) -> LeaveBalanceResponse:
        """Get an employee's leave balance."""
        # Get the employee
        from app.models.tenant_user_association import TenantUserAssociation  # noqa: E402

        stmt = (
            select(Employee)
            .options(joinedload(Employee.user_association).joinedload(TenantUserAssociation.user))
            .where(Employee.id == employee_id, Employee.tenant_id == tenant_id)
        )
        result = await db.execute(stmt)
        employee = result.scalars().first()

        if not employee:
            raise NotFoundError(f"Employee with id {employee_id} not found")

        # Get the employee name
        user = employee.user_association.user
        employee_name = f"{user.first_name} {user.last_name}"

        # In a real system, this would come from some allocation logic or separate table
        # For this example, we just use hardcoded values and approved leave requests
        vacation_days_allocated = 20.0
        sick_days_allocated = 10.0
        personal_days_allocated = 5.0

        # Calculate used leave
        current_year = datetime.now().year
        year_start = date(current_year, 1, 1)
        year_end = date(current_year, 12, 31)

        stmt = select(LeaveRequest).where(
            LeaveRequest.employee_id == employee_id,
            LeaveRequest.tenant_id == tenant_id,
            LeaveRequest.status == LeaveRequestStatus.APPROVED,
            LeaveRequest.start_date >= year_start,
            LeaveRequest.end_date <= year_end,
        )

        result = await db.execute(stmt)
        approved_leaves = result.scalars().all()

        # Count used days by leave type
        used_leaves = {
            LeaveType.VACATION.value: 0.0,
            LeaveType.SICK.value: 0.0,
            LeaveType.PERSONAL.value: 0.0,
            LeaveType.BEREAVEMENT.value: 0.0,
            LeaveType.MATERNITY.value: 0.0,
            LeaveType.PATERNITY.value: 0.0,
            LeaveType.UNPAID.value: 0.0,
            LeaveType.OTHER.value: 0.0,
        }

        for leave in approved_leaves:
            used_leaves[leave.leave_type.value] += leave.duration_days

        # Calculate available days
        vacation_days_available = vacation_days_allocated - used_leaves[LeaveType.VACATION.value]
        sick_days_available = sick_days_allocated - used_leaves[LeaveType.SICK.value]
        personal_days_available = personal_days_allocated - used_leaves[LeaveType.PERSONAL.value]

        # Available in each category
        leave_balances = {
            LeaveType.VACATION.value: vacation_days_available,
            LeaveType.SICK.value: sick_days_available,
            LeaveType.PERSONAL.value: personal_days_available,
            LeaveType.BEREAVEMENT.value: 3.0,  # Typically fixed
            LeaveType.MATERNITY.value: 0.0,  # Would depend on policy
            LeaveType.PATERNITY.value: 0.0,  # Would depend on policy
            LeaveType.UNPAID.value: 30.0,  # Example limit
            LeaveType.OTHER.value: 0.0,
        }

        return LeaveBalanceResponse(
            employee_id=employee_id,
            employee_name=employee_name,
            vacation_days_available=vacation_days_available,
            sick_days_available=sick_days_available,
            personal_days_available=personal_days_available,
            leave_balances=leave_balances,
        )


# Create a singleton instance
work_schedule_service = WorkScheduleService()
