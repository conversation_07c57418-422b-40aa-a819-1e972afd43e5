"""
Reviews Schemas

Schemas Pydantic para o módulo de reviews.
"""

# Review schemas
from .review_schemas import (
    ReviewBase,
    ReviewCreate,
    ReviewUpdate,
    ReviewResponse,
    ReviewListResponse,
    ReviewStatsResponse,
    ReviewFilterParams,
    ReviewModerationRequest,
)

# Review moderation schemas
from .review_moderation_schemas import (
    ReviewReportCreate,
    ReviewReportResponse,
    ReviewHelpfulnessCreate,
    ReviewHelpfulnessResponse,
    ModerationActionCreate,
    ModerationActionResponse,
    ReportModerationRequest,
    ModerationQueueResponse,
    ModerationStatsResponse,
)

# Review metrics schemas
from .review_metrics_schemas import (
    ProductReviewMetricsResponse,
    ReviewerMetricsResponse,
    ReviewDistributionResponse,
    ReviewTrendsResponse,
    ReviewComparisonResponse,
    ReviewAnalyticsResponse
)

__all__ = [
    # Review schemas
    "ReviewBase",
    "ReviewCreate",
    "ReviewUpdate",
    "ReviewResponse",
    "ReviewListResponse",
    "ReviewStatsResponse",
    "ReviewFilterParams",
    "ReviewModerationRequest",

    # Review moderation schemas
    "ReviewReportCreate",
    "ReviewReportResponse",
    "ReviewHelpfulnessCreate",
    "ReviewHelpfulnessResponse",
    "ModerationActionCreate",
    "ModerationActionResponse",
    "ReportModerationRequest",
    "ModerationQueueResponse",
    "ModerationStatsResponse",

    # Review metrics schemas
    "ProductReviewMetricsResponse",
    "ReviewerMetricsResponse",
    "ReviewDistributionResponse",
    "ReviewTrendsResponse",
    "ReviewComparisonResponse",
    "ReviewAnalyticsResponse"
]