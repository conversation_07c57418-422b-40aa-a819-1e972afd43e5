# Core - Auth

**Categoria:** Core
**<PERSON><PERSON><PERSON><PERSON>:** Auth
**Total de Endpoints:** 11
**<PERSON><PERSON><PERSON> em:** 27/06/2025, 20:20:26

## 📋 Endpoints

- [POST /api/auth/change-password](#post-apiauthchange-password) - Change Password
- [POST /api/auth/forgot-password](#post-apiauthforgot-password) - Forgot Password
- [POST /api/auth/login](#post-apiauthlogin) - Login Access Token
- [GET /api/auth/me](#get-apiauthme) - Get Current User Info
- [POST /api/auth/refresh-token](#post-apiauthrefresh-token) - Refresh Token
- [POST /api/auth/register](#post-apiauthregister) - Register User
- [POST /api/auth/reset-password](#post-apiauthreset-password) - Reset Password
- [GET /api/auth/tenants](#get-apiauthtenants) - Get User Tenants
- [POST /api/auth/test-auth](#post-apiauthtest-auth) - Test Auth
- [POST /api/auth/test-db](#post-apiauthtest-db) - Test Db
- [POST /api/auth/test-simple](#post-apiauthtest-simple) - Test Simple

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### Body_change_password_api_auth_change_password_post

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `current_password` | string | ✅ | - |
| `new_password` | string | ✅ | - |

### Body_forgot_password_api_auth_forgot_password_post

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `email` | string | ✅ | - |

### Body_login_access_token_api_auth_login_post

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `grant_type` | unknown | ❌ | - |
| `username` | string | ✅ | - |
| `password` | string | ✅ | - |
| `scope` | string | ❌ | - |
| `client_id` | unknown | ❌ | - |
| `client_secret` | unknown | ❌ | - |

### Body_refresh_token_api_auth_refresh_token_post

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `refresh_token` | string | ✅ | - |

### Body_reset_password_api_auth_reset_password_post

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `token` | string | ✅ | - |
| `new_password` | string | ✅ | - |

### Body_test_auth_api_auth_test_auth_post

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `email` | string | ✅ | - |
| `password` | string | ✅ | - |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### Token

**Descrição:** Schema for the token response returned by the login endpoint.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `access_token` | string | ✅ | - |
| `refresh_token` | string | ✅ | - |
| `token_type` | string | ✅ | - |

### User

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `email` | string | ✅ | - |
| `full_name` | unknown | ❌ | - |
| `phone_number` | unknown | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `system_role` | string | ❌ | - |
| `data_sharing_consent` | boolean | ❌ | - |
| `id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### UserCreate

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `email` | string | ✅ | - |
| `full_name` | unknown | ❌ | - |
| `phone_number` | unknown | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `system_role` | string | ❌ | - |
| `data_sharing_consent` | boolean | ❌ | - |
| `password` | string | ✅ | - |

## 🔗 Endpoints Detalhados

### POST /api/auth/change-password {#post-apiauthchange-password}

**Resumo:** Change Password
**Descrição:** Change user password.
Requires authentication and current password verification.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/x-www-form-urlencoded`
**Schema:** [Body_change_password_api_auth_change_password_post](#body_change_password_api_auth_change_password_post)

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/auth/change-password" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/auth/forgot-password {#post-apiauthforgot-password}

**Resumo:** Forgot Password
**Descrição:** Request password reset.
Sends reset instructions to email (placeholder implementation).

**🔐 Autenticação:** Não requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/x-www-form-urlencoded`
**Schema:** [Body_forgot_password_api_auth_forgot_password_post](#body_forgot_password_api_auth_forgot_password_post)

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/auth/forgot-password" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d '{"example": "data"}'
```

---

### POST /api/auth/login {#post-apiauthlogin}

**Resumo:** Login Access Token
**Descrição:** Login endpoint, get an access token for future requests.

**🔐 Autenticação:** Não requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/x-www-form-urlencoded`
**Schema:** [Body_login_access_token_api_auth_login_post](#body_login_access_token_api_auth_login_post)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [Token](#token)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d '{"example": "data"}'
```

---

### GET /api/auth/me {#get-apiauthme}

**Resumo:** Get Current User Info
**Descrição:** Get current user information.
Requires authentication.

**🔐 Autenticação:** Requerida

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [User](#user)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/auth/me" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/auth/refresh-token {#post-apiauthrefresh-token}

**Resumo:** Refresh Token
**Descrição:** Get a new access token using a refresh token.

**🔐 Autenticação:** Não requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/x-www-form-urlencoded`
**Schema:** [Body_refresh_token_api_auth_refresh_token_post](#body_refresh_token_api_auth_refresh_token_post)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [Token](#token)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/auth/refresh-token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d '{"example": "data"}'
```

---

### POST /api/auth/register {#post-apiauthregister}

**Resumo:** Register User
**Descrição:** Register a new user account.
Public endpoint - no authentication required.

**🔐 Autenticação:** Não requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [UserCreate](#usercreate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [Token](#token)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/auth/register" \
  -H "Content-Type: application/json" \
  -d '{"example": "data"}'
```

---

### POST /api/auth/reset-password {#post-apiauthreset-password}

**Resumo:** Reset Password
**Descrição:** Reset password using token.
Placeholder implementation - requires token validation system.

**🔐 Autenticação:** Não requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/x-www-form-urlencoded`
**Schema:** [Body_reset_password_api_auth_reset_password_post](#body_reset_password_api_auth_reset_password_post)

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/auth/reset-password" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d '{"example": "data"}'
```

---

### GET /api/auth/tenants {#get-apiauthtenants}

**Resumo:** Get User Tenants
**Descrição:** Get user's associated tenants.
Requires authentication.

**🔐 Autenticação:** Requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/auth/tenants" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/auth/test-auth {#post-apiauthtest-auth}

**Resumo:** Test Auth
**Descrição:** Test authentication step by step.

**🔐 Autenticação:** Não requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/x-www-form-urlencoded`
**Schema:** [Body_test_auth_api_auth_test_auth_post](#body_test_auth_api_auth_test_auth_post)

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/auth/test-auth" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d '{"example": "data"}'
```

---

### POST /api/auth/test-db {#post-apiauthtest-db}

**Resumo:** Test Db
**Descrição:** Test endpoint with database access.

**🔐 Autenticação:** Não requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/auth/test-db"
```

---

### POST /api/auth/test-simple {#post-apiauthtest-simple}

**Resumo:** Test Simple
**Descrição:** Simple test endpoint without database access.

**🔐 Autenticação:** Não requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/auth/test-simple"
```

---
