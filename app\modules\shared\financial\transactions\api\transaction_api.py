"""API endpoints for managing financial transactions."""

from typing import List, Optional, TYPE_CHECKING
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.auth_dependencies import get_current_active_user
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    get_current_tenant_from_header,
    require_tenant_role,
)
from app.modules.core.roles.models.roles import TenantRole

if TYPE_CHECKING:
    from app.modules.core.users.models.user import User
from app.modules.shared.financial.transactions.services.transaction_service import FinancialTransactionService
from app.modules.shared.financial.transactions.schemas.transaction import (
    FinancialTransactionCreate,
    FinancialTransactionUpdate,
    FinancialTransactionInDB,
)
from app.modules.shared.shopping_list.services.financial_integration_service import (
    financial_integration_service
)
from pydantic import BaseModel, Field
from decimal import Decimal
from datetime import date

router = APIRouter()


# === Schemas for Invoice Payment ===

class InvoicePaymentCreate(BaseModel):
    """Schema for registering payment of a supplier invoice."""
    payment_method_id: UUID = Field(..., description="Payment method used")
    payment_date: date = Field(..., description="Date of payment")
    payment_amount: Decimal = Field(..., gt=0, description="Amount paid")
    payment_reference: Optional[str] = Field(None, max_length=100, description="Payment reference")
    payment_receipt_file: Optional[str] = Field(None, description="Receipt file path")
    notes: Optional[str] = Field(None, description="Additional payment notes")


class InvoicePaymentRead(BaseModel):
    """Schema for reading invoice payment information."""
    id: UUID
    tenant_id: UUID
    amount: Decimal
    description: str
    transaction_date: date
    reference_number: str
    payment_method_id: Optional[UUID] = None
    notes: Optional[str] = None
    is_paid: bool = Field(default=False, description="Whether invoice is paid")

    model_config = {"from_attributes": True}


def get_transaction_service(
    db: AsyncSession = Depends(get_db),
) -> FinancialTransactionService:
    return FinancialTransactionService(db)


@router.post("/", response_model=FinancialTransactionInDB, status_code=status.HTTP_201_CREATED)
async def create_transaction(
    transaction_data: FinancialTransactionCreate,
    service: FinancialTransactionService = Depends(get_transaction_service),
    current_user: "User" = Depends(get_current_active_user),
    tenant_id: UUID = Depends(get_current_tenant_from_header),
):
    """Create a new financial transaction."""
    return await service.create_transaction(
        transaction_data=transaction_data,
        tenant_id=tenant_id,
        user_id=current_user.id,
    )


@router.get("/", response_model=List[FinancialTransactionInDB])
async def get_all_transactions(
    service: FinancialTransactionService = Depends(get_transaction_service),
    tenant_id: UUID = Depends(get_current_tenant_from_header),
    skip: int = 0,
    limit: int = 100,
):
    """Retrieve all transactions for the tenant."""
    return await service.get_all_transactions(
        tenant_id=tenant_id, skip=skip, limit=limit
    )


@router.get("/{transaction_id}", response_model=FinancialTransactionInDB)
async def get_transaction(
    transaction_id: UUID,
    service: FinancialTransactionService = Depends(get_transaction_service),
    tenant_id: UUID = Depends(get_current_tenant_from_header),
):
    """Retrieve a specific transaction by its ID."""
    db_transaction = await service.get_transaction_by_id(
        transaction_id=transaction_id, tenant_id=tenant_id
    )
    if db_transaction is None:
        raise HTTPException(status_code=404, detail="Transaction not found")
    return db_transaction


@router.put("/{transaction_id}", response_model=FinancialTransactionInDB)
async def update_transaction(
    transaction_id: UUID,
    transaction_data: FinancialTransactionUpdate,
    service: FinancialTransactionService = Depends(get_transaction_service),
    tenant_id: UUID = Depends(get_current_tenant_from_header),
):
    """Update a financial transaction."""
    updated_transaction = await service.update_transaction(
        transaction_id=transaction_id,
        transaction_data=transaction_data,
        tenant_id=tenant_id,
    )
    if updated_transaction is None:
        raise HTTPException(status_code=404, detail="Transaction not found")
    return updated_transaction


@router.delete("/{transaction_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_transaction(
    transaction_id: UUID,
    service: FinancialTransactionService = Depends(get_transaction_service),
    tenant_id: UUID = Depends(get_current_tenant_from_header),
):
    """Delete a financial transaction."""
    deleted = await service.delete_transaction(
        transaction_id=transaction_id, tenant_id=tenant_id
    )
    if not deleted:
        raise HTTPException(status_code=404, detail="Transaction not found")
    return


# === Supplier Invoice Payment Endpoints ===

@router.post(
    "/{transaction_id}/payment",
    response_model=InvoicePaymentRead,
    summary="Registrar Pagamento de Fatura",
    description="Registra pagamento de uma fatura de fornecedor.",
)
async def register_invoice_payment(
    transaction_id: UUID,
    payment_data: InvoicePaymentCreate,
    service: FinancialTransactionService = Depends(get_transaction_service),
    current_user: "User" = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
    tenant_id: UUID = Depends(get_current_tenant_from_header),
):
    """
    Registra pagamento de fatura de fornecedor.
    Requer role STAFF, MANAGER ou OWNER.
    """
    try:
        # Register payment using financial integration service
        transaction = await financial_integration_service.register_invoice_payment(
            db=service.db,
            transaction_id=transaction_id,
            tenant_id=tenant_id,
            payment_method_id=payment_data.payment_method_id,
            payment_date=payment_data.payment_date,
            payment_amount=payment_data.payment_amount,
            payment_reference=payment_data.payment_reference,
            payment_receipt_file=payment_data.payment_receipt_file,
            notes=payment_data.notes,
            updated_by=current_user.id
        )

        # Convert to response format
        return InvoicePaymentRead(
            id=transaction.id,
            tenant_id=transaction.tenant_id,
            amount=transaction.amount,
            description=transaction.description,
            transaction_date=transaction.transaction_date,
            reference_number=transaction.reference_number,
            payment_method_id=transaction.payment_method_id,
            notes=transaction.notes,
            is_paid=transaction.payment_method_id is not None
        )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to register payment: {str(e)}"
        )


@router.get(
    "/supplier-invoices",
    response_model=List[InvoicePaymentRead],
    summary="Listar Faturas de Fornecedores",
    description="Lista faturas de fornecedores pendentes de pagamento.",
)
async def list_supplier_invoices(
    skip: int = 0,
    limit: int = 100,
    service: FinancialTransactionService = Depends(get_transaction_service),
    current_user: "User" = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
    tenant_id: UUID = Depends(get_current_tenant_from_header),
):
    """
    Lista faturas de fornecedores.
    Requer role STAFF, MANAGER ou OWNER.
    """
    try:
        # Get pending supplier invoices
        transactions = await financial_integration_service.get_pending_supplier_invoices(
            db=service.db,
            tenant_id=tenant_id,
            skip=skip,
            limit=limit
        )

        # Convert to response format
        invoices = []
        for transaction in transactions:
            invoice = InvoicePaymentRead(
                id=transaction.id,
                tenant_id=transaction.tenant_id,
                amount=transaction.amount,
                description=transaction.description,
                transaction_date=transaction.transaction_date,
                reference_number=transaction.reference_number,
                payment_method_id=transaction.payment_method_id,
                notes=transaction.notes,
                is_paid=transaction.payment_method_id is not None
            )
            invoices.append(invoice)

        return invoices

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch supplier invoices: {str(e)}"
        )