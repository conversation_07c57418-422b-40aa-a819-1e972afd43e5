"""
Knowledge Base Service

Serviço para gerenciamento da base de conhecimento.
"""

import logging
from datetime import datetime, timezone
from typing import List, Optional, Dict, Any
from uuid import UUID

from sqlalchemy import and_, desc, func, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from sqlalchemy.future import select

from app.modules.core.users.models.user import User

from ..models import KnowledgeBaseArticle
from ..schemas import (
    KnowledgeBaseArticleCreate, KnowledgeBaseArticleUpdate,
    KnowledgeBaseArticleResponse, KnowledgeBaseArticleListResponse,
    KnowledgeBaseSearchRequest
)

logger = logging.getLogger(__name__)


class KnowledgeBaseService:
    """Serviço para gerenciamento da base de conhecimento."""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def create_article(
        self,
        article_data: KnowledgeBaseArticleCreate,
        admin_user: User
    ) -> Optional[KnowledgeBaseArticle]:
        """
        Cria um novo artigo.
        
        Args:
            article_data: Dados do artigo
            admin_user: Admin criando o artigo
            
        Returns:
            Artigo criado ou None se não for admin
        """
        if admin_user.system_role != "admin":
            return None
        
        article = KnowledgeBaseArticle(
            title=article_data.title,
            content=article_data.content,
            category=article_data.category,
            tags=article_data.tags,
            is_public=article_data.is_public,
            is_internal=article_data.is_internal,
            is_active=article_data.is_active,
            created_by_admin_id=admin_user.id
        )
        
        self.db.add(article)
        await self.db.commit()
        await self.db.refresh(article)
        
        logger.info(f"Artigo KB criado: {article.id} por admin {admin_user.id}")
        return article

    async def get_article(
        self,
        article_id: UUID,
        user: User,
        increment_view: bool = True
    ) -> Optional[KnowledgeBaseArticle]:
        """
        Obtém um artigo específico.
        
        Args:
            article_id: ID do artigo
            user: Usuário solicitante
            increment_view: Se deve incrementar contador de visualizações
            
        Returns:
            Artigo ou None se não encontrado/sem acesso
        """
        stmt = select(KnowledgeBaseArticle).options(
            selectinload(KnowledgeBaseArticle.created_by)
        ).where(KnowledgeBaseArticle.id == article_id)
        
        result = await self.db.execute(stmt)
        article = result.scalar_one_or_none()

        if not article:
            return None
        
        # Verifica acesso
        if not self._user_can_access_article(article, user):
            return None
        
        # Incrementa contador de visualizações
        if increment_view:
            article.increment_view_count()
            await self.db.commit()
        
        return article

    async def update_article(
        self,
        article_id: UUID,
        article_data: KnowledgeBaseArticleUpdate,
        admin_user: User
    ) -> Optional[KnowledgeBaseArticle]:
        """
        Atualiza um artigo.
        
        Args:
            article_id: ID do artigo
            article_data: Dados para atualização
            admin_user: Admin atualizando
            
        Returns:
            Artigo atualizado ou None se não encontrado/sem acesso
        """
        if admin_user.system_role != "admin":
            return None
        
        article = await self.get_article(article_id, admin_user, increment_view=False)
        if not article:
            return None
        
        # Atualiza campos
        if article_data.title is not None:
            article.title = article_data.title
        if article_data.content is not None:
            article.content = article_data.content
        if article_data.category is not None:
            article.category = article_data.category
        if article_data.tags is not None:
            article.tags = article_data.tags
        if article_data.is_public is not None:
            article.is_public = article_data.is_public
        if article_data.is_internal is not None:
            article.is_internal = article_data.is_internal
        if article_data.is_active is not None:
            article.is_active = article_data.is_active
        
        await self.db.commit()
        await self.db.refresh(article)
        
        logger.info(f"Artigo KB atualizado: {article.id} por admin {admin_user.id}")
        return article

    async def list_articles(
        self,
        user: User,
        category: Optional[str] = None,
        search: Optional[str] = None,
        page: int = 1,
        per_page: int = 20
    ) -> KnowledgeBaseArticleListResponse:
        """
        Lista artigos da base de conhecimento.
        
        Args:
            user: Usuário solicitante
            category: Filtro por categoria
            search: Termo de busca
            page: Página
            per_page: Itens por página
            
        Returns:
            Lista de artigos paginada
        """
        stmt = select(KnowledgeBaseArticle).options(
            selectinload(KnowledgeBaseArticle.created_by)
        )
        
        # Filtros de acesso
        if user.system_role == "admin":
            # Admin vê todos os artigos
            stmt = stmt.where(KnowledgeBaseArticle.is_active == True)
        else:
            # Usuário vê apenas artigos públicos
            stmt = stmt.where(
                and_(
                    KnowledgeBaseArticle.is_public == True,
                    KnowledgeBaseArticle.is_active == True
                )
            )
        
        # Filtros
        if category:
            stmt = stmt.where(KnowledgeBaseArticle.category == category)
        
        if search:
            search_term = f"%{search}%"
            stmt = stmt.where(
                or_(
                    KnowledgeBaseArticle.title.ilike(search_term),
                    KnowledgeBaseArticle.content.ilike(search_term),
                    KnowledgeBaseArticle.tags.ilike(search_term)
                )
            )
        
        # Ordenação por relevância (view_count) e data
        stmt = stmt.order_by(desc(KnowledgeBaseArticle.view_count), desc(KnowledgeBaseArticle.created_at))
        
        # Contagem total
        count_stmt = select(func.count()).select_from(stmt.subquery())
        total_result = await self.db.execute(count_stmt)
        total = total_result.scalar()
        
        # Paginação
        offset = (page - 1) * per_page
        stmt = stmt.offset(offset).limit(per_page)
        
        result = await self.db.execute(stmt)
        articles = result.scalars().all()
        
        # Converter para response
        article_responses = []
        for article in articles:
            article_response = KnowledgeBaseArticleResponse(
                id=article.id,
                title=article.title,
                content=article.content,
                category=article.category,
                tags=article.tags,
                is_public=article.is_public,
                is_internal=article.is_internal,
                is_active=article.is_active,
                created_by_admin_id=article.created_by_admin_id,
                view_count=article.view_count,
                helpful_count=article.helpful_count,
                not_helpful_count=article.not_helpful_count,
                helpfulness_ratio=article.helpfulness_ratio,
                created_at=article.created_at,
                updated_at=article.updated_at,
                created_by_name=article.created_by.full_name if article.created_by else None
            )
            article_responses.append(article_response)
        
        return KnowledgeBaseArticleListResponse(
            articles=article_responses,
            total=total,
            page=page,
            per_page=per_page,
            has_next=offset + per_page < total,
            has_prev=page > 1
        )

    async def search_articles(
        self,
        search_data: KnowledgeBaseSearchRequest,
        user: User
    ) -> List[KnowledgeBaseArticleResponse]:
        """
        Busca artigos na base de conhecimento.
        
        Args:
            search_data: Dados da busca
            user: Usuário solicitante
            
        Returns:
            Lista de artigos encontrados
        """
        response = await self.list_articles(
            user=user,
            category=search_data.category,
            search=search_data.query,
            page=1,
            per_page=search_data.limit
        )
        
        return response.articles

    async def mark_article_helpful(
        self,
        article_id: UUID,
        user: User,
        is_helpful: bool
    ) -> Optional[KnowledgeBaseArticle]:
        """
        Marca artigo como útil ou não útil.
        
        Args:
            article_id: ID do artigo
            user: Usuário avaliando
            is_helpful: Se é útil ou não
            
        Returns:
            Artigo atualizado ou None se não encontrado
        """
        article = await self.get_article(article_id, user, increment_view=False)
        if not article:
            return None
        
        if is_helpful:
            article.mark_as_helpful()
        else:
            article.mark_as_not_helpful()
        
        await self.db.commit()
        await self.db.refresh(article)
        
        logger.info(f"Artigo {article_id} marcado como {'útil' if is_helpful else 'não útil'} por {user.id}")
        return article

    async def delete_article(
        self,
        article_id: UUID,
        admin_user: User
    ) -> bool:
        """
        Deleta um artigo (apenas admin).
        
        Args:
            article_id: ID do artigo
            admin_user: Admin executando a operação
            
        Returns:
            True se deletado com sucesso
        """
        if admin_user.system_role != "admin":
            return False
        
        article = await self.get_article(article_id, admin_user, increment_view=False)
        if not article:
            return False
        
        await self.db.delete(article)
        await self.db.commit()
        
        logger.info(f"Artigo KB {article_id} deletado pelo admin {admin_user.id}")
        return True

    def _user_can_access_article(self, article: KnowledgeBaseArticle, user: User) -> bool:
        """
        Verifica se usuário pode acessar o artigo.
        
        Args:
            article: Artigo
            user: Usuário
            
        Returns:
            True se pode acessar
        """
        # Admin pode acessar todos os artigos ativos
        if user.system_role == "admin":
            return article.is_active
        
        # Usuário pode acessar apenas artigos públicos e ativos
        return article.is_public and article.is_active

    async def list_admin_articles(self, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Lista artigos para administradores.

        Args:
            filters: Filtros a aplicar

        Returns:
            Lista de artigos com metadados completos
        """
        filters = filters or {}

        # Query base
        stmt = select(KnowledgeBaseArticle).options(
            selectinload(KnowledgeBaseArticle.created_by)
        )

        # Aplicar filtros
        if filters.get("search"):
            search_term = f"%{filters['search']}%"
            stmt = stmt.where(
                or_(
                    KnowledgeBaseArticle.title.ilike(search_term),
                    KnowledgeBaseArticle.content.ilike(search_term),
                    KnowledgeBaseArticle.tags.ilike(search_term)
                )
            )

        if filters.get("category"):
            stmt = stmt.where(KnowledgeBaseArticle.category == filters["category"])

        if filters.get("is_published") is not None:
            stmt = stmt.where(KnowledgeBaseArticle.is_published == filters["is_published"])

        # Ordenar por data de criação (mais recentes primeiro)
        stmt = stmt.order_by(KnowledgeBaseArticle.created_at.desc())

        # Executar query
        result = await self.db.execute(stmt)
        articles = result.scalars().all()

        # Preparar resposta
        article_list = []
        for article in articles:
            article_dict = {
                "id": str(article.id),
                "title": article.title,
                "content": article.content,
                "category": article.category,
                "tags": article.tags,
                "view_count": article.view_count,
                "helpful_count": article.helpful_count,
                "not_helpful_count": article.not_helpful_count,
                "helpfulness_ratio": article.helpfulness_ratio,
                "is_published": article.is_public,  # Mapear is_public para is_published
                "created_at": article.created_at.isoformat(),
                "updated_at": article.updated_at.isoformat() if article.updated_at else None,
                "created_by_name": article.created_by.full_name if article.created_by else None
            }
            article_list.append(article_dict)

        return article_list

    async def create_article(self, article_data: Dict[str, Any], admin_user: User) -> Dict[str, Any]:
        """
        Cria novo artigo da base de conhecimento.

        Args:
            article_data: Dados do artigo
            admin_user: Usuário admin criando o artigo

        Returns:
            Dados do artigo criado
        """
        if admin_user.system_role != "admin":
            raise ValueError("Apenas administradores podem criar artigos")

        # Criar artigo
        article = KnowledgeBaseArticle(
            title=article_data["title"],
            content=article_data["content"],
            category=article_data.get("category"),
            tags=article_data.get("tags"),
            is_public=article_data.get("is_published", True),  # Mapear is_published para is_public
            is_internal=False,
            is_active=True,
            created_by_admin_id=admin_user.id
        )

        self.db.add(article)
        await self.db.commit()
        await self.db.refresh(article)

        logger.info(f"Artigo criado: {article.id} por admin {admin_user.id}")

        return {
            "id": str(article.id),
            "title": article.title,
            "content": article.content,
            "category": article.category,
            "tags": article.tags,
            "is_published": article.is_public,  # Mapear is_public para is_published
            "is_public": article.is_public,
            "is_internal": article.is_internal,
            "is_active": article.is_active,
            "created_at": article.created_at.isoformat()
        }

    async def update_article(
        self,
        article_id: UUID,
        article_data: Dict[str, Any],
        admin_user: User
    ) -> Optional[Dict[str, Any]]:
        """
        Atualiza artigo da base de conhecimento.

        Args:
            article_id: ID do artigo
            article_data: Dados para atualização
            admin_user: Usuário admin atualizando

        Returns:
            Dados do artigo atualizado ou None se não encontrado
        """
        if admin_user.system_role != "admin":
            raise ValueError("Apenas administradores podem atualizar artigos")

        # Buscar artigo
        stmt = select(KnowledgeBaseArticle).where(KnowledgeBaseArticle.id == article_id)
        result = await self.db.execute(stmt)
        article = result.scalar_one_or_none()

        if not article:
            return None

        # Atualizar campos
        if "title" in article_data:
            article.title = article_data["title"]
        if "content" in article_data:
            article.content = article_data["content"]
        if "category" in article_data:
            article.category = article_data["category"]
        if "tags" in article_data:
            article.tags = article_data["tags"]
        if "is_published" in article_data:
            article.is_public = article_data["is_published"]  # Mapear is_published para is_public

        article.updated_at = datetime.now(timezone.utc)

        await self.db.commit()
        await self.db.refresh(article)

        logger.info(f"Artigo atualizado: {article.id} por admin {admin_user.id}")

        return {
            "id": str(article.id),
            "title": article.title,
            "content": article.content,
            "category": article.category,
            "tags": article.tags,
            "is_published": article.is_public,  # Mapear is_public para is_published
            "is_public": article.is_public,
            "is_internal": article.is_internal,
            "is_active": article.is_active,
            "updated_at": article.updated_at.isoformat()
        }
