import uuid
from typing import Optional
from sqlalchemy import Column, String, ForeignKey, Numeric, Integer, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, Mapped

from app.db.base import Base


class SaleTransaction(Base):  # TimestampMixin já está em Base
    __tablename__ = "sale_transactions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    cash_register_id = Column(
        UUID(as_uuid=True), ForeignKey("cash_registers.id"), nullable=False, index=True
    )
    # Pode ser nulo se a venda não for associada a um cliente
    customer_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True, index=True)
    session_id = Column(
        UUID(as_uuid=True),
        ForeignKey("cash_register_sessions.id"),
        nullable=True,
        index=True,
    )  # Referência à sessão do caixa
    # Exemplo: 10 dígitos no total, 2 decimais
    total_amount = Column(Numeric(10, 2), nullable=False)
    payment_method = Column(String, nullable=True)
    status = Column(String, default="completed", nullable=False)
    unregistered_customer_email: Mapped[Optional[str]] = Column(String, nullable=True, index=True)
    unregistered_customer_phone: Mapped[Optional[str]] = Column(String, nullable=True, index=True)
    # items = Column(JSON, nullable=True) # Para detalhamento futuro dos itens

    tenant = relationship(
        "app.modules.core.tenants.models.tenant.Tenant", back_populates="sale_transactions"
    )
    cash_register = relationship("CashRegister", back_populates="transactions")
    customer = relationship("User", back_populates="sale_transactions")
    session = relationship("CashRegisterSession", back_populates="transactions")
    refunds = relationship("Refund", back_populates="transaction", cascade="all, delete-orphan")
    payments = relationship(
        "app.modules.core.functions.pos.models.payment_method.TransactionPayment",
        back_populates="transaction",
        cascade="all, delete-orphan",
    )
    items = relationship("SaleItem", back_populates="transaction", cascade="all, delete-orphan")


class SaleItem(Base):
    """Model for sale items."""

    __tablename__ = "sale_items"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    transaction_id = Column(
        UUID(as_uuid=True),
        ForeignKey("sale_transactions.id"),
        nullable=False,
        index=True,
    )
    product_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    quantity = Column(Integer, nullable=False)
    unit_price = Column(Numeric(10, 2), nullable=False)
    discount = Column(Numeric(10, 2), default=0, nullable=False)
    notes = Column(Text, nullable=True)

    transaction = relationship("SaleTransaction", back_populates="items")
