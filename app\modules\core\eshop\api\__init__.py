from fastapi import APIRouter

from .products import router as products_router
from .categories import router as categories_router
from .variants import router as variants_router
from .modifiers import router as modifiers_router
from .optionals import router as optionals_router

# Create main eshop API router (without prefix since it's added in main api.py)
eshop_router = APIRouter(tags=["eshop"])

# Include all sub-routers
eshop_router.include_router(products_router)
eshop_router.include_router(categories_router)
eshop_router.include_router(variants_router)
eshop_router.include_router(modifiers_router)
eshop_router.include_router(optionals_router)

# Additional EShop API Routers
from .approval_api import router as approval_router  
from .integration_api import router as integration_router
from .role_management_api import router as role_management_router
from .tcustomers_api import router as tcustomers_router

__all__ = [
    "eshop_router",
    "products_router",
    "approval_router",
    "integration_router",
    "role_management_router",
    "tcustomers_router"
]