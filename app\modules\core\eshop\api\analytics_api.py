"""
EShop Analytics API

Provides comprehensive analytics and reporting for the EShop module.
"""

from typing import Optional, List
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, Query, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import func, desc, and_, or_
from sqlalchemy.orm import selectinload

from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.auth_dependencies import get_current_active_user
from app.modules.core.users.models.user import User
from app.modules.core.tenants.dependencies.tenant_dependencies import get_current_tenant
from app.modules.core.tenants.models.tenant import Tenant

from ..schemas.analytics_schemas import (
    DashboardStatsResponse,
    CommissionAnalyticsResponse,
    ProductAnalyticsResponse,
    TopProductResponse,
    TopVendorResponse,
    RecentOrderResponse,
    MonthlyRevenueResponse
)
from ..services.analytics_service import EShopAnalyticsService

# Create analytics router
router = APIRouter(prefix="/analytics", tags=["EShop Analytics"])


@router.get("/dashboard", response_model=DashboardStatsResponse)
async def get_dashboard_stats(
    time_range: Optional[str] = Query("30d", regex="^(7d|30d|90d|1y)$"),
    market_type: Optional[str] = Query(None, regex="^(B2B|PUBLIC)$"),
    date_from: Optional[str] = Query(None),
    date_to: Optional[str] = Query(None),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get comprehensive dashboard statistics for EShop.
    
    Args:
        time_range: Predefined time range (7d, 30d, 90d, 1y)
        market_type: Filter by market type (B2B, PUBLIC)
        date_from: Custom start date (YYYY-MM-DD)
        date_to: Custom end date (YYYY-MM-DD)
    """
    try:
        # Parse date filters if provided
        start_date = None
        end_date = None
        if date_from:
            start_date = datetime.strptime(date_from, "%Y-%m-%d")
        if date_to:
            end_date = datetime.strptime(date_to, "%Y-%m-%d")

        # Initialize analytics service
        analytics_service = EShopAnalyticsService(db)

        # Get real dashboard statistics
        stats = await analytics_service.get_dashboard_stats(
            time_range=time_range,
            market_type=market_type,
            date_from=start_date,
            date_to=end_date,
            tenant_id=current_user.tenant_id
        )

        return DashboardStatsResponse(**stats)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching dashboard statistics: {str(e)}"
        )


@router.get("/commissions", response_model=CommissionAnalyticsResponse)
async def get_commission_analytics(
    time_range: Optional[str] = Query("30d", regex="^(7d|30d|90d|1y)$"),
    date_from: Optional[str] = Query(None),
    date_to: Optional[str] = Query(None),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get commission analytics and statistics.
    """
    try:
        # Parse date filters if provided
        start_date = None
        end_date = None
        if date_from:
            start_date = datetime.strptime(date_from, "%Y-%m-%d")
        if date_to:
            end_date = datetime.strptime(date_to, "%Y-%m-%d")

        # Initialize analytics service
        analytics_service = EShopAnalyticsService(db)

        # Get real commission analytics
        commission_data = await analytics_service.get_commission_analytics(
            time_range=time_range,
            date_from=start_date,
            date_to=end_date,
            tenant_id=current_user.tenant_id
        )

        return CommissionAnalyticsResponse(**commission_data)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching commission analytics: {str(e)}"
        )


@router.get("/products", response_model=ProductAnalyticsResponse)
async def get_product_analytics(
    market_type: Optional[str] = Query(None, regex="^(B2B|PUBLIC)$"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get product analytics and statistics.
    """
    try:
        # Initialize analytics service
        analytics_service = EShopAnalyticsService(db)

        # Get real product analytics
        product_data = await analytics_service.get_product_analytics(
            market_type=market_type,
            tenant_id=current_user.tenant_id
        )

        return ProductAnalyticsResponse(**product_data)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching product analytics: {str(e)}"
        )


@router.get("/realtime")
async def get_realtime_metrics(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get real-time metrics for live dashboard updates.
    """
    try:
        # Mock real-time data
        realtime_data = {
            "active_sessions": 1247,
            "orders_today": 89,
            "revenue_today": 15750.25,
            "pending_approvals": 12,
            "system_health": {
                "uptime": "99.9%",
                "response_time": "245ms",
                "error_rate": "0.1%"
            },
            "last_updated": datetime.now().isoformat()
        }

        return realtime_data

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching real-time metrics: {str(e)}"
        )


@router.get("/export/{analytics_type}")
async def export_analytics(
    analytics_type: str,
    format: str = Query(..., regex="^(csv|xlsx|pdf)$"),
    time_range: Optional[str] = Query("30d", regex="^(7d|30d|90d|1y)$"),
    market_type: Optional[str] = Query(None, regex="^(B2B|PUBLIC)$"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Export analytics data in various formats.
    
    Args:
        analytics_type: Type of analytics (dashboard, commissions, products)
        format: Export format (csv, xlsx, pdf)
    """
    try:
        # This would generate and return the file
        # For now, return a placeholder response
        return {
            "message": f"Export functionality for {analytics_type} in {format} format",
            "status": "placeholder",
            "note": "Export functionality not yet implemented"
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error exporting analytics: {str(e)}"
        )


@router.get("/health")
async def analytics_health_check():
    """Health check for analytics services."""
    return {
        'status': 'healthy',
        'service': 'eshop-analytics',
        'features': [
            'dashboard_stats',
            'commission_analytics',
            'product_analytics',
            'realtime_metrics',
            'export_functionality'
        ],
        'timestamp': datetime.now().isoformat()
    }
