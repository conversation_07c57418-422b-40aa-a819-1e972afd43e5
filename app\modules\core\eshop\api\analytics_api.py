"""
EShop Analytics API

Provides comprehensive analytics and reporting for the EShop module.
"""

from typing import Optional, List
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, Query, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import func, desc, and_, or_
from sqlalchemy.orm import selectinload

from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.auth_dependencies import get_current_active_user
from app.modules.core.auth.models.user import User
from app.modules.core.tenants.dependencies.tenant_dependencies import get_current_tenant
from app.modules.core.tenants.models.tenant import Tenant

from ..schemas.analytics_schemas import (
    DashboardStatsResponse,
    CommissionAnalyticsResponse,
    ProductAnalyticsResponse,
    TopProductResponse,
    TopVendorResponse,
    RecentOrderResponse,
    MonthlyRevenueResponse
)

# Create analytics router
router = APIRouter(prefix="/analytics", tags=["EShop Analytics"])


@router.get("/dashboard", response_model=DashboardStatsResponse)
async def get_dashboard_stats(
    time_range: Optional[str] = Query("30d", regex="^(7d|30d|90d|1y)$"),
    market_type: Optional[str] = Query(None, regex="^(B2B|PUBLIC)$"),
    date_from: Optional[str] = Query(None),
    date_to: Optional[str] = Query(None),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get comprehensive dashboard statistics for EShop.
    
    Args:
        time_range: Predefined time range (7d, 30d, 90d, 1y)
        market_type: Filter by market type (B2B, PUBLIC)
        date_from: Custom start date (YYYY-MM-DD)
        date_to: Custom end date (YYYY-MM-DD)
    """
    try:
        # Calculate date range
        end_date = datetime.now()
        if date_to:
            end_date = datetime.strptime(date_to, "%Y-%m-%d")
        
        if date_from:
            start_date = datetime.strptime(date_from, "%Y-%m-%d")
        else:
            days_map = {"7d": 7, "30d": 30, "90d": 90, "1y": 365}
            days = days_map.get(time_range, 30)
            start_date = end_date - timedelta(days=days)

        # Mock data for now - replace with actual database queries
        mock_stats = {
            "total_revenue": 2847650.75,
            "total_orders": 15847,
            "total_customers": 3421,
            "total_vendors": 287,
            "avg_order_value": 179.65,
            "conversion_rate": 3.2,
            "revenue_growth": 12.5,
            "orders_growth": 8.3,
            "customers_growth": 15.7,
            "top_selling_products": [
                {
                    "id": "1",
                    "name": "Premium Coffee Blend",
                    "vendor_name": "João Silva Coffee",
                    "total_sales": 1247,
                    "units_sold": 3891,
                    "revenue": 48750.30,
                    "market_type": "B2B"
                }
            ],
            "top_vendors": [
                {
                    "id": "1",
                    "name": "João Silva",
                    "business_name": "Coffee Store Premium",
                    "total_orders": 2847,
                    "total_revenue": 485750.30,
                    "commission_earned": 24287.52,
                    "rating": 4.8,
                    "market_type": "B2B"
                }
            ],
            "recent_orders": [
                {
                    "id": "1",
                    "order_number": "ORD-2024-001547",
                    "customer_name": "Restaurant ABC",
                    "vendor_name": "João Silva Coffee",
                    "total_amount": 1250.75,
                    "status": "confirmed",
                    "created_at": "2024-12-27T10:30:00Z",
                    "market_type": "B2B"
                }
            ],
            "revenue_by_month": [
                {
                    "month": "Jan",
                    "revenue": 185000,
                    "orders": 1250,
                    "b2b_revenue": 110000,
                    "public_revenue": 75000
                }
            ]
        }

        return DashboardStatsResponse(**mock_stats)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching dashboard statistics: {str(e)}"
        )


@router.get("/commissions", response_model=CommissionAnalyticsResponse)
async def get_commission_analytics(
    time_range: Optional[str] = Query("30d", regex="^(7d|30d|90d|1y)$"),
    date_from: Optional[str] = Query(None),
    date_to: Optional[str] = Query(None),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get commission analytics and statistics.
    """
    try:
        # Mock data for now
        mock_commission_data = {
            "total_commission_paid": 125750.50,
            "avg_commission_rate": 5.2,
            "monthly_commission": 42580.25,
            "top_earning_seller": "João Silva Coffee",
            "commission_by_vendor": [
                {
                    "vendor_id": "1",
                    "vendor_name": "João Silva Coffee",
                    "total_commission": 24287.52,
                    "commission_rate": 5.0
                }
            ]
        }

        return CommissionAnalyticsResponse(**mock_commission_data)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching commission analytics: {str(e)}"
        )


@router.get("/products", response_model=ProductAnalyticsResponse)
async def get_product_analytics(
    market_type: Optional[str] = Query(None, regex="^(B2B|PUBLIC)$"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get product analytics and statistics.
    """
    try:
        # Mock data for now
        mock_product_data = {
            "total_products": 1547,
            "active_products": 1423,
            "pending_approval": 89,
            "top_categories": [
                {
                    "category_id": "1",
                    "category_name": "Coffee & Beverages",
                    "product_count": 245,
                    "total_revenue": 125750.30
                }
            ],
            "stock_alerts": [
                {
                    "product_id": "1",
                    "product_name": "Premium Coffee Blend",
                    "current_stock": 5,
                    "min_stock": 10
                }
            ]
        }

        return ProductAnalyticsResponse(**mock_product_data)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching product analytics: {str(e)}"
        )


@router.get("/realtime")
async def get_realtime_metrics(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get real-time metrics for live dashboard updates.
    """
    try:
        # Mock real-time data
        realtime_data = {
            "active_sessions": 1247,
            "orders_today": 89,
            "revenue_today": 15750.25,
            "pending_approvals": 12,
            "system_health": {
                "uptime": "99.9%",
                "response_time": "245ms",
                "error_rate": "0.1%"
            },
            "last_updated": datetime.now().isoformat()
        }

        return realtime_data

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching real-time metrics: {str(e)}"
        )


@router.get("/export/{analytics_type}")
async def export_analytics(
    analytics_type: str,
    format: str = Query(..., regex="^(csv|xlsx|pdf)$"),
    time_range: Optional[str] = Query("30d", regex="^(7d|30d|90d|1y)$"),
    market_type: Optional[str] = Query(None, regex="^(B2B|PUBLIC)$"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Export analytics data in various formats.
    
    Args:
        analytics_type: Type of analytics (dashboard, commissions, products)
        format: Export format (csv, xlsx, pdf)
    """
    try:
        # This would generate and return the file
        # For now, return a placeholder response
        return {
            "message": f"Export functionality for {analytics_type} in {format} format",
            "status": "placeholder",
            "note": "Export functionality not yet implemented"
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error exporting analytics: {str(e)}"
        )


@router.get("/health")
async def analytics_health_check():
    """Health check for analytics services."""
    return {
        'status': 'healthy',
        'service': 'eshop-analytics',
        'features': [
            'dashboard_stats',
            'commission_analytics',
            'product_analytics',
            'realtime_metrics',
            'export_functionality'
        ],
        'timestamp': datetime.now().isoformat()
    }
