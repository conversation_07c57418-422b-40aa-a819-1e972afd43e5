from .product import *
from .approval_schemas import *
from .integration_schemas import *
from .tcostumer import (
    TCostumerCreate, TCostumerUpdate, TCostumerRead,
    TCostumerResponse, TCostumerStats
)
from .tvendor_supplier import (
    TVendorSupplierCreate, TVendorSupplierUpdate, TVendorSupplierRead,
    TVendorSupplierResponse, TVendorSupplierStats
)

__all__ = [
    # Product schemas
    "ProductBase",
    "ProductCreate",
    "ProductUpdate",
    "ProductResponse",
    "ProductListResponse",
    "ProductSearchRequest",
    
    # Approval schemas
    "ProductApprovalRequest",
    "ProductApprovalResponse",
    "ProductRejectionRequest",
    "ProductRevisionRequest",
    "ProductApprovalHistoryResponse",
    "ProductApprovalSettingsResponse",
    "ProductApprovalStatsResponse",
    "BulkApprovalRequest",
    "BulkApprovalResponse",
    
    # Integration schemas
    "ProductSyncRequest",
    "ProductSyncResponse",
    "OrderCreateRequest",
    "OrderCreateResponse",
    "CommissionCalculationResponse",
    "CustomerSyncResponse",
    "POSEnableRequest",
    "POSEnableResponse",
    "IntegrationStatusResponse",
    "BulkSyncRequest",
    "BulkSyncResponse",
    "ShippingCalculationRequest",
    "ShippingCalculationResponse",
    "ProductReviewsResponse",
    "SEOOptimizationResponse",
    "IntegrationAnalyticsResponse",

    # B2B Customer schemas
    "TCostumerCreate",
    "TCostumerUpdate",
    "TCostumerRead",
    "TCostumerResponse",
    "TCostumerStats",

    # B2B Vendor schemas
    "TVendorSupplierCreate",
    "TVendorSupplierUpdate",
    "TVendorSupplierRead",
    "TVendorSupplierResponse",
    "TVendorSupplierStats",
]
