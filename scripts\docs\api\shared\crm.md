# Shared - Crm

**Categoria:** Shared
**<PERSON><PERSON><PERSON><PERSON>:** Crm
**Total de Endpoints:** 39
**Gerado em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [GET /api/modules/crm/crm/accounts/](#get-apimodulescrmcrmaccounts) - Get Accounts
- [POST /api/modules/crm/crm/accounts/](#post-apimodulescrmcrmaccounts) - Create Account
- [DELETE /api/modules/crm/crm/accounts/{account_id}](#delete-apimodulescrmcrmaccountsaccount-id) - Delete Account
- [GET /api/modules/crm/crm/accounts/{account_id}](#get-apimodulescrmcrmaccountsaccount-id) - Get Account
- [PUT /api/modules/crm/crm/accounts/{account_id}](#put-apimodulescrmcrmaccountsaccount-id) - Update Account
- [GET /api/modules/crm/crm/contacts/](#get-apimodulescrmcrmcontacts) - Get Contacts
- [POST /api/modules/crm/crm/contacts/](#post-apimodulescrmcrmcontacts) - Create Contact
- [GET /api/modules/crm/crm/contacts/account/{account_id}](#get-apimodulescrmcrmcontactsaccountaccount-id) - Get Account Contacts
- [DELETE /api/modules/crm/crm/contacts/{contact_id}](#delete-apimodulescrmcrmcontactscontact-id) - Delete Contact
- [GET /api/modules/crm/crm/contacts/{contact_id}](#get-apimodulescrmcrmcontactscontact-id) - Get Contact
- [PUT /api/modules/crm/crm/contacts/{contact_id}](#put-apimodulescrmcrmcontactscontact-id) - Update Contact
- [GET /api/modules/crm/crm/interactions/](#get-apimodulescrmcrminteractions) - Get Interactions
- [POST /api/modules/crm/crm/interactions/](#post-apimodulescrmcrminteractions) - Create Interaction
- [GET /api/modules/crm/crm/interactions/account/{account_id}](#get-apimodulescrmcrminteractionsaccountaccount-id) - Get Account Interactions
- [DELETE /api/modules/crm/crm/interactions/{interaction_id}](#delete-apimodulescrmcrminteractionsinteraction-id) - Delete Interaction
- [GET /api/modules/crm/crm/interactions/{interaction_id}](#get-apimodulescrmcrminteractionsinteraction-id) - Get Interaction
- [PUT /api/modules/crm/crm/interactions/{interaction_id}](#put-apimodulescrmcrminteractionsinteraction-id) - Update Interaction
- [POST /api/modules/crm/crm/loyalty/memberships](#post-apimodulescrmcrmloyaltymemberships) - Create Loyalty Membership
- [GET /api/modules/crm/crm/loyalty/memberships/account/{account_id}](#get-apimodulescrmcrmloyaltymembershipsaccountaccount-id) - Get Account Loyalty Memberships
- [GET /api/modules/crm/crm/loyalty/memberships/{membership_id}](#get-apimodulescrmcrmloyaltymembershipsmembership-id) - Get Loyalty Membership
- [PUT /api/modules/crm/crm/loyalty/memberships/{membership_id}](#put-apimodulescrmcrmloyaltymembershipsmembership-id) - Update Loyalty Membership
- [GET /api/modules/crm/crm/loyalty/programs](#get-apimodulescrmcrmloyaltyprograms) - Get Loyalty Programs
- [POST /api/modules/crm/crm/loyalty/programs](#post-apimodulescrmcrmloyaltyprograms) - Create Loyalty Program
- [GET /api/modules/crm/crm/loyalty/programs/{program_id}](#get-apimodulescrmcrmloyaltyprogramsprogram-id) - Get Loyalty Program
- [PUT /api/modules/crm/crm/loyalty/programs/{program_id}](#put-apimodulescrmcrmloyaltyprogramsprogram-id) - Update Loyalty Program
- [POST /api/modules/crm/crm/loyalty/transactions](#post-apimodulescrmcrmloyaltytransactions) - Create Loyalty Transaction
- [GET /api/modules/crm/crm/loyalty/transactions/membership/{membership_id}](#get-apimodulescrmcrmloyaltytransactionsmembershipmembership-id) - Get Membership Transactions
- [POST /api/modules/crm/crm/pricing/assignments](#post-apimodulescrmcrmpricingassignments) - Assign Pricing Tier To Customer
- [GET /api/modules/crm/crm/pricing/assignments/account/{account_id}](#get-apimodulescrmcrmpricingassignmentsaccountaccount-id) - Get Customer Pricing Assignments
- [GET /api/modules/crm/crm/pricing/assignments/{assignment_id}](#get-apimodulescrmcrmpricingassignmentsassignment-id) - Get Customer Pricing Assignment
- [PUT /api/modules/crm/crm/pricing/assignments/{assignment_id}](#put-apimodulescrmcrmpricingassignmentsassignment-id) - Update Customer Pricing Assignment
- [POST /api/modules/crm/crm/pricing/rules](#post-apimodulescrmcrmpricingrules) - Create Pricing Rule
- [GET /api/modules/crm/crm/pricing/rules/tier/{tier_id}](#get-apimodulescrmcrmpricingrulestiertier-id) - Get Pricing Rules For Tier
- [GET /api/modules/crm/crm/pricing/rules/{rule_id}](#get-apimodulescrmcrmpricingrulesrule-id) - Get Pricing Rule
- [PUT /api/modules/crm/crm/pricing/rules/{rule_id}](#put-apimodulescrmcrmpricingrulesrule-id) - Update Pricing Rule
- [GET /api/modules/crm/crm/pricing/tiers](#get-apimodulescrmcrmpricingtiers) - Get Pricing Tiers
- [POST /api/modules/crm/crm/pricing/tiers](#post-apimodulescrmcrmpricingtiers) - Create Pricing Tier
- [GET /api/modules/crm/crm/pricing/tiers/{tier_id}](#get-apimodulescrmcrmpricingtierstier-id) - Get Pricing Tier
- [PUT /api/modules/crm/crm/pricing/tiers/{tier_id}](#put-apimodulescrmcrmpricingtierstier-id) - Update Pricing Tier

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### AccountCreate

**Descrição:** Schema for creating a new Account.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `account_type` | AccountType | ❌ | - |
| `status` | AccountStatus | ❌ | - |
| `email` | unknown | ❌ | - |
| `phone` | unknown | ❌ | - |
| `website` | unknown | ❌ | - |
| `address_line1` | unknown | ❌ | - |
| `address_line2` | unknown | ❌ | - |
| `city` | unknown | ❌ | - |
| `state` | unknown | ❌ | - |
| `postal_code` | unknown | ❌ | - |
| `country` | unknown | ❌ | - |
| `tax_id` | unknown | ❌ | - |
| `industry` | unknown | ❌ | - |
| `annual_revenue` | unknown | ❌ | - |
| `number_of_employees` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `notes` | unknown | ❌ | - |
| `user_tenant_association_id` | unknown | ❌ | - |
| `acquisition_date` | unknown | ❌ | - |

### AccountRead

**Descrição:** Schema for reading an Account.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `account_type` | AccountType | ❌ | - |
| `status` | AccountStatus | ❌ | - |
| `email` | unknown | ❌ | - |
| `phone` | unknown | ❌ | - |
| `website` | unknown | ❌ | - |
| `address_line1` | unknown | ❌ | - |
| `address_line2` | unknown | ❌ | - |
| `city` | unknown | ❌ | - |
| `state` | unknown | ❌ | - |
| `postal_code` | unknown | ❌ | - |
| `country` | unknown | ❌ | - |
| `tax_id` | unknown | ❌ | - |
| `industry` | unknown | ❌ | - |
| `annual_revenue` | unknown | ❌ | - |
| `number_of_employees` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `notes` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `user_tenant_association_id` | unknown | ❌ | - |
| `acquisition_date` | unknown | ❌ | - |
| `last_contact_date` | unknown | ❌ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### AccountUpdate

**Descrição:** Schema for updating an Account.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | - |
| `account_type` | unknown | ❌ | - |
| `status` | unknown | ❌ | - |
| `email` | unknown | ❌ | - |
| `phone` | unknown | ❌ | - |
| `website` | unknown | ❌ | - |
| `address_line1` | unknown | ❌ | - |
| `address_line2` | unknown | ❌ | - |
| `city` | unknown | ❌ | - |
| `state` | unknown | ❌ | - |
| `postal_code` | unknown | ❌ | - |
| `country` | unknown | ❌ | - |
| `tax_id` | unknown | ❌ | - |
| `industry` | unknown | ❌ | - |
| `annual_revenue` | unknown | ❌ | - |
| `number_of_employees` | unknown | ❌ | - |
| `acquisition_date` | unknown | ❌ | - |
| `last_contact_date` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `notes` | unknown | ❌ | - |

### ContactCreate

**Descrição:** Schema for creating a new Contact.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `first_name` | string | ✅ | - |
| `last_name` | string | ✅ | - |
| `contact_type` | ContactType | ❌ | - |
| `status` | ContactStatus | ❌ | - |
| `email` | unknown | ❌ | - |
| `phone` | unknown | ❌ | - |
| `mobile` | unknown | ❌ | - |
| `job_title` | unknown | ❌ | - |
| `department` | unknown | ❌ | - |
| `preferred_contact_method` | unknown | ❌ | - |
| `communication_preferences` | unknown | ❌ | - |
| `notes` | unknown | ❌ | - |
| `account_id` | string | ✅ | - |
| `user_tenant_association_id` | unknown | ❌ | - |
| `full_name` | unknown | ❌ | - |

### ContactRead

**Descrição:** Schema for reading a Contact.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `first_name` | string | ✅ | - |
| `last_name` | string | ✅ | - |
| `contact_type` | ContactType | ❌ | - |
| `status` | ContactStatus | ❌ | - |
| `email` | unknown | ❌ | - |
| `phone` | unknown | ❌ | - |
| `mobile` | unknown | ❌ | - |
| `job_title` | unknown | ❌ | - |
| `department` | unknown | ❌ | - |
| `preferred_contact_method` | unknown | ❌ | - |
| `communication_preferences` | unknown | ❌ | - |
| `notes` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `account_id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `user_tenant_association_id` | unknown | ❌ | - |
| `full_name` | unknown | ❌ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### ContactUpdate

**Descrição:** Schema for updating a Contact.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `first_name` | unknown | ❌ | - |
| `last_name` | unknown | ❌ | - |
| `full_name` | unknown | ❌ | - |
| `contact_type` | unknown | ❌ | - |
| `status` | unknown | ❌ | - |
| `email` | unknown | ❌ | - |
| `phone` | unknown | ❌ | - |
| `mobile` | unknown | ❌ | - |
| `job_title` | unknown | ❌ | - |
| `department` | unknown | ❌ | - |
| `preferred_contact_method` | unknown | ❌ | - |
| `communication_preferences` | unknown | ❌ | - |
| `notes` | unknown | ❌ | - |

### CustomerPricingAssignmentCreate

**Descrição:** Schema for creating a new CustomerPricingAssignment.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `start_date` | string | ❌ | - |
| `end_date` | unknown | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `custom_discount_percentage` | unknown | ❌ | - |
| `notes` | unknown | ❌ | - |
| `pricing_tier_id` | string | ✅ | - |
| `account_id` | string | ✅ | - |

### CustomerPricingAssignmentRead

**Descrição:** Schema for reading a CustomerPricingAssignment.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `start_date` | string | ❌ | - |
| `end_date` | unknown | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `custom_discount_percentage` | unknown | ❌ | - |
| `notes` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `pricing_tier_id` | string | ✅ | - |
| `account_id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### CustomerPricingAssignmentUpdate

**Descrição:** Schema for updating a CustomerPricingAssignment.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `end_date` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `custom_discount_percentage` | unknown | ❌ | - |
| `notes` | unknown | ❌ | - |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### InteractionCreate

**Descrição:** Schema for creating a new Interaction.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `interaction_type` | InteractionType | ✅ | - |
| `channel` | InteractionChannel | ✅ | - |
| `subject` | string | ✅ | - |
| `description` | unknown | ❌ | - |
| `interaction_date` | string | ❌ | - |
| `duration_minutes` | unknown | ❌ | - |
| `requires_followup` | boolean | ❌ | - |
| `followup_date` | unknown | ❌ | - |
| `followup_notes` | unknown | ❌ | - |
| `is_completed` | boolean | ❌ | - |
| `outcome` | unknown | ❌ | - |
| `metadata` | unknown | ❌ | - |
| `account_id` | string | ✅ | - |
| `contact_id` | unknown | ❌ | - |
| `created_by_user_id` | unknown | ❌ | - |

### InteractionRead

**Descrição:** Schema for reading an Interaction.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `interaction_type` | InteractionType | ✅ | - |
| `channel` | InteractionChannel | ✅ | - |
| `subject` | string | ✅ | - |
| `description` | unknown | ❌ | - |
| `interaction_date` | string | ❌ | - |
| `duration_minutes` | unknown | ❌ | - |
| `requires_followup` | boolean | ❌ | - |
| `followup_date` | unknown | ❌ | - |
| `followup_notes` | unknown | ❌ | - |
| `is_completed` | boolean | ❌ | - |
| `outcome` | unknown | ❌ | - |
| `metadata` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `account_id` | string | ✅ | - |
| `contact_id` | unknown | ❌ | - |
| `tenant_id` | string | ✅ | - |
| `created_by_user_id` | unknown | ❌ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### InteractionUpdate

**Descrição:** Schema for updating an Interaction.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `interaction_type` | unknown | ❌ | - |
| `channel` | unknown | ❌ | - |
| `subject` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `interaction_date` | unknown | ❌ | - |
| `duration_minutes` | unknown | ❌ | - |
| `requires_followup` | unknown | ❌ | - |
| `followup_date` | unknown | ❌ | - |
| `followup_notes` | unknown | ❌ | - |
| `is_completed` | unknown | ❌ | - |
| `outcome` | unknown | ❌ | - |
| `metadata` | unknown | ❌ | - |

### LoyaltyMembershipCreate

**Descrição:** Schema for creating a new LoyaltyMembership.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `membership_number` | unknown | ❌ | - |
| `status` | LoyaltyMembershipStatus | ❌ | - |
| `points_balance` | integer | ❌ | - |
| `tier_level` | unknown | ❌ | - |
| `join_date` | string | ❌ | - |
| `expiry_date` | unknown | ❌ | - |
| `program_id` | string | ✅ | - |
| `account_id` | string | ✅ | - |

### LoyaltyMembershipRead

**Descrição:** Schema for reading a LoyaltyMembership.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `membership_number` | unknown | ❌ | - |
| `status` | LoyaltyMembershipStatus | ❌ | - |
| `points_balance` | integer | ❌ | - |
| `tier_level` | unknown | ❌ | - |
| `join_date` | string | ❌ | - |
| `expiry_date` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `program_id` | string | ✅ | - |
| `account_id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `last_activity_date` | unknown | ❌ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### LoyaltyMembershipUpdate

**Descrição:** Schema for updating a LoyaltyMembership.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `membership_number` | unknown | ❌ | - |
| `status` | unknown | ❌ | - |
| `points_balance` | unknown | ❌ | - |
| `tier_level` | unknown | ❌ | - |
| `expiry_date` | unknown | ❌ | - |
| `last_activity_date` | unknown | ❌ | - |

### LoyaltyProgramCreate

**Descrição:** Schema for creating a new LoyaltyProgram.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `description` | unknown | ❌ | - |
| `program_type` | LoyaltyProgramType | ✅ | - |
| `is_active` | boolean | ❌ | - |
| `start_date` | unknown | ❌ | - |
| `end_date` | unknown | ❌ | - |
| `earning_rules` | unknown | ❌ | - |
| `redemption_rules` | unknown | ❌ | - |
| `expiration_rules` | unknown | ❌ | - |
| `tier_rules` | unknown | ❌ | - |

### LoyaltyProgramRead

**Descrição:** Schema for reading a LoyaltyProgram.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `description` | unknown | ❌ | - |
| `program_type` | LoyaltyProgramType | ✅ | - |
| `is_active` | boolean | ❌ | - |
| `start_date` | unknown | ❌ | - |
| `end_date` | unknown | ❌ | - |
| `earning_rules` | unknown | ❌ | - |
| `redemption_rules` | unknown | ❌ | - |
| `expiration_rules` | unknown | ❌ | - |
| `tier_rules` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### LoyaltyProgramUpdate

**Descrição:** Schema for updating a LoyaltyProgram.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `program_type` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `start_date` | unknown | ❌ | - |
| `end_date` | unknown | ❌ | - |
| `earning_rules` | unknown | ❌ | - |
| `redemption_rules` | unknown | ❌ | - |
| `expiration_rules` | unknown | ❌ | - |
| `tier_rules` | unknown | ❌ | - |

### LoyaltyTransactionCreate

**Descrição:** Schema for creating a new LoyaltyTransaction.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `transaction_type` | LoyaltyTransactionType | ✅ | - |
| `points` | integer | ✅ | - |
| `reference_type` | unknown | ❌ | - |
| `reference_id` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `transaction_date` | string | ❌ | - |
| `membership_id` | string | ✅ | - |

### LoyaltyTransactionRead

**Descrição:** Schema for reading a LoyaltyTransaction.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `transaction_type` | LoyaltyTransactionType | ✅ | - |
| `points` | integer | ✅ | - |
| `reference_type` | unknown | ❌ | - |
| `reference_id` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `transaction_date` | string | ❌ | - |
| `id` | string | ✅ | - |
| `membership_id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### PricingRuleCreate

**Descrição:** Schema for creating a new PricingRule.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `description` | unknown | ❌ | - |
| `rule_type` | PricingRuleType | ✅ | - |
| `product_category` | unknown | ❌ | - |
| `product_id` | unknown | ❌ | - |
| `discount_percentage` | unknown | ❌ | - |
| `discount_amount` | unknown | ❌ | - |
| `custom_price` | unknown | ❌ | - |
| `min_quantity` | unknown | ❌ | - |
| `start_date` | unknown | ❌ | - |
| `end_date` | unknown | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `conditions` | unknown | ❌ | - |
| `pricing_tier_id` | string | ✅ | - |

### PricingRuleRead

**Descrição:** Schema for reading a PricingRule.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `description` | unknown | ❌ | - |
| `rule_type` | PricingRuleType | ✅ | - |
| `product_category` | unknown | ❌ | - |
| `product_id` | unknown | ❌ | - |
| `discount_percentage` | unknown | ❌ | - |
| `discount_amount` | unknown | ❌ | - |
| `custom_price` | unknown | ❌ | - |
| `min_quantity` | unknown | ❌ | - |
| `start_date` | unknown | ❌ | - |
| `end_date` | unknown | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `conditions` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `pricing_tier_id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### PricingRuleUpdate

**Descrição:** Schema for updating a PricingRule.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `rule_type` | unknown | ❌ | - |
| `product_category` | unknown | ❌ | - |
| `product_id` | unknown | ❌ | - |
| `discount_percentage` | unknown | ❌ | - |
| `discount_amount` | unknown | ❌ | - |
| `custom_price` | unknown | ❌ | - |
| `min_quantity` | unknown | ❌ | - |
| `start_date` | unknown | ❌ | - |
| `end_date` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `conditions` | unknown | ❌ | - |

### PricingTierCreate

**Descrição:** Schema for creating a new PricingTier.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `description` | unknown | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `default_discount_percentage` | number | ❌ | - |

### PricingTierRead

**Descrição:** Schema for reading a PricingTier.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `description` | unknown | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `default_discount_percentage` | number | ❌ | - |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### PricingTierUpdate

**Descrição:** Schema for updating a PricingTier.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `default_discount_percentage` | unknown | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/modules/crm/crm/accounts/ {#get-apimodulescrmcrmaccounts}

**Resumo:** Get Accounts
**Descrição:** Get all CRM accounts with optional filtering.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | Number of accounts to skip |
| `limit` | integer | query | ❌ | Maximum number of accounts to return |
| `status` | string | query | ❌ | Filter by account status |
| `account_type` | string | query | ❌ | Filter by account type |
| `search` | string | query | ❌ | Search term for account name, email, or phone |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/accounts/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/crm/crm/accounts/ {#post-apimodulescrmcrmaccounts}

**Resumo:** Create Account
**Descrição:** Create a new CRM account.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [AccountCreate](#accountcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [AccountRead](#accountread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/crm/crm/accounts/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/crm/crm/accounts/{account_id} {#delete-apimodulescrmcrmaccountsaccount-id}

**Resumo:** Delete Account
**Descrição:** Delete a CRM account.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `account_id` | string | path | ✅ | The ID of the account to delete |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/crm/crm/accounts/{account_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/crm/crm/accounts/{account_id} {#get-apimodulescrmcrmaccountsaccount-id}

**Resumo:** Get Account
**Descrição:** Get a CRM account by ID.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `account_id` | string | path | ✅ | The ID of the account to retrieve |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [AccountRead](#accountread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/accounts/{account_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/crm/crm/accounts/{account_id} {#put-apimodulescrmcrmaccountsaccount-id}

**Resumo:** Update Account
**Descrição:** Update a CRM account.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `account_id` | string | path | ✅ | The ID of the account to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [AccountUpdate](#accountupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [AccountRead](#accountread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/crm/crm/accounts/{account_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/crm/crm/contacts/ {#get-apimodulescrmcrmcontacts}

**Resumo:** Get Contacts
**Descrição:** Get all CRM contacts with optional filtering.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `account_id` | string | query | ❌ | Filter by account ID |
| `skip` | integer | query | ❌ | Number of contacts to skip |
| `limit` | integer | query | ❌ | Maximum number of contacts to return |
| `status` | string | query | ❌ | Filter by contact status |
| `contact_type` | string | query | ❌ | Filter by contact type |
| `search` | string | query | ❌ | Search term for contact name, email, or phone |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/contacts/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/crm/crm/contacts/ {#post-apimodulescrmcrmcontacts}

**Resumo:** Create Contact
**Descrição:** Create a new CRM contact.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ContactCreate](#contactcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ContactRead](#contactread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/crm/crm/contacts/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/crm/crm/contacts/account/{account_id} {#get-apimodulescrmcrmcontactsaccountaccount-id}

**Resumo:** Get Account Contacts
**Descrição:** Get all contacts for a specific account.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `account_id` | string | path | ✅ | The ID of the account to get contacts for |
| `skip` | integer | query | ❌ | Number of contacts to skip |
| `limit` | integer | query | ❌ | Maximum number of contacts to return |
| `status` | string | query | ❌ | Filter by contact status |
| `contact_type` | string | query | ❌ | Filter by contact type |
| `search` | string | query | ❌ | Search term for contact name, email, or phone |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/contacts/account/{account_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### DELETE /api/modules/crm/crm/contacts/{contact_id} {#delete-apimodulescrmcrmcontactscontact-id}

**Resumo:** Delete Contact
**Descrição:** Delete a CRM contact.

Requires tenant owner or manager role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `contact_id` | string | path | ✅ | The ID of the contact to delete |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/crm/crm/contacts/{contact_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/crm/crm/contacts/{contact_id} {#get-apimodulescrmcrmcontactscontact-id}

**Resumo:** Get Contact
**Descrição:** Get a CRM contact by ID.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `contact_id` | string | path | ✅ | The ID of the contact to retrieve |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ContactRead](#contactread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/contacts/{contact_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/crm/crm/contacts/{contact_id} {#put-apimodulescrmcrmcontactscontact-id}

**Resumo:** Update Contact
**Descrição:** Update a CRM contact.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `contact_id` | string | path | ✅ | The ID of the contact to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ContactUpdate](#contactupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ContactRead](#contactread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/crm/crm/contacts/{contact_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/crm/crm/interactions/ {#get-apimodulescrmcrminteractions}

**Resumo:** Get Interactions
**Descrição:** Get all CRM interactions with optional filtering.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `account_id` | string | query | ❌ | Filter by account ID |
| `contact_id` | string | query | ❌ | Filter by contact ID |
| `skip` | integer | query | ❌ | Number of interactions to skip |
| `limit` | integer | query | ❌ | Maximum number of interactions to return |
| `interaction_type` | string | query | ❌ | Filter by interaction type |
| `requires_followup` | string | query | ❌ | Filter by requires_followup flag |
| `is_completed` | string | query | ❌ | Filter by is_completed flag |
| `start_date` | string | query | ❌ | Filter by start date (inclusive) |
| `end_date` | string | query | ❌ | Filter by end date (inclusive) |
| `search` | string | query | ❌ | Search term for interaction subject or description |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/interactions/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/crm/crm/interactions/ {#post-apimodulescrmcrminteractions}

**Resumo:** Create Interaction
**Descrição:** Create a new CRM interaction.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [InteractionCreate](#interactioncreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [InteractionRead](#interactionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/crm/crm/interactions/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/crm/crm/interactions/account/{account_id} {#get-apimodulescrmcrminteractionsaccountaccount-id}

**Resumo:** Get Account Interactions
**Descrição:** Get all interactions for a specific account.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `account_id` | string | path | ✅ | The ID of the account to get interactions for |
| `skip` | integer | query | ❌ | Number of interactions to skip |
| `limit` | integer | query | ❌ | Maximum number of interactions to return |
| `interaction_type` | string | query | ❌ | Filter by interaction type |
| `requires_followup` | string | query | ❌ | Filter by requires_followup flag |
| `is_completed` | string | query | ❌ | Filter by is_completed flag |
| `start_date` | string | query | ❌ | Filter by start date (inclusive) |
| `end_date` | string | query | ❌ | Filter by end date (inclusive) |
| `search` | string | query | ❌ | Search term for interaction subject or description |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/interactions/account/{account_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### DELETE /api/modules/crm/crm/interactions/{interaction_id} {#delete-apimodulescrmcrminteractionsinteraction-id}

**Resumo:** Delete Interaction
**Descrição:** Delete a CRM interaction.

Requires tenant owner or manager role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `interaction_id` | string | path | ✅ | The ID of the interaction to delete |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/crm/crm/interactions/{interaction_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/crm/crm/interactions/{interaction_id} {#get-apimodulescrmcrminteractionsinteraction-id}

**Resumo:** Get Interaction
**Descrição:** Get a CRM interaction by ID.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `interaction_id` | string | path | ✅ | The ID of the interaction to retrieve |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [InteractionRead](#interactionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/interactions/{interaction_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/crm/crm/interactions/{interaction_id} {#put-apimodulescrmcrminteractionsinteraction-id}

**Resumo:** Update Interaction
**Descrição:** Update a CRM interaction.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `interaction_id` | string | path | ✅ | The ID of the interaction to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [InteractionUpdate](#interactionupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [InteractionRead](#interactionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/crm/crm/interactions/{interaction_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/crm/crm/loyalty/memberships {#post-apimodulescrmcrmloyaltymemberships}

**Resumo:** Create Loyalty Membership
**Descrição:** Create a new loyalty membership for a customer.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [LoyaltyMembershipCreate](#loyaltymembershipcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LoyaltyMembershipRead](#loyaltymembershipread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/crm/crm/loyalty/memberships" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/crm/crm/loyalty/memberships/account/{account_id} {#get-apimodulescrmcrmloyaltymembershipsaccountaccount-id}

**Resumo:** Get Account Loyalty Memberships
**Descrição:** Get all loyalty memberships for a specific account.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `account_id` | string | path | ✅ | The ID of the account to get memberships for |
| `skip` | integer | query | ❌ | Number of memberships to skip |
| `limit` | integer | query | ❌ | Maximum number of memberships to return |
| `status` | string | query | ❌ | Filter by membership status |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/loyalty/memberships/account/{account_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/crm/crm/loyalty/memberships/{membership_id} {#get-apimodulescrmcrmloyaltymembershipsmembership-id}

**Resumo:** Get Loyalty Membership
**Descrição:** Get a loyalty membership by ID.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `membership_id` | string | path | ✅ | The ID of the loyalty membership to retrieve |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LoyaltyMembershipRead](#loyaltymembershipread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/loyalty/memberships/{membership_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/crm/crm/loyalty/memberships/{membership_id} {#put-apimodulescrmcrmloyaltymembershipsmembership-id}

**Resumo:** Update Loyalty Membership
**Descrição:** Update a loyalty membership.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `membership_id` | string | path | ✅ | The ID of the loyalty membership to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [LoyaltyMembershipUpdate](#loyaltymembershipupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LoyaltyMembershipRead](#loyaltymembershipread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/crm/crm/loyalty/memberships/{membership_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/crm/crm/loyalty/programs {#get-apimodulescrmcrmloyaltyprograms}

**Resumo:** Get Loyalty Programs
**Descrição:** Get all loyalty programs with optional filtering.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | Number of programs to skip |
| `limit` | integer | query | ❌ | Maximum number of programs to return |
| `is_active` | string | query | ❌ | Filter by active status |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/loyalty/programs" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/crm/crm/loyalty/programs {#post-apimodulescrmcrmloyaltyprograms}

**Resumo:** Create Loyalty Program
**Descrição:** Create a new loyalty program.

Requires tenant owner or manager role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [LoyaltyProgramCreate](#loyaltyprogramcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LoyaltyProgramRead](#loyaltyprogramread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/crm/crm/loyalty/programs" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/crm/crm/loyalty/programs/{program_id} {#get-apimodulescrmcrmloyaltyprogramsprogram-id}

**Resumo:** Get Loyalty Program
**Descrição:** Get a loyalty program by ID.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `program_id` | string | path | ✅ | The ID of the loyalty program to retrieve |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LoyaltyProgramRead](#loyaltyprogramread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/loyalty/programs/{program_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/crm/crm/loyalty/programs/{program_id} {#put-apimodulescrmcrmloyaltyprogramsprogram-id}

**Resumo:** Update Loyalty Program
**Descrição:** Update a loyalty program.

Requires tenant owner or manager role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `program_id` | string | path | ✅ | The ID of the loyalty program to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [LoyaltyProgramUpdate](#loyaltyprogramupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LoyaltyProgramRead](#loyaltyprogramread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/crm/crm/loyalty/programs/{program_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/crm/crm/loyalty/transactions {#post-apimodulescrmcrmloyaltytransactions}

**Resumo:** Create Loyalty Transaction
**Descrição:** Create a new loyalty transaction (earn or redeem points).

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [LoyaltyTransactionCreate](#loyaltytransactioncreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LoyaltyTransactionRead](#loyaltytransactionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/crm/crm/loyalty/transactions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/crm/crm/loyalty/transactions/membership/{membership_id} {#get-apimodulescrmcrmloyaltytransactionsmembershipmembership-id}

**Resumo:** Get Membership Transactions
**Descrição:** Get all transactions for a specific loyalty membership.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `membership_id` | string | path | ✅ | The ID of the membership to get transactions for |
| `skip` | integer | query | ❌ | Number of transactions to skip |
| `limit` | integer | query | ❌ | Maximum number of transactions to return |
| `transaction_type` | string | query | ❌ | Filter by transaction type |
| `start_date` | string | query | ❌ | Filter by start date (inclusive) |
| `end_date` | string | query | ❌ | Filter by end date (inclusive) |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/loyalty/transactions/membership/{membership_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/crm/crm/pricing/assignments {#post-apimodulescrmcrmpricingassignments}

**Resumo:** Assign Pricing Tier To Customer
**Descrição:** Assign a pricing tier to a customer.

Requires tenant owner or manager role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [CustomerPricingAssignmentCreate](#customerpricingassignmentcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CustomerPricingAssignmentRead](#customerpricingassignmentread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/crm/crm/pricing/assignments" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/crm/crm/pricing/assignments/account/{account_id} {#get-apimodulescrmcrmpricingassignmentsaccountaccount-id}

**Resumo:** Get Customer Pricing Assignments
**Descrição:** Get all pricing assignments for a specific customer.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `account_id` | string | path | ✅ | The ID of the account to get pricing assignments for |
| `skip` | integer | query | ❌ | Number of assignments to skip |
| `limit` | integer | query | ❌ | Maximum number of assignments to return |
| `is_active` | string | query | ❌ | Filter by active status |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/pricing/assignments/account/{account_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/crm/crm/pricing/assignments/{assignment_id} {#get-apimodulescrmcrmpricingassignmentsassignment-id}

**Resumo:** Get Customer Pricing Assignment
**Descrição:** Get a customer pricing assignment by ID.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `assignment_id` | string | path | ✅ | The ID of the pricing assignment to retrieve |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CustomerPricingAssignmentRead](#customerpricingassignmentread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/pricing/assignments/{assignment_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/crm/crm/pricing/assignments/{assignment_id} {#put-apimodulescrmcrmpricingassignmentsassignment-id}

**Resumo:** Update Customer Pricing Assignment
**Descrição:** Update a customer pricing assignment.

Requires tenant owner or manager role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `assignment_id` | string | path | ✅ | The ID of the pricing assignment to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [CustomerPricingAssignmentUpdate](#customerpricingassignmentupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CustomerPricingAssignmentRead](#customerpricingassignmentread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/crm/crm/pricing/assignments/{assignment_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/crm/crm/pricing/rules {#post-apimodulescrmcrmpricingrules}

**Resumo:** Create Pricing Rule
**Descrição:** Create a new pricing rule.

Requires tenant owner or manager role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [PricingRuleCreate](#pricingrulecreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PricingRuleRead](#pricingruleread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/crm/crm/pricing/rules" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/crm/crm/pricing/rules/tier/{tier_id} {#get-apimodulescrmcrmpricingrulestiertier-id}

**Resumo:** Get Pricing Rules For Tier
**Descrição:** Get all pricing rules for a specific tier.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tier_id` | string | path | ✅ | The ID of the pricing tier to get rules for |
| `skip` | integer | query | ❌ | Number of rules to skip |
| `limit` | integer | query | ❌ | Maximum number of rules to return |
| `is_active` | string | query | ❌ | Filter by active status |
| `rule_type` | string | query | ❌ | Filter by rule type |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/pricing/rules/tier/{tier_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/crm/crm/pricing/rules/{rule_id} {#get-apimodulescrmcrmpricingrulesrule-id}

**Resumo:** Get Pricing Rule
**Descrição:** Get a pricing rule by ID.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `rule_id` | string | path | ✅ | The ID of the pricing rule to retrieve |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PricingRuleRead](#pricingruleread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/pricing/rules/{rule_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/crm/crm/pricing/rules/{rule_id} {#put-apimodulescrmcrmpricingrulesrule-id}

**Resumo:** Update Pricing Rule
**Descrição:** Update a pricing rule.

Requires tenant owner or manager role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `rule_id` | string | path | ✅ | The ID of the pricing rule to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [PricingRuleUpdate](#pricingruleupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PricingRuleRead](#pricingruleread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/crm/crm/pricing/rules/{rule_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/crm/crm/pricing/tiers {#get-apimodulescrmcrmpricingtiers}

**Resumo:** Get Pricing Tiers
**Descrição:** Get all pricing tiers with optional filtering.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | Number of tiers to skip |
| `limit` | integer | query | ❌ | Maximum number of tiers to return |
| `is_active` | string | query | ❌ | Filter by active status |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/pricing/tiers" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/crm/crm/pricing/tiers {#post-apimodulescrmcrmpricingtiers}

**Resumo:** Create Pricing Tier
**Descrição:** Create a new pricing tier.

Requires tenant owner or manager role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [PricingTierCreate](#pricingtiercreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PricingTierRead](#pricingtierread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/crm/crm/pricing/tiers" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/crm/crm/pricing/tiers/{tier_id} {#get-apimodulescrmcrmpricingtierstier-id}

**Resumo:** Get Pricing Tier
**Descrição:** Get a pricing tier by ID.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tier_id` | string | path | ✅ | The ID of the pricing tier to retrieve |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PricingTierRead](#pricingtierread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/pricing/tiers/{tier_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/crm/crm/pricing/tiers/{tier_id} {#put-apimodulescrmcrmpricingtierstier-id}

**Resumo:** Update Pricing Tier
**Descrição:** Update a pricing tier.

Requires tenant owner or manager role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tier_id` | string | path | ✅ | The ID of the pricing tier to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [PricingTierUpdate](#pricingtierupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PricingTierRead](#pricingtierread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/crm/crm/pricing/tiers/{tier_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
