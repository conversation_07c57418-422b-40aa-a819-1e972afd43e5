"""Financial Control Report Service."""

from typing import List, Optional, Dict, Any
from uuid import UUID
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from ..models.control_report import ControlReport, ReportType, ReportStatus
from ..schemas.control_report_schemas import ControlReportCreate, ReportParametersSchema


class ControlReportService:
    """Service for financial control reports."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_report(
        self, 
        report_data: ControlReportCreate, 
        tenant_id: UUID, 
        user_id: UUID
    ) -> ControlReport:
        """Create a new financial control report."""
        
        report = ControlReport(
            tenant_id=tenant_id,
            generated_by=user_id,
            **report_data.model_dump(exclude_unset=True)
        )
        
        self.db.add(report)
        await self.db.commit()
        await self.db.refresh(report)
        
        return report
    
    async def get_report(self, report_id: UUID, tenant_id: UUID) -> Optional[ControlReport]:
        """Get a report by ID."""
        
        query = select(ControlReport).where(
            and_(
                ControlReport.id == report_id,
                ControlReport.tenant_id == tenant_id
            )
        )
        
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def list_reports(self, tenant_id: UUID) -> List[ControlReport]:
        """List all reports for a tenant."""
        
        query = select(ControlReport).where(
            ControlReport.tenant_id == tenant_id
        ).order_by(ControlReport.generated_at.desc())
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def update_report_status(
        self, 
        report_id: UUID, 
        status: ReportStatus, 
        data: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None
    ) -> Optional[ControlReport]:
        """Update report status and data."""
        
        report = await self.get_report(report_id, None)  # Skip tenant check for internal use
        if not report:
            return None
        
        report.status = status
        if data:
            import json
            report.data = json.dumps(data)
        if error_message:
            report.error_message = error_message
        if status == ReportStatus.COMPLETED:
            report.completed_at = datetime.utcnow()
        
        await self.db.commit()
        await self.db.refresh(report)
        
        return report
    
    async def delete_report(self, report_id: UUID, tenant_id: UUID) -> bool:
        """Delete a report."""
        
        report = await self.get_report(report_id, tenant_id)
        if not report:
            return False
        
        await self.db.delete(report)
        await self.db.commit()
        
        return True
