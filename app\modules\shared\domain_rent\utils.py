"""Utility functions for Domain Rent module."""

import re  # noqa: E402
from typing import <PERSON><PERSON>, Optional


def parse_domain(domain_name: str) -> Tuple[str, str]:
    """Parse a domain name into name and TLD.

    Args:
        domain_name: Full domain name (e.g., "example.com")

    Returns:
        Tuple of (name, tld)

    Raises:
        ValueError: If the domain name is invalid
    """
    if not domain_name or "." not in domain_name:
        raise ValueError(f"Invalid domain name: {domain_name}")

    parts = domain_name.split(".")
    tld = parts[-1]
    name = ".".join(parts[:-1])

    return name, tld


def validate_domain_name(domain_name: str) -> bool:
    """Validate a domain name.

    Args:
        domain_name: Domain name to validate

    Returns:
        True if the domain name is valid, False otherwise
    """
    # Basic domain name validation
    pattern = r"^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$"  # noqa: E501
    return bool(re.match(pattern, domain_name))


def validate_nameserver(nameserver: str) -> bool:
    """Validate a nameserver hostname.

    Args:
        nameserver: Nameserver hostname to validate

    Returns:
        True if the nameserver is valid, False otherwise
    """
    # Basic hostname validation
    pattern = r"^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$"  # noqa: E501
    return bool(re.match(pattern, nameserver))


def format_domain_name(domain_name: str) -> str:
    """Format a domain name (lowercase).

    Args:
        domain_name: Domain name to format

    Returns:
        Formatted domain name
    """
    return domain_name.lower()


def get_domain_expiry_days(expiry_date: str, current_date: str) -> int:
    """Get the number of days until a domain expires.

    Args:
        expiry_date: Domain expiry date (ISO format)
        current_date: Current date (ISO format)

    Returns:
        Number of days until expiry
    """
    from datetime import datetime  # noqa: E402

    expiry = datetime.fromisoformat(expiry_date)
    current = datetime.fromisoformat(current_date)

    delta = expiry - current
    return delta.days
