# General - Payment Methods

**Categoria:** General
**Módulo:** Payment Methods
**Total de Endpoints:** 5
**Gerado em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [GET /api/modules/core/payments/methods](#get-apimodulescorepaymentsmethods) - List Payment Methods
- [POST /api/modules/core/payments/methods](#post-apimodulescorepaymentsmethods) - Create Payment Method
- [DELETE /api/modules/core/payments/methods/{method_id}](#delete-apimodulescorepaymentsmethodsmethod-id) - Delete Payment Method
- [GET /api/modules/core/payments/methods/{method_id}](#get-apimodulescorepaymentsmethodsmethod-id) - Get Payment Method
- [PUT /api/modules/core/payments/methods/{method_id}](#put-apimodulescorepaymentsmethodsmethod-id) - Update Payment Method

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### app__modules__core__payments__schemas__payment_method__PaymentMethodCreate

**Descrição:** Schema for creating a new payment method.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `method_type` | app__modules__core__payments__models__payment_method__PaymentMethodType | ✅ | - |
| `is_active` | boolean | ❌ | - |
| `is_default` | boolean | ❌ | - |
| `icon` | unknown | ❌ | - |
| `display_order` | integer | ❌ | - |
| `processor_method_id` | unknown | ❌ | - |
| `processor_config` | unknown | ❌ | - |
| `processor_id` | unknown | ❌ | - |

### app__modules__core__payments__schemas__payment_method__PaymentMethodRead

**Descrição:** Schema for reading a payment method.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `method_type` | app__modules__core__payments__models__payment_method__PaymentMethodType | ✅ | - |
| `is_active` | boolean | ❌ | - |
| `is_default` | boolean | ❌ | - |
| `icon` | unknown | ❌ | - |
| `display_order` | integer | ❌ | - |
| `processor_method_id` | unknown | ❌ | - |
| `processor_config` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `processor_id` | unknown | ❌ | - |

### app__modules__core__payments__schemas__payment_method__PaymentMethodUpdate

**Descrição:** Schema for updating an existing payment method.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | - |
| `method_type` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `is_default` | unknown | ❌ | - |
| `icon` | unknown | ❌ | - |
| `display_order` | unknown | ❌ | - |
| `processor_id` | unknown | ❌ | - |
| `processor_method_id` | unknown | ❌ | - |
| `processor_config` | unknown | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/modules/core/payments/methods {#get-apimodulescorepaymentsmethods}

**Resumo:** List Payment Methods
**Descrição:** List all payment methods for the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `is_active` | string | query | ❌ | Filter by active status |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/payments/methods" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/payments/methods {#post-apimodulescorepaymentsmethods}

**Resumo:** Create Payment Method
**Descrição:** Create a new payment method for the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [app__modules__core__payments__schemas__payment_method__PaymentMethodCreate](#app__modules__core__payments__schemas__payment_method__paymentmethodcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [app__modules__core__payments__schemas__payment_method__PaymentMethodRead](#app__modules__core__payments__schemas__payment_method__paymentmethodread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/payments/methods" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/core/payments/methods/{method_id} {#delete-apimodulescorepaymentsmethodsmethod-id}

**Resumo:** Delete Payment Method
**Descrição:** Delete an existing payment method.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `method_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/core/payments/methods/{method_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/payments/methods/{method_id} {#get-apimodulescorepaymentsmethodsmethod-id}

**Resumo:** Get Payment Method
**Descrição:** Get details of a specific payment method.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `method_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [app__modules__core__payments__schemas__payment_method__PaymentMethodRead](#app__modules__core__payments__schemas__payment_method__paymentmethodread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/payments/methods/{method_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/core/payments/methods/{method_id} {#put-apimodulescorepaymentsmethodsmethod-id}

**Resumo:** Update Payment Method
**Descrição:** Update an existing payment method.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `method_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [app__modules__core__payments__schemas__payment_method__PaymentMethodUpdate](#app__modules__core__payments__schemas__payment_method__paymentmethodupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [app__modules__core__payments__schemas__payment_method__PaymentMethodRead](#app__modules__core__payments__schemas__payment_method__paymentmethodread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/core/payments/methods/{method_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
