"""
SEO Schemas

Pydantic schemas for SEO validation and serialization.
"""

from .seo_schemas import (
    SEOMetaBase,
    SEOMetaCreate,
    SEOMetaUpdate,
    SEOMetaResponse,
    URLSlugBase,
    URLSlugCreate,
    URLSlugUpdate,
    URLSlugResponse,
    SEOAnalysisResponse,
    MetaTagsResponse,
    HreflangResponse
)
from .sitemap_schemas import (
    SitemapEntryBase,
    SitemapEntryCreate,
    SitemapEntryUpdate,
    SitemapEntryResponse,
    SitemapResponse,
    SitemapIndexResponse
)

__all__ = [
    "SEOMetaBase",
    "SEOMetaCreate", 
    "SEOMetaUpdate",
    "SEOMetaResponse",
    "URLSlugBase",
    "URLSlugCreate",
    "URLSlugUpdate", 
    "URLSlugResponse",
    "SEOAnalysisResponse",
    "MetaTagsResponse",
    "HreflangResponse",
    "SitemapEntryBase",
    "SitemapEntryCreate",
    "SitemapEntryUpdate",
    "SitemapEntryResponse",
    "SitemapResponse",
    "SitemapIndexResponse",
]
