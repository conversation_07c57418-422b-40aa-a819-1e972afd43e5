"""Shopping List models for tenant-specific shopping list management."""

import uuid
import enum
from datetime import date, datetime
from decimal import Decimal
from typing import TYPE_CHECKING, List, Optional
from sqlalchemy import (
    Column, String, ForeignKey, Text, Boolean, Index,
    DateTime, Date, Numeric, Integer
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base
from app.modules.core.functions.inventory.models.inventory_item import InventoryItem

# Use TYPE_CHECKING to handle circular dependencies for type hinting
if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.core.users.models.user import User
    from .shopping_list_category import ShoppingListCategory


class ShoppingListStatus(str, enum.Enum):
    """Enum for shopping list status."""

    DRAFT = "draft"
    ACTIVE = "active"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class ShoppingListItemStatus(str, enum.Enum):
    """Enum for shopping list item status."""

    PENDING = "pending"
    ORDERED = "ordered"
    RECEIVED = "received"
    CANCELLED = "cancelled"


class ShoppingListItemPriority(str, enum.Enum):
    """Enum for shopping list item priority."""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class ShoppingList(Base):
    """
    Model for shopping lists.

    Shopping lists are created for tenants to manage purchasing needs.
    Items are automatically added when inventory is low.
    """

    __tablename__ = "shopping_lists"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(
        UUID(as_uuid=True),
        ForeignKey("tenants.id"),
        nullable=False,
        index=True
    )

    # Shopping list identification
    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    status = Column(
        String(20),
        nullable=False,
        default=ShoppingListStatus.DRAFT.value
    )

    # Dates
    created_date = Column(Date, nullable=False, default=date.today)
    target_date = Column(Date, nullable=True)
    completed_date = Column(Date, nullable=True)

    # Budget information
    estimated_total = Column(
        Numeric(10, 2),
        nullable=False,
        default=Decimal('0.00')
    )
    actual_total = Column(
        Numeric(10, 2),
        nullable=False,
        default=Decimal('0.00')
    )

    # Additional information
    notes = Column(Text, nullable=True)
    auto_generated = Column(Boolean, default=False, nullable=False)

    # System fields
    created_by = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False
    )
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime,
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        nullable=False
    )

    # Relationships
    tenant = relationship(
        "app.modules.core.tenants.models.tenant.Tenant",
        viewonly=True
    )

    created_by_user = relationship(
        "app.modules.core.users.models.user.User",
        foreign_keys=[created_by],
        viewonly=True
    )

    # Shopping list items (one-to-many relationship)
    items = relationship(
        "ShoppingListItem",
        back_populates="shopping_list",
        cascade="all, delete-orphan"
    )

    # Table indexes for performance
    __table_args__ = (
        Index("ix_shopping_lists_tenant_status", "tenant_id", "status"),
        Index("ix_shopping_lists_tenant_date", "tenant_id", "created_date"),
        Index("ix_shopping_lists_auto_generated", "auto_generated"),
    )

    def __repr__(self):
        return (
            f"<ShoppingList(id={self.id}, "
            f"name='{self.name}', "
            f"status='{self.status}', "
            f"items_count={len(self.items) if self.items else 0})>"
        )


class ShoppingListItem(Base):
    """
    Model for shopping list items.

    Each shopping list can have multiple items with quantities, priorities, etc.
    """

    __tablename__ = "shopping_list_items"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    shopping_list_id = Column(
        UUID(as_uuid=True),
        ForeignKey("shopping_lists.id"),
        nullable=False,
        index=True
    )

    # Item details
    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    quantity_needed = Column(
        Numeric(10, 3),
        nullable=False,
        default=Decimal('1.000')
    )
    quantity_ordered = Column(
        Numeric(10, 3),
        nullable=True
    )
    quantity_received = Column(
        Numeric(10, 3),
        nullable=True
    )

    unit = Column(String(20), nullable=True)  # e.g., "pcs", "kg", "liters"
    estimated_unit_cost = Column(
        Numeric(10, 2),
        nullable=False,
        default=Decimal('0.00')
    )
    actual_unit_cost = Column(
        Numeric(10, 2),
        nullable=True
    )
    estimated_total_cost = Column(
        Numeric(10, 2),
        nullable=False,
        default=Decimal('0.00')
    )
    actual_total_cost = Column(
        Numeric(10, 2),
        nullable=True
    )

    # Status and priority
    status = Column(
        String(20),
        nullable=False,
        default=ShoppingListItemStatus.PENDING.value
    )
    priority = Column(
        String(20),
        nullable=False,
        default=ShoppingListItemPriority.MEDIUM.value
    )

    # Optional fields
    brand_preference = Column(String(100), nullable=True)
    supplier_preference = Column(String(100), nullable=True)
    notes = Column(Text, nullable=True)

    # Inventory item reference (if linked to existing inventory)
    inventory_item_id = Column(
        UUID(as_uuid=True),
        ForeignKey("inventory_items.id"),
        nullable=True,
        index=True
    )

    # Category reference (for organization)
    category_id = Column(
        UUID(as_uuid=True),
        ForeignKey("shopping_list_categories.id"),
        nullable=True,
        index=True
    )

    # Auto-generation info
    auto_generated = Column(Boolean, default=False, nullable=False)
    low_stock_threshold = Column(Numeric(10, 3), nullable=True)
    current_stock_level = Column(Numeric(10, 3), nullable=True)

    # Display order
    sort_order = Column(Integer, nullable=False, default=0)

    # Relationships
    shopping_list = relationship(
        "ShoppingList",
        back_populates="items"
    )

    inventory_item = relationship(
        "InventoryItem",
        viewonly=True
    )

    category = relationship(
        "ShoppingListCategory",
        back_populates="shopping_list_items"
    )

    # Table indexes for performance
    __table_args__ = (
        Index("ix_shopping_list_items_list_sort", "shopping_list_id", "sort_order"),
        Index("ix_shopping_list_items_status", "status"),
        Index("ix_shopping_list_items_priority", "priority"),
        Index("ix_shopping_list_items_inventory", "inventory_item_id"),
        Index("ix_shopping_list_items_category", "category_id"),
        Index("ix_shopping_list_items_auto_generated", "auto_generated"),
    )

    def __repr__(self):
        return (
            f"<ShoppingListItem(id={self.id}, "
            f"name='{self.name}', "
            f"quantity={self.quantity_needed}, "
            f"status='{self.status}', "
            f"priority='{self.priority}')>"
        )
