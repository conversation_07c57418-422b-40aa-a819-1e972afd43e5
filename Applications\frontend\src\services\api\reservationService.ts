import { apiClient } from '@/lib/api/client';
import type { ReservationFilters, ReservationStatus } from '@/types/reservations';

// Mock data for development/fallback
const mockReservations: Reservation[] = [
  {
    id: '1',
    tenant_id: 'tenant-1',
    table_id: 'table-5',
    guest_name: '<PERSON>',
    guest_phone: '+1234567890',
    guest_email: '<EMAIL>',
    reservation_number: 'RES-001',
    party_size: 4,
    reservation_date: new Date().toISOString(),
    duration_minutes: 90,
    status: 'confirmed',
    special_requests: 'Window table preferred',
    occasion: 'Anniversary dinner',
    notes: 'VIP customer',
    deposit_required: true,
    deposit_amount: 50,
    deposit_paid: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    table: { table_number: 'Table 5', capacity: 4 }
  },
  {
    id: '2',
    tenant_id: 'tenant-1',
    guest_name: '<PERSON>',
    guest_phone: '+1234567891',
    guest_email: '<EMAIL>',
    reservation_number: 'RES-002',
    party_size: 2,
    reservation_date: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(),
    duration_minutes: 60,
    status: 'pending',
    special_requests: 'Vegetarian options',
    deposit_required: false,
    deposit_amount: 0,
    deposit_paid: false,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: '3',
    tenant_id: 'tenant-1',
    table_id: 'table-8',
    guest_name: 'David Johnson',
    guest_phone: '+1234567892',
    reservation_number: 'RES-003',
    party_size: 6,
    reservation_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    duration_minutes: 120,
    status: 'confirmed',
    deposit_required: false,
    deposit_amount: 0,
    deposit_paid: false,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    table: { table_number: 'Table 8', capacity: 8 }
  },
  {
    id: '4',
    tenant_id: 'tenant-1',
    table_id: 'table-3',
    guest_name: 'Sarah Wilson',
    guest_phone: '+1234567893',
    guest_email: '<EMAIL>',
    reservation_number: 'RES-004',
    party_size: 3,
    reservation_date: new Date(Date.now() + 3 * 60 * 60 * 1000).toISOString(),
    duration_minutes: 75,
    status: 'seated',
    occasion: 'Business lunch',
    special_requests: 'Quiet table',
    deposit_required: false,
    deposit_amount: 0,
    deposit_paid: false,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    table: { table_number: 'Table 3', capacity: 4 }
  },
  {
    id: '5',
    tenant_id: 'tenant-1',
    guest_name: 'Michael Brown',
    guest_phone: '+1234567894',
    reservation_number: 'RES-005',
    party_size: 2,
    reservation_date: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
    duration_minutes: 90,
    status: 'completed',
    notes: 'Regular customer',
    deposit_required: false,
    deposit_amount: 0,
    deposit_paid: false,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: '6',
    tenant_id: 'tenant-1',
    guest_name: 'Emily Davis',
    guest_phone: '+1234567895',
    guest_email: '<EMAIL>',
    reservation_number: 'RES-006',
    party_size: 8,
    reservation_date: new Date(Date.now() + 5 * 60 * 60 * 1000).toISOString(),
    duration_minutes: 150,
    status: 'pending',
    occasion: 'Birthday party',
    special_requests: 'Birthday cake setup, balloons, special lighting, window table with view, vegetarian options for 3 guests',
    notes: 'VIP customer - regular visitor, prefers quiet corner, allergic to nuts, celebrating 25th birthday',
    deposit_required: true,
    deposit_amount: 75,
    deposit_paid: false,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: '7',
    tenant_id: 'tenant-1',
    guest_name: 'Robert Taylor',
    guest_phone: '+1234567896',
    reservation_number: 'RES-007',
    party_size: 4,
    reservation_date: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    duration_minutes: 90,
    status: 'no_show',
    notes: 'Did not show up',
    deposit_required: false,
    deposit_amount: 0,
    deposit_paid: false,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: '8',
    tenant_id: 'tenant-1',
    table_id: 'table-2',
    guest_name: 'Lisa Anderson',
    guest_phone: '+1234567897',
    guest_email: '<EMAIL>',
    reservation_number: 'RES-008',
    party_size: 2,
    reservation_date: new Date(Date.now() + 1 * 60 * 60 * 1000).toISOString(),
    duration_minutes: 60,
    status: 'confirmed',
    occasion: 'Date night',
    special_requests: 'Romantic setting',
    deposit_required: true,
    deposit_amount: 25,
    deposit_paid: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    table: { table_number: 'Table 2', capacity: 2 }
  },
  {
    id: '9',
    tenant_id: 'tenant-1',
    guest_name: 'Carlos Rodriguez',
    guest_phone: '+1234567898',
    reservation_number: 'RES-009',
    party_size: 1,
    reservation_date: new Date(Date.now() + 4 * 60 * 60 * 1000).toISOString(),
    duration_minutes: 45,
    status: 'confirmed',
    notes: 'Business meeting',
    deposit_required: false,
    deposit_amount: 0,
    deposit_paid: false,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: '10',
    tenant_id: 'tenant-1',
    table_id: 'table-6',
    guest_name: 'Jennifer Kim',
    guest_phone: '+1234567899',
    guest_email: '<EMAIL>',
    reservation_number: 'RES-010',
    party_size: 5,
    reservation_date: new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString(),
    duration_minutes: 120,
    status: 'confirmed',
    occasion: 'Family reunion',
    special_requests: 'High chair needed, gluten-free menu',
    notes: 'Celebrating grandmother\'s 80th birthday, please prepare special dessert',
    deposit_required: true,
    deposit_amount: 50,
    deposit_paid: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    table: { table_number: 'Table 6', capacity: 6 }
  }
];

const mockBlacklist: CustomerBlacklist[] = [
  {
    id: '1',
    tenant_id: 'tenant-1',
    guest_name: 'Problem Customer',
    guest_phone: '+1234567999',
    guest_email: '<EMAIL>',
    blacklist_type: 'suspended',
    reason: 'Repeated no-shows',
    suspension_start_date: new Date().toISOString(),
    suspension_end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    require_deposit: true,
    deposit_amount: 100,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

export interface Reservation {
  id: string;
  tenant_id: string;
  table_id?: string;
  customer_id?: string;

  // Guest information (for non-registered customers)
  guest_name?: string;
  guest_email?: string;
  guest_phone?: string;

  // Reservation details
  reservation_number: string;
  party_size: number;
  reservation_date: string;
  end_time?: string;
  duration_minutes?: number;
  status: 'pending' | 'confirmed' | 'seated' | 'completed' | 'cancelled' | 'no_show';

  // Additional information
  special_requests?: string;
  occasion?: string;
  notes?: string;

  // Deposit information
  deposit_required: boolean;
  deposit_amount?: number;
  deposit_paid: boolean;
  payment_id?: string;

  // Timestamps
  created_at: string;
  updated_at: string;

  // Related data
  table?: any;
  customer?: any;
}

export interface ReservationCreate {
  table_id?: string;
  customer_id?: string;

  // Guest information
  guest_name?: string;
  guest_email?: string;
  guest_phone?: string;

  // Reservation details
  party_size: number;
  reservation_date: string;
  duration_minutes?: number;
  special_requests?: string;
  occasion?: string;
  notes?: string;

  // Deposit information
  deposit_required?: boolean;
  deposit_amount?: number;
}

export interface ReservationUpdate {
  table_id?: string;
  party_size?: number;
  reservation_date?: string;
  duration_minutes?: number;
  status?: Reservation['status'];
  special_requests?: string;
  occasion?: string;
  notes?: string;
  deposit_required?: boolean;
  deposit_amount?: number;
}

export interface AvailableTablesQuery {
  reservation_date: string;
  duration_minutes?: number;
  party_size: number;
  layout_id?: string;
}

export interface CustomerBlacklist {
  id: string;
  tenant_id: string;
  customer_id?: string;
  guest_name?: string;
  guest_email?: string;
  guest_phone?: string;
  blacklist_type: 'suspended' | 'banned';
  reason?: string;
  suspension_start_date?: string;
  suspension_end_date?: string;
  require_deposit: boolean;
  deposit_amount?: number;
  created_at: string;
  updated_at: string;
}

export interface CustomerBlacklistCreate {
  customer_id?: string;
  guest_name?: string;
  guest_email?: string;
  guest_phone?: string;
  blacklist_type?: 'suspended' | 'banned';
  reason?: string;
  suspension_start_date?: string;
  suspension_end_date?: string;
  require_deposit?: boolean;
  deposit_amount?: number;
}

class ReservationService {
  private baseUrl = '/api/modules/restaurants/reservations';

  // Reservation CRUD operations
  async getReservations(filters?: ReservationFilters): Promise<Reservation[]> {
    try {
      const params = new URLSearchParams();

      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            params.append(key, value.toString());
          }
        });
      }

      const url = `${this.baseUrl}/reservations/${params.toString() ? `?${params.toString()}` : ''}`;
      const response = await apiClient.get<Reservation[]>(url);
      return response.data;
    } catch (error) {
      console.warn('API not available, using mock data for reservations:', error);

      // Apply filters to mock data
      let filteredReservations = [...mockReservations];

      if (filters?.status) {
        filteredReservations = filteredReservations.filter(r => r.status === filters.status);
      }

      if (filters?.date_from) {
        const fromDate = new Date(filters.date_from);
        filteredReservations = filteredReservations.filter(r =>
          new Date(r.reservation_date) >= fromDate
        );
      }

      if (filters?.date_to) {
        const toDate = new Date(filters.date_to);
        filteredReservations = filteredReservations.filter(r =>
          new Date(r.reservation_date) <= toDate
        );
      }

      if (filters?.table_id) {
        filteredReservations = filteredReservations.filter(r => r.table_id === filters.table_id);
      }

      if (filters?.customer_id) {
        filteredReservations = filteredReservations.filter(r => r.customer_id === filters.customer_id);
      }

      // Apply pagination
      const skip = filters?.skip || 0;
      const limit = filters?.limit || 50;

      return filteredReservations.slice(skip, skip + limit);
    }
  }

  async getReservation(id: string): Promise<Reservation> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/reservations/${id}`);
      return response.data;
    } catch (error) {
      console.warn('API not available, using mock data for single reservation:', error);
      const reservation = mockReservations.find(r => r.id === id);
      if (!reservation) {
        throw new Error('Reservation not found');
      }
      return reservation;
    }
  }

  async createReservation(data: ReservationCreate): Promise<Reservation> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/reservations/`, data);
      return response.data;
    } catch (error) {
      console.warn('API not available, simulating reservation creation:', error);

      // Simulate creating a new reservation
      const newReservation: Reservation = {
        id: `mock-${Date.now()}`,
        tenant_id: 'tenant-1',
        reservation_number: `RES-${String(mockReservations.length + 1).padStart(3, '0')}`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        status: 'pending',
        deposit_paid: false,
        deposit_required: data.deposit_required ?? false,
        ...data
      };

      mockReservations.push(newReservation);
      return newReservation;
    }
  }

  async updateReservation(id: string, data: ReservationUpdate): Promise<Reservation> {
    try {
      const response = await apiClient.put(`${this.baseUrl}/reservations/${id}`, data);
      return response.data;
    } catch (error) {
      console.warn('API not available, simulating reservation update:', error);

      const index = mockReservations.findIndex(r => r.id === id);
      if (index === -1) {
        throw new Error('Reservation not found');
      }

      mockReservations[index] = {
        ...mockReservations[index],
        ...data,
        updated_at: new Date().toISOString()
      };

      return mockReservations[index];
    }
  }

  async deleteReservation(id: string): Promise<void> {
    try {
      await apiClient.delete(`${this.baseUrl}/reservations/${id}`);
    } catch (error) {
      console.warn('API not available, simulating reservation deletion:', error);

      const index = mockReservations.findIndex(r => r.id === id);
      if (index === -1) {
        throw new Error('Reservation not found');
      }

      mockReservations.splice(index, 1);
    }
  }

  async updateReservationStatus(id: string, status: Reservation['status']): Promise<Reservation> {
    try {
      const response = await apiClient.patch(`${this.baseUrl}/reservations/${id}/status`, { status });
      return response.data;
    } catch (error) {
      console.warn('API not available, simulating status update:', error);

      const index = mockReservations.findIndex(r => r.id === id);
      if (index === -1) {
        throw new Error('Reservation not found');
      }

      mockReservations[index] = {
        ...mockReservations[index],
        status,
        updated_at: new Date().toISOString()
      };

      return mockReservations[index];
    }
  }

  // Table availability
  async findAvailableTables(query: AvailableTablesQuery): Promise<any[]> {
    try {
      const params = new URLSearchParams();
      Object.entries(query).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString());
        }
      });

      const response = await apiClient.get(`${this.baseUrl}/reservations/available-tables?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.warn('API not available, using mock available tables:', error);

      // Return mock available tables
      return [
        { id: 'table-1', table_number: 'Table 1', capacity: 2 },
        { id: 'table-2', table_number: 'Table 2', capacity: 4 },
        { id: 'table-3', table_number: 'Table 3', capacity: 6 },
        { id: 'table-4', table_number: 'Table 4', capacity: 8 }
      ].filter(table => table.capacity >= query.party_size);
    }
  }

  // Blacklist operations
  async getBlacklist(): Promise<CustomerBlacklist[]> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/blacklist/`);
      return response.data;
    } catch (error) {
      console.warn('API not available, using mock blacklist data:', error);
      return [...mockBlacklist];
    }
  }

  async getBlacklistEntry(id: string): Promise<CustomerBlacklist> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/blacklist/${id}`);
      return response.data;
    } catch (error) {
      console.warn('API not available, using mock blacklist entry:', error);
      const entry = mockBlacklist.find(e => e.id === id);
      if (!entry) {
        throw new Error('Blacklist entry not found');
      }
      return entry;
    }
  }

  async createBlacklistEntry(data: CustomerBlacklistCreate): Promise<CustomerBlacklist> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/blacklist/`, data);
      return response.data;
    } catch (error) {
      console.warn('API not available, simulating blacklist entry creation:', error);

      const newEntry: CustomerBlacklist = {
        id: `mock-bl-${Date.now()}`,
        tenant_id: 'tenant-1',
        blacklist_type: 'suspended',
        require_deposit: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        ...data
      };

      mockBlacklist.push(newEntry);
      return newEntry;
    }
  }

  async updateBlacklistEntry(id: string, data: Partial<CustomerBlacklistCreate>): Promise<CustomerBlacklist> {
    try {
      const response = await apiClient.put(`${this.baseUrl}/blacklist/${id}`, data);
      return response.data;
    } catch (error) {
      console.warn('API not available, simulating blacklist entry update:', error);

      const index = mockBlacklist.findIndex(e => e.id === id);
      if (index === -1) {
        throw new Error('Blacklist entry not found');
      }

      mockBlacklist[index] = {
        ...mockBlacklist[index],
        ...data,
        updated_at: new Date().toISOString()
      };

      return mockBlacklist[index];
    }
  }

  async deleteBlacklistEntry(id: string): Promise<void> {
    try {
      await apiClient.delete(`${this.baseUrl}/blacklist/${id}`);
    } catch (error) {
      console.warn('API not available, simulating blacklist entry deletion:', error);

      const index = mockBlacklist.findIndex(e => e.id === id);
      if (index === -1) {
        throw new Error('Blacklist entry not found');
      }

      mockBlacklist.splice(index, 1);
    }
  }

  // Utility methods
  async getReservationsByDate(date: string): Promise<Reservation[]> {
    return this.getReservations({
      date_from: date,
      date_to: date,
    });
  }

  async getReservationsByDateRange(dateFrom: string, dateTo: string): Promise<Reservation[]> {
    return this.getReservations({
      date_from: dateFrom,
      date_to: dateTo,
    });
  }

  async getReservationsByTable(tableId: string): Promise<Reservation[]> {
    return this.getReservations({
      table_id: tableId,
    });
  }

  async getReservationsByCustomer(customerId: string): Promise<Reservation[]> {
    return this.getReservations({
      customer_id: customerId,
    });
  }

  async getReservationsByStatus(status: Reservation['status']): Promise<Reservation[]> {
    return this.getReservations({
      status,
    });
  }
}

export const reservationService = new ReservationService();
export default reservationService;

export type { ReservationFilters, ReservationStatus } from '@/types/reservations';
