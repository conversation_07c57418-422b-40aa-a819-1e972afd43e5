"""
Review Service

Serviço principal para gerenciamento de reviews.
"""

from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Tuple
from uuid import UUID

from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.modules.core.functions.reviews.models.review import (
    Review,
    ReviewStatus
)
from app.modules.core.functions.reviews.schemas.review_schemas import (
    ReviewCreate,
    ReviewResponse,
    ReviewListResponse,
    ReviewStatsResponse,
    ReviewFilterParams
)


class ReviewService:
    """Serviço para gerenciamento de reviews"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_review(
        self,
        review_data: ReviewCreate,
        user_id: UUID
    ) -> ReviewResponse:
        """Criar uma nova review genérica"""
        
        # Verificar se o usuário já fez review desta entidade
        if review_data.entity_type and review_data.entity_id:
            existing_review = await self._get_user_entity_review(
                user_id, review_data.entity_type, review_data.entity_id
            )
            if existing_review:
                raise ValueError("User already reviewed this entity")
        
        # Verificar se é compra verificada (se order_id fornecido)
        is_verified_purchase = False
        if review_data.order_id:
            is_verified_purchase = await self._verify_purchase(
                user_id, review_data.entity_id, review_data.order_id
            )
        
        # Criar review
        review = Review(
            user_id=user_id,
            rating=review_data.rating,
            comment=review_data.comment,
            review_type=review_data.review_type,
            entity_type=review_data.entity_type,
            entity_id=review_data.entity_id,
            tenant_id=review_data.tenant_id,
            order_id=review_data.order_id,
            verified_purchase=is_verified_purchase,
            status=ReviewStatus.ACTIVE
        )
        
        self.db.add(review)
        await self.db.commit()
        await self.db.refresh(review)
        
        return ReviewResponse.from_orm(review)
    
    async def get_reviews_by_entity(
        self,
        entity_type: str,
        entity_id: UUID,
        filters: Optional[ReviewFilterParams] = None,
        page: int = 1,
        page_size: int = 20
    ) -> ReviewListResponse:
        """Buscar reviews de uma entidade específica"""
        
        # Construir query base
        query = select(Review).where(
            and_(
                Review.entity_type == entity_type,
                Review.entity_id == entity_id
            )
        )
        
        # Aplicar filtros se fornecidos
        if filters:
            query = self._apply_filters(query, filters)
            query = self._apply_sorting(query, filters.sort_by, filters.sort_order)
        else:
            query = query.order_by(desc(Review.created_at))
        
        # Contar total
        count_query = select(func.count()).select_from(query.subquery())
        total_count = await self.db.scalar(count_query)
        
        # Aplicar paginação
        offset = (page - 1) * page_size
        query = query.offset(offset).limit(page_size)
        
        # Executar query com relacionamentos
        query = query.options(selectinload(Review.user))
        
        result = await self.db.execute(query)
        reviews = result.scalars().all()
        
        # Converter para response
        review_responses = [ReviewResponse.from_orm(review) for review in reviews]
        
        return ReviewListResponse(
            reviews=review_responses,
            total=total_count,
            page=page,
            per_page=page_size,
            pages=(total_count + page_size - 1) // page_size,
            has_next=page * page_size < total_count,
            has_prev=page > 1
        )
    
    async def get_reviews_by_tenant(
        self,
        tenant_id: UUID,
        filters: Optional[ReviewFilterParams] = None,
        page: int = 1,
        page_size: int = 20
    ) -> ReviewListResponse:
        """Buscar reviews de um tenant"""
        
        # Construir query base
        query = select(Review).where(Review.tenant_id == tenant_id)
        
        # Aplicar filtros se fornecidos
        if filters:
            query = self._apply_filters(query, filters)
            query = self._apply_sorting(query, filters.sort_by, filters.sort_order)
        else:
            query = query.order_by(desc(Review.created_at))
        
        # Contar total
        count_query = select(func.count()).select_from(query.subquery())
        total_count = await self.db.scalar(count_query)
        
        # Aplicar paginação
        offset = (page - 1) * page_size
        query = query.offset(offset).limit(page_size)
        
        # Executar query com relacionamentos
        query = query.options(selectinload(Review.user))
        
        result = await self.db.execute(query)
        reviews = result.scalars().all()
        
        # Converter para response
        review_responses = [ReviewResponse.from_orm(review) for review in reviews]
        
        return ReviewListResponse(
            reviews=review_responses,
            total=total_count,
            page=page,
            per_page=page_size,
            pages=(total_count + page_size - 1) // page_size,
            has_next=page * page_size < total_count,
            has_prev=page > 1
        )
    
    async def get_review_stats(
        self,
        entity_type: Optional[str] = None,
        entity_id: Optional[UUID] = None,
        tenant_id: Optional[UUID] = None
    ) -> ReviewStatsResponse:
        """Obter estatísticas de reviews genéricas"""
        
        # Construir query base
        query = select(Review)
        conditions = []
        
        if entity_type and entity_id:
            conditions.extend([
                Review.entity_type == entity_type,
                Review.entity_id == entity_id
            ])
        elif tenant_id:
            conditions.append(Review.tenant_id == tenant_id)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # Executar query
        result = await self.db.execute(query)
        reviews = result.scalars().all()
        
        if not reviews:
            return ReviewStatsResponse(
                entity_type=entity_type,
                entity_id=entity_id,
                tenant_id=tenant_id,
                total_reviews=0,
                average_rating=Decimal('0.0'),
                rating_distribution={1: 0, 2: 0, 3: 0, 4: 0, 5: 0}
            )
        
        # Calcular estatísticas
        total_reviews = len(reviews)
        total_rating = sum(review.rating for review in reviews)
        average_rating = Decimal(str(total_rating / total_reviews))
        
        # Distribuição por rating
        rating_distribution = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0}
        for review in reviews:
            rating_distribution[review.rating] += 1
        
        return ReviewStatsResponse(
            entity_type=entity_type,
            entity_id=entity_id,
            tenant_id=tenant_id,
            total_reviews=total_reviews,
            average_rating=average_rating,
            rating_distribution=rating_distribution
        )
    
    async def get_review_by_id(
        self,
        review_id: UUID
    ) -> Optional[ReviewResponse]:
        """Buscar review por ID"""
        
        query = select(Review).where(Review.id == review_id).options(
            selectinload(Review.user)
        )
        
        review = await self.db.scalar(query)
        
        if not review:
            return None
        
        return ReviewResponse.from_orm(review)
    
    async def delete_review(
        self,
        review_id: UUID,
        user_id: UUID
    ) -> bool:
        """Deletar review (apenas pelo próprio usuário)"""
        
        query = select(Review).where(
            and_(
                Review.id == review_id,
                Review.user_id == user_id
            )
        )
        
        review = await self.db.scalar(query)
        
        if not review:
            return False
        
        # Deletar review
        await self.db.delete(review)
        await self.db.commit()
        
        return True
    
    # Métodos privados auxiliares
    
    async def _get_user_entity_review(
        self,
        user_id: UUID,
        entity_type: str,
        entity_id: UUID
    ) -> Optional[Review]:
        """Verificar se usuário já fez review desta entidade"""
        query = select(Review).where(
            and_(
                Review.user_id == user_id,
                Review.entity_type == entity_type,
                Review.entity_id == entity_id
            )
        )
        return await self.db.scalar(query)
    
    async def _verify_purchase(
        self,
        user_id: UUID,
        entity_id: Optional[UUID],
        order_id: UUID
    ) -> bool:
        """Verificar se é uma compra válida"""
        # TODO: Implementar verificação com módulo de orders
        # Por enquanto, retorna True se order_id foi fornecido
        return order_id is not None
    
    def _apply_filters(self, query, filters: ReviewFilterParams):
        """Aplicar filtros à query"""
        
        if filters.rating:
            query = query.where(Review.rating == filters.rating)
        
        if filters.verified_only and hasattr(Review, 'verified_purchase'):
            query = query.where(Review.verified_purchase == True)
        
        if filters.status and hasattr(Review, 'status'):
            query = query.where(Review.status == filters.status)
        
        return query
    
    def _apply_sorting(self, query, sort_by: str, sort_order: str):
        """Aplicar ordenação à query"""
        
        if sort_by == "created_at":
            order_field = Review.created_at
        elif sort_by == "rating":
            order_field = Review.rating
        elif sort_by == "helpful_count" and hasattr(Review, 'helpful_count'):
            order_field = Review.helpful_count
        else:
            order_field = Review.created_at
        
        if sort_order == "desc":
            query = query.order_by(desc(order_field))
        else:
            query = query.order_by(order_field)
        
        return query