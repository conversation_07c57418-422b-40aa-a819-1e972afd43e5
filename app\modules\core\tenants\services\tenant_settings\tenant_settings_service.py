"""
Service for managing tenant settings with comprehensive CRUD operations.
"""

import uuid
from typing import List, Optional, Dict, Any
from decimal import Decimal

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update as sqlalchemy_update, and_

from app.modules.core.tenants.models.tenant_settings import TenantSettings
from app.modules.core.tenants.models.tenant_payment_method_config import (
    TenantPaymentMethodConfig,
)
from app.modules.core.tenants.schemas.tenant_settings import (
    TenantSettingsCreate,
    TenantSettingsUpdate,
    BusinessSettingsUpdate,
    OperatingHoursUpdate,
    LanguageSettingsUpdate,
    LoyaltySettingsUpdate,
    LocationSettingsUpdate,
    TaxSettingsUpdate,
    CurrencySettingsUpdate,
    SocialMediaSettingsUpdate,
)
from app.modules.core.tenants.models.tenant import Tenant
from app.modules.core.tenants.schemas.tenant_settings.payment_method_config import (
    TenantPaymentMethodConfigCreate,
    TenantPaymentMethodConfigUpdate,
)
from app.modules.core.tenants.schemas.tenant_settings.responses import (
    LanguageOption,
    CurrencyOption,
    CurrencyConfiguration,
    SubscriptionInfo,
)
from app.modules.core.tenants.services.tenant_settings.tenant_settings_integration_service import (
    TenantSettingsIntegrationService,
)
from app.modules.core.payments.models.payment_method import PaymentMethod
from app.modules.core.i18n.models.language import Language
from app.core.exceptions import NotFoundError, BusinessLogicError


class TenantSettingsService:
    """Service for managing tenant settings and configurations."""

    def __init__(self):
        self.integration_service = TenantSettingsIntegrationService()

    async def get_settings(
        self, db: AsyncSession, tenant_id: uuid.UUID
    ) -> Optional[TenantSettings]:
        """
        Get tenant settings by tenant ID.
        Creates default settings if none exist.
        Includes tenant_slug from RestaurantTenantSettings if available.
        """
        result = await db.execute(
            select(TenantSettings).where(TenantSettings.tenant_id == tenant_id)
        )
        settings = result.scalars().first()

        if not settings:
            # Create default settings if none exist
            settings = await self.create_default_settings(db, tenant_id)

        # Try to get tenant_slug from RestaurantTenantSettings
        try:
            from app.modules.tenants.restaurants.tenant_settings.models.restaurant_tenant_settings import RestaurantTenantSettings
            restaurant_result = await db.execute(
                select(RestaurantTenantSettings).where(RestaurantTenantSettings.tenant_id == tenant_id)
            )
            restaurant_settings = restaurant_result.scalars().first()

            if restaurant_settings and restaurant_settings.tenant_slug:
                # Add tenant_slug to the settings object dynamically
                settings.tenant_slug = restaurant_settings.tenant_slug
            else:
                settings.tenant_slug = None
        except Exception as e:
            # If restaurant settings don't exist or there's an error, set tenant_slug to None
            settings.tenant_slug = None

        return settings

    async def create_default_settings(
        self, db: AsyncSession, tenant_id: uuid.UUID
    ) -> TenantSettings:
        """
        Create default tenant settings for a new tenant.
        """
        default_currency_config = {
            "default_currency": "BRL",
            "enabled_currencies": ["BRL"],
            "exchange_rates": {"BRL": 1.0},
            "auto_update_rates": False,
            "rate_update_frequency": "manual",
            "display_currency": "BRL",
            "currency_formatting": {
                "BRL": {
                    "decimal_separator": ",",
                    "thousands_separator": ".",
                    "symbol_position": "left",
                    "symbol_spacing": True
                }
            }
        }

        # Default operating hours with examples of all three schedule types
        default_operating_hours = {
            "monday": {
                "is_open": True,
                "service_hours": [
                    {
                        "id": "service_lunch",
                        "open": "11:00",
                        "close": "15:00",
                        "type": "service",
                        "label": "Lunch Service"
                    },
                    {
                        "id": "service_dinner",
                        "open": "18:00",
                        "close": "23:00",
                        "type": "service",
                        "label": "Dinner Service"
                    }
                ],
                "break_periods": [
                    {
                        "id": "break_afternoon",
                        "open": "15:00",
                        "close": "18:00",
                        "type": "break",
                        "label": "Kitchen Break"
                    }
                ],
                "happy_hour": [
                    {
                        "id": "happy_evening",
                        "open": "17:00",
                        "close": "19:00",
                        "type": "happy_hour",
                        "label": "Happy Hour"
                    }
                ]
            },
            "tuesday": {
                "is_open": True,
                "service_hours": [
                    {
                        "id": "service_all_day",
                        "open": "11:00",
                        "close": "23:00",
                        "type": "service",
                        "label": "All Day Service"
                    }
                ],
                "break_periods": [],
                "happy_hour": [
                    {
                        "id": "happy_afternoon",
                        "open": "16:00",
                        "close": "18:00",
                        "type": "happy_hour",
                        "label": "Afternoon Happy Hour"
                    },
                    {
                        "id": "happy_late",
                        "open": "21:00",
                        "close": "22:30",
                        "type": "happy_hour",
                        "label": "Late Night Happy Hour"
                    }
                ]
            },
            "wednesday": {"is_open": True, "service_hours": [], "break_periods": [], "happy_hour": []},
            "thursday": {"is_open": True, "service_hours": [], "break_periods": [], "happy_hour": []},
            "friday": {"is_open": True, "service_hours": [], "break_periods": [], "happy_hour": []},
            "saturday": {"is_open": True, "service_hours": [], "break_periods": [], "happy_hour": []},
            "sunday": {"is_open": False, "service_hours": [], "break_periods": [], "happy_hour": []}
        }

        # Default address example with contact information
        default_address = {
            "street": "Rua das Flores",
            "number": "123",
            "complement": "",
            "city": "São Paulo",
            "state": "SP",
            "zipCode": "01234-567",
            "country": "BR",
            "latitude": -23.5505,
            "longitude": -46.6333,
            "phone": "+55 11 99999-9999",
            "phone_is_whatsapp": True,
            "phone_secondary": "+55 11 3333-3333",
            "phone_secondary_is_whatsapp": False,
            "fax": "+55 11 3333-3334"
        }

        settings = TenantSettings(
            tenant_id=tenant_id,
            default_currency="BRL",
            currency_config=default_currency_config,
            timezone="America/Sao_Paulo",
            multi_language_enabled=False,
            default_language="pt-BR",
            loyalty_enabled=False,
            base_tax_rate=Decimal("0.0000"),
            tax_calculation_method="incremental",
            subscription_status="active",
            operating_hours=default_operating_hours,
            address=default_address,
        )

        db.add(settings)
        await db.commit()
        await db.refresh(settings)
        return settings

    async def update_settings(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID,
        settings_data: TenantSettingsUpdate,
    ) -> TenantSettings:
        """
        Update tenant settings with comprehensive validation.
        Handles tenant_slug updates by delegating to RestaurantTenantSettings.
        """
        settings = await self.get_settings(db, tenant_id)
        if not settings:
            raise NotFoundError(f"Settings not found for tenant {tenant_id}")

        # Get update data excluding unset fields
        update_data = settings_data.model_dump(exclude_unset=True)

        # Handle tenant_slug separately (it belongs to RestaurantTenantSettings)
        tenant_slug_value = None
        if "tenant_slug" in update_data:
            tenant_slug_value = update_data.pop("tenant_slug")

        # Validate currency configuration if being updated
        if "currency_config" in update_data:
            await self.integration_service._validate_currency_config(update_data["currency_config"])

        # Update fields in TenantSettings
        for field, value in update_data.items():
            setattr(settings, field, value)

        db.add(settings)
        await db.commit()
        await db.refresh(settings)

        # Update tenant_slug in RestaurantTenantSettings if provided
        if tenant_slug_value is not None:
            try:
                from app.modules.tenants.restaurants.tenant_settings.services.restaurant_tenant_settings_service import restaurant_tenant_settings_service
                from app.modules.tenants.restaurants.tenant_settings.schemas.restaurant_tenant_settings import RestaurantTenantSettingsUpdate

                restaurant_update = RestaurantTenantSettingsUpdate(tenant_slug=tenant_slug_value)
                await restaurant_tenant_settings_service.async_update_with_validation(
                    db, tenant_id, restaurant_update
                )

                # Update the settings object with the new tenant_slug for response
                settings.tenant_slug = tenant_slug_value
            except Exception as e:
                # If restaurant settings update fails, log but don't fail the main update
                print(f"Warning: Failed to update tenant_slug in restaurant settings: {e}")

        return settings

    async def update_business_settings(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID,
        business_data: BusinessSettingsUpdate,
    ) -> TenantSettings:
        """Update business information settings."""
        settings = await self.get_settings(db, tenant_id)
        if not settings:
            raise NotFoundError(f"Settings not found for tenant {tenant_id}")

        update_data = business_data.model_dump(exclude_unset=True)
        
        for field, value in update_data.items():
            setattr(settings, field, value)

        db.add(settings)
        await db.commit()
        await db.refresh(settings)
        return settings

    async def update_operating_hours(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID,
        hours_data: OperatingHoursUpdate,
    ) -> TenantSettings:
        """Update operating hours configuration with support for new time slot structure."""
        settings = await self.get_settings(db, tenant_id)
        if not settings:
            raise NotFoundError(f"Settings not found for tenant {tenant_id}")

        # Validate operating hours format
        await self.integration_service._validate_operating_hours(hours_data.operating_hours)

        # Convert legacy format to new format if needed
        normalized_hours = await self._normalize_operating_hours(hours_data.operating_hours)

        settings.operating_hours = normalized_hours
        db.add(settings)
        await db.commit()
        await db.refresh(settings)
        return settings

    async def _normalize_operating_hours(self, operating_hours: Dict[str, Any]) -> Dict[str, Any]:
        """Convert legacy operating hours format to new structure with service_hours, break_periods, and happy_hour."""
        normalized = {}

        for day, config in operating_hours.items():
            if not isinstance(config, dict):
                continue

            normalized_day = {
                "is_open": config.get("is_open", False),
                "service_hours": [],
                "break_periods": [],
                "happy_hour": []
            }

            # If already in new format, keep as is
            if any(key in config for key in ["service_hours", "break_periods", "happy_hour"]):
                normalized_day.update({
                    "service_hours": config.get("service_hours", []),
                    "break_periods": config.get("break_periods", []),
                    "happy_hour": config.get("happy_hour", [])
                })

            # Convert legacy slots format
            elif "slots" in config and isinstance(config["slots"], list):
                for slot in config["slots"]:
                    if isinstance(slot, dict) and "open" in slot and "close" in slot:
                        normalized_slot = {
                            "id": slot.get("id", f"slot_{uuid.uuid4().hex[:8]}"),
                            "open": slot["open"],
                            "close": slot["close"],
                            "type": "break" if slot.get("isBreak", False) else "service",
                            "label": slot.get("label", "Service Hours" if not slot.get("isBreak", False) else "Break Period")
                        }

                        if slot.get("isBreak", False):
                            normalized_day["break_periods"].append(normalized_slot)
                        else:
                            normalized_day["service_hours"].append(normalized_slot)

            # Convert very old legacy format
            elif config.get("is_open", False) and "open_time" in config and "close_time" in config:
                normalized_slot = {
                    "id": f"legacy_{uuid.uuid4().hex[:8]}",
                    "open": config["open_time"],
                    "close": config["close_time"],
                    "type": "service",
                    "label": "Service Hours"
                }
                normalized_day["service_hours"].append(normalized_slot)

            # Handle very old format with just open/close
            elif config.get("is_open", False) and "open" in config and "close" in config:
                normalized_slot = {
                    "id": f"legacy_{uuid.uuid4().hex[:8]}",
                    "open": config["open"],
                    "close": config["close"],
                    "type": "service",
                    "label": "Service Hours"
                }
                normalized_day["service_hours"].append(normalized_slot)

            normalized[day] = normalized_day

        return normalized

    async def update_language_settings(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID,
        language_data: LanguageSettingsUpdate,
    ) -> TenantSettings:
        """Update language settings with validation."""
        settings = await self.get_settings(db, tenant_id)
        if not settings:
            raise NotFoundError(f"Settings not found for tenant {tenant_id}")

        update_data = language_data.model_dump(exclude_unset=True)

        # Validate available languages if provided
        if "available_languages" in update_data:
            await self.integration_service._validate_available_languages(
                db, update_data["available_languages"]
            )

        for field, value in update_data.items():
            setattr(settings, field, value)

        db.add(settings)
        await db.commit()
        await db.refresh(settings)
        return settings

    async def update_loyalty_settings(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID,
        loyalty_data: LoyaltySettingsUpdate,
    ) -> TenantSettings:
        """Update loyalty system settings."""
        settings = await self.get_settings(db, tenant_id)
        if not settings:
            raise NotFoundError(f"Settings not found for tenant {tenant_id}")

        update_data = loyalty_data.model_dump(exclude_unset=True)

        # Validate loyalty configuration if provided
        if "loyalty_config" in update_data:
            await self.integration_service._validate_loyalty_config(update_data["loyalty_config"])

        for field, value in update_data.items():
            setattr(settings, field, value)

        db.add(settings)
        await db.commit()
        await db.refresh(settings)
        return settings

    async def update_location_settings(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID,
        location_data: LocationSettingsUpdate,
    ) -> TenantSettings:
        """Update location and address settings."""
        settings = await self.get_settings(db, tenant_id)
        if not settings:
            raise NotFoundError(f"Settings not found for tenant {tenant_id}")

        update_data = location_data.model_dump(exclude_unset=True)

        for field, value in update_data.items():
            setattr(settings, field, value)

        db.add(settings)
        await db.commit()
        await db.refresh(settings)
        return settings

    async def update_tax_settings(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID,
        tax_data: TaxSettingsUpdate,
    ) -> TenantSettings:
        """Update tax configuration settings."""
        settings = await self.get_settings(db, tenant_id)
        if not settings:
            raise NotFoundError(f"Settings not found for tenant {tenant_id}")

        update_data = tax_data.model_dump(exclude_unset=True)

        for field, value in update_data.items():
            setattr(settings, field, value)

        db.add(settings)
        await db.commit()
        await db.refresh(settings)
        return settings



    async def update_currency_settings(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID,
        currency_data: CurrencySettingsUpdate,
    ) -> TenantSettings:
        """Update multi-currency settings."""
        settings = await self.get_settings(db, tenant_id)
        if not settings:
            raise NotFoundError(f"Settings not found for tenant {tenant_id}")

        update_data = currency_data.model_dump(exclude_unset=True)

        # Validate currency configuration
        if "currency_config" in update_data:
            await self.integration_service._validate_currency_config(update_data["currency_config"])

        for field, value in update_data.items():
            setattr(settings, field, value)

        db.add(settings)
        await db.commit()
        await db.refresh(settings)
        return settings

    async def update_social_media_settings(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID,
        social_media_data: SocialMediaSettingsUpdate,
    ) -> TenantSettings:
        """Update social media links settings."""
        settings = await self.get_settings(db, tenant_id)
        if not settings:
            raise NotFoundError(f"Settings not found for tenant {tenant_id}")

        update_data = social_media_data.model_dump(exclude_unset=True)

        # Validate social media links if provided
        if "social_media_links" in update_data:
            await self.integration_service._validate_social_media_links(update_data["social_media_links"])

        for field, value in update_data.items():
            setattr(settings, field, value)

        db.add(settings)
        await db.commit()
        await db.refresh(settings)
        return settings


# Instance of the service to be used in endpoints
tenant_settings_service = TenantSettingsService()
