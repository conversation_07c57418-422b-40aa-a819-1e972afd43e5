import uuid
import enum
from typing import TYPE_CHECKING, List, Optional
from sqlalchemy import (
    Column,
    String,
    Foreign<PERSON>ey,
    JSON,
    DateTime,
    Enum,
    Numeric,
    Integer,
    Boolean,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import Base

# Use TYPE_CHECKING to handle circular dependencies for type hinting
if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.core.users.models.user import User
    from app.modules.core.payments.models.payment_method import PaymentMethod
    from app.modules.core.payments.models.payment_processor import PaymentProcessor
    from app.modules.core.payments.models.payment_transaction import PaymentTransaction
    from app.modules.core.functions.orders.models.order import Order


class PaymentStatus(str, enum.Enum):
    """Payment status enum."""

    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    REFUNDED = "refunded"
    PARTIALLY_REFUNDED = "partially_refunded"
    CANCELLED = "cancelled"


class Payment(Base):
    """Payment model for all types of payments across tenants."""

    __tablename__ = "payments"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    order_id = Column(UUID(as_uuid=True), ForeignKey("orders.id"), nullable=False, index=True)
    payment_method_id = Column(
        UUID(as_uuid=True), ForeignKey("core_payment_methods.id"), nullable=False
    )
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # Payment details
    amount = Column(Numeric(10, 2), nullable=False)
    currency = Column(String, nullable=False, default="USD")
    status = Column(Enum(PaymentStatus), default=PaymentStatus.PENDING, nullable=False, index=True)
    reference_number = Column(String, nullable=True)

    # Additional information
    notes = Column(String, nullable=True)
    payment_metadata = Column(JSON, nullable=True)  # For any additional data

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )
    completed_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    tenant = relationship(
        "app.modules.core.tenants.models.tenant.Tenant",
        viewonly=True,  # Use viewonly to avoid circular references
    )
    order = relationship("app.modules.core.functions.orders.models.order.Order", back_populates="payments")
    payment_method = relationship(
        "app.modules.core.payments.models.payment_method.PaymentMethod",
        viewonly=True,  # Use viewonly to avoid circular references
    )
    user = relationship(
        "app.modules.core.users.models.user.User",
        viewonly=True,  # Use viewonly to avoid circular references
    )
    transactions = relationship(
        "app.modules.core.payments.models.payment_transaction.PaymentTransaction",
        back_populates="payment",
        cascade="all, delete-orphan",
    )

    def __repr__(self):
        return (
            f"<Payment(id={self.id}, tenant_id={self.tenant_id}, "
            f"order_id={self.order_id}, status={self.status})>"
        )
