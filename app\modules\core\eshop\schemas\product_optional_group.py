from typing import Optional, List
from pydantic import BaseModel, Field
from uuid import UUID

from .product_optional_option import ProductOptionalOptionResponse


class ProductOptionalGroupBase(BaseModel):
    """Base schema for product optional groups."""
    name: str = Field(..., min_length=1, max_length=100, description="Optional group name")
    description: Optional[str] = Field(None, max_length=255, description="Optional group description")
    min_selection: int = Field(0, ge=0, description="Minimum number of selections required")
    max_selection: int = Field(5, ge=1, description="Maximum number of selections allowed")
    display_order: int = Field(0, description="Display order within the product")
    is_required: bool = Field(False, description="Whether this optional group is required")
    is_active: bool = Field(True, description="Whether this optional group is active")
    is_template: bool = Field(False, description="Whether this is a template group")
    template_id: Optional[UUID] = Field(None, description="Reference to template group")


class ProductOptionalGroupCreate(ProductOptionalGroupBase):
    """Schema for creating a new product optional group."""
    tenant_id: Optional[UUID] = Field(None, description="Tenant ID (null for global groups)")


class ProductOptionalGroupUpdate(BaseModel):
    """Schema for updating a product optional group."""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="Optional group name")
    description: Optional[str] = Field(None, max_length=255, description="Optional group description")
    min_selection: Optional[int] = Field(None, ge=0, description="Minimum number of selections required")
    max_selection: Optional[int] = Field(None, ge=1, description="Maximum number of selections allowed")
    display_order: Optional[int] = Field(None, description="Display order within the product")
    is_required: Optional[bool] = Field(None, description="Whether this optional group is required")
    is_active: Optional[bool] = Field(None, description="Whether this optional group is active")
    is_template: Optional[bool] = Field(None, description="Whether this is a template group")
    template_id: Optional[UUID] = Field(None, description="Reference to template group")


class ProductOptionalGroupResponse(ProductOptionalGroupBase):
    """Schema for product optional group responses."""
    id: UUID
    tenant_id: Optional[UUID]
    options: List[ProductOptionalOptionResponse] = Field(default_factory=list, description="Optional options")
    
    class Config:
        from_attributes = True
