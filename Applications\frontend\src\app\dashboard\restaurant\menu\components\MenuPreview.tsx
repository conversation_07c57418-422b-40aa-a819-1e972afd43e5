'use client';

import { useState } from 'react';
import {
  XMarkIcon,
  EyeIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  DeviceTabletIcon,
  QrCodeIcon,
  ShareIcon
} from '@heroicons/react/24/outline';
import Image from 'next/image';
import { useCurrency } from '@/hooks/useCurrency';
import { useMenuItemMedia } from '@/hooks/useMenuItemMedia';

interface MenuPreviewProps {
  menu: any;
  categories: any[];
  items: any[];
  onClose: () => void;
}

export function MenuPreview({ menu, categories, items, onClose }: MenuPreviewProps) {
  const [previewMode, setPreviewMode] = useState<'mobile' | 'tablet' | 'desktop'>('mobile');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const { formatCurrency } = useCurrency();

  const filteredItems = selectedCategory 
    ? items.filter(item => item.category_id === selectedCategory)
    : items;

  const getPreviewClasses = () => {
    switch (previewMode) {
      case 'mobile':
        return 'w-80 h-[600px]';
      case 'tablet':
        return 'w-96 h-[700px]';
      case 'desktop':
        return 'w-full h-[800px]';
      default:
        return 'w-80 h-[600px]';
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-semibold text-gray-900">Preview do Menu</h3>
          <button
            onClick={onClose}
            className="p-1 text-gray-400 hover:text-gray-600 rounded"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>

        {/* Device Toggle */}
        <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setPreviewMode('mobile')}
            className={`p-2 rounded transition-colors ${
              previewMode === 'mobile' 
                ? 'bg-white shadow-sm text-blue-600' 
                : 'text-gray-600 hover:bg-gray-200'
            }`}
            title="Mobile"
          >
            <DevicePhoneMobileIcon className="h-4 w-4" />
          </button>
          
          <button
            onClick={() => setPreviewMode('tablet')}
            className={`p-2 rounded transition-colors ${
              previewMode === 'tablet' 
                ? 'bg-white shadow-sm text-blue-600' 
                : 'text-gray-600 hover:bg-gray-200'
            }`}
            title="Tablet"
          >
            <DeviceTabletIcon className="h-4 w-4" />
          </button>
          
          <button
            onClick={() => setPreviewMode('desktop')}
            className={`p-2 rounded transition-colors ${
              previewMode === 'desktop' 
                ? 'bg-white shadow-sm text-blue-600' 
                : 'text-gray-600 hover:bg-gray-200'
            }`}
            title="Desktop"
          >
            <ComputerDesktopIcon className="h-4 w-4" />
          </button>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-2 mt-3">
          <button className="flex items-center space-x-2 px-3 py-1.5 text-xs bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors">
            <QrCodeIcon className="h-4 w-4" />
            <span>QR Code</span>
          </button>
          
          <button className="flex items-center space-x-2 px-3 py-1.5 text-xs bg-gray-50 text-gray-600 rounded-lg hover:bg-gray-100 transition-colors">
            <ShareIcon className="h-4 w-4" />
            <span>Compartilhar</span>
          </button>
        </div>
      </div>

      {/* Preview Content */}
      <div className="flex-1 overflow-hidden p-4">
        <div className={`mx-auto bg-white rounded-lg shadow-lg overflow-hidden ${getPreviewClasses()}`}>
          {/* Menu Header */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4">
            <h1 className="text-xl font-bold">{menu.name}</h1>
            {menu.description && (
              <p className="text-blue-100 text-sm mt-1">{menu.description}</p>
            )}
          </div>

          {/* Categories Navigation */}
          {categories.length > 0 && (
            <div className="border-b border-gray-200 p-2">
              <div className="flex space-x-1 overflow-x-auto">
                <button
                  onClick={() => setSelectedCategory('')}
                  className={`px-3 py-1.5 text-sm rounded-lg whitespace-nowrap transition-colors ${
                    !selectedCategory
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  Todos
                </button>
                {categories.map(category => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`px-3 py-1.5 text-sm rounded-lg whitespace-nowrap transition-colors ${
                      selectedCategory === category.id
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    {category.name}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Items List */}
          <div className="flex-1 overflow-y-auto p-4">
            {filteredItems.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">Nenhum item encontrado</p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredItems.map(item => {
                  return (
                    <MenuPreviewItem
                      key={item.id}
                      item={item}
                      categories={categories}
                      formatCurrency={formatCurrency}
                    />
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Componente separado para renderizar cada item no preview
function MenuPreviewItem({
  item,
  categories,
  formatCurrency
}: {
  item: any;
  categories: any[];
  formatCurrency: (value: number) => string;
}) {
  const { primaryImage } = useMenuItemMedia(item);
  const price = parseFloat(item.base_price || 0);
  const category = categories.find(cat => cat.id === item.category_id);
  const imageUrl = primaryImage || item.image_url;
  
  return (
    <div className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
      {/* Item Image */}
      <div className="w-16 h-16 bg-gray-200 rounded-lg flex-shrink-0 overflow-hidden">
        {imageUrl ? (
          <Image
            src={imageUrl.startsWith('http') ? imageUrl : `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}${imageUrl}`}
            alt={item.name}
            width={64}
            height={64}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full bg-gray-300"></div>
        )}
       </div>

       {/* Item Info */}
       <div className="flex-1 min-w-0">
         <div className="flex items-start justify-between">
           <div className="flex-1 min-w-0">
             <h4 className="font-medium text-gray-900 truncate">
               {item.name}
             </h4>
             {item.description && (
               <p className="text-sm text-gray-500 mt-1 line-clamp-2">
                 {item.description}
               </p>
             )}
             {category && (
               <p className="text-xs text-gray-400 mt-1">
                 {category.name}
               </p>
             )}
           </div>
           
           <div className="ml-3 text-right">
             <div className="font-semibold text-green-600">
               {formatCurrency(price)}
             </div>
             {!item.is_available && (
               <div className="text-xs text-red-500 mt-1">
                 Indisponível
               </div>
             )}
           </div>
         </div>

         {/* Item Features */}
         {(item.variants?.length > 0 || item.modifiers?.length > 0 || item.optionals?.length > 0) && (
           <div className="flex items-center space-x-2 mt-2">
             {item.variants?.length > 0 && (
               <span className="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded">
                 Variantes
               </span>
             )}
             {item.modifiers?.length > 0 && (
                <span className="text-xs bg-orange-100 text-orange-800 px-2 py-0.5 rounded">
                  Modificadores
                </span>
              )}
              {item.optionals?.length > 0 && (
                <span className="text-xs bg-purple-100 text-purple-800 px-2 py-0.5 rounded">
                  Opcionais
                </span>
              )}
            </div>
          )}
        </div>
      </div>
    );
   };

                {filteredItems.map((item) => (
                  <MenuPreviewItem key={item.id} item={item} />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <div className="text-xs text-gray-500 text-center">
          <p>Preview em tempo real • {filteredItems.length} {filteredItems.length === 1 ? 'item' : 'itens'}</p>
          <p className="mt-1">Última atualização: {new Date().toLocaleTimeString()}</p>
        </div>
      </div>
    </div>
  );
}
