import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF2196F3);
  static const Color primaryContainer = Color(0xFFE3F2FD);
  static const Color primaryLight = Color(0xFF64B5F6);
  static const Color primaryDark = Color(0xFF1976D2);
  
  // Secondary Colors
  static const Color secondary = Color(0xFF03DAC6);
  static const Color secondaryContainer = Color(0xFFE0F7FA);
  static const Color secondaryLight = Color(0xFF66FFF9);
  static const Color secondaryDark = Color(0xFF00A896);
  
  // Tertiary Colors
  static const Color tertiary = Color(0xFF9C27B0);
  static const Color tertiaryContainer = Color(0xFFF3E5F5);
  
  // Background Colors
  static const Color background = Color(0xFFF5F5F5);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF8F9FA);
  
  // Text Colors
  static const Color onPrimary = Color(0xFFFFFFFF);
  static const Color onSecondary = Color(0xFF000000);
  static const Color onBackground = Color(0xFF212121);
  static const Color onSurface = Color(0xFF212121);
  static const Color onSurfaceVariant = Color(0xFF757575);
  
  // Outline Colors
  static const Color outline = Color(0xFFBDBDBD);
  static const Color onInverseSurface = Color(0xFFFFFFFF);
  
  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color successLight = Color(0xFF81C784);
  static const Color successDark = Color(0xFF388E3C);
  
  static const Color warning = Color(0xFFFF9800);
  static const Color warningLight = Color(0xFFFFB74D);
  static const Color warningDark = Color(0xFFF57C00);
  
  static const Color error = Color(0xFFF44336);
  static const Color errorLight = Color(0xFFE57373);
  static const Color errorDark = Color(0xFFD32F2F);
  
  static const Color info = Color(0xFF2196F3);
  static const Color infoLight = Color(0xFF64B5F6);
  static const Color infoDark = Color(0xFF1976D2);
  
  // Order Status Colors
  static const Color orderPending = Color(0xFFFF9800);
  static const Color orderPreparing = Color(0xFF2196F3);
  static const Color orderReady = Color(0xFF4CAF50);
  static const Color orderCompleted = Color(0xFF9E9E9E);
  static const Color orderCancelled = Color(0xFFF44336);
  
  // Table Status Colors
  static const Color tableFree = Color(0xFF4CAF50);
  static const Color tableOccupied = Color(0xFFF44336);
  static const Color tableReserved = Color(0xFF2196F3);
  static const Color tableCleaning = Color(0xFFFF9800);
  
  // Payment Status Colors
  static const Color paymentPending = Color(0xFFFF9800);
  static const Color paymentPaid = Color(0xFF4CAF50);
  static const Color paymentRefunded = Color(0xFF9E9E9E);
  
  // Delivery Status Colors
  static const Color deliveryPreparing = Color(0xFF2196F3);
  static const Color deliveryReady = Color(0xFF4CAF50);
  static const Color deliveryOutForDelivery = Color(0xFFFF9800);
  static const Color deliveryDelivered = Color(0xFF9E9E9E);
  
  // Neutral Colors
  static const Color grey50 = Color(0xFFFAFAFA);
  static const Color grey100 = Color(0xFFF5F5F5);
  static const Color grey200 = Color(0xFFEEEEEE);
  static const Color grey300 = Color(0xFFE0E0E0);
  static const Color grey400 = Color(0xFFBDBDBD);
  static const Color grey500 = Color(0xFF9E9E9E);
  static const Color grey600 = Color(0xFF757575);
  static const Color grey700 = Color(0xFF616161);
  static const Color grey800 = Color(0xFF424242);
  static const Color grey900 = Color(0xFF212121);
  
  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient successGradient = LinearGradient(
    colors: [success, successDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient warningGradient = LinearGradient(
    colors: [warning, warningDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient errorGradient = LinearGradient(
    colors: [error, errorDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Shadow Colors
  static const Color shadowLight = Color(0x1A000000);
  static const Color shadowMedium = Color(0x33000000);
  static const Color shadowDark = Color(0x4D000000);
  
  // Overlay Colors
  static const Color overlayLight = Color(0x0A000000);
  static const Color overlayMedium = Color(0x1F000000);
  static const Color overlayDark = Color(0x33000000);
  
  // Dark theme colors
  static const Color backgroundDark = Color(0xFF121212);
  static const Color surfaceDark = Color(0xFF1E1E1E);
  static const Color surfaceVariantDark = Color(0xFF2C2C2C);
  static const Color onSurfaceDark = Color(0xFFE1E1E1);
  static const Color onSurfaceVariantDark = Color(0xFFB3B3B3);
  static const Color outlineDark = Color(0xFF404040);
  
  // Border Colors
  static const Color borderLight = Color(0xFFE0E0E0);
  static const Color borderMedium = Color(0xFFBDBDBD);
  static const Color borderDark = Color(0xFF757575);
  
  // Helper methods
  static Color getOrderStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
      case 'pendente':
        return orderPending;
      case 'preparing':
      case 'preparando':
        return orderPreparing;
      case 'ready':
      case 'pronto':
        return orderReady;
      case 'completed':
      case 'concluído':
        return orderCompleted;
      case 'cancelled':
      case 'cancelado':
        return orderCancelled;
      default:
        return grey500;
    }
  }
  
  static Color getTableStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'free':
      case 'livre':
        return tableFree;
      case 'occupied':
      case 'ocupada':
        return tableOccupied;
      case 'reserved':
      case 'reservada':
        return tableReserved;
      case 'cleaning':
      case 'limpeza':
        return tableCleaning;
      default:
        return grey500;
    }
  }
  
  static Color getPaymentStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
      case 'pendente':
        return paymentPending;
      case 'paid':
      case 'pago':
        return paymentPaid;
      case 'refunded':
      case 'reembolsado':
        return paymentRefunded;
      default:
        return grey500;
    }
  }
  
  static Color getDeliveryStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'preparing':
      case 'preparando':
        return deliveryPreparing;
      case 'ready':
      case 'pronto':
        return deliveryReady;
      case 'out_for_delivery':
      case 'saiu_para_entrega':
        return deliveryOutForDelivery;
      case 'delivered':
      case 'entregue':
        return deliveryDelivered;
      default:
        return grey500;
    }
  }
  
  static Color getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
      case 'alta':
        return error;
      case 'medium':
      case 'média':
        return warning;
      case 'low':
      case 'baixa':
        return info;
      default:
        return grey500;
    }
  }
  
  static Color withValues(alpha: Color color, double opacity) {
    return color.withValues(alpha: opacity);
  }
  
  static Color lighten(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslLight = hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0));
    return hslLight.toColor();
  }
  
  static Color darken(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));
    return hslDark.toColor();
  }
}

// Color schemes for different contexts
class OrderPadColorScheme {
  static const ColorScheme light = ColorScheme(
    brightness: Brightness.light,
    primary: AppColors.primary,
    onPrimary: AppColors.onPrimary,
    secondary: AppColors.secondary,
    onSecondary: AppColors.onSecondary,
    error: AppColors.error,
    onError: AppColors.onPrimary,
    background: AppColors.background,
    onBackground: AppColors.onBackground,
    surface: AppColors.surface,
    onSurface: AppColors.onSurface,
    surfaceVariant: AppColors.surfaceVariant,
    onSurfaceVariant: AppColors.onSurfaceVariant,
  );
  
  static const ColorScheme dark = ColorScheme(
    brightness: Brightness.dark,
    primary: AppColors.primaryLight,
    onPrimary: AppColors.onBackground,
    secondary: AppColors.secondaryLight,
    onSecondary: AppColors.onBackground,
    error: AppColors.errorLight,
    onError: AppColors.onBackground,
    background: AppColors.backgroundDark,
    onBackground: AppColors.onSurfaceDark,
    surface: AppColors.surfaceDark,
    onSurface: AppColors.onSurfaceDark,
    surfaceVariant: AppColors.surfaceVariantDark,
    onSurfaceVariant: AppColors.onSurfaceVariantDark,
  );
}