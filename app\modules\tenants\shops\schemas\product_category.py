import uuid
from typing import Optional, List
from pydantic import BaseModel, Field, ConfigDict
import datetime
import uuid

# Base schema for ProductCategory


class ProductCategoryBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255, description="Name of the category")
    description: Optional[str] = Field(None, description="Description of the category")
    parent_category_id: Optional[uuid.UUID] = Field(
        None, description="ID of the parent category for subcategories"
    )


# Schema for creating a new category


class ProductCategoryCreate(ProductCategoryBase):
    # tenant_id will be added by the service
    pass


# Schema for updating an existing category


class ProductCategoryUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    parent_category_id: Optional[uuid.UUID] = None  # Allow changing parent


# Schema representing category data in the database


class ProductCategoryInDBBase(ProductCategoryBase):
    id: uuid.UUID
    tenant_id: uuid.UUID
    created_at: datetime.datetime
    updated_at: datetime.datetime

    model_config = ConfigDict(from_attributes=True)  # Use model_config in Pydantic V2


# Forward declaration needed for recursive self-referencing in ProductCategoryRead


class ProductCategoryReadMinimal(BaseModel):
    id: uuid.UUID
    name: str
    # Add other fields needed for minimal representation if necessary

    model_config = ConfigDict(from_attributes=True)


# Schema for reading/returning category data


class ProductCategoryRead(ProductCategoryInDBBase):
    # Include nested parent and sub-category information
    parent_category: Optional["ProductCategoryReadMinimal"] = None  # Use forward reference string
    sub_categories: List["ProductCategoryReadMinimal"] = []  # Use forward reference string


# Now define the minimal schema properly after the main Read schema is defined
# This replaces the previous forward declaration and avoids the need for model_rebuild
# (Although model_rebuild might still be needed depending on import order elsewhere)
# class ProductCategoryReadMinimal(BaseModel):
#     id: uuid.UUID
#     name: str
#     # Add other fields needed for minimal representation if necessary
#
#     model_config = ConfigDict(from_attributes=True)

# If using forward references as strings ('ProductCategoryReadMinimal'),
# Pydantic usually handles the resolution automatically.
# If issues persist, uncommenting the explicit model_rebuild might be necessary,
# but it's cleaner to avoid it if possible.
# ProductCategoryRead.model_rebuild()
# ProductCategoryReadMinimal.model_rebuild() # May not be needed if
# defined inline or not used recursively itself
