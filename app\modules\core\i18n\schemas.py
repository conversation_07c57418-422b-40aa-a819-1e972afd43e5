from datetime import datetime
from typing import Optional, List
from uuid import UUID

from pydantic import BaseModel, ConfigDict

from app.modules.i18n.models import TranslationSuggestionStatus

# Forward-referencing User schema if needed, or import directly
# from app.schemas.user import UserRead # Assuming UserRead schema exists


# Schemas para Language
class LanguageBase(BaseModel):
    code: str
    name: str
    is_active: bool = True
    is_default: bool = False


class LanguageCreate(LanguageBase):
    pass


class LanguageUpdate(BaseModel):
    code: Optional[str] = None
    name: Optional[str] = None
    is_active: Optional[bool] = None
    is_default: Optional[bool] = None


class LanguageInDBBase(LanguageBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class LanguageRead(LanguageInDBBase):
    pass


# Schemas para TranslationKey
class TranslationKeyBase(BaseModel):
    key_string: str
    module: Optional[str] = None
    description: Optional[str] = None


class TranslationKeyCreate(TranslationKeyBase):
    pass


class TranslationKeyUpdate(BaseModel):
    key_string: Optional[str] = None
    module: Optional[str] = None
    description: Optional[str] = None


class TranslationKeyInDBBase(TranslationKeyBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class TranslationKeyRead(TranslationKeyInDBBase):
    pass


# Schemas para Translation
class TranslationBase(BaseModel):
    translated_text: str
    is_approved: bool = True
    version: int = 1
    key_id: int
    language_id: int
    last_updated_by_user_id: Optional[UUID] = None


class TranslationCreate(TranslationBase):
    pass


class TranslationUpdate(BaseModel):
    translated_text: Optional[str] = None
    is_approved: Optional[bool] = None
    version: Optional[int] = None
    last_updated_by_user_id: Optional[UUID] = None


class TranslationInDBBase(TranslationBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class TranslationRead(TranslationInDBBase):
    language: Optional[LanguageRead] = None
    # translation_key: Optional[TranslationKeyRead] = None # Evitar dependência circular se TranslationKeyRead tiver TranslationRead
    # last_updated_by_user: Optional[UserRead] = None # Se UserRead for definido


# Schemas para TranslationSuggestion
class TranslationSuggestionBase(BaseModel):
    suggested_text: str
    current_text_on_suggestion: Optional[str] = None
    status: TranslationSuggestionStatus = TranslationSuggestionStatus.PENDING
    admin_notes: Optional[str] = None
    key_id: int
    language_id: int
    user_id: UUID


class TranslationSuggestionCreate(TranslationSuggestionBase):
    pass


class TranslationSuggestionUpdate(BaseModel):
    suggested_text: Optional[str] = None
    current_text_on_suggestion: Optional[str] = None
    status: Optional[TranslationSuggestionStatus] = None
    admin_notes: Optional[str] = None
    # key_id, language_id, user_id não devem ser atualizáveis geralmente


class TranslationSuggestionInDBBase(TranslationSuggestionBase):
    id: int
    reviewed_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class TranslationSuggestionRead(TranslationSuggestionInDBBase):
    language: Optional[LanguageRead] = None
    # translation_key: Optional[TranslationKeyRead] = None # Evitar dependência circular
    # user: Optional[UserRead] = None # Se UserRead for definido


# Para evitar dependências circulares nos schemas de leitura com relacionamentos completos:
# Você pode definir schemas "light" sem os relacionamentos profundos para alguns casos
# ou usar Pydantic v2 com TypeAdapter ou anotações postergadas se necessário.
# Por simplicidade, comentei alguns relacionamentos que poderiam causar importação circular
# se os schemas referenciados também tentassem carregar este de volta.


# Exemplo de como TranslationKeyRead poderia ser se precisasse de traduções:
class TranslationKeyReadWithTranslations(TranslationKeyRead):
    translations: List[TranslationRead] = []  # Ou Optional[List[TranslationRead]] = None
    suggestions: List[TranslationSuggestionRead] = (
        []
    )  # Ou Optional[List[TranslationSuggestionRead]] = None


class LanguageReadWithTranslations(LanguageRead):
    translations: List[TranslationRead] = []
    translation_suggestions: List[TranslationSuggestionRead] = []


# E o UserRead precisaria ser definido em app.schemas.user.py
# class UserRead(UserInDBBase): # Supondo que UserInDBBase exista
# updated_translations: List[TranslationRead] = []
# translation_suggestions: List[TranslationSuggestionRead] = []
# pass
