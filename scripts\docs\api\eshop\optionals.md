# Eshop - Optionals

**Categoria:** Eshop
**Módulo:** Optionals
**Total de Endpoints:** 6
**Gerado em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [GET /api/eshop/optionals/groups/](#get-apieshopoptionalsgroups) - Read Optional Groups
- [POST /api/eshop/optionals/groups/](#post-apieshopoptionalsgroups) - Create Optional Group
- [GET /api/eshop/optionals/groups/{group_id}](#get-apieshopoptionalsgroupsgroup-id) - Read Optional Group
- [GET /api/eshop/optionals/options/](#get-apieshopoptionalsoptions) - Read Optional Options
- [POST /api/eshop/optionals/options/](#post-apieshopoptionalsoptions) - Create Optional Option
- [GET /api/eshop/optionals/options/{option_id}](#get-apieshopoptionalsoptionsoption-id) - Read Optional Option

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### ProductOptionalGroupCreate

**Descrição:** Schema for creating a new product optional group.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Optional group name |
| `description` | unknown | ❌ | Optional group description |
| `min_selection` | integer | ❌ | Minimum number of selections required |
| `max_selection` | integer | ❌ | Maximum number of selections allowed |
| `display_order` | integer | ❌ | Display order within the product |
| `is_required` | boolean | ❌ | Whether this optional group is required |
| `is_active` | boolean | ❌ | Whether this optional group is active |
| `is_template` | boolean | ❌ | Whether this is a template group |
| `template_id` | unknown | ❌ | Reference to template group |
| `tenant_id` | unknown | ❌ | Tenant ID (null for global groups) |

### ProductOptionalGroupResponse

**Descrição:** Schema for product optional group responses.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Optional group name |
| `description` | unknown | ❌ | Optional group description |
| `min_selection` | integer | ❌ | Minimum number of selections required |
| `max_selection` | integer | ❌ | Maximum number of selections allowed |
| `display_order` | integer | ❌ | Display order within the product |
| `is_required` | boolean | ❌ | Whether this optional group is required |
| `is_active` | boolean | ❌ | Whether this optional group is active |
| `is_template` | boolean | ❌ | Whether this is a template group |
| `template_id` | unknown | ❌ | Reference to template group |
| `id` | string | ✅ | - |
| `tenant_id` | unknown | ✅ | - |
| `options` | Array[ProductOptionalOptionResponse] | ❌ | Optional options |

### ProductOptionalOptionCreate

**Descrição:** Schema for creating a new product optional option.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Optional option name |
| `description` | unknown | ❌ | Optional option description |
| `price_adjustment` | unknown | ❌ | Additional cost for this optional |
| `price_adjustment_type` | string | ❌ | Price adjustment type |
| `display_order` | integer | ❌ | Display order within the group |
| `is_default` | boolean | ❌ | Whether this is the default selection |
| `is_active` | boolean | ❌ | Whether this optional option is active |
| `stock_quantity` | unknown | ❌ | Limited availability for this optional |
| `sku` | unknown | ❌ | Separate SKU for this optional item |
| `image_url` | unknown | ❌ | Image URL for this optional |
| `related_product_id` | unknown | ❌ | Related product ID if this optional is another product |
| `tenant_id` | unknown | ❌ | Tenant ID (null for global options) |
| `optional_group_id` | string | ✅ | Parent optional group ID |

### ProductOptionalOptionResponse

**Descrição:** Schema for product optional option responses.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Optional option name |
| `description` | unknown | ❌ | Optional option description |
| `price_adjustment` | string | ❌ | Additional cost for this optional |
| `price_adjustment_type` | string | ❌ | Price adjustment type |
| `display_order` | integer | ❌ | Display order within the group |
| `is_default` | boolean | ❌ | Whether this is the default selection |
| `is_active` | boolean | ❌ | Whether this optional option is active |
| `stock_quantity` | unknown | ❌ | Limited availability for this optional |
| `sku` | unknown | ❌ | Separate SKU for this optional item |
| `image_url` | unknown | ❌ | Image URL for this optional |
| `related_product_id` | unknown | ❌ | Related product ID if this optional is another product |
| `id` | string | ✅ | - |
| `tenant_id` | unknown | ✅ | - |
| `optional_group_id` | string | ✅ | - |

## 🔗 Endpoints Detalhados

### GET /api/eshop/optionals/groups/ {#get-apieshopoptionalsgroups}

**Resumo:** Read Optional Groups
**Descrição:** Retrieve optional groups.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `is_active` | string | query | ❌ | - |
| `include_options` | boolean | query | ❌ | - |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/optionals/groups/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/eshop/optionals/groups/ {#post-apieshopoptionalsgroups}

**Resumo:** Create Optional Group
**Descrição:** Create a new optional group.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ProductOptionalGroupCreate](#productoptionalgroupcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductOptionalGroupResponse](#productoptionalgroupresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/eshop/optionals/groups/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/eshop/optionals/groups/{group_id} {#get-apieshopoptionalsgroupsgroup-id}

**Resumo:** Read Optional Group
**Descrição:** Retrieve a specific optional group.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `group_id` | string | path | ✅ | - |
| `include_options` | boolean | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductOptionalGroupResponse](#productoptionalgroupresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/optionals/groups/{group_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/eshop/optionals/options/ {#get-apieshopoptionalsoptions}

**Resumo:** Read Optional Options
**Descrição:** Retrieve optional options.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `optional_group_id` | string | query | ❌ | - |
| `is_active` | string | query | ❌ | - |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/optionals/options/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/eshop/optionals/options/ {#post-apieshopoptionalsoptions}

**Resumo:** Create Optional Option
**Descrição:** Create a new optional option.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ProductOptionalOptionCreate](#productoptionaloptioncreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductOptionalOptionResponse](#productoptionaloptionresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/eshop/optionals/options/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/eshop/optionals/options/{option_id} {#get-apieshopoptionalsoptionsoption-id}

**Resumo:** Read Optional Option
**Descrição:** Retrieve a specific optional option.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `option_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductOptionalOptionResponse](#productoptionaloptionresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/eshop/optionals/options/{option_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
