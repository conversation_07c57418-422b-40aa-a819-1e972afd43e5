"""Financial category service."""

import uuid
import logging
from typing import Optional, List, Dict, Any
from sqlalchemy import select, func, and_, or_, desc, asc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from fastapi import HTTPException, status

from ..models.category import FinancialCategory, CategoryType
from ..schemas.category import (
    FinancialCategoryCreate,
    FinancialCategoryUpdate,
    FinancialCategoryRead,
    FinancialCategoryTree,
    FinancialCategoryFilter,
    FinancialCategoryListResponse,
)

logger = logging.getLogger(__name__)


class FinancialCategoryService:
    """Service for managing financial categories."""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def create_category(
        self,
        category_data: FinancialCategoryCreate,
        tenant_id: uuid.UUID,
    ) -> FinancialCategory:
        """Create a new financial category."""
        try:
            # Validate parent category if specified
            if category_data.parent_id:
                parent = await self.get_category(category_data.parent_id, tenant_id)
                if not parent:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Parent category not found"
                    )
                
                # Ensure parent and child have compatible types
                if parent.category_type != category_data.category_type:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Parent and child categories must have compatible types"
                    )
            
            # Check for duplicate names within the same parent
            await self._check_duplicate_name(
                category_data.name, 
                tenant_id, 
                category_data.parent_id
            )
            
            # Create category
            db_category = FinancialCategory(
                **category_data.model_dump(),
                tenant_id=tenant_id,
            )
            
            self.db.add(db_category)
            await self.db.flush()
            await self.db.refresh(db_category)
            
            logger.info(
                f"Created financial category {db_category.id} "
                f"for tenant {tenant_id}"
            )
            
            return db_category
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating financial category: {e}")
            await self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating financial category"
            )

    async def get_category(
        self,
        category_id: uuid.UUID,
        tenant_id: uuid.UUID,
    ) -> Optional[FinancialCategory]:
        """Get a financial category by ID."""
        try:
            stmt = (
                select(FinancialCategory)
                .where(
                    and_(
                        FinancialCategory.id == category_id,
                        FinancialCategory.tenant_id == tenant_id,
                    )
                )
                .options(
                    selectinload(FinancialCategory.parent),
                    selectinload(FinancialCategory.children),
                )
            )
            
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"Error getting financial category {category_id}: {e}")
            return None

    async def get_categories(
        self,
        tenant_id: uuid.UUID,
        filters: Optional[FinancialCategoryFilter] = None,
        page: int = 1,
        per_page: int = 20,
        order_by: str = "display_order",
        order_direction: str = "asc",
    ) -> FinancialCategoryListResponse:
        """Get paginated list of financial categories with filters."""
        try:
            # Build base query
            stmt = select(FinancialCategory).where(
                FinancialCategory.tenant_id == tenant_id
            )
            
            # Apply filters
            if filters:
                stmt = self._apply_filters(stmt, filters)
            
            # Apply ordering
            order_column = getattr(FinancialCategory, order_by, None)
            if order_column:
                if order_direction.lower() == "desc":
                    stmt = stmt.order_by(desc(order_column))
                else:
                    stmt = stmt.order_by(asc(order_column))
            else:
                stmt = stmt.order_by(asc(FinancialCategory.display_order))
            
            # Count total records
            count_stmt = select(func.count()).select_from(stmt.subquery())
            total_result = await self.db.execute(count_stmt)
            total = total_result.scalar()
            
            # Apply pagination
            offset = (page - 1) * per_page
            stmt = stmt.offset(offset).limit(per_page)
            
            # Execute query with relationships
            stmt = stmt.options(
                selectinload(FinancialCategory.parent),
                selectinload(FinancialCategory.children),
            )
            
            result = await self.db.execute(stmt)
            categories = result.scalars().all()
            
            # Calculate pagination info
            pages = (total + per_page - 1) // per_page
            
            return FinancialCategoryListResponse(
                categories=[
                    self._category_to_read_schema(c) for c in categories
                ],
                total=total,
                page=page,
                per_page=per_page,
                pages=pages,
            )
            
        except Exception as e:
            logger.error(f"Error getting financial categories: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving financial categories"
            )

    async def get_category_tree(
        self,
        tenant_id: uuid.UUID,
        category_type: Optional[CategoryType] = None,
    ) -> List[FinancialCategoryTree]:
        """Get hierarchical category tree."""
        try:
            # Get all categories for the tenant
            stmt = select(FinancialCategory).where(
                and_(
                    FinancialCategory.tenant_id == tenant_id,
                    FinancialCategory.is_active == True,
                )
            )
            
            if category_type:
                stmt = stmt.where(
                    FinancialCategory.category_type == category_type
                )
            
            stmt = stmt.order_by(asc(FinancialCategory.display_order))
            
            result = await self.db.execute(stmt)
            categories = result.scalars().all()
            
            # Build tree structure
            category_dict = {cat.id: cat for cat in categories}
            tree = []
            
            for category in categories:
                if category.parent_id is None:
                    # Root category
                    tree_node = self._build_category_tree_node(
                        category, category_dict
                    )
                    tree.append(tree_node)
            
            return tree
            
        except Exception as e:
            logger.error(f"Error getting category tree: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error retrieving category tree"
            )

    async def update_category(
        self,
        category_id: uuid.UUID,
        tenant_id: uuid.UUID,
        category_data: FinancialCategoryUpdate,
    ) -> Optional[FinancialCategory]:
        """Update a financial category."""
        try:
            # Get existing category
            category = await self.get_category(category_id, tenant_id)
            if not category:
                return None
            
            # Validate parent category if being updated
            update_data = category_data.model_dump(exclude_unset=True)
            if "parent_id" in update_data and update_data["parent_id"]:
                parent = await self.get_category(update_data["parent_id"], tenant_id)
                if not parent:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Parent category not found"
                    )
                
                # Prevent circular references
                if await self._would_create_circular_reference(
                    category_id, update_data["parent_id"], tenant_id
                ):
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Cannot create circular reference"
                    )
            
            # Check for duplicate names if name is being updated
            if "name" in update_data:
                await self._check_duplicate_name(
                    update_data["name"],
                    tenant_id,
                    update_data.get("parent_id", category.parent_id),
                    exclude_id=category_id,
                )
            
            # Update fields
            for field, value in update_data.items():
                setattr(category, field, value)
            
            await self.db.flush()
            await self.db.refresh(category)
            
            logger.info(f"Updated financial category {category_id}")
            
            return category
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating financial category {category_id}: {e}")
            await self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error updating financial category"
            )

    async def delete_category(
        self,
        category_id: uuid.UUID,
        tenant_id: uuid.UUID,
    ) -> bool:
        """Delete a financial category."""
        try:
            category = await self.get_category(category_id, tenant_id)
            if not category:
                return False
            
            # Check if category has transactions
            from app.modules.shared.financial.transactions.models.transaction import FinancialTransaction
            
            stmt = select(func.count()).where(
                and_(
                    FinancialTransaction.category_id == category_id,
                    FinancialTransaction.tenant_id == tenant_id,
                )
            )
            result = await self.db.execute(stmt)
            transaction_count = result.scalar()
            
            if transaction_count > 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Cannot delete category with existing transactions"
                )
            
            # Check if category has children
            if category.children:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Cannot delete category with child categories"
                )
            
            await self.db.delete(category)
            await self.db.flush()
            
            logger.info(f"Deleted financial category {category_id}")
            
            return True
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting financial category {category_id}: {e}")
            await self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error deleting financial category"
            )

    async def create_default_categories(self, tenant_id: uuid.UUID) -> List[FinancialCategory]:
        """Create default categories for a new tenant."""
        default_categories = [
            # Income categories
            {"name": "Sales", "category_type": CategoryType.INCOME, "icon": "💰"},
            {"name": "Services", "category_type": CategoryType.INCOME, "icon": "🔧"},
            {"name": "Other Income", "category_type": CategoryType.INCOME, "icon": "📈"},
            
            # Expense categories
            {"name": "Cost of Goods", "category_type": CategoryType.EXPENSE, "icon": "📦"},
            {"name": "Salaries", "category_type": CategoryType.EXPENSE, "icon": "👥"},
            {"name": "Utilities", "category_type": CategoryType.EXPENSE, "icon": "⚡"},
            {"name": "Marketing", "category_type": CategoryType.EXPENSE, "icon": "📢"},
            {"name": "Office Supplies", "category_type": CategoryType.EXPENSE, "icon": "📋"},
            {"name": "Other Expenses", "category_type": CategoryType.EXPENSE, "icon": "📉"},
        ]
        
        created_categories = []
        
        for i, cat_data in enumerate(default_categories):
            try:
                category = FinancialCategory(
                    tenant_id=tenant_id,
                    name=cat_data["name"],
                    category_type=cat_data["category_type"],
                    icon=cat_data["icon"],
                    display_order=i,
                    is_default=True,
                    is_active=True,
                )
                
                self.db.add(category)
                created_categories.append(category)
                
            except Exception as e:
                logger.error(f"Error creating default category {cat_data['name']}: {e}")
        
        try:
            await self.db.flush()
            logger.info(f"Created {len(created_categories)} default categories for tenant {tenant_id}")
            return created_categories
        except Exception as e:
            logger.error(f"Error saving default categories: {e}")
            await self.db.rollback()
            return []

    def _apply_filters(self, stmt, filters: FinancialCategoryFilter):
        """Apply filters to the query."""
        if filters.category_type:
            stmt = stmt.where(
                FinancialCategory.category_type == filters.category_type
            )
        
        if filters.parent_id:
            stmt = stmt.where(FinancialCategory.parent_id == filters.parent_id)
        
        if filters.is_active is not None:
            stmt = stmt.where(FinancialCategory.is_active == filters.is_active)
        
        if filters.search:
            search_term = f"%{filters.search}%"
            stmt = stmt.where(
                or_(
                    FinancialCategory.name.ilike(search_term),
                    FinancialCategory.description.ilike(search_term),
                )
            )
        
        return stmt

    async def _check_duplicate_name(
        self,
        name: str,
        tenant_id: uuid.UUID,
        parent_id: Optional[uuid.UUID] = None,
        exclude_id: Optional[uuid.UUID] = None,
    ):
        """Check for duplicate category names within the same parent."""
        stmt = select(FinancialCategory).where(
            and_(
                FinancialCategory.tenant_id == tenant_id,
                FinancialCategory.name.ilike(name),
                FinancialCategory.parent_id == parent_id,
            )
        )
        
        if exclude_id:
            stmt = stmt.where(FinancialCategory.id != exclude_id)
        
        result = await self.db.execute(stmt)
        existing = result.scalar_one_or_none()
        
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Category name already exists in this location"
            )

    async def _would_create_circular_reference(
        self,
        category_id: uuid.UUID,
        new_parent_id: uuid.UUID,
        tenant_id: uuid.UUID,
    ) -> bool:
        """Check if setting new_parent_id would create a circular reference."""
        current_id = new_parent_id
        
        while current_id:
            if current_id == category_id:
                return True
            
            stmt = select(FinancialCategory.parent_id).where(
                and_(
                    FinancialCategory.id == current_id,
                    FinancialCategory.tenant_id == tenant_id,
                )
            )
            result = await self.db.execute(stmt)
            current_id = result.scalar_one_or_none()
        
        return False

    def _build_category_tree_node(
        self,
        category: FinancialCategory,
        category_dict: Dict[uuid.UUID, FinancialCategory],
    ) -> FinancialCategoryTree:
        """Build a tree node with children."""
        children = []
        
        for cat in category_dict.values():
            if cat.parent_id == category.id:
                child_node = self._build_category_tree_node(cat, category_dict)
                children.append(child_node)
        
        # Sort children by display_order
        children.sort(key=lambda x: x.display_order)
        
        return FinancialCategoryTree(
            **self._category_to_read_schema(category).model_dump(),
            children=children,
        )

    def _category_to_read_schema(
        self, 
        category: FinancialCategory
    ) -> FinancialCategoryRead:
        """Convert category model to read schema."""
        return FinancialCategoryRead(
            id=category.id,
            tenant_id=category.tenant_id,
            name=category.name,
            description=category.description,
            category_type=category.category_type,
            parent_id=category.parent_id,
            display_order=category.display_order,
            is_active=category.is_active,
            is_default=category.is_default,
            color=category.color,
            icon=category.icon,
            parent_name=category.parent.name if category.parent else None,
            children_count=len(category.children) if category.children else 0,
            transactions_count=0,  # TODO: Implement transaction count
            total_income=None,  # TODO: Implement income total
            total_expense=None,  # TODO: Implement expense total
        )
