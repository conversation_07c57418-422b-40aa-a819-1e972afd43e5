'use client';

import { useState, useEffect, useCallback } from 'react';
import {
  CurrencyDollarIcon,
  ClockIcon,
  MapPinIcon,
  CreditCardIcon,
  Cog6ToothIcon,
  CheckIcon,
  XMarkIcon,
  TableCellsIcon,
  PlusIcon,
  LanguageIcon,
  GiftIcon,
  CalculatorIcon,
  WifiIcon,
  CreditCardIcon as SubscriptionIcon,
  BuildingOfficeIcon,
  ExclamationTriangleIcon,
  ShareIcon,
  CalendarDaysIcon
} from '@heroicons/react/24/outline';
import { useTenant } from '@/lib/tenant/TenantProvider';
import toast from 'react-hot-toast';
import { ModuleLayout } from '@/components/dashboard/shared';
import { useTableManagement } from '@/hooks/useTableManagement';
import TableManagement from './components/TableManagement';
import BusinessInfoTab from './components/business/BusinessInfoTab';
import OperatingHoursTab from './components/business/OperatingHoursTab';
import PaymentMethodsTab from './components/payments/PaymentMethodsTab';
import LanguageSettingsTab from './components/languages/LanguageSettingsTab';
import LoyaltySystemTab from './components/loyalty/LoyaltySystemTab';

import TaxSettingsTab from './components/tax/TaxSettingsTab';
import WiFiNetworksTab from './components/wifi/WiFiNetworksTab';
import SubscriptionTab from './components/subscription/SubscriptionTab';
import SocialMediaTab from './components/social/SocialMediaTab';
import SpecialCalendarTab from './components/business/SpecialCalendarTab';
import { tenantSettingsService, TenantSettings } from '@/services/api/tenantSettingsService';
import { restaurantSettingsService, RestaurantSettings } from '@/services/api/restaurantSettingsService';
import { tenantService } from '@/services/api/tenantService';



const DAYS_OF_WEEK = [
  'monday', 'tuesday', 'wednesday', 'thursday', 
  'friday', 'saturday', 'sunday'
];

const CURRENCIES = [
  { code: 'USD', symbol: '$', name: 'US Dollar' },
  { code: 'BRL', symbol: 'R$', name: 'Brazilian Real' },
  { code: 'EUR', symbol: '€', name: 'Euro' },
  { code: 'GBP', symbol: '£', name: 'British Pound' },
];

export default function RestaurantSettingsPage() {
  const { currentTenant, refreshTenant } = useTenant();
  const {
    tables,
    loadTables
  } = useTableManagement();

  const [settings, setSettings] = useState<TenantSettings | null>(null);
  const [restaurantSettings, setRestaurantSettings] = useState<RestaurantSettings | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('business');
  const [error, setError] = useState<string | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [zones, setZones] = useState<string[]>([]);
  const [savingField, setSavingField] = useState<string | null>(null);

  const loadRestaurantSettings = useCallback(async () => {
    try {
      console.log('🏪 RestaurantSettings: Loading restaurant-specific settings...');
      const restaurantData = await restaurantSettingsService.getSettings();
      console.log('🏪 RestaurantSettings: Loaded restaurant settings:', restaurantData);
      console.log('🏪 RestaurantSettings: Tenant slug:', restaurantData.tenant_slug);

      setRestaurantSettings(restaurantData);
    } catch (error: any) {
      console.error('🏪 RestaurantSettings: Error loading restaurant settings:', error);
      // Set default restaurant settings if API fails
      setRestaurantSettings({
        id: '',
        tenant_id: currentTenant?.id || '',
        tenant_slug: '',
        wifi_networks: [],
        social_media_links: [],
        address_extensions: {
          phone_is_whatsapp: false,
          phone_secondary_is_whatsapp: false,
          accepts_delivery_orders: true,
          accepts_pickup_orders: true,
          accepts_dine_in: true
        },
        operating_hours: {},
        special_calendar: {},
        has_tenant_slug: false,
        has_wifi_networks: false,
        has_social_media_links: false,
        has_address_extensions: false,
        has_operating_hours: false,
        has_special_calendar: false,
        is_open_today: false,
        phone_is_whatsapp: false
      });
    }
  }, [currentTenant?.id]);

  // Load settings and tables when component mounts
  useEffect(() => {
    if (currentTenant) {
      loadTables();
      loadSettings();
      loadRestaurantSettings();
      loadZones();
    }
  }, [currentTenant, loadTables, loadRestaurantSettings]);

  const loadSettings = async () => {
    setIsInitialLoading(true);
    setError(null);

    try {
      console.log('🔍 RestaurantSettings: Loading tenant settings...');
      const tenantSettings = await tenantSettingsService.getSettings();
      console.log('🔍 RestaurantSettings: Loaded settings:', tenantSettings);

      setSettings(tenantSettings);
    } catch (error: any) {
      console.error('🔍 RestaurantSettings: Error loading settings:', error);
      setError(error.message || 'Failed to load settings');

      // Set default settings if API fails
      setSettings({
        currency: 'BRL',
        timezone: 'America/Sao_Paulo',
        operating_hours: {
          monday: { is_open: true, slots: [{ id: '1', open: '09:00', close: '22:00' }] },
          tuesday: { is_open: true, slots: [{ id: '1', open: '09:00', close: '22:00' }] },
          wednesday: { is_open: true, slots: [{ id: '1', open: '09:00', close: '22:00' }] },
          thursday: { is_open: true, slots: [{ id: '1', open: '09:00', close: '22:00' }] },
          friday: { is_open: true, slots: [{ id: '1', open: '09:00', close: '23:00' }] },
          saturday: { is_open: true, slots: [{ id: '1', open: '10:00', close: '23:00' }] },
          sunday: { is_open: false, slots: [{ id: '1', open: '10:00', close: '21:00' }] },
        },
        payment_methods: {
          cash: true,
          credit_card: true,
          debit_card: true,
          pix: true,
          digital_wallet: false,
        },
        multi_language_enabled: false,
        available_languages: [],
        loyalty_config: {
          enabled: false,
          pointsPerDollar: 1,
          dollarPerPoint: 0.01,
          minimumRedemption: 100,
          pointsExpiration: 365,
          tierSystem: false,
          welcomeBonus: 100
        },
        address: {
          street: '',
          city: '',
          state: '',
          zipCode: '',
          country: 'US'
        },
        tax_config: {
          baseTaxRate: 8.25,
          calculationMethod: 'incremental',
          allowProductOverrides: true,
          taxName: 'Sales Tax',
          taxDescription: 'Standard sales tax applied to orders'
        },
        wifi_networks: [],
        social_media_links: []
      });
    } finally {
      setIsInitialLoading(false);
    }
  };



  const loadZones = async () => {
    try {
      console.log('🔍 RestaurantSettings: Loading zones...');
      const availableZones = await tenantSettingsService.getZones();
      console.log('🔍 RestaurantSettings: Loaded zones:', availableZones);

      setZones(availableZones);
    } catch (error: any) {
      console.error('🔍 RestaurantSettings: Error loading zones:', error);
      // Use default zones if API fails
      setZones([
        'Main Dining',
        'Private Dining',
        'Bar Area',
        'Outdoor Patio',
        'Kitchen',
        'Office'
      ]);
    }
  };

  const handleSaveSettings = async () => {
    if (!settings) return;

    setIsLoading(true);
    setError(null);

    try {
      console.log('🔍 RestaurantSettings: Saving settings...', settings);
      await tenantSettingsService.updateSettings(settings);

      // Refresh tenant cache to update tenant selector
      console.log('🔄 Refreshing tenant cache...');
      await refreshTenant();
      console.log('✅ Tenant cache refreshed');

      setHasUnsavedChanges(false);
      toast.success('Settings saved successfully!');
    } catch (error: any) {
      console.error('🔍 RestaurantSettings: Error saving settings:', error);
      setError(error.message || 'Failed to save settings');
      toast.error('Failed to save settings');
    } finally {
      setIsLoading(false);
    }
  };

  const updateSettings = (field: keyof TenantSettings, value: any) => {
    if (!settings) return;

    setSettings(prev => prev ? { ...prev, [field]: value } : null);
    setHasUnsavedChanges(true);
  };



  const updateSettingsSection = async (section: string, data: any) => {
    setIsLoading(true);
    setError(null);

    try {
      console.log(`🔍 RestaurantSettings: Updating section ${section}:`, data);
      const updatedSettings = await tenantSettingsService.updateSection(section, data);

      setSettings(updatedSettings);
      setHasUnsavedChanges(false);
      toast.success(`${section} settings updated successfully!`);
    } catch (error: any) {
      console.error(`🔍 RestaurantSettings: Error updating ${section}:`, error);
      setError(error.message || `Failed to update ${section} settings`);
      toast.error(`Failed to update ${section} settings`);
    } finally {
      setIsLoading(false);
    }
  };

  const updateRestaurantOperatingHours = async (operatingHours: any) => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('🏪 RestaurantSettings: Updating operating hours:', operatingHours);
      const updatedSettings = await restaurantSettingsService.updateOperatingHours(operatingHours);

      setRestaurantSettings(updatedSettings);
      setHasUnsavedChanges(false);
      toast.success('Operating hours updated successfully!');
    } catch (error: any) {
      console.error('🏪 RestaurantSettings: Error updating operating hours:', error);
      setError(error.message || 'Failed to update operating hours');
      toast.error('Failed to update operating hours');
    } finally {
      setIsLoading(false);
    }
  };

  const updateRestaurantSpecialCalendar = async (specialCalendar: any) => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('🏪 RestaurantSettings: Updating special calendar:', specialCalendar);
      const updatedSettings = await restaurantSettingsService.updateSpecialCalendar(specialCalendar);

      setRestaurantSettings(updatedSettings);
      setHasUnsavedChanges(false);
      toast.success('Special calendar updated successfully!');
    } catch (error: any) {
      console.error('🏪 RestaurantSettings: Error updating special calendar:', error);
      setError(error.message || 'Failed to update special calendar');
      toast.error('Failed to update special calendar');
    } finally {
      setIsLoading(false);
    }
  };

  // Show loading state while initial data loads
  if (isInitialLoading) {
    return (
      <ModuleLayout
        title="Restaurant Settings"
        description="Configure your restaurant's operational settings"
        gradientFrom="gray-50"
        gradientVia="slate-50/30"
        gradientTo="zinc-50/20"
        titleGradientFrom="gray-600"
        titleGradientTo="slate-600"
      >
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-500">Loading settings...</p>
          </div>
        </div>
      </ModuleLayout>
    );
  }

  // Show error state if settings failed to load
  if (error && !settings) {
    return (
      <ModuleLayout
        title="Restaurant Settings"
        description="Configure your restaurant's operational settings"
        gradientFrom="gray-50"
        gradientVia="slate-50/30"
        gradientTo="zinc-50/20"
        titleGradientFrom="gray-600"
        titleGradientTo="slate-600"
      >
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <ExclamationTriangleIcon className="h-8 w-8 text-red-500 mx-auto mb-2" />
            <p className="text-sm text-gray-900 font-medium">Failed to load settings</p>
            <p className="text-sm text-gray-500 mt-1">{error}</p>
            <button
              onClick={loadSettings}
              className="mt-4 px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
            >
              Retry
            </button>
          </div>
        </div>
      </ModuleLayout>
    );
  }

  if (!settings) {
    return null;
  }

  const tabs = [
    { id: 'business', name: 'Business Info & Location', icon: BuildingOfficeIcon },
    { id: 'tables', name: 'Table Management', icon: TableCellsIcon },
    { id: 'hours', name: 'Operating Hours & Calendar', icon: ClockIcon },
    { id: 'payments', name: 'Payment Methods', icon: CreditCardIcon },
    { id: 'languages', name: 'Languages', icon: LanguageIcon },
    { id: 'loyalty', name: 'Loyalty System', icon: GiftIcon },
    { id: 'tax', name: 'Tax Settings', icon: CalculatorIcon },
    { id: 'wifi', name: 'WiFi Networks', icon: WifiIcon },
    { id: 'social', name: 'Social Media', icon: ShareIcon },
    { id: 'subscription', name: 'Subscription', icon: SubscriptionIcon },
  ];



  return (
    <ModuleLayout
      title="Restaurant Settings"
      description="Configure your restaurant's operational settings"
      gradientFrom="gray-50"
      gradientVia="slate-50/30"
      gradientTo="zinc-50/20"
      titleGradientFrom="gray-600"
      titleGradientTo="slate-600"
      actions={
        <div className="flex items-center space-x-3">
          {hasUnsavedChanges && (
            <span className="text-sm text-yellow-600 font-medium">
              Unsaved changes
            </span>
          )}
          <button
            onClick={handleSaveSettings}
            disabled={isLoading || !hasUnsavedChanges}
            className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Saving...' : 'Save Settings'}
          </button>
        </div>
      }
    >
      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                activeTab === tab.id
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              <span>{tab.name}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Error Banner */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
            </div>
          </div>
        </div>
      )}



      {/* Business Information & Location Settings */}
      {activeTab === 'business' && (
        <BusinessInfoTab
            settings={{
              business_name: settings.business_name || '', // Get from tenant settings
              business_type: settings.business_type || '', // Get from tenant settings
              currency: settings.currency,
              currency_format: settings.currency_format,
              timezone: settings.timezone,
              subscription_plan: settings.subscription_plan,
              subscription_status: settings.subscription_status,
              address: settings.address,
              tenant_slug: settings.tenant_slug || '', // Get from tenant settings
            contact: {
              phone: settings.address?.phone,
              phone_is_whatsapp: restaurantSettings?.address_extensions?.phone_is_whatsapp || false,
              phone_secondary: settings.address?.phone_secondary,
              phone_secondary_is_whatsapp: restaurantSettings?.address_extensions?.phone_secondary_is_whatsapp || false,
              fax: settings.address?.fax
            }
          }}
          savingField={savingField}
          onUpdate={async (field, value) => {
            if (field === 'tenant_slug' || field === 'business_name' || field === 'business_type') {
              // Update business info in tenant settings (single source of truth)
              try {
                const updatedSettings = await tenantSettingsService.updateSettings({
                  [field]: value
                });
                setSettings(updatedSettings);

                // Refresh tenant cache to update tenant selector
                await refreshTenant();

                toast.success('Business information updated successfully!');
              } catch (error: any) {
                console.error('Error updating business info:', error);
                toast.error('Failed to update business information');
              }
            } else if (field === 'contact') {
              // Update contact fields in both places
              const newAddress = {
                ...settings.address,
                phone: value.phone,
                phone_secondary: value.phone_secondary,
                fax: value.fax
              };
              updateSettings('address', newAddress);

              // Update WhatsApp flags in restaurant settings
              if (restaurantSettings) {
                try {
                  const updatedExtensions = {
                    accepts_delivery_orders: true,
                    accepts_pickup_orders: true,
                    accepts_dine_in: true,
                    ...restaurantSettings.address_extensions,
                    phone_is_whatsapp: value.phone_is_whatsapp,
                    phone_secondary_is_whatsapp: value.phone_secondary_is_whatsapp
                  };
                  const updatedSettings = await restaurantSettingsService.updateAddressExtensions(updatedExtensions);
                  setRestaurantSettings(updatedSettings);
                } catch (error: any) {
                  console.error('Error updating contact extensions:', error);
                }
              }
            } else if (field === 'business_name' || field === 'business_type') {
              // These fields are now handled above in the tenant settings update
              console.log('Business fields are handled by tenant settings update');
            } else if (field === 'address') {
              // Auto-save address changes with rate limiting
              try {
                setSavingField(field);
                console.log('[Settings] Auto-saving address:', value);

                // Update UI immediately for better UX
                const optimisticSettings = { ...settings, address: value };
                setSettings(optimisticSettings);

                // Save to backend with retry logic
                const updatedSettings = await tenantSettingsService.updateLocationSettings({
                  address: value
                });

                // Update with actual backend response
                setSettings(updatedSettings);
                setHasUnsavedChanges(false);
                console.log('[Settings] Address saved successfully');

                // Show success feedback
                toast.success('Address updated successfully!');

              } catch (error: any) {
                console.error('Error updating address:', error);

                // Show user-friendly error message
                if (error.response?.status === 401) {
                  toast.error('Session expired. Please refresh the page and try again.');
                } else {
                  toast.error('Failed to save address. Please try again.');
                }

                // Revert optimistic update on error
                try {
                  const revertedSettings = await tenantSettingsService.getSettings();
                  setSettings(revertedSettings);
                  console.log('[Settings] Reverted to backend state due to error');
                } catch (revertError) {
                  console.error('[Settings] Failed to revert settings:', revertError);
                }
              } finally {
                setSavingField(null);
              }
            } else if (field === 'currency' || field === 'currency_format' || field === 'timezone') {
              // Auto-save currency and timezone settings with optimistic updates
              try {
                setSavingField(field);
                console.log(`[Settings] Auto-saving ${field}:`, value);

                // Optimistic update - update UI immediately
                const optimisticSettings = { ...settings };
                if (field === 'currency') {
                  optimisticSettings.currency = value;
                  // Also apply default format for the new currency
                  const DEFAULT_FORMATS: Record<string, any> = {
                    'USD': { decimal_separator: '.', thousands_separator: ',', symbol_position: 'left', symbol_spacing: false },
                    'BRL': { decimal_separator: ',', thousands_separator: '.', symbol_position: 'left', symbol_spacing: true },
                    'EUR': { decimal_separator: ',', thousands_separator: '.', symbol_position: 'right', symbol_spacing: true },
                    'GBP': { decimal_separator: '.', thousands_separator: ',', symbol_position: 'left', symbol_spacing: false },
                    'JPY': { decimal_separator: '.', thousands_separator: ',', symbol_position: 'left', symbol_spacing: false },
                  };
                  optimisticSettings.currency_format = DEFAULT_FORMATS[value] || DEFAULT_FORMATS['USD'];
                } else if (field === 'currency_format') {
                  optimisticSettings.currency_format = value;
                } else {
                  optimisticSettings[field] = value;
                }

                // Update UI immediately for better UX
                setSettings(optimisticSettings);

                let updateData: any = {};

                if (field === 'currency') {
                  // Map currency to default_currency
                  updateData.default_currency = value;
                } else if (field === 'currency_format') {
                  // Map currency_format to currency_config structure
                  const currentCurrency = settings.currency || 'BRL';
                  console.log(`[Settings] Processing currency_format for ${currentCurrency}:`, value);

                  // Ensure we have a complete currency_format structure
                  const currentConfig = settings.currency_format || {};
                  console.log(`[Settings] Current currency_format:`, currentConfig);

                  // Get correct default formats for all currencies
                  const getDefaultFormat = (currency: string) => {
                    const DEFAULT_FORMATS: Record<string, any> = {
                      'USD': { decimal_separator: '.', thousands_separator: ',', symbol_position: 'left', symbol_spacing: false },
                      'BRL': { decimal_separator: ',', thousands_separator: '.', symbol_position: 'left', symbol_spacing: true },
                      'EUR': { decimal_separator: ',', thousands_separator: '.', symbol_position: 'right', symbol_spacing: true },
                      'GBP': { decimal_separator: '.', thousands_separator: ',', symbol_position: 'left', symbol_spacing: false },
                      'JPY': { decimal_separator: '.', thousands_separator: ',', symbol_position: 'left', symbol_spacing: false },
                    };
                    return DEFAULT_FORMATS[currency] || DEFAULT_FORMATS['USD'];
                  };

                  // Update the currency_format directly (single format object)
                  updateData.currency_format = value;
                  console.log(`[Settings] New currency_format to send:`, updateData.currency_format);
                } else {
                  // timezone and other fields
                  updateData[field] = value;
                }

                console.log(`[Settings] Sending to backend:`, updateData);
                const updatedSettings = await tenantSettingsService.updateSettings(updateData);
                console.log(`[Settings] Backend response:`, updatedSettings);

                // Update with actual backend response (should match optimistic update)
                setSettings(updatedSettings);
                console.log(`[Settings] Settings confirmed, new currency:`, updatedSettings.currency);
                console.log(`[Settings] Settings confirmed, new currency_format:`, updatedSettings.currency_format);
                setHasUnsavedChanges(false);
                toast.success(`${field === 'currency_format' ? 'Currency format' : field === 'currency' ? 'Currency' : field} updated successfully!`);
              } catch (error: any) {
                console.error(`Error updating ${field}:`, error);

                // Revert optimistic update on error
                const revertedSettings = await tenantSettingsService.getSettings();
                setSettings(revertedSettings);
                console.log(`[Settings] Reverted to backend state due to error`);

                toast.error(`Failed to update ${field}`);
              } finally {
                setSavingField(null);
              }
            } else {
              updateSettings(field as keyof TenantSettings, value);
            }
          }}
        />
      )}

      {/* Table Management */}
      {activeTab === 'tables' && (
        <TableManagement />
      )}

      {/* Operating Hours & Special Calendar */}
      {activeTab === 'hours' && restaurantSettings && (
        <OperatingHoursTab
          operatingHours={restaurantSettings.operating_hours as any || {}}
          specialCalendar={restaurantSettings.special_calendar || {}}
          onUpdate={(day, field, value) => {
            const currentHours = restaurantSettings.operating_hours || {};
            const updatedHours = {
              ...currentHours,
              [day]: value
            };
            updateRestaurantOperatingHours(updatedHours);
          }}
          onSpecialCalendarUpdate={(calendar) => {
            updateRestaurantSpecialCalendar(calendar);
          }}
        />
      )}



      {/* Payment Methods */}
      {activeTab === 'payments' && (
        <PaymentMethodsTab
          paymentMethods={settings.payment_methods}
          onUpdate={(method, enabled) => {
            const currentMethods = settings.payment_methods || {};
            const updatedMethods = {
              ...currentMethods,
              [method]: enabled
            };
            updateSettings('payment_methods', updatedMethods);
          }}
        />
      )}

      {/* Languages Settings */}
      {activeTab === 'languages' && (
        <LanguageSettingsTab
          multiLanguageEnabled={settings.multi_language_enabled}
          availableLanguages={settings.available_languages}
          onUpdate={(field, value) => updateSettings(field as keyof TenantSettings, value)}
        />
      )}

      {/* Loyalty System */}
      {activeTab === 'loyalty' && (
        <LoyaltySystemTab
          loyaltyConfig={settings.loyalty_config}
          onUpdate={(config) => updateSettings('loyalty_config', config)}
        />
      )}



      {/* Tax Settings */}
      {activeTab === 'tax' && (
        <TaxSettingsTab
          taxConfig={settings.tax_config}
          onUpdate={(config) => updateSettings('tax_config', config)}
        />
      )}

      {/* WiFi Networks */}
      {activeTab === 'wifi' && restaurantSettings && (
        <WiFiNetworksTab
          networks={restaurantSettings.wifi_networks as any || []}
          zones={zones}
          onUpdate={async (networks) => {
            try {
              const updatedSettings = await restaurantSettingsService.updateWiFiNetworks(networks as any);
              setRestaurantSettings(updatedSettings);
              toast.success('WiFi networks updated successfully!');
            } catch (error: any) {
              console.error('Error updating WiFi networks:', error);
              toast.error('Failed to update WiFi networks');
            }
          }}
        />
      )}

      {/* Social Media */}
      {activeTab === 'social' && restaurantSettings && (
        <SocialMediaTab
          socialMediaLinks={restaurantSettings.social_media_links || []}
          onUpdate={async (links) => {
            try {
              const updatedSettings = await restaurantSettingsService.updateSocialMediaLinks(links);
              setRestaurantSettings(updatedSettings);
              toast.success('Social media links updated successfully!');
            } catch (error: any) {
              console.error('Error updating social media links:', error);
              toast.error('Failed to update social media links');
            }
          }}
        />
      )}

      {/* Subscription Management */}
      {activeTab === 'subscription' && (
        <SubscriptionTab
          onUpdate={(subscription) => {
            // Handle subscription updates
            console.log('Subscription updated:', subscription);
          }}
        />
      )}


    </ModuleLayout>
  );
}
