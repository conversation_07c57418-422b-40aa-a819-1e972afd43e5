"""
Language schemas for the i18n module.
"""

import uuid
from pydantic import BaseModel, Field, ConfigDict


class LanguageBase(BaseModel):
    """Base schema for Language."""

    code: str = Field(
        ..., min_length=2, max_length=10, description="Language code (e.g., 'en', 'pt-br')"
    )
    name: str = Field(
        ...,
        min_length=1,
        max_length=100,
        description="Language name in English (e.g., 'English', 'Portuguese (Brazil)')",
    )
    native_name: str = Field(
        ...,
        min_length=1,
        max_length=100,
        description="Language name in its native form (e.g., 'English', 'Português (Brasil)')",
    )
    is_active: bool = Field(True, description="Whether the language is active")
    is_default: bool = Field(False, description="Whether this is the default language")


class LanguageCreate(LanguageBase):
    """Schema for creating a new Language."""

    pass


class LanguageUpdate(BaseModel):
    """Schema for updating an existing Language."""

    code: str | None = Field(None, min_length=2, max_length=10)
    name: str | None = Field(None, min_length=1, max_length=100)
    native_name: str | None = Field(None, min_length=1, max_length=100)
    is_active: bool | None = None
    is_default: bool | None = None


class LanguageRead(LanguageBase):
    """Schema for reading a Language."""

    id: uuid.UUID
    version_code: str = Field(..., description="Version code for tracking language updates")

    model_config = ConfigDict(from_attributes=True)


class LanguageVersionCheck(BaseModel):
    """Schema for checking language version."""

    code: str = Field(..., min_length=2, max_length=10, description="Language code")
    version_code: str = Field(..., min_length=6, max_length=6, description="Current version code")
