import uuid
from sqlalchemy import Column, String, ForeignKey, Numeric, DateTime, Text
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import Base


class OnlineOrder(Base):
    """Model for online orders."""

    __tablename__ = "online_orders"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)

    # Customer information
    customer_name = Column(String(255), nullable=False)
    customer_email = Column(String(255), nullable=True)
    customer_phone = Column(String(50), nullable=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # Order details
    order_number = Column(String(50), nullable=False, index=True)
    status = Column(String(50), nullable=False, index=True)
    total = Column(Numeric(10, 2), nullable=False)
    notes = Column(Text, nullable=True)

    # Linked transaction (if processed)
    transaction_id = Column(UUID(as_uuid=True), ForeignKey("sale_transactions.id"), nullable=True)

    # Additional data
    order_metadata = Column(JSONB, nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )

    # Relationships
    tenant = relationship("Tenant")
    transaction = relationship("SaleTransaction")
    user = relationship("User")

    def __repr__(self):
        return f"<OnlineOrder(id={self.id}, order_number={self.order_number}, status={self.status})>"  # noqa: E501
