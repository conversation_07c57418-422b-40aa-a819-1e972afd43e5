import 'package:uuid/uuid.dart';

class MenuItemModel {
  final String id;
  final String name;
  final String description;
  final double price;
  final String category;
  final String? image;
  final bool isAvailable;
  final bool isVegetarian;
  final bool isVegan;
  final bool isGlutenFree;
  final bool isSpicy;
  final int preparationTime; // in minutes
  final List<String> allergens;
  final List<MenuItemModifier> modifiers;
  final Map<String, dynamic>? nutritionalInfo;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int sortOrder;

  MenuItemModel({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.category,
    this.image,
    this.isAvailable = true,
    this.isVegetarian = false,
    this.isVegan = false,
    this.isGlutenFree = false,
    this.isSpicy = false,
    this.preparationTime = 15,
    this.allergens = const [],
    this.modifiers = const [],
    this.nutritionalInfo,
    required this.createdAt,
    required this.updatedAt,
    this.sortOrder = 0,
  });

  factory MenuItemModel.create({
    required String name,
    required String description,
    required double price,
    required String category,
    String? image,
    bool isAvailable = true,
    bool isVegetarian = false,
    bool isVegan = false,
    bool isGlutenFree = false,
    bool isSpicy = false,
    int preparationTime = 15,
    List<String> allergens = const [],
    List<MenuItemModifier> modifiers = const [],
    Map<String, dynamic>? nutritionalInfo,
    int sortOrder = 0,
  }) {
    final now = DateTime.now();
    return MenuItemModel(
      id: const Uuid().v4(),
      name: name,
      description: description,
      price: price,
      category: category,
      image: image,
      isAvailable: isAvailable,
      isVegetarian: isVegetarian,
      isVegan: isVegan,
      isGlutenFree: isGlutenFree,
      isSpicy: isSpicy,
      preparationTime: preparationTime,
      allergens: allergens,
      modifiers: modifiers,
      nutritionalInfo: nutritionalInfo,
      createdAt: now,
      updatedAt: now,
      sortOrder: sortOrder,
    );
  }

  factory MenuItemModel.fromJson(Map<String, dynamic> json) {
    return MenuItemModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      price: (json['price'] ?? 0.0).toDouble(),
      category: json['category'] ?? '',
      image: json['image'],
      isAvailable: json['isAvailable'] ?? true,
      isVegetarian: json['isVegetarian'] ?? false,
      isVegan: json['isVegan'] ?? false,
      isGlutenFree: json['isGlutenFree'] ?? false,
      isSpicy: json['isSpicy'] ?? false,
      preparationTime: json['preparationTime'] ?? 15,
      allergens: List<String>.from(json['allergens'] ?? []),
      modifiers: (json['modifiers'] as List<dynamic>? ?? [])
          .map((m) => MenuItemModifier.fromJson(m))
          .toList(),
      nutritionalInfo: json['nutritionalInfo'] != null
          ? Map<String, dynamic>.from(json['nutritionalInfo'])
          : null,
      createdAt: DateTime.parse(
          json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(
          json['updatedAt'] ?? DateTime.now().toIso8601String()),
      sortOrder: json['sortOrder'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'category': category,
      'image': image,
      'isAvailable': isAvailable,
      'isVegetarian': isVegetarian,
      'isVegan': isVegan,
      'isGlutenFree': isGlutenFree,
      'isSpicy': isSpicy,
      'preparationTime': preparationTime,
      'allergens': allergens,
      'modifiers': modifiers.map((m) => m.toJson()).toList(),
      'nutritionalInfo': nutritionalInfo,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'sortOrder': sortOrder,
    };
  }

  MenuItemModel copyWith({
    String? id,
    String? name,
    String? description,
    double? price,
    String? category,
    String? image,
    bool? isAvailable,
    bool? isVegetarian,
    bool? isVegan,
    bool? isGlutenFree,
    bool? isSpicy,
    int? preparationTime,
    List<String>? allergens,
    List<MenuItemModifier>? modifiers,
    Map<String, dynamic>? nutritionalInfo,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? sortOrder,
  }) {
    return MenuItemModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      category: category ?? this.category,
      image: image ?? this.image,
      isAvailable: isAvailable ?? this.isAvailable,
      isVegetarian: isVegetarian ?? this.isVegetarian,
      isVegan: isVegan ?? this.isVegan,
      isGlutenFree: isGlutenFree ?? this.isGlutenFree,
      isSpicy: isSpicy ?? this.isSpicy,
      preparationTime: preparationTime ?? this.preparationTime,
      allergens: allergens ?? this.allergens,
      modifiers: modifiers ?? this.modifiers,
      nutritionalInfo: nutritionalInfo ?? this.nutritionalInfo,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      sortOrder: sortOrder ?? this.sortOrder,
    );
  }

  List<String> get dietaryTags {
    final tags = <String>[];
    if (isVegetarian) tags.add('Vegetariano');
    if (isVegan) tags.add('Vegano');
    if (isGlutenFree) tags.add('Sem Glúten');
    if (isSpicy) tags.add('Picante');
    return tags;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MenuItemModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'MenuItemModel(id: $id, name: $name, price: $price, category: $category)';
  }
}

class MenuItemModifier {
  final String id;
  final String name;
  final String type; // 'single', 'multiple', 'quantity'
  final bool isRequired;
  final int? minSelections;
  final int? maxSelections;
  final List<ModifierOption> options;

  MenuItemModifier({
    required this.id,
    required this.name,
    required this.type,
    this.isRequired = false,
    this.minSelections,
    this.maxSelections,
    this.options = const [],
  });

  factory MenuItemModifier.fromJson(Map<String, dynamic> json) {
    return MenuItemModifier(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      type: json['type'] ?? 'single',
      isRequired: json['isRequired'] ?? false,
      minSelections: json['minSelections'],
      maxSelections: json['maxSelections'],
      options: (json['options'] as List<dynamic>? ?? [])
          .map((o) => ModifierOption.fromJson(o))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'isRequired': isRequired,
      'minSelections': minSelections,
      'maxSelections': maxSelections,
      'options': options.map((o) => o.toJson()).toList(),
    };
  }
}

class ModifierOption {
  final String id;
  final String name;
  final double priceModifier; // Can be positive or negative
  final bool isDefault;
  final bool isAvailable;

  ModifierOption({
    required this.id,
    required this.name,
    this.priceModifier = 0.0,
    this.isDefault = false,
    this.isAvailable = true,
  });

  factory ModifierOption.fromJson(Map<String, dynamic> json) {
    return ModifierOption(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      priceModifier: (json['priceModifier'] ?? 0.0).toDouble(),
      isDefault: json['isDefault'] ?? false,
      isAvailable: json['isAvailable'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'priceModifier': priceModifier,
      'isDefault': isDefault,
      'isAvailable': isAvailable,
    };
  }
}

// Menu categories
class MenuCategories {
  static const String appetizers = 'Entradas';
  static const String mainCourses = 'Pratos Principais';
  static const String desserts = 'Sobremesas';
  static const String beverages = 'Bebidas';
  static const String salads = 'Saladas';
  static const String soups = 'Sopas';
  static const String pasta = 'Massas';
  static const String pizza = 'Pizzas';
  static const String grilled = 'Grelhados';
  static const String seafood = 'Frutos do Mar';
  static const String vegetarian = 'Vegetarianos';
  static const String kids = 'Infantil';
  static const String specials = 'Especiais';

  static List<String> get all => [
        appetizers,
        mainCourses,
        desserts,
        beverages,
        salads,
        soups,
        pasta,
        pizza,
        grilled,
        seafood,
        vegetarian,
        kids,
        specials,
      ];
}