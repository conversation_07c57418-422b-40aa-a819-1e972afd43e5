import uuid
from typing import Optional, TYPE_CHECKING
import logging
import uuid

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.modules.core.auth.security import (
    verify_password,
    create_access_token,
    create_refresh_token,
)
from app.modules.core.auth.security_modern import verify_password_with_migration

if TYPE_CHECKING:
    from app.modules.core.users.models.user import User

logger = logging.getLogger(__name__)


class AuthService:
    async def get_user_by_email(self, db: AsyncSession, *, email: str) -> Optional["User"]:
        """Fetches a user by email."""
        from app.modules.core.users.models.user import User

        logger.info(f"AuthService.get_user_by_email: Looking for user with email: {email}")
        result = await db.execute(select(User).where(User.email == email))
        user = result.scalars().first()
        if user:
            logger.info(f"AuthService.get_user_by_email: User found: {user.id}")
        else:
            logger.warning(f"AuthService.get_user_by_email: User with email {email} NOT found.")
        return user

    async def get_user_by_id(self, db: AsyncSession, *, user_id: uuid.UUID) -> Optional["User"]:
        """Fetches a user by ID."""
        from app.modules.core.users.models.user import User

        logger.info(
            f"AuthService.get_user_by_id: Looking for user with ID: {user_id} in DB session: {db}"
        )

        result = await db.execute(select(User).where(User.id == user_id))
        user = result.scalars().first()
        logger.info(
            f"AuthService.get_user_by_id: Query executed for user_id: {user_id}. User found in DB: {'YES' if user else 'NO'}"
        )
        if user:
            logger.info(
                f"AuthService.get_user_by_id: User {user_id} found: {user.email}, system_role: {user.system_role}, is_active: {user.is_active}"
            )
        else:
            logger.warning(
                f"AuthService.get_user_by_id: User {user_id} NOT found in DB session: {db}"
            )
        return user

    async def authenticate_user(
        self, db: AsyncSession, *, email: str, password: str
    ) -> Optional["User"]:
        """
        Authenticates a user based on email and password.
        Automatically migrates bcrypt hashes to Argon2id during authentication.

        Args:
            db: Async database session.
            email: User's email.
            password: User's provided password.

        Returns:
            The User object if authentication is successful, None otherwise.
        """
        user = await self.get_user_by_email(db, email=email)

        if not user:
            return None

        # Use modern verification with automatic migration
        is_valid, new_hash = verify_password_with_migration(password, user.hashed_password)

        if not is_valid:
            return None

        # If migration occurred, update the user's hash in database
        if new_hash:
            logger.info(f"AuthService.authenticate_user: Migrating password hash for user: {user.email}")
            user.hashed_password = new_hash
            db.add(user)
            await db.commit()
            await db.refresh(user)

        return user

    def create_tokens(self, user_id: uuid.UUID) -> tuple[str, str]:
        """Creates access and refresh tokens for a user."""
        access_token = create_access_token(subject=str(user_id))
        refresh_token = create_refresh_token(subject=str(user_id))
        return access_token, refresh_token


# Service instance
auth_service = AuthService()
