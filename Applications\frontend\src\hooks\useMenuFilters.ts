'use client';

import { useMemo } from 'react';
import { MenuCategory, MenuItem } from '@/hooks/useMenuManagement';

interface UseMenuFiltersProps {
  categories: MenuCategory[];
  items: MenuItem[];
  selectedCategory: MenuCategory | null;
  selectedSubcategoryId: string | null;
  searchQuery: string;
  filterBy: 'category' | 'items';
  // New advanced filter props
  availabilityFilter?: 'all' | 'available' | 'unavailable';
  typeFilter?: 'all' | 'combo' | 'individual';
  priceRange?: { min: number; max: number } | null;
}

interface UseMenuFiltersReturn {
  filteredItems: MenuItem[];
  filteredCategories: MenuCategory[];
  categoriesWithChildren: MenuCategory[];
  finalFilteredItems: MenuItem[];
  selectedCategoryWithChildren: MenuCategory | null;
}

export function useMenuFilters({
  categories,
  items,
  selectedCategory,
  selectedSubcategoryId,
  searchQuery,
  filterBy,
  availabilityFilter = 'all',
  typeFilter = 'all',
  priceRange = null,
}: UseMenuFiltersProps): UseMenuFiltersReturn {
  
  // Organize children within their parent categories
  // Categories already come with hierarchical structure from useMenuOperations
  const categoriesWithChildren = useMemo(() => {
    const mainCategories = categories.filter(cat => !cat.parent_id);

    // Remove duplicates based on ID and name (extra safety for default category)
    const uniqueCategories = mainCategories.filter((cat, index, self) => {
      const firstOccurrenceIndex = self.findIndex(c => c.id === cat.id);
      if (firstOccurrenceIndex !== index) return false;

      // Extra check for default category duplicates by name
      if (cat.is_default ||
          cat.name.toLowerCase() === 'sem categoria' ||
          cat.name.toLowerCase() === 'uncategorized') {
        const defaultIndex = self.findIndex(c =>
          c.is_default ||
          c.name.toLowerCase() === 'sem categoria' ||
          c.name.toLowerCase() === 'uncategorized'
        );
        return defaultIndex === index;
      }

      return true;
    });



    return uniqueCategories.map(mainCat => ({
      ...mainCat,
      // Preserve children structure from useMenuOperations
      children: mainCat.children || []
    }));
  }, [categories]);

  // Filter categories based on search query and selected category
  const filteredCategories = useMemo(() => {
    let categoriesToFilter = categoriesWithChildren;
    
    // If filtering by a specific category, include its children too
    if (selectedCategory && filterBy === 'category') {
      if (selectedCategory.parent_id) {
        // If it's a child category, show only the parent and its children
        const parentCategory = categories.find(cat => cat.id === selectedCategory.parent_id);
        if (parentCategory) {
          categoriesToFilter = [parentCategory].map(mainCat => ({
            ...mainCat,
            // Preserve children structure from useMenuOperations if it exists
            children: mainCat.children || []
          }));
        }
      }
    }

    // Filter by search query while maintaining original order
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return categoriesToFilter.filter(cat =>
        cat.name.toLowerCase().includes(query) ||
        cat.description?.toLowerCase().includes(query) ||
        cat.children?.some((sub: any) =>
          sub.name.toLowerCase().includes(query) ||
          sub.description?.toLowerCase().includes(query)
        )
      );
    }

    return categoriesToFilter;
  }, [categoriesWithChildren, selectedCategory, searchQuery, filterBy, categories]);

  // Filter items based on search query and advanced filters
  const filteredItems = useMemo(() => {
    let result = items;

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(item =>
        item.name.toLowerCase().includes(query) ||
        item.description?.toLowerCase().includes(query) ||
        // Also search in allergens if they exist
        (item.allergens && JSON.stringify(item.allergens).toLowerCase().includes(query))
      );
    }

    // Apply availability filter
    if (availabilityFilter !== 'all') {
      result = result.filter(item => {
        if (availabilityFilter === 'available') {
          return item.is_available && item.is_active;
        } else if (availabilityFilter === 'unavailable') {
          return !item.is_available || !item.is_active;
        }
        return true;
      });
    }

    // Apply type filter
    if (typeFilter !== 'all') {
      result = result.filter(item => {
        if (typeFilter === 'combo') {
          return item.is_combo;
        } else if (typeFilter === 'individual') {
          return !item.is_combo;
        }
        return true;
      });
    }

    // Apply price range filter
    if (priceRange) {
      result = result.filter(item => {
        const price = typeof item.base_price === 'string'
          ? parseFloat(item.base_price)
          : item.base_price || 0;
        return price >= priceRange.min && price <= priceRange.max;
      });
    }

    return result;
  }, [items, searchQuery, availabilityFilter, typeFilter, priceRange]);

  // Apply category and subcategory filters to items
  const finalFilteredItems = useMemo(() => {
    let result = filteredItems;
    
    // If a category is selected in sidebar, filter by it
    if (selectedCategory) {
      if (selectedSubcategoryId) {
        // Show only items from specific subcategory
        result = result.filter(item => item.category_id === selectedSubcategoryId);
      } else {
        // Show items from selected category and all its subcategories
        const categoryIds = [selectedCategory.id];
        
        // Find subcategories from selected category's children
        const subcategories = selectedCategory.children || [];
        categoryIds.push(...subcategories.map(child => child.id));
        
        result = result.filter(item => categoryIds.includes(item.category_id));
      }
    }
    
    return result;
  }, [filteredItems, selectedCategory, selectedSubcategoryId]);

  // Create selectedCategory with children for SubcategoryTabs
  const selectedCategoryWithChildren = useMemo(() => {
    if (!selectedCategory) return null;
    
    const subcategories = selectedCategory.children || [];
    
    return {
      ...selectedCategory,
      children: subcategories
    };
  }, [selectedCategory]);

  return {
    filteredItems,
    filteredCategories,
    categoriesWithChildren,
    finalFilteredItems,
    selectedCategoryWithChildren,
  };
}
