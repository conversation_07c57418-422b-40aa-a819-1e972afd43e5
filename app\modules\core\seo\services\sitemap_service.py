"""
Sitemap Service

Business logic for sitemap generation and management.
"""

import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from xml.etree.ElementTree import Element, SubElement, tostring
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, distinct

from app.modules.core.seo.models.sitemap_entry import SitemapEntry
from app.modules.core.seo.models.url_slug import URLSlug
from app.modules.core.seo.schemas.sitemap_schemas import (
    SitemapEntryCreate, SitemapEntryUpdate, SitemapResponse, 
    SitemapIndexResponse, SitemapURLEntry, SitemapIndexEntry
)
from app.modules.core.i18n.models.language import Language


class SitemapService:
    """Service for sitemap generation and management."""

    async def create_sitemap_entry(
        self,
        db: AsyncSession,
        entry_data: SitemapEntryCreate,
        user_id: Optional[uuid.UUID] = None
    ) -> SitemapEntry:
        """Create a new sitemap entry."""
        sitemap_entry = SitemapEntry(
            **entry_data.dict(),
            created_by_id=user_id
        )
        
        db.add(sitemap_entry)
        await db.commit()
        await db.refresh(sitemap_entry)
        return sitemap_entry

    async def get_sitemap_entry(
        self,
        db: AsyncSession,
        content_type: str,
        content_id: uuid.UUID,
        language_code: str
    ) -> Optional[SitemapEntry]:
        """Get sitemap entry for specific content and language."""
        result = await db.execute(
            select(SitemapEntry).filter(
                and_(
                    SitemapEntry.content_type == content_type,
                    SitemapEntry.content_id == content_id,
                    SitemapEntry.language_code == language_code,
                    SitemapEntry.is_active == True
                )
            )
        )
        return result.scalar_one_or_none()

    async def update_sitemap_entry(
        self,
        db: AsyncSession,
        entry_id: uuid.UUID,
        entry_data: SitemapEntryUpdate
    ) -> Optional[SitemapEntry]:
        """Update sitemap entry."""
        result = await db.execute(
            select(SitemapEntry).filter(SitemapEntry.id == entry_id)
        )
        sitemap_entry = result.scalar_one_or_none()
        
        if not sitemap_entry:
            return None

        update_data = entry_data.dict(exclude_unset=True)
        if update_data:
            for field, value in update_data.items():
                setattr(sitemap_entry, field, value)
            
            sitemap_entry.last_modified = datetime.utcnow()
            await db.commit()
            await db.refresh(sitemap_entry)
        
        return sitemap_entry

    async def generate_sitemap_xml(
        self,
        db: AsyncSession,
        language_code: Optional[str] = None,
        content_type: Optional[str] = None,
        base_url: str = "https://example.com"
    ) -> str:
        """Generate XML sitemap."""
        # Build query
        query = select(SitemapEntry).filter(
            and_(
                SitemapEntry.is_active == True,
                SitemapEntry.is_indexed == True
            )
        )
        
        if language_code:
            query = query.filter(SitemapEntry.language_code == language_code)
        
        if content_type:
            query = query.filter(SitemapEntry.content_type == content_type)
        
        query = query.order_by(SitemapEntry.priority.desc(), SitemapEntry.last_modified.desc())
        
        result = await db.execute(query)
        entries = result.scalars().all()
        
        # Create XML
        urlset = Element("urlset")
        urlset.set("xmlns", "http://www.sitemaps.org/schemas/sitemap/0.9")
        urlset.set("xmlns:xhtml", "http://www.w3.org/1999/xhtml")
        urlset.set("xmlns:image", "http://www.google.com/schemas/sitemap-image/1.1")
        
        for entry in entries:
            url_elem = SubElement(urlset, "url")
            
            # Location
            loc = SubElement(url_elem, "loc")
            loc.text = f"{base_url.rstrip('/')}{entry.url}"
            
            # Last modified
            if entry.last_modified:
                lastmod = SubElement(url_elem, "lastmod")
                lastmod.text = entry.last_modified.strftime("%Y-%m-%d")
            
            # Change frequency
            changefreq = SubElement(url_elem, "changefreq")
            changefreq.text = entry.change_frequency
            
            # Priority
            priority = SubElement(url_elem, "priority")
            priority.text = f"{entry.sitemap_priority:.1f}"
            
            # Hreflang alternates
            hreflang_data = await self._get_hreflang_for_entry(db, entry, base_url)
            for hreflang in hreflang_data:
                link = SubElement(url_elem, "xhtml:link")
                link.set("rel", "alternate")
                link.set("hreflang", hreflang["hreflang"])
                link.set("href", hreflang["href"])
            
            # Image information
            if entry.image_url:
                image = SubElement(url_elem, "image:image")
                image_loc = SubElement(image, "image:loc")
                image_loc.text = entry.image_url
                
                if entry.title:
                    image_title = SubElement(image, "image:title")
                    image_title.text = entry.title
        
        return tostring(urlset, encoding="unicode", method="xml")

    async def generate_sitemap_index_xml(
        self,
        db: AsyncSession,
        base_url: str = "https://example.com"
    ) -> str:
        """Generate XML sitemap index."""
        # Get all languages with sitemap entries
        result = await db.execute(
            select(distinct(SitemapEntry.language_code))
            .filter(
                and_(
                    SitemapEntry.is_active == True,
                    SitemapEntry.is_indexed == True
                )
            )
            .order_by(SitemapEntry.language_code)
        )
        languages = result.scalars().all()
        
        # Get all content types with sitemap entries
        result = await db.execute(
            select(distinct(SitemapEntry.content_type))
            .filter(
                and_(
                    SitemapEntry.is_active == True,
                    SitemapEntry.is_indexed == True
                )
            )
            .order_by(SitemapEntry.content_type)
        )
        content_types = result.scalars().all()
        
        # Create XML
        sitemapindex = Element("sitemapindex")
        sitemapindex.set("xmlns", "http://www.sitemaps.org/schemas/sitemap/0.9")
        
        # Add language-specific sitemaps
        for language in languages:
            sitemap = SubElement(sitemapindex, "sitemap")
            loc = SubElement(sitemap, "loc")
            loc.text = f"{base_url.rstrip('/')}/sitemap-{language}.xml"
            
            lastmod = SubElement(sitemap, "lastmod")
            lastmod.text = datetime.utcnow().strftime("%Y-%m-%d")
        
        # Add content-type specific sitemaps
        for content_type in content_types:
            sitemap = SubElement(sitemapindex, "sitemap")
            loc = SubElement(sitemap, "loc")
            loc.text = f"{base_url.rstrip('/')}/sitemap-{content_type}.xml"
            
            lastmod = SubElement(sitemap, "lastmod")
            lastmod.text = datetime.utcnow().strftime("%Y-%m-%d")
        
        return tostring(sitemapindex, encoding="unicode", method="xml")

    async def sync_sitemap_from_slugs(
        self,
        db: AsyncSession,
        content_type: str,
        content_id: uuid.UUID,
        base_url: str = "https://example.com"
    ):
        """Sync sitemap entries from URL slugs."""
        # Get all slugs for this content
        result = await db.execute(
            select(URLSlug).filter(
                and_(
                    URLSlug.content_type == content_type,
                    URLSlug.content_id == content_id,
                    URLSlug.is_active == True,
                    URLSlug.is_primary == True
                )
            )
        )
        slugs = result.scalars().all()
        
        for slug in slugs:
            # Check if sitemap entry exists
            existing_entry = await self.get_sitemap_entry(
                db, content_type, content_id, slug.language_code
            )
            
            url = f"{slug.full_url}" if slug.full_path else f"/{slug.language_code}/{content_type}/{slug.slug}"
            
            if existing_entry:
                # Update existing entry
                await self.update_sitemap_entry(
                    db,
                    existing_entry.id,
                    SitemapEntryUpdate(
                        url=url,
                        priority=slug.get_sitemap_priority(),
                        change_frequency=slug.change_frequency
                    )
                )
            else:
                # Create new entry
                await self.create_sitemap_entry(
                    db,
                    SitemapEntryCreate(
                        content_type=content_type,
                        content_id=content_id,
                        language_code=slug.language_code,
                        url=url,
                        priority=slug.get_sitemap_priority(),
                        change_frequency=slug.change_frequency
                    )
                )

    async def get_sitemap_stats(self, db: AsyncSession) -> Dict[str, Any]:
        """Get sitemap statistics."""
        # Total entries
        total_result = await db.execute(
            select(func.count(SitemapEntry.id))
        )
        total_entries = total_result.scalar()
        
        # Active entries
        active_result = await db.execute(
            select(func.count(SitemapEntry.id))
            .filter(SitemapEntry.is_active == True)
        )
        active_entries = active_result.scalar()
        
        # Indexed entries
        indexed_result = await db.execute(
            select(func.count(SitemapEntry.id))
            .filter(
                and_(
                    SitemapEntry.is_active == True,
                    SitemapEntry.is_indexed == True
                )
            )
        )
        indexed_entries = indexed_result.scalar()
        
        # Languages
        languages_result = await db.execute(
            select(distinct(SitemapEntry.language_code))
            .filter(SitemapEntry.is_active == True)
        )
        languages = languages_result.scalars().all()
        
        # Content types
        content_types_result = await db.execute(
            select(distinct(SitemapEntry.content_type))
            .filter(SitemapEntry.is_active == True)
        )
        content_types = content_types_result.scalars().all()
        
        return {
            "total_entries": total_entries,
            "active_entries": active_entries,
            "indexed_entries": indexed_entries,
            "languages": list(languages),
            "content_types": list(content_types),
            "last_generated": datetime.utcnow()
        }

    async def _get_hreflang_for_entry(
        self,
        db: AsyncSession,
        entry: SitemapEntry,
        base_url: str
    ) -> List[Dict[str, str]]:
        """Get hreflang alternates for a sitemap entry."""
        # Get all entries for the same content in different languages
        result = await db.execute(
            select(SitemapEntry).filter(
                and_(
                    SitemapEntry.content_type == entry.content_type,
                    SitemapEntry.content_id == entry.content_id,
                    SitemapEntry.is_active == True,
                    SitemapEntry.is_indexed == True
                )
            )
        )
        entries = result.scalars().all()
        
        hreflang_data = []
        for alt_entry in entries:
            hreflang_data.append({
                "hreflang": alt_entry.language_code,
                "href": f"{base_url.rstrip('/')}{alt_entry.url}"
            })
        
        return hreflang_data
