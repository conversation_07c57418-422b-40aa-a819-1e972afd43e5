import uuid
from typing import Optional, List
from decimal import Decimal
from pydantic import BaseModel, ConfigDict, Field, EmailStr

from app.modules.core.functions.pos.schemas.refund import RefundRead

# --- SaleItem Schemas ---


class SaleItemCreate(BaseModel):
    """Schema for creating a sale item."""

    product_id: uuid.UUID
    quantity: int = Field(..., gt=0)
    unit_price: Decimal = Field(..., max_digits=10, decimal_places=2)
    discount: Optional[Decimal] = Field(0, max_digits=10, decimal_places=2)
    notes: Optional[str] = None


# --- SaleTransaction Schemas ---

# Shared properties


class SaleTransactionBase(BaseModel):
    total_amount: Decimal = Field(..., max_digits=10, decimal_places=2)
    payment_method: Optional[str] = None
    status: Optional[str] = "completed"
    customer_id: Optional[uuid.UUID] = None
    # items: Optional[Any] = None # Para detalhamento futuro


# Properties to receive via API on creation
# tenant_id e cash_register_id virão do contexto/path


class SaleTransactionCreate(BaseModel):
    total_amount: Decimal = Field(..., max_digits=10, decimal_places=2)
    payment_method: Optional[str] = None
    status: Optional[str] = "completed"
    customer_email: Optional[EmailStr] = None
    customer_phone: Optional[str] = None
    # items: Optional[Any] = None


# Properties to receive via API on update (Exemplo, pode não ser necessário agora)


class SaleTransactionUpdate(BaseModel):
    payment_method: Optional[str] = None
    status: Optional[str] = None
    customer_id: Optional[uuid.UUID] = None
    # items: Optional[Any] = None


# Properties stored in DB


class SaleTransactionInDBBase(SaleTransactionBase):
    id: uuid.UUID
    tenant_id: uuid.UUID
    cash_register_id: uuid.UUID
    model_config = ConfigDict(from_attributes=True)


# Properties to return to client


class SaleTransactionRead(SaleTransactionInDBBase):
    # Campos adicionais para estornos
    refunds: Optional[List[RefundRead]] = None
    refunded_amount: Optional[Decimal] = None  # Soma total dos estornos
    remaining_amount: Optional[Decimal] = None  # Valor restante disponível para estorno
