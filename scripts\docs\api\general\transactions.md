# General - Transactions

**Categoria:** General
**Módulo:** Transactions
**Total de Endpoints:** 7
**Gerado em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [GET /api/financial/transactions/](#get-apifinancialtransactions) - Get All Transactions
- [POST /api/financial/transactions/](#post-apifinancialtransactions) - Create Transaction
- [GET /api/financial/transactions/supplier-invoices](#get-apifinancialtransactionssupplier-invoices) - Listar Faturas de Fornecedores
- [DELETE /api/financial/transactions/{transaction_id}](#delete-apifinancialtransactionstransaction-id) - Delete Transaction
- [GET /api/financial/transactions/{transaction_id}](#get-apifinancialtransactionstransaction-id) - Get Transaction
- [PUT /api/financial/transactions/{transaction_id}](#put-apifinancialtransactionstransaction-id) - Update Transaction
- [POST /api/financial/transactions/{transaction_id}/payment](#post-apifinancialtransactionstransaction-idpayment) - Registrar Pagamento de Fatura

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### FinancialTransactionCreate

**Descrição:** Schema for creating a financial transaction.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `transaction_type` | TransactionType | ✅ | Type of transaction (income or expense) |
| `amount` | unknown | ✅ | Transaction amount |
| `description` | string | ✅ | Transaction description |
| `transaction_date` | string | ✅ | Date when the transaction occurred |
| `payment_method_id` | unknown | ❌ | ID of the payment method used |
| `category_id` | unknown | ❌ | ID of the financial category |
| `reference_number` | unknown | ❌ | Reference number (invoice, receipt, etc.) |
| `notes` | unknown | ❌ | Additional notes |
| `media_upload_ids` | unknown | ❌ | - |

### FinancialTransactionInDB

**Descrição:** Schema for representing a transaction in a response.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `transaction_type` | TransactionType | ✅ | Type of transaction (income or expense) |
| `amount` | string | ✅ | Transaction amount |
| `description` | string | ✅ | Transaction description |
| `transaction_date` | string | ✅ | Date when the transaction occurred |
| `payment_method_id` | unknown | ❌ | ID of the payment method used |
| `category_id` | unknown | ❌ | ID of the financial category |
| `reference_number` | unknown | ❌ | Reference number (invoice, receipt, etc.) |
| `notes` | unknown | ❌ | Additional notes |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_by` | string | ✅ | - |
| `media_uploads` | Array[MediaUploadInfo] | ❌ | - |

### FinancialTransactionUpdate

**Descrição:** Schema for updating a financial transaction.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `transaction_type` | unknown | ❌ | Type of transaction (income or expense) |
| `amount` | unknown | ❌ | Transaction amount |
| `description` | unknown | ❌ | Transaction description |
| `transaction_date` | unknown | ❌ | Date when the transaction occurred |
| `payment_method_id` | unknown | ❌ | ID of the payment method used |
| `category_id` | unknown | ❌ | ID of the financial category |
| `reference_number` | unknown | ❌ | Reference number (invoice, receipt, etc.) |
| `notes` | unknown | ❌ | Additional notes |
| `media_upload_ids` | unknown | ❌ | - |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### InvoicePaymentCreate

**Descrição:** Schema for registering payment of a supplier invoice.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `payment_method_id` | string | ✅ | Payment method used |
| `payment_date` | string | ✅ | Date of payment |
| `payment_amount` | unknown | ✅ | Amount paid |
| `payment_reference` | unknown | ❌ | Payment reference |
| `payment_receipt_file` | unknown | ❌ | Receipt file path |
| `notes` | unknown | ❌ | Additional payment notes |

### InvoicePaymentRead

**Descrição:** Schema for reading invoice payment information.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `amount` | string | ✅ | - |
| `description` | string | ✅ | - |
| `transaction_date` | string | ✅ | - |
| `reference_number` | string | ✅ | - |
| `payment_method_id` | unknown | ❌ | - |
| `notes` | unknown | ❌ | - |
| `is_paid` | boolean | ❌ | Whether invoice is paid |

## 🔗 Endpoints Detalhados

### GET /api/financial/transactions/ {#get-apifinancialtransactions}

**Resumo:** Get All Transactions
**Descrição:** Retrieve all transactions for the tenant.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/transactions/"
```

---

### POST /api/financial/transactions/ {#post-apifinancialtransactions}

**Resumo:** Create Transaction
**Descrição:** Create a new financial transaction.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [FinancialTransactionCreate](#financialtransactioncreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [FinancialTransactionInDB](#financialtransactionindb)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/financial/transactions/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/financial/transactions/supplier-invoices {#get-apifinancialtransactionssupplier-invoices}

**Resumo:** Listar Faturas de Fornecedores
**Descrição:** Lista faturas de fornecedores pendentes de pagamento.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/transactions/supplier-invoices" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### DELETE /api/financial/transactions/{transaction_id} {#delete-apifinancialtransactionstransaction-id}

**Resumo:** Delete Transaction
**Descrição:** Delete a financial transaction.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `transaction_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/financial/transactions/{transaction_id}"
```

---

### GET /api/financial/transactions/{transaction_id} {#get-apifinancialtransactionstransaction-id}

**Resumo:** Get Transaction
**Descrição:** Retrieve a specific transaction by its ID.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `transaction_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [FinancialTransactionInDB](#financialtransactionindb)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/transactions/{transaction_id}"
```

---

### PUT /api/financial/transactions/{transaction_id} {#put-apifinancialtransactionstransaction-id}

**Resumo:** Update Transaction
**Descrição:** Update a financial transaction.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `transaction_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [FinancialTransactionUpdate](#financialtransactionupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [FinancialTransactionInDB](#financialtransactionindb)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/financial/transactions/{transaction_id}" \
  -H "Content-Type: application/json" \
  -d '{"example": "data"}'
```

---

### POST /api/financial/transactions/{transaction_id}/payment {#post-apifinancialtransactionstransaction-idpayment}

**Resumo:** Registrar Pagamento de Fatura
**Descrição:** Registra pagamento de uma fatura de fornecedor.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `transaction_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [InvoicePaymentCreate](#invoicepaymentcreate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [InvoicePaymentRead](#invoicepaymentread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/financial/transactions/{transaction_id}/payment" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
