from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import delete

from app.modules.core.i18n.models import TranslationKey
from app.modules.core.i18n import schemas


class TranslationKeyService:
    async def create_key(
        self, db: AsyncSession, key_in: schemas.TranslationKeyCreate
    ) -> TranslationKey:
        """Create a new translation key."""
        db_key = TranslationKey(
            key_string=key_in.key_string, module=key_in.module, description=key_in.description
        )
        db.add(db_key)
        await db.flush()
        await db.refresh(db_key)
        return db_key

    async def get_key(self, db: AsyncSession, key_id: int) -> Optional[TranslationKey]:
        """Get a translation key by ID."""
        result = await db.execute(select(TranslationKey).filter(TranslationKey.id == key_id))
        return result.scalars().first()

    async def get_key_by_string(
        self, db: AsyncSession, key_string: str
    ) -> Optional[TranslationKey]:
        """Get a translation key by its string value."""
        result = await db.execute(
            select(TranslationKey).filter(TranslationKey.key_string == key_string)
        )
        return result.scalars().first()

    async def get_keys(
        self, db: AsyncSession, module: Optional[str] = None, skip: int = 0, limit: int = 100
    ) -> List[TranslationKey]:
        """Get all translation keys with optional module filter and pagination."""
        query = select(TranslationKey)
        if module:
            query = query.filter(TranslationKey.module == module)
        query = query.offset(skip).limit(limit)
        result = await db.execute(query)
        return result.scalars().all()

    async def update_key(
        self, db: AsyncSession, key_id: int, key_in: schemas.TranslationKeyUpdate
    ) -> Optional[TranslationKey]:
        """Update a translation key."""
        db_key = await self.get_key(db, key_id)
        if not db_key:
            return None

        # Update fields
        for field, value in key_in.model_dump(exclude_unset=True).items():
            setattr(db_key, field, value)

        await db.flush()
        await db.refresh(db_key)
        return db_key

    async def delete_key(self, db: AsyncSession, key_id: int) -> bool:
        """Delete a translation key."""
        db_key = await self.get_key(db, key_id)
        if not db_key:
            return False

        await db.delete(db_key)
        await db.flush()
        return True
