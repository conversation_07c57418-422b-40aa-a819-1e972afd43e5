"""
B2B Invoice API Endpoints
========================

API endpoints específicos para gestão de faturas B2B entre TVendorSupplier e TCostumer.
"""

import uuid
from typing import Optional, List
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Query
from fastapi.responses import FileResponse
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_db
from app.core.auth import get_current_user
from app.modules.core.users.models.user import User
from ..services.invoice_service import InvoiceService
from ..schemas.invoice import (
    B2BInvoiceCreate, InvoiceUpdate, InvoiceRead,
    B2BInvoiceResponse, B2BInvoiceListResponse, B2BInvoiceStats,
    B2BInvoiceDownloadRequest, B2BInvoiceFileUpload
)
from ..models.invoice import InvoiceStatus, InvoiceType

router = APIRouter(prefix="/b2b-invoices", tags=["b2b-invoices"])


@router.post("/", response_model=B2BInvoiceResponse)
async def create_b2b_invoice(
    invoice_data: B2BInvoiceCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Cria nova fatura B2B.
    
    Apenas TVendorSupplier pode criar faturas B2B.
    """
    service = InvoiceService(db)
    
    # TODO: Verificar se usuário é TVendorSupplier
    # Por enquanto, usar user_id como vendor_id
    
    try:
        invoice = await service.create_b2b_invoice(
            invoice_data=invoice_data,
            tenant_id=current_user.tenant_id,
            created_by=current_user.id
        )
        
        return B2BInvoiceResponse(
            success=True,
            message="B2B invoice created successfully",
            invoice=InvoiceRead.from_orm(invoice)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/{invoice_id}/upload", response_model=B2BInvoiceResponse)
async def upload_b2b_invoice_file(
    invoice_id: str,
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Faz upload do arquivo da fatura B2B.
    
    Apenas o TVendorSupplier proprietário pode fazer upload.
    """
    service = InvoiceService(db)
    
    # TODO: Verificar se usuário é TVendorSupplier
    vendor_id = current_user.id
    
    try:
        invoice = await service.upload_b2b_invoice_file(
            invoice_id=uuid.UUID(invoice_id),
            file=file,
            vendor_id=vendor_id,
            tenant_id=current_user.tenant_id
        )
        
        return B2BInvoiceResponse(
            success=True,
            message="File uploaded successfully",
            invoice=InvoiceRead.from_orm(invoice)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/", response_model=B2BInvoiceListResponse)
async def list_b2b_invoices(
    vendor_id: Optional[str] = Query(None, description="Filter by vendor ID"),
    customer_id: Optional[str] = Query(None, description="Filter by customer ID"),
    status: Optional[InvoiceStatus] = Query(None, description="Filter by status"),
    overdue_only: bool = Query(False, description="Show only overdue invoices"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Lista faturas B2B com filtros.
    
    Usuários veem apenas suas faturas (como vendor ou customer).
    """
    service = InvoiceService(db)
    
    # TODO: Aplicar filtros baseados no role do usuário
    # Por enquanto, usar user_id como vendor_id ou customer_id
    
    try:
        # Get invoices using existing method with B2B filter
        invoices = await service.get_invoices(
            tenant_id=current_user.tenant_id,
            skip=(page - 1) * limit,
            limit=limit,
            status=status
        )
        
        # Filter B2B invoices only
        b2b_invoices = [
            inv for inv in invoices 
            if inv.invoice_type in [InvoiceType.B2B_VENDOR, InvoiceType.B2B_CUSTOMER]
        ]
        
        # Apply additional filters
        if vendor_id:
            b2b_invoices = [inv for inv in b2b_invoices if str(inv.vendor_id) == vendor_id]
        if customer_id:
            b2b_invoices = [inv for inv in b2b_invoices if str(inv.customer_b2b_id) == customer_id]
        if overdue_only:
            b2b_invoices = [inv for inv in b2b_invoices if inv.is_overdue]
        
        # Convert to summary format
        invoice_summaries = []
        for invoice in b2b_invoices:
            summary = {
                "id": str(invoice.id),
                "invoice_number": invoice.invoice_number,
                "invoice_type": invoice.invoice_type,
                "status": invoice.status,
                "issue_date": invoice.issue_date.isoformat(),
                "due_date": invoice.due_date.isoformat() if invoice.due_date else None,
                "total_amount": float(invoice.total_amount),
                "outstanding_amount": float(invoice.outstanding_amount),
                "is_overdue": invoice.is_overdue,
                "days_until_due": invoice.days_until_due,
                "vendor_id": str(invoice.vendor_id) if invoice.vendor_id else None,
                "customer_b2b_id": str(invoice.customer_b2b_id) if invoice.customer_b2b_id else None,
                "has_file": bool(invoice.file_path),
                "viewed_date": invoice.viewed_date.isoformat() if invoice.viewed_date else None,
                "sent_date": invoice.sent_date.isoformat() if invoice.sent_date else None
            }
            invoice_summaries.append(summary)
        
        # Get statistics
        stats = await service.get_b2b_invoice_stats(
            tenant_id=current_user.tenant_id,
            vendor_id=uuid.UUID(vendor_id) if vendor_id else None,
            customer_id=uuid.UUID(customer_id) if customer_id else None
        )
        
        total = len(invoice_summaries)
        total_pages = (total + limit - 1) // limit
        
        return B2BInvoiceListResponse(
            invoices=invoice_summaries,
            total=total,
            page=page,
            limit=limit,
            total_pages=total_pages,
            stats=stats
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/{invoice_id}", response_model=B2BInvoiceResponse)
async def get_b2b_invoice(
    invoice_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Busca fatura B2B por ID.
    
    Apenas vendor ou customer podem acessar.
    """
    service = InvoiceService(db)
    
    try:
        invoice = await service.get_invoice_by_id(
            invoice_id=uuid.UUID(invoice_id),
            tenant_id=current_user.tenant_id
        )
        
        if not invoice:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invoice not found"
            )
        
        # Check if it's a B2B invoice
        if invoice.invoice_type not in [InvoiceType.B2B_VENDOR, InvoiceType.B2B_CUSTOMER]:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Not a B2B invoice"
            )
        
        # Check access permissions
        # TODO: Implement proper role checking
        user_has_access = (
            invoice.vendor_id == current_user.id or 
            invoice.customer_b2b_id == current_user.id
        )
        
        if not user_has_access:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Mark as viewed if customer is viewing
        if invoice.customer_b2b_id == current_user.id and invoice.status == InvoiceStatus.SENT.value:
            await service.mark_b2b_invoice_as_viewed(
                invoice_id=uuid.UUID(invoice_id),
                customer_id=current_user.id,
                tenant_id=current_user.tenant_id
            )
            # Refresh invoice
            invoice = await service.get_invoice_by_id(
                invoice_id=uuid.UUID(invoice_id),
                tenant_id=current_user.tenant_id
            )
        
        return B2BInvoiceResponse(
            success=True,
            message="Invoice retrieved successfully",
            invoice=InvoiceRead.from_orm(invoice)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/{invoice_id}/send", response_model=B2BInvoiceResponse)
async def send_b2b_invoice(
    invoice_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Envia fatura B2B para cliente.
    
    Apenas TVendorSupplier proprietário pode enviar.
    """
    service = InvoiceService(db)
    
    # TODO: Verificar se usuário é TVendorSupplier
    vendor_id = current_user.id
    
    try:
        invoice = await service.send_b2b_invoice(
            invoice_id=uuid.UUID(invoice_id),
            vendor_id=vendor_id,
            tenant_id=current_user.tenant_id
        )
        
        return B2BInvoiceResponse(
            success=True,
            message="Invoice sent successfully",
            invoice=InvoiceRead.from_orm(invoice)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/{invoice_id}/download-token", response_model=B2BInvoiceResponse)
async def generate_b2b_download_token(
    invoice_id: str,
    request: B2BInvoiceDownloadRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Gera token para download da fatura B2B.
    
    Vendor ou customer podem gerar token.
    """
    service = InvoiceService(db)
    
    try:
        token = await service.generate_b2b_download_token(
            invoice_id=uuid.UUID(invoice_id),
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            expires_hours=request.expires_hours
        )
        
        return B2BInvoiceResponse(
            success=True,
            message="Download token generated successfully",
            access_token=token
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/stats/summary", response_model=B2BInvoiceStats)
async def get_b2b_invoice_stats(
    vendor_id: Optional[str] = Query(None, description="Filter by vendor ID"),
    customer_id: Optional[str] = Query(None, description="Filter by customer ID"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Estatísticas de faturas B2B.
    
    Filtros aplicados baseados no role do usuário.
    """
    service = InvoiceService(db)
    
    try:
        stats = await service.get_b2b_invoice_stats(
            tenant_id=current_user.tenant_id,
            vendor_id=uuid.UUID(vendor_id) if vendor_id else None,
            customer_id=uuid.UUID(customer_id) if customer_id else None
        )
        
        return stats
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
