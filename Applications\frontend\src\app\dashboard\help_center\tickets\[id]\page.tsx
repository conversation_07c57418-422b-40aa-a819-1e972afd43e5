'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/lib/auth/AuthProvider';
import { apiClient } from '@/lib/api/client';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { TicketDetail } from '../../components/TicketDetail';
import { 
  ArrowLeftIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface Ticket {
  id: string;
  title: string;
  description: string;
  status: 'new' | 'open' | 'pending' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'question' | 'incident' | 'problem' | 'request' | 'suggestion';
  created_at: string;
  updated_at?: string;
  resolved_at?: string;
  closed_at?: string;
  is_read_by_admin: boolean;
  is_read_by_user: boolean;
  user_name?: string;
  user_email?: string;
  assigned_admin_name?: string;
  message_count?: number;
}

export default function TicketDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const [ticket, setTicket] = useState<Ticket | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const ticketId = params.id as string;

  const fetchTicket = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await apiClient.get<Ticket>(
        `/modules/core/help-center/tickets/${ticketId}`
      );

      setTicket(response.data);
    } catch (err: any) {
      console.error('Erro ao carregar ticket:', err);
      if (err.response?.status === 404) {
        setError('Ticket não encontrado.');
      } else if (err.response?.status === 403) {
        setError('Você não tem permissão para acessar este ticket.');
      } else {
        setError('Erro ao carregar ticket. Tente novamente.');
      }
    } finally {
      setIsLoading(false);
    }
  }, [ticketId]);

  useEffect(() => {
    if (ticketId) {
      fetchTicket();
    }
  }, [ticketId, fetchTicket]);

  const handleTicketUpdate = (updatedTicket: Ticket) => {
    setTicket(updatedTicket);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        {/* Header with back button */}
        <div className="flex items-center space-x-4">
          <Link
            href="/dashboard/help_center"
            className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-1" />
            Voltar para Help Center
          </Link>
        </div>

        {/* Error Message */}
        <div className="glass rounded-xl p-6">
          <div className="text-center py-12">
            <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              Erro ao carregar ticket
            </h3>
            <p className="mt-1 text-sm text-gray-500">{error}</p>
            <div className="mt-6 space-x-3">
              <button
                onClick={fetchTicket}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
              >
                Tentar Novamente
              </button>
              <Link
                href="/dashboard/help_center"
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                Voltar
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!ticket) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Header with back button */}
      <div className="flex items-center justify-between">
        <Link
          href="/dashboard/help_center"
          className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 transition-colors"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-1" />
          Voltar para Help Center
        </Link>
        
        <div className="text-sm text-gray-500">
          Ticket #{ticket?.id?.slice(0, 8) || 'N/A'}
        </div>
      </div>

      {/* Ticket Detail Component */}
      <TicketDetail 
        ticket={ticket} 
        onTicketUpdate={handleTicketUpdate}
      />
    </div>
  );
}
