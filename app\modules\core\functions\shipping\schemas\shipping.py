import uuid
from pydantic import BaseModel, Field, ConfigDict
from typing import Optional
from app.modules.core.functions.shipping.models.shipping import ShipmentStatus

# ShippingMethod Schemas
class ShippingMethodBase(BaseModel):
    name: str = Field(..., max_length=100)
    description: Optional[str] = Field(None, max_length=255)

class ShippingMethodCreate(ShippingMethodBase):
    pass

class ShippingMethodUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = Field(None, max_length=255)

class ShippingMethodRead(ShippingMethodBase):
    id: uuid.UUID
    tenant_id: uuid.UUID
    model_config = ConfigDict(from_attributes=True)

# ShippingCarrier Schemas
class ShippingCarrierBase(BaseModel):
    name: str = Field(..., max_length=100)
    tracking_url_template: Optional[str] = Field(None, max_length=255)

class ShippingCarrierCreate(ShippingCarrierBase):
    pass

class ShippingCarrierUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=100)
    tracking_url_template: Optional[str] = Field(None, max_length=255)

class ShippingCarrierRead(ShippingCarrierBase):
    id: uuid.UUID
    tenant_id: uuid.UUID
    model_config = ConfigDict(from_attributes=True)

# Shipment Schemas
class ShipmentBase(BaseModel):
    order_id: uuid.UUID
    shipping_method_id: uuid.UUID
    carrier_id: uuid.UUID
    tracking_code: Optional[str] = Field(None, max_length=255)
    status: ShipmentStatus = ShipmentStatus.PENDING

class ShipmentCreate(ShipmentBase):
    pass

class ShipmentUpdate(BaseModel):
    carrier_id: Optional[uuid.UUID] = None
    tracking_code: Optional[str] = Field(None, max_length=255)
    status: Optional[ShipmentStatus] = None

class ShipmentRead(ShipmentBase):
    id: uuid.UUID
    tenant_id: uuid.UUID
    model_config = ConfigDict(from_attributes=True)