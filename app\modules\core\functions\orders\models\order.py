import uuid
import enum
from typing import TYPE_CHECKING, List, Optional
from sqlalchemy import (
    Column,
    String,
    Foreign<PERSON>ey,
    JSON,
    DateTime,
    Enum,
    Numeric,
    Integer,
    Boolean,
    Table,
)
from sqlalchemy.orm import relationship  # noqa: E402
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from app.db.base import Base

# Use TYPE_CHECKING to handle circular dependencies for type hinting
if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.core.users.models.user import User
    from app.modules.shared.crm.models.account import Account
    from app.modules.tenants.restaurants.table_management.models.table import Table
    from app.modules.tenants.restaurants.kds.models.kitchen_order import KitchenOrder
    from app.modules.core.payments.models.payment import Payment
    from app.modules.shared.financial.invoices.models.invoice import Invoice
    from app.modules.core.functions.shipping.models.shipping import Shipment


class OrderStatus(str, enum.Enum):
    """Enum for order status."""

    PENDING = "pending"
    PREPARING = "preparing"
    READY = "ready"
    DELIVERED = "delivered"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


# Association table for order items and variant options
order_item_variant_options = Table(
    "order_item_variant_options",
    Base.metadata,
    Column(
        "order_item_id",
        UUID(as_uuid=True),
        ForeignKey("order_items.id"),
        primary_key=True,
    ),
    Column(
        "variant_option_id",
        UUID(as_uuid=True),
        ForeignKey("variant_options.id"),
        primary_key=True,
    ),
)

# Association table for order items and modifier options
order_item_modifier_options = Table(
    "order_item_modifier_options",
    Base.metadata,
    Column(
        "order_item_id",
        UUID(as_uuid=True),
        ForeignKey("order_items.id"),
        primary_key=True,
    ),
    Column(
        "modifier_option_id",
        UUID(as_uuid=True),
        ForeignKey("modifier_options.id"),
        primary_key=True,
    ),
)


class Order(Base):
    """Order model for all types of orders across tenants."""

    __tablename__ = "orders"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    customer_id = Column(UUID(as_uuid=True), ForeignKey("crm_accounts.id"), nullable=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    table_id = Column(
        UUID(as_uuid=True), ForeignKey("restaurant_tables.id"), nullable=True, index=True
    )

    # Order details
    order_number = Column(String, nullable=False)
    status = Column(Enum(OrderStatus), default=OrderStatus.PENDING, nullable=False, index=True)
    order_type = Column(String, nullable=False)  # dine-in, takeout, delivery, etc.
    table_number = Column(String, nullable=True)  # For dine-in orders (legacy field)

    # Financial details
    subtotal = Column(Numeric(10, 2), nullable=False)
    tax = Column(Numeric(10, 2), nullable=False)
    discount = Column(Numeric(10, 2), default=0.0, nullable=False)
    total = Column(Numeric(10, 2), nullable=False)

    # Additional information
    notes = Column(String, nullable=True)
    order_metadata = Column(JSON, nullable=True)  # For any additional data

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )

    # Relationships
    tenant = relationship(
        "app.modules.core.tenants.models.tenant.Tenant",
        viewonly=True,  # Use viewonly to avoid circular references
    )
    customer = relationship(
        "app.modules.shared.crm.models.account.Account",
        viewonly=True,  # Use viewonly to avoid circular references
    )
    user = relationship(
        "app.modules.core.users.models.user.User",
        viewonly=True,  # Use viewonly to avoid circular references
    )
    items = relationship("OrderItem", back_populates="order", cascade="all, delete-orphan")
    table = relationship(
        "app.modules.tenants.restaurants.table_management.models.table.Table",
        viewonly=True,  # Use viewonly to avoid circular references
    )

    # KDS relationship
    kitchen_order = relationship(
        "app.modules.tenants.restaurants.kds.models.kitchen_order.KitchenOrder",
        uselist=False,
        foreign_keys="[app.modules.tenants.restaurants.kds.models.kitchen_order.KitchenOrder.order_id]",
        back_populates="order",
    )

    # Payment relationship
    payments = relationship(
        "app.modules.core.payments.models.payment.Payment",
        back_populates="order",
        cascade="all, delete-orphan",
    )

    # Invoice relationship
    invoices = relationship(
        "app.modules.shared.financial.invoices.models.invoice.Invoice",
        back_populates="order",
        cascade="all, delete-orphan",
    )

    # Shipping relationship
    shipments = relationship(
        "Shipment",
        back_populates="order",
        cascade="all, delete-orphan",
    )

    def __repr__(self):
        return f"<Order(id={self.id}, tenant_id={self.tenant_id}, status={self.status})>"


class OrderItem(Base):
    """Order item model for items in an order."""

    __tablename__ = "order_items"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    order_id = Column(UUID(as_uuid=True), ForeignKey("orders.id"), nullable=False)
    menu_item_id = Column(UUID(as_uuid=True), ForeignKey("menu_items.id"), nullable=False)

    # Item details
    name = Column(String, nullable=False)
    quantity = Column(Integer, nullable=False, default=1)
    unit_price = Column(Numeric(10, 2), nullable=False)
    total_price = Column(Numeric(10, 2), nullable=False)

    # Additional information
    notes = Column(String, nullable=True)
    is_cancelled = Column(Boolean, default=False, nullable=False)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )

    # Relationships
    order = relationship("Order", back_populates="items")
    menu_item = relationship("MenuItem")

    # Many-to-many relationships for variants and modifiers
    variant_options = relationship(
        "VariantOption", secondary=order_item_variant_options, backref="order_items"
    )

    modifier_options = relationship(
        "ModifierOption", secondary=order_item_modifier_options, backref="order_items"
    )

    def __repr__(self):
        return f"<OrderItem(id={self.id}, name={self.name}, quantity={self.quantity})>"
