import uuid
from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, EmailStr

from app.modules.shared.pos.schemas.sale_transaction import SaleTransactionRead


class OnlineOrderBase(BaseModel):
    """Base schema for online orders."""

    order_number: str
    customer_name: str
    customer_email: Optional[EmailStr] = None
    customer_phone: Optional[str] = None
    status: str
    total: float
    notes: Optional[str] = None


class OnlineOrderCreate(OnlineOrderBase):
    """Schema for creating an online order."""

    pass


class OnlineOrderRead(OnlineOrderBase):
    """Schema for reading an online order."""

    model_config = ConfigDict(from_attributes=True)

    id: uuid.UUID
    tenant_id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    transaction_id: Optional[uuid.UUID] = None
    transaction: Optional[SaleTransactionRead] = None
