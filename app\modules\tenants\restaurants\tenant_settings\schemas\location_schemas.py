"""
Restaurant Location and Address Extensions specific schemas.
"""

from typing import Optional

from pydantic import BaseModel, Field


class RestaurantAddressExtensionsSchema(BaseModel):
    """Schema for restaurant-specific address extensions."""

    # WhatsApp Integration
    phone_is_whatsapp: bool = Field(
        False, 
        description="Whether primary phone is also WhatsApp"
    )
    phone_secondary_is_whatsapp: bool = Field(
        False, 
        description="Whether secondary phone is also WhatsApp"
    )

    # Delivery/Service Area (Restaurant-specific)
    delivery_radius_km: Optional[float] = Field(
        None, 
        ge=0, 
        description="Delivery radius in kilometers"
    )
    service_area_notes: Optional[str] = Field(
        None, 
        max_length=300, 
        description="Additional service area information"
    )

    # Restaurant-specific contact preferences
    accepts_delivery_orders: bool = Field(
        True, 
        description="Whether restaurant accepts delivery orders"
    )
    accepts_pickup_orders: bool = Field(
        True, 
        description="Whether restaurant accepts pickup orders"
    )
    accepts_dine_in: bool = Field(
        True, 
        description="Whether restaurant accepts dine-in customers"
    )

    # Additional restaurant location settings
    parking_available: Optional[bool] = Field(
        None, 
        description="Whether parking is available"
    )
    parking_notes: Optional[str] = Field(
        None, 
        max_length=200, 
        description="Additional parking information"
    )
    accessibility_features: Optional[str] = Field(
        None, 
        max_length=300, 
        description="Accessibility features description"
    )


class RestaurantLocationSettingsUpdate(BaseModel):
    """Schema for updating restaurant location settings."""
    
    address_extensions: Optional[RestaurantAddressExtensionsSchema] = Field(
        None,
        description="Restaurant-specific address extensions"
    )
