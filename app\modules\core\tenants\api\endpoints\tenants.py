import uuid
from typing import Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, status, Body
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import selectinload
from pydantic import BaseModel

from app.core.db_dependencies import get_db
from app.core.dependencies import (
    get_current_active_user,
    require_system_role,
    require_tenant_role,
)

from app.modules.core.roles.models.roles import (
    SystemRole,
    TenantRole,
    RolePermissions,
    TenantStaffSubRole,
)

from app.modules.core.users.models.tenant_user_association import (
    TenantUserAssociation as TenantUserAssociationModel,
)
from app.modules.core.users.models.user import User
from app.modules.core.tenants.schemas.tenant import Tenant as TenantRead, TenantCreate
from app.modules.core.users.schemas.tenant_user_association import TenantUserAssociationRead
from app.modules.core.tenants.services.tenant_service import tenant_service
from app.modules.core.users.services.tenant_user_association_service import (
    tenant_user_association_service,
)

router = APIRouter()


# === Tenant Management Endpoints (System Admin Level) ===


@router.post(
    "/",
    response_model=TenantRead,
    status_code=status.HTTP_201_CREATED,
    dependencies=[Depends(get_current_active_user)],
)
async def create_tenant(
    *,
    db: AsyncSession = Depends(get_db),
    tenant_in: TenantCreate,
) -> Any:
    """
    Creates a new tenant.
    Only superusers can create tenants.
    """
    try:
        tenant = await tenant_service.create_tenant(db=db, tenant_in=tenant_in)
        return tenant
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Could not create tenant: {e}",
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {e}",
        )


@router.get(
    "/",
    response_model=List[TenantRead],
    dependencies=[Depends(require_system_role([SystemRole.ADMIN]))],
)
async def read_tenants(
    db: AsyncSession = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    Retrieves a list of active tenants.
    Requires authentication. More granular permissions can be added.
    """
    tenants = await tenant_service.get_tenants(db=db, skip=skip, limit=limit)
    return tenants


@router.get(
    "/{tenant_id}",
    response_model=TenantRead,
    dependencies=[
        Depends(require_tenant_role(RolePermissions.VIEW_ROLES, tenant_id_source="path"))
    ],
)
async def read_tenant(
    *,
    db: AsyncSession = Depends(get_db),
    tenant_id: uuid.UUID,
) -> Any:
    """
    Gets details of a specific tenant by ID.
    Requires authentication.
    """
    tenant = await tenant_service.get_tenant(db=db, tenant_id=tenant_id)
    if not tenant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tenant not found or access denied",
        )
    return tenant


# === User Management Within a Tenant ===


# Schema for the request body to add a user to a tenant
class AddUserToTenantPayload(BaseModel):
    user_id: uuid.UUID
    role: TenantRole
    staff_sub_role: Optional[TenantStaffSubRole] = None
    data_sharing_consent: Optional[bool] = False


@router.post(
    "/{tenant_id}/users",
    response_model=TenantUserAssociationRead,
    status_code=status.HTTP_201_CREATED,
    dependencies=[
        Depends(require_tenant_role(RolePermissions.ADMIN_ROLES, tenant_id_source="path"))
    ],
)
async def add_user_to_tenant(
    *,
    db: AsyncSession = Depends(get_db),
    tenant_id: uuid.UUID,
    payload: AddUserToTenantPayload = Body(...),
) -> Any:
    """
    Adds an existing user to a specific tenant with a role.
    Requires that the authenticated user has the ADMIN or MANAGER role in the tenant specified in the path.
    """
    # Validate role and sub_role in payload
    if payload.role != TenantRole.STAFF and payload.staff_sub_role is not None:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="staff_sub_role can only be set if role is 'staff'.",
        )

    try:
        association = await tenant_user_association_service.add_user_to_tenant(
            db=db,
            user_id=payload.user_id,
            tenant_id=tenant_id,
            role=payload.role,
            staff_sub_role=payload.staff_sub_role,
            data_sharing_consent=payload.data_sharing_consent,
        )
        # To return TenantUserAssociationRead, we need to load User and Tenant
        result = await db.execute(
            select(TenantUserAssociationModel)
            .options(
                selectinload(TenantUserAssociationModel.user),
                selectinload(TenantUserAssociationModel.tenant),
            )
            .where(TenantUserAssociationModel.id == association.id)
        )
        detailed_association = result.scalars().first()
        if not detailed_association:
            raise HTTPException(status_code=500, detail="Failed to retrieve association details")
        return detailed_association

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {e}",
        )


@router.get(
    "/{tenant_id}/users",
    response_model=List[TenantUserAssociationRead],
    dependencies=[
        Depends(require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="path"))
    ],
)
async def read_tenant_users(
    *,
    db: AsyncSession = Depends(get_db),
    tenant_id: uuid.UUID,
) -> Any:
    """
    Lists users associated with a specific tenant.
    Requires that the authenticated user has at least the STAFF role in the tenant specified in the path.
    """
    try:
        stmt = (
            select(TenantUserAssociationModel)
            .options(
                selectinload(TenantUserAssociationModel.user),
                selectinload(TenantUserAssociationModel.tenant),
            )
            .where(TenantUserAssociationModel.tenant_id == tenant_id)
            .join(User)
            .where(User.is_active)
        )

        result = await db.execute(stmt)
        associations = result.scalars().all()
        return associations
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred while fetching tenant users: {e}",
        )


# TODO: Add endpoints for:
# - GET /tenants/{tenant_id}/users/{user_id} (Get details of a specific user in the tenant)
# - PUT /tenants/{tenant_id}/users/{user_id} (Update user's role in the tenant)
# - DELETE /tenants/{tenant_id}/users/{user_id} (Remove user from the tenant)
# - DELETE /tenants/{tenant_id} (Mark tenant as inactive - Superuser)
# - PUT /tenants/{tenant_id} (Update tenant data - Superuser/Tenant Admin?)
