import axios from 'axios';
import axios from 'axios';
import { apiGet, apiPost } from './client';
import {
  LoginCredentials,
  RegisterCredentials,
  TokenResponse,
  User,
  Tenant
} from '@/types/auth';

// Determine API URL based on environment
const getApiUrl = (): string => {
  if (typeof window === 'undefined') {
    // Server-side: use internal Docker URL
    return process.env.INTERNAL_API_URL || 'http://backend:8000/api';
  }
  // Client-side: use public URL (already includes /api)
  return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';
};

export const authApi = {
  // Login with OAuth2 form data
  login: async (formData: URLSearchParams): Promise<TokenResponse> => {
    try {
      const response = await axios.post(
        `${getApiUrl()}/auth/login`,
        formData,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );
      return response.data;
    } catch (error: any) {
      console.error('Login error:', error.response?.data || error.message);
      throw error;
    }
  },

  // Register new user
  register: async (credentials: RegisterCredentials): Promise<TokenResponse> => {
    return apiPost('/auth/register', {
      email: credentials.email,
      password: credentials.password,
      full_name: credentials.full_name,
    });
  },

  // Refresh access token
  refresh: async (refreshToken: string): Promise<TokenResponse> => {
    try {
      const formData = new URLSearchParams();
      formData.append('refresh_token', refreshToken);

      const response = await axios.post(
        `${getApiUrl()}/auth/refresh-token`,
        formData,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );
      return response.data;
    } catch (error: any) {
      console.error('Refresh token error:', error.response?.data || error.message);
      throw error;
    }
  },

  // Get current user data
  getCurrentUser: async (): Promise<User> => {
    return apiGet('/auth/me');
  },

  // Get user's tenants
  getUserTenants: async (): Promise<Tenant[]> => {
    return apiGet('/users/me/tenants');
  },

  // Get all tenants (admin only)
  getAllTenants: async (): Promise<Tenant[]> => {
    return apiGet('/tenants');
  },

  // Admin view: Get tenant for admin access
  adminViewTenant: async (tenantId: string): Promise<Tenant> => {
    return apiGet(`/tenants/admin/tenant/${tenantId}/view`);
  },

  // Admin view: Get tenant dashboard data
  adminGetTenantDashboardData: async (tenantId: string): Promise<any> => {
    return apiGet(`/tenants/admin/tenant/${tenantId}/dashboard-data`);
  },

  // Get user's tenant associations (for role checking)
  getUserTenantAssociations: async (): Promise<any[]> => {
    return apiGet('/users/me/tenant-associations');
  },

  // Forgot password
  forgotPassword: async (email: string): Promise<{ message: string }> => {
    return apiPost('/auth/forgot-password', { email });
  },

  // Reset password
  resetPassword: async (token: string, password: string): Promise<{ message: string }> => {
    return apiPost('/auth/reset-password', { token, password });
  },

  // Change password (authenticated)
  changePassword: async (
    currentPassword: string,
    newPassword: string
  ): Promise<{ message: string }> => {
    return apiPost('/auth/change-password', {
      current_password: currentPassword,
      new_password: newPassword,
    });
  },

  // Logout (if backend supports it)
  logout: async (): Promise<void> => {
    try {
      await apiPost('/auth/logout');
    } catch (error) {
      // Logout endpoint might not exist, that's okay
      console.warn('Logout endpoint not available:', error);
    }
  },
};
