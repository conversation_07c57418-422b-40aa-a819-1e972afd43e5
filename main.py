# Importar app.db.base PRIMEIRO para garantir que todos os modelos
# sejam conhecidos pelo Base.metadata compartilhado antes de qualquer outra
# importação de app que possa acionar a configuração do mapper do SQLAlchemy.
import app.db.base  # noqa: F401, F811 - Importações de modelo no final de app.db.base.py

# Importar modelos do supplier para garantir registro
import app.modules.shared.supplier.models  # noqa: F401
import logging  # Adicionado para configurar o logging global

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api.api import api_router
from app.core.config import settings
from app.core.socketio_manager import (
    sio_app,
)  # Importa a aplicação ASGI do Socket.IO centralizado
import app.core.socketio_handlers  # Importa e registra os handlers do Socket.IO
from app.middleware.tenant_resolver import TenantResolverMiddleware

# As importações de User, Tenant e outros modelos específicos foram movidas para app.db.base.py
# para centralizar o carregamento de modelos.

# from app.middleware.request_context_middleware import RequestContextMiddleware # Removed import

# Configuração básica do logging
logging.basicConfig(
    level=settings.LOGGING_LEVEL if hasattr(settings, "LOGGING_LEVEL") else logging.INFO
)
logger_main = logging.getLogger(__name__)
logger_main.info(f"Logging level set to: {logging.getLogger().getEffectiveLevel()}")


# FORÇANDO API_PREFIX DEVIDO A VARIÁVEL DE AMBIENTE CONFLITANTE
settings.API_PREFIX = "/api"
logger_main.info(
    f"API_PREFIX foi forçado para: '{settings.API_PREFIX}'"
)  # Substituído print por logger

# Cria a instância principal da aplicação FastAPI
app = FastAPI(  # noqa: F811
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_PREFIX}/openapi.json",  # Define o caminho para o schema OpenAPI
)

# Configuração do CORS
# Verifica se há origens definidas nas configurações
logger_main.info(f"CORS Origins loaded: {settings.BACKEND_CORS_ORIGINS}")
if settings.BACKEND_CORS_ORIGINS:
    # Remove trailing slashes from CORS origins to avoid mismatch
    cors_origins = [str(origin).rstrip('/') for origin in settings.BACKEND_CORS_ORIGINS]
    logger_main.info(f"Adding CORS middleware with origins: {cors_origins}")
    app.add_middleware(
        CORSMiddleware,
        allow_origins=cors_origins,  # Lista de origens permitidas
        allow_credentials=True,  # Permite cookies/autenticação
        allow_methods=["*"],  # Métodos HTTP permitidos
        allow_headers=["*"],  # Cabeçalhos HTTP permitidos
    )
else:
    logger_main.warning("No CORS origins configured!")




# Middlewares
# Adicionar RequestContextMiddleware primeiro para popular request.state
# app.add_middleware(RequestContextMiddleware) # Removed non-existent middleware

# Adicionar TenantResolverMiddleware apenas se não estiver em TESTING_MODE
if not settings.TESTING_MODE:
    app.add_middleware(TenantResolverMiddleware)

# Inclui o roteador principal da API (ainda vindo de api_v1 internamente)
app.include_router(api_router, prefix=settings.API_PREFIX)

# --- TODO: Adicionar Middlewares ---
# Aqui adicionaremos o middleware de multi-tenancy e outros globais
# from app.core.middleware import TenantMiddleware
# app.add_middleware(TenantMiddleware)

# Monta a aplicação Socket.IO na aplicação FastAPI
# Todas as requisições para /socket.io (ou o path definido) serão tratadas pelo sio_app
app.mount("/socket.io", sio_app, name="socketio")  # Monta no path /socket.io


# Endpoint raiz simples para teste
@app.get("/", tags=["Root"])
async def read_root():
    """Endpoint raiz da API."""
    return {"message": f"Welcome to {settings.PROJECT_NAME}!"}


# Endpoint de teste para verificar se o Socket.IO está funcionando
@app.get("/ws-test", tags=["WebSocket"])
async def websocket_test():
    """Endpoint para testar se o WebSocket está configurado."""
    return {
        "message": "WebSocket endpoint mounted at /socket.io",
        "socketio_path": "/socket.io/",
        "test_url": "http://localhost:8000/socket.io/?EIO=4&transport=polling"
    }


# Endpoint para compatibilidade com clientes que esperam /v1/models
@app.get("/v1/models", tags=["Models"])
async def models_compatibility():
    """Endpoint de compatibilidade para /v1/models."""
    return {
        "object": "list",
        "data": [
            {
                "id": "trix-api-v1",
                "object": "model",
                "created": 1677649963,
                "owned_by": "trix",
                "permission": [],
                "root": "trix-api-v1",
                "parent": None
            }
        ]
    }


# --- TODO: Configurar i18n ---
# Aqui adicionaremos a configuração da biblioteca i18n

# Para executar a aplicação (exemplo usando uvicorn):
# uvicorn main:app --reload --host 0.0.0.0 --port 8000
