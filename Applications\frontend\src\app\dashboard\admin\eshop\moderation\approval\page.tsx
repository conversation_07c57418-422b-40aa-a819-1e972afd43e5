'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import {
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  EyeIcon,
  DocumentTextIcon,
  UserIcon,
  CalendarDaysIcon,
  TagIcon,
  CurrencyDollarIcon,
  BuildingStorefrontIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { toast } from 'sonner';

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  market_type: 'b2b' | 'b2c' | 'marketplace';
  approval_status: 'pending' | 'approved' | 'rejected' | 'revision_requested';
  vendor_name: string;
  category: string;
  submitted_date: string;
  business_name?: string;
  commission_rate?: number;
  rejection_reason?: string;
  revision_notes?: string;
  featured_image_url?: string;
}

const mockProducts: Product[] = [
  {
    id: '1',
    name: 'Premium Coffee Beans',
    description: 'High-quality arabica coffee beans from Brazil',
    price: 29.99,
    market_type: 'b2b',
    approval_status: 'pending',
    vendor_name: 'Coffee Supplier Ltd',
    category: 'Beverages',
    submitted_date: '2025-01-23T10:30:00Z',
    business_name: 'Coffee Supplier Ltd',
    commission_rate: 5.5,
    featured_image_url: '/api/placeholder/300/300'
  },
  {
    id: '2',
    name: 'Organic Tea Collection',
    description: 'Premium organic tea varieties from different regions',
    price: 45.00,
    market_type: 'b2c',
    approval_status: 'pending',
    vendor_name: 'Green Tea Co',
    category: 'Beverages',
    submitted_date: '2025-01-23T09:15:00Z',
    commission_rate: 7.0,
    featured_image_url: '/api/placeholder/300/300'
  },
  {
    id: '3',
    name: 'Professional Kitchen Equipment',
    description: 'Commercial grade kitchen equipment for restaurants',
    price: 1299.99,
    market_type: 'b2b',
    approval_status: 'revision_requested',
    vendor_name: 'Kitchen Pro',
    category: 'Equipment',
    submitted_date: '2025-01-22T14:20:00Z',
    business_name: 'Kitchen Pro Solutions',
    commission_rate: 3.5,
    revision_notes: 'Please provide more detailed product specifications',
    featured_image_url: '/api/placeholder/300/300'
  }
];

const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending': return 'bg-yellow-100 text-yellow-800';
    case 'approved': return 'bg-green-100 text-green-800';
    case 'rejected': return 'bg-red-100 text-red-800';
    case 'revision_requested': return 'bg-orange-100 text-orange-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getMarketTypeColor = (type: string) => {
  switch (type) {
    case 'b2b': return 'bg-blue-100 text-blue-800';
    case 'b2c': return 'bg-purple-100 text-purple-800';
    case 'marketplace': return 'bg-indigo-100 text-indigo-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

export default function EShopModerationApprovalPage() {
  const [products, setProducts] = useState<Product[]>(mockProducts);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterMarket, setFilterMarket] = useState('all');
  const [isLoading, setIsLoading] = useState(false);

  // Filter products based on status, market type, and search term
  const filteredProducts = products.filter(product => {
    const matchesStatus = filterStatus === 'all' || product.approval_status === filterStatus;
    const matchesMarket = filterMarket === 'all' || product.market_type === filterMarket;
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.vendor_name.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesStatus && matchesMarket && matchesSearch;
  });

  const statusCounts = {
    pending: products.filter(p => p.approval_status === 'pending').length,
    approved: products.filter(p => p.approval_status === 'approved').length,
    rejected: products.filter(p => p.approval_status === 'rejected').length,
    revision_requested: products.filter(p => p.approval_status === 'revision_requested').length
  };

  const handleApprove = async (productId: string) => {
    setIsLoading(true);
    try {
      // API call would go here
      setProducts(prev => prev.map(p =>
        p.id === productId ? { ...p, approval_status: 'approved' as const } : p
      ));
      toast.success('Product approved successfully!');
    } catch (error) {
      toast.error('Failed to approve product');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReject = async (productId: string, reason: string) => {
    setIsLoading(true);
    try {
      // API call would go here
      setProducts(prev => prev.map(p =>
        p.id === productId ? {
          ...p,
          approval_status: 'rejected' as const,
          rejection_reason: reason
        } : p
      ));
      toast.success('Product rejected');
    } catch (error) {
      toast.error('Failed to reject product');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRequestRevision = async (productId: string, notes: string) => {
    setIsLoading(true);
    try {
      // API call would go here
      setProducts(prev => prev.map(p =>
        p.id === productId ? {
          ...p,
          approval_status: 'revision_requested' as const,
          revision_notes: notes
        } : p
      ));
      toast.success('Revision requested');
    } catch (error) {
      toast.error('Failed to request revision');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="border-b border-gray-200 pb-4">
        <h1 className="text-2xl font-bold text-gray-900">Product Approval Dashboard</h1>
        <p className="text-gray-600 mt-1">
          Review and manage product submissions for marketplace approval
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white p-6 rounded-lg shadow border border-gray-200"
        >
          <div className="flex items-center">
            <ClockIcon className="h-8 w-8 text-yellow-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Pending Review</p>
              <p className="text-2xl font-bold text-gray-900">{statusCounts.pending}</p>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white p-6 rounded-lg shadow border border-gray-200"
        >
          <div className="flex items-center">
            <CheckCircleIcon className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Approved</p>
              <p className="text-2xl font-bold text-gray-900">{statusCounts.approved}</p>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white p-6 rounded-lg shadow border border-gray-200"
        >
          <div className="flex items-center">
            <XCircleIcon className="h-8 w-8 text-red-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Rejected</p>
              <p className="text-2xl font-bold text-gray-900">{statusCounts.rejected}</p>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white p-6 rounded-lg shadow border border-gray-200"
        >
          <div className="flex items-center">
            <DocumentTextIcon className="h-8 w-8 text-orange-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Needs Revision</p>
              <p className="text-2xl font-bold text-gray-900">{statusCounts.revision_requested}</p>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white p-6 rounded-lg shadow border border-gray-200">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="text"
                placeholder="Search products or vendors..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="approved">Approved</SelectItem>
              <SelectItem value="rejected">Rejected</SelectItem>
              <SelectItem value="revision_requested">Needs Revision</SelectItem>
            </SelectContent>
          </Select>

          <Select value={filterMarket} onValueChange={setFilterMarket}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue placeholder="Filter by market" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Markets</SelectItem>
              <SelectItem value="b2b">B2B</SelectItem>
              <SelectItem value="b2c">B2C</SelectItem>
              <SelectItem value="marketplace">Marketplace</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Products Table */}
      <div className="bg-white rounded-lg shadow border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Product Submissions</h2>
          <p className="text-sm text-gray-600 mt-1">
            {filteredProducts.length} product{filteredProducts.length !== 1 ? 's' : ''} found
          </p>
        </div>

        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Product</TableHead>
                <TableHead>Vendor</TableHead>
                <TableHead>Market</TableHead>
                <TableHead>Price</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Submitted</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredProducts.map((product) => (
                <TableRow
                  key={product.id}
                  className="hover:bg-gray-50 cursor-pointer"
                  onClick={() => setSelectedProduct(product)}
                >
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 relative">
                        <Image
                          src={product.featured_image_url || '/api/placeholder/40/40'}
                          alt={product.name}
                          fill
                          className="rounded-lg object-cover"
                        />
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">{product.name}</div>
                        <div className="text-sm text-gray-500 truncate max-w-xs">{product.description}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="text-sm text-gray-900">{product.vendor_name}</div>
                      {product.business_name && (
                        <div className="text-xs text-gray-500">{product.business_name}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getMarketTypeColor(product.market_type)}>
                      {product.market_type.toUpperCase()}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-gray-900">${product.price}</span>
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(product.approval_status)}>
                      {product.approval_status.replace('_', ' ').toUpperCase()}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-gray-500">
                      {new Date(product.submitted_date).toLocaleDateString()}
                    </span>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedProduct(product);
                      }}
                    >
                      <EyeIcon className="h-4 w-4" />
                    </Button>
                    {product.approval_status === 'pending' && (
                      <div className="flex space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleApprove(product.id);
                          }}
                          className="text-green-600 hover:text-green-700"
                        >
                          <CheckCircleIcon className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleReject(product.id, 'Quality standards not met');
                          }}
                          className="text-red-600 hover:text-red-700"
                        >
                          <XCircleIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Product Detail Modal */}
      {selectedProduct && (
        <Dialog open={!!selectedProduct} onOpenChange={() => setSelectedProduct(null)}>
          <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <div className="flex items-center justify-between">
                <DialogTitle>
                  <h3 className="text-lg font-semibold text-gray-900">Product Details</h3>
                </DialogTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedProduct(null)}
                >
                  ×
                </Button>
              </div>
            </DialogHeader>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Product Name</label>
                <p className="text-sm text-gray-900">{selectedProduct.name}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">Description</label>
                <p className="text-sm text-gray-900">{selectedProduct.description}</p>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Price</label>
                  <p className="text-sm text-gray-900">${selectedProduct.price}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Category</label>
                  <p className="text-sm text-gray-900">{selectedProduct.category}</p>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Market Type</label>
                  <Badge className={getMarketTypeColor(selectedProduct.market_type)}>
                    {selectedProduct.market_type.toUpperCase()}
                  </Badge>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Status</label>
                  <Badge className={getStatusColor(selectedProduct.approval_status)}>
                    {selectedProduct.approval_status.replace('_', ' ').toUpperCase()}
                  </Badge>
                </div>
              </div>
              
              {selectedProduct.commission_rate && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Commission Rate</label>
                  <p className="text-sm text-gray-900">{selectedProduct.commission_rate}%</p>
                </div>
              )}
              
              {selectedProduct.rejection_reason && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Rejection Reason</label>
                  <p className="text-sm text-red-600">{selectedProduct.rejection_reason}</p>
                </div>
              )}
              
              {selectedProduct.revision_notes && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Revision Notes</label>
                  <p className="text-sm text-orange-600">{selectedProduct.revision_notes}</p>
                </div>
              )}
              
              {selectedProduct.approval_status === 'pending' && (
                <div className="flex space-x-2 pt-4">
                  <Button
                    variant="outline"
                    onClick={() => handleRequestRevision(selectedProduct.id, 'Please provide more details')}
                    disabled={isLoading}
                  >
                    Request Revision
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={() => handleReject(selectedProduct.id, 'Does not meet quality standards')}
                    disabled={isLoading}
                  >
                    Reject
                  </Button>
                  <Button
                    onClick={() => handleApprove(selectedProduct.id)}
                    disabled={isLoading}
                  >
                    Approve
                  </Button>
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}