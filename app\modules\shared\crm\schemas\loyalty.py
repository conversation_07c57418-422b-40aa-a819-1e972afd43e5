"""Loyalty schemas for CRM module."""

import uuid  # noqa: E402
from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field, ConfigDict

from app.modules.shared.crm.models.loyalty import (  # noqa: E402
    LoyaltyProgramType,
    LoyaltyMembershipStatus,
    LoyaltyTransactionType,
)


# Base LoyaltyProgram Schema
class LoyaltyProgramBase(BaseModel):
    """Base schema for LoyaltyProgram."""

    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    program_type: LoyaltyProgramType

    # Status
    is_active: bool = True
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None

    # Rules
    earning_rules: Optional[Dict[str, Any]] = None
    redemption_rules: Optional[Dict[str, Any]] = None
    expiration_rules: Optional[Dict[str, Any]] = None
    tier_rules: Optional[Dict[str, Any]] = None


# Schema for creating a new loyalty program
class LoyaltyProgramCreate(LoyaltyProgramBase):
    """Schema for creating a new LoyaltyProgram."""


# Schema for updating a loyalty program
class LoyaltyProgramUpdate(BaseModel):
    """Schema for updating a LoyaltyProgram."""

    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    program_type: Optional[LoyaltyProgramType] = None

    # Status
    is_active: Optional[bool] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None

    # Rules
    earning_rules: Optional[Dict[str, Any]] = None
    redemption_rules: Optional[Dict[str, Any]] = None
    expiration_rules: Optional[Dict[str, Any]] = None
    tier_rules: Optional[Dict[str, Any]] = None


# Schema for reading a loyalty program
class LoyaltyProgramRead(LoyaltyProgramBase):
    """Schema for reading a LoyaltyProgram."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Base LoyaltyMembership Schema
class LoyaltyMembershipBase(BaseModel):
    """Base schema for LoyaltyMembership."""

    membership_number: Optional[str] = None
    status: LoyaltyMembershipStatus = LoyaltyMembershipStatus.ACTIVE

    # Points/balance
    points_balance: int = 0
    tier_level: Optional[str] = None

    # Dates
    join_date: datetime = Field(default_factory=datetime.utcnow)
    expiry_date: Optional[datetime] = None


# Schema for creating a new loyalty membership
class LoyaltyMembershipCreate(LoyaltyMembershipBase):
    """Schema for creating a new LoyaltyMembership."""

    program_id: uuid.UUID
    account_id: uuid.UUID


# Schema for updating a loyalty membership
class LoyaltyMembershipUpdate(BaseModel):
    """Schema for updating a LoyaltyMembership."""

    membership_number: Optional[str] = None
    status: Optional[LoyaltyMembershipStatus] = None

    # Points/balance
    points_balance: Optional[int] = None
    tier_level: Optional[str] = None

    # Dates
    expiry_date: Optional[datetime] = None
    last_activity_date: Optional[datetime] = None


# Schema for reading a loyalty membership
class LoyaltyMembershipRead(LoyaltyMembershipBase):
    """Schema for reading a LoyaltyMembership."""

    id: uuid.UUID
    program_id: uuid.UUID
    account_id: uuid.UUID
    tenant_id: uuid.UUID
    last_activity_date: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Base LoyaltyTransaction Schema
class LoyaltyTransactionBase(BaseModel):
    """Base schema for LoyaltyTransaction."""

    transaction_type: LoyaltyTransactionType
    points: int

    # Reference information
    reference_type: Optional[str] = None
    reference_id: Optional[str] = None

    # Description
    description: Optional[str] = None

    # Timing
    transaction_date: datetime = Field(default_factory=datetime.utcnow)


# Schema for creating a new loyalty transaction
class LoyaltyTransactionCreate(LoyaltyTransactionBase):
    """Schema for creating a new LoyaltyTransaction."""

    membership_id: uuid.UUID


# Schema for reading a loyalty transaction
class LoyaltyTransactionRead(LoyaltyTransactionBase):
    """Schema for reading a LoyaltyTransaction."""

    id: uuid.UUID
    membership_id: uuid.UUID
    tenant_id: uuid.UUID
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)
