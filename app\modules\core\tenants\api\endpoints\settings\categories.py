"""
Categories/Zones settings API endpoints for tenant configuration.
"""

import uuid
from typing import List, Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func

from app.core.db_dependencies import get_db
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    get_current_tenant_from_header as get_current_tenant,
    require_tenant_role,
)
from app.modules.core.roles.models.roles import RolePermissions
from app.core.exceptions import BusinessLogicError, NotFoundError
from app.modules.core.tenants.models.tenant import Tenant
from app.modules.core.users.models.user import User

from app.modules.core.tenants.schemas.tenant_settings import (
    TenantSettingsRead,
)
from app.modules.core.tenants.services.tenant_settings import (
    tenant_settings_service,
)

# Import table model to get zones
from app.modules.tenants.restaurants.table_management.models.table import Table

router = APIRouter()


@router.get(
    "/zones",
    response_model=List[Dict[str, Any]],
    status_code=status.HTTP_200_OK,
    summary="Get Restaurant Zones",
    description="Get all zones/categories used in the restaurant.",
)
async def get_restaurant_zones(
    *,
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role(
            required_roles=RolePermissions.STAFF_ROLES, 
            tenant_id_source="header"
        )
    ),
) -> List[Dict[str, Any]]:
    """
    Get all zones/categories used in the restaurant.
    Includes zones from tables and custom zones from tenant settings.
    Requires STAFF, MANAGER, or OWNER role.
    """
    try:
        # Get zones from tables
        result = await db.execute(
            select(Table.zone, func.count(Table.id).label('table_count'))
            .where(Table.tenant_id == current_tenant.id)
            .where(Table.zone.isnot(None))
            .group_by(Table.zone)
        )
        table_zones = result.fetchall()

        # Get custom zones from tenant settings
        settings = await tenant_settings_service.get_settings(db, current_tenant.id)
        custom_zones = []
        if settings and settings.additional_settings:
            custom_zones = settings.additional_settings.get('custom_zones', [])

        # Combine zones
        zones_data = []
        
        # Add zones from tables
        for zone_name, table_count in table_zones:
            zones_data.append({
                "name": zone_name,
                "type": "table_zone",
                "table_count": table_count,
                "is_active": True,
                "is_custom": False
            })

        # Add custom zones (zones without tables)
        table_zone_names = {zone_name for zone_name, _ in table_zones}
        for custom_zone in custom_zones:
            if custom_zone not in table_zone_names:
                zones_data.append({
                    "name": custom_zone,
                    "type": "custom_zone",
                    "table_count": 0,
                    "is_active": True,
                    "is_custom": True
                })

        # Sort zones by name
        zones_data.sort(key=lambda x: x["name"])

        return zones_data

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving zones: {str(e)}"
        )


@router.post(
    "/zones",
    response_model=Dict[str, Any],
    status_code=status.HTTP_201_CREATED,
    summary="Create Custom Zone",
    description="Create a new custom zone/category.",
)
async def create_custom_zone(
    *,
    db: AsyncSession = Depends(get_db),
    zone_name: str,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role(
            required_roles=RolePermissions.ADMIN_ROLES, 
            tenant_id_source="header"
        )
    ),
) -> Dict[str, Any]:
    """
    Create a new custom zone/category.
    Requires MANAGER or OWNER role.
    """
    try:
        # Validate zone name
        if not zone_name or not zone_name.strip():
            raise BusinessLogicError("Zone name cannot be empty")

        zone_name = zone_name.strip()

        # Check if zone already exists in tables
        result = await db.execute(
            select(Table.zone)
            .where(Table.tenant_id == current_tenant.id)
            .where(Table.zone == zone_name)
        )
        if result.first():
            raise BusinessLogicError(f"Zone '{zone_name}' already exists in tables")

        # Get current settings
        settings = await tenant_settings_service.get_settings(db, current_tenant.id)
        
        # Get current custom zones
        additional_settings = settings.additional_settings or {}
        custom_zones = additional_settings.get('custom_zones', [])

        # Check if zone already exists in custom zones
        if zone_name in custom_zones:
            raise BusinessLogicError(f"Custom zone '{zone_name}' already exists")

        # Add new zone
        custom_zones.append(zone_name)
        additional_settings['custom_zones'] = custom_zones

        # Update settings
        settings.additional_settings = additional_settings
        db.add(settings)
        await db.commit()

        return {
            "name": zone_name,
            "type": "custom_zone",
            "table_count": 0,
            "is_active": True,
            "is_custom": True,
            "message": f"Custom zone '{zone_name}' created successfully"
        }

    except BusinessLogicError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating zone: {str(e)}"
        )


@router.delete(
    "/zones/{zone_name}",
    response_model=Dict[str, str],
    status_code=status.HTTP_200_OK,
    summary="Delete Custom Zone",
    description="Delete a custom zone/category (only if it has no tables).",
)
async def delete_custom_zone(
    *,
    db: AsyncSession = Depends(get_db),
    zone_name: str,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role(
            required_roles=RolePermissions.ADMIN_ROLES, 
            tenant_id_source="header"
        )
    ),
) -> Dict[str, str]:
    """
    Delete a custom zone/category.
    Only custom zones without tables can be deleted.
    Requires MANAGER or OWNER role.
    """
    try:
        # Check if zone has tables
        result = await db.execute(
            select(func.count(Table.id))
            .where(Table.tenant_id == current_tenant.id)
            .where(Table.zone == zone_name)
        )
        table_count = result.scalar()

        if table_count > 0:
            raise BusinessLogicError(
                f"Cannot delete zone '{zone_name}' because it has {table_count} table(s). "
                "Please move or delete the tables first."
            )

        # Get current settings
        settings = await tenant_settings_service.get_settings(db, current_tenant.id)
        
        # Get current custom zones
        additional_settings = settings.additional_settings or {}
        custom_zones = additional_settings.get('custom_zones', [])

        # Check if zone exists in custom zones
        if zone_name not in custom_zones:
            raise NotFoundError(f"Custom zone '{zone_name}' not found")

        # Remove zone
        custom_zones.remove(zone_name)
        additional_settings['custom_zones'] = custom_zones

        # Update settings
        settings.additional_settings = additional_settings
        db.add(settings)
        await db.commit()

        return {"message": f"Custom zone '{zone_name}' deleted successfully"}

    except (BusinessLogicError, NotFoundError) as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting zone: {str(e)}"
        )
