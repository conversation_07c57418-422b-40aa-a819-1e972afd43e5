import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../models/menu_item_model.dart';
import '../services/storage_service.dart';
import '../constants/app_constants.dart';

// Menu state
class MenuState {
  final List<MenuItemModel> items;
  final bool isLoading;
  final String? error;
  final String selectedCategory;
  final List<String> categories;
  final MenuItemModel? selectedItem;

  const MenuState({
    this.items = const [],
    this.isLoading = false,
    this.error,
    this.selectedCategory = 'all',
    this.categories = const [],
    this.selectedItem,
  });

  MenuState copyWith({
    List<MenuItemModel>? items,
    bool? isLoading,
    String? error,
    String? selectedCategory,
    List<String>? categories,
    MenuItemModel? selectedItem,
  }) {
    return MenuState(
      items: items ?? this.items,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      selectedCategory: selectedCategory ?? this.selectedCategory,
      categories: categories ?? this.categories,
      selectedItem: selectedItem ?? this.selectedItem,
    );
  }
}

// Menu notifier
class MenuNotifier extends StateNotifier<MenuState> {
  MenuNotifier() : super(const MenuState()) {
    loadMenu();
  }

  final _uuid = const Uuid();

  Future<void> loadMenu() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final items = await StorageService.getAllMenuItems();
      
      // If no items exist, create mock menu
      if (items.isEmpty) {
        await _createMockMenu();
        final mockItemsData = StorageService.getAllMenuItems();
        final mockItems = mockItemsData.map((item) => MenuItemModel.fromJson(item)).toList();
        final categories = _extractCategories(mockItems);
        state = state.copyWith(
          items: mockItems,
          categories: categories,
          isLoading: false,
        );
      } else {
        final categories = _extractCategories(items);
        state = state.copyWith(
          items: items,
          categories: categories,
          isLoading: false,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Erro ao carregar menu: ${e.toString()}',
      );
    }
  }

  Future<void> _createMockMenu() async {
    final mockItems = [
      // Entradas
      MenuItemModel(
        id: _uuid.v4(),
        name: 'Bruschetta Italiana',
        description: 'Pão italiano tostado com tomate, manjericão e azeite extra virgem',
        price: 18.90,
        category: MenuCategories.appetizers,
        imageUrl: 'assets/images/bruschetta.jpg',
        isAvailable: true,
        preparationTime: 8,
        allergens: ['glúten'],
        createdAt: DateTime.now(),
        modifiers: [
          MenuModifier(
            id: _uuid.v4(),
            name: 'Queijo Extra',
            options: [
              'Mozzarella',
              'Parmesão',
            ],
          ),
        ],
      ),
      MenuItemModel(
        id: _uuid.v4(),
        name: 'Camarão Empanado',
        description: 'Camarões grandes empanados servidos com molho tártaro',
        price: 32.90,
        createdAt: DateTime.now(),
        category: MenuCategories.appetizers,
        imageUrl: 'assets/images/camarao.jpg',
        isAvailable: true,
        preparationTime: 12,
        allergens: ['crustáceos', 'glúten'],
      ),
      
      // Pratos Principais
      MenuItemModel(
        id: _uuid.v4(),
        name: 'Salmão Grelhado',
        description: 'Filé de salmão grelhado com legumes e arroz de ervas',
        price: 45.90,
        category: MenuCategories.mainCourses,
        imageUrl: 'assets/images/salmao.jpg',
        isAvailable: true,
        preparationTime: 18,
        allergens: ['peixe'],
        createdAt: DateTime.now(),
        modifiers: [
          MenuModifier(
            id: _uuid.v4(),
            name: 'Ponto da Carne',
            isRequired: true,
            options: [
              'Mal Passado',
              'Ao Ponto',
              'Bem Passado',
            ],
          ),
        ],
      ),
      MenuItemModel(
        id: _uuid.v4(),
        name: 'Picanha na Brasa',
        description: 'Picanha premium grelhada na brasa com farofa e vinagrete',
        price: 52.90,
        createdAt: DateTime.now(),
        category: MenuCategories.mainCourses,
        imageUrl: 'assets/images/picanha.jpg',
        isAvailable: true,
        preparationTime: 25,
        modifiers: [
          MenuModifier(
            id: _uuid.v4(),
            name: 'Ponto da Carne',
            isRequired: true,
            options: [
              'Mal Passado',
              'Ao Ponto',
              'Bem Passado',
            ],
          ),
          MenuModifier(
            id: _uuid.v4(),
            name: 'Acompanhamentos',
            options: [
              'Batata Frita',
              'Arroz à Grega',
            ],
          ),
        ],
      ),
      MenuItemModel(
        id: _uuid.v4(),
        name: 'Risotto de Cogumelos',
        description: 'Risotto cremoso com mix de cogumelos e parmesão',
        price: 38.90,
        category: MenuCategories.mainCourses,
        imageUrl: 'assets/images/risotto.jpg',
        isAvailable: true,
        preparationTime: 20,
        allergens: ['laticínios'],
        createdAt: DateTime.now(),
      ),
      
      // Pizzas
      MenuItemModel(
        id: _uuid.v4(),
        name: 'Pizza Margherita',
        description: 'Molho de tomate, mozzarella, manjericão e azeite',
        price: 28.90,
        category: MenuCategories.pizzas,
        imageUrl: 'assets/images/margherita.jpg',
        isAvailable: true,
        preparationTime: 15,
        allergens: ['glúten', 'laticínios'],
        createdAt: DateTime.now(),
        modifiers: [
          MenuModifier(
            id: _uuid.v4(),
            name: 'Tamanho',
            isRequired: true,
            options: [
              'Pequena',
              'Média',
              'Grande',
            ],
          ),
          MenuModifier(
            id: _uuid.v4(),
            name: 'Ingredientes Extras',
            options: [
              'Pepperoni',
              'Azeitonas',
              'Rúcula',
            ],
          ),
        ],
      ),
      MenuItemModel(
        id: _uuid.v4(),
        name: 'Pizza Portuguesa',
        description: 'Presunto, ovos, cebola, azeitonas e mozzarella',
        price: 35.90,
        category: MenuCategories.pizzas,
        imageUrl: 'assets/images/portuguesa.jpg',
        isAvailable: true,
        preparationTime: 15,
        allergens: ['glúten', 'laticínios', 'ovos'],
        createdAt: DateTime.now(),
      ),
      
      // Bebidas
      MenuItemModel(
        id: _uuid.v4(),
        name: 'Suco Natural de Laranja',
        description: 'Suco de laranja natural espremido na hora',
        price: 8.90,
        category: MenuCategories.beverages,
        imageUrl: 'assets/images/suco_laranja.jpg',
        isAvailable: true,
        preparationTime: 3,
        createdAt: DateTime.now(),
        modifiers: [
          MenuModifier(
            id: _uuid.v4(),
            name: 'Tamanho',
            isRequired: true,
            options: [
              '300ml',
              '500ml',
              '700ml',
            ],
          ),
        ],
      ),
      MenuItemModel(
        id: _uuid.v4(),
        name: 'Refrigerante Coca-Cola',
        description: 'Coca-Cola gelada',
        price: 6.50,
        category: MenuCategories.beverages,
        imageUrl: 'assets/images/coca_cola.jpg',
        isAvailable: true,
        preparationTime: 1,
        createdAt: DateTime.now(),
        modifiers: [
          MenuModifier(
            id: _uuid.v4(),
            name: 'Tamanho',
            isRequired: true,
            options: [
              'Lata 350ml',
              'Garrafa 600ml',
            ],
          ),
        ],
      ),
      MenuItemModel(
        id: _uuid.v4(),
        name: 'Vinho Tinto Reserva',
        description: 'Vinho tinto seco, safra especial',
        price: 45.00,
        category: MenuCategories.beverages,
        imageUrl: 'assets/images/vinho_tinto.jpg',
        isAvailable: true,
        preparationTime: 2,
        createdAt: DateTime.now(),
      ),
      
      // Sobremesas
      MenuItemModel(
        id: _uuid.v4(),
        name: 'Tiramisu',
        description: 'Sobremesa italiana com café, mascarpone e cacau',
        price: 16.90,
        category: MenuCategories.desserts,
        imageUrl: 'assets/images/tiramisu.jpg',
        createdAt: DateTime.now(),
        isAvailable: true,
        preparationTime: 5,
        allergens: ['laticínios', 'ovos', 'glúten'],
      ),
      MenuItemModel(
        id: _uuid.v4(),
        name: 'Petit Gateau',
        description: 'Bolinho de chocolate quente com sorvete de baunilha',
        price: 18.90,
        category: MenuCategories.desserts,
        imageUrl: 'assets/images/petit_gateau.jpg',
        isAvailable: true,
        preparationTime: 12,
        allergens: ['laticínios', 'ovos', 'glúten'],
        createdAt: DateTime.now(),
      ),
      MenuItemModel(
        id: _uuid.v4(),
        name: 'Salada de Frutas',
        description: 'Mix de frutas frescas da estação',
        price: 12.90,
        category: MenuCategories.desserts,
        imageUrl: 'assets/images/salada_frutas.jpg',
        isAvailable: true,
        preparationTime: 5,
        createdAt: DateTime.now(),
      ),
    ];

    for (final item in mockItems) {
      await StorageService.saveMenuItem(item.id, item.toJson());
    }
  }

  List<String> _extractCategories(List<MenuItemModel> items) {
    final categories = items.map((item) => item.category).toSet().toList();
    categories.sort();
    return ['all', ...categories];
  }

  Future<bool> createMenuItem(MenuItemModel item) async {
    try {
      await StorageService.saveMenuItem(item.id, item.toJson());
      
      final updatedItems = [...state.items, item];
      final categories = _extractCategories(updatedItems);
      
      state = state.copyWith(
        items: updatedItems,
        categories: categories,
      );
      
      return true;
    } catch (e) {
      state = state.copyWith(
        error: 'Erro ao criar item: ${e.toString()}',
      );
      return false;
    }
  }

  Future<bool> updateMenuItem(MenuItemModel item) async {
    try {
      await StorageService.saveMenuItem(item.id, item.toJson());
      
      final itemIndex = state.items.indexWhere((i) => i.id == item.id);
      if (itemIndex == -1) return false;
      
      final updatedItems = [...state.items];
      updatedItems[itemIndex] = item;
      
      final categories = _extractCategories(updatedItems);
      
      state = state.copyWith(
        items: updatedItems,
        categories: categories,
      );
      
      return true;
    } catch (e) {
      state = state.copyWith(
        error: 'Erro ao atualizar item: ${e.toString()}',
      );
      return false;
    }
  }

  Future<bool> deleteMenuItem(String itemId) async {
    try {
      await StorageService.deleteMenuItem(itemId);
      
      final updatedItems = state.items.where((i) => i.id != itemId).toList();
      final categories = _extractCategories(updatedItems);
      
      state = state.copyWith(
        items: updatedItems,
        categories: categories,
      );
      
      return true;
    } catch (e) {
      state = state.copyWith(
        error: 'Erro ao excluir item: ${e.toString()}',
      );
      return false;
    }
  }

  Future<bool> toggleItemAvailability(String itemId) async {
    try {
      final itemIndex = state.items.indexWhere((i) => i.id == itemId);
      if (itemIndex == -1) return false;
      
      final item = state.items[itemIndex];
      final updatedItem = item.copyWith(isAvailable: !item.isAvailable);
      
      await StorageService.saveMenuItem(updatedItem.id, updatedItem.toJson());
      
      final updatedItems = [...state.items];
      updatedItems[itemIndex] = updatedItem;
      
      state = state.copyWith(items: updatedItems);
      return true;
    } catch (e) {
      state = state.copyWith(
        error: 'Erro ao alterar disponibilidade: ${e.toString()}',
      );
      return false;
    }
  }

  void setSelectedCategory(String category) {
    state = state.copyWith(selectedCategory: category);
  }

  void selectItem(MenuItemModel? item) {
    state = state.copyWith(selectedItem: item);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  // Getters for filtered items
  List<MenuItemModel> get filteredItems {
    if (state.selectedCategory == 'all') {
      return state.items;
    }
    return state.items.where((item) => item.category == state.selectedCategory).toList();
  }

  List<MenuItemModel> get availableItems => 
      state.items.where((item) => item.isAvailable).toList();
  
  List<MenuItemModel> get unavailableItems => 
      state.items.where((item) => !item.isAvailable).toList();

  List<MenuItemModel> getItemsByCategory(String category) {
    return state.items.where((item) => item.category == category).toList();
  }

  List<MenuItemModel> searchItems(String query) {
    final lowercaseQuery = query.toLowerCase();
    return state.items.where((item) => 
        item.name.toLowerCase().contains(lowercaseQuery) ||
        item.description.toLowerCase().contains(lowercaseQuery) ||
        item.category.toLowerCase().contains(lowercaseQuery)
    ).toList();
  }

  MenuItemModel? getItemById(String itemId) {
    try {
      return state.items.firstWhere((item) => item.id == itemId);
    } catch (e) {
      return null;
    }
  }

  // Statistics
  int get totalItems => state.items.length;
  int get availableItemsCount => availableItems.length;
  double get averagePrice => state.items.isEmpty ? 0 : 
      state.items.fold(0.0, (sum, item) => sum + item.price) / state.items.length;
}

// Menu provider
final menuProvider = StateNotifierProvider<MenuNotifier, MenuState>((ref) {
  return MenuNotifier();
});

// Convenience providers
final filteredMenuItemsProvider = Provider<List<MenuItemModel>>((ref) {
  return ref.watch(menuProvider.notifier).filteredItems;
});

final availableMenuItemsProvider = Provider<List<MenuItemModel>>((ref) {
  return ref.watch(menuProvider.notifier).availableItems;
});

final menuCategoriesProvider = Provider<List<String>>((ref) {
  return ref.watch(menuProvider).categories;
});

final selectedMenuItemProvider = Provider<MenuItemModel?>((ref) {
  return ref.watch(menuProvider).selectedItem;
});

final menuStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final notifier = ref.watch(menuProvider.notifier);
  return {
    'total': notifier.totalItems,
    'available': notifier.availableItemsCount,
    'averagePrice': notifier.averagePrice,
  };
});

// Search provider
final menuSearchProvider = StateProvider<String>((ref) => '');

final searchedMenuItemsProvider = Provider<List<MenuItemModel>>((ref) {
  final query = ref.watch(menuSearchProvider);
  final notifier = ref.watch(menuProvider.notifier);
  
  if (query.isEmpty) {
    return notifier.filteredItems;
  }
  
  return notifier.searchItems(query);
});