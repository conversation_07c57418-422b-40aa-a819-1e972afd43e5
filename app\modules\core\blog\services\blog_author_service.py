"""
Blog Author Service

Business logic for blog author management.
"""

import uuid
from typing import List, Optional, TYPE_CHECKING
from sqlalchemy import and_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from ..models.blog_author import <PERSON><PERSON><PERSON>uth<PERSON>
from ..schemas.blog_author import BlogAuthorCreate, BlogAuthorUpdate

if TYPE_CHECKING:
    from app.modules.core.users.models.user import User


class BlogAuthorService:
    """Service for blog author operations."""

    async def get_authors(
        self,
        db: AsyncSession,
        skip: int = 0,
        limit: int = 20,
        is_active: Optional[bool] = None,
        is_featured: Optional[bool] = None
    ) -> List[BlogAuthor]:
        """
        Get blog authors with filtering and pagination.

        Args:
            db: Database session
            skip: Number of authors to skip
            limit: Maximum number of authors to return
            is_active: Optional active status filter
            is_featured: Optional featured status filter

        Returns:
            List of blog authors
        """
        query = select(BlogAuthor).options(
            selectinload(BlogAuthor.user)
        )

        # Apply filters
        filters = []
        if is_active is not None:
            filters.append(BlogAuthor.is_active == is_active)
        if is_featured is not None:
            filters.append(BlogAuthor.is_featured == is_featured)

        if filters:
            query = query.where(and_(*filters))

        # Apply pagination
        query = query.offset(skip).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    async def get_author_by_slug(
        self,
        db: AsyncSession,
        slug: str
    ) -> Optional[BlogAuthor]:
        """
        Get a blog author by slug.

        Args:
            db: Database session
            slug: Author slug

        Returns:
            Blog author or None if not found
        """
        query = select(BlogAuthor).options(
            selectinload(BlogAuthor.user)
        ).where(BlogAuthor.slug == slug)

        result = await db.execute(query)
        return result.scalar_one_or_none()

    async def get_author_by_id(
        self,
        db: AsyncSession,
        author_id: uuid.UUID
    ) -> Optional[BlogAuthor]:
        """
        Get a blog author by ID.

        Args:
            db: Database session
            author_id: Author ID

        Returns:
            Blog author or None if not found
        """
        query = select(BlogAuthor).options(
            selectinload(BlogAuthor.user)
        ).where(BlogAuthor.id == author_id)

        result = await db.execute(query)
        return result.scalar_one_or_none()

    async def create_author(
        self,
        db: AsyncSession,
        author_data: BlogAuthorCreate
    ) -> BlogAuthor:
        """
        Create a new blog author.

        Args:
            db: Database session
            author_data: Author creation data

        Returns:
            Created blog author
        """
        db_author = BlogAuthor(
            slug=author_data.slug,
            display_name=author_data.display_name,
            email=author_data.email,
            bio=author_data.bio,
            avatar_url=author_data.avatar_url,
            website_url=author_data.website_url,
            twitter_handle=author_data.twitter_handle,
            linkedin_url=author_data.linkedin_url,
            github_url=author_data.github_url,
            is_active=author_data.is_active,
            is_featured=author_data.is_featured,
            user_id=author_data.user_id
        )

        db.add(db_author)
        await db.commit()
        await db.refresh(db_author)

        # Load relationships explicitly
        result = await db.execute(
            select(BlogAuthor).options(
                selectinload(BlogAuthor.user)
            ).where(BlogAuthor.id == db_author.id)
        )
        return result.scalar_one()

    async def link_user_to_author(
        self,
        db: AsyncSession,
        author_id: uuid.UUID,
        user_id: uuid.UUID
    ) -> Optional[BlogAuthor]:
        """
        Link a system user to an existing blog author.

        Args:
            db: Database session
            author_id: Author ID
            user_id: User ID to link

        Returns:
            Updated blog author or None if not found
        """
        # Check if user exists
        from app.modules.core.users.models.user import User
        user_query = select(User).where(User.id == user_id)
        user_result = await db.execute(user_query)
        user = user_result.scalar_one_or_none()

        if not user:
            return None

        # Get the author
        author_query = select(BlogAuthor).where(BlogAuthor.id == author_id)
        author_result = await db.execute(author_query)
        author = author_result.scalar_one_or_none()

        if not author:
            return None

        # Link the user
        author.user_id = user_id
        await db.commit()
        await db.refresh(author)

        # Load relationships explicitly
        result = await db.execute(
            select(BlogAuthor).options(
                selectinload(BlogAuthor.user)
            ).where(BlogAuthor.id == author.id)
        )
        return result.scalar_one()

    async def unlink_user_from_author(
        self,
        db: AsyncSession,
        author_id: uuid.UUID
    ) -> Optional[BlogAuthor]:
        """
        Unlink a system user from a blog author.

        Args:
            db: Database session
            author_id: Author ID

        Returns:
            Updated blog author or None if not found
        """
        # Get the author
        author_query = select(BlogAuthor).where(BlogAuthor.id == author_id)
        author_result = await db.execute(author_query)
        author = author_result.scalar_one_or_none()

        if not author:
            return None

        # Unlink the user
        author.user_id = None
        await db.commit()
        await db.refresh(author)

        # Load relationships explicitly
        result = await db.execute(
            select(BlogAuthor).options(
                selectinload(BlogAuthor.user)
            ).where(BlogAuthor.id == author.id)
        )
        return result.scalar_one()
