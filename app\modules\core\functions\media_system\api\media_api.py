"""
Media API

Endpoints REST para o sistema de mídia.
"""

import logging
from typing import List, Optional
from uuid import UUID
from pydantic import BaseModel

from fastapi import (
    APIRouter,
    Depends,
    File,
    Form,
    Header,
    HTTPException,
    Query,
    UploadFile,
    status
)
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.modules.core.auth.dependencies import get_current_user
from app.modules.core.users.models import User

from ..dependencies import (
    get_media_context_service,
    get_media_upload_service,
    get_media_processing_service,
    get_media_directory_service
)
from ..models import MediaContextType, MediaFileType
from ..schemas import (
    MediaContextRequest,
    MediaContextResponse,
    MediaUploadResponse,
    MediaUploadRead,
    MediaDirectoryCreate,
    MediaDirectoryRead,
    MediaQuotaResponse,
    MediaQuotaCheckResponse,
    MediaStatsResponse
)
from ..services import (
    MediaContextService,
    MediaUploadService,
    MediaProcessingService,
    MediaDirectoryService
)
from ..services.media_cleanup_service import MediaCleanupService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/media", tags=["Media System"])


@router.post("/upload", response_model=MediaUploadResponse)
async def upload_file(
    file: UploadFile = File(...),
    context_type: str = Form(...),
    context_id: Optional[UUID] = Form(None),
    directory_id: Optional[UUID] = Form(None),
    current_user: User = Depends(get_current_user),
    upload_service: MediaUploadService = Depends(get_media_upload_service),
    context_service: MediaContextService = Depends(get_media_context_service)
):
    """
    Faz upload de um arquivo.
    
    Args:
        file: Arquivo para upload
        context_type: Tipo de contexto (USER ou TENANT)
        context_id: ID do contexto (obrigatório para TENANT)
        directory_id: ID do diretório (opcional)
        current_user: Usuário atual
        upload_service: Serviço de upload
        context_service: Serviço de contexto
        
    Returns:
        MediaUploadResponse: Resposta do upload
    """
    try:
        # Converte string para enum
        try:
            context_type_enum = MediaContextType(context_type)
        except ValueError:
            valid_values = [e.value for e in MediaContextType]
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"context_type inválido. Valores válidos: {valid_values}"
            )

        # Valida contexto
        if context_type_enum == MediaContextType.TENANT and not context_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="context_id é obrigatório para contexto TENANT"
            )

        # Para contexto USER, usa o ID do usuário atual
        if context_type_enum == MediaContextType.USER:
            context_id = current_user.id

        # Obtém ou cria contexto
        context = await context_service.get_or_create_context(context_type_enum, context_id, current_user.id)

        # Faz upload
        result = await upload_service.upload_file(
            file=file,
            context=context,
            uploaded_by=current_user.id,
            directory_id=directory_id
        )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro no upload: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno no upload"
        )


@router.get("/download/{upload_id}")
async def download_file(
    upload_id: UUID,
    thumbnail: bool = Query(False, description="Baixar thumbnail"),
    compressed: bool = Query(False, description="Baixar versão comprimida"),
    # current_user: User = Depends(get_current_user),  # Temporariamente desabilitado
    upload_service: MediaUploadService = Depends(get_media_upload_service)
):
    """
    Faz download de um arquivo.
    
    Args:
        upload_id: ID do upload
        thumbnail: Se deve baixar thumbnail
        compressed: Se deve baixar versão comprimida
        current_user: Usuário atual
        upload_service: Serviço de upload
        
    Returns:
        StreamingResponse: Arquivo para download
    """
    try:
        upload = await upload_service.get_upload(upload_id)
        if not upload:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Arquivo não encontrado"
            )

        # TODO: Verificar permissões de acesso ao arquivo

        # Determina qual arquivo baixar
        file_path = upload.file_path
        filename = upload.filename

        if thumbnail and upload.thumbnail_path:
            file_path = upload.thumbnail_path
            filename = f"thumb_{upload.filename}"
        elif compressed and upload.compressed_path:
            file_path = upload.compressed_path
            filename = f"compressed_{upload.filename}"

        # Lê conteúdo do arquivo
        content = await upload_service.get_file_content(upload_id)
        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conteúdo do arquivo não encontrado"
            )

        # Retorna arquivo
        def generate():
            yield content

        return StreamingResponse(
            generate(),
            media_type=upload.mime_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro no download: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno no download"
        )


@router.get("/uploads", response_model=List[MediaUploadRead])
async def list_uploads(
    context_type: str = Query(...),
    context_id: Optional[UUID] = Query(None),
    media_type: Optional[str] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user: User = Depends(get_current_user),
    upload_service: MediaUploadService = Depends(get_media_upload_service),
    context_service: MediaContextService = Depends(get_media_context_service)
):
    """
    Lista uploads por contexto.

    Args:
        context_type: Tipo de contexto (USER ou TENANT)
        context_id: ID do contexto (obrigatório para TENANT)
        media_type: Filtro por tipo de mídia (opcional)
        skip: Número de registros para pular
        limit: Limite de registros
        current_user: Usuário atual
        upload_service: Serviço de upload
        context_service: Serviço de contexto

    Returns:
        List[MediaUploadRead]: Lista de uploads
    """
    try:
        logger.info(f"Listing uploads - context_type: {context_type}, context_id: {context_id}")

        # Converte string para enum
        try:
            context_type_enum = MediaContextType(context_type)
            logger.info(f"Context type converted to enum: {context_type_enum}")
        except ValueError:
            valid_values = [e.value for e in MediaContextType]
            logger.error(f"Invalid context_type: {context_type}. Valid values: {valid_values}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"context_type inválido. Valores válidos: {valid_values}"
            )

        # Converte media_type se fornecido
        media_type_enum = None
        if media_type:
            try:
                media_type_enum = MediaFileType(media_type)
            except ValueError:
                valid_values = [e.value for e in MediaFileType]
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"media_type inválido. Valores válidos: {valid_values}"
                )

        # Valida contexto
        if context_type_enum == MediaContextType.TENANT and not context_id:
            logger.error("context_id is required for TENANT context")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="context_id é obrigatório para contexto TENANT"
            )

        # Para contexto USER, usa o ID do usuário atual
        if context_type_enum == MediaContextType.USER:
            context_id = current_user.id
            logger.info(f"Using current user ID as context_id: {context_id}")

        logger.info(f"Getting context for type: {context_type_enum}, id: {context_id}")

        # Obtém contexto
        context = await context_service.get_or_create_context(context_type_enum, context_id, current_user.id)
        logger.info(f"Context obtained: {context.id if context else 'None'}")

        # Lista uploads
        logger.info(f"Listing uploads for context: {context.id}, skip: {skip}, limit: {limit}")
        uploads = await upload_service.get_uploads_by_context(
            context_id=context.id,
            skip=skip,
            limit=limit,
            media_type=media_type_enum
        )

        logger.info(f"Found {len(uploads)} uploads")
        return uploads

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao listar uploads: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno ao listar uploads"
        )


@router.delete("/uploads/{upload_id}")
async def delete_upload(
    upload_id: UUID,
    current_user: User = Depends(get_current_user),
    upload_service: MediaUploadService = Depends(get_media_upload_service)
):
    """
    Remove um upload.
    
    Args:
        upload_id: ID do upload
        current_user: Usuário atual
        upload_service: Serviço de upload
        
    Returns:
        dict: Resultado da operação
    """
    try:
        upload = await upload_service.get_upload(upload_id)
        if not upload:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Upload não encontrado"
            )

        # TODO: Verificar permissões para deletar

        success = await upload_service.delete_upload(upload_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Erro ao remover upload"
            )

        return {"message": "Upload removido com sucesso"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao remover upload: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno ao remover upload"
        )


@router.get("/quota", response_model=MediaQuotaResponse)
async def get_quota_info(
    context_type: MediaContextType = Query(...),
    context_id: Optional[UUID] = Query(None),
    current_user: User = Depends(get_current_user),
    context_service: MediaContextService = Depends(get_media_context_service)
):
    """
    Obtém informações de quota de um contexto.
    
    Args:
        context_type: Tipo de contexto
        context_id: ID do contexto
        current_user: Usuário atual
        context_service: Serviço de contexto
        
    Returns:
        MediaQuotaResponse: Informações de quota
    """
    try:
        # Valida contexto
        if context_type == MediaContextType.TENANT and not context_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="context_id é obrigatório para contexto TENANT"
            )

        # Para contexto USER, usa o ID do usuário atual
        if context_type == MediaContextType.USER:
            context_id = current_user.id

        # Obtém contexto
        context = await context_service.get_or_create_context(context_type, context_id, current_user.id)

        # Obtém informações de quota
        quota_info = await context_service.get_quota_info(context.id)
        if not quota_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Informações de quota não encontradas"
            )

        return quota_info

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao obter quota: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno ao obter quota"
        )


@router.post("/quota/check", response_model=MediaQuotaCheckResponse)
async def check_quota(
    context_type: MediaContextType = Form(...),
    context_id: Optional[UUID] = Form(None),
    file_size_mb: int = Form(..., ge=0),
    current_user: User = Depends(get_current_user),
    context_service: MediaContextService = Depends(get_media_context_service)
):
    """
    Verifica se um arquivo pode ser enviado considerando a quota.
    
    Args:
        context_type: Tipo de contexto
        context_id: ID do contexto
        file_size_mb: Tamanho do arquivo em MB
        current_user: Usuário atual
        context_service: Serviço de contexto
        
    Returns:
        MediaQuotaCheckResponse: Resultado da verificação
    """
    try:
        # Valida contexto
        if context_type == MediaContextType.TENANT and not context_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="context_id é obrigatório para contexto TENANT"
            )

        # Para contexto USER, usa o ID do usuário atual
        if context_type == MediaContextType.USER:
            context_id = current_user.id

        # Obtém contexto
        context = await context_service.get_or_create_context(context_type, context_id, current_user.id)

        # Verifica quota
        check_result = await context_service.check_quota(context.id, file_size_mb)

        return MediaQuotaCheckResponse(**check_result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao verificar quota: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno ao verificar quota"
        )


@router.get("/menu-items/{menu_item_id}/media")
async def get_menu_item_media(
    menu_item_id: UUID,
    # current_user: User = Depends(get_current_user),  # Temporariamente desabilitado
    upload_service: MediaUploadService = Depends(get_media_upload_service)
):
    """
    Lista mídia de um item do menu com informações de is_primary e display_order.

    Args:
        menu_item_id: ID do item do menu
        current_user: Usuário atual
        upload_service: Serviço de upload

    Returns:
        List: Lista de mídias do item com informações completas
    """
    try:
        # TODO: Verificar permissões para acessar o item do menu

        # Buscar mídia do item do menu
        media_list = await upload_service.get_menu_item_media(menu_item_id)

        return media_list

    except Exception as e:
        logger.error(f"Erro ao buscar mídia do item do menu: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno ao buscar mídia do item do menu"
        )


# Schemas for new endpoints
class MediaOrderItem(BaseModel):
    id: str
    display_order: int

class MediaOrderRequest(BaseModel):
    media_order: List[MediaOrderItem]

class SetPrimaryMediaRequest(BaseModel):
    upload_id: str


@router.put("/menu-items/{menu_item_id}/order")
async def update_media_order(
    menu_item_id: UUID,
    request: MediaOrderRequest,
    current_user: User = Depends(get_current_user),
    upload_service: MediaUploadService = Depends(get_media_upload_service)
):
    """
    Atualiza a ordem das mídias de um item do menu.

    Args:
        menu_item_id: ID do item do menu
        request: Nova ordem das mídias
        current_user: Usuário atual
        upload_service: Serviço de upload

    Returns:
        dict: Resultado da operação
    """
    try:
        # TODO: Verificar permissões para editar o item do menu

        # Atualiza ordem das mídias
        for item in request.media_order:
            await upload_service.update_display_order(
                upload_id=UUID(item.id),
                display_order=item.display_order
            )

        return {"message": "Ordem das mídias atualizada com sucesso"}

    except Exception as e:
        logger.error(f"Erro ao atualizar ordem das mídias: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno ao atualizar ordem das mídias"
        )


@router.put("/menu-items/{menu_item_id}/primary")
async def set_primary_media(
    menu_item_id: UUID,
    request: SetPrimaryMediaRequest,
    current_user: User = Depends(get_current_user),
    upload_service: MediaUploadService = Depends(get_media_upload_service)
):
    """
    Define a mídia primária de um item do menu.

    Args:
        menu_item_id: ID do item do menu
        request: ID da mídia a ser definida como primária
        current_user: Usuário atual
        upload_service: Serviço de upload

    Returns:
        dict: Resultado da operação
    """
    try:
        logger.info(f"🎯 API: Setting primary media for menu_item_id={menu_item_id}, upload_id={request.upload_id}")

        # TODO: Verificar permissões para editar o item do menu

        # Define mídia primária
        await upload_service.set_primary_media(
            menu_item_id=menu_item_id,
            upload_id=UUID(request.upload_id)
        )

        logger.info(f"✅ API: Successfully set primary media for menu_item_id={menu_item_id}")
        return {"message": "Mídia primária definida com sucesso"}

    except Exception as e:
        logger.error(f"Erro ao definir mídia primária: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno ao definir mídia primária"
        )


class LinkUploadRequest(BaseModel):
    upload_id: str


@router.post("/menu-items/{menu_item_id}/link")
async def link_upload_to_menu_item(
    menu_item_id: UUID,
    request: LinkUploadRequest,
    current_user: User = Depends(get_current_user),
    upload_service: MediaUploadService = Depends(get_media_upload_service)
):
    """
    Vincula um upload temporário a um item do menu.

    Args:
        menu_item_id: ID do item do menu
        request: ID do upload a ser vinculado
        current_user: Usuário atual
        upload_service: Serviço de upload

    Returns:
        dict: Resultado da operação
    """
    try:
        # TODO: Verificar permissões para editar o item do menu

        # Criar associação com menu item
        await upload_service.create_menu_item_association(
            menu_item_id=menu_item_id,
            upload_id=UUID(request.upload_id)
        )

        logger.info(f"Linked upload {request.upload_id} to menu item {menu_item_id}")

        return {"message": "Upload vinculado ao item com sucesso"}

    except Exception as e:
        logger.error(f"Erro ao vincular upload ao item: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno ao vincular upload ao item"
        )


@router.get("/cleanup/preview")
async def preview_cleanup(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Visualiza quais uploads seriam removidos na limpeza automática.

    Args:
        current_user: Usuário atual
        db: Sessão do banco de dados

    Returns:
        dict: Lista de uploads candidatos à remoção
    """
    try:
        # TODO: Verificar se usuário tem permissão de admin

        cleanup_service = MediaCleanupService(db)
        candidates = await cleanup_service.get_cleanup_candidates()

        total_size_mb = sum(c["file_size_mb"] for c in candidates)

        return {
            "candidates": candidates,
            "total_count": len(candidates),
            "total_size_mb": round(total_size_mb, 2),
            "threshold_days": cleanup_service.cleanup_threshold_days
        }

    except Exception as e:
        logger.error(f"Erro ao visualizar limpeza: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno ao visualizar limpeza"
        )


@router.post("/cleanup/run")
async def run_cleanup(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Executa limpeza automática de uploads não vinculados.

    Args:
        current_user: Usuário atual
        db: Sessão do banco de dados

    Returns:
        dict: Estatísticas da limpeza executada
    """
    try:
        # TODO: Verificar se usuário tem permissão de admin

        cleanup_service = MediaCleanupService(db)
        stats = await cleanup_service.cleanup_unlinked_media()

        return {
            "message": "Limpeza executada com sucesso",
            "stats": stats
        }

    except Exception as e:
        logger.error(f"Erro ao executar limpeza: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno ao executar limpeza"
        )


@router.post("/menu-items/{menu_item_id}/upload", response_model=MediaUploadResponse)
async def upload_menu_item_media(
    menu_item_id: UUID,
    file: UploadFile = File(...),
    x_tenant_id: str = Header(..., alias="X-Tenant-ID"),
    current_user: User = Depends(get_current_user),
    upload_service: MediaUploadService = Depends(get_media_upload_service),
    context_service: MediaContextService = Depends(get_media_context_service)
):
    """
    Faz upload de mídia para um item do menu e cria a associação automaticamente.

    Args:
        menu_item_id: ID do item do menu
        file: Arquivo para upload
        current_user: Usuário atual
        upload_service: Serviço de upload
        context_service: Serviço de contexto

    Returns:
        MediaUploadResponse: Resposta do upload
    """
    try:
        # TODO: Verificar permissões para editar o item do menu

        # Usar contexto TENANT (menu items pertencem a tenants)
        context_type = MediaContextType.TENANT
        try:
            context_id = UUID(x_tenant_id)  # Converter string para UUID
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid tenant ID format"
            )

        # Obtém ou cria contexto
        context = await context_service.get_or_create_context(context_type, context_id, current_user.id)

        # Faz upload
        result = await upload_service.upload_file(
            file=file,
            context=context,
            uploaded_by=current_user.id,
            directory_id=None
        )

        if result.success and result.upload_id:
            # Criar associação com menu item
            await upload_service.create_menu_item_association(
                menu_item_id=menu_item_id,
                upload_id=result.upload_id
            )

            logger.info(f"Created menu item media association: {menu_item_id} -> {result.upload_id}")

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro no upload de mídia do menu item: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno no upload de mídia do menu item"
        )
