# General - Cart

**Categoria:** General
**Módulo:** Cart
**Total de Endpoints:** 6
**Gerado em:** 27/06/2025, 20:20:26

## 📋 Endpoints

- [GET /api/cart/](#get-apicart) - Get Cart
- [POST /api/cart/items](#post-apicartitems) - Add Item To Cart
- [DELETE /api/cart/items/{cart_item_id}](#delete-apicartitemscart-item-id) - Remove Cart Item
- [PUT /api/cart/items/{cart_item_id}](#put-apicartitemscart-item-id) - Update Cart Item
- [GET /api/cart/summary](#get-apicartsummary) - Get Cart Summary
- [DELETE /api/cart/{cart_id}/clear](#delete-apicartcart-idclear) - Clear Cart

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### AddToCartRequest

**Descrição:** Schema para adicionar item ao carrinho.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `product_id` | unknown | ❌ | ID do produto EShop |
| `menu_item_id` | unknown | ❌ | ID do item de menu |
| `quantity` | integer | ❌ | Quantidade |
| `selected_variants` | unknown | ❌ | Variações selecionadas |
| `selected_modifiers` | unknown | ❌ | Modificadores selecionados |
| `special_instructions` | unknown | ❌ | Instruções especiais |

### CartRead

**Descrição:** Schema para leitura de carrinho.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `market_context` | string | ❌ | Contexto do mercado (b2b/b2c) |
| `notes` | unknown | ❌ | Notas do carrinho |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `user_id` | unknown | ✅ | - |
| `session_id` | unknown | ✅ | - |
| `status` | CartStatus | ✅ | - |
| `subtotal` | string | ✅ | - |
| `tax_amount` | string | ✅ | - |
| `discount_amount` | string | ✅ | - |
| `shipping_amount` | string | ✅ | - |
| `total_amount` | string | ✅ | - |
| `currency` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `expires_at` | unknown | ✅ | - |
| `converted_at` | unknown | ✅ | - |

### CartResponse

**Descrição:** Schema de resposta padrão para operações de carrinho.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `success` | boolean | ✅ | Sucesso da operação |
| `message` | string | ✅ | Mensagem de retorno |
| `cart` | unknown | ❌ | Dados do carrinho |

### CartSummary

**Descrição:** Schema para resumo do carrinho.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `id` | string | ✅ | - |
| `item_count` | integer | ✅ | - |
| `total_amount` | string | ✅ | - |
| `currency` | string | ✅ | - |
| `status` | CartStatus | ✅ | - |
| `market_context` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### UpdateCartItemRequest

**Descrição:** Schema para atualizar item do carrinho.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `quantity` | integer | ✅ | Nova quantidade |
| `selected_variants` | unknown | ❌ | Variações atualizadas |
| `selected_modifiers` | unknown | ❌ | Modificadores atualizados |
| `special_instructions` | unknown | ❌ | Instruções especiais |

## 🔗 Endpoints Detalhados

### GET /api/cart/ {#get-apicart}

**Resumo:** Get Cart
**Descrição:** Obtém o carrinho atual do usuário ou sessão.

Para usuários logados, usa o user_id.
Para usuários anônimos, usa o session_id do header.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `market_context` | string | query | ❌ | Contexto do mercado |
| `X-Session-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CartRead](#cartread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/cart/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/cart/items {#post-apicartitems}

**Resumo:** Add Item To Cart
**Descrição:** Adiciona item ao carrinho.

Se o item já existir com as mesmas customizações,
incrementa a quantidade.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `market_context` | string | query | ❌ | Contexto do mercado |
| `X-Session-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [AddToCartRequest](#addtocartrequest)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CartResponse](#cartresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/cart/items" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/cart/items/{cart_item_id} {#delete-apicartitemscart-item-id}

**Resumo:** Remove Cart Item
**Descrição:** Remove item do carrinho.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `cart_item_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CartResponse](#cartresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/cart/items/{cart_item_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/cart/items/{cart_item_id} {#put-apicartitemscart-item-id}

**Resumo:** Update Cart Item
**Descrição:** Atualiza item do carrinho.

Permite alterar quantidade, variações, modificadores
e instruções especiais.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `cart_item_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [UpdateCartItemRequest](#updatecartitemrequest)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CartResponse](#cartresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/cart/items/{cart_item_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/cart/summary {#get-apicartsummary}

**Resumo:** Get Cart Summary
**Descrição:** Obtém resumo do carrinho (para exibição em header/menu).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `market_context` | string | query | ❌ | Contexto do mercado |
| `X-Session-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CartSummary](#cartsummary)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/cart/summary" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### DELETE /api/cart/{cart_id}/clear {#delete-apicartcart-idclear}

**Resumo:** Clear Cart
**Descrição:** Limpa todos os itens do carrinho.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `cart_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CartResponse](#cartresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/cart/{cart_id}/clear" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
