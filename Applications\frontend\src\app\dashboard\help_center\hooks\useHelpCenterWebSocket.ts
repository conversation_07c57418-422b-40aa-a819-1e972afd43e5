'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { useAuth } from '@/lib/auth/AuthProvider';
import { useTenant } from '@/lib/tenant/TenantProvider';

interface TicketMessage {
  id: string;
  ticket_id: string;
  sender_id: string;
  message_content: string;
  message_type: 'text' | 'image' | 'file';
  file_path?: string;
  file_name?: string;
  file_size?: number;
  mime_type?: string;
  is_read: boolean;
  created_at: string;
  sender_name?: string;
  sender_email?: string;
  is_admin?: boolean;
}

interface TicketUpdate {
  id: string;
  status: string;
  priority?: string;
  assigned_admin_name?: string;
  updated_at: string;
}

interface TypingIndicator {
  ticket_id: string;
  user_id: string;
  user_name: string;
  is_typing: boolean;
}

interface ReadReceipt {
  ticket_id: string;
  message_id: string;
  user_id: string;
  read_at: string;
}

interface UseHelpCenterWebSocketProps {
  ticketId?: string;
  onNewMessage?: (message: TicketMessage) => void;
  onTicketUpdate?: (ticket: TicketUpdate) => void;
  onTypingUpdate?: (typing: TypingIndicator) => void;
  onReadReceipt?: (receipt: ReadReceipt) => void;
}

export function useHelpCenterWebSocket({
  ticketId,
  onNewMessage,
  onTicketUpdate,
  onTypingUpdate,
  onReadReceipt
}: UseHelpCenterWebSocketProps) {
  const { user } = useAuth();
  const { currentTenant } = useTenant();
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  const connect = useCallback(() => {
    if (!user || wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    try {
      const wsUrl = process.env.NODE_ENV === 'production'
        ? `wss://${window.location.host}/ws/help-center`
        : `ws://localhost:8000/ws/help-center`;

      const params = new URLSearchParams({
        user_id: user.id,
        ...(currentTenant && { tenant_id: currentTenant.id }),
        ...(ticketId && { ticket_id: ticketId })
      });

      wsRef.current = new WebSocket(`${wsUrl}?${params.toString()}`);

      wsRef.current.onopen = () => {
        console.log('Help Center WebSocket connected');
        setIsConnected(true);
        setConnectionError(null);
        reconnectAttempts.current = 0;
      };

      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          
          switch (data.type) {
            case 'new_message':
              onNewMessage?.(data.message);
              break;
            case 'ticket_updated':
              onTicketUpdate?.(data.ticket);
              break;
            case 'typing_indicator':
              onTypingUpdate?.(data.typing);
              break;
            case 'read_receipt':
              onReadReceipt?.(data.receipt);
              break;
            case 'error':
              console.error('WebSocket error:', data.message);
              setConnectionError(data.message);
              break;
            default:
              console.log('Unknown WebSocket message type:', data.type);
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      wsRef.current.onclose = (event) => {
        console.log('Help Center WebSocket disconnected:', event.code, event.reason);
        setIsConnected(false);
        
        // Attempt to reconnect if not a normal closure
        if (event.code !== 1000 && reconnectAttempts.current < maxReconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000);
          reconnectAttempts.current++;
          
          console.log(`Attempting to reconnect in ${delay}ms (attempt ${reconnectAttempts.current})`);
          
          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, delay);
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('Help Center WebSocket error:', error);
        setConnectionError('Connection error occurred');
      };

    } catch (error) {
      console.error('Error creating WebSocket connection:', error);
      setConnectionError('Failed to create connection');
    }
  }, [user, currentTenant, ticketId, onNewMessage, onTicketUpdate, onTypingUpdate, onReadReceipt]);

  const disconnect = () => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close(1000, 'Component unmounting');
      wsRef.current = null;
    }

    setIsConnected(false);
    setConnectionError(null);
  };

  const sendMessage = (type: string, data: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({ type, ...data }));
    } else {
      console.warn('WebSocket not connected, cannot send message');
    }
  };

  const sendTypingIndicator = (isTyping: boolean) => {
    if (ticketId) {
      sendMessage('typing_indicator', {
        ticket_id: ticketId,
        is_typing: isTyping
      });
    }
  };

  const markMessageAsRead = (messageId: string) => {
    if (ticketId) {
      sendMessage('mark_read', {
        ticket_id: ticketId,
        message_id: messageId
      });
    }
  };

  const joinTicket = useCallback((newTicketId: string) => {
    sendMessage('join_ticket', {
      ticket_id: newTicketId
    });
  }, []);

  const leaveTicket = useCallback((oldTicketId: string) => {
    sendMessage('leave_ticket', {
      ticket_id: oldTicketId
    });
  }, []);

  useEffect(() => {
    if (user) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [user, currentTenant, connect]);

  useEffect(() => {
    if (ticketId && isConnected) {
      joinTicket(ticketId);
    }

    return () => {
      if (ticketId && isConnected) {
        leaveTicket(ticketId);
      }
    };
  }, [ticketId, isConnected, joinTicket, leaveTicket]);

  return {
    isConnected,
    connectionError,
    sendTypingIndicator,
    markMessageAsRead,
    joinTicket,
    leaveTicket,
    reconnect: connect
  };
}
