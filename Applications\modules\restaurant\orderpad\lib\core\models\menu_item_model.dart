import 'package:hive/hive.dart';

part 'menu_item_model.g.dart';

@HiveType(typeId: 2)
class MenuItemModel extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String name;

  @HiveField(2)
  String description;

  @HiveField(3)
  double price;

  @HiveField(4)
  String category;

  @HiveField(5)
  String? imageUrl;

  @HiveField(6)
  bool isAvailable;

  @HiveField(7)
  List<String>? allergens;

  @HiveField(8)
  int preparationTime; // in minutes

  @HiveField(9)
  List<MenuModifier>? modifiers;

  @HiveField(10)
  DateTime createdAt;

  @HiveField(11)
  DateTime? updatedAt;

  MenuItemModel({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.category,
    this.imageUrl,
    this.isAvailable = true,
    this.allergens,
    this.preparationTime = 15,
    this.modifiers,
    required this.createdAt,
    this.updatedAt,
  });

  factory MenuItemModel.fromJson(Map<String, dynamic> json) {
    return MenuItemModel(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      price: json['price'].toDouble(),
      category: json['category'],
      imageUrl: json['imageUrl'],
      isAvailable: json['isAvailable'] ?? true,
      allergens: json['allergens'] != null 
          ? List<String>.from(json['allergens']) 
          : null,
      preparationTime: json['preparationTime'] ?? 15,
      modifiers: json['modifiers'] != null
          ? (json['modifiers'] as List)
              .map((modifier) => MenuModifier.fromJson(modifier))
              .toList()
          : null,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'category': category,
      'imageUrl': imageUrl,
      'isAvailable': isAvailable,
      'allergens': allergens,
      'preparationTime': preparationTime,
      'modifiers': modifiers?.map((modifier) => modifier.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }
}

@HiveType(typeId: 3)
class MenuModifier extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String name;

  @HiveField(2)
  double additionalPrice;

  @HiveField(3)
  bool isRequired;

  @HiveField(4)
  List<String> options;

  MenuModifier({
    required this.id,
    required this.name,
    this.additionalPrice = 0.0,
    this.isRequired = false,
    required this.options,
  });

  factory MenuModifier.fromJson(Map<String, dynamic> json) {
    return MenuModifier(
      id: json['id'],
      name: json['name'],
      additionalPrice: json['additionalPrice']?.toDouble() ?? 0.0,
      isRequired: json['isRequired'] ?? false,
      options: List<String>.from(json['options']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'additionalPrice': additionalPrice,
      'isRequired': isRequired,
      'options': options,
    };
  }
}