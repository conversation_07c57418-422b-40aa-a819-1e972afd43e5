import uuid
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update as sqlalchemy_update, delete as sqlalchemy_delete

from app.modules.core.functions.pos.models.cash_register import CashRegister
from app.modules.core.functions.pos.schemas.cash_register import (
    CashRegisterCreate,
    CashRegisterUpdate,
)


class CashRegisterService:
    async def get(
        self, db: AsyncSession, id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Optional[CashRegister]:
        """
        Obtém um caixa pelo ID, garantindo que pertença ao tenant especificado.
        """
        result = await db.execute(
            select(CashRegister).where(CashRegister.id == id, CashRegister.tenant_id == tenant_id)
        )
        return result.scalars().first()

    async def get_multi(
        self, db: AsyncSession, *, tenant_id: uuid.UUID, skip: int = 0, limit: int = 100
    ) -> List[CashRegister]:
        """
        Obtém uma lista de caixas para um tenant específico.
        """
        result = await db.execute(
            select(CashRegister)
            .where(CashRegister.tenant_id == tenant_id)
            .offset(skip)
            .limit(limit)
            .order_by(CashRegister.name)  # Ou outra ordenação desejada
        )
        return result.scalars().all()

    async def create(
        self, db: AsyncSession, *, obj_in: CashRegisterCreate, tenant_id: uuid.UUID
    ) -> CashRegister:
        """
        Cria um novo caixa associado ao tenant especificado.
        """
        db_obj = CashRegister(**obj_in.model_dump(), tenant_id=tenant_id)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def update(
        self, db: AsyncSession, *, id: uuid.UUID, obj_in: CashRegisterUpdate, tenant_id: uuid.UUID
    ) -> Optional[CashRegister]:
        """
        Atualiza um caixa existente, garantindo que pertença ao tenant especificado.
        Retorna o objeto atualizado ou None se não encontrado/não pertencer ao tenant.
        """
        update_data = obj_in.model_dump(exclude_unset=True)
        if not update_data:
            # Se não há dados para atualizar, busca e retorna o objeto existente
            return await self.get(db=db, id=id, tenant_id=tenant_id)

        # Verifica se o registro existe e pertence ao tenant antes de atualizar
        db_obj = await self.get(db=db, id=id, tenant_id=tenant_id)
        if not db_obj:
            return None

        # Executa a atualização
        await db.execute(
            sqlalchemy_update(CashRegister)
            .where(CashRegister.id == id, CashRegister.tenant_id == tenant_id)
            .values(**update_data)
        )
        await db.commit()
        # Recarrega o objeto para retornar os dados atualizados
        await db.refresh(db_obj)
        return db_obj

    async def remove(
        self, db: AsyncSession, *, id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Optional[CashRegister]:
        """
        Remove um caixa pelo ID, garantindo que pertença ao tenant especificado.
        Retorna o objeto removido ou None se não encontrado/não pertencer ao tenant.
        """
        # Verifica se o registro existe e pertence ao tenant antes de remover
        db_obj = await self.get(db=db, id=id, tenant_id=tenant_id)
        if not db_obj:
            return None

        await db.execute(
            sqlalchemy_delete(CashRegister).where(
                CashRegister.id == id, CashRegister.tenant_id == tenant_id
            )
        )
        await db.commit()
        # O objeto db_obj ainda contém os dados antes da exclusão,
        # pode ser útil retornar isso ou apenas um status de sucesso.
        return db_obj


# Instância do serviço para ser usada nos endpoints
cash_register_service = CashRegisterService()
