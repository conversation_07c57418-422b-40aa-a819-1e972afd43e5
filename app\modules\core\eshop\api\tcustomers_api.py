"""EShop TCustomers Management API.

Provides REST endpoints for managing TCustomers (B2B clients),
including listing, filtering, status management, and detailed information.
"""

import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from pydantic import BaseModel, Field

from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.auth_dependencies import get_current_active_user
from app.modules.core.users.models.user import User
from app.modules.core.users.models.tenant_user_association import TenantUserAssociation
from app.modules.core.eshop.services.role_service import RoleManagementService
from app.modules.core.roles.models.roles import TenantRole
from app.core.exceptions import ValidationError, NotFoundError, AuthorizationError

router = APIRouter(prefix="/api/v1/eshop/tcustomers", tags=["EShop TCustomers Management"])


# =====================================================================
# PYDANTIC SCHEMAS
# =====================================================================

class TCustomerBase(BaseModel):
    """Base schema for TCustomer data."""
    
    name: str = Field(..., description="Customer name")
    email: str = Field(..., description="Customer email")
    company_name: str = Field(..., description="Company name")
    phone: Optional[str] = Field(None, description="Phone number")
    tax_id: Optional[str] = Field(None, description="Tax ID/CNPJ")
    address: Optional[str] = Field(None, description="Business address")


class TCustomerResponse(TCustomerBase):
    """Response schema for TCustomer data."""
    
    id: uuid.UUID
    customer_type: str = Field(..., description="Customer type (tcostumer or business_client)")
    status: str = Field(..., description="Customer status")
    verification_status: str = Field(..., description="Verification status")
    credit_limit: float = Field(..., description="Credit limit")
    total_purchases: float = Field(..., description="Total purchases amount")
    orders_count: int = Field(..., description="Number of orders")
    rating: float = Field(..., description="Customer rating")
    payment_terms: int = Field(..., description="Payment terms in days")
    discount_rate: float = Field(..., description="Discount rate percentage")
    joined_date: datetime = Field(..., description="Date joined")
    last_order: Optional[datetime] = Field(None, description="Last order date")
    
    class Config:
        from_attributes = True


class TCustomerListResponse(BaseModel):
    """Response schema for TCustomer list."""
    
    customers: List[TCustomerResponse]
    total_count: int
    stats: Dict[str, Any]


class TCustomerStatusUpdate(BaseModel):
    """Schema for updating TCustomer status."""
    
    status: str = Field(..., description="New status (active, suspended, inactive)")
    reason: Optional[str] = Field(None, description="Reason for status change")


class TCustomerStatsResponse(BaseModel):
    """Response schema for TCustomer statistics."""
    
    active_count: int
    pending_count: int
    suspended_count: int
    inactive_count: int
    total_revenue: float
    average_order_value: float
    total_orders: int


# =====================================================================
# API ENDPOINTS
# =====================================================================

@router.get("/list", response_model=TCustomerListResponse)
async def list_tcustomers(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    status: Optional[str] = Query(None, description="Filter by status"),
    customer_type: Optional[str] = Query(None, description="Filter by customer type"),
    search: Optional[str] = Query(None, description="Search term"),
    limit: int = Query(50, le=100, description="Number of results to return"),
    offset: int = Query(0, description="Number of results to skip")
):
    """List TCustomers with filtering and pagination."""
    
    try:
        # Build query for TCustomers (users with tcostumer role)
        query = db.query(
            TenantUserAssociation,
            User
        ).join(
            User, TenantUserAssociation.user_id == User.id
        ).filter(
            TenantUserAssociation.role == TenantRole.TCOSTUMER
        )
        
        # Apply filters
        if status:
            # Map frontend status to backend status logic
            if status == "active":
                query = query.filter(TenantUserAssociation.is_authorized_for_b2b == True)
            elif status == "pending":
                query = query.filter(TenantUserAssociation.is_authorized_for_b2b == False)
        
        if customer_type:
            # For now, all tcostumers are of type 'tcostumer'
            # This can be extended with additional fields if needed
            pass
        
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    User.full_name.ilike(search_term),
                    User.email.ilike(search_term),
                    TenantUserAssociation.business_name.ilike(search_term)
                )
            )
        
        # Get total count
        total_count = query.count()
        
        # Apply pagination
        results = query.offset(offset).limit(limit).all()
        
        # Transform results to response format
        customers = []
        for association, user in results:
            customer = TCustomerResponse(
                id=association.id,
                name=user.full_name or user.email,
                email=user.email,
                company_name=association.business_name or "N/A",
                phone=user.phone,
                tax_id=association.business_registration_number,
                address="N/A",  # Add address field to model if needed
                customer_type="tcostumer",
                status="active" if association.is_authorized_for_b2b else "pending",
                verification_status="verified" if association.business_verification_status == "approved" else "pending",
                credit_limit=float(association.pricing_tier_value or 10000),  # Default credit limit
                total_purchases=0.0,  # Calculate from orders if needed
                orders_count=0,  # Calculate from orders if needed
                rating=4.5,  # Default rating
                payment_terms=30,  # Default payment terms
                discount_rate=float(association.pricing_tier_value or 5),  # Default discount
                joined_date=association.created_at,
                last_order=None  # Calculate from orders if needed
            )
            customers.append(customer)
        
        # Calculate stats
        stats = {
            "active": len([c for c in customers if c.status == "active"]),
            "pending": len([c for c in customers if c.status == "pending"]),
            "suspended": len([c for c in customers if c.status == "suspended"]),
            "inactive": len([c for c in customers if c.status == "inactive"]),
            "total_revenue": sum(c.total_purchases for c in customers)
        }
        
        return TCustomerListResponse(
            customers=customers,
            total_count=total_count,
            stats=stats
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching TCustomers: {str(e)}"
        )


@router.get("/stats", response_model=TCustomerStatsResponse)
async def get_tcustomer_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get TCustomer statistics."""
    
    try:
        # Get all TCustomer associations
        associations = db.query(TenantUserAssociation).filter(
            TenantUserAssociation.role == TenantRole.TCOSTUMER
        ).all()
        
        active_count = len([a for a in associations if a.is_authorized_for_b2b])
        pending_count = len([a for a in associations if not a.is_authorized_for_b2b])
        
        return TCustomerStatsResponse(
            active_count=active_count,
            pending_count=pending_count,
            suspended_count=0,  # Implement suspension logic if needed
            inactive_count=0,
            total_revenue=0.0,  # Calculate from orders
            average_order_value=0.0,  # Calculate from orders
            total_orders=0  # Calculate from orders
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching TCustomer stats: {str(e)}"
        )


@router.get("/{customer_id}", response_model=TCustomerResponse)
async def get_tcustomer(
    customer_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get detailed information about a specific TCustomer."""
    
    try:
        # Get TCustomer association and user data
        result = db.query(
            TenantUserAssociation,
            User
        ).join(
            User, TenantUserAssociation.user_id == User.id
        ).filter(
            TenantUserAssociation.id == customer_id,
            TenantUserAssociation.role == TenantRole.TCOSTUMER
        ).first()
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="TCustomer not found"
            )
        
        association, user = result
        
        customer = TCustomerResponse(
            id=association.id,
            name=user.full_name or user.email,
            email=user.email,
            company_name=association.business_name or "N/A",
            phone=user.phone,
            tax_id=association.business_registration_number,
            address="N/A",
            customer_type="tcostumer",
            status="active" if association.is_authorized_for_b2b else "pending",
            verification_status="verified" if association.business_verification_status == "approved" else "pending",
            credit_limit=float(association.pricing_tier_value or 10000),
            total_purchases=0.0,
            orders_count=0,
            rating=4.5,
            payment_terms=30,
            discount_rate=float(association.pricing_tier_value or 5),
            joined_date=association.created_at,
            last_order=None
        )
        
        return customer
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching TCustomer: {str(e)}"
        )


@router.patch("/{customer_id}/status", response_model=Dict[str, str])
async def update_tcustomer_status(
    customer_id: uuid.UUID,
    status_update: TCustomerStatusUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Update TCustomer status (activate, suspend, etc.)."""
    
    try:
        # Get TCustomer association
        association = db.query(TenantUserAssociation).filter(
            TenantUserAssociation.id == customer_id,
            TenantUserAssociation.role == TenantRole.TCOSTUMER
        ).first()
        
        if not association:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="TCustomer not found"
            )
        
        # Update status based on the request
        if status_update.status == "active":
            association.is_authorized_for_b2b = True
            association.business_verification_status = "approved"
        elif status_update.status == "suspended":
            association.is_authorized_for_b2b = False
            association.business_verification_status = "suspended"
        elif status_update.status == "pending":
            association.is_authorized_for_b2b = False
            association.business_verification_status = "pending"
        
        # Add status change reason if provided
        if status_update.reason:
            association.approval_notes = status_update.reason
        
        association.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(association)
        
        return {
            "message": f"TCustomer status updated to {status_update.status}",
            "status": status_update.status
        }
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating TCustomer status: {str(e)}"
        )


@router.post("/{customer_id}/activate", response_model=Dict[str, str])
async def activate_tcustomer(
    customer_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Activate a TCustomer for B2B access."""
    
    return await update_tcustomer_status(
        customer_id,
        TCustomerStatusUpdate(status="active", reason="Activated by admin"),
        db,
        current_user
    )


@router.post("/{customer_id}/suspend", response_model=Dict[str, str])
async def suspend_tcustomer(
    customer_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Suspend a TCustomer's B2B access."""
    
    return await update_tcustomer_status(
        customer_id,
        TCustomerStatusUpdate(status="suspended", reason="Suspended by admin"),
        db,
        current_user
    )