"""
Tenant dependencies for FastAPI.
"""

import logging
import uuid
from typing import Annotated, Optional

from fastapi import Depends, <PERSON>er, HTTPException, Path, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.tenants.models.tenant import Tenant
from app.modules.core.tenants.services.tenant_service import tenant_service
from app.modules.core.users.models.tenant_user_association import TenantUserAssociation
from app.modules.core.users.models.user import User
from app.modules.core.users.services.tenant_user_association_service import (
    tenant_user_association_service,
)
from app.modules.core.auth.dependencies.user_auth import (
    get_current_active_user,
    permission_denied_exception,
)
from app.modules.core.roles.models.roles import TenantRole, TenantStaffSubRole

logger = logging.getLogger(__name__)


async def get_current_tenant(
    db: Annotated[AsyncSession, Depends(get_db)],
    tenant_id: uuid.UUID,
) -> Tenant:
    """
    Get the current tenant by ID.

    Args:
        db: The async database session.
        tenant_id: The tenant ID.

    Returns:
        The Tenant object.

    Raises:
        HTTPException: If the tenant doesn't exist or is inactive.
    """
    tenant = await tenant_service.get_tenant(db, tenant_id=tenant_id)
    if not tenant or not tenant.is_active:
        logger.warning(f"Tenant with ID {tenant_id} not found or inactive.")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tenant not found or inactive.",
        )
    return tenant


async def get_current_tenant_from_header(
    db: Annotated[AsyncSession, Depends(get_db)],
    x_tenant_id: Annotated[str, Header()],
) -> Tenant:
    """
    Get the current tenant from the X-Tenant-ID header.

    Args:
        db: The async database session.
        x_tenant_id: The tenant ID from the X-Tenant-ID header.

    Returns:
        The Tenant object.

    Raises:
        HTTPException: If the tenant ID is invalid, the tenant doesn't exist, or is inactive.
    """
    try:
        tenant_id = uuid.UUID(x_tenant_id)
    except ValueError:
        logger.warning(f"Invalid tenant ID format in header: {x_tenant_id}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid tenant ID format.",
        )

    return await get_current_tenant(db, tenant_id=tenant_id)


async def get_current_tenant_by_slug_or_id(
    db: Annotated[AsyncSession, Depends(get_db)],
    tenant_id_or_slug: str = Path(..., description="Tenant ID or slug"),
) -> Tenant:
    """
    Get the current tenant by ID or slug.

    Args:
        db: The async database session.
        tenant_id_or_slug: The tenant ID or slug.

    Returns:
        The Tenant object.

    Raises:
        HTTPException: If the tenant doesn't exist or is inactive.
    """
    # Try to parse as UUID first
    try:
        tenant_id = uuid.UUID(tenant_id_or_slug)
        tenant = await tenant_service.get_tenant(db, tenant_id=tenant_id)
    except ValueError:
        # If not a valid UUID, try as slug
        tenant = await tenant_service.get_tenant_by_slug(db, slug=tenant_id_or_slug)

    if not tenant or not tenant.is_active:
        logger.warning(f"Tenant with ID/slug {tenant_id_or_slug} not found or inactive.")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tenant not found or inactive.",
        )

    return tenant


async def require_tenant_role(
    db: Annotated[AsyncSession, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)],
    required_role: TenantRole,
) -> TenantUserAssociation:
    """
    Dependency to check if the current user has the required tenant role.

    Args:
        db: The async database session.
        current_user: The User object from the get_current_active_user dependency.
        tenant: The Tenant object from the get_current_tenant_from_header dependency.
        required_role: The required tenant role.

    Returns:
        The TenantUserAssociation object if the user has the required role.

    Raises:
        HTTPException: If the user doesn't have the required role.
    """
    association = await tenant_user_association_service._get_association(
        db, user_id=current_user.id, tenant_id=tenant.id
    )

    if not association or association.role != required_role.value:
        logger.warning(
            f"require_tenant_role: User {current_user.id} with role '{association.role if association else None}' "
            f"does not have required role '{required_role.value}' for tenant {tenant.id}. Access denied."
        )
        raise permission_denied_exception

    logger.info(
        f"require_tenant_role: User {current_user.id} with role '{association.role}' "
        f"has required role '{required_role.value}' for tenant {tenant.id}. Access granted."
    )
    return association


async def require_tenant_sub_role(
    db: Annotated[AsyncSession, Depends(get_db)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)],
    required_sub_role: TenantStaffSubRole,
) -> TenantUserAssociation:
    """
    Dependency to check if the current user has the required tenant staff sub-role.

    Args:
        db: The async database session.
        current_user: The User object from the get_current_active_user dependency.
        tenant: The Tenant object from the get_current_tenant_from_header dependency.
        required_sub_role: The required tenant staff sub-role.

    Returns:
        The TenantUserAssociation object if the user has the required sub-role.

    Raises:
        HTTPException: If the user doesn't have the required sub-role.
    """
    association = await tenant_user_association_service._get_association(
        db, user_id=current_user.id, tenant_id=tenant.id
    )

    if (
        not association
        or association.role != TenantRole.STAFF.value
        or association.staff_sub_role != required_sub_role.value
    ):
        logger.warning(
            f"require_tenant_sub_role: User {current_user.id} with role '{association.role if association else None}' "
            f"and sub-role '{association.staff_sub_role if association else None}' "
            f"does not have required sub-role '{required_sub_role.value}' for tenant {tenant.id}. Access denied."
        )
        raise permission_denied_exception

    logger.info(
        f"require_tenant_sub_role: User {current_user.id} with role '{association.role}' "
        f"and sub-role '{association.staff_sub_role}' "
        f"has required sub-role '{required_sub_role.value}' for tenant {tenant.id}. Access granted."
    )
    return association
