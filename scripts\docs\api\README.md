# 📚 Sumário da Documentação da API - TrixModular

**Guia completo para navegar na documentação da API do TrixModular**

Este documento serve como **sumário central** para localizar rapidamente qualquer informação sobre a API.

**📅 Última atualização:** 27/06/2025, 20:20:27

## 🧭 Guia Rápido de Navegação

| 🎯 **Preciso de...** | 📁 **Onde encontrar** | 📄 **Arquivo específico** |
|---------------------|----------------------|---------------------------|
| Autenticação e Login | `core/` | [auth.md](core/auth.md) |
| Gestão de Cardápio | `restaurant/` | [menu_management.md](restaurant/menu_management.md) |
| Sistema de Cozinha (KDS) | `restaurant/` | [kitchen_display_system.md](restaurant/kitchen_display_system.md) |
| Ponto de Venda (POS) | `shared/` | [pos.md](shared/pos.md) |
| Gestão de Pedidos | `shared/` | [orders.md](shared/orders.md) |
| Controle de Estoque | `shared/` | [inventory.md](shared/inventory.md) |
| Gestão de Clientes (CRM) | `crm/` | [contacts.md](crm/contacts.md) |
| Recursos Humanos | `shared/` | [hr.md](shared/hr.md) |
| Sistema de Blog | `blog/` | [posts.md](blog/posts.md) |
| Gestão de Usuários | `core/` | [users.md](core/users.md) |
| Gestão de Tenants | `core/` | [tenants.md](core/tenants.md) |
| Sistema de Pagamentos | `core/` | [payments.md](core/payments.md) |
| Internacionalização | `i18n/` | [translations.md](i18n/translations.md) |
| Catálogo de Produtos | `shops/` | [catalog.md](shops/catalog.md) |
| Status do Sistema | `system/` | [health.md](system/health.md) |

## 🗂️ Organização por Funcionalidade

### 🏪 **Operações de Restaurante**
- **📁 `restaurant/`** - Funcionalidades específicas de restaurantes
  - 🍽️ [Gestão de Cardápio](restaurant/menu_management.md) - Criar, editar e organizar itens do menu
  - 👨‍🍳 [Sistema de Cozinha (KDS)](restaurant/kitchen_display_system.md) - Gerenciar pedidos na cozinha
  - 🪑 [Gestão de Mesas](restaurant/table_management.md) - Controlar mesas e ocupação
  - 📅 [Reservas](restaurant/table_reservation.md) - Sistema de reservas de mesas
  - 📱 [Menu Digital](restaurant/digital_menu_management.md) - Menu digital para clientes

### 💼 **Operações Comerciais**
- **📁 `shared/`** - Funcionalidades compartilhadas entre módulos
  - 💰 [Ponto de Venda (POS)](shared/pos.md) - Sistema de vendas e pagamentos
  - 📦 [Gestão de Pedidos](shared/orders.md) - Processar e acompanhar pedidos
  - 📊 [Controle de Estoque](shared/inventory.md) - Gerenciar produtos e estoque
  - 👥 [Recursos Humanos](shared/hr.md) - Gestão de funcionários
- **📁 `crm/`** - Gestão de Relacionamento com Cliente
  - 👤 [Gestão de Clientes](crm/contacts.md) - Cadastro e histórico de clientes
  - 🏢 [Contas Empresariais](crm/accounts.md) - Gestão de contas corporativas
  - 🎁 [Programa de Fidelidade](crm/loyalty.md) - Sistema de pontos e recompensas
  - 💬 [Interações](crm/interactions.md) - Histórico de comunicações

### ⚙️ **Administração do Sistema**
- **📁 `core/`** - Funcionalidades centrais do sistema
  - 🔐 [Autenticação](core/auth.md) - Login, logout e gestão de sessões
  - 👤 [Gestão de Usuários](core/users.md) - Criar e gerenciar usuários
  - 🏢 [Gestão de Tenants](core/tenants.md) - Configuração de estabelecimentos
  - 💳 [Sistema de Pagamentos](core/payments.md) - Processamento de pagamentos
  - 📝 [Sistema de Blog](core/blog.md) - Gestão de conteúdo
  - 📁 [Sistema FTP](core/ftp_system.md) - Gestão de arquivos

### 🌐 **Recursos Adicionais**
- **📁 `i18n/`** - Internacionalização
  - 🌍 [Gestão de Idiomas](i18n/languages.md) - Configurar idiomas disponíveis
  - 📝 [Traduções](i18n/translations.md) - Gerenciar textos traduzidos
  - 🔄 [Sugestões de Tradução](i18n/translation_suggestions.md) - Sistema colaborativo
- **📁 `shops/`** - E-commerce
  - 🛒 [Catálogo de Produtos](shops/catalog.md) - Gestão de produtos online
  - 🏪 [Loja Online](shops/online_store.md) - Configuração da loja virtual
- **📁 `blog/`** - Sistema de Blog
  - 📰 [Gestão de Posts](blog/posts.md) - Criar e editar artigos
  - 🏷️ [Categorias e Tags](blog/categories.md) - Organizar conteúdo
  - 💬 [Comentários](blog/comments.md) - Interação com leitores

## 🔐 Autenticação

A API utiliza autenticação JWT (JSON Web Token). Para acessar endpoints protegidos:

### Headers Obrigatórios
```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

### 👥 Usuários de Teste

| Tipo | Email | Senha | Descrição |
|------|-------|-------|-----------|
| Admin | `<EMAIL>` | `password` | Administrador do sistema |
| Tenant Owner | `<EMAIL>` | `password` | Proprietário de restaurante |
| Customer | `<EMAIL>` | `password` | Cliente final |

## 🎯 Cenários de Uso Comuns

### 🚀 **Primeiros Passos**
1. **Autenticação:** Comece com [core/auth.md](core/auth.md) para fazer login
2. **Configurar Tenant:** Use [core/tenants.md](core/tenants.md) para configurar seu restaurante
3. **Criar Usuários:** Adicione funcionários via [core/users.md](core/users.md)

### 🍽️ **Configurar Restaurante**
1. **Criar Cardápio:** Use [restaurant/menu_management.md](restaurant/menu_management.md)
2. **Configurar Mesas:** Configure layout via [restaurant/table_management.md](restaurant/table_management.md)
3. **Ativar KDS:** Configure cozinha via [restaurant/kitchen_display_system.md](restaurant/kitchen_display_system.md)
4. **Configurar POS:** Ative vendas via [shared/pos.md](shared/pos.md)

### 📊 **Operações Diárias**
1. **Processar Pedidos:** Use [shared/orders.md](shared/orders.md)
2. **Gerenciar Estoque:** Controle via [shared/inventory.md](shared/inventory.md)
3. **Atender Clientes:** Gerencie via [crm/contacts.md](crm/contacts.md)
4. **Acompanhar Vendas:** Monitore via [shared/pos.md](shared/pos.md)

## 📊 Estatísticas da API

- **Total de Endpoints:** 559
- **Total de Schemas:** 436
- **Total de Tags:** 122

### Distribuição por Método HTTP

- **DELETE:** 46 endpoints
- **GET:** 272 endpoints
- **PATCH:** 10 endpoints
- **POST:** 149 endpoints
- **PUT:** 82 endpoints

## 📋 Índice Completo por Categoria

### 📂 Admin

- **[Admin - Tenant View](admin/tenant_view.md)**

### 📂 B2B

- **[B2B - Invoices](b2b/invoices.md)**

### 📂 Blog

- **[Blog - Authors](blog/authors.md)**
- **[Blog - Categories](blog/categories.md)**
- **[Blog - Comments](blog/comments.md)**
- **[Blog - Posts](blog/posts.md)**
- **[Blog - Tags](blog/tags.md)**

### 📂 Core

- **[Core - Audit System](core/audit_system.md)**
- **[Core - Auth](core/auth.md)**
- **[Core - Blog](core/blog.md)**
- **[Core - Eshop Integration](core/eshop_integration.md)**
- **[Core - Eshop Role Management](core/eshop_role_management.md)**
- **[Core - Eshop Tcustomers](core/eshop_tcustomers.md)**
- **[Core - Help Center](core/help_center.md)**
- **[Core - Logging System](core/logging_system.md)**
- **[Core - Media System](core/media_system.md)**
- **[Core - Notifications](core/notifications.md)**
- **[Core - Payments](core/payments.md)**
- **[Core - Tenants](core/tenants.md)**
- **[Core - Users](core/users.md)**

### 📂 Core Functions

- **[Core Functions - Offerts](core_functions/offerts.md)**
- **[Core Functions - Reviews](core_functions/reviews.md)**
- **[Core Functions - Shipping](core_functions/shipping.md)**

### 📂 Crm

- **[Crm - Accounts](crm/accounts.md)**
- **[Crm - Contacts](crm/contacts.md)**
- **[Crm - Interactions](crm/interactions.md)**
- **[Crm - Loyalty](crm/loyalty.md)**
- **[Crm - Pricing](crm/pricing.md)**

### 📂 Eshop

- **[Eshop - Categories](eshop/categories.md)**
- **[Eshop - Modifiers](eshop/modifiers.md)**
- **[Eshop - Optionals](eshop/optionals.md)**
- **[Eshop - Products](eshop/products.md)**
- **[Eshop - Variants](eshop/variants.md)**

### 📂 General

- **[General - Allergens](general/allergens.md)**
- **[General - Audit](general/audit.md)**
- **[General - Blog](general/blog.md)**
- **[General - Cart](general/cart.md)**
- **[General - Categories](general/categories.md)**
- **[General - Checkout](general/checkout.md)**
- **[General - Coupons](general/coupons.md)**
- **[General - Custom Domains](general/custom_domains.md)**
- **[General - Domain Integration](general/domain_integration.md)**
- **[General - Domain Rent](general/domain_rent.md)**
- **[General - Eshop](general/eshop.md)**
- **[General - Eshop Integration](general/eshop_integration.md)**
- **[General - Eshop Role Management](general/eshop_role_management.md)**
- **[General - Eshop Tcustomers Management](general/eshop_tcustomers_management.md)**
- **[General - Financial](general/financial.md)**
- **[General - Financial Categories](general/financial_categories.md)**
- **[General - Financial Control](general/financial_control.md)**
- **[General - Financial Control Analytics](general/financial_control_analytics.md)**
- **[General - Help Center](general/help_center.md)**
- **[General - Invoices](general/invoices.md)**
- **[General - Media System](general/media_system.md)**
- **[General - Models](general/models.md)**
- **[General - Notification Events](general/notification_events.md)**
- **[General - Notification Metrics](general/notification_metrics.md)**
- **[General - Notification Queue](general/notification_queue.md)**
- **[General - Notifications](general/notifications.md)**
- **[General - Payment Methods](general/payment_methods.md)**
- **[General - Payment Processor Integration](general/payment_processor_integration.md)**
- **[General - Payment Processors](general/payment_processors.md)**
- **[General - Payment Transactions](general/payment_transactions.md)**
- **[General - Public Menu Api](general/public_menu_api.md)**
- **[General - Public Tenant Api](general/public_tenant_api.md)**
- **[General - Restaurant Operating Hours](general/restaurant_operating_hours.md)**
- **[General - Restaurant Settings](general/restaurant_settings.md)**
- **[General - Restaurant Special Calendar](general/restaurant_special_calendar.md)**
- **[General - Restaurant Zones](general/restaurant_zones.md)**
- **[General - Root](general/root.md)**
- **[General - Shipping](general/shipping.md)**
- **[General - Shopping List](general/shopping_list.md)**
- **[General - Shopping Lists](general/shopping_lists.md)**
- **[General - Supplier](general/supplier.md)**
- **[General - System Logs](general/system_logs.md)**
- **[General - Transactions](general/transactions.md)**
- **[General - Websocket](general/websocket.md)**

### 📂 I18N

- **[I18N - Languages](i18n/languages.md)**
- **[I18N - Translation Changes](i18n/translation_changes.md)**
- **[I18N - Translation Suggestions](i18n/translation_suggestions.md)**
- **[I18N - Translations](i18n/translations.md)**
- **[I18N - Trixlingua](i18n/trixlingua.md)**

### 📂 Restaurant

- **[Restaurant - Digital Menu Management](restaurant/digital_menu_management.md)**
- **[Restaurant - General](restaurant/general.md)**
- **[Restaurant - Kds Authentication](restaurant/kds_authentication.md)**
- **[Restaurant - Kitchen Display System](restaurant/kitchen_display_system.md)**
- **[Restaurant - Menu Groups](restaurant/menu_groups.md)**
- **[Restaurant - Menu Management](restaurant/menu_management.md)**
- **[Restaurant - Table Management](restaurant/table_management.md)**
- **[Restaurant - Table Reservation](restaurant/table_reservation.md)**
- **[Restaurant - Tenant Settings](restaurant/tenant_settings.md)**

### 📂 Shared

- **[Shared - Crm](shared/crm.md)**
- **[Shared - Hr](shared/hr.md)**
- **[Shared - Inventory](shared/inventory.md)**
- **[Shared - Orders](shared/orders.md)**
- **[Shared - Pos](shared/pos.md)**

### 📂 Shops

- **[Shops - Catalog](shops/catalog.md)**
- **[Shops - Catalog Management](shops/catalog_management.md)**
- **[Shops - Online Store](shops/online_store.md)**

### 📂 Subscriptions

- **[Subscriptions - Admin](subscriptions/admin.md)**
- **[Subscriptions - Tenant](subscriptions/tenant.md)**

### 📂 Supplier

- **[Supplier - Tenant](supplier/tenant.md)**

### 📂 System

- **[System - Health](system/health.md)**

### 📂 Tenant

- **[Tenant - Settings](tenant/settings.md)**
- **[Tenant - Settings Legacy](tenant/settings_legacy.md)**

### 📂 Tenant Settings

- **[Tenant Settings - Business](tenant_settings/business.md)**
- **[Tenant Settings - Categories](tenant_settings/categories.md)**
- **[Tenant Settings - Currency](tenant_settings/currency.md)**
- **[Tenant Settings - Hours](tenant_settings/hours.md)**
- **[Tenant Settings - Languages](tenant_settings/languages.md)**
- **[Tenant Settings - Location](tenant_settings/location.md)**
- **[Tenant Settings - Loyalty](tenant_settings/loyalty.md)**
- **[Tenant Settings - Main](tenant_settings/main.md)**
- **[Tenant Settings - Social Media](tenant_settings/social_media.md)**
- **[Tenant Settings - Tax](tenant_settings/tax.md)**

## 📖 Recursos Adicionais

### 📚 **Documentação Técnica**
- **[📄 Documentação Unificada](api_documentation.md)** - Todos os endpoints em um arquivo único
- **[⚙️ Especificação OpenAPI JSON](openapi.json)** - Especificação técnica para ferramentas
- **[📝 Especificação OpenAPI YAML](openapi.yaml)** - Especificação em formato legível

### 🛠️ **Ferramentas Recomendadas**
- **Postman/Insomnia:** Importe `openapi.json` para testar endpoints
- **Swagger UI:** Visualize a API usando `openapi.json`
- **VS Code:** Use extensões REST Client para testar diretamente

### 📞 **Suporte e Contato**
- **Documentação:** Este sumário é seu ponto de partida
- **Busca Rápida:** Use Ctrl+F para encontrar funcionalidades específicas
- **Estrutura:** Cada arquivo temático é independente e completo

---

## 📋 **Como Usar Este Sumário**

1. **🔍 Busca Rápida:** Use a tabela "Guia Rápido" para encontrar o que precisa
2. **📁 Navegação por Pasta:** Explore as seções organizadas por funcionalidade
3. **🎯 Cenários de Uso:** Siga os fluxos recomendados para tarefas comuns
4. **📋 Índice Completo:** Acesse todos os arquivos organizados por categoria

**🚀 TrixModular** - Sistema microSaaS para Restaurantes

*📅 Sumário atualizado automaticamente em 27/06/2025, 20:20:27*
