# General - Payment Transactions

**Categoria:** General
**Módulo:** Payment Transactions
**Total de Endpoints:** 6
**Gerado em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [PUT /api/modules/core/payments/refunds/{refund_id}](#put-apimodulescorepaymentsrefundsrefund-id) - Update Payment Refund
- [GET /api/modules/core/payments/transactions](#get-apimodulescorepaymentstransactions) - List Payment Transactions
- [POST /api/modules/core/payments/transactions](#post-apimodulescorepaymentstransactions) - Create Payment Transaction
- [GET /api/modules/core/payments/transactions/{transaction_id}](#get-apimodulescorepaymentstransactionstransaction-id) - Get Payment Transaction
- [PUT /api/modules/core/payments/transactions/{transaction_id}](#put-apimodulescorepaymentstransactionstransaction-id) - Update Payment Transaction
- [POST /api/modules/core/payments/transactions/{transaction_id}/refunds](#post-apimodulescorepaymentstransactionstransaction-idrefunds) - Create Payment Refund

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### PaymentRefundCreate

**Descrição:** Schema for creating a new payment refund.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `amount` | unknown | ✅ | - |
| `reason` | unknown | ❌ | - |
| `status` | PaymentStatus | ❌ | - |
| `external_id` | unknown | ❌ | - |
| `external_reference` | unknown | ❌ | - |
| `metadata` | unknown | ❌ | - |

### PaymentRefundRead

**Descrição:** Schema for reading a payment refund.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `amount` | string | ✅ | - |
| `reason` | unknown | ❌ | - |
| `status` | PaymentStatus | ❌ | - |
| `external_id` | unknown | ❌ | - |
| `external_reference` | unknown | ❌ | - |
| `metadata` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `transaction_id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `processed_at` | unknown | ❌ | - |
| `created_by` | unknown | ❌ | - |

### PaymentRefundUpdate

**Descrição:** Schema for updating an existing payment refund.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `status` | unknown | ❌ | - |
| `external_id` | unknown | ❌ | - |
| `external_reference` | unknown | ❌ | - |
| `processed_at` | unknown | ❌ | - |
| `metadata` | unknown | ❌ | - |

### PaymentTransactionCreate

**Descrição:** Schema for creating a new payment transaction.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `amount` | unknown | ✅ | - |
| `currency` | string | ❌ | - |
| `status` | PaymentStatus | ❌ | - |
| `source_type` | string | ✅ | - |
| `source_id` | unknown | ❌ | - |
| `external_id` | unknown | ❌ | - |
| `external_reference` | unknown | ❌ | - |
| `customer_id` | unknown | ❌ | - |
| `customer_email` | unknown | ❌ | - |
| `customer_name` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `metadata` | unknown | ❌ | - |
| `processor_id` | unknown | ❌ | - |
| `method_id` | unknown | ❌ | - |

### PaymentTransactionRead

**Descrição:** Schema for reading a payment transaction.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `amount` | string | ✅ | - |
| `currency` | string | ❌ | - |
| `status` | PaymentStatus | ❌ | - |
| `source_type` | string | ✅ | - |
| `source_id` | unknown | ❌ | - |
| `external_id` | unknown | ❌ | - |
| `external_reference` | unknown | ❌ | - |
| `customer_id` | unknown | ❌ | - |
| `customer_email` | unknown | ❌ | - |
| `customer_name` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `metadata` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `processor_id` | unknown | ❌ | - |
| `method_id` | unknown | ❌ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `processed_at` | unknown | ❌ | - |

### PaymentTransactionUpdate

**Descrição:** Schema for updating an existing payment transaction.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `status` | unknown | ❌ | - |
| `external_id` | unknown | ❌ | - |
| `external_reference` | unknown | ❌ | - |
| `processed_at` | unknown | ❌ | - |
| `metadata` | unknown | ❌ | - |

## 🔗 Endpoints Detalhados

### PUT /api/modules/core/payments/refunds/{refund_id} {#put-apimodulescorepaymentsrefundsrefund-id}

**Resumo:** Update Payment Refund
**Descrição:** Update an existing payment refund.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `refund_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [PaymentRefundUpdate](#paymentrefundupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PaymentRefundRead](#paymentrefundread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/core/payments/refunds/{refund_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/payments/transactions {#get-apimodulescorepaymentstransactions}

**Resumo:** List Payment Transactions
**Descrição:** List all payment transactions for the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `status_filter` | string | query | ❌ | Filter by transaction status |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/payments/transactions" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/payments/transactions {#post-apimodulescorepaymentstransactions}

**Resumo:** Create Payment Transaction
**Descrição:** Create a new payment transaction for the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [PaymentTransactionCreate](#paymenttransactioncreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PaymentTransactionRead](#paymenttransactionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/payments/transactions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/payments/transactions/{transaction_id} {#get-apimodulescorepaymentstransactionstransaction-id}

**Resumo:** Get Payment Transaction
**Descrição:** Get details of a specific payment transaction.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `transaction_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PaymentTransactionRead](#paymenttransactionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/payments/transactions/{transaction_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/core/payments/transactions/{transaction_id} {#put-apimodulescorepaymentstransactionstransaction-id}

**Resumo:** Update Payment Transaction
**Descrição:** Update an existing payment transaction.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `transaction_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [PaymentTransactionUpdate](#paymenttransactionupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PaymentTransactionRead](#paymenttransactionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/core/payments/transactions/{transaction_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/core/payments/transactions/{transaction_id}/refunds {#post-apimodulescorepaymentstransactionstransaction-idrefunds}

**Resumo:** Create Payment Refund
**Descrição:** Create a new refund for a payment transaction.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `transaction_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [PaymentRefundCreate](#paymentrefundcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PaymentRefundRead](#paymentrefundread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/payments/transactions/{transaction_id}/refunds" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
