from fastapi import APIRouter, Depends, HTTPException, status

# Modificado para usar AsyncSession
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List  # Removed unused import: Optional
from uuid import UUID
import logging

# Corrigir importação para apontar para app.core.dependencies
from app.core.dependencies import get_db, get_current_active_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from . import schemas, services

# Import subscription service for feature checking
from app.modules.subscriptions import services as subscription_services

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/", response_model=schemas.CustomDomain, status_code=status.HTTP_201_CREATED)
async def create_custom_domain(  # Tornar async
    domain_in: schemas.CustomDomainCreate,
    db: AsyncSession = Depends(get_db),  # Usar AsyncSession
    current_user: User = Depends(get_current_active_user),
    current_tenant: Tenant = Depends(get_current_tenant),
):
    """
    Create a new custom domain for the current tenant.
    """
    # Validação adicional: O usuário tem permissão para gerenciar domínios neste tenant?
    # (Pode ser parte de deps.get_current_tenant_id ou uma verificação separada)

    # --- Verificação de Feature de Assinatura ---
    has_feature_access = await subscription_services.tenant_has_feature(
        db=db,  # Agora db é AsyncSession
        tenant_id=current_tenant.id,
        feature_key="feature_custom_domain",  # Chave da feature a verificar
    )
    if not has_feature_access:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="A funcionalidade de Domínio Personalizado não está incluída no seu plano de assinatura atual.",  # noqa: E501
        )
    # --- Fim da Verificação de Feature ---

    # Usar await para chamar o serviço async
    existing_domain = await services.custom_domain_service.get_custom_domain_by_name(
        db, domain_name=domain_in.domain_name.lower()
    )
    if existing_domain:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="This domain name is already registered.",
        )

    # If using an existing registered domain
    if (
        hasattr(domain_in, "use_existing_domain")
        and domain_in.use_existing_domain
        and hasattr(domain_in, "domain_registration_id")
        and domain_in.domain_registration_id
    ):
        from app.modules.core.custom_domains.services.domain_integration_service import (  # noqa: E402
            DomainIntegrationService,
        )

        integration_service = DomainIntegrationService(db)
        try:
            custom_domain = await integration_service.associate_registered_domain_with_tenant(
                domain_registration_id=domain_in.domain_registration_id,
                tenant_id=current_tenant.id,
                frontend_type=domain_in.frontend_type,
            )
            return custom_domain
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e),
            )
        except Exception as e:
            # Logar o erro e retornar um erro genérico
            logger.error(f"Error associating registered domain: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while associating the domain",
            )

    try:
        # Usar await para chamar o serviço async
        custom_domain = await services.custom_domain_service.create_custom_domain(
            db=db, domain_in=domain_in, tenant_id=current_tenant.id
        )
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        # Logar o erro e retornar um erro genérico
        logger.error(f"Error creating custom domain: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Could not create custom domain.",
        )
    return custom_domain


# Os outros endpoints também precisarão ser async e usar AsyncSession se interagirem com o DB


@router.get("/", response_model=List[schemas.CustomDomain])
async def list_custom_domains(  # Tornar async
    db: AsyncSession = Depends(get_db),  # Usar AsyncSession
    current_user: User = Depends(get_current_active_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    skip: int = 0,
    limit: int = 100,
):
    """
    List custom domains for the current tenant.
    """
    # Usar await para chamar o serviço async
    domains = await services.custom_domain_service.get_custom_domains_by_tenant(
        db, tenant_id=current_tenant.id, skip=skip, limit=limit
    )
    return domains


@router.get("/{domain_id}", response_model=schemas.CustomDomain)
async def get_custom_domain(  # Tornar async
    domain_id: UUID,
    db: AsyncSession = Depends(get_db),  # Usar AsyncSession
    current_user: User = Depends(get_current_active_user),
    current_tenant: Tenant = Depends(get_current_tenant),
):
    """
    Get a specific custom domain by ID.
    Ensures the domain belongs to the current tenant.
    """
    # Usar await para chamar o serviço async
    domain = await services.custom_domain_service.get_custom_domain_by_id(db, domain_id=domain_id)
    if not domain or domain.tenant_id != current_tenant.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Custom domain not found."
        )
    return domain


@router.delete("/{domain_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_custom_domain(  # Tornar async
    domain_id: UUID,
    db: AsyncSession = Depends(get_db),  # Usar AsyncSession
    current_user: User = Depends(get_current_active_user),
    current_tenant: Tenant = Depends(get_current_tenant),
):
    """
    Delete a custom domain.
    Ensures the domain belongs to the current tenant.
    """
    # Usar await para chamar o serviço async
    domain = await services.custom_domain_service.get_custom_domain_by_id(db, domain_id=domain_id)
    if not domain:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Custom domain not found."
        )

    if domain.tenant_id != current_tenant.id:  # Use current_tenant.id
        # Mesmo que exista, se não for do tenant, é 404 para o tenant atual.
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Custom domain not found for this tenant.",
        )

    # TODO: Adicionar lógica para remover configuração do reverse proxy e liberar certificado SSL antes de deletar do DB.  # noqa: E501
    # Esta é uma operação complexa que pode envolver chamadas a sistemas externos ou scripts.
    # Para o MVP, podemos apenas deletar do banco e tratar a infraestrutura
    # manualmente ou em fase posterior.

    # Usar await para chamar o serviço async
    deleted_domain = await services.custom_domain_service.delete_custom_domain(
        db, domain_id=domain_id
    )
    if not deleted_domain:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Custom domain not found during deletion.",
        )
    return None  # HTTP 204


@router.post("/{domain_id}/verify", response_model=schemas.CustomDomain)
async def trigger_dns_verification(  # Já era async, mas db precisa ser AsyncSession
    domain_id: UUID,
    db: AsyncSession = Depends(get_db),  # Usar AsyncSession
    current_user: User = Depends(get_current_active_user),
    current_tenant: Tenant = Depends(get_current_tenant),
):
    """
    Triggers the DNS verification process for a specific custom domain.
    (For MVP, this might just update a timestamp or trigger a background task in a later phase)
    """
    # Usar await para chamar o serviço async
    domain = await services.custom_domain_service.get_custom_domain_by_id(db, domain_id=domain_id)
    if not domain or domain.tenant_id != current_tenant.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Custom domain not found."
        )

    # A verificação acima já garante que o domínio pertence ao tenant, esta é redundante.
    # domain = await services.custom_domain_service.get_custom_domain_by_id(db, domain_id=domain_id)
    # if not domain or domain.tenant_id != current_tenant.id:
    #     raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Custom domain not found.")  # noqa: E501

    # Chama a função de serviço que realiza a verificação
    try:
        # A função de serviço agora é async
        verified_domain = await services.custom_domain_service.verify_domain_dns(
            db=db, domain_id=domain_id
        )
    except ValueError as e:
        # Erros esperados do serviço (ex: domínio não encontrado)
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception:
        # Logar erro inesperado
        # logger.error(f"Unexpected error during DNS verification for domain {domain_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="DNS verification failed due to an internal error.",
        )

    return verified_domain


# TODO: Adicionar endpoint para o sistema (admin ou worker) atualizar o status de um domínio
# (ex: após verificação de DNS ou emissão de SSL bem-sucedida).
# Ex: PUT /internal/custom-domains/{domain_id}/status
# Este endpoint seria protegido por autenticação de serviço/admin.
