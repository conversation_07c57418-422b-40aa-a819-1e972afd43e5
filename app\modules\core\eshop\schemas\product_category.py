from typing import Optional, List
from pydantic import BaseModel, Field
from uuid import UUID


class ProductCategoryBase(BaseModel):
    """Base schema for product categories."""
    name: str = Field(..., min_length=1, max_length=100, description="Category name")
    description: Optional[str] = Field(None, max_length=1000, description="Category description")
    slug: str = Field(..., min_length=1, max_length=120, description="URL-friendly category name")
    parent_id: Optional[UUID] = Field(None, description="Parent category ID for subcategories")
    display_order: int = Field(0, description="Display order")
    is_active: bool = Field(True, description="Whether category is active")
    is_featured: bool = Field(False, description="Whether category is featured")
    meta_title: Optional[str] = Field(None, max_length=200, description="SEO meta title")
    meta_description: Optional[str] = Field(None, max_length=500, description="SEO meta description")
    image_url: Optional[str] = Field(None, max_length=500, description="Category image URL")
    icon: Optional[str] = Field(None, max_length=100, description="Category icon")


class ProductCategoryCreate(ProductCategoryBase):
    """Schema for creating a new product category."""
    tenant_id: Optional[UUID] = Field(None, description="Tenant ID (null for global categories)")


class ProductCategoryUpdate(BaseModel):
    """Schema for updating a product category."""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="Category name")
    description: Optional[str] = Field(None, max_length=1000, description="Category description")
    slug: Optional[str] = Field(None, min_length=1, max_length=120, description="URL-friendly category name")
    parent_id: Optional[UUID] = Field(None, description="Parent category ID for subcategories")
    display_order: Optional[int] = Field(None, description="Display order")
    is_active: Optional[bool] = Field(None, description="Whether category is active")
    is_featured: Optional[bool] = Field(None, description="Whether category is featured")
    meta_title: Optional[str] = Field(None, max_length=200, description="SEO meta title")
    meta_description: Optional[str] = Field(None, max_length=500, description="SEO meta description")
    image_url: Optional[str] = Field(None, max_length=500, description="Category image URL")
    icon: Optional[str] = Field(None, max_length=100, description="Category icon")


class ProductCategoryResponse(ProductCategoryBase):
    """Schema for product category responses."""
    id: UUID
    tenant_id: Optional[UUID]
    product_count: Optional[int] = Field(None, description="Number of products in this category")
    
    class Config:
        from_attributes = True


class ProductCategoryTreeResponse(ProductCategoryResponse):
    """Schema for product category tree responses with children."""
    children: List['ProductCategoryTreeResponse'] = Field(default_factory=list, description="Child categories")
    
    class Config:
        from_attributes = True


# Update forward references
ProductCategoryTreeResponse.model_rebuild()
