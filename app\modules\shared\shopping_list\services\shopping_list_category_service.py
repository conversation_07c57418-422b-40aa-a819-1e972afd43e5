import uuid
from typing import Optional, Sequence
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.modules.shared.shopping_list.models.shopping_list_category import ShoppingListCategory
from app.modules.shared.shopping_list.schemas.shopping_list_category import (
    ShoppingListCategoryCreate,
    ShoppingListCategoryUpdate,
)

# Supondo que existam exceções customizadas
from app.core.exceptions import NotFoundError


class ShoppingListCategoryService:
    """Serviço para gerenciamento de categorias de lista de compras."""

    async def get_categories(
        self,
        db: AsyncSession,
        *,
        tenant_id: uuid.UUID,
        active_only: bool = True,
        skip: int = 0,
        limit: int = 100
    ) -> Sequence[ShoppingListCategory]:
        """Lista categorias do tenant."""
        query = select(ShoppingListCategory).where(ShoppingListCategory.tenant_id == tenant_id)

        if active_only:
            query = query.where(ShoppingListCategory.is_active == True)

        query = query.order_by(ShoppingListCategory.display_order, ShoppingListCategory.name)
        query = query.offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()

    async def get_category_by_id(
        self,
        db: AsyncSession,
        *,
        category_id: uuid.UUID,
        tenant_id: uuid.UUID
    ) -> Optional[ShoppingListCategory]:
        """Obtém categoria por ID."""
        result = await db.execute(
            select(ShoppingListCategory).where(
                ShoppingListCategory.id == category_id,
                ShoppingListCategory.tenant_id == tenant_id
            )
        )
        return result.scalars().first()

    async def create_category(
        self,
        db: AsyncSession,
        *,
        category_data: ShoppingListCategoryCreate,
        tenant_id: uuid.UUID
    ) -> ShoppingListCategory:
        """Cria nova categoria."""
        db_category = ShoppingListCategory(
            tenant_id=tenant_id,
            **category_data.model_dump()
        )
        db.add(db_category)
        await db.commit()
        await db.refresh(db_category)
        return db_category

    async def update_category(
        self,
        db: AsyncSession,
        *,
        category_id: uuid.UUID,
        category_data: ShoppingListCategoryUpdate,
        tenant_id: uuid.UUID
    ) -> ShoppingListCategory:
        """Atualiza categoria."""
        db_category = await self.get_category_by_id(
            db=db, category_id=category_id, tenant_id=tenant_id
        )
        if not db_category:
            raise NotFoundError(f"Category with id {category_id} not found")

        update_data = category_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_category, field, value)

        await db.commit()
        await db.refresh(db_category)
        return db_category

    async def delete_category(
        self,
        db: AsyncSession,
        *,
        category_id: uuid.UUID,
        tenant_id: uuid.UUID
    ) -> ShoppingListCategory:
        """Exclui categoria (soft delete)."""
        db_category = await self.get_category_by_id(
            db=db, category_id=category_id, tenant_id=tenant_id
        )
        if not db_category:
            raise NotFoundError(f"Category with id {category_id} not found")

        db_category.is_active = False
        await db.commit()
        await db.refresh(db_category)
        return db_category

    async def get_category_by_name(
        self,
        db: AsyncSession,
        *,
        name: str,
        tenant_id: uuid.UUID,
        active_only: bool = True
    ) -> Optional[ShoppingListCategory]:
        """Obtém categoria por nome."""
        query = select(ShoppingListCategory).where(
            ShoppingListCategory.tenant_id == tenant_id,
            ShoppingListCategory.name.ilike(name)
        )
        
        if active_only:
            query = query.where(ShoppingListCategory.is_active == True)
        
        result = await db.execute(query)
        return result.scalars().first()

    async def create_default_categories(
        self,
        db: AsyncSession,
        *,
        tenant_id: uuid.UUID
    ) -> Sequence[ShoppingListCategory]:
        """Cria categorias padrão para um novo tenant."""
        default_categories = [
            {
                "name": "Vegetais e Frutas",
                "description": "Produtos frescos, vegetais e frutas",
                "display_order": 1,
                "color": "#4CAF50"
            },
            {
                "name": "Proteínas",
                "description": "Carnes, peixes e proteínas",
                "display_order": 2,
                "color": "#F44336"
            },
            {
                "name": "Laticínios",
                "description": "Leite, queijos e derivados",
                "display_order": 3,
                "color": "#2196F3"
            },
            {
                "name": "Produtos de Limpeza",
                "description": "Produtos de limpeza e higiene",
                "display_order": 4,
                "color": "#FF9800"
            },
            {
                "name": "Geral",
                "description": "Outros produtos diversos",
                "display_order": 5,
                "color": "#9E9E9E"
            }
        ]

        created_categories = []
        for category_data in default_categories:
            # Verificar se categoria já existe
            existing = await self.get_category_by_name(
                db=db, name=category_data["name"], tenant_id=tenant_id
            )
            
            if not existing:
                category_create = ShoppingListCategoryCreate(**category_data)
                db_category = await self.create_category(
                    db=db, category_data=category_create, tenant_id=tenant_id
                )
                created_categories.append(db_category)
            else:
                created_categories.append(existing)

        return created_categories


# Instância do serviço para uso em outros módulos
shopping_list_category_service = ShoppingListCategoryService()
