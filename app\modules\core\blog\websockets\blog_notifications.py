"""
Blog Notification Manager

Manages real-time notifications for blog features.
"""

import json
import uuid
from typing import Dict, Set, Optional, Any
from fastapi import WebSocket
from fastapi.websockets import WebSocketState


class BlogNotificationManager:
    """
    Manages WebSocket connections and notifications for blog features.
    
    Handles:
    - Client connection management
    - Post-specific subscriptions
    - Admin notifications
    - Broadcasting messages
    """
    
    def __init__(self):
        # Active WebSocket connections: client_id -> WebSocket
        self.active_connections: Dict[str, WebSocket] = {}
        
        # Client metadata: client_id -> user_info
        self.client_info: Dict[str, Dict[str, Any]] = {}
        
        # Post subscriptions: post_id -> set of client_ids
        self.post_subscriptions: Dict[str, Set[str]] = {}
        
        # Admin subscriptions: set of admin client_ids
        self.admin_subscriptions: Set[str] = set()
    
    async def connect(self, client_id: str, websocket: WebSocket, user_id: str = None):
        """
        Register a new WebSocket connection.
        
        Args:
            client_id: Unique client identifier
            websocket: WebSocket connection
            user_id: Optional user ID for authenticated users
        """
        self.active_connections[client_id] = websocket
        self.client_info[client_id] = {
            "user_id": user_id,
            "connected_at": str(uuid.uuid4()),  # Timestamp placeholder
        }
        
        # Send welcome message
        await self.send_to_client(client_id, {
            "type": "connected",
            "client_id": client_id,
            "message": "Connected to blog notifications"
        })
    
    async def disconnect(self, client_id: str):
        """
        Remove a WebSocket connection and clean up subscriptions.
        
        Args:
            client_id: Client identifier to disconnect
        """
        # Remove from active connections
        if client_id in self.active_connections:
            del self.active_connections[client_id]
        
        # Remove client info
        if client_id in self.client_info:
            del self.client_info[client_id]
        
        # Remove from post subscriptions
        for post_id, subscribers in self.post_subscriptions.items():
            subscribers.discard(client_id)
        
        # Remove empty post subscriptions
        self.post_subscriptions = {
            post_id: subscribers 
            for post_id, subscribers in self.post_subscriptions.items()
            if subscribers
        }
        
        # Remove from admin subscriptions
        self.admin_subscriptions.discard(client_id)
    
    async def send_to_client(self, client_id: str, message: Dict[str, Any]):
        """
        Send a message to a specific client.
        
        Args:
            client_id: Target client identifier
            message: Message data to send
        """
        if client_id in self.active_connections:
            websocket = self.active_connections[client_id]
            
            # Check if connection is still active
            if websocket.client_state == WebSocketState.CONNECTED:
                try:
                    await websocket.send_text(json.dumps(message))
                except Exception as e:
                    print(f"Error sending message to {client_id}: {e}")
                    # Remove dead connection
                    await self.disconnect(client_id)
            else:
                # Remove dead connection
                await self.disconnect(client_id)
    
    async def broadcast_to_all(self, message: Dict[str, Any]):
        """
        Broadcast a message to all connected clients.
        
        Args:
            message: Message data to broadcast
        """
        disconnected_clients = []
        
        for client_id, websocket in self.active_connections.items():
            if websocket.client_state == WebSocketState.CONNECTED:
                try:
                    await websocket.send_text(json.dumps(message))
                except Exception as e:
                    print(f"Error broadcasting to {client_id}: {e}")
                    disconnected_clients.append(client_id)
            else:
                disconnected_clients.append(client_id)
        
        # Clean up disconnected clients
        for client_id in disconnected_clients:
            await self.disconnect(client_id)
    
    async def subscribe_to_post(self, client_id: str, post_id: str):
        """
        Subscribe a client to notifications for a specific post.
        
        Args:
            client_id: Client identifier
            post_id: Post identifier
        """
        if post_id not in self.post_subscriptions:
            self.post_subscriptions[post_id] = set()
        
        self.post_subscriptions[post_id].add(client_id)
        
        await self.send_to_client(client_id, {
            "type": "subscribed",
            "post_id": post_id,
            "message": f"Subscribed to post {post_id} notifications"
        })
    
    async def unsubscribe_from_post(self, client_id: str, post_id: str):
        """
        Unsubscribe a client from post notifications.
        
        Args:
            client_id: Client identifier
            post_id: Post identifier
        """
        if post_id in self.post_subscriptions:
            self.post_subscriptions[post_id].discard(client_id)
            
            # Remove empty subscription
            if not self.post_subscriptions[post_id]:
                del self.post_subscriptions[post_id]
        
        await self.send_to_client(client_id, {
            "type": "unsubscribed",
            "post_id": post_id,
            "message": f"Unsubscribed from post {post_id} notifications"
        })
    
    async def broadcast_to_post(
        self, 
        post_id: str, 
        message: Dict[str, Any], 
        exclude_client: str = None
    ):
        """
        Broadcast a message to all clients subscribed to a post.
        
        Args:
            post_id: Post identifier
            message: Message data to broadcast
            exclude_client: Optional client ID to exclude from broadcast
        """
        if post_id not in self.post_subscriptions:
            return
        
        subscribers = self.post_subscriptions[post_id].copy()
        
        if exclude_client:
            subscribers.discard(exclude_client)
        
        for client_id in subscribers:
            await self.send_to_client(client_id, message)
    
    async def subscribe_to_admin(self, client_id: str):
        """
        Subscribe a client to admin notifications.
        
        Args:
            client_id: Client identifier (should be admin)
        """
        self.admin_subscriptions.add(client_id)
        
        await self.send_to_client(client_id, {
            "type": "admin_subscribed",
            "message": "Subscribed to admin notifications"
        })
    
    async def broadcast_to_admins(self, message: Dict[str, Any]):
        """
        Broadcast a message to all admin subscribers.
        
        Args:
            message: Message data to broadcast
        """
        admin_clients = self.admin_subscriptions.copy()
        
        for client_id in admin_clients:
            await self.send_to_client(client_id, message)
    
    async def notify_new_comment(
        self, 
        post_id: str, 
        comment_data: Dict[str, Any]
    ):
        """
        Notify subscribers about a new comment on a post.
        
        Args:
            post_id: Post identifier
            comment_data: Comment information
        """
        message = {
            "type": "new_comment",
            "post_id": post_id,
            "comment": comment_data
        }
        
        # Notify post subscribers
        await self.broadcast_to_post(post_id, message)
        
        # Notify admins if comment needs moderation
        if comment_data.get("status") == "pending":
            admin_message = {
                "type": "comment_moderation_needed",
                "post_id": post_id,
                "comment": comment_data
            }
            await self.broadcast_to_admins(admin_message)
    
    async def notify_post_published(self, post_data: Dict[str, Any]):
        """
        Notify all clients about a new published post.
        
        Args:
            post_data: Post information
        """
        message = {
            "type": "post_published",
            "post": post_data
        }
        
        await self.broadcast_to_all(message)
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """
        Get current connection statistics.
        
        Returns:
            Dictionary with connection statistics
        """
        return {
            "total_connections": len(self.active_connections),
            "post_subscriptions": len(self.post_subscriptions),
            "admin_subscriptions": len(self.admin_subscriptions),
            "active_posts": list(self.post_subscriptions.keys()),
        }
