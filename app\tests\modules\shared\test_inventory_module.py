"""
Comprehensive tests for Inventory module.

Tests all inventory functionality including items management,
stock adjustments, and proper authorization.
"""

import httpx
from typing import Dict


class TestInventoryItems:
    """Test inventory items functionality."""

    async def test_list_inventory_items_tenant_owner(
        self, async_client: httpx.AsyncClient, tenant_headers_tenant_owner: Dict[str, str]
    ):
        """Test listing inventory items as tenant owner."""
        response = await async_client.get(
            "/api/modules/inventory/items", headers=tenant_headers_tenant_owner
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    async def test_list_inventory_items_customer_forbidden(
        self, async_client: httpx.AsyncClient, tenant_headers_customer: Dict[str, str]
    ):
        """Test customer cannot list inventory items."""
        response = await async_client.get(
            "/api/modules/inventory/items", headers=tenant_headers_customer
        )
        assert response.status_code == 403

    async def test_list_inventory_items_missing_tenant_header(
        self, async_client: httpx.AsyncClient, auth_headers_tenant_owner: Dict[str, str]
    ):
        """Test inventory access without X-Tenant-ID header."""
        response = await async_client.get(
            "/api/modules/inventory/items", headers=auth_headers_tenant_owner
        )
        assert response.status_code == 400

    async def test_create_inventory_item_tenant_owner(
        self, async_client: httpx.AsyncClient, tenant_headers_tenant_owner: Dict[str, str]
    ):
        """Test creating inventory item as tenant owner."""
        item_data = {
            "name": "Test Inventory Item",
            "description": "A test item for inventory",
            "sku": "TEST-001",
            "quantity": 100,
            "unit_price": 5.99,
            "category": "Test Category",
            "minimum_stock": 10,
        }
        response = await async_client.post(
            "/api/modules/inventory/items",
            headers=tenant_headers_tenant_owner,
            json=item_data,
        )
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "Test Inventory Item"
        assert data["sku"] == "TEST-001"
        assert data["quantity"] == 100
        assert data["unit_price"] == 5.99

    async def test_create_inventory_item_customer_forbidden(
        self, async_client: httpx.AsyncClient, tenant_headers_customer: Dict[str, str]
    ):
        """Test customer cannot create inventory item."""
        item_data = {
            "name": "Test Item",
            "sku": "TEST-002",
            "quantity": 50,
            "unit_price": 3.99,
        }
        response = await async_client.post(
            "/api/modules/inventory/items",
            headers=tenant_headers_customer,
            json=item_data,
        )
        assert response.status_code == 403


class TestInventoryItemDetails:
    """Test inventory item detail operations."""

    async def test_get_inventory_item_by_id(
        self, async_client: httpx.AsyncClient, tenant_headers_tenant_owner: Dict[str, str]
    ):
        """Test getting inventory item by ID."""
        # First create an item
        item_data = {
            "name": "Detail Test Item",
            "sku": "DETAIL-001",
            "quantity": 75,
            "unit_price": 8.50,
        }
        create_response = await async_client.post(
            "/api/modules/inventory/items",
            headers=tenant_headers_tenant_owner,
            json=item_data,
        )
        assert create_response.status_code == 201
        created_item = create_response.json()
        item_id = created_item["id"]

        # Now get the item by ID
        response = await async_client.get(
            f"/api/modules/inventory/items/{item_id}",
            headers=tenant_headers_tenant_owner,
        )
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == item_id
        assert data["name"] == "Detail Test Item"
        assert data["sku"] == "DETAIL-001"

    async def test_update_inventory_item(
        self, async_client: httpx.AsyncClient, tenant_headers_tenant_owner: Dict[str, str]
    ):
        """Test updating inventory item."""
        # First create an item
        item_data = {
            "name": "Update Test Item",
            "sku": "UPDATE-001",
            "quantity": 50,
            "unit_price": 10.00,
        }
        create_response = await async_client.post(
            "/api/modules/inventory/items",
            headers=tenant_headers_tenant_owner,
            json=item_data,
        )
        assert create_response.status_code == 201
        created_item = create_response.json()
        item_id = created_item["id"]

        # Update the item
        update_data = {
            "name": "Updated Test Item",
            "description": "Updated description",
            "unit_price": 12.50,
        }
        response = await async_client.put(
            f"/api/modules/inventory/items/{item_id}",
            headers=tenant_headers_tenant_owner,
            json=update_data,
        )
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Updated Test Item"
        assert data["unit_price"] == 12.50

    async def test_get_nonexistent_inventory_item(
        self, async_client: httpx.AsyncClient, tenant_headers_tenant_owner: Dict[str, str]
    ):
        """Test getting non-existent inventory item."""
        response = await async_client.get(
            "/api/modules/inventory/items/nonexistent-id",
            headers=tenant_headers_tenant_owner,
        )
        assert response.status_code == 404


class TestInventoryStockAdjustments:
    """Test inventory stock adjustment functionality."""

    async def test_adjust_stock_positive(
        self, async_client: httpx.AsyncClient, tenant_headers_tenant_owner: Dict[str, str]
    ):
        """Test positive stock adjustment (adding stock)."""
        # First create an item
        item_data = {
            "name": "Stock Test Item",
            "sku": "STOCK-001",
            "quantity": 100,
            "unit_price": 5.00,
        }
        create_response = await async_client.post(
            "/api/modules/inventory/items",
            headers=tenant_headers_tenant_owner,
            json=item_data,
        )
        assert create_response.status_code == 201
        created_item = create_response.json()
        item_id = created_item["id"]

        # Adjust stock positively
        adjustment_data = {
            "quantity_change": 25,
            "reason": "Stock replenishment",
        }
        response = await async_client.post(
            f"/api/modules/inventory/items/{item_id}/adjust-stock",
            headers=tenant_headers_tenant_owner,
            json=adjustment_data,
        )
        assert response.status_code == 200
        data = response.json()
        assert data["quantity"] == 125  # 100 + 25

    async def test_adjust_stock_negative(
        self, async_client: httpx.AsyncClient, tenant_headers_tenant_owner: Dict[str, str]
    ):
        """Test negative stock adjustment (removing stock)."""
        # First create an item
        item_data = {
            "name": "Stock Reduction Test",
            "sku": "STOCK-002",
            "quantity": 100,
            "unit_price": 5.00,
        }
        create_response = await async_client.post(
            "/api/modules/inventory/items",
            headers=tenant_headers_tenant_owner,
            json=item_data,
        )
        assert create_response.status_code == 201
        created_item = create_response.json()
        item_id = created_item["id"]

        # Adjust stock negatively
        adjustment_data = {
            "quantity_change": -30,
            "reason": "Damaged goods",
        }
        response = await async_client.post(
            f"/api/modules/inventory/items/{item_id}/adjust-stock",
            headers=tenant_headers_tenant_owner,
            json=adjustment_data,
        )
        assert response.status_code == 200
        data = response.json()
        assert data["quantity"] == 70  # 100 - 30

    async def test_adjust_stock_customer_forbidden(
        self, async_client: httpx.AsyncClient, tenant_headers_customer: Dict[str, str]
    ):
        """Test customer cannot adjust stock."""
        adjustment_data = {
            "quantity_change": 10,
            "reason": "Test adjustment",
        }
        response = await async_client.post(
            "/api/modules/inventory/items/dummy-id/adjust-stock",
            headers=tenant_headers_customer,
            json=adjustment_data,
        )
        assert response.status_code == 403


class TestInventoryValidation:
    """Test inventory data validation."""

    async def test_create_item_invalid_data(
        self, async_client: httpx.AsyncClient, tenant_headers_tenant_owner: Dict[str, str]
    ):
        """Test creating item with invalid data."""
        invalid_data = {
            "name": "",  # Empty name should be invalid
            "sku": "INVALID-001",
            "quantity": -10,  # Negative quantity should be invalid
            "unit_price": -5.00,  # Negative price should be invalid
        }
        response = await async_client.post(
            "/api/modules/inventory/items",
            headers=tenant_headers_tenant_owner,
            json=invalid_data,
        )
        assert response.status_code == 422

    async def test_create_item_duplicate_sku(
        self, async_client: httpx.AsyncClient, tenant_headers_tenant_owner: Dict[str, str]
    ):
        """Test creating item with duplicate SKU."""
        # Create first item
        item_data = {
            "name": "First Item",
            "sku": "DUPLICATE-SKU",
            "quantity": 10,
            "unit_price": 5.00,
        }
        first_response = await async_client.post(
            "/api/modules/inventory/items",
            headers=tenant_headers_tenant_owner,
            json=item_data,
        )
        assert first_response.status_code == 201

        # Try to create second item with same SKU
        duplicate_data = {
            "name": "Second Item",
            "sku": "DUPLICATE-SKU",  # Same SKU
            "quantity": 20,
            "unit_price": 10.00,
        }
        response = await async_client.post(
            "/api/modules/inventory/items",
            headers=tenant_headers_tenant_owner,
            json=duplicate_data,
        )
        # Should fail due to duplicate SKU
        assert response.status_code in [400, 409, 422]


class TestInventoryFiltering:
    """Test inventory filtering and querying."""

    async def test_filter_items_by_category(
        self, async_client: httpx.AsyncClient, tenant_headers_tenant_owner: Dict[str, str]
    ):
        """Test filtering items by category."""
        response = await async_client.get(
            "/api/modules/inventory/items?category=Electronics",
            headers=tenant_headers_tenant_owner,
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    async def test_filter_items_by_low_stock(
        self, async_client: httpx.AsyncClient, tenant_headers_tenant_owner: Dict[str, str]
    ):
        """Test filtering items by low stock."""
        response = await async_client.get(
            "/api/modules/inventory/items?low_stock=true",
            headers=tenant_headers_tenant_owner,
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    async def test_search_items_by_name(
        self, async_client: httpx.AsyncClient, tenant_headers_tenant_owner: Dict[str, str]
    ):
        """Test searching items by name."""
        response = await async_client.get(
            "/api/modules/inventory/items?search=test",
            headers=tenant_headers_tenant_owner,
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
