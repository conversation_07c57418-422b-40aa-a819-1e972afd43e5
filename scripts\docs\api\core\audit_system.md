# Core - Audit System

**Categoria:** Core
**Módulo:** Audit System
**Total de Endpoints:** 5
**Gerado em:** 27/06/2025, 20:20:26

## 📋 Endpoints

- [GET /api/modules/core/audit/logs](#get-apimodulescoreauditlogs) - Get Audit Logs
- [GET /api/modules/core/audit/logs/{audit_id}](#get-apimodulescoreauditlogsaudit-id) - Get Audit Log
- [GET /api/modules/core/audit/resource/{resource}/{resource_id}/logs](#get-apimodulescoreauditresourceresourceresource-idlogs) - Get Resource Audit Logs
- [GET /api/modules/core/audit/stats](#get-apimodulescoreauditstats) - Get Audit Stats
- [GET /api/modules/core/audit/user/{user_id}/logs](#get-apimodulescoreaudituseruser-idlogs) - Get User Audit Logs

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### AuditLogListResponse

**Descrição:** Schema for audit log list responses.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `items` | Array[AuditLogRead] | ✅ | - |
| `total` | integer | ✅ | - |
| `skip` | integer | ✅ | - |
| `limit` | integer | ✅ | - |
| `has_more` | boolean | ✅ | - |

### AuditLogRead

**Descrição:** Schema for reading audit log entries.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `user_id` | unknown | ❌ | - |
| `action` | AuditAction | ✅ | - |
| `resource` | AuditResource | ✅ | - |
| `resource_id` | unknown | ❌ | - |
| `tenant_id` | unknown | ❌ | - |
| `operation_type` | OperationType | ✅ | - |
| `session_id` | unknown | ❌ | - |
| `ip_address` | unknown | ❌ | - |
| `user_agent` | unknown | ❌ | - |
| `request_id` | unknown | ❌ | - |
| `correlation_id` | unknown | ❌ | - |
| `old_values` | unknown | ❌ | - |
| `new_values` | unknown | ❌ | - |
| `details` | unknown | ❌ | - |
| `security_level` | SecurityLevel | ❌ | - |
| `compliance_tags` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `timestamp` | string | ✅ | - |
| `status` | AuditStatus | ✅ | - |
| `error_message` | unknown | ❌ | - |
| `duration_ms` | unknown | ❌ | - |

### AuditLogStats

**Descrição:** Schema for audit log statistics.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `total_entries` | integer | ✅ | - |
| `success_count` | integer | ✅ | - |
| `failure_count` | integer | ✅ | - |
| `pending_count` | integer | ✅ | - |
| `resource_breakdown` | object | ✅ | - |
| `action_breakdown` | object | ✅ | - |
| `recent_activity_count` | integer | ✅ | - |
| `sensitive_operations_count` | integer | ✅ | - |
| `critical_operations_count` | integer | ✅ | - |
| `avg_duration_ms` | unknown | ❌ | - |
| `max_duration_ms` | unknown | ❌ | - |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/modules/core/audit/logs {#get-apimodulescoreauditlogs}

**Resumo:** Get Audit Logs
**Descrição:** Get audit logs with comprehensive filtering.

Requires admin role or appropriate permissions.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `user_id` | string | query | ❌ | Filter by user ID |
| `tenant_id` | string | query | ❌ | Filter by tenant ID |
| `action` | string | query | ❌ | Filter by action |
| `resource` | string | query | ❌ | Filter by resource |
| `resource_id` | string | query | ❌ | Filter by resource ID |
| `operation_type` | string | query | ❌ | Filter by operation type |
| `status` | string | query | ❌ | Filter by status |
| `security_level` | string | query | ❌ | Filter by security level |
| `start_date` | string | query | ❌ | Start date for filtering |
| `end_date` | string | query | ❌ | End date for filtering |
| `search_term` | string | query | ❌ | Search term |
| `ip_address` | string | query | ❌ | Filter by IP address |
| `skip` | integer | query | ❌ | Number of records to skip |
| `limit` | integer | query | ❌ | Number of records to return |
| `sort_by` | string | query | ❌ | Field to sort by |
| `sort_order` | string | query | ❌ | Sort order |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [AuditLogListResponse](#auditloglistresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/audit/logs" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/audit/logs/{audit_id} {#get-apimodulescoreauditlogsaudit-id}

**Resumo:** Get Audit Log
**Descrição:** Get a specific audit log entry by ID.

Requires admin role or ownership of the audit log.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `audit_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [AuditLogRead](#auditlogread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/audit/logs/{audit_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/audit/resource/{resource}/{resource_id}/logs {#get-apimodulescoreauditresourceresourceresource-idlogs}

**Resumo:** Get Resource Audit Logs
**Descrição:** Get audit logs for a specific resource.

Requires admin role or appropriate resource permissions.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `resource` | string | path | ✅ | - |
| `resource_id` | string | path | ✅ | - |
| `start_date` | string | query | ❌ | Start date for filtering |
| `end_date` | string | query | ❌ | End date for filtering |
| `action` | string | query | ❌ | Filter by action |
| `skip` | integer | query | ❌ | Number of records to skip |
| `limit` | integer | query | ❌ | Number of records to return |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [AuditLogListResponse](#auditloglistresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/audit/resource/{resource}/{resource_id}/logs" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/audit/stats {#get-apimodulescoreauditstats}

**Resumo:** Get Audit Stats
**Descrição:** Get audit statistics and metrics.

Requires admin role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `start_date` | string | query | ❌ | Start date for stats |
| `end_date` | string | query | ❌ | End date for stats |
| `tenant_id` | string | query | ❌ | Filter by tenant ID |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [AuditLogStats](#auditlogstats)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/audit/stats" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/audit/user/{user_id}/logs {#get-apimodulescoreaudituseruser-idlogs}

**Resumo:** Get User Audit Logs
**Descrição:** Get audit logs for a specific user.

Users can only see their own logs unless they are admin.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `user_id` | string | path | ✅ | - |
| `start_date` | string | query | ❌ | Start date for filtering |
| `end_date` | string | query | ❌ | End date for filtering |
| `action` | string | query | ❌ | Filter by action |
| `resource` | string | query | ❌ | Filter by resource |
| `skip` | integer | query | ❌ | Number of records to skip |
| `limit` | integer | query | ❌ | Number of records to return |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [AuditLogListResponse](#auditloglistresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/audit/user/{user_id}/logs" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
