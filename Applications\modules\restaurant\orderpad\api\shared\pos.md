# Shared - Pos

**Categoria:** Shared
**<PERSON><PERSON><PERSON><PERSON>:** Pos
**Total de Endpoints:** 18
**Gerado em:** 08/06/2025, 17:26:01

## 📋 Endpoints

- [GET /api/modules/pos/cash-registers](#get-apimodulesposcash-registers) - List Cash Registers
- [POST /api/modules/pos/cash-registers](#post-apimodulesposcash-registers) - Create Cash Register
- [GET /api/modules/pos/cash-registers/sessions/{session_id}](#get-apimodulesposcash-registerssessionssession-id) - Get Cash Register Session
- [PUT /api/modules/pos/cash-registers/sessions/{session_id}/close](#put-apimodulesposcash-registerssessionssession-idclose) - Close Cash Register Session
- [GET /api/modules/pos/cash-registers/{cash_register_id}/active-session](#get-apimodulesposcash-registerscash-register-idactive-session) - Get Active Cash Register Session
- [GET /api/modules/pos/cash-registers/{cash_register_id}/sessions](#get-apimodulesposcash-registerscash-register-idsessions) - List Cash Register Sessions
- [POST /api/modules/pos/cash-registers/{cash_register_id}/sessions](#post-apimodulesposcash-registerscash-register-idsessions) - Open Cash Register Session
- [POST /api/modules/pos/cash-registers/{cash_register_id}/transactions](#post-apimodulesposcash-registerscash-register-idtransactions) - Create Sale Transaction
- [GET /api/modules/pos/payment-methods](#get-apimodulespospayment-methods) - List Payment Methods
- [POST /api/modules/pos/payment-methods](#post-apimodulespospayment-methods) - Create Payment Method
- [GET /api/modules/pos/payment-methods/{payment_method_id}](#get-apimodulespospayment-methodspayment-method-id) - Get Payment Method
- [PUT /api/modules/pos/payment-methods/{payment_method_id}](#put-apimodulespospayment-methodspayment-method-id) - Update Payment Method
- [GET /api/modules/pos/transactions](#get-apimodulespostransactions) - List Sale Transactions
- [GET /api/modules/pos/transactions/{transaction_id}](#get-apimodulespostransactionstransaction-id) - Get Transaction
- [GET /api/modules/pos/transactions/{transaction_id}/payments](#get-apimodulespostransactionstransaction-idpayments) - List Transaction Payments
- [POST /api/modules/pos/transactions/{transaction_id}/payments](#post-apimodulespostransactionstransaction-idpayments) - Add Transaction Payment
- [POST /api/modules/pos/transactions/{transaction_id}/refund](#post-apimodulespostransactionstransaction-idrefund) - Refund Transaction
- [GET /api/modules/pos/transactions/{transaction_id}/refunds](#get-apimodulespostransactionstransaction-idrefunds) - Get Transaction Refunds

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### CashRegisterCreate

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `is_active` | unknown | ❌ | - |

### CashRegisterRead

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `is_active` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |

### CashRegisterSessionClose

**Descrição:** Schema for closing a cash register session.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `closing_balance` | unknown | ✅ | - |
| `closing_notes` | unknown | ❌ | - |
| `actual_cash_amount` | unknown | ✅ | - |
| `discrepancy_reason` | unknown | ❌ | - |

### CashRegisterSessionOpen

**Descrição:** Schema for opening a cash register session.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `opening_balance` | unknown | ✅ | - |
| `opening_notes` | unknown | ❌ | - |

### CashRegisterSessionRead

**Descrição:** Schema for reading a cash register session.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `cash_register_id` | string | ✅ | - |
| `operator_id` | string | ✅ | - |
| `is_open` | boolean | ✅ | - |
| `opening_timestamp` | string | ✅ | - |
| `opening_balance` | string | ✅ | - |
| `opening_notes` | unknown | ❌ | - |
| `closing_timestamp` | unknown | ❌ | - |
| `closing_balance` | unknown | ❌ | - |
| `closing_notes` | unknown | ❌ | - |
| `expected_cash_amount` | unknown | ❌ | - |
| `actual_cash_amount` | unknown | ❌ | - |
| `discrepancy_amount` | unknown | ❌ | - |
| `discrepancy_reason` | unknown | ❌ | - |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### RefundCreate

**Descrição:** Schema for creating a refund.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `amount` | unknown | ✅ | - |
| `reason` | string | ✅ | - |

### SaleTransactionCreate

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `total_amount` | unknown | ✅ | - |
| `payment_method` | unknown | ❌ | - |
| `status` | unknown | ❌ | - |
| `customer_email` | unknown | ❌ | - |
| `customer_phone` | unknown | ❌ | - |

### SaleTransactionRead

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `total_amount` | string | ✅ | - |
| `payment_method` | unknown | ❌ | - |
| `status` | unknown | ❌ | - |
| `customer_id` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `cash_register_id` | string | ✅ | - |
| `refunds` | unknown | ❌ | - |
| `refunded_amount` | unknown | ❌ | - |
| `remaining_amount` | unknown | ❌ | - |

### TransactionPaymentCreate

**Descrição:** Schema for creating a new transaction payment.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `payment_method_id` | string | ✅ | - |
| `amount` | string | ✅ | - |
| `reference` | unknown | ❌ | - |
| `details` | unknown | ❌ | - |

### TransactionPaymentRead

**Descrição:** Schema for reading a transaction payment.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `payment_method_id` | string | ✅ | - |
| `amount` | string | ✅ | - |
| `reference` | unknown | ❌ | - |
| `details` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `transaction_id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |

### app__modules__shared__pos__schemas__payment_method__PaymentMethodCreate

**Descrição:** Schema for creating a new payment method.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `method_type` | app__modules__shared__pos__models__payment_method__PaymentMethodType | ✅ | - |
| `is_active` | boolean | ❌ | - |
| `processor_id` | unknown | ❌ | - |
| `processor_config` | unknown | ❌ | - |

### app__modules__shared__pos__schemas__payment_method__PaymentMethodRead

**Descrição:** Schema for reading a payment method.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `method_type` | app__modules__shared__pos__models__payment_method__PaymentMethodType | ✅ | - |
| `is_active` | boolean | ❌ | - |
| `processor_id` | unknown | ❌ | - |
| `processor_config` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |

### app__modules__shared__pos__schemas__payment_method__PaymentMethodUpdate

**Descrição:** Schema for updating an existing payment method.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | - |
| `method_type` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `processor_id` | unknown | ❌ | - |
| `processor_config` | unknown | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/modules/pos/cash-registers {#get-apimodulesposcash-registers}

**Resumo:** List Cash Registers
**Descrição:** List all cash registers for the current tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/pos/cash-registers" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/pos/cash-registers {#post-apimodulesposcash-registers}

**Resumo:** Create Cash Register
**Descrição:** Create a new cash register for the current tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [CashRegisterCreate](#cashregistercreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CashRegisterRead](#cashregisterread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/pos/cash-registers" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/pos/cash-registers/sessions/{session_id} {#get-apimodulesposcash-registerssessionssession-id}

**Resumo:** Get Cash Register Session
**Descrição:** Get details of a specific cash register session.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `session_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CashRegisterSessionRead](#cashregistersessionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/pos/cash-registers/sessions/{session_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/pos/cash-registers/sessions/{session_id}/close {#put-apimodulesposcash-registerssessionssession-idclose}

**Resumo:** Close Cash Register Session
**Descrição:** Close an active cash register session.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `session_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [CashRegisterSessionClose](#cashregistersessionclose)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CashRegisterSessionRead](#cashregistersessionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/pos/cash-registers/sessions/{session_id}/close" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/pos/cash-registers/{cash_register_id}/active-session {#get-apimodulesposcash-registerscash-register-idactive-session}

**Resumo:** Get Active Cash Register Session
**Descrição:** Get the active session for a specific cash register, if any.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `cash_register_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CashRegisterSessionRead](#cashregistersessionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/pos/cash-registers/{cash_register_id}/active-session" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/pos/cash-registers/{cash_register_id}/sessions {#get-apimodulesposcash-registerscash-register-idsessions}

**Resumo:** List Cash Register Sessions
**Descrição:** List all sessions for a specific cash register.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `cash_register_id` | string | path | ✅ | - |
| `is_open` | string | query | ❌ | Filter by session status (open/closed) |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/pos/cash-registers/{cash_register_id}/sessions" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/pos/cash-registers/{cash_register_id}/sessions {#post-apimodulesposcash-registerscash-register-idsessions}

**Resumo:** Open Cash Register Session
**Descrição:** Open a new session for a specific cash register.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `cash_register_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [CashRegisterSessionOpen](#cashregistersessionopen)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CashRegisterSessionRead](#cashregistersessionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/pos/cash-registers/{cash_register_id}/sessions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/pos/cash-registers/{cash_register_id}/transactions {#post-apimodulesposcash-registerscash-register-idtransactions}

**Resumo:** Create Sale Transaction
**Descrição:** Register a new sale transaction for a specific cash register of the current tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `cash_register_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [SaleTransactionCreate](#saletransactioncreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [SaleTransactionRead](#saletransactionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/pos/cash-registers/{cash_register_id}/transactions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/pos/payment-methods {#get-apimodulespospayment-methods}

**Resumo:** List Payment Methods
**Descrição:** List all payment methods for the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `is_active` | string | query | ❌ | Filter by active status |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/pos/payment-methods" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/pos/payment-methods {#post-apimodulespospayment-methods}

**Resumo:** Create Payment Method
**Descrição:** Create a new payment method for the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [app__modules__shared__pos__schemas__payment_method__PaymentMethodCreate](#app__modules__shared__pos__schemas__payment_method__paymentmethodcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [app__modules__shared__pos__schemas__payment_method__PaymentMethodRead](#app__modules__shared__pos__schemas__payment_method__paymentmethodread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/pos/payment-methods" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/pos/payment-methods/{payment_method_id} {#get-apimodulespospayment-methodspayment-method-id}

**Resumo:** Get Payment Method
**Descrição:** Get details of a specific payment method.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `payment_method_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [app__modules__shared__pos__schemas__payment_method__PaymentMethodRead](#app__modules__shared__pos__schemas__payment_method__paymentmethodread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/pos/payment-methods/{payment_method_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/pos/payment-methods/{payment_method_id} {#put-apimodulespospayment-methodspayment-method-id}

**Resumo:** Update Payment Method
**Descrição:** Update an existing payment method.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `payment_method_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [app__modules__shared__pos__schemas__payment_method__PaymentMethodUpdate](#app__modules__shared__pos__schemas__payment_method__paymentmethodupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [app__modules__shared__pos__schemas__payment_method__PaymentMethodRead](#app__modules__shared__pos__schemas__payment_method__paymentmethodread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/pos/payment-methods/{payment_method_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/pos/transactions {#get-apimodulespostransactions}

**Resumo:** List Sale Transactions
**Descrição:** List sale transactions for the current tenant, with optional filters.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `cash_register_id` | string | query | ❌ | Filter by cash register ID |
| `customer_id` | string | query | ❌ | Filter by customer ID |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/pos/transactions" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/pos/transactions/{transaction_id} {#get-apimodulespostransactionstransaction-id}

**Resumo:** Get Transaction
**Descrição:** Get details of a specific sale transaction.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `transaction_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [SaleTransactionRead](#saletransactionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/pos/transactions/{transaction_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/pos/transactions/{transaction_id}/payments {#get-apimodulespostransactionstransaction-idpayments}

**Resumo:** List Transaction Payments
**Descrição:** List all payments for a transaction.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `transaction_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/pos/transactions/{transaction_id}/payments" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/pos/transactions/{transaction_id}/payments {#post-apimodulespostransactionstransaction-idpayments}

**Resumo:** Add Transaction Payment
**Descrição:** Add a payment to a transaction.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `transaction_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TransactionPaymentCreate](#transactionpaymentcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TransactionPaymentRead](#transactionpaymentread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/pos/transactions/{transaction_id}/payments" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/pos/transactions/{transaction_id}/refund {#post-apimodulespostransactionstransaction-idrefund}

**Resumo:** Refund Transaction
**Descrição:** Process a refund for a sale transaction.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `transaction_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [RefundCreate](#refundcreate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [SaleTransactionRead](#saletransactionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/pos/transactions/{transaction_id}/refund" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/pos/transactions/{transaction_id}/refunds {#get-apimodulespostransactionstransaction-idrefunds}

**Resumo:** Get Transaction Refunds
**Descrição:** Get refund history for a specific sale transaction.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `transaction_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/pos/transactions/{transaction_id}/refunds" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
