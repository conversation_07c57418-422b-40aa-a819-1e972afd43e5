import uuid
import logging
from app.websockets.manager import emit_to_tenant
from app.modules.core.functions.orders.schemas.order import OrderRead

logger = logging.getLogger(__name__)


async def emit_new_order(tenant_id: uuid.UUID, order: OrderRead) -> None:
    """
    Emit a WebSocket event when a new order is created.

    Args:
        tenant_id: ID of the tenant to emit the event to
        order: Order data to emit
    """
    try:
        # Convert the Pydantic model to a dictionary
        order_data = order.model_dump()

        # Emit event to the tenant
        await emit_to_tenant(
            tenant_id=tenant_id,
            event="order_created",
            data=order_data,
        )
        logger.info(f"Emitted event order_created for tenant {tenant_id}, order {order.id}")
    except Exception as e:
        logger.error(f"Error emitting order_created event: {e}")


async def emit_order_update(tenant_id: uuid.UUID, order: OrderRead) -> None:
    """
    Emit a WebSocket event when an order is updated.

    Args:
        tenant_id: ID of the tenant to emit the event to
        order: Updated order data to emit
    """
    try:
        # Convert the Pydantic model to a dictionary
        order_data = order.model_dump()

        # Emit event to the tenant
        await emit_to_tenant(
            tenant_id=tenant_id,
            event="order_updated",
            data=order_data,
        )
        logger.info(
            f"Emitted event order_updated for tenant {tenant_id}, order {order.id}, status {order.status}"  # noqa: E501
        )
    except Exception as e:
        logger.error(f"Error emitting order_updated event: {e}")


async def emit_order_item_update(
    tenant_id: uuid.UUID, order_id: uuid.UUID, item_id: uuid.UUID, item_data: dict
) -> None:
    """
    Emit a WebSocket event when an order item is updated.

    Args:
        tenant_id: ID of the tenant to emit the event to
        order_id: ID of the order containing the item
        item_id: ID of the updated item
        item_data: Updated item data to emit
    """
    try:
        # Prepare the data to emit
        data = {
            "order_id": str(order_id),
            "item_id": str(item_id),
            "item_data": item_data,
        }

        # Emit event to the tenant
        await emit_to_tenant(
            tenant_id=tenant_id,
            event="order_item_updated",
            data=data,
        )
        logger.info(
            f"Emitted event order_item_updated for tenant {tenant_id}, order {order_id}, item {item_id}"  # noqa: E501
        )
    except Exception as e:
        logger.error(f"Error emitting order_item_updated event: {e}")
