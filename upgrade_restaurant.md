# Planejamento de Evolução do Módulo Restaurante

## 1. Diagnóstico Técnico Detalhado

### Backend (FastAPI)
- **Robustez:** APIs REST assíncronas, controle de permissões multi-tenant, endpoints públicos otimizados, lógica clara para hierarquia de categorias/itens, integração com serviços de mídia e cache.
- **Velocidade:** Uso de async/await, cache de tenants, queries otimizadas, mas pode evoluir com profiling real e cache de queries críticas.
- **Pontos Fortes:** Modularidade, extensibilidade, separação de responsabilidades, fácil integração com novos módulos (ex: estoque, pedidos).
- **Pontos de Atenção:** Testes automatizados, documentação OpenAPI, profiling de queries, logs de auditoria.

### Frontend Administrativo (Next.js)
- **Robustez:** Componentização avançada, hooks para permissões e filtros, drag-and-drop, modais, integração direta com backend, UX rica.
- **Velocidade:** Suspense, lazy loading, overlays de loading, mas pode evoluir com bundle splitting e otimização de dependências.
- **Pontos Fortes:** Modularidade, facilidade de evolução, UX moderna.
- **Pontos de Atenção:** Testes de usabilidade, acessibilidade, cobertura de testes automatizados.

### Menu Digital Público
- **Robustez:** Providers para currency/carrinho, integração com backend público, fallback de loading, arquitetura para multi-tenant.
- **Velocidade:** Carregamento rápido, Suspense, mas pode evoluir com caching e fallback offline.
- **Pontos de Atenção:** Responsividade, acessibilidade, integração fluida com pedidos.

---

## 2. Exemplos Práticos de Integração

### Fluxo de Pedido → Baixa de Estoque

```mermaid
sequenceDiagram
    participant Cliente
    participant MenuDigital
    participant BackendPedidos
    participant BackendEstoque

    Cliente->>MenuDigital: Seleciona itens e envia pedido
    MenuDigital->>BackendPedidos: POST /orders (itens, mesa, tenant)
    BackendPedidos->>BackendEstoque: PATCH /stock/decrement (itens do pedido)
    BackendEstoque-->>BackendPedidos: Confirma baixa ou erro
    BackendPedidos-->>MenuDigital: Confirmação do pedido ou erro
    MenuDigital-->>Cliente: Exibe status do pedido
```

#### Exemplo de endpoint de baixa de estoque (FastAPI):

```python
# PATCH /stock/decrement
@router.patch("/stock/decrement")
async def decrement_stock(items: List[StockDecrementItem], tenant_id: UUID = Depends(get_current_tenant)):
    for item in items:
        stock = await stock_service.get_stock(item.product_id, tenant_id)
        if stock.quantity < item.quantity:
            raise HTTPException(status_code=400, detail="Estoque insuficiente")
        await stock_service.decrement(item.product_id, item.quantity, tenant_id)
    return {"status": "ok"}
```

- **Automação:** Se estoque insuficiente, bloquear pedido e notificar cliente/admin.

### Fluxo de Promoções Automáticas

- Cadastro de regras de promoção (ex: “Combo X”, “Desconto em horário”, “Fidelidade”).
- Backend avalia regras ao criar pedido, aplica descontos automaticamente.
- Histórico de promoções aplicado salvo para relatórios.

#### Exemplo de engine de promoções (pseudocódigo):

```python
def aplicar_promocoes(pedido, regras):
    descontos = []
    for regra in regras:
        if regra.tipo == "combo" and regra.valida_para(pedido):
            descontos.append(regra.calcular_desconto(pedido))
        # outros tipos...
    pedido.total -= sum(d.valor for d in descontos)
    pedido.promocoes_aplicadas = descontos
    return pedido
```

### Fluxo de Relatórios

- Backend agrega dados de pedidos, estoque, promoções.
- Geração de dashboards: vendas por período, itens mais vendidos, promoções mais usadas, rupturas de estoque.
- Exportação para CSV/PDF.

#### Exemplo de endpoint de relatório (FastAPI):

```python
@router.get("/reports/sales")
async def sales_report(period: str, tenant_id: UUID = Depends(get_current_tenant)):
    data = await report_service.get_sales_data(period, tenant_id)
    return data
```

---

## 3. Detalhamento de Automação de Promoções e Relatórios

### Promoções
- **Tipos:** Desconto por horário, combos, cashback, fidelidade, primeira compra, cupom.
- **Automação:** Engine de regras no backend, aplicação automática no fechamento do pedido, logs de promoções aplicadas.
- **Extensibilidade:** Suporte a plugins para promoções customizadas por tenant.
- **Exemplo:** 
  - “Das 12h às 15h, 10% off em todos os pratos executivos.”
  - “Na compra de 2 pizzas, ganhe 1 refrigerante.”

### Relatórios
- **Vendas:** Por período, por item, por categoria, por canal (QR, balcão, delivery).
- **Estoque:** Itens com baixa rotatividade, rupturas, previsão de compras.
- **Promoções:** Efetividade, uso por cliente, impacto no ticket médio.
- **Automação:** Geração automática diária/semana/mensal, envio por email, alertas de anomalias.
- **Indicadores:** Ticket médio, margem, giro de estoque, % de pedidos com promoção.

---

## 4. Plano de Ação Incremental

1. **Estoque:** CRUD, movimentações, integração com pedidos.
2. **Livro de receitas:** Cadastro, custos, integração com estoque.
3. **Compras:** Pedidos a fornecedores, integração estoque/financeiro.
4. **Pedidos digitais:** Integração menu público + backend pedidos + acompanhamento em tempo real (websockets).
5. **Promoções:** Engine de regras, automação na finalização do pedido.
6. **Relatórios:** Dashboards, exportação, automação de envio.
7. **Customização tenant:** Temas, idiomas, regras.
8. **Permissões e auditoria:** Refinar papéis e logs.
9. **Acessibilidade e internacionalização:** Garantir padrões.
10. **Omnichannel:** Unificar pedidos QR, app, balcão, delivery.

---

## 5. Diagrama de Arquitetura Integrada

```mermaid
flowchart TD
    A[Menu Digital Público] -->|Pedidos| B[Backend Pedidos]
    B -->|Atualiza| C[Estoque]
    B -->|Consulta| D[Livro de Receitas]
    B -->|Movimenta| C
    B -->|Gera| E[Relatórios]
    F[Admin Dashboard] -->|Gerencia| B
    F -->|Gerencia| C
    F -->|Gerencia| D
    F -->|Gerencia| G[Compras]
    G -->|Movimenta| C
    F -->|Configura| H[Customização Tenant]
    F -->|Acessa| E
    B -->|Notifica| I[Websockets/Notificações]
    B -->|Aplica| J[Promoções Engine]
    J -->|Log| E
    B -->|Integra| K[Omnichannel]
```

---

## 6. Práticas de Mercado e Recomendações Avançadas

- **Performance:** Monitorar métricas de latência, throughput, uso de cache, profiling periódico.
- **CI/CD:** Pipeline automatizado com testes, lint, build, deploy, rollback.
- **Testes:** Cobertura unitária, integração, E2E, mocks para integrações externas.
- **Extensibilidade:** Plugins para promoções, integrações com ERPs, gateways de pagamento.
- **Omnichannel:** API unificada para pedidos QR, app, delivery, balcão.
- **Indicadores de sucesso:** Redução de rupturas, aumento do ticket médio, engajamento em promoções, tempo médio de atendimento.

---

## 7. Recomendações Técnicas

- Garantir isolamento de dados por tenant em todas as queries.
- Implementar engine de promoções baseada em regras configuráveis.
- Automatizar relatórios e alertas.
- Investir em testes automatizados e cobertura de integrações críticas.
- Evoluir UX do menu público para pedidos rápidos, responsividade e acessibilidade.
- Documentar APIs públicas e fluxos de integração.
- Adotar arquitetura orientada a eventos para integrações e automações futuras.

---

**Resumo:**  
O sistema é robusto, modular e moderno, mas pode evoluir com automação, extensibilidade, omnichannel, práticas de mercado e indicadores claros de sucesso. O plano acima detalha as etapas para transformar o módulo restaurante em uma solução de referência no mercado multi-tenant.
