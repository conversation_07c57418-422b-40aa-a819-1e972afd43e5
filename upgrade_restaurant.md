# Planejamento de Evolução do Módulo Restaurante

## 1. Diagnóstico Técnico Detalhado

### Backend (FastAPI)
- **Robustez:** Serviços seguem padrão façade, delegando para serviços especializados (categorias, itens, variantes, modificadores). Validações rigorosas (prevenção de ciclos, integridade de dados, rollback em erros), uso de transações assíncronas, limpeza de dados órfãos após deleção em cascata.
- **Velocidade:** Queries otimizadas com selectinload/joinedload, filtros flexíveis, ordenação e paginação, mas pode evoluir com índices específicos e profiling de queries críticas.
- **Extensibilidade:** Estrutura facilita adição de hooks para integrações externas (ex: plugins de estoque, ERPs, promoções), eventos via websocket para atualização em tempo real.
- **Pontos de Atenção:** Garantir atomicidade em operações concorrentes, logging estruturado, rastreabilidade de eventos, testes de stress e concorrência.

---

## 2. Sistema de Fila: Robustez e Resiliência

### Justificativa
- **Evita falhas e conflitos:** Garante que operações críticas (pedidos, baixa de estoque, deleções em cascata, notificações) sejam processadas de forma ordenada, sem race conditions.
- **Escalabilidade:** Permite desacoplar processamento pesado (ex: geração de relatórios, envio de emails, integrações externas) do fluxo principal da aplicação.
- **Resiliência:** Possibilita retry automático em caso de falhas temporárias, evitando perda de dados.

### Exemplos de Uso

#### Fluxo de Pedido com Fila

```mermaid
sequenceDiagram
    participant Cliente
    participant MenuDigital
    participant BackendPedidos
    participant Fila
    participant Worker
    participant BackendEstoque

    Cliente->>MenuDigital: Envia pedido
    MenuDigital->>BackendPedidos: POST /orders
    BackendPedidos->>Fila: Enfileira pedido
    Worker->>Fila: Consome pedido
    Worker->>BackendEstoque: Baixa estoque
    Worker->>BackendPedidos: Atualiza status do pedido
    BackendPedidos-->>MenuDigital: Confirmação/erro
```

#### Exemplo de Integração (FastAPI + Celery + Redis)

```python
# tasks.py
from celery import Celery

celery_app = Celery('tasks', broker='redis://redis:6379/0')

@celery_app.task
def process_order(order_id):
    # lógica de baixa de estoque, atualização de status, etc.
    pass

# No endpoint FastAPI
@app.post("/orders")
async def create_order(order: OrderCreate):
    order_id = await save_order_to_db(order)
    process_order.delay(order_id)  # Enfileira tarefa
    return {"status": "processing"}
```

#### Outros usos recomendados:
- Baixa de estoque
- Geração de relatórios
- Envio de notificações (email, push)
- Integrações externas (ERPs, pagamentos)

### Tecnologias Sugeridas
- **Fila/Broker:** Redis, RabbitMQ, Amazon SQS
- **Worker:** Celery (Python), Dramatiq, RQ
- **Monitoramento:** Flower (Celery), Prometheus, Grafana

### Recomendações de Integração
- Garantir idempotência das tarefas (evitar duplicidade em caso de retry)
- Monitorar filas e workers (alertas para falhas ou backlog)
- Implementar fallback para tarefas críticas (ex: reprocessamento manual)
- Documentar fluxos assíncronos e dependências

---

## 3. Recomendações Avançadas

- **Testes de concorrência:** Simular múltiplos usuários editando menus/itens simultaneamente para garantir ausência de race conditions.
- **Stress testing:** Avaliar performance com grande volume de categorias/itens e múltiplos tenants.
- **Auditoria:** Registrar histórico de alterações (quem, quando, o quê) para rastreabilidade e compliance.
- **Extensibilidade:** Permitir plugins para promoções, integrações externas, automações customizadas por tenant.
- **Atomicidade:** Garantir que deleções em cascata e limpezas de dados órfãos sejam sempre atômicas.

---

## 4. Plano de Ação Incremental (Aprimorado)

1. **Estoque:** CRUD, movimentações, integração com pedidos, hooks para atualização automática.
2. **Livro de receitas:** Cadastro, custos, integração com estoque, rastreio de consumo por pedido.
3. **Compras:** Pedidos a fornecedores, integração estoque/financeiro, automação de reposição.
4. **Pedidos digitais:** Integração menu público + backend pedidos + acompanhamento em tempo real (websockets).
5. **Promoções:** Engine de regras, automação na finalização do pedido, plugins customizáveis.
6. **Relatórios:** Dashboards, exportação, automação de envio, logs estruturados.
7. **Customização tenant:** Temas, idiomas, regras, plugins.
8. **Permissões e auditoria:** Refinar papéis, logs de alteração, histórico.
9. **Acessibilidade e internacionalização:** Garantir padrões.
10. **Omnichannel:** Unificar pedidos QR, app, balcão, delivery.
11. **Fila:** Implementar fila para pedidos, estoque, notificações e tarefas críticas.

---

## 5. Indicadores de Sucesso

- Redução de inconsistências em operações concorrentes.
- Performance estável com grande volume de dados.
- Facilidade de integração com sistemas externos.
- Rastreabilidade total de alterações e eventos.
- Satisfação do usuário final (admin e cliente).
- Monitoramento e ausência de backlog/falhas em filas.

---

**Resumo:**  
A adoção de um sistema de fila é fundamental para garantir robustez, escalabilidade e resiliência em operações críticas do módulo restaurante. O planejamento agora inclui recomendações práticas para integração de filas, exemplos de uso e monitoramento, tornando o sistema preparado para crescimento e alta disponibilidade.
