"""Service for managing payment methods."""

import uuid  # noqa: E402
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update as sqlalchemy_update, and_

from app.modules.core.payments.models.payment_method import PaymentMethod  # noqa: E402
from app.modules.core.payments.models.payment_processor import PaymentProcessor
from app.modules.core.payments.schemas.payment_method import (
    PaymentMethodCreate,
    PaymentMethodUpdate,
)
from app.core.exceptions import BusinessLogicError  # noqa: E402


class PaymentMethodService:
    """Service for managing payment methods."""

    async def get(
        self, db: AsyncSession, id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Optional[PaymentMethod]:
        """
        Get a payment method by ID, ensuring it belongs to the specified tenant.
        """
        result = await db.execute(
            select(PaymentMethod).where(
                PaymentMethod.id == id, PaymentMethod.tenant_id == tenant_id
            )
        )
        return result.scalars().first()

    async def get_multi(
        self,
        db: AsyncSession,
        *,
        tenant_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        is_active: Optional[bool] = None,
        method_type: Optional[str] = None,
        processor_id: Optional[uuid.UUID] = None,
    ) -> List[PaymentMethod]:
        """
        Get multiple payment methods with optional filtering.
        """
        query = select(PaymentMethod).where(PaymentMethod.tenant_id == tenant_id)

        if is_active is not None:
            query = query.where(PaymentMethod.is_active == is_active)

        if method_type is not None:
            query = query.where(PaymentMethod.method_type == method_type)

        if processor_id is not None:
            query = query.where(PaymentMethod.processor_id == processor_id)

        query = (
            query.order_by(PaymentMethod.display_order, PaymentMethod.name)
            .offset(skip)
            .limit(limit)
        )

        result = await db.execute(query)
        return result.scalars().all()

    async def create(
        self, db: AsyncSession, *, tenant_id: uuid.UUID, obj_in: PaymentMethodCreate
    ) -> PaymentMethod:
        """
        Create a new payment method.

        If is_default is True, sets all other methods of the same type to is_default=False.
        If processor_id is provided, validates that it exists and belongs to the tenant.
        """
        # Check if this is the default method
        is_default = obj_in.is_default
        method_type = obj_in.method_type
        processor_id = obj_in.processor_id

        # Validate processor if provided
        if processor_id:
            processor = await self._validate_processor(db, processor_id, tenant_id)
            if not processor:
                raise BusinessLogicError(
                    f"Payment processor with id {processor_id} not found or does not belong to the tenant."  # noqa: E501
                )

        # If this is the default method, unset default on all others of the same type
        if is_default:
            await self._unset_default_methods(db, tenant_id, method_type)

        # Create the method
        create_data = obj_in.model_dump()
        if "processor_id" in create_data and create_data["processor_id"] is None:
            del create_data["processor_id"]

        db_obj = PaymentMethod(tenant_id=tenant_id, **create_data)

        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)

        return db_obj

    async def update(
        self,
        db: AsyncSession,
        *,
        id: uuid.UUID,
        tenant_id: uuid.UUID,
        obj_in: PaymentMethodUpdate,
    ) -> Optional[PaymentMethod]:
        """
        Update an existing payment method.

        If is_default is True, sets all other methods of the same type to is_default=False.
        If processor_id is provided, validates that it exists and belongs to the tenant.
        """
        method = await self.get(db, id, tenant_id)

        if not method:
            return None

        # Get update data
        update_data = obj_in.model_dump(exclude_unset=True)

        # Validate processor if provided
        processor_id = update_data.get("processor_id")
        if processor_id:
            processor = await self._validate_processor(db, processor_id, tenant_id)
            if not processor:
                raise BusinessLogicError(
                    f"Payment processor with id {processor_id} not found or does not belong to the tenant."  # noqa: E501
                )

        # Check if default status or method type is changing
        is_default = update_data.get("is_default")
        method_type = update_data.get("method_type", method.method_type)

        # If this is being set as the default method, unset default on all others of the same type
        if is_default and not method.is_default:
            await self._unset_default_methods(db, tenant_id, method_type)

        # Update the method
        for field, value in update_data.items():
            setattr(method, field, value)

        db.add(method)
        await db.commit()
        await db.refresh(method)

        return method

    async def delete(self, db: AsyncSession, *, id: uuid.UUID, tenant_id: uuid.UUID) -> bool:
        """
        Delete a payment method.

        Returns True if the method was deleted, False if it was not found.
        """
        method = await self.get(db, id, tenant_id)

        if not method:
            return False

        await db.delete(method)
        await db.commit()

        return True

    async def get_default_method(
        self, db: AsyncSession, *, tenant_id: uuid.UUID, method_type: str
    ) -> Optional[PaymentMethod]:
        """
        Get the default payment method of a specific type for a tenant.
        """
        result = await db.execute(
            select(PaymentMethod).where(
                PaymentMethod.tenant_id == tenant_id,
                PaymentMethod.method_type == method_type,
                PaymentMethod.is_default,
                PaymentMethod.is_active,
            )
        )
        return result.scalars().first()

    async def _unset_default_methods(
        self, db: AsyncSession, tenant_id: uuid.UUID, method_type: str
    ) -> None:
        """
        Unset the default flag on all methods of a specific type for a tenant.
        """
        await db.execute(
            sqlalchemy_update(PaymentMethod)
            .where(
                and_(
                    PaymentMethod.tenant_id == tenant_id,
                    PaymentMethod.method_type == method_type,
                    PaymentMethod.is_default,
                )
            )
            .values(is_default=False)
        )
        await db.commit()

    async def _validate_processor(
        self, db: AsyncSession, processor_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Optional[PaymentProcessor]:
        """
        Validate that a payment processor exists and belongs to the tenant.
        """
        result = await db.execute(
            select(PaymentProcessor).where(
                PaymentProcessor.id == processor_id,
                PaymentProcessor.tenant_id == tenant_id,
            )
        )
        return result.scalars().first()


# Instance of the service to be used in endpoints
payment_method_service = PaymentMethodService()
