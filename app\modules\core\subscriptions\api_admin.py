import uuid
from typing import List, Annotated, Optional

from fastapi import APIRouter, Depends, HTTPException, status, Path, Body
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.dependencies import get_db, require_system_role
from app.core.roles import SystemRole
from app.models.user import User
from . import schemas, services

# Roteador específico para endpoints de administração de assinaturas
router = APIRouter()

# Dependência comum para verificar se o usuário é Super Admin
AdminDep = Annotated[User, Depends(require_system_role([SystemRole.ADMIN]))]
DbDep = Annotated[AsyncSession, Depends(get_db)]

# ============================================
# Admin: Feature Management Endpoints
# ============================================


@router.post(
    "/admin/features/",
    response_model=schemas.Feature,
    status_code=status.HTTP_201_CREATED,
    summary="Admin: Criar nova Feature",
    dependencies=[Depends(require_system_role([SystemRole.ADMIN]))],
)
async def admin_create_feature(
    feature_in: schemas.FeatureCreate,
    db: DbDep,
):
    """Cria uma nova feature globalmente disponível para ser associada a planos."""
    existing_feature = await services.get_feature_by_key(db, feature_key=feature_in.key)
    if existing_feature:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"Feature com a chave '{feature_in.key}' já existe.",
        )
    return await services.create_feature(db=db, feature_in=feature_in)


@router.get(
    "/admin/features/",
    response_model=List[schemas.Feature],
    summary="Admin: Listar todas as Features",
    dependencies=[Depends(require_system_role([SystemRole.ADMIN]))],
)
async def admin_read_features(
    db: DbDep,
    skip: int = 0,
    limit: int = 100,
):
    """Lista todas as features globais cadastradas."""
    return await services.get_features(db=db, skip=skip, limit=limit)


@router.get(
    "/admin/features/{feature_id}",
    response_model=schemas.Feature,
    summary="Admin: Obter detalhes de uma Feature",
    dependencies=[Depends(require_system_role([SystemRole.ADMIN]))],
)
async def admin_read_feature(
    db: DbDep,
    feature_id: int = Path(..., title="ID da Feature"),
):
    """Obtém os detalhes de uma feature específica pelo seu ID."""
    db_feature = await services.get_feature(db, feature_id=feature_id)
    if db_feature is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Feature não encontrada")
    return db_feature


@router.put(
    "/admin/features/{feature_id}",
    response_model=schemas.Feature,
    summary="Admin: Atualizar uma Feature",
    dependencies=[Depends(require_system_role([SystemRole.ADMIN]))],
)
async def admin_update_feature(
    db: DbDep,
    feature_id: int = Path(..., title="ID da Feature a atualizar"),
    feature_in: schemas.FeatureUpdate = Body(...),
):
    """Atualiza os dados de uma feature existente."""
    db_feature = await services.get_feature(db, feature_id=feature_id)
    if db_feature is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Feature não encontrada")
    # Nota: A atualização da 'key' não é permitida aqui, pois é um identificador único.
    return await services.update_feature(db=db, db_feature=db_feature, feature_in=feature_in)


@router.delete(
    "/admin/features/{feature_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Admin: Deletar uma Feature",
    dependencies=[Depends(require_system_role([SystemRole.ADMIN]))],
)
async def admin_delete_feature(
    db: DbDep,
    feature_id: int = Path(..., title="ID da Feature a deletar"),
):
    """Deleta uma feature global (use com cautela, verificar dependências)."""
    db_feature = await services.get_feature(db, feature_id=feature_id)
    if db_feature is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Feature não encontrada")
    await services.delete_feature(db=db, db_feature=db_feature)
    return None  # Retorna 204 No Content


# ============================================
# Admin: Plan Management Endpoints
# ============================================


@router.post(
    "/admin/plans/",
    response_model=schemas.Plan,
    status_code=status.HTTP_201_CREATED,
    summary="Admin: Criar novo Plano",
    dependencies=[Depends(require_system_role([SystemRole.ADMIN]))],
)
async def admin_create_plan(
    plan_in: schemas.PlanCreate,
    db: DbDep,
):
    """Cria um novo plano de assinatura."""
    # Adicionar verificação se nome já existe?
    return await services.create_plan(db=db, plan_in=plan_in)


@router.get(
    "/admin/plans/",
    response_model=List[schemas.Plan],
    summary="Admin: Listar Planos",
    dependencies=[Depends(require_system_role([SystemRole.ADMIN]))],
)
async def admin_read_plans(
    db: DbDep,
    skip: int = 0,
    limit: int = 100,
    is_active: Optional[bool] = None,  # Filtro opcional
):
    """Lista os planos de assinatura, com opção de filtrar por ativos/inativos."""
    return await services.get_plans(db=db, skip=skip, limit=limit, is_active=is_active)


@router.get(
    "/admin/plans/{plan_id}",
    response_model=schemas.Plan,
    summary="Admin: Obter detalhes de um Plano",
    dependencies=[Depends(require_system_role([SystemRole.ADMIN]))],
)
async def admin_read_plan(
    db: DbDep,
    plan_id: int = Path(..., title="ID do Plano"),
):
    """Obtém detalhes de um plano específico, incluindo suas features vinculadas."""
    db_plan = await services.get_plan(db, plan_id=plan_id)
    if db_plan is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Plano não encontrado")
    # O schema Plan precisa ser ajustado para lidar com features_links
    # Vamos adaptar o schema de resposta ou a forma como carregamos os dados no serviço
    # Por ora, retornamos o básico. A adaptação do schema Plan para incluir features é necessária.
    # O schema atual Plan espera uma lista de PlanFeatureValue, que precisa ser construído
    plan_data = schemas.Plan.from_orm(db_plan)
    plan_data.features = [
        schemas.PlanFeatureValue(key=link.feature.key, value=link.value)
        for link in db_plan.features_links
    ]
    return plan_data


@router.put(
    "/admin/plans/{plan_id}",
    response_model=schemas.Plan,
    summary="Admin: Atualizar um Plano",
    dependencies=[Depends(require_system_role([SystemRole.ADMIN]))],
)
async def admin_update_plan(
    db: DbDep,
    plan_id: int = Path(..., title="ID do Plano a atualizar"),
    plan_in: schemas.PlanUpdate = Body(...),
):
    """Atualiza os dados de um plano existente."""
    db_plan = await services.get_plan(db, plan_id=plan_id)  # get_plan já carrega features
    if db_plan is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Plano não encontrado")
    updated_plan = await services.update_plan(db=db, db_plan=db_plan, plan_in=plan_in)
    # Reconstruir a resposta para incluir features
    plan_data = schemas.Plan.from_orm(updated_plan)
    plan_data.features = [
        schemas.PlanFeatureValue(key=link.feature.key, value=link.value)
        for link in updated_plan.features_links  # Acessa via relacionamento atualizado
    ]
    return plan_data


@router.delete(
    "/admin/plans/{plan_id}",
    response_model=schemas.Plan,  # Retorna o plano desativado
    summary="Admin: Desativar um Plano",
    dependencies=[Depends(require_system_role([SystemRole.ADMIN]))],
)
async def admin_deactivate_plan(
    db: DbDep,
    plan_id: int = Path(..., title="ID do Plano a desativar"),
):
    """Marca um plano como inativo (soft delete)."""
    db_plan = await services.get_plan(db, plan_id=plan_id)
    if db_plan is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Plano não encontrado")
    if not db_plan.is_active:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Plano já está inativo")
    # delete_plan agora desativa
    deactivated_plan = await services.delete_plan(db=db, db_plan=db_plan)
    # Reconstruir a resposta
    plan_data = schemas.Plan.from_orm(deactivated_plan)
    plan_data.features = [
        schemas.PlanFeatureValue(key=link.feature.key, value=link.value)
        for link in deactivated_plan.features_links
    ]
    return plan_data


# ============================================
# Admin: Plan Feature Link Management Endpoints
# ============================================


@router.post(
    "/admin/plans/{plan_id}/features/",
    response_model=schemas.PlanFeatureValue,  # Retorna a feature vinculada com valor
    status_code=status.HTTP_201_CREATED,
    summary="Admin: Vincular Feature a Plano",
    dependencies=[Depends(require_system_role([SystemRole.ADMIN]))],
)
async def admin_link_feature_to_plan(
    db: DbDep,  # Mover db para antes
    plan_id: int = Path(..., title="ID do Plano"),
    link_in: schemas.PlanFeatureLinkCreate = Body(...),
):
    """Vincula uma feature existente a um plano com um valor específico."""
    db_plan = await services.get_plan(db, plan_id=plan_id)
    if not db_plan:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Plano não encontrado")
    db_feature = await services.get_feature(db, feature_id=link_in.feature_id)
    if not db_feature:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Feature não encontrada")

    # TODO: Validar se o 'value' é compatível com o 'feature.value_type'

    try:
        created_link = await services.link_feature_to_plan(
            db=db, plan_id=plan_id, feature_id=link_in.feature_id, value=link_in.value
        )
        # Carrega a feature associada para retornar a chave
        await db.refresh(created_link, attribute_names=["feature"])
        return schemas.PlanFeatureValue(key=created_link.feature.key, value=created_link.value)
    except Exception:  # Captura erro de PK duplicada se o link já existir
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"Feature {link_in.feature_id} já está vinculada ao plano {plan_id}. Use PUT para atualizar.",  # noqa: E501
        )


@router.put(
    "/admin/plans/{plan_id}/features/{feature_id}",
    response_model=schemas.PlanFeatureValue,
    summary="Admin: Atualizar valor de Feature em Plano",
    dependencies=[Depends(require_system_role([SystemRole.ADMIN]))],
)
async def admin_update_plan_feature_link(
    db: DbDep,  # Mover db para antes
    plan_id: int = Path(..., title="ID do Plano"),
    feature_id: int = Path(..., title="ID da Feature"),
    update_in: schemas.PlanFeatureLinkUpdate = Body(...),
):
    """Atualiza o valor de uma feature já vinculada a um plano."""
    # TODO: Validar se o 'value' é compatível com o 'feature.value_type'
    updated_link = await services.update_plan_feature_link(
        db=db, plan_id=plan_id, feature_id=feature_id, value=update_in.value
    )
    if not updated_link:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Vínculo Feature-Plano não encontrado.",
        )
    # Carrega a feature associada para retornar a chave
    await db.refresh(updated_link, attribute_names=["feature"])
    return schemas.PlanFeatureValue(key=updated_link.feature.key, value=updated_link.value)


@router.delete(
    "/admin/plans/{plan_id}/features/{feature_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Admin: Desvincular Feature de Plano",
    dependencies=[Depends(require_system_role([SystemRole.ADMIN]))],
)
async def admin_unlink_feature_from_plan(
    db: DbDep,  # Mover db para antes
    plan_id: int = Path(..., title="ID do Plano"),
    feature_id: int = Path(..., title="ID da Feature"),
):
    """Remove o vínculo entre uma feature e um plano."""
    deleted = await services.unlink_feature_from_plan(db=db, plan_id=plan_id, feature_id=feature_id)
    if not deleted:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Vínculo Feature-Plano não encontrado.",
        )
    return None


# ============================================
# Admin: Tenant Subscription Management Endpoints
# ============================================


@router.get(
    "/admin/tenants/{tenant_id}/subscription",
    response_model=Optional[schemas.TenantSubscription],  # Pode ser nulo se não tiver assinatura
    summary="Admin: Obter Assinatura de um Tenant",
    dependencies=[Depends(require_system_role([SystemRole.ADMIN]))],
)
async def admin_get_tenant_subscription(
    db: DbDep,  # Mover db para antes dos parâmetros com valor padrão
    tenant_id: uuid.UUID = Path(..., title="ID do Tenant"),
    active_only: bool = False,  # Permite ver assinaturas inativas/antigas
):
    """Obtém a assinatura mais recente (ou ativa) de um tenant específico."""
    # TODO: Verificar se o Tenant existe?
    subscription = await services.get_tenant_subscription(
        db=db, tenant_id=tenant_id, active_only=active_only
    )
    # O schema TenantSubscription espera 'plan', que é carregado por get_tenant_subscription
    return subscription


@router.post(
    "/admin/tenants/{tenant_id}/subscription",
    response_model=schemas.TenantSubscription,
    status_code=status.HTTP_201_CREATED,
    summary="Admin: Criar/Vincular Assinatura a Tenant",
    dependencies=[Depends(require_system_role([SystemRole.ADMIN]))],
)
async def admin_assign_subscription_to_tenant(
    db: DbDep,  # Mover db para antes
    tenant_id: uuid.UUID = Path(..., title="ID do Tenant"),
    subscription_in: schemas.TenantSubscriptionAdminCreate = Body(...),
):
    """Cria ou vincula uma assinatura a um tenant (para suporte, migração, etc.)."""
    # TODO: Verificar se o Tenant existe
    db_plan = await services.get_plan(db, plan_id=subscription_in.plan_id)
    if not db_plan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Plano especificado não encontrado.",
        )
    # Lógica adicional pode ser necessária (ex: desativar assinatura antiga)
    new_subscription = await services.assign_subscription_to_tenant(
        db=db, tenant_id=tenant_id, subscription_details=subscription_in
    )
    await db.refresh(new_subscription, attribute_names=["plan"])  # Carrega o plano para a resposta
    return new_subscription


@router.put(
    "/admin/tenants/subscriptions/{subscription_id}/status",
    response_model=schemas.TenantSubscription,
    summary="Admin: Atualizar Status da Assinatura de Tenant",
    dependencies=[Depends(require_system_role([SystemRole.ADMIN]))],
)
async def admin_update_tenant_subscription_status(
    db: DbDep,  # Mover db para antes
    subscription_id: int = Path(..., title="ID da Assinatura"),
    status_update: schemas.UpdateSubscriptionStatusRequest = Body(...),
):
    """Atualiza o status de uma assinatura específica (ex: para SUSPENDED)."""
    updated_subscription = await services.update_tenant_subscription_status(
        db=db,
        subscription_id=subscription_id,
        new_status=status_update.new_status,
        reason=status_update.reason,
    )
    if not updated_subscription:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Assinatura não encontrada."
        )
    # O schema TenantSubscription espera 'plan', que é carregado por get_subscription_by_id se usado no serviço  # noqa: E501
    # Se update_tenant_subscription_status não carrega, precisamos carregar
    # aqui ou ajustar o serviço
    # Garante que o plano está carregado
    await db.refresh(updated_subscription, attribute_names=["plan"])
    return updated_subscription
