#!/usr/bin/env python3
"""
Script para testar a integração do sistema FTP com upload de imagens do menu.

Este script verifica:
1. Se o servidor FTP está rodando
2. Se os volumes estão montados corretamente
3. Se as APIs de upload estão funcionando
4. Se a estrutura de pastas está correta
"""

import asyncio
import os
import sys
import requests
from pathlib import Path

# Adicionar o diretório raiz ao path
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import get_async_session
from app.modules.core.ftp_system.services.ftp_user_service import FTPUserService
from app.modules.core.ftp_system.services.quota_service import QuotaService


async def test_ftp_system():
    """Testa o sistema FTP completo."""
    print("🔍 Testando Sistema FTP...")
    
    # 1. Verificar se o diretório FTP existe
    ftp_base_path = "/ftp_data/public"
    print(f"\n1. Verificando diretório FTP: {ftp_base_path}")
    
    if os.path.exists(ftp_base_path):
        print("✅ Diretório FTP existe")
        print(f"   Conteúdo: {os.listdir(ftp_base_path) if os.path.isdir(ftp_base_path) else 'Não é um diretório'}")
    else:
        print("❌ Diretório FTP não encontrado")
        print("   Criando diretório...")
        try:
            os.makedirs(ftp_base_path, exist_ok=True)
            print("✅ Diretório criado com sucesso")
        except Exception as e:
            print(f"❌ Erro ao criar diretório: {e}")
            return False
    
    # 2. Verificar conexão com banco de dados
    print("\n2. Verificando conexão com banco de dados...")
    try:
        async for session in get_async_session():
            quota_service = QuotaService(session)
            print("✅ Conexão com banco de dados OK")
            break
    except Exception as e:
        print(f"❌ Erro na conexão com banco: {e}")
        return False
    
    # 3. Verificar se o servidor backend está rodando
    print("\n3. Verificando servidor backend...")
    try:
        response = requests.get("http://localhost:8000/api/modules/core/ftp_system/health", timeout=5)
        if response.status_code == 200:
            print("✅ Servidor backend está rodando")
            print(f"   Resposta: {response.json()}")
        else:
            print(f"❌ Servidor backend retornou status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Erro ao conectar com servidor backend: {e}")
        return False
    
    # 4. Verificar se o servidor FTP está rodando
    print("\n4. Verificando servidor FTP...")
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(('localhost', 21))
        sock.close()
        
        if result == 0:
            print("✅ Servidor FTP está rodando na porta 21")
        else:
            print("❌ Servidor FTP não está acessível na porta 21")
            return False
    except Exception as e:
        print(f"❌ Erro ao verificar servidor FTP: {e}")
        return False
    
    # 5. Verificar estrutura de usuários FTP
    print("\n5. Verificando usuários FTP no banco...")
    try:
        async for session in get_async_session():
            ftp_service = FTPUserService(session)
            
            # Buscar todos os usuários FTP
            from sqlalchemy import select
            from app.modules.core.ftp_system.models import FTPUser
            
            result = await session.execute(select(FTPUser))
            ftp_users = result.scalars().all()
            
            print(f"   Usuários FTP encontrados: {len(ftp_users)}")
            
            for user in ftp_users:
                print(f"   - {user.username} (UUID: {user.folder_uuid})")
                
                # Verificar se a pasta do usuário existe
                user_folder = Path(ftp_base_path) / str(user.folder_uuid)
                if user_folder.exists():
                    print(f"     ✅ Pasta existe: {user_folder}")
                else:
                    print(f"     ❌ Pasta não existe: {user_folder}")
                    print(f"     Criando pasta...")
                    try:
                        user_folder.mkdir(parents=True, exist_ok=True)
                        # Criar subpastas padrão
                        for subfolder in ['images', 'videos', 'audio', 'documents', 'archives', 'text', 'other']:
                            (user_folder / subfolder).mkdir(exist_ok=True)
                        print(f"     ✅ Pasta criada com subpastas")
                    except Exception as e:
                        print(f"     ❌ Erro ao criar pasta: {e}")
            
            break
    except Exception as e:
        print(f"❌ Erro ao verificar usuários FTP: {e}")
        return False
    
    print("\n🎉 Teste do sistema FTP concluído!")
    return True


def test_frontend_connection():
    """Testa a conexão do frontend com as APIs."""
    print("\n🔍 Testando conexão do frontend...")
    
    # Verificar se o frontend está rodando
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200:
            print("✅ Frontend está rodando")
        else:
            print(f"❌ Frontend retornou status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Erro ao conectar com frontend: {e}")
        return False
    
    return True


def main():
    """Função principal."""
    print("=" * 60)
    print("🚀 TESTE DE INTEGRAÇÃO DO SISTEMA FTP")
    print("=" * 60)
    
    # Executar testes
    success = asyncio.run(test_ftp_system())
    
    if success:
        frontend_success = test_frontend_connection()
        
        if frontend_success:
            print("\n" + "=" * 60)
            print("✅ TODOS OS TESTES PASSARAM!")
            print("✅ Sistema FTP está pronto para uso")
            print("=" * 60)
            
            print("\n📋 PRÓXIMOS PASSOS:")
            print("1. Acesse http://localhost:3000/dashboard/restaurant/menu")
            print("2. Crie ou edite um item do menu")
            print("3. Teste o upload de imagens")
            print("4. Verifique se as imagens aparecem corretamente")
            
        else:
            print("\n❌ Problemas no frontend detectados")
    else:
        print("\n❌ Problemas no sistema FTP detectados")
        print("   Verifique os logs do Docker e tente novamente")


if __name__ == "__main__":
    main()
