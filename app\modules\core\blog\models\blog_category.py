"""
Blog Category Models

Category management with multi-language support and hierarchical structure.
"""

import uuid
from datetime import datetime
from typing import List, Optional

from sqlalchemy import Column, DateTime, ForeignKey, Integer, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base


class BlogCategory(Base):
    """
    Blog category model with hierarchical support.

    Supports parent-child relationships for category organization.
    Language-specific content is stored in BlogCategoryTranslation.
    """

    __tablename__ = "blog_categories"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Core identifiers
    slug = Column(String(255), unique=True, nullable=False, index=True)

    # Hierarchical structure
    parent_id = Column(UUID(as_uuid=True), ForeignKey("blog_categories.id"),
                      nullable=True)
    sort_order = Column(Integer, default=0)

    # SEO and display
    color = Column(String(7), nullable=True)  # Hex color code
    icon = Column(String(100), nullable=True)  # Icon class or URL

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow,
                       onupdate=datetime.utcnow, nullable=False)

    # Relationships
    parent = relationship("BlogCategory", remote_side=[id], back_populates="children")
    children = relationship("BlogCategory", back_populates="parent")
    posts = relationship("BlogPost", back_populates="category")
    translations = relationship("BlogCategoryTranslation", back_populates="category",
                               cascade="all, delete-orphan")

    def __repr__(self):
        return f"<BlogCategory(id={self.id}, slug='{self.slug}')>"


class BlogCategoryTranslation(Base):
    """
    Blog category translations for multi-language support.

    Stores language-specific content for blog categories.
    """

    __tablename__ = "blog_category_translations"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Foreign keys
    category_id = Column(UUID(as_uuid=True), ForeignKey("blog_categories.id"),
                        nullable=False)
    language_code = Column(String(10), nullable=False)  # e.g., 'en', 'pt-BR'

    # Content
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)

    # SEO specific content
    meta_title = Column(String(60), nullable=True)  # SEO title (max 60 chars)
    meta_description = Column(String(160), nullable=True)  # SEO desc (max 160)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow,
                       onupdate=datetime.utcnow, nullable=False)

    # Relationships
    category = relationship("BlogCategory", back_populates="translations")

    def __repr__(self):
        return f"<BlogCategoryTranslation(category_id={self.category_id}, " \
               f"language='{self.language_code}')>"
