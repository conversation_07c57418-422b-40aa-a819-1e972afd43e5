"""
Middleware para resolver o tenant a partir do cabeçalho X-Tenant-ID.
"""

from typing import Optional
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
import logging

logger = logging.getLogger(__name__)


class TenantResolverMiddleware(BaseHTTPMiddleware):
    """
    Middleware para resolver o tenant a partir do cabeçalho X-Tenant-ID.

    Este middleware extrai o ID do tenant do cabeçalho X-Tenant-ID e o armazena
    no objeto de estado da requisição para uso posterior.
    """

    async def dispatch(self, request: Request, call_next) -> Response:
        """
        Processa a requisição, extraindo o ID do tenant do cabeçalho X-Tenant-ID.

        Args:
            request: O objeto de requisição.
            call_next: A próxima função a ser chamada no pipeline de middleware.

        Returns:
            A resposta da requisição.
        """
        # Extrai o ID do tenant do cabeçalho X-Tenant-ID
        tenant_id = self._get_tenant_id_from_header(request)

        # Armazena o ID do tenant no objeto de estado da requisição
        request.state.tenant_id = tenant_id

        # Continua o processamento da requisição
        response = await call_next(request)

        return response

    def _get_tenant_id_from_header(self, request: Request) -> Optional[str]:
        """
        Extrai o ID do tenant do cabeçalho X-Tenant-ID.

        Args:
            request: O objeto de requisição.

        Returns:
            O ID do tenant, ou None se não for encontrado.
        """
        tenant_id = request.headers.get("X-Tenant-ID")

        if tenant_id:
            logger.debug(f"Tenant ID encontrado no cabeçalho: {tenant_id}")
        else:
            logger.debug("Nenhum Tenant ID encontrado no cabeçalho")

        return tenant_id
