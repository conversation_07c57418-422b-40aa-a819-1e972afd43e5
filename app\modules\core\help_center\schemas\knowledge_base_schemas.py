"""
Knowledge Base Schemas

Schemas Pydantic para validação de dados da base de conhecimento.
"""

from datetime import datetime
from typing import List, Optional
from uuid import UUID
from pydantic import BaseModel, Field


class KnowledgeBaseArticleCreate(BaseModel):
    title: str = Field(..., min_length=1, max_length=255, description="Título do artigo")
    content: str = Field(..., min_length=1, description="Conteúdo do artigo")
    category: Optional[str] = Field(None, max_length=100, description="Categoria do artigo")
    tags: Optional[str] = Field(None, max_length=500, description="Tags separadas por vírgula")
    is_public: bool = Field(True, description="Se o artigo é público")
    is_internal: bool = Field(False, description="Se o artigo é apenas para uso interno")
    is_active: bool = Field(True, description="Se o artigo está ativo")


class KnowledgeBaseArticleUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    content: Optional[str] = Field(None, min_length=1)
    category: Optional[str] = Field(None, max_length=100)
    tags: Optional[str] = Field(None, max_length=500)
    is_public: Optional[bool] = None
    is_internal: Optional[bool] = None
    is_active: Optional[bool] = None


class KnowledgeBaseArticleResponse(BaseModel):
    id: UUID
    title: str
    content: str
    category: Optional[str] = None
    tags: Optional[str] = None
    is_public: bool
    is_internal: bool
    is_active: bool
    created_by_admin_id: UUID
    view_count: int
    helpful_count: int
    not_helpful_count: int
    helpfulness_ratio: float
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    # Campos adicionais para resposta
    created_by_name: Optional[str] = None

    class Config:
        from_attributes = True


class KnowledgeBaseArticleListResponse(BaseModel):
    articles: List[KnowledgeBaseArticleResponse]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


class KnowledgeBaseSearchRequest(BaseModel):
    query: str = Field(..., min_length=1, max_length=255, description="Termo de busca")
    category: Optional[str] = Field(None, description="Filtrar por categoria")
    tags: Optional[List[str]] = Field(None, description="Filtrar por tags")
    limit: int = Field(10, ge=1, le=50, description="Limite de resultados")


class KnowledgeBaseArticleVote(BaseModel):
    article_id: UUID
    is_helpful: bool = Field(..., description="Se o artigo foi útil")


class KnowledgeBaseArticleView(BaseModel):
    article_id: UUID
    user_id: Optional[UUID] = None
    viewed_at: datetime


class KnowledgeBaseCategory(BaseModel):
    name: str
    description: Optional[str] = None
    article_count: int = 0


class KnowledgeBaseTag(BaseModel):
    name: str
    usage_count: int = 0


class KnowledgeBaseStats(BaseModel):
    total_articles: int
    public_articles: int
    internal_articles: int
    total_views: int
    total_votes: int
    average_helpfulness: float
    categories: List[KnowledgeBaseCategory]
    popular_tags: List[KnowledgeBaseTag]
