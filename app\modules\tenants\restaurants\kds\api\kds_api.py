from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from typing import List, Optional, Annotated, Any, TYPE_CHECKING
import uuid

from app.core.db_dependencies import get_db

# Use TYPE_CHECKING to avoid circular import
if TYPE_CHECKING:
    from app.modules.core.users.models.user import User
from app.modules.core.tenants.models.tenant import Tenant

from app.modules.tenants.restaurants.kds.schemas.kitchen_order import (
    KitchenOrderCreate,
    KitchenOrderRead,
    KitchenOrderUpdate,
)
from app.modules.tenants.restaurants.kds.services.kds_service import KdsService  # noqa: E402

from app.modules.core.auth.dependencies.auth_dependencies import (  # noqa: E402
    get_current_active_user,
)
from app.modules.core.tenants.dependencies.tenant_dependencies import (  # noqa: E402
    get_current_tenant_from_header,
    require_tenant_role,
)
from app.modules.core.roles.models.roles import RolePermissions
from sqlalchemy.ext.asyncio import AsyncSession

router = APIRouter()

# Define required roles for different operations
# KDS should only be accessible to staff, not customers
view_roles = RolePermissions.STAFF_ROLES  # OWNER, MANAGER, STAFF (not CUSTOMER)
write_roles = RolePermissions.ADMIN_ROLES  # Apenas papéis administrativos podem modificar


@router.post("/orders/", response_model=KitchenOrderRead, status_code=status.HTTP_201_CREATED)
async def create_kitchen_order(
    order_in: KitchenOrderCreate,
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(write_roles, tenant_id_source="header"))] = None,
    kds_service: KdsService = Depends(lambda: KdsService()),
):
    """
    Cria um novo pedido na cozinha (KDS).
    Requer papel de OWNER ou MANAGER no tenant.
    """
    return await kds_service.create_order(db=db, order_in=order_in, tenant_id=current_tenant.id)


@router.get("/orders/", response_model=List[KitchenOrderRead])
async def read_kitchen_orders(
    status: Optional[str] = Query(
        None, description="Filtrar por status (pending, preparing, ready, served)"
    ),
    skip: int = Query(0, ge=0, description="Número de registros para pular"),
    limit: int = Query(100, ge=1, le=200, description="Número máximo de registros para retornar"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(view_roles, tenant_id_source="header"))] = None,
    kds_service: KdsService = Depends(lambda: KdsService()),
):
    """
    Recupera todos os pedidos da cozinha para o tenant atual, opcionalmente filtrados por status.
    Requer papel de OWNER, MANAGER ou STAFF no tenant (customers não têm acesso).
    """
    orders = await kds_service.get_orders(
        db=db, tenant_id=current_tenant.id, status=status, skip=skip, limit=limit
    )
    return orders


@router.get("/orders/{order_id}", response_model=KitchenOrderRead)
async def read_kitchen_order(
    order_id: Annotated[uuid.UUID, Path(..., description="ID do pedido a ser recuperado")],
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(view_roles, tenant_id_source="header"))] = None,
    kds_service: KdsService = Depends(lambda: KdsService()),
):
    """
    Recupera um pedido específico da cozinha pelo ID.
    Requer papel de OWNER, MANAGER ou STAFF no tenant (customers não têm acesso).
    """
    order = await kds_service.get_order_by_id(db=db, order_id=order_id, tenant_id=current_tenant.id)
    if order is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Pedido não encontrado",
        )
    return order


@router.patch("/orders/{order_id}/status", response_model=KitchenOrderRead)
async def update_kitchen_order_status(
    order_id: Annotated[uuid.UUID, Path(..., description="ID do pedido a ser atualizado")],
    order_update: KitchenOrderUpdate,
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(write_roles, tenant_id_source="header"))] = None,
    kds_service: KdsService = Depends(lambda: KdsService()),
):
    """
    Atualiza o status e/ou detalhes de um pedido da cozinha.
    Requer papel de OWNER ou MANAGER no tenant.
    """
    if not order_update.status and not order_update.order_details:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Status ou order_details devem ser fornecidos",
        )

    updated_order = await kds_service.update_order(
        db=db,
        order_id=order_id,
        tenant_id=current_tenant.id,
        new_status=order_update.status,
        new_order_details=order_update.order_details,
    )
    if updated_order is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Pedido não encontrado",
        )
    return updated_order


@router.get(
    "/status",
    status_code=status.HTTP_200_OK,
    summary="Get KDS Status",
    description="Get the current status of the Kitchen Display System.",
)
async def get_kds_status(
    db: AsyncSession = Depends(get_db),
    current_user: "User" = Depends(get_current_active_user),
) -> dict:
    """
    Get the current status of the Kitchen Display System.
    """
    return {"status": "active", "message": "KDS is operational"}


@router.get(
    "/orders",
    response_model=List[dict],
    status_code=status.HTTP_200_OK,
    summary="Get KDS Orders",
    description="Get orders displayed on the Kitchen Display System.",
)
async def get_kds_orders(
    db: AsyncSession = Depends(get_db),
    current_user: "User" = Depends(get_current_active_user),
    status_filter: Optional[str] = None,
) -> List[dict]:
    """
    Get orders displayed on the Kitchen Display System.
    """
    # Placeholder implementation
    return []
