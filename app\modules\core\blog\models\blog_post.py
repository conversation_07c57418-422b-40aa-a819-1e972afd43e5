"""
Blog Post Models

Main blog post model with multi-language support and SEO optimization.
"""

import uuid
from datetime import datetime
from typing import List, Optional
from enum import Enum

from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    Foreign<PERSON>ey,
    Integer,
    String,
    Text,
    Table,
    Enum as SQLEnum,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base
from app.modules.core.functions.media_system.models import MediaUpload


class PostVisibility(str, Enum):
    """
    Post visibility levels for access control.
    """
    PUBLIC = "public"          # Any user (authenticated or anonymous) can read
    PRIVATE = "private"        # Only registered/authenticated users can read
    MEMBER_ONLY = "member_only"  # Only users with subscriber status can read

# Association table for blog post tags (many-to-many)
blog_post_tags = Table(
    "blog_post_tags",
    Base.metadata,
    Column("blog_post_id", UUID(as_uuid=True), ForeignKey("blog_posts.id")),
    Column("blog_tag_id", UUID(as_uuid=True), Foreign<PERSON>ey("blog_tags.id")),
)


class BlogPost(Base):
    """
    Main blog post model.

    This model stores the core blog post information that is language-independent.
    Language-specific content is stored in BlogPostTranslation.
    """

    __tablename__ = "blog_posts"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Core identifiers
    slug = Column(String(255), unique=True, nullable=False, index=True)

    # Relationships
    author_id = Column(UUID(as_uuid=True), ForeignKey("blog_authors.id"),
                      nullable=False)
    category_id = Column(UUID(as_uuid=True), ForeignKey("blog_categories.id"),
                        nullable=True)
    featured_image_id = Column(UUID(as_uuid=True), ForeignKey("media_uploads.id"),
                               nullable=True)

    # Publishing information
    status = Column(String(20), default="draft", nullable=False)
    # draft, published, scheduled, archived
    is_featured = Column(Boolean, default=False)

    # Access control
    visibility = Column(String(20), default=PostVisibility.PUBLIC.value, nullable=False)
    # public, private, member_only

    # Scheduling
    published_at = Column(DateTime, nullable=True)
    scheduled_at = Column(DateTime, nullable=True)

    # SEO and Analytics
    view_count = Column(Integer, default=0)
    like_count = Column(Integer, default=0)
    comment_count = Column(Integer, default=0)

    # Media - DEPRECATED, kept for potential data migration
    # featured_image_url = Column(String(500), nullable=True)
    # featured_image_alt = Column(String(255), nullable=True)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow,
                       onupdate=datetime.utcnow, nullable=False)

    # Relationships
    author = relationship("BlogAuthor", back_populates="posts")
    category = relationship("BlogCategory", back_populates="posts")
    featured_image = relationship("MediaUpload")
    tags = relationship("BlogTag", secondary=blog_post_tags,
                       back_populates="posts")
    translations = relationship("BlogPostTranslation", back_populates="post",
                               cascade="all, delete-orphan")
    comments = relationship("BlogComment", back_populates="post",
                           cascade="all, delete-orphan")
    seo = relationship("BlogSEO", back_populates="post", uselist=False,
                      cascade="all, delete-orphan")

    def __repr__(self):
        return f"<BlogPost(id={self.id}, slug='{self.slug}')>"


class BlogPostTranslation(Base):
    """
    Blog post translations for multi-language support.

    Stores language-specific content for blog posts.
    """

    __tablename__ = "blog_post_translations"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Foreign keys
    post_id = Column(UUID(as_uuid=True), ForeignKey("blog_posts.id"),
                    nullable=False)
    language_code = Column(String(10), nullable=False)  # e.g., 'en', 'pt-BR'

    # Content
    title = Column(String(255), nullable=False)
    subtitle = Column(String(500), nullable=True)
    excerpt = Column(Text, nullable=True)
    content = Column(Text, nullable=False)

    # SEO specific content
    meta_title = Column(String(60), nullable=True)  # SEO title (max 60 chars)
    meta_description = Column(String(160), nullable=True)  # SEO desc (max 160)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow,
                       onupdate=datetime.utcnow, nullable=False)

    # Relationships
    post = relationship("BlogPost", back_populates="translations")

    # Unique constraint for post + language
    __table_args__ = (
        {"schema": None},
    )

    def __repr__(self):
        return f"<BlogPostTranslation(post_id={self.post_id}, " \
               f"language='{self.language_code}')>"
