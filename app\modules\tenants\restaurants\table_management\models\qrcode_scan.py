"""
QR code scan model for restaurant table management.
"""

import uuid
from typing import TYPE_CHECKING, List, Optional
from sqlalchemy import Column, String, <PERSON>olean, ForeignKey, DateTime
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import Base

# Use TYPE_CHECKING to handle circular dependencies for type hinting
if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.core.users.models.user import User
    from app.modules.shared.crm.models.account import Account
    from app.modules.tenants.restaurants.table_management.models.table import Table
    from app.modules.tenants.restaurants.table_management.models.universal_qrcode import (
        UniversalQRCode,
    )


class QRCodeScan(Base):
    """
    Model for tracking QR code scans.

    This model is used for analytics and tracking purposes.
    It records when a QR code is scanned, by whom, and whether it resulted in an order.
    """

    __tablename__ = "restaurant_qrcode_scans"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    table_id = Column(
        UUID(as_uuid=True), ForeignKey("restaurant_tables.id"), nullable=True, index=True
    )
    universal_qrcode_id = Column(
        UUID(as_uuid=True), ForeignKey("restaurant_universal_qrcodes.id"), nullable=True, index=True
    )
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    customer_id = Column(UUID(as_uuid=True), ForeignKey("crm_accounts.id"), nullable=True)
    scan_time = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False, index=True
    )
    ip_address = Column(String(50), nullable=True)
    user_agent = Column(String(255), nullable=True)
    session_id = Column(String(100), nullable=True)
    resulted_in_order = Column(Boolean, nullable=False, default=False)

    # Relationships
    tenant = relationship("app.modules.core.tenants.models.tenant.Tenant", viewonly=True)
    table = relationship(
        "app.modules.tenants.restaurants.table_management.models.table.Table",
        back_populates="qrcode_scans",
    )
    universal_qrcode = relationship(
        "app.modules.tenants.restaurants.table_management.models.universal_qrcode.UniversalQRCode",
        back_populates="scans",
    )
    user = relationship("app.modules.core.users.models.user.User", viewonly=True)
    customer = relationship("app.modules.shared.crm.models.account.Account", viewonly=True)

    def __repr__(self):
        return f"<QRCodeScan(id={self.id}, scan_time='{self.scan_time}')>"
