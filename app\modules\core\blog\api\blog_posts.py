"""
Blog Posts API

REST API endpoints for blog post management.
"""

import uuid
from typing import List, Optional, TYPE_CHECKING
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.auth_dependencies import get_optional_current_user

if TYPE_CHECKING:
    from app.modules.core.users.models.user import User

from ..dependencies.access_control import (
    require_admin_permission,
    check_post_access_permission,
    BlogAccessControl
)
from ..schemas.blog_post import (
    BlogPostCreate,
    BlogPostUpdate,
    BlogPostRead,
    BlogPostList,
    BlogPostSearchResult,
)
from ..services.blog_post_service import BlogPostService

router = APIRouter()
blog_post_service = BlogPostService()


@router.get("/", response_model=List[BlogPostList])
async def get_posts(
    skip: int = Query(0, ge=0, description="Number of posts to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of posts to return"),
    status: Optional[str] = Query(None, description="Filter by post status"),
    category_id: Optional[uuid.UUID] = Query(None, description="Filter by category"),
    tag_id: Optional[uuid.UUID] = Query(None, description="Filter by tag"),
    author_id: Optional[uuid.UUID] = Query(None, description="Filter by author"),
    language: Optional[str] = Query(None, description="Language code"),
    is_featured: Optional[bool] = Query(None, description="Filter featured posts"),
    order_by: str = Query("created_at", description="Field to order by"),
    order_direction: str = Query("desc", regex="^(asc|desc)$"),
    db: AsyncSession = Depends(get_db),
    current_user: Optional["User"] = Depends(get_optional_current_user),
):
    """
    Get blog posts with filtering and pagination.

    Supports filtering by status, category, tag, author, language, and featured status.
    Results are paginated and can be ordered by various fields.
    """
    posts = await blog_post_service.get_posts(
        db=db,
        skip=skip,
        limit=limit,
        status=status,
        category_id=category_id,
        tag_id=tag_id,
        author_id=author_id,
        language_code=language,
        is_featured=is_featured,
        order_by=order_by,
        order_direction=order_direction,
        current_user=current_user,
    )

    return posts


@router.get("/{post_id}", response_model=BlogPostRead)
async def get_post(
    post_id: uuid.UUID,
    language: Optional[str] = Query(None, description="Language code"),
    db: AsyncSession = Depends(get_db),
    current_user: Optional["User"] = Depends(get_optional_current_user),
):
    """
    Get a specific blog post by ID.

    Optionally filter translations to a specific language.
    Automatically increments view count.
    """
    post = await blog_post_service.get_post_by_id(
        db=db,
        post_id=post_id,
        language_code=language
    )

    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Blog post not found"
        )

    # Check if user has permission to view this post
    has_access = await check_post_access_permission(post, current_user)
    if not has_access:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have permission to view this post"
        )

    # Increment view count
    await blog_post_service.increment_view_count(db=db, post_id=post_id)

    return post


@router.get("/slug/{slug}", response_model=BlogPostRead)
async def get_post_by_slug(
    slug: str,
    language: Optional[str] = Query(None, description="Language code"),
    db: AsyncSession = Depends(get_db),
    current_user: Optional["User"] = Depends(get_optional_current_user),
):
    """
    Get a specific blog post by slug.

    Optionally filter translations to a specific language.
    Automatically increments view count.
    """
    post = await blog_post_service.get_post_by_slug(
        db=db,
        slug=slug,
        language_code=language
    )

    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Blog post not found"
        )

    # Check if user has permission to view this post
    has_access = await check_post_access_permission(post, current_user)
    if not has_access:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have permission to view this post"
        )

    # Increment view count
    await blog_post_service.increment_view_count(db=db, post_id=post.id)

    return post


@router.post("/", response_model=BlogPostRead, status_code=status.HTTP_201_CREATED)
async def create_post(
    post_data: BlogPostCreate,
    db: AsyncSession = Depends(get_db),
    current_user: "User" = Depends(require_admin_permission),
):
    """
    Create a new blog post.

    Requires authentication. The post will be created with the specified author.
    Must include at least one translation.
    """
    try:
        post = await blog_post_service.create_post(
            db=db,
            post_data=post_data,
            current_user_id=current_user.id,
        )
        return post
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create blog post: {str(e)}"
        )


@router.put("/{post_id}", response_model=BlogPostRead)
async def update_post(
    post_id: uuid.UUID,
    post_data: BlogPostUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: "User" = Depends(require_admin_permission),
):
    """
    Update a blog post.

    Requires authentication. Only the author or admin can update a post.
    """
    # Check if post exists and user has permission
    existing_post = await blog_post_service.get_post_by_id(db=db, post_id=post_id)
    if not existing_post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Blog post not found"
        )

    # TODO: Add permission check (author or admin)

    updated_post = await blog_post_service.update_post(
        db=db,
        post_id=post_id,
        post_data=post_data,
    )

    if not updated_post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Blog post not found"
        )

    return updated_post


@router.delete("/{post_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_post(
    post_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_user: "User" = Depends(require_admin_permission),
):
    """
    Delete a blog post.

    Requires authentication. Only the author or admin can delete a post.
    """
    # Check if post exists and user has permission
    existing_post = await blog_post_service.get_post_by_id(db=db, post_id=post_id)
    if not existing_post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Blog post not found"
        )

    # TODO: Add permission check (author or admin)

    success = await blog_post_service.delete_post(db=db, post_id=post_id)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Blog post not found"
        )


@router.get("/{post_id}/related", response_model=List[BlogPostList])
async def get_related_posts(
    post_id: uuid.UUID,
    limit: int = Query(5, ge=1, le=20, description="Number of related posts"),
    db: AsyncSession = Depends(get_db),
):
    """
    Get related blog posts.

    Returns posts that share tags or category with the specified post.
    """
    related_posts = await blog_post_service.get_related_posts(
        db=db,
        post_id=post_id,
        limit=limit
    )

    return related_posts
