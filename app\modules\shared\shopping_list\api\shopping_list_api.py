"""Shopping List API endpoints for tenant-specific shopping list management."""

from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi import status as http_status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, TYPE_CHECKING
import uuid

from app.core.db_dependencies import get_db
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    get_current_tenant,
    require_tenant_role,
)
from app.modules.core.roles.models.roles import RolePermissions

from app.modules.shared.shopping_list.models.shopping_list import (
    ShoppingListStatus,
    ShoppingListItemStatus,
    ShoppingListItemPriority
)
from app.modules.shared.shopping_list.schemas.shopping_list import (
    ShoppingListCreate,
    ShoppingListUpdate,
    ShoppingListRead,
    ShoppingListWithItems,
    ShoppingListItemCreate,
    ShoppingListItemUpdate,
    ShoppingListItemRead,
    ShoppingListSummary,
    AutoGenerateShoppingListRequest
)
from app.modules.shared.shopping_list.schemas.shopping_list_category import (
    ShoppingListCategoryCreate,
    ShoppingListCategoryUpdate,
    ShoppingListCategoryRead
)
from app.modules.shared.shopping_list.services.shopping_list_service import (
    shopping_list_service
)
from app.modules.shared.shopping_list.services.shopping_list_category_service import (
    shopping_list_category_service
)
from app.modules.core.functions.inventory.services.inventory_integration_service import (
    inventory_integration_service
)

if TYPE_CHECKING:
    from app.modules.core.users.models.user import User

router = APIRouter(prefix="/shopping-lists", tags=["Shopping Lists"])


@router.get(
    "/",
    response_model=List[ShoppingListSummary],
    summary="List Shopping Lists",
    description="Get all shopping lists for the current tenant with summary information.",
)
async def get_shopping_lists(
    status: Optional[ShoppingListStatus] = Query(None, description="Filter by status"),
    auto_generated: Optional[bool] = Query(None, description="Filter by auto-generated"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    db: AsyncSession = Depends(get_db),
    current_tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
):
    """Get shopping lists for the current tenant."""
    try:
        shopping_lists = await shopping_list_service.get_shopping_lists(
            db=db,
            tenant_id=current_tenant.id,
            status=status,
            auto_generated=auto_generated,
            skip=skip,
            limit=limit
        )

        # Convert to summary format
        summaries = []
        for shopping_list in shopping_lists:
            # Count items by status
            items_count = len(shopping_list.items) if shopping_list.items else 0
            pending_items = sum(1 for item in (shopping_list.items or []) if item.status == ShoppingListItemStatus.PENDING)
            ordered_items = sum(1 for item in (shopping_list.items or []) if item.status == ShoppingListItemStatus.ORDERED)
            received_items = sum(1 for item in (shopping_list.items or []) if item.status == ShoppingListItemStatus.RECEIVED)
            high_priority_items = sum(1 for item in (shopping_list.items or []) if item.priority in [ShoppingListItemPriority.HIGH, ShoppingListItemPriority.URGENT])

            summary = ShoppingListSummary(
                id=shopping_list.id,
                name=shopping_list.name,
                status=shopping_list.status,
                created_date=shopping_list.created_date,
                target_date=shopping_list.target_date,
                estimated_total=shopping_list.estimated_total,
                items_count=items_count,
                pending_items=pending_items,
                ordered_items=ordered_items,
                received_items=received_items,
                high_priority_items=high_priority_items,
                auto_generated=shopping_list.auto_generated
            )
            summaries.append(summary)

        return summaries

    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch shopping lists: {str(e)}"
        )


@router.get(
    "/{list_id}",
    response_model=ShoppingListWithItems,
    summary="Get Shopping List",
    description="Get a specific shopping list with all its items.",
)
async def get_shopping_list(
    list_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
):
    """Get a specific shopping list with items."""
    try:
        shopping_list = await shopping_list_service.get_shopping_list_by_id(
            db=db,
            list_id=list_id,
            tenant_id=current_tenant.id
        )

        if not shopping_list:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Shopping list not found"
            )

        # Manually construct response to avoid greenlet issues
        return ShoppingListWithItems(
            id=shopping_list.id,
            tenant_id=shopping_list.tenant_id,
            name=shopping_list.name,
            description=shopping_list.description,
            created_at=shopping_list.created_at,
            updated_at=shopping_list.updated_at,
            items=[ShoppingListItemRead.model_validate(item) for item in shopping_list.items]
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch shopping list: {str(e)}"
        )


@router.get(
    "/{list_id}/items",
    response_model=List[ShoppingListItemRead],
    summary="Get Shopping List Items",
    description="Get all items for a specific shopping list.",
)
async def get_shopping_list_items(
    list_id: uuid.UUID,
    status: Optional[ShoppingListItemStatus] = Query(None, description="Filter by status"),
    priority: Optional[ShoppingListItemPriority] = Query(None, description="Filter by priority"),
    db: AsyncSession = Depends(get_db),
    current_tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
):
    """Get items for a shopping list."""
    try:
        items = await shopping_list_service.get_shopping_list_items(
            db=db,
            list_id=list_id,
            tenant_id=current_tenant.id,
            status=status,
            priority=priority
        )

        return [ShoppingListItemRead.from_orm(item) for item in items]

    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch shopping list items: {str(e)}"
        )


@router.get(
    "/low-stock-items",
    response_model=List[dict],
    summary="Get Low Stock Items",
    description="Get inventory items that are below their minimum stock threshold.",
)
async def get_low_stock_items(
    include_categories: Optional[List[str]] = Query(None, description="Categories to include"),
    exclude_categories: Optional[List[str]] = Query(None, description="Categories to exclude"),
    db: AsyncSession = Depends(get_db),
    current_tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
):
    """Get low stock items that can be added to shopping lists."""
    try:
        low_stock_items = await inventory_integration_service.get_low_stock_items(
            db=db,
            tenant_id=current_tenant.id,
            include_categories=include_categories,
            exclude_categories=exclude_categories
        )

        return low_stock_items

    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch low stock items: {str(e)}"
        )


# === Shopping List Item CRUD Endpoints ===

@router.post(
    "/{list_id}/items",
    response_model=ShoppingListItemRead,
    status_code=http_status.HTTP_201_CREATED,
    summary="Create Shopping List Item",
    description="Create a new item in a shopping list.",
)
async def create_shopping_list_item(
    list_id: uuid.UUID,
    item_data: ShoppingListItemCreate,
    db: AsyncSession = Depends(get_db),
    current_tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
):
    """Create a new shopping list item."""
    try:
        # Verify list exists and belongs to tenant
        shopping_list = await shopping_list_service.get_shopping_list_by_id(
            db=db, list_id=list_id, tenant_id=current_tenant.id
        )
        if not shopping_list:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Shopping list not found"
            )

        item = await shopping_list_service.create_shopping_list_item(
            db=db,
            list_id=list_id,
            item_data=item_data,
            tenant_id=current_tenant.id
        )

        return ShoppingListItemRead.from_orm(item)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create shopping list item: {str(e)}"
        )


@router.get(
    "/items/{item_id}",
    response_model=ShoppingListItemRead,
    summary="Get Shopping List Item",
    description="Get a specific shopping list item by ID.",
)
async def get_shopping_list_item(
    item_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
):
    """Get a specific shopping list item."""
    try:
        item = await shopping_list_service.get_shopping_list_item_by_id(
            db=db,
            item_id=item_id,
            tenant_id=current_tenant.id
        )

        if not item:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Shopping list item not found"
            )

        return ShoppingListItemRead.from_orm(item)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch shopping list item: {str(e)}"
        )


@router.put(
    "/items/{item_id}",
    response_model=ShoppingListItemRead,
    summary="Update Shopping List Item",
    description="Update a shopping list item.",
)
async def update_shopping_list_item(
    item_id: uuid.UUID,
    item_data: ShoppingListItemUpdate,
    db: AsyncSession = Depends(get_db),
    current_tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
):
    """Update a shopping list item."""
    try:
        item = await shopping_list_service.update_shopping_list_item(
            db=db,
            item_id=item_id,
            item_data=item_data,
            tenant_id=current_tenant.id
        )

        if not item:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Shopping list item not found"
            )

        return ShoppingListItemRead.from_orm(item)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update shopping list item: {str(e)}"
        )


@router.delete(
    "/items/{item_id}",
    status_code=http_status.HTTP_204_NO_CONTENT,
    summary="Delete Shopping List Item",
    description="Delete a shopping list item.",
)
async def delete_shopping_list_item(
    item_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
):
    """Delete a shopping list item."""
    try:
        success = await shopping_list_service.delete_shopping_list_item(
            db=db,
            item_id=item_id,
            tenant_id=current_tenant.id
        )

        if not success:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Shopping list item not found"
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete shopping list item: {str(e)}"
        )


@router.post(
    "/auto-generate",
    response_model=ShoppingListRead,
    status_code=http_status.HTTP_201_CREATED,
    summary="Auto-Generate Shopping List",
    description="Auto-generate shopping list from low stock inventory items.",
)
async def auto_generate_shopping_list(
    request: AutoGenerateShoppingListRequest,
    db: AsyncSession = Depends(get_db),
    current_tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
):
    """Auto-generate shopping list from low stock items."""
    try:
        shopping_list = await inventory_integration_service.auto_generate_shopping_list(
            db=db,
            request=request,
            tenant_id=current_tenant.id,
            created_by=current_user.id
        )

        return ShoppingListRead.from_orm(shopping_list)

    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to auto-generate shopping list: {str(e)}"
        )


@router.post(
    "/test-integration",
    response_model=dict,
    summary="Test Integration",
    description="Test inventory-shopping list integration status.",
)
async def test_integration(
    db: AsyncSession = Depends(get_db),
    current_tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
):
    """Test integration functionality."""
    try:
        # Integration service is already imported at module level
        # Simple validation without complex queries
        return {
            "status": "success",
            "message": "Integration services are available",
            "tenant_id": str(current_tenant.id),
            "services": {
                "inventory_integration_service": "available",
                "shopping_list_service": "available"
            },
            "integration_features": {
                "auto_add_low_stock_items": "implemented",
                "sync_inventory_to_shopping_list": "implemented",
                "update_inventory_from_shopping_list": "implemented",
                "check_and_auto_generate_shopping_lists": "implemented"
            }
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"Integration test failed: {str(e)}",
            "tenant_id": str(current_tenant.id)
        }


@router.post(
    "/test-auto-add-low-stock",
    response_model=dict,
    summary="Test Auto Add Low Stock Items",
    description="Test automatic addition of low stock items to shopping list.",
)
async def test_auto_add_low_stock(
    threshold: int = Query(10, ge=1, le=100, description="Stock threshold"),
    db: AsyncSession = Depends(get_db),
    current_tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
):
    """Test automatic addition of low stock items."""
    try:
        # Integration service is already imported at module level
        # Test auto add low stock items
        added_items = await inventory_integration_service.auto_add_low_stock_items(
            db=db,
            tenant_id=current_tenant.id,
            threshold=threshold
        )

        return {
            "status": "success",
            "message": f"Successfully tested auto-add low stock items",
            "tenant_id": str(current_tenant.id),
            "threshold_used": threshold,
            "items_added": len(added_items),
            "added_items": [
                {
                    "id": str(item.id),
                    "name": item.name,
                    "quantity": item.quantity,
                    "priority": item.priority,
                    "notes": item.notes
                } for item in added_items
            ]
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"Auto-add test failed: {str(e)}",
            "tenant_id": str(current_tenant.id),
            "threshold_used": threshold
        }


# === Shopping List Category Endpoints ===

@router.get(
    "/categories",
    response_model=List[ShoppingListCategoryRead],
    summary="List Shopping List Categories",
    description="Get all shopping list categories for the current tenant.",
)
async def list_shopping_list_categories(
    active_only: bool = Query(True, description="Only active categories"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    db: AsyncSession = Depends(get_db),
    current_tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
):
    """
    List shopping list categories for the current tenant.
    Requires STAFF, MANAGER or OWNER role.
    """
    try:
        categories = await shopping_list_category_service.get_categories(
            db=db,
            tenant_id=current_tenant.id,
            active_only=active_only,
            skip=skip,
            limit=limit
        )
        return categories
    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch categories: {str(e)}"
        )


@router.post(
    "/categories",
    response_model=ShoppingListCategoryRead,
    status_code=http_status.HTTP_201_CREATED,
    summary="Create Shopping List Category",
    description="Create a new shopping list category.",
)
async def create_shopping_list_category(
    category_data: ShoppingListCategoryCreate,
    db: AsyncSession = Depends(get_db),
    current_tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.ADMIN_ROLES, tenant_id_source="header")
    ),
):
    """
    Create a new shopping list category.
    Requires MANAGER or OWNER role.
    """
    try:
        category = await shopping_list_category_service.create_category(
            db=db,
            category_data=category_data,
            tenant_id=current_tenant.id
        )
        return category
    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create category: {str(e)}"
        )


@router.get(
    "/categories/{category_id}",
    response_model=ShoppingListCategoryRead,
    summary="Get Shopping List Category",
    description="Get details of a specific shopping list category.",
)
async def get_shopping_list_category(
    category_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
):
    """
    Get category by ID.
    Requires STAFF, MANAGER or OWNER role.
    """
    try:
        category = await shopping_list_category_service.get_category_by_id(
            db=db,
            category_id=category_id,
            tenant_id=current_tenant.id
        )
        if not category:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Category not found"
            )
        return category
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch category: {str(e)}"
        )


@router.put(
    "/categories/{category_id}",
    response_model=ShoppingListCategoryRead,
    summary="Update Shopping List Category",
    description="Update a shopping list category.",
)
async def update_shopping_list_category(
    category_id: uuid.UUID,
    category_data: ShoppingListCategoryUpdate,
    db: AsyncSession = Depends(get_db),
    current_tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.ADMIN_ROLES, tenant_id_source="header")
    ),
):
    """
    Update category.
    Requires MANAGER or OWNER role.
    """
    try:
        category = await shopping_list_category_service.update_category(
            db=db,
            category_id=category_id,
            category_data=category_data,
            tenant_id=current_tenant.id
        )
        return category
    except Exception as e:
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Category not found"
            )
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update category: {str(e)}"
        )


@router.delete(
    "/categories/{category_id}",
    response_model=ShoppingListCategoryRead,
    summary="Delete Shopping List Category",
    description="Delete a shopping list category (soft delete).",
)
async def delete_shopping_list_category(
    category_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.ADMIN_ROLES, tenant_id_source="header")
    ),
):
    """
    Delete category (soft delete).
    Requires MANAGER or OWNER role.
    """
    try:
        category = await shopping_list_category_service.delete_category(
            db=db,
            category_id=category_id,
            tenant_id=current_tenant.id
        )
        return category
    except Exception as e:
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Category not found"
            )
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete category: {str(e)}"
        )

