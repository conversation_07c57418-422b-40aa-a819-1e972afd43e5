from fastapi import APIRouter

from app.modules.core.tenants.api.endpoints import tenants_router, settings_router, public_tenant_router
from app.modules.core.tenants.api.endpoints.admin_view import router as admin_view_router

router = APIRouter()

# Include tenants endpoints
router.include_router(tenants_router, tags=["tenants"])

# Include admin view endpoints
router.include_router(admin_view_router, prefix="/admin", tags=["admin-tenant-view"])

# Include tenant settings endpoints with new structure
router.include_router(settings_router, prefix="/settings", tags=["tenant-settings"])

# Backward compatibility: Include settings router again with old prefix
# This maintains compatibility with existing frontend calls
router.include_router(
    settings_router,
    prefix="/tenant-settings",
    tags=["tenant-settings-legacy"],
    deprecated=True
)
