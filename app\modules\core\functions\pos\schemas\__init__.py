"""Schemas for POS module."""

from .cash_register import CashRegisterBase, CashRegisterCreate, CashRegisterRead  # noqa: E402
from .sale_transaction import (
    SaleTransactionBase,
    SaleTransactionCreate,
    SaleTransactionRead,
    SaleItemCreate,
)
from .refund import RefundBase, RefundCreate, RefundRead  # noqa: E402
from .cash_register_session import (
    CashRegisterSessionOpenBase,
    CashRegisterSessionOpen,
    CashRegisterSessionCloseBase,
    CashRegisterSessionClose,
    CashRegisterSessionRead,
)
from .payment_method import (  # noqa: E402
    PaymentMethodBase,
    PaymentMethodCreate,
    PaymentMethodUpdate,
    PaymentMethodRead,
    TransactionPaymentBase,
    TransactionPaymentCreate,
    TransactionPaymentRead,
    TransactionPaymentWithMethodRead,
)

__all__ = [
    # Cash Register
    "CashRegisterBase",
    "CashRegisterCreate",
    "CashRegisterRead",
    # Sale Transaction
    "SaleTransactionBase",
    "SaleTransactionCreate",
    "SaleTransactionRead",
    "SaleItemCreate",
    # Refund
    "RefundBase",
    "RefundCreate",
    "RefundRead",
    # Cash Register Session
    "CashRegisterSessionOpenBase",
    "CashRegisterSessionOpen",
    "CashRegisterSessionCloseBase",
    "CashRegisterSessionClose",
    "CashRegisterSessionRead",
    # Payment Method
    "PaymentMethodBase",
    "PaymentMethodCreate",
    "PaymentMethodUpdate",
    "PaymentMethodRead",
    "TransactionPaymentBase",
    "TransactionPaymentCreate",
    "TransactionPaymentRead",
    "TransactionPaymentWithMethodRead",
]
