# General - Invoices

**Categoria:** General
**Módulo:** Invoices
**Total de Endpoints:** 6
**Gerado em:** 27/06/2025, 20:20:26

## 📋 Endpoints

- [GET /api/financial/invoices/](#get-apifinancialinvoices) - Get Invoices
- [POST /api/financial/invoices/](#post-apifinancialinvoices) - Create Invoice
- [DELETE /api/financial/invoices/{invoice_id}](#delete-apifinancialinvoicesinvoice-id) - Delete Invoice
- [GET /api/financial/invoices/{invoice_id}](#get-apifinancialinvoicesinvoice-id) - Get Invoice
- [PUT /api/financial/invoices/{invoice_id}](#put-apifinancialinvoicesinvoice-id) - Update Invoice
- [POST /api/financial/invoices/{invoice_id}/generate-pdf](#post-apifinancialinvoicesinvoice-idgenerate-pdf) - Generate Invoice Pdf

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### InvoiceCreate

**Descrição:** Schema for creating invoices.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `invoice_type` | InvoiceType | ✅ | Type of invoice |
| `customer_name` | string | ✅ | Customer name |
| `customer_email` | unknown | ❌ | Customer email |
| `customer_phone` | unknown | ❌ | Customer phone |
| `customer_address` | unknown | ❌ | Customer address |
| `customer_tax_id` | unknown | ❌ | Customer tax ID |
| `issue_date` | string | ✅ | Invoice issue date |
| `due_date` | unknown | ❌ | Payment due date |
| `tax_rate` | unknown | ❌ | Default tax rate percentage |
| `discount_amount` | unknown | ❌ | Discount amount |
| `payment_method` | unknown | ❌ | Payment method |
| `notes` | unknown | ❌ | Invoice notes |
| `terms_and_conditions` | unknown | ❌ | Terms and conditions |
| `order_id` | unknown | ❌ | Related order ID |
| `vendor_id` | unknown | ❌ | B2B vendor ID |
| `customer_b2b_id` | unknown | ❌ | B2B customer ID |
| `notify_on_view` | boolean | ❌ | Notify when invoice is viewed |
| `notify_on_due` | boolean | ❌ | Notify when invoice is due |
| `items` | Array[InvoiceItemCreate] | ❌ | Invoice items |

### InvoiceRead

**Descrição:** Schema for reading invoices.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `invoice_type` | InvoiceType | ✅ | Type of invoice |
| `customer_name` | string | ✅ | Customer name |
| `customer_email` | unknown | ❌ | Customer email |
| `customer_phone` | unknown | ❌ | Customer phone |
| `customer_address` | unknown | ❌ | Customer address |
| `customer_tax_id` | unknown | ❌ | Customer tax ID |
| `issue_date` | string | ✅ | Invoice issue date |
| `due_date` | unknown | ❌ | Payment due date |
| `tax_rate` | string | ❌ | Default tax rate percentage |
| `discount_amount` | string | ❌ | Discount amount |
| `payment_method` | unknown | ❌ | Payment method |
| `notes` | unknown | ❌ | Invoice notes |
| `terms_and_conditions` | unknown | ❌ | Terms and conditions |
| `order_id` | unknown | ❌ | Related order ID |
| `vendor_id` | unknown | ❌ | B2B vendor ID |
| `customer_b2b_id` | unknown | ❌ | B2B customer ID |
| `notify_on_view` | boolean | ❌ | Notify when invoice is viewed |
| `notify_on_due` | boolean | ❌ | Notify when invoice is due |
| `id` | string | ✅ | Invoice ID |
| `tenant_id` | string | ✅ | Tenant ID |
| `invoice_number` | string | ✅ | Invoice number |
| `status` | InvoiceStatus | ✅ | Invoice status |
| `created_by` | string | ✅ | Created by user ID |
| `subtotal` | string | ✅ | Subtotal amount |
| `tax_amount` | string | ✅ | Tax amount |
| `total_amount` | string | ✅ | Total amount |
| `paid_date` | unknown | ❌ | Payment date |
| `payment_reference` | unknown | ❌ | Payment reference |
| `created_by_name` | unknown | ❌ | Created by user name |
| `order_number` | unknown | ❌ | Related order number |
| `pdf_generated_at` | unknown | ❌ | PDF generation timestamp |
| `pdf_available` | boolean | ❌ | Whether PDF is available |
| `items` | Array[InvoiceItemRead] | ❌ | Invoice items |

### InvoiceUpdate

**Descrição:** Schema for updating invoices.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `invoice_type` | unknown | ❌ | Type of invoice |
| `status` | unknown | ❌ | Invoice status |
| `customer_name` | unknown | ❌ | Customer name |
| `customer_email` | unknown | ❌ | Customer email |
| `customer_phone` | unknown | ❌ | Customer phone |
| `customer_address` | unknown | ❌ | Customer address |
| `customer_tax_id` | unknown | ❌ | Customer tax ID |
| `issue_date` | unknown | ❌ | Invoice issue date |
| `due_date` | unknown | ❌ | Payment due date |
| `paid_date` | unknown | ❌ | Payment date |
| `tax_rate` | unknown | ❌ | Default tax rate percentage |
| `discount_amount` | unknown | ❌ | Discount amount |
| `payment_method` | unknown | ❌ | Payment method |
| `payment_reference` | unknown | ❌ | Payment reference |
| `notes` | unknown | ❌ | Invoice notes |
| `terms_and_conditions` | unknown | ❌ | Terms and conditions |

## 🔗 Endpoints Detalhados

### GET /api/financial/invoices/ {#get-apifinancialinvoices}

**Resumo:** Get Invoices

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `status` | string | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/invoices/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/financial/invoices/ {#post-apifinancialinvoices}

**Resumo:** Create Invoice

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [InvoiceCreate](#invoicecreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [InvoiceRead](#invoiceread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/financial/invoices/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/financial/invoices/{invoice_id} {#delete-apifinancialinvoicesinvoice-id}

**Resumo:** Delete Invoice

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `invoice_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/financial/invoices/{invoice_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/financial/invoices/{invoice_id} {#get-apifinancialinvoicesinvoice-id}

**Resumo:** Get Invoice

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `invoice_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [InvoiceRead](#invoiceread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/invoices/{invoice_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/financial/invoices/{invoice_id} {#put-apifinancialinvoicesinvoice-id}

**Resumo:** Update Invoice

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `invoice_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [InvoiceUpdate](#invoiceupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [InvoiceRead](#invoiceread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/financial/invoices/{invoice_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/financial/invoices/{invoice_id}/generate-pdf {#post-apifinancialinvoicesinvoice-idgenerate-pdf}

**Resumo:** Generate Invoice Pdf

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `invoice_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/financial/invoices/{invoice_id}/generate-pdf" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
