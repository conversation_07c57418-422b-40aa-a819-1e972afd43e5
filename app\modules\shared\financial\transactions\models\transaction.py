"""Financial transaction models."""

import uuid
import enum
from typing import TYPE_CHECKING, Optional
from sqlalchemy import (
    Column, String, ForeignKey, Numeric, Text, Enum, Date, Index, Table
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base
from app.modules.core.functions.media_system.models import MediaUpload

# Use TYPE_CHECKING to handle circular dependencies for type hinting
if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.core.users.models.user import User
    from app.modules.core.payments.models.payment_method import PaymentMethod
    from app.modules.shared.financial.categories.models.category import (
        FinancialCategory
    )


class TransactionType(str, enum.Enum):
    """Enum for financial transaction types."""
    
    INCOME = "income"
    EXPENSE = "expense"


# Association Table for Many-to-Many relationship
financial_transaction_media_association = Table(
    "financial_transaction_media_association",
    Base.metadata,
    Column("transaction_id", UUID(as_uuid=True), Foreign<PERSON>ey("financial_transactions.id", ondelete="CASCADE"), primary_key=True),
    <PERSON>umn("media_upload_id", UUID(as_uuid=True), Foreign<PERSON>ey("media_uploads.id", ondelete="CASCADE"), primary_key=True),
)


class FinancialTransaction(Base):
    """
    Model for financial transactions (income and expenses).
    
    This represents a financial transaction that can be either income or expense,
    with support for document attachments via media system integration.
    """
    
    __tablename__ = "financial_transactions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("tenants.id"), 
        nullable=False, 
        index=True
    )
    
    # Transaction details
    transaction_type = Column(Enum(TransactionType), nullable=False)
    amount = Column(Numeric(10, 2), nullable=False)
    description = Column(Text, nullable=False)
    transaction_date = Column(Date, nullable=False, index=True)
    
    # Payment method reference (reusing existing PaymentMethod)
    payment_method_id = Column(
        UUID(as_uuid=True),
        ForeignKey("core_payment_methods.id"),
        nullable=True,
        index=True
    )
    
    # Category reference (will be created in categories module)
    category_id = Column(
        UUID(as_uuid=True),
        ForeignKey("financial_categories.id"),
        nullable=True,
        index=True
    )
    
    # User who created the transaction
    created_by = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        index=True
    )
    
    # Additional information
    reference_number = Column(String, nullable=True)  # Invoice number, etc.
    notes = Column(Text, nullable=True)
    
    # Relationships
    tenant = relationship(
        "app.modules.core.tenants.models.tenant.Tenant",
        viewonly=True
    )
    payment_method = relationship(
        "app.modules.core.payments.models.payment_method.PaymentMethod",
        viewonly=True
    )
    category = relationship(
        "app.modules.shared.financial.categories.models.category.FinancialCategory",
        back_populates="transactions"
    )
    created_by_user = relationship(
        "app.modules.core.users.models.user.User",
        viewonly=True
    )
    
    # Many-to-many relationship with MediaUpload
    media_uploads = relationship(
        "MediaUpload",
        secondary=financial_transaction_media_association,
        backref="financial_transactions",
        lazy="selectin"
    )
    
    # Table indexes for performance
    __table_args__ = (
        Index("ix_financial_transactions_tenant_date", "tenant_id", "transaction_date"),
        Index("ix_financial_transactions_tenant_type", "tenant_id", "transaction_type"),
        Index("ix_financial_transactions_tenant_category", "tenant_id", "category_id"),
    )
    
    def __repr__(self):
        return (
            f"<FinancialTransaction(id={self.id}, "
            f"type='{self.transaction_type}', "
            f"amount={self.amount})>"
        )
