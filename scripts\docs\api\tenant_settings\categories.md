# Tenant Settings - Categories

**Categoria:** Tenant Settings
**Módulo:** Categories
**Total de Endpoints:** 6
**Gerado em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [GET /api/tenants/settings/categories/zones](#get-apitenantssettingscategorieszones) - Get Restaurant Zones
- [POST /api/tenants/settings/categories/zones](#post-apitenantssettingscategorieszones) - Create Custom Zone
- [DELETE /api/tenants/settings/categories/zones/{zone_name}](#delete-apitenantssettingscategorieszoneszone-name) - Delete Custom Zone
- [GET /api/tenants/tenant-settings/categories/zones](#get-apitenantstenant-settingscategorieszones) - Get Restaurant Zones
- [POST /api/tenants/tenant-settings/categories/zones](#post-apitenantstenant-settingscategorieszones) - Create Custom Zone
- [DELETE /api/tenants/tenant-settings/categories/zones/{zone_name}](#delete-apitenantstenant-settingscategorieszoneszone-name) - Delete Custom Zone

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/tenants/settings/categories/zones {#get-apitenantssettingscategorieszones}

**Resumo:** Get Restaurant Zones
**Descrição:** Get all zones/categories used in the restaurant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/tenants/settings/categories/zones" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/tenants/settings/categories/zones {#post-apitenantssettingscategorieszones}

**Resumo:** Create Custom Zone
**Descrição:** Create a new custom zone/category.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `zone_name` | string | query | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**201:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/tenants/settings/categories/zones" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### DELETE /api/tenants/settings/categories/zones/{zone_name} {#delete-apitenantssettingscategorieszoneszone-name}

**Resumo:** Delete Custom Zone
**Descrição:** Delete a custom zone/category (only if it has no tables).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `zone_name` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/tenants/settings/categories/zones/{zone_name}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/tenants/tenant-settings/categories/zones {#get-apitenantstenant-settingscategorieszones}

**Resumo:** Get Restaurant Zones
**Descrição:** Get all zones/categories used in the restaurant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/tenants/tenant-settings/categories/zones" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/tenants/tenant-settings/categories/zones {#post-apitenantstenant-settingscategorieszones}

**Resumo:** Create Custom Zone
**Descrição:** Create a new custom zone/category.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `zone_name` | string | query | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**201:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/tenants/tenant-settings/categories/zones" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### DELETE /api/tenants/tenant-settings/categories/zones/{zone_name} {#delete-apitenantstenant-settingscategorieszoneszone-name}

**Resumo:** Delete Custom Zone
**Descrição:** Delete a custom zone/category (only if it has no tables).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `zone_name` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/tenants/tenant-settings/categories/zones/{zone_name}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
