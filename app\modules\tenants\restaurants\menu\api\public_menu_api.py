"""
Public API for menu viewing without authentication.
Allows customers to view restaurant menus without login.
Enhanced with caching and performance optimizations.
"""

import logging
import uuid
from typing import List, Optional
from fastapi import APIRouter, HTTPException, status, Path, Query, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from functools import lru_cache
import asyncio

# Database dependency
from app.core.db_dependencies import get_db

# Import services
from app.modules.tenants.restaurants.menu.services.digital_menu_service import DigitalMenuService
from app.modules.tenants.restaurants.menu.services.menu_category_service import MenuCategoryService
from app.modules.tenants.restaurants.menu.services.menu_item_service import MenuItemService

# Import schemas
from app.modules.tenants.restaurants.menu.schemas.digital_menu import (
    DigitalMenuRead,
    DigitalMenuWithCategories,
    DigitalMenuWithCategoriesPublic,
)
from app.modules.tenants.restaurants.menu.schemas.menu_category import MenuCategoryRead, MenuCategoryReadSimple, MenuCategoryReadPublic
from app.modules.tenants.restaurants.menu.schemas.menu_item import MenuItemRead, MenuItemReadPublic

# Import tenant service to resolve tenant by slug
from app.modules.core.tenants.services.tenant_service import TenantService

logger = logging.getLogger(__name__)

# Simple in-memory cache for tenant lookups
_tenant_cache = {}

# Router setup
router = APIRouter(
    prefix="/public/menu",
    tags=["Public Menu API"],
    # No authentication required for public endpoints
)


async def get_cached_tenant_id(tenant_slug: str, db: AsyncSession) -> uuid.UUID:
    """
    Get tenant ID with simple caching to reduce database lookups.
    
    Supports both UUID and tenant_slug resolution with efficient caching.
    Cache entries are stored for both the original identifier and resolved UUID
    to improve performance for subsequent requests.
    """
    # Check cache first using original identifier
    if tenant_slug in _tenant_cache:
        logger.debug(f"Cache hit for tenant identifier: {tenant_slug}")
        return _tenant_cache[tenant_slug]

    logger.debug(f"Cache miss for tenant identifier: {tenant_slug}, querying database")
    
    tenant_service = TenantService()
    tenant = await tenant_service.get_tenant_by_slug_or_id(db, tenant_slug)
    
    if not tenant:
        logger.warning(f"Restaurant '{tenant_slug}' not found")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Restaurant '{tenant_slug}' not found"
        )

    # Cache the result using the original identifier
    _tenant_cache[tenant_slug] = tenant.id
    
    # Also cache using the UUID if the original identifier was a slug
    # This provides bidirectional caching for better performance
    try:
        uuid.UUID(tenant_slug)  # If this succeeds, tenant_slug is already a UUID
    except ValueError:
        # If tenant_slug is not a UUID, also cache by the actual UUID
        _tenant_cache[str(tenant.id)] = tenant.id
        logger.debug(f"Bidirectional cache entry created: slug '{tenant_slug}' <-> UUID '{tenant.id}'")
    
    logger.info(f"Tenant resolved and cached: {tenant_slug} -> {tenant.id}")
    return tenant.id


async def get_tenant_by_slug(
    tenant_slug: str = Path(..., description="Tenant slug (restaurant identifier)"),
    db: AsyncSession = Depends(get_db)
) -> uuid.UUID:
    """
    Dependency to resolve tenant ID from slug with caching.
    """
    return await get_cached_tenant_id(tenant_slug, db)


async def load_all_images_for_items(db: AsyncSession, items):
    """
    Load all images from media system for menu items.
    Adds 'images' attribute to each item with all image URLs.
    """
    try:
        logger.info(f"Loading all images for {len(items) if items else 0} items")
        # Import here to avoid circular imports
        from app.modules.core.functions.media_system.models import MediaMenuItemMedia, MediaUpload

        if not items:
            return

        # Get all item IDs
        item_ids = [item.id for item in items]

        # Query for all images ordered by display_order
        stmt = select(MediaMenuItemMedia, MediaUpload).join(
            MediaUpload, MediaMenuItemMedia.media_upload_id == MediaUpload.id
        ).where(
            MediaMenuItemMedia.menu_item_id.in_(item_ids)
        ).order_by(MediaMenuItemMedia.menu_item_id, MediaMenuItemMedia.display_order)

        result = await db.execute(stmt)
        all_images = result.all()

        # Create mappings for primary and all images
        primary_image_map = {}
        all_images_map = {}

        for media_menu_item_media, media_upload in all_images:
            item_id = media_menu_item_media.menu_item_id
            image_url = f"/api/modules/core/media/download/{media_upload.id}"

            # Track primary image
            if media_menu_item_media.is_primary:
                primary_image_map[item_id] = image_url

            # Track all images
            if item_id not in all_images_map:
                all_images_map[item_id] = []
            all_images_map[item_id].append(image_url)

        # Update items with image URLs
        for item in items:
            # Set primary image
            if item.id in primary_image_map:
                item.image_url = primary_image_map[item.id]

            # Set all images array
            if item.id in all_images_map:
                # Add images attribute dynamically
                setattr(item, 'images', all_images_map[item.id])
                logger.debug(f"Set {len(all_images_map[item.id])} images for item {item.id}")

        logger.info(f"Loaded images for {len(all_images_map)} out of {len(items)} items")

    except Exception as e:
        logger.error(f"Error loading all images: {e}")
        # Don't raise - this is not critical for the main functionality


@router.get("/{tenant_slug}/menus", response_model=List[DigitalMenuRead])
async def get_public_menus(
    tenant_id: uuid.UUID = Depends(get_tenant_by_slug),
    db: AsyncSession = Depends(get_db)
):
    """
    Get all active digital menus for a restaurant.
    Public endpoint - no authentication required.
    """
    try:
        menu_service = DigitalMenuService(db)
        menus = await menu_service.get_digital_menus(
            tenant_id=tenant_id,
            include_categories=False
        )
        return menus
    except Exception as e:
        logger.error(f"Error retrieving public menus for tenant {tenant_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving menus"
        )


@router.get("/{tenant_slug}/menu/current", response_model=DigitalMenuWithCategoriesPublic)
async def get_current_public_menu(
    tenant_id: uuid.UUID = Depends(get_tenant_by_slug),
    timezone: str = Query("UTC", description="Timezone for schedule checking"),
    db: AsyncSession = Depends(get_db)
):
    """
    Get the currently active menu based on time scheduling.
    Returns the menu with categories and items.
    Public endpoint - no authentication required.
    """
    try:
        logger.info(f"Getting current menu for tenant: {tenant_id}")
        menu_service = DigitalMenuService(db)
        current_menu = await menu_service.get_active_menu_for_time(
            tenant_id=tenant_id,
            timezone_str=timezone
        )

        logger.info(f"Found menu: {current_menu.name if current_menu else 'None'}")
        if not current_menu:
            logger.warning(f"No active menu found for tenant {tenant_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No active menu found for this restaurant"
            )

        # Get categories separately to avoid greenlet issues (same as get_public_menu_by_id)
        category_service = MenuCategoryService(db)
        categories = await category_service.get_categories(
            tenant_id=tenant_id,
            include_children=True,
            include_items=True
        )

        # Filter categories by this specific menu and exclude default "Sem Categoria"
        menu_categories = [
            cat for cat in categories
            if cat.digital_menu_id == current_menu.id and not getattr(cat, 'is_default', False)
        ]

        # Organize categories hierarchically (exact same logic as get_public_menu_by_id)
        item_service = MenuItemService(db)

        # Helper function to build category with items and children
        async def build_category_dict(category):
            # Get items for this category
            items = await item_service.get_items(
                tenant_id=tenant_id,
                category_id=category.id,
                include_details=True
            )
            # Filter available items and convert to public schema (without redundant category object)
            available_items = []
            for item in items:
                if item.is_available and item.is_active:
                    # Convert to public schema without redundant category object
                    # Note: images attribute is added dynamically by _load_all_images
                    # Convert allergens manually to avoid circular references
                    from fastapi.encoders import jsonable_encoder

                    # Convert allergens safely
                    allergens_data = []
                    if hasattr(item, 'allergens') and item.allergens:
                        for allergen in item.allergens:
                            allergens_data.append({
                                'id': str(allergen.id),
                                'name': allergen.name,
                                'description': allergen.description,
                                'icon': allergen.icon,
                                'is_active': allergen.is_active,
                                'created_at': allergen.created_at.isoformat() if allergen.created_at else None,
                                'updated_at': allergen.updated_at.isoformat() if allergen.updated_at else None
                            })

                    # Convert groups safely
                    variant_groups_data = jsonable_encoder(item.variant_groups) if item.variant_groups else []
                    modifier_groups_data = jsonable_encoder(item.modifier_groups) if item.modifier_groups else []
                    optional_groups_data = jsonable_encoder(item.optional_groups) if item.optional_groups else []

                    public_item = MenuItemReadPublic(
                        id=item.id,
                        name=item.name,
                        description=item.description,
                        base_price=item.base_price,
                        image_url=getattr(item, 'image_url', None),
                        images=getattr(item, 'images', None),
                        allergens=allergens_data,
                        is_available=item.is_available,
                        is_active=item.is_active,
                        is_combo=item.is_combo,
                        discount_percentage=item.discount_percentage,
                        display_order=item.display_order,
                        tenant_id=item.tenant_id,
                        category_id=item.category_id,  # Keep only ID, not full object
                        variant_groups=variant_groups_data,
                        modifier_groups=modifier_groups_data,
                        optional_groups=optional_groups_data
                    )
                    available_items.append(public_item)

            # Images are now loaded by MenuItemService._load_all_images

            # Find children categories
            children_categories = [cat for cat in menu_categories if cat.parent_id == category.id]
            children = []
            for child_cat in children_categories:
                child_dict = await build_category_dict(child_cat)
                children.append(child_dict)

            # Sort children by display_order
            children.sort(key=lambda x: x.display_order)

            # Create MenuCategoryReadPublic object
            return MenuCategoryReadPublic(
                id=category.id,
                name=category.name,
                description=category.description,
                display_order=category.display_order,
                is_active=category.is_active,
                is_default=getattr(category, 'is_default', False),
                parent_id=category.parent_id,
                digital_menu_id=category.digital_menu_id,
                tenant_id=category.tenant_id,
                created_at=category.created_at,
                updated_at=category.updated_at,
                menu_items=available_items,
                children=children
            )

        # Build hierarchy starting with top-level categories
        all_categories = []
        top_level_categories = [cat for cat in menu_categories if not cat.parent_id]
        for category in top_level_categories:
            category_dict = await build_category_dict(category)
            all_categories.append(category_dict)

        # Sort top-level categories by display_order
        all_categories.sort(key=lambda x: x.display_order)

        # Create response with categories
        menu_response = DigitalMenuWithCategoriesPublic(
            id=current_menu.id,
            tenant_id=current_menu.tenant_id,
            name=current_menu.name,
            description=current_menu.description,
            is_active=current_menu.is_active,
            display_order=current_menu.display_order,
            schedule_enabled=current_menu.schedule_enabled,
            schedule_config=current_menu.schedule_config,
            created_at=current_menu.created_at,
            updated_at=current_menu.updated_at,
            categories=all_categories
        )

        return menu_response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving current public menu for tenant {tenant_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving current menu"
        )


@router.get("/{tenant_slug}/menu/{menu_id}", response_model=DigitalMenuWithCategories)
async def get_public_menu_by_id(
    menu_id: uuid.UUID = Path(..., description="Digital menu ID"),
    tenant_id: uuid.UUID = Depends(get_tenant_by_slug),
    db: AsyncSession = Depends(get_db)
):
    """
    Get a specific digital menu by ID with its categories and items.
    Public endpoint - no authentication required.
    """
    try:
        logger.info(f"Getting menu {menu_id} for tenant: {tenant_id}")
        menu_service = DigitalMenuService(db)
        menu = await menu_service.get_digital_menu(
            menu_id=menu_id,
            tenant_id=tenant_id,
            include_categories=False  # Don't include categories to avoid greenlet issues
        )

        if not menu:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Menu with ID {menu_id} not found"
            )

        # Get categories separately to avoid greenlet issues
        category_service = MenuCategoryService(db)
        categories = await category_service.get_categories(
            tenant_id=tenant_id,
            include_children=True,
            include_items=True
        )

        # Filter categories by this specific menu and exclude default "Sem Categoria"
        menu_categories = [
            cat for cat in categories
            if cat.digital_menu_id == menu_id and not getattr(cat, 'is_default', False)
        ]

        # Organize categories hierarchically
        item_service = MenuItemService(db)

        # Helper function to build category with items and children
        async def build_category_dict(category):
            # Get items for this category
            items = await item_service.get_items(
                tenant_id=tenant_id,
                category_id=category.id,
                include_details=True
            )
            # Filter available items and convert to public schema (without redundant category object)
            available_items = []
            for item in items:
                if item.is_available and item.is_active:
                    # Convert to public schema without redundant category object
                    # Note: images attribute is added dynamically by _load_all_images
                    # Convert allergens manually to avoid circular references
                    from fastapi.encoders import jsonable_encoder

                    # Convert allergens safely
                    allergens_data = []
                    if hasattr(item, 'allergens') and item.allergens:
                        for allergen in item.allergens:
                            allergens_data.append({
                                'id': str(allergen.id),
                                'name': allergen.name,
                                'description': allergen.description,
                                'icon': allergen.icon,
                                'is_active': allergen.is_active,
                                'created_at': allergen.created_at.isoformat() if allergen.created_at else None,
                                'updated_at': allergen.updated_at.isoformat() if allergen.updated_at else None
                            })

                    # Convert groups safely
                    variant_groups_data = jsonable_encoder(item.variant_groups) if item.variant_groups else []
                    modifier_groups_data = jsonable_encoder(item.modifier_groups) if item.modifier_groups else []
                    optional_groups_data = jsonable_encoder(item.optional_groups) if item.optional_groups else []

                    public_item = MenuItemReadPublic(
                        id=item.id,
                        name=item.name,
                        description=item.description,
                        base_price=item.base_price,
                        image_url=getattr(item, 'image_url', None),
                        images=getattr(item, 'images', None),
                        allergens=allergens_data,
                        is_available=item.is_available,
                        is_active=item.is_active,
                        is_combo=item.is_combo,
                        discount_percentage=item.discount_percentage,
                        display_order=item.display_order,
                        tenant_id=item.tenant_id,
                        category_id=item.category_id,  # Keep only ID, not full object
                        variant_groups=variant_groups_data,
                        modifier_groups=modifier_groups_data,
                        optional_groups=optional_groups_data
                    )
                    available_items.append(public_item)

            # Images are now loaded by MenuItemService._load_all_images

            # Find children categories
            children_categories = [cat for cat in menu_categories if cat.parent_id == category.id]
            children = []
            for child_cat in children_categories:
                child_dict = await build_category_dict(child_cat)
                children.append(child_dict)

            # Sort children by display_order
            children.sort(key=lambda x: x.display_order)

            # Create MenuCategoryReadPublic object
            return MenuCategoryReadPublic(
                id=category.id,
                name=category.name,
                description=category.description,
                display_order=category.display_order,
                is_active=category.is_active,
                is_default=getattr(category, 'is_default', False),
                parent_id=category.parent_id,
                digital_menu_id=category.digital_menu_id,
                tenant_id=category.tenant_id,
                created_at=category.created_at,
                updated_at=category.updated_at,
                menu_items=available_items,
                children=children
            )

        # Build hierarchy starting with top-level categories
        all_categories = []
        top_level_categories = [cat for cat in menu_categories if not cat.parent_id]
        for category in top_level_categories:
            category_dict = await build_category_dict(category)
            all_categories.append(category_dict)

        # Sort top-level categories by display_order
        all_categories.sort(key=lambda x: x.display_order)

        # Create response with categories
        menu_response = DigitalMenuWithCategories(
            id=menu.id,
            tenant_id=menu.tenant_id,
            name=menu.name,
            description=menu.description,
            is_active=menu.is_active,
            display_order=menu.display_order,
            schedule_enabled=menu.schedule_enabled,
            schedule_config=menu.schedule_config,
            created_at=menu.created_at,
            updated_at=menu.updated_at,
            categories=all_categories
        )

        return menu_response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving public menu {menu_id} for tenant {tenant_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving menu"
        )


@router.get("/{tenant_slug}/categories")
async def get_public_categories(
    tenant_id: uuid.UUID = Depends(get_tenant_by_slug),
    menu_id: Optional[uuid.UUID] = Query(None, description="Filter by digital menu ID"),
    parent_id: Optional[uuid.UUID] = Query(None, description="Filter by parent category ID"),
    only_top_level: bool = Query(False, description="Only return top-level categories"),
    include_items: bool = Query(False, description="Include menu items with full details"),
    db: AsyncSession = Depends(get_db)
):
    """
    Get menu categories for a restaurant.
    Public endpoint - no authentication required.
    """
    try:
        category_service = MenuCategoryService(db)
        categories = await category_service.get_categories(
            tenant_id=tenant_id,
            parent_id=parent_id,
            include_children=True,
            include_items=include_items,
            only_top_level=only_top_level
        )

        # Filter by digital menu if specified and exclude default "Sem Categoria"
        if menu_id:
            categories = [
                cat for cat in categories
                if cat.digital_menu_id == menu_id and not getattr(cat, 'is_default', False)
            ]
        else:
            # Always exclude default "Sem Categoria" from public endpoints
            categories = [cat for cat in categories if not getattr(cat, 'is_default', False)]

        # Convert to appropriate response format using jsonable_encoder
        from fastapi.encoders import jsonable_encoder
        from fastapi.responses import JSONResponse

        if include_items:
            # Return full categories with items (use jsonable_encoder)
            return JSONResponse(content=jsonable_encoder(categories))
        else:
            # Return simplified categories without items (use jsonable_encoder)
            return JSONResponse(content=jsonable_encoder(categories))

    except Exception as e:
        logger.error(f"Error retrieving public categories for tenant {tenant_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving categories"
        )


@router.get("/{tenant_slug}/items", response_model=List[MenuItemRead])
async def get_public_items(
    tenant_id: uuid.UUID = Depends(get_tenant_by_slug),
    menu_id: Optional[uuid.UUID] = Query(None, description="Filter by digital menu ID"),
    category_id: Optional[uuid.UUID] = Query(None, description="Filter by category ID"),
    search: Optional[str] = Query(None, description="Search items by name"),
    db: AsyncSession = Depends(get_db)
):
    """
    Get menu items for a restaurant.
    Public endpoint - no authentication required.
    """
    try:
        item_service = MenuItemService(db)

        # If menu_id is provided, filter categories by that menu first
        if menu_id and not category_id:
            # Get categories for this menu
            category_service = MenuCategoryService(db)
            categories = await category_service.get_categories(
                tenant_id=tenant_id,
                include_children=False
            )
            # Filter categories by digital menu
            menu_categories = [cat for cat in categories if cat.digital_menu_id == menu_id]
            category_ids = [cat.id for cat in menu_categories]

            # Get items for all categories in this menu
            all_items = []
            for cat_id in category_ids:
                cat_items = await item_service.get_items(
                    tenant_id=tenant_id,
                    category_id=cat_id,
                    include_details=True
                )
                # Filter by availability and search term
                filtered_items = [
                    item for item in cat_items
                    if item.is_available and item.is_active and
                    (not search or search.lower() in item.name.lower() or search.lower() in (item.description or "").lower())
                ]
                all_items.extend(filtered_items)

            # Images are now loaded by MenuItemService._load_all_images
            return all_items
        else:
            items = await item_service.get_items(
                tenant_id=tenant_id,
                category_id=category_id,
                include_details=True
            )
            # Filter by availability and search term
            filtered_items = [
                item for item in items
                if item.is_available and item.is_active and
                (not search or search.lower() in item.name.lower() or search.lower() in (item.description or "").lower())
            ]

            # Images are now loaded by MenuItemService._load_all_images
            return filtered_items

    except Exception as e:
        logger.error(f"Error retrieving public items for tenant {tenant_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving items"
        )


@router.get("/{tenant_slug}/items/{item_id}", response_model=MenuItemRead)
async def get_public_item_by_id(
    item_id: uuid.UUID = Path(..., description="Menu item ID"),
    tenant_id: uuid.UUID = Depends(get_tenant_by_slug),
    db: AsyncSession = Depends(get_db)
):
    """
    Get a specific menu item by ID with variants, modifiers, and optionals.
    Public endpoint - no authentication required.
    """
    try:
        item_service = MenuItemService(db)
        item = await item_service.get_item(
            item_id=item_id,
            tenant_id=tenant_id
        )

        if not item or not item.is_available:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Item with ID {item_id} not found or not available"
            )

        # Images are now loaded by MenuItemService._load_all_images
        return item

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving public item {item_id} for tenant {tenant_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving item"
        )
