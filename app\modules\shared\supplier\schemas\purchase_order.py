"""Purchase Order schemas for supplier system."""

from datetime import date, datetime
from decimal import Decimal
from typing import List, Optional
from pydantic import BaseModel, Field, ConfigDict
import uuid

from app.modules.shared.supplier.models.purchase_order import (
    PurchaseOrderStatus,
    PurchaseOrderItemStatus
)


# Purchase Order Item Schemas
class PurchaseOrderItemBase(BaseModel):
    """Base schema for purchase order items."""
    name: str = Field(..., max_length=200)
    description: Optional[str] = None
    quantity_requested: Decimal = Field(..., gt=0)
    unit: Optional[str] = Field(None, max_length=20)
    unit_price: Decimal = Field(..., ge=0)
    product_code: Optional[str] = Field(None, max_length=50)
    notes: Optional[str] = None
    inventory_item_id: Optional[uuid.UUID] = None
    shopping_list_item_id: Optional[uuid.UUID] = None
    sort_order: int = Field(default=0, ge=0)


class PurchaseOrderItemCreate(PurchaseOrderItemBase):
    """Schema for creating purchase order items."""
    pass


class PurchaseOrderItemUpdate(BaseModel):
    """Schema for updating purchase order items."""
    name: Optional[str] = Field(None, max_length=200)
    description: Optional[str] = None
    quantity_requested: Optional[Decimal] = Field(None, gt=0)
    quantity_confirmed: Optional[Decimal] = Field(None, ge=0)
    quantity_delivered: Optional[Decimal] = Field(None, ge=0)
    unit: Optional[str] = Field(None, max_length=20)
    unit_price: Optional[Decimal] = Field(None, ge=0)
    status: Optional[PurchaseOrderItemStatus] = None
    product_code: Optional[str] = Field(None, max_length=50)
    notes: Optional[str] = None
    supplier_notes: Optional[str] = None
    sort_order: Optional[int] = Field(None, ge=0)


class PurchaseOrderItemSupplierUpdate(BaseModel):
    """Schema for supplier updates to purchase order items."""
    quantity_confirmed: Optional[Decimal] = Field(None, ge=0)
    quantity_delivered: Optional[Decimal] = Field(None, ge=0)
    status: PurchaseOrderItemStatus
    supplier_notes: Optional[str] = None
    unit_price: Optional[Decimal] = Field(None, ge=0)


class PurchaseOrderItemRead(PurchaseOrderItemBase):
    """Schema for reading purchase order items."""
    id: uuid.UUID
    purchase_order_id: uuid.UUID
    quantity_confirmed: Optional[Decimal] = None
    quantity_delivered: Optional[Decimal] = None
    total_price: Decimal
    status: PurchaseOrderItemStatus
    supplier_notes: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


# Purchase Order Schemas
class PurchaseOrderBase(BaseModel):
    """Base schema for purchase orders."""
    supplier_id: uuid.UUID
    shopping_list_id: Optional[uuid.UUID] = None
    requested_delivery_date: Optional[date] = None
    delivery_address: Optional[str] = None
    delivery_notes: Optional[str] = None
    notes: Optional[str] = None


class PurchaseOrderCreate(PurchaseOrderBase):
    """Schema for creating purchase orders."""
    items: List[PurchaseOrderItemCreate] = Field(..., min_items=1)


class PurchaseOrderUpdate(BaseModel):
    """Schema for updating purchase orders."""
    status: Optional[PurchaseOrderStatus] = None
    requested_delivery_date: Optional[date] = None
    confirmed_delivery_date: Optional[date] = None
    actual_delivery_date: Optional[date] = None
    delivery_address: Optional[str] = None
    delivery_notes: Optional[str] = None
    supplier_invoice_number: Optional[str] = Field(None, max_length=100)
    supplier_delivery_note: Optional[str] = Field(None, max_length=100)
    notes: Optional[str] = None
    internal_notes: Optional[str] = None


class PurchaseOrderSupplierUpdate(BaseModel):
    """Schema for supplier updates to purchase orders."""
    status: PurchaseOrderStatus
    confirmed_delivery_date: Optional[date] = None
    actual_delivery_date: Optional[date] = None
    supplier_invoice_number: Optional[str] = Field(None, max_length=100)
    supplier_delivery_note: Optional[str] = Field(None, max_length=100)
    delivery_notes: Optional[str] = None


class PurchaseOrderRead(PurchaseOrderBase):
    """Schema for reading purchase orders."""
    id: uuid.UUID
    tenant_id: uuid.UUID
    order_number: str
    status: PurchaseOrderStatus
    order_date: date
    confirmed_delivery_date: Optional[date] = None
    actual_delivery_date: Optional[date] = None
    subtotal: Decimal
    tax_amount: Decimal
    total_amount: Decimal
    supplier_invoice_number: Optional[str] = None
    supplier_delivery_note: Optional[str] = None
    internal_notes: Optional[str] = None
    created_by: uuid.UUID
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class PurchaseOrderWithItems(PurchaseOrderRead):
    """Schema for reading purchase orders with items."""
    items: List[PurchaseOrderItemRead] = []


class PurchaseOrderSummary(BaseModel):
    """Schema for purchase order summary."""
    id: uuid.UUID
    order_number: str
    status: PurchaseOrderStatus
    supplier_name: str
    order_date: date
    total_amount: Decimal
    items_count: int
    pending_items: int
    confirmed_items: int
    delivered_items: int

    model_config = ConfigDict(from_attributes=True)


# Supplier Portal Schemas
class SupplierPurchaseOrderView(BaseModel):
    """Schema for supplier view of purchase orders."""
    id: uuid.UUID
    order_number: str
    status: PurchaseOrderStatus
    order_date: date
    requested_delivery_date: Optional[date] = None
    confirmed_delivery_date: Optional[date] = None
    tenant_name: str
    total_amount: Decimal
    items_count: int
    delivery_address: Optional[str] = None
    delivery_notes: Optional[str] = None
    notes: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


class SupplierPurchaseOrderWithItems(SupplierPurchaseOrderView):
    """Schema for supplier view of purchase orders with items."""
    items: List[PurchaseOrderItemRead] = []


# Bulk Operations Schemas
class BulkItemStatusUpdate(BaseModel):
    """Schema for bulk updating item statuses."""
    item_updates: List[dict] = Field(..., min_items=1)
    # Each dict should contain: {"item_id": uuid, "status": status, "quantity_confirmed": decimal, etc.}


class PurchaseOrderGenerateRequest(BaseModel):
    """Schema for generating purchase orders from shopping list."""
    shopping_list_id: uuid.UUID
    supplier_id: uuid.UUID
    requested_delivery_date: Optional[date] = None
    delivery_address: Optional[str] = None
    delivery_notes: Optional[str] = None
    notes: Optional[str] = None
    item_ids: Optional[List[uuid.UUID]] = None  # If None, include all items
