"""
Base Pydantic schemas for tenant settings operations.
"""

import uuid
from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any, List

from pydantic import BaseModel, ConfigDict, Field, validator

# Import will be done locally to avoid circular imports


class TenantSettingsBase(BaseModel):
    """Base schema for tenant settings."""
    
    # Business Information
    business_name: Optional[str] = Field(
        None,
        max_length=200,
        description="Business name"
    )
    business_type: Optional[str] = Field(
        None,
        max_length=100,
        description="Type of business"
    )
    tenant_slug: Optional[str] = Field(
        None,
        max_length=63,
        pattern=r"^[a-z0-9](?:[a-z0-9\-]{0,61}[a-z0-9])?$",
        description="Unique slug for tenant identification in public URLs"
    )
    # Multi-Currency Configuration
    default_currency: str = Field(
        "BRL",
        min_length=3,
        max_length=3,
        description="Default currency code (1:1 ratio for stability)"
    )
    currency_config: Optional[Dict[str, Any]] = Field(
        None,
        description="Multi-currency configuration with exchange rates and formatting"
    )
    timezone: str = Field(
        "America/Sao_Paulo", 
        max_length=50, 
        description="Timezone identifier"
    )
    
    # Operating Hours
    operating_hours: Optional[Dict[str, Any]] = Field(
        None, 
        description="Operating hours configuration by day of week"
    )
    
    # Multi-language Settings
    multi_language_enabled: bool = Field(
        False, 
        description="Enable multi-language support"
    )
    available_languages: Optional[List[str]] = Field(
        None, 
        description="List of available language codes"
    )
    default_language: str = Field(
        "pt-BR", 
        max_length=5, 
        description="Default language code"
    )
    
    # Loyalty System
    loyalty_enabled: bool = Field(
        False, 
        description="Enable loyalty system"
    )
    loyalty_config: Optional[Dict[str, Any]] = Field(
        None, 
        description="Loyalty system configuration"
    )
    
    # Location & Address
    country: Optional[str] = Field(
        None, 
        min_length=2, 
        max_length=2, 
        description="Country code (ISO 3166-1 alpha-2)"
    )
    address: Optional[Dict[str, Any]] = Field(
        None,
        description="Complete address with coordinates"
    )
    
    # Tax Configuration
    base_tax_rate: Decimal = Field(
        Decimal("0.0000"), 
        ge=0, 
        le=100, 
        max_digits=5, 
        decimal_places=4,
        description="Base tax rate percentage"
    )
    tax_calculation_method: str = Field(
        "incremental", 
        description="Tax calculation method: 'incremental' or 'inclusive'"
    )
    
    # WiFi Management
    wifi_networks: Optional[List[Dict[str, Any]]] = Field(
        None,
        description="WiFi networks configuration by zone"
    )

    # Social Media Links
    social_media_links: Optional[List[Dict[str, Any]]] = Field(
        None,
        description="Social media platform links with icons"
    )

    # Subscription Information
    subscription_plan: Optional[str] = Field(
        None,
        max_length=100,
        description="Current subscription plan"
    )
    subscription_status: str = Field(
        "active",
        max_length=20,
        description="Subscription status"
    )

    # Additional Settings
    additional_settings: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional configuration settings"
    )

    @validator('default_currency')
    def validate_default_currency(cls, v):
        """Validate default currency code format."""
        if v and len(v) != 3:
            raise ValueError('Default currency must be a 3-letter ISO 4217 code')
        return v.upper() if v else v

    @validator('tax_calculation_method')
    def validate_tax_calculation_method(cls, v):
        """Validate tax calculation method."""
        allowed_methods = ['incremental', 'inclusive']
        if v not in allowed_methods:
            raise ValueError(f'Tax calculation method must be one of: {allowed_methods}')
        return v

    @validator('subscription_status')
    def validate_subscription_status(cls, v):
        """Validate subscription status."""
        allowed_statuses = ['active', 'inactive', 'suspended', 'cancelled', 'trial']
        if v not in allowed_statuses:
            raise ValueError(f'Subscription status must be one of: {allowed_statuses}')
        return v


class TenantSettingsCreate(TenantSettingsBase):
    """Schema for creating tenant settings."""
    pass


class TenantSettingsUpdate(BaseModel):
    """Schema for updating tenant settings. All fields are optional."""
    
    # Business Information
    business_name: Optional[str] = Field(
        None,
        max_length=200,
        description="Business name"
    )
    business_type: Optional[str] = Field(
        None,
        max_length=100,
        description="Type of business"
    )
    tenant_slug: Optional[str] = Field(
        None,
        max_length=63,
        pattern=r"^[a-z0-9](?:[a-z0-9\-]{0,61}[a-z0-9])?$",
        description="Unique slug for tenant identification in public URLs"
    )
    # Multi-Currency Configuration
    default_currency: Optional[str] = Field(
        None,
        min_length=3,
        max_length=3,
        description="Default currency code (1:1 ratio for stability)"
    )
    currency_config: Optional[Dict[str, Any]] = Field(
        None,
        description="Multi-currency configuration with exchange rates and formatting"
    )
    timezone: Optional[str] = Field(
        None, 
        max_length=50, 
        description="Timezone identifier"
    )
    
    # Operating Hours
    operating_hours: Optional[Dict[str, Any]] = Field(
        None, 
        description="Operating hours configuration by day of week"
    )
    
    # Multi-language Settings
    multi_language_enabled: Optional[bool] = Field(
        None, 
        description="Enable multi-language support"
    )
    available_languages: Optional[List[str]] = Field(
        None, 
        description="List of available language codes"
    )
    default_language: Optional[str] = Field(
        None, 
        max_length=5, 
        description="Default language code"
    )
    
    # Loyalty System
    loyalty_enabled: Optional[bool] = Field(
        None, 
        description="Enable loyalty system"
    )
    loyalty_config: Optional[Dict[str, Any]] = Field(
        None, 
        description="Loyalty system configuration"
    )
    
    # Location & Address
    country: Optional[str] = Field(
        None, 
        min_length=2, 
        max_length=2, 
        description="Country code (ISO 3166-1 alpha-2)"
    )
    address: Optional[Dict[str, Any]] = Field(
        None,
        description="Complete address with coordinates"
    )
    
    # Tax Configuration
    base_tax_rate: Optional[Decimal] = Field(
        None, 
        ge=0, 
        le=100, 
        max_digits=5, 
        decimal_places=4,
        description="Base tax rate percentage"
    )
    tax_calculation_method: Optional[str] = Field(
        None, 
        description="Tax calculation method: 'incremental' or 'inclusive'"
    )
    
    # WiFi Management
    wifi_networks: Optional[List[Dict[str, Any]]] = Field(
        None,
        description="WiFi networks configuration by zone"
    )

    # Social Media Links
    social_media_links: Optional[List[Dict[str, Any]]] = Field(
        None,
        description="Social media platform links with icons"
    )

    # Subscription Information
    subscription_plan: Optional[str] = Field(
        None,
        max_length=100,
        description="Current subscription plan"
    )
    subscription_status: Optional[str] = Field(
        None,
        max_length=20,
        description="Subscription status"
    )

    # Additional Settings
    additional_settings: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional configuration settings"
    )

    @validator('default_currency')
    def validate_default_currency(cls, v):
        """Validate default currency code format."""
        if v and len(v) != 3:
            raise ValueError('Default currency must be a 3-letter ISO 4217 code')
        return v.upper() if v else v

    @validator('tax_calculation_method')
    def validate_tax_calculation_method(cls, v):
        """Validate tax calculation method."""
        if v is not None:
            allowed_methods = ['incremental', 'inclusive']
            if v not in allowed_methods:
                raise ValueError(f'Tax calculation method must be one of: {allowed_methods}')
        return v

    @validator('subscription_status')
    def validate_subscription_status(cls, v):
        """Validate subscription status."""
        if v is not None:
            allowed_statuses = ['active', 'inactive', 'suspended', 'cancelled', 'trial']
            if v not in allowed_statuses:
                raise ValueError(f'Subscription status must be one of: {allowed_statuses}')
        return v


class TenantSettingsRead(TenantSettingsBase):
    """Schema for reading tenant settings."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    # Computed properties
    has_social_media_links: bool = Field(
        False,
        description="Whether social media links are configured"
    )

    model_config = ConfigDict(from_attributes=True)
