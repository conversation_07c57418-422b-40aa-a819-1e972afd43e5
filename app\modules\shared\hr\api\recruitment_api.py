from typing import List, Optional
import uuid
from fastapi import APIRouter, Depends, Query, status, UploadFile, File, Form
from sqlalchemy.ext.asyncio import AsyncSession
import json

from app.core.dependencies import get_db, get_current_tenant, require_tenant_role
from app.core.roles import TenantRole
from app.models.user import User
from app.models.tenant import Tenant

from app.modules.shared.hr.models.recruitment import JobStatus, CandidateStatus
from app.modules.shared.hr.schemas.recruitment import (
    JobRead,
    JobCreate,
    JobUpdate,
    CandidateRead,
    CandidateCreate,
    InterviewRead,
    InterviewCreate,
    AssessmentRead,
    AssessmentCreate,
    JobApplicationCreate,
)
from app.modules.shared.hr.services.recruitment_service import recruitment_service  # noqa: E402

router = APIRouter(prefix="/recruitment", tags=["HR - Recruitment"])

# Job endpoints


@router.post(
    "/jobs",
    response_model=JobRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create Job",
    description="Create a new job posting.",
)
async def create_job(
    job_data: JobCreate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Create a new job posting."""
    return await recruitment_service.create_job(db=db, tenant_id=tenant.id, job_data=job_data)


@router.get(
    "/jobs/{job_id}",
    response_model=JobRead,
    summary="Get Job",
    description="Get a job by ID.",
)
async def get_job(
    job_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Get a job by ID."""
    return await recruitment_service.get_job(db=db, tenant_id=tenant.id, job_id=job_id)


@router.get(
    "/jobs",
    response_model=List[JobRead],
    summary="List Jobs",
    description="Get all jobs with optional filtering.",
)
async def get_jobs(
    status: Optional[JobStatus] = None,
    department: Optional[str] = None,
    search: Optional[str] = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Get all jobs with optional filtering."""
    return await recruitment_service.get_jobs(
        db=db,
        tenant_id=tenant.id,
        status=status,
        department=department,
        search=search,
        skip=skip,
        limit=limit,
    )


@router.put(
    "/jobs/{job_id}",
    response_model=JobRead,
    summary="Update Job",
    description="Update a job by ID.",
)
async def update_job(
    job_id: uuid.UUID,
    job_data: JobUpdate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Update a job by ID."""
    return await recruitment_service.update_job(
        db=db, tenant_id=tenant.id, job_id=job_id, job_data=job_data
    )


@router.delete(
    "/jobs/{job_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete Job",
    description="Delete a job by ID.",
)
async def delete_job(
    job_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Delete a job by ID."""
    await recruitment_service.delete_job(db=db, tenant_id=tenant.id, job_id=job_id)


# Candidate endpoints


@router.post(
    "/candidates",
    response_model=CandidateRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create Candidate",
    description="Create a new candidate.",
)
async def create_candidate(
    job_id: uuid.UUID = Form(...),
    first_name: str = Form(...),
    last_name: str = Form(...),
    email: str = Form(...),
    phone: Optional[str] = Form(None),
    source: Optional[str] = Form(None),
    notes: Optional[str] = Form(None),
    status: CandidateStatus = Form(CandidateStatus.NEW),
    rating: Optional[int] = Form(None),
    metadata: Optional[str] = Form(None),
    resume: Optional[UploadFile] = File(None),
    cover_letter: Optional[UploadFile] = File(None),
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Create a new candidate."""
    # Parse JSON strings
    metadata_dict = json.loads(metadata) if metadata else None

    candidate_data = CandidateCreate(
        job_id=job_id,
        first_name=first_name,
        last_name=last_name,
        email=email,
        phone=phone,
        source=source,
        notes=notes,
        status=status,
        rating=rating,
        metadata=metadata_dict,
        resume_path=None,  # Will be set during upload
        cover_letter_path=None,  # Will be set during upload
    )

    return await recruitment_service.create_candidate(
        db=db,
        tenant_id=tenant.id,
        candidate_data=candidate_data,
        resume_file=resume,
        cover_letter_file=cover_letter,
    )


@router.post(
    "/apply",
    response_model=CandidateRead,
    status_code=status.HTTP_201_CREATED,
    summary="Apply for Job",
    description="Submit a job application.",
)
async def apply_for_job(
    job_id: uuid.UUID = Form(...),
    first_name: str = Form(...),
    last_name: str = Form(...),
    email: str = Form(...),
    phone: Optional[str] = Form(None),
    cover_letter: Optional[str] = Form(None),
    source: Optional[str] = Form(None),
    metadata: Optional[str] = Form(None),
    resume_file: Optional[UploadFile] = File(None),
    cover_letter_file: Optional[UploadFile] = File(None),
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
):
    """Submit a job application."""
    # Parse JSON strings
    metadata_dict = json.loads(metadata) if metadata else None

    application_data = JobApplicationCreate(
        job_id=job_id,
        first_name=first_name,
        last_name=last_name,
        email=email,
        phone=phone,
        cover_letter=cover_letter,
        source=source,
        metadata=metadata_dict,
    )

    return await recruitment_service.process_job_application(
        db=db,
        tenant_id=tenant.id,
        application_data=application_data,
        resume_file=resume_file,
        cover_letter_file=cover_letter_file,
    )


# Interview endpoints


@router.post(
    "/interviews",
    response_model=InterviewRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create Interview",
    description="Schedule a new interview.",
)
async def create_interview(
    interview_data: InterviewCreate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Schedule a new interview."""
    return await recruitment_service.create_interview(
        db=db, tenant_id=tenant.id, interview_data=interview_data
    )


# Assessment endpoints


@router.post(
    "/assessments",
    response_model=AssessmentRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create Assessment",
    description="Create a new assessment.",
)
async def create_assessment(
    assessment_data: AssessmentCreate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Create a new assessment."""
    return await recruitment_service.create_assessment(
        db=db, tenant_id=tenant.id, assessment_data=assessment_data
    )
