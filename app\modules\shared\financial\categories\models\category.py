"""Financial category models."""

import uuid
import enum
from typing import TYPE_CHECKING
from sqlalchemy import (
    Column, String, Text, Foreign<PERSON>ey, Boolean, Integer, Enum, Index
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base

# Use TYPE_CHECKING to handle circular dependencies for type hinting
if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.shared.financial.transactions.models.transaction import (
        FinancialTransaction
    )


class CategoryType(str, enum.Enum):
    """Enum for financial category types."""
    
    INCOME = "income"
    EXPENSE = "expense"


class FinancialCategory(Base):
    """
    Model for financial categories with hierarchical support.
    
    Supports parent-child relationships for category organization.
    Categories can be either income or expense types.
    """
    
    __tablename__ = "financial_categories"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("tenants.id"), 
        nullable=False, 
        index=True
    )
    
    # Category details
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    category_type = Column(Enum(CategoryType), nullable=False, index=True)
    
    # Hierarchical structure
    parent_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("financial_categories.id"), 
        nullable=True, 
        index=True
    )
    
    # Display and organization
    display_order = Column(Integer, default=0, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_default = Column(Boolean, default=False, nullable=False)
    
    # Visual customization
    color = Column(String(7), nullable=True)  # Hex color code
    icon = Column(String(100), nullable=True)  # Icon class or URL
    
    # Relationships
    tenant = relationship(
        "app.modules.core.tenants.models.tenant.Tenant",
        viewonly=True
    )
    
    # Self-referential relationship for subcategories
    parent = relationship(
        "FinancialCategory",
        remote_side=[id],
        back_populates="children",
        foreign_keys=[parent_id]
    )
    children = relationship(
        "FinancialCategory",
        back_populates="parent",
        cascade="all, delete-orphan",
        foreign_keys="[FinancialCategory.parent_id]"
    )
    
    # Relationship with transactions
    transactions = relationship(
        "app.modules.shared.financial.transactions.models.transaction.FinancialTransaction",
        back_populates="category",
        cascade="all, delete-orphan"
    )
    
    # Table indexes for performance
    __table_args__ = (
        Index(
            "ix_financial_categories_tenant_name_parent",
            "tenant_id",
            "name", 
            "parent_id",
            unique=True
        ),  # Name should be unique within a tenant and parent
        Index(
            "ix_financial_categories_tenant_display_order", 
            "tenant_id", 
            "display_order"
        ),
        Index(
            "ix_financial_categories_tenant_type", 
            "tenant_id", 
            "category_type"
        ),
        Index("ix_financial_categories_parent_id", "parent_id"),
    )
    
    def __repr__(self):
        return (
            f"<FinancialCategory(id={self.id}, "
            f"name='{self.name}', "
            f"type='{self.category_type}')>"
        )
