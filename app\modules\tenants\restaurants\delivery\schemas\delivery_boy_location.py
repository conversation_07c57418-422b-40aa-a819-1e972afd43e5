from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, Field, ConfigDict  # Adicionado ConfigDict


# Shared properties
class DeliveryBoyLocationBase(BaseModel):
    latitude: float = Field(..., json_schema_extra={"example": 34.052235})
    longitude: float = Field(..., json_schema_extra={"example": -118.243683})
    accuracy: Optional[float] = Field(None, json_schema_extra={"example": 5.0})
    timestamp: Optional[datetime] = Field(
        None, json_schema_extra={"example": "2023-10-26T10:30:00Z"}
    )


# Properties to receive on item creation
class DeliveryBoyLocationCreate(DeliveryBoyLocationBase):
    pass


# Properties to return to client
class DeliveryBoyLocationRead(DeliveryBoyLocationBase):
    id: UUID
    tenant_id: UUID
    delivery_boy_id: UUID
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)
