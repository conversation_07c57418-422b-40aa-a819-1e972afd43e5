import uuid
from sqlalchemy import Column, String, ForeignKey, Numeric, DateTime, Integer, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from datetime import datetime

from app.db.base import Base


class Refund(Base):
    """
    Modelo para representar um estorno/reembolso de uma transação de venda.
    Permite múltiplos estornos parciais para uma mesma transação.
    """

    __tablename__ = "refunds"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    transaction_id = Column(
        UUID(as_uuid=True),
        ForeignKey("sale_transactions.id"),
        nullable=False,
        index=True,
    )
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    amount = Column(Numeric(10, 2), nullable=False)  # Valor do estorno
    reason = Column(String, nullable=False)  # Motivo do estorno
    created_by = Column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=True
    )  # Usuário que criou o estorno
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    # Relacionamentos
    transaction = relationship("SaleTransaction", back_populates="refunds")
    tenant = relationship("Tenant")
    creator = relationship("User", foreign_keys=[created_by])
    items = relationship("RefundItem", back_populates="refund", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Refund(id={self.id}, transaction_id={self.transaction_id}, amount={self.amount})>"


class RefundItem(Base):
    """
    Model for refund items. Represents individual items in a refund.
    """

    __tablename__ = "refund_items"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    refund_id = Column(UUID(as_uuid=True), ForeignKey("refunds.id"), nullable=False, index=True)
    sale_item_id = Column(
        UUID(as_uuid=True), ForeignKey("sale_items.id"), nullable=True, index=True
    )
    product_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    quantity = Column(Integer, nullable=False)
    unit_price = Column(Numeric(10, 2), nullable=False)
    notes = Column(Text, nullable=True)

    # Relationships
    refund = relationship("Refund", back_populates="items")
    sale_item = relationship("SaleItem")
