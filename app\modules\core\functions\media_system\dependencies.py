"""
Media System Dependencies

Injeção de dependência para o sistema de mídia.
"""

from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_db

from .services import (
    MediaContextService,
    MediaUploadService,
    MediaProcessingService,
    MediaDirectoryService
)


def get_media_context_service(db: AsyncSession = Depends(get_db)) -> MediaContextService:
    """
    Obtém instância do serviço de contexto de mídia.

    Args:
        db: Sessão do banco de dados

    Returns:
        MediaContextService: Instância do serviço
    """
    return MediaContextService(db)


def get_media_upload_service(db: AsyncSession = Depends(get_db)) -> MediaUploadService:
    """
    Obtém instância do serviço de upload de mídia.
    
    Args:
        db: Sessão do banco de dados
        
    Returns:
        MediaUploadService: Instância do serviço
    """
    return MediaUploadService(db)


def get_media_processing_service(db: AsyncSession = Depends(get_db)) -> MediaProcessingService:
    """
    Obtém instância do serviço de processamento de mídia.

    Args:
        db: Sessão do banco de dados

    Returns:
        MediaProcessingService: Instância do serviço
    """
    return MediaProcessingService(db)


def get_media_directory_service(db: AsyncSession = Depends(get_db)) -> MediaDirectoryService:
    """
    Obtém instância do serviço de diretório de mídia.
    
    Args:
        db: Sessão do banco de dados
        
    Returns:
        MediaDirectoryService: Instância do serviço
    """
    return MediaDirectoryService(db)
