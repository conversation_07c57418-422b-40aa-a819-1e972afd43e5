'use client';

import React, { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { 
  XMarkIcon, 
  PlusIcon, 
  MinusIcon, 
  TrashIcon,
  ShoppingBagIcon,
  CreditCardIcon
} from '@heroicons/react/24/outline';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { useEshopCart } from '@/contexts/EshopCartContext';
import { formatCurrency } from '@/lib/utils';

export function EshopCartSidebar() {
  const router = useRouter();
  const { 
    cart, 
    isOpen, 
    isLoading,
    marketContext,
    closeCart, 
    updateItemQuantity, 
    removeItem, 
    clearCart 
  } = useEshopCart();

  const handleCheckout = () => {
    closeCart();
    router.push('/eshop/checkout');
  };

  const handleQuantityChange = (itemId: string, newQuantity: number) => {
    updateItemQuantity(itemId, newQuantity);
  };

  const handleRemoveItem = (itemId: string) => {
    removeItem(itemId);
  };

  const totalItems = cart?.total_items || 0;
  const totalAmount = cart?.total_amount || 0;
  const items = cart?.items || [];

  return (
    <Transition.Root show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={closeCart}>
        <Transition.Child
          as={Fragment}
          enter="ease-in-out duration-500"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in-out duration-500"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-hidden">
          <div className="absolute inset-0 overflow-hidden">
            <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-in-out duration-500 sm:duration-700"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in-out duration-500 sm:duration-700"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="pointer-events-auto w-screen max-w-md">
                  <div className="flex h-full flex-col overflow-y-scroll bg-white shadow-xl">
                    {/* Header */}
                    <div className="flex-1 overflow-y-auto px-4 py-6 sm:px-6">
                      <div className="flex items-start justify-between">
                        <Dialog.Title className="text-lg font-medium text-gray-900">
                          Carrinho {marketContext === 'b2b' ? 'B2B' : 'B2C'}
                        </Dialog.Title>
                        <div className="ml-3 flex h-7 items-center">
                          <button
                            type="button"
                            className="relative -m-2 p-2 text-gray-400 hover:text-gray-500"
                            onClick={closeCart}
                          >
                            <span className="absolute -inset-0.5" />
                            <span className="sr-only">Fechar painel</span>
                            <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                          </button>
                        </div>
                      </div>

                      {/* Cart Items */}
                      <div className="mt-8">
                        <div className="flow-root">
                          {items.length === 0 ? (
                            <div className="text-center py-12">
                              <ShoppingBagIcon className="mx-auto h-12 w-12 text-gray-400" />
                              <h3 className="mt-2 text-sm font-medium text-gray-900">
                                Carrinho vazio
                              </h3>
                              <p className="mt-1 text-sm text-gray-500">
                                Adicione produtos para começar suas compras.
                              </p>
                            </div>
                          ) : (
                            <ul role="list" className="-my-6 divide-y divide-gray-200">
                              {items.map((item) => (
                                <li key={item.id} className="flex py-6">
                                  <div className="h-24 w-24 flex-shrink-0 overflow-hidden rounded-md border border-gray-200">
                                    <img
                                      src={item.product_image_url || '/placeholder-product.jpg'}
                                      alt={item.product_name || 'Produto'}
                                      className="h-full w-full object-cover object-center"
                                    />
                                  </div>

                                  <div className="ml-4 flex flex-1 flex-col">
                                    <div>
                                      <div className="flex justify-between text-base font-medium text-gray-900">
                                        <h3>
                                          <span>{item.product_name}</span>
                                        </h3>
                                        <p className="ml-4">{formatCurrency(item.total_price)}</p>
                                      </div>
                                      {item.product_sku && (
                                        <p className="mt-1 text-sm text-gray-500">SKU: {item.product_sku}</p>
                                      )}
                                      {item.special_instructions && (
                                        <p className="mt-1 text-sm text-gray-500">
                                          Obs: {item.special_instructions}
                                        </p>
                                      )}
                                    </div>
                                    <div className="flex flex-1 items-end justify-between text-sm">
                                      <div className="flex items-center space-x-2">
                                        <button
                                          type="button"
                                          className="p-1 text-gray-400 hover:text-gray-500"
                                          onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                                          disabled={isLoading}
                                        >
                                          <MinusIcon className="h-4 w-4" />
                                        </button>
                                        <span className="text-gray-500 min-w-[2rem] text-center">
                                          {item.quantity}
                                        </span>
                                        <button
                                          type="button"
                                          className="p-1 text-gray-400 hover:text-gray-500"
                                          onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                                          disabled={isLoading}
                                        >
                                          <PlusIcon className="h-4 w-4" />
                                        </button>
                                      </div>

                                      <div className="flex">
                                        <button
                                          type="button"
                                          className="font-medium text-red-600 hover:text-red-500"
                                          onClick={() => handleRemoveItem(item.id)}
                                          disabled={isLoading}
                                        >
                                          <TrashIcon className="h-4 w-4" />
                                        </button>
                                      </div>
                                    </div>
                                  </div>
                                </li>
                              ))}
                            </ul>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Footer */}
                    {items.length > 0 && (
                      <div className="border-t border-gray-200 px-4 py-6 sm:px-6">
                        <div className="flex justify-between text-base font-medium text-gray-900">
                          <p>Subtotal</p>
                          <p>{formatCurrency(cart?.subtotal || 0)}</p>
                        </div>
                        {cart?.tax_amount && cart.tax_amount > 0 && (
                          <div className="flex justify-between text-sm text-gray-500">
                            <p>Impostos</p>
                            <p>{formatCurrency(cart.tax_amount)}</p>
                          </div>
                        )}
                        {cart?.discount_amount && cart.discount_amount > 0 && (
                          <div className="flex justify-between text-sm text-green-600">
                            <p>Desconto</p>
                            <p>-{formatCurrency(cart.discount_amount)}</p>
                          </div>
                        )}
                        <div className="flex justify-between text-lg font-bold text-gray-900 border-t border-gray-200 pt-2 mt-2">
                          <p>Total</p>
                          <p>{formatCurrency(totalAmount)}</p>
                        </div>
                        <p className="mt-0.5 text-sm text-gray-500">
                          Frete e impostos calculados no checkout.
                        </p>
                        <div className="mt-6 space-y-3">
                          <Button
                            onClick={handleCheckout}
                            className="w-full"
                            disabled={isLoading}
                          >
                            <CreditCardIcon className="h-4 w-4 mr-2" />
                            Finalizar Compra
                          </Button>
                          <Button
                            variant="outline"
                            onClick={clearCart}
                            className="w-full"
                            disabled={isLoading}
                          >
                            Limpar Carrinho
                          </Button>
                        </div>
                        <div className="mt-6 flex justify-center text-center text-sm text-gray-500">
                          <p>
                            ou{' '}
                            <button
                              type="button"
                              className="font-medium text-indigo-600 hover:text-indigo-500"
                              onClick={closeCart}
                            >
                              Continuar Comprando
                              <span aria-hidden="true"> &rarr;</span>
                            </button>
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
