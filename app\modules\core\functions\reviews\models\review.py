"""
Review Model

Sistema de avaliações imutáveis para produtos do eshop.
Reviews não podem ser editadas após submissão.
"""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Optional
from uuid import UUID, uuid4

from sqlalchemy import Column, String, Text, DateTime, Integer, Boolean, ForeignKey, Numeric
from sqlalchemy.dialects.postgresql import UUID as PostgreSQLUUID
from sqlalchemy.orm import relationship

from app.db.base import Base


class ReviewStatus(str, Enum):
    """Status da review"""
    ACTIVE = "active"
    HIDDEN_TEXT = "hidden_text"  # Texto oculto, nota permanece
    HIDDEN_COMPLETE = "hidden_complete"  # Review completamente oculta
    REPORTED = "reported"  # Denunciada, aguardando moderação


class Review(Base):
    """
    Modelo de Review para produtos do eshop.
    
    Características:
    - Imutável após criação (não pode ser editada)
    - Admin pode ocultar texto ou review completa
    - Nota sempre permanece para cálculo de média
    - Sistema de denúncia integrado
    """
    __tablename__ = "eshop_reviews"

    id: UUID = Column(
        PostgreSQLUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        index=True
    )
    
    # Relacionamento com usuário
    user_id: UUID = Column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    # Adicionado relacionamento com produto do eshop
    product_id: Optional[UUID] = Column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("eshop_products.id", ondelete="CASCADE"),
        nullable=True,
        index=True
    )
    
    # Dados da review
    rating: int = Column(Integer, nullable=False)  # 1-5 estrelas
    comment: str = Column(Text, nullable=True)
    review_type: str = Column(String(50), nullable=False)  # 'product', 'service', 'tenant', etc.
    
    # Campos genéricos para associação
    entity_type: Optional[str] = Column(String(50), nullable=True)  # 'product', 'menu_item', 'service'
    entity_id: Optional[UUID] = Column(PostgreSQLUUID(as_uuid=True), nullable=True)
    tenant_id: Optional[UUID] = Column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("tenants.id", ondelete="CASCADE"),
        nullable=True,
        index=True
    )
    
    # Timestamps
    created_at: datetime = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at: datetime = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Metadados opcionais (para funcionalidades avançadas futuras)
    status: Optional[ReviewStatus] = Column(String(20), default=ReviewStatus.ACTIVE, nullable=True)
    verified_purchase: Optional[bool] = Column(Boolean, default=False, nullable=True)
    order_id: Optional[UUID] = Column(PostgreSQLUUID(as_uuid=True), nullable=True)
    
    # Moderação (opcional)
    moderated_by: Optional[UUID] = Column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True
    )
    moderated_at: Optional[datetime] = Column(DateTime, nullable=True)
    moderation_reason: Optional[str] = Column(Text, nullable=True)
    
    # Métricas (opcional)
    report_count: Optional[int] = Column(Integer, default=0, nullable=True)
    helpful_count: Optional[int] = Column(Integer, default=0, nullable=True)
    not_helpful_count: Optional[int] = Column(Integer, default=0, nullable=True)
    
    # Relacionamentos SQLAlchemy
    user = relationship("User", foreign_keys=[user_id])
    tenant = relationship("Tenant", foreign_keys=[tenant_id])
    moderator = relationship("User", foreign_keys=[moderated_by])
    product = relationship("app.modules.core.eshop.models.product.Product", back_populates="reviews")
    
    def __repr__(self) -> str:
        return f"<Review(id={self.id}, product_id={self.product_id}, rating={self.rating})>"
    
    @property
    def is_visible(self) -> bool:
        """Verifica se a review está visível para o público"""
        return self.status == ReviewStatus.ACTIVE
    
    @property
    def text_visible(self) -> bool:
        """Verifica se o texto da review está visível"""
        return self.status in [ReviewStatus.ACTIVE, ReviewStatus.REPORTED]
    
    @property
    def rating_counts_for_average(self) -> bool:
        """Verifica se a nota conta para cálculo de média"""
        # Nota sempre conta, mesmo com texto oculto
        return self.status != ReviewStatus.HIDDEN_COMPLETE
    
    def hide_text(self, moderator_id: UUID, reason: str) -> None:
        """Oculta apenas o texto da review, mantendo a nota"""
        self.status = ReviewStatus.HIDDEN_TEXT
        self.moderated_by = moderator_id
        self.moderated_at = datetime.utcnow()
        self.moderation_reason = reason
    
    def hide_complete(self, moderator_id: UUID, reason: str) -> None:
        """Oculta a review completamente"""
        self.status = ReviewStatus.HIDDEN_COMPLETE
        self.moderated_by = moderator_id
        self.moderated_at = datetime.utcnow()
        self.moderation_reason = reason
    
    def restore(self, moderator_id: UUID) -> None:
        """Restaura a review para ativa"""
        self.status = ReviewStatus.ACTIVE
        self.moderated_by = moderator_id
        self.moderated_at = datetime.utcnow()
        self.moderation_reason = "Review restaurada"
    
    def add_report(self) -> None:
        """Adiciona uma denúncia à review"""
        self.report_count += 1
        if self.report_count >= 5:  # Threshold para moderação automática
            self.status = ReviewStatus.REPORTED
    
    def mark_helpful(self, helpful: bool) -> None:
        """Marca review como útil ou não útil"""
        if helpful:
            self.helpful_count += 1
        else:
            self.not_helpful_count += 1
    
    @property
    def helpfulness_ratio(self) -> float:
        """Calcula a proporção de utilidade da review"""
        total_votes = self.helpful_count + self.not_helpful_count
        if total_votes == 0:
            return 0.0
        return self.helpful_count / total_votes