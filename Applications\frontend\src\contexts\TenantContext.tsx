"use client"

import React, { createContext, useContext, useState, useEffect } from 'react'

interface Tenant {
  id: string
  name: string
  slug: string
  domain?: string
  settings?: Record<string, any>
}

interface TenantContextType {
  tenant: Tenant | null
  setTenant: (tenant: Tenant | null) => void
  isLoading: boolean
}

const TenantContext = createContext<TenantContextType | undefined>(undefined)

export function TenantProvider({ children }: { children: React.ReactNode }) {
  const [tenant, setTenant] = useState<Tenant | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Simular carregamento do tenant
    // Em uma implementação real, isso viria de uma API ou localStorage
    const loadTenant = async () => {
      try {
        // Placeholder para tenant padrão
        const defaultTenant: Tenant = {
          id: '1aea26e9-e023-4af8-9eb8-3ee48e8fd8b5',
          name: 'Tenant <PERSON>ão',
          slug: 'default',
          domain: 'localhost'
        }
        setTenant(defaultTenant)
      } catch (error) {
        console.error('Erro ao carregar tenant:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadTenant()
  }, [])

  return (
    <TenantContext.Provider value={{ tenant, setTenant, isLoading }}>
      {children}
    </TenantContext.Provider>
  )
}

export function useTenant() {
  const context = useContext(TenantContext)
  if (context === undefined) {
    throw new Error('useTenant must be used within a TenantProvider')
  }
  return context
}
