"""
Core users module for managing user-related functionality.
"""

from app.modules.core.users.models.user import User
from app.modules.core.users.models.tenant_user_association import TenantUserAssociation
from app.modules.core.users.services.user_service import user_service
from app.modules.core.users.services.tenant_user_association_service import (
    tenant_user_association_service,
)
from app.modules.core.auth.dependencies.user_auth import get_current_active_user
from app.modules.core.auth.dependencies.role_auth import require_system_role, require_admin_user

__all__ = [
    "User",
    "TenantUserAssociation",
    "user_service",
    "tenant_user_association_service",
    "get_current_active_user",
    "require_system_role",
    "require_admin_user",
]
