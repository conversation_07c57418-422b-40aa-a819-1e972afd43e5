"""
Pydantic schemas for Digital Menu operations.
Supports multi-menu functionality with time-based scheduling.
"""

import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, ConfigDict


class DigitalMenuBase(BaseModel):
    """Base schema for Digital Menu, shared fields."""

    name: str = Field(
        ..., max_length=150, description="Name of the digital menu (e.g., Breakfast Menu)"
    )
    description: Optional[str] = Field(None, description="Optional description for the menu")
    is_active: bool = Field(True, description="Whether the menu is currently active")
    display_order: int = Field(0, description="Order in which the menu should be displayed")
    schedule_enabled: bool = Field(
        False, description="Whether time-based scheduling is enabled for this menu"
    )
    schedule_config: Optional[Dict[str, Any]] = Field(
        None, description="JSON configuration for menu scheduling (timezone, time ranges, days)"
    )


class DigitalMenuCreate(DigitalMenuBase):
    """Schema for creating a new Digital Menu."""

    # tenant_id will be added by the service based on the authenticated user


class DigitalMenuUpdate(BaseModel):
    """Schema for updating an existing Digital Menu. All fields are optional."""

    name: Optional[str] = Field(None, max_length=150)
    description: Optional[str] = None
    is_active: Optional[bool] = None
    display_order: Optional[int] = None
    schedule_enabled: Optional[bool] = None
    schedule_config: Optional[Dict[str, Any]] = None


class DigitalMenuRead(DigitalMenuBase):
    """Schema for reading a Digital Menu, includes the ID and metadata."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class DigitalMenuWithCategories(DigitalMenuRead):
    """Schema for reading a Digital Menu with its categories."""

    categories: List["MenuCategoryRead"] = Field(
        default_factory=list, description="List of categories in this menu"
    )


class DigitalMenuWithCategoriesPublic(DigitalMenuRead):
    """Schema for reading a Digital Menu with its categories for public API (without redundant data)."""

    categories: List["MenuCategoryReadPublic"] = Field(
        default_factory=list, description="List of categories in this menu with optimized structure"
    )


# Schedule configuration helper schemas
class MenuScheduleTimeRange(BaseModel):
    """Time range for menu availability."""
    
    start_time: str = Field(..., description="Start time in HH:MM format")
    end_time: str = Field(..., description="End time in HH:MM format")


class MenuScheduleDay(BaseModel):
    """Schedule configuration for a specific day."""
    
    day: str = Field(..., description="Day of week (monday, tuesday, etc.)")
    enabled: bool = Field(True, description="Whether the menu is available on this day")
    time_ranges: List[MenuScheduleTimeRange] = Field(
        default_factory=list, description="Time ranges when menu is available"
    )


class MenuScheduleConfig(BaseModel):
    """Complete schedule configuration for a digital menu."""
    
    timezone: str = Field("UTC", description="Timezone for schedule (e.g., America/New_York)")
    days: List[MenuScheduleDay] = Field(
        default_factory=list, description="Schedule configuration for each day"
    )
    
    
# Forward reference resolution
from app.modules.tenants.restaurants.menu.schemas.menu_category import MenuCategoryRead, MenuCategoryReadPublic
DigitalMenuWithCategories.model_rebuild()
DigitalMenuWithCategoriesPublic.model_rebuild()
