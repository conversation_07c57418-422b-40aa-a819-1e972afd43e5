"""Loyalty models for CRM module."""

import uuid
import enum
from datetime import datetime
from typing import TYPE_CHECKING, List, Optional
from sqlalchemy import (
    String,
    <PERSON>olean,
    ForeignKey,
    Text,
    Enum,
    Integer,
    DateTime,
    JSON,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, Mapped, mapped_column

from app.db.base import Base

if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.shared.crm.models.account import Account


class LoyaltyProgramType(str, enum.Enum):
    """Loyalty program type enum."""

    POINTS = "points"
    CASHBACK = "cashback"
    TIERED = "tiered"
    SUBSCRIPTION = "subscription"
    PUNCH_CARD = "punch_card"
    DISCOUNT = "discount"
    OTHER = "other"


class LoyaltyMembershipStatus(str, enum.Enum):
    """Loyalty membership status enum."""

    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"
    EXPIRED = "expired"


class LoyaltyTransactionType(str, enum.Enum):
    """Loyalty transaction type enum."""

    EARN = "earn"
    REDEEM = "redeem"
    EXPIRE = "expire"
    ADJUST = "adjust"
    BONUS = "bonus"


class LoyaltyProgram(Base):
    """Loyalty program model for CRM module."""

    __tablename__ = "crm_loyalty_programs"

    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)

    name: Mapped[str] = mapped_column(String, nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    program_type: Mapped[LoyaltyProgramType] = mapped_column(Enum(LoyaltyProgramType), nullable=False)

    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    start_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    end_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)

    earning_rules: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    redemption_rules: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    expiration_rules: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    tier_rules: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)

    # Relationships
    tenant: Mapped["Tenant"] = relationship()
    memberships: Mapped[List["LoyaltyMembership"]] = relationship(
        "LoyaltyMembership", back_populates="program", cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<LoyaltyProgram(id={self.id}, name='{self.name}', type='{self.program_type}')>"


class LoyaltyMembership(Base):
    """Loyalty membership model for CRM module."""

    __tablename__ = "crm_loyalty_memberships"

    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    program_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("crm_loyalty_programs.id"), nullable=False)
    account_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("crm_accounts.id"), nullable=False)

    membership_number: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    status: Mapped[LoyaltyMembershipStatus] = mapped_column(
        Enum(LoyaltyMembershipStatus),
        nullable=False,
        default=LoyaltyMembershipStatus.ACTIVE,
    )

    points_balance: Mapped[int] = mapped_column(Integer, default=0)
    tier_level: Mapped[Optional[str]] = mapped_column(String, nullable=True)

    join_date: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=datetime.utcnow)
    expiry_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    last_activity_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)

    # Relationships
    tenant: Mapped["Tenant"] = relationship()
    program: Mapped["LoyaltyProgram"] = relationship("LoyaltyProgram", back_populates="memberships")
    account: Mapped["Account"] = relationship("Account", back_populates="loyalty_memberships")
    transactions: Mapped[List["LoyaltyTransaction"]] = relationship(
        "LoyaltyTransaction", back_populates="membership", cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<LoyaltyMembership(id={self.id}, account_id='{self.account_id}', points={self.points_balance})>"


class LoyaltyTransaction(Base):
    """Loyalty transaction model for CRM module."""

    __tablename__ = "crm_loyalty_transactions"

    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    membership_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), ForeignKey("crm_loyalty_memberships.id"), nullable=False
    )

    transaction_type: Mapped[LoyaltyTransactionType] = mapped_column(Enum(LoyaltyTransactionType), nullable=False)
    points: Mapped[int] = mapped_column(Integer, nullable=False)

    reference_type: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    reference_id: Mapped[Optional[str]] = mapped_column(String, nullable=True)

    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    transaction_date: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=datetime.utcnow)

    # Relationships
    tenant: Mapped["Tenant"] = relationship()
    membership: Mapped["LoyaltyMembership"] = relationship("LoyaltyMembership", back_populates="transactions")

    def __repr__(self):
        return f"<LoyaltyTransaction(id={self.id}, type='{self.transaction_type}', points={self.points})>"

if TYPE_CHECKING:
    from app.modules.shared.crm.models.loyalty import LoyaltyMembership, LoyaltyProgram, LoyaltyTransaction
