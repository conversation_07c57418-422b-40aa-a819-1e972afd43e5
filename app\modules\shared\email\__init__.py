"""Email module for managing email services.

This module provides functionality for managing email domains, accounts, and aliases,
as well as sending and receiving emails through Postfix and Dovecot.
"""

from fastapi import APIRouter, FastAPI  # noqa: E402

from app.modules.shared.email.api.endpoints import router as email_router  # noqa: E402
from app.modules.shared.email.api.webmail import router as webmail_router
from app.modules.shared.email.websocket.handlers import register_email_handlers


def get_router() -> APIRouter:
    """Return the API router for the email module."""
    # Combine the email and webmail routers
    combined_router = APIRouter()
    combined_router.include_router(email_router)
    combined_router.include_router(webmail_router)
    return combined_router


def register_websocket_handlers(app: FastAPI) -> None:
    """Register WebSocket handlers for the email module."""
    register_email_handlers(app)
