"""
Help Center Celery Configuration

Configuração específica do Celery para o sistema de help center.
"""

from celery import Celery
from celery.schedules import crontab
from typing import Dict, Any

# Configuração das filas específicas do help center
HELP_CENTER_QUEUES = {
    'help_center_notifications': {
        'exchange': 'help_center',
        'exchange_type': 'direct',
        'routing_key': 'notifications'
    },
    'help_center_emails': {
        'exchange': 'help_center',
        'exchange_type': 'direct',
        'routing_key': 'emails'
    },
    'help_center_cleanup': {
        'exchange': 'help_center',
        'exchange_type': 'direct',
        'routing_key': 'cleanup'
    },
    'help_center_analytics': {
        'exchange': 'help_center',
        'exchange_type': 'direct',
        'routing_key': 'analytics'
    },
    'help_center_files': {
        'exchange': 'help_center',
        'exchange_type': 'direct',
        'routing_key': 'files'
    }
}

# Roteamento de tarefas
HELP_CENTER_TASK_ROUTES = {
    # Notificações em tempo real
    'app.modules.core.help_center.tasks.help_center_tasks.process_ticket_notification': {
        'queue': 'help_center_notifications',
        'priority': 8
    },
    'app.modules.core.help_center.tasks.help_center_tasks.send_websocket_notification': {
        'queue': 'help_center_notifications',
        'priority': 9
    },
    
    # Emails
    'app.modules.core.help_center.tasks.help_center_tasks.send_ticket_email_notification': {
        'queue': 'help_center_emails',
        'priority': 6
    },
    'app.modules.core.help_center.tasks.help_center_tasks.send_daily_digest_email': {
        'queue': 'help_center_emails',
        'priority': 4
    },
    
    # Limpeza e manutenção
    'app.modules.core.help_center.tasks.help_center_tasks.cleanup_expired_messages': {
        'queue': 'help_center_cleanup',
        'priority': 2
    },
    'app.modules.core.help_center.tasks.help_center_tasks.cleanup_expired_tickets': {
        'queue': 'help_center_cleanup',
        'priority': 2
    },
    'app.modules.core.help_center.tasks.help_center_tasks.cleanup_orphaned_files': {
        'queue': 'help_center_cleanup',
        'priority': 3
    },
    
    # Analytics e relatórios
    'app.modules.core.help_center.tasks.help_center_tasks.generate_help_center_report': {
        'queue': 'help_center_analytics',
        'priority': 3
    },
    'app.modules.core.help_center.tasks.help_center_tasks.update_metrics_cache': {
        'queue': 'help_center_analytics',
        'priority': 5
    },
    
    # Processamento de arquivos
    'app.modules.core.help_center.tasks.help_center_tasks.process_uploaded_file': {
        'queue': 'help_center_files',
        'priority': 7
    },
    'app.modules.core.help_center.tasks.help_center_tasks.generate_file_thumbnails': {
        'queue': 'help_center_files',
        'priority': 4
    }
}

# Tarefas periódicas
HELP_CENTER_BEAT_SCHEDULE = {
    # Limpeza de mensagens expiradas - diariamente às 2:00
    'cleanup-expired-messages': {
        'task': 'app.modules.core.help_center.tasks.help_center_tasks.cleanup_expired_messages',
        'schedule': crontab(hour=2, minute=0),
        'options': {'queue': 'help_center_cleanup'}
    },
    
    # Limpeza de tickets expirados - semanalmente aos domingos às 3:00
    'cleanup-expired-tickets': {
        'task': 'app.modules.core.help_center.tasks.help_center_tasks.cleanup_expired_tickets',
        'schedule': crontab(hour=3, minute=0, day_of_week=0),
        'options': {'queue': 'help_center_cleanup'}
    },
    
    # Limpeza de arquivos órfãos - diariamente às 4:00
    'cleanup-orphaned-files': {
        'task': 'app.modules.core.help_center.tasks.help_center_tasks.cleanup_orphaned_files',
        'schedule': crontab(hour=4, minute=0),
        'options': {'queue': 'help_center_cleanup'}
    },
    
    # Atualização de cache de métricas - a cada 15 minutos
    'update-metrics-cache': {
        'task': 'app.modules.core.help_center.tasks.help_center_tasks.update_metrics_cache',
        'schedule': crontab(minute='*/15'),
        'options': {'queue': 'help_center_analytics'}
    },
    
    # Relatório diário - todos os dias às 8:00
    'daily-help-center-report': {
        'task': 'app.modules.core.help_center.tasks.help_center_tasks.generate_daily_report',
        'schedule': crontab(hour=8, minute=0),
        'options': {'queue': 'help_center_analytics'}
    },
    
    # Digest semanal por email - segundas às 9:00
    'weekly-digest-email': {
        'task': 'app.modules.core.help_center.tasks.help_center_tasks.send_weekly_digest',
        'schedule': crontab(hour=9, minute=0, day_of_week=1),
        'options': {'queue': 'help_center_emails'}
    }
}

# Configurações específicas do help center
HELP_CENTER_CELERY_CONFIG = {
    # Configurações de retry
    'task_default_retry_delay': 60,  # 1 minuto
    'task_max_retries': 3,
    
    # Configurações de timeout
    'task_soft_time_limit': 300,  # 5 minutos
    'task_time_limit': 600,       # 10 minutos
    
    # Configurações de serialização
    'task_serializer': 'json',
    'result_serializer': 'json',
    'accept_content': ['json'],
    
    # Configurações de resultado
    'result_expires': 3600,  # 1 hora
    'result_backend_transport_options': {
        'master_name': 'mymaster',
        'visibility_timeout': 3600,
    },
    
    # Configurações de worker
    'worker_prefetch_multiplier': 1,
    'worker_max_tasks_per_child': 1000,
    'worker_disable_rate_limits': False,
    
    # Configurações de monitoramento
    'worker_send_task_events': True,
    'task_send_sent_event': True,
    
    # Configurações de log
    'worker_log_format': '[%(asctime)s: %(levelname)s/%(processName)s] %(message)s',
    'worker_task_log_format': '[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s',
    
    # Configurações de segurança
    'worker_hijack_root_logger': False,
    'worker_log_color': False,
}


def configure_help_center_celery(app: Celery) -> None:
    """
    Configura o Celery para as tarefas do help center.
    
    Args:
        app: Instância do Celery
    """
    # Adicionar filas
    if not hasattr(app.conf, 'task_routes'):
        app.conf.task_routes = {}
    app.conf.task_routes.update(HELP_CENTER_TASK_ROUTES)
    
    # Adicionar schedule das tarefas periódicas
    if not hasattr(app.conf, 'beat_schedule'):
        app.conf.beat_schedule = {}
    app.conf.beat_schedule.update(HELP_CENTER_BEAT_SCHEDULE)
    
    # Aplicar configurações específicas
    app.conf.update(HELP_CENTER_CELERY_CONFIG)
    
    # Configurar filas
    app.conf.task_create_missing_queues = True
    
    print("✅ Help Center Celery configuration applied")


def get_help_center_worker_config() -> Dict[str, Any]:
    """
    Retorna configuração específica para workers do help center.
    
    Returns:
        Dicionário com configurações do worker
    """
    return {
        'queues': list(HELP_CENTER_QUEUES.keys()),
        'concurrency': 4,  # 4 workers por processo
        'max_tasks_per_child': 1000,
        'prefetch_multiplier': 1,
        'optimization': 'fair',
        'pool': 'prefork',  # ou 'gevent' para I/O intensivo
        'autoscale': '10,2',  # máximo 10, mínimo 2 workers
    }


def get_help_center_beat_config() -> Dict[str, Any]:
    """
    Retorna configuração específica para o beat scheduler do help center.
    
    Returns:
        Dicionário com configurações do beat
    """
    return {
        'schedule': HELP_CENTER_BEAT_SCHEDULE,
        'scheduler': 'django_celery_beat.schedulers:DatabaseScheduler',
        'max_interval': 300,  # 5 minutos
    }


# Configurações de monitoramento
HELP_CENTER_MONITORING = {
    'flower': {
        'port': 5555,
        'broker_api': 'redis://localhost:6379/0',
        'basic_auth': ['admin:admin'],
        'url_prefix': '/help-center-flower'
    },
    'prometheus': {
        'metrics_port': 8080,
        'metrics_path': '/help-center-metrics'
    }
}


# Configurações de alertas
HELP_CENTER_ALERTS = {
    'queue_length_threshold': 100,  # Alerta se fila > 100 tarefas
    'task_failure_threshold': 10,   # Alerta se > 10 falhas por hora
    'response_time_threshold': 300, # Alerta se tempo > 5 minutos
    'disk_usage_threshold': 0.8,   # Alerta se uso de disco > 80%
}
