# Core - Users

**Categoria:** Core
**Módulo:** Users
**Total de Endpoints:** 8
**Gerado em:** 08/06/2025, 17:26:01

## 📋 Endpoints

- [GET /api/users/](#get-apiusers) - Read Users
- [POST /api/users/](#post-apiusers) - Create User
- [GET /api/users/me](#get-apiusersme) - Read User Me
- [PUT /api/users/me](#put-apiusersme) - Update User Me
- [GET /api/users/me/tenant-associations](#get-apiusersmetenant-associations) - Read User Tenant Associations
- [GET /api/users/me/tenants](#get-apiusersmetenants) - Read User Tenants
- [GET /api/users/{user_id}](#get-apiusersuser-id) - Read User By Id
- [PUT /api/users/{user_id}](#put-apiusersuser-id) - Update User

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### User

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `email` | string | ✅ | - |
| `full_name` | unknown | ❌ | - |
| `phone_number` | unknown | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `system_role` | string | ❌ | - |
| `data_sharing_consent` | boolean | ❌ | - |
| `id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### UserCreate

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `email` | string | ✅ | - |
| `full_name` | unknown | ❌ | - |
| `phone_number` | unknown | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `system_role` | string | ❌ | - |
| `data_sharing_consent` | boolean | ❌ | - |
| `password` | string | ✅ | - |

### UserUpdate

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `email` | unknown | ❌ | - |
| `full_name` | unknown | ❌ | - |
| `phone_number` | unknown | ❌ | - |
| `password` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `system_role` | unknown | ❌ | - |
| `data_sharing_consent` | unknown | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/users/ {#get-apiusers}

**Resumo:** Read Users
**Descrição:** Retrieve users.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/users/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/users/ {#post-apiusers}

**Resumo:** Create User
**Descrição:** Create new user.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [UserCreate](#usercreate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [User](#user)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/users/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/users/me {#get-apiusersme}

**Resumo:** Read User Me
**Descrição:** Get current user.

**🔐 Autenticação:** Requerida

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [User](#user)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/users/me" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/users/me {#put-apiusersme}

**Resumo:** Update User Me
**Descrição:** Update own user.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [UserUpdate](#userupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [User](#user)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/users/me" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/users/me/tenant-associations {#get-apiusersmetenant-associations}

**Resumo:** Read User Tenant Associations
**Descrição:** Get current user's tenant associations with roles.

**🔐 Autenticação:** Requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/users/me/tenant-associations" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/users/me/tenants {#get-apiusersmetenants}

**Resumo:** Read User Tenants
**Descrição:** Get current user's tenants.

**🔐 Autenticação:** Requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/users/me/tenants" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/users/{user_id} {#get-apiusersuser-id}

**Resumo:** Read User By Id
**Descrição:** Get a specific user by id.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `user_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [User](#user)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/users/{user_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/users/{user_id} {#put-apiusersuser-id}

**Resumo:** Update User
**Descrição:** Update a user.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `user_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [UserUpdate](#userupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [User](#user)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/users/{user_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
