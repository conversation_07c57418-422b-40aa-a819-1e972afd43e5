"""Contact schemas for the Domain Rent module."""

import uuid  # noqa: E402
from datetime import datetime
from typing import Dict, Optional

from pydantic import BaseModel, Field, EmailStr, ConfigDict  # noqa: E402

from app.modules.shared.domain_rent.models.domain_contact import ContactType  # noqa: E402


class DomainContactBase(BaseModel):
    """Base schema for domain contact."""

    contact_type: ContactType = Field(..., description="Type of contact")
    first_name: str = Field(..., description="First name")
    last_name: str = Field(..., description="Last name")
    organization: Optional[str] = Field(None, description="Organization name")
    email: EmailStr = Field(..., description="Email address")
    phone: str = Field(..., description="Phone number")
    address_line_1: str = Field(..., description="Address line 1")
    address_line_2: Optional[str] = Field(None, description="Address line 2")
    city: str = Field(..., description="City")
    state_province: str = Field(..., description="State or province")
    postal_code: str = Field(..., description="Postal code")
    country: str = Field(..., description="Country code (2-letter ISO)")


class DomainContactCreate(DomainContactBase):
    """Schema for creating a domain contact."""

    domain_registration_id: uuid.UUID = Field(..., description="ID of the domain registration")


class DomainContactUpdate(BaseModel):
    """Schema for updating a domain contact."""

    first_name: Optional[str] = Field(None, description="First name")
    last_name: Optional[str] = Field(None, description="Last name")
    organization: Optional[str] = Field(None, description="Organization name")
    email: Optional[EmailStr] = Field(None, description="Email address")
    phone: Optional[str] = Field(None, description="Phone number")
    address_line_1: Optional[str] = Field(None, description="Address line 1")
    address_line_2: Optional[str] = Field(None, description="Address line 2")
    city: Optional[str] = Field(None, description="City")
    state_province: Optional[str] = Field(None, description="State or province")
    postal_code: Optional[str] = Field(None, description="Postal code")
    country: Optional[str] = Field(None, description="Country code (2-letter ISO)")


class DomainContactRead(DomainContactBase):
    """Schema for reading a domain contact."""

    id: uuid.UUID
    domain_registration_id: uuid.UUID
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class ContactUpdateRequest(BaseModel):
    """Schema for updating contacts for a domain."""

    domain_id: uuid.UUID = Field(..., description="ID of the domain to update")
    contacts: Dict[str, DomainContactUpdate] = Field(
        ..., description="Contact information to update"
    )
