import logging
import uuid
from typing import Annotated, List, Literal, Optional

from fastapi import Depends, HTTPException, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.auth_dependencies import get_current_active_user
from app.modules.core.auth.dependencies.role_auth import require_system_role
from app.modules.core.auth.security import permission_denied_exception
from app.modules.core.roles.models.roles import (
    RolePermissions,
    SystemRole,
    TenantRole,
    TenantStaffSubRole,
    TenantType,
)
from app.modules.core.tenants.dependencies.tenant_dependencies import get_current_tenant, require_tenant_role
from app.modules.core.tenants.models.tenant import Tenant
from app.modules.core.users.models.user import User
from app.modules.core.users.services.tenant_user_association_service import (
    tenant_user_association_service,
)
from app.modules.core.functions.inventory.services.inventory_service import InventoryService
from app.modules.core.functions.pos.services.sale_transaction_service import (
    SaleTransactionService,
)
from app.modules.tenants.restaurants.kds.services.kds_service import KdsService
from app.modules.tenants.restaurants.menu.services.digital_menu_service import (
    DigitalMenuService,
)
from app.modules.tenants.restaurants.menu.services.menu_service import MenuService
from app.modules.tenants.shops.services.category_service import CategoryService
from app.modules.tenants.shops.services.online_order_service import OnlineOrderService
from app.modules.tenants.shops.services.product_service import ProductService

logger = logging.getLogger(__name__)


def require_tenant_sub_role(
    required_sub_roles: List[TenantStaffSubRole],
    required_tenant_type: Optional[TenantType] = None,
):
    """
    Factory function to check if the current user has required staff_sub_roles within a tenant
    and if the tenant matches the required_tenant_type (if specified).
    Tenant ID is implicitly taken from X-Tenant-ID via get_current_tenant.
    Allows System Admins and Tenant Owners/Managers of the specific tenant automatically
    for any sub_role check if their main role grants broad permissions.
    """

    async def sub_role_checker(
        db: Annotated[AsyncSession, Depends(get_db)],
        current_user: Annotated[User, Depends(get_current_active_user)],
        current_tenant: Annotated[Tenant, Depends(get_current_tenant)],
    ) -> User:
        logger.debug(
            f"require_tenant_sub_role: User '{current_user.email}' "
            f"(SystemRole: {current_user.system_role}) "
            f"in Tenant '{current_tenant.name}' (Type: {current_tenant.tenant_type}). "
            f"Required sub_roles: {[sr.value for sr in required_sub_roles]}. "
            f"Required tenant_type: "
            f"{required_tenant_type.value if required_tenant_type else 'Any'}"
        )

        if required_tenant_type and current_tenant.tenant_type != required_tenant_type.value:
            logger.warning(
                f"require_tenant_sub_role: Tenant '{current_tenant.name}' type '{current_tenant.tenant_type}' "
                f"does not match required type '{required_tenant_type.value}'. Access denied."
            )
            raise permission_denied_exception

        if current_user.system_role == SystemRole.ADMIN.value:
            logger.info(
                f"require_tenant_sub_role: User '{current_user.email}' is SystemRole.ADMIN. "
                f"Access granted to tenant '{current_tenant.name}' for sub_role check."
            )
            return current_user

        if current_user.system_role == SystemRole.USER.value:
            association = (
                await tenant_user_association_service.get_association_by_user_and_tenant(
                    db=db, user_id=current_user.id, tenant_id=current_tenant.id
                )
            )

            if not association:
                logger.warning(
                    f"require_tenant_sub_role: User '{current_user.email}' has no association with tenant "
                    f"'{current_tenant.name}'. Access denied."
                )
                raise permission_denied_exception

            user_tenant_role_str = association.role.value if association.role else None
            user_staff_sub_role_str = (
                association.staff_sub_role.value if association.staff_sub_role else None
            )

            logger.debug(
                f"require_tenant_sub_role: User '{current_user.email}' in tenant '{current_tenant.name}' "
                f"has TenantRole: '{user_tenant_role_str}', "
                f"StaffSubRole: '{user_staff_sub_role_str}'."
            )

            if RolePermissions.has_permission(
                user_tenant_role_str,
                [TenantRole.OWNER.value, TenantRole.MANAGER.value],
            ):
                logger.info(
                    f"require_tenant_sub_role: User '{current_user.email}' with role '{user_tenant_role_str}' "
                    f"in tenant '{current_tenant.name}' has superseding permissions. "
                    "Access granted for sub_role check."
                )
                return current_user

            if user_tenant_role_str == TenantRole.STAFF.value:
                if not user_staff_sub_role_str or user_staff_sub_role_str not in [
                    sr.value for sr in required_sub_roles
                ]:
                    logger.warning(
                        f"require_tenant_sub_role: User '{current_user.email}' with TenantRole.STAFF "
                        f"and StaffSubRole '{user_staff_sub_role_str}' in tenant '{current_tenant.name}' "
                        "does not have one of the required sub_roles: "
                        f"{[sr.value for sr in required_sub_roles]}. Access denied."
                    )
                    raise permission_denied_exception

                logger.info(
                    f"require_tenant_sub_role: User '{current_user.email}' with TenantRole.STAFF "
                    f"and StaffSubRole '{user_staff_sub_role_str}' in tenant '{current_tenant.name}' "
                    "has required sub_role. Access granted."
                )
                return current_user
            else:
                logger.warning(
                    f"require_tenant_sub_role: User '{current_user.email}' with TenantRole '{user_tenant_role_str}' "
                    f"in tenant '{current_tenant.name}' is not STAFF, OWNER, or MANAGER. "
                    "Access denied for sub_role check."
                )
                raise permission_denied_exception

        logger.error(
            f"require_tenant_sub_role: User '{current_user.email}' has an unhandled system_role "
            f"'{current_user.system_role}'. Access denied in tenant '{current_tenant.name}'."
        )
        raise permission_denied_exception

    return sub_role_checker


def get_menu_service(db: Annotated[AsyncSession, Depends(get_db)]) -> MenuService:
    """Provides an instance of MenuService with a database session."""
    return MenuService(db)


def get_digital_menu_service(
    db: Annotated[AsyncSession, Depends(get_db)],
) -> DigitalMenuService:
    """Provides an instance of DigitalMenuService with a database session."""
    return DigitalMenuService(db)


def get_kds_service() -> KdsService:
    """Provides an instance of KdsService."""
    return KdsService()


def get_category_service() -> CategoryService:
    """Provides an instance of CategoryService."""
    return CategoryService()


async def get_product_service(db: AsyncSession = Depends(get_db)) -> ProductService:
    """Provides an instance of ProductService."""
    return ProductService(db)


def get_inventory_service(db: Annotated[AsyncSession, Depends(get_db)]) -> InventoryService:
    """Provides an instance of InventoryService."""
    return InventoryService(db_session=db)


def get_sale_transaction_service() -> SaleTransactionService:
    """Provides an instance of SaleTransactionService."""
    return SaleTransactionService()


def get_online_order_service(
    product_service: Annotated[ProductService, Depends(get_product_service)],
    inventory_service: Annotated[InventoryService, Depends(get_inventory_service)],
    sale_transaction_service: Annotated[
        SaleTransactionService, Depends(get_sale_transaction_service)
    ],
) -> OnlineOrderService:
    """Provides an instance of OnlineOrderService with its dependencies."""
    return OnlineOrderService(
        product_service=product_service,
        inventory_service=inventory_service,
        sale_transaction_service=sale_transaction_service,
    )
