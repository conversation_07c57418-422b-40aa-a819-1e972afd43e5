from typing import List, Optional, Dict, TYPE_CHECKING, Union, Any, Annotated
from enum import Enum
from fastapi import APIRouter, Depends, HTTPException, Query, Response, Header, Request
from fastapi.responses import J<PERSON>NResponse
from sqlalchemy.orm import Session
import hashlib
import json  # Para serializar o dicionário para o Redis
import time
import logging

from app.db.session import get_db
from app.core.dependencies import require_admin_user
from app.core.security import get_current_active_user
from app.core.roles import TenantRole
from app.models.tenant import Tenant

# Se require_admin_user não existir, precisaremos de algo como:
# from app.core.security import require_tenant_role
# from app.models.tenant import TenantRole # Supondo TenantRole.ADMIN
if TYPE_CHECKING:
    from app.models.user import User
from app.modules.i18n import schemas
from app.modules.i18n.services import (
    language_service,
    translation_key_service,
    translation_service,
    translation_suggestion_service,
)

# Idealmente, teríamos uma instância de cliente Redis injetável ou global
# from app.core.redis_client import redis_client # Exemplo

# Configuração do cliente Redis para o módulo i18n
try:
    from redis.asyncio import Redis as AsyncRedis
    from app.core.config import settings

    # Criar uma instância do cliente Redis usando a URL configurada
    redis_client = AsyncRedis.from_url(settings.REDIS_URL, decode_responses=True)
except (ImportError, AttributeError) as e:
    print(f"Warning: Redis client initialization failed: {e}")
    redis_client = None

logger = logging.getLogger(__name__)

# Router principal para i18n
router = APIRouter()

# Router para rotas de administração
admin_router = APIRouter(
    prefix="/admin",
    tags=["i18n_admin"],
    dependencies=[Depends(require_admin_user)],  # Restore dependency
)

# --- Include Admin Router into the main I18n Router ---
router.include_router(
    admin_router
)  # This makes admin routes available under the main router's prefix


# --- Funções Utilitárias para Caching ---
async def get_translations_version_hash(
    db: Session, lang_code: str, modules: Optional[List[str]]
) -> str:
    """
    Gera um hash de versão para as traduções de um idioma específico.
    Este hash é usado para invalidação de cache e ETags.

    Esta implementação gera um hash baseado no conteúdo atual das traduções.
    Em um ambiente de produção, poderia ser otimizado para usar um contador
    ou timestamp armazenado no Redis.
    """
    try:
        # Get the language ID from the code
        lang_result = await db.execute(
            select(models.Language).filter(models.Language.code == lang_code)
        )
        language = lang_result.scalar_one_or_none()

        if not language:
            # If language doesn't exist, return a fallback hash
            return hashlib.md5(f"{lang_code}-{str(modules)}-fallback".encode("utf-8")).hexdigest()

        # Check if we have a cached version hash in Redis
        if redis_client:
            cache_key = f"version_hash:{lang_code}"
            if modules:
                sorted_modules = sorted(modules)
                cache_key += f":modules:{':'.join(sorted_modules)}"

            cached_hash = await redis_client.get(cache_key)
            if cached_hash:
                return cached_hash

        # If no cached hash, generate one from the translations content
        translations_dict = await translation_service.get_translations_for_language_code(
            db=db, lang_code=lang_code, modules=modules, for_caching=True
        )

        # Sort to ensure consistent hash
        serialized_translations = json.dumps(translations_dict, sort_keys=True)
        hash_value = hashlib.md5(serialized_translations.encode("utf-8")).hexdigest()

        # Cache the hash if Redis is available
        if redis_client:
            cache_key = f"version_hash:{lang_code}"
            if modules:
                sorted_modules = sorted(modules)
                cache_key += f":modules:{':'.join(sorted_modules)}"

            await redis_client.set(cache_key, hash_value, ex=3600)  # Cache for 1 hour

        return hash_value

    except Exception as e:
        # Log the error in a real implementation
        logger.error(f"Error generating translation version hash: {e}")
        # Return a fallback hash
        return hashlib.md5(f"{lang_code}-{str(modules)}-fallback".encode("utf-8")).hexdigest()


# --- Rotas Públicas/Autenticadas ---


@router.get(
    "/translations/{lang_code}",
    response_model=Dict[str, str],
    summary="Get translations for a language code",
    tags=["i18n"],
)
async def get_translations_by_lang_code(
    request: Request,
    lang_code: str,
    modules: Optional[List[str]] = Query(None),
    db: Session = Depends(get_db),
    # current_user: User = Depends(get_current_active_user) # Se precisar de autenticação
):
    """
    Retrieves all translation key-value pairs for a given language code,
    optionally filtered by a list of modules.

    Implements ETag-based caching and Redis caching.
    """
    if not redis_client:  # Se o cliente Redis não estiver disponível
        # Comportamento sem cache
        translations = await translation_service.get_translations_for_language_code(
            db=db, lang_code=lang_code, modules=modules
        )
        if not translations:
            raise HTTPException(
                status_code=404, detail="Translations not found for this language code."
            )
        return translations

    # Gerar ETag
    version_hash = await get_translations_version_hash(db, lang_code, modules)
    current_etag = f'"{version_hash}"'  # ETags devem estar entre aspas

    # Verificar header If-None-Match
    if_none_match = request.headers.get("if-none-match")
    if if_none_match == current_etag:
        return Response(status_code=304, headers={"ETag": current_etag})

    # Verificar cache Redis
    cache_key = f"translations:{lang_code}:{version_hash}"
    if modules:  # Adicionar módulos à chave de cache se presentes
        sorted_modules = sorted(modules)  # Garantir ordem consistente
        cache_key += f":modules:{':'.join(sorted_modules)}"

    cached_translations = await redis_client.get(cache_key)
    if cached_translations:
        return Response(
            content=cached_translations,
            media_type="application/json",
            headers={"ETag": current_etag},
        )

    # Se não estiver no cache ou ETag não corresponder, buscar do serviço
    translations = await translation_service.get_translations_for_language_code(
        db=db, lang_code=lang_code, modules=modules
    )
    if not translations:
        # Ainda assim, retornamos um ETag para o estado "não encontrado" para evitar múltiplas chamadas.
        # Ou poderíamos levantar 404 diretamente, mas isso pode levar a cache de 404 no cliente.
        # Para esta implementação, vamos retornar um ETag de "vazio".
        empty_state_etag = f'"empty-{lang_code}-{version_hash}"'
        if if_none_match == empty_state_etag:
            return Response(status_code=304, headers={"ETag": empty_state_etag})

        # Cache o resultado vazio (ou um marcador de vazio) se desejar evitar re-consultas
        # await redis_client.set(cache_key, json.dumps({}), ex=3600) # Cache por 1 hora
        # return Response(content=json.dumps({}), media_type="application/json", headers={"ETag": empty_state_etag})
        raise HTTPException(
            status_code=404, detail="Translations not found for this language code."
        )

    # Armazenar no Redis
    translations_json = json.dumps(translations)
    await redis_client.set(cache_key, translations_json, ex=3600)  # Cache por 1 hora

    return Response(
        content=translations_json, media_type="application/json", headers={"ETag": current_etag}
    )


# --- Rotas de Administração ---


# Languages
@admin_router.post("/languages/", response_model=schemas.LanguageRead)
async def create_language(
    language_in: schemas.LanguageCreate,
    db: Session = Depends(get_db),
    # current_admin_user: User = Depends(require_admin_user) # Implicitly handled by router dependency
):
    from app.modules.i18n.services import language_service

    return await language_service.create_language(db=db, language_in=language_in)


@admin_router.get("/languages/", response_model=List[schemas.LanguageRead])
async def read_languages(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    # current_admin_user: User = Depends(require_admin_user) # Implicitly handled
):
    from app.modules.i18n.services import language_service

    return await language_service.get_languages(db=db, skip=skip, limit=limit)


@admin_router.get("/languages/{language_id}", response_model=schemas.LanguageRead)
async def read_language(
    language_id: int,
    db: Session = Depends(get_db),
    # current_admin_user: User = Depends(require_admin_user) # Implicitly handled
):
    from app.modules.i18n.services import language_service

    db_language = await language_service.get_language(db=db, language_id=language_id)
    if db_language is None:
        raise HTTPException(status_code=404, detail="Language not found")
    return db_language


@admin_router.put("/languages/{language_id}", response_model=schemas.LanguageRead)
async def update_language(
    language_id: int,
    language_in: schemas.LanguageUpdate,
    db: Session = Depends(get_db),
    # current_admin_user: User = Depends(require_admin_user) # Implicitly handled
):
    from app.modules.i18n.services import language_service

    db_language = await language_service.update_language(
        db=db, language_id=language_id, language_in=language_in
    )
    if db_language is None:
        raise HTTPException(status_code=404, detail="Language not found")
    return db_language


@admin_router.delete("/languages/{language_id}", status_code=204)
async def delete_language(
    language_id: int,
    db: Session = Depends(get_db),
    # language_service: services.LanguageService = Depends(),
    # current_admin_user: User = Depends(require_admin_user) # Implicitly handled
):
    success = await language_service.delete_language(db=db, language_id=language_id)
    if not success:
        raise HTTPException(status_code=404, detail="Language not found")
    return Response(status_code=204)


@admin_router.post("/languages/{language_id}/set-default", response_model=schemas.LanguageRead)
async def set_default_language(
    language_id: int,
    db: Session = Depends(get_db),
    # language_service: services.LanguageService = Depends(),
    # current_admin_user: User = Depends(require_admin_user) # Implicitly handled
):
    db_language = await language_service.set_default_language(db=db, language_id=language_id)
    if db_language is None:
        raise HTTPException(status_code=404, detail="Language not found")
    return db_language


# TranslationKeys
@admin_router.post("/keys/", response_model=schemas.TranslationKeyRead)
async def create_translation_key(
    key_in: schemas.TranslationKeyCreate,
    db: Session = Depends(get_db),
    # key_service: services.TranslationKeyService = Depends(),
    # current_admin_user: User = Depends(require_admin_user) # Implicitly handled
):
    from app.modules.i18n.services import translation_key_service as key_service

    return await key_service.create_key(db=db, key_in=key_in)


@admin_router.get("/keys/", response_model=List[schemas.TranslationKeyRead])
async def read_translation_keys(
    skip: int = 0,
    limit: int = 100,
    module: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    # key_service: services.TranslationKeyService = Depends(),
    # current_admin_user: User = Depends(require_admin_user) # Implicitly handled
):
    from app.modules.i18n.services import translation_key_service as key_service

    return await key_service.get_keys(db=db, module=module, skip=skip, limit=limit)


@admin_router.get("/keys/{key_id}", response_model=schemas.TranslationKeyRead)
async def read_translation_key(
    key_id: int,
    db: Session = Depends(get_db),
    # key_service: services.TranslationKeyService = Depends(),
    # current_admin_user: User = Depends(require_admin_user) # Implicitly handled
):
    from app.modules.i18n.services import translation_key_service as key_service

    db_key = await key_service.get_key(db=db, key_id=key_id)
    if db_key is None:
        raise HTTPException(status_code=404, detail="TranslationKey not found")
    return db_key


@admin_router.put("/keys/{key_id}", response_model=schemas.TranslationKeyRead)
async def update_translation_key(
    key_id: int,
    key_in: schemas.TranslationKeyUpdate,
    db: Session = Depends(get_db),
    # key_service: services.TranslationKeyService = Depends(),
    # current_admin_user: User = Depends(require_admin_user) # Implicitly handled
):
    from app.modules.i18n.services import translation_key_service as key_service

    db_key = await key_service.update_key(db=db, key_id=key_id, key_in=key_in)
    if db_key is None:
        raise HTTPException(status_code=404, detail="TranslationKey not found")
    return db_key


@admin_router.delete("/keys/{key_id}", status_code=204)
async def delete_translation_key(
    key_id: int,
    db: Session = Depends(get_db),
    # key_service: services.TranslationKeyService = Depends(),
    # current_admin_user: User = Depends(require_admin_user) # Implicitly handled
):
    from app.modules.i18n.services import translation_key_service as key_service

    success = await key_service.delete_key(db=db, key_id=key_id)
    if not success:
        raise HTTPException(status_code=404, detail="TranslationKey not found")
    return Response(status_code=204)


# Translations
@admin_router.post("/translations/", response_model=schemas.TranslationRead)
async def create_translation(
    translation_in: schemas.TranslationCreate,
    db: Session = Depends(get_db),
    # translation_service: services.TranslationService = Depends(),
    # current_admin_user: User = Depends(require_admin_user) # Implicitly handled
):
    # Invalidação de cache relevante aqui
    # Ex: await redis_client.delete(f"translations:{language_code_of_translation}:*")
    return await translation_service.create_translation(db=db, translation_in=translation_in)


@admin_router.get(
    "/translations/key/{key_id}/lang/{language_id}", response_model=schemas.TranslationRead
)
async def read_translation_by_key_and_lang(
    key_id: int,
    language_id: int,
    db: Session = Depends(get_db),
    # translation_service: services.TranslationService = Depends(),
    # current_admin_user: User = Depends(require_admin_user) # Implicitly handled
):
    db_translation = await translation_service.get_translation_by_key_and_lang(
        db=db, key_id=key_id, language_id=language_id
    )
    if db_translation is None:
        raise HTTPException(status_code=404, detail="Translation not found")
    return db_translation


@admin_router.put("/translations/{translation_id}", response_model=schemas.TranslationRead)
async def update_translation(
    translation_id: int,
    translation_in: schemas.TranslationUpdate,
    db: Session = Depends(get_db),
    # translation_service: services.TranslationService = Depends(),
    # current_admin_user: User = Depends(require_admin_user) # Implicitly handled
):
    # Invalidação de cache relevante aqui
    db_translation = await translation_service.update_translation(
        db=db, translation_id=translation_id, translation_in=translation_in
    )
    if db_translation is None:
        raise HTTPException(status_code=404, detail="Translation not found")
    # Invalidação do cache após a atualização bem-sucedida
    # lang_code = db_translation.language.code # Supondo que o serviço retorna o objeto com o idioma
    # await redis_client.delete(f"translations:{lang_code}:*") # Exemplo simplificado
    return db_translation


@admin_router.delete("/translations/{translation_id}", status_code=204)
async def delete_translation(
    translation_id: int,
    db: Session = Depends(get_db),
    # translation_service: services.TranslationService = Depends(),
    # current_admin_user: User = Depends(require_admin_user) # Implicitly handled
):
    # Invalidação de cache relevante aqui
    # Primeiro, obter a tradução para saber o lang_code antes de deletar
    # translation_to_delete = await translation_service.get_translation(db, translation_id)
    success = await translation_service.delete_translation(db=db, translation_id=translation_id)
    if not success:
        raise HTTPException(status_code=404, detail="Translation not found")
    # if translation_to_delete:
    #     await redis_client.delete(f"translations:{translation_to_delete.language.code}:*")
    return Response(status_code=204)


# TranslationSuggestions
@admin_router.get("/suggestions/", response_model=List[schemas.TranslationSuggestionRead])
async def read_suggestions(
    status: Optional[schemas.TranslationSuggestionStatus] = Query(None),
    lang_code: Optional[str] = Query(None),
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    # suggestion_service: services.TranslationSuggestionService = Depends(),
    # current_admin_user: User = Depends(require_admin_user) # Implicitly handled
):
    from app.modules.i18n.services import translation_suggestion_service as suggestion_service

    return await suggestion_service.get_suggestions(
        db=db, status=status, lang_code=lang_code, skip=skip, limit=limit
    )


@admin_router.get("/suggestions/{suggestion_id}", response_model=schemas.TranslationSuggestionRead)
async def read_suggestion(
    suggestion_id: int,
    db: Session = Depends(get_db),
    # suggestion_service: services.TranslationSuggestionService = Depends(),
    # current_admin_user: User = Depends(require_admin_user) # Implicitly handled
):
    from app.modules.i18n.services import translation_suggestion_service as suggestion_service

    db_suggestion = await suggestion_service.get_suggestion(db=db, suggestion_id=suggestion_id)
    if db_suggestion is None:
        raise HTTPException(status_code=404, detail="Suggestion not found")
    return db_suggestion


@admin_router.post(
    "/suggestions/{suggestion_id}/approve", response_model=schemas.TranslationSuggestionRead
)
async def approve_suggestion(
    suggestion_id: int,
    current_user: "User" = Depends(
        get_current_active_user
    ),  # For admin user already verified by router dependency
    db: Session = Depends(get_db),
    # suggestion_service: services.TranslationSuggestionService = Depends(),
    # current_admin_user: User = Depends(require_admin_user) # Implicitly handled by router dependency
):
    # Invalidação de cache relevante aqui após aprovação
    from app.modules.i18n.services import translation_suggestion_service as suggestion_service

    suggestion = await suggestion_service.approve_suggestion(
        db=db, suggestion_id=suggestion_id, approver_id=current_user.id
    )
    # if suggestion and suggestion.language: # Supondo que language é carregado
    #    await redis_client.delete(f"translations:{suggestion.language.code}:*")
    return suggestion


@admin_router.post(
    "/suggestions/{suggestion_id}/reject", response_model=schemas.TranslationSuggestionRead
)
async def reject_suggestion(
    suggestion_id: int,
    current_user: "User" = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    # suggestion_service: services.TranslationSuggestionService = Depends(),
    # current_admin_user: User = Depends(require_admin_user) # Implicitly handled
):
    from app.modules.i18n.services import translation_suggestion_service as suggestion_service

    return await suggestion_service.reject_suggestion(
        db=db, suggestion_id=suggestion_id, reviewer_id=current_user.id
    )


@admin_router.post(
    "/suggestions/{suggestion_id}/implement-manually",
    response_model=schemas.TranslationSuggestionRead,
)
async def implement_suggestion_manually(
    suggestion_id: int,
    current_user: "User" = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    # suggestion_service: services.TranslationSuggestionService = Depends(),
    # current_admin_user: User = Depends(require_admin_user) # Implicitly handled
):
    # Invalidação de cache pode ser necessária aqui também se isso implicar uma mudança
    from app.modules.i18n.services import translation_suggestion_service as suggestion_service

    return await suggestion_service.mark_as_manually_implemented(
        db=db, suggestion_id=suggestion_id, reviewer_id=current_user.id
    )


# --- Rotas para Usuários Sugerirem Traduções ---
@router.post(
    "/suggestions/",
    response_model=schemas.TranslationSuggestionRead,
    summary="Submit a new translation suggestion",
    tags=["i18n_suggestions"],
    dependencies=[Depends(get_current_active_user)],  # Requer usuário autenticado
)
async def create_user_suggestion(
    suggestion_in: schemas.TranslationSuggestionCreate,
    db: Session = Depends(get_db),
    current_user: "User" = Depends(get_current_active_user),
):
    # O serviço precisará de lógica para buscar TranslationKey e Language
    # a partir de key_string e lang_code.
    # E associar o current_user.id como suggested_by_id.
    from app.modules.i18n.services import translation_suggestion_service as suggestion_service

    return await suggestion_service.create_suggestion_from_client(
        db=db, suggestion_in=suggestion_in, suggested_by_id=current_user.id
    )
