import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/models/table_model.dart';
import '../../../../core/providers/tables_provider.dart';
import '../../../../core/router/app_router.dart';

class TablesPage extends ConsumerStatefulWidget {
  const TablesPage({super.key});

  @override
  ConsumerState<TablesPage> createState() => _TablesPageState();
}

class _TablesPageState extends ConsumerState<TablesPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String _selectedArea = 'all';
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    
    // Load tables when page initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(tablesProvider.notifier).loadTables();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final tablesState = ref.watch(tablesProvider);
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: const Text('Mesas'),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showAreaFilterDialog,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.read(tablesProvider.notifier).loadTables(),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: [
            Tab(
              child: _buildTabWithBadge(
                'Todas',
                tablesState.tables.length,
                theme.colorScheme.primary,
              ),
            ),
            Tab(
              child: _buildTabWithBadge(
                'Livres',
                tablesState.freeTables.length,
                Colors.green,
              ),
            ),
            Tab(
              child: _buildTabWithBadge(
                'Ocupadas',
                tablesState.occupiedTables.length,
                Colors.red,
              ),
            ),
            Tab(
              child: _buildTabWithBadge(
                'Reservadas',
                tablesState.reservedTables.length,
                Colors.orange,
              ),
            ),
            Tab(
              child: _buildTabWithBadge(
                'Limpeza',
                tablesState.cleaningTables.length,
                Colors.blue,
              ),
            ),
          ],
        ),
      ),
      body: tablesState.isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Statistics bar
                _buildStatisticsBar(tablesState),
                
                // Tables content
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildTablesGrid(tablesState.tables),
                      _buildTablesGrid(tablesState.freeTables),
                      _buildTablesGrid(tablesState.occupiedTables),
                      _buildTablesGrid(tablesState.reservedTables),
                      _buildTablesGrid(tablesState.cleaningTables),
                    ],
                  ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCreateTableDialog(),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildTabWithBadge(String title, int count, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(title),
        if (count > 0) ...[
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Text(
              count.toString(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildStatisticsBar(TablesState state) {
    final theme = Theme.of(context);
    final stats = state.statistics;
    
    return Container(
      padding: const EdgeInsets.all(16),
      color: theme.colorScheme.surface,
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'Total',
              stats['total'].toString(),
              Icons.table_restaurant,
              theme.colorScheme.primary,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              'Ocupação',
              '${(((stats['occupied'] ?? 0) / (stats['total'] ?? 1)) * 100).toInt()}%',
              Icons.people,
              Colors.orange,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              'Capacidade',
              '${stats['totalCapacity']}',
              Icons.person,
              Colors.blue,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 20,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTablesGrid(List<TableModel> tables) {
    if (tables.isEmpty) {
      return _buildEmptyState();
    }

    // Apply search and area filters
    final filteredTables = tables.where((table) {
      final matchesSearch = _searchQuery.isEmpty ||
          table.number.toString().contains(_searchQuery) ||
          table.name?.toLowerCase().contains(_searchQuery.toLowerCase()) == true;
      
      final matchesArea = _selectedArea == 'all' || table.area == _selectedArea;
      
      return matchesSearch && matchesArea;
    }).toList();

    if (filteredTables.isEmpty) {
      return _buildNoResultsState();
    }

    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(tablesProvider.notifier).loadTables();
      },
      child: GridView.builder(
        padding: const EdgeInsets.all(16),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 1.2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
        ),
        itemCount: filteredTables.length,
        itemBuilder: (context, index) {
          final table = filteredTables[index];
          return TableCard(
            table: table,
            onTap: () => _onTableTap(table),
            onStatusChange: (newStatus) => _onStatusChange(table, newStatus),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    final theme = Theme.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.table_restaurant_outlined,
            size: 64,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'Nenhuma mesa encontrada',
            style: theme.textTheme.titleLarge?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'As mesas aparecerão aqui quando forem criadas',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _showCreateTableDialog(),
            icon: const Icon(Icons.add),
            label: const Text('Criar Primeira Mesa'),
          ),
        ],
      ),
    );
  }

  Widget _buildNoResultsState() {
    final theme = Theme.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'Nenhum resultado encontrado',
            style: theme.textTheme.titleLarge?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tente ajustar os filtros de busca',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ),
          const SizedBox(height: 24),
          OutlinedButton(
            onPressed: () {
              setState(() {
                _searchQuery = '';
                _selectedArea = 'all';
                _searchController.clear();
              });
            },
            child: const Text('Limpar Filtros'),
          ),
        ],
      ),
    );
  }

  void _onTableTap(TableModel table) {
    _showTableDetailsBottomSheet(table);
  }

  void _onStatusChange(TableModel table, String newStatus) {
    ref.read(tablesProvider.notifier).updateTableStatus(table.id, newStatus);
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Buscar Mesas'),
        content: TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            hintText: 'Digite o número ou nome da mesa...',
            prefixIcon: Icon(Icons.search),
          ),
          autofocus: true,
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _searchQuery = '';
                _searchController.clear();
              });
              Navigator.pop(context);
            },
            child: const Text('Limpar'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fechar'),
          ),
        ],
      ),
    );
  }

  void _showAreaFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filtrar por Área'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('Todas'),
              leading: Radio<String>(
                value: 'all',
                groupValue: _selectedArea,
                onChanged: (value) {
                  setState(() {
                    _selectedArea = value!;
                  });
                },
              ),
            ),
            ListTile(
              title: const Text('Área Interna'),
              leading: Radio<String>(
                value: 'indoor',
                groupValue: _selectedArea,
                onChanged: (value) {
                  setState(() {
                    _selectedArea = value!;
                  });
                },
              ),
            ),
            ListTile(
              title: const Text('Área Externa'),
              leading: Radio<String>(
                value: 'outdoor',
                groupValue: _selectedArea,
                onChanged: (value) {
                  setState(() {
                    _selectedArea = value!;
                  });
                },
              ),
            ),
            ListTile(
              title: const Text('VIP'),
              leading: Radio<String>(
                value: 'vip',
                groupValue: _selectedArea,
                onChanged: (value) {
                  setState(() {
                    _selectedArea = value!;
                  });
                },
              ),
            ),
            ListTile(
              title: const Text('Bar'),
              leading: Radio<String>(
                value: 'bar',
                groupValue: _selectedArea,
                onChanged: (value) {
                  setState(() {
                    _selectedArea = value!;
                  });
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Aplicar'),
          ),
        ],
      ),
    );
  }

  void _showCreateTableDialog() {
    final numberController = TextEditingController();
    final nameController = TextEditingController();
    final capacityController = TextEditingController();
    String selectedArea = 'indoor';
    String selectedShape = 'round';
    
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Nova Mesa'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: numberController,
                  decoration: const InputDecoration(
                    labelText: 'Número da Mesa',
                    hintText: 'Ex: 1, 2, 3...',
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'Nome (Opcional)',
                    hintText: 'Ex: Mesa da Janela',
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: capacityController,
                  decoration: const InputDecoration(
                    labelText: 'Capacidade',
                    hintText: 'Número de pessoas',
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: selectedArea,
                  decoration: const InputDecoration(
                    labelText: 'Área',
                  ),
                  items: const [
                    DropdownMenuItem(
                      value: 'indoor',
                      child: Text('Área Interna'),
                    ),
                    DropdownMenuItem(
                      value: 'outdoor',
                      child: Text('Área Externa'),
                    ),
                    DropdownMenuItem(
                      value: 'vip',
                      child: Text('VIP'),
                    ),
                    DropdownMenuItem(
                      value: 'bar',
                      child: Text('Bar'),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      selectedArea = value!;
                    });
                  },
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: selectedShape,
                  decoration: const InputDecoration(
                    labelText: 'Formato',
                  ),
                  items: const [
                    DropdownMenuItem(
                      value: 'round',
                      child: Text('Redonda'),
                    ),
                    DropdownMenuItem(
                      value: 'square',
                      child: Text('Quadrada'),
                    ),
                    DropdownMenuItem(
                      value: 'rectangular',
                      child: Text('Retangular'),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      selectedShape = value!;
                    });
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancelar'),
            ),
            ElevatedButton(
              onPressed: () {
                final number = int.tryParse(numberController.text);
                final capacity = int.tryParse(capacityController.text);
                
                if (number != null && capacity != null) {
                  ref.read(tablesProvider.notifier).createTable(
                    number: number,
                    name: nameController.text.isEmpty ? 'Mesa $number' : nameController.text,
                    capacity: capacity,
                    area: selectedArea,
                    shape: selectedShape,
                  );
                  Navigator.pop(context);
                }
              },
              child: const Text('Criar'),
            ),
          ],
        ),
      ),
    );
  }

  void _showTableDetailsBottomSheet(TableModel table) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => TableDetailsBottomSheet(table: table),
    );
  }
}

class TableCard extends StatelessWidget {
  final TableModel table;
  final VoidCallback? onTap;
  final Function(String)? onStatusChange;

  const TableCard({
    super.key,
    required this.table,
    this.onTap,
    this.onStatusChange,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final statusColor = _getStatusColor(table.status);
    final areaColor = _getAreaColor(table.area ?? '');
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: statusColor.withValues(alpha: 0.3),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: statusColor.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Mesa ${table.number}',
                            style: theme.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          if (table.name?.isNotEmpty == true)
                            Text(
                              table.name!,
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                              ),
                            ),
                        ],
                      ),
                    ),
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: statusColor,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                // Status
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    table.statusDisplayName,
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: statusColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                
                const Spacer(),
                
                // Details
                Row(
                  children: [
                    Icon(
                      Icons.people,
                      size: 16,
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${table.capacity}',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: areaColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        table.areaDisplayName,
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: areaColor,
                          fontSize: 10,
                        ),
                      ),
                    ),
                  ],
                ),
                
                // Current order info
                if (table.currentOrderId?.isNotEmpty == true) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Icons.receipt,
                        size: 14,
                        color: theme.colorScheme.primary,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Pedido ativo',
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'available':
        return Colors.green;
      case 'occupied':
        return Colors.red;
      case 'reserved':
        return Colors.orange;
      case 'cleaning':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  Color _getAreaColor(String area) {
    switch (area) {
      case 'indoor':
        return Colors.blue;
      case 'outdoor':
        return Colors.green;
      case 'vip':
        return Colors.purple;
      case 'bar':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }
}

class TableDetailsBottomSheet extends ConsumerWidget {
  final TableModel table;

  const TableDetailsBottomSheet({
    super.key,
    required this.table,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final statusColor = _getStatusColor(table.status);
    
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Mesa ${table.number}',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (table.name?.isNotEmpty == true)
                      Text(
                        table.name!,
                        style: theme.textTheme.bodyLarge?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                      ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  table.statusDisplayName,
                  style: theme.textTheme.labelMedium?.copyWith(
                    color: statusColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Details
          _buildDetailRow(
            context,
            'Capacidade',
            '${table.capacity} pessoas',
            Icons.people,
          ),
          _buildDetailRow(
            context,
            'Área',
            table.areaDisplayName,
            Icons.location_on,
          ),
          _buildDetailRow(
            context,
            'Formato',
            table.shapeDisplayName,
            Icons.crop_square,
          ),
          
          if (table.assignedWaiterId?.isNotEmpty == true)
            _buildDetailRow(
              context,
              'Garçom',
              table.assignedWaiterId!,
              Icons.person,
            ),
          
          if (table.notes?.isNotEmpty == true) ...[
            const SizedBox(height: 16),
            Text(
              'Observações',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              table.notes!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ],
          
          const SizedBox(height: 32),
          
          // Actions
          Row(
            children: [
              if (table.status == 'available') ...[
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      context.pushNamed(
                        AppRoutes.createOrder,
                        queryParameters: {'tableId': table.id},
                      );
                    },
                    icon: const Icon(Icons.add),
                    label: const Text('Novo Pedido'),
                  ),
                ),
              ] else if (table.currentOrderId?.isNotEmpty == true) ...[
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      context.pushNamed(
                        AppRoutes.orderDetails,
                        pathParameters: {'orderId': table.currentOrderId!},
                      );
                    },
                    icon: const Icon(Icons.receipt),
                    label: const Text('Ver Pedido'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _showStatusChangeDialog(context, ref),
                    icon: const Icon(Icons.edit),
                    label: const Text('Alterar Status'),
                  ),
                ),
              ] else ...[
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _showStatusChangeDialog(context, ref),
                    icon: const Icon(Icons.edit),
                    label: const Text('Alterar Status'),
                  ),
                ),
              ],
            ],
          ),
          
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          ),
          const SizedBox(width: 12),
          Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
          const Spacer(),
          Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  void _showStatusChangeDialog(BuildContext context, WidgetRef ref) {
    final statuses = [
      'available',
      'occupied',
      'reserved',
      'cleaning',
    ];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Alterar Status'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: statuses.map((status) => ListTile(
            leading: Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: _getStatusColor(status),
                shape: BoxShape.circle,
              ),
            ),
            title: Text(_getStatusDisplayName(status)),
            selected: table.status == status,
            onTap: () {
              ref.read(tablesProvider.notifier).updateTableStatus(table.id, status);
              Navigator.pop(context);
              Navigator.pop(context);
            },
          )).toList(),
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'available':
        return Colors.green;
      case 'occupied':
        return Colors.red;
      case 'reserved':
        return Colors.orange;
      case 'cleaning':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  String _getStatusDisplayName(String status) {
    switch (status) {
      case 'available':
        return 'Livre';
      case 'occupied':
        return 'Ocupada';
      case 'reserved':
        return 'Reservada';
      case 'cleaning':
        return 'Limpeza';
      default:
        return 'Desconhecido';
    }
  }
}