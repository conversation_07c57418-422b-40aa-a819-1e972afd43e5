'use client';

import { useState, useEffect, useRef } from 'react';
import { MapPinIcon, MagnifyingGlassIcon, CheckIcon } from '@heroicons/react/24/outline';
import { searchAddresses, type AddressSuggestion } from '@/services/locationService';

interface Address {
  street: string;
  number?: string;
  complement?: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  latitude?: number;
  longitude?: number;
}

interface LocalAddressSuggestion {
  display_name: string;
  lat: string;
  lon: string;
  address: {
    house_number?: string;
    road?: string;
    city?: string;
    town?: string;
    village?: string;
    state?: string;
    postcode?: string;
    country?: string;
    country_code?: string;
  };
}

interface AddressAutocompleteProps {
  address: Address;
  onUpdate: (field: keyof Address, value: any) => void;
  onAddressSelect: (address: Address) => void;
  countryCode?: string;
  stateName?: string;
  cityName?: string;
}

export default function AddressAutocomplete({
  address,
  onUpdate,
  onAddressSelect,
  countryCode = 'US',
  stateName,
  cityName
}: AddressAutocompleteProps) {
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState<LocalAddressSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [hasUserInteracted, setHasUserInteracted] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Debug: Log address changes
  useEffect(() => {
    console.log('🔍 AddressAutocomplete: Address prop changed:', address);
    console.log('🔍 AddressAutocomplete: Number value:', address.number);
  }, [address]);

  // Debounce search - only if user has interacted
  useEffect(() => {
    if (!hasUserInteracted || query.length < 3) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    const timeoutId = setTimeout(() => {
      searchAddressesFunc(query);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [query, hasUserInteracted, address.number]); // Include number in dependencies

  const searchAddressesFunc = async (searchQuery: string) => {
    if (searchQuery.length < 3) return;

    setIsLoading(true);
    try {
      // Include number in search if available
      let fullSearchQuery = searchQuery;
      console.log('🔍 AddressAutocomplete: Original query:', searchQuery);
      console.log('🔍 AddressAutocomplete: Current address.number:', address.number);

      if (address.number && address.number.trim()) {
        // Try to include number in search for better results
        fullSearchQuery = `${address.number} ${searchQuery}`.trim();
        console.log('🔍 AddressAutocomplete: Including number in search:', fullSearchQuery);
      } else {
        console.log('🔍 AddressAutocomplete: No number to include, using original query');
      }

      const results = await searchAddresses(
        fullSearchQuery,
        countryCode || address.country,
        stateName,
        cityName
      );

      console.log('🔍 AddressAutocomplete: Search results:', results.length, results);
      setSuggestions(results);
      setShowSuggestions(results.length > 0);
      setSelectedIndex(-1);
    } catch (error) {
      console.error('Address search error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (value: string) => {
    setQuery(value);
    setHasUserInteracted(true);
    // Don't call onUpdate here - only update local state during typing
    // onUpdate will be called only when user selects from suggestions
    console.log('⌨️ AddressAutocomplete: User typing, query updated to:', value);
  };

  const selectSuggestion = (suggestion: LocalAddressSuggestion) => {
    console.log('🏠 AddressAutocomplete: Suggestion selected:', suggestion);
    console.log('🔄 AddressAutocomplete: Current address before update:', address);
    console.log('🔄 AddressAutocomplete: User has city:', address.city, 'suggestion has city:', suggestion.city);
    console.log('🔄 AddressAutocomplete: User has state:', address.state, 'suggestion has state:', suggestion.state);

    // Create incremental address - preserve user-selected data, only update what's missing
    const selectedAddress: Address = {
      ...address, // Keep existing data (like number, complement, user-selected city/state)
      street: suggestion.street,
      // PRESERVE user-selected city/state, only use suggestion if user hasn't selected
      city: address.city || suggestion.city,
      state: address.state || suggestion.state,
      // For zipCode, use suggestion if available (more accurate)
      zipCode: suggestion.zipCode || address.zipCode || '',
      latitude: suggestion.latitude,
      longitude: suggestion.longitude
    };

    console.log('📍 AddressAutocomplete: Incremental address created:', selectedAddress);
    console.log('✅ AddressAutocomplete: Preserved user city:', selectedAddress.city, 'state:', selectedAddress.state);

    // Notify parent component with complete address (UI update only)
    onAddressSelect(selectedAddress);

    // Update UI
    setQuery(suggestion.street);
    setShowSuggestions(false);
    setSelectedIndex(-1);
    setHasUserInteracted(false); // Reset interaction flag
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions || suggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
          selectSuggestion(suggestions[selectedIndex]);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedIndex(-1);
        break;
    }
  };

  const formatSuggestionDisplay = (suggestion: LocalAddressSuggestion) => {
    const main = suggestion.street;
    const secondaryParts = [];
    if (suggestion.neighborhood) secondaryParts.push(suggestion.neighborhood);
    if (suggestion.city) secondaryParts.push(suggestion.city);
    if (suggestion.zipCode) secondaryParts.push(suggestion.zipCode);
    const secondary = secondaryParts.join(', ');
    return { main, secondary };
  };

  // Initialize query from current address
  useEffect(() => {
    if (address.street && !query) {
      setQuery(address.street);
    }
  }, [address.street]);

  return (
    <div className="relative">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Street Address *
      </label>
      
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <MagnifyingGlassIcon className="h-4 w-4 text-gray-400" />
        </div>
        
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => handleInputChange(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={(e) => {
            // Remove readonly on focus (anti-autocomplete trick)
            e.target.removeAttribute('readonly');
            setHasUserInteracted(true);
            if (suggestions.length > 0 && hasUserInteracted) {
              setShowSuggestions(true);
            }
          }}
          onBlur={() => {
            // Delay hiding suggestions to allow for clicks
            setTimeout(() => setShowSuggestions(false), 200);
          }}
          placeholder="Start typing street name (number will be included automatically)..."
          autoComplete="new-password"
          autoCorrect="off"
          autoCapitalize="off"
          spellCheck="false"
          data-form-type="other"
          data-lpignore="true"
          data-1p-ignore="true"
          name={`street-search-${Math.random().toString(36).substr(2, 9)}`}
          readOnly
          className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
        />
        
        {isLoading && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600"></div>
          </div>
        )}
      </div>

      {/* Suggestions Dropdown */}
      {showSuggestions && suggestions.length > 0 && hasUserInteracted && (
        <div
          ref={suggestionsRef}
          className="absolute z-[9999] w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto"
        >
          {suggestions.map((suggestion, index) => {
            const { main, secondary } = formatSuggestionDisplay(suggestion);
            return (
              <div
                key={index}
                onClick={() => selectSuggestion(suggestion)}
                className={`px-4 py-3 cursor-pointer border-b border-gray-100 last:border-b-0 hover:bg-gray-50 ${
                  index === selectedIndex ? 'bg-primary-50 border-primary-200' : ''
                }`}
              >
                <div className="flex items-start space-x-3">
                  <MapPinIcon className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {main}
                    </p>
                    <p className="text-xs text-gray-500 truncate">
                      {secondary}
                    </p>
                  </div>
                  {index === selectedIndex && (
                    <CheckIcon className="h-4 w-4 text-primary-600 flex-shrink-0" />
                  )}
                </div>
              </div>
            );
          })}
        </div>
      )}


    </div>
  );
}
