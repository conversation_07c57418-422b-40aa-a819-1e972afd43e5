"""add_tcostumer_and_tvendor_supplier_b2b_models

Revision ID: e8f9a2b1c4d5
Revises: d7809fd8cdd3
Create Date: 2025-06-27 15:30:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'e8f9a2b1c4d5'
down_revision: Union[str, None] = 'd7809fd8cdd3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    
    # Create enum types for TCostumer
    tcostumer_status_enum = postgresql.ENUM(
        'pending', 'active', 'suspended', 'inactive', 'rejected',
        name='tcostumerstatus'
    )
    tcostumer_status_enum.create(op.get_bind())
    
    business_type_enum = postgresql.ENUM(
        'corporation', 'llc', 'partnership', 'sole_proprietorship', 
        'nonprofit', 'government', 'other',
        name='businesstype'
    )
    business_type_enum.create(op.get_bind())
    
    payment_terms_enum = postgresql.ENUM(
        'immediate', 'net_15', 'net_30', 'net_45', 'net_60', 'custom',
        name='paymentterms'
    )
    payment_terms_enum.create(op.get_bind())
    
    # Create enum types for TVendorSupplier
    tvendor_status_enum = postgresql.ENUM(
        'pending', 'active', 'suspended', 'inactive', 'rejected', 'under_review',
        name='tvendorstatus'
    )
    tvendor_status_enum.create(op.get_bind())
    
    supplier_type_enum = postgresql.ENUM(
        'manufacturer', 'distributor', 'wholesaler', 'retailer', 
        'dropshipper', 'service_provider', 'other',
        name='suppliertype'
    )
    supplier_type_enum.create(op.get_bind())
    
    verification_status_enum = postgresql.ENUM(
        'pending', 'documents_submitted', 'under_review', 
        'verified', 'rejected', 'expired',
        name='verificationstatus'
    )
    verification_status_enum.create(op.get_bind())
    
    # Create eshop_tcostumers table
    op.create_table(
        'eshop_tcostumers',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('tenant_user_association_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('status', tcostumer_status_enum, nullable=False),
        sa.Column('company_name', sa.String(length=255), nullable=False),
        sa.Column('business_type', business_type_enum, nullable=False),
        sa.Column('tax_id', sa.String(length=50), nullable=False),
        sa.Column('business_registration_number', sa.String(length=100), nullable=True),
        sa.Column('business_address', sa.JSON(), nullable=True),
        sa.Column('business_phone', sa.String(length=20), nullable=True),
        sa.Column('business_email', sa.String(length=255), nullable=True),
        sa.Column('website', sa.String(length=255), nullable=True),
        sa.Column('credit_limit', sa.Numeric(precision=12, scale=2), nullable=False),
        sa.Column('available_credit', sa.Numeric(precision=12, scale=2), nullable=False),
        sa.Column('credit_used', sa.Numeric(precision=12, scale=2), nullable=False),
        sa.Column('payment_terms', payment_terms_enum, nullable=False),
        sa.Column('custom_payment_days', sa.Integer(), nullable=True),
        sa.Column('default_discount_rate', sa.Numeric(precision=5, scale=2), nullable=False),
        sa.Column('business_verification_status', sa.String(length=20), nullable=False),
        sa.Column('business_verification_date', sa.DateTime(), nullable=True),
        sa.Column('business_verification_notes', sa.Text(), nullable=True),
        sa.Column('verification_documents', sa.JSON(), nullable=True),
        sa.Column('annual_revenue', sa.Numeric(precision=15, scale=2), nullable=True),
        sa.Column('employee_count', sa.Integer(), nullable=True),
        sa.Column('years_in_business', sa.Integer(), nullable=True),
        sa.Column('total_orders', sa.Integer(), nullable=False),
        sa.Column('total_spent', sa.Numeric(precision=12, scale=2), nullable=False),
        sa.Column('average_order_value', sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column('last_order_date', sa.DateTime(), nullable=True),
        sa.Column('auto_approve_orders', sa.Boolean(), nullable=False),
        sa.Column('require_po_number', sa.Boolean(), nullable=False),
        sa.Column('sales_rep_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('metadata', sa.JSON(), nullable=True),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('approved_at', sa.DateTime(), nullable=True),
        sa.Column('last_activity_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['sales_rep_id'], ['users.id'], ),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('tax_id')
    )
    
    # Create eshop_tvendor_suppliers table
    op.create_table(
        'eshop_tvendor_suppliers',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('tenant_user_association_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('status', tvendor_status_enum, nullable=False),
        sa.Column('company_name', sa.String(length=255), nullable=False),
        sa.Column('supplier_type', supplier_type_enum, nullable=False),
        sa.Column('tax_id', sa.String(length=50), nullable=False),
        sa.Column('business_registration_number', sa.String(length=100), nullable=True),
        sa.Column('business_address', sa.JSON(), nullable=True),
        sa.Column('business_phone', sa.String(length=20), nullable=True),
        sa.Column('business_email', sa.String(length=255), nullable=True),
        sa.Column('website', sa.String(length=255), nullable=True),
        sa.Column('verification_status', verification_status_enum, nullable=False),
        sa.Column('verification_date', sa.DateTime(), nullable=True),
        sa.Column('verification_notes', sa.Text(), nullable=True),
        sa.Column('verification_expires_at', sa.DateTime(), nullable=True),
        sa.Column('verification_documents', sa.JSON(), nullable=True),
        sa.Column('commission_rate', sa.Numeric(precision=5, scale=2), nullable=False),
        sa.Column('commission_type', sa.String(length=20), nullable=False),
        sa.Column('minimum_commission', sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column('max_products_allowed', sa.Integer(), nullable=False),
        sa.Column('auto_approve_products', sa.Boolean(), nullable=False),
        sa.Column('require_product_approval', sa.Boolean(), nullable=False),
        sa.Column('annual_revenue', sa.Numeric(precision=15, scale=2), nullable=True),
        sa.Column('employee_count', sa.Integer(), nullable=True),
        sa.Column('years_in_business', sa.Integer(), nullable=True),
        sa.Column('total_products', sa.Integer(), nullable=False),
        sa.Column('active_products', sa.Integer(), nullable=False),
        sa.Column('total_sales', sa.Numeric(precision=12, scale=2), nullable=False),
        sa.Column('total_orders', sa.Integer(), nullable=False),
        sa.Column('average_order_value', sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column('total_commission_earned', sa.Numeric(precision=12, scale=2), nullable=False),
        sa.Column('customer_rating', sa.Numeric(precision=3, scale=2), nullable=False),
        sa.Column('total_reviews', sa.Integer(), nullable=False),
        sa.Column('fulfillment_rate', sa.Numeric(precision=5, scale=2), nullable=False),
        sa.Column('on_time_delivery_rate', sa.Numeric(precision=5, scale=2), nullable=False),
        sa.Column('can_manage_inventory', sa.Boolean(), nullable=False),
        sa.Column('can_set_pricing', sa.Boolean(), nullable=False),
        sa.Column('can_create_promotions', sa.Boolean(), nullable=False),
        sa.Column('account_manager_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('payment_terms', sa.String(length=50), nullable=False),
        sa.Column('payment_method', sa.String(length=50), nullable=True),
        sa.Column('bank_account_info', sa.JSON(), nullable=True),
        sa.Column('shipping_zones', sa.JSON(), nullable=True),
        sa.Column('shipping_methods', sa.JSON(), nullable=True),
        sa.Column('processing_time_days', sa.Integer(), nullable=False),
        sa.Column('metadata', sa.JSON(), nullable=True),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('approved_at', sa.DateTime(), nullable=True),
        sa.Column('last_activity_at', sa.DateTime(), nullable=True),
        sa.Column('last_sale_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['account_manager_id'], ['users.id'], ),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('tax_id')
    )
    
    # Create indexes for eshop_tcostumers
    op.create_index('ix_tcostumer_tenant_user', 'eshop_tcostumers', ['tenant_id', 'user_id'], unique=False)
    op.create_index('ix_tcostumer_status_tenant', 'eshop_tcostumers', ['status', 'tenant_id'], unique=False)
    op.create_index('ix_tcostumer_company_name', 'eshop_tcostumers', ['company_name'], unique=False)
    op.create_index('ix_tcostumer_verification_status', 'eshop_tcostumers', ['business_verification_status'], unique=False)
    op.create_index('ix_tcostumer_sales_rep', 'eshop_tcostumers', ['sales_rep_id'], unique=False)
    op.create_index('ix_tcostumer_created', 'eshop_tcostumers', ['created_at'], unique=False)
    op.create_index(op.f('ix_eshop_tcostumers_status'), 'eshop_tcostumers', ['status'], unique=False)
    op.create_index(op.f('ix_eshop_tcostumers_tenant_id'), 'eshop_tcostumers', ['tenant_id'], unique=False)
    op.create_index(op.f('ix_eshop_tcostumers_user_id'), 'eshop_tcostumers', ['user_id'], unique=False)
    
    # Create indexes for eshop_tvendor_suppliers
    op.create_index('ix_tvendor_tenant_user', 'eshop_tvendor_suppliers', ['tenant_id', 'user_id'], unique=False)
    op.create_index('ix_tvendor_status_tenant', 'eshop_tvendor_suppliers', ['status', 'tenant_id'], unique=False)
    op.create_index('ix_tvendor_company_name', 'eshop_tvendor_suppliers', ['company_name'], unique=False)
    op.create_index('ix_tvendor_verification_status', 'eshop_tvendor_suppliers', ['verification_status'], unique=False)
    op.create_index('ix_tvendor_account_manager', 'eshop_tvendor_suppliers', ['account_manager_id'], unique=False)
    op.create_index('ix_tvendor_created', 'eshop_tvendor_suppliers', ['created_at'], unique=False)
    op.create_index('ix_tvendor_supplier_type', 'eshop_tvendor_suppliers', ['supplier_type'], unique=False)
    op.create_index(op.f('ix_eshop_tvendor_suppliers_status'), 'eshop_tvendor_suppliers', ['status'], unique=False)
    op.create_index(op.f('ix_eshop_tvendor_suppliers_tenant_id'), 'eshop_tvendor_suppliers', ['tenant_id'], unique=False)
    op.create_index(op.f('ix_eshop_tvendor_suppliers_user_id'), 'eshop_tvendor_suppliers', ['user_id'], unique=False)


def downgrade() -> None:
    """Downgrade schema."""
    
    # Drop indexes for eshop_tvendor_suppliers
    op.drop_index(op.f('ix_eshop_tvendor_suppliers_user_id'), table_name='eshop_tvendor_suppliers')
    op.drop_index(op.f('ix_eshop_tvendor_suppliers_tenant_id'), table_name='eshop_tvendor_suppliers')
    op.drop_index(op.f('ix_eshop_tvendor_suppliers_status'), table_name='eshop_tvendor_suppliers')
    op.drop_index('ix_tvendor_supplier_type', table_name='eshop_tvendor_suppliers')
    op.drop_index('ix_tvendor_created', table_name='eshop_tvendor_suppliers')
    op.drop_index('ix_tvendor_account_manager', table_name='eshop_tvendor_suppliers')
    op.drop_index('ix_tvendor_verification_status', table_name='eshop_tvendor_suppliers')
    op.drop_index('ix_tvendor_company_name', table_name='eshop_tvendor_suppliers')
    op.drop_index('ix_tvendor_status_tenant', table_name='eshop_tvendor_suppliers')
    op.drop_index('ix_tvendor_tenant_user', table_name='eshop_tvendor_suppliers')
    
    # Drop indexes for eshop_tcostumers
    op.drop_index(op.f('ix_eshop_tcostumers_user_id'), table_name='eshop_tcostumers')
    op.drop_index(op.f('ix_eshop_tcostumers_tenant_id'), table_name='eshop_tcostumers')
    op.drop_index(op.f('ix_eshop_tcostumers_status'), table_name='eshop_tcostumers')
    op.drop_index('ix_tcostumer_created', table_name='eshop_tcostumers')
    op.drop_index('ix_tcostumer_sales_rep', table_name='eshop_tcostumers')
    op.drop_index('ix_tcostumer_verification_status', table_name='eshop_tcostumers')
    op.drop_index('ix_tcostumer_company_name', table_name='eshop_tcostumers')
    op.drop_index('ix_tcostumer_status_tenant', table_name='eshop_tcostumers')
    op.drop_index('ix_tcostumer_tenant_user', table_name='eshop_tcostumers')
    
    # Drop tables
    op.drop_table('eshop_tvendor_suppliers')
    op.drop_table('eshop_tcostumers')
    
    # Drop enum types
    verification_status_enum = postgresql.ENUM(name='verificationstatus')
    verification_status_enum.drop(op.get_bind())
    
    supplier_type_enum = postgresql.ENUM(name='suppliertype')
    supplier_type_enum.drop(op.get_bind())
    
    tvendor_status_enum = postgresql.ENUM(name='tvendorstatus')
    tvendor_status_enum.drop(op.get_bind())
    
    payment_terms_enum = postgresql.ENUM(name='paymentterms')
    payment_terms_enum.drop(op.get_bind())
    
    business_type_enum = postgresql.ENUM(name='businesstype')
    business_type_enum.drop(op.get_bind())
    
    tcostumer_status_enum = postgresql.ENUM(name='tcostumerstatus')
    tcostumer_status_enum.drop(op.get_bind())
