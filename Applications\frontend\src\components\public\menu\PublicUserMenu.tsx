'use client';

import Link from 'next/link';
import { Fragment } from 'react';
import { Menu, Transition } from '@headlessui/react';
import {
  UserIcon,
  ShoppingCartIcon,
  HeartIcon,
  ClockIcon,
  ArrowRightOnRectangleIcon,
  UserCircleIcon,
  Cog6ToothIcon,
  StarIcon,
  TrophyIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '@/lib/auth/AuthProvider';
import { useCart } from '@/contexts/CartContext';
import { clsx } from 'clsx';

interface PublicUserMenuProps {
  variant?: 'header' | 'floating';
  userLoyalty?: {
    points: number;
    title?: string;
    level?: string;
  };
}

export function PublicUserMenu({ variant = 'header', userLoyalty }: PublicUserMenuProps) {
  const { user, isAuthenticated, logout } = useAuth();
  const { items, totalItems } = useCart();

  if (!isAuthenticated || !user) {
    return (
      <div className="flex items-center">
        <Link
          href="/auth?mode=login"
          className={clsx(
            "inline-flex items-center gap-2 px-3 py-2 text-sm font-medium transition-all duration-300",
            "rounded-l-full rounded-r-none border-r-0", // Arredondado apenas à esquerda
            variant === 'header'
              ? "bg-white/20 backdrop-blur-md border border-white/30 text-white hover:bg-white/30"
              : "bg-white/90 backdrop-blur-sm border border-gray-200 text-gray-700 hover:bg-white"
          )}
        >
          <UserIcon className="h-4 w-4" />
          <span className="hidden sm:inline">Entrar</span>
        </Link>
        <Link
          href="/auth?mode=register"
          className={clsx(
            "inline-flex items-center gap-2 px-3 py-2 text-sm font-medium transition-all duration-300",
            "rounded-r-full rounded-l-none", // Arredondado apenas à direita
            variant === 'header'
              ? "bg-orange-500/80 backdrop-blur-md border border-orange-400/50 text-white hover:bg-orange-600/80"
              : "bg-orange-500 border border-orange-400 text-white hover:bg-orange-600"
          )}
        >
          <UserIcon className="h-4 w-4" />
          <span className="hidden sm:inline">Registrar</span>
        </Link>
      </div>
    );
  }

  // totalItems já vem do contexto

  const buttonClasses = variant === 'header' 
    ? "p-2 bg-white/20 backdrop-blur-md border border-white/30 rounded-full hover:bg-white/30 transition-all duration-300 hover:scale-110 shadow-lg"
    : "p-3 bg-white/90 backdrop-blur-sm border border-gray-200 rounded-full hover:bg-white transition-all duration-300 hover:scale-110 shadow-lg";

  return (
    <div className="flex items-center gap-3">
      {/* Carrinho - apenas quando autenticado */}
      {totalItems > 0 && (
        <div className="relative">
          <button className={buttonClasses}>
            <ShoppingCartIcon className={clsx(
              "h-5 w-5",
              variant === 'header' ? "text-white" : "text-gray-700"
            )} />
            <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold">
              {totalItems}
            </span>
          </button>
        </div>
      )}

      {/* Menu do usuário */}
      <Menu as="div" className="relative">
        <Menu.Button className={buttonClasses}>
          <span className="sr-only">Menu do usuário</span>
          <div className="flex items-center gap-2">
            <UserCircleIcon className={clsx(
              "h-6 w-6",
              variant === 'header' ? "text-white" : "text-gray-700"
            )} />
            <span className={clsx(
              "text-sm font-medium hidden sm:block",
              variant === 'header' ? "text-white" : "text-gray-700"
            )}>
              {user.full_name?.split(' ')[0] || 'Usuário'}
            </span>
          </div>
        </Menu.Button>

        <Transition
          as={Fragment}
          enter="transition ease-out duration-100"
          enterFrom="transform opacity-0 scale-95"
          enterTo="transform opacity-100 scale-100"
          leave="transition ease-in duration-75"
          leaveFrom="transform opacity-100 scale-100"
          leaveTo="transform opacity-0 scale-95"
        >
          <Menu.Items className="absolute right-0 z-50 mt-2 w-56 origin-top-right bg-white/95 backdrop-blur-lg rounded-xl py-2 shadow-xl border border-gray-200/50 focus:outline-none">
            {/* Header do menu */}
            <div className="px-4 py-3 border-b border-gray-200/50">
              <div className="flex items-center justify-between mb-1">
                <p className="text-sm font-medium text-gray-900">
                  {user.full_name || 'Usuário'}
                </p>
                {userLoyalty?.title && (
                  <span className="inline-flex items-center gap-1 px-2 py-1 bg-gradient-to-r from-amber-100 to-yellow-100 text-amber-800 text-xs font-medium rounded-full border border-amber-200">
                    <TrophyIcon className="h-3 w-3" />
                    {userLoyalty.title}
                  </span>
                )}
              </div>
              <p className="text-xs text-gray-500 truncate mb-2">
                {user.email}
              </p>
              {userLoyalty && (
                <div className="flex items-center gap-2 px-2 py-1 bg-orange-50 rounded-lg">
                  <StarIcon className="h-4 w-4 text-orange-500" />
                  <span className="text-sm font-medium text-orange-700">
                    {userLoyalty.points.toLocaleString()} pontos
                  </span>
                  {userLoyalty.level && (
                    <span className="text-xs text-orange-600 ml-auto">
                      {userLoyalty.level}
                    </span>
                  )}
                </div>
              )}
            </div>

            {/* Opções do menu */}
            <div className="py-1">
              <Menu.Item>
                {({ active }) => (
                  <button
                    className={clsx(
                      'flex w-full items-center gap-3 px-4 py-2 text-sm transition-colors duration-200',
                      active ? 'bg-orange-50 text-orange-600' : 'text-gray-700'
                    )}
                  >
                    <ShoppingCartIcon className="h-4 w-4" />
                    Meus Pedidos
                    {totalItems > 0 && (
                      <span className="ml-auto bg-orange-100 text-orange-600 text-xs px-2 py-1 rounded-full">
                        {totalItems}
                      </span>
                    )}
                  </button>
                )}
              </Menu.Item>

              <Menu.Item>
                {({ active }) => (
                  <button
                    className={clsx(
                      'flex w-full items-center gap-3 px-4 py-2 text-sm transition-colors duration-200',
                      active ? 'bg-orange-50 text-orange-600' : 'text-gray-700'
                    )}
                  >
                    <HeartIcon className="h-4 w-4" />
                    Favoritos
                  </button>
                )}
              </Menu.Item>

              <Menu.Item>
                {({ active }) => (
                  <button
                    className={clsx(
                      'flex w-full items-center gap-3 px-4 py-2 text-sm transition-colors duration-200',
                      active ? 'bg-orange-50 text-orange-600' : 'text-gray-700'
                    )}
                  >
                    <ClockIcon className="h-4 w-4" />
                    Histórico
                  </button>
                )}
              </Menu.Item>

              <Menu.Item>
                {({ active }) => (
                  <button
                    className={clsx(
                      'flex w-full items-center gap-3 px-4 py-2 text-sm transition-colors duration-200',
                      active ? 'bg-orange-50 text-orange-600' : 'text-gray-700'
                    )}
                  >
                    <Cog6ToothIcon className="h-4 w-4" />
                    Configurações
                  </button>
                )}
              </Menu.Item>
            </div>

            {/* Logout */}
            <div className="border-t border-gray-200/50 py-1">
              <Menu.Item>
                {({ active }) => (
                  <button
                    onClick={logout}
                    className={clsx(
                      'flex w-full items-center gap-3 px-4 py-2 text-sm transition-colors duration-200',
                      active ? 'bg-red-50 text-red-600' : 'text-gray-700'
                    )}
                  >
                    <ArrowRightOnRectangleIcon className="h-4 w-4" />
                    Sair
                  </button>
                )}
              </Menu.Item>
            </div>
          </Menu.Items>
        </Transition>
      </Menu>
    </div>
  );
}
