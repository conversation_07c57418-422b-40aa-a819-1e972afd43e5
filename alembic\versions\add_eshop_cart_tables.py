"""Add EShop Cart Tables

Revision ID: add_eshop_cart_tables
Revises: 
Create Date: 2024-12-27 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_eshop_cart_tables'
down_revision = None  # TODO: Set to actual previous revision
branch_labels = None
depends_on = None


def upgrade():
    """Create cart tables for EShop system."""
    
    # Create cart status enum
    cart_status_enum = postgresql.ENUM(
        'active', 'abandoned', 'converted', 'expired',
        name='cartstatus',
        create_type=False
    )
    cart_status_enum.create(op.get_bind(), checkfirst=True)
    
    # Create eshop_carts table
    op.create_table(
        'eshop_carts',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True), 
                 sa.<PERSON>('tenants.id'), nullable=False, index=True),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), 
                 sa.<PERSON>ey('users.id'), nullable=True, index=True),
        sa.Column('session_id', sa.String(255), nullable=True, index=True),
        sa.Column('status', cart_status_enum, nullable=False, 
                 default='active', index=True),
        sa.Column('market_context', sa.String(20), nullable=False, default='b2c'),
        sa.Column('subtotal', sa.Numeric(10, 2), nullable=False, default=0.00),
        sa.Column('tax_amount', sa.Numeric(10, 2), nullable=False, default=0.00),
        sa.Column('discount_amount', sa.Numeric(10, 2), nullable=False, default=0.00),
        sa.Column('shipping_amount', sa.Numeric(10, 2), nullable=False, default=0.00),
        sa.Column('total_amount', sa.Numeric(10, 2), nullable=False, default=0.00),
        sa.Column('currency', sa.String(3), nullable=False, default='BRL'),
        sa.Column('notes', sa.Text, nullable=True),
        sa.Column('metadata', postgresql.JSON, nullable=True),
        sa.Column('created_at', sa.DateTime, nullable=False, 
                 server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, nullable=False, 
                 server_default=sa.func.now()),
        sa.Column('expires_at', sa.DateTime, nullable=True),
        sa.Column('converted_at', sa.DateTime, nullable=True),
    )
    
    # Create eshop_cart_items table
    op.create_table(
        'eshop_cart_items',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('cart_id', postgresql.UUID(as_uuid=True), 
                 sa.ForeignKey('eshop_carts.id'), nullable=False, index=True),
        sa.Column('product_id', postgresql.UUID(as_uuid=True), 
                 sa.ForeignKey('eshop_products.id'), nullable=False, index=True),
        sa.Column('quantity', sa.Integer, nullable=False, default=1),
        sa.Column('unit_price', sa.Numeric(10, 2), nullable=False),
        sa.Column('total_price', sa.Numeric(10, 2), nullable=False),
        sa.Column('selected_variants', postgresql.JSON, nullable=True),
        sa.Column('selected_modifiers', postgresql.JSON, nullable=True),
        sa.Column('special_instructions', sa.Text, nullable=True),
        sa.Column('metadata', postgresql.JSON, nullable=True),
        sa.Column('created_at', sa.DateTime, nullable=False, 
                 server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, nullable=False, 
                 server_default=sa.func.now()),
    )
    
    # Create performance indexes
    op.create_index(
        'ix_carts_tenant_user', 
        'eshop_carts', 
        ['tenant_id', 'user_id']
    )
    op.create_index(
        'ix_carts_tenant_session', 
        'eshop_carts', 
        ['tenant_id', 'session_id']
    )
    op.create_index(
        'ix_carts_status_expires', 
        'eshop_carts', 
        ['status', 'expires_at']
    )
    op.create_index(
        'ix_carts_market_context', 
        'eshop_carts', 
        ['market_context']
    )
    op.create_index(
        'ix_cart_items_cart_product', 
        'eshop_cart_items', 
        ['cart_id', 'product_id']
    )
    op.create_index(
        'ix_cart_items_created', 
        'eshop_cart_items', 
        ['created_at']
    )


def downgrade():
    """Drop cart tables."""
    
    # Drop indexes
    op.drop_index('ix_cart_items_created', 'eshop_cart_items')
    op.drop_index('ix_cart_items_cart_product', 'eshop_cart_items')
    op.drop_index('ix_carts_market_context', 'eshop_carts')
    op.drop_index('ix_carts_status_expires', 'eshop_carts')
    op.drop_index('ix_carts_tenant_session', 'eshop_carts')
    op.drop_index('ix_carts_tenant_user', 'eshop_carts')
    
    # Drop tables
    op.drop_table('eshop_cart_items')
    op.drop_table('eshop_carts')
    
    # Drop enum
    cart_status_enum = postgresql.ENUM(name='cartstatus')
    cart_status_enum.drop(op.get_bind(), checkfirst=True)
