"""Financial category API endpoints."""

import uuid
from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_db
from app.modules.core.auth.dependencies.auth_dependencies import get_current_active_user
from app.modules.core.tenants.dependencies.tenant_dependencies import get_current_tenant_from_header
from app.modules.core.users.models.user import User

from ..schemas.category import (
    FinancialCategoryCreate,
    FinancialCategoryUpdate,
    FinancialCategoryRead,
    FinancialCategoryTree,
    FinancialCategoryFilter,
    FinancialCategoryListResponse,
)
from ..models.category import CategoryType
from ..services.category_service import FinancialCategoryService

router = APIRouter(tags=["Financial Categories"])


@router.post(
    "/",
    response_model=FinancialCategoryRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create financial category",
    description="Create a new financial category for the current tenant.",
)
async def create_category(
    category_data: FinancialCategoryCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    tenant_id: uuid.UUID = Depends(get_current_tenant_from_header),
):
    """Create a new financial category."""
    service = FinancialCategoryService(db)
    
    category = await service.create_category(
        category_data=category_data,
        tenant_id=tenant_id,
    )
    
    await db.commit()
    
    # Return the created category
    created_category = await service.get_category(category.id, tenant_id)
    
    return service._category_to_read_schema(created_category)


@router.get(
    "/",
    response_model=FinancialCategoryListResponse,
    summary="List financial categories",
    description="Get paginated list of financial categories with optional filters.",
)
async def list_categories(
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    order_by: str = Query("display_order", description="Order by field"),
    order_direction: str = Query("asc", pattern="^(asc|desc)$", description="Order direction"),
    
    # Filters
    category_type: Optional[CategoryType] = Query(None, description="Filter by category type"),
    parent_id: Optional[uuid.UUID] = Query(None, description="Filter by parent category ID"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    search: Optional[str] = Query(None, description="Search in name and description"),
    
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    tenant_id: uuid.UUID = Depends(get_current_tenant_from_header),
):
    """Get paginated list of financial categories."""
    service = FinancialCategoryService(db)
    
    # Build filters
    filters = FinancialCategoryFilter()
    if category_type:
        filters.category_type = category_type
    if parent_id:
        filters.parent_id = parent_id
    if is_active is not None:
        filters.is_active = is_active
    if search:
        filters.search = search
    
    return await service.get_categories(
        tenant_id=tenant_id,
        filters=filters,
        page=page,
        per_page=per_page,
        order_by=order_by,
        order_direction=order_direction,
    )


@router.get(
    "/tree",
    response_model=List[FinancialCategoryTree],
    summary="Get category tree",
    description="Get hierarchical tree of financial categories.",
)
async def get_category_tree(
    category_type: Optional[CategoryType] = Query(None, description="Filter by category type"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    tenant_id: uuid.UUID = Depends(get_current_tenant_from_header),
):
    """Get hierarchical tree of financial categories."""
    service = FinancialCategoryService(db)
    
    return await service.get_category_tree(
        tenant_id=tenant_id,
        category_type=category_type,
    )


@router.get(
    "/{category_id}",
    response_model=FinancialCategoryRead,
    summary="Get financial category",
    description="Get a specific financial category by ID.",
)
async def get_category(
    category_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    tenant_id: uuid.UUID = Depends(get_current_tenant_from_header),
):
    """Get a specific financial category."""
    service = FinancialCategoryService(db)
    
    category = await service.get_category(category_id, tenant_id)
    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Financial category not found"
        )
    
    return service._category_to_read_schema(category)


@router.put(
    "/{category_id}",
    response_model=FinancialCategoryRead,
    summary="Update financial category",
    description="Update a specific financial category.",
)
async def update_category(
    category_id: uuid.UUID,
    category_data: FinancialCategoryUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    tenant_id: uuid.UUID = Depends(get_current_tenant_from_header),
):
    """Update a specific financial category."""
    service = FinancialCategoryService(db)
    
    category = await service.update_category(
        category_id=category_id,
        tenant_id=tenant_id,
        category_data=category_data,
    )
    
    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Financial category not found"
        )
    
    await db.commit()
    
    return service._category_to_read_schema(category)


@router.delete(
    "/{category_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete financial category",
    description="Delete a specific financial category.",
)
async def delete_category(
    category_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    tenant_id: uuid.UUID = Depends(get_current_tenant_from_header),
):
    """Delete a specific financial category."""
    service = FinancialCategoryService(db)
    
    success = await service.delete_category(category_id, tenant_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Financial category not found"
        )
    
    await db.commit()


@router.post(
    "/defaults",
    response_model=List[FinancialCategoryRead],
    status_code=status.HTTP_201_CREATED,
    summary="Create default categories",
    description="Create default financial categories for the current tenant.",
)
async def create_default_categories(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    tenant_id: uuid.UUID = Depends(get_current_tenant_from_header),
):
    """Create default financial categories for the current tenant."""
    service = FinancialCategoryService(db)
    
    categories = await service.create_default_categories(tenant_id)
    
    await db.commit()
    
    return [service._category_to_read_schema(cat) for cat in categories]
