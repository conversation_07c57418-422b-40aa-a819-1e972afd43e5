import logging
import uuid
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, insert, update, text

from app.modules.tenants.restaurants.menu.models.menu_item import MenuItem
from app.modules.core.functions.allergens.models.allergen import Allergen
from app.db.base import menu_item_allergens

logger = logging.getLogger(__name__)


class MenuItemAllergenManager:
    def __init__(self, db_session: AsyncSession):
        self.db = db_session

    async def update_item_allergens(self, db_item: MenuItem, allergens_data: List[Dict[str, Any]], 
                                  tenant_id: uuid.UUID):
        """Update allergens for a menu item."""
        try:
            # Remove existing allergen associations
            await self._remove_existing_allergens(db_item.id, tenant_id)

            # Process new allergens
            if allergens_data:
                await self._process_allergens_data(db_item, allergens_data, tenant_id)

            logger.info(f"Updated allergens for item {db_item.id}")

        except Exception as e:
            logger.error(f"Error updating allergens for item {db_item.id}: {e}")
            raise

    async def _remove_existing_allergens(self, item_id: uuid.UUID, tenant_id: uuid.UUID):
        """Remove existing allergen associations for a menu item."""
        await self.db.execute(
            delete(menu_item_allergens).where(
                menu_item_allergens.c.menu_item_id == item_id
            )
        )
        await self.db.flush()

    async def _process_allergens_data(self, db_item: MenuItem, allergens_data: List[Dict[str, Any]], 
                                    tenant_id: uuid.UUID):
        """Process allergens data and create associations."""
        for allergen_data in allergens_data:
            await self._process_single_allergen(db_item, allergen_data, tenant_id)

    async def _process_single_allergen(self, db_item: MenuItem, allergen_data: Dict[str, Any], 
                                     tenant_id: uuid.UUID):
        """Process a single allergen and create association."""
        try:
            allergen_id = allergen_data.get('id') or allergen_data.get('allergen_id')
            
            if not allergen_id:
                logger.warning(f"No allergen ID provided in data: {allergen_data}")
                return

            # Verify allergen exists
            allergen = await self._get_allergen_by_id(allergen_id, tenant_id)
            if not allergen:
                logger.warning(f"Allergen {allergen_id} not found for tenant {tenant_id}")
                return

            # Create association
            await self.db.execute(
                insert(menu_item_allergens).values(
                    menu_item_id=db_item.id,
                    allergen_id=allergen_id,
                    tenant_id=tenant_id,
                    severity_level=allergen_data.get('severity_level', 'medium'),
                    is_trace=allergen_data.get('is_trace', False),
                    notes=allergen_data.get('notes')
                )
            )
            logger.info(f"Added allergen {allergen_id} to item {db_item.id}")

        except Exception as e:
            logger.error(f"Error processing allergen {allergen_data}: {e}")

    async def _get_allergen_by_id(self, allergen_id: uuid.UUID, tenant_id: uuid.UUID) -> Optional[Allergen]:
        """Get allergen by ID (allergens are global, tenant_id is for compatibility)."""
        try:
            result = await self.db.execute(
                select(Allergen).where(
                    Allergen.id == allergen_id,
                    Allergen.is_active == True
                )
            )
            return result.scalar_one_or_none()

        except Exception as e:
            logger.error(f"Error getting allergen {allergen_id}: {e}")
            return None

    async def add_allergen_to_item(self, item_id: uuid.UUID, allergen_id: uuid.UUID, 
                                 tenant_id: uuid.UUID, severity_level: str = 'medium',
                                 is_trace: bool = False, notes: Optional[str] = None):
        """Add a single allergen to a menu item."""
        try:
            # Check if association already exists
            existing = await self._get_item_allergen_association(item_id, allergen_id, tenant_id)
            if existing:
                logger.info(f"Allergen {allergen_id} already associated with item {item_id}")
                return existing

            # Verify allergen exists
            allergen = await self._get_allergen_by_id(allergen_id, tenant_id)
            if not allergen:
                raise ValueError(f"Allergen {allergen_id} not found")

            # Create association
            await self.db.execute(
                insert(menu_item_allergens).values(
                    menu_item_id=item_id,
                    allergen_id=allergen_id,
                    tenant_id=tenant_id,
                    severity_level=severity_level,
                    is_trace=is_trace,
                    notes=notes
                )
            )
            await self.db.flush()

            logger.info(f"Added allergen {allergen_id} to item {item_id}")
            return True

        except Exception as e:
            logger.error(f"Error adding allergen {allergen_id} to item {item_id}: {e}")
            raise

    async def remove_allergen_from_item(self, item_id: uuid.UUID, allergen_id: uuid.UUID,
                                      tenant_id: uuid.UUID):
        """Remove a specific allergen from a menu item."""
        try:
            await self.db.execute(
                delete(menu_item_allergens).where(
                    menu_item_allergens.c.menu_item_id == item_id,
                    menu_item_allergens.c.allergen_id == allergen_id
                )
            )
            await self.db.flush()

            logger.info(f"Removed allergen {allergen_id} from item {item_id}")

        except Exception as e:
            logger.error(f"Error removing allergen {allergen_id} from item {item_id}: {e}")
            raise

    async def _get_item_allergen_association(self, item_id: uuid.UUID, allergen_id: uuid.UUID,
                                           tenant_id: uuid.UUID) -> Optional[Dict]:
        """Get existing allergen association."""
        try:
            result = await self.db.execute(
                select(menu_item_allergens).where(
                    menu_item_allergens.c.menu_item_id == item_id,
                    menu_item_allergens.c.allergen_id == allergen_id
                )
            )
            row = result.first()
            return dict(row) if row else None

        except Exception as e:
            logger.error(f"Error getting allergen association: {e}")
            return None

    async def get_item_allergens(self, item_id: uuid.UUID, tenant_id: uuid.UUID) -> List[Dict]:
        """Get all allergens for a menu item."""
        try:
            result = await self.db.execute(
                select(menu_item_allergens).where(
                    menu_item_allergens.c.menu_item_id == item_id
                )
            )
            return [dict(row) for row in result.fetchall()]

        except Exception as e:
            logger.error(f"Error getting allergens for item {item_id}: {e}")
            return []

    async def update_allergen_severity(self, item_id: uuid.UUID, allergen_id: uuid.UUID,
                                     tenant_id: uuid.UUID, severity_level: str,
                                     is_trace: bool = False, notes: Optional[str] = None):
        """Update allergen severity and details for a menu item."""
        try:
            await self.db.execute(
                update(menu_item_allergens).where(
                    menu_item_allergens.c.menu_item_id == item_id,
                    menu_item_allergens.c.allergen_id == allergen_id
                ).values(
                    severity_level=severity_level,
                    is_trace=is_trace,
                    notes=notes
                )
            )
            await self.db.flush()

            logger.info(f"Updated allergen {allergen_id} severity for item {item_id}")

        except Exception as e:
            logger.error(f"Error updating allergen severity: {e}")
            raise

    async def get_allergens_by_severity(self, item_id: uuid.UUID, tenant_id: uuid.UUID,
                                      severity_level: str) -> List[Dict]:
        """Get allergens for a menu item filtered by severity level."""
        try:
            result = await self.db.execute(
                select(menu_item_allergens).where(
                    menu_item_allergens.c.menu_item_id == item_id,
                    menu_item_allergens.c.severity_level == severity_level
                )
            )
            return [dict(row) for row in result.fetchall()]

        except Exception as e:
            logger.error(f"Error getting allergens by severity for item {item_id}: {e}")
            return []

    async def check_allergen_conflicts(self, item_id: uuid.UUID, tenant_id: uuid.UUID) -> Dict[str, Any]:
        """Check for potential allergen conflicts or warnings."""
        try:
            allergens = await self.get_item_allergens(item_id, tenant_id)
            
            conflicts = {
                'high_severity_count': 0,
                'trace_allergens': [],
                'conflicting_allergens': [],
                'warnings': []
            }

            for allergen in allergens:
                if allergen.get('severity_level') == 'high':
                    conflicts['high_severity_count'] += 1
                
                if allergen.get('is_trace'):
                    conflicts['trace_allergens'].append(allergen.get('allergen_id'))

            # Add warnings based on analysis
            if conflicts['high_severity_count'] > 3:
                conflicts['warnings'].append("Item has many high-severity allergens")
            
            if len(conflicts['trace_allergens']) > 0:
                conflicts['warnings'].append("Item contains trace allergens")

            return conflicts

        except Exception as e:
            logger.error(f"Error checking allergen conflicts for item {item_id}: {e}")
            return {}

    async def bulk_update_allergens(self, items_allergens: Dict[uuid.UUID, List[Dict[str, Any]]], 
                                  tenant_id: uuid.UUID):
        """Bulk update allergens for multiple menu items."""
        try:
            for item_id, allergens_data in items_allergens.items():
                # Get the menu item
                result = await self.db.execute(
                    select(MenuItem).where(
                        MenuItem.id == item_id,
                        MenuItem.tenant_id == tenant_id
                    )
                )
                db_item = result.scalar_one_or_none()
                
                if db_item:
                    await self.update_item_allergens(db_item, allergens_data, tenant_id)
                else:
                    logger.warning(f"Menu item {item_id} not found for bulk allergen update")

            logger.info(f"Bulk updated allergens for {len(items_allergens)} items")

        except Exception as e:
            logger.error(f"Error in bulk allergen update: {e}")
            raise