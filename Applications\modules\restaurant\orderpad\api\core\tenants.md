# Core - Tenants

**Categoria:** Core
**Módulo:** Tenants
**Total de Endpoints:** 5
**Gerado em:** 08/06/2025, 17:26:01

## 📋 Endpoints

- [GET /api/tenants/](#get-apitenants) - Read Tenants
- [POST /api/tenants/](#post-apitenants) - Create Tenant
- [GET /api/tenants/{tenant_id}](#get-apitenantstenant-id) - Read Tenant
- [GET /api/tenants/{tenant_id}/users](#get-apitenantstenant-idusers) - Read Tenant Users
- [POST /api/tenants/{tenant_id}/users](#post-apitenantstenant-idusers) - Add User To Tenant

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### AddUserToTenantPayload

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `user_id` | string | ✅ | - |
| `role` | TenantRole | ✅ | - |
| `staff_sub_role` | unknown | ❌ | - |
| `data_sharing_consent` | unknown | ❌ | - |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### Tenant

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Nome do Tenant |
| `tenant_slug` | string | ✅ | Slug único para identificação do tenant em URLs públicas |
| `tenant_type` | unknown | ❌ | Tipo do Tenant (ex: restaurant, shop) |
| `is_active` | boolean | ❌ | - |
| `id` | string | ✅ | - |
| `created_at` | unknown | ❌ | - |
| `updated_at` | unknown | ❌ | - |

### TenantCreate

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Nome do Tenant |
| `tenant_slug` | string | ✅ | Slug único para identificação do tenant em URLs públicas |
| `tenant_type` | unknown | ❌ | Tipo do Tenant (ex: restaurant, shop) |
| `is_active` | boolean | ❌ | - |

### TenantUserAssociationRead

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `role` | string | ✅ | - |
| `staff_sub_role` | unknown | ❌ | - |
| `data_sharing_consent` | boolean | ❌ | - |
| `user_id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `user` | User | ✅ | - |
| `tenant` | Tenant | ✅ | - |

## 🔗 Endpoints Detalhados

### GET /api/tenants/ {#get-apitenants}

**Resumo:** Read Tenants
**Descrição:** Retrieves a list of active tenants.
Requires authentication. More granular permissions can be added.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/tenants/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/tenants/ {#post-apitenants}

**Resumo:** Create Tenant
**Descrição:** Creates a new tenant.
Only superusers can create tenants.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TenantCreate](#tenantcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [Tenant](#tenant)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/tenants/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/tenants/{tenant_id} {#get-apitenantstenant-id}

**Resumo:** Read Tenant
**Descrição:** Gets details of a specific tenant by ID.
Requires authentication.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [Tenant](#tenant)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/tenants/{tenant_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/tenants/{tenant_id}/users {#get-apitenantstenant-idusers}

**Resumo:** Read Tenant Users
**Descrição:** Lists users associated with a specific tenant.
Requires that the authenticated user has at least the STAFF role in the tenant specified in the path.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/tenants/{tenant_id}/users" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/tenants/{tenant_id}/users {#post-apitenantstenant-idusers}

**Resumo:** Add User To Tenant
**Descrição:** Adds an existing user to a specific tenant with a role.
Requires that the authenticated user has the ADMIN or MANAGER role in the tenant specified in the path.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [AddUserToTenantPayload](#addusertotenantpayload)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantUserAssociationRead](#tenantuserassociationread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/tenants/{tenant_id}/users" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
