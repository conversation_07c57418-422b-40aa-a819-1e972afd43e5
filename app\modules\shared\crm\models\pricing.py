"""Pricing models for CRM module."""

import uuid  # noqa: E402
import enum
from datetime import datetime
from typing import TYPE_CHECKING, List
from sqlalchemy import (
    Column,
    String,
    Boolean,
    ForeignKey,
    Text,
    Enum,
    Integer,
    Float,
    DateTime,
    JSON,
)
from sqlalchemy.dialects.postgresql import UUID  # noqa: E402
from sqlalchemy.orm import relationship, Mapped, mapped_column

from app.db.base import Base  # noqa: E402

if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.shared.crm.models.account import Account


class PricingRuleType(str, enum.Enum):
    """Pricing rule type enum."""

    PERCENTAGE_DISCOUNT = "percentage_discount"
    FIXED_DISCOUNT = "fixed_discount"
    CUSTOM_PRICE = "custom_price"
    BULK_DISCOUNT = "bulk_discount"
    SEASONAL_PRICING = "seasonal_pricing"
    OTHER = "other"


class PricingTier(Base):
    """Pricing tier model for CRM module.

    This represents a pricing tier that can be assigned to customers.
    """

    __tablename__ = "crm_pricing_tiers"

    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)

    # Tier details
    name: Mapped[str] = mapped_column(String, nullable=False)
    description: Mapped[str] = mapped_column(Text, nullable=True)

    # Status
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)

    # Default discount percentage (can be overridden by specific rules)
    default_discount_percentage: Mapped[float] = mapped_column(Float, default=0.0)

    # Relationships
    tenant: Mapped["Tenant"] = relationship(back_populates="crm_pricing_tiers")
    pricing_rules: Mapped[List["PricingRule"]] = relationship(
        "PricingRule", back_populates="pricing_tier", cascade="all, delete-orphan"
    )
    customer_assignments: Mapped[List["CustomerPricingAssignment"]] = relationship(
        "CustomerPricingAssignment",
        back_populates="pricing_tier",
        cascade="all, delete-orphan",
    )

    def __repr__(self):
        return f"<PricingTier(id={self.id}, name='{self.name}', discount={self.default_discount_percentage}%)>"  # noqa: E501


class CustomerPricingAssignment(Base):
    """Customer pricing assignment model for CRM module.

    This represents the assignment of a pricing tier to a customer account.
    """

    __tablename__ = "crm_customer_pricing_assignments"

    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    pricing_tier_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("crm_pricing_tiers.id"), nullable=False)
    account_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("crm_accounts.id"), nullable=False)

    # Assignment details
    start_date: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=datetime.utcnow)
    end_date: Mapped[datetime] = mapped_column(DateTime, nullable=True)  # Null means indefinite

    # Status
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)

    # Override the default tier discount if needed
    custom_discount_percentage: Mapped[float] = mapped_column(Float, nullable=True)

    # Notes
    notes: Mapped[str] = mapped_column(Text, nullable=True)

    # Relationships
    tenant: Mapped["Tenant"] = relationship(back_populates="crm_customer_pricing_assignments")
    pricing_tier: Mapped["PricingTier"] = relationship("PricingTier", back_populates="customer_assignments")
    account: Mapped["Account"] = relationship("Account", back_populates="pricing_assignments")

    def __repr__(self):
        return f"<CustomerPricingAssignment(id={self.id}, account_id='{self.account_id}', tier_id='{self.pricing_tier_id}')>"  # noqa: E501


class PricingRule(Base):
    """Pricing rule model for CRM module.

    This represents a specific pricing rule within a pricing tier.
    """

    __tablename__ = "crm_pricing_rules"

    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    pricing_tier_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("crm_pricing_tiers.id"), nullable=False)

    # Rule details
    name: Mapped[str] = mapped_column(String, nullable=False)
    description: Mapped[str] = mapped_column(Text, nullable=True)
    rule_type: Mapped[PricingRuleType] = mapped_column(Enum(PricingRuleType), nullable=False)

    # Target
    product_category: Mapped[str] = mapped_column(String, nullable=True)  # Can be null for "all products"
    product_id: Mapped[str] = mapped_column(String, nullable=True)  # Can be null for "all products in category"

    # Rule parameters
    discount_percentage: Mapped[float] = mapped_column(Float, nullable=True)
    discount_amount: Mapped[float] = mapped_column(Float, nullable=True)
    custom_price: Mapped[float] = mapped_column(Float, nullable=True)
    min_quantity: Mapped[int] = mapped_column(Integer, nullable=True)

    # Validity
    start_date: Mapped[datetime] = mapped_column(DateTime, nullable=True)
    end_date: Mapped[datetime] = mapped_column(DateTime, nullable=True)

    # Status
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)

    # Additional conditions (JSON for flexibility)
    conditions: Mapped[dict] = mapped_column(JSON, nullable=True)

    # Relationships
    tenant: Mapped["Tenant"] = relationship(back_populates="crm_pricing_rules")
    pricing_tier: Mapped["PricingTier"] = relationship("PricingTier", back_populates="pricing_rules")

    def __repr__(self):
        return f"<PricingRule(id={self.id}, name='{self.name}', type='{self.rule_type}')>"

if TYPE_CHECKING:
    from app.modules.shared.crm.models.pricing import PricingTier, CustomerPricingAssignment, PricingRule
