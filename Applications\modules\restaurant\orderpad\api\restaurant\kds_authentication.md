# Restaurant - Kds Authentication

**Categoria:** Restaurant
**Módulo:** Kds Authentication
**Total de Endpoints:** 3
**Gerado em:** 08/06/2025, 17:26:01

## 📋 Endpoints

- [POST /api/modules/restaurants/kds/auth](#post-apimodulesrestaurantskdsauth) - Authenticate Kds
- [GET /api/modules/restaurants/kds/current-code](#get-apimodulesrestaurantskdscurrent-code) - Get Current Temp Code
- [POST /api/modules/restaurants/kds/generate-temp-code](#post-apimodulesrestaurantskdsgenerate-temp-code) - Generate Kds Temp Code

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### KDSAuthRequest

**Descrição:** Schema para requisição de autenticação do KDS.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `restaurant_uuid` | string | ✅ | UUID do restaurante |
| `temp_code` | string | ✅ | Código temporário de 6 dígitos |

### KDSAuthResponse

**Descrição:** Schema para resposta de autenticação do KDS.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `access_token` | string | ✅ | Token de acesso JWT |
| `token_type` | string | ❌ | Tipo do token |
| `expires_in` | integer | ✅ | Segundos até expiração do token |
| `tenant_id` | string | ✅ | ID do tenant autenticado |
| `tenant_name` | string | ✅ | Nome do restaurante |

### KDSTempCodeGenerate

**Descrição:** Schema para resposta de geração de código temporário.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `code` | string | ✅ | Código de 6 dígitos gerado |
| `expires_at` | string | ✅ | Data/hora de expiração |
| `expires_in_hours` | integer | ✅ | Horas até expiração |
| `tenant_id` | string | ✅ | ID do tenant |
| `message` | string | ✅ | Mensagem informativa |

## 🔗 Endpoints Detalhados

### POST /api/modules/restaurants/kds/auth {#post-apimodulesrestaurantskdsauth}

**Resumo:** Authenticate Kds
**Descrição:** Autentica um app KDS usando UUID do restaurante + código temporário de 6 dígitos.
Endpoint público - não requer autenticação prévia.

Args:
    auth_request: UUID do restaurante e código temporário

Returns:
    Token JWT para acesso às APIs do KDS

**🔐 Autenticação:** Não requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [KDSAuthRequest](#kdsauthrequest)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [KDSAuthResponse](#kdsauthresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/restaurants/kds/auth" \
  -H "Content-Type: application/json" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/restaurants/kds/current-code {#get-apimodulesrestaurantskdscurrent-code}

**Resumo:** Get Current Temp Code
**Descrição:** Obtém o código temporário ativo atual para o tenant.
Apenas OWNERS e MANAGERS podem visualizar códigos.

Returns:
    Código ativo se existir, None caso contrário

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/kds/current-code" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/restaurants/kds/generate-temp-code {#post-apimodulesrestaurantskdsgenerate-temp-code}

**Resumo:** Generate Kds Temp Code
**Descrição:** Gera um código temporário de 6 dígitos para autenticação do KDS.
Apenas OWNERS e MANAGERS podem gerar códigos.

Args:
    expires_hours: Horas até expiração (padrão: 24h, máximo: 168h)

Returns:
    Código gerado com informações de expiração

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `expires_hours` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [KDSTempCodeGenerate](#kdstempcodegenerate)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/restaurants/kds/generate-temp-code" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
