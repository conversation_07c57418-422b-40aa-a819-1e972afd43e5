import uuid
from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict

from app.modules.core.users.schemas.user import User
from app.modules.core.tenants.schemas.tenant import Tenant


class TenantUserAssociationBase(BaseModel):
    role: str
    staff_sub_role: Optional[str] = None
    data_sharing_consent: bool = False


class TenantUserAssociationCreate(TenantUserAssociationBase):
    user_id: uuid.UUID
    tenant_id: uuid.UUID


class TenantUserAssociationUpdate(BaseModel):
    role: Optional[str] = None
    staff_sub_role: Optional[str] = None
    data_sharing_consent: Optional[bool] = None


class TenantUserAssociationInDBBase(TenantUserAssociationBase):
    user_id: uuid.UUID
    tenant_id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    model_config = ConfigDict(from_attributes=True)


class TenantUserAssociation(TenantUserAssociationInDBBase):
    pass


class TenantUserAssociationRead(TenantUserAssociationInDBBase):
    user: User
    tenant: Tenant
