"""GoDaddy API processor for domain operations."""

import json  # noqa: E402
import logging
from datetime import date, datetime
from typing import Dict, List, Optional, Any, Tuple

import httpx  # noqa: E402
from pydantic import ValidationError

from app.modules.shared.domain_rent.processors.base_processor import BaseProcessor  # noqa: E402
from app.modules.shared.domain_rent.schemas.domain_schemas import (
    DomainAvailabilityResult,
    DomainPriceInfo,
    ContactInfo,
)
from app.modules.shared.domain_rent.exceptions import (  # noqa: E402
    RegistrarApiError,
    DomainNotAvailableError,
)


logger = logging.getLogger(__name__)


class GoDaddyProcessor(BaseProcessor):
    """Processor for GoDaddy domain registrar API."""

    def __init__(
        self,
        api_key: str,
        api_secret: str,
        api_url: str = "https://api.godaddy.com/",
        is_production: bool = True,
    ):
        """Initialize GoDaddy processor.

        Args:
            api_key: GoDaddy API key
            api_secret: GoDaddy API secret
            api_url: GoDaddy API URL
            is_production: Whether to use production environment
        """
        self.api_key = api_key
        self.api_secret = api_secret
        self.api_url = api_url.rstrip("/")
        self.is_production = is_production

        # API version
        self.api_version = "v1"

        # Headers for API requests
        self.headers = {
            "Authorization": f"sso-key {api_key}:{api_secret}",
            "Content-Type": "application/json",
            "Accept": "application/json",
        }

    @property
    def registrar_name(self) -> str:
        """Return the name of the registrar."""
        return "godaddy"

    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """Make a request to the GoDaddy API.

        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            endpoint: API endpoint (without base URL)
            data: Request data (for POST/PUT)
            params: Query parameters

        Returns:
            Response data as dictionary

        Raises:
            RegistrarApiError: If the API request fails
        """
        url = f"{self.api_url}/{self.api_version}/{endpoint}"

        try:
            async with httpx.AsyncClient() as client:
                response = await client.request(
                    method=method,
                    url=url,
                    headers=self.headers,
                    json=data,
                    params=params,
                    timeout=30.0,
                )

                # Check for errors
                if response.status_code >= 400:
                    error_data = response.json() if response.text else {}
                    error_msg = (
                        error_data.get("message", "Unknown error")
                        if isinstance(error_data, dict)
                        else "Unknown error"
                    )
                    raise RegistrarApiError(
                        f"GoDaddy API error: {error_msg} " f"(Status: {response.status_code})"
                    )

                # Return response data
                return response.json() if response.text else {}

        except httpx.RequestError as e:
            raise RegistrarApiError(f"GoDaddy API request failed: {str(e)}")
        except json.JSONDecodeError:
            raise RegistrarApiError("GoDaddy API returned invalid JSON")

    async def search_availability(
        self, domain_name: str, tlds: Optional[List[str]] = None
    ) -> List[DomainAvailabilityResult]:
        """Search for domain availability.

        Args:
            domain_name: Domain name without TLD
            tlds: List of TLDs to check (if None, check common TLDs)

        Returns:
            List of availability results for each domain/TLD combination
        """
        # If no TLDs provided, use common ones
        if not tlds:
            tlds = ["com", "net", "org", "io", "co"]

        results = []

        # Check each TLD
        for tld in tlds:
            full_domain = f"{domain_name}.{tld}"

            try:
                # Call GoDaddy API to check availability
                response = await self._make_request(
                    "GET", f"domains/available?domain={full_domain}"
                )

                available = response.get("available", False)
                price = response.get("price", 0)
                currency = response.get("currency", "USD")

                # Get additional price information if available
                price_info = None
                if "price" in response:
                    price_info = DomainPriceInfo(
                        currency=currency,
                        registration={
                            "1": float(price) / 1000000  # GoDaddy prices are in microseconds
                        },
                        renewal={"1": float(price) / 1000000},  # Assuming same as registration
                    )

                results.append(
                    DomainAvailabilityResult(
                        domain_name=full_domain,
                        tld=tld,
                        available=available,
                        registrar=self.registrar_name,
                        price_info=price_info,
                        reason=None if available else "Domain already registered",
                    )
                )
            except Exception as e:
                logger.error(f"Error checking availability for {full_domain}: {str(e)}")
                # Add error result
                results.append(
                    DomainAvailabilityResult(
                        domain_name=full_domain,
                        tld=tld,
                        available=False,
                        registrar=self.registrar_name,
                        price_info=None,
                        reason=f"Error checking availability: {str(e)}",
                    )
                )

        return results

    async def register_domain(
        self,
        domain_name: str,
        tld: str,
        period_years: int,
        contacts: Dict[str, ContactInfo],
        nameservers: Optional[List[str]] = None,
        whois_privacy: bool = False,
    ) -> Dict[str, Any]:
        """Register a new domain.

        Args:
            domain_name: Domain name without TLD
            tld: TLD to register
            period_years: Registration period in years
            contacts: Contact information for the domain
            nameservers: List of nameserver hostnames
            whois_privacy: Whether to enable WHOIS privacy protection

        Returns:
            Dictionary with registration details (registrar-specific)
        """
        full_domain = f"{domain_name}.{tld}"

        # Check if domain is available
        availability_results = await self.search_availability(domain_name, [tld])
        if not availability_results or not availability_results[0].available:
            raise DomainNotAvailableError(f"Domain {full_domain} is not available")

        # Prepare contact information
        godaddy_contacts = {}
        for contact_type, contact_info in contacts.items():
            godaddy_contacts[contact_type] = {
                "firstName": contact_info.first_name,
                "lastName": contact_info.last_name,
                "organization": contact_info.organization or "",
                "email": str(contact_info.email),
                "phone": contact_info.phone,
                "addressMailing": {
                    "address1": contact_info.address_line_1,
                    "address2": contact_info.address_line_2 or "",
                    "city": contact_info.city,
                    "state": contact_info.state_province,
                    "postalCode": contact_info.postal_code,
                    "country": contact_info.country,
                },
            }

        # Prepare registration data
        registration_data = {
            "domain": full_domain,
            "period": period_years,
            "nameServers": nameservers or [],
            "renewAuto": True,
            "privacy": whois_privacy,
            "contactAdmin": godaddy_contacts.get("admin", godaddy_contacts.get("registrant")),
            "contactBilling": godaddy_contacts.get("billing", godaddy_contacts.get("registrant")),
            "contactRegistrant": godaddy_contacts.get("registrant"),
            "contactTech": godaddy_contacts.get("technical", godaddy_contacts.get("registrant")),
        }

        # Register domain
        try:
            response = await self._make_request("POST", "domains/purchase", data=registration_data)

            return {
                "domain": full_domain,
                "order_id": response.get("orderId"),
                "created_at": datetime.now().isoformat(),
                "expiry_date": None,  # GoDaddy doesn't return expiry date in purchase response
                "registrar_data": json.dumps(response),
            }
        except Exception as e:
            logger.error(f"Error registering domain {full_domain}: {str(e)}")
            raise RegistrarApiError(f"Failed to register domain: {str(e)}")

    async def renew_domain(
        self,
        domain_name: str,
        tld: str,
        period_years: int,
        current_expiry_date: date,
    ) -> Dict[str, Any]:
        """Renew an existing domain.

        Args:
            domain_name: Domain name without TLD
            tld: TLD of the domain
            period_years: Renewal period in years
            current_expiry_date: Current expiry date of the domain

        Returns:
            Dictionary with renewal details (registrar-specific)
        """
        full_domain = f"{domain_name}.{tld}"

        # Prepare renewal data
        renewal_data = {
            "period": period_years,
            "renewAuto": True,
        }

        # Renew domain
        try:
            response = await self._make_request(
                "POST", f"domains/{full_domain}/renew", data=renewal_data
            )

            # Calculate new expiry date based on current_expiry_date
            new_expiry_date = date(
                current_expiry_date.year + period_years,
                current_expiry_date.month,
                current_expiry_date.day,
            )

            return {
                "domain": full_domain,
                "order_id": response.get("orderId"),
                "created_at": datetime.now().isoformat(),
                "expiry_date": new_expiry_date.isoformat(),
                "registrar_data": json.dumps(response),
            }
        except Exception as e:
            logger.error(f"Error renewing domain {full_domain}: {str(e)}")
            raise RegistrarApiError(f"Failed to renew domain: {str(e)}")

    async def get_domain_info(self, domain_name: str, tld: str) -> Dict[str, Any]:
        """Get information about a domain.

        Args:
            domain_name: Domain name without TLD
            tld: TLD of the domain

        Returns:
            Dictionary with domain information (registrar-specific)
        """
        full_domain = f"{domain_name}.{tld}"

        try:
            response = await self._make_request("GET", f"domains/{full_domain}")
            return response
        except Exception as e:
            logger.error(f"Error getting domain info for {full_domain}: {str(e)}")
            raise RegistrarApiError(f"Failed to get domain info: {str(e)}")

    async def get_nameservers(self, domain_name: str, tld: str) -> List[str]:
        """Get nameservers for a domain.

        Args:
            domain_name: Domain name without TLD
            tld: TLD of the domain

        Returns:
            List of nameserver hostnames
        """
        domain_info = await self.get_domain_info(domain_name, tld)
        return domain_info.get("nameServers", [])

    async def update_nameservers(self, domain_name: str, tld: str, nameservers: List[str]) -> bool:
        """Update nameservers for a domain.

        Args:
            domain_name: Domain name without TLD
            tld: TLD of the domain
            nameservers: List of nameserver hostnames

        Returns:
            True if successful, False otherwise
        """
        full_domain = f"{domain_name}.{tld}"

        try:
            await self._make_request(
                "PATCH",
                f"domains/{full_domain}",
                data={"nameServers": nameservers},
            )
            return True
        except Exception as e:
            logger.error(f"Error updating nameservers for {full_domain}: {str(e)}")
            return False

    async def get_auth_code(self, domain_name: str, tld: str) -> str:
        """Get authorization code for domain transfer.

        Args:
            domain_name: Domain name without TLD
            tld: TLD of the domain

        Returns:
            Authorization code
        """
        full_domain = f"{domain_name}.{tld}"

        try:
            response = await self._make_request("POST", f"domains/{full_domain}/transfer")
            return response.get("authCode", "")
        except Exception as e:
            logger.error(f"Error getting auth code for {full_domain}: {str(e)}")
            raise RegistrarApiError(f"Failed to get auth code: {str(e)}")

    async def toggle_whois_privacy(self, domain_name: str, tld: str, enable: bool) -> bool:
        """Enable or disable WHOIS privacy for a domain.

        Args:
            domain_name: Domain name without TLD
            tld: TLD of the domain
            enable: Whether to enable or disable WHOIS privacy

        Returns:
            True if successful, False otherwise
        """
        full_domain = f"{domain_name}.{tld}"

        try:
            await self._make_request(
                "PATCH",
                f"domains/{full_domain}",
                data={"privacy": enable},
            )
            return True
        except Exception as e:
            logger.error(f"Error toggling WHOIS privacy for {full_domain}: {str(e)}")
            return False

    async def toggle_domain_lock(self, domain_name: str, tld: str, lock: bool) -> bool:
        """Lock or unlock a domain for transfer.

        Args:
            domain_name: Domain name without TLD
            tld: TLD of the domain
            lock: Whether to lock or unlock the domain

        Returns:
            True if successful, False otherwise
        """
        full_domain = f"{domain_name}.{tld}"

        try:
            await self._make_request(
                "PATCH",
                f"domains/{full_domain}",
                data={"locked": lock},
            )
            return True
        except Exception as e:
            logger.error(f"Error toggling domain lock for {full_domain}: {str(e)}")
            return False

    async def transfer_domain(
        self,
        domain_name: str,
        tld: str,
        auth_code: str,
        contacts: Dict[str, ContactInfo],
        nameservers: Optional[List[str]] = None,
    ) -> Dict[str, Any]:
        """Transfer a domain from another registrar.

        Args:
            domain_name: Domain name without TLD
            tld: TLD of the domain
            auth_code: Authorization code for transfer
            contacts: Contact information for the domain
            nameservers: List of nameserver hostnames

        Returns:
            Dictionary with transfer details (registrar-specific)
        """
        full_domain = f"{domain_name}.{tld}"

        # Prepare contact information
        godaddy_contacts = {}
        for contact_type, contact_info in contacts.items():
            godaddy_contacts[contact_type] = {
                "firstName": contact_info.first_name,
                "lastName": contact_info.last_name,
                "organization": contact_info.organization or "",
                "email": str(contact_info.email),
                "phone": contact_info.phone,
                "addressMailing": {
                    "address1": contact_info.address_line_1,
                    "address2": contact_info.address_line_2 or "",
                    "city": contact_info.city,
                    "state": contact_info.state_province,
                    "postalCode": contact_info.postal_code,
                    "country": contact_info.country,
                },
            }

        # Prepare transfer data
        transfer_data = {
            "domain": full_domain,
            "authCode": auth_code,
            "nameServers": nameservers or [],
            "renewAuto": True,
            "contactAdmin": godaddy_contacts.get("admin", godaddy_contacts.get("registrant")),
            "contactBilling": godaddy_contacts.get("billing", godaddy_contacts.get("registrant")),
            "contactRegistrant": godaddy_contacts.get("registrant"),
            "contactTech": godaddy_contacts.get("technical", godaddy_contacts.get("registrant")),
        }

        # Transfer domain
        try:
            response = await self._make_request("POST", "domains/transfer", data=transfer_data)

            return {
                "domain": full_domain,
                "order_id": response.get("orderId"),
                "created_at": datetime.now().isoformat(),
                "expiry_date": None,  # GoDaddy doesn't return expiry date in transfer response
                "registrar_data": json.dumps(response),
            }
        except Exception as e:
            logger.error(f"Error transferring domain {full_domain}: {str(e)}")
            raise RegistrarApiError(f"Failed to transfer domain: {str(e)}")
