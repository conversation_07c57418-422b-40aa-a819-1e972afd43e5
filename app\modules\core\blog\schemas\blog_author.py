"""
Blog Author Sc<PERSON>as

Pydantic models for blog author validation and serialization.
"""

import uuid
from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field, EmailStr, validator, ConfigDict


class BlogAuthorBase(BaseModel):
    """Base schema for blog authors."""
    slug: str = Field(..., max_length=255)
    display_name: str = Field(..., max_length=100)
    email: Optional[EmailStr] = None
    bio: Optional[str] = None
    avatar_url: Optional[str] = Field(None, max_length=500)
    website_url: Optional[str] = Field(None, max_length=500)
    twitter_handle: Optional[str] = Field(None, max_length=100)
    linkedin_url: Optional[str] = Field(None, max_length=500)
    github_url: Optional[str] = Field(None, max_length=500)
    is_active: bool = True
    is_featured: bool = False
    
    @validator('twitter_handle')
    def validate_twitter_handle(cls, v):
        if v and not v.startswith('@'):
            return f'@{v}'
        return v


class BlogAuthorCreate(BlogAuthorBase):
    """Schema for creating blog authors."""
    user_id: Optional[uuid.UUID] = None


class BlogAuthorUpdate(BaseModel):
    """Schema for updating blog authors."""
    slug: Optional[str] = Field(None, max_length=255)
    display_name: Optional[str] = Field(None, max_length=100)
    email: Optional[EmailStr] = None
    bio: Optional[str] = None
    avatar_url: Optional[str] = Field(None, max_length=500)
    website_url: Optional[str] = Field(None, max_length=500)
    twitter_handle: Optional[str] = Field(None, max_length=100)
    linkedin_url: Optional[str] = Field(None, max_length=500)
    github_url: Optional[str] = Field(None, max_length=500)
    is_active: Optional[bool] = None
    is_featured: Optional[bool] = None
    
    @validator('twitter_handle')
    def validate_twitter_handle(cls, v):
        if v and not v.startswith('@'):
            return f'@{v}'
        return v


class BlogAuthorRead(BlogAuthorBase):
    """Schema for reading blog authors."""
    id: uuid.UUID
    user_id: Optional[uuid.UUID]
    created_at: datetime
    updated_at: datetime
    
    model_config = ConfigDict(from_attributes=True)


class BlogAuthorList(BaseModel):
    """Schema for blog author list items (simplified)."""
    id: uuid.UUID
    slug: str
    display_name: str
    avatar_url: Optional[str]
    is_active: bool
    is_featured: bool
    created_at: datetime
    
    # Statistics
    post_count: int = 0
    total_views: int = 0
    
    model_config = ConfigDict(from_attributes=True)


class BlogAuthorProfile(BaseModel):
    """Schema for public author profile."""
    slug: str
    display_name: str
    bio: Optional[str]
    avatar_url: Optional[str]
    website_url: Optional[str]
    twitter_handle: Optional[str]
    linkedin_url: Optional[str]
    github_url: Optional[str]
    
    # Public statistics
    post_count: int = 0
    total_views: int = 0
    
    model_config = ConfigDict(from_attributes=True)
