"""
Help Center API

Endpoints da API para o sistema de help center.
"""

import logging
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status, UploadFile, File
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_db
from app.modules.core.auth.dependencies import get_current_user
from app.modules.core.users.models.user import User

from ..services import (
    TicketService, MessageService, KnowledgeBaseService,
    HelpCenterAnalyticsService, HelpCenterFileService
)
from ..schemas import (
    TicketCreate, TicketUpdate, TicketResponse, TicketListResponse,
    TicketFilters, TicketMarkAsRead, TicketAssignmentRequest, BulkTicketOperation,
    TicketMessageCreate, TicketMessageResponse, TicketMessageListResponse,
    MessageMarkAsRead,
    KnowledgeBaseArticleCreate, KnowledgeBaseArticleUpdate,
    KnowledgeBaseArticleResponse, KnowledgeBaseArticleListResponse,
    KnowledgeBaseSearchRequest,
    HelpCenterMetricsResponse, AdminDashboardMetrics
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/help-center", tags=["Help Center"])


# Dependency para obter serviços
def get_ticket_service(db: AsyncSession = Depends(get_db)) -> TicketService:
    return TicketService(db)


def get_message_service(db: AsyncSession = Depends(get_db)) -> MessageService:
    return MessageService(db)


def get_kb_service(db: AsyncSession = Depends(get_db)) -> KnowledgeBaseService:
    return KnowledgeBaseService(db)


def get_analytics_service(db: AsyncSession = Depends(get_db)) -> HelpCenterAnalyticsService:
    return HelpCenterAnalyticsService(db)


def get_file_service(db: AsyncSession = Depends(get_db)) -> HelpCenterFileService:
    return HelpCenterFileService(db)


# ===== TICKETS ENDPOINTS =====

@router.post("/tickets", response_model=TicketResponse)
async def create_ticket(
    ticket_data: TicketCreate,
    current_user: User = Depends(get_current_user),
    ticket_service: TicketService = Depends(get_ticket_service)
):
    """Cria um novo ticket de suporte."""
    try:
        ticket = await ticket_service.create_ticket(ticket_data, current_user)
        
        # Converter para response
        return TicketResponse(
            id=ticket.id,
            title=ticket.title,
            description=ticket.description,
            category=ticket.category,
            status=ticket.status,
            priority=ticket.priority,
            user_id=ticket.user_id,
            tenant_id=ticket.tenant_id,
            assigned_admin_id=ticket.assigned_admin_id,
            is_read_by_admin=ticket.is_read_by_admin,
            is_read_by_user=ticket.is_read_by_user,
            created_at=ticket.created_at,
            updated_at=ticket.updated_at,
            resolved_at=ticket.resolved_at,
            closed_at=ticket.closed_at,
            expires_at=ticket.expires_at,
            is_expired=ticket.is_expired
        )
    except Exception as e:
        logger.error(f"Erro ao criar ticket: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno do servidor"
        )


@router.get("/tickets", response_model=TicketListResponse)
async def list_tickets(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    status_filter: Optional[str] = Query(None, alias="status"),
    priority_filter: Optional[str] = Query(None, alias="priority"),
    category_filter: Optional[str] = Query(None, alias="category"),
    search: Optional[str] = Query(None),
    current_user: User = Depends(get_current_user),
    ticket_service: TicketService = Depends(get_ticket_service)
):
    """Lista tickets com filtros."""
    try:
        # Construir filtros
        filters = TicketFilters()
        if status_filter:
            filters.status = status_filter
        if priority_filter:
            filters.priority = priority_filter
        if category_filter:
            filters.category = category_filter
        if search:
            filters.search = search
        
        return await ticket_service.list_tickets(
            user=current_user,
            filters=filters,
            page=page,
            per_page=per_page
        )
    except Exception as e:
        logger.error(f"Erro ao listar tickets: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno do servidor"
        )


@router.get("/tickets/{ticket_id}", response_model=TicketResponse)
async def get_ticket(
    ticket_id: UUID,
    current_user: User = Depends(get_current_user),
    ticket_service: TicketService = Depends(get_ticket_service)
):
    """Obtém um ticket específico."""
    ticket = await ticket_service.get_ticket(ticket_id, current_user)
    if not ticket:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Ticket não encontrado"
        )
    
    return TicketResponse(
        id=ticket.id,
        title=ticket.title,
        description=ticket.description,
        category=ticket.category,
        status=ticket.status,
        priority=ticket.priority,
        user_id=ticket.user_id,
        tenant_id=ticket.tenant_id,
        assigned_admin_id=ticket.assigned_admin_id,
        is_read_by_admin=ticket.is_read_by_admin,
        is_read_by_user=ticket.is_read_by_user,
        created_at=ticket.created_at,
        updated_at=ticket.updated_at,
        resolved_at=ticket.resolved_at,
        closed_at=ticket.closed_at,
        expires_at=ticket.expires_at,
        is_expired=ticket.is_expired,
        user_name=ticket.user.full_name if ticket.user else None,
        user_email=ticket.user.email if ticket.user else None,
        assigned_admin_name=ticket.assigned_admin.full_name if ticket.assigned_admin else None,
        message_count=len(ticket.messages) if ticket.messages else 0
    )


@router.put("/tickets/{ticket_id}", response_model=TicketResponse)
async def update_ticket(
    ticket_id: UUID,
    ticket_data: TicketUpdate,
    current_user: User = Depends(get_current_user),
    ticket_service: TicketService = Depends(get_ticket_service)
):
    """Atualiza um ticket."""
    ticket = await ticket_service.update_ticket(ticket_id, ticket_data, current_user)
    if not ticket:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Ticket não encontrado ou sem permissão"
        )
    
    return TicketResponse(
        id=ticket.id,
        title=ticket.title,
        description=ticket.description,
        category=ticket.category,
        status=ticket.status,
        priority=ticket.priority,
        user_id=ticket.user_id,
        tenant_id=ticket.tenant_id,
        assigned_admin_id=ticket.assigned_admin_id,
        is_read_by_admin=ticket.is_read_by_admin,
        is_read_by_user=ticket.is_read_by_user,
        created_at=ticket.created_at,
        updated_at=ticket.updated_at,
        resolved_at=ticket.resolved_at,
        closed_at=ticket.closed_at,
        expires_at=ticket.expires_at,
        is_expired=ticket.is_expired
    )


@router.delete("/tickets/{ticket_id}")
async def delete_ticket(
    ticket_id: UUID,
    current_user: User = Depends(get_current_user),
    ticket_service: TicketService = Depends(get_ticket_service)
):
    """Deleta um ticket (apenas admin)."""
    success = await ticket_service.delete_ticket(ticket_id, current_user)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Ticket não encontrado ou sem permissão"
        )
    
    return {"message": "Ticket deletado com sucesso"}


@router.post("/tickets/assign")
async def assign_ticket(
    assignment_data: TicketAssignmentRequest,
    current_user: User = Depends(get_current_user),
    ticket_service: TicketService = Depends(get_ticket_service)
):
    """Atribui ticket a um admin."""
    ticket = await ticket_service.assign_ticket(assignment_data, current_user)
    if not ticket:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Ticket não encontrado ou sem permissão"
        )
    
    return {"message": "Ticket atribuído com sucesso"}


@router.post("/tickets/bulk-operation")
async def bulk_ticket_operation(
    operation_data: BulkTicketOperation,
    current_user: User = Depends(get_current_user),
    ticket_service: TicketService = Depends(get_ticket_service)
):
    """Executa operação em lote nos tickets."""
    processed_tickets = await ticket_service.bulk_operation(operation_data, current_user)
    
    return {
        "message": f"Operação executada em {len(processed_tickets)} tickets",
        "processed_tickets": processed_tickets
    }


@router.post("/tickets/mark-as-read")
async def mark_tickets_as_read(
    mark_data: TicketMarkAsRead,
    current_user: User = Depends(get_current_user),
    ticket_service: TicketService = Depends(get_ticket_service)
):
    """Marca tickets como lidos."""
    processed_tickets = await ticket_service.mark_tickets_as_read(mark_data.ticket_ids, current_user)
    
    return {
        "message": f"{len(processed_tickets)} tickets marcados como lidos",
        "processed_tickets": processed_tickets
    }


# ===== MESSAGES ENDPOINTS =====

@router.post("/tickets/{ticket_id}/messages", response_model=TicketMessageResponse)
async def create_message(
    ticket_id: UUID,
    message_data: TicketMessageCreate,
    current_user: User = Depends(get_current_user),
    message_service: MessageService = Depends(get_message_service)
):
    """Cria uma nova mensagem em um ticket."""
    # Garantir que o ticket_id da URL corresponde ao do body
    message_data.ticket_id = ticket_id

    message = await message_service.create_message(message_data, current_user)
    if not message:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Ticket não encontrado ou sem permissão"
        )

    return TicketMessageResponse(
        id=message.id,
        ticket_id=message.ticket_id,
        sender_id=message.sender_id,
        message_content=message.message_content,
        message_type=message.message_type,
        file_path=message.file_path,
        file_name=message.file_name,
        file_size=message.file_size,
        mime_type=message.mime_type,
        is_read=message.is_read,
        created_at=message.created_at,
        expires_at=message.expires_at,
        is_expired=message.is_expired,
        sender_name=message.sender.full_name if message.sender else None,
        sender_email=message.sender.email if message.sender else None,
        is_admin=message.sender.system_role == "admin" if message.sender else False
    )


@router.get("/tickets/{ticket_id}/messages", response_model=TicketMessageListResponse)
async def list_messages(
    ticket_id: UUID,
    page: int = Query(1, ge=1),
    per_page: int = Query(50, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    message_service: MessageService = Depends(get_message_service)
):
    """Lista mensagens de um ticket."""
    messages = await message_service.list_messages(
        ticket_id=ticket_id,
        user=current_user,
        page=page,
        per_page=per_page
    )

    if not messages:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Ticket não encontrado ou sem permissão"
        )

    return messages


@router.delete("/messages/{message_id}")
async def delete_message(
    message_id: UUID,
    current_user: User = Depends(get_current_user),
    message_service: MessageService = Depends(get_message_service)
):
    """Deleta uma mensagem."""
    success = await message_service.delete_message(message_id, current_user)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Mensagem não encontrada ou sem permissão"
        )

    return {"message": "Mensagem deletada com sucesso"}


@router.post("/messages/mark-as-read")
async def mark_messages_as_read(
    mark_data: MessageMarkAsRead,
    current_user: User = Depends(get_current_user),
    message_service: MessageService = Depends(get_message_service)
):
    """Marca mensagens como lidas."""
    processed_messages = await message_service.mark_messages_as_read(mark_data, current_user)

    return {
        "message": f"{len(processed_messages)} mensagens marcadas como lidas",
        "processed_messages": processed_messages
    }


# ===== KNOWLEDGE BASE ENDPOINTS =====

@router.post("/kb/articles", response_model=KnowledgeBaseArticleResponse)
async def create_kb_article(
    article_data: KnowledgeBaseArticleCreate,
    current_user: User = Depends(get_current_user),
    kb_service: KnowledgeBaseService = Depends(get_kb_service)
):
    """Cria um novo artigo da base de conhecimento (apenas admin)."""
    article = await kb_service.create_article(article_data, current_user)
    if not article:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Apenas administradores podem criar artigos"
        )

    return KnowledgeBaseArticleResponse(
        id=article.id,
        title=article.title,
        content=article.content,
        category=article.category,
        tags=article.tags,
        is_public=article.is_public,
        is_internal=article.is_internal,
        is_active=article.is_active,
        created_by_admin_id=article.created_by_admin_id,
        view_count=article.view_count,
        helpful_count=article.helpful_count,
        not_helpful_count=article.not_helpful_count,
        helpfulness_ratio=article.helpfulness_ratio,
        created_at=article.created_at,
        updated_at=article.updated_at,
        created_by_name=article.created_by.full_name if article.created_by else None
    )


@router.get("/kb/articles", response_model=KnowledgeBaseArticleListResponse)
async def list_kb_articles(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    category: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    current_user: User = Depends(get_current_user),
    kb_service: KnowledgeBaseService = Depends(get_kb_service)
):
    """Lista artigos da base de conhecimento."""
    return await kb_service.list_articles(
        user=current_user,
        category=category,
        search=search,
        page=page,
        per_page=per_page
    )


@router.get("/kb/articles/{article_id}", response_model=KnowledgeBaseArticleResponse)
async def get_kb_article(
    article_id: UUID,
    current_user: User = Depends(get_current_user),
    kb_service: KnowledgeBaseService = Depends(get_kb_service)
):
    """Obtém um artigo específico da base de conhecimento."""
    article = await kb_service.get_article(article_id, current_user)
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Artigo não encontrado"
        )

    return KnowledgeBaseArticleResponse(
        id=article.id,
        title=article.title,
        content=article.content,
        category=article.category,
        tags=article.tags,
        is_public=article.is_public,
        is_internal=article.is_internal,
        is_active=article.is_active,
        created_by_admin_id=article.created_by_admin_id,
        view_count=article.view_count,
        helpful_count=article.helpful_count,
        not_helpful_count=article.not_helpful_count,
        helpfulness_ratio=article.helpfulness_ratio,
        created_at=article.created_at,
        updated_at=article.updated_at,
        created_by_name=article.created_by.full_name if article.created_by else None
    )


@router.put("/kb/articles/{article_id}", response_model=KnowledgeBaseArticleResponse)
async def update_kb_article(
    article_id: UUID,
    article_data: KnowledgeBaseArticleUpdate,
    current_user: User = Depends(get_current_user),
    kb_service: KnowledgeBaseService = Depends(get_kb_service)
):
    """Atualiza um artigo da base de conhecimento (apenas admin)."""
    article = await kb_service.update_article(article_id, article_data, current_user)
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Artigo não encontrado ou sem permissão"
        )

    return KnowledgeBaseArticleResponse(
        id=article.id,
        title=article.title,
        content=article.content,
        category=article.category,
        tags=article.tags,
        is_public=article.is_public,
        is_internal=article.is_internal,
        is_active=article.is_active,
        created_by_admin_id=article.created_by_admin_id,
        view_count=article.view_count,
        helpful_count=article.helpful_count,
        not_helpful_count=article.not_helpful_count,
        helpfulness_ratio=article.helpfulness_ratio,
        created_at=article.created_at,
        updated_at=article.updated_at,
        created_by_name=article.created_by.full_name if article.created_by else None
    )


@router.delete("/kb/articles/{article_id}")
async def delete_kb_article(
    article_id: UUID,
    current_user: User = Depends(get_current_user),
    kb_service: KnowledgeBaseService = Depends(get_kb_service)
):
    """Deleta um artigo da base de conhecimento (apenas admin)."""
    success = await kb_service.delete_article(article_id, current_user)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Artigo não encontrado ou sem permissão"
        )

    return {"message": "Artigo deletado com sucesso"}


@router.post("/kb/search", response_model=List[KnowledgeBaseArticleResponse])
async def search_kb_articles(
    search_data: KnowledgeBaseSearchRequest,
    current_user: User = Depends(get_current_user),
    kb_service: KnowledgeBaseService = Depends(get_kb_service)
):
    """Busca artigos na base de conhecimento."""
    return await kb_service.search_articles(search_data, current_user)


@router.post("/kb/articles/{article_id}/helpful")
async def mark_article_helpful(
    article_id: UUID,
    is_helpful: bool = Query(...),
    current_user: User = Depends(get_current_user),
    kb_service: KnowledgeBaseService = Depends(get_kb_service)
):
    """Marca artigo como útil ou não útil."""
    article = await kb_service.mark_article_helpful(article_id, current_user, is_helpful)
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Artigo não encontrado"
        )

    return {"message": f"Artigo marcado como {'útil' if is_helpful else 'não útil'}"}


# ===== ANALYTICS ENDPOINTS =====

@router.get("/admin/metrics", response_model=HelpCenterMetricsResponse)
async def get_help_center_metrics(
    days: int = Query(30, ge=1, le=365),
    current_user: User = Depends(get_current_user),
    analytics_service: HelpCenterAnalyticsService = Depends(get_analytics_service)
):
    """Obtém métricas do help center (apenas admin)."""
    metrics = await analytics_service.get_help_center_metrics(current_user, days)
    if not metrics:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Apenas administradores podem acessar métricas"
        )

    return metrics


@router.get("/admin/dashboard", response_model=AdminDashboardMetrics)
async def get_admin_dashboard_metrics(
    days: int = Query(30, ge=1, le=365),
    current_user: User = Depends(get_current_user),
    analytics_service: HelpCenterAnalyticsService = Depends(get_analytics_service)
):
    """Obtém métricas completas para o dashboard admin."""
    metrics = await analytics_service.get_admin_dashboard_metrics(current_user, days)
    if not metrics:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Apenas administradores podem acessar o dashboard"
        )

    return metrics


# ===== FILE UPLOAD ENDPOINTS =====

@router.post("/tickets/{ticket_id}/upload")
async def upload_ticket_file(
    ticket_id: UUID,
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    file_service: HelpCenterFileService = Depends(get_file_service)
):
    """Faz upload de arquivo para um ticket."""
    # Validar tipo e tamanho do arquivo
    if file.size and file.size > 50 * 1024 * 1024:  # 50MB limit
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail="Arquivo muito grande. Limite: 50MB"
        )

    # Tipos permitidos
    allowed_types = [
        'image/jpeg', 'image/png', 'image/gif', 'image/webp',
        'application/pdf', 'text/plain', 'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/zip', 'application/x-rar-compressed'
    ]

    if file.content_type not in allowed_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Tipo de arquivo não permitido"
        )

    upload = await file_service.upload_ticket_file(
        file=file,
        user=current_user,
        ticket_id=ticket_id
    )

    if not upload:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro ao fazer upload do arquivo"
        )

    return {
        "message": "Arquivo enviado com sucesso",
        "file_id": str(upload.id),
        "filename": upload.original_filename,
        "file_size": upload.file_size,
        "mime_type": upload.mime_type
    }


@router.get("/tickets/{ticket_id}/files")
async def list_ticket_files(
    ticket_id: UUID,
    current_user: User = Depends(get_current_user),
    file_service: HelpCenterFileService = Depends(get_file_service)
):
    """Lista arquivos de um ticket."""
    files = await file_service.get_ticket_files(ticket_id, current_user)

    file_list = []
    for file_upload in files:
        file_url = await file_service.get_file_url(file_upload.id, current_user)
        file_list.append({
            "id": str(file_upload.id),
            "filename": file_upload.original_filename,
            "file_size": file_upload.file_size,
            "mime_type": file_upload.mime_type,
            "created_at": file_upload.created_at,
            "url": file_url
        })

    return {"files": file_list}


@router.get("/files/{file_id}/url")
async def get_file_url(
    file_id: UUID,
    current_user: User = Depends(get_current_user),
    file_service: HelpCenterFileService = Depends(get_file_service)
):
    """Obtém URL de acesso a um arquivo."""
    file_url = await file_service.get_file_url(file_id, current_user)

    if not file_url:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Arquivo não encontrado ou sem permissão"
        )

    return {"url": file_url}


@router.delete("/files/{file_id}")
async def delete_file(
    file_id: UUID,
    current_user: User = Depends(get_current_user),
    file_service: HelpCenterFileService = Depends(get_file_service)
):
    """Deleta um arquivo."""
    success = await file_service.delete_ticket_file(file_id, current_user)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Arquivo não encontrado ou sem permissão"
        )

    return {"message": "Arquivo deletado com sucesso"}


@router.post("/admin/cleanup-orphaned-files")
async def cleanup_orphaned_files(
    current_user: User = Depends(get_current_user),
    file_service: HelpCenterFileService = Depends(get_file_service)
):
    """Remove arquivos órfãos (apenas admin)."""
    if current_user.system_role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Apenas administradores podem executar limpeza"
        )

    removed_count = await file_service.cleanup_orphaned_files()

    return {
        "message": f"{removed_count} arquivos órfãos removidos",
        "removed_count": removed_count
    }


@router.post("/admin/tickets/bulk-update")
async def bulk_update_tickets(
    request: dict,
    current_user: User = Depends(get_current_user),
    ticket_service: TicketService = Depends(get_ticket_service)
):
    """Atualização em lote de tickets (apenas admin)."""
    if current_user.system_role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Apenas administradores podem fazer atualizações em lote"
        )

    ticket_ids = request.get("ticket_ids", [])
    action = request.get("action")
    value = request.get("value")

    if not ticket_ids or not action:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="ticket_ids e action são obrigatórios"
        )

    try:
        updated_count = await ticket_service.bulk_update_tickets(
            ticket_ids=ticket_ids,
            action=action,
            value=value,
            admin_user=current_user
        )

        return {
            "message": f"{updated_count} tickets atualizados com sucesso",
            "updated_count": updated_count
        }

    except Exception as e:
        logger.error(f"Erro na atualização em lote: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno na atualização em lote"
        )


@router.get("/admin/tickets")
async def list_admin_tickets(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    status_filter: Optional[str] = Query(None, alias="status"),
    priority_filter: Optional[str] = Query(None, alias="priority"),
    category_filter: Optional[str] = Query(None, alias="category"),
    search: Optional[str] = Query(None),
    assigned: Optional[str] = Query(None),
    sort_by: str = Query("created_at"),
    sort_order: str = Query("desc"),
    current_user: User = Depends(get_current_user),
    ticket_service: TicketService = Depends(get_ticket_service)
):
    """Lista todos os tickets para admin."""
    try:
        if current_user.system_role != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Apenas administradores podem listar todos os tickets"
            )

        filters = {}
        if status_filter:
            filters["status"] = status_filter
        if priority_filter:
            filters["priority"] = priority_filter
        if category_filter:
            filters["category"] = category_filter
        if search:
            filters["search"] = search
        if assigned:
            filters["assigned"] = assigned

        result = await ticket_service.list_admin_tickets(
            page=page,
            per_page=per_page,
            filters=filters,
            sort_by=sort_by,
            sort_order=sort_order
        )

        return result
    except Exception as e:
        logger.error(f"Erro ao listar tickets admin: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno do servidor: {str(e)}"
        )


# ===== ADMIN KNOWLEDGE BASE ENDPOINTS =====

@router.get("/admin/kb/articles")
async def list_admin_kb_articles(
    search: Optional[str] = Query(None),
    category: Optional[str] = Query(None),
    is_published: Optional[bool] = Query(None),
    current_user: User = Depends(get_current_user),
    kb_service: KnowledgeBaseService = Depends(get_kb_service)
):
    """Lista artigos da base de conhecimento para admin."""
    if current_user.system_role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Apenas administradores podem listar artigos"
        )

    filters = {}
    if search:
        filters["search"] = search
    if category:
        filters["category"] = category
    if is_published is not None:
        filters["is_published"] = is_published

    articles = await kb_service.list_admin_articles(filters)
    return {"articles": articles}


@router.post("/admin/kb/articles")
async def create_kb_article(
    article_data: dict,
    current_user: User = Depends(get_current_user),
    kb_service: KnowledgeBaseService = Depends(get_kb_service)
):
    """Cria novo artigo da base de conhecimento."""
    if current_user.system_role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Apenas administradores podem criar artigos"
        )

    article = await kb_service.create_article(article_data, current_user)
    return article


@router.put("/admin/kb/articles/{article_id}")
async def update_kb_article(
    article_id: UUID,
    article_data: dict,
    current_user: User = Depends(get_current_user),
    kb_service: KnowledgeBaseService = Depends(get_kb_service)
):
    """Atualiza artigo da base de conhecimento."""
    if current_user.system_role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Apenas administradores podem atualizar artigos"
        )

    article = await kb_service.update_article(article_id, article_data, current_user)
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Artigo não encontrado"
        )

    return article


@router.delete("/admin/kb/articles/{article_id}")
async def delete_kb_article(
    article_id: UUID,
    current_user: User = Depends(get_current_user),
    kb_service: KnowledgeBaseService = Depends(get_kb_service)
):
    """Deleta artigo da base de conhecimento."""
    if current_user.system_role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Apenas administradores podem deletar artigos"
        )

    success = await kb_service.delete_article(article_id, current_user)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Artigo não encontrado"
        )

    return {"message": "Artigo deletado com sucesso"}
