# Core - Eshop Tcustomers

**Categoria:** Core
**Mó<PERSON>lo:** Eshop Tcustomers
**Total de Endpoints:** 6
**Gerado em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [GET /api/modules/core/eshop/tcustomers/api/v1/eshop/tcustomers/list](#get-apimodulescoreeshoptcustomersapiv1eshoptcustomerslist) - List Tcustomers
- [GET /api/modules/core/eshop/tcustomers/api/v1/eshop/tcustomers/stats](#get-apimodulescoreeshoptcustomersapiv1eshoptcustomersstats) - Get Tcustomer Stats
- [GET /api/modules/core/eshop/tcustomers/api/v1/eshop/tcustomers/{customer_id}](#get-apimodulescoreeshoptcustomersapiv1eshoptcustomerscustomer-id) - Get Tcustomer
- [POST /api/modules/core/eshop/tcustomers/api/v1/eshop/tcustomers/{customer_id}/activate](#post-apimodulescoreeshoptcustomersapiv1eshoptcustomerscustomer-idactivate) - Activate Tcustomer
- [PATCH /api/modules/core/eshop/tcustomers/api/v1/eshop/tcustomers/{customer_id}/status](#patch-apimodulescoreeshoptcustomersapiv1eshoptcustomerscustomer-idstatus) - Update Tcustomer Status
- [POST /api/modules/core/eshop/tcustomers/api/v1/eshop/tcustomers/{customer_id}/suspend](#post-apimodulescoreeshoptcustomersapiv1eshoptcustomerscustomer-idsuspend) - Suspend Tcustomer

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### TCustomerListResponse

**Descrição:** Response schema for TCustomer list.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `customers` | Array[TCustomerResponse] | ✅ | - |
| `total_count` | integer | ✅ | - |
| `stats` | object | ✅ | - |

### TCustomerResponse

**Descrição:** Response schema for TCustomer data.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Customer name |
| `email` | string | ✅ | Customer email |
| `company_name` | string | ✅ | Company name |
| `phone` | unknown | ❌ | Phone number |
| `tax_id` | unknown | ❌ | Tax ID/CNPJ |
| `address` | unknown | ❌ | Business address |
| `id` | string | ✅ | - |
| `customer_type` | string | ✅ | Customer type (tcostumer or business_client) |
| `status` | string | ✅ | Customer status |
| `verification_status` | string | ✅ | Verification status |
| `credit_limit` | number | ✅ | Credit limit |
| `total_purchases` | number | ✅ | Total purchases amount |
| `orders_count` | integer | ✅ | Number of orders |
| `rating` | number | ✅ | Customer rating |
| `payment_terms` | integer | ✅ | Payment terms in days |
| `discount_rate` | number | ✅ | Discount rate percentage |
| `joined_date` | string | ✅ | Date joined |
| `last_order` | unknown | ❌ | Last order date |

### TCustomerStatsResponse

**Descrição:** Response schema for TCustomer statistics.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `active_count` | integer | ✅ | - |
| `pending_count` | integer | ✅ | - |
| `suspended_count` | integer | ✅ | - |
| `inactive_count` | integer | ✅ | - |
| `total_revenue` | number | ✅ | - |
| `average_order_value` | number | ✅ | - |
| `total_orders` | integer | ✅ | - |

### TCustomerStatusUpdate

**Descrição:** Schema for updating TCustomer status.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `status` | string | ✅ | New status (active, suspended, inactive) |
| `reason` | unknown | ❌ | Reason for status change |

## 🔗 Endpoints Detalhados

### GET /api/modules/core/eshop/tcustomers/api/v1/eshop/tcustomers/list {#get-apimodulescoreeshoptcustomersapiv1eshoptcustomerslist}

**Resumo:** List Tcustomers
**Descrição:** List TCustomers with filtering and pagination.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `status` | string | query | ❌ | Filter by status |
| `customer_type` | string | query | ❌ | Filter by customer type |
| `search` | string | query | ❌ | Search term |
| `limit` | integer | query | ❌ | Number of results to return |
| `offset` | integer | query | ❌ | Number of results to skip |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TCustomerListResponse](#tcustomerlistresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/eshop/tcustomers/api/v1/eshop/tcustomers/list" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/eshop/tcustomers/api/v1/eshop/tcustomers/stats {#get-apimodulescoreeshoptcustomersapiv1eshoptcustomersstats}

**Resumo:** Get Tcustomer Stats
**Descrição:** Get TCustomer statistics.

**🔐 Autenticação:** Requerida

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TCustomerStatsResponse](#tcustomerstatsresponse)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/eshop/tcustomers/api/v1/eshop/tcustomers/stats" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/eshop/tcustomers/api/v1/eshop/tcustomers/{customer_id} {#get-apimodulescoreeshoptcustomersapiv1eshoptcustomerscustomer-id}

**Resumo:** Get Tcustomer
**Descrição:** Get detailed information about a specific TCustomer.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `customer_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TCustomerResponse](#tcustomerresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/eshop/tcustomers/api/v1/eshop/tcustomers/{customer_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/eshop/tcustomers/api/v1/eshop/tcustomers/{customer_id}/activate {#post-apimodulescoreeshoptcustomersapiv1eshoptcustomerscustomer-idactivate}

**Resumo:** Activate Tcustomer
**Descrição:** Activate a TCustomer for B2B access.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `customer_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/eshop/tcustomers/api/v1/eshop/tcustomers/{customer_id}/activate" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PATCH /api/modules/core/eshop/tcustomers/api/v1/eshop/tcustomers/{customer_id}/status {#patch-apimodulescoreeshoptcustomersapiv1eshoptcustomerscustomer-idstatus}

**Resumo:** Update Tcustomer Status
**Descrição:** Update TCustomer status (activate, suspend, etc.).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `customer_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TCustomerStatusUpdate](#tcustomerstatusupdate)

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PATCH "http://localhost:8000/api/modules/core/eshop/tcustomers/api/v1/eshop/tcustomers/{customer_id}/status" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/core/eshop/tcustomers/api/v1/eshop/tcustomers/{customer_id}/suspend {#post-apimodulescoreeshoptcustomersapiv1eshoptcustomerscustomer-idsuspend}

**Resumo:** Suspend Tcustomer
**Descrição:** Suspend a TCustomer's B2B access.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `customer_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/eshop/tcustomers/api/v1/eshop/tcustomers/{customer_id}/suspend" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
