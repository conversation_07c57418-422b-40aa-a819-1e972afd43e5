# General - Allergens

**Categoria:** General
**Módu<PERSON>:** Allergens
**Total de Endpoints:** 5
**Gerado em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [GET /api/modules/allergens/](#get-apimodulesallergens) - Get Allergens
- [POST /api/modules/allergens/](#post-apimodulesallergens) - Create Allergen
- [DELETE /api/modules/allergens/{allergen_id}](#delete-apimodulesallergensallergen-id) - Delete Allergen
- [GET /api/modules/allergens/{allergen_id}](#get-apimodulesallergensallergen-id) - Get Allergen
- [PUT /api/modules/allergens/{allergen_id}](#put-apimodulesallergensallergen-id) - Update Allergen

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### AllergenCreate

**Descrição:** Schema for creating a new allergen.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Name of the allergen (e.g., 'Glúten', 'Lactose') |
| `icon` | unknown | ❌ | Emoji or icon representation (e.g., '🌾', '🥛') |
| `description` | unknown | ❌ | Detailed description of the allergen |
| `is_active` | boolean | ❌ | Whether this allergen is active and available for use |

### AllergenRead

**Descrição:** Schema for reading allergen data with all fields.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Name of the allergen (e.g., 'Glúten', 'Lactose') |
| `icon` | unknown | ❌ | Emoji or icon representation (e.g., '🌾', '🥛') |
| `description` | unknown | ❌ | Detailed description of the allergen |
| `is_active` | boolean | ❌ | Whether this allergen is active and available for use |
| `id` | string | ✅ | Unique identifier for the allergen |

### AllergenUpdate

**Descrição:** Schema for updating an existing allergen.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | Name of the allergen |
| `icon` | unknown | ❌ | Emoji or icon representation |
| `description` | unknown | ❌ | Detailed description of the allergen |
| `is_active` | unknown | ❌ | Whether this allergen is active |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/modules/allergens/ {#get-apimodulesallergens}

**Resumo:** Get Allergens
**Descrição:** Get list of allergens.

This endpoint is accessible to all authenticated users as they need
to see allergens when viewing menu items.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | Number of records to skip |
| `limit` | integer | query | ❌ | Number of records to return |
| `active_only` | boolean | query | ❌ | Return only active allergens |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/allergens/"
```

---

### POST /api/modules/allergens/ {#post-apimodulesallergens}

**Resumo:** Create Allergen
**Descrição:** Create a new allergen.

Only system administrators can create allergens.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [AllergenCreate](#allergencreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [AllergenRead](#allergenread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/allergens/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/allergens/{allergen_id} {#delete-apimodulesallergensallergen-id}

**Resumo:** Delete Allergen
**Descrição:** Delete an allergen.

Only system administrators can delete allergens.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `allergen_id` | string | path | ✅ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/allergens/{allergen_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/allergens/{allergen_id} {#get-apimodulesallergensallergen-id}

**Resumo:** Get Allergen
**Descrição:** Get a specific allergen by ID.

This endpoint is accessible to all authenticated users.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `allergen_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [AllergenRead](#allergenread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/allergens/{allergen_id}"
```

---

### PUT /api/modules/allergens/{allergen_id} {#put-apimodulesallergensallergen-id}

**Resumo:** Update Allergen
**Descrição:** Update an existing allergen.

Only system administrators can update allergens.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `allergen_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [AllergenUpdate](#allergenupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [AllergenRead](#allergenread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/allergens/{allergen_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
