import uuid
import enum
from datetime import datetime
from sqlalchemy import (
    <PERSON>umn,
    String,
    <PERSON><PERSON><PERSON>,
    <PERSON>olean,
    DateTime,
    Float,
    Integer,
    Enum,
    Text,
)
from sqlalchemy.dialects.postgresql import UUID, JSONB  # noqa: E402
from sqlalchemy.orm import relationship

from app.db.base import Base  # noqa: E402
from app.core.tenant_mixin import TenantMixin


class TimeRecordType(str, enum.Enum):
    """Time record type enum."""

    CHECK_IN = "check_in"
    CHECK_OUT = "check_out"
    BREAK_START = "break_start"
    BREAK_END = "break_end"
    LUNCH_START = "lunch_start"
    LUNCH_END = "lunch_end"


class TimeRecordSource(str, enum.Enum):
    """Source of the time record."""

    WEB = "web"
    MOBILE = "mobile"
    KIOSK = "kiosk"
    BIOMETRIC = "biometric"
    FACIAL = "facial"
    MANUAL = "manual"
    SYSTEM = "system"
    GEOLOCATION = "geolocation"


class VerificationStatus(str, enum.Enum):
    """Verification status for time records."""

    PENDING = "pending"
    VERIFIED = "verified"
    REJECTED = "rejected"
    ADJUSTED = "adjusted"


class TimeRecord(Base, TenantMixin):
    """Time record model for time tracking.

    This represents a single time record entry (check-in, check-out, break, etc.)
    """

    __tablename__ = "hr_time_records"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    employee_id = Column(UUID(as_uuid=True), ForeignKey("hr_employees.id"), nullable=False)

    # Time record details
    record_time = Column(DateTime, nullable=False, default=datetime.utcnow)
    record_type = Column(Enum(TimeRecordType), nullable=False)
    source = Column(Enum(TimeRecordSource), nullable=False, default=TimeRecordSource.WEB)

    # Location data
    latitude = Column(Float, nullable=True)
    longitude = Column(Float, nullable=True)
    location_name = Column(String, nullable=True)

    # Notes and metadata
    notes = Column(Text, nullable=True)
    metadata = Column(JSONB, default={})

    # Is this record verified/approved
    is_verified = Column(Boolean, default=False)
    verification_status = Column(Enum(VerificationStatus), default=VerificationStatus.PENDING)
    verified_by = Column(UUID(as_uuid=True), nullable=True)
    verification_time = Column(DateTime, nullable=True)

    # Device information for mobile/biometric check-ins
    device_id = Column(String, nullable=True)
    device_info = Column(JSONB, nullable=True)

    # Relationship
    employee = relationship("Employee", back_populates="time_records")
    time_mirror = relationship(
        "TimeMirror", secondary="hr_time_mirror_records", back_populates="time_records"
    )


class OvertimeRecord(Base, TenantMixin):
    """Overtime record model.

    This represents overtime work performed by an employee.
    """

    __tablename__ = "hr_overtime_records"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    employee_id = Column(UUID(as_uuid=True), ForeignKey("hr_employees.id"), nullable=False)

    # Overtime details
    date = Column(DateTime, nullable=False)
    start_time = Column(DateTime, nullable=False)
    end_time = Column(DateTime, nullable=False)
    duration_minutes = Column(Integer, nullable=False)

    # Approval status
    is_approved = Column(Boolean, default=False)
    approved_by = Column(UUID(as_uuid=True), nullable=True)
    approval_time = Column(DateTime, nullable=True)

    # Compensation type (paid or time bank)
    is_paid = Column(Boolean, default=True)
    is_banked = Column(Boolean, default=False)

    # Reason and notes
    reason = Column(Text, nullable=True)
    notes = Column(Text, nullable=True)

    # Additional fields for different types of overtime
    is_night_shift = Column(Boolean, default=False)
    night_shift_percentage = Column(Float, nullable=True)
    is_holiday = Column(Boolean, default=False)
    holiday_percentage = Column(Float, nullable=True)

    # Relationship
    employee = relationship("Employee", backref="overtime_records")


class TimeBank(Base, TenantMixin):
    """Time bank model for tracking banked hours.

    This represents the time bank balance for an employee.
    """

    __tablename__ = "hr_time_banks"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    employee_id = Column(
        UUID(as_uuid=True), ForeignKey("hr_employees.id"), nullable=False, unique=True
    )

    # Balance in minutes
    balance_minutes = Column(Integer, default=0)
    last_updated = Column(DateTime, default=datetime.utcnow)

    # Expiry settings
    has_expiry = Column(Boolean, default=False)
    expiry_date = Column(DateTime, nullable=True)

    # Compensation rules
    compensation_rate = Column(Float, default=1.0)  # 1.0 = 1:1 compensation, 1.5 = time and a half

    # Policy settings
    max_balance_minutes = Column(Integer, nullable=True)  # Maximum allowed balance
    min_withdrawal_minutes = Column(Integer, default=60)  # Minimum time that can be withdrawn

    # Relationship
    employee = relationship("Employee", backref="time_bank")
    transactions = relationship(
        "TimeBankTransaction", back_populates="time_bank", cascade="all, delete-orphan"
    )


class TimeBankTransactionType(str, enum.Enum):
    """Time bank transaction type enum."""

    DEPOSIT = "deposit"  # Adding time to the bank
    WITHDRAWAL = "withdrawal"  # Using banked time
    ADJUSTMENT = "adjustment"  # Administrative adjustment
    EXPIRY = "expiry"  # Time expired


class TimeBankTransaction(Base, TenantMixin):
    """Time bank transaction model.

    This represents a single transaction in an employee's time bank.
    """

    __tablename__ = "hr_time_bank_transactions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    time_bank_id = Column(UUID(as_uuid=True), ForeignKey("hr_time_banks.id"), nullable=False)

    # Transaction details
    transaction_time = Column(DateTime, nullable=False, default=datetime.utcnow)
    transaction_type = Column(Enum(TimeBankTransactionType), nullable=False)
    minutes = Column(Integer, nullable=False)  # Positive for deposit, negative for withdrawal

    # Reference to source of transaction
    # Reference to overtime record, leave request, etc.
    reference_id = Column(UUID(as_uuid=True), nullable=True)
    reference_type = Column(String, nullable=True)

    # Description and notes
    description = Column(Text, nullable=True)
    notes = Column(Text, nullable=True)

    # Who performed the transaction
    created_by = Column(UUID(as_uuid=True), nullable=True)

    # Relationship
    time_bank = relationship("TimeBank", back_populates="transactions")


class TimeMirrorStatus(str, enum.Enum):
    """Time mirror status enum."""

    DRAFT = "draft"
    SUBMITTED = "submitted"
    APPROVED = "approved"
    REJECTED = "rejected"
    FINALIZED = "finalized"


class TimeMirror(Base, TenantMixin):
    """Time mirror (espelho de ponto) model.

    This represents a summary of an employee's time records for a specific period,
    typically a month. It serves as the official record for payroll and compliance.
    """

    __tablename__ = "hr_time_mirrors"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    employee_id = Column(UUID(as_uuid=True), ForeignKey("hr_employees.id"), nullable=False)

    # Period details
    start_date = Column(DateTime, nullable=False)
    end_date = Column(DateTime, nullable=False)
    generation_date = Column(DateTime, nullable=False, default=datetime.utcnow)

    # Summary statistics
    total_worked_minutes = Column(Integer, default=0)
    total_overtime_minutes = Column(Integer, default=0)
    total_absence_minutes = Column(Integer, default=0)

    # Status tracking
    status = Column(Enum(TimeMirrorStatus), nullable=False, default=TimeMirrorStatus.DRAFT)
    submitted_at = Column(DateTime, nullable=True)
    approved_at = Column(DateTime, nullable=True)
    approved_by = Column(UUID(as_uuid=True), nullable=True)
    finalized_at = Column(DateTime, nullable=True)

    # Notes and adjustments
    notes = Column(Text, nullable=True)
    adjustment_reason = Column(Text, nullable=True)

    # Integration with payroll
    payroll_processed = Column(Boolean, default=False)
    payroll_reference = Column(String, nullable=True)

    # Relationships
    employee = relationship("Employee", backref="time_mirrors")
    time_records = relationship(
        "TimeRecord", secondary="hr_time_mirror_records", back_populates="time_mirror"
    )


class TimeMirrorRecord(Base, TenantMixin):
    """Association table between TimeMirror and TimeRecord.

    This represents which time records are included in a time mirror.
    """

    __tablename__ = "hr_time_mirror_records"

    time_mirror_id = Column(UUID(as_uuid=True), ForeignKey("hr_time_mirrors.id"), primary_key=True)
    time_record_id = Column(UUID(as_uuid=True), ForeignKey("hr_time_records.id"), primary_key=True)


class OvertimeRule(Base, TenantMixin):
    """Overtime rule model.

    This defines how overtime is calculated for different groups of employees.
    """

    __tablename__ = "hr_overtime_rules"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Rule identification
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True)

    # Target application
    department = Column(String, nullable=True)
    job_title = Column(String, nullable=True)

    # Regular overtime rules
    regular_rate = Column(Float, default=1.5)  # Default is time and a half
    weekend_rate = Column(Float, default=2.0)  # Default is double time
    holiday_rate = Column(Float, default=2.0)

    # Night shift (adicional noturno)
    night_shift_start = Column(String, nullable=True)  # HH:MM format
    night_shift_end = Column(String, nullable=True)  # HH:MM format
    night_shift_rate = Column(Float, default=1.2)  # 20% premium

    # Tolerance settings (in minutes)
    checkin_tolerance = Column(Integer, default=15)
    checkout_tolerance = Column(Integer, default=15)

    # Banking rules
    allow_banking = Column(Boolean, default=True)
    max_bank_hours = Column(Integer, nullable=True)
    banking_expiry_days = Column(Integer, nullable=True)
