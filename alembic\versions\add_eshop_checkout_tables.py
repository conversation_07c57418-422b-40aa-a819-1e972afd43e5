"""Add EShop checkout tables

Revision ID: add_eshop_checkout_tables
Revises: add_eshop_cart_tables
Create Date: 2025-01-27 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_eshop_checkout_tables'
down_revision = 'add_eshop_cart_tables'
branch_labels = None
depends_on = None


def upgrade():
    # Create enum types
    checkout_status_enum = postgresql.ENUM(
        'initiated', 'payment_pending', 'payment_processing', 
        'payment_confirmed', 'payment_failed', 'completed', 
        'cancelled', 'expired',
        name='checkoutstatus'
    )
    checkout_status_enum.create(op.get_bind())
    
    payment_method_enum = postgresql.ENUM(
        'credit_card', 'debit_card', 'pix', 'bank_transfer',
        'digital_wallet', 'cash_on_delivery', 'bank_slip',
        name='paymentmethod'
    )
    payment_method_enum.create(op.get_bind())
    
    shipping_method_enum = postgresql.ENUM(
        'standard', 'express', 'overnight', 'pickup', 'same_day',
        name='shippingmethod'
    )
    shipping_method_enum.create(op.get_bind())
    
    # Create eshop_checkout_sessions table
    op.create_table(
        'eshop_checkout_sessions',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('cart_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('order_id', postgresql.UUID(as_uuid=True), nullable=True),
        
        # Status and configuration
        sa.Column('status', checkout_status_enum, nullable=False, default='initiated'),
        sa.Column('market_context', sa.String(length=20), nullable=False, default='b2c'),
        
        # Shipping information
        sa.Column('shipping_address', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('billing_address', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('shipping_method', shipping_method_enum, nullable=True),
        sa.Column('shipping_cost', sa.Numeric(precision=10, scale=2), nullable=False, default=0.00),
        sa.Column('shipping_notes', sa.Text(), nullable=True),
        
        # Payment information
        sa.Column('payment_method', payment_method_enum, nullable=True),
        sa.Column('payment_provider', sa.String(length=50), nullable=True),
        sa.Column('payment_external_id', sa.String(length=255), nullable=True),
        sa.Column('payment_reference', sa.String(length=255), nullable=True),
        
        # Calculated totals
        sa.Column('subtotal', sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column('tax_amount', sa.Numeric(precision=10, scale=2), nullable=False, default=0.00),
        sa.Column('discount_amount', sa.Numeric(precision=10, scale=2), nullable=False, default=0.00),
        sa.Column('total_amount', sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column('currency', sa.String(length=3), nullable=False, default='BRL'),
        
        # Additional information
        sa.Column('customer_notes', sa.Text(), nullable=True),
        sa.Column('special_instructions', sa.Text(), nullable=True),
        sa.Column('metadata', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        
        # Timestamps
        sa.Column('created_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('expires_at', sa.DateTime(), nullable=True),
        sa.Column('payment_confirmed_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        
        # Primary key and foreign keys
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
        sa.ForeignKeyConstraint(['cart_id'], ['eshop_carts.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.ForeignKeyConstraint(['order_id'], ['orders.id'], ),
    )
    
    # Create indexes for performance
    op.create_index('ix_checkout_tenant_id', 'eshop_checkout_sessions', ['tenant_id'])
    op.create_index('ix_checkout_cart_id', 'eshop_checkout_sessions', ['cart_id'])
    op.create_index('ix_checkout_user_id', 'eshop_checkout_sessions', ['user_id'])
    op.create_index('ix_checkout_order_id', 'eshop_checkout_sessions', ['order_id'])
    op.create_index('ix_checkout_status', 'eshop_checkout_sessions', ['status'])
    op.create_index('ix_checkout_market_context', 'eshop_checkout_sessions', ['market_context'])
    op.create_index('ix_checkout_payment_method', 'eshop_checkout_sessions', ['payment_method'])
    op.create_index('ix_checkout_created_at', 'eshop_checkout_sessions', ['created_at'])
    
    # Composite indexes for common queries
    op.create_index('ix_checkout_tenant_user', 'eshop_checkout_sessions', ['tenant_id', 'user_id'])
    op.create_index('ix_checkout_status_expires', 'eshop_checkout_sessions', ['status', 'expires_at'])


def downgrade():
    # Drop indexes
    op.drop_index('ix_checkout_status_expires', table_name='eshop_checkout_sessions')
    op.drop_index('ix_checkout_tenant_user', table_name='eshop_checkout_sessions')
    op.drop_index('ix_checkout_created_at', table_name='eshop_checkout_sessions')
    op.drop_index('ix_checkout_payment_method', table_name='eshop_checkout_sessions')
    op.drop_index('ix_checkout_market_context', table_name='eshop_checkout_sessions')
    op.drop_index('ix_checkout_status', table_name='eshop_checkout_sessions')
    op.drop_index('ix_checkout_order_id', table_name='eshop_checkout_sessions')
    op.drop_index('ix_checkout_user_id', table_name='eshop_checkout_sessions')
    op.drop_index('ix_checkout_cart_id', table_name='eshop_checkout_sessions')
    op.drop_index('ix_checkout_tenant_id', table_name='eshop_checkout_sessions')
    
    # Drop table
    op.drop_table('eshop_checkout_sessions')
    
    # Drop enum types
    op.execute('DROP TYPE IF EXISTS shippingmethod')
    op.execute('DROP TYPE IF EXISTS paymentmethod')
    op.execute('DROP TYPE IF EXISTS checkoutstatus')
