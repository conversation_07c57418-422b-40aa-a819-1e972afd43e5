class AppConstants {
  // App Info
  static const String appName = 'OrderPad';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Sistema de pedidos para restaurantes';

  // Storage Keys
  static const String userBoxKey = 'user_box';
  static const String settingsBoxKey = 'settings_box';
  static const String ordersBoxKey = 'orders_box';
  static const String tablesBoxKey = 'tables_box';
  static const String menuBoxKey = 'menu_box';
  
  // User Preferences Keys
  static const String isLoggedInKey = 'is_logged_in';
  static const String currentUserKey = 'current_user';
  static const String userRoleKey = 'user_role';
  static const String rememberMeKey = 'remember_me';
  
  // User Roles
  static const String roleOwner = 'owner';
  static const String roleStaff = 'staff';
  
  // Order Status
  static const String orderStatusPending = 'pending';
  static const String orderStatusPreparing = 'preparing';
  static const String orderStatusReady = 'ready';
  static const String orderStatusDelivered = 'delivered';
  static const String orderStatusCancelled = 'cancelled';
  
  // Table Status
  static const String tableStatusFree = 'free';
  static const String tableStatusOccupied = 'occupied';
  static const String tableStatusReserved = 'reserved';
  static const String tableStatusCleaning = 'cleaning';
  
  // Order Types
  static const String orderTypeDineIn = 'dine_in';
  static const String orderTypeDelivery = 'delivery';
  static const String orderTypeTakeaway = 'takeaway';
  
  // Payment Methods
  static const String paymentCash = 'cash';
  static const String paymentCard = 'card';
  static const String paymentPix = 'pix';
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  static const double largeBorderRadius = 16.0;
  
  // Grid Constants
  static const int tablesPerRow = 3;
  static const int menuItemsPerRow = 2;
  
  // Limits
  static const int maxOrderItems = 50;
  static const int maxTableCapacity = 12;
  static const double maxOrderValue = 9999.99;
  
  // Mock Data
  static const String mockOwnerEmail = '<EMAIL>';
  static const String mockOwnerPassword = 'owner123';
  static const String mockStaffEmail = '<EMAIL>';
  static const String mockStaffPassword = 'waiter123';
}

class MenuCategories {
  static const String appetizers = 'Entradas';
  static const String mainCourses = 'Pratos Principais';
  static const String pizzas = 'Pizzas';
  static const String beverages = 'Bebidas';
  static const String desserts = 'Sobremesas';
  
  static const List<String> all = [
    appetizers,
    mainCourses,
    pizzas,
    beverages,
    desserts,
  ];
}

class OrderType {
  static const String dineIn = 'dine_in';
  static const String takeaway = 'takeaway';
  static const String delivery = 'delivery';
}

class PaymentMethod {
  static const String cash = 'cash';
  static const String creditCard = 'credit_card';
  static const String debitCard = 'debit_card';
  static const String pix = 'pix';
  static const String pending = 'pending';
}

class AppImages {
  static const String _basePath = 'assets/images';
  
  // Logos
  static const String logo = '$_basePath/logo.png';
  static const String logoWhite = '$_basePath/logo_white.png';
  
  // Placeholders
  static const String foodPlaceholder = '$_basePath/food_placeholder.png';
  static const String userPlaceholder = '$_basePath/user_placeholder.png';
  
  // Illustrations
  static const String emptyOrders = '$_basePath/empty_orders.svg';
  static const String emptyTables = '$_basePath/empty_tables.svg';
  static const String noInternet = '$_basePath/no_internet.svg';
}

class AppIcons {
  static const String _basePath = 'assets/icons';
  
  // Navigation
  static const String home = '$_basePath/home.svg';
  static const String orders = '$_basePath/orders.svg';
  static const String tables = '$_basePath/tables.svg';
  static const String delivery = '$_basePath/delivery.svg';
  static const String reports = '$_basePath/reports.svg';
  static const String settings = '$_basePath/settings.svg';
  
  // Actions
  static const String add = '$_basePath/add.svg';
  static const String edit = '$_basePath/edit.svg';
  static const String delete = '$_basePath/delete.svg';
  static const String search = '$_basePath/search.svg';
  static const String filter = '$_basePath/filter.svg';
  
  // Status
  static const String pending = '$_basePath/pending.svg';
  static const String preparing = '$_basePath/preparing.svg';
  static const String ready = '$_basePath/ready.svg';
  static const String delivered = '$_basePath/delivered.svg';
}

class AppAnimations {
  static const String _basePath = 'assets/animations';
  
  static const String loading = '$_basePath/loading.json';
  static const String success = '$_basePath/success.json';
  static const String error = '$_basePath/error.json';
  static const String empty = '$_basePath/empty.json';
}