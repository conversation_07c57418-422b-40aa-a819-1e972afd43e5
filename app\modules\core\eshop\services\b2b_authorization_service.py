"""
B2B Authorization Service
========================

Serviço para gerenciar o workflow de autorização B2B.
"""

import uuid
import hashlib
import os
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Tu<PERSON>
from decimal import Decimal
from pathlib import Path

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, desc
from sqlalchemy.orm import selectinload
from fastapi import UploadFile, HTTPException

from app.modules.core.eshop.models.b2b_authorization import (
    B2BAuthorizationRequest,
    B2BAuthorizationDocument,
    B2BAuthorizationHistory,
    B2BAuthorizationSettings,
    AuthorizationStatus,
    DocumentType,
    ApprovalType
)
from app.modules.core.eshop.models.tcostumer import TCostumer
from app.modules.core.eshop.models.tvendor_supplier import TVendorSupplier
from app.modules.core.eshop.schemas.b2b_authorization import (
    B2BAuthorizationRequestCreate,
    B2BAuthorizationRequestUpdate,
    B2BAuthorizationApproval,
    B2BAuthorizationRejection,
    B2BAuthorizationRequestFilter,
    B2BAuthorizationStats,
    EntityType
)


class B2BAuthorizationService:
    """Serviço para gerenciar autorizações B2B."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_authorization_request(
        self,
        request_data: B2BAuthorizationRequestCreate,
        user_id: uuid.UUID,
        tenant_id: uuid.UUID
    ) -> B2BAuthorizationRequest:
        """Cria uma nova solicitação de autorização."""
        
        # Verificar se já existe solicitação pendente para este usuário
        existing = await self.db.execute(
            select(B2BAuthorizationRequest).where(
                and_(
                    B2BAuthorizationRequest.user_id == user_id,
                    B2BAuthorizationRequest.tenant_id == tenant_id,
                    B2BAuthorizationRequest.entity_type == request_data.entity_type,
                    B2BAuthorizationRequest.status.in_([
                        AuthorizationStatus.PENDING,
                        AuthorizationStatus.UNDER_REVIEW
                    ])
                )
            )
        )
        
        if existing.scalar_one_or_none():
            raise HTTPException(
                status_code=400,
                detail=f"Já existe uma solicitação pendente para {request_data.entity_type}"
            )
        
        # Obter configurações do tenant
        settings = await self.get_or_create_settings(tenant_id)
        
        # Determinar tipo de aprovação
        approval_type = await self._determine_approval_type(request_data, settings)
        
        # Calcular data de expiração
        expires_at = datetime.utcnow() + timedelta(days=settings.request_expiry_days)
        
        # Criar solicitação
        authorization_request = B2BAuthorizationRequest(
            tenant_id=tenant_id,
            user_id=user_id,
            entity_type=request_data.entity_type.value,
            status=AuthorizationStatus.PENDING,
            approval_type=approval_type,
            company_name=request_data.company_name,
            tax_id=request_data.tax_id,
            business_type=request_data.business_type,
            contact_person=request_data.contact_person,
            contact_email=request_data.contact_email,
            contact_phone=request_data.contact_phone,
            business_address=request_data.business_address.dict() if request_data.business_address else None,
            billing_address=request_data.billing_address.dict() if request_data.billing_address else None,
            requested_credit_limit=request_data.requested_credit_limit,
            requested_payment_terms=request_data.requested_payment_terms,
            requested_commission_rate=request_data.requested_commission_rate,
            supplier_type=request_data.supplier_type,
            expires_at=expires_at,
            additional_info=request_data.additional_info
        )
        
        self.db.add(authorization_request)
        await self.db.flush()
        
        # Registrar histórico
        await self._add_history_entry(
            authorization_request.id,
            "created",
            None,
            AuthorizationStatus.PENDING.value,
            user_id,
            "Solicitação de autorização criada"
        )
        
        # Se aprovação automática, processar imediatamente
        if approval_type == ApprovalType.AUTOMATIC:
            await self._process_automatic_approval(authorization_request, settings)
        
        await self.db.commit()
        return authorization_request
    
    async def _determine_approval_type(
        self,
        request_data: B2BAuthorizationRequestCreate,
        settings: B2BAuthorizationSettings
    ) -> ApprovalType:
        """Determina o tipo de aprovação baseado nas configurações e dados."""
        
        if request_data.entity_type == EntityType.TCOSTUMER:
            if not settings.auto_approve_tcostumer:
                return ApprovalType.MANUAL
            
            # Verificar limite de crédito
            if (request_data.requested_credit_limit and 
                request_data.requested_credit_limit > settings.max_auto_credit_limit):
                return ApprovalType.MANUAL
        
        elif request_data.entity_type == EntityType.TVENDOR:
            if not settings.auto_approve_tvendor:
                return ApprovalType.MANUAL
            
            # Verificar taxa de comissão
            if (request_data.requested_commission_rate and 
                request_data.requested_commission_rate > settings.max_auto_commission_rate):
                return ApprovalType.MANUAL
        
        return ApprovalType.AUTOMATIC
    
    async def _process_automatic_approval(
        self,
        authorization_request: B2BAuthorizationRequest,
        settings: B2BAuthorizationSettings
    ):
        """Processa aprovação automática."""
        
        authorization_request.status = AuthorizationStatus.APPROVED
        authorization_request.approved_at = datetime.utcnow()
        authorization_request.approved_credit_limit = authorization_request.requested_credit_limit
        authorization_request.approved_payment_terms = authorization_request.requested_payment_terms
        authorization_request.approved_commission_rate = authorization_request.requested_commission_rate
        
        # Criar entidade correspondente
        entity = await self._create_entity(authorization_request)
        authorization_request.entity_id = entity.id
        
        # Registrar histórico
        await self._add_history_entry(
            authorization_request.id,
            "auto_approved",
            AuthorizationStatus.PENDING.value,
            AuthorizationStatus.APPROVED.value,
            None,  # Sistema
            "Aprovação automática baseada nas configurações"
        )
    
    async def _create_entity(self, authorization_request: B2BAuthorizationRequest):
        """Cria a entidade TCostumer ou TVendorSupplier."""
        
        if authorization_request.entity_type == 'tcostumer':
            entity = TCostumer(
                tenant_id=authorization_request.tenant_id,
                user_id=authorization_request.user_id,
                company_name=authorization_request.company_name,
                tax_id=authorization_request.tax_id,
                business_type=authorization_request.business_type,
                contact_person=authorization_request.contact_person,
                contact_email=authorization_request.contact_email,
                contact_phone=authorization_request.contact_phone,
                billing_address=authorization_request.billing_address,
                shipping_address=authorization_request.business_address,
                credit_limit=authorization_request.approved_credit_limit or Decimal('0'),
                payment_terms=authorization_request.approved_payment_terms or 'net_30',
                business_verification_status='verified',
                additional_metadata=authorization_request.additional_info
            )
        
        elif authorization_request.entity_type == 'tvendor':
            entity = TVendorSupplier(
                tenant_id=authorization_request.tenant_id,
                user_id=authorization_request.user_id,
                company_name=authorization_request.company_name,
                tax_id=authorization_request.tax_id,
                supplier_type=authorization_request.supplier_type or 'standard',
                contact_person=authorization_request.contact_person,
                contact_email=authorization_request.contact_email,
                contact_phone=authorization_request.contact_phone,
                business_address=authorization_request.business_address,
                commission_rate=authorization_request.approved_commission_rate or Decimal('5.0'),
                verification_status='verified',
                additional_metadata=authorization_request.additional_info
            )
        
        else:
            raise ValueError(f"Tipo de entidade inválido: {authorization_request.entity_type}")
        
        self.db.add(entity)
        await self.db.flush()
        return entity
    
    async def approve_request(
        self,
        request_id: uuid.UUID,
        approval_data: B2BAuthorizationApproval,
        approver_id: uuid.UUID,
        tenant_id: uuid.UUID
    ) -> B2BAuthorizationRequest:
        """Aprova uma solicitação de autorização."""
        
        # Buscar solicitação
        result = await self.db.execute(
            select(B2BAuthorizationRequest).where(
                and_(
                    B2BAuthorizationRequest.id == request_id,
                    B2BAuthorizationRequest.tenant_id == tenant_id
                )
            )
        )
        
        authorization_request = result.scalar_one_or_none()
        if not authorization_request:
            raise HTTPException(status_code=404, detail="Solicitação não encontrada")
        
        if not authorization_request.can_be_approved():
            raise HTTPException(
                status_code=400,
                detail=f"Solicitação não pode ser aprovada. Status atual: {authorization_request.status}"
            )
        
        # Atualizar solicitação
        previous_status = authorization_request.status
        authorization_request.status = AuthorizationStatus.APPROVED
        authorization_request.approved_by = approver_id
        authorization_request.approved_at = datetime.utcnow()
        authorization_request.approved_credit_limit = approval_data.approved_credit_limit
        authorization_request.approved_payment_terms = approval_data.approved_payment_terms
        authorization_request.approved_commission_rate = approval_data.approved_commission_rate
        authorization_request.internal_notes = approval_data.internal_notes
        
        # Criar entidade correspondente
        entity = await self._create_entity(authorization_request)
        authorization_request.entity_id = entity.id
        
        # Registrar histórico
        await self._add_history_entry(
            authorization_request.id,
            "approved",
            previous_status.value,
            AuthorizationStatus.APPROVED.value,
            approver_id,
            "Solicitação aprovada manualmente"
        )
        
        await self.db.commit()
        return authorization_request
    
    async def reject_request(
        self,
        request_id: uuid.UUID,
        rejection_data: B2BAuthorizationRejection,
        rejector_id: uuid.UUID,
        tenant_id: uuid.UUID
    ) -> B2BAuthorizationRequest:
        """Rejeita uma solicitação de autorização."""
        
        # Buscar solicitação
        result = await self.db.execute(
            select(B2BAuthorizationRequest).where(
                and_(
                    B2BAuthorizationRequest.id == request_id,
                    B2BAuthorizationRequest.tenant_id == tenant_id
                )
            )
        )
        
        authorization_request = result.scalar_one_or_none()
        if not authorization_request:
            raise HTTPException(status_code=404, detail="Solicitação não encontrada")
        
        if not authorization_request.can_be_approved():
            raise HTTPException(
                status_code=400,
                detail=f"Solicitação não pode ser rejeitada. Status atual: {authorization_request.status}"
            )
        
        # Atualizar solicitação
        previous_status = authorization_request.status
        authorization_request.status = AuthorizationStatus.REJECTED
        authorization_request.rejection_reason = rejection_data.rejection_reason
        authorization_request.rejection_notes = rejection_data.rejection_notes
        
        # Registrar histórico
        await self._add_history_entry(
            authorization_request.id,
            "rejected",
            previous_status.value,
            AuthorizationStatus.REJECTED.value,
            rejector_id,
            f"Solicitação rejeitada: {rejection_data.rejection_reason}"
        )
        
        await self.db.commit()
        return authorization_request
    
    async def _add_history_entry(
        self,
        request_id: uuid.UUID,
        action: str,
        previous_status: Optional[str],
        new_status: Optional[str],
        performed_by: Optional[uuid.UUID],
        notes: Optional[str] = None,
        changes: Optional[Dict[str, Any]] = None
    ):
        """Adiciona entrada no histórico."""
        
        history_entry = B2BAuthorizationHistory(
            authorization_request_id=request_id,
            action=action,
            previous_status=previous_status,
            new_status=new_status,
            performed_by=performed_by,
            notes=notes,
            changes=changes
        )
        
        self.db.add(history_entry)
    
    async def get_or_create_settings(self, tenant_id: uuid.UUID) -> B2BAuthorizationSettings:
        """Obtém ou cria configurações para o tenant."""
        
        result = await self.db.execute(
            select(B2BAuthorizationSettings).where(
                B2BAuthorizationSettings.tenant_id == tenant_id
            )
        )
        
        settings = result.scalar_one_or_none()
        if not settings:
            settings = B2BAuthorizationSettings(tenant_id=tenant_id)
            self.db.add(settings)
            await self.db.flush()
        
        return settings

    async def upload_document(
        self,
        request_id: uuid.UUID,
        document_type: DocumentType,
        document_name: str,
        file: UploadFile,
        tenant_id: uuid.UUID
    ) -> B2BAuthorizationDocument:
        """Faz upload de documento para a solicitação."""

        # Verificar se solicitação existe
        result = await self.db.execute(
            select(B2BAuthorizationRequest).where(
                and_(
                    B2BAuthorizationRequest.id == request_id,
                    B2BAuthorizationRequest.tenant_id == tenant_id
                )
            )
        )

        authorization_request = result.scalar_one_or_none()
        if not authorization_request:
            raise HTTPException(status_code=404, detail="Solicitação não encontrada")

        # Validar arquivo
        await self._validate_document_file(file)

        # Gerar hash do arquivo
        file_content = await file.read()
        file_hash = hashlib.sha256(file_content).hexdigest()

        # Definir caminho do arquivo
        upload_dir = self._get_upload_directory(tenant_id, request_id)
        upload_dir.mkdir(parents=True, exist_ok=True)

        file_extension = Path(file.filename).suffix
        safe_filename = f"{document_type.value}_{uuid.uuid4()}{file_extension}"
        file_path = upload_dir / safe_filename

        # Salvar arquivo
        with open(file_path, "wb") as f:
            f.write(file_content)

        # Criar registro do documento
        document = B2BAuthorizationDocument(
            authorization_request_id=request_id,
            document_type=document_type,
            document_name=document_name,
            original_filename=file.filename,
            file_path=str(file_path),
            file_size=len(file_content),
            file_type=file.content_type,
            file_hash=file_hash
        )

        self.db.add(document)
        await self.db.commit()

        return document

    async def _validate_document_file(self, file: UploadFile):
        """Valida arquivo de documento."""

        # Verificar tamanho (máximo 10MB)
        max_size = 10 * 1024 * 1024  # 10MB
        file_content = await file.read()
        await file.seek(0)  # Reset file pointer

        if len(file_content) > max_size:
            raise HTTPException(
                status_code=400,
                detail="Arquivo muito grande. Tamanho máximo: 10MB"
            )

        # Verificar tipo de arquivo
        allowed_types = [
            'application/pdf',
            'image/jpeg',
            'image/png',
            'image/gif',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ]

        if file.content_type not in allowed_types:
            raise HTTPException(
                status_code=400,
                detail="Tipo de arquivo não permitido"
            )

    def _get_upload_directory(self, tenant_id: uuid.UUID, request_id: uuid.UUID) -> Path:
        """Retorna diretório para upload de documentos."""
        base_dir = Path("/app/uploads/b2b_authorization")
        return base_dir / str(tenant_id) / str(request_id)

    async def list_requests(
        self,
        tenant_id: uuid.UUID,
        filters: Optional[B2BAuthorizationRequestFilter] = None,
        page: int = 1,
        per_page: int = 20
    ) -> Tuple[List[B2BAuthorizationRequest], int]:
        """Lista solicitações com filtros e paginação."""

        query = select(B2BAuthorizationRequest).where(
            B2BAuthorizationRequest.tenant_id == tenant_id
        ).options(
            selectinload(B2BAuthorizationRequest.documents),
            selectinload(B2BAuthorizationRequest.history)
        )

        # Aplicar filtros
        if filters:
            if filters.status:
                query = query.where(B2BAuthorizationRequest.status.in_(filters.status))

            if filters.entity_type:
                query = query.where(B2BAuthorizationRequest.entity_type.in_([et.value for et in filters.entity_type]))

            if filters.approval_type:
                query = query.where(B2BAuthorizationRequest.approval_type.in_(filters.approval_type))

            if filters.company_name:
                query = query.where(B2BAuthorizationRequest.company_name.ilike(f"%{filters.company_name}%"))

            if filters.tax_id:
                query = query.where(B2BAuthorizationRequest.tax_id.ilike(f"%{filters.tax_id}%"))

            if filters.contact_email:
                query = query.where(B2BAuthorizationRequest.contact_email.ilike(f"%{filters.contact_email}%"))

            if filters.created_from:
                query = query.where(B2BAuthorizationRequest.created_at >= filters.created_from)

            if filters.created_to:
                query = query.where(B2BAuthorizationRequest.created_at <= filters.created_to)

            if filters.is_expired is not None:
                if filters.is_expired:
                    query = query.where(
                        and_(
                            B2BAuthorizationRequest.expires_at.isnot(None),
                            B2BAuthorizationRequest.expires_at < datetime.utcnow()
                        )
                    )
                else:
                    query = query.where(
                        or_(
                            B2BAuthorizationRequest.expires_at.is_(None),
                            B2BAuthorizationRequest.expires_at >= datetime.utcnow()
                        )
                    )

        # Contar total
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()

        # Aplicar paginação e ordenação
        query = query.order_by(desc(B2BAuthorizationRequest.created_at))
        query = query.offset((page - 1) * per_page).limit(per_page)

        result = await self.db.execute(query)
        requests = result.scalars().all()

        return requests, total

    async def get_request_by_id(
        self,
        request_id: uuid.UUID,
        tenant_id: uuid.UUID
    ) -> Optional[B2BAuthorizationRequest]:
        """Busca solicitação por ID."""

        result = await self.db.execute(
            select(B2BAuthorizationRequest).where(
                and_(
                    B2BAuthorizationRequest.id == request_id,
                    B2BAuthorizationRequest.tenant_id == tenant_id
                )
            ).options(
                selectinload(B2BAuthorizationRequest.documents),
                selectinload(B2BAuthorizationRequest.history)
            )
        )

        return result.scalar_one_or_none()

    async def get_statistics(self, tenant_id: uuid.UUID) -> B2BAuthorizationStats:
        """Obtém estatísticas de autorização."""

        # Contadores básicos
        total_result = await self.db.execute(
            select(func.count()).where(B2BAuthorizationRequest.tenant_id == tenant_id)
        )
        total_requests = total_result.scalar()

        # Por status
        status_counts = {}
        for status in AuthorizationStatus:
            result = await self.db.execute(
                select(func.count()).where(
                    and_(
                        B2BAuthorizationRequest.tenant_id == tenant_id,
                        B2BAuthorizationRequest.status == status
                    )
                )
            )
            status_counts[status.value] = result.scalar()

        # Por tipo de entidade
        tcostumer_result = await self.db.execute(
            select(func.count()).where(
                and_(
                    B2BAuthorizationRequest.tenant_id == tenant_id,
                    B2BAuthorizationRequest.entity_type == 'tcostumer'
                )
            )
        )
        tcostumer_requests = tcostumer_result.scalar()

        tvendor_result = await self.db.execute(
            select(func.count()).where(
                and_(
                    B2BAuthorizationRequest.tenant_id == tenant_id,
                    B2BAuthorizationRequest.entity_type == 'tvendor'
                )
            )
        )
        tvendor_requests = tvendor_result.scalar()

        # Aprovações automáticas vs manuais
        auto_result = await self.db.execute(
            select(func.count()).where(
                and_(
                    B2BAuthorizationRequest.tenant_id == tenant_id,
                    B2BAuthorizationRequest.approval_type == ApprovalType.AUTOMATIC,
                    B2BAuthorizationRequest.status == AuthorizationStatus.APPROVED
                )
            )
        )
        automatic_approvals = auto_result.scalar()

        manual_result = await self.db.execute(
            select(func.count()).where(
                and_(
                    B2BAuthorizationRequest.tenant_id == tenant_id,
                    B2BAuthorizationRequest.approval_type == ApprovalType.MANUAL,
                    B2BAuthorizationRequest.status == AuthorizationStatus.APPROVED
                )
            )
        )
        manual_approvals = manual_result.scalar()

        # Documentos
        docs_result = await self.db.execute(
            select(func.count()).select_from(
                B2BAuthorizationDocument.__table__.join(
                    B2BAuthorizationRequest.__table__
                )
            ).where(B2BAuthorizationRequest.tenant_id == tenant_id)
        )
        total_documents = docs_result.scalar()

        verified_docs_result = await self.db.execute(
            select(func.count()).select_from(
                B2BAuthorizationDocument.__table__.join(
                    B2BAuthorizationRequest.__table__
                )
            ).where(
                and_(
                    B2BAuthorizationRequest.tenant_id == tenant_id,
                    B2BAuthorizationDocument.is_verified == True
                )
            )
        )
        verified_documents = verified_docs_result.scalar()

        return B2BAuthorizationStats(
            total_requests=total_requests,
            pending_requests=status_counts.get('pending', 0),
            approved_requests=status_counts.get('approved', 0),
            rejected_requests=status_counts.get('rejected', 0),
            expired_requests=status_counts.get('expired', 0),
            tcostumer_requests=tcostumer_requests,
            tvendor_requests=tvendor_requests,
            automatic_approvals=automatic_approvals,
            manual_approvals=manual_approvals,
            total_documents=total_documents,
            verified_documents=verified_documents,
            pending_verification=total_documents - verified_documents
        )
