"""
Audit Log Model

Database model for storing audit trail of all critical operations.
"""

import uuid
from datetime import datetime
from typing import Optional

from sqlalchemy import Column, String, DateTime, Text, JSON, Index
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func

from app.db.base import Base


class AuditLog(Base):
    """
    Audit log model for tracking all critical system operations.
    
    This table stores a complete audit trail of:
    - User actions (login, logout, profile changes)
    - B2B operations (approvals, rejections, status changes)
    - Financial transactions (payments, refunds, commission changes)
    - Order operations (creation, status changes, cancellations)
    - Administrative actions (user management, system configuration)
    - Data modifications (create, update, delete operations)
    """
    
    __tablename__ = "audit_logs"
    
    # Primary key
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        index=True
    )
    
    # Timestamp information
    timestamp = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        index=True
    )
    
    # User information
    user_id = Column(
        UUID(as_uuid=True),
        nullable=True,  # Some system actions may not have a user
        index=True
    )
    
    # Session information
    session_id = Column(String(255), nullable=True, index=True)
    ip_address = Column(String(45), nullable=True)  # IPv6 compatible
    user_agent = Column(Text, nullable=True)
    
    # Action information
    action = Column(String(50), nullable=False, index=True)
    resource = Column(String(50), nullable=False, index=True)
    resource_id = Column(String(255), nullable=True, index=True)
    
    # Tenant context (for multi-tenant operations)
    tenant_id = Column(
        UUID(as_uuid=True),
        nullable=True,
        index=True
    )
    
    # Operation details
    operation_type = Column(String(20), nullable=False, index=True)  # CREATE, READ, UPDATE, DELETE, EXECUTE
    status = Column(String(20), nullable=False, default="SUCCESS")  # SUCCESS, FAILURE, PENDING
    
    # Data tracking
    old_values = Column(JSON, nullable=True)  # Previous state (for updates/deletes)
    new_values = Column(JSON, nullable=True)  # New state (for creates/updates)
    
    # Additional context
    details = Column(JSON, nullable=True)  # Additional operation details
    error_message = Column(Text, nullable=True)  # Error details if status is FAILURE
    
    # Request context
    request_id = Column(String(255), nullable=True, index=True)  # For tracing requests
    correlation_id = Column(String(255), nullable=True, index=True)  # For tracing workflows
    
    # Performance tracking
    duration_ms = Column(String(20), nullable=True)  # Operation duration in milliseconds
    
    # Security context
    security_level = Column(String(20), nullable=False, default="NORMAL")  # NORMAL, SENSITIVE, CRITICAL
    compliance_tags = Column(JSON, nullable=True)  # For compliance requirements (GDPR, PCI, etc.)
    
    # Indexes for efficient querying
    __table_args__ = (
        # Composite indexes for common query patterns
        Index('ix_audit_user_timestamp', 'user_id', 'timestamp'),
        Index('ix_audit_resource_timestamp', 'resource', 'timestamp'),
        Index('ix_audit_tenant_timestamp', 'tenant_id', 'timestamp'),
        Index('ix_audit_action_resource', 'action', 'resource'),
        Index('ix_audit_status_timestamp', 'status', 'timestamp'),
        Index('ix_audit_security_level', 'security_level'),
        Index('ix_audit_operation_type', 'operation_type'),
        
        # Specialized indexes for audit queries
        Index('ix_audit_resource_id_timestamp', 'resource', 'resource_id', 'timestamp'),
        Index('ix_audit_user_action_timestamp', 'user_id', 'action', 'timestamp'),
        Index('ix_audit_tenant_resource_timestamp', 'tenant_id', 'resource', 'timestamp'),
    )
    
    def __repr__(self) -> str:
        return (
            f"<AuditLog(id={self.id}, "
            f"user_id={self.user_id}, "
            f"action={self.action}, "
            f"resource={self.resource}, "
            f"timestamp={self.timestamp})>"
        )
    
    @property
    def is_sensitive(self) -> bool:
        """Check if this audit log contains sensitive information."""
        return self.security_level in ["SENSITIVE", "CRITICAL"]
    
    @property
    def is_financial(self) -> bool:
        """Check if this audit log is related to financial operations."""
        financial_resources = [
            "payment", "invoice", "commission", "refund", 
            "transaction", "financial_account", "billing"
        ]
        return self.resource in financial_resources
    
    @property
    def is_b2b_operation(self) -> bool:
        """Check if this audit log is related to B2B operations."""
        b2b_resources = [
            "tcustomer", "tvendor_supplier", "b2b_approval", 
            "b2b_application", "credit_limit", "pricing_tier"
        ]
        return self.resource in b2b_resources or "b2b" in self.action
    
    def to_dict(self) -> dict:
        """Convert audit log to dictionary for JSON serialization."""
        return {
            "id": str(self.id),
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
            "user_id": str(self.user_id) if self.user_id else None,
            "session_id": self.session_id,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "action": self.action,
            "resource": self.resource,
            "resource_id": self.resource_id,
            "tenant_id": str(self.tenant_id) if self.tenant_id else None,
            "operation_type": self.operation_type,
            "status": self.status,
            "old_values": self.old_values,
            "new_values": self.new_values,
            "details": self.details,
            "error_message": self.error_message,
            "request_id": self.request_id,
            "correlation_id": self.correlation_id,
            "duration_ms": self.duration_ms,
            "security_level": self.security_level,
            "compliance_tags": self.compliance_tags,
        }
