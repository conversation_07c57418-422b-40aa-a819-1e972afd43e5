"""
Router for the i18n module.
"""

from fastapi import APIRouter

from app.modules.core.i18n.api.language_api import router as language_router
from app.modules.core.i18n.api.translation_api import router as translation_router
from app.modules.core.i18n.api.translation_suggestion_api import (
    router as translation_suggestion_router,
)
from app.modules.core.i18n.api.translation_change_api import router as translation_change_router

# Create the main router
router = APIRouter()

# Include the sub-routers
router.include_router(language_router, prefix="/languages", tags=["i18n-languages"])
router.include_router(translation_router, prefix="/translations", tags=["i18n-translations"])
router.include_router(
    translation_suggestion_router,
    prefix="/translation-suggestions",
    tags=["i18n-translation-suggestions"],
)
router.include_router(
    translation_change_router, prefix="/translation-changes", tags=["i18n-translation-changes"]
)
