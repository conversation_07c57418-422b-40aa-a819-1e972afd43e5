"""
Security exceptions for the auth module.
"""

from fastapi import HTT<PERSON><PERSON>x<PERSON>, status

# Common exceptions for authentication and authorization
credentials_exception = HTTPException(
    status_code=status.HTTP_401_UNAUTHORIZED,
    detail="Could not validate credentials",
    headers={"WWW-Authenticate": "Bearer"},
)

permission_denied_exception = HTTPException(
    status_code=status.HTTP_403_FORBIDDEN,
    detail="Not enough permissions",
)
