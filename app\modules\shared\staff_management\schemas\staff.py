# app/modules/sharedModules/staff_management/schemas/staff.py
from pydantic import BaseModel, EmailStr, ConfigDict
from typing import Optional


class StaffAssociationCreate(BaseModel):
    """
    Schema for associating an existing user with a tenant as staff.
    """

    user_email: EmailStr
    role: str  # e.g., 'manager', 'staff'


class StaffAssociationUpdate(BaseModel):
    """
    Schema for updating the role of a staff member within a tenant.
    """

    role: str


class StaffMember(BaseModel):
    """
    Schema for representing a staff member, combining user details and their role.
    """

    id: int
    email: EmailStr
    full_name: Optional[str] = None
    role: str

    model_config = ConfigDict(from_attributes=True)
