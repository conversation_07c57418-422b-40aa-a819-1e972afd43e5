import uuid
from typing import TYPE_CHECKING
from sqlalchemy import (
    Integer,
    String,
    ForeignKey,
    Numeric,
    Index,
    Boolean,
)
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.orm import relationship, Mapped, mapped_column

from app.db.base import Base

if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from .variant_group import VariantGroup


class VariantOption(Base):
    """
    Represents a specific choice within a VariantGroup (e.g., Small, Medium, Large).
    """
    __tablename__ = "variant_options"

    id: Mapped[uuid.UUID] = mapped_column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    tenant_id: Mapped[uuid.UUID] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    variant_group_id: Mapped[uuid.UUID] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("variant_groups.id"), nullable=False, index=True)

    name: Mapped[str] = mapped_column(String(100), nullable=False)
    price_adjustment: Mapped[float] = mapped_column(Numeric(10, 2), default=0.00, nullable=False)
    display_order: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    is_default: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)

    # Relationships
    tenant: Mapped["Tenant"] = relationship("Tenant")
    variant_group: Mapped["VariantGroup"] = relationship("VariantGroup", back_populates="options")

    __table_args__ = (
        Index("ix_variant_options_tenant_id_group_id", "tenant_id", "variant_group_id"),
        Index(
            "ix_variant_options_tenant_id_group_id_name",
            "tenant_id",
            "variant_group_id",
            "name",
            unique=True,
        ),
    )

    def __repr__(self) -> str:
        return f"<VariantOption(id={self.id}, name='{self.name}', group_id={self.variant_group_id}, tenant_id={self.tenant_id})>" 