"""
Comprehensive Shopping List Module Tests
Tests shopping list management, item operations, and inventory integration
"""

import pytest
import subprocess
import json
import uuid
from typing import Dict, Any, Optional


class ShoppingListTester:
    """Helper class for shopping list module testing"""

    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.tokens = {}
        self.tenant_id = None
        self.list_id = None
        self.item_id = None
        self.inventory_item_id = None

    def curl_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        token: Optional[str] = None,
        tenant_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Execute curl request and return parsed JSON response"""

        cmd = ["curl", "-s", "-X", method, f"{self.base_url}{endpoint}"]

        # Add headers
        if headers:
            for key, value in headers.items():
                cmd.extend(["-H", f"{key}: {value}"])

        # Add authorization header
        if token:
            cmd.extend(["-H", f"Authorization: Bearer {token}"])

        # Add tenant header
        if tenant_id:
            cmd.extend(["-H", f"X-Tenant-ID: {tenant_id}"])

        # Add content type for POST/PUT requests
        if data:
            cmd.extend(["-H", "Content-Type: application/json"])
            cmd.extend(["-d", json.dumps(data)])

        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.stdout:
                return json.loads(result.stdout)
            return {"error": "No response", "stderr": result.stderr}
        except json.JSONDecodeError:
            return {"error": "Invalid JSON", "stdout": result.stdout}
        except Exception as e:
            return {"error": str(e)}

    def login(self, email: str, password: str) -> str:
        """Login and return access token"""
        response = self.curl_request(
            "POST", "/api/auth/login", data={"email": email, "password": password}
        )

        if "access_token" in response:
            token = response["access_token"]
            self.tokens[email] = token
            return token

        raise Exception(f"Login failed for {email}: {response}")

    def get_user_tenant(self, token: str) -> str:
        """Get user's first tenant ID"""
        response = self.curl_request("GET", "/api/users/me/tenants", token=token)

        tenants = response if isinstance(response, list) else response.get("tenants", [])
        if tenants:
            return tenants[0].get("id") or tenants[0].get("tenant_id")

        return None

    def setup_inventory_item(self, token: str) -> str:
        """Create inventory item for integration tests"""
        item_data = {
            "name": f"Test Inventory Item {uuid.uuid4().hex[:8]}",
            "description": "Test item for shopping list integration",
            "quantity": 5,  # Low stock for testing
            "unit_cost": 10.50,
        }

        response = self.curl_request(
            "POST",
            "/api/modules/inventory/items",
            data=item_data,
            token=token,
            tenant_id=self.tenant_id,
        )

        if "id" in response:
            self.inventory_item_id = response["id"]
            return response["id"]
        
        return None


@pytest.fixture
def shopping_list_tester():
    """Fixture providing shopping list tester instance"""
    tester = ShoppingListTester()
    # Setup tenant context
    token = tester.login("<EMAIL>", "password")
    tester.tenant_id = tester.get_user_tenant(token)
    return tester


class TeseshoppingListPermissions:
    """Test shopping list module access permissions"""

    def test_tenant_owner_can_access_shopping_list(self, shopping_list_tester):
        """Test tenant owner can access shopping list module"""
        token = shopping_list_tester.tokens["<EMAIL>"]

        response = shopping_list_tester.curl_request(
            "GET",
            "/api/modules/shopping-list/lists",
            token=token,
            tenant_id=shopping_list_tester.tenant_id,
        )

        # Should return list or successful response
        assert "error" not in response or response.get("status_code") != 403
        assert isinstance(response, list) or "lists" in response

    def test_customer_cannot_access_shopping_list(self, shopping_list_tester):
        """Test customer cannot access shopping list module"""
        token = shopping_list_tester.login("<EMAIL>", "password")

        response = shopping_list_tester.curl_request(
            "GET",
            "/api/modules/shopping-list/lists",
            token=token,
            tenant_id=shopping_list_tester.tenant_id,
        )

        # Should return 403 Forbidden
        assert (
            "error" in response
            or "detail" in response
            or response.get("status_code") == 403
            or "Forbidden" in str(response)
            or "permission" in str(response).lower()
        )

    def test_unauthenticated_cannot_access_shopping_list(self, shopping_list_tester):
        """Test unauthenticated user cannot access shopping list module"""
        response = shopping_list_tester.curl_request(
            "GET", "/api/modules/shopping-list/lists", tenant_id=shopping_list_tester.tenant_id
        )

        # Should return 401 Unauthorized
        assert (
            "error" in response
            or "detail" in response
            or "Unauthorized" in str(response)
            or "authentication" in str(response).lower()
        )


class TeseshoppingListManagement:
    """Test shopping list CRUD operations"""

    def test_create_shopping_list(self, shopping_list_tester):
        """Test creating a new shopping list"""
        token = shopping_list_tester.tokens["<EMAIL>"]

        list_data = {
            "name": f"Test Shopping List {uuid.uuid4().hex[:8]}",
            "description": "Test shopping list for automated testing",
            "is_active": True,
        }

        response = shopping_list_tester.curl_request(
            "POST",
            "/api/modules/shopping-list/lists",
            data=list_data,
            token=token,
            tenant_id=shopping_list_tester.tenant_id,
        )

        if "error" not in response and "detail" not in response:
            # List creation successful
            assert "id" in response
            assert "name" in response
            shopping_list_tester.list_id = response["id"]
        else:
            # Check if it's a permission error or other issue
            assert "error" in response or "detail" in response

    def test_list_shopping_lists(self, shopping_list_tester):
        """Test listing shopping lists"""
        token = shopping_list_tester.tokens["<EMAIL>"]

        response = shopping_list_tester.curl_request(
            "GET",
            "/api/modules/shopping-list/lists",
            token=token,
            tenant_id=shopping_list_tester.tenant_id,
        )

        # Should return list of shopping lists
        assert "error" not in response or response.get("status_code") != 403
        assert isinstance(response, list) or "lists" in response

    def test_get_shopping_list_details(self, shopping_list_tester):
        """Test getting shopping list details"""
        if not shopping_list_tester.list_id:
            pytest.skip("No shopping list ID available")

        token = shopping_list_tester.tokens["<EMAIL>"]

        response = shopping_list_tester.curl_request(
            "GET",
            f"/api/modules/shopping-list/lists/{shopping_list_tester.list_id}",
            token=token,
            tenant_id=shopping_list_tester.tenant_id,
        )

        if "error" not in response and "detail" not in response:
            # Should return list details
            assert "id" in response
            assert "name" in response
        else:
            # Endpoint might not exist or list not found
            assert "error" in response or "detail" in response

    def test_update_shopping_list(self, shopping_list_tester):
        """Test updating shopping list"""
        if not shopping_list_tester.list_id:
            pytest.skip("No shopping list ID available")

        token = shopping_list_tester.tokens["<EMAIL>"]

        update_data = {
            "name": f"Updated Shopping List {uuid.uuid4().hex[:8]}",
            "description": "Updated shopping list description",
        }

        response = shopping_list_tester.curl_request(
            "PUT",
            f"/api/modules/shopping-list/lists/{shopping_list_tester.list_id}",
            data=update_data,
            token=token,
            tenant_id=shopping_list_tester.tenant_id,
        )

        if "error" not in response and "detail" not in response:
            # Update successful
            assert "id" in response
        else:
            # Endpoint might not exist
            assert "error" in response or "detail" in response

    def test_get_list_with_items(self, shopping_list_tester):
        """Test getting shopping list with items"""
        if not shopping_list_tester.list_id:
            pytest.skip("No shopping list ID available")

        token = shopping_list_tester.tokens["<EMAIL>"]

        response = shopping_list_tester.curl_request(
            "GET",
            f"/api/modules/shopping-list/lists/{shopping_list_tester.list_id}/with-items",
            token=token,
            tenant_id=shopping_list_tester.tenant_id,
        )

        if "error" not in response and "detail" not in response:
            # Should return list with items
            assert "id" in response
            assert "items" in response
        else:
            # Endpoint might not exist
            assert "error" in response or "detail" in response


class TeseshoppingListItemManagement:
    """Test shopping list item CRUD operations"""

    @pytest.fixture(autouse=True)
    def setup_shopping_list(self, shopping_list_tester):
        """Setup shopping list for item tests"""
        token = shopping_list_tester.tokens["<EMAIL>"]

        # Create a shopping list first
        list_data = {
            "name": f"Item Test List {uuid.uuid4().hex[:8]}",
            "description": "Test list for item operations",
            "is_active": True,
        }

        response = shopping_list_tester.curl_request(
            "POST",
            "/api/modules/shopping-list/lists",
            data=list_data,
            token=token,
            tenant_id=shopping_list_tester.tenant_id,
        )

        if "id" in response:
            shopping_list_tester.list_id = response["id"]

    def test_create_shopping_list_item(self, shopping_list_tester):
        """Test creating a new shopping list item"""
        if not shopping_list_tester.list_id:
            pytest.skip("No shopping list available")

        token = shopping_list_tester.tokens["<EMAIL>"]

        item_data = {
            "name": f"Test Item {uuid.uuid4().hex[:8]}",
            "quantity": 3,
            "unit": "kg",
            "priority": "medium",
            "estimated_price": 15.50,
            "notes": "Test shopping list item",
        }

        response = shopping_list_tester.curl_request(
            "POST",
            f"/api/modules/shopping-list/lists/{shopping_list_tester.list_id}/items",
            data=item_data,
            token=token,
            tenant_id=shopping_list_tester.tenant_id,
        )

        if "error" not in response and "detail" not in response:
            # Item creation successful
            assert "id" in response
            assert "name" in response
            shopping_list_tester.item_id = response["id"]
        else:
            # Check if it's a permission error or other issue
            assert "error" in response or "detail" in response

    def test_list_shopping_list_items(self, shopping_list_tester):
        """Test listing shopping list items"""
        if not shopping_list_tester.list_id:
            pytest.skip("No shopping list available")

        token = shopping_list_tester.tokens["<EMAIL>"]

        response = shopping_list_tester.curl_request(
            "GET",
            f"/api/modules/shopping-list/lists/{shopping_list_tester.list_id}/items",
            token=token,
            tenant_id=shopping_list_tester.tenant_id,
        )

        # Should return list of items
        assert "error" not in response or response.get("status_code") != 403
        assert isinstance(response, list) or "items" in response

    def test_get_shopping_list_item_details(self, shopping_list_tester):
        """Test getting shopping list item details"""
        if not shopping_list_tester.item_id:
            pytest.skip("No shopping list item ID available")

        token = shopping_list_tester.tokens["<EMAIL>"]

        response = shopping_list_tester.curl_request(
            "GET",
            f"/api/modules/shopping-list/items/{shopping_list_tester.item_id}",
            token=token,
            tenant_id=shopping_list_tester.tenant_id,
        )

        if "error" not in response and "detail" not in response:
            # Should return item details
            assert "id" in response
            assert "name" in response
        else:
            # Endpoint might not exist or item not found
            assert "error" in response or "detail" in response

    def test_update_shopping_list_item(self, shopping_list_tester):
        """Test updating shopping list item"""
        if not shopping_list_tester.item_id:
            pytest.skip("No shopping list item ID available")

        token = shopping_list_tester.tokens["<EMAIL>"]

        update_data = {
            "name": f"Updated Item {uuid.uuid4().hex[:8]}",
            "quantity": 5,
            "priority": "high",
            "notes": "Updated shopping list item",
        }

        response = shopping_list_tester.curl_request(
            "PUT",
            f"/api/modules/shopping-list/items/{shopping_list_tester.item_id}",
            data=update_data,
            token=token,
            tenant_id=shopping_list_tester.tenant_id,
        )

        if "error" not in response and "detail" not in response:
            # Update successful
            assert "id" in response
        else:
            # Endpoint might not exist
            assert "error" in response or "detail" in response

    def test_toggle_item_purchased(self, shopping_list_tester):
        """Test toggling item purchased status"""
        if not shopping_list_tester.item_id:
            pytest.skip("No shopping list item ID available")

        token = shopping_list_tester.tokens["<EMAIL>"]

        toggle_data = {"purchased": True}

        response = shopping_list_tester.curl_request(
            "POST",
            f"/api/modules/shopping-list/items/{shopping_list_tester.item_id}/toggle-purchased",
            data=toggle_data,
            token=token,
            tenant_id=shopping_list_tester.tenant_id,
        )

        if "error" not in response and "detail" not in response:
            # Toggle successful
            assert "id" in response
            assert "purchased" in response
        else:
            # Endpoint might not exist
            assert "error" in response or "detail" in response

    def test_delete_shopping_list_item(self, shopping_list_tester):
        """Test deleting shopping list item"""
        if not shopping_list_tester.item_id:
            pytest.skip("No shopping list item ID available")

        token = shopping_list_tester.tokens["<EMAIL>"]

        response = shopping_list_tester.curl_request(
            "DELETE",
            f"/api/modules/shopping-list/items/{shopping_list_tester.item_id}",
            token=token,
            tenant_id=shopping_list_tester.tenant_id,
        )

        if "error" not in response and "detail" not in response:
            # Delete successful
            assert "id" in response or response == {}
        else:
            # Endpoint might not exist
            assert "error" in response or "detail" in response


class TestInventoryIntegration:
    """Test shopping list integration with inventory module"""

    @pytest.fixture(autouse=True)
    def setup_integration_test(self, shopping_list_tester):
        """Setup for integration tests"""
        token = shopping_list_tester.tokens["<EMAIL>"]

        # Setup inventory item
        shopping_list_tester.setup_inventory_item(token)

        # Setup shopping list
        list_data = {
            "name": f"Integration Test List {uuid.uuid4().hex[:8]}",
            "description": "Test list for inventory integration",
            "is_active": True,
        }

        response = shopping_list_tester.curl_request(
            "POST",
            "/api/modules/shopping-list/lists",
            data=list_data,
            token=token,
            tenant_id=shopping_list_tester.tenant_id,
        )

        if "id" in response:
            shopping_list_tester.list_id = response["id"]

    def test_get_low_stock_suggestions(self, shopping_list_tester):
        """Test getting low stock suggestions from inventory"""
        token = shopping_list_tester.tokens["<EMAIL>"]

        response = shopping_list_tester.curl_request(
            "GET",
            "/api/modules/shopping-list/suggestions/low-stock?threshold=10",
            token=token,
            tenant_id=shopping_list_tester.tenant_id,
        )

        if "error" not in response and "detail" not in response:
            # Should return list of suggestions
            assert isinstance(response, list)
        else:
            # Endpoint might not exist
            assert "error" in response or "detail" in response

    def test_create_item_from_inventory(self, shopping_list_tester):
        """Test creating shopping list item from inventory item"""
        if not shopping_list_tester.list_id or not shopping_list_tester.inventory_item_id:
            pytest.skip("No shopping list or inventory item available")

        token = shopping_list_tester.tokens["<EMAIL>"]

        item_data = {
            "inventory_item_id": shopping_list_tester.inventory_item_id,
            "quantity": 2,
        }

        response = shopping_list_tester.curl_request(
            "POST",
            f"/api/modules/shopping-list/lists/{shopping_list_tester.list_id}/items/from-inventory",
            data=item_data,
            token=token,
            tenant_id=shopping_list_tester.tenant_id,
        )

        if "error" not in response and "detail" not in response:
            # Item creation from inventory successful
            assert "id" in response
            assert "inventory_item_id" in response
        else:
            # Endpoint might not exist
            assert "error" in response or "detail" in response


class TeseshoppingListTenantIsolation:
    """Test tenant isolation in shopping list module"""

    def test_cannot_access_other_tenant_shopping_lists(self, shopping_list_tester):
        """Test that users cannot access other tenant's shopping lists"""
        token = shopping_list_tester.tokens["<EMAIL>"]
        fake_tenant_id = str(uuid.uuid4())

        response = shopping_list_tester.curl_request(
            "GET", "/api/modules/shopping-list/lists", token=token, tenant_id=fake_tenant_id
        )

        # Should return error about tenant not found or access denied
        assert (
            "error" in response
            or "detail" in response
            or "tenant" in str(response).lower()
            or "not found" in str(response).lower()
            or "access" in str(response).lower()
        )

    def test_missing_tenant_header_error(self, shopping_list_tester):
        """Test that missing tenant header returns appropriate error"""
        token = shopping_list_tester.tokens["<EMAIL>"]

        response = shopping_list_tester.curl_request(
            "GET", "/api/modules/shopping-list/lists", token=token
        )

        # Should return error about missing tenant header
        assert (
            "error" in response
            or "detail" in response
            or "tenant" in str(response).lower()
            or "header" in str(response).lower()
        )


class TeseshoppingListValidation:
    """Test shopping list data validation"""

    def test_create_list_with_invalid_data(self, shopping_list_tester):
        """Test creating shopping list with invalid data"""
        token = shopping_list_tester.tokens["<EMAIL>"]

        # Missing required fields
        invalid_data = {"description": "List without name"}

        response = shopping_list_tester.curl_request(
            "POST",
            "/api/modules/shopping-list/lists",
            data=invalid_data,
            token=token,
            tenant_id=shopping_list_tester.tenant_id,
        )

        # Should return validation error
        assert (
            "error" in response
            or "detail" in response
            or "validation" in str(response).lower()
            or "required" in str(response).lower()
        )

    def test_create_item_with_invalid_priority(self, shopping_list_tester):
        """Test creating item with invalid priority"""
        if not shopping_list_tester.list_id:
            # Create a list first
            token = shopping_list_tester.tokens["<EMAIL>"]
            list_data = {
                "name": f"Validation Test List {uuid.uuid4().hex[:8]}",
                "description": "Test list for validation",
            }

            list_response = shopping_list_tester.curl_request(
                "POST",
                "/api/modules/shopping-list/lists",
                data=list_data,
                token=token,
                tenant_id=shopping_list_tester.tenant_id,
            )

            if "id" in list_response:
                shopping_list_tester.list_id = list_response["id"]
            else:
                pytest.skip("Could not create shopping list for validation test")

        token = shopping_list_tester.tokens["<EMAIL>"]

        # Invalid priority value
        invalid_data = {
            "name": "Test Item",
            "quantity": 1,
            "priority": "invalid_priority",
        }

        response = shopping_list_tester.curl_request(
            "POST",
            f"/api/modules/shopping-list/lists/{shopping_list_tester.list_id}/items",
            data=invalid_data,
            token=token,
            tenant_id=shopping_list_tester.tenant_id,
        )

        # Should return validation error
        assert (
            "error" in response
            or "detail" in response
            or "validation" in str(response).lower()
            or "invalid" in str(response).lower()
        )
