"""
Help Center Analytics Service

Serviço para métricas e analytics do sistema de help center.
"""

import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional
from uuid import UUID

from sqlalchemy import and_, func, case, Float
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.modules.core.users.models.user import User

from ..models import Ticket, TicketMessage, KnowledgeBaseArticle, TicketStatus, TicketPriority
from ..schemas import HelpCenterMetricsResponse, AdminDashboardMetrics

logger = logging.getLogger(__name__)


class HelpCenterAnalyticsService:
    """Serviço para analytics do help center."""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_help_center_metrics(
        self,
        admin_user: User,
        days: int = 30
    ) -> Optional[HelpCenterMetricsResponse]:
        """
        Obtém métricas gerais do help center.
        
        Args:
            admin_user: Admin solicitante
            days: Período em dias para análise
            
        Returns:
            Métricas ou None se não for admin
        """
        if admin_user.system_role != "admin":
            return None
        
        # Data de início do período
        start_date = datetime.now(timezone.utc) - timedelta(days=days)
        today = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        week_start = today - timedelta(days=7)
        
        # Métricas gerais de tickets
        total_tickets_stmt = select(func.count(Ticket.id))
        total_tickets_result = await self.db.execute(total_tickets_stmt)
        total_tickets = total_tickets_result.scalar() or 0
        
        # Tickets por status
        status_counts = {}
        for status in TicketStatus:
            status_stmt = select(func.count(Ticket.id)).where(Ticket.status == status)
            status_result = await self.db.execute(status_stmt)
            status_counts[status.value] = status_result.scalar() or 0
        
        # Tickets por prioridade
        priority_counts = {}
        for priority in TicketPriority:
            priority_stmt = select(func.count(Ticket.id)).where(
                and_(
                    Ticket.priority == priority,
                    Ticket.status.in_([TicketStatus.NEW, TicketStatus.OPEN, TicketStatus.PENDING])
                )
            )
            priority_result = await self.db.execute(priority_stmt)
            priority_counts[priority.value] = priority_result.scalar() or 0
        
        # Métricas de tempo de resposta
        response_time_stmt = select(
            func.avg(
                func.extract('epoch', TicketMessage.created_at - Ticket.created_at) / 3600
            )
        ).select_from(
            Ticket.__table__.join(
                TicketMessage.__table__,
                and_(
                    Ticket.id == TicketMessage.ticket_id,
                    TicketMessage.sender_id != Ticket.user_id  # Primeira resposta do admin
                )
            )
        ).where(Ticket.created_at >= start_date)
        
        response_time_result = await self.db.execute(response_time_stmt)
        avg_response_time = response_time_result.scalar() or 0.0
        
        # Tempo de resolução
        resolution_time_stmt = select(
            func.avg(
                func.extract('epoch', Ticket.resolved_at - Ticket.created_at) / 3600
            )
        ).where(
            and_(
                Ticket.status == TicketStatus.RESOLVED,
                Ticket.created_at >= start_date,
                Ticket.resolved_at.isnot(None)
            )
        )
        
        resolution_time_result = await self.db.execute(resolution_time_stmt)
        avg_resolution_time = resolution_time_result.scalar() or 0.0
        
        # Métricas da base de conhecimento
        kb_total_stmt = select(func.count(KnowledgeBaseArticle.id)).where(
            KnowledgeBaseArticle.is_active == True
        )
        kb_total_result = await self.db.execute(kb_total_stmt)
        total_kb_articles = kb_total_result.scalar() or 0
        
        kb_views_stmt = select(func.sum(KnowledgeBaseArticle.view_count)).where(
            KnowledgeBaseArticle.is_active == True
        )
        kb_views_result = await self.db.execute(kb_views_stmt)
        total_kb_views = kb_views_result.scalar() or 0
        
        # Taxa de utilidade da KB
        kb_helpfulness_stmt = select(
            func.avg(
                case(
                    (KnowledgeBaseArticle.helpful_count + KnowledgeBaseArticle.not_helpful_count > 0,
                     func.cast(KnowledgeBaseArticle.helpful_count, Float) /
                     (KnowledgeBaseArticle.helpful_count + KnowledgeBaseArticle.not_helpful_count)),
                    else_=0.0
                )
            )
        ).where(KnowledgeBaseArticle.is_active == True)
        
        kb_helpfulness_result = await self.db.execute(kb_helpfulness_stmt)
        avg_kb_helpfulness = kb_helpfulness_result.scalar() or 0.0
        
        # Tickets criados hoje
        tickets_today_stmt = select(func.count(Ticket.id)).where(
            Ticket.created_at >= today
        )
        tickets_today_result = await self.db.execute(tickets_today_stmt)
        tickets_created_today = tickets_today_result.scalar() or 0
        
        # Tickets resolvidos hoje
        resolved_today_stmt = select(func.count(Ticket.id)).where(
            and_(
                Ticket.status == TicketStatus.RESOLVED,
                Ticket.resolved_at >= today
            )
        )
        resolved_today_result = await self.db.execute(resolved_today_stmt)
        tickets_resolved_today = resolved_today_result.scalar() or 0
        
        # Tickets criados esta semana
        tickets_week_stmt = select(func.count(Ticket.id)).where(
            Ticket.created_at >= week_start
        )
        tickets_week_result = await self.db.execute(tickets_week_stmt)
        tickets_created_this_week = tickets_week_result.scalar() or 0
        
        # Tickets resolvidos esta semana
        resolved_week_stmt = select(func.count(Ticket.id)).where(
            and_(
                Ticket.status == TicketStatus.RESOLVED,
                Ticket.resolved_at >= week_start
            )
        )
        resolved_week_result = await self.db.execute(resolved_week_stmt)
        tickets_resolved_this_week = resolved_week_result.scalar() or 0
        
        # Taxa de resolução
        resolution_rate = 0.0
        if total_tickets > 0:
            resolved_count = status_counts.get('resolved', 0) + status_counts.get('closed', 0)
            resolution_rate = (resolved_count / total_tickets) * 100
        
        # Taxa de primeira resposta
        first_response_rate = 0.0
        if total_tickets > 0:
            responded_tickets_stmt = select(func.count(func.distinct(Ticket.id))).select_from(
                Ticket.__table__.join(TicketMessage.__table__)
            ).where(
                and_(
                    Ticket.created_at >= start_date,
                    TicketMessage.sender_id != Ticket.user_id
                )
            )
            responded_result = await self.db.execute(responded_tickets_stmt)
            responded_tickets = responded_result.scalar() or 0
            
            period_tickets_stmt = select(func.count(Ticket.id)).where(
                Ticket.created_at >= start_date
            )
            period_tickets_result = await self.db.execute(period_tickets_stmt)
            period_tickets = period_tickets_result.scalar() or 0
            
            if period_tickets > 0:
                first_response_rate = (responded_tickets / period_tickets) * 100
        
        return HelpCenterMetricsResponse(
            total_tickets=total_tickets,
            open_tickets=status_counts.get('open', 0) + status_counts.get('new', 0),
            resolved_tickets=status_counts.get('resolved', 0),
            closed_tickets=status_counts.get('closed', 0),
            pending_tickets=status_counts.get('pending', 0),
            urgent_tickets=priority_counts.get('urgent', 0),
            high_priority_tickets=priority_counts.get('high', 0),
            medium_priority_tickets=priority_counts.get('medium', 0),
            low_priority_tickets=priority_counts.get('low', 0),
            average_response_time_hours=round(avg_response_time, 2),
            average_resolution_time_hours=round(avg_resolution_time, 2),
            total_kb_articles=total_kb_articles,
            total_kb_views=total_kb_views,
            average_kb_helpfulness=round(avg_kb_helpfulness, 2),
            tickets_created_today=tickets_created_today,
            tickets_resolved_today=tickets_resolved_today,
            tickets_created_this_week=tickets_created_this_week,
            tickets_resolved_this_week=tickets_resolved_this_week,
            resolution_rate=round(resolution_rate, 2),
            first_response_rate=round(first_response_rate, 2)
        )

    async def get_admin_dashboard_metrics(
        self,
        admin_user: User,
        days: int = 30
    ) -> Optional[AdminDashboardMetrics]:
        """
        Obtém métricas completas para o dashboard admin.
        
        Args:
            admin_user: Admin solicitante
            days: Período em dias para análise
            
        Returns:
            Métricas do dashboard ou None se não for admin
        """
        if admin_user.system_role != "admin":
            return None
        
        # Métricas gerais
        overview = await self.get_help_center_metrics(admin_user, days)
        if not overview:
            return None
        
        # Tickets por status (últimos 30 dias)
        start_date = datetime.now(timezone.utc) - timedelta(days=30)
        tickets_by_status = {}
        
        for status in TicketStatus:
            status_stmt = select(func.count(Ticket.id)).where(
                and_(
                    Ticket.status == status,
                    Ticket.created_at >= start_date
                )
            )
            status_result = await self.db.execute(status_stmt)
            tickets_by_status[status.value] = status_result.scalar() or 0
        
        # Tickets por categoria (últimos 30 dias)
        tickets_by_category = {}
        categories_stmt = select(
            Ticket.category,
            func.count(Ticket.id)
        ).where(
            Ticket.created_at >= start_date
        ).group_by(Ticket.category)
        
        categories_result = await self.db.execute(categories_stmt)
        for category, count in categories_result:
            tickets_by_category[category.value] = count
        
        # Performance dos admins (simplificado)
        admin_performance = []
        admins_stmt = select(
            Ticket.assigned_admin_id,
            func.count(Ticket.id).label('assigned_tickets'),
            func.count(
                case((Ticket.status == TicketStatus.RESOLVED, 1))
            ).label('resolved_tickets')
        ).where(
            and_(
                Ticket.assigned_admin_id.isnot(None),
                Ticket.created_at >= start_date
            )
        ).group_by(Ticket.assigned_admin_id)
        
        admins_result = await self.db.execute(admins_stmt)
        for admin_id, assigned, resolved in admins_result:
            admin_performance.append({
                'admin_id': str(admin_id),
                'assigned_tickets': assigned,
                'resolved_tickets': resolved,
                'resolution_rate': round((resolved / assigned) * 100, 2) if assigned > 0 else 0
            })
        
        # Tendência diária (últimos 7 dias)
        daily_trend = []
        for i in range(7):
            day_start = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=i)
            day_end = day_start + timedelta(days=1)
            
            created_stmt = select(func.count(Ticket.id)).where(
                and_(
                    Ticket.created_at >= day_start,
                    Ticket.created_at < day_end
                )
            )
            created_result = await self.db.execute(created_stmt)
            created_count = created_result.scalar() or 0
            
            daily_trend.append({
                'date': day_start.strftime('%Y-%m-%d'),
                'tickets_created': created_count
            })
        
        # Tendência semanal de resolução (últimas 4 semanas)
        weekly_resolution_trend = []
        for i in range(4):
            week_start = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(weeks=i+1)
            week_end = week_start + timedelta(weeks=1)
            
            resolved_stmt = select(func.count(Ticket.id)).where(
                and_(
                    Ticket.status == TicketStatus.RESOLVED,
                    Ticket.resolved_at >= week_start,
                    Ticket.resolved_at < week_end
                )
            )
            resolved_result = await self.db.execute(resolved_stmt)
            resolved_count = resolved_result.scalar() or 0
            
            weekly_resolution_trend.append({
                'week_start': week_start.strftime('%Y-%m-%d'),
                'tickets_resolved': resolved_count
            })
        
        return AdminDashboardMetrics(
            overview=overview,
            tickets_by_status=tickets_by_status,
            tickets_by_category=tickets_by_category,
            admin_performance=admin_performance,
            daily_ticket_trend=daily_trend,
            weekly_resolution_trend=weekly_resolution_trend
        )
