import logging
import uuid
from typing import List, Optional, Annotated, Any

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession

logger = logging.getLogger(__name__)

from app.modules.core.users.models.user import User
from app.modules.core.auth.dependencies.auth_dependencies import get_current_active_user
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    get_current_tenant_from_header,
    require_tenant_role,
)
from app.modules.core.tenants.models.tenant import Tenant
from app.modules.core.roles.models.roles import RolePermissions

from app.modules.core.eshop.services.product_variant_service import ProductVariantService
from app.modules.core.eshop.schemas.product_variant_group import (
    ProductVariantGroupCreate,
    ProductVariantGroupUpdate,
    ProductVariantGroupResponse,
)
from app.modules.core.eshop.schemas.product_variant_option import (
    ProductVariantOptionCreate,
    ProductVariantOptionUpdate,
    ProductVariantOptionResponse,
)

from app.core.dependencies import get_db

async def get_variant_service(db_session: AsyncSession = Depends(get_db)) -> ProductVariantService:
    return ProductVariantService(db_session)

router = APIRouter(prefix="/variants", tags=["eshop - Variants"])

write_roles = RolePermissions.ADMIN_ROLES + ["TVendorSupplier"]
view_roles = RolePermissions.VIEW_ROLES + ["TCostumer"]


# Variant Groups
@router.post("/groups/", response_model=ProductVariantGroupResponse, status_code=status.HTTP_201_CREATED)
async def create_variant_group(
    group_in: ProductVariantGroupCreate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Optional[Tenant], Depends(get_current_tenant_from_header)],
    variant_service: Annotated[ProductVariantService, Depends(get_variant_service)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=write_roles))],
):
    """Create a new variant group."""
    try:
        if current_tenant:
            group_in.tenant_id = current_tenant.id

        created_group = await variant_service.create_variant_group(
            group_in=group_in,
            current_user_id=current_user.id
        )
        
        return ProductVariantGroupResponse.model_validate(created_group)
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"Error creating variant group: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/groups/", response_model=List[ProductVariantGroupResponse])
async def read_variant_groups(
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Optional[Tenant], Depends(get_current_tenant_from_header)],
    variant_service: Annotated[ProductVariantService, Depends(get_variant_service)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))],
    is_active: Optional[bool] = Query(None),
    is_template: Optional[bool] = Query(None),
    include_options: bool = Query(False),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=200),
):
    """Retrieve variant groups."""
    try:
        tenant_id = current_tenant.id if current_tenant else None
        
        groups = await variant_service.get_variant_groups(
            tenant_id=tenant_id,
            is_active=is_active,
            is_template=is_template,
            include_options=include_options,
            skip=skip,
            limit=limit,
        )

        return [ProductVariantGroupResponse.model_validate(group) for group in groups]
    except Exception as e:
        logger.exception(f"Error retrieving variant groups: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/groups/{group_id}", response_model=ProductVariantGroupResponse)
async def read_variant_group(
    group_id: Annotated[uuid.UUID, Path(...)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Optional[Tenant], Depends(get_current_tenant_from_header)],
    variant_service: Annotated[ProductVariantService, Depends(get_variant_service)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))],
    include_options: bool = Query(False),
):
    """Retrieve a specific variant group."""
    try:
        tenant_id = current_tenant.id if current_tenant else None
        
        group = await variant_service.get_variant_group(
            group_id=group_id,
            tenant_id=tenant_id,
            include_options=include_options
        )
        
        if group is None:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Variant group not found")

        return ProductVariantGroupResponse.model_validate(group)
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"Error retrieving variant group {group_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


# Variant Options
@router.post("/options/", response_model=ProductVariantOptionResponse, status_code=status.HTTP_201_CREATED)
async def create_variant_option(
    option_in: ProductVariantOptionCreate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Optional[Tenant], Depends(get_current_tenant_from_header)],
    variant_service: Annotated[ProductVariantService, Depends(get_variant_service)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=write_roles))],
):
    """Create a new variant option."""
    try:
        if current_tenant:
            option_in.tenant_id = current_tenant.id

        created_option = await variant_service.create_variant_option(
            option_in=option_in,
            current_user_id=current_user.id
        )
        
        return ProductVariantOptionResponse.model_validate(created_option)
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"Error creating variant option: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/options/", response_model=List[ProductVariantOptionResponse])
async def read_variant_options(
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Optional[Tenant], Depends(get_current_tenant_from_header)],
    variant_service: Annotated[ProductVariantService, Depends(get_variant_service)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))],
    variant_group_id: Optional[uuid.UUID] = Query(None),
    is_active: Optional[bool] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=200),
):
    """Retrieve variant options."""
    try:
        tenant_id = current_tenant.id if current_tenant else None
        
        options = await variant_service.get_variant_options(
            variant_group_id=variant_group_id,
            tenant_id=tenant_id,
            is_active=is_active,
            skip=skip,
            limit=limit,
        )

        return [ProductVariantOptionResponse.model_validate(option) for option in options]
    except Exception as e:
        logger.exception(f"Error retrieving variant options: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/options/{option_id}", response_model=ProductVariantOptionResponse)
async def read_variant_option(
    option_id: Annotated[uuid.UUID, Path(...)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Optional[Tenant], Depends(get_current_tenant_from_header)],
    variant_service: Annotated[ProductVariantService, Depends(get_variant_service)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))],
):
    """Retrieve a specific variant option."""
    try:
        tenant_id = current_tenant.id if current_tenant else None
        
        option = await variant_service.get_variant_option(option_id=option_id, tenant_id=tenant_id)
        
        if option is None:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Variant option not found")

        return ProductVariantOptionResponse.model_validate(option)
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"Error retrieving variant option {option_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
