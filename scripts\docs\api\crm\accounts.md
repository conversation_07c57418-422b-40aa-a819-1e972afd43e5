# Crm - Accounts

**Categoria:** Crm
**<PERSON>ó<PERSON>lo:** Accounts
**Total de Endpoints:** 5
**Gerado em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [GET /api/modules/crm/crm/accounts/](#get-apimodulescrmcrmaccounts) - Get Accounts
- [POST /api/modules/crm/crm/accounts/](#post-apimodulescrmcrmaccounts) - Create Account
- [DELETE /api/modules/crm/crm/accounts/{account_id}](#delete-apimodulescrmcrmaccountsaccount-id) - Delete Account
- [GET /api/modules/crm/crm/accounts/{account_id}](#get-apimodulescrmcrmaccountsaccount-id) - Get Account
- [PUT /api/modules/crm/crm/accounts/{account_id}](#put-apimodulescrmcrmaccountsaccount-id) - Update Account

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### AccountCreate

**Descrição:** Schema for creating a new Account.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `account_type` | AccountType | ❌ | - |
| `status` | AccountStatus | ❌ | - |
| `email` | unknown | ❌ | - |
| `phone` | unknown | ❌ | - |
| `website` | unknown | ❌ | - |
| `address_line1` | unknown | ❌ | - |
| `address_line2` | unknown | ❌ | - |
| `city` | unknown | ❌ | - |
| `state` | unknown | ❌ | - |
| `postal_code` | unknown | ❌ | - |
| `country` | unknown | ❌ | - |
| `tax_id` | unknown | ❌ | - |
| `industry` | unknown | ❌ | - |
| `annual_revenue` | unknown | ❌ | - |
| `number_of_employees` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `notes` | unknown | ❌ | - |
| `user_tenant_association_id` | unknown | ❌ | - |
| `acquisition_date` | unknown | ❌ | - |

### AccountRead

**Descrição:** Schema for reading an Account.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `account_type` | AccountType | ❌ | - |
| `status` | AccountStatus | ❌ | - |
| `email` | unknown | ❌ | - |
| `phone` | unknown | ❌ | - |
| `website` | unknown | ❌ | - |
| `address_line1` | unknown | ❌ | - |
| `address_line2` | unknown | ❌ | - |
| `city` | unknown | ❌ | - |
| `state` | unknown | ❌ | - |
| `postal_code` | unknown | ❌ | - |
| `country` | unknown | ❌ | - |
| `tax_id` | unknown | ❌ | - |
| `industry` | unknown | ❌ | - |
| `annual_revenue` | unknown | ❌ | - |
| `number_of_employees` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `notes` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `user_tenant_association_id` | unknown | ❌ | - |
| `acquisition_date` | unknown | ❌ | - |
| `last_contact_date` | unknown | ❌ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### AccountUpdate

**Descrição:** Schema for updating an Account.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | - |
| `account_type` | unknown | ❌ | - |
| `status` | unknown | ❌ | - |
| `email` | unknown | ❌ | - |
| `phone` | unknown | ❌ | - |
| `website` | unknown | ❌ | - |
| `address_line1` | unknown | ❌ | - |
| `address_line2` | unknown | ❌ | - |
| `city` | unknown | ❌ | - |
| `state` | unknown | ❌ | - |
| `postal_code` | unknown | ❌ | - |
| `country` | unknown | ❌ | - |
| `tax_id` | unknown | ❌ | - |
| `industry` | unknown | ❌ | - |
| `annual_revenue` | unknown | ❌ | - |
| `number_of_employees` | unknown | ❌ | - |
| `acquisition_date` | unknown | ❌ | - |
| `last_contact_date` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `notes` | unknown | ❌ | - |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/modules/crm/crm/accounts/ {#get-apimodulescrmcrmaccounts}

**Resumo:** Get Accounts
**Descrição:** Get all CRM accounts with optional filtering.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | Number of accounts to skip |
| `limit` | integer | query | ❌ | Maximum number of accounts to return |
| `status` | string | query | ❌ | Filter by account status |
| `account_type` | string | query | ❌ | Filter by account type |
| `search` | string | query | ❌ | Search term for account name, email, or phone |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/accounts/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/crm/crm/accounts/ {#post-apimodulescrmcrmaccounts}

**Resumo:** Create Account
**Descrição:** Create a new CRM account.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [AccountCreate](#accountcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [AccountRead](#accountread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/crm/crm/accounts/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/crm/crm/accounts/{account_id} {#delete-apimodulescrmcrmaccountsaccount-id}

**Resumo:** Delete Account
**Descrição:** Delete a CRM account.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `account_id` | string | path | ✅ | The ID of the account to delete |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/crm/crm/accounts/{account_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/crm/crm/accounts/{account_id} {#get-apimodulescrmcrmaccountsaccount-id}

**Resumo:** Get Account
**Descrição:** Get a CRM account by ID.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `account_id` | string | path | ✅ | The ID of the account to retrieve |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [AccountRead](#accountread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/accounts/{account_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/crm/crm/accounts/{account_id} {#put-apimodulescrmcrmaccountsaccount-id}

**Resumo:** Update Account
**Descrição:** Update a CRM account.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `account_id` | string | path | ✅ | The ID of the account to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [AccountUpdate](#accountupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [AccountRead](#accountread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/crm/crm/accounts/{account_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
