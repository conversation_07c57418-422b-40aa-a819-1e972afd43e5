import uuid
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from typing import List, Optional
from app.modules.core.eshop.models.eshop_category import eshopCategory
from app.modules.core.eshop.schemas.eshop_category import eshopCategoryCreate, eshopCategoryUpdate
from app.core.enums import MarketType

class CategoryService:
    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session

    async def create_category(self, category_in: eshopCategoryCreate) -> eshopCategory:
        category = eshopCategory(**category_in.dict())
        self.db_session.add(category)
        await self.db_session.commit()
        await self.db_session.refresh(category)
        return category

    async def get_category(self, category_id: uuid.UUID) -> eshopCategory | None:
        result = await self.db_session.execute(
            select(eshopCategory).filter(eshopCategory.id == category_id)
        )
        return result.scalars().first()

    async def get_all_categories(self, tenant_id: str, market_type: Optional[MarketType] = None) -> List[eshopCategory]:
        """Retrieve all categories for a tenant, optionally filtered by market type"""
        query = select(eshopCategory).filter(eshopCategory.tenant_id == tenant_id)
        
        if market_type and market_type != MarketType.ALL:
            query = query.filter(eshopCategory.market_type == market_type)
            
        query = query.options(selectinload(eshopCategory.children))
        result = await self.db_session.execute(query)
        return result.scalars().all()

    async def get_all_universal_categories(self, market_type: Optional[MarketType] = None) -> List[eshopCategory]:
        """Retrieve all universal categories, optionally filtered by market type
        
        When filtering by market_type, also includes parent categories needed
        to maintain hierarchy, even if they have different market_type.
        """
        if not market_type or market_type == MarketType.ALL:
            # Return all categories if no filter or ALL
            query = select(eshopCategory)
            result = await self.db_session.execute(query)
            return result.scalars().all()
        
        # Get categories of the specified market_type
        target_categories_query = select(eshopCategory).filter(
            eshopCategory.market_type == market_type
        )
        target_result = await self.db_session.execute(target_categories_query)
        target_categories = target_result.scalars().all()
        
        # Collect all parent IDs needed for hierarchy
        parent_ids = set()
        for category in target_categories:
            current_parent_id = category.parent_id
            while current_parent_id:
                parent_ids.add(current_parent_id)
                # Find the parent to get its parent_id
                parent_query = select(eshopCategory).filter(
                    eshopCategory.id == current_parent_id
                )
                parent_result = await self.db_session.execute(parent_query)
                parent = parent_result.scalar_one_or_none()
                current_parent_id = parent.parent_id if parent else None
        
        # Get all needed parent categories
        if parent_ids:
            parents_query = select(eshopCategory).filter(
                eshopCategory.id.in_(parent_ids)
            )
            parents_result = await self.db_session.execute(parents_query)
            parent_categories = parents_result.scalars().all()
        else:
            parent_categories = []
        
        # Combine target categories and their parents
        all_categories = list(target_categories) + list(parent_categories)
        
        # Remove duplicates based on ID
        seen_ids = set()
        unique_categories = []
        for category in all_categories:
            if category.id not in seen_ids:
                seen_ids.add(category.id)
                unique_categories.append(category)
        
        return unique_categories

    async def update_category(
        self, category_id: uuid.UUID, category_in: eshopCategoryUpdate
    ) -> eshopCategory | None:
        category = await self.get_category(category_id)
        if category:
            update_data = category_in.dict(exclude_unset=True)
            for key, value in update_data.items():
                setattr(category, key, value)
            await self.db_session.commit()
            await self.db_session.refresh(category)
        return category

    async def delete_category(self, category_id: uuid.UUID) -> bool:
        category = await self.get_category(category_id)
        if category:
            await self.db_session.delete(category)
            await self.db_session.commit()
            return True
        return False