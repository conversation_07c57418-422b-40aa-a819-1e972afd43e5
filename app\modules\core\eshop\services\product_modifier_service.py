import logging
from typing import Optional, Sequence, List
from sqlalchemy import select, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.exc import IntegrityError
from fastapi import HTTPException, status
import uuid

# Import models
from app.modules.core.eshop.models.product_modifier_group import ProductModifierGroup
from app.modules.core.eshop.models.product_modifier_option import ProductModifierOption

# Import schemas
from app.modules.core.eshop.schemas.product_modifier_group import (
    ProductModifierGroupCreate,
    ProductModifierGroupUpdate,
    ProductModifierGroupResponse,
)
from app.modules.core.eshop.schemas.product_modifier_option import (
    ProductModifierOptionCreate,
    ProductModifierOptionUpdate,
    ProductModifierOptionResponse,
)

# Import WebSocket utility
from app.websockets.manager import emit_to_tenant

logger = logging.getLogger(__name__)


class ProductModifierService:
    def __init__(self, db_session: AsyncSession):
        self.db = db_session

    # Modifier Group Methods
    async def create_modifier_group(
        self, 
        group_in: ProductModifierGroupCreate,
        current_user_id: uuid.UUID
    ) -> ProductModifierGroup:
        """Creates a new product modifier group."""
        logger.info(f"Creating product modifier group: {group_in.name}")

        try:
            group_data = group_in.model_dump()
            db_group = ProductModifierGroup(**group_data)
            self.db.add(db_group)
            await self.db.commit()
            await self.db.refresh(db_group)

            # Emit WebSocket notification
            if group_in.tenant_id:
                await emit_to_tenant(
                    group_in.tenant_id,
                    "modifier_group_created",
                    {"group_id": str(db_group.id), "group_name": db_group.name}
                )

            logger.info(f"Product modifier group created: {db_group.id}")
            return db_group

        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"IntegrityError creating modifier group: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Error creating modifier group. Database constraint violation: {str(e)}",
            )
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Unexpected error creating modifier group: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal server error: {str(e)}",
            )

    async def get_modifier_group(
        self, 
        group_id: uuid.UUID, 
        tenant_id: Optional[uuid.UUID] = None,
        include_options: bool = False
    ) -> Optional[ProductModifierGroup]:
        """Gets a specific product modifier group by ID."""
        query = select(ProductModifierGroup).where(ProductModifierGroup.id == group_id)
        
        if tenant_id:
            query = query.where(
                or_(ProductModifierGroup.tenant_id == tenant_id, ProductModifierGroup.tenant_id.is_(None))
            )

        if include_options:
            query = query.options(selectinload(ProductModifierGroup.options))

        result = await self.db.execute(query)
        group = result.scalars().first()

        if not group:
            logger.warning(f"Modifier group {group_id} not found")
            return None

        return group

    async def get_modifier_groups(
        self,
        tenant_id: Optional[uuid.UUID] = None,
        is_active: Optional[bool] = None,
        is_template: Optional[bool] = None,
        include_options: bool = False,
        skip: int = 0,
        limit: int = 100,
    ) -> Sequence[ProductModifierGroup]:
        """Gets a list of product modifier groups with filtering options."""
        query = select(ProductModifierGroup)

        # Apply filters
        filters = []
        
        if tenant_id:
            filters.append(
                or_(ProductModifierGroup.tenant_id == tenant_id, ProductModifierGroup.tenant_id.is_(None))
            )
            
        if is_active is not None:
            filters.append(ProductModifierGroup.is_active == is_active)
            
        if is_template is not None:
            filters.append(ProductModifierGroup.is_template == is_template)

        if filters:
            query = query.where(and_(*filters))

        # Apply ordering and pagination
        query = query.order_by(ProductModifierGroup.display_order, ProductModifierGroup.name)
        query = query.offset(skip).limit(limit)

        # Load options if requested
        if include_options:
            query = query.options(selectinload(ProductModifierGroup.options))

        result = await self.db.execute(query)
        groups = result.scalars().all()

        return groups

    async def update_modifier_group(
        self, 
        group_id: uuid.UUID, 
        group_in: ProductModifierGroupUpdate,
        current_user_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID] = None
    ) -> Optional[ProductModifierGroup]:
        """Updates an existing product modifier group."""
        db_group = await self.get_modifier_group(group_id, tenant_id)
        if not db_group:
            return None

        try:
            update_data = group_in.model_dump(exclude_unset=True)
            
            # Apply updates
            for field, value in update_data.items():
                setattr(db_group, field, value)

            await self.db.commit()
            await self.db.refresh(db_group)

            # Emit WebSocket notification
            if db_group.tenant_id:
                await emit_to_tenant(
                    db_group.tenant_id,
                    "modifier_group_updated",
                    {"group_id": str(db_group.id), "group_name": db_group.name}
                )

            logger.info(f"Product modifier group updated: {db_group.id}")
            return db_group

        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"IntegrityError updating modifier group: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Error updating modifier group. Database constraint violation: {str(e)}",
            )
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Unexpected error updating modifier group: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal server error: {str(e)}",
            )

    async def delete_modifier_group(
        self, 
        group_id: uuid.UUID,
        current_user_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID] = None
    ) -> bool:
        """Deletes a product modifier group."""
        db_group = await self.get_modifier_group(group_id, tenant_id, include_options=True)
        if not db_group:
            return False

        try:
            await self.db.delete(db_group)
            await self.db.commit()

            # Emit WebSocket notification
            if db_group.tenant_id:
                await emit_to_tenant(
                    db_group.tenant_id,
                    "modifier_group_deleted",
                    {"group_id": str(group_id), "group_name": db_group.name}
                )

            logger.info(f"Product modifier group deleted: {group_id}")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Error deleting modifier group: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal server error: {str(e)}",
            )

    # Modifier Option Methods
    async def create_modifier_option(
        self, 
        option_in: ProductModifierOptionCreate,
        current_user_id: uuid.UUID
    ) -> ProductModifierOption:
        """Creates a new product modifier option."""
        logger.info(f"Creating product modifier option: {option_in.name}")

        # Check if modifier group exists
        group = await self.get_modifier_group(option_in.modifier_group_id, option_in.tenant_id)
        if not group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Modifier group not found.",
            )

        try:
            option_data = option_in.model_dump()
            db_option = ProductModifierOption(**option_data)
            self.db.add(db_option)
            await self.db.commit()
            await self.db.refresh(db_option)

            # Emit WebSocket notification
            if option_in.tenant_id:
                await emit_to_tenant(
                    option_in.tenant_id,
                    "modifier_option_created",
                    {"option_id": str(db_option.id), "option_name": db_option.name}
                )

            logger.info(f"Product modifier option created: {db_option.id}")
            return db_option

        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"IntegrityError creating modifier option: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Error creating modifier option. Database constraint violation: {str(e)}",
            )
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Unexpected error creating modifier option: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal server error: {str(e)}",
            )

    async def get_modifier_option(
        self, 
        option_id: uuid.UUID, 
        tenant_id: Optional[uuid.UUID] = None
    ) -> Optional[ProductModifierOption]:
        """Gets a specific product modifier option by ID."""
        query = select(ProductModifierOption).where(ProductModifierOption.id == option_id)
        
        if tenant_id:
            query = query.where(
                or_(ProductModifierOption.tenant_id == tenant_id, ProductModifierOption.tenant_id.is_(None))
            )

        result = await self.db.execute(query)
        option = result.scalars().first()

        if not option:
            logger.warning(f"Modifier option {option_id} not found")
            return None

        return option

    async def get_modifier_options(
        self,
        modifier_group_id: Optional[uuid.UUID] = None,
        tenant_id: Optional[uuid.UUID] = None,
        is_active: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> Sequence[ProductModifierOption]:
        """Gets a list of product modifier options with filtering options."""
        query = select(ProductModifierOption)

        # Apply filters
        filters = []
        
        if modifier_group_id:
            filters.append(ProductModifierOption.modifier_group_id == modifier_group_id)
        
        if tenant_id:
            filters.append(
                or_(ProductModifierOption.tenant_id == tenant_id, ProductModifierOption.tenant_id.is_(None))
            )
            
        if is_active is not None:
            filters.append(ProductModifierOption.is_active == is_active)

        if filters:
            query = query.where(and_(*filters))

        # Apply ordering and pagination
        query = query.order_by(ProductModifierOption.display_order, ProductModifierOption.name)
        query = query.offset(skip).limit(limit)

        result = await self.db.execute(query)
        options = result.scalars().all()

        return options

    async def update_modifier_option(
        self, 
        option_id: uuid.UUID, 
        option_in: ProductModifierOptionUpdate,
        current_user_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID] = None
    ) -> Optional[ProductModifierOption]:
        """Updates an existing product modifier option."""
        db_option = await self.get_modifier_option(option_id, tenant_id)
        if not db_option:
            return None

        try:
            update_data = option_in.model_dump(exclude_unset=True)
            
            # Apply updates
            for field, value in update_data.items():
                setattr(db_option, field, value)

            await self.db.commit()
            await self.db.refresh(db_option)

            # Emit WebSocket notification
            if db_option.tenant_id:
                await emit_to_tenant(
                    db_option.tenant_id,
                    "modifier_option_updated",
                    {"option_id": str(db_option.id), "option_name": db_option.name}
                )

            logger.info(f"Product modifier option updated: {db_option.id}")
            return db_option

        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"IntegrityError updating modifier option: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Error updating modifier option. Database constraint violation: {str(e)}",
            )
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Unexpected error updating modifier option: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal server error: {str(e)}",
            )

    async def delete_modifier_option(
        self, 
        option_id: uuid.UUID,
        current_user_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID] = None
    ) -> bool:
        """Deletes a product modifier option."""
        db_option = await self.get_modifier_option(option_id, tenant_id)
        if not db_option:
            return False

        try:
            await self.db.delete(db_option)
            await self.db.commit()

            # Emit WebSocket notification
            if db_option.tenant_id:
                await emit_to_tenant(
                    db_option.tenant_id,
                    "modifier_option_deleted",
                    {"option_id": str(option_id), "option_name": db_option.name}
                )

            logger.info(f"Product modifier option deleted: {option_id}")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Error deleting modifier option: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal server error: {str(e)}",
            ) 