"""
Translation suggestion schemas for the i18n module.
"""

import uuid
from pydantic import BaseModel, Field, ConfigDict
from typing import Optional


class TranslationSuggestionBase(BaseModel):
    """Base schema for TranslationSuggestion."""

    key: str = Field(..., min_length=1, max_length=255, description="Translation key")
    suggested_text: str = Field(..., description="Suggested translation text")
    language_id: uuid.UUID = Field(..., description="ID of the language")


class TranslationSuggestionCreate(TranslationSuggestionBase):
    """Schema for creating a new TranslationSuggestion."""

    pass


class TranslationSuggestionUpdate(BaseModel):
    """Schema for updating an existing TranslationSuggestion."""

    key: Optional[str] = Field(None, min_length=1, max_length=255)
    suggested_text: Optional[str] = None
    language_id: Optional[uuid.UUID] = None
    status: Optional[str] = Field(
        None, description="Status of the suggestion (pending, approved, rejected)"
    )


class TranslationSuggestionRead(TranslationSuggestionBase):
    """Schema for reading a TranslationSuggestion."""

    id: uuid.UUID
    user_id: uuid.UUID
    status: str

    model_config = ConfigDict(from_attributes=True)
