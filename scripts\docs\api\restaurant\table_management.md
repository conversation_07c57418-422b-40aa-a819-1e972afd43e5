# Restaurant - Table Management

**Categoria:** Restaurant
**Módulo:** Table Management
**Total de Endpoints:** 12
**Gerado em:** 27/06/2025, 20:20:27

## 📋 Endpoints

- [GET /api/modules/restaurants/tables/layouts/](#get-apimodulesrestaurantstableslayouts) - Get all table layouts
- [POST /api/modules/restaurants/tables/layouts/](#post-apimodulesrestaurantstableslayouts) - Create a new table layout
- [DELETE /api/modules/restaurants/tables/layouts/{layout_id}](#delete-apimodulesrestaurantstableslayoutslayout-id) - Delete a table layout
- [GET /api/modules/restaurants/tables/layouts/{layout_id}](#get-apimodulesrestaurantstableslayoutslayout-id) - Get a table layout by ID
- [PUT /api/modules/restaurants/tables/layouts/{layout_id}](#put-apimodulesrestaurantstableslayoutslayout-id) - Update a table layout
- [GET /api/modules/restaurants/tables/tables/](#get-apimodulesrestaurantstablestables) - Get all tables
- [POST /api/modules/restaurants/tables/tables/](#post-apimodulesrestaurantstablestables) - Create a new table
- [GET /api/modules/restaurants/tables/tables/available](#get-apimodulesrestaurantstablestablesavailable) - Get available tables
- [DELETE /api/modules/restaurants/tables/tables/{table_id}](#delete-apimodulesrestaurantstablestablestable-id) - Delete a table
- [GET /api/modules/restaurants/tables/tables/{table_id}](#get-apimodulesrestaurantstablestablestable-id) - Get a table by ID
- [PUT /api/modules/restaurants/tables/tables/{table_id}](#put-apimodulesrestaurantstablestablestable-id) - Update a table
- [PATCH /api/modules/restaurants/tables/tables/{table_id}/status](#patch-apimodulesrestaurantstablestablestable-idstatus) - Update table status

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### TableCreate

**Descrição:** Schema for creating a new table.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `table_number` | string | ✅ | Table number or identifier |
| `name` | unknown | ❌ | Optional display name for the table |
| `capacity` | integer | ✅ | Number of people the table can accommodate |
| `zone` | unknown | ❌ | Zone where the table is located |
| `status` | TableStatus | ❌ | Current status of the table |
| `position_x` | unknown | ❌ | X coordinate in the layout |
| `position_y` | unknown | ❌ | Y coordinate in the layout |
| `width` | unknown | ❌ | Width of the table in the layout |
| `height` | unknown | ❌ | Height of the table in the layout |
| `shape` | unknown | ❌ | Shape of the table (rectangle, circle, custom) |
| `custom_shape_data` | unknown | ❌ | Data for custom shapes |
| `is_active` | boolean | ❌ | Whether the table is active |
| `notes` | unknown | ❌ | Additional notes about the table |
| `qrcode_enabled` | boolean | ❌ | Whether the QR code is enabled for this table |
| `layout_id` | unknown | ❌ | ID of the layout this table belongs to |

### TableLayoutCreate

**Descrição:** Schema for creating a new table layout.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Name of the layout |
| `description` | unknown | ❌ | Description of the layout |
| `floor_number` | unknown | ❌ | Floor number |
| `width` | unknown | ❌ | Width of the layout |
| `height` | unknown | ❌ | Height of the layout |
| `background_image_url` | unknown | ❌ | URL to background image |
| `is_active` | boolean | ❌ | Whether the layout is active |
| `layout_data` | unknown | ❌ | Additional layout information |

### TableLayoutRead

**Descrição:** Schema for reading a table layout.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Name of the layout |
| `description` | unknown | ❌ | Description of the layout |
| `floor_number` | unknown | ❌ | Floor number |
| `width` | unknown | ❌ | Width of the layout |
| `height` | unknown | ❌ | Height of the layout |
| `background_image_url` | unknown | ❌ | URL to background image |
| `is_active` | boolean | ❌ | Whether the layout is active |
| `layout_data` | unknown | ❌ | Additional layout information |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `tables` | unknown | ❌ | - |

### TableLayoutUpdate

**Descrição:** Schema for updating an existing table layout.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | Name of the layout |
| `description` | unknown | ❌ | Description of the layout |
| `floor_number` | unknown | ❌ | Floor number |
| `width` | unknown | ❌ | Width of the layout |
| `height` | unknown | ❌ | Height of the layout |
| `background_image_url` | unknown | ❌ | URL to background image |
| `is_active` | unknown | ❌ | Whether the layout is active |
| `layout_data` | unknown | ❌ | Additional layout information |

### TableRead

**Descrição:** Schema for reading a table.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `table_number` | string | ✅ | Table number or identifier |
| `name` | unknown | ❌ | Optional display name for the table |
| `capacity` | integer | ✅ | Number of people the table can accommodate |
| `zone` | unknown | ❌ | Zone where the table is located |
| `status` | TableStatus | ❌ | Current status of the table |
| `position_x` | unknown | ❌ | X coordinate in the layout |
| `position_y` | unknown | ❌ | Y coordinate in the layout |
| `width` | unknown | ❌ | Width of the table in the layout |
| `height` | unknown | ❌ | Height of the table in the layout |
| `shape` | unknown | ❌ | Shape of the table (rectangle, circle, custom) |
| `custom_shape_data` | unknown | ❌ | Data for custom shapes |
| `is_active` | boolean | ❌ | Whether the table is active |
| `notes` | unknown | ❌ | Additional notes about the table |
| `qrcode_enabled` | boolean | ❌ | Whether the QR code is enabled for this table |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `layout_id` | unknown | ❌ | - |
| `qrcode_id` | unknown | ❌ | Unique identifier for the QR code |
| `layout` | unknown | ❌ | - |

### TableUpdate

**Descrição:** Schema for updating an existing table.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `table_number` | unknown | ❌ | Table number or identifier |
| `name` | unknown | ❌ | Optional display name for the table |
| `capacity` | unknown | ❌ | Number of people the table can accommodate |
| `zone` | unknown | ❌ | Zone where the table is located |
| `status` | unknown | ❌ | Current status of the table |
| `layout_id` | unknown | ❌ | ID of the layout this table belongs to |
| `position_x` | unknown | ❌ | X coordinate in the layout |
| `position_y` | unknown | ❌ | Y coordinate in the layout |
| `width` | unknown | ❌ | Width of the table in the layout |
| `height` | unknown | ❌ | Height of the table in the layout |
| `shape` | unknown | ❌ | Shape of the table (rectangle, circle, custom) |
| `custom_shape_data` | unknown | ❌ | Data for custom shapes |
| `is_active` | unknown | ❌ | Whether the table is active |
| `notes` | unknown | ❌ | Additional notes about the table |
| `qrcode_enabled` | unknown | ❌ | Whether the QR code is enabled for this table |

## 🔗 Endpoints Detalhados

### GET /api/modules/restaurants/tables/layouts/ {#get-apimodulesrestaurantstableslayouts}

**Resumo:** Get all table layouts
**Descrição:** Get all table layouts for the current tenant with optional filters.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `is_active` | string | query | ❌ | Filter by active status |
| `include_tables` | boolean | query | ❌ | Include tables in the response |
| `skip` | integer | query | ❌ | Number of layouts to skip |
| `limit` | integer | query | ❌ | Maximum number of layouts to return |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/tables/layouts/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/restaurants/tables/layouts/ {#post-apimodulesrestaurantstableslayouts}

**Resumo:** Create a new table layout
**Descrição:** Create a new table layout for the current tenant.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TableLayoutCreate](#tablelayoutcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TableLayoutRead](#tablelayoutread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/restaurants/tables/layouts/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/restaurants/tables/layouts/{layout_id} {#delete-apimodulesrestaurantstableslayoutslayout-id}

**Resumo:** Delete a table layout
**Descrição:** Delete a table layout for the current tenant.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `layout_id` | string | path | ✅ | The ID of the layout to delete |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/restaurants/tables/layouts/{layout_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/tables/layouts/{layout_id} {#get-apimodulesrestaurantstableslayoutslayout-id}

**Resumo:** Get a table layout by ID
**Descrição:** Get a table layout by ID for the current tenant.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `layout_id` | string | path | ✅ | The ID of the layout to get |
| `include_tables` | boolean | query | ❌ | Include tables in the response |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TableLayoutRead](#tablelayoutread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/tables/layouts/{layout_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/restaurants/tables/layouts/{layout_id} {#put-apimodulesrestaurantstableslayoutslayout-id}

**Resumo:** Update a table layout
**Descrição:** Update a table layout for the current tenant.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `layout_id` | string | path | ✅ | The ID of the layout to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TableLayoutUpdate](#tablelayoutupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TableLayoutRead](#tablelayoutread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/tables/layouts/{layout_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/restaurants/tables/tables/ {#get-apimodulesrestaurantstablestables}

**Resumo:** Get all tables
**Descrição:** Get all tables for the current tenant with optional filters.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `layout_id` | string | query | ❌ | Filter by layout ID |
| `status` | string | query | ❌ | Filter by table status |
| `capacity_min` | string | query | ❌ | Minimum capacity |
| `capacity_max` | string | query | ❌ | Maximum capacity |
| `is_active` | string | query | ❌ | Filter by active status |
| `zone` | string | query | ❌ | Filter by zone |
| `skip` | integer | query | ❌ | Number of tables to skip |
| `limit` | integer | query | ❌ | Maximum number of tables to return |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/tables/tables/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/restaurants/tables/tables/ {#post-apimodulesrestaurantstablestables}

**Resumo:** Create a new table
**Descrição:** Create a new table for the current tenant.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TableCreate](#tablecreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TableRead](#tableread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/restaurants/tables/tables/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/restaurants/tables/tables/available {#get-apimodulesrestaurantstablestablesavailable}

**Resumo:** Get available tables
**Descrição:** Get all available tables for the current tenant.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `capacity` | string | query | ❌ | Minimum capacity required |
| `layout_id` | string | query | ❌ | Filter by layout ID |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/tables/tables/available" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### DELETE /api/modules/restaurants/tables/tables/{table_id} {#delete-apimodulesrestaurantstablestablestable-id}

**Resumo:** Delete a table
**Descrição:** Delete a table for the current tenant.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `table_id` | string | path | ✅ | The ID of the table to delete |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/restaurants/tables/tables/{table_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/tables/tables/{table_id} {#get-apimodulesrestaurantstablestablestable-id}

**Resumo:** Get a table by ID
**Descrição:** Get a table by ID for the current tenant.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `table_id` | string | path | ✅ | The ID of the table to get |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TableRead](#tableread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/tables/tables/{table_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/restaurants/tables/tables/{table_id} {#put-apimodulesrestaurantstablestablestable-id}

**Resumo:** Update a table
**Descrição:** Update a table for the current tenant.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `table_id` | string | path | ✅ | The ID of the table to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TableUpdate](#tableupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TableRead](#tableread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/tables/tables/{table_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PATCH /api/modules/restaurants/tables/tables/{table_id}/status {#patch-apimodulesrestaurantstablestablestable-idstatus}

**Resumo:** Update table status
**Descrição:** Update the status of a table for the current tenant.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `table_id` | string | path | ✅ | The ID of the table to update |
| `status` | string | query | ✅ | The new status for the table |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TableRead](#tableread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PATCH "http://localhost:8000/api/modules/restaurants/tables/tables/{table_id}/status" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
