"""
Media System Schemas

Schemas Pydantic para validação de dados do sistema de mídia.
"""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, validator

from .models import MediaUploadStatus, MediaFileType, MediaContextType


# Media Context Schemas
class MediaContextBase(BaseModel):
    """Schema base para contexto de mídia."""
    context_type: MediaContextType = Field(..., description="Tipo de contexto")
    context_id: UUID = Field(..., description="ID do contexto (user ou tenant)")
    quota_limit_mb: int = Field(2048, ge=0, description="Limite de quota em MB")
    is_quota_enabled: bool = Field(True, description="Se a quota está habilitada")


class MediaContextCreate(MediaContextBase):
    """Schema para criação de contexto de mídia."""
    pass


class MediaContextUpdate(BaseModel):
    """Schema para atualização de contexto de mídia."""
    quota_limit_mb: Optional[int] = Field(None, ge=0)
    is_quota_enabled: Optional[bool] = None


class MediaContextRead(MediaContextBase):
    """Schema para leitura de contexto de mídia."""
    id: UUID
    used_space_mb: int
    created_at: datetime
    updated_at: Optional[datetime]
    
    # Campos calculados
    quota_usage_percentage: Optional[float] = None
    available_space_mb: Optional[int] = None
    is_quota_exceeded: Optional[bool] = None

    class Config:
        from_attributes = True


# Media Directory Schemas
class MediaDirectoryBase(BaseModel):
    """Schema base para diretório de mídia."""
    name: str = Field(..., min_length=1, max_length=100)
    path: str = Field(..., max_length=500)
    parent_id: Optional[UUID] = None


class MediaDirectoryCreate(MediaDirectoryBase):
    """Schema para criação de diretório de mídia."""
    context_id: UUID = Field(..., description="ID do contexto")


class MediaDirectoryRead(MediaDirectoryBase):
    """Schema para leitura de diretório de mídia."""
    id: UUID
    context_id: UUID
    created_at: datetime
    children: List['MediaDirectoryRead'] = []

    class Config:
        from_attributes = True


# Media Upload Schemas
class MediaUploadBase(BaseModel):
    """Schema base para upload de mídia."""
    filename: str = Field(..., max_length=255)
    original_filename: str = Field(..., max_length=255)
    file_path: str = Field(..., max_length=500)
    file_size: int = Field(..., gt=0)
    mime_type: str = Field(..., max_length=100)
    media_type: MediaFileType = Field(...)
    directory_id: Optional[UUID] = None


class MediaUploadCreate(MediaUploadBase):
    """Schema para criação de upload de mídia."""
    context_id: UUID = Field(..., description="ID do contexto")
    uploaded_by: UUID = Field(..., description="ID do usuário que fez upload")
    file_metadata: Optional[str] = Field(None, description="Metadados em JSON")


class MediaUploadUpdate(BaseModel):
    """Schema para atualização de upload de mídia."""
    upload_status: Optional[MediaUploadStatus] = None
    thumbnail_path: Optional[str] = Field(None, max_length=500)
    compressed_path: Optional[str] = Field(None, max_length=500)
    file_metadata: Optional[str] = None


class MediaUploadRead(MediaUploadBase):
    """Schema para leitura de upload de mídia."""
    id: UUID
    context_id: UUID
    uploaded_by: UUID
    upload_status: MediaUploadStatus
    thumbnail_path: Optional[str]
    compressed_path: Optional[str]
    file_metadata: Optional[str]
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


# Menu Item Media Schemas
class MenuItemMediaBase(BaseModel):
    """Schema base para mídia de item de menu."""
    menu_item_id: UUID = Field(..., description="ID do item de menu")
    media_upload_id: UUID = Field(..., description="ID do upload de mídia")
    display_order: int = Field(0, ge=0, description="Ordem de exibição")
    is_primary: bool = Field(False, description="Se é a imagem principal")
    alt_text: Optional[str] = Field(None, max_length=255)


class MenuItemMediaCreate(MenuItemMediaBase):
    """Schema para criação de mídia de item de menu."""
    pass


class MenuItemMediaUpdate(BaseModel):
    """Schema para atualização de mídia de item de menu."""
    display_order: Optional[int] = Field(None, ge=0)
    is_primary: Optional[bool] = None
    alt_text: Optional[str] = Field(None, max_length=255)


class MenuItemMediaRead(MenuItemMediaBase):
    """Schema para leitura de mídia de item de menu."""
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]
    upload: MediaUploadRead

    class Config:
        from_attributes = True


# Request/Response Schemas
class MediaContextRequest(BaseModel):
    """Schema para requisições com contexto."""
    context_type: MediaContextType = Field(..., description="Tipo de contexto")
    context_id: Optional[UUID] = Field(None, description="ID do contexto")

    @validator('context_id')
    def validate_context_id(cls, v, values):
        """Valida se context_id é obrigatório para TENANT."""
        context_type = values.get('context_type')
        if context_type == MediaContextType.TENANT and not v:
            raise ValueError('context_id é obrigatório para contexto TENANT')
        return v


class MediaContextResponse(BaseModel):
    """Schema de resposta com informações de contexto."""
    context_type: MediaContextType
    context_id: UUID
    storage_path: str
    available_space_mb: Optional[int] = None


class MediaUploadResponse(BaseModel):
    """Schema de resposta para upload."""
    success: bool
    message: str
    upload_id: Optional[UUID] = None
    file_url: Optional[str] = None
    thumbnail_url: Optional[str] = None


class MediaStatsResponse(BaseModel):
    """Schema de resposta para estatísticas de mídia."""
    total_uploads: int
    total_size: int
    active_uploads: int
    failed_uploads: int
    by_media_type: dict


class MediaQuotaResponse(BaseModel):
    """Schema de resposta para informações de quota."""
    context_id: UUID
    context_type: MediaContextType
    quota_limit_mb: int
    used_space_mb: int
    available_space_mb: int
    quota_usage_percentage: float
    is_quota_enabled: bool
    is_quota_exceeded: bool


class MediaQuotaCheckResponse(BaseModel):
    """Schema de resposta para verificação de quota."""
    can_upload: bool
    reason: Optional[str] = None
    quota_limit_mb: Optional[int] = None
    used_space_mb: Optional[int] = None
    available_space_mb: Optional[int] = None
    file_size_mb: Optional[int] = None


# Update forward references
MediaDirectoryRead.model_rebuild()
