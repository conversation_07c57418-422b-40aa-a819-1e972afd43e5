"""
Message Service

Serviço para gerenciamento de mensagens de tickets.
"""

import logging
from typing import List, Optional
from uuid import UUID

from sqlalchemy import desc, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from sqlalchemy.future import select

from app.modules.core.users.models.user import User

from ..models import Ticket, TicketMessage, MessageType
from ..schemas import (
    TicketMessageCreate, TicketMessageResponse, TicketMessageListResponse,
    MessageMarkAsRead
)

logger = logging.getLogger(__name__)


class MessageService:
    """Serviço para gerenciamento de mensagens."""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def create_message(
        self,
        message_data: TicketMessageCreate,
        sender: User
    ) -> Optional[TicketMessage]:
        """
        Cria uma nova mensagem.
        
        Args:
            message_data: Dados da mensagem
            sender: Usuário que enviou a mensagem
            
        Returns:
            Mensagem criada ou None se ticket não encontrado/sem acesso
        """
        # Verifica se o ticket existe e se o usuário tem acesso
        ticket_stmt = select(Ticket).where(Ticket.id == message_data.ticket_id)
        ticket_result = await self.db.execute(ticket_stmt)
        ticket = ticket_result.scalar_one_or_none()
        
        if not ticket:
            return None
        
        # Verifica acesso ao ticket
        if not self._user_can_access_ticket(ticket, sender):
            return None
        
        message = TicketMessage(
            ticket_id=message_data.ticket_id,
            sender_id=sender.id,
            message_content=message_data.message_content,
            message_type=message_data.message_type,
            file_path=message_data.file_path,
            file_name=message_data.file_name,
            file_size=message_data.file_size,
            mime_type=message_data.mime_type,
            is_read=False
        )
        
        self.db.add(message)
        
        # Atualiza status de leitura do ticket
        if sender.system_role == "admin":
            ticket.is_read_by_user = False  # Admin respondeu, usuário precisa ler
        else:
            ticket.is_read_by_admin = False  # Usuário respondeu, admin precisa ler
        
        await self.db.commit()
        await self.db.refresh(message)

        # Enviar notificação em background
        from ..tasks.help_center_tasks import process_ticket_notification
        message_data = {
            "ticket_id": str(ticket.id),
            "sender_id": str(sender.id),
            "message": {
                "id": str(message.id),
                "content": message.message_content,
                "type": message.message_type.value,
                "created_at": message.created_at.isoformat()
            }
        }
        process_ticket_notification.delay(message_data, "message_sent")

        logger.info(f"Mensagem criada: {message.id} no ticket {ticket.id} por {sender.id}")
        return message

    async def get_message(
        self,
        message_id: UUID,
        user: User
    ) -> Optional[TicketMessage]:
        """
        Obtém uma mensagem específica.
        
        Args:
            message_id: ID da mensagem
            user: Usuário solicitante
            
        Returns:
            Mensagem ou None se não encontrada/sem acesso
        """
        stmt = select(TicketMessage).options(
            selectinload(TicketMessage.sender),
            selectinload(TicketMessage.ticket)
        ).where(TicketMessage.id == message_id)
        
        result = await self.db.execute(stmt)
        message = result.scalar_one_or_none()

        if not message:
            return None
        
        # Verifica acesso
        if not self._user_can_access_ticket(message.ticket, user):
            return None
        
        return message

    async def list_messages(
        self,
        ticket_id: UUID,
        user: User,
        page: int = 1,
        per_page: int = 50
    ) -> Optional[TicketMessageListResponse]:
        """
        Lista mensagens de um ticket.
        
        Args:
            ticket_id: ID do ticket
            user: Usuário solicitante
            page: Página
            per_page: Itens por página
            
        Returns:
            Lista de mensagens paginada ou None se sem acesso
        """
        # Verifica acesso ao ticket
        ticket_stmt = select(Ticket).where(Ticket.id == ticket_id)
        ticket_result = await self.db.execute(ticket_stmt)
        ticket = ticket_result.scalar_one_or_none()
        
        if not ticket or not self._user_can_access_ticket(ticket, user):
            return None
        
        stmt = select(TicketMessage).options(
            selectinload(TicketMessage.sender)
        ).where(TicketMessage.ticket_id == ticket_id)
        
        # Ordenação (mais antigas primeiro para chat)
        stmt = stmt.order_by(TicketMessage.created_at)
        
        # Contagem total
        count_stmt = select(func.count()).select_from(
            select(TicketMessage).where(TicketMessage.ticket_id == ticket_id).subquery()
        )
        total_result = await self.db.execute(count_stmt)
        total = total_result.scalar()
        
        # Paginação
        offset = (page - 1) * per_page
        stmt = stmt.offset(offset).limit(per_page)
        
        result = await self.db.execute(stmt)
        messages = result.scalars().all()
        
        # Converter para response
        message_responses = []
        for message in messages:
            message_response = TicketMessageResponse(
                id=message.id,
                ticket_id=message.ticket_id,
                sender_id=message.sender_id,
                message_content=message.message_content,
                message_type=message.message_type,
                file_path=message.file_path,
                file_name=message.file_name,
                file_size=message.file_size,
                mime_type=message.mime_type,
                is_read=message.is_read,
                created_at=message.created_at,
                expires_at=message.expires_at,
                is_expired=message.is_expired,
                sender_name=message.sender.full_name if message.sender else None,
                sender_email=message.sender.email if message.sender else None,
                is_admin=message.sender.system_role == "admin" if message.sender else False
            )
            message_responses.append(message_response)
        
        return TicketMessageListResponse(
            messages=message_responses,
            total=total,
            page=page,
            per_page=per_page,
            has_next=offset + per_page < total,
            has_prev=page > 1
        )

    async def mark_messages_as_read(
        self,
        mark_data: MessageMarkAsRead,
        user: User
    ) -> List[UUID]:
        """
        Marca mensagens como lidas.
        
        Args:
            mark_data: Dados das mensagens para marcar
            user: Usuário marcando como lidas
            
        Returns:
            Lista de IDs processados
        """
        processed_messages = []
        
        for message_id in mark_data.message_ids:
            message = await self.get_message(message_id, user)
            if not message:
                continue
            
            message.mark_as_read()
            processed_messages.append(message_id)
        
        await self.db.commit()
        return processed_messages

    async def delete_message(
        self,
        message_id: UUID,
        user: User
    ) -> bool:
        """
        Deleta uma mensagem (apenas admin ou remetente).
        
        Args:
            message_id: ID da mensagem
            user: Usuário executando a operação
            
        Returns:
            True se deletada com sucesso
        """
        message = await self.get_message(message_id, user)
        if not message:
            return False
        
        # Apenas admin ou remetente podem deletar
        if user.system_role != "admin" and message.sender_id != user.id:
            return False
        
        await self.db.delete(message)
        await self.db.commit()
        
        logger.info(f"Mensagem {message_id} deletada por {user.id}")
        return True

    def _user_can_access_ticket(self, ticket: Ticket, user: User) -> bool:
        """
        Verifica se usuário pode acessar o ticket.
        
        Args:
            ticket: Ticket
            user: Usuário
            
        Returns:
            True se pode acessar
        """
        # Admin pode acessar todos
        if user.system_role == "admin":
            return True
        
        # Usuário pode acessar seus próprios tickets
        if ticket.user_id == user.id:
            return True
        
        return False
