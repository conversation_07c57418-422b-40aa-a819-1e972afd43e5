'use client';

import { useState, useEffect, useMemo } from 'react';
import { useAuth } from '@/lib/auth/AuthProvider';

interface TypingUser {
  user_id: string;
  user_name: string;
  is_typing: boolean;
}

interface TypingIndicatorProps {
  typingUsers: TypingUser[];
  className?: string;
}

export function TypingIndicator({ typingUsers, className = '' }: TypingIndicatorProps) {
  const { user } = useAuth();
  const [visibleUsers, setVisibleUsers] = useState<TypingUser[]>([]);

  useEffect(() => {
    // Filter out current user and only show users who are actually typing
    const filtered = typingUsers.filter(
      (typingUser) => typingUser.user_id !== user?.id && typingUser.is_typing
    );
    setVisibleUsers(filtered);
  }, [typingUsers, user?.id]);

  if (visibleUsers.length === 0) {
    return null;
  }

  const getTypingText = () => {
    if (visibleUsers.length === 1) {
      return `${visibleUsers[0].user_name} está digitando...`;
    } else if (visibleUsers.length === 2) {
      return `${visibleUsers[0].user_name} e ${visibleUsers[1].user_name} estão digitando...`;
    } else {
      return `${visibleUsers[0].user_name} e mais ${visibleUsers.length - 1} pessoas estão digitando...`;
    }
  };

  return (
    <div className={`flex items-center space-x-2 text-sm text-gray-500 ${className}`}>
      <div className="flex space-x-1">
        <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
        <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
        <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
      </div>
      <span className="italic">{getTypingText()}</span>
    </div>
  );
}

interface TypingIndicatorManagerProps {
  ticketId: string;
  children: React.ReactNode;
}

export function TypingIndicatorManager({ ticketId, children }: TypingIndicatorManagerProps) {
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([]);
  const typingTimeouts = useMemo(() => new Map<string, NodeJS.Timeout>(), []);

  const handleTypingUpdate = (typing: { ticket_id: string; user_id: string; user_name: string; is_typing: boolean }) => {
    if (typing.ticket_id !== ticketId) return;

    setTypingUsers(prev => {
      const filtered = prev.filter(user => user.user_id !== typing.user_id);
      
      if (typing.is_typing) {
        // Clear existing timeout for this user
        const existingTimeout = typingTimeouts.get(typing.user_id);
        if (existingTimeout) {
          clearTimeout(existingTimeout);
        }

        // Set new timeout to remove typing indicator after 3 seconds
        const timeout = setTimeout(() => {
          setTypingUsers(current => 
            current.filter(user => user.user_id !== typing.user_id)
          );
          typingTimeouts.delete(typing.user_id);
        }, 3000);

        typingTimeouts.set(typing.user_id, timeout);

        return [...filtered, {
          user_id: typing.user_id,
          user_name: typing.user_name,
          is_typing: true
        }];
      } else {
        // Clear timeout when user stops typing
        const existingTimeout = typingTimeouts.get(typing.user_id);
        if (existingTimeout) {
          clearTimeout(existingTimeout);
          typingTimeouts.delete(typing.user_id);
        }

        return filtered;
      }
    });
  };

  // Clean up timeouts on unmount
  useEffect(() => {
    return () => {
      typingTimeouts.forEach(timeout => clearTimeout(timeout));
      typingTimeouts.clear();
    };
  }, [typingTimeouts]);

  return (
    <div>
      {children}
      <TypingIndicator typingUsers={typingUsers} className="px-4 py-2" />
    </div>
  );
}
