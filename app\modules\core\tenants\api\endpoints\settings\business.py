"""
Business settings API endpoints for tenant configuration.
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    get_current_tenant_from_header as get_current_tenant,
    require_tenant_role,
)
from app.modules.core.roles.models.roles import RolePermissions
from app.core.exceptions import BusinessLogicError, NotFoundError
from app.modules.core.tenants.models.tenant import Tenant
from app.modules.core.users.models.user import User

from app.modules.core.tenants.schemas.tenant_settings import (
    TenantSettingsRead,
    BusinessSettingsUpdate,
)
from app.modules.core.tenants.services.tenant_settings import (
    tenant_settings_service,
)

router = APIRouter()


@router.put(
    "/",
    response_model=TenantSettingsRead,
    status_code=status.HTTP_200_OK,
    summary="Update Business Settings",
    description="Update business information and currency settings.",
)
async def update_business_settings(
    *,
    db: AsyncSession = Depends(get_db),
    business_data: BusinessSettingsUpdate,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role(
            required_roles=RolePermissions.ADMIN_ROLES, 
            tenant_id_source="header"
        )
    ),
) -> TenantSettingsRead:
    """
    Update business information and currency settings.
    Requires MANAGER or OWNER role.
    """
    try:
        settings = await tenant_settings_service.update_business_settings(
            db, current_tenant.id, business_data
        )
        return settings
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except BusinessLogicError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
