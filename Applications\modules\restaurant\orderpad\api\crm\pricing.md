# Crm - Pricing

**Categoria:** Crm
**Módulo:** Pricing
**Total de Endpoints:** 12
**Gerado em:** 08/06/2025, 17:26:01

## 📋 Endpoints

- [POST /api/modules/crm/crm/pricing/assignments](#post-apimodulescrmcrmpricingassignments) - Assign Pricing Tier To Customer
- [GET /api/modules/crm/crm/pricing/assignments/account/{account_id}](#get-apimodulescrmcrmpricingassignmentsaccountaccount-id) - Get Customer Pricing Assignments
- [GET /api/modules/crm/crm/pricing/assignments/{assignment_id}](#get-apimodulescrmcrmpricingassignmentsassignment-id) - Get Customer Pricing Assignment
- [PUT /api/modules/crm/crm/pricing/assignments/{assignment_id}](#put-apimodulescrmcrmpricingassignmentsassignment-id) - Update Customer Pricing Assignment
- [POST /api/modules/crm/crm/pricing/rules](#post-apimodulescrmcrmpricingrules) - Create Pricing Rule
- [GET /api/modules/crm/crm/pricing/rules/tier/{tier_id}](#get-apimodulescrmcrmpricingrulestiertier-id) - Get Pricing Rules For Tier
- [GET /api/modules/crm/crm/pricing/rules/{rule_id}](#get-apimodulescrmcrmpricingrulesrule-id) - Get Pricing Rule
- [PUT /api/modules/crm/crm/pricing/rules/{rule_id}](#put-apimodulescrmcrmpricingrulesrule-id) - Update Pricing Rule
- [GET /api/modules/crm/crm/pricing/tiers](#get-apimodulescrmcrmpricingtiers) - Get Pricing Tiers
- [POST /api/modules/crm/crm/pricing/tiers](#post-apimodulescrmcrmpricingtiers) - Create Pricing Tier
- [GET /api/modules/crm/crm/pricing/tiers/{tier_id}](#get-apimodulescrmcrmpricingtierstier-id) - Get Pricing Tier
- [PUT /api/modules/crm/crm/pricing/tiers/{tier_id}](#put-apimodulescrmcrmpricingtierstier-id) - Update Pricing Tier

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### CustomerPricingAssignmentCreate

**Descrição:** Schema for creating a new CustomerPricingAssignment.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `start_date` | string | ❌ | - |
| `end_date` | unknown | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `custom_discount_percentage` | unknown | ❌ | - |
| `notes` | unknown | ❌ | - |
| `pricing_tier_id` | string | ✅ | - |
| `account_id` | string | ✅ | - |

### CustomerPricingAssignmentRead

**Descrição:** Schema for reading a CustomerPricingAssignment.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `start_date` | string | ❌ | - |
| `end_date` | unknown | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `custom_discount_percentage` | unknown | ❌ | - |
| `notes` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `pricing_tier_id` | string | ✅ | - |
| `account_id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### CustomerPricingAssignmentUpdate

**Descrição:** Schema for updating a CustomerPricingAssignment.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `end_date` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `custom_discount_percentage` | unknown | ❌ | - |
| `notes` | unknown | ❌ | - |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### PricingRuleCreate

**Descrição:** Schema for creating a new PricingRule.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `description` | unknown | ❌ | - |
| `rule_type` | PricingRuleType | ✅ | - |
| `product_category` | unknown | ❌ | - |
| `product_id` | unknown | ❌ | - |
| `discount_percentage` | unknown | ❌ | - |
| `discount_amount` | unknown | ❌ | - |
| `custom_price` | unknown | ❌ | - |
| `min_quantity` | unknown | ❌ | - |
| `start_date` | unknown | ❌ | - |
| `end_date` | unknown | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `conditions` | unknown | ❌ | - |
| `pricing_tier_id` | string | ✅ | - |

### PricingRuleRead

**Descrição:** Schema for reading a PricingRule.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `description` | unknown | ❌ | - |
| `rule_type` | PricingRuleType | ✅ | - |
| `product_category` | unknown | ❌ | - |
| `product_id` | unknown | ❌ | - |
| `discount_percentage` | unknown | ❌ | - |
| `discount_amount` | unknown | ❌ | - |
| `custom_price` | unknown | ❌ | - |
| `min_quantity` | unknown | ❌ | - |
| `start_date` | unknown | ❌ | - |
| `end_date` | unknown | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `conditions` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `pricing_tier_id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### PricingRuleUpdate

**Descrição:** Schema for updating a PricingRule.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `rule_type` | unknown | ❌ | - |
| `product_category` | unknown | ❌ | - |
| `product_id` | unknown | ❌ | - |
| `discount_percentage` | unknown | ❌ | - |
| `discount_amount` | unknown | ❌ | - |
| `custom_price` | unknown | ❌ | - |
| `min_quantity` | unknown | ❌ | - |
| `start_date` | unknown | ❌ | - |
| `end_date` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `conditions` | unknown | ❌ | - |

### PricingTierCreate

**Descrição:** Schema for creating a new PricingTier.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `description` | unknown | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `default_discount_percentage` | number | ❌ | - |

### PricingTierRead

**Descrição:** Schema for reading a PricingTier.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `description` | unknown | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `default_discount_percentage` | number | ❌ | - |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### PricingTierUpdate

**Descrição:** Schema for updating a PricingTier.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `default_discount_percentage` | unknown | ❌ | - |

## 🔗 Endpoints Detalhados

### POST /api/modules/crm/crm/pricing/assignments {#post-apimodulescrmcrmpricingassignments}

**Resumo:** Assign Pricing Tier To Customer
**Descrição:** Assign a pricing tier to a customer.

Requires tenant owner or manager role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [CustomerPricingAssignmentCreate](#customerpricingassignmentcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CustomerPricingAssignmentRead](#customerpricingassignmentread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/crm/crm/pricing/assignments" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/crm/crm/pricing/assignments/account/{account_id} {#get-apimodulescrmcrmpricingassignmentsaccountaccount-id}

**Resumo:** Get Customer Pricing Assignments
**Descrição:** Get all pricing assignments for a specific customer.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `account_id` | string | path | ✅ | The ID of the account to get pricing assignments for |
| `skip` | integer | query | ❌ | Number of assignments to skip |
| `limit` | integer | query | ❌ | Maximum number of assignments to return |
| `is_active` | string | query | ❌ | Filter by active status |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/pricing/assignments/account/{account_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/crm/crm/pricing/assignments/{assignment_id} {#get-apimodulescrmcrmpricingassignmentsassignment-id}

**Resumo:** Get Customer Pricing Assignment
**Descrição:** Get a customer pricing assignment by ID.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `assignment_id` | string | path | ✅ | The ID of the pricing assignment to retrieve |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CustomerPricingAssignmentRead](#customerpricingassignmentread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/pricing/assignments/{assignment_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/crm/crm/pricing/assignments/{assignment_id} {#put-apimodulescrmcrmpricingassignmentsassignment-id}

**Resumo:** Update Customer Pricing Assignment
**Descrição:** Update a customer pricing assignment.

Requires tenant owner or manager role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `assignment_id` | string | path | ✅ | The ID of the pricing assignment to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [CustomerPricingAssignmentUpdate](#customerpricingassignmentupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CustomerPricingAssignmentRead](#customerpricingassignmentread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/crm/crm/pricing/assignments/{assignment_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/crm/crm/pricing/rules {#post-apimodulescrmcrmpricingrules}

**Resumo:** Create Pricing Rule
**Descrição:** Create a new pricing rule.

Requires tenant owner or manager role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [PricingRuleCreate](#pricingrulecreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PricingRuleRead](#pricingruleread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/crm/crm/pricing/rules" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/crm/crm/pricing/rules/tier/{tier_id} {#get-apimodulescrmcrmpricingrulestiertier-id}

**Resumo:** Get Pricing Rules For Tier
**Descrição:** Get all pricing rules for a specific tier.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tier_id` | string | path | ✅ | The ID of the pricing tier to get rules for |
| `skip` | integer | query | ❌ | Number of rules to skip |
| `limit` | integer | query | ❌ | Maximum number of rules to return |
| `is_active` | string | query | ❌ | Filter by active status |
| `rule_type` | string | query | ❌ | Filter by rule type |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/pricing/rules/tier/{tier_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/crm/crm/pricing/rules/{rule_id} {#get-apimodulescrmcrmpricingrulesrule-id}

**Resumo:** Get Pricing Rule
**Descrição:** Get a pricing rule by ID.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `rule_id` | string | path | ✅ | The ID of the pricing rule to retrieve |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PricingRuleRead](#pricingruleread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/pricing/rules/{rule_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/crm/crm/pricing/rules/{rule_id} {#put-apimodulescrmcrmpricingrulesrule-id}

**Resumo:** Update Pricing Rule
**Descrição:** Update a pricing rule.

Requires tenant owner or manager role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `rule_id` | string | path | ✅ | The ID of the pricing rule to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [PricingRuleUpdate](#pricingruleupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PricingRuleRead](#pricingruleread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/crm/crm/pricing/rules/{rule_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/crm/crm/pricing/tiers {#get-apimodulescrmcrmpricingtiers}

**Resumo:** Get Pricing Tiers
**Descrição:** Get all pricing tiers with optional filtering.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | Number of tiers to skip |
| `limit` | integer | query | ❌ | Maximum number of tiers to return |
| `is_active` | string | query | ❌ | Filter by active status |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/pricing/tiers" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/crm/crm/pricing/tiers {#post-apimodulescrmcrmpricingtiers}

**Resumo:** Create Pricing Tier
**Descrição:** Create a new pricing tier.

Requires tenant owner or manager role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [PricingTierCreate](#pricingtiercreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PricingTierRead](#pricingtierread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/crm/crm/pricing/tiers" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/crm/crm/pricing/tiers/{tier_id} {#get-apimodulescrmcrmpricingtierstier-id}

**Resumo:** Get Pricing Tier
**Descrição:** Get a pricing tier by ID.

Requires tenant owner, manager, or staff role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tier_id` | string | path | ✅ | The ID of the pricing tier to retrieve |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PricingTierRead](#pricingtierread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/crm/crm/pricing/tiers/{tier_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/crm/crm/pricing/tiers/{tier_id} {#put-apimodulescrmcrmpricingtierstier-id}

**Resumo:** Update Pricing Tier
**Descrição:** Update a pricing tier.

Requires tenant owner or manager role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tier_id` | string | path | ✅ | The ID of the pricing tier to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [PricingTierUpdate](#pricingtierupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PricingTierRead](#pricingtierread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/crm/crm/pricing/tiers/{tier_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
