import uuid
import logging
from typing import Optional, List, Dict, Any, Sequence
from sqlalchemy import select, update as sqlalchemy_update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.exc import IntegrityError
from fastapi import HTTPException, status

from app.modules.core.functions.orders.models.order import Order, OrderItem, OrderStatus
from app.modules.core.functions.orders.schemas.order import OrderCreate, OrderUpdate, OrderRead

# Import KDS service for integration
from app.modules.tenants.restaurants.kds.services.kds_service import KdsService
from app.modules.tenants.restaurants.kds.schemas.kitchen_order import KitchenOrderCreate

# Import WebSocket utility for real-time updates
from app.modules.core.functions.orders.websockets.order_websockets import (
    emit_new_order,
    emit_order_update,
)

logger = logging.getLogger(__name__)


class OrderService:
    """Service for managing orders across all tenants."""

    def __init__(self, db_session: Optional[AsyncSession] = None):
        """Initialize the service with an optional database session."""
        self.db = db_session
        self.kds_service = KdsService()

    async def _generate_order_number(self, db: AsyncSession, tenant_id: uuid.UUID) -> str:
        """Generate a unique order number for the tenant."""
        # Get the count of orders for this tenant
        stmt = select(Order).where(Order.tenant_id == tenant_id)
        result = await db.execute(stmt)
        count = len(result.scalars().all())

        # Generate order number in format: ORD-{tenant_id_first_8_chars}-{count+1}
        tenant_prefix = str(tenant_id).replace("-", "")[:8].upper()
        order_number = f"ORD-{tenant_prefix}-{count + 1:06d}"

        return order_number

    async def create_order(
        self,
        db: AsyncSession,
        order_in: OrderCreate,
        tenant_id: uuid.UUID,
        user_id: Optional[uuid.UUID] = None,
    ) -> Order:
        """Create a new order for the specified tenant."""
        try:
            # Generate order number
            order_number = await self._generate_order_number(db, tenant_id)

            # Create order object
            order_data = order_in.model_dump(exclude={"items"})
            db_order = Order(
                **order_data,
                tenant_id=tenant_id,
                user_id=user_id,
                order_number=order_number,
                status=OrderStatus.PENDING,
            )

            # Add order to database
            db.add(db_order)
            await db.flush()  # Flush to get the order ID

            # Create order items
            for item_data in order_in.items:
                item_dict = item_data.model_dump(
                    exclude={"variant_option_ids", "modifier_option_ids"}
                )
                db_item = OrderItem(**item_dict, order_id=db_order.id)
                db.add(db_item)
                await db.flush()  # Flush to get the item ID

                # Add variant options
                if item_data.variant_option_ids:
                    for variant_id in item_data.variant_option_ids:
                        await db.execute(
                            """
                            INSERT INTO order_item_variant_options (order_item_id, variant_option_id)  # noqa: E501
                            VALUES (:order_item_id, :variant_option_id)
                            """,
                            {
                                "order_item_id": db_item.id,
                                "variant_option_id": variant_id,
                            },
                        )

                # Add modifier options
                if item_data.modifier_option_ids:
                    for modifier_id in item_data.modifier_option_ids:
                        await db.execute(
                            """
                            INSERT INTO order_item_modifier_options (order_item_id, modifier_option_id)  # noqa: E501
                            VALUES (:order_item_id, :modifier_option_id)
                            """,
                            {
                                "order_item_id": db_item.id,
                                "modifier_option_id": modifier_id,
                            },
                        )

            # Commit the transaction
            await db.commit()

            # Refresh the order with all relationships
            await db.refresh(db_order, attribute_names=["items"])

            # Create KDS order if it's a food order
            if order_in.order_type in ["dine-in", "takeout", "delivery"]:
                await self._create_kitchen_order(db, db_order)

            # Emit WebSocket event for real-time updates
            try:
                order_read = await self.get_order(db, db_order.id, tenant_id)
                await emit_new_order(tenant_id, order_read)
            except Exception as ws_error:
                logger.error(f"Failed to emit WebSocket event for new order: {ws_error}")

            return db_order

        except IntegrityError as e:
            await db.rollback()
            logger.error(f"Integrity error creating order for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Error creating order. Check input data.",
            )
        except Exception as e:
            await db.rollback()
            logger.exception(f"Unexpected error creating order for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def _create_kitchen_order(self, db: AsyncSession, order: Order) -> None:
        """Create a kitchen order in the KDS system."""
        try:
            # Prepare order details for KDS
            order_details = {
                "order_number": order.order_number,
                "order_type": order.order_type,
                "table_number": order.table_number,
                "items": [],
            }

            # Add items to order details
            for item in order.items:
                item_details = {
                    "name": item.name,
                    "quantity": item.quantity,
                    "notes": item.notes,
                    "variant_options": [],
                    "modifier_options": [],
                }

                # Add variant options
                for variant in item.variant_options:
                    item_details["variant_options"].append(
                        {"id": str(variant.id), "name": variant.name}
                    )

                # Add modifier options
                for modifier in item.modifier_options:
                    item_details["modifier_options"].append(
                        {"id": str(modifier.id), "name": modifier.name}
                    )

                order_details["items"].append(item_details)

            # Create KDS order
            kds_order_in = KitchenOrderCreate(
                order_details=order_details,
                status="pending",
                source_order_id=order.id,
                creator_user_id=order.user_id,
            )

            await self.kds_service.create_order(
                db=db, order_in=kds_order_in, tenant_id=order.tenant_id
            )

        except Exception as e:
            logger.error(f"Error creating kitchen order for order {order.id}: {e}")
            # Don't raise exception, just log the error to avoid failing the main order creation

    async def get_order(
        self, db: AsyncSession, order_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Optional[OrderRead]:
        """Get an order by ID for the specified tenant."""
        stmt = (
            select(Order)
            .options(
                selectinload(Order.items).selectinload(OrderItem.variant_options),
                selectinload(Order.items).selectinload(OrderItem.modifier_options),
                joinedload(Order.customer),
                joinedload(Order.user),
                joinedload(Order.kitchen_order),
            )
            .where(Order.id == order_id, Order.tenant_id == tenant_id)
        )

        result = await db.execute(stmt)
        order = result.scalars().first()

        if not order:
            return None

        # Convert to Pydantic model
        return OrderRead.model_validate(order)

    async def get_orders(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID,
        status: Optional[OrderStatus] = None,
        order_type: Optional[str] = None,
        customer_id: Optional[uuid.UUID] = None,
        customer_name: Optional[str] = None,
        customer_email: Optional[str] = None,
        order_number: Optional[str] = None,
        date_from: Optional[str] = None,
        date_to: Optional[str] = None,
        min_amount: Optional[float] = None,
        max_amount: Optional[float] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> Sequence[Order]:
        """Get orders for the specified tenant with optional filters."""
        stmt = select(Order).where(Order.tenant_id == tenant_id)

        # Apply filters
        if status:
            stmt = stmt.where(Order.status == status)

        if order_type:
            stmt = stmt.where(Order.order_type == order_type)

        if customer_id:
            stmt = stmt.where(Order.customer_id == customer_id)

        # Handle search filters with OR logic if multiple search terms are provided
        search_conditions = []
        needs_customer_join = False
        
        if order_number:
            search_conditions.append(Order.order_number.ilike(f"%{order_number}%"))
            
        if customer_name:
            from app.modules.shared.crm.models.account import Account
            needs_customer_join = True
            search_conditions.append(Account.name.ilike(f"%{customer_name}%"))
            
        if customer_email:
            from app.modules.shared.crm.models.account import Account
            needs_customer_join = True
            search_conditions.append(Account.email.ilike(f"%{customer_email}%"))
            
        if needs_customer_join:
            stmt = stmt.outerjoin(Account, Order.customer_id == Account.id)
            
        if search_conditions:
            from sqlalchemy import or_
            stmt = stmt.where(or_(*search_conditions))

        if date_from:
            from datetime import datetime
            date_from_obj = datetime.strptime(date_from, "%Y-%m-%d")
            stmt = stmt.where(Order.created_at >= date_from_obj)

        if date_to:
            from datetime import datetime
            date_to_obj = datetime.strptime(date_to, "%Y-%m-%d")
            # Add one day to include the entire day
            date_to_obj = date_to_obj.replace(hour=23, minute=59, second=59)
            stmt = stmt.where(Order.created_at <= date_to_obj)

        if min_amount is not None:
            stmt = stmt.where(Order.total >= min_amount)

        if max_amount is not None:
            stmt = stmt.where(Order.total <= max_amount)

        # Add pagination
        stmt = stmt.order_by(Order.created_at.desc()).offset(skip).limit(limit)

        # Add eager loading of relationships
        stmt = stmt.options(selectinload(Order.items), joinedload(Order.kitchen_order))

        result = await db.execute(stmt)
        return result.scalars().all()

    async def count_orders(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID,
        status: Optional[OrderStatus] = None,
        order_type: Optional[str] = None,
        customer_id: Optional[uuid.UUID] = None,
        customer_name: Optional[str] = None,
        customer_email: Optional[str] = None,
        order_number: Optional[str] = None,
        date_from: Optional[str] = None,
        date_to: Optional[str] = None,
        min_amount: Optional[float] = None,
        max_amount: Optional[float] = None,
    ) -> int:
        """Count orders for the specified tenant with optional filters."""
        from sqlalchemy import func
        
        stmt = select(func.count(Order.id)).where(Order.tenant_id == tenant_id)

        # Apply filters
        if status:
            stmt = stmt.where(Order.status == status)

        if order_type:
            stmt = stmt.where(Order.order_type == order_type)

        if customer_id:
            stmt = stmt.where(Order.customer_id == customer_id)

        # Handle search filters with OR logic if multiple search terms are provided
        search_conditions = []
        needs_customer_join = False
        
        if order_number:
            search_conditions.append(Order.order_number.ilike(f"%{order_number}%"))
            
        if customer_name:
            from app.modules.shared.crm.models.account import Account
            needs_customer_join = True
            search_conditions.append(Account.name.ilike(f"%{customer_name}%"))
            
        if customer_email:
            from app.modules.shared.crm.models.account import Account
            needs_customer_join = True
            search_conditions.append(Account.email.ilike(f"%{customer_email}%"))
            
        if needs_customer_join:
            stmt = stmt.outerjoin(Account, Order.customer_id == Account.id)
            
        if search_conditions:
            from sqlalchemy import or_
            stmt = stmt.where(or_(*search_conditions))

        if date_from:
            from datetime import datetime
            date_from_obj = datetime.strptime(date_from, "%Y-%m-%d")
            stmt = stmt.where(Order.created_at >= date_from_obj)

        if date_to:
            from datetime import datetime
            date_to_obj = datetime.strptime(date_to, "%Y-%m-%d")
            # Add one day to include the entire day
            date_to_obj = date_to_obj.replace(hour=23, minute=59, second=59)
            stmt = stmt.where(Order.created_at <= date_to_obj)

        if min_amount is not None:
            stmt = stmt.where(Order.total >= min_amount)

        if max_amount is not None:
            stmt = stmt.where(Order.total <= max_amount)

        result = await db.execute(stmt)
        return result.scalar() or 0

    async def update_order_status(
        self,
        db: AsyncSession,
        order_id: uuid.UUID,
        status: OrderStatus,
        tenant_id: uuid.UUID,
    ) -> Optional[Order]:
        """Update the status of an order."""
        # Check if order exists
        order = await self.get_order(db, order_id, tenant_id)
        if not order:
            return None

        try:
            # Update order status
            stmt = (
                sqlalchemy_update(Order)
                .where(Order.id == order_id, Order.tenant_id == tenant_id)
                .values(status=status)
                .returning(Order)
            )
            result = await db.execute(stmt)
            updated_order = result.scalars().first()

            if not updated_order:
                return None

            # Commit the transaction
            await db.commit()

            # Update KDS order status if needed
            if updated_order.kitchen_order:
                kds_status = self._map_order_status_to_kds_status(status)
                await self.kds_service.update_order_status(
                    db=db,
                    order_id=updated_order.kitchen_order.id,
                    new_status=kds_status,
                    tenant_id=tenant_id,
                )

            # Emit WebSocket event for real-time updates
            try:
                order_read = await self.get_order(db, order_id, tenant_id)
                await emit_order_update(tenant_id, order_read)
            except Exception as ws_error:
                logger.error(f"Failed to emit WebSocket event for order update: {ws_error}")

            return updated_order

        except Exception as e:
            await db.rollback()
            logger.exception(f"Error updating order status: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def get_order_stats(self, db: AsyncSession, tenant_id: uuid.UUID) -> Dict[str, Any]:
        """Get order statistics for a tenant."""
        try:
            from sqlalchemy import func
            from decimal import Decimal
            
            # Get total orders count
            total_orders_stmt = select(func.count(Order.id)).where(Order.tenant_id == tenant_id)
            total_orders_result = await db.execute(total_orders_stmt)
            total_orders = total_orders_result.scalar() or 0
            
            # Get orders by status
            pending_orders_stmt = select(func.count(Order.id)).where(
                Order.tenant_id == tenant_id,
                Order.status == OrderStatus.PENDING
            )
            pending_orders_result = await db.execute(pending_orders_stmt)
            pending_orders = pending_orders_result.scalar() or 0
            
            confirmed_orders_stmt = select(func.count(Order.id)).where(
                Order.tenant_id == tenant_id,
                Order.status.in_([OrderStatus.CONFIRMED, OrderStatus.PREPARING, OrderStatus.READY])
            )
            confirmed_orders_result = await db.execute(confirmed_orders_stmt)
            confirmed_orders = confirmed_orders_result.scalar() or 0
            
            delivered_orders_stmt = select(func.count(Order.id)).where(
                Order.tenant_id == tenant_id,
                Order.status.in_([OrderStatus.DELIVERED, OrderStatus.COMPLETED])
            )
            delivered_orders_result = await db.execute(delivered_orders_stmt)
            delivered_orders = delivered_orders_result.scalar() or 0
            
            cancelled_orders_stmt = select(func.count(Order.id)).where(
                Order.tenant_id == tenant_id,
                Order.status == OrderStatus.CANCELLED
            )
            cancelled_orders_result = await db.execute(cancelled_orders_stmt)
            cancelled_orders = cancelled_orders_result.scalar() or 0
            
            # Get total revenue (sum of all completed orders)
            total_revenue_stmt = select(func.sum(Order.total)).where(
                Order.tenant_id == tenant_id,
                Order.status.in_([OrderStatus.DELIVERED, OrderStatus.COMPLETED])
            )
            total_revenue_result = await db.execute(total_revenue_stmt)
            total_revenue = total_revenue_result.scalar() or Decimal('0')
            
            # Calculate average order value
            if delivered_orders > 0:
                average_order_value = total_revenue / delivered_orders
            else:
                average_order_value = Decimal('0')
            
            return {
                "total_orders": total_orders,
                "pending_orders": pending_orders,
                "confirmed_orders": confirmed_orders,
                "delivered_orders": delivered_orders,
                "cancelled_orders": cancelled_orders,
                "total_revenue": float(total_revenue),
                "average_order_value": float(average_order_value)
            }
            
        except Exception as e:
            logger.exception(f"Error getting order stats: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    def _map_order_status_to_kds_status(self, order_status: OrderStatus) -> str:
        """Map order status to KDS status."""
        status_mapping = {
            OrderStatus.PENDING: "pending",
            OrderStatus.PREPARING: "preparing",
            OrderStatus.READY: "ready",
            OrderStatus.DELIVERED: "served",
            OrderStatus.COMPLETED: "completed",
            OrderStatus.CANCELLED: "cancelled",
        }
        return status_mapping.get(order_status, "pending")


# Create a global instance for dependency injection
order_service = OrderService()
