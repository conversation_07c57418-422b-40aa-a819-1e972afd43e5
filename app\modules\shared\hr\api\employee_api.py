from typing import List, Optional
import uuid
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    get_current_tenant,
    require_tenant_role,
)
from app.modules.core.roles.models.roles import TenantRole
from app.modules.core.users.models.user import User
from app.modules.core.tenants.models.tenant import Tenant

from app.modules.shared.hr.core.schemas.employee import (
    EmployeeCreate,
    EmployeeUpdate,
    EmployeeRead,
    EmployeeWithUser,
)
from app.modules.shared.hr.core.services.employee_service import employee_service  # noqa: E402
from app.modules.shared.hr.core.models.employee import EmploymentStatus

router = APIRouter(prefix="/employees", tags=["HR - Employees"])


@router.post(
    "",
    response_model=EmployeeRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create Employee",
    description="Create a new employee for the current tenant.",
)
async def create_employee(
    employee_data: EmployeeCreate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Create a new employee."""
    return await employee_service.create_employee(
        db=db, tenant_id=tenant.id, employee_data=employee_data
    )


@router.get(
    "/{employee_id}",
    response_model=EmployeeRead,
    summary="Get Employee",
    description="Get an employee by ID.",
)
async def get_employee(
    employee_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Get an employee by ID."""
    return await employee_service.get_employee(db=db, tenant_id=tenant.id, employee_id=employee_id)


@router.get(
    "/{employee_id}/with-user",
    response_model=EmployeeWithUser,
    summary="Get Employee With User",
    description="Get an employee by ID with associated user information.",
)
async def get_employee_with_user(
    employee_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Get an employee with user data."""
    return await employee_service.get_employee_with_user(
        db=db, tenant_id=tenant.id, employee_id=employee_id
    )


@router.get(
    "",
    response_model=List[EmployeeRead],
    summary="List Employees",
    description="Get all employees with optional filtering.",
)
async def get_employees(
    status: Optional[EmploymentStatus] = None,
    department: Optional[str] = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Get all employees with optional filtering."""
    return await employee_service.get_employees(
        db=db,
        tenant_id=tenant.id,
        skip=skip,
        limit=limit,
        status=status,
        department=department,
    )


@router.put(
    "/{employee_id}",
    response_model=EmployeeRead,
    summary="Update Employee",
    description="Update an employee by ID.",
)
async def update_employee(
    employee_id: uuid.UUID,
    employee_data: EmployeeUpdate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Update an employee."""
    return await employee_service.update_employee(
        db=db, tenant_id=tenant.id, employee_id=employee_id, employee_data=employee_data
    )


@router.delete(
    "/{employee_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete Employee",
    description="Delete an employee by ID.",
)
async def delete_employee(
    employee_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Delete an employee."""
    await employee_service.delete_employee(db=db, tenant_id=tenant.id, employee_id=employee_id)
    return None


@router.get(
    "/by-user/{user_id}",
    response_model=Optional[EmployeeRead],
    summary="Get Employee by User",
    description="Get an employee by user ID.",
)
async def get_employee_by_user(
    user_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Get an employee by user ID."""
    employee = await employee_service.get_employee_by_user_id(
        db=db, tenant_id=tenant.id, user_id=user_id
    )
    if not employee:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"No employee found for user {user_id} in tenant {tenant.id}",
        )
    return employee
