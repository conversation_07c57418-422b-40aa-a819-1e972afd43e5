"""
Ticket Service

Serviço para gerenciamento de tickets de suporte.
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import List, Optional, Tuple, Dict, Any
from uuid import UUID

from sqlalchemy import and_, desc, func, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from sqlalchemy.future import select

from app.modules.core.users.models.user import User
from app.modules.core.tenants.models.tenant import Tenant

from ..models import Ticket, TicketMessage, TicketStatus, TicketPriority, TicketCategory
from ..schemas import (
    TicketCreate, TicketUpdate, TicketFilters, TicketResponse,
    TicketListResponse, TicketAssignmentRequest, BulkTicketOperation
)

logger = logging.getLogger(__name__)


class TicketService:
    """Serviço para gerenciamento de tickets."""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def create_ticket(
        self,
        ticket_data: TicketCreate,
        user: User
    ) -> Ticket:
        """
        Cria um novo ticket.
        
        Args:
            ticket_data: Dados do ticket
            user: Usuário que criou o ticket
            
        Returns:
            Ticket criado
        """
        ticket = Ticket(
            title=ticket_data.title,
            description=ticket_data.description,
            category=ticket_data.category,
            user_id=user.id,
            tenant_id=ticket_data.tenant_id,
            status=TicketStatus.NEW,
            priority=TicketPriority.MEDIUM,  # Prioridade padrão, admin pode alterar
            is_read_by_admin=False,
            is_read_by_user=True
        )
        
        self.db.add(ticket)
        await self.db.commit()
        await self.db.refresh(ticket)

        # Enviar notificação em background
        # TODO: Implementar notificações quando o sistema estiver estável
        # from ..tasks.help_center_tasks import process_ticket_notification
        # ticket_data = {
        #     "id": str(ticket.id),
        #     "title": ticket.title,
        #     "description": ticket.description,
        #     "category": ticket.category.value,
        #     "priority": ticket.priority.value,
        #     "user_id": str(ticket.user_id),
        #     "tenant_id": str(ticket.tenant_id) if ticket.tenant_id else None
        # }
        # process_ticket_notification.delay(ticket_data, "ticket_created")

        logger.info(f"Ticket criado: {ticket.id} por usuário {user.id}")
        return ticket

    async def get_ticket(
        self,
        ticket_id: UUID,
        user: User
    ) -> Optional[Ticket]:
        """
        Obtém um ticket específico.
        
        Args:
            ticket_id: ID do ticket
            user: Usuário solicitante
            
        Returns:
            Ticket ou None se não encontrado/sem acesso
        """
        stmt = select(Ticket).options(
            selectinload(Ticket.user),
            selectinload(Ticket.assigned_admin),
            selectinload(Ticket.tenant),
            selectinload(Ticket.messages)
        ).where(Ticket.id == ticket_id)
        
        result = await self.db.execute(stmt)
        ticket = result.scalar_one_or_none()

        if not ticket:
            return None
        
        # Verifica acesso
        if not self._user_can_access_ticket(ticket, user):
            return None
        
        # Marca como lido se for admin
        if user.system_role == "admin" and not ticket.is_read_by_admin:
            ticket.mark_as_read_by_admin()
            await self.db.commit()
        
        return ticket

    async def update_ticket(
        self,
        ticket_id: UUID,
        ticket_data: TicketUpdate,
        user: User
    ) -> Optional[Ticket]:
        """
        Atualiza um ticket.
        
        Args:
            ticket_id: ID do ticket
            ticket_data: Dados para atualização
            user: Usuário que está atualizando
            
        Returns:
            Ticket atualizado ou None se não encontrado/sem acesso
        """
        ticket = await self.get_ticket(ticket_id, user)
        if not ticket:
            return None
        
        # Apenas admins podem alterar status, prioridade e atribuição
        if user.system_role == "admin":
            if ticket_data.status is not None:
                ticket.status = ticket_data.status
                if ticket_data.status == TicketStatus.RESOLVED:
                    ticket.resolved_at = datetime.now(timezone.utc)
                elif ticket_data.status == TicketStatus.CLOSED:
                    ticket.closed_at = datetime.now(timezone.utc)
            
            if ticket_data.priority is not None:
                ticket.priority = ticket_data.priority
            
            if ticket_data.assigned_admin_id is not None:
                ticket.assigned_admin_id = ticket_data.assigned_admin_id
        
        # Usuário pode atualizar título, descrição e categoria
        if ticket.user_id == user.id:
            if ticket_data.title is not None:
                ticket.title = ticket_data.title
            if ticket_data.description is not None:
                ticket.description = ticket_data.description
            if ticket_data.category is not None:
                ticket.category = ticket_data.category
        
        await self.db.commit()
        await self.db.refresh(ticket)
        
        logger.info(f"Ticket atualizado: {ticket.id} por usuário {user.id}")
        return ticket

    async def list_tickets(
        self,
        user: User,
        filters: Optional[TicketFilters] = None,
        page: int = 1,
        per_page: int = 20
    ) -> TicketListResponse:
        """
        Lista tickets com filtros.
        
        Args:
            user: Usuário solicitante
            filters: Filtros opcionais
            page: Página
            per_page: Itens por página
            
        Returns:
            Lista de tickets paginada
        """
        stmt = select(Ticket).options(
            selectinload(Ticket.user),
            selectinload(Ticket.assigned_admin),
            selectinload(Ticket.tenant),
            selectinload(Ticket.messages)
        )
        
        # Filtros de acesso baseados no usuário
        if user.system_role == "admin":
            # Admin vê todos os tickets
            pass
        else:
            # Usuário vê apenas seus próprios tickets
            stmt = stmt.where(Ticket.user_id == user.id)
        
        # Aplicar filtros
        if filters:
            if filters.status:
                stmt = stmt.where(Ticket.status == filters.status)
            if filters.priority:
                stmt = stmt.where(Ticket.priority == filters.priority)
            if filters.category:
                stmt = stmt.where(Ticket.category == filters.category)
            if filters.assigned_admin_id:
                stmt = stmt.where(Ticket.assigned_admin_id == filters.assigned_admin_id)
            if filters.user_id and user.system_role == "admin":
                stmt = stmt.where(Ticket.user_id == filters.user_id)
            if filters.tenant_id:
                stmt = stmt.where(Ticket.tenant_id == filters.tenant_id)
            if filters.search:
                search_term = f"%{filters.search}%"
                stmt = stmt.where(
                    or_(
                        Ticket.title.ilike(search_term),
                        Ticket.description.ilike(search_term)
                    )
                )
            if filters.date_from:
                stmt = stmt.where(Ticket.created_at >= filters.date_from)
            if filters.date_to:
                stmt = stmt.where(Ticket.created_at <= filters.date_to)
        
        # Ordenação
        stmt = stmt.order_by(desc(Ticket.created_at))
        
        # Contagem total
        count_stmt = select(func.count()).select_from(stmt.subquery())
        total_result = await self.db.execute(count_stmt)
        total = total_result.scalar()
        
        # Paginação
        offset = (page - 1) * per_page
        stmt = stmt.offset(offset).limit(per_page)
        
        result = await self.db.execute(stmt)
        tickets = result.scalars().all()
        
        # Converter para response
        ticket_responses = []
        for ticket in tickets:
            ticket_response = TicketResponse(
                id=ticket.id,
                title=ticket.title,
                description=ticket.description,
                category=ticket.category,
                status=ticket.status,
                priority=ticket.priority,
                user_id=ticket.user_id,
                tenant_id=ticket.tenant_id,
                assigned_admin_id=ticket.assigned_admin_id,
                is_read_by_admin=ticket.is_read_by_admin,
                is_read_by_user=ticket.is_read_by_user,
                created_at=ticket.created_at,
                updated_at=ticket.updated_at,
                resolved_at=ticket.resolved_at,
                closed_at=ticket.closed_at,
                expires_at=ticket.expires_at,
                is_expired=ticket.is_expired,
                user_name=ticket.user.full_name if ticket.user else None,
                user_email=ticket.user.email if ticket.user else None,
                assigned_admin_name=ticket.assigned_admin.full_name if ticket.assigned_admin else None,
                message_count=len(ticket.messages) if ticket.messages else 0
            )
            ticket_responses.append(ticket_response)
        
        return TicketListResponse(
            tickets=ticket_responses,
            total=total,
            page=page,
            per_page=per_page,
            has_next=offset + per_page < total,
            has_prev=page > 1
        )

    def _user_can_access_ticket(self, ticket: Ticket, user: User) -> bool:
        """
        Verifica se usuário pode acessar o ticket.
        
        Args:
            ticket: Ticket
            user: Usuário
            
        Returns:
            True se pode acessar
        """
        # Admin pode acessar todos
        if user.system_role == "admin":
            return True
        
        # Usuário pode acessar seus próprios tickets
        if ticket.user_id == user.id:
            return True
        
        return False

    async def assign_ticket(
        self,
        assignment_data: TicketAssignmentRequest,
        admin_user: User
    ) -> Optional[Ticket]:
        """
        Atribui ticket a um admin.

        Args:
            assignment_data: Dados da atribuição
            admin_user: Admin que está fazendo a atribuição

        Returns:
            Ticket atualizado ou None se não encontrado
        """
        if admin_user.system_role != "admin":
            return None

        ticket = await self.get_ticket(assignment_data.ticket_id, admin_user)
        if not ticket:
            return None

        ticket.assigned_admin_id = assignment_data.admin_id
        await self.db.commit()
        await self.db.refresh(ticket)

        logger.info(f"Ticket {ticket.id} atribuído ao admin {assignment_data.admin_id}")
        return ticket

    async def bulk_operation(
        self,
        operation_data: BulkTicketOperation,
        admin_user: User
    ) -> List[UUID]:
        """
        Executa operação em lote nos tickets.

        Args:
            operation_data: Dados da operação
            admin_user: Admin executando a operação

        Returns:
            Lista de IDs dos tickets processados
        """
        if admin_user.system_role != "admin":
            return []

        processed_tickets = []

        for ticket_id in operation_data.ticket_ids:
            ticket = await self.get_ticket(ticket_id, admin_user)
            if not ticket:
                continue

            if operation_data.operation == "close":
                ticket.status = TicketStatus.CLOSED
                ticket.closed_at = datetime.now(timezone.utc)
            elif operation_data.operation == "resolve":
                ticket.status = TicketStatus.RESOLVED
                ticket.resolved_at = datetime.now(timezone.utc)
            elif operation_data.operation == "assign" and operation_data.value:
                try:
                    admin_id = UUID(operation_data.value)
                    ticket.assigned_admin_id = admin_id
                except ValueError:
                    continue
            elif operation_data.operation == "change_priority" and operation_data.value:
                try:
                    priority = TicketPriority(operation_data.value)
                    ticket.priority = priority
                except ValueError:
                    continue

            processed_tickets.append(ticket_id)

        await self.db.commit()
        logger.info(f"Operação em lote '{operation_data.operation}' executada em {len(processed_tickets)} tickets")
        return processed_tickets

    async def delete_ticket(
        self,
        ticket_id: UUID,
        admin_user: User
    ) -> bool:
        """
        Deleta um ticket (apenas admin).

        Args:
            ticket_id: ID do ticket
            admin_user: Admin executando a operação

        Returns:
            True se deletado com sucesso
        """
        if admin_user.system_role != "admin":
            return False

        ticket = await self.get_ticket(ticket_id, admin_user)
        if not ticket:
            return False

        await self.db.delete(ticket)
        await self.db.commit()

        logger.info(f"Ticket {ticket_id} deletado pelo admin {admin_user.id}")
        return True

    async def mark_tickets_as_read(
        self,
        ticket_ids: List[UUID],
        user: User
    ) -> List[UUID]:
        """
        Marca tickets como lidos.

        Args:
            ticket_ids: IDs dos tickets
            user: Usuário marcando como lido

        Returns:
            Lista de IDs processados
        """
        processed_tickets = []

        for ticket_id in ticket_ids:
            ticket = await self.get_ticket(ticket_id, user)
            if not ticket:
                continue

            if user.system_role == "admin":
                ticket.mark_as_read_by_admin()
            else:
                ticket.mark_as_read_by_user()

            processed_tickets.append(ticket_id)

        await self.db.commit()
        return processed_tickets

    async def bulk_update_tickets(
        self,
        ticket_ids: List[str],
        action: str,
        value: Optional[str],
        admin_user: User
    ) -> int:
        """
        Atualiza múltiplos tickets em lote.

        Args:
            ticket_ids: Lista de IDs dos tickets
            action: Ação a ser executada (status, priority, assign)
            value: Valor para a ação
            admin_user: Usuário admin executando a ação

        Returns:
            Número de tickets atualizados
        """
        if admin_user.system_role != "admin":
            raise ValueError("Apenas administradores podem fazer atualizações em lote")

        updated_count = 0

        for ticket_id_str in ticket_ids:
            try:
                ticket_id = UUID(ticket_id_str)

                # Buscar ticket
                stmt = select(Ticket).where(Ticket.id == ticket_id)
                result = await self.db.execute(stmt)
                ticket = result.scalar_one_or_none()

                if not ticket:
                    continue

                # Aplicar ação
                if action == "status" and value:
                    if value in ["new", "open", "pending", "resolved", "closed"]:
                        old_status = ticket.status
                        ticket.status = TicketStatus(value)
                        ticket.updated_at = datetime.now(timezone.utc)

                        # Enviar notificação de mudança de status
                        # TODO: Implementar notificações quando o sistema estiver estável
                        # from ..tasks.help_center_tasks import process_ticket_notification
                        # status_data = {
                        #     "ticket_id": str(ticket.id),
                        #     "old_status": old_status.value,
                        #     "new_status": value,
                        #     "changed_by": str(admin_user.id)
                        # }
                        # process_ticket_notification.delay(status_data, "ticket_status_changed")

                elif action == "priority" and value:
                    if value in ["low", "medium", "high", "urgent"]:
                        ticket.priority = TicketPriority(value)
                        ticket.updated_at = datetime.now(timezone.utc)

                elif action == "assign" and value:
                    # Verificar se o usuário existe e é admin
                    admin_stmt = select(User).where(
                        and_(User.id == value, User.system_role == "admin")
                    )
                    admin_result = await self.db.execute(admin_stmt)
                    assigned_admin = admin_result.scalar_one_or_none()

                    if assigned_admin:
                        ticket.assigned_admin_id = assigned_admin.id
                        ticket.updated_at = datetime.now(timezone.utc)

                updated_count += 1

            except Exception as e:
                logger.error(f"Erro ao atualizar ticket {ticket_id_str}: {e}")
                continue

        await self.db.commit()
        logger.info(f"Atualização em lote: {updated_count} tickets atualizados por {admin_user.id}")

        return updated_count

    async def list_admin_tickets(
        self,
        page: int = 1,
        per_page: int = 20,
        filters: Optional[Dict[str, Any]] = None,
        sort_by: str = "created_at",
        sort_order: str = "desc"
    ) -> Dict[str, Any]:
        """
        Lista todos os tickets para administradores.

        Args:
            page: Página atual
            per_page: Itens por página
            filters: Filtros a aplicar
            sort_by: Campo para ordenação
            sort_order: Ordem (asc/desc)

        Returns:
            Dicionário com tickets e metadados de paginação
        """
        filters = filters or {}

        # Query base
        stmt = select(Ticket).options(
            selectinload(Ticket.user),
            selectinload(Ticket.tenant),
            selectinload(Ticket.assigned_admin)
        )

        # Aplicar filtros
        if filters.get("status"):
            stmt = stmt.where(Ticket.status == TicketStatus(filters["status"]))

        if filters.get("priority"):
            stmt = stmt.where(Ticket.priority == TicketPriority(filters["priority"]))

        if filters.get("category"):
            stmt = stmt.where(Ticket.category == TicketCategory(filters["category"]))

        if filters.get("search"):
            search_term = f"%{filters['search']}%"
            stmt = stmt.where(
                or_(
                    Ticket.title.ilike(search_term),
                    Ticket.description.ilike(search_term)
                )
            )

        if filters.get("assigned"):
            if filters["assigned"] == "unassigned":
                stmt = stmt.where(Ticket.assigned_admin_id.is_(None))
            else:
                stmt = stmt.where(Ticket.assigned_admin_id == filters["assigned"])

        # Contar total
        count_stmt = select(func.count()).select_from(stmt.subquery())
        count_result = await self.db.execute(count_stmt)
        total = count_result.scalar()

        # Aplicar ordenação
        if sort_by == "created_at":
            order_field = Ticket.created_at
        elif sort_by == "updated_at":
            order_field = Ticket.updated_at
        elif sort_by == "priority":
            order_field = Ticket.priority
        else:
            order_field = Ticket.created_at

        if sort_order == "desc":
            stmt = stmt.order_by(order_field.desc())
        else:
            stmt = stmt.order_by(order_field.asc())

        # Aplicar paginação
        offset = (page - 1) * per_page
        stmt = stmt.offset(offset).limit(per_page)

        # Executar query
        result = await self.db.execute(stmt)
        tickets = result.scalars().all()

        # Preparar resposta
        ticket_list = []
        for ticket in tickets:
            ticket_dict = {
                "id": str(ticket.id),
                "title": ticket.title,
                "description": ticket.description,
                "status": ticket.status.value,
                "priority": ticket.priority.value,
                "category": ticket.category.value,
                "created_at": ticket.created_at.isoformat(),
                "updated_at": ticket.updated_at.isoformat() if ticket.updated_at else None,
                "is_read_by_admin": ticket.is_read_by_admin,
                "is_read_by_user": ticket.is_read_by_user,
                "user_name": ticket.user.full_name if ticket.user else None,
                "user_email": ticket.user.email if ticket.user else None,
                "assigned_admin_name": ticket.assigned_admin.full_name if ticket.assigned_admin else None,
                "tenant_id": str(ticket.tenant.id) if ticket.tenant else None
            }

            # Contar mensagens
            msg_stmt = select(func.count()).where(TicketMessage.ticket_id == ticket.id)
            msg_result = await self.db.execute(msg_stmt)
            ticket_dict["message_count"] = msg_result.scalar()

            ticket_list.append(ticket_dict)

        return {
            "tickets": ticket_list,
            "total": total,
            "page": page,
            "per_page": per_page,
            "has_next": page * per_page < total,
            "has_prev": page > 1
        }
