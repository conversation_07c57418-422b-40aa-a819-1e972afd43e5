from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from typing import List, Optional, Annotated, Any, TYPE_CHECKING
import uuid
from fastapi import APIRouter, HTTPException, status, Depends, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db

if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.core.users.models.user import User

from app.modules.core.functions.orders.schemas.order import (
    OrderCreate,
    OrderUpdate,
    OrderRead,
    OrderStats,
    OrderListResponse,
)
from app.modules.core.functions.orders.models.order import OrderStatus  # noqa: E402
from app.modules.core.functions.orders.services.order_service import OrderService

from app.modules.core.auth.dependencies.auth_dependencies import (  # noqa: E402
    get_current_active_user,
)
from app.modules.core.tenants.dependencies.tenant_dependencies import (  # noqa: E402
    get_tenant_for_admin_or_header,
    require_tenant_role,
)
from app.modules.core.roles.models.roles import RolePermissions

router = APIRouter()

# Define required roles for different operations
view_roles = RolePermissions.VIEW_ROLES  # All roles that can view
write_roles = RolePermissions.ADMIN_ROLES  # Only admin roles can modify


@router.post("/", response_model=OrderRead, status_code=status.HTTP_201_CREATED)
async def create_order(
    order_in: OrderCreate,
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Optional["Tenant"], Depends(get_tenant_for_admin_or_header)] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=write_roles))] = None,
    order_service: OrderService = Depends(lambda: OrderService()),
):
    """
    Create a new order for the current tenant.
    Requires OWNER or MANAGER tenant role.
    """
    # For order creation, we require a tenant even for system admins
    if not current_tenant:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Tenant context required for order creation. Please specify X-Tenant-ID header.",
        )
    
    return await order_service.create_order(
        db=db, order_in=order_in, tenant_id=current_tenant.id, user_id=current_user.id
    )


@router.get("/", response_model=OrderListResponse)
async def read_orders(
    status: Optional[OrderStatus] = Query(None, description="Filter by order status"),
    order_type: Optional[str] = Query(
        None, description="Filter by order type (dine-in, takeout, delivery, etc.)"
    ),
    customer_id: Optional[uuid.UUID] = Query(None, description="Filter by customer ID"),
    customer_name: Optional[str] = Query(None, description="Filter by customer name"),
    customer_email: Optional[str] = Query(None, description="Filter by customer email"),
    order_number: Optional[str] = Query(None, description="Filter by order number"),
    date_from: Optional[str] = Query(None, description="Filter orders from date (YYYY-MM-DD)"),
    date_to: Optional[str] = Query(None, description="Filter orders to date (YYYY-MM-DD)"),
    min_amount: Optional[float] = Query(None, description="Filter by minimum order amount"),
    max_amount: Optional[float] = Query(None, description="Filter by maximum order amount"),
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(10, ge=1, le=200, description="Number of records per page"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Optional["Tenant"], Depends(get_tenant_for_admin_or_header)] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))] = None,
    order_service: OrderService = Depends(lambda: OrderService()),
):
    """
    Retrieve orders for the current tenant, with optional filters.
    Requires at least costumer role in the tenant.
    """
    # Calculate skip value from page and per_page
    skip = (page - 1) * per_page
    
    # Get orders and total count
    # For system admins, tenant_id can be None to get all orders
    tenant_id = current_tenant.id if current_tenant else None
    orders = await order_service.get_orders(
        db=db,
        tenant_id=tenant_id,
        status=status,
        order_type=order_type,
        customer_id=customer_id,
        customer_name=customer_name,
        customer_email=customer_email,
        order_number=order_number,
        date_from=date_from,
        date_to=date_to,
        min_amount=min_amount,
        max_amount=max_amount,
        skip=skip,
        limit=per_page,
    )
    
    total = await order_service.count_orders(
        db=db,
        tenant_id=tenant_id,
        status=status,
        order_type=order_type,
        customer_id=customer_id,
        customer_name=customer_name,
        customer_email=customer_email,
        order_number=order_number,
        date_from=date_from,
        date_to=date_to,
        min_amount=min_amount,
        max_amount=max_amount,
    )
    
    # Calculate total pages
    total_pages = (total + per_page - 1) // per_page
    
    return OrderListResponse(
        orders=orders,
        total=total,
        page=page,
        per_page=per_page,
        total_pages=total_pages,
    )


@router.get("/stats", response_model=OrderStats)
async def get_order_stats(
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Optional["Tenant"], Depends(get_tenant_for_admin_or_header)] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))] = None,
    order_service: OrderService = Depends(lambda: OrderService()),
):
    """
    Get order statistics for the current tenant.
    Requires at least customer role in the tenant.
    """
    # For system admins, tenant_id can be None to get stats from all tenants
    tenant_id = current_tenant.id if current_tenant else None
    stats_data = await order_service.get_order_stats(db=db, tenant_id=tenant_id)
    return OrderStats(**stats_data)


@router.get("/{order_id}", response_model=OrderRead)
async def read_order(
    order_id: Annotated[uuid.UUID, Path(..., description="ID of the order to retrieve")],
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Optional["Tenant"], Depends(get_tenant_for_admin_or_header)] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))] = None,
    order_service: OrderService = Depends(lambda: OrderService()),
):
    """
    Retrieve a specific order by ID.
    Requires at least costumer role in the tenant.
    """
    # For system admins, tenant_id can be None to get order from any tenant
    tenant_id = current_tenant.id if current_tenant else None
    order = await order_service.get_order(db=db, order_id=order_id, tenant_id=tenant_id)
    if order is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Order not found",
        )
    return order


@router.patch("/{order_id}/status", response_model=OrderRead)
async def update_order_status(
    order_id: Annotated[uuid.UUID, Path(..., description="ID of the order to update")],
    status_update: OrderUpdate,
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Optional["Tenant"], Depends(get_tenant_for_admin_or_header)] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(required_roles=write_roles))] = None,
    order_service: OrderService = Depends(lambda: OrderService()),
):
    """
    Update the status of an order.
    Requires OWNER or MANAGER tenant role.
    """
    if not status_update.status:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Status not provided",
        )

    # For system admins, tenant_id can be None to update order from any tenant
    tenant_id = current_tenant.id if current_tenant else None
    updated_order = await order_service.update_order_status(
        db=db,
        order_id=order_id,
        status=status_update.status,
        tenant_id=tenant_id,
    )
    if updated_order is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Order not found",
        )

    # Get the full order with all relationships
    return await order_service.get_order(db=db, order_id=order_id, tenant_id=current_tenant.id)
