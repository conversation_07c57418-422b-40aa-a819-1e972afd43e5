# General - Websocket

**Categoria:** General
**<PERSON><PERSON><PERSON><PERSON>:** Websocket
**Total de Endpoints:** 1
**Gerado em:** 27/06/2025, 20:20:27

## 📋 Endpoints

- [GET /ws-test](#get-ws-test) - Websocket Test

## 🔗 Endpoints Detalhados

### GET /ws-test {#get-ws-test}

**Resumo:** Websocket Test
**Descrição:** Endpoint para testar se o WebSocket está configurado.

**🔐 Autenticação:** Não requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/ws-test"
```

---
