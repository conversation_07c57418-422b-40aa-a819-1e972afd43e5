"""Enhanced notification system with email and system integration

Revision ID: 23885627cae7
Revises: 9703bc3d3f86
Create Date: 2025-06-27 15:56:20.345776

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '23885627cae7'
down_revision: Union[str, None] = '9703bc3d3f86'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Skip index modification due to foreign key dependencies
    # op.drop_index(op.f('ix_languages_code'), table_name='languages')
    # op.create_index('ix_languages_code', 'languages', ['code'], unique=False)
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Skip index modification due to foreign key dependencies
    # op.drop_index('ix_languages_code', table_name='languages')
    # op.create_index(op.f('ix_languages_code'), 'languages', ['code'], unique=True)
    pass
    # ### end Alembic commands ###
