"""
Tenant Payment Method Configuration model for managing payment method settings per tenant.
"""

import uuid
from typing import TYPE_CHECKING, Optional, Dict, Any

from sqlalchemy import String, Boolean, Integer, JSON, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.db.base import Base

if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.core.payments.models.payment_method import PaymentMethod


class TenantPaymentMethodConfig(Base):
    """
    Model for tenant-specific payment method configurations.
    
    This model allows tenants to enable/disable payment methods,
    set display order, and configure custom settings for each payment method.
    """

    __tablename__ = "tenant_payment_method_configs"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    tenant_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), 
        ForeignKey("tenants.id", ondelete="CASCADE"), 
        nullable=False,
        index=True
    )
    payment_method_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), 
        ForeignKey("core_payment_methods.id", ondelete="CASCADE"), 
        nullable=False,
        index=True
    )

    # Configuration Settings
    is_enabled: Mapped[bool] = mapped_column(
        Boolean, default=True, nullable=False
    )
    display_order: Mapped[int] = mapped_column(
        Integer, default=0, nullable=False
    )
    
    # Custom configuration for this payment method (tenant-specific)
    custom_config: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON, nullable=True
    )
    
    # Display settings
    custom_name: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True
    )  # Custom display name for this tenant
    custom_icon: Mapped[Optional[str]] = mapped_column(
        String(255), nullable=True
    )  # Custom icon URL or name
    
    # Processing settings
    is_default: Mapped[bool] = mapped_column(
        Boolean, default=False, nullable=False
    )  # Is this the default payment method for this tenant
    
    # Fee configuration (if applicable)
    fee_percentage: Mapped[Optional[float]] = mapped_column(
        nullable=True
    )  # Fee percentage for this payment method
    fee_fixed: Mapped[Optional[float]] = mapped_column(
        nullable=True
    )  # Fixed fee for this payment method
    
    # Minimum/Maximum amounts
    min_amount: Mapped[Optional[float]] = mapped_column(
        nullable=True
    )  # Minimum transaction amount
    max_amount: Mapped[Optional[float]] = mapped_column(
        nullable=True
    )  # Maximum transaction amount

    # Relationships
    tenant: Mapped["Tenant"] = relationship(
        "app.modules.core.tenants.models.tenant.Tenant",
        lazy="select"
    )
    payment_method: Mapped["PaymentMethod"] = relationship(
        "app.modules.core.payments.models.payment_method.PaymentMethod",
        lazy="select"
    )

    def __repr__(self) -> str:
        return (
            f"<TenantPaymentMethodConfig(id={self.id}, tenant_id={self.tenant_id}, "
            f"payment_method_id={self.payment_method_id}, is_enabled={self.is_enabled})>"
        )

    @property
    def effective_name(self) -> str:
        """Get the effective display name (custom or default)."""
        if self.custom_name:
            return self.custom_name
        return self.payment_method.name if self.payment_method else "Unknown"

    @property
    def effective_icon(self) -> Optional[str]:
        """Get the effective icon (custom or default)."""
        if self.custom_icon:
            return self.custom_icon
        return self.payment_method.icon if self.payment_method else None

    @property
    def has_fees(self) -> bool:
        """Check if this payment method has fees configured."""
        return (self.fee_percentage is not None and self.fee_percentage > 0) or \
               (self.fee_fixed is not None and self.fee_fixed > 0)

    @property
    def has_amount_limits(self) -> bool:
        """Check if this payment method has amount limits."""
        return self.min_amount is not None or self.max_amount is not None

    def calculate_fee(self, amount: float) -> float:
        """Calculate the fee for a given amount."""
        total_fee = 0.0
        
        if self.fee_percentage:
            total_fee += amount * (self.fee_percentage / 100)
        
        if self.fee_fixed:
            total_fee += self.fee_fixed
            
        return total_fee

    def is_amount_valid(self, amount: float) -> bool:
        """Check if the given amount is within the configured limits."""
        if self.min_amount is not None and amount < self.min_amount:
            return False
        
        if self.max_amount is not None and amount > self.max_amount:
            return False
            
        return True
