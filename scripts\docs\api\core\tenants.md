# Core - Tenants

**Categoria:** Core
**Mó<PERSON><PERSON>:** Tenants
**Total de Endpoints:** 37
**Gera<PERSON> em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [GET /api/tenants/](#get-apitenants) - Read Tenants
- [POST /api/tenants/](#post-apitenants) - Create Tenant
- [GET /api/tenants/admin/audit/tenant/{tenant_id}](#get-apitenantsadminaudittenanttenant-id) - Admin: Get tenant audit log
- [POST /api/tenants/admin/tenant/{tenant_id}/action](#post-apitenantsadmintenanttenant-idaction) - Admin: Perform action on tenant
- [GET /api/tenants/admin/tenant/{tenant_id}/dashboard-data](#get-apitenantsadmintenanttenant-iddashboard-data) - Admin: Get tenant dashboard data
- [GET /api/tenants/admin/tenant/{tenant_id}/view](#get-apitenantsadmintenanttenant-idview) - Admin: View any tenant
- [GET /api/tenants/settings/](#get-apitenantssettings) - Get Tenant Settings
- [PUT /api/tenants/settings/](#put-apitenantssettings) - Update Tenant Settings
- [PUT /api/tenants/settings/business/](#put-apitenantssettingsbusiness) - Update Business Settings
- [GET /api/tenants/settings/categories/zones](#get-apitenantssettingscategorieszones) - Get Restaurant Zones
- [POST /api/tenants/settings/categories/zones](#post-apitenantssettingscategorieszones) - Create Custom Zone
- [DELETE /api/tenants/settings/categories/zones/{zone_name}](#delete-apitenantssettingscategorieszoneszone-name) - Delete Custom Zone
- [PUT /api/tenants/settings/currency/](#put-apitenantssettingscurrency) - Update Currency Settings
- [PUT /api/tenants/settings/languages/](#put-apitenantssettingslanguages) - Update Language Settings
- [PUT /api/tenants/settings/location/](#put-apitenantssettingslocation) - Update Location Settings
- [PUT /api/tenants/settings/loyalty/](#put-apitenantssettingsloyalty) - Update Loyalty Settings
- [PUT /api/tenants/settings/operating-hours/](#put-apitenantssettingsoperating-hours) - Update Operating Hours
- [PUT /api/tenants/settings/social-media/](#put-apitenantssettingssocial-media) - Update Social Media Settings
- [POST /api/tenants/settings/sync-tenant-data](#post-apitenantssettingssync-tenant-data) - Sync Tenant Data
- [PUT /api/tenants/settings/tax/](#put-apitenantssettingstax) - Update Tax Settings
- [GET /api/tenants/tenant-settings/](#get-apitenantstenant-settings) - Get Tenant Settings
- [PUT /api/tenants/tenant-settings/](#put-apitenantstenant-settings) - Update Tenant Settings
- [PUT /api/tenants/tenant-settings/business/](#put-apitenantstenant-settingsbusiness) - Update Business Settings
- [GET /api/tenants/tenant-settings/categories/zones](#get-apitenantstenant-settingscategorieszones) - Get Restaurant Zones
- [POST /api/tenants/tenant-settings/categories/zones](#post-apitenantstenant-settingscategorieszones) - Create Custom Zone
- [DELETE /api/tenants/tenant-settings/categories/zones/{zone_name}](#delete-apitenantstenant-settingscategorieszoneszone-name) - Delete Custom Zone
- [PUT /api/tenants/tenant-settings/currency/](#put-apitenantstenant-settingscurrency) - Update Currency Settings
- [PUT /api/tenants/tenant-settings/languages/](#put-apitenantstenant-settingslanguages) - Update Language Settings
- [PUT /api/tenants/tenant-settings/location/](#put-apitenantstenant-settingslocation) - Update Location Settings
- [PUT /api/tenants/tenant-settings/loyalty/](#put-apitenantstenant-settingsloyalty) - Update Loyalty Settings
- [PUT /api/tenants/tenant-settings/operating-hours/](#put-apitenantstenant-settingsoperating-hours) - Update Operating Hours
- [PUT /api/tenants/tenant-settings/social-media/](#put-apitenantstenant-settingssocial-media) - Update Social Media Settings
- [POST /api/tenants/tenant-settings/sync-tenant-data](#post-apitenantstenant-settingssync-tenant-data) - Sync Tenant Data
- [PUT /api/tenants/tenant-settings/tax/](#put-apitenantstenant-settingstax) - Update Tax Settings
- [GET /api/tenants/{tenant_id}](#get-apitenantstenant-id) - Read Tenant
- [GET /api/tenants/{tenant_id}/users](#get-apitenantstenant-idusers) - Read Tenant Users
- [POST /api/tenants/{tenant_id}/users](#post-apitenantstenant-idusers) - Add User To Tenant

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### AddUserToTenantPayload

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `user_id` | string | ✅ | - |
| `role` | TenantRole | ✅ | - |
| `staff_sub_role` | unknown | ❌ | - |
| `data_sharing_consent` | unknown | ❌ | - |

### BusinessSettingsUpdate

**Descrição:** Schema for updating common business settings.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `business_name` | unknown | ❌ | Business name |
| `business_type` | unknown | ❌ | Type of business |

### CurrencySettingsUpdate

**Descrição:** Schema for updating currency settings.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `default_currency` | unknown | ❌ | Default currency code (1:1 ratio for stability) |
| `currency_config` | unknown | ❌ | Multi-currency configuration with exchange rates and formatting |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### LanguageSettingsUpdate

**Descrição:** Schema for updating language settings.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `multi_language_enabled` | unknown | ❌ | Enable multi-language support |
| `available_languages` | unknown | ❌ | List of available language codes |
| `default_language` | unknown | ❌ | Default language code |

### LocationSettingsUpdate

**Descrição:** Schema for updating location settings.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `country` | unknown | ❌ | Country code (ISO 3166-1 alpha-2) |
| `address` | unknown | ❌ | Complete address with coordinates |

### LoyaltySettingsUpdate

**Descrição:** Schema for updating loyalty settings.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `loyalty_enabled` | unknown | ❌ | Enable loyalty system |
| `loyalty_config` | unknown | ❌ | Loyalty system configuration |

### OperatingHoursUpdate

**Descrição:** Schema for updating operating hours.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `operating_hours` | unknown | ❌ | Operating hours configuration by day of week |

### SocialMediaSettingsUpdate

**Descrição:** Schema for updating social media settings.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `social_media_links` | unknown | ❌ | Social media platform links with icons |

### TaxSettingsUpdate

**Descrição:** Schema for updating tax settings.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `base_tax_rate` | unknown | ❌ | Base tax rate percentage |
| `tax_calculation_method` | unknown | ❌ | Tax calculation method: 'incremental' or 'inclusive' |

### Tenant

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `is_active` | boolean | ❌ | - |
| `id` | string | ✅ | - |
| `created_at` | unknown | ❌ | - |
| `updated_at` | unknown | ❌ | - |
| `name` | unknown | ❌ | - |
| `tenant_slug` | unknown | ❌ | - |

### TenantCreate

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `is_active` | boolean | ❌ | - |

### TenantSettingsRead

**Descrição:** Schema for reading tenant settings.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `business_name` | unknown | ❌ | Business name |
| `business_type` | unknown | ❌ | Type of business |
| `tenant_slug` | unknown | ❌ | Unique slug for tenant identification in public URLs |
| `default_currency` | string | ❌ | Default currency code (1:1 ratio for stability) |
| `currency_config` | unknown | ❌ | Multi-currency configuration with exchange rates and formatting |
| `timezone` | string | ❌ | Timezone identifier |
| `operating_hours` | unknown | ❌ | Operating hours configuration by day of week |
| `multi_language_enabled` | boolean | ❌ | Enable multi-language support |
| `available_languages` | unknown | ❌ | List of available language codes |
| `default_language` | string | ❌ | Default language code |
| `loyalty_enabled` | boolean | ❌ | Enable loyalty system |
| `loyalty_config` | unknown | ❌ | Loyalty system configuration |
| `country` | unknown | ❌ | Country code (ISO 3166-1 alpha-2) |
| `address` | unknown | ❌ | Complete address with coordinates |
| `base_tax_rate` | string | ❌ | Base tax rate percentage |
| `tax_calculation_method` | string | ❌ | Tax calculation method: 'incremental' or 'inclusive' |
| `wifi_networks` | unknown | ❌ | WiFi networks configuration by zone |
| `social_media_links` | unknown | ❌ | Social media platform links with icons |
| `subscription_plan` | unknown | ❌ | Current subscription plan |
| `subscription_status` | string | ❌ | Subscription status |
| `additional_settings` | unknown | ❌ | Additional configuration settings |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | unknown | ❌ | - |
| `updated_at` | unknown | ❌ | - |
| `has_social_media_links` | boolean | ❌ | Whether social media links are configured |

### TenantSettingsUpdate

**Descrição:** Schema for updating tenant settings. All fields are optional.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `business_name` | unknown | ❌ | Business name |
| `business_type` | unknown | ❌ | Type of business |
| `tenant_slug` | unknown | ❌ | Unique slug for tenant identification in public URLs |
| `default_currency` | unknown | ❌ | Default currency code (1:1 ratio for stability) |
| `currency_config` | unknown | ❌ | Multi-currency configuration with exchange rates and formatting |
| `timezone` | unknown | ❌ | Timezone identifier |
| `operating_hours` | unknown | ❌ | Operating hours configuration by day of week |
| `multi_language_enabled` | unknown | ❌ | Enable multi-language support |
| `available_languages` | unknown | ❌ | List of available language codes |
| `default_language` | unknown | ❌ | Default language code |
| `loyalty_enabled` | unknown | ❌ | Enable loyalty system |
| `loyalty_config` | unknown | ❌ | Loyalty system configuration |
| `country` | unknown | ❌ | Country code (ISO 3166-1 alpha-2) |
| `address` | unknown | ❌ | Complete address with coordinates |
| `base_tax_rate` | unknown | ❌ | Base tax rate percentage |
| `tax_calculation_method` | unknown | ❌ | Tax calculation method: 'incremental' or 'inclusive' |
| `wifi_networks` | unknown | ❌ | WiFi networks configuration by zone |
| `social_media_links` | unknown | ❌ | Social media platform links with icons |
| `subscription_plan` | unknown | ❌ | Current subscription plan |
| `subscription_status` | unknown | ❌ | Subscription status |
| `additional_settings` | unknown | ❌ | Additional configuration settings |

### TenantUserAssociationRead

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `role` | string | ✅ | - |
| `staff_sub_role` | unknown | ❌ | - |
| `data_sharing_consent` | boolean | ❌ | - |
| `user_id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `user` | User | ✅ | - |
| `tenant` | Tenant | ✅ | - |

## 🔗 Endpoints Detalhados

### GET /api/tenants/ {#get-apitenants}

**Resumo:** Read Tenants
**Descrição:** Retrieves a list of active tenants.
Requires authentication. More granular permissions can be added.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/tenants/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/tenants/ {#post-apitenants}

**Resumo:** Create Tenant
**Descrição:** Creates a new tenant.
Only superusers can create tenants.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TenantCreate](#tenantcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [Tenant](#tenant)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/tenants/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/tenants/admin/audit/tenant/{tenant_id} {#get-apitenantsadminaudittenanttenant-id}

**Resumo:** Admin: Get tenant audit log
**Descrição:** Get audit log for a specific tenant

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_id` | string | path | ✅ | - |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/tenants/admin/audit/tenant/{tenant_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/tenants/admin/tenant/{tenant_id}/action {#post-apitenantsadmintenanttenant-idaction}

**Resumo:** Admin: Perform action on tenant
**Descrição:** Perform administrative actions on a tenant with full audit logging

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** {'type': 'object', 'additionalProperties': True, 'title': 'Action Data'}

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/tenants/admin/tenant/{tenant_id}/action" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/tenants/admin/tenant/{tenant_id}/dashboard-data {#get-apitenantsadmintenanttenant-iddashboard-data}

**Resumo:** Admin: Get tenant dashboard data
**Descrição:** Get comprehensive dashboard data for a tenant (admin view)

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/tenants/admin/tenant/{tenant_id}/dashboard-data" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/tenants/admin/tenant/{tenant_id}/view {#get-apitenantsadmintenanttenant-idview}

**Resumo:** Admin: View any tenant
**Descrição:** Allows system administrators to view any tenant for admin purposes

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [Tenant](#tenant)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/tenants/admin/tenant/{tenant_id}/view" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/tenants/settings/ {#get-apitenantssettings}

**Resumo:** Get Tenant Settings
**Descrição:** Get current tenant settings configuration.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/tenants/settings/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/tenants/settings/ {#put-apitenantssettings}

**Resumo:** Update Tenant Settings
**Descrição:** Update tenant settings configuration.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TenantSettingsUpdate](#tenantsettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/tenants/settings/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/tenants/settings/business/ {#put-apitenantssettingsbusiness}

**Resumo:** Update Business Settings
**Descrição:** Update business information and currency settings.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [BusinessSettingsUpdate](#businesssettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/tenants/settings/business/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/tenants/settings/categories/zones {#get-apitenantssettingscategorieszones}

**Resumo:** Get Restaurant Zones
**Descrição:** Get all zones/categories used in the restaurant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/tenants/settings/categories/zones" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/tenants/settings/categories/zones {#post-apitenantssettingscategorieszones}

**Resumo:** Create Custom Zone
**Descrição:** Create a new custom zone/category.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `zone_name` | string | query | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**201:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/tenants/settings/categories/zones" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### DELETE /api/tenants/settings/categories/zones/{zone_name} {#delete-apitenantssettingscategorieszoneszone-name}

**Resumo:** Delete Custom Zone
**Descrição:** Delete a custom zone/category (only if it has no tables).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `zone_name` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/tenants/settings/categories/zones/{zone_name}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/tenants/settings/currency/ {#put-apitenantssettingscurrency}

**Resumo:** Update Currency Settings
**Descrição:** Update multi-currency configuration.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [CurrencySettingsUpdate](#currencysettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/tenants/settings/currency/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/tenants/settings/languages/ {#put-apitenantssettingslanguages}

**Resumo:** Update Language Settings
**Descrição:** Update multi-language configuration.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [LanguageSettingsUpdate](#languagesettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/tenants/settings/languages/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/tenants/settings/location/ {#put-apitenantssettingslocation}

**Resumo:** Update Location Settings
**Descrição:** Update location and address configuration.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [LocationSettingsUpdate](#locationsettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/tenants/settings/location/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/tenants/settings/loyalty/ {#put-apitenantssettingsloyalty}

**Resumo:** Update Loyalty Settings
**Descrição:** Update loyalty system configuration.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [LoyaltySettingsUpdate](#loyaltysettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/tenants/settings/loyalty/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/tenants/settings/operating-hours/ {#put-apitenantssettingsoperating-hours}

**Resumo:** Update Operating Hours
**Descrição:** Update operating hours configuration.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [OperatingHoursUpdate](#operatinghoursupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/tenants/settings/operating-hours/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/tenants/settings/social-media/ {#put-apitenantssettingssocial-media}

**Resumo:** Update Social Media Settings
**Descrição:** Update social media links configuration.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [SocialMediaSettingsUpdate](#socialmediasettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/tenants/settings/social-media/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/tenants/settings/sync-tenant-data {#post-apitenantssettingssync-tenant-data}

**Resumo:** Sync Tenant Data
**Descrição:** Manually sync tenant data from Tenant model to TenantSettings.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/tenants/settings/sync-tenant-data" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/tenants/settings/tax/ {#put-apitenantssettingstax}

**Resumo:** Update Tax Settings
**Descrição:** Update tax configuration settings.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TaxSettingsUpdate](#taxsettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/tenants/settings/tax/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/tenants/tenant-settings/ {#get-apitenantstenant-settings}

**Resumo:** Get Tenant Settings
**Descrição:** Get current tenant settings configuration.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/tenants/tenant-settings/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/tenants/tenant-settings/ {#put-apitenantstenant-settings}

**Resumo:** Update Tenant Settings
**Descrição:** Update tenant settings configuration.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TenantSettingsUpdate](#tenantsettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/tenants/tenant-settings/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/tenants/tenant-settings/business/ {#put-apitenantstenant-settingsbusiness}

**Resumo:** Update Business Settings
**Descrição:** Update business information and currency settings.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [BusinessSettingsUpdate](#businesssettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/tenants/tenant-settings/business/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/tenants/tenant-settings/categories/zones {#get-apitenantstenant-settingscategorieszones}

**Resumo:** Get Restaurant Zones
**Descrição:** Get all zones/categories used in the restaurant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/tenants/tenant-settings/categories/zones" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/tenants/tenant-settings/categories/zones {#post-apitenantstenant-settingscategorieszones}

**Resumo:** Create Custom Zone
**Descrição:** Create a new custom zone/category.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `zone_name` | string | query | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**201:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/tenants/tenant-settings/categories/zones" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### DELETE /api/tenants/tenant-settings/categories/zones/{zone_name} {#delete-apitenantstenant-settingscategorieszoneszone-name}

**Resumo:** Delete Custom Zone
**Descrição:** Delete a custom zone/category (only if it has no tables).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `zone_name` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/tenants/tenant-settings/categories/zones/{zone_name}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/tenants/tenant-settings/currency/ {#put-apitenantstenant-settingscurrency}

**Resumo:** Update Currency Settings
**Descrição:** Update multi-currency configuration.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [CurrencySettingsUpdate](#currencysettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/tenants/tenant-settings/currency/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/tenants/tenant-settings/languages/ {#put-apitenantstenant-settingslanguages}

**Resumo:** Update Language Settings
**Descrição:** Update multi-language configuration.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [LanguageSettingsUpdate](#languagesettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/tenants/tenant-settings/languages/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/tenants/tenant-settings/location/ {#put-apitenantstenant-settingslocation}

**Resumo:** Update Location Settings
**Descrição:** Update location and address configuration.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [LocationSettingsUpdate](#locationsettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/tenants/tenant-settings/location/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/tenants/tenant-settings/loyalty/ {#put-apitenantstenant-settingsloyalty}

**Resumo:** Update Loyalty Settings
**Descrição:** Update loyalty system configuration.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [LoyaltySettingsUpdate](#loyaltysettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/tenants/tenant-settings/loyalty/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/tenants/tenant-settings/operating-hours/ {#put-apitenantstenant-settingsoperating-hours}

**Resumo:** Update Operating Hours
**Descrição:** Update operating hours configuration.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [OperatingHoursUpdate](#operatinghoursupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/tenants/tenant-settings/operating-hours/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/tenants/tenant-settings/social-media/ {#put-apitenantstenant-settingssocial-media}

**Resumo:** Update Social Media Settings
**Descrição:** Update social media links configuration.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [SocialMediaSettingsUpdate](#socialmediasettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/tenants/tenant-settings/social-media/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/tenants/tenant-settings/sync-tenant-data {#post-apitenantstenant-settingssync-tenant-data}

**Resumo:** Sync Tenant Data
**Descrição:** Manually sync tenant data from Tenant model to TenantSettings.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/tenants/tenant-settings/sync-tenant-data" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/tenants/tenant-settings/tax/ {#put-apitenantstenant-settingstax}

**Resumo:** Update Tax Settings
**Descrição:** Update tax configuration settings.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TaxSettingsUpdate](#taxsettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/tenants/tenant-settings/tax/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/tenants/{tenant_id} {#get-apitenantstenant-id}

**Resumo:** Read Tenant
**Descrição:** Gets details of a specific tenant by ID.
Requires authentication.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [Tenant](#tenant)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/tenants/{tenant_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/tenants/{tenant_id}/users {#get-apitenantstenant-idusers}

**Resumo:** Read Tenant Users
**Descrição:** Lists users associated with a specific tenant.
Requires that the authenticated user has at least the STAFF role in the tenant specified in the path.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/tenants/{tenant_id}/users" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/tenants/{tenant_id}/users {#post-apitenantstenant-idusers}

**Resumo:** Add User To Tenant
**Descrição:** Adds an existing user to a specific tenant with a role.
Requires that the authenticated user has the ADMIN or MANAGER role in the tenant specified in the path.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [AddUserToTenantPayload](#addusertotenantpayload)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantUserAssociationRead](#tenantuserassociationread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/tenants/{tenant_id}/users" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
