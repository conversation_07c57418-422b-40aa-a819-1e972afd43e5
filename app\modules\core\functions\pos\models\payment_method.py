"""Payment method models for POS module."""

import uuid  # noqa: E402
import enum
from typing import TYPE_CHECKING, List, Optional
from sqlalchemy import Column, String, <PERSON>olean, Foreign<PERSON>ey, JSON, Enum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base  # noqa: E402
from app.modules.core.functions.pos.models.sale_transaction import SaleTransaction

# Use TYPE_CHECKING to handle circular dependencies for type hinting
if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant


class PaymentMethodType(str, enum.Enum):
    """Enum for payment method types."""

    CASH = "cash"
    CREDIT_CARD = "credit_card"
    DEBIT_CARD = "debit_card"
    BANK_TRANSFER = "bank_transfer"
    MOBILE_PAYMENT = "mobile_payment"
    STORE_CREDIT = "store_credit"
    GIFT_CARD = "gift_card"
    CHECK = "check"
    CRYPTO = "crypto"
    OTHER = "other"


class POSPaymentMethod(Base):
    """
    Model for payment methods available to tenants in the POS system.

    This represents a payment method that can be used in POS transactions.
    """

    __tablename__ = "pos_payment_methods"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)

    name = Column(String, nullable=False)
    method_type = Column(Enum(PaymentMethodType), nullable=False)
    is_active = Column(Boolean, default=True)

    # For integration with payment processors
    processor_id = Column(String, nullable=True)  # ID in the payment processor system
    processor_config = Column(JSON, nullable=True)  # Configuration for the payment processor

    # Relationships
    tenant = relationship("app.modules.core.tenants.models.tenant.Tenant", viewonly=True)
    transaction_payments = relationship(
        "TransactionPayment", back_populates="payment_method", cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<POSPaymentMethod(id={self.id}, name='{self.name}', type='{self.method_type}')>"


class TransactionPayment(Base):
    """
    Model for tracking payments in a transaction.

    This allows for split payments across multiple payment methods.
    """

    __tablename__ = "transaction_payments"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    transaction_id = Column(
        UUID(as_uuid=True),
        ForeignKey("sale_transactions.id"),
        nullable=False,
        index=True,
    )
    payment_method_id = Column(
        UUID(as_uuid=True), ForeignKey("pos_payment_methods.id"), nullable=False
    )
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)

    amount = Column(String, nullable=False)  # Amount paid with this method
    reference = Column(String, nullable=True)  # Reference number, authorization code, etc.
    details = Column(JSON, nullable=True)  # Additional payment details

    # Relationships
    transaction = relationship(
        "app.modules.core.functions.pos.models.sale_transaction.SaleTransaction", back_populates="payments"
    )
    payment_method = relationship(
        "app.modules.core.functions.pos.models.payment_method.POSPaymentMethod",
        back_populates="transaction_payments",
    )
    tenant = relationship("app.modules.core.tenants.models.tenant.Tenant")

    def __repr__(self):
        return f"<TransactionPayment(id={self.id}, transaction_id={self.transaction_id}, method_id={self.payment_method_id}, amount={self.amount})>"  # noqa: E501
