'use client';

import React, { useMemo } from 'react';
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { usePOSStore } from '@/stores/posStore';
import CategorySelector from './CategorySelector';
import MenuItemGrid from './MenuItemGrid';
import SearchBar from './SearchBar';

interface MenuPanelProps {
  onInputFocus?: () => void;
  onInputBlur?: () => void;
}

/**
 * Panel esquerdo do POS - Navegação do menu e seleção de produtos
 * Touch-friendly com categorias e grid de produtos
 */
export default function MenuPanel({ onInputFocus, onInputBlur }: MenuPanelProps) {
  const {
    categories,
    menuItems,
    activeCategory,
    searchQuery,
    setActiveCategory,
    setSearchQuery,
    isLoading
  } = usePOSStore();

  // Filter items based on category and search
  const filteredItems = useMemo(() => {
    let filtered = menuItems;

    // Filter by category
    if (activeCategory !== 'all') {
      filtered = filtered.filter(item => item.category_id === activeCategory);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(item => 
        item.name.toLowerCase().includes(query) ||
        item.description?.toLowerCase().includes(query)
      );
    }

    // Only show available items
    return filtered.filter(item => item.is_available);
  }, [menuItems, activeCategory, searchQuery]);

  // Get category with "All" option
  const categoriesWithAll = useMemo(() => {
    const allCategory = {
      id: 'all',
      name: 'Todos',
      description: 'Todos os produtos',
      display_order: 0,
      is_active: true,
      parent_id: null
    };

    return [allCategory, ...categories.filter(cat => cat.is_active)];
  }, [categories]);

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-2"></div>
          <p className="text-gray-600 text-sm">Carregando menu...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Search Bar */}
      <div className="p-4 border-b border-gray-200">
        <SearchBar
          value={searchQuery}
          onChange={setSearchQuery}
          onFocus={onInputFocus}
          onBlur={onInputBlur}
          placeholder="Buscar produtos..."
        />
      </div>

      {/* Categories */}
      <div className="border-b border-gray-200 bg-gray-50">
        <CategorySelector
          categories={categoriesWithAll}
          activeCategory={activeCategory}
          onCategorySelect={setActiveCategory}
        />
      </div>

      {/* Menu Items Grid */}
      <div className="flex-1 overflow-y-auto">
        {filteredItems.length === 0 ? (
          <div className="h-full flex items-center justify-center">
            <div className="text-center text-gray-500">
              {searchQuery ? (
                <>
                  <MagnifyingGlassIcon className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                  <p className="text-lg font-medium mb-1">Nenhum produto encontrado</p>
                  <p className="text-sm">Tente buscar por outro termo</p>
                </>
              ) : (
                <>
                  <div className="h-12 w-12 mx-auto mb-3 bg-gray-200 rounded-lg flex items-center justify-center">
                    <span className="text-gray-400 text-xl">📋</span>
                  </div>
                  <p className="text-lg font-medium mb-1">Nenhum produto disponível</p>
                  <p className="text-sm">Esta categoria está vazia</p>
                </>
              )}
            </div>
          </div>
        ) : (
          <MenuItemGrid 
            items={filteredItems}
            searchQuery={searchQuery}
          />
        )}
      </div>

      {/* Footer with item count */}
      <div className="px-4 py-2 bg-gray-50 border-t border-gray-200">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>
            {filteredItems.length} produto{filteredItems.length !== 1 ? 's' : ''}
            {searchQuery && ` encontrado${filteredItems.length !== 1 ? 's' : ''}`}
          </span>
          
          {activeCategory !== 'all' && (
            <button
              onClick={() => setActiveCategory('all')}
              className="text-primary-600 hover:text-primary-700 font-medium"
            >
              Ver todos
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
