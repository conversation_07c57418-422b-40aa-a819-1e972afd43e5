import uuid
from sqlalchemy import (
    Column,
    String,
    <PERSON>olean,
    Integer,
    Numeric,
    ForeignKey,
    Index,
    DateTime,
    Text,
    Enum as SQLEnum
)
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from enum import Enum
from typing import TYPE_CHECKING

from app.db.base import Base

if TYPE_CHECKING:
    from app.models.tenant import Tenant
    from app.modules.shared.inventory.models.inventory_item import InventoryItem


class SupplierStatus(str, Enum):
    """Status do fornecedor."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"


class PriorityLevel(str, Enum):
    """Nível de prioridade do fornecedor."""
    PRIMARY = "primary"
    SECONDARY = "secondary"
    BACKUP = "backup"


class Supplier(Base):
    """Modelo para fornecedores."""
    __tablename__ = "suppliers"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False)
    
    # Supplier basic info
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    contact_email = Column(String, nullable=True)
    contact_phone = Column(String, nullable=True)
    address = Column(Text, nullable=True)
    
    # Business info
    tax_id = Column(String, nullable=True)  # CNPJ/CPF
    website = Column(String, nullable=True)
    
    # Status and settings
    status = Column(SQLEnum(SupplierStatus), nullable=False, default=SupplierStatus.ACTIVE)
    auto_replenishment_enabled = Column(Boolean, default=False, nullable=False)
    competitive_pricing_visible = Column(Boolean, default=True, nullable=False)
    
    # Relationships
    tenant = relationship("Tenant")
    product_suppliers = relationship("ProductSupplier", back_populates="supplier")
    price_history = relationship("SupplierPriceHistory", back_populates="supplier")
    # purchase_orders relationship will be added later to avoid circular imports

    __table_args__ = (
        Index("ix_suppliers_tenant_id", "tenant_id"),
        Index("ix_suppliers_status", "status"),
        Index("ix_suppliers_name", "name"),
    )


class ProductSupplier(Base):
    """Associação entre produtos e fornecedores com prioridade."""
    __tablename__ = "product_suppliers"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False)
    inventory_item_id = Column(UUID(as_uuid=True), 
                              ForeignKey("inventory_items.id"), nullable=False)
    supplier_id = Column(UUID(as_uuid=True), 
                         ForeignKey("suppliers.id"), nullable=False)
    
    # Priority and settings
    priority_level = Column(SQLEnum(PriorityLevel), 
                           nullable=False, default=PriorityLevel.SECONDARY)
    priority_order = Column(Integer, nullable=False, default=1)
    
    # Product-specific info for this supplier
    supplier_product_code = Column(String, nullable=True)
    supplier_product_name = Column(String, nullable=True)
    current_price = Column(Numeric(10, 2), nullable=True)
    minimum_order_quantity = Column(Integer, nullable=True, default=1)
    lead_time_days = Column(Integer, nullable=True, default=1)
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    auto_replenishment_authorized = Column(Boolean, default=False, nullable=False)
    
    # Relationships
    tenant = relationship("Tenant")
    inventory_item = relationship("InventoryItem")
    supplier = relationship("Supplier", back_populates="product_suppliers")
    price_history = relationship("SupplierPriceHistory", back_populates="product_supplier")

    __table_args__ = (
        Index("ix_product_suppliers_tenant_id", "tenant_id"),
        Index("ix_product_suppliers_inventory_item_id", "inventory_item_id"),
        Index("ix_product_suppliers_supplier_id", "supplier_id"),
        Index("ix_product_suppliers_priority", "priority_level", "priority_order"),
    )


class SupplierPriceHistory(Base):
    """Histórico de preços por fornecedor e produto."""
    __tablename__ = "supplier_price_history"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False)
    product_supplier_id = Column(UUID(as_uuid=True), 
                                ForeignKey("product_suppliers.id"), nullable=False)
    supplier_id = Column(UUID(as_uuid=True), 
                        ForeignKey("suppliers.id"), nullable=False)
    
    # Price info
    price = Column(Numeric(10, 2), nullable=False)
    previous_price = Column(Numeric(10, 2), nullable=True)
    price_change_percentage = Column(Numeric(5, 2), nullable=True)
    
    # Source of price update
    updated_by_supplier = Column(Boolean, default=False, nullable=False)
    updated_by_user_id = Column(UUID(as_uuid=True), nullable=True)
    
    # Additional info
    notes = Column(Text, nullable=True)
    effective_date = Column(DateTime, nullable=True)
    
    # Relationships
    tenant = relationship("Tenant")
    product_supplier = relationship("ProductSupplier", back_populates="price_history")
    supplier = relationship("Supplier", back_populates="price_history")

    __table_args__ = (
        Index("ix_supplier_price_history_tenant_id", "tenant_id"),
        Index("ix_supplier_price_history_product_supplier_id", "product_supplier_id"),
        Index("ix_supplier_price_history_supplier_id", "supplier_id"),
        Index("ix_supplier_price_history_created_at", "created_at"),
    )
