'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/lib/auth/AuthProvider';
import { apiClient } from '@/lib/api/client';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { MessageInput } from './MessageInput';
import { MessageBubble } from './MessageBubble';
import { FileUpload } from './FileUpload';
import { TypingIndicatorManager } from './TypingIndicator';
import { useHelpCenterWebSocket } from '../hooks/useHelpCenterWebSocket';
import { 
  TicketIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  UserIcon,
  CalendarIcon,
  TagIcon
} from '@heroicons/react/24/outline';

interface Ticket {
  id: string;
  title: string;
  description: string;
  status: 'new' | 'open' | 'pending' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'question' | 'incident' | 'problem' | 'request' | 'suggestion';
  created_at: string;
  updated_at?: string;
  resolved_at?: string;
  closed_at?: string;
  is_read_by_admin: boolean;
  is_read_by_user: boolean;
  user_name?: string;
  user_email?: string;
  assigned_admin_name?: string;
  message_count?: number;
}

interface TicketMessage {
  id: string;
  ticket_id: string;
  sender_id: string;
  message_content: string;
  message_type: 'text' | 'image' | 'file';
  file_path?: string;
  file_name?: string;
  file_size?: number;
  mime_type?: string;
  is_read: boolean;
  created_at: string;
  sender_name?: string;
  sender_email?: string;
  is_admin?: boolean;
}

interface TicketDetailProps {
  ticket: Ticket;
  onTicketUpdate: (ticket: Ticket) => void;
}

export function TicketDetail({ ticket, onTicketUpdate }: TicketDetailProps) {
  const { user, isAdmin } = useAuth();
  const [messages, setMessages] = useState<TicketMessage[]>([]);
  const [isLoadingMessages, setIsLoadingMessages] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const handleNewMessage = useCallback((message: TicketMessage) => {
    setMessages(prev => [...prev, message]);
  }, []);

  const handleTicketUpdate = useCallback((updatedTicket: any) => {
    onTicketUpdate(updatedTicket);
  }, [onTicketUpdate]);

  const handleTypingUpdate = useCallback((typing: any) => {
    // Typing updates are handled by TypingIndicatorManager
  }, []);

  const handleReadReceipt = useCallback((receipt: any) => {
    setMessages(prev =>
      prev.map(msg =>
        msg.id === receipt.message_id
          ? { ...msg, is_read: true }
          : msg
      )
    );
  }, []);

  const { isConnected, markMessageAsRead } = useHelpCenterWebSocket({
    ticketId: ticket.id,
    onNewMessage: handleNewMessage,
    onTicketUpdate: handleTicketUpdate,
    onTypingUpdate: handleTypingUpdate,
    onReadReceipt: handleReadReceipt
  });

  const fetchMessages = useCallback(async () => {
    try {
      setIsLoadingMessages(true);
      setError(null);

      const response = await apiClient.get(
        `/modules/core/help-center/tickets/${ticket.id}/messages`
      );

      setMessages((response as any).messages || []);
    } catch (err: any) {
      console.error('Erro ao carregar mensagens:', err);
      setError('Erro ao carregar mensagens do ticket.');
    } finally {
      setIsLoadingMessages(false);
    }
  }, [ticket.id]);

  useEffect(() => {
    fetchMessages();
  }, [ticket.id, fetchMessages]);

  const handleMessageSent = (message: TicketMessage) => {
    // Message will be added via WebSocket, but we can add it immediately for better UX
    setMessages(prev => [...prev, message]);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'new':
        return <TicketIcon className="h-5 w-5 text-blue-500" />;
      case 'open':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'pending':
        return <ExclamationTriangleIcon className="h-5 w-5 text-orange-500" />;
      case 'resolved':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'closed':
        return <XCircleIcon className="h-5 w-5 text-gray-500" />;
      default:
        return <TicketIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    const statusMap = {
      new: 'Novo',
      open: 'Aberto',
      pending: 'Pendente',
      resolved: 'Resolvido',
      closed: 'Fechado'
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityText = (priority: string) => {
    const priorityMap = {
      urgent: 'Urgente',
      high: 'Alta',
      medium: 'Média',
      low: 'Baixa'
    };
    return priorityMap[priority as keyof typeof priorityMap] || priority;
  };

  const getCategoryText = (category: string) => {
    const categoryMap = {
      question: 'Dúvida',
      incident: 'Incidente',
      problem: 'Problema',
      request: 'Solicitação',
      suggestion: 'Sugestão'
    };
    return categoryMap[category as keyof typeof categoryMap] || category;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-6">
      {/* Ticket Header */}
      <div className="glass rounded-xl p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              {getStatusIcon(ticket.status)}
              <h1 className="text-2xl font-bold text-gray-900">
                {ticket.title}
              </h1>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(ticket.priority)}`}>
                {getPriorityText(ticket.priority)}
              </span>
            </div>
            
            <p className="text-gray-600 mb-4">
              {ticket.description}
            </p>
          </div>
        </div>

        {/* Ticket Metadata */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
          <div className="flex items-center space-x-2">
            <TicketIcon className="h-4 w-4 text-gray-400" />
            <span className="text-gray-600">Status:</span>
            <span className="font-medium">{getStatusText(ticket.status)}</span>
          </div>
          
          <div className="flex items-center space-x-2">
            <TagIcon className="h-4 w-4 text-gray-400" />
            <span className="text-gray-600">Categoria:</span>
            <span className="font-medium">{getCategoryText(ticket.category)}</span>
          </div>
          
          <div className="flex items-center space-x-2">
            <CalendarIcon className="h-4 w-4 text-gray-400" />
            <span className="text-gray-600">Criado:</span>
            <span className="font-medium">{formatDate(ticket.created_at)}</span>
          </div>
          
          {ticket.user_name && (
            <div className="flex items-center space-x-2">
              <UserIcon className="h-4 w-4 text-gray-400" />
              <span className="text-gray-600">Criado por:</span>
              <span className="font-medium">{ticket.user_name}</span>
            </div>
          )}
        </div>

        {ticket.assigned_admin_name && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="flex items-center space-x-2 text-sm">
              <UserIcon className="h-4 w-4 text-blue-400" />
              <span className="text-gray-600">Atribuído a:</span>
              <span className="font-medium text-blue-600">{ticket.assigned_admin_name}</span>
            </div>
          </div>
        )}
      </div>

      {/* Messages Section */}
      <div className="glass rounded-xl">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-medium text-gray-900">
                Conversa
              </h2>
              <p className="text-sm text-gray-600">
                Histórico de mensagens e comunicação
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className="text-xs text-gray-500">
                {isConnected ? 'Conectado' : 'Desconectado'}
              </span>
            </div>
          </div>
        </div>

        <TypingIndicatorManager ticketId={ticket.id}>
          <div className="p-6">
            {isLoadingMessages ? (
              <div className="flex items-center justify-center py-8">
                <LoadingSpinner size="md" />
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <div className="text-red-600 mb-4">{error}</div>
                <button
                  onClick={fetchMessages}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Tentar Novamente
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                {messages.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    Nenhuma mensagem ainda. Seja o primeiro a responder!
                  </div>
                ) : (
                  messages.map((message) => (
                    <MessageBubble
                      key={message.id}
                      message={message}
                      isCurrentUser={message.sender_id === user?.id}
                      onMarkAsRead={() => markMessageAsRead(message.id)}
                    />
                  ))
                )}
              </div>
            )}
          </div>
        </TypingIndicatorManager>

        {/* Message Input */}
        {ticket.status !== 'closed' && (
          <div className="border-t border-gray-200 p-6">
            <MessageInput
              ticketId={ticket.id}
              onMessageSent={handleMessageSent}
            />
          </div>
        )}
      </div>


    </div>
  );
}
