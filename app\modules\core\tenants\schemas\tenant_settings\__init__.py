"""
Schemas for tenant settings operations.
"""

from app.modules.core.tenants.schemas.tenant_settings.base_schemas import (
    TenantSettingsBase,
    TenantSettingsCreate,
    TenantSettingsUpdate,
    TenantSettingsRead,
)
from app.modules.core.tenants.schemas.tenant_settings.business_schemas import (
    BusinessSettingsUpdate,
)
from app.modules.core.tenants.schemas.tenant_settings.operating_hours_schemas import (
    OperatingHoursUpdate,
    TimeSlotSchema,
    DayScheduleSchema,
    OperatingHoursSchema,
)
from app.modules.core.tenants.schemas.tenant_settings.language_schemas import (
    LanguageSettingsUpdate,
)
from app.modules.core.tenants.schemas.tenant_settings.loyalty_schemas import (
    LoyaltySettingsUpdate,
)
from app.modules.core.tenants.schemas.tenant_settings.location_schemas import (
    LocationSettingsUpdate,
    AddressSchema,
    AddressUpdateSchema,
)
from app.modules.core.tenants.schemas.tenant_settings.tax_schemas import (
    TaxSettingsUpdate,
)
from app.modules.core.tenants.schemas.tenant_settings.currency_schemas import (
    CurrencySettingsUpdate,
)
from app.modules.core.tenants.schemas.tenant_settings.social_media_schemas import (
    SocialMediaSettingsUpdate,
    SocialMediaLinkSchema,
)

__all__ = [
    # Base schemas
    "TenantSettingsBase",
    "TenantSettingsCreate",
    "TenantSettingsUpdate",
    "TenantSettingsRead",
    # Specific update schemas
    "BusinessSettingsUpdate",
    "OperatingHoursUpdate",
    "LanguageSettingsUpdate",
    "LoyaltySettingsUpdate",
    "LocationSettingsUpdate",
    "TaxSettingsUpdate",
    "CurrencySettingsUpdate",
    "SocialMediaSettingsUpdate",
    # Component schemas
    "TimeSlotSchema",
    "DayScheduleSchema",
    "OperatingHoursSchema",
    "AddressSchema",
    "AddressUpdateSchema",
    "SocialMediaLinkSchema",
]
