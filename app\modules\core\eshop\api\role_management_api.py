"""
EShop Role Management API - B2B Authorization and Context Management.

Provides REST endpoints for managing TVendor and TCostumer roles,
authorization workflows, and market context switching.
"""

import uuid
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.auth_dependencies import get_current_active_user
from app.modules.core.users.models.user import User
from app.modules.core.eshop.services.role_service import RoleManagementService
# Temporarily commented out to resolve circular import
# from app.modules.core.eshop.middleware.market_context_middleware import (
#     get_market_context, require_b2b_context, require_vendor_role, 
#     require_customer_role, get_pricing_tier, get_commission_rate
# )
from app.modules.core.roles.models.roles import TenantRole, MarketContext
from app.core.exceptions import ValidationError, NotFoundError, AuthorizationError

# Temporary placeholder functions for middleware dependencies
def get_market_context(request: Request) -> str:
    return "b2c"

def require_b2b_context(request: Request) -> None:
    pass

def require_vendor_role(request: Request) -> None:
    pass

def require_customer_role(request: Request) -> None:
    pass

def get_pricing_tier(request: Request) -> Optional[str]:
    return None

def get_commission_rate(request: Request) -> Optional[float]:
    return None

router = APIRouter(prefix="/api/v1/eshop/roles", tags=["EShop Role Management"])


# =====================================================================
# PYDANTIC SCHEMAS
# =====================================================================

class B2BAuthorizationRequest(BaseModel):
    """Request schema for B2B role authorization."""
    
    requested_role: str = Field(..., description="Requested B2B role (tvendor or tcostumer)")
    business_name: str = Field(..., description="Business name")
    business_registration_number: str = Field(..., description="Business registration number")
    requester_notes: Optional[str] = Field(None, description="Additional notes from requester")


class B2BApprovalRequest(BaseModel):
    """Request schema for B2B authorization approval."""
    
    commission_rate: Optional[float] = Field(None, description="Commission rate for vendors (percentage)")
    pricing_tier: Optional[str] = Field(None, description="Pricing tier for customers")
    approval_notes: Optional[str] = Field(None, description="Approval notes")


class B2BRejectionRequest(BaseModel):
    """Request schema for B2B authorization rejection."""
    
    rejection_reason: str = Field(..., description="Reason for rejection")


class MarketContextSwitchRequest(BaseModel):
    """Request schema for market context switching."""
    
    new_context: str = Field(..., description="New market context (b2b, b2c, marketplace)")


class B2BAuthorizationResponse(BaseModel):
    """Response schema for B2B authorization operations."""
    
    association_id: uuid.UUID
    status: str
    message: str
    requested_role: Optional[str] = None
    authorization_date: Optional[str] = None


class RoleInfoResponse(BaseModel):
    """Response schema for user role information."""
    
    association_id: uuid.UUID
    role: str
    market_context: str
    is_b2b_role: bool
    b2b_authorized: bool
    business_verification_status: Optional[str]
    pricing_tier: Optional[str]
    commission_rate: Optional[float]
    permissions: Dict[str, Any]
    created_at: str
    updated_at: str


class PendingRequestResponse(BaseModel):
    """Response schema for pending B2B requests."""
    
    association_id: uuid.UUID
    user_id: uuid.UUID
    requested_role: str
    business_name: str
    business_registration_number: str
    requester_notes: Optional[str]
    created_at: str


# =====================================================================
# B2B AUTHORIZATION ENDPOINTS
# =====================================================================

@router.post("/b2b/request", response_model=B2BAuthorizationResponse)
async def request_b2b_authorization(
    request_data: B2BAuthorizationRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Request B2B authorization for TVENDOR or TCOSTUMER role."""
    try:
        service = RoleManagementService(db)
        
        # Extract tenant ID from user context or headers
        tenant_id = current_user.default_tenant_id
        if not tenant_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No tenant context available"
            )
        
        result = await service.request_b2b_authorization(
            user_id=current_user.id,
            tenant_id=tenant_id,
            requested_role=request_data.requested_role,
            business_data={
                'business_name': request_data.business_name,
                'business_registration_number': request_data.business_registration_number
            },
            requester_notes=request_data.requester_notes
        )
        
        return B2BAuthorizationResponse(**result)
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to request B2B authorization: {str(e)}"
        )


@router.post("/b2b/approve/{association_id}", response_model=B2BAuthorizationResponse)
async def approve_b2b_authorization(
    association_id: uuid.UUID,
    approval_data: B2BApprovalRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Approve B2B authorization request (Admin/Manager only)."""
    try:
        service = RoleManagementService(db)
        
        result = await service.approve_b2b_authorization(
            association_id=association_id,
            approver_user_id=current_user.id,
            approval_data={
                'commission_rate': approval_data.commission_rate,
                'pricing_tier': approval_data.pricing_tier,
                'approval_notes': approval_data.approval_notes
            }
        )
        
        return B2BAuthorizationResponse(**result)
        
    except (NotFoundError, ValidationError, AuthorizationError) as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to approve B2B authorization: {str(e)}"
        )


@router.post("/b2b/reject/{association_id}", response_model=B2BAuthorizationResponse)
async def reject_b2b_authorization(
    association_id: uuid.UUID,
    rejection_data: B2BRejectionRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Reject B2B authorization request (Admin/Manager only)."""
    try:
        service = RoleManagementService(db)
        
        result = await service.reject_b2b_authorization(
            association_id=association_id,
            rejector_user_id=current_user.id,
            rejection_reason=rejection_data.rejection_reason
        )
        
        return B2BAuthorizationResponse(**result)
        
    except (NotFoundError, ValidationError, AuthorizationError) as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to reject B2B authorization: {str(e)}"
        )


@router.get("/b2b/pending", response_model=List[PendingRequestResponse])
async def list_pending_b2b_requests(
    limit: int = 50,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """List pending B2B authorization requests (Admin/Manager only)."""
    try:
        service = RoleManagementService(db)
        
        tenant_id = current_user.default_tenant_id
        if not tenant_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No tenant context available"
            )
        
        requests = service.list_pending_b2b_requests(tenant_id, limit)
        
        return [PendingRequestResponse(**req) for req in requests]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list pending requests: {str(e)}"
        )


# =====================================================================
# MARKET CONTEXT ENDPOINTS
# =====================================================================

@router.post("/context/switch")
async def switch_market_context(
    context_data: MarketContextSwitchRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Switch user's market context (B2B/B2C/Marketplace)."""
    try:
        service = RoleManagementService(db)
        
        tenant_id = current_user.default_tenant_id
        if not tenant_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No tenant context available"
            )
        
        result = await service.switch_market_context(
            user_id=current_user.id,
            tenant_id=tenant_id,
            new_context=context_data.new_context
        )
        
        return result
        
    except (ValidationError, AuthorizationError) as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to switch market context: {str(e)}"
        )


@router.get("/context/current")
async def get_current_context(
    request: Request,
    market_context: str = Depends(get_market_context),
    pricing_tier: Optional[str] = Depends(get_pricing_tier),
    commission_rate: Optional[float] = Depends(get_commission_rate)
):
    """Get current market context and user information."""
    return {
        'market_context': market_context,
        'pricing_tier': pricing_tier,
        'commission_rate': commission_rate,
        'user_role': getattr(request.state, 'user_role', None),
        'b2b_authorized': getattr(request.state, 'b2b_authorized', False)
    }


# =====================================================================
# ROLE INFORMATION ENDPOINTS
# =====================================================================

@router.get("/info", response_model=RoleInfoResponse)
async def get_role_info(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get comprehensive role information for current user."""
    try:
        service = RoleManagementService(db)
        
        tenant_id = current_user.default_tenant_id
        if not tenant_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No tenant context available"
            )
        
        info = service.get_user_role_info(
            user_id=current_user.id,
            tenant_id=tenant_id
        )
        
        return RoleInfoResponse(**info)
        
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get role information: {str(e)}"
        )


# =====================================================================
# B2B SPECIFIC ENDPOINTS
# =====================================================================

@router.get("/vendor/dashboard")
async def get_vendor_dashboard(
    request: Request,
    _: None = Depends(require_vendor_role),
    commission_rate: Optional[float] = Depends(get_commission_rate),
    db: Session = Depends(get_db)
):
    """Get vendor-specific dashboard information."""
    return {
        'role': 'tvendor',
        'market_context': 'b2b',
        'commission_rate': commission_rate,
        'permissions': [
            'manage_products',
            'view_analytics', 
            'manage_inventory',
            'process_orders',
            'set_pricing',
            'bulk_operations'
        ],
        'message': 'Welcome to the B2B Vendor Dashboard'
    }


@router.get("/customer/dashboard")
async def get_customer_dashboard(
    request: Request,
    _: None = Depends(require_customer_role),
    pricing_tier: Optional[str] = Depends(get_pricing_tier),
    db: Session = Depends(get_db)
):
    """Get B2B customer-specific dashboard information."""
    return {
        'role': 'tcostumer',
        'market_context': 'b2b',
        'pricing_tier': pricing_tier,
        'permissions': [
            'bulk_purchase',
            'view_b2b_catalog',
            'request_quotes',
            'manage_purchase_orders',
            'view_purchase_history',
            'negotiate_terms'
        ],
        'message': 'Welcome to the B2B Customer Dashboard'
    }


# =====================================================================
# HEALTH CHECK ENDPOINT
# =====================================================================

@router.get("/health")
async def role_management_health_check():
    """Health check for role management services."""
    return {
        'status': 'healthy',
        'supported_roles': [role.value for role in TenantRole],
        'market_contexts': [ctx.value for ctx in MarketContext],
        'b2b_roles': ['tvendor', 'tcostumer'],
        'timestamp': '2025-01-23T12:00:00Z'
    } 