"""Schemas for payment transactions."""

import uuid  # noqa: E402
from typing import Optional, Dict, Any, List
from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel, ConfigDict, Field, EmailStr

from app.modules.core.payments.models.payment_transaction import PaymentStatus  # noqa: E402

# --- PaymentTransaction Schemas ---

# Base schema for payment transactions


class PaymentTransactionBase(BaseModel):
    """Base schema for payment transactions."""

    amount: Decimal = Field(..., max_digits=10, decimal_places=2, gt=0)
    currency: str = Field("BRL", min_length=3, max_length=3)
    status: PaymentStatus = PaymentStatus.PENDING
    source_type: str
    source_id: Optional[uuid.UUID] = None
    external_id: Optional[str] = None
    external_reference: Optional[str] = None
    customer_id: Optional[uuid.UUID] = None
    customer_email: Optional[EmailStr] = None
    customer_name: Optional[str] = None
    description: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


# Schema for creating a new payment transaction


class PaymentTransactionCreate(PaymentTransactionBase):
    """Schema for creating a new payment transaction."""

    processor_id: Optional[uuid.UUID] = None
    method_id: Optional[uuid.UUID] = None


# Schema for updating an existing payment transaction


class PaymentTransactionUpdate(BaseModel):
    """Schema for updating an existing payment transaction."""

    status: Optional[PaymentStatus] = None
    external_id: Optional[str] = None
    external_reference: Optional[str] = None
    processed_at: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None


# Schema for reading a payment transaction


class PaymentTransactionRead(PaymentTransactionBase):
    """Schema for reading a payment transaction."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    processor_id: Optional[uuid.UUID] = None
    method_id: Optional[uuid.UUID] = None
    created_at: datetime
    updated_at: datetime
    processed_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


# --- PaymentRefund Schemas ---

# Base schema for payment refunds


class PaymentRefundBase(BaseModel):
    """Base schema for payment refunds."""

    amount: Decimal = Field(..., max_digits=10, decimal_places=2, gt=0)
    reason: Optional[str] = None
    status: PaymentStatus = PaymentStatus.PENDING
    external_id: Optional[str] = None
    external_reference: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


# Schema for creating a new payment refund


class PaymentRefundCreate(PaymentRefundBase):
    """Schema for creating a new payment refund."""


# Schema for updating an existing payment refund


class PaymentRefundUpdate(BaseModel):
    """Schema for updating an existing payment refund."""

    status: Optional[PaymentStatus] = None
    external_id: Optional[str] = None
    external_reference: Optional[str] = None
    processed_at: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None


# Schema for reading a payment refund


class PaymentRefundRead(PaymentRefundBase):
    """Schema for reading a payment refund."""

    id: uuid.UUID
    transaction_id: uuid.UUID
    tenant_id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    processed_at: Optional[datetime] = None
    created_by: Optional[uuid.UUID] = None

    model_config = ConfigDict(from_attributes=True)


# Schema for reading a payment transaction with refunds


class PaymentTransactionWithRefundsRead(PaymentTransactionRead):
    """Schema for reading a payment transaction with refunds."""

    refunds: List[PaymentRefundRead] = []
    refunded_amount: Optional[Decimal] = None
    remaining_amount: Optional[Decimal] = None

    model_config = ConfigDict(from_attributes=True)
