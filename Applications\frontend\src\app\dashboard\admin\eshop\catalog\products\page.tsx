export default function EShopCatalogProductsPage() {
'use client';

import { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  PlusCircle, 
  Edit, 
  Trash2, 
  Search, 
  Filter,
  Eye,
  EyeOff,
  Package,
  DollarSign,
  Star
} from "lucide-react";
import { toast } from 'sonner';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ProductDialog } from "./components/ProductDialog";
import { 
  useUniversalEshopProducts, 
  EshopProduct,
  MarketType 
} from "@/hooks/useUniversalEshopProducts";
import { useUniversalEshopCategories } from "@/hooks/useUniversalEshopCategories";
import { LoadingSpinner } from "@/components/ui/LoadingSpinner";
import { MarketTypeSelector, MarketTypeBadge } from "@/components/eshop/MarketTypeSelector";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function EShopCatalogProductsPage() {
    const [selectedMarketType, setSelectedMarketType] = useState<MarketType>('ALL');
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(20);
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedCategory, setSelectedCategory] = useState<string>('');
    const [sortBy, setSortBy] = useState<string>('created_at_desc');
    
    const { 
        products, 
        total, 
        pages, 
        isLoading, 
        isError, 
        deleteProduct,
        toggleProductStatus 
    } = useUniversalEshopProducts(
        selectedMarketType,
        currentPage,
        pageSize,
        searchTerm,
        selectedCategory,
        sortBy
    );
    
    const { categories } = useUniversalEshopCategories('ALL');

    // Flatten categories for select options
    const flattenCategories = (cats: any[], level = 0): any[] => {
        let flatList: any[] = [];
        for (const cat of cats) {
            flatList.push({ ...cat, level });
            if (cat.children && cat.children.length > 0) {
                flatList = flatList.concat(flattenCategories(cat.children, level + 1));
            }
        }
        return flatList;
    };

    const flatCategories = flattenCategories(categories);

    const handleDeleteProduct = async (productId: string, productName: string) => {
        if (window.confirm(`Tem certeza que deseja excluir o produto "${productName}"? Esta ação não pode ser desfeita.`)) {
            try {
                await deleteProduct(productId);
                toast.success('Produto excluído com sucesso!');
            } catch (error: any) {
                console.error('Erro ao excluir produto:', error);
                let errorMessage = 'Erro ao excluir produto';
                if (error?.response?.data?.detail) {
                    errorMessage = error.response.data.detail;
                }
                toast.error(errorMessage);
            }
        }
    };

    const handleToggleStatus = async (productId: string, currentStatus: boolean) => {
        try {
            await toggleProductStatus(productId, !currentStatus);
            toast.success(`Produto ${!currentStatus ? 'ativado' : 'desativado'} com sucesso!`);
        } catch (error: any) {
            console.error('Erro ao alterar status do produto:', error);
            let errorMessage = 'Erro ao alterar status do produto';
            if (error?.response?.data?.detail) {
                errorMessage = error.response.data.detail;
            }
            toast.error(errorMessage);
        }
    };

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL'
        }).format(price);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('pt-BR');
    };

    const getMarketTypeBadge = (marketType: string) => {
        return marketType === 'public' ? (
            <Badge variant="default" className="bg-blue-100 text-blue-800">
                EShop
            </Badge>
        ) : (
            <Badge variant="default" className="bg-green-100 text-green-800">
                TShop
            </Badge>
        );
    };

    const getStatusBadge = (isActive: boolean) => {
        return isActive ? (
            <Badge variant="default" className="bg-green-100 text-green-800">
                Ativo
            </Badge>
        ) : (
            <Badge variant="secondary" className="bg-red-100 text-red-800">
                Inativo
            </Badge>
        );
    };

    const renderProductRows = () => {
        if (isLoading) {
            return (
                <TableRow>
                    <TableCell colSpan={9} className="h-24 text-center">
                        <LoadingSpinner />
                    </TableCell>
                </TableRow>
            );
        }

        if (isError) {
            return (
                <TableRow>
                    <TableCell colSpan={9} className="text-center text-red-500">
                        Erro ao carregar os produtos. Tente novamente.
                    </TableCell>
                </TableRow>
            );
        }

        if (products.length === 0) {
            return (
                <TableRow>
                    <TableCell colSpan={9} className="text-center">
                        Nenhum produto encontrado.
                    </TableCell>
                </TableRow>
            );
        }

        return products.map((product) => (
            <TableRow key={product.id}>
                <TableCell className="font-medium">
                    <div className="flex items-center gap-3">
                        {product.images && product.images.length > 0 ? (
                            <img 
                                src={product.images[0]} 
                                alt={product.name}
                                className="w-10 h-10 object-cover rounded"
                            />
                        ) : (
                            <div className="w-10 h-10 bg-gray-200 rounded flex items-center justify-center">
                                <Package className="w-5 h-5 text-gray-400" />
                            </div>
                        )}
                        <div>
                            <div className="font-medium">{product.name}</div>
                            <div className="text-sm text-gray-500">{product.sku || 'Sem SKU'}</div>
                        </div>
                    </div>
                </TableCell>
                <TableCell>{product.category_name || 'N/A'}</TableCell>
                <TableCell>{getMarketTypeBadge(product.market_type)}</TableCell>
                <TableCell>
                    <div className="flex flex-col">
                        <span className="font-medium">{formatPrice(product.price)}</span>
                        {product.original_price && product.original_price > product.price && (
                            <span className="text-sm text-gray-500 line-through">
                                {formatPrice(product.original_price)}
                            </span>
                        )}
                    </div>
                </TableCell>
                <TableCell>
                    <div className="text-center">
                        <span className={`font-medium ${
                            (product.stock_quantity || 0) > 10 
                                ? 'text-green-600' 
                                : (product.stock_quantity || 0) > 0 
                                    ? 'text-yellow-600' 
                                    : 'text-red-600'
                        }`}>
                            {product.stock_quantity || 0}
                        </span>
                    </div>
                </TableCell>
                <TableCell>
                    {product.rating ? (
                        <div className="flex items-center gap-1">
                            <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                            <span className="text-sm">
                                {product.rating.toFixed(1)} ({product.review_count || 0})
                            </span>
                        </div>
                    ) : (
                        <span className="text-gray-400 text-sm">Sem avaliações</span>
                    )}
                </TableCell>
                <TableCell>{getStatusBadge(product.is_active)}</TableCell>
                <TableCell>{formatDate(product.created_at)}</TableCell>
                <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleToggleStatus(product.id, product.is_active)}
                            className={product.is_active ? 'text-red-500 hover:text-red-600' : 'text-green-500 hover:text-green-600'}
                        >
                            {product.is_active ? (
                                <><EyeOff className="h-4 w-4 mr-1" />Desativar</>
                            ) : (
                                <><Eye className="h-4 w-4 mr-1" />Ativar</>
                            )}
                        </Button>
                        <ProductDialog product={product} marketType={selectedMarketType}>
                            <Button variant="ghost" size="sm" className="mr-2">
                                <Edit className="h-4 w-4 mr-1" />
                                Editar
                            </Button>
                        </ProductDialog>
                        <Button 
                            variant="ghost" 
                            size="sm" 
                            className="text-red-500 hover:text-red-600"
                            onClick={() => handleDeleteProduct(product.id, product.name)}
                        >
                            <Trash2 className="h-4 w-4 mr-1" />
                            Excluir
                        </Button>
                    </div>
                </TableCell>
            </TableRow>
        ));
    };

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-2xl font-bold">Gerenciamento de Produtos</h1>
                    <p className="text-muted-foreground">
                        Gerencie todos os produtos do EShop e TShop em um só lugar.
                    </p>
                </div>
                <ProductDialog marketType={selectedMarketType}>
                    <Button>
                        <PlusCircle className="mr-2 h-4 w-4" />
                        Adicionar Produto
                    </Button>
                </ProductDialog>
            </div>
            
            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total de Produtos</CardTitle>
                        <Package className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{total}</div>
                        <p className="text-xs text-muted-foreground">
                            Em todas as categorias
                        </p>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Produtos Ativos</CardTitle>
                        <Eye className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">
                            {products.filter(p => p.is_active).length}
                        </div>
                        <p className="text-xs text-muted-foreground">
                            Visíveis para clientes
                        </p>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Estoque Baixo</CardTitle>
                        <Package className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">
                            {products.filter(p => (p.stock_quantity || 0) <= 5).length}
                        </div>
                        <p className="text-xs text-muted-foreground">
                            Produtos com ≤ 5 unidades
                        </p>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Valor Médio</CardTitle>
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">
                            {products.length > 0 
                                ? formatPrice(products.reduce((acc, p) => acc + p.price, 0) / products.length)
                                : formatPrice(0)
                            }
                        </div>
                        <p className="text-xs text-muted-foreground">
                            Preço médio dos produtos
                        </p>
                    </CardContent>
                </Card>
            </div>

            {/* Filters */}
            <Card>
                <CardHeader>
                    <CardTitle className="text-lg">Filtros</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    {/* Market Type Selector */}
                    <MarketTypeSelector 
                        value={selectedMarketType}
                        onChange={setSelectedMarketType}
                        showAll={true}
                    />
                    
                    {/* Search and Filters Row */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div className="relative">
                            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                            <Input
                                placeholder="Buscar produtos..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="pl-10"
                            />
                        </div>
                        
                        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                            <SelectTrigger>
                                <SelectValue placeholder="Todas as categorias" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="">Todas as categorias</SelectItem>
                                {flatCategories.map((category) => (
                                    <SelectItem key={category.id} value={category.id}>
                                        <span style={{ paddingLeft: `${category.level * 1.5}rem` }}>
                                            {category.level > 0 && '↳ '}
                                            {category.name}
                                        </span>
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        
                        <Select value={sortBy} onValueChange={setSortBy}>
                            <SelectTrigger>
                                <SelectValue placeholder="Ordenar por" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="created_at_desc">Mais recentes</SelectItem>
                                <SelectItem value="created_at_asc">Mais antigos</SelectItem>
                                <SelectItem value="name_asc">Nome (A-Z)</SelectItem>
                                <SelectItem value="name_desc">Nome (Z-A)</SelectItem>
                                <SelectItem value="price_asc">Menor preço</SelectItem>
                                <SelectItem value="price_desc">Maior preço</SelectItem>
                                <SelectItem value="stock_asc">Menor estoque</SelectItem>
                                <SelectItem value="stock_desc">Maior estoque</SelectItem>
                            </SelectContent>
                        </Select>
                        
                        <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(parseInt(value))}>
                            <SelectTrigger>
                                <SelectValue placeholder="Itens por página" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="10">10 por página</SelectItem>
                                <SelectItem value="20">20 por página</SelectItem>
                                <SelectItem value="50">50 por página</SelectItem>
                                <SelectItem value="100">100 por página</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </CardContent>
            </Card>

            {/* Products Table */}
            <div className="border rounded-lg">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-1/4">Produto</TableHead>
                            <TableHead>Categoria</TableHead>
                            <TableHead>Tipo</TableHead>
                            <TableHead>Preço</TableHead>
                            <TableHead className="text-center">Estoque</TableHead>
                            <TableHead>Avaliação</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Criado em</TableHead>
                            <TableHead className="text-right">Ações</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {renderProductRows()}
                    </TableBody>
                </Table>
            </div>

            {/* Pagination */}
            {pages > 1 && (
                <div className="flex items-center justify-between">
                    <div className="text-sm text-muted-foreground">
                        Mostrando {((currentPage - 1) * pageSize) + 1} a {Math.min(currentPage * pageSize, total)} de {total} produtos
                    </div>
                    <div className="flex items-center gap-2">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setCurrentPage(currentPage - 1)}
                            disabled={currentPage === 1}
                        >
                            Anterior
                        </Button>
                        <div className="flex items-center gap-1">
                            {Array.from({ length: Math.min(5, pages) }, (_, i) => {
                                const page = i + 1;
                                return (
                                    <Button
                                        key={page}
                                        variant={currentPage === page ? "default" : "outline"}
                                        size="sm"
                                        onClick={() => setCurrentPage(page)}
                                    >
                                        {page}
                                    </Button>
                                );
                            })}
                        </div>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setCurrentPage(currentPage + 1)}
                            disabled={currentPage === pages}
                        >
                            Próxima
                        </Button>
                    </div>
                </div>
            )}
        </div>
    );
}