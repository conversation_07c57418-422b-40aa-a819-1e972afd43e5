"""WebSocket handlers for Email module."""

import logging  # noqa: E402
import json
import uuid
from typing import Dict, Any, List, Set

from fastapi import WebSocket, WebSocketDisconnect  # noqa: E402
from sqlalchemy.ext.asyncio import AsyncSession

from app.modules.shared.email.models import EmailMetadata  # noqa: E402

logger = logging.getLogger(__name__)

# Store active connections
# Key: user_id, Value: Set of WebSocket connections
active_connections: Dict[uuid.UUID, Set[WebSocket]] = {}


async def register_email_handlers(app):
    """Register WebSocket handlers for Email module."""

    @app.websocket("/ws/email/{user_id}")
    async def websocket_endpoint(websocket: WebSocket, user_id: uuid.UUID):
        await websocket.accept()

        # Add connection to active connections
        if user_id not in active_connections:
            active_connections[user_id] = set()
        active_connections[user_id].add(websocket)

        try:
            while True:
                # Wait for messages (not used for now)
                data = await websocket.receive_text()
                # Process messages if needed
        except WebSocketDisconnect:
            # Remove connection from active connections
            if user_id in active_connections:
                active_connections[user_id].remove(websocket)
                if not active_connections[user_id]:
                    del active_connections[user_id]


async def handle_new_email(user_id: uuid.UUID, email_metadata: EmailMetadata) -> None:
    """Handle a new email notification."""
    if user_id not in active_connections:
        return

    # Prepare notification data
    notification = {
        "type": "new_email",
        "data": {
            "id": str(email_metadata.id),
            "from": email_metadata.from_address,
            "subject": email_metadata.subject,
            "received_date": email_metadata.received_date.isoformat(),
            "mailbox": email_metadata.mailbox,
        },
    }

    # Send notification to all connections for this user
    for websocket in active_connections[user_id]:
        try:
            await websocket.send_text(json.dumps(notification))
        except Exception as e:
            logger.error(f"Error sending WebSocket notification: {e}")


async def handle_email_status_update(
    user_id: uuid.UUID, email_id: uuid.UUID, status_updates: Dict[str, Any]
) -> None:
    """Handle an email status update notification."""
    if user_id not in active_connections:
        return

    # Prepare notification data
    notification = {
        "type": "email_status_update",
        "data": {"id": str(email_id), **status_updates},
    }

    # Send notification to all connections for this user
    for websocket in active_connections[user_id]:
        try:
            await websocket.send_text(json.dumps(notification))
        except Exception as e:
            logger.error(f"Error sending WebSocket notification: {e}")


async def broadcast_to_user(user_id: uuid.UUID, message_type: str, data: Dict[str, Any]) -> None:
    """Broadcast a message to all connections for a user."""
    if user_id not in active_connections:
        return

    # Prepare notification data
    notification = {"type": message_type, "data": data}

    # Send notification to all connections for this user
    for websocket in active_connections[user_id]:
        try:
            await websocket.send_text(json.dumps(notification))
        except Exception as e:
            logger.error(f"Error sending WebSocket notification: {e}")


def get_active_users() -> List[uuid.UUID]:
    """Get a list of active users (users with active WebSocket connections)."""
    return list(active_connections.keys())
