"""Provision Service for Email module."""

import logging  # noqa: E402
import uuid
import os
import subprocess
from typing import List, Optional, Dict, Any
from datetime import datetime

from sqlalchemy import select  # noqa: E402
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.modules.shared.email.models import Em<PERSON>Domain, EmailAccount, EmailAlias  # noqa: E402
from app.modules.shared.email.api.schemas import (
    EmailDomainCreate,
    EmailDomainUpdate,
    EmailDomainDNSRecords,
    EmailAccountCreate,
    EmailAccountUpdate,
    EmailAliasCreate,
    EmailAliasUpdate,
)
from app.modules.shared.email.services.auth_service import EmailAuthService  # noqa: E402

# Import for integration with custom_domains
from app.modules.core.custom_domains.models import CustomDomain  # noqa: E402
from app.modules.shared.domain_rent.models.domain_registration import DomainRegistration

logger = logging.getLogger(__name__)


class ProvisionService:
    """Service for provisioning email domains, accounts, and aliases."""

    # Integration with custom_domains and domain_rent

    async def create_email_domain_from_custom_domain(
        self, db: AsyncSession, custom_domain: CustomDomain
    ) -> Optional[EmailDomain]:
        """Create an EmailDomain from a CustomDomain."""
        # Check if an EmailDomain already exists for this domain name
        result = await db.execute(
            select(EmailDomain).filter(EmailDomain.domain_name == custom_domain.domain_name)
        )
        existing_domain = result.scalars().first()
        if existing_domain:
            logger.info(
                f"EmailDomain already exists for {custom_domain.domain_name}, skipping creation"
            )
            return existing_domain

        # Create a new EmailDomain
        email_domain = EmailDomain(
            domain_name=custom_domain.domain_name,
            user_id=custom_domain.user_id,
            tenant_id=custom_domain.tenant_id,
            is_active=False,  # Will be activated after DNS verification
            custom_domain_id=custom_domain.id,
        )

        db.add(email_domain)
        await db.commit()
        await db.refresh(email_domain)

        logger.info(f"Created EmailDomain for CustomDomain {custom_domain.domain_name}")
        return email_domain

    async def create_email_domain_from_domain_registration(
        self, db: AsyncSession, domain_registration: DomainRegistration
    ) -> Optional[EmailDomain]:
        """Create an EmailDomain from a DomainRegistration."""
        # Check if an EmailDomain already exists for this domain name
        result = await db.execute(
            select(EmailDomain).filter(EmailDomain.domain_name == domain_registration.domain_name)
        )
        existing_domain = result.scalars().first()
        if existing_domain:
            logger.info(
                f"EmailDomain already exists for {domain_registration.domain_name}, skipping creation"  # noqa: E501
            )
            return existing_domain

        # Create a new EmailDomain
        email_domain = EmailDomain(
            domain_name=domain_registration.domain_name,
            user_id=domain_registration.user_id,
            tenant_id=domain_registration.tenant_id,
            is_active=False,  # Will be activated after DNS verification
            domain_registration_id=domain_registration.id,
        )

        db.add(email_domain)
        await db.commit()
        await db.refresh(email_domain)

        logger.info(f"Created EmailDomain for DomainRegistration {domain_registration.domain_name}")
        return email_domain

    async def generate_email_dns_records(
        self, db: AsyncSession, domain_id: uuid.UUID
    ) -> EmailDomainDNSRecords:
        """Generate DNS records for an EmailDomain."""
        # Get the EmailDomain
        result = await db.execute(select(EmailDomain).filter(EmailDomain.id == domain_id))
        email_domain = result.scalars().first()
        if not email_domain:
            raise ValueError(f"EmailDomain with id {domain_id} not found")

        # Generate DNS records
        mx_record = f"10 mail.{email_domain.domain_name}."
        spf_record = "v=spf1 mx ~all"
        dkim_record = "v=DKIM1; k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC5"
        dmarc_record = "v=DMARC1; p=none; rua=mailto:postmaster@{email_domain.domain_name}"

        return EmailDomainDNSRecords(
            domain_id=email_domain.id,
            domain_name=email_domain.domain_name,
            mx_record=mx_record,
            spf_record=spf_record,
            dkim_record=dkim_record,
            dmarc_record=dmarc_record,
        )

    def __init__(self):
        """Initialize the service."""
        self.auth_service = EmailAuthService()

    # Email Domain methods

    async def get_email_domains(self, db: AsyncSession, tenant_id: uuid.UUID) -> List[EmailDomain]:
        """Get all email domains for a tenant."""
        result = await db.execute(
            select(EmailDomain)
            .where(EmailDomain.tenant_id == tenant_id)
            .order_by(EmailDomain.domain_name)
        )
        return result.scalars().all()

    async def get_email_domain(
        self, db: AsyncSession, domain_id: uuid.UUID
    ) -> Optional[EmailDomain]:
        """Get an email domain by ID."""
        return await db.get(EmailDomain, domain_id)

    async def get_email_domain_by_name(
        self, db: AsyncSession, domain_name: str
    ) -> Optional[EmailDomain]:
        """Get an email domain by name."""
        result = await db.execute(select(EmailDomain).where(EmailDomain.domain_name == domain_name))
        return result.scalars().first()

    async def create_email_domain(
        self, db: AsyncSession, tenant_id: uuid.UUID, domain_in: EmailDomainCreate
    ) -> EmailDomain:
        """Create a new email domain."""
        # Check if domain already exists
        existing_domain = await self.get_email_domain_by_name(db, domain_in.domain_name)
        if existing_domain:
            raise ValueError(f"Domain {domain_in.domain_name} already exists")

        # Create domain
        domain = EmailDomain(
            tenant_id=tenant_id,
            domain_name=domain_in.domain_name,
            domain_registration_id=domain_in.domain_registration_id,
            is_active=False,  # Inactive until DNS records are verified
        )

        db.add(domain)
        await db.commit()
        await db.refresh(domain)

        # Generate DKIM keys
        await self.generate_dkim_keys(db, domain.id)

        return domain

    async def update_email_domain(
        self, db: AsyncSession, domain_id: uuid.UUID, domain_in: EmailDomainUpdate
    ) -> EmailDomain:
        """Update an email domain."""
        domain = await self.get_email_domain(db, domain_id)
        if not domain:
            raise ValueError(f"Domain with ID {domain_id} not found")

        # Update fields
        if domain_in.is_active is not None:
            domain.is_active = domain_in.is_active

        if domain_in.dkim_selector is not None:
            domain.dkim_selector = domain_in.dkim_selector

        await db.commit()
        await db.refresh(domain)

        return domain

    async def delete_email_domain(self, db: AsyncSession, domain_id: uuid.UUID) -> None:
        """Delete an email domain."""
        domain = await self.get_email_domain(db, domain_id)
        if not domain:
            raise ValueError(f"Domain with ID {domain_id} not found")

        # Delete domain
        await db.delete(domain)
        await db.commit()

    async def generate_dkim_keys(self, db: AsyncSession, domain_id: uuid.UUID) -> EmailDomain:
        """Generate DKIM keys for a domain."""
        domain = await self.get_email_domain(db, domain_id)
        if not domain:
            raise ValueError(f"Domain with ID {domain_id} not found")

        # In a real implementation, this would generate actual DKIM keys
        # For now, we'll just simulate it
        domain.dkim_private_key = "-----BEGIN RSA PRIVATE KEY-----\nMIIEpAIBAAKCAQEA1234567890abcdefghijklmnopqrstuvwxyz\n-----END RSA PRIVATE KEY-----"  # noqa: E501
        domain.dkim_configured = True

        await db.commit()
        await db.refresh(domain)

        return domain

    async def get_dns_records(
        self, db: AsyncSession, domain_id: uuid.UUID
    ) -> EmailDomainDNSRecords:
        """Get DNS records for a domain."""
        domain = await self.get_email_domain(db, domain_id)
        if not domain:
            raise ValueError(f"Domain with ID {domain_id} not found")

        # In a real implementation, this would generate actual DNS records
        # For now, we'll just return sample records
        return EmailDomainDNSRecords(
            mx_records=[
                f"10 mail.{domain.domain_name}",
                f"20 mail2.{domain.domain_name}",
            ],
            spf_record=f"v=spf1 mx a:{domain.domain_name} ~all",
            dkim_record=f"v=DKIM1; k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC5N3lnvvrYgPCRSoqn+awTpE+iGYcKBPpo8HHbcFfCIIV10Hwo4PhCoGZSaKVHOjDm4yefKXhQjM7iKzEPuBatE7O47hAx1CJpNuIdLxhILSbEmbMxJrJAG0HZVn8z6EAoOHZNaPHmK2h4UUrjOG8zA5BHfzJf7tGwI+K619fFUwIDAQAB",  # noqa: E501
            dmarc_record=f"v=DMARC1; p=none; sp=none; rua=mailto:dmarc@{domain.domain_name}",
        )

    async def verify_dns_records(self, db: AsyncSession, domain_id: uuid.UUID) -> EmailDomain:
        """Verify DNS records for a domain."""
        domain = await self.get_email_domain(db, domain_id)
        if not domain:
            raise ValueError(f"Domain with ID {domain_id} not found")

        # In a real implementation, this would check actual DNS records
        # For now, we'll just simulate it
        domain.mx_records_configured = True
        domain.spf_record_configured = True
        domain.dkim_configured = True
        domain.dmarc_configured = True
        domain.is_active = True

        await db.commit()
        await db.refresh(domain)

        return domain

    # Email Account methods

    async def get_email_accounts(
        self, db: AsyncSession, domain_id: uuid.UUID
    ) -> List[EmailAccount]:
        """Get all email accounts for a domain."""
        result = await db.execute(
            select(EmailAccount)
            .where(EmailAccount.email_domain_id == domain_id)
            .order_by(EmailAccount.username)
        )
        return result.scalars().all()

    async def get_email_account(
        self, db: AsyncSession, account_id: uuid.UUID
    ) -> Optional[EmailAccount]:
        """Get an email account by ID."""
        return await db.get(EmailAccount, account_id)

    async def get_email_account_by_email(
        self, db: AsyncSession, email: str
    ) -> Optional[EmailAccount]:
        """Get an email account by email address."""
        result = await db.execute(select(EmailAccount).where(EmailAccount.full_email == email))
        return result.scalars().first()

    async def create_email_account(
        self, db: AsyncSession, account_in: EmailAccountCreate
    ) -> EmailAccount:
        """Create a new email account."""
        # Check if account already exists
        existing_account = await self.get_email_account_by_email(db, account_in.full_email)
        if existing_account:
            raise ValueError(f"Email account {account_in.full_email} already exists")

        # Get domain
        domain = await self.get_email_domain(db, account_in.email_domain_id)
        if not domain:
            raise ValueError(f"Domain with ID {account_in.email_domain_id} not found")

        # Hash password
        password_hash = await self.auth_service.hash_password(account_in.password)

        # Create account
        account = EmailAccount(
            email_domain_id=account_in.email_domain_id,
            username=account_in.username,
            full_email=account_in.full_email,
            password_hash=password_hash,
            quota_mb=account_in.quota_mb,
            user_id=account_in.user_id,
            is_active=True,
        )

        db.add(account)
        await db.commit()
        await db.refresh(account)

        return account

    async def update_email_account(
        self, db: AsyncSession, account_id: uuid.UUID, account_in: EmailAccountUpdate
    ) -> EmailAccount:
        """Update an email account."""
        account = await self.get_email_account(db, account_id)
        if not account:
            raise ValueError(f"Account with ID {account_id} not found")

        # Update fields
        if account_in.quota_mb is not None:
            account.quota_mb = account_in.quota_mb

        if account_in.is_active is not None:
            account.is_active = account_in.is_active

        await db.commit()
        await db.refresh(account)

        return account

    async def delete_email_account(self, db: AsyncSession, account_id: uuid.UUID) -> None:
        """Delete an email account."""
        account = await self.get_email_account(db, account_id)
        if not account:
            raise ValueError(f"Account with ID {account_id} not found")

        # Delete account
        await db.delete(account)
        await db.commit()

    # Email Alias methods

    async def get_email_aliases(self, db: AsyncSession, domain_id: uuid.UUID) -> List[EmailAlias]:
        """Get all email aliases for a domain."""
        result = await db.execute(
            select(EmailAlias)
            .where(EmailAlias.email_domain_id == domain_id)
            .order_by(EmailAlias.source)
        )
        return result.scalars().all()

    async def get_email_alias(self, db: AsyncSession, alias_id: uuid.UUID) -> Optional[EmailAlias]:
        """Get an email alias by ID."""
        return await db.get(EmailAlias, alias_id)

    async def get_email_alias_by_source(
        self, db: AsyncSession, domain_id: uuid.UUID, source: str
    ) -> Optional[EmailAlias]:
        """Get an email alias by source address."""
        result = await db.execute(
            select(EmailAlias).where(
                EmailAlias.email_domain_id == domain_id, EmailAlias.source == source
            )
        )
        return result.scalars().first()

    async def create_email_alias(self, db: AsyncSession, alias_in: EmailAliasCreate) -> EmailAlias:
        """Create a new email alias."""
        # Check if alias already exists
        existing_alias = await self.get_email_alias_by_source(
            db, alias_in.email_domain_id, alias_in.source
        )
        if existing_alias:
            raise ValueError(f"Email alias {alias_in.source} already exists")

        # Get domain
        domain = await self.get_email_domain(db, alias_in.email_domain_id)
        if not domain:
            raise ValueError(f"Domain with ID {alias_in.email_domain_id} not found")

        # Create alias
        alias = EmailAlias(
            email_domain_id=alias_in.email_domain_id,
            source=alias_in.source,
            destination=alias_in.destination,
            is_active=True,
        )

        db.add(alias)
        await db.commit()
        await db.refresh(alias)

        return alias

    async def update_email_alias(
        self, db: AsyncSession, alias_id: uuid.UUID, alias_in: EmailAliasUpdate
    ) -> EmailAlias:
        """Update an email alias."""
        alias = await self.get_email_alias(db, alias_id)
        if not alias:
            raise ValueError(f"Alias with ID {alias_id} not found")

        # Update fields
        if alias_in.destination is not None:
            alias.destination = alias_in.destination

        if alias_in.is_active is not None:
            alias.is_active = alias_in.is_active

        await db.commit()
        await db.refresh(alias)

        return alias

    async def delete_email_alias(self, db: AsyncSession, alias_id: uuid.UUID) -> None:
        """Delete an email alias."""
        alias = await self.get_email_alias(db, alias_id)
        if not alias:
            raise ValueError(f"Alias with ID {alias_id} not found")

        # Delete alias
        await db.delete(alias)
        await db.commit()
