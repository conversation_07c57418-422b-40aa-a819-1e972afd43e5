import logging
from typing import Optional, Sequence, List
from sqlalchemy import select, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.exc import IntegrityError
from fastapi import HTTPException, status
import uuid

# Import models
from app.modules.core.eshop.models.product_optional_group import ProductOptionalGroup
from app.modules.core.eshop.models.product_optional_option import ProductOptionalOption

# Import schemas
from app.modules.core.eshop.schemas.product_optional_group import (
    ProductOptionalGroupCreate,
    ProductOptionalGroupUpdate,
    ProductOptionalGroupResponse,
)
from app.modules.core.eshop.schemas.product_optional_option import (
    ProductOptionalOptionCreate,
    ProductOptionalOptionUpdate,
    ProductOptionalOptionResponse,
)

# Import WebSocket utility
from app.websockets.manager import emit_to_tenant

logger = logging.getLogger(__name__)


class ProductOptionalService:
    def __init__(self, db_session: AsyncSession):
        self.db = db_session

    # Optional Group Methods
    async def create_optional_group(
        self, 
        group_in: ProductOptionalGroupCreate,
        current_user_id: uuid.UUID
    ) -> ProductOptionalGroup:
        """Creates a new product optional group."""
        logger.info(f"Creating product optional group: {group_in.name}")

        try:
            group_data = group_in.model_dump()
            db_group = ProductOptionalGroup(**group_data)
            self.db.add(db_group)
            await self.db.commit()
            await self.db.refresh(db_group)

            # Emit WebSocket notification
            if group_in.tenant_id:
                await emit_to_tenant(
                    group_in.tenant_id,
                    "optional_group_created",
                    {"group_id": str(db_group.id), "group_name": db_group.name}
                )

            logger.info(f"Product optional group created: {db_group.id}")
            return db_group

        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"IntegrityError creating optional group: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Error creating optional group. Database constraint violation: {str(e)}",
            )
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Unexpected error creating optional group: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal server error: {str(e)}",
            )

    async def get_optional_group(
        self, 
        group_id: uuid.UUID, 
        tenant_id: Optional[uuid.UUID] = None,
        include_options: bool = False
    ) -> Optional[ProductOptionalGroup]:
        """Gets a specific product optional group by ID."""
        query = select(ProductOptionalGroup).where(ProductOptionalGroup.id == group_id)
        
        if tenant_id:
            query = query.where(
                or_(ProductOptionalGroup.tenant_id == tenant_id, ProductOptionalGroup.tenant_id.is_(None))
            )

        if include_options:
            query = query.options(selectinload(ProductOptionalGroup.options))

        result = await self.db.execute(query)
        group = result.scalars().first()

        if not group:
            logger.warning(f"Optional group {group_id} not found")
            return None

        return group

    async def get_optional_groups(
        self,
        tenant_id: Optional[uuid.UUID] = None,
        is_active: Optional[bool] = None,
        is_template: Optional[bool] = None,
        include_options: bool = False,
        skip: int = 0,
        limit: int = 100,
    ) -> Sequence[ProductOptionalGroup]:
        """Gets a list of product optional groups with filtering options."""
        query = select(ProductOptionalGroup)

        # Apply filters
        filters = []
        
        if tenant_id:
            filters.append(
                or_(ProductOptionalGroup.tenant_id == tenant_id, ProductOptionalGroup.tenant_id.is_(None))
            )
            
        if is_active is not None:
            filters.append(ProductOptionalGroup.is_active == is_active)
            
        if is_template is not None:
            filters.append(ProductOptionalGroup.is_template == is_template)

        if filters:
            query = query.where(and_(*filters))

        # Apply ordering and pagination
        query = query.order_by(ProductOptionalGroup.display_order, ProductOptionalGroup.name)
        query = query.offset(skip).limit(limit)

        # Load options if requested
        if include_options:
            query = query.options(selectinload(ProductOptionalGroup.options))

        result = await self.db.execute(query)
        groups = result.scalars().all()

        return groups

    async def update_optional_group(
        self, 
        group_id: uuid.UUID, 
        group_in: ProductOptionalGroupUpdate,
        current_user_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID] = None
    ) -> Optional[ProductOptionalGroup]:
        """Updates an existing product optional group."""
        db_group = await self.get_optional_group(group_id, tenant_id)
        if not db_group:
            return None

        try:
            update_data = group_in.model_dump(exclude_unset=True)
            
            # Apply updates
            for field, value in update_data.items():
                setattr(db_group, field, value)

            await self.db.commit()
            await self.db.refresh(db_group)

            # Emit WebSocket notification
            if db_group.tenant_id:
                await emit_to_tenant(
                    db_group.tenant_id,
                    "optional_group_updated",
                    {"group_id": str(db_group.id), "group_name": db_group.name}
                )

            logger.info(f"Product optional group updated: {db_group.id}")
            return db_group

        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"IntegrityError updating optional group: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Error updating optional group. Database constraint violation: {str(e)}",
            )
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Unexpected error updating optional group: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal server error: {str(e)}",
            )

    async def delete_optional_group(
        self, 
        group_id: uuid.UUID,
        current_user_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID] = None
    ) -> bool:
        """Deletes a product optional group."""
        db_group = await self.get_optional_group(group_id, tenant_id, include_options=True)
        if not db_group:
            return False

        try:
            await self.db.delete(db_group)
            await self.db.commit()

            # Emit WebSocket notification
            if db_group.tenant_id:
                await emit_to_tenant(
                    db_group.tenant_id,
                    "optional_group_deleted",
                    {"group_id": str(group_id), "group_name": db_group.name}
                )

            logger.info(f"Product optional group deleted: {group_id}")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Error deleting optional group: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal server error: {str(e)}",
            )

    # Optional Option Methods
    async def create_optional_option(
        self, 
        option_in: ProductOptionalOptionCreate,
        current_user_id: uuid.UUID
    ) -> ProductOptionalOption:
        """Creates a new product optional option."""
        logger.info(f"Creating product optional option: {option_in.name}")

        # Check if optional group exists
        group = await self.get_optional_group(option_in.optional_group_id, option_in.tenant_id)
        if not group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Optional group not found.",
            )

        try:
            option_data = option_in.model_dump()
            db_option = ProductOptionalOption(**option_data)
            self.db.add(db_option)
            await self.db.commit()
            await self.db.refresh(db_option)

            # Emit WebSocket notification
            if option_in.tenant_id:
                await emit_to_tenant(
                    option_in.tenant_id,
                    "optional_option_created",
                    {"option_id": str(db_option.id), "option_name": db_option.name}
                )

            logger.info(f"Product optional option created: {db_option.id}")
            return db_option

        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"IntegrityError creating optional option: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Error creating optional option. Database constraint violation: {str(e)}",
            )
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Unexpected error creating optional option: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal server error: {str(e)}",
            )

    async def get_optional_option(
        self, 
        option_id: uuid.UUID, 
        tenant_id: Optional[uuid.UUID] = None
    ) -> Optional[ProductOptionalOption]:
        """Gets a specific product optional option by ID."""
        query = select(ProductOptionalOption).where(ProductOptionalOption.id == option_id)
        
        if tenant_id:
            query = query.where(
                or_(ProductOptionalOption.tenant_id == tenant_id, ProductOptionalOption.tenant_id.is_(None))
            )

        result = await self.db.execute(query)
        option = result.scalars().first()

        if not option:
            logger.warning(f"Optional option {option_id} not found")
            return None

        return option

    async def get_optional_options(
        self,
        optional_group_id: Optional[uuid.UUID] = None,
        tenant_id: Optional[uuid.UUID] = None,
        is_active: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> Sequence[ProductOptionalOption]:
        """Gets a list of product optional options with filtering options."""
        query = select(ProductOptionalOption)

        # Apply filters
        filters = []
        
        if optional_group_id:
            filters.append(ProductOptionalOption.optional_group_id == optional_group_id)
        
        if tenant_id:
            filters.append(
                or_(ProductOptionalOption.tenant_id == tenant_id, ProductOptionalOption.tenant_id.is_(None))
            )
            
        if is_active is not None:
            filters.append(ProductOptionalOption.is_active == is_active)

        if filters:
            query = query.where(and_(*filters))

        # Apply ordering and pagination
        query = query.order_by(ProductOptionalOption.display_order, ProductOptionalOption.name)
        query = query.offset(skip).limit(limit)

        result = await self.db.execute(query)
        options = result.scalars().all()

        return options

    async def update_optional_option(
        self, 
        option_id: uuid.UUID, 
        option_in: ProductOptionalOptionUpdate,
        current_user_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID] = None
    ) -> Optional[ProductOptionalOption]:
        """Updates an existing product optional option."""
        db_option = await self.get_optional_option(option_id, tenant_id)
        if not db_option:
            return None

        try:
            update_data = option_in.model_dump(exclude_unset=True)
            
            # Apply updates
            for field, value in update_data.items():
                setattr(db_option, field, value)

            await self.db.commit()
            await self.db.refresh(db_option)

            # Emit WebSocket notification
            if db_option.tenant_id:
                await emit_to_tenant(
                    db_option.tenant_id,
                    "optional_option_updated",
                    {"option_id": str(db_option.id), "option_name": db_option.name}
                )

            logger.info(f"Product optional option updated: {db_option.id}")
            return db_option

        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"IntegrityError updating optional option: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Error updating optional option. Database constraint violation: {str(e)}",
            )
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Unexpected error updating optional option: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal server error: {str(e)}",
            )

    async def delete_optional_option(
        self, 
        option_id: uuid.UUID,
        current_user_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID] = None
    ) -> bool:
        """Deletes a product optional option."""
        db_option = await self.get_optional_option(option_id, tenant_id)
        if not db_option:
            return False

        try:
            await self.db.delete(db_option)
            await self.db.commit()

            # Emit WebSocket notification
            if db_option.tenant_id:
                await emit_to_tenant(
                    db_option.tenant_id,
                    "optional_option_deleted",
                    {"option_id": str(option_id), "option_name": db_option.name}
                )

            logger.info(f"Product optional option deleted: {option_id}")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Error deleting optional option: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal server error: {str(e)}",
            ) 