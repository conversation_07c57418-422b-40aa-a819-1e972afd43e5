"""
Cart API for EShop System
=========================

APIs para gerenciamento de carrinho de compras com suporte a
sessões persistentes, produtos do eshop e contexto B2B/B2C.
"""

import uuid
import logging
from datetime import datetime
from typing import Optional, List

from fastapi import APIRouter, Depends, HTTPException, status, Header, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.auth_dependencies import get_optional_current_user
from app.modules.core.users.models.user import User
from app.modules.core.functions.cart.services.cart_service import get_cart_service
from app.modules.core.functions.cart.schemas.cart import (
    CartRead, CartSummary, CartResponse, AddToCartRequest,
    UpdateCartItemRequest, CartItemRead, CartStats
)

logger = logging.getLogger(__name__)

router = APIRouter(
    tags=["Cart"]
)


@router.get("/", response_model=CartRead)
async def get_cart(
    session_id: Optional[str] = Header(None, alias="X-Session-ID"),
    market_context: str = Query(default='b2c', description="Contexto do mercado"),
    current_user: Optional[User] = Depends(get_optional_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Obtém o carrinho atual do usuário ou sessão.
    
    Para usuários logados, usa o user_id.
    Para usuários anônimos, usa o session_id do header.
    """
    try:
        cart_service = get_cart_service(db)
        
        # Determinar tenant_id (placeholder - deve vir do contexto)
        tenant_id = uuid.uuid4()  # TODO: Obter do contexto real
        
        user_id = current_user.id if current_user else None
        
        if not user_id and not session_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Session ID é obrigatório para usuários anônimos"
            )
        
        cart = await cart_service.get_or_create_cart(
            tenant_id=tenant_id,
            user_id=user_id,
            session_id=session_id,
            market_context=market_context
        )
        
        return cart
        
    except Exception as e:
        logger.error(f"Erro ao obter carrinho: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno do servidor"
        )


@router.post("/items", response_model=CartResponse)
async def add_item_to_cart(
    request: AddToCartRequest,
    session_id: Optional[str] = Header(None, alias="X-Session-ID"),
    market_context: str = Query(default='b2c', description="Contexto do mercado"),
    current_user: Optional[User] = Depends(get_optional_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Adiciona item ao carrinho.
    
    Se o item já existir com as mesmas customizações,
    incrementa a quantidade.
    """
    try:
        cart_service = get_cart_service(db)
        
        # Obter ou criar carrinho
        tenant_id = uuid.uuid4()  # TODO: Obter do contexto real
        user_id = current_user.id if current_user else None
        
        if not user_id and not session_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Session ID é obrigatório para usuários anônimos"
            )
        
        cart = await cart_service.get_or_create_cart(
            tenant_id=tenant_id,
            user_id=user_id,
            session_id=session_id,
            market_context=market_context
        )
        
        # Adicionar item
        cart_item = await cart_service.add_item_to_cart(cart.id, request)
        
        # Recarregar carrinho com itens atualizados
        updated_cart = await cart_service.get_cart_by_id(cart.id)
        
        return CartResponse(
            success=True,
            message="Item adicionado ao carrinho com sucesso",
            cart=updated_cart
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao adicionar item ao carrinho: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno do servidor"
        )


@router.put("/items/{cart_item_id}", response_model=CartResponse)
async def update_cart_item(
    cart_item_id: uuid.UUID,
    request: UpdateCartItemRequest,
    current_user: Optional[User] = Depends(get_optional_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Atualiza item do carrinho.
    
    Permite alterar quantidade, variações, modificadores
    e instruções especiais.
    """
    try:
        cart_service = get_cart_service(db)
        
        cart_item = await cart_service.update_cart_item(
            cart_item_id=cart_item_id,
            quantity=request.quantity,
            selected_variants=request.selected_variants,
            selected_modifiers=request.selected_modifiers,
            special_instructions=request.special_instructions
        )
        
        # Recarregar carrinho
        updated_cart = await cart_service.get_cart_by_id(cart_item.cart_id)
        
        return CartResponse(
            success=True,
            message="Item do carrinho atualizado com sucesso",
            cart=updated_cart
        )
        
    except Exception as e:
        logger.error(f"Erro ao atualizar item do carrinho: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno do servidor"
        )


@router.delete("/items/{cart_item_id}", response_model=CartResponse)
async def remove_cart_item(
    cart_item_id: uuid.UUID,
    current_user: Optional[User] = Depends(get_optional_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Remove item do carrinho.
    """
    try:
        cart_service = get_cart_service(db)
        
        # Obter carrinho antes de remover o item
        cart_item_query = await db.execute(
            "SELECT cart_id FROM eshop_cart_items WHERE id = :item_id",
            {"item_id": cart_item_id}
        )
        cart_id = cart_item_query.scalar()
        
        if not cart_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Item do carrinho não encontrado"
            )
        
        await cart_service.remove_cart_item(cart_item_id)
        
        # Recarregar carrinho
        updated_cart = await cart_service.get_cart_by_id(cart_id)
        
        return CartResponse(
            success=True,
            message="Item removido do carrinho com sucesso",
            cart=updated_cart
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao remover item do carrinho: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno do servidor"
        )


@router.delete("/{cart_id}/clear", response_model=CartResponse)
async def clear_cart(
    cart_id: uuid.UUID,
    current_user: Optional[User] = Depends(get_optional_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Limpa todos os itens do carrinho.
    """
    try:
        cart_service = get_cart_service(db)
        
        await cart_service.clear_cart(cart_id)
        
        # Recarregar carrinho
        updated_cart = await cart_service.get_cart_by_id(cart_id)
        
        return CartResponse(
            success=True,
            message="Carrinho limpo com sucesso",
            cart=updated_cart
        )
        
    except Exception as e:
        logger.error(f"Erro ao limpar carrinho: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno do servidor"
        )


@router.get("/summary", response_model=CartSummary)
async def get_cart_summary(
    session_id: Optional[str] = Header(None, alias="X-Session-ID"),
    market_context: str = Query(default='b2c', description="Contexto do mercado"),
    current_user: Optional[User] = Depends(get_optional_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Obtém resumo do carrinho (para exibição em header/menu).
    """
    try:
        cart_service = get_cart_service(db)
        
        tenant_id = uuid.uuid4()  # TODO: Obter do contexto real
        user_id = current_user.id if current_user else None
        
        if not user_id and not session_id:
            # Retornar carrinho vazio para usuários sem sessão
            return CartSummary(
                id=uuid.uuid4(),
                item_count=0,
                total_amount=0.00,
                currency='BRL',
                status='active',
                market_context=market_context,
                updated_at=datetime.utcnow()
            )
        
        cart = await cart_service.get_or_create_cart(
            tenant_id=tenant_id,
            user_id=user_id,
            session_id=session_id,
            market_context=market_context
        )
        
        return CartSummary(
            id=cart.id,
            item_count=cart.item_count,
            total_amount=cart.total_amount,
            currency=cart.currency,
            status=cart.status,
            market_context=cart.market_context,
            updated_at=cart.updated_at
        )
        
    except Exception as e:
        logger.error(f"Erro ao obter resumo do carrinho: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno do servidor"
        )
