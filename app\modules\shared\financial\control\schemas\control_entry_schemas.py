"""Financial Control Entry Schemas."""

from typing import Optional, List, Dict, Any
from datetime import date, datetime
from decimal import Decimal
from pydantic import BaseModel, Field, validator
from uuid import UUID

from ..models.control_entry import ControlEntryType, ControlEntryStatus


class ControlEntryBase(BaseModel):
    """Base schema for control entries."""
    
    entry_type: ControlEntryType
    amount: Decimal = Field(..., gt=0, description="Entry amount")
    gross_amount: Optional[Decimal] = Field(None, description="Amount before taxes/discounts")
    net_amount: Optional[Decimal] = Field(None, description="Amount after taxes/discounts")
    tax_amount: Optional[Decimal] = Field(None, ge=0, description="Tax amount")
    discount_amount: Optional[Decimal] = Field(None, ge=0, description="Discount amount")
    
    title: str = Field(..., min_length=1, max_length=255, description="Entry title")
    description: Optional[str] = Field(None, description="Entry description")
    reference_number: Optional[str] = Field(None, max_length=100, description="Reference number")
    external_reference: Optional[str] = Field(None, max_length=100, description="External reference")
    
    entry_date: date = Field(..., description="Entry date")
    due_date: Optional[date] = Field(None, description="Due date")
    payment_date: Optional[date] = Field(None, description="Payment date")
    
    category_id: Optional[UUID] = Field(None, description="Category ID")
    subcategory: Optional[str] = Field(None, max_length=100, description="Subcategory")
    tags: Optional[List[str]] = Field(None, description="Tags")
    
    supplier_id: Optional[UUID] = Field(None, description="Supplier ID")
    order_id: Optional[UUID] = Field(None, description="Order ID")
    
    payment_method: Optional[str] = Field(None, max_length=50, description="Payment method")
    payment_reference: Optional[str] = Field(None, max_length=100, description="Payment reference")
    bank_account: Optional[str] = Field(None, max_length=100, description="Bank account")
    
    is_recurring: bool = Field(False, description="Is recurring entry")
    is_tax_deductible: bool = Field(False, description="Is tax deductible")
    requires_approval: bool = Field(False, description="Requires approval")
    
    notes: Optional[str] = Field(None, description="Additional notes")
    additional_data: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    
    @validator('due_date')
    def validate_due_date(cls, v, values):
        if v and 'entry_date' in values and v < values['entry_date']:
            raise ValueError('Due date cannot be before entry date')
        return v
    
    @validator('payment_date')
    def validate_payment_date(cls, v, values):
        if v and 'entry_date' in values and v < values['entry_date']:
            raise ValueError('Payment date cannot be before entry date')
        return v


class ControlEntryCreate(ControlEntryBase):
    """Schema for creating control entries."""
    
    status: ControlEntryStatus = Field(ControlEntryStatus.PENDING, description="Entry status")


class ControlEntryUpdate(BaseModel):
    """Schema for updating control entries."""
    
    entry_type: Optional[ControlEntryType] = None
    status: Optional[ControlEntryStatus] = None
    amount: Optional[Decimal] = Field(None, gt=0)
    gross_amount: Optional[Decimal] = None
    net_amount: Optional[Decimal] = None
    tax_amount: Optional[Decimal] = Field(None, ge=0)
    discount_amount: Optional[Decimal] = Field(None, ge=0)
    
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    reference_number: Optional[str] = Field(None, max_length=100)
    external_reference: Optional[str] = Field(None, max_length=100)
    
    entry_date: Optional[date] = None
    due_date: Optional[date] = None
    payment_date: Optional[date] = None
    
    category_id: Optional[UUID] = None
    subcategory: Optional[str] = Field(None, max_length=100)
    tags: Optional[List[str]] = None
    
    supplier_id: Optional[UUID] = None
    order_id: Optional[UUID] = None
    
    payment_method: Optional[str] = Field(None, max_length=50)
    payment_reference: Optional[str] = Field(None, max_length=100)
    bank_account: Optional[str] = Field(None, max_length=100)
    
    is_recurring: Optional[bool] = None
    is_tax_deductible: Optional[bool] = None
    requires_approval: Optional[bool] = None
    
    notes: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None


class ControlEntryResponse(ControlEntryBase):
    """Schema for control entry responses."""
    
    id: UUID
    tenant_id: UUID
    status: ControlEntryStatus
    transaction_id: Optional[UUID] = None
    
    is_automated: bool
    is_overdue: bool
    calculated_net_amount: Decimal
    
    approved_by: Optional[UUID] = None
    approved_at: Optional[datetime] = None
    approval_notes: Optional[str] = None
    
    created_by: UUID
    updated_by: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime
    
    # Related data
    category_name: Optional[str] = None
    supplier_name: Optional[str] = None
    document_count: int = 0
    
    class Config:
        from_attributes = True


class ControlEntryList(BaseModel):
    """Schema for paginated control entry lists."""
    
    items: List[ControlEntryResponse]
    total: int
    page: int
    size: int
    pages: int


class ControlEntryFilters(BaseModel):
    """Schema for filtering control entries."""
    
    entry_type: Optional[ControlEntryType] = None
    status: Optional[List[ControlEntryStatus]] = None
    category_id: Optional[UUID] = None
    supplier_id: Optional[UUID] = None
    
    date_from: Optional[date] = None
    date_to: Optional[date] = None
    due_date_from: Optional[date] = None
    due_date_to: Optional[date] = None
    
    amount_min: Optional[Decimal] = Field(None, ge=0)
    amount_max: Optional[Decimal] = Field(None, ge=0)
    
    search: Optional[str] = Field(None, max_length=255, description="Search in title, description, reference")
    tags: Optional[List[str]] = None
    
    is_overdue: Optional[bool] = None
    is_recurring: Optional[bool] = None
    is_tax_deductible: Optional[bool] = None
    has_documents: Optional[bool] = None
    
    sort_by: Optional[str] = Field("entry_date", description="Sort field")
    sort_order: Optional[str] = Field("desc", pattern="^(asc|desc)$", description="Sort order")


class ControlEntryBulkUpdate(BaseModel):
    """Schema for bulk updating control entries."""
    
    entry_ids: List[UUID] = Field(..., min_items=1, description="List of entry IDs")
    updates: ControlEntryUpdate = Field(..., description="Updates to apply")
    
    @validator('entry_ids')
    def validate_entry_ids(cls, v):
        if len(v) > 100:
            raise ValueError('Cannot update more than 100 entries at once')
        return v
