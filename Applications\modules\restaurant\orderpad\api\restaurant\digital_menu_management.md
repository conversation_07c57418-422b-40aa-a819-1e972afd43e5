# Restaurant - Digital Menu Management

**Categoria:** Restaurant
**Módulo:** Digital Menu Management
**Total de Endpoints:** 7
**Gerado em:** 08/06/2025, 17:26:01

## 📋 Endpoints

- [GET /api/modules/restaurants/menu/digital-menus/](#get-apimodulesrestaurantsmenudigital-menus) - Get Digital Menus
- [POST /api/modules/restaurants/menu/digital-menus/](#post-apimodulesrestaurantsmenudigital-menus) - Create Digital Menu
- [GET /api/modules/restaurants/menu/digital-menus/current](#get-apimodulesrestaurantsmenudigital-menuscurrent) - Get Current Digital Menu
- [PUT /api/modules/restaurants/menu/digital-menus/reorder](#put-apimodulesrestaurantsmenudigital-menusreorder) - Reorder Digital Menus
- [DELETE /api/modules/restaurants/menu/digital-menus/{menu_id}](#delete-apimodulesrestaurantsmenudigital-menusmenu-id) - Delete Digital Menu
- [GET /api/modules/restaurants/menu/digital-menus/{menu_id}](#get-apimodulesrestaurantsmenudigital-menusmenu-id) - Get Digital Menu
- [PUT /api/modules/restaurants/menu/digital-menus/{menu_id}](#put-apimodulesrestaurantsmenudigital-menusmenu-id) - Update Digital Menu

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### DigitalMenuCreate

**Descrição:** Schema for creating a new Digital Menu.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Name of the digital menu (e.g., Breakfast Menu) |
| `description` | unknown | ❌ | Optional description for the menu |
| `is_active` | boolean | ❌ | Whether the menu is currently active |
| `display_order` | integer | ❌ | Order in which the menu should be displayed |
| `schedule_enabled` | boolean | ❌ | Whether time-based scheduling is enabled for this menu |
| `schedule_config` | unknown | ❌ | JSON configuration for menu scheduling (timezone, time ranges, days) |

### DigitalMenuRead

**Descrição:** Schema for reading a Digital Menu, includes the ID and metadata.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Name of the digital menu (e.g., Breakfast Menu) |
| `description` | unknown | ❌ | Optional description for the menu |
| `is_active` | boolean | ❌ | Whether the menu is currently active |
| `display_order` | integer | ❌ | Order in which the menu should be displayed |
| `schedule_enabled` | boolean | ❌ | Whether time-based scheduling is enabled for this menu |
| `schedule_config` | unknown | ❌ | JSON configuration for menu scheduling (timezone, time ranges, days) |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### DigitalMenuUpdate

**Descrição:** Schema for updating an existing Digital Menu. All fields are optional.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `display_order` | unknown | ❌ | - |
| `schedule_enabled` | unknown | ❌ | - |
| `schedule_config` | unknown | ❌ | - |

### DigitalMenuWithCategories

**Descrição:** Schema for reading a Digital Menu with its categories.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Name of the digital menu (e.g., Breakfast Menu) |
| `description` | unknown | ❌ | Optional description for the menu |
| `is_active` | boolean | ❌ | Whether the menu is currently active |
| `display_order` | integer | ❌ | Order in which the menu should be displayed |
| `schedule_enabled` | boolean | ❌ | Whether time-based scheduling is enabled for this menu |
| `schedule_config` | unknown | ❌ | JSON configuration for menu scheduling (timezone, time ranges, days) |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `categories` | Array[MenuCategoryRead] | ❌ | List of categories in this menu |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/modules/restaurants/menu/digital-menus/ {#get-apimodulesrestaurantsmenudigital-menus}

**Resumo:** Get Digital Menus
**Descrição:** Get all digital menus for the current tenant.
Requires tenant owner or staff permissions.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `include_categories` | boolean | query | ❌ | Include categories in response |
| `skip` | integer | query | ❌ | Number of records to skip |
| `limit` | integer | query | ❌ | Number of records to return |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/menu/digital-menus/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/restaurants/menu/digital-menus/ {#post-apimodulesrestaurantsmenudigital-menus}

**Resumo:** Create Digital Menu
**Descrição:** Create a new digital menu.
Requires tenant owner or staff permissions.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [DigitalMenuCreate](#digitalmenucreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [DigitalMenuRead](#digitalmenuread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/restaurants/menu/digital-menus/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/restaurants/menu/digital-menus/current {#get-apimodulesrestaurantsmenudigital-menuscurrent}

**Resumo:** Get Current Digital Menu
**Descrição:** Get the currently active digital menu based on time scheduling.
Requires tenant owner or staff permissions.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `timezone` | string | query | ❌ | Timezone for schedule checking |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [DigitalMenuWithCategories](#digitalmenuwithcategories)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/menu/digital-menus/current" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/restaurants/menu/digital-menus/reorder {#put-apimodulesrestaurantsmenudigital-menusreorder}

**Resumo:** Reorder Digital Menus
**Descrição:** Reorder digital menus by updating their display_order.
Requires tenant owner or staff permissions.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** {'type': 'array', 'items': {'type': 'object', 'additionalProperties': True}, 'title': 'Menu Orders'}

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/menu/digital-menus/reorder" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/restaurants/menu/digital-menus/{menu_id} {#delete-apimodulesrestaurantsmenudigital-menusmenu-id}

**Resumo:** Delete Digital Menu
**Descrição:** Hard delete a digital menu and all related data (categories, items, variants, modifiers, optionals).
This action is irreversible. Requires tenant owner or staff permissions.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `menu_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [DigitalMenuRead](#digitalmenuread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/restaurants/menu/digital-menus/{menu_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/menu/digital-menus/{menu_id} {#get-apimodulesrestaurantsmenudigital-menusmenu-id}

**Resumo:** Get Digital Menu
**Descrição:** Get a specific digital menu by ID.
Requires tenant owner or staff permissions.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `menu_id` | string | path | ✅ | - |
| `include_categories` | boolean | query | ❌ | Include categories in response |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [DigitalMenuWithCategories](#digitalmenuwithcategories)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/menu/digital-menus/{menu_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/restaurants/menu/digital-menus/{menu_id} {#put-apimodulesrestaurantsmenudigital-menusmenu-id}

**Resumo:** Update Digital Menu
**Descrição:** Update a digital menu.
Requires tenant owner or staff permissions.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `menu_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [DigitalMenuUpdate](#digitalmenuupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [DigitalMenuRead](#digitalmenuread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/menu/digital-menus/{menu_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
