"""
Restaurant Operating Hours specific schemas.
"""

from typing import Optional, List, Dict, Any
from datetime import date

from pydantic import BaseModel, Field, field_validator


class RestaurantTimeSlotSchema(BaseModel):
    """Schema for individual time slots in restaurant schedule."""

    id: str = Field(..., description="Unique identifier for the time slot")
    open: str = Field(
        ..., 
        pattern=r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$', 
        description="Opening time in HH:MM format"
    )
    close: str = Field(
        ..., 
        pattern=r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$', 
        description="Closing time in HH:MM format"
    )
    type: str = Field(
        ..., 
        description="Type of time slot: service, break, or happy_hour"
    )
    label: Optional[str] = Field(
        None, 
        max_length=100, 
        description="Optional label for the time slot"
    )
    description: Optional[str] = Field(
        None, 
        max_length=200, 
        description="Optional description"
    )

    @field_validator('type')
    @classmethod
    def validate_type(cls, v):
        """Validate time slot type."""
        allowed_types = ['service', 'break', 'happy_hour']
        if v not in allowed_types:
            raise ValueError(f'Time slot type must be one of: {allowed_types}')
        return v

    @field_validator('close')
    @classmethod
    def validate_close_after_open(cls, v, info):
        """Validate that close time is after open time."""
        if info.data and 'open' in info.data:
            # Handle overnight hours (e.g., 22:00 - 02:00)
            open_time = info.data['open']
            if v < open_time:
                # This might be overnight, which is valid
                # Additional validation can be added if needed
                pass
        return v


class RestaurantDayScheduleSchema(BaseModel):
    """Schema for a restaurant day's complete schedule."""

    is_open: bool = Field(
        False, 
        description="Whether the restaurant is open on this day"
    )
    service_hours: List[RestaurantTimeSlotSchema] = Field(
        default_factory=list, 
        description="Main service hours when customers can be served"
    )
    break_periods: List[RestaurantTimeSlotSchema] = Field(
        default_factory=list, 
        description="Break periods when service is unavailable"
    )
    happy_hour: List[RestaurantTimeSlotSchema] = Field(
        default_factory=list, 
        description="Happy hour periods with special pricing"
    )

    # Legacy support for backward compatibility
    slots: Optional[List[Dict[str, Any]]] = Field(
        None, 
        description="Legacy slots format (deprecated)"
    )

    @field_validator('service_hours', 'break_periods', 'happy_hour')
    @classmethod
    def validate_time_slots_no_overlap(cls, v, info):
        """Validate that time slots don't overlap within the same type."""
        if len(v) <= 1:
            return v

        # Sort slots by open time
        sorted_slots = sorted(v, key=lambda x: x.open)

        for i in range(len(sorted_slots) - 1):
            current_close = sorted_slots[i].close
            next_open = sorted_slots[i + 1].open

            if current_close > next_open:
                raise ValueError(f'Overlapping time slots in {info.field_name}')

        return v


class RestaurantOperatingHoursSchema(BaseModel):
    """Schema for complete restaurant operating hours configuration."""

    monday: Optional[RestaurantDayScheduleSchema] = None
    tuesday: Optional[RestaurantDayScheduleSchema] = None
    wednesday: Optional[RestaurantDayScheduleSchema] = None
    thursday: Optional[RestaurantDayScheduleSchema] = None
    friday: Optional[RestaurantDayScheduleSchema] = None
    saturday: Optional[RestaurantDayScheduleSchema] = None
    sunday: Optional[RestaurantDayScheduleSchema] = None


class RestaurantSpecialEventSchema(BaseModel):
    """Schema for special calendar events."""

    is_closed: Optional[bool] = Field(
        None, 
        description="Whether restaurant is closed on this date"
    )
    reason: str = Field(
        ..., 
        max_length=200, 
        description="Reason for special event or closure"
    )
    special_hours: Optional[Dict[str, str]] = Field(
        None, 
        description="Special operating hours for this date"
    )
    note: Optional[str] = Field(
        None, 
        max_length=500, 
        description="Additional notes for this special event"
    )

    @field_validator('special_hours')
    @classmethod
    def validate_special_hours(cls, v):
        """Validate special hours format."""
        if v is not None:
            required_fields = ['open', 'close']
            for field in required_fields:
                if field not in v:
                    raise ValueError(f'Special hours must include {field}')

                # Validate time format
                time_pattern = r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$'
                import re
                if not re.match(time_pattern, v[field]):
                    raise ValueError(f'Invalid time format for {field}: {v[field]}')

        return v


class RestaurantSpecialCalendarSchema(BaseModel):
    """Schema for restaurant special calendar configuration."""

    events: Dict[str, RestaurantSpecialEventSchema] = Field(
        default_factory=dict,
        description="Special events by date (YYYY-MM-DD format)"
    )

    @field_validator('events')
    @classmethod
    def validate_date_format(cls, v):
        """Validate date format in events keys."""
        import re
        date_pattern = r'^\d{4}-\d{2}-\d{2}$'

        for date_str in v.keys():
            if not re.match(date_pattern, date_str):
                raise ValueError(f'Invalid date format: {date_str}. Use YYYY-MM-DD')

        return v


class RestaurantOperatingHoursUpdate(BaseModel):
    """Schema for updating restaurant operating hours."""
    
    operating_hours: Optional[Dict[str, Any]] = Field(
        None, 
        description="Operating hours configuration by day of week"
    )


class RestaurantSpecialCalendarUpdate(BaseModel):
    """Schema for updating restaurant special calendar."""
    
    special_calendar: Optional[Dict[str, Any]] = Field(
        None, 
        description="Special calendar events by date"
    )
