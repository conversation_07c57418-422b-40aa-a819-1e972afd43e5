"""Domain service for Domain Rent module."""

import logging  # noqa: E402
import uuid
from datetime import datetime, date
from typing import Dict, List, Optional, Any, Tuple

from sqlalchemy.ext.asyncio import AsyncSession  # noqa: E402
from sqlalchemy.future import select
from sqlalchemy import or_, and_

from app.modules.shared.domain_rent.models.domain_registration import (  # noqa: E402
    DomainRegistration,
    DomainStatus,
)
from app.modules.shared.domain_rent.models.domain_contact import (  # noqa: E402
    DomainContact,
    ContactType,
)
from app.modules.shared.domain_rent.models.domain_nameserver import DomainNameserver  # noqa: E402
from app.modules.shared.domain_rent.schemas.domain_schemas import (
    DomainAvailabilityResult,
    DomainRegistrationRequest,
    DomainTransferRequest,
    DomainRenewalRequest,
    NameserverUpdateRequest,
    WhoisPrivacyRequest,
    AutoRenewRequest,
    ContactInfo,
)
from app.modules.shared.domain_rent.services.config_service import ConfigService  # noqa: E402
from app.modules.shared.domain_rent.exceptions import (
    DomainRentError,
    DomainNotAvailableError,
    RegistrarApiError,
)


logger = logging.getLogger(__name__)


class DomainService:
    """Service for domain operations."""

    def __init__(self, db: AsyncSession, config_service: ConfigService):
        """Initialize domain service.

        Args:
            db: Database session
            config_service: Configuration service
        """
        self.db = db
        self.config_service = config_service

    async def check_domain_availability(
        self, domain_name: str, tlds: Optional[List[str]] = None
    ) -> List[DomainAvailabilityResult]:
        """Check domain availability across all registrars.

        Args:
            domain_name: Domain name without TLD
            tlds: List of TLDs to check (if None, check common TLDs)

        Returns:
            List of availability results for each domain/TLD combination
        """
        processor_aggregator = self.config_service.get_processor_aggregator()
        return await processor_aggregator.search_availability(domain_name, tlds)

    async def register_domain(
        self,
        user_id: uuid.UUID,
        request: DomainRegistrationRequest,
        tenant_id: Optional[uuid.UUID] = None,
    ) -> DomainRegistration:
        """Register a new domain.

        Args:
            user_id: ID of the user registering the domain
            request: Domain registration request
            tenant_id: ID of the tenant (optional)

        Returns:
            Registered domain

        Raises:
            DomainNotAvailableError: If the domain is not available
            RegistrarApiError: If the registration fails
        """
        # Determine registrar to use
        registrar = request.registrar
        if not registrar:
            # Check availability and get best registrar
            availability_results = await self.check_domain_availability(
                request.domain_name, [request.tld]
            )
            if not availability_results:
                raise DomainNotAvailableError(
                    f"Domain {request.domain_name}.{request.tld} is not available"
                )

            processor_aggregator = self.config_service.get_processor_aggregator()
            registrar = processor_aggregator.get_best_registrar(availability_results)
            if not registrar:
                raise DomainNotAvailableError(
                    f"Domain {request.domain_name}.{request.tld} is not available from any registrar"  # noqa: E501
                )

        # Get processor for the selected registrar
        processor = self.config_service.get_processor(registrar)

        # Register domain with the registrar
        registration_result = await processor.register_domain(
            domain_name=request.domain_name,
            tld=request.tld,
            period_years=request.period_years,
            contacts=request.contacts,
            nameservers=request.nameservers,
            whois_privacy=request.whois_privacy,
        )

        # Create domain registration record
        domain_registration = DomainRegistration(
            user_id=user_id,
            tenant_id=tenant_id,
            domain_name=request.domain_name,
            tld=request.tld,
            registrar=registrar,
            registration_date=datetime.now(),
            expiry_date=(
                datetime.fromisoformat(registration_result["expiry_date"])
                if registration_result.get("expiry_date")
                else datetime.now()
            ),
            status=DomainStatus.ACTIVE,
            auto_renew=request.auto_renew,
            whois_privacy_enabled=request.whois_privacy,
            registrar_data=registration_result.get("registrar_data"),
        )

        self.db.add(domain_registration)
        await self.db.flush()

        # Create contact records
        for contact_type, contact_info in request.contacts.items():
            contact = DomainContact(
                domain_registration_id=domain_registration.id,
                contact_type=ContactType(contact_type),
                first_name=contact_info.first_name,
                last_name=contact_info.last_name,
                organization=contact_info.organization,
                email=contact_info.email,
                phone=contact_info.phone,
                address_line_1=contact_info.address_line_1,
                address_line_2=contact_info.address_line_2,
                city=contact_info.city,
                state_province=contact_info.state_province,
                postal_code=contact_info.postal_code,
                country=contact_info.country,
            )
            self.db.add(contact)

        # Create nameserver records
        if request.nameservers:
            for i, nameserver in enumerate(request.nameservers):
                ns = DomainNameserver(
                    domain_registration_id=domain_registration.id,
                    hostname=nameserver,
                    sort_order=i,
                )
                self.db.add(ns)

        await self.db.commit()
        await self.db.refresh(domain_registration)

        return domain_registration

    async def renew_domain(
        self,
        domain_id: uuid.UUID,
        request: DomainRenewalRequest,
    ) -> DomainRegistration:
        """Renew an existing domain.

        Args:
            domain_id: ID of the domain to renew
            request: Domain renewal request

        Returns:
            Renewed domain

        Raises:
            DomainRentError: If the domain is not found
            RegistrarApiError: If the renewal fails
        """
        # Get domain registration
        domain_registration = await self.get_domain_by_id(domain_id)
        if not domain_registration:
            raise DomainRentError(f"Domain with ID {domain_id} not found")

        # Get processor for the registrar
        processor = self.config_service.get_processor(domain_registration.registrar)

        # Renew domain with the registrar
        renewal_result = await processor.renew_domain(
            domain_name=domain_registration.domain_name,
            tld=domain_registration.tld,
            period_years=request.period_years,
            current_expiry_date=domain_registration.expiry_date.date(),
        )

        # Update domain registration record
        domain_registration.expiry_date = datetime.fromisoformat(renewal_result["expiry_date"])
        domain_registration.registrar_data = renewal_result.get("registrar_data")

        await self.db.commit()
        await self.db.refresh(domain_registration)

        return domain_registration

    async def transfer_domain(
        self,
        user_id: uuid.UUID,
        request: DomainTransferRequest,
        tenant_id: Optional[uuid.UUID] = None,
    ) -> DomainRegistration:
        """Transfer a domain from another registrar.

        Args:
            user_id: ID of the user transferring the domain
            request: Domain transfer request
            tenant_id: ID of the tenant (optional)

        Returns:
            Transferred domain

        Raises:
            RegistrarApiError: If the transfer fails
        """
        # Extract domain name and TLD from full domain
        parts = request.domain_name.split(".")
        if len(parts) < 2:
            raise DomainRentError(f"Invalid domain name: {request.domain_name}")

        domain_name = ".".join(parts[:-1])
        tld = parts[-1]

        # Determine registrar to use
        registrar = request.registrar
        if not registrar:
            # Use the first available registrar
            active_registrars = self.config_service.get_active_registrars()
            if not active_registrars:
                raise DomainRentError("No active registrars available")
            registrar = active_registrars[0]

        # Get processor for the selected registrar
        processor = self.config_service.get_processor(registrar)

        # Transfer domain with the registrar
        transfer_result = await processor.transfer_domain(
            domain_name=domain_name,
            tld=tld,
            auth_code=request.auth_code,
            contacts=request.contacts,
            nameservers=request.nameservers,
        )

        # Create domain registration record
        domain_registration = DomainRegistration(
            user_id=user_id,
            tenant_id=tenant_id,
            domain_name=domain_name,
            tld=tld,
            registrar=registrar,
            registration_date=datetime.now(),
            expiry_date=(
                datetime.fromisoformat(transfer_result["expiry_date"])
                if transfer_result.get("expiry_date")
                else datetime.now()
            ),
            status=DomainStatus.PENDING_TRANSFER,
            auto_renew=True,  # Default to auto-renew for transferred domains
            whois_privacy_enabled=False,  # Default to no WHOIS privacy
            registrar_data=transfer_result.get("registrar_data"),
        )

        self.db.add(domain_registration)
        await self.db.flush()

        # Create contact records
        for contact_type, contact_info in request.contacts.items():
            contact = DomainContact(
                domain_registration_id=domain_registration.id,
                contact_type=ContactType(contact_type),
                first_name=contact_info.first_name,
                last_name=contact_info.last_name,
                organization=contact_info.organization,
                email=contact_info.email,
                phone=contact_info.phone,
                address_line_1=contact_info.address_line_1,
                address_line_2=contact_info.address_line_2,
                city=contact_info.city,
                state_province=contact_info.state_province,
                postal_code=contact_info.postal_code,
                country=contact_info.country,
            )
            self.db.add(contact)

        # Create nameserver records
        if request.nameservers:
            for i, nameserver in enumerate(request.nameservers):
                ns = DomainNameserver(
                    domain_registration_id=domain_registration.id,
                    hostname=nameserver,
                    sort_order=i,
                )
                self.db.add(ns)

        await self.db.commit()
        await self.db.refresh(domain_registration)

        return domain_registration

    async def get_domain_by_id(self, domain_id: uuid.UUID) -> Optional[DomainRegistration]:
        """Get a domain by ID.

        Args:
            domain_id: ID of the domain

        Returns:
            Domain registration or None if not found
        """
        result = await self.db.execute(
            select(DomainRegistration).where(DomainRegistration.id == domain_id)
        )
        return result.scalars().first()

    async def get_user_domains(
        self,
        user_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID] = None,
        status: Optional[DomainStatus] = None,
    ) -> List[DomainRegistration]:
        """Get domains for a user.

        Args:
            user_id: ID of the user
            tenant_id: ID of the tenant (optional)
            status: Filter by domain status (optional)

        Returns:
            List of domain registrations
        """
        query = select(DomainRegistration).where(DomainRegistration.user_id == user_id)

        if tenant_id:
            query = query.where(DomainRegistration.tenant_id == tenant_id)

        if status:
            query = query.where(DomainRegistration.status == status)

        result = await self.db.execute(query)
        return result.scalars().all()

    async def update_nameservers(
        self,
        domain_id: uuid.UUID,
        request: NameserverUpdateRequest,
    ) -> DomainRegistration:
        """Update nameservers for a domain.

        Args:
            domain_id: ID of the domain
            request: Nameserver update request

        Returns:
            Updated domain

        Raises:
            DomainRentError: If the domain is not found
            RegistrarApiError: If the update fails
        """
        # Get domain registration
        domain_registration = await self.get_domain_by_id(domain_id)
        if not domain_registration:
            raise DomainRentError(f"Domain with ID {domain_id} not found")

        # Get processor for the registrar
        processor = self.config_service.get_processor(domain_registration.registrar)

        # Update nameservers with the registrar
        success = await processor.update_nameservers(
            domain_name=domain_registration.domain_name,
            tld=domain_registration.tld,
            nameservers=request.nameservers,
        )

        if not success:
            raise RegistrarApiError("Failed to update nameservers")

        # Delete existing nameserver records
        await self.db.execute(
            "DELETE FROM domain_nameservers WHERE domain_registration_id = :domain_id",
            {"domain_id": domain_id},
        )

        # Create new nameserver records
        for i, nameserver in enumerate(request.nameservers):
            ns = DomainNameserver(
                domain_registration_id=domain_registration.id,
                hostname=nameserver,
                sort_order=i,
            )
            self.db.add(ns)

        await self.db.commit()
        await self.db.refresh(domain_registration)

        return domain_registration

    async def toggle_whois_privacy(
        self,
        domain_id: uuid.UUID,
        request: WhoisPrivacyRequest,
    ) -> DomainRegistration:
        """Toggle WHOIS privacy for a domain.

        Args:
            domain_id: ID of the domain
            request: WHOIS privacy request

        Returns:
            Updated domain

        Raises:
            DomainRentError: If the domain is not found
            RegistrarApiError: If the update fails
        """
        # Get domain registration
        domain_registration = await self.get_domain_by_id(domain_id)
        if not domain_registration:
            raise DomainRentError(f"Domain with ID {domain_id} not found")

        # Get processor for the registrar
        processor = self.config_service.get_processor(domain_registration.registrar)

        # Toggle WHOIS privacy with the registrar
        success = await processor.toggle_whois_privacy(
            domain_name=domain_registration.domain_name,
            tld=domain_registration.tld,
            enable=request.enable,
        )

        if not success:
            raise RegistrarApiError("Failed to toggle WHOIS privacy")

        # Update domain registration record
        domain_registration.whois_privacy_enabled = request.enable

        await self.db.commit()
        await self.db.refresh(domain_registration)

        return domain_registration

    async def toggle_auto_renew(
        self,
        domain_id: uuid.UUID,
        request: AutoRenewRequest,
    ) -> DomainRegistration:
        """Toggle auto-renewal for a domain.

        Args:
            domain_id: ID of the domain
            request: Auto-renew request

        Returns:
            Updated domain

        Raises:
            DomainRentError: If the domain is not found
        """
        # Get domain registration
        domain_registration = await self.get_domain_by_id(domain_id)
        if not domain_registration:
            raise DomainRentError(f"Domain with ID {domain_id} not found")

        # Update domain registration record
        domain_registration.auto_renew = request.enable

        await self.db.commit()
        await self.db.refresh(domain_registration)

        return domain_registration

    async def get_auth_code(
        self,
        domain_id: uuid.UUID,
    ) -> str:
        """Get authorization code for domain transfer.

        Args:
            domain_id: ID of the domain

        Returns:
            Authorization code

        Raises:
            DomainRentError: If the domain is not found
            RegistrarApiError: If the request fails
        """
        # Get domain registration
        domain_registration = await self.get_domain_by_id(domain_id)
        if not domain_registration:
            raise DomainRentError(f"Domain with ID {domain_id} not found")

        # Get processor for the registrar
        processor = self.config_service.get_processor(domain_registration.registrar)

        # Get auth code from the registrar
        return await processor.get_auth_code(
            domain_name=domain_registration.domain_name,
            tld=domain_registration.tld,
        )
