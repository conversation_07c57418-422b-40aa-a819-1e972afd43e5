# Restaurant - Menu Groups

**Categoria:** Restaurant
**Módulo:** Menu Groups
**Total de Endpoints:** 6
**Gerado em:** 27/06/2025, 20:20:27

## 📋 Endpoints

- [GET /api/modules/restaurants/menu/modifier-groups](#get-apimodulesrestaurantsmenumodifier-groups) - Get Modifier Groups
- [GET /api/modules/restaurants/menu/modifier-groups/{group_id}](#get-apimodulesrestaurantsmenumodifier-groupsgroup-id) - Get Modifier Group By Id
- [GET /api/modules/restaurants/menu/optional-groups](#get-apimodulesrestaurantsmenuoptional-groups) - Get Optional Groups
- [GET /api/modules/restaurants/menu/optional-groups/{group_id}](#get-apimodulesrestaurantsmenuoptional-groupsgroup-id) - Get Optional Group By Id
- [GET /api/modules/restaurants/menu/variant-groups](#get-apimodulesrestaurantsmenuvariant-groups) - Get Variant Groups
- [GET /api/modules/restaurants/menu/variant-groups/{group_id}](#get-apimodulesrestaurantsmenuvariant-groupsgroup-id) - Get Variant Group By Id

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### ModifierGroupReadWithOptions

**Descrição:** Schema for reading a Modifier Group with options.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `menu_item_id` | unknown | ❌ | - |
| `name` | string | ✅ | - |
| `description` | unknown | ❌ | - |
| `min_selection` | integer | ✅ | - |
| `max_selection` | integer | ✅ | - |
| `display_order` | integer | ✅ | - |
| `is_required` | boolean | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `options` | Array[app__modules__tenants__restaurants__menu__schemas__modifier_option__ModifierOptionRead] | ❌ | - |
| `usage_count` | unknown | ❌ | - |

### OptionalGroupReadWithOptions

**Descrição:** Schema for reading an Optional Group with options.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `menu_item_id` | unknown | ❌ | - |
| `name` | string | ✅ | - |
| `description` | unknown | ✅ | - |
| `min_selection` | integer | ✅ | - |
| `max_selection` | integer | ✅ | - |
| `display_order` | integer | ✅ | - |
| `is_required` | boolean | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `options` | Array[app__modules__tenants__restaurants__menu__schemas__optional_option__OptionalOptionRead] | ❌ | - |
| `usage_count` | unknown | ❌ | - |

### VariantGroupReadWithOptions

**Descrição:** Schema for reading a Variant Group with options.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `menu_item_id` | unknown | ❌ | - |
| `name` | string | ✅ | - |
| `description` | unknown | ❌ | - |
| `min_selection` | integer | ✅ | - |
| `max_selection` | integer | ✅ | - |
| `display_order` | integer | ✅ | - |
| `is_required` | boolean | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `requires_default_selection` | boolean | ❌ | - |
| `options` | Array[app__modules__tenants__restaurants__menu__schemas__variant_option__VariantOptionRead] | ❌ | - |
| `usage_count` | unknown | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/modules/restaurants/menu/modifier-groups {#get-apimodulesrestaurantsmenumodifier-groups}

**Resumo:** Get Modifier Groups
**Descrição:** Retrieve modifier groups for the current tenant.
These can be used as templates for other menu items.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `digital_menu_id` | string | query | ❌ | Filter groups by digital menu ID |
| `include_usage_count` | boolean | query | ❌ | Include usage count for each group |
| `skip` | integer | query | ❌ | Number of records to skip |
| `limit` | integer | query | ❌ | Maximum number of records to return |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/menu/modifier-groups" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/menu/modifier-groups/{group_id} {#get-apimodulesrestaurantsmenumodifier-groupsgroup-id}

**Resumo:** Get Modifier Group By Id
**Descrição:** Retrieve a specific modifier group by ID.
Used for template functionality.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `group_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ModifierGroupReadWithOptions](#modifiergroupreadwithoptions)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/menu/modifier-groups/{group_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/menu/optional-groups {#get-apimodulesrestaurantsmenuoptional-groups}

**Resumo:** Get Optional Groups
**Descrição:** Retrieve optional groups for the current tenant.
These can be used as templates for other menu items.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `digital_menu_id` | string | query | ❌ | Filter groups by digital menu ID |
| `include_usage_count` | boolean | query | ❌ | Include usage count for each group |
| `skip` | integer | query | ❌ | Number of records to skip |
| `limit` | integer | query | ❌ | Maximum number of records to return |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/menu/optional-groups" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/menu/optional-groups/{group_id} {#get-apimodulesrestaurantsmenuoptional-groupsgroup-id}

**Resumo:** Get Optional Group By Id
**Descrição:** Retrieve a specific optional group by ID.
Used for template functionality.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `group_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [OptionalGroupReadWithOptions](#optionalgroupreadwithoptions)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/menu/optional-groups/{group_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/menu/variant-groups {#get-apimodulesrestaurantsmenuvariant-groups}

**Resumo:** Get Variant Groups
**Descrição:** Retrieve variant groups for the current tenant.
These can be used as templates for other menu items.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `digital_menu_id` | string | query | ❌ | Filter groups by digital menu ID |
| `include_usage_count` | boolean | query | ❌ | Include usage count for each group |
| `skip` | integer | query | ❌ | Number of records to skip |
| `limit` | integer | query | ❌ | Maximum number of records to return |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/menu/variant-groups" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/menu/variant-groups/{group_id} {#get-apimodulesrestaurantsmenuvariant-groupsgroup-id}

**Resumo:** Get Variant Group By Id
**Descrição:** Retrieve a specific variant group by ID.
Used for template functionality.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `group_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [VariantGroupReadWithOptions](#variantgroupreadwithoptions)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/menu/variant-groups/{group_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
