'use client';

import { useState, useEffect, useCallback } from 'react';
import {
  CalendarDaysIcon,
  PlusIcon,
  ExclamationTriangleIcon,
  ChartBarIcon,
  FunnelIcon,
  Cog6ToothIcon,
  ClockIcon,
  XMarkIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';
import { ModuleLayout, StatsCards, StatCard } from '@/components/dashboard/shared';

// Import our new components
import { ReservationModal } from '@/app/dashboard/restaurant/reservations/components/ReservationModal';
import { ReservationFilters } from '@/app/dashboard/restaurant/reservations/components/ReservationFilters';
import { ReservationCard } from '@/app/dashboard/restaurant/reservations/components/ReservationCard';
import { ReservationStats } from '@/app/dashboard/restaurant/reservations/components/ReservationStats';
import { BlacklistManager } from '@/app/dashboard/restaurant/reservations/components/BlacklistManager';
import { WaitlistManager } from '@/app/dashboard/restaurant/reservations/components/WaitlistManager';

// Import services
import {
  Reservation as ApiReservation,
  ReservationCreate,
  ReservationUpdate,
  ReservationFilters as IReservationFilters,
  reservationService
} from '@/services/api/reservationService';

// Import component types
import { Reservation } from '@/app/dashboard/restaurant/reservations/components/types';

type ViewMode = 'list' | 'day' | 'week' | 'stats';

// Convert API reservation to component reservation format
const convertApiReservation = (apiReservation: ApiReservation): Reservation => {
  // Extract time from reservation_date if it includes time, otherwise use a default
  const reservationDate = new Date(apiReservation.reservation_date);
  const dateOnly = reservationDate.toISOString().split('T')[0];
  const timeOnly = reservationDate.toTimeString().slice(0, 5);

  return {
    id: apiReservation.id,
    customer_name: apiReservation.guest_name || 'Unknown Guest',
    customer_phone: apiReservation.guest_phone || '',
    customer_email: apiReservation.guest_email,
    party_size: apiReservation.party_size,
    reservation_date: dateOnly,
    reservation_time: timeOnly,
    status: apiReservation.status,
    table_id: apiReservation.table_id,
    table: apiReservation.table ? {
      id: apiReservation.table_id || '',
      number: apiReservation.table.table_number || '',
      capacity: apiReservation.table.capacity || 0,
      zone: 'Main', // Default zone
      is_available: true,
      created_at: '',
      updated_at: '',
    } : undefined,
    special_requests: apiReservation.special_requests,
    notes: apiReservation.notes,
    created_at: apiReservation.created_at,
    updated_at: apiReservation.updated_at,
    tenant_id: apiReservation.tenant_id,
  };
};

export default function ReservationsPage() {
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [showReservationModal, setShowReservationModal] = useState(false);
  const [showBlacklistManager, setShowBlacklistManager] = useState(false);
  const [showWaitlistManager, setShowWaitlistManager] = useState(false);
  const [editingReservation, setEditingReservation] = useState<Reservation | null>(null);

  // Data state
  const [reservations, setReservations] = useState<Reservation[]>([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState<IReservationFilters>({
    limit: 50,
    skip: 0,
  });

  const loadReservations = useCallback(async () => {
    setLoading(true);
    try {
      const apiData = await reservationService.getReservations(filters);
      const convertedData = apiData.map(convertApiReservation);
      setReservations(convertedData);

      // Show demo mode notification on first load
      if (reservations.length === 0 && convertedData.length > 0) {
        toast.success('Demo mode: Using sample reservation data', {
          duration: 4000,
          icon: '🎭'
        });
      }

    } catch (error) {
      console.error('Error loading reservations:', error);
      toast.error('Failed to load reservations');
    } finally {
      setLoading(false);
    }
  }, [filters, reservations.length]);

  // Load reservations on component mount and when filters change
  useEffect(() => {
    loadReservations();
  }, [loadReservations]);

  // Handler functions
  const handleAddReservation = () => {
    setEditingReservation(null);
    setShowReservationModal(true);
  };

  const handleEditReservation = (reservation: Reservation) => {
    setEditingReservation(reservation);
    setShowReservationModal(true);
  };

  const handleDeleteReservation = async (reservationId: string) => {
    if (!confirm('Are you sure you want to delete this reservation?')) {
      return;
    }

    try {
      await reservationService.deleteReservation(reservationId);
      await loadReservations();
      toast.success('Reservation deleted successfully');
    } catch (error) {
      console.error('Error deleting reservation:', error);
      toast.error('Failed to delete reservation');
    }
  };

  const handleStatusChange = async (reservationId: string, newStatus: ApiReservation['status']) => {
    try {
      await reservationService.updateReservationStatus(reservationId, newStatus);
      await loadReservations();
      toast.success(`Reservation ${newStatus} successfully`);
    } catch (error) {
      console.error('Error updating reservation status:', error);
      toast.error('Failed to update reservation status');
    }
  };

  const handleSaveReservation = async (data: ReservationCreate | ReservationUpdate) => {
    try {
      if (editingReservation) {
        await reservationService.updateReservation(editingReservation.id, data as ReservationUpdate);
      } else {
        await reservationService.createReservation(data as ReservationCreate);
      }
      await loadReservations();
    } catch (error) {
      console.error('Error saving reservation:', error);
      throw error; // Re-throw to let the modal handle the error
    }
  };

  const handleFiltersChange = (newFilters: IReservationFilters) => {
    setFilters(newFilters);
  };

  const handleClearFilters = () => {
    setFilters({
      limit: 50,
      skip: 0,
    });
  };

  // Calculate stats
  const reservationStats: StatCard[] = [
    {
      name: 'Total Reservations',
      value: reservations.length,
      icon: CalendarDaysIcon,
      color: 'blue'
    },
    {
      name: 'Today\'s Reservations',
      value: reservations.filter(r => r.reservation_date === new Date().toISOString().split('T')[0]).length,
      icon: ClockIcon,
      color: 'green'
    },
    {
      name: 'Confirmed',
      value: reservations.filter(r => r.status === 'confirmed').length,
      icon: CheckCircleIcon,
      color: 'emerald'
    },
    {
      name: 'Pending',
      value: reservations.filter(r => r.status === 'pending').length,
      icon: ExclamationTriangleIcon,
      color: 'yellow'
    }
  ];

  const getReservationsForDate = (date: Date) => {
    const dateStr = date.toISOString().split('T')[0];
    return reservations.filter(res => res.reservation_date.startsWith(dateStr));
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Render functions for different views
  const renderListView = () => {
    if (loading) {
      return (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
          <p className="text-sm text-gray-500 mt-2">Loading reservations...</p>
        </div>
      );
    }

    if (reservations.length === 0) {
      return (
        <div className="text-center py-8 text-gray-500">
          <CalendarDaysIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <p>No reservations found</p>
          <p className="text-sm">Try adjusting your filters or create a new reservation</p>
        </div>
      );
    }

    return (
      <div className="columns-1 sm:columns-1 md:columns-2 lg:columns-2 xl:columns-3 2xl:columns-4 gap-6 space-y-6">
        {reservations.map(reservation => (
          <div key={reservation.id} className="break-inside-avoid mb-6">
            <ReservationCard
              reservation={reservation}
              onEdit={handleEditReservation}
              onCancel={(id) => handleStatusChange(id, 'cancelled')}
              onConfirm={(id) => handleStatusChange(id, 'confirmed')}
              onSeat={(id) => handleStatusChange(id, 'seated')}
              onComplete={(id) => handleStatusChange(id, 'completed')}
              onNoShow={(id) => handleStatusChange(id, 'no_show')}
              loading={loading}
            />
          </div>
        ))}
      </div>
    );
  };

  const renderDayView = () => {
    const dayReservations = getReservationsForDate(selectedDate);

    return (
      <div className="space-y-4">
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="p-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">
              {formatDate(selectedDate)}
            </h3>
            <p className="text-sm text-gray-600">
              {dayReservations.length} reservations
            </p>
          </div>

          <div className="p-4">
            {dayReservations.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <CalendarDaysIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <p>No reservations for this date</p>
              </div>
            ) : (
              <div className="space-y-3">
                {dayReservations
                  .sort((a, b) => new Date(a.reservation_date).getTime() - new Date(b.reservation_date).getTime())
                  .map(reservation => (
                    <ReservationCard
                      key={reservation.id}
                      reservation={reservation}
                      onEdit={handleEditReservation}
                      onCancel={(id) => handleStatusChange(id, 'cancelled')}
                      onConfirm={(id) => handleStatusChange(id, 'confirmed')}
                      onSeat={(id) => handleStatusChange(id, 'seated')}
                      onComplete={(id) => handleStatusChange(id, 'completed')}
                      onNoShow={(id) => handleStatusChange(id, 'no_show')}
                      loading={loading}
                    />
                  ))}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderWeekView = () => {
    const startOfWeek = new Date(selectedDate);
    startOfWeek.setDate(selectedDate.getDate() - selectedDate.getDay());

    const weekDays = Array.from({ length: 7 }, (_, i) => {
      const day = new Date(startOfWeek);
      day.setDate(startOfWeek.getDate() + i);
      return day;
    });

    return (
      <div className="grid grid-cols-7 gap-4">
        {weekDays.map(day => {
          const dayReservations = getReservationsForDate(day);
          const isToday = day.toDateString() === new Date().toDateString();

          return (
            <div
              key={day.toISOString()}
              className={`bg-white rounded-lg shadow-sm border ${isToday ? 'ring-2 ring-primary-500' : ''}`}
            >
              <div className="p-3 border-b border-gray-200">
                <h4 className="font-medium text-gray-900">
                  {day.toLocaleDateString('en-US', { weekday: 'short' })}
                </h4>
                <p className="text-sm text-gray-600">
                  {day.getDate()}
                </p>
              </div>
              <div className="p-2 space-y-1">
                {dayReservations.slice(0, 3).map(reservation => (
                  <ReservationCard
                    key={reservation.id}
                    reservation={reservation}
                    onEdit={handleEditReservation}
                    onCancel={(id) => handleStatusChange(id, 'cancelled')}
                    onConfirm={(id) => handleStatusChange(id, 'confirmed')}
                    onSeat={(id) => handleStatusChange(id, 'seated')}
                    onComplete={(id) => handleStatusChange(id, 'completed')}
                    onNoShow={(id) => handleStatusChange(id, 'no_show')}
                    loading={loading}
                  />
                ))}
                {dayReservations.length > 3 && (
                  <div className="text-xs text-gray-500 text-center">
                    +{dayReservations.length - 3} more
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const renderStatsView = () => {
    return (
      <ReservationStats
        reservations={reservations}
        selectedDate={selectedDate.toISOString().split('T')[0]}
      />
    );
  };

  return (
    <ModuleLayout
      title="Reservations"
      description="Manage your restaurant reservations"
      gradientFrom="purple-50"
      gradientVia="indigo-50/30"
      gradientTo="blue-50/20"
      titleGradientFrom="purple-600"
      titleGradientTo="indigo-600"
      actions={
        <div className="flex flex-wrap items-center gap-3">
          <button
            onClick={() => setShowWaitlistManager(true)}
            className="bg-yellow-600 text-white px-5 py-3 rounded-xl active:bg-yellow-700 transition-colors flex items-center space-x-2 touch-manipulation min-h-[48px] shadow-md"
          >
            <ClockIcon className="h-5 w-5" />
            <span className="font-medium">Waitlist</span>
          </button>
          <button
            onClick={() => setShowBlacklistManager(true)}
            className="bg-red-600 text-white px-5 py-3 rounded-xl active:bg-red-700 transition-colors flex items-center space-x-2 touch-manipulation min-h-[48px] shadow-md"
          >
            <ExclamationTriangleIcon className="h-5 w-5" />
            <span className="font-medium">Blacklist</span>
          </button>
          <button
            onClick={handleAddReservation}
            className="bg-primary-600 text-white px-5 py-3 rounded-xl active:bg-primary-700 transition-colors flex items-center space-x-2 touch-manipulation min-h-[48px] shadow-md"
          >
            <PlusIcon className="h-5 w-5" />
            <span className="font-medium">New Reservation</span>
          </button>
        </div>
      }
    >
      {/* Stats Cards */}
      <StatsCards stats={reservationStats} />

      {/* Filters */}
      <ReservationFilters
        filters={filters}
        onFiltersChange={handleFiltersChange}
        tables={[]} // TODO: Load tables from API
        onClearFilters={handleClearFilters}
      />

      {/* Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {(viewMode === 'day' || viewMode === 'week') && (
            <>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setSelectedDate(new Date(selectedDate.getTime() - 24 * 60 * 60 * 1000))}
                  className="p-3 text-gray-600 bg-gray-100 border border-gray-200 touch-manipulation rounded-lg active:bg-gray-200 min-h-[48px] min-w-[48px] flex items-center justify-center shadow-sm"
                >
                  <span className="text-lg">←</span>
                </button>
                <input
                  type="date"
                  value={selectedDate.toISOString().split('T')[0]}
                  onChange={(e) => setSelectedDate(new Date(e.target.value))}
                  className="px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-primary-500 focus:border-primary-500 touch-manipulation min-h-[48px] text-base shadow-sm"
                />
                <button
                  onClick={() => setSelectedDate(new Date(selectedDate.getTime() + 24 * 60 * 60 * 1000))}
                  className="p-3 text-gray-600 bg-gray-100 border border-gray-200 touch-manipulation rounded-lg active:bg-gray-200 min-h-[48px] min-w-[48px] flex items-center justify-center shadow-sm"
                >
                  <span className="text-lg">→</span>
                </button>
              </div>

              <button
                onClick={() => setSelectedDate(new Date())}
                className="px-4 py-3 text-base bg-blue-100 text-blue-700 border border-blue-200 rounded-xl active:bg-blue-200 touch-manipulation min-h-[48px] font-medium shadow-sm"
              >
                Today
              </button>
            </>
          )}
        </div>

        <div className="flex flex-wrap items-center gap-2">
          {(['list', 'day', 'week', 'stats'] as ViewMode[]).map(mode => (
            <button
              key={mode}
              onClick={() => setViewMode(mode)}
              className={`px-4 py-3 text-base font-medium rounded-xl capitalize flex items-center space-x-2 touch-manipulation min-h-[48px] transition-colors border ${
                viewMode === mode
                  ? 'bg-primary-600 text-white shadow-lg border-primary-600'
                  : 'bg-gray-100 text-gray-700 border-gray-200 active:bg-gray-200'
              }`}
            >
              {mode === 'stats' && <ChartBarIcon className="h-5 w-5" />}
              {mode === 'list' && <FunnelIcon className="h-5 w-5" />}
              {mode === 'day' && <CalendarDaysIcon className="h-5 w-5" />}
              {mode === 'week' && <Cog6ToothIcon className="h-5 w-5" />}
              <span>{mode}</span>
            </button>
          ))}
        </div>
      </div>

      {/* View Content */}
      <div>
        {viewMode === 'list' && renderListView()}
        {viewMode === 'day' && renderDayView()}
        {viewMode === 'week' && renderWeekView()}
        {viewMode === 'stats' && renderStatsView()}
      </div>

      {/* Modals */}
      <ReservationModal
        isOpen={showReservationModal}
        onClose={() => setShowReservationModal(false)}
        onSave={handleSaveReservation}
        reservation={editingReservation}
        tables={[]} // TODO: Load tables from API
        loading={loading}
      />

      {showBlacklistManager && (
        <div className="fixed inset-0 z-50 bg-black/25 backdrop-blur-sm">
          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4">
              <div className="w-full max-w-4xl bg-white rounded-xl shadow-2xl">
                <div className="flex items-center justify-between p-6 border-b border-gray-200">
                  <h2 className="text-xl font-semibold text-gray-900">Lista Negra</h2>
                  <button
                    onClick={() => setShowBlacklistManager(false)}
                    className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <XMarkIcon className="h-5 w-5 text-gray-400" />
                  </button>
                </div>
                <div className="p-6">
                  <BlacklistManager
                    blacklistEntries={[]} // TODO: Load from API
                    onAddEntry={async (entry) => {
                      console.log('Add blacklist entry:', entry);
                      // TODO: Implement API call
                    }}
                    onRemoveEntry={async (entryId) => {
                      console.log('Remove blacklist entry:', entryId);
                      // TODO: Implement API call
                    }}
                    onToggleEntry={async (entryId, isActive) => {
                      console.log('Toggle blacklist entry:', entryId, isActive);
                      // TODO: Implement API call
                    }}
                    loading={loading}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {showWaitlistManager && (
        <div className="fixed inset-0 z-50 bg-black/25 backdrop-blur-sm">
          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4">
              <div className="w-full max-w-4xl bg-white rounded-xl shadow-2xl">
                <div className="flex items-center justify-between p-6 border-b border-gray-200">
                  <h2 className="text-xl font-semibold text-gray-900">Lista de Espera</h2>
                  <button
                    onClick={() => setShowWaitlistManager(false)}
                    className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <XMarkIcon className="h-5 w-5 text-gray-400" />
                  </button>
                </div>
                <div className="p-6">
                  <WaitlistManager
                    waitlistEntries={[]} // TODO: Load from API
                    onAddEntry={async (entry) => {
                      console.log('Add waitlist entry:', entry);
                      // TODO: Implement API call
                    }}
                    onSeatEntry={async (entryId) => {
                      console.log('Seat waitlist entry:', entryId);
                      // TODO: Implement API call
                    }}
                    onCancelEntry={async (entryId) => {
                      console.log('Cancel waitlist entry:', entryId);
                      // TODO: Implement API call
                    }}
                    onNoShowEntry={async (entryId) => {
                      console.log('No show waitlist entry:', entryId);
                      // TODO: Implement API call
                    }}
                    onUpdateWaitTime={async (entryId, waitTime) => {
                      console.log('Update wait time:', entryId, waitTime);
                      // TODO: Implement API call
                    }}
                    loading={loading}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </ModuleLayout>
  );
}
