"""API endpoints for Domain Rent module."""

import logging  # noqa: E402
import uuid
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, status  # noqa: E402
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db  # noqa: E402
from app.core.security import get_current_active_user as get_current_user
from app.models.user import User
from app.modules.shared.domain_rent.models.domain_registration import (
    DomainRegistration,
    DomainStatus,
)
from app.modules.shared.domain_rent.schemas.domain_schemas import (  # noqa: E402
    DomainAvailabilityRequest,
    DomainAvailabilityResult,
    DomainRegistrationRequest,
    DomainRegistrationRead,
    DomainTransferRequest,
    DomainRenewalRequest,
    NameserverUpdateRequest,
    WhoisPrivacyRequest,
    AutoRenewRequest,
)
from app.modules.shared.domain_rent.services.config_service import ConfigService  # noqa: E402
from app.modules.shared.domain_rent.services.domain_service import DomainService
from app.modules.shared.domain_rent.exceptions import (
    DomainRentError,
    DomainNotAvailableError,
    RegistrarApiError,
)


logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/modules/shared/domain_rent", tags=["domain_rent"])


def get_config_service() -> ConfigService:
    """Get config service dependency."""
    return ConfigService()


def get_domain_service(
    db: AsyncSession = Depends(get_db),
    config_service: ConfigService = Depends(get_config_service),
) -> DomainService:
    """Get domain service dependency."""
    return DomainService(db, config_service)


@router.post("/availability", response_model=List[DomainAvailabilityResult])
async def check_domain_availability(
    request: DomainAvailabilityRequest,
    domain_service: DomainService = Depends(get_domain_service),
    current_user: User = Depends(get_current_user),
):
    """Check domain availability."""
    try:
        return await domain_service.check_domain_availability(
            domain_name=request.domain_name,
            tlds=request.tlds,
        )
    except Exception as e:
        logger.error(f"Error checking domain availability: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error checking domain availability: {str(e)}",
        )


@router.post(
    "/domains",
    response_model=DomainRegistrationRead,
    status_code=status.HTTP_201_CREATED,
)
async def register_domain(
    request: DomainRegistrationRequest,
    domain_service: DomainService = Depends(get_domain_service),
    current_user: User = Depends(get_current_user),
):
    """Register a new domain."""
    try:
        return await domain_service.register_domain(
            user_id=current_user.id,
            request=request,
            tenant_id=request.tenant_id,
        )
    except DomainNotAvailableError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except RegistrarApiError as e:
        logger.error(f"Error registering domain: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error registering domain: {str(e)}",
        )
    except Exception as e:
        logger.error(f"Unexpected error registering domain: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error: {str(e)}",
        )


@router.post(
    "/domains/transfer",
    response_model=DomainRegistrationRead,
    status_code=status.HTTP_201_CREATED,
)
async def transfer_domain(
    request: DomainTransferRequest,
    domain_service: DomainService = Depends(get_domain_service),
    current_user: User = Depends(get_current_user),
):
    """Transfer a domain from another registrar."""
    try:
        return await domain_service.transfer_domain(
            user_id=current_user.id,
            request=request,
            tenant_id=request.tenant_id,
        )
    except RegistrarApiError as e:
        logger.error(f"Error transferring domain: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error transferring domain: {str(e)}",
        )
    except Exception as e:
        logger.error(f"Unexpected error transferring domain: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error: {str(e)}",
        )


@router.get("/domains", response_model=List[DomainRegistrationRead])
async def get_user_domains(
    tenant_id: Optional[uuid.UUID] = None,
    status: Optional[DomainStatus] = None,
    domain_service: DomainService = Depends(get_domain_service),
    current_user: User = Depends(get_current_user),
):
    """Get domains for the current user."""
    try:
        return await domain_service.get_user_domains(
            user_id=current_user.id,
            tenant_id=tenant_id,
            status=status,
        )
    except Exception as e:
        logger.error(f"Error getting user domains: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting user domains: {str(e)}",
        )


@router.get("/domains/{domain_id}", response_model=DomainRegistrationRead)
async def get_domain(
    domain_id: uuid.UUID,
    domain_service: DomainService = Depends(get_domain_service),
    current_user: User = Depends(get_current_user),
):
    """Get a domain by ID."""
    try:
        domain = await domain_service.get_domain_by_id(domain_id)
        if not domain:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Domain with ID {domain_id} not found",
            )

        # Check if the domain belongs to the current user
        if domain.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to access this domain",
            )

        return domain
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting domain: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting domain: {str(e)}",
        )


@router.post("/domains/{domain_id}/renew", response_model=DomainRegistrationRead)
async def renew_domain(
    domain_id: uuid.UUID,
    request: DomainRenewalRequest,
    domain_service: DomainService = Depends(get_domain_service),
    current_user: User = Depends(get_current_user),
):
    """Renew a domain."""
    try:
        # Check if the domain belongs to the current user
        domain = await domain_service.get_domain_by_id(domain_id)
        if not domain:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Domain with ID {domain_id} not found",
            )

        if domain.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to renew this domain",
            )

        return await domain_service.renew_domain(
            domain_id=domain_id,
            request=request,
        )
    except HTTPException:
        raise
    except DomainRentError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except RegistrarApiError as e:
        logger.error(f"Error renewing domain: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error renewing domain: {str(e)}",
        )
    except Exception as e:
        logger.error(f"Unexpected error renewing domain: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error: {str(e)}",
        )


@router.put("/domains/{domain_id}/nameservers", response_model=DomainRegistrationRead)
async def update_nameservers(
    domain_id: uuid.UUID,
    request: NameserverUpdateRequest,
    domain_service: DomainService = Depends(get_domain_service),
    current_user: User = Depends(get_current_user),
):
    """Update nameservers for a domain."""
    try:
        # Check if the domain belongs to the current user
        domain = await domain_service.get_domain_by_id(domain_id)
        if not domain:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Domain with ID {domain_id} not found",
            )

        if domain.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to update this domain",
            )

        return await domain_service.update_nameservers(
            domain_id=domain_id,
            request=request,
        )
    except HTTPException:
        raise
    except DomainRentError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except RegistrarApiError as e:
        logger.error(f"Error updating nameservers: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating nameservers: {str(e)}",
        )
    except Exception as e:
        logger.error(f"Unexpected error updating nameservers: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error: {str(e)}",
        )


@router.put("/domains/{domain_id}/whois-privacy", response_model=DomainRegistrationRead)
async def toggle_whois_privacy(
    domain_id: uuid.UUID,
    request: WhoisPrivacyRequest,
    domain_service: DomainService = Depends(get_domain_service),
    current_user: User = Depends(get_current_user),
):
    """Toggle WHOIS privacy for a domain."""
    try:
        # Check if the domain belongs to the current user
        domain = await domain_service.get_domain_by_id(domain_id)
        if not domain:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Domain with ID {domain_id} not found",
            )

        if domain.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to update this domain",
            )

        return await domain_service.toggle_whois_privacy(
            domain_id=domain_id,
            request=request,
        )
    except HTTPException:
        raise
    except DomainRentError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except RegistrarApiError as e:
        logger.error(f"Error toggling WHOIS privacy: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error toggling WHOIS privacy: {str(e)}",
        )
    except Exception as e:
        logger.error(f"Unexpected error toggling WHOIS privacy: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error: {str(e)}",
        )


@router.put("/domains/{domain_id}/auto-renew", response_model=DomainRegistrationRead)
async def toggle_auto_renew(
    domain_id: uuid.UUID,
    request: AutoRenewRequest,
    domain_service: DomainService = Depends(get_domain_service),
    current_user: User = Depends(get_current_user),
):
    """Toggle auto-renewal for a domain."""
    try:
        # Check if the domain belongs to the current user
        domain = await domain_service.get_domain_by_id(domain_id)
        if not domain:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Domain with ID {domain_id} not found",
            )

        if domain.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to update this domain",
            )

        return await domain_service.toggle_auto_renew(
            domain_id=domain_id,
            request=request,
        )
    except HTTPException:
        raise
    except DomainRentError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"Unexpected error toggling auto-renew: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error: {str(e)}",
        )


@router.get("/domains/{domain_id}/auth-code")
async def get_auth_code(
    domain_id: uuid.UUID,
    domain_service: DomainService = Depends(get_domain_service),
    current_user: User = Depends(get_current_user),
):
    """Get authorization code for domain transfer."""
    try:
        # Check if the domain belongs to the current user
        domain = await domain_service.get_domain_by_id(domain_id)
        if not domain:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Domain with ID {domain_id} not found",
            )

        if domain.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to access this domain",
            )

        auth_code = await domain_service.get_auth_code(domain_id)
        return {"auth_code": auth_code}
    except HTTPException:
        raise
    except DomainRentError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except RegistrarApiError as e:
        logger.error(f"Error getting auth code: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting auth code: {str(e)}",
        )
    except Exception as e:
        logger.error(f"Unexpected error getting auth code: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error: {str(e)}",
        )


@router.get("/registrars")
async def get_active_registrars(
    config_service: ConfigService = Depends(get_config_service),
    _: User = Depends(get_current_user),  # Authentication required but user not used
):
    """Get active registrars."""
    try:
        return {"registrars": config_service.get_active_registrars()}
    except Exception as e:
        logger.error(f"Error getting active registrars: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting active registrars: {str(e)}",
        )
