'use client';

import { useState, useMemo } from 'react';
import { DndContext, closestCenter } from '@dnd-kit/core';
import {
  CubeIcon,
  TagIcon,
  CurrencyDollarIcon,
  ArchiveBoxIcon
} from '@heroicons/react/24/outline';
import { StatsCards, StatCard } from '@/components/dashboard/shared';
import { CatalogEditorLayout } from './CatalogEditorLayout';
import { ProductModal } from './ProductModal';
import { CategoryModal } from './CategoryModal';

export function CatalogEditor() {
  // State management
  const [categories, setCategories] = useState([
    {
      id: '1',
      name: 'Electronics',
      description: 'Electronic devices and accessories',
      display_order: 1,
      is_active: true
    },
    {
      id: '2', 
      name: 'Clothing',
      description: 'Fashion and apparel',
      display_order: 2,
      is_active: true
    }
  ]);

  const [products, setProducts] = useState([
    {
      id: '1',
      name: 'Smartphone Pro Max',
      description: 'Latest smartphone with advanced features',
      category_id: '1',
      base_price: 1299.99,
      stock_quantity: 15,
      is_active: true,
      images: []
    },
    {
      id: '2',
      name: 'Wireless Headphones',
      description: 'High-quality wireless headphones',
      category_id: '1',
      base_price: 199.99,
      stock_quantity: 8,
      is_active: true,
      images: []
    }
  ]);

  const [selectedCategory, setSelectedCategory] = useState<any>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showPreview, setShowPreview] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeId, setActiveId] = useState<string | null>(null);
  const [dragType, setDragType] = useState<'category' | 'product' | null>(null);
  const [savingProduct, setSavingProduct] = useState(false);

  // Modal states
  const [showProductModal, setShowProductModal] = useState(false);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [editingProduct, setEditingProduct] = useState(null);
  const [editingCategory, setEditingCategory] = useState(null);

  // Computed values
  const displayCategories = useMemo(() => {
    return categories.filter(cat => cat.is_active)
      .sort((a, b) => a.display_order - b.display_order);
  }, [categories]);

  const filteredProducts = useMemo(() => {
    let filtered = products.filter(product => product.is_active);

    if (selectedCategory) {
      filtered = filtered.filter(product => product.category_id === (selectedCategory as any).id);
    }

    if (searchTerm) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return filtered;
  }, [products, selectedCategory, searchTerm]);

  // Stats calculation
  const catalogStats: StatCard[] = useMemo(() => {
    const totalProducts = products.filter(p => p.is_active).length;
    const totalCategories = categories.filter(c => c.is_active).length;
    const totalValue = products
      .filter(p => p.is_active)
      .reduce((sum, p) => sum + (p.base_price * p.stock_quantity), 0);
    const lowStockItems = products
      .filter(p => p.is_active && p.stock_quantity < 10).length;

    return [
      {
        name: 'Total Products',
        value: totalProducts,
        icon: CubeIcon,
        color: 'emerald'
      },
      {
        name: 'Categories',
        value: totalCategories,
        icon: TagIcon,
        color: 'green'
      },
      {
        name: 'Inventory Value',
        value: `$${totalValue.toLocaleString()}`,
        icon: CurrencyDollarIcon,
        color: 'blue'
      },
      {
        name: 'Low Stock Items',
        value: lowStockItems,
        icon: ArchiveBoxIcon,
        color: lowStockItems > 0 ? 'red' : 'gray'
      }
    ];
  }, [products, categories]);

  // Handlers
  const handleCreateCategory = () => {
    setEditingCategory(null);
    setShowCategoryModal(true);
  };

  const handleEditCategory = (category: any) => {
    setEditingCategory(category);
    setShowCategoryModal(true);
  };

  const handleDeleteCategory = (categoryId: any) => {
    if (confirm('Are you sure you want to delete this category?')) {
      setCategories(prev => prev.filter(cat => cat.id !== categoryId));
    }
  };

  const handleCreateProduct = (categoryId: any) => {
    setEditingProduct(null);
    setShowProductModal(true);
  };

  const handleEditProduct = (product: any) => {
    setEditingProduct(product);
    setShowProductModal(true);
  };

  const handleDeleteProduct = (productId: any) => {
    if (confirm('Are you sure you want to delete this product?')) {
      setProducts(prev => prev.filter(prod => prod.id !== productId));
    }
  };

  const handleCategorySelect = (categoryId: any) => {
    const category = categories.find(cat => cat.id === categoryId);
    setSelectedCategory(category || null);
  };

  const handleDragStart = (event: any) => {
    setActiveId(event.active.id);
    // Determine drag type based on data
    const isDraggingCategory = categories.some(cat => cat.id === event.active.id);
    setDragType(isDraggingCategory ? 'category' : 'product');
  };

  const handleDragEnd = (event: any) => {
    setActiveId(null);
    setDragType(null);
    // Handle reordering logic here
  };

  return (
    <DndContext
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="space-y-6">
        {/* Stats Cards */}
        <StatsCards stats={catalogStats} />

        <div className="h-full flex flex-col">
          <CatalogEditorLayout
          displayCategories={displayCategories}
          selectedCategory={selectedCategory}
          filteredProducts={filteredProducts}
          products={products}
          categories={categories}
          viewMode={viewMode}
          showPreview={showPreview}
          searchTerm={searchTerm}
          selectedCategoryForView={selectedCategory?.id || ''}
          activeId={activeId}
          dragType={dragType}
          savingProduct={savingProduct}
          handleCreateCategory={handleCreateCategory}
          handleCreateProduct={handleCreateProduct}
          handleEditCategory={handleEditCategory}
          handleDeleteCategory={handleDeleteCategory}
          handleEditProduct={handleEditProduct}
          handleDeleteProduct={handleDeleteProduct}
          handleCategorySelect={handleCategorySelect}
          setViewMode={setViewMode}
          setShowPreview={setShowPreview}
          setSearchTerm={setSearchTerm}
        />
        </div>
      </div>

      {/* Modals */}
      {showProductModal && (
        <ProductModal
          product={editingProduct}
          categories={categories}
          onClose={() => setShowProductModal(false)}
          onSave={(productData) => {
            // Handle save logic
            setShowProductModal(false);
          }}
        />
      )}

      {showCategoryModal && (
        <CategoryModal
          category={editingCategory}
          onClose={() => setShowCategoryModal(false)}
          onSave={(categoryData) => {
            // Handle save logic
            setShowCategoryModal(false);
          }}
        />
      )}
    </DndContext>
  );
}
