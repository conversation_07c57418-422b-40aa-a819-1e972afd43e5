/**
 * Store Zustand para Gerenciamento de Estado do POS
 * Estado centralizado para carrinho, pedidos, mesas e UI
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import {
  MenuItem,
  MenuCategory,
  POSOrder,
  Table,
  PaymentMethod,
  CartItem,
  POSState,
  ItemCustomization,
  ConnectionStatus,
  SyncItem,
  DigitalMenu,
  TableStatus,
} from '@/types/pos';
import { posService } from '@/services/posService';
import { tableService } from '@/services/api/tableService';

interface POSStore extends POSState {
  // ===============================
  // ACTIONS - MENU MANAGEMENT
  // ===============================

  loadMenuData: () => Promise<void>;
  setActiveCategory: (categoryId: string) => void;
  setSearchQuery: (query: string) => void;

  // ===============================
  // ACTIONS - CART MANAGEMENT
  // ===============================

  addToCart: (customization: ItemCustomization) => void;
  updateCartItem: (itemId: string, updates: Partial<CartItem>) => void;
  removeFromCart: (itemId: string) => void;
  clearCart: () => void;

  // ===============================
  // ACTIONS - ORDER MANAGEMENT
  // ===============================

  createNewOrder: (tableId?: string) => void;
  updateOrder: (updates: Partial<POSOrder>) => void;
  finalizeOrder: () => Promise<POSOrder | null>;
  sendToKitchen: () => Promise<void>;

  // ===============================
  // ACTIONS - TABLE MANAGEMENT
  // ===============================

  loadTables: () => Promise<void>;
  loadAvailableTables: (capacity?: number) => Promise<void>;
  loadTablesByZone: (zone: string) => Promise<void>;
  selectTable: (table: Table | null) => void;
  updateTableStatus: (tableId: string, status: Table['status']) => Promise<void>;

  // ===============================
  // ACTIONS - PAYMENT
  // ===============================

  loadPaymentMethods: () => Promise<void>;
  processPayment: (methodId: string, amount: number) => Promise<boolean>;
  togglePaymentModal: (show?: boolean) => void;

  // ===============================
  // OFFLINE & SYNC ACTIONS
  // ===============================

  setOfflineMode: (offline: boolean) => void;
  setConnectionStatus: (status: ConnectionStatus) => void;
  addToSyncQueue: (item: SyncItem) => void;
  removeFromSyncQueue: (itemId: string) => void;
  triggerManualSync: () => Promise<void>;

  // ===============================
  // UTILITIES
  // ===============================

  calculateSubtotal: () => number;
  calculateTax: (subtotal: number) => number;
  calculateTotal: () => number;
  calculateTotals: () => {
    subtotal: number;
    tax: number;
    total: number;
  };

  reset: () => void;
}

export const usePOSStore = create<POSStore>()(
  devtools(
    persist(
      (set, get) => ({
        // ===============================
        // INITIAL STATE
        // ===============================

        // Menu Data
        currentMenu: null,
        categories: [],
        menuItems: [],

        // Current Order
        currentOrder: null,
        cart: [],
        selectedTable: null,

        // UI State
        activeCategory: 'all',
        searchQuery: '',
        isLoading: false,

        // Payment
        availablePaymentMethods: [],
        showPaymentModal: false,

        // Tables
        tables: [],
        tableLayout: null,

        // Offline State
        isOfflineMode: false,
        connectionStatus: { isOnline: true, lastChecked: new Date().toISOString() },
        pendingSync: [],
        lastSyncTime: null,

        // ===============================
        // MENU ACTIONS
        // ===============================

        loadMenuData: async () => {
          console.log('🍽️ POS Store: Starting loadMenuData...');
          set({ isLoading: true });
          try {
            console.log('🍽️ POS Store: Calling posService methods...');
            const [categories, items] = await Promise.all([
              posService.getMenuCategories(),
              posService.getMenuItems()
            ]);

            console.log(`✅ POS Store: Loaded ${categories.length} categories and ${items.length} items`);
            console.log('🍽️ POS Store: Categories:', categories);
            console.log('🍽️ POS Store: Items:', items);

            set({
              categories,
              menuItems: items,
              isLoading: false
            });

            console.log('🍽️ POS Store: Menu data set in store successfully');
          } catch (error) {
            console.error('❌ POS Store: Error loading menu data:', error);
            set({ isLoading: false });
          }
        },

        setActiveCategory: (categoryId: string) => {
          set({ activeCategory: categoryId });
        },

        setSearchQuery: (query: string) => {
          set({ searchQuery: query });
        },

        // ===============================
        // CART ACTIONS
        // ===============================

        addToCart: (customization: ItemCustomization) => {
          const { cart, currentOrder, createNewOrder } = get();

          // Criar novo order se não existir
          if (!currentOrder) {
            createNewOrder();
          }

          const itemId = `${customization.menu_item_id}_${Date.now()}`;

          // Find the menu item
          const { menuItems } = get();
          const menuItem = menuItems.find(item => item.id === customization.menu_item_id);

          if (!menuItem) {
            console.error('Menu item not found:', customization.menu_item_id);
            return;
          }

          // Calculate price with modifiers and variants
          let unitPrice = menuItem.price;

          // Add modifier price adjustments
          customization.selected_modifiers.forEach(modifier => {
            unitPrice += modifier.price_adjustment;
          });

          // Add variant price adjustments
          customization.selected_variants.forEach(variant => {
            unitPrice += variant.price_adjustment;
          });

          // Add optional price adjustments
          customization.selected_optionals.forEach(optional => {
            unitPrice += optional.price_adjustment;
          });

          const quantity = customization.quantity;
          const totalPrice = unitPrice * quantity;

          const cartItem: CartItem = {
            id: itemId,
            menu_item: menuItem,
            quantity,
            unit_price: unitPrice,
            total_price: totalPrice,
            selected_variants: customization.selected_variants,
            selected_modifiers: customization.selected_modifiers,
            selected_optionals: customization.selected_optionals,
            special_instructions: customization.special_instructions,
            created_at: new Date().toISOString(),
          };

          set({ cart: [...cart, cartItem] });
        },

        updateCartItem: (itemId: string, updates: Partial<CartItem>) => {
          const { cart } = get();

          set({
            cart: cart.map(item => {
              if (item.id === itemId) {
                const updatedItem = { ...item, ...updates };
                // Recalcular total se quantidade mudou
                if (updates.quantity !== undefined) {
                  updatedItem.total_price = updatedItem.unit_price * updatedItem.quantity;
                }
                return updatedItem;
              }
              return item;
            })
          });
        },

        removeFromCart: (itemId: string) => {
          const { cart } = get();
          set({ cart: cart.filter(item => item.id !== itemId) });
        },

        clearCart: () => {
          set({ cart: [] });
        },

        // ===============================
        // ORDER ACTIONS
        // ===============================

        createNewOrder: (tableId?: string) => {
          const { selectedTable } = get();
          const table = tableId ? { id: tableId } as Table : selectedTable;

          const newOrder: POSOrder = {
            id: `order_${Date.now()}`,
            order_number: `ORD-${Date.now().toString().slice(-6)}`,
            table_id: table?.id,
            items: [],
            subtotal: 0,
            tax_amount: 0,
            discount_amount: 0,
            total_amount: 0,
            status: 'draft',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };

          set({ currentOrder: newOrder });
        },

        updateOrder: (updates: Partial<POSOrder>) => {
          const { currentOrder } = get();
          if (currentOrder) {
            set({
              currentOrder: {
                ...currentOrder,
                ...updates,
                updated_at: new Date().toISOString(),
              }
            });
          }
        },

        finalizeOrder: async () => {
          const { currentOrder, cart, calculateTotals, selectedTable } = get();

          if (!currentOrder || cart.length === 0) {
            return null;
          }

          try {
            const totals = calculateTotals();

            // Converter dados do POS para formato OrderCreate
            const orderCreateData: Partial<POSOrder> = {
              table_id: selectedTable?.id,
              subtotal: totals.subtotal,
              tax_amount: totals.tax,
              discount_amount: 0,
              total_amount: totals.total,
              notes: currentOrder.notes || undefined,
              status: 'draft',
              items: cart
            };

            const savedOrder = await posService.createOrder(orderCreateData);

            // Atualizar currentOrder com dados do backend
            const updatedOrder: POSOrder = {
              ...currentOrder,
              id: savedOrder.id,
              order_number: savedOrder.order_number,
              items: cart,
              subtotal: totals.subtotal,
              tax_amount: totals.tax,
              total_amount: totals.total,
              status: 'pending_payment',
              table_id: selectedTable?.id,
              user_id: savedOrder.user_id,
              tenant_id: savedOrder.tenant_id
            };

            set({
              currentOrder: updatedOrder,
              cart: [],
            });

            return updatedOrder;
          } catch (error) {
            console.error('Error finalizing order:', error);
            return null;
          }
        },

        sendToKitchen: async () => {
          const { currentOrder } = get();

          if (!currentOrder) {
            throw new Error('No active order to send to kitchen');
          }

          try {
            await posService.sendOrderToKitchen(currentOrder);

            set({
              currentOrder: {
                ...currentOrder,
                status: 'preparing',
                updated_at: new Date().toISOString(),
              }
            });
          } catch (error) {
            console.error('Error sending order to kitchen:', error);
            throw error;
          }
        },

        // ===============================
        // TABLE ACTIONS
        // ===============================

        loadTables: async (filters = {}) => {
          try {
            // Verificar se há token de acesso e tenant ID
            const accessToken = typeof window !== 'undefined' ? document.cookie.includes('access_token') : false;
            const tenantId = typeof window !== 'undefined' ? document.cookie.includes('tenant_id') : false;

            if (!accessToken || !tenantId) {
              console.warn('User not authenticated or no tenant selected. Using mock data.');
              throw new Error('Authentication required');
            }

            const tables = await tableService.getTables(filters);
            set({ tables });
          } catch (error) {
            console.error('Error loading tables:', error);
            // Fallback para mock data se API falhar
            const mockTables: Table[] = Array.from({ length: 24 }, (_, i) => ({
              id: `table-${i + 1}`,
              tenant_id: 'mock-tenant',
              table_number: `${i + 1}`,
              name: `Mesa ${i + 1}`,
              capacity: Math.floor(Math.random() * 6) + 2,
              zone: i < 12 ? 'Zona 1' : 'Zona da Terraça',
              status: ['available', 'occupied', 'reserved'][Math.floor(Math.random() * 3)] as TableStatus,
              position_x: (i % 6) * 120,
              position_y: Math.floor(i / 6) * 120,
              width: 100,
              height: 100,
              is_active: true,
              qrcode_enabled: true,
              // Legacy support
              number: `${i + 1}`,
              position: {
                x: (i % 6) * 120,
                y: Math.floor(i / 6) * 120,
                width: 100,
                height: 100,
              }
            }));
            set({ tables: mockTables });
          }
        },

        loadAvailableTables: async (capacity?: number, layoutId?: string) => {
          try {
            const tables = await tableService.getAvailableTables({ capacity, layout_id: layoutId });
            set({ tables });
          } catch (error) {
            console.error('Error loading available tables:', error);
            // Fallback para filtrar mock data
            await get().loadTables();
            const { tables } = get();
            const availableTables = tables.filter(table =>
              table.status === 'available' &&
              (!capacity || table.capacity >= capacity)
            );
            set({ tables: availableTables });
          }
        },

        loadTablesByZone: async (zone: string) => {
          try {
            const tables = await tableService.getTables({ zone });
            set({ tables });
          } catch (error) {
            console.error('Error loading tables by zone:', error);
            // Fallback para filtrar mock data por zona
            await get().loadTables();
            const { tables } = get();
            const zoneFilteredTables = tables.filter(table => table.zone === zone);
            set({ tables: zoneFilteredTables });
          }
        },

        selectTable: (table: Table | null) => {
          set({ selectedTable: table });
        },

        updateTableStatus: async (tableId: string, status: TableStatus) => {
          try {
            const updatedTable = await tableService.updateTableStatus(tableId, status);

            // Atualizar a mesa na lista local
            const { tables } = get();
            const updatedTables = tables.map(table =>
              table.id === tableId ? updatedTable : table
            );
            set({ tables: updatedTables });
          } catch (error) {
            console.error('Error updating table status:', error);
            throw error;
          }
        },

        createTable: async (tableData: any) => {
          try {
            const newTable = await tableService.createTable(tableData);

            // Adicionar nova mesa à lista local
            const { tables } = get();
            set({ tables: [...tables, newTable] });

            return newTable;
          } catch (error) {
            console.error('Error creating table:', error);
            throw error;
          }
        },

        updateTable: async (tableId: string, tableData: any) => {
          try {
            const updatedTable = await tableService.updateTable(tableId, tableData);

            // Atualizar a mesa na lista local
            const { tables } = get();
            const updatedTables = tables.map(table =>
              table.id === tableId ? updatedTable : table
            );
            set({ tables: updatedTables });

            return updatedTable;
          } catch (error) {
            console.error('Error updating table:', error);
            throw error;
          }
        },

        deleteTable: async (tableId: string) => {
          try {
            await tableService.deleteTable(tableId);

            // Remover mesa da lista local
            const { tables } = get();
            const filteredTables = tables.filter(table => table.id !== tableId);
            set({ tables: filteredTables });
          } catch (error) {
            console.error('Error deleting table:', error);
            throw error;
          }
        },

        // ===============================
        // PAYMENT ACTIONS
        // ===============================

        loadPaymentMethods: async () => {
          try {
            const methods = await posService.getPaymentMethods();
            set({ availablePaymentMethods: methods });
          } catch (error) {
            console.error('Error loading payment methods, using defaults:', error);
            // Use default payment methods as fallback
            set({
              availablePaymentMethods: [
                {
                  id: 'cash',
                  name: 'Dinheiro',
                  type: 'cash',
                  is_active: true,
                  icon: '💵'
                },
                {
                  id: 'credit_card',
                  name: 'Cartão de Crédito',
                  type: 'card',
                  is_active: true,
                  icon: '💳'
                },
                {
                  id: 'debit_card',
                  name: 'Cartão de Débito',
                  type: 'card',
                  is_active: true,
                  icon: '💳'
                },
                {
                  id: 'pix',
                  name: 'PIX',
                  type: 'pix',
                  is_active: true,
                  icon: '📱'
                }
              ]
            });
          }
        },

        processPayment: async (methodId: string, amount: number) => {
          const { currentOrder } = get();

          if (!currentOrder) {
            throw new Error('No active order for payment');
          }

          try {
            const result = await posService.processPayment(currentOrder.id, {
              method_id: methodId,
              amount,
            });

            if (result.success) {
              set({
                currentOrder: {
                  ...currentOrder,
                  status: 'paid',
                  updated_at: new Date().toISOString(),
                },
                showPaymentModal: false,
              });

              return true;
            }

            return false;
          } catch (error) {
            console.error('Error processing payment:', error);
            return false;
          }
        },

        togglePaymentModal: (show?: boolean) => {
          const { showPaymentModal } = get();
          set({ showPaymentModal: show ?? !showPaymentModal });
        },

        // ===============================
        // OFFLINE & SYNC ACTIONS
        // ===============================

        setOfflineMode: (offline: boolean) => {
          set({ isOfflineMode: offline });
        },

        setConnectionStatus: (status: ConnectionStatus) => {
          set({ connectionStatus: status });
        },

        addToSyncQueue: (item: SyncItem) => {
          const { pendingSync } = get();
          set({ pendingSync: [...pendingSync, item] });
        },

        removeFromSyncQueue: (itemId: string) => {
          const { pendingSync } = get();
          set({ pendingSync: pendingSync.filter(item => item.id !== itemId) });
        },

        triggerManualSync: async () => {
          const { pendingSync } = get();

          if (pendingSync.length === 0) return;

          try {
            // Process sync queue
            for (const item of pendingSync) {
              try {
                await posService.syncItem(item);
                get().removeFromSyncQueue(item.id);
              } catch (error) {
                console.error(`Failed to sync item ${item.id}:`, error);
              }
            }

            // Update last sync time on successful completion
            set({ lastSyncTime: new Date().toISOString() });
          } catch (error) {
            console.error('Manual sync failed:', error);
            throw error;
          }
        },

        // ===============================
        // UTILITIES
        // ===============================

        calculateSubtotal: () => {
          const { cart } = get();
          return cart.reduce((sum, item) => sum + item.total_price, 0);
        },

        calculateTax: (subtotal: number) => {
          return subtotal * 0.1; // 10% tax - should come from settings
        },

        calculateTotal: () => {
          const subtotal = get().calculateSubtotal();
          const tax = get().calculateTax(subtotal);
          return subtotal + tax;
        },

        calculateTotals: () => {
          const { cart } = get();

          const subtotal = cart.reduce((sum, item) => sum + item.total_price, 0);
          const tax = subtotal * 0.1; // 10% tax - should come from settings
          const total = subtotal + tax;

          return { subtotal, tax, total };
        },

        reset: () => {
          set({
            currentOrder: null,
            cart: [],
            selectedTable: null,
            activeCategory: 'all',
            searchQuery: '',
            showPaymentModal: false,
          });
        },
      }),
      {
        name: 'pos-store',
        partialize: (state) => ({
          // Persistir apenas dados necessários
          selectedTable: state.selectedTable,
          cart: state.cart,
          currentOrder: state.currentOrder,
        }),
      }
    ),
    { name: 'POS Store' }
  )
);

export default usePOSStore;