"""Financial Control WebSocket Manager."""

import json
from typing import Dict, Any, List
from uuid import UUID
import socketio

from app.core.socketio_manager import sio_server as sio


class ControlWebSocketManager:
    """WebSocket manager for financial control real-time updates."""
    
    def __init__(self):
        self.namespace = "/financial-control"
    
    async def emit_entry_created(self, tenant_id: UUID, entry_data: Dict[str, Any]):
        """Emit event when a new control entry is created."""
        
        await sio.emit(
            "entry_created",
            {
                "type": "entry_created",
                "tenant_id": str(tenant_id),
                "entry": entry_data,
                "timestamp": entry_data.get("created_at")
            },
            room=f"tenant_{tenant_id}",
            namespace=self.namespace
        )
    
    async def emit_entry_updated(self, tenant_id: UUID, entry_data: Dict[str, Any]):
        """Emit event when a control entry is updated."""
        
        await sio.emit(
            "entry_updated",
            {
                "type": "entry_updated",
                "tenant_id": str(tenant_id),
                "entry": entry_data,
                "timestamp": entry_data.get("updated_at")
            },
            room=f"tenant_{tenant_id}",
            namespace=self.namespace
        )
    
    async def emit_entry_status_changed(
        self, 
        tenant_id: UUID, 
        entry_id: UUID, 
        old_status: str, 
        new_status: str
    ):
        """Emit event when entry status changes."""
        
        await sio.emit(
            "entry_status_changed",
            {
                "type": "entry_status_changed",
                "tenant_id": str(tenant_id),
                "entry_id": str(entry_id),
                "old_status": old_status,
                "new_status": new_status,
                "timestamp": json.dumps({"timestamp": "now"})  # Would use actual timestamp
            },
            room=f"tenant_{tenant_id}",
            namespace=self.namespace
        )
    
    async def emit_payment_processed(
        self, 
        tenant_id: UUID, 
        entry_id: UUID, 
        payment_data: Dict[str, Any]
    ):
        """Emit event when payment is processed."""
        
        await sio.emit(
            "payment_processed",
            {
                "type": "payment_processed",
                "tenant_id": str(tenant_id),
                "entry_id": str(entry_id),
                "payment": payment_data,
                "timestamp": payment_data.get("payment_date")
            },
            room=f"tenant_{tenant_id}",
            namespace=self.namespace
        )
    
    async def emit_budget_alert(
        self, 
        tenant_id: UUID, 
        category_id: UUID, 
        alert_data: Dict[str, Any]
    ):
        """Emit budget alert."""
        
        await sio.emit(
            "budget_alert",
            {
                "type": "budget_alert",
                "tenant_id": str(tenant_id),
                "category_id": str(category_id),
                "alert": alert_data,
                "severity": alert_data.get("severity", "warning"),
                "timestamp": json.dumps({"timestamp": "now"})
            },
            room=f"tenant_{tenant_id}",
            namespace=self.namespace
        )
    
    async def emit_report_generated(
        self, 
        tenant_id: UUID, 
        report_id: UUID, 
        report_data: Dict[str, Any]
    ):
        """Emit event when report is generated."""
        
        await sio.emit(
            "report_generated",
            {
                "type": "report_generated",
                "tenant_id": str(tenant_id),
                "report_id": str(report_id),
                "report": report_data,
                "timestamp": report_data.get("completed_at")
            },
            room=f"tenant_{tenant_id}",
            namespace=self.namespace
        )
    
    async def emit_metrics_updated(self, tenant_id: UUID, metrics_data: Dict[str, Any]):
        """Emit event when financial metrics are updated."""
        
        await sio.emit(
            "metrics_updated",
            {
                "type": "metrics_updated",
                "tenant_id": str(tenant_id),
                "metrics": metrics_data,
                "timestamp": json.dumps({"timestamp": "now"})
            },
            room=f"tenant_{tenant_id}",
            namespace=self.namespace
        )
    
    async def join_tenant_room(self, sid: str, tenant_id: UUID):
        """Join user to tenant room for real-time updates."""
        
        await sio.enter_room(sid, f"tenant_{tenant_id}", namespace=self.namespace)
    
    async def leave_tenant_room(self, sid: str, tenant_id: UUID):
        """Remove user from tenant room."""
        
        await sio.leave_room(sid, f"tenant_{tenant_id}", namespace=self.namespace)


# Global instance
control_ws_manager = ControlWebSocketManager()


# WebSocket event handlers
@sio.event(namespace="/financial-control")
async def connect(sid, environ, auth):
    """Handle client connection."""
    print(f"Financial Control WebSocket client connected: {sid}")


@sio.event(namespace="/financial-control")
async def disconnect(sid):
    """Handle client disconnection."""
    print(f"Financial Control WebSocket client disconnected: {sid}")


@sio.event(namespace="/financial-control")
async def join_tenant(sid, data):
    """Handle joining tenant room."""
    tenant_id = data.get("tenant_id")
    if tenant_id:
        await control_ws_manager.join_tenant_room(sid, UUID(tenant_id))
        await sio.emit(
            "joined_tenant",
            {"tenant_id": tenant_id},
            room=sid,
            namespace="/financial-control"
        )


@sio.event(namespace="/financial-control")
async def leave_tenant(sid, data):
    """Handle leaving tenant room."""
    tenant_id = data.get("tenant_id")
    if tenant_id:
        await control_ws_manager.leave_tenant_room(sid, UUID(tenant_id))
        await sio.emit(
            "left_tenant",
            {"tenant_id": tenant_id},
            room=sid,
            namespace="/financial-control"
        )
