import logging
from typing import Annotated, <PERSON>, TYPE_CHECKING

from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.roles.models.roles import SystemRole
from app.modules.core.auth.dependencies.user_auth import (
    get_current_active_user,
    permission_denied_exception,
)

if TYPE_CHECKING:
    from app.modules.core.users.models.user import User

logger = logging.getLogger(__name__)


def require_system_role(required_roles: List[SystemRole]):
    """
    Factory function to check if the current user has the required system role.

    Args:
        required_roles: List of SystemRole values that are allowed to access the endpoint.

    Returns:
        A dependency function that checks if the user has the required role.
    """

    async def system_role_checker(
        current_user: Annotated["User", Depends(get_current_active_user)],
    ) -> "User":
        """
        Checks if the current user has one of the required system roles.

        Args:
            current_user: The User object from the get_current_active_user dependency.

        Returns:
            The User object if the user has the required role.

        Raises:
            HTTPException: If the user doesn't have the required role.
        """
        # Convert required_roles (List[SystemRole]) to List[str] for comparison
        required_role_values = [r.value for r in required_roles]

        if current_user.system_role not in required_role_values:
            logger.warning(
                f"system_role_checker: User {current_user.id} with role '{current_user.system_role}' "
                f"does not have required role. Required: {required_role_values}"
            )
            raise permission_denied_exception

        logger.info(
            f"system_role_checker: User {current_user.id} with role '{current_user.system_role}' "
            f"has required role. Access granted."
        )
        return current_user

    return system_role_checker


async def require_admin_user(
    current_user: Annotated["User", Depends(get_current_active_user)],
) -> "User":
    """
    Dependency to check if the current user has the admin system role.

    Args:
        current_user: The User object from the get_current_active_user dependency.

    Returns:
        The User object if the user has the admin role.

    Raises:
        HTTPException: If the user doesn't have the admin role.
    """
    if current_user.system_role != SystemRole.ADMIN.value:
        logger.warning(
            f"require_admin_user: User {current_user.id} with role '{current_user.system_role}' "
            f"does not have admin role. Access denied."
        )
        raise permission_denied_exception

    logger.info(
        f"require_admin_user: User {current_user.id} with role '{current_user.system_role}' "
        f"has admin role. Access granted."
    )
    return current_user
