"""
EShop Analytics Schemas

Pydantic schemas for analytics and reporting endpoints.
"""

from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field
from decimal import Decimal


# =====================================================================
# BASE ANALYTICS SCHEMAS
# =====================================================================

class TopProductResponse(BaseModel):
    """Schema for top selling product data."""
    
    id: str = Field(..., description="Product ID")
    name: str = Field(..., description="Product name")
    vendor_name: str = Field(..., description="Vendor name")
    total_sales: int = Field(..., description="Total number of sales")
    units_sold: int = Field(..., description="Total units sold")
    revenue: float = Field(..., description="Total revenue generated")
    market_type: str = Field(..., description="Market type (B2B or PUBLIC)")


class TopVendorResponse(BaseModel):
    """Schema for top performing vendor data."""
    
    id: str = Field(..., description="Vendor ID")
    name: str = Field(..., description="Vendor name")
    business_name: str = Field(..., description="Business name")
    total_orders: int = Field(..., description="Total number of orders")
    total_revenue: float = Field(..., description="Total revenue generated")
    commission_earned: float = Field(..., description="Total commission earned")
    rating: float = Field(..., description="Average vendor rating")
    market_type: str = Field(..., description="Market type (B2B or PUBLIC)")


class RecentOrderResponse(BaseModel):
    """Schema for recent order data."""
    
    id: str = Field(..., description="Order ID")
    order_number: str = Field(..., description="Order number")
    customer_name: str = Field(..., description="Customer name")
    vendor_name: str = Field(..., description="Vendor name")
    total_amount: float = Field(..., description="Order total amount")
    status: str = Field(..., description="Order status")
    created_at: str = Field(..., description="Order creation timestamp")
    market_type: str = Field(..., description="Market type (B2B or PUBLIC)")


class MonthlyRevenueResponse(BaseModel):
    """Schema for monthly revenue data."""
    
    month: str = Field(..., description="Month name")
    revenue: float = Field(..., description="Total revenue for the month")
    orders: int = Field(..., description="Total orders for the month")
    b2b_revenue: float = Field(..., description="B2B revenue for the month")
    public_revenue: float = Field(..., description="Public revenue for the month")


# =====================================================================
# DASHBOARD ANALYTICS SCHEMAS
# =====================================================================

class DashboardStatsResponse(BaseModel):
    """Schema for comprehensive dashboard statistics."""
    
    total_revenue: float = Field(..., description="Total revenue")
    total_orders: int = Field(..., description="Total number of orders")
    total_customers: int = Field(..., description="Total number of customers")
    total_vendors: int = Field(..., description="Total number of vendors")
    avg_order_value: float = Field(..., description="Average order value")
    conversion_rate: float = Field(..., description="Conversion rate percentage")
    revenue_growth: float = Field(..., description="Revenue growth percentage")
    orders_growth: float = Field(..., description="Orders growth percentage")
    customers_growth: float = Field(..., description="Customers growth percentage")
    top_selling_products: List[TopProductResponse] = Field(
        ..., description="List of top selling products"
    )
    top_vendors: List[TopVendorResponse] = Field(
        ..., description="List of top performing vendors"
    )
    recent_orders: List[RecentOrderResponse] = Field(
        ..., description="List of recent orders"
    )
    revenue_by_month: List[MonthlyRevenueResponse] = Field(
        ..., description="Monthly revenue breakdown"
    )


# =====================================================================
# COMMISSION ANALYTICS SCHEMAS
# =====================================================================

class CommissionByVendorResponse(BaseModel):
    """Schema for commission data by vendor."""
    
    vendor_id: str = Field(..., description="Vendor ID")
    vendor_name: str = Field(..., description="Vendor name")
    total_commission: float = Field(..., description="Total commission earned")
    commission_rate: float = Field(..., description="Commission rate percentage")


class CommissionAnalyticsResponse(BaseModel):
    """Schema for commission analytics data."""
    
    total_commission_paid: float = Field(..., description="Total commission paid")
    avg_commission_rate: float = Field(..., description="Average commission rate")
    monthly_commission: float = Field(..., description="Monthly commission amount")
    top_earning_seller: str = Field(..., description="Top earning seller name")
    commission_by_vendor: List[CommissionByVendorResponse] = Field(
        ..., description="Commission breakdown by vendor"
    )


# =====================================================================
# PRODUCT ANALYTICS SCHEMAS
# =====================================================================

class TopCategoryResponse(BaseModel):
    """Schema for top category data."""
    
    category_id: str = Field(..., description="Category ID")
    category_name: str = Field(..., description="Category name")
    product_count: int = Field(..., description="Number of products in category")
    total_revenue: float = Field(..., description="Total revenue from category")


class StockAlertResponse(BaseModel):
    """Schema for stock alert data."""
    
    product_id: str = Field(..., description="Product ID")
    product_name: str = Field(..., description="Product name")
    current_stock: int = Field(..., description="Current stock level")
    min_stock: int = Field(..., description="Minimum stock threshold")


class ProductAnalyticsResponse(BaseModel):
    """Schema for product analytics data."""
    
    total_products: int = Field(..., description="Total number of products")
    active_products: int = Field(..., description="Number of active products")
    pending_approval: int = Field(..., description="Number of products pending approval")
    top_categories: List[TopCategoryResponse] = Field(
        ..., description="Top performing categories"
    )
    stock_alerts: List[StockAlertResponse] = Field(
        ..., description="Products with low stock alerts"
    )


# =====================================================================
# REAL-TIME METRICS SCHEMAS
# =====================================================================

class SystemHealthResponse(BaseModel):
    """Schema for system health metrics."""
    
    uptime: str = Field(..., description="System uptime percentage")
    response_time: str = Field(..., description="Average response time")
    error_rate: str = Field(..., description="Error rate percentage")


class RealTimeMetricsResponse(BaseModel):
    """Schema for real-time metrics data."""
    
    active_sessions: int = Field(..., description="Number of active sessions")
    orders_today: int = Field(..., description="Orders placed today")
    revenue_today: float = Field(..., description="Revenue generated today")
    pending_approvals: int = Field(..., description="Pending product approvals")
    system_health: SystemHealthResponse = Field(..., description="System health metrics")
    last_updated: str = Field(..., description="Last update timestamp")


# =====================================================================
# EXPORT SCHEMAS
# =====================================================================

class ExportRequest(BaseModel):
    """Schema for analytics export requests."""
    
    analytics_type: str = Field(..., description="Type of analytics to export")
    format: str = Field(..., description="Export format (csv, xlsx, pdf)")
    time_range: Optional[str] = Field(None, description="Time range filter")
    market_type: Optional[str] = Field(None, description="Market type filter")
    date_from: Optional[str] = Field(None, description="Start date filter")
    date_to: Optional[str] = Field(None, description="End date filter")


class ExportResponse(BaseModel):
    """Schema for export response."""
    
    message: str = Field(..., description="Export status message")
    download_url: Optional[str] = Field(None, description="Download URL for the exported file")
    file_size: Optional[int] = Field(None, description="File size in bytes")
    expires_at: Optional[datetime] = Field(None, description="Download link expiration")


# =====================================================================
# FILTER SCHEMAS
# =====================================================================

class AnalyticsFilters(BaseModel):
    """Schema for analytics filtering options."""
    
    time_range: Optional[str] = Field(None, description="Predefined time range")
    market_type: Optional[str] = Field(None, description="Market type filter")
    date_from: Optional[str] = Field(None, description="Custom start date")
    date_to: Optional[str] = Field(None, description="Custom end date")
    vendor_ids: Optional[List[str]] = Field(None, description="Specific vendor IDs")
    category_ids: Optional[List[str]] = Field(None, description="Specific category IDs")
    product_ids: Optional[List[str]] = Field(None, description="Specific product IDs")


# =====================================================================
# HEALTH CHECK SCHEMA
# =====================================================================

class AnalyticsHealthResponse(BaseModel):
    """Schema for analytics service health check."""
    
    status: str = Field(..., description="Service status")
    service: str = Field(..., description="Service name")
    features: List[str] = Field(..., description="Available features")
    timestamp: str = Field(..., description="Health check timestamp")
