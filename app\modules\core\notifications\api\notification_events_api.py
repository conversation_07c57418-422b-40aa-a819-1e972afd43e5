"""
Notification Events API

Endpoints para eventos de notificação do sistema.
"""

import logging
from typing import Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.user_auth import get_current_active_user
from app.modules.core.auth.dependencies.role_auth import require_admin_user
from app.modules.core.users.models.user import User

from ..services.notification_integration_service import NotificationIntegrationService
from ..services.email_notification_service import EmailNotificationService

logger = logging.getLogger(__name__)
router = APIRouter()


# ===== B2B EVENTS =====

@router.post("/b2b/approval-request")
async def trigger_b2b_approval_request(
    user_id: UUID,
    user_type: str,
    tenant_id: Optional[UUID] = None,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Dispara notificação de solicitação de aprovação B2B.
    
    Args:
        user_id: ID do usuário solicitante
        user_type: Tipo de aprovação (tcustomer/tvendor_supplier)
        tenant_id: ID do tenant (opcional)
    """
    if user_type not in ["tcustomer", "tvendor_supplier"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Tipo de usuário inválido. Use 'tcustomer' ou 'tvendor_supplier'"
        )

    integration_service = NotificationIntegrationService(db)
    
    result = await integration_service.handle_b2b_approval_request(
        user_id, user_type, tenant_id
    )
    
    if result["status"] == "error":
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result["message"]
        )
    
    return result


@router.post("/b2b/approval-status")
async def trigger_b2b_approval_status(
    user_id: UUID,
    user_type: str,
    approval_status: str,
    reason: Optional[str] = None,
    current_user: User = Depends(require_admin_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Dispara notificação de mudança de status de aprovação B2B.
    
    Args:
        user_id: ID do usuário
        user_type: Tipo de aprovação
        approval_status: Status (approved/rejected)
        reason: Motivo (se rejeitado)
    """
    if approval_status not in ["approved", "rejected"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Status inválido. Use 'approved' ou 'rejected'"
        )

    integration_service = NotificationIntegrationService(db)
    
    result = await integration_service.handle_b2b_approval_status(
        user_id, user_type, approval_status, reason
    )
    
    if result["status"] == "error":
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result["message"]
        )
    
    return result


# ===== ORDER EVENTS =====

@router.post("/orders/status-change")
async def trigger_order_status_change(
    order_id: UUID,
    customer_id: UUID,
    old_status: str,
    new_status: str,
    tenant_id: Optional[UUID] = None,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Dispara notificação de mudança de status de pedido.
    
    Args:
        order_id: ID do pedido
        customer_id: ID do cliente
        old_status: Status anterior
        new_status: Novo status
        tenant_id: ID do tenant
    """
    integration_service = NotificationIntegrationService(db)
    
    result = await integration_service.handle_order_status_change(
        order_id, customer_id, old_status, new_status, tenant_id
    )
    
    if result["status"] == "error":
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result["message"]
        )
    
    return result


@router.post("/orders/new-order")
async def trigger_new_order(
    order_id: UUID,
    customer_id: UUID,
    tenant_id: UUID,
    order_total: float,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Dispara notificação de novo pedido.
    
    Args:
        order_id: ID do pedido
        customer_id: ID do cliente
        tenant_id: ID do tenant
        order_total: Valor total do pedido
    """
    integration_service = NotificationIntegrationService(db)
    
    result = await integration_service.handle_new_order(
        order_id, customer_id, tenant_id, order_total
    )
    
    if result["status"] == "error":
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result["message"]
        )
    
    return result


# ===== AUCTION EVENTS =====

@router.post("/auctions/new-bid")
async def trigger_auction_bid(
    auction_id: UUID,
    bidder_id: UUID,
    bid_amount: float,
    previous_bidder_id: Optional[UUID] = None,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Dispara notificação de novo lance em leilão.
    
    Args:
        auction_id: ID do leilão
        bidder_id: ID do licitante
        bid_amount: Valor do lance
        previous_bidder_id: ID do licitante anterior
    """
    integration_service = NotificationIntegrationService(db)
    
    result = await integration_service.handle_auction_bid(
        auction_id, bidder_id, bid_amount, previous_bidder_id
    )
    
    if result["status"] == "error":
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result["message"]
        )
    
    return result


@router.post("/auctions/end")
async def trigger_auction_end(
    auction_id: UUID,
    winner_id: Optional[UUID] = None,
    winning_bid: Optional[float] = None,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Dispara notificação de fim de leilão.
    
    Args:
        auction_id: ID do leilão
        winner_id: ID do vencedor
        winning_bid: Lance vencedor
    """
    integration_service = NotificationIntegrationService(db)
    
    result = await integration_service.handle_auction_end(
        auction_id, winner_id, winning_bid
    )
    
    if result["status"] == "error":
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result["message"]
        )
    
    return result


# ===== INVOICE EVENTS =====

@router.post("/invoices/generated")
async def trigger_invoice_generated(
    invoice_id: UUID,
    customer_id: UUID,
    tenant_id: UUID,
    invoice_total: float,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Dispara notificação de fatura gerada.
    
    Args:
        invoice_id: ID da fatura
        customer_id: ID do cliente
        tenant_id: ID do tenant
        invoice_total: Valor da fatura
    """
    integration_service = NotificationIntegrationService(db)
    
    result = await integration_service.handle_invoice_generated(
        invoice_id, customer_id, tenant_id, invoice_total
    )
    
    if result["status"] == "error":
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result["message"]
        )
    
    return result


# ===== SYSTEM EVENTS =====

@router.post("/system/maintenance")
async def trigger_system_maintenance(
    title: str,
    message: str,
    start_time: str,
    end_time: str,
    current_user: User = Depends(require_admin_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Dispara notificação de manutenção do sistema.
    
    Args:
        title: Título da manutenção
        message: Mensagem detalhada
        start_time: Horário de início
        end_time: Horário de fim
    """
    integration_service = NotificationIntegrationService(db)
    
    result = await integration_service.handle_system_maintenance(
        title, message, start_time, end_time
    )
    
    if result["status"] == "error":
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result["message"]
        )
    
    return result


# ===== EMAIL TESTING =====

@router.post("/email/test")
async def send_test_email(
    to_email: str,
    current_user: User = Depends(require_admin_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Envia email de teste.
    
    Args:
        to_email: Email de destino
    """
    email_service = EmailNotificationService(db)
    
    success = await email_service.send_test_email(to_email)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Falha ao enviar email de teste"
        )
    
    return {"message": f"Email de teste enviado para {to_email}", "success": True}
