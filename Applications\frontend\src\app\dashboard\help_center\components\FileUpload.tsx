'use client';

import { useState, useEffect, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { useAuth } from '@/lib/auth/AuthProvider';
import { apiClient } from '@/lib/api/client';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import {
  CloudArrowUpIcon
} from '@heroicons/react/24/outline';
import { FileGrid } from './FilePreview';

interface UploadedFile {
  id: string;
  filename: string;
  file_size: number;
  mime_type: string;
  created_at: string;
  url?: string;
}

interface FileUploadProps {
  ticketId: string;
}

export function FileUpload({ ticketId }: FileUploadProps) {
  const { user } = useAuth();
  const [files, setFiles] = useState<UploadedFile[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [error, setError] = useState<string | null>(null);
  const [dragActive, setDragActive] = useState(false);

  const fetchFiles = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await apiClient.get(
        `/modules/core/help-center/tickets/${ticketId}/files`
      );

      setFiles((response as any).files || []);
    } catch (err: any) {
      console.error('Erro ao carregar arquivos:', err);
      setError('Erro ao carregar arquivos do ticket.');
    } finally {
      setIsLoading(false);
    }
  }, [ticketId]);

  useEffect(() => {
    fetchFiles();
  }, [ticketId, fetchFiles]);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;

    const file = acceptedFiles[0]; // Only handle one file at a time

    try {
      setIsUploading(true);
      setUploadProgress(0);
      setError(null);

      const formData = new FormData();
      formData.append('file', file);

      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      await apiClient.post(
        `/modules/core/help-center/tickets/${ticketId}/upload`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: (progressEvent) => {
            if (progressEvent.total) {
              const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
              setUploadProgress(progress);
            }
          }
        }
      );

      clearInterval(progressInterval);
      setUploadProgress(100);

      // Refresh file list
      await fetchFiles();

      // Reset progress after a short delay
      setTimeout(() => {
        setUploadProgress(0);
      }, 1000);
    } catch (err: any) {
      console.error('Erro ao fazer upload:', err);
      setError(err.response?.data?.detail || 'Erro ao fazer upload do arquivo.');
      setUploadProgress(0);
    } finally {
      setIsUploading(false);
    }
  }, [ticketId, fetchFiles]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    onDragEnter: () => setDragActive(true),
    onDragLeave: () => setDragActive(false),
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp'],
      'application/pdf': ['.pdf'],
      'text/plain': ['.txt'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/zip': ['.zip'],
      'application/x-rar-compressed': ['.rar']
    },
    maxSize: 50 * 1024 * 1024, // 50MB
    multiple: false,
    disabled: isUploading
  });

  const handleDelete = async (fileId: string) => {
    if (!confirm('Tem certeza que deseja deletar este arquivo?')) {
      return;
    }

    try {
      await apiClient.delete(`/modules/core/help-center/files/${fileId}`);
      
      // Remove from local state
      setFiles(prev => prev.filter(f => f.id !== fileId));
    } catch (err: any) {
      console.error('Erro ao deletar arquivo:', err);
      setError('Erro ao deletar arquivo.');
    }
  };

  const handleDownload = async (file: UploadedFile) => {
    try {
      if (file.url) {
        const link = document.createElement('a');
        link.href = file.url;
        link.download = file.filename;
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (err: any) {
      console.error('Erro ao baixar arquivo:', err);
      setError('Erro ao baixar arquivo.');
    }
  };



  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <LoadingSpinner size="md" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <div className="text-sm text-red-600">{error}</div>
        </div>
      )}

      {/* Upload Area */}
      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all duration-200
          ${isDragActive || dragActive
            ? 'border-blue-400 bg-blue-50 scale-105'
            : 'border-gray-300 hover:border-gray-400'
          }
          ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      >
        <input {...getInputProps()} disabled={isUploading} />

        {isUploading ? (
          <div className="space-y-4">
            <LoadingSpinner size="lg" className="mx-auto" />
            <div className="space-y-2">
              <p className="text-sm text-gray-600">Fazendo upload...</p>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
              <p className="text-xs text-gray-500">{uploadProgress}%</p>
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            <CloudArrowUpIcon className={`mx-auto h-12 w-12 transition-colors ${
              isDragActive || dragActive ? 'text-blue-500' : 'text-gray-400'
            }`} />
            <div className="text-sm text-gray-600">
              {isDragActive || dragActive ? (
                <p className="text-blue-600 font-medium">Solte o arquivo aqui...</p>
              ) : (
                <div>
                  <p>
                    <span className="font-medium text-blue-600 hover:text-blue-500">
                      Clique para fazer upload
                    </span>{' '}
                    ou arraste e solte
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    PNG, JPG, PDF, DOC, TXT, ZIP até 50MB
                  </p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Files Grid */}
      {files.length > 0 && (
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-900">
            Arquivos Anexados ({files.length})
          </h4>

          <FileGrid
            files={files}
            onDelete={handleDelete}
            onDownload={handleDownload}
          />
        </div>
      )}
    </div>
  );
}
