"""Purchase Order service for supplier system."""

import uuid
from datetime import date, datetime
from decimal import Decimal
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, or_, func, text

from app.modules.shared.supplier.models.purchase_order import (
    PurchaseOrder,
    PurchaseOrderItem,
    PurchaseOrderStatus,
    PurchaseOrderItemStatus
)
from app.modules.shared.supplier.schemas.purchase_order import (
    PurchaseOrderCreate,
    PurchaseOrderUpdate,
    PurchaseOrderSupplierUpdate,
    PurchaseOrderItemUpdate,
    PurchaseOrderItemSupplierUpdate,
    PurchaseOrderGenerateRequest
)
from app.core.exceptions import NotFoundError, ValidationError


class PurchaseOrderService:
    """Service for managing purchase orders."""

    async def generate_order_number(self, db: AsyncSession, tenant_id: uuid.UUID) -> str:
        """Generate unique order number for tenant."""
        # Get current year and month
        now = datetime.now()
        prefix = f"PO{now.year}{now.month:02d}"
        
        # Find the highest number for this prefix
        result = await db.execute(
            text("""
                SELECT order_number 
                FROM purchase_orders 
                WHERE tenant_id = :tenant_id 
                AND order_number LIKE :prefix 
                ORDER BY order_number DESC 
                LIMIT 1
            """),
            {"tenant_id": tenant_id, "prefix": f"{prefix}%"}
        )
        
        last_order = result.scalar_one_or_none()
        
        if last_order:
            # Extract number and increment
            try:
                last_num = int(last_order[len(prefix):])
                next_num = last_num + 1
            except (ValueError, IndexError):
                next_num = 1
        else:
            next_num = 1
        
        return f"{prefix}{next_num:04d}"

    async def create_purchase_order(
        self,
        db: AsyncSession,
        *,
        order_data: PurchaseOrderCreate,
        tenant_id: uuid.UUID,
        created_by: uuid.UUID
    ) -> PurchaseOrder:
        """Create a new purchase order."""
        
        # Generate order number
        order_number = await self.generate_order_number(db, tenant_id)
        
        # Calculate totals
        subtotal = sum(
            item.quantity_requested * item.unit_price 
            for item in order_data.items
        )
        
        # Create purchase order
        purchase_order = PurchaseOrder(
            tenant_id=tenant_id,
            order_number=order_number,
            supplier_id=order_data.supplier_id,
            shopping_list_id=order_data.shopping_list_id,
            requested_delivery_date=order_data.requested_delivery_date,
            delivery_address=order_data.delivery_address,
            delivery_notes=order_data.delivery_notes,
            notes=order_data.notes,
            subtotal=subtotal,
            total_amount=subtotal,  # No tax for now
            created_by=created_by
        )
        
        db.add(purchase_order)
        await db.flush()  # Get the ID
        
        # Create purchase order items
        for i, item_data in enumerate(order_data.items):
            total_price = item_data.quantity_requested * item_data.unit_price
            
            item = PurchaseOrderItem(
                purchase_order_id=purchase_order.id,
                name=item_data.name,
                description=item_data.description,
                quantity_requested=item_data.quantity_requested,
                unit=item_data.unit,
                unit_price=item_data.unit_price,
                total_price=total_price,
                product_code=item_data.product_code,
                notes=item_data.notes,
                inventory_item_id=item_data.inventory_item_id,
                shopping_list_item_id=item_data.shopping_list_item_id,
                sort_order=item_data.sort_order or i
            )
            db.add(item)
        
        await db.commit()
        await db.refresh(purchase_order)
        return purchase_order

    async def get_purchase_orders(
        self,
        db: AsyncSession,
        *,
        tenant_id: uuid.UUID,
        supplier_id: Optional[uuid.UUID] = None,
        status: Optional[PurchaseOrderStatus] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[PurchaseOrder]:
        """Get purchase orders with filters."""
        
        query = select(PurchaseOrder).where(PurchaseOrder.tenant_id == tenant_id)
        
        if supplier_id:
            query = query.where(PurchaseOrder.supplier_id == supplier_id)
        
        if status:
            query = query.where(PurchaseOrder.status == status.value)
        
        query = query.order_by(PurchaseOrder.created_at.desc())
        query = query.offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()

    async def get_supplier_purchase_orders(
        self,
        db: AsyncSession,
        *,
        supplier_user_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID] = None,
        status: Optional[PurchaseOrderStatus] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[PurchaseOrder]:
        """Get purchase orders for a supplier user."""
        
        # First, get supplier associations for this user
        supplier_tenants_query = text("""
            SELECT DISTINCT tua.tenant_id, s.id as supplier_id
            FROM tenant_user_associations tua
            JOIN suppliers s ON s.tenant_id = tua.tenant_id
            WHERE tua.user_id = :user_id AND tua.role = 'supplier'
        """)
        
        if tenant_id:
            supplier_tenants_query = text("""
                SELECT DISTINCT tua.tenant_id, s.id as supplier_id
                FROM tenant_user_associations tua
                JOIN suppliers s ON s.tenant_id = tua.tenant_id
                WHERE tua.user_id = :user_id 
                AND tua.role = 'supplier' 
                AND tua.tenant_id = :tenant_id
            """)
        
        params = {"user_id": supplier_user_id}
        if tenant_id:
            params["tenant_id"] = tenant_id
        
        result = await db.execute(supplier_tenants_query, params)
        supplier_data = result.fetchall()
        
        if not supplier_data:
            return []
        
        # Get supplier IDs
        supplier_ids = [row.supplier_id for row in supplier_data]
        
        # Build query for purchase orders
        query = select(PurchaseOrder).where(
            PurchaseOrder.supplier_id.in_(supplier_ids)
        )
        
        if status:
            query = query.where(PurchaseOrder.status == status.value)
        
        query = query.order_by(PurchaseOrder.created_at.desc())
        query = query.offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()

    async def get_purchase_order_by_id(
        self,
        db: AsyncSession,
        *,
        order_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID] = None
    ) -> Optional[PurchaseOrder]:
        """Get purchase order by ID."""
        
        query = select(PurchaseOrder).where(PurchaseOrder.id == order_id)
        
        if tenant_id:
            query = query.where(PurchaseOrder.tenant_id == tenant_id)
        
        result = await db.execute(query)
        return result.scalar_one_or_none()

    async def update_purchase_order(
        self,
        db: AsyncSession,
        *,
        order_id: uuid.UUID,
        order_data: PurchaseOrderUpdate,
        tenant_id: uuid.UUID
    ) -> PurchaseOrder:
        """Update purchase order."""
        
        purchase_order = await self.get_purchase_order_by_id(
            db, order_id=order_id, tenant_id=tenant_id
        )
        
        if not purchase_order:
            raise NotFoundError("Purchase order not found")
        
        # Update fields
        for field, value in order_data.dict(exclude_unset=True).items():
            setattr(purchase_order, field, value)
        
        purchase_order.updated_at = datetime.utcnow()
        
        await db.commit()
        await db.refresh(purchase_order)
        return purchase_order

    async def supplier_update_purchase_order(
        self,
        db: AsyncSession,
        *,
        order_id: uuid.UUID,
        order_data: PurchaseOrderSupplierUpdate,
        supplier_user_id: uuid.UUID
    ) -> PurchaseOrder:
        """Update purchase order by supplier."""
        
        # Verify supplier has access to this order
        purchase_order = await self.get_purchase_order_by_id(db, order_id=order_id)
        
        if not purchase_order:
            raise NotFoundError("Purchase order not found")
        
        # Verify supplier access
        access_check = await db.execute(
            text("""
                SELECT 1 FROM tenant_user_associations tua
                JOIN suppliers s ON s.tenant_id = tua.tenant_id
                WHERE tua.user_id = :user_id 
                AND tua.role = 'supplier'
                AND s.id = :supplier_id
            """),
            {"user_id": supplier_user_id, "supplier_id": purchase_order.supplier_id}
        )
        
        if not access_check.scalar_one_or_none():
            raise ValidationError("Access denied: not authorized for this supplier")
        
        # Update fields
        for field, value in order_data.dict(exclude_unset=True).items():
            setattr(purchase_order, field, value)
        
        purchase_order.updated_at = datetime.utcnow()
        
        await db.commit()
        await db.refresh(purchase_order)
        return purchase_order

    async def get_purchase_order_items(
        self,
        db: AsyncSession,
        *,
        order_id: uuid.UUID
    ) -> List[PurchaseOrderItem]:
        """Get items for a purchase order."""
        
        query = select(PurchaseOrderItem).where(
            PurchaseOrderItem.purchase_order_id == order_id
        ).order_by(PurchaseOrderItem.sort_order)
        
        result = await db.execute(query)
        return result.scalars().all()

    async def update_purchase_order_item(
        self,
        db: AsyncSession,
        *,
        item_id: uuid.UUID,
        item_data: PurchaseOrderItemUpdate
    ) -> PurchaseOrderItem:
        """Update purchase order item."""
        
        result = await db.execute(
            select(PurchaseOrderItem).where(PurchaseOrderItem.id == item_id)
        )
        item = result.scalar_one_or_none()
        
        if not item:
            raise NotFoundError("Purchase order item not found")
        
        # Update fields
        for field, value in item_data.dict(exclude_unset=True).items():
            setattr(item, field, value)
        
        # Recalculate total price if quantity or unit price changed
        if hasattr(item_data, 'quantity_requested') or hasattr(item_data, 'unit_price'):
            item.total_price = item.quantity_requested * item.unit_price
        
        await db.commit()
        await db.refresh(item)
        return item

    async def supplier_update_purchase_order_item(
        self,
        db: AsyncSession,
        *,
        item_id: uuid.UUID,
        item_data: PurchaseOrderItemSupplierUpdate,
        supplier_user_id: uuid.UUID
    ) -> PurchaseOrderItem:
        """Update purchase order item by supplier."""
        
        # Get item with purchase order
        result = await db.execute(
            select(PurchaseOrderItem, PurchaseOrder)
            .join(PurchaseOrder)
            .where(PurchaseOrderItem.id == item_id)
        )
        
        row = result.first()
        if not row:
            raise NotFoundError("Purchase order item not found")
        
        item, purchase_order = row
        
        # Verify supplier access
        access_check = await db.execute(
            text("""
                SELECT 1 FROM tenant_user_associations tua
                JOIN suppliers s ON s.tenant_id = tua.tenant_id
                WHERE tua.user_id = :user_id 
                AND tua.role = 'supplier'
                AND s.id = :supplier_id
            """),
            {"user_id": supplier_user_id, "supplier_id": purchase_order.supplier_id}
        )
        
        if not access_check.scalar_one_or_none():
            raise ValidationError("Access denied: not authorized for this supplier")
        
        # Update fields
        for field, value in item_data.dict(exclude_unset=True).items():
            setattr(item, field, value)
        
        # Recalculate total price if unit price changed
        if item_data.unit_price is not None:
            confirmed_qty = item_data.quantity_confirmed or item.quantity_confirmed or item.quantity_requested
            item.total_price = confirmed_qty * item_data.unit_price
        
        await db.commit()
        await db.refresh(item)
        return item


# Service instance
purchase_order_service = PurchaseOrderService()
