# General - Public Menu Api

**Categoria:** General
**Módulo:** Public Menu Api
**Total de Endpoints:** 6
**Gerado em:** 27/06/2025, 20:20:27

## 📋 Endpoints

- [GET /api/public/menu/{tenant_slug}/categories](#get-apipublicmenutenant-slugcategories) - Get Public Categories
- [GET /api/public/menu/{tenant_slug}/items](#get-apipublicmenutenant-slugitems) - Get Public Items
- [GET /api/public/menu/{tenant_slug}/items/{item_id}](#get-apipublicmenutenant-slugitemsitem-id) - Get Public Item By Id
- [GET /api/public/menu/{tenant_slug}/menu/current](#get-apipublicmenutenant-slugmenucurrent) - Get Current Public Menu
- [GET /api/public/menu/{tenant_slug}/menu/{menu_id}](#get-apipublicmenutenant-slugmenumenu-id) - Get Public Menu By Id
- [GET /api/public/menu/{tenant_slug}/menus](#get-apipublicmenutenant-slugmenus) - Get Public Menus

## 📊 Schemas

### DigitalMenuWithCategories

**Descrição:** Schema for reading a Digital Menu with its categories.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Name of the digital menu (e.g., Breakfast Menu) |
| `description` | unknown | ❌ | Optional description for the menu |
| `is_active` | boolean | ❌ | Whether the menu is currently active |
| `display_order` | integer | ❌ | Order in which the menu should be displayed |
| `schedule_enabled` | boolean | ❌ | Whether time-based scheduling is enabled for this menu |
| `schedule_config` | unknown | ❌ | JSON configuration for menu scheduling (timezone, time ranges, days) |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `categories` | Array[MenuCategoryRead] | ❌ | List of categories in this menu |

### DigitalMenuWithCategoriesPublic

**Descrição:** Schema for reading a Digital Menu with its categories for public API (without redundant data).

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Name of the digital menu (e.g., Breakfast Menu) |
| `description` | unknown | ❌ | Optional description for the menu |
| `is_active` | boolean | ❌ | Whether the menu is currently active |
| `display_order` | integer | ❌ | Order in which the menu should be displayed |
| `schedule_enabled` | boolean | ❌ | Whether time-based scheduling is enabled for this menu |
| `schedule_config` | unknown | ❌ | JSON configuration for menu scheduling (timezone, time ranges, days) |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `categories` | Array[MenuCategoryReadPublic] | ❌ | List of categories in this menu with optimized structure |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### MenuItemRead

**Descrição:** Schema for reading a Menu Item, including all details and relations.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Name of the menu item (e.g., Classic Burger) |
| `description` | unknown | ❌ | Detailed description of the item |
| `base_price` | string | ✅ | Base price of the item before variants/modifiers |
| `image_url` | unknown | ❌ | URL for the item's primary image |
| `allergen_ids` | unknown | ❌ | List of allergen IDs associated with this menu item |
| `is_available` | boolean | ❌ | Is the item currently available for ordering? |
| `is_active` | boolean | ❌ | Is the item active in the system (for soft delete)? |
| `is_combo` | boolean | ❌ | Is this item a combo meal? |
| `discount_percentage` | unknown | ❌ | Optional discount percentage (e.g., 10.5 for 10.5%) |
| `display_order` | integer | ❌ | Order within the menu category |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `category_id` | string | ✅ | - |
| `category` | MenuCategoryReadSimple | ✅ | - |
| `variant_groups` | Array[app__modules__tenants__restaurants__menu__schemas__variant_group__VariantGroupRead] | ❌ | - |
| `modifier_groups` | Array[app__modules__tenants__restaurants__menu__schemas__modifier_group__ModifierGroupRead] | ❌ | - |
| `optional_groups` | Array[app__modules__tenants__restaurants__menu__schemas__optional_group__OptionalGroupRead] | ❌ | - |
| `allergens` | Array[AllergenReadSimple] | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/public/menu/{tenant_slug}/categories {#get-apipublicmenutenant-slugcategories}

**Resumo:** Get Public Categories
**Descrição:** Get menu categories for a restaurant.
Public endpoint - no authentication required.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_slug` | string | path | ✅ | Tenant slug (restaurant identifier) |
| `menu_id` | string | query | ❌ | Filter by digital menu ID |
| `parent_id` | string | query | ❌ | Filter by parent category ID |
| `only_top_level` | boolean | query | ❌ | Only return top-level categories |
| `include_items` | boolean | query | ❌ | Include menu items with full details |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/public/menu/{tenant_slug}/categories"
```

---

### GET /api/public/menu/{tenant_slug}/items {#get-apipublicmenutenant-slugitems}

**Resumo:** Get Public Items
**Descrição:** Get menu items for a restaurant.
Public endpoint - no authentication required.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_slug` | string | path | ✅ | Tenant slug (restaurant identifier) |
| `menu_id` | string | query | ❌ | Filter by digital menu ID |
| `category_id` | string | query | ❌ | Filter by category ID |
| `search` | string | query | ❌ | Search items by name |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/public/menu/{tenant_slug}/items"
```

---

### GET /api/public/menu/{tenant_slug}/items/{item_id} {#get-apipublicmenutenant-slugitemsitem-id}

**Resumo:** Get Public Item By Id
**Descrição:** Get a specific menu item by ID with variants, modifiers, and optionals.
Public endpoint - no authentication required.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `item_id` | string | path | ✅ | Menu item ID |
| `tenant_slug` | string | path | ✅ | Tenant slug (restaurant identifier) |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [MenuItemRead](#menuitemread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/public/menu/{tenant_slug}/items/{item_id}"
```

---

### GET /api/public/menu/{tenant_slug}/menu/current {#get-apipublicmenutenant-slugmenucurrent}

**Resumo:** Get Current Public Menu
**Descrição:** Get the currently active menu based on time scheduling.
Returns the menu with categories and items.
Public endpoint - no authentication required.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_slug` | string | path | ✅ | Tenant slug (restaurant identifier) |
| `timezone` | string | query | ❌ | Timezone for schedule checking |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [DigitalMenuWithCategoriesPublic](#digitalmenuwithcategoriespublic)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/public/menu/{tenant_slug}/menu/current"
```

---

### GET /api/public/menu/{tenant_slug}/menu/{menu_id} {#get-apipublicmenutenant-slugmenumenu-id}

**Resumo:** Get Public Menu By Id
**Descrição:** Get a specific digital menu by ID with its categories and items.
Public endpoint - no authentication required.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `menu_id` | string | path | ✅ | Digital menu ID |
| `tenant_slug` | string | path | ✅ | Tenant slug (restaurant identifier) |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [DigitalMenuWithCategories](#digitalmenuwithcategories)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/public/menu/{tenant_slug}/menu/{menu_id}"
```

---

### GET /api/public/menu/{tenant_slug}/menus {#get-apipublicmenutenant-slugmenus}

**Resumo:** Get Public Menus
**Descrição:** Get all active digital menus for a restaurant.
Public endpoint - no authentication required.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_slug` | string | path | ✅ | Tenant slug (restaurant identifier) |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/public/menu/{tenant_slug}/menus"
```

---
