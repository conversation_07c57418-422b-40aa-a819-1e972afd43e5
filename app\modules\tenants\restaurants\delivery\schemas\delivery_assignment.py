from typing import Optional, Dict, Any
import uuid
from pydantic import Field, ConfigDict  # Adicionado ConfigDict
from datetime import datetime
from app.schemas import BaseSchema  # Corrigido o caminho da importação
from app.modules.tenants.restaurants.delivery.enums import DeliveryAssignmentStatus
from app.modules.tenants.restaurants.delivery.schemas.delivery_boy import (
    DeliveryBoyRead,
)

# TimestampMixin removido, pois os campos virão do modelo ORM ou são definidos explicitamente abaixo

# Simplified OnlineOrder schema for DeliveryAssignmentRead


class SimplifiedOnlineOrderRead(BaseSchema):
    id: uuid.UUID
    order_number: str
    customer_name: Optional[str] = None
    customer_phone: Optional[str] = None
    # Delivery address fields are crucial for the delivery boy
    delivery_address_line1: Optional[str] = None
    delivery_address_line2: Optional[str] = None
    delivery_city: Optional[str] = None
    delivery_state: Optional[str] = None
    delivery_postal_code: Optional[str] = None
    delivery_instructions: Optional[str] = None
    # Consider adding order_type as well for context
    # from app.modules.tenants.restaurants.models.online_order import PyOrderTypeEnum as OrderTypeEnum  # noqa: E501
    # order_type: Optional[OrderTypeEnum] = None

    model_config = ConfigDict(
        from_attributes=True, use_enum_values=True  # If OrderTypeEnum is added
    )


class DeliveryAssignmentBase(BaseSchema):
    online_order_id: uuid.UUID = Field(..., description="ID of the online order being assigned")
    delivery_boy_id: uuid.UUID = Field(..., description="ID of the delivery boy assigned")
    assignment_status: DeliveryAssignmentStatus = Field(
        default=DeliveryAssignmentStatus.PENDING_ACCEPTANCE,
        description="Current status of the delivery assignment",
    )
    delivery_address_snapshot: Dict[str, Any] = Field(
        ..., description="Snapshot of the delivery address from the OnlineOrder"
    )
    notes: Optional[str] = Field(None, description="Additional notes for the delivery")
    estimated_delivery_time: Optional[datetime] = Field(
        None, description="Estimated time of delivery"
    )


class DeliveryAssignmentCreate(DeliveryAssignmentBase):
    tenant_id: uuid.UUID  # Added tenant_id as it's required on creation


class DeliveryAssignmentUpdate(BaseSchema):
    assignment_status: Optional[DeliveryAssignmentStatus] = None
    notes: Optional[str] = None
    estimated_delivery_time: Optional[datetime] = None
    # Timestamps like accepted_at, picked_up_at, delivered_at will be set by the service


class DeliveryAssignmentRead(DeliveryAssignmentBase):  # Removido TimestampMixin da herança
    id: uuid.UUID
    tenant_id: uuid.UUID
    assigned_at: datetime  # Este campo já existe, created_at e updated_at virão do modelo ORM se existirem  # noqa: E501
    accepted_at: Optional[datetime] = None
    picked_up_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None
    cancelled_at: Optional[datetime] = None
    actual_delivery_time: Optional[datetime] = None

    delivery_boy: DeliveryBoyRead
    online_order: SimplifiedOnlineOrderRead  # Using the simplified version

    model_config = ConfigDict(from_attributes=True)
