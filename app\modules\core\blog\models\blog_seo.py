"""
Blog SEO Models

Advanced SEO optimization features for blog posts.
"""

import uuid
from datetime import datetime
from typing import Dict, Any

from sqlalchemy import Column, DateTime, ForeignKey, String, Text, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base


class BlogSEO(Base):
    """
    Blog SEO model for advanced search engine optimization.

    Stores SEO-specific data for blog posts including structured data,
    social media optimization, and search engine directives.
    """

    __tablename__ = "blog_seo"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Foreign key
    post_id = Column(UUID(as_uuid=True), ForeignKey("blog_posts.id"),
                    nullable=False, unique=True)

    # Basic SEO
    canonical_url = Column(String(500), nullable=True)
    robots_directive = Column(String(100), default="index,follow")

    # Open Graph (Facebook, LinkedIn, etc.)
    og_title = Column(String(95), nullable=True)  # Max 95 chars for OG
    og_description = Column(String(300), nullable=True)  # Max 300 chars for OG
    og_image_url = Column(String(500), nullable=True)
    og_image_alt = Column(String(255), nullable=True)
    og_type = Column(String(50), default="article")

    # Twitter Cards
    twitter_card_type = Column(String(50), default="summary_large_image")
    twitter_title = Column(String(70), nullable=True)  # Max 70 chars for Twitter
    twitter_description = Column(String(200), nullable=True)  # Max 200 chars
    twitter_image_url = Column(String(500), nullable=True)
    twitter_image_alt = Column(String(255), nullable=True)
    twitter_creator = Column(String(100), nullable=True)  # @username

    # Structured Data (JSON-LD)
    structured_data = Column(JSON, nullable=True)  # Store JSON-LD markup

    # Additional meta tags
    additional_meta_tags = Column(JSON, nullable=True)  # Custom meta tags

    # Schema.org Article properties
    article_section = Column(String(100), nullable=True)  # Article section
    article_tags = Column(JSON, nullable=True)  # Array of tags

    # Focus keyword and SEO analysis
    focus_keyword = Column(String(100), nullable=True)
    keyword_density = Column(String(10), nullable=True)  # e.g., "2.5%"
    readability_score = Column(String(20), nullable=True)  # e.g., "Good"

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow,
                       onupdate=datetime.utcnow, nullable=False)

    # Relationships
    post = relationship("BlogPost", back_populates="seo")

    def __repr__(self):
        return f"<BlogSEO(id={self.id}, post_id={self.post_id})>"

    def get_structured_data(self) -> Dict[str, Any]:
        """
        Generate JSON-LD structured data for the blog post.

        Returns:
            Dict containing structured data for search engines
        """
        if self.structured_data:
            return self.structured_data

        # Default structured data template
        return {
            "@context": "https://schema.org",
            "@type": "BlogPosting",
            "headline": self.og_title or self.post.translations[0].title,
            "description": self.og_description or self.post.translations[0].excerpt,
            "image": self.og_image_url or self.post.featured_image_url,
            "author": {
                "@type": "Person",
                "name": self.post.author.display_name,
                "url": self.post.author.website_url
            },
            "publisher": {
                "@type": "Organization",
                "name": "Your Blog Name",  # This should be configurable
            },
            "datePublished": self.post.published_at.isoformat() if self.post.published_at else None,
            "dateModified": self.post.updated_at.isoformat(),
            "mainEntityOfPage": {
                "@type": "WebPage",
                "@id": self.canonical_url
            }
        }
