"""
TVendorSupplier Schemas for EShop B2B System
============================================

Schemas de validação para fornecedores B2B (TVendorSupplier) com campos específicos
para gestão de produtos, comissões e verificação empresarial.
"""

import uuid
from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any, List

from pydantic import BaseModel, Field, ConfigDict, validator, EmailStr

from app.modules.core.eshop.models.tvendor_supplier import (
    TVendorStatus, SupplierType, VerificationStatus
)


class TVendorSupplierBase(BaseModel):
    """Schema base para TVendorSupplier."""
    
    company_name: str = Field(..., max_length=255, description="Nome da empresa")
    supplier_type: SupplierType = Field(..., description="Tipo de fornecedor")
    tax_id: str = Field(..., max_length=50, description="CNPJ/Tax ID")
    business_registration_number: Optional[str] = Field(
        None, max_length=100, description="Número de registro empresarial"
    )
    business_phone: Optional[str] = Field(
        None, max_length=20, description="Telefone empresarial"
    )
    business_email: Optional[EmailStr] = Field(
        None, description="Email empresarial"
    )
    website: Optional[str] = Field(
        None, max_length=255, description="Website da empresa"
    )
    
    @validator('tax_id')
    def validate_tax_id(cls, v):
        """Valida formato do CNPJ/Tax ID."""
        # Remover caracteres especiais
        clean_id = ''.join(filter(str.isdigit, v))
        
        # Validação básica de CNPJ (14 dígitos)
        if len(clean_id) == 14:
            return clean_id
        # Validação básica de CPF (11 dígitos) para MEI
        elif len(clean_id) == 11:
            return clean_id
        else:
            raise ValueError('Tax ID deve ter 11 (CPF) ou 14 (CNPJ) dígitos')


class TVendorSupplierCreate(TVendorSupplierBase):
    """Schema para criação de TVendorSupplier."""
    
    user_id: uuid.UUID = Field(..., description="ID do usuário")
    business_address: Optional[Dict[str, Any]] = Field(
        None, description="Endereço empresarial"
    )
    commission_rate: Decimal = Field(
        default=Decimal('5.00'), ge=0, le=100, description="Taxa de comissão (%)"
    )
    commission_type: str = Field(
        default='percentage', description="Tipo de comissão"
    )
    minimum_commission: Decimal = Field(
        default=Decimal('0.00'), ge=0, description="Comissão mínima"
    )
    max_products_allowed: int = Field(
        default=100, ge=1, description="Máximo de produtos permitidos"
    )
    annual_revenue: Optional[Decimal] = Field(
        None, ge=0, description="Receita anual"
    )
    employee_count: Optional[int] = Field(
        None, ge=1, description="Número de funcionários"
    )
    years_in_business: Optional[int] = Field(
        None, ge=0, description="Anos de operação"
    )
    account_manager_id: Optional[uuid.UUID] = Field(
        None, description="ID do gerente de conta"
    )
    payment_terms: str = Field(
        default='net_30', description="Termos de pagamento"
    )
    processing_time_days: int = Field(
        default=1, ge=0, le=30, description="Tempo de processamento em dias"
    )
    notes: Optional[str] = Field(
        None, max_length=2000, description="Observações"
    )


class TVendorSupplierUpdate(BaseModel):
    """Schema para atualização de TVendorSupplier."""
    
    company_name: Optional[str] = Field(
        None, max_length=255, description="Nome da empresa"
    )
    supplier_type: Optional[SupplierType] = Field(
        None, description="Tipo de fornecedor"
    )
    business_registration_number: Optional[str] = Field(
        None, max_length=100, description="Número de registro empresarial"
    )
    business_address: Optional[Dict[str, Any]] = Field(
        None, description="Endereço empresarial"
    )
    business_phone: Optional[str] = Field(
        None, max_length=20, description="Telefone empresarial"
    )
    business_email: Optional[EmailStr] = Field(
        None, description="Email empresarial"
    )
    website: Optional[str] = Field(
        None, max_length=255, description="Website da empresa"
    )
    commission_rate: Optional[Decimal] = Field(
        None, ge=0, le=100, description="Taxa de comissão (%)"
    )
    commission_type: Optional[str] = Field(
        None, description="Tipo de comissão"
    )
    minimum_commission: Optional[Decimal] = Field(
        None, ge=0, description="Comissão mínima"
    )
    max_products_allowed: Optional[int] = Field(
        None, ge=1, description="Máximo de produtos permitidos"
    )
    annual_revenue: Optional[Decimal] = Field(
        None, ge=0, description="Receita anual"
    )
    employee_count: Optional[int] = Field(
        None, ge=1, description="Número de funcionários"
    )
    years_in_business: Optional[int] = Field(
        None, ge=0, description="Anos de operação"
    )
    account_manager_id: Optional[uuid.UUID] = Field(
        None, description="ID do gerente de conta"
    )
    payment_terms: Optional[str] = Field(
        None, description="Termos de pagamento"
    )
    payment_method: Optional[str] = Field(
        None, description="Método de pagamento"
    )
    processing_time_days: Optional[int] = Field(
        None, ge=0, le=30, description="Tempo de processamento em dias"
    )
    auto_approve_products: Optional[bool] = Field(
        None, description="Aprovação automática de produtos"
    )
    can_manage_inventory: Optional[bool] = Field(
        None, description="Pode gerenciar estoque"
    )
    can_set_pricing: Optional[bool] = Field(
        None, description="Pode definir preços"
    )
    can_create_promotions: Optional[bool] = Field(
        None, description="Pode criar promoções"
    )
    shipping_zones: Optional[List[str]] = Field(
        None, description="Zonas de entrega"
    )
    shipping_methods: Optional[List[str]] = Field(
        None, description="Métodos de entrega"
    )
    notes: Optional[str] = Field(
        None, max_length=2000, description="Observações"
    )


class TVendorSupplierRead(TVendorSupplierBase):
    """Schema para leitura de TVendorSupplier."""
    
    id: uuid.UUID
    tenant_id: uuid.UUID
    user_id: uuid.UUID
    status: TVendorStatus
    
    # Endereço empresarial
    business_address: Optional[Dict[str, Any]]
    
    # Verificação empresarial
    verification_status: VerificationStatus
    verification_date: Optional[datetime]
    verification_notes: Optional[str]
    verification_expires_at: Optional[datetime]
    
    # Configurações de comissão
    commission_rate: Decimal
    commission_type: str
    minimum_commission: Decimal
    
    # Configurações de produtos
    max_products_allowed: int
    auto_approve_products: bool
    require_product_approval: bool
    
    # Informações financeiras
    annual_revenue: Optional[Decimal]
    employee_count: Optional[int]
    years_in_business: Optional[int]
    
    # Estatísticas de vendas
    total_products: int
    active_products: int
    total_sales: Decimal
    total_orders: int
    average_order_value: Decimal
    total_commission_earned: Decimal
    
    # Performance metrics
    customer_rating: Decimal
    total_reviews: int
    fulfillment_rate: Decimal
    on_time_delivery_rate: Decimal
    
    # Configurações de conta
    can_manage_inventory: bool
    can_set_pricing: bool
    can_create_promotions: bool
    
    # Gerente de conta
    account_manager_id: Optional[uuid.UUID]
    
    # Configurações de pagamento
    payment_terms: str
    payment_method: Optional[str]
    
    # Configurações de entrega
    processing_time_days: int
    shipping_zones: Optional[List[str]]
    shipping_methods: Optional[List[str]]
    
    # Metadados
    notes: Optional[str]
    
    # Timestamps
    created_at: datetime
    updated_at: datetime
    approved_at: Optional[datetime]
    last_activity_at: Optional[datetime]
    last_sale_at: Optional[datetime]
    
    # Propriedades calculadas
    is_verified: bool = False
    is_active: bool = False
    verification_expired: bool = False
    commission_rate_decimal: Decimal = Decimal('0.00')
    
    model_config = ConfigDict(from_attributes=True)


class TVendorSupplierApprovalRequest(BaseModel):
    """Schema para aprovação de TVendorSupplier."""
    
    approved: bool = Field(..., description="Aprovado ou rejeitado")
    notes: Optional[str] = Field(
        None, max_length=1000, description="Notas da aprovação/rejeição"
    )
    commission_rate: Optional[Decimal] = Field(
        None, ge=0, le=100, description="Taxa de comissão aprovada"
    )
    max_products_allowed: Optional[int] = Field(
        None, ge=1, description="Máximo de produtos permitidos"
    )


class TVendorSupplierCommissionUpdateRequest(BaseModel):
    """Schema para atualização de comissão."""
    
    commission_rate: Decimal = Field(..., ge=0, le=100, description="Nova taxa de comissão")
    commission_type: str = Field(..., description="Tipo de comissão")
    minimum_commission: Decimal = Field(..., ge=0, description="Comissão mínima")
    reason: str = Field(..., max_length=500, description="Motivo da alteração")


class TVendorSupplierSummary(BaseModel):
    """Schema para resumo de TVendorSupplier."""
    
    id: uuid.UUID
    company_name: str
    supplier_type: SupplierType
    status: TVendorStatus
    verification_status: VerificationStatus
    total_products: int
    active_products: int
    total_sales: Decimal
    commission_rate: Decimal
    customer_rating: Decimal
    created_at: datetime
    
    model_config = ConfigDict(from_attributes=True)


class TVendorSupplierResponse(BaseModel):
    """Schema de resposta padrão para operações de TVendorSupplier."""
    
    success: bool = Field(..., description="Sucesso da operação")
    message: str = Field(..., description="Mensagem de retorno")
    vendor: Optional[TVendorSupplierRead] = Field(
        None, description="Dados do TVendorSupplier"
    )
    
    model_config = ConfigDict(from_attributes=True)


class TVendorSupplierStats(BaseModel):
    """Schema para estatísticas de TVendorSuppliers."""
    
    total_vendors: int = Field(..., description="Total de fornecedores")
    active_vendors: int = Field(..., description="Fornecedores ativos")
    pending_vendors: int = Field(..., description="Fornecedores pendentes")
    verified_vendors: int = Field(..., description="Fornecedores verificados")
    suspended_vendors: int = Field(..., description="Fornecedores suspensos")
    
    total_products: int = Field(..., description="Total de produtos")
    active_products: int = Field(..., description="Produtos ativos")
    total_sales: Decimal = Field(..., description="Vendas totais")
    total_commission_paid: Decimal = Field(..., description="Comissão total paga")
    average_commission_rate: Decimal = Field(..., description="Taxa média de comissão")
    
    # Estatísticas por tipo de fornecedor
    supplier_type_stats: Dict[str, int] = Field(
        default_factory=dict, description="Estatísticas por tipo de fornecedor"
    )
    
    # Estatísticas por status de verificação
    verification_status_stats: Dict[str, int] = Field(
        default_factory=dict, description="Estatísticas por status de verificação"
    )
    
    # Performance metrics
    average_customer_rating: Decimal = Field(..., description="Avaliação média dos clientes")
    average_fulfillment_rate: Decimal = Field(..., description="Taxa média de cumprimento")
    average_delivery_rate: Decimal = Field(..., description="Taxa média de entrega no prazo")
    
    model_config = ConfigDict(from_attributes=True)


class TVendorSupplierListResponse(BaseModel):
    """Schema para resposta de lista de TVendorSuppliers."""
    
    vendors: List[TVendorSupplierSummary] = Field(..., description="Lista de fornecedores")
    total: int = Field(..., description="Total de registros")
    page: int = Field(..., description="Página atual")
    limit: int = Field(..., description="Limite por página")
    total_pages: int = Field(..., description="Total de páginas")
    
    model_config = ConfigDict(from_attributes=True)
