"""
SEO Helper Functions

Utility functions for SEO optimization and analysis.
"""

import re
import math
from typing import List, Dict, Optional, Tuple
from collections import Counter


def generate_meta_description(
    content: str,
    max_length: int = 160,
    focus_keyword: Optional[str] = None
) -> str:
    """Generate meta description from content."""
    if not content:
        return ""
    
    # Clean content
    clean_content = re.sub(r'<[^>]+>', '', content)  # Remove HTML tags
    clean_content = re.sub(r'\s+', ' ', clean_content).strip()  # Normalize whitespace
    
    # If focus keyword provided, try to include it
    if focus_keyword:
        keyword_lower = focus_keyword.lower()
        content_lower = clean_content.lower()
        
        # Find sentence containing the keyword
        sentences = re.split(r'[.!?]+', clean_content)
        for sentence in sentences:
            if keyword_lower in sentence.lower():
                sentence = sentence.strip()
                if len(sentence) <= max_length:
                    return sentence
                elif len(sentence) <= max_length + 20:  # Allow slight overflow
                    return sentence[:max_length-3] + "..."
    
    # Fallback: use first sentences up to max_length
    if len(clean_content) <= max_length:
        return clean_content
    
    # Find last complete sentence within limit
    truncated = clean_content[:max_length]
    last_sentence_end = max(
        truncated.rfind('.'),
        truncated.rfind('!'),
        truncated.rfind('?')
    )
    
    if last_sentence_end > max_length * 0.7:  # If we have at least 70% of content
        return clean_content[:last_sentence_end + 1]
    else:
        return clean_content[:max_length - 3] + "..."


def extract_keywords(
    content: str,
    min_length: int = 3,
    max_keywords: int = 20,
    exclude_common: bool = True
) -> List[str]:
    """Extract keywords from content."""
    if not content:
        return []
    
    # Common words to exclude
    common_words = {
        'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
        'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before',
        'after', 'above', 'below', 'between', 'among', 'this', 'that', 'these',
        'those', 'i', 'me', 'my', 'myself', 'we', 'our', 'ours', 'ourselves',
        'you', 'your', 'yours', 'yourself', 'yourselves', 'he', 'him', 'his',
        'himself', 'she', 'her', 'hers', 'herself', 'it', 'its', 'itself',
        'they', 'them', 'their', 'theirs', 'themselves', 'what', 'which',
        'who', 'whom', 'whose', 'this', 'that', 'these', 'those', 'am', 'is',
        'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had',
        'having', 'do', 'does', 'did', 'doing', 'will', 'would', 'could',
        'should', 'may', 'might', 'must', 'can', 'shall'
    } if exclude_common else set()
    
    # Clean and extract words
    clean_content = re.sub(r'<[^>]+>', '', content)  # Remove HTML
    words = re.findall(r'\b[a-zA-Z]+\b', clean_content.lower())
    
    # Filter words
    filtered_words = [
        word for word in words
        if len(word) >= min_length and word not in common_words
    ]
    
    # Count frequency and return most common
    word_counts = Counter(filtered_words)
    return [word for word, count in word_counts.most_common(max_keywords)]


def calculate_reading_time(content: str, words_per_minute: int = 200) -> int:
    """Calculate estimated reading time in minutes."""
    if not content:
        return 0
    
    # Clean content and count words
    clean_content = re.sub(r'<[^>]+>', '', content)
    word_count = len(clean_content.split())
    
    # Calculate reading time
    reading_time = math.ceil(word_count / words_per_minute)
    return max(1, reading_time)  # Minimum 1 minute


def optimize_title_for_seo(
    title: str,
    focus_keyword: Optional[str] = None,
    max_length: int = 60,
    brand_name: Optional[str] = None
) -> str:
    """Optimize title for SEO."""
    if not title:
        return ""
    
    # Clean title
    title = title.strip()
    
    # Add focus keyword if not present
    if focus_keyword and focus_keyword.lower() not in title.lower():
        title = f"{focus_keyword} - {title}"
    
    # Add brand name if provided and not present
    if brand_name and brand_name.lower() not in title.lower():
        separator = " | " if len(title) + len(brand_name) + 3 <= max_length else " - "
        title = f"{title}{separator}{brand_name}"
    
    # Truncate if too long
    if len(title) > max_length:
        if brand_name and brand_name in title:
            # Try to keep brand name
            brand_part = f" | {brand_name}"
            available_length = max_length - len(brand_part)
            if available_length > 20:  # Minimum meaningful title length
                title_part = title.replace(brand_part, "").replace(f" - {brand_name}", "")
                title = f"{title_part[:available_length].rstrip()}...{brand_part}"
            else:
                title = title[:max_length - 3] + "..."
        else:
            title = title[:max_length - 3] + "..."
    
    return title


def validate_meta_tags(meta_data: Dict[str, str]) -> Dict[str, List[str]]:
    """Validate meta tag data and return issues."""
    issues = {
        "errors": [],
        "warnings": [],
        "suggestions": []
    }
    
    # Title validation
    title = meta_data.get("title", "")
    if not title:
        issues["errors"].append("Title is required")
    elif len(title) < 30:
        issues["warnings"].append("Title is too short (recommended: 30-60 characters)")
    elif len(title) > 60:
        issues["warnings"].append("Title is too long (recommended: 30-60 characters)")
    
    # Description validation
    description = meta_data.get("description", "")
    if not description:
        issues["errors"].append("Meta description is required")
    elif len(description) < 120:
        issues["warnings"].append("Meta description is too short (recommended: 120-160 characters)")
    elif len(description) > 160:
        issues["warnings"].append("Meta description is too long (recommended: 120-160 characters)")
    
    # Keywords validation
    keywords = meta_data.get("keywords", "")
    if keywords:
        keyword_list = [k.strip() for k in keywords.split(",")]
        if len(keyword_list) > 10:
            issues["warnings"].append("Too many keywords (recommended: 5-10 keywords)")
        elif len(keyword_list) < 3:
            issues["suggestions"].append("Consider adding more relevant keywords")
    
    # URL validation
    canonical_url = meta_data.get("canonical_url", "")
    if canonical_url and not re.match(r'^https?://', canonical_url):
        issues["errors"].append("Canonical URL must be a valid HTTP/HTTPS URL")
    
    # Open Graph validation
    og_title = meta_data.get("og_title", "")
    if og_title and len(og_title) > 95:
        issues["warnings"].append("Open Graph title is too long (recommended: max 95 characters)")
    
    og_description = meta_data.get("og_description", "")
    if og_description and len(og_description) > 300:
        issues["warnings"].append("Open Graph description is too long (recommended: max 300 characters)")
    
    # Twitter Card validation
    twitter_title = meta_data.get("twitter_title", "")
    if twitter_title and len(twitter_title) > 70:
        issues["warnings"].append("Twitter title is too long (recommended: max 70 characters)")
    
    twitter_description = meta_data.get("twitter_description", "")
    if twitter_description and len(twitter_description) > 200:
        issues["warnings"].append("Twitter description is too long (recommended: max 200 characters)")
    
    return issues


def analyze_keyword_density(content: str, keyword: str) -> Dict[str, float]:
    """Analyze keyword density in content."""
    if not content or not keyword:
        return {"density": 0.0, "count": 0, "total_words": 0}
    
    # Clean content
    clean_content = re.sub(r'<[^>]+>', '', content)
    words = clean_content.lower().split()
    total_words = len(words)
    
    if total_words == 0:
        return {"density": 0.0, "count": 0, "total_words": 0}
    
    # Count keyword occurrences
    keyword_lower = keyword.lower()
    keyword_count = clean_content.lower().count(keyword_lower)
    
    # Calculate density
    density = (keyword_count / total_words) * 100
    
    return {
        "density": round(density, 2),
        "count": keyword_count,
        "total_words": total_words
    }


def generate_slug_suggestions(title: str, existing_slugs: List[str] = None) -> List[str]:
    """Generate multiple slug suggestions from title."""
    if not title:
        return []
    
    existing_slugs = existing_slugs or []
    suggestions = []
    
    # Basic slug
    basic_slug = re.sub(r'[^a-zA-Z0-9\s-]', '', title.lower())
    basic_slug = re.sub(r'\s+', '-', basic_slug).strip('-')
    
    if basic_slug and basic_slug not in existing_slugs:
        suggestions.append(basic_slug)
    
    # Shortened versions
    words = basic_slug.split('-')
    if len(words) > 3:
        # First 3 words
        short_slug = '-'.join(words[:3])
        if short_slug not in existing_slugs:
            suggestions.append(short_slug)
        
        # Last 3 words
        short_slug = '-'.join(words[-3:])
        if short_slug not in existing_slugs:
            suggestions.append(short_slug)
    
    # Remove common words
    important_words = [
        word for word in words
        if word not in {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with'}
    ]
    if important_words and len(important_words) != len(words):
        important_slug = '-'.join(important_words)
        if important_slug not in existing_slugs:
            suggestions.append(important_slug)
    
    return suggestions[:5]  # Return top 5 suggestions
