"""
Módulo centralizado para gerenciamento de conexões Redis.
Fornece clientes Redis para diferentes propósitos (cache, Celery, etc.)
e funções utilitárias para operações comuns.
"""

import logging  # noqa: E402
from typing import Optional, Any
import json
from functools import wraps

# Importações Redis
from redis.asyncio import Redis as AsyncRedis  # noqa: E402
from redis.exceptions import RedisError

# Configurações da aplicação
from app.core.config import settings  # noqa: E402

# Configuração de logging
logger = logging.getLogger(__name__)

# Cliente Redis assíncrono para uso geral (cache, etc.)
async_redis_client = AsyncRedis.from_url(
    settings.REDIS_URL, decode_responses=True, db=0  # Usar DB 0 para cache geral
)

# Cliente Redis para Celery (pode usar um DB diferente)
celery_redis_url = getattr(settings, "CELERY_BROKER_URL", settings.REDIS_URL)
# Não precisamos criar um cliente aqui, apenas fornecer a URL para o Celery

# Tempo de expiração padrão para cache (1 hora)
DEFAULT_CACHE_TTL = 3600

# Funções utilitárias para operações comuns


async def get_cache(key: str) -> Optional[str]:
    """
    Recupera um valor do cache.

    Args:
        key: A chave do cache

    Returns:
        O valor armazenado ou None se não encontrado
    """
    try:
        return await async_redis_client.get(key)
    except RedisError as e:
        logger.error(f"Erro ao recuperar do cache (key={key}): {e}")
        return None


async def set_cache(key: str, value: str, ttl: int = DEFAULT_CACHE_TTL) -> bool:
    """
    Armazena um valor no cache.

    Args:
        key: A chave do cache
        value: O valor a ser armazenado
        ttl: Tempo de vida em segundos (padrão: 1 hora)

    Returns:
        True se bem-sucedido, False caso contrário
    """
    try:
        await async_redis_client.set(key, value, ex=ttl)
        return True
    except RedisError as e:
        logger.error(f"Erro ao armazenar no cache (key={key}): {e}")
        return False


async def delete_cache(key: str) -> bool:
    """
    Remove um valor do cache.

    Args:
        key: A chave do cache

    Returns:
        True se bem-sucedido, False caso contrário
    """
    try:
        await async_redis_client.delete(key)
        return True
    except RedisError as e:
        logger.error(f"Erro ao remover do cache (key={key}): {e}")
        return False


async def cache_json(key: str, data: Any, ttl: int = DEFAULT_CACHE_TTL) -> bool:
    """
    Armazena dados JSON no cache.

    Args:
        key: A chave do cache
        data: Os dados a serem serializados e armazenados
        ttl: Tempo de vida em segundos (padrão: 1 hora)

    Returns:
        True se bem-sucedido, False caso contrário
    """
    try:
        json_data = json.dumps(data)
        return await set_cache(key, json_data, ttl)
    except (TypeError, ValueError) as e:
        logger.error(f"Erro ao serializar dados para cache (key={key}): {e}")
        return False


async def get_json_cache(key: str) -> Optional[Any]:
    """
    Recupera e desserializa dados JSON do cache.

    Args:
        key: A chave do cache

    Returns:
        Os dados desserializados ou None se não encontrado ou erro
    """
    try:
        data = await get_cache(key)
        if data:
            return json.loads(data)
        return None
    except json.JSONDecodeError as e:
        logger.error(f"Erro ao desserializar dados do cache (key={key}): {e}")
        return None


# Decorador para cache de funções assíncronas


def async_cache(prefix: str, ttl: int = DEFAULT_CACHE_TTL):
    """
    Decorador para cache de funções assíncronas.

    Args:
        prefix: Prefixo para a chave do cache
        ttl: Tempo de vida em segundos (padrão: 1 hora)

    Returns:
        Decorador para a função
    """

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Gerar chave de cache baseada nos argumentos
            key_parts = [prefix, func.__name__]

            # Adicionar args e kwargs à chave
            if args:
                key_parts.extend([str(arg) for arg in args])

            if kwargs:
                # Ordenar kwargs para garantir consistência
                sorted_kwargs = sorted(kwargs.items())
                key_parts.extend([f"{k}={v}" for k, v in sorted_kwargs])

            cache_key = ":".join(key_parts)

            # Tentar obter do cache
            cached_result = await get_json_cache(cache_key)
            if cached_result is not None:
                return cached_result

            # Executar função e armazenar resultado
            result = await func(*args, **kwargs)
            await cache_json(cache_key, result, ttl)
            return result

        return wrapper

    return decorator


# Função para obter o cliente Redis


def get_redis_client():
    """
    Retorna o cliente Redis assíncrono.

    Returns:
        Instância do cliente Redis assíncrono
    """
    return async_redis_client
