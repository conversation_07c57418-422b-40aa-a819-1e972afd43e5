"""
Translation change model for the i18n module.

This model tracks changes to translations, allowing for incremental updates.
"""

import uuid
import enum
from sqlalchemy import Column, String, Text, ForeignKey, Index, Enum, DateTime
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import Base


class ChangeType(enum.Enum):
    """Type of change made to a translation."""

    ADDED = "added"
    UPDATED = "updated"
    DELETED = "deleted"


class TranslationChange(Base):
    """
    Model for tracking changes to translations.

    This allows clients to request only the changes since a specific version,
    making updates more efficient.
    """

    __tablename__ = "translation_changes"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # The translation key that was changed
    key = Column(String(255), nullable=False, index=True)

    # The sector/namespace this translation belongs to (e.g., "menu", "auth", "common")
    sector = Column(String(50), nullable=False, index=True)

    # The type of change (added, updated, deleted)
    change_type = Column(Enum(ChangeType), nullable=False)

    # The new text value (null if deleted)
    new_text = Column(Text, nullable=True)

    # The previous text value (null if added)
    previous_text = Column(Text, nullable=True)

    # When the change was made
    changed_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)

    # Foreign keys
    language_id = Column(UUID(as_uuid=True), ForeignKey("languages.id"), nullable=False)
    changed_by_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # The version code after this change
    version_code = Column(String(6), nullable=False)

    # Relationships
    language = relationship("Language", backref="translation_changes")
    changed_by = relationship("User", foreign_keys=[changed_by_id], backref="translation_changes")

    __table_args__ = (
        # Index for efficient querying of changes by language and version
        Index("ix_translation_changes_language_id_version", "language_id", "version_code"),
        # Index for efficient querying of changes by sector
        Index("ix_translation_changes_sector", "sector"),
    )

    def __repr__(self):
        return f"<TranslationChange(id={self.id}, key='{self.key}', sector='{self.sector}', change_type='{self.change_type.value}')>"
