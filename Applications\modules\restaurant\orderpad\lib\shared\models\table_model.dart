import 'package:uuid/uuid.dart';

class TableModel {
  final String id;
  final String number;
  final String name;
  final int capacity;
  final String status; // 'free', 'occupied', 'reserved', 'cleaning'
  final String? currentOrderId;
  final String? assignedWaiterId;
  final String? assignedWaiterName;
  final DateTime? occupiedSince;
  final DateTime? reservedAt;
  final String? reservedFor; // Customer name for reservation
  final String? reservedPhone;
  final String area; // 'indoor', 'outdoor', 'vip', 'bar'
  final Map<String, double> position; // x, y coordinates for visual layout
  final String shape; // 'round', 'square', 'rectangle'
  final bool isActive;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  TableModel({
    required this.id,
    required this.number,
    required this.name,
    required this.capacity,
    required this.status,
    this.currentOrderId,
    this.assignedWaiterId,
    this.assignedWaiterName,
    this.occupiedSince,
    this.reservedAt,
    this.reservedFor,
    this.reservedPhone,
    this.area = 'indoor',
    this.position = const {'x': 0, 'y': 0},
    this.shape = 'round',
    this.isActive = true,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory TableModel.create({
    required String number,
    required String name,
    required int capacity,
    String status = 'free',
    String area = 'indoor',
    Map<String, double> position = const {'x': 0, 'y': 0},
    String shape = 'round',
    String? notes,
  }) {
    final now = DateTime.now();
    return TableModel(
      id: const Uuid().v4(),
      number: number,
      name: name,
      capacity: capacity,
      status: status,
      area: area,
      position: position,
      shape: shape,
      notes: notes,
      createdAt: now,
      updatedAt: now,
    );
  }

  factory TableModel.fromJson(Map<String, dynamic> json) {
    return TableModel(
      id: json['id'] ?? '',
      number: json['number'] ?? '',
      name: json['name'] ?? '',
      capacity: json['capacity'] ?? 2,
      status: json['status'] ?? 'free',
      currentOrderId: json['currentOrderId'],
      assignedWaiterId: json['assignedWaiterId'],
      assignedWaiterName: json['assignedWaiterName'],
      occupiedSince: json['occupiedSince'] != null
          ? DateTime.parse(json['occupiedSince'])
          : null,
      reservedAt: json['reservedAt'] != null
          ? DateTime.parse(json['reservedAt'])
          : null,
      reservedFor: json['reservedFor'],
      reservedPhone: json['reservedPhone'],
      area: json['area'] ?? 'indoor',
      position: json['position'] != null
          ? Map<String, double>.from(json['position'])
          : const {'x': 0, 'y': 0},
      shape: json['shape'] ?? 'round',
      isActive: json['isActive'] ?? true,
      notes: json['notes'],
      createdAt: DateTime.parse(
          json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(
          json['updatedAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'number': number,
      'name': name,
      'capacity': capacity,
      'status': status,
      'currentOrderId': currentOrderId,
      'assignedWaiterId': assignedWaiterId,
      'assignedWaiterName': assignedWaiterName,
      'occupiedSince': occupiedSince?.toIso8601String(),
      'reservedAt': reservedAt?.toIso8601String(),
      'reservedFor': reservedFor,
      'reservedPhone': reservedPhone,
      'area': area,
      'position': position,
      'shape': shape,
      'isActive': isActive,
      'notes': notes,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  TableModel copyWith({
    String? id,
    String? number,
    String? name,
    int? capacity,
    String? status,
    String? currentOrderId,
    String? assignedWaiterId,
    String? assignedWaiterName,
    DateTime? occupiedSince,
    DateTime? reservedAt,
    String? reservedFor,
    String? reservedPhone,
    String? area,
    Map<String, double>? position,
    String? shape,
    bool? isActive,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TableModel(
      id: id ?? this.id,
      number: number ?? this.number,
      name: name ?? this.name,
      capacity: capacity ?? this.capacity,
      status: status ?? this.status,
      currentOrderId: currentOrderId ?? this.currentOrderId,
      assignedWaiterId: assignedWaiterId ?? this.assignedWaiterId,
      assignedWaiterName: assignedWaiterName ?? this.assignedWaiterName,
      occupiedSince: occupiedSince ?? this.occupiedSince,
      reservedAt: reservedAt ?? this.reservedAt,
      reservedFor: reservedFor ?? this.reservedFor,
      reservedPhone: reservedPhone ?? this.reservedPhone,
      area: area ?? this.area,
      position: position ?? this.position,
      shape: shape ?? this.shape,
      isActive: isActive ?? this.isActive,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  // Helper methods
  bool get isFree => status == TableStatus.free;
  bool get isOccupied => status == TableStatus.occupied;
  bool get isReserved => status == TableStatus.reserved;
  bool get isCleaning => status == TableStatus.cleaning;
  bool get hasOrder => currentOrderId != null && currentOrderId!.isNotEmpty;
  bool get hasWaiter => assignedWaiterId != null && assignedWaiterId!.isNotEmpty;

  Duration? get occupiedDuration {
    if (occupiedSince == null) return null;
    return DateTime.now().difference(occupiedSince!);
  }

  String get displayName => name.isNotEmpty ? name : 'Mesa $number';

  String get statusDisplay {
    switch (status) {
      case TableStatus.free:
        return 'Livre';
      case TableStatus.occupied:
        return 'Ocupada';
      case TableStatus.reserved:
        return 'Reservada';
      case TableStatus.cleaning:
        return 'Limpeza';
      default:
        return 'Desconhecido';
    }
  }

  String get areaDisplay {
    switch (area) {
      case TableArea.indoor:
        return 'Interno';
      case TableArea.outdoor:
        return 'Externo';
      case TableArea.vip:
        return 'VIP';
      case TableArea.bar:
        return 'Bar';
      default:
        return area;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TableModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'TableModel(id: $id, number: $number, name: $name, status: $status)';
  }
}

// Table status constants
class TableStatus {
  static const String free = 'free';
  static const String occupied = 'occupied';
  static const String reserved = 'reserved';
  static const String cleaning = 'cleaning';

  static List<String> get all => [free, occupied, reserved, cleaning];

  static String getDisplayName(String status) {
    switch (status) {
      case free:
        return 'Livre';
      case occupied:
        return 'Ocupada';
      case reserved:
        return 'Reservada';
      case cleaning:
        return 'Limpeza';
      default:
        return 'Desconhecido';
    }
  }
}

// Table area constants
class TableArea {
  static const String indoor = 'indoor';
  static const String outdoor = 'outdoor';
  static const String vip = 'vip';
  static const String bar = 'bar';

  static List<String> get all => [indoor, outdoor, vip, bar];

  static String getDisplayName(String area) {
    switch (area) {
      case indoor:
        return 'Interno';
      case outdoor:
        return 'Externo';
      case vip:
        return 'VIP';
      case bar:
        return 'Bar';
      default:
        return area;
    }
  }
}

// Table shape constants
class TableShape {
  static const String round = 'round';
  static const String square = 'square';
  static const String rectangle = 'rectangle';

  static List<String> get all => [round, square, rectangle];

  static String getDisplayName(String shape) {
    switch (shape) {
      case round:
        return 'Redonda';
      case square:
        return 'Quadrada';
      case rectangle:
        return 'Retangular';
      default:
        return shape;
    }
  }
}