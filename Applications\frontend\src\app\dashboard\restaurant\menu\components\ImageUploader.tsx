'use client';

import { useState, useRef, useEffect, useCallback } from 'react';
import Image from 'next/image';
import {
  PhotoIcon,
  PlusIcon,
  XMarkIcon,
  MagnifyingGlassIcon,
} from '@heroicons/react/24/outline';
import { Loader2 } from 'lucide-react';
import { mediaUploadService, MediaUploadResponse, UploadProgress } from '@/lib/services/mediaUploadService';
import { mediaSyncService } from '@/lib/services/mediaSyncService';
import { AuthenticatedImage } from '@/components/common/AuthenticatedImage';
import Cookies from 'js-cookie';
import { ImageViewer } from './ImageViewer';

interface ImageData {
  url: string;
  mediaData?: MediaUploadResponse;
  isUploading?: boolean;
  uploadProgress?: UploadProgress;
  error?: string;
  tempId?: string; // Temporary ID for tracking uploads
}

interface ImageUploaderProps {
  images: string[];
  onImagesChange: (images: string[]) => void;
  menuItemId?: string;
  maxImages?: number;
  disabled?: boolean;
}

export function ImageUploader({
  images,
  onImagesChange,
  menuItemId,
  maxImages = 9,
  disabled = false,
}: ImageUploaderProps) {
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);
  const [imageDataList, setImageDataList] = useState<ImageData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [viewerOpen, setViewerOpen] = useState(false);
  const [viewerImageSrc, setViewerImageSrc] = useState('');
  const [viewerImageAlt, setViewerImageAlt] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const lastNotifiedUrls = useRef<string[]>([]);

  // Initialize image data from props
  useEffect(() => {
    const newImageDataList = images.map(url => ({ url }));
    setImageDataList(newImageDataList);
  }, [images]);

  // TEMPORARILY DISABLED - causing infinite loops
  // TODO: Fix notification system later
  /*
  useEffect(() => {
    const urls = imageDataList.map(img => img.url);
    const urlsChanged = JSON.stringify(urls) !== JSON.stringify(lastNotifiedUrls.current);

    if (urlsChanged) {
      lastNotifiedUrls.current = urls;
      setTimeout(() => onImagesChange(urls), 0);
    }
  }, [imageDataList]);
  */

  const loadExistingMedia = useCallback(async () => {
    if (!menuItemId) return;

    setIsLoading(true);
    try {
      const mediaList = await mediaUploadService.getMenuItemMedia(menuItemId);
      const imageData = mediaList.map(media => ({
        url: media.file_url, // Use the file_url from the API response
        mediaData: media,
        isUploading: false // Ensure existing images are marked as not uploading
      }));

      setImageDataList(imageData);

      console.log(`Loaded ${imageData.length} existing images for item ${menuItemId}`);
    } catch (error) {
      console.error('Failed to load existing media:', error);
      // Don't clear existing images on error, just log it
    } finally {
      setIsLoading(false);
    }
  }, [menuItemId]);

  // Load existing media if menuItemId is provided
  useEffect(() => {
    if (menuItemId) {
      loadExistingMedia();
    }
  }, [menuItemId, loadExistingMedia]); // Removed images.length dependency to always load when menuItemId changes

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    const remainingSlots = maxImages - images.length;
    const filesToProcess = Array.from(files).slice(0, remainingSlots);

    for (const file of filesToProcess) {
      if (file.type.startsWith('image/')) {
        await processFile(file);
      }
    }

    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const processFile = async (file: File) => {
    console.log('🔍 ImageUploader.processFile - Starting upload for file:', file.name, 'menuItemId:', menuItemId);

    // Create preview immediately
    const previewUrl = mediaUploadService.createPreviewUrl(file);
    const tempId = `uploading-${Date.now()}-${Math.random()}`;
    const tempImageData: ImageData = {
      url: previewUrl,
      isUploading: true,
      uploadProgress: { loaded: 0, total: file.size, percentage: 0 },
      tempId // Add temporary ID for tracking
    };

    // Add to list with loading state - use functional update to avoid stale state
    setImageDataList(prevList => [...prevList, tempImageData]);

    // If no menuItemId, upload to user context for temporary storage
    if (!menuItemId) {
      console.log('🔍 ImageUploader.processFile - No menuItemId, uploading to user context');
      try {
        const response = await mediaUploadService.uploadUserMedia([file], {
          onProgress: (progress) => {
            setImageDataList(prevList =>
              prevList.map(img =>
                img.tempId === tempId
                  ? { ...img, uploadProgress: progress }
                  : img
              )
            );
          },
          onSuccess: (apiResponse) => {
            if (apiResponse.success && apiResponse.uploads && apiResponse.uploads.length > 0) {
              const mediaData = apiResponse.uploads[0];
              const updatedImageData: ImageData = {
                url: mediaData.file_url,
                mediaData,
                isUploading: false,
                tempId: undefined // Clear temp ID
              };

              setImageDataList(prevList =>
                prevList.map(img =>
                  img.tempId === tempId ? updatedImageData : img
                )
              );

              // Clean up preview URL
              mediaUploadService.revokePreviewUrl(previewUrl);

              // TEMPORARILY DISABLED - causing errors
              // if (menuItemId) {
              //   mediaSyncService.onMediaChange(menuItemId);
              // }
            }
          },
          onError: (error) => {
            setImageDataList(prevList =>
              prevList.map(img =>
                img.tempId === tempId
                  ? { ...img, isUploading: false, error: error.message }
                  : img
              )
            );
          }
        });
      } catch (error) {
        console.error('Upload failed:', error);
        setImageDataList(prevList =>
          prevList.map(img =>
            img.tempId === tempId
              ? {
                  ...img,
                  isUploading: false,
                  error: error instanceof Error ? error.message : 'Upload failed'
                }
              : img
          )
        );
      }
      return;
    }

    // Upload to FTP system
    try {
      console.log('🔍 ImageUploader.processFile - Uploading to menu item:', menuItemId);
      const response = await mediaUploadService.uploadMenuItemMedia(menuItemId, [file], {
        onProgress: (progress) => {
          setImageDataList(prevList =>
            prevList.map(img =>
              img.tempId === tempId
                ? { ...img, uploadProgress: progress }
                : img
            )
          );
        },
        onSuccess: (apiResponse) => {
          console.log('🔍 ImageUploader.processFile - Upload success:', apiResponse);
          if (apiResponse.success && apiResponse.uploads && apiResponse.uploads.length > 0) {
            const mediaData = apiResponse.uploads[0]; // Get first uploaded file
            console.log('🔍 ImageUploader.processFile - Media data received:', mediaData);
            const updatedImageData: ImageData = {
              url: mediaData.file_url, // Use file_url from API response
              mediaData,
              isUploading: false,
              tempId: undefined // Clear temp ID
            };

            setImageDataList(prevList =>
              prevList.map(img =>
                img.tempId === tempId ? updatedImageData : img
              )
            );

            // Clean up preview URL
            mediaUploadService.revokePreviewUrl(previewUrl);

            // TEMPORARILY DISABLED - causing errors
            // console.log('🔍 ImageUploader.processFile - Calling mediaSyncService for:', menuItemId);
            // mediaSyncService.onMediaChange(menuItemId);
          }
        },
        onError: (error) => {
          setImageDataList(prevList =>
            prevList.map(img =>
              img.tempId === tempId
                ? { ...img, isUploading: false, error: error.message }
                : img
            )
          );
        }
      });
    } catch (error) {
      console.error('Upload failed:', error);
      setImageDataList(prevList =>
        prevList.map(img =>
          img.tempId === tempId
            ? {
                ...img,
                isUploading: false,
                error: error instanceof Error ? error.message : 'Upload failed'
              }
            : img
        )
      );
    }
  };

  const removeImage = async (index: number) => {
    const imageData = imageDataList[index];

    // If it's an uploaded media file, delete from server
    if (imageData?.mediaData && menuItemId) {
      try {
        await mediaUploadService.deleteMedia(menuItemId, imageData.mediaData.id);
      } catch (error) {
        console.error('Failed to delete media from server:', error);
      }
    }

    // Clean up preview URL if it's a blob
    if (imageData?.url.startsWith('blob:')) {
      mediaUploadService.revokePreviewUrl(imageData.url);
    }

    const newImageDataList = imageDataList.filter((_, i) => i !== index);

    setImageDataList(newImageDataList);

    // TEMPORARILY DISABLED - causing errors
    // if (menuItemId) {
    //   localStorage.removeItem(`media_cache_${menuItemId}`);
    //   mediaSyncService.onMediaChange(menuItemId);
    // }
  };

  const openImageModal = (index: number) => {
    setSelectedImageIndex(index);
  };

  const closeImageModal = () => {
    setSelectedImageIndex(null);
  };

  const canAddMore = imageDataList.length < maxImages;

  // Test function to check media API
  const testMediaAPI = async () => {
    const token = Cookies.get('access_token');
    console.log('🔍 Testing Media API endpoints...');

    const endpointsToTest = [
      '/api/modules/core/media/uploads?context_type=menu_item&limit=1',
      '/api/media/uploads?context_type=menu_item&limit=1',
      '/api/modules/core/media_system/uploads?context_type=menu_item&limit=1'
    ];

    for (const endpoint of endpointsToTest) {
      try {
        console.log(`🔍 Testing endpoint: ${endpoint}`);
        const response = await fetch(endpoint, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
        console.log(`🔍 ${endpoint} - Status: ${response.status}`);
        if (response.ok) {
          const data = await response.json();
          console.log(`🔍 ${endpoint} - Data:`, data);
        } else {
          const errorText = await response.text();
          console.log(`🔍 ${endpoint} - Error:`, errorText);
        }
      } catch (err) {
        console.error(`🔍 ${endpoint} - Exception:`, err);
      }
    }
  };



  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Carregando imagens...</span>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      <div className="grid grid-cols-3 gap-4">
        {/* Existing Images */}
        {imageDataList.map((imageData, index) => (
          <div
            key={index}
            className="relative aspect-square bg-gray-100 rounded-lg overflow-hidden group cursor-pointer"
            onClick={() => !imageData.isUploading && openImageModal(index)}
          >
            {/* Use blob URL for preview or authenticated request for uploaded images */}
            {imageData.url.startsWith('blob:') ? (
              <Image
                src={imageData.url}
                alt={`Imagem ${index + 1}`}
                fill
                className="object-cover"
                onLoad={() => {
                  console.log('🔍 Blob image loaded successfully:', imageData.url);
                }}
              />
            ) : (
              <AuthenticatedImage
                src={imageData.url}
                alt={`Imagem ${index + 1}`}
                className="w-full h-full object-cover"
                onError={() => {
                  console.log('🔍 Authenticated image load error for URL:', imageData.url);
                  console.log('🔍 Image data:', imageData);
                }}
                onLoad={() => {
                  // Image loaded successfully
                }}
              />
            )}

            {/* Upload Progress Overlay */}
            {imageData.isUploading && (
              <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                <div className="text-center text-white">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
                  <div className="text-sm">
                    {imageData.uploadProgress?.percentage || 0}%
                  </div>
                  <div className="w-20 bg-gray-200 rounded-full h-2 mt-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${imageData.uploadProgress?.percentage || 0}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            )}

            {/* Error Overlay */}
            {imageData.error && (
              <div className="absolute inset-0 bg-red-500 bg-opacity-75 flex items-center justify-center">
                <div className="text-center text-white p-2">
                  <XMarkIcon className="h-6 w-6 mx-auto mb-1" />
                  <div className="text-xs">{imageData.error}</div>
                </div>
              </div>
            )}

            {/* Hover Overlay */}
            {!imageData.isUploading && (
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2">
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      openImageModal(index);
                    }}
                    className="p-2 bg-white rounded-full shadow-lg hover:bg-gray-50"
                    disabled={disabled}
                  >
                    <MagnifyingGlassIcon className="h-4 w-4 text-gray-600" />
                  </button>
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      removeImage(index);
                    }}
                    className="p-2 bg-white rounded-full shadow-lg hover:bg-gray-50"
                    disabled={disabled}
                  >
                    <XMarkIcon className="h-4 w-4 text-red-600" />
                  </button>
                </div>
              </div>
            )}

            {/* Image Number */}
            <div className="absolute top-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
              {index + 1}
            </div>

            {/* Upload Status Indicator */}
            {imageData.mediaData && (
              <div className="absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded">
                ✓
              </div>
            )}
          </div>
        ))}

        {/* Add New Image Button */}
        {canAddMore && (
          <button
            type="button"
            onClick={() => fileInputRef.current?.click()}
            className="aspect-square border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center text-gray-500 hover:border-gray-400 hover:text-gray-600 transition-colors"
            disabled={disabled}
          >
            <PlusIcon className="h-8 w-8 mb-2" />
            <span className="text-sm font-medium">Adicionar Foto</span>
            <span className="text-xs text-gray-400">
              {images.length}/{maxImages}
            </span>
          </button>
        )}
      </div>

      {/* File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        multiple
        onChange={handleFileSelect}
        className="hidden"
        disabled={disabled}
      />

      {/* Info */}
      <div className="text-sm text-gray-600">
        <p>• Máximo de {maxImages} fotos</p>
        <p>• Formatos aceitos: JPG, PNG, GIF, WebP, SVG</p>
        <p>• Tamanho máximo: 10MB por arquivo</p>
        <p>• Clique nas fotos para visualizar em tamanho grande</p>
        {menuItemId && (
          <p>• ✓ Imagens são salvas automaticamente no sistema de mídia</p>
        )}

        {/* Debug button */}
        <button
          type="button"
          onClick={testMediaAPI}
          className="mt-2 px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600"
        >
          🔍 Test Media API
        </button>

        {/* Debug button */}
        <button
          type="button"
          onClick={testMediaAPI}
          className="mt-2 px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600"
        >
          🔍 Test Media API
        </button>
      </div>

      {/* Image Viewer */}
      {selectedImageIndex !== null && imageDataList[selectedImageIndex] && (
        <ImageViewer
          src={imageDataList[selectedImageIndex].url}
          alt={`Imagem ${selectedImageIndex + 1} de ${imageDataList.length}`}
          isOpen={true}
          onClose={closeImageModal}
        />
      )}
    </div>
  );
}




