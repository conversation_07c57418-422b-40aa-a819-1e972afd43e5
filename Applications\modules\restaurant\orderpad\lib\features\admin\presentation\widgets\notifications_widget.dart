import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class NotificationsWidget extends StatefulWidget {
  final List<NotificationItem> notifications;
  final VoidCallback? onViewAll;
  final Function(NotificationItem)? onNotificationTap;
  final Function(NotificationItem)? onMarkAsRead;
  final Function(NotificationItem)? onDismiss;
  final int maxNotifications;

  const NotificationsWidget({
    super.key,
    required this.notifications,
    this.onViewAll,
    this.onNotificationTap,
    this.onMarkAsRead,
    this.onDismiss,
    this.maxNotifications = 5,
  });

  @override
  State<NotificationsWidget> createState() => _NotificationsWidgetState();
}

class _NotificationsWidgetState extends State<NotificationsWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final displayNotifications = widget.notifications
        .take(widget.maxNotifications)
        .toList();
    final unreadCount = widget.notifications
        .where((n) => !n.isRead)
        .length;

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Stack(
                    children: [
                      Icon(
                        Icons.notifications,
                        color: theme.primaryColor,
                        size: 24,
                      ),
                      if (unreadCount > 0)
                        Positioned(
                          right: 0,
                          top: 0,
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            constraints: const BoxConstraints(
                              minWidth: 16,
                              minHeight: 16,
                            ),
                            child: Text(
                              unreadCount.toString(),
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 10,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Notificações',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  if (widget.onViewAll != null)
                    TextButton(
                      onPressed: widget.onViewAll,
                      child: const Text('Ver Todas'),
                    ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              if (displayNotifications.isEmpty)
                _buildEmptyState(theme)
              else
                ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: displayNotifications.length,
                  separatorBuilder: (context, index) => const SizedBox(height: 8),
                  itemBuilder: (context, index) {
                    return NotificationCard(
                      notification: displayNotifications[index],
                      onTap: widget.onNotificationTap,
                      onMarkAsRead: widget.onMarkAsRead,
                      onDismiss: widget.onDismiss,
                    );
                  },
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(
            Icons.notifications_none,
            size: 48,
            color: theme.colorScheme.onSurface.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'Nenhuma notificação',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Você está em dia com tudo!',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.5),
            ),
          ),
        ],
      ),
    );
  }
}

class NotificationCard extends StatefulWidget {
  final NotificationItem notification;
  final Function(NotificationItem)? onTap;
  final Function(NotificationItem)? onMarkAsRead;
  final Function(NotificationItem)? onDismiss;

  const NotificationCard({
    super.key,
    required this.notification,
    this.onTap,
    this.onMarkAsRead,
    this.onDismiss,
  });

  @override
  State<NotificationCard> createState() => _NotificationCardState();
}

class _NotificationCardState extends State<NotificationCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final timeAgo = _getTimeAgo(widget.notification.timestamp);

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Dismissible(
            key: Key(widget.notification.id),
            direction: DismissDirection.endToStart,
            onDismissed: (direction) {
              widget.onDismiss?.call(widget.notification);
            },
            background: Container(
              alignment: Alignment.centerRight,
              padding: const EdgeInsets.only(right: 16),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.delete,
                color: Colors.white,
              ),
            ),
            child: Card(
              elevation: widget.notification.isRead ? 0 : 1,
              color: widget.notification.isRead
                  ? theme.colorScheme.surface
                  : theme.colorScheme.primary.withOpacity(0.05),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: widget.notification.isRead
                    ? BorderSide.none
                    : BorderSide(
                        color: theme.colorScheme.primary.withOpacity(0.2),
                        width: 1,
                      ),
              ),
              child: InkWell(
                onTap: () {
                  _animationController.forward().then((_) {
                    _animationController.reverse();
                  });
                  widget.onTap?.call(widget.notification);
                },
                borderRadius: BorderRadius.circular(12),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: widget.notification.type.color.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          widget.notification.type.icon,
                          color: widget.notification.type.color,
                          size: 20,
                        ),
                      ),
                      
                      const SizedBox(width: 12),
                      
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    widget.notification.title,
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      fontWeight: widget.notification.isRead
                                          ? FontWeight.normal
                                          : FontWeight.bold,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                if (!widget.notification.isRead)
                                  Container(
                                    width: 8,
                                    height: 8,
                                    decoration: BoxDecoration(
                                      color: theme.colorScheme.primary,
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                  ),
                              ],
                            ),
                            
                            if (widget.notification.message != null) ...[
                              const SizedBox(height: 4),
                              Text(
                                widget.notification.message!,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                            
                            const SizedBox(height: 4),
                            
                            Row(
                              children: [
                                Text(
                                  timeAgo,
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.colorScheme.onSurface.withOpacity(0.5),
                                  ),
                                ),
                                
                                if (widget.notification.priority == NotificationPriority.high) ...[
                                  const SizedBox(width: 8),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 6,
                                      vertical: 2,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colors.red.withOpacity(0.2),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: Text(
                                      'URGENTE',
                                      style: theme.textTheme.bodySmall?.copyWith(
                                        color: Colors.red,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 10,
                                      ),
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ],
                        ),
                      ),
                      
                      if (!widget.notification.isRead && widget.onMarkAsRead != null)
                        IconButton(
                          onPressed: () {
                            widget.onMarkAsRead?.call(widget.notification);
                          },
                          icon: Icon(
                            Icons.mark_email_read,
                            size: 20,
                            color: theme.colorScheme.primary,
                          ),
                          tooltip: 'Marcar como lida',
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  String _getTimeAgo(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Agora';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m atrás';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h atrás';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d atrás';
    } else {
      return DateFormat('dd/MM').format(timestamp);
    }
  }
}

class NotificationItem {
  final String id;
  final String title;
  final String? message;
  final NotificationType type;
  final NotificationPriority priority;
  final DateTime timestamp;
  final bool isRead;
  final Map<String, dynamic>? data;

  const NotificationItem({
    required this.id,
    required this.title,
    this.message,
    required this.type,
    this.priority = NotificationPriority.normal,
    required this.timestamp,
    this.isRead = false,
    this.data,
  });

  NotificationItem copyWith({
    String? id,
    String? title,
    String? message,
    NotificationType? type,
    NotificationPriority? priority,
    DateTime? timestamp,
    bool? isRead,
    Map<String, dynamic>? data,
  }) {
    return NotificationItem(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      data: data ?? this.data,
    );
  }

  // Factory constructors for common notification types
  static NotificationItem newOrder({
    required String orderId,
    required String tableNumber,
    DateTime? timestamp,
  }) {
    return NotificationItem(
      id: 'order_$orderId',
      title: 'Novo Pedido',
      message: 'Mesa $tableNumber fez um novo pedido',
      type: NotificationType.order,
      priority: NotificationPriority.high,
      timestamp: timestamp ?? DateTime.now(),
      data: {'orderId': orderId, 'tableNumber': tableNumber},
    );
  }

  static NotificationItem orderReady({
    required String orderId,
    required String tableNumber,
    DateTime? timestamp,
  }) {
    return NotificationItem(
      id: 'ready_$orderId',
      title: 'Pedido Pronto',
      message: 'Pedido da Mesa $tableNumber está pronto',
      type: NotificationType.order,
      priority: NotificationPriority.normal,
      timestamp: timestamp ?? DateTime.now(),
      data: {'orderId': orderId, 'tableNumber': tableNumber},
    );
  }

  static NotificationItem lowStock({
    required String itemName,
    required int quantity,
    DateTime? timestamp,
  }) {
    return NotificationItem(
      id: 'stock_$itemName',
      title: 'Estoque Baixo',
      message: '$itemName - Restam apenas $quantity unidades',
      type: NotificationType.inventory,
      priority: NotificationPriority.high,
      timestamp: timestamp ?? DateTime.now(),
      data: {'itemName': itemName, 'quantity': quantity},
    );
  }

  static NotificationItem staffLogin({
    required String staffName,
    DateTime? timestamp,
  }) {
    return NotificationItem(
      id: 'login_${DateTime.now().millisecondsSinceEpoch}',
      title: 'Funcionário Conectado',
      message: '$staffName fez login no sistema',
      type: NotificationType.staff,
      priority: NotificationPriority.low,
      timestamp: timestamp ?? DateTime.now(),
      data: {'staffName': staffName},
    );
  }

  static NotificationItem paymentReceived({
    required String orderId,
    required double amount,
    DateTime? timestamp,
  }) {
    return NotificationItem(
      id: 'payment_$orderId',
      title: 'Pagamento Recebido',
      message: 'R\$ ${amount.toStringAsFixed(2)} - Pedido #$orderId',
      type: NotificationType.payment,
      priority: NotificationPriority.normal,
      timestamp: timestamp ?? DateTime.now(),
      data: {'orderId': orderId, 'amount': amount},
    );
  }

  static NotificationItem systemAlert({
    required String message,
    NotificationPriority? priority,
    DateTime? timestamp,
  }) {
    return NotificationItem(
      id: 'system_${DateTime.now().millisecondsSinceEpoch}',
      title: 'Alerta do Sistema',
      message: message,
      type: NotificationType.system,
      priority: priority ?? NotificationPriority.normal,
      timestamp: timestamp ?? DateTime.now(),
    );
  }

  static NotificationItem customerFeedback({
    required String customerName,
    required int rating,
    String? comment,
    DateTime? timestamp,
  }) {
    return NotificationItem(
      id: 'feedback_${DateTime.now().millisecondsSinceEpoch}',
      title: 'Nova Avaliação',
      message: '$customerName avaliou com $rating estrelas${comment != null ? ': "$comment"' : ''}',
      type: NotificationType.feedback,
      priority: NotificationPriority.normal,
      timestamp: timestamp ?? DateTime.now(),
      data: {'customerName': customerName, 'rating': rating, 'comment': comment},
    );
  }
}

enum NotificationType {
  order(Icons.receipt_long, Colors.blue),
  inventory(Icons.inventory, Colors.orange),
  staff(Icons.people, Colors.green),
  payment(Icons.payment, Colors.purple),
  system(Icons.settings, Colors.grey),
  feedback(Icons.star, Colors.amber),
  delivery(Icons.delivery_dining, Colors.teal),
  promotion(Icons.local_offer, Colors.red);

  const NotificationType(this.icon, this.color);
  
  final IconData icon;
  final Color color;
}

enum NotificationPriority {
  low,
  normal,
  high,
}

class CompactNotificationsWidget extends StatelessWidget {
  final List<NotificationItem> notifications;
  final VoidCallback? onViewAll;
  final Function(NotificationItem)? onNotificationTap;
  final int maxNotifications;

  const CompactNotificationsWidget({
    super.key,
    required this.notifications,
    this.onViewAll,
    this.onNotificationTap,
    this.maxNotifications = 3,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final displayNotifications = notifications
        .take(maxNotifications)
        .toList();
    final unreadCount = notifications
        .where((n) => !n.isRead)
        .length;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Stack(
              children: [
                Icon(
                  Icons.notifications,
                  color: theme.primaryColor,
                  size: 20,
                ),
                if (unreadCount > 0)
                  Positioned(
                    right: 0,
                    top: 0,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 12,
                        minHeight: 12,
                      ),
                      child: Text(
                        unreadCount.toString(),
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 8,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(width: 8),
            Text(
              'Notificações',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            if (onViewAll != null)
              TextButton(
                onPressed: onViewAll,
                child: const Text('Ver Todas'),
              ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        if (displayNotifications.isEmpty)
          Container(
            padding: const EdgeInsets.all(16),
            child: Text(
              'Nenhuma notificação',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          )
        else
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: displayNotifications.length,
            separatorBuilder: (context, index) => const SizedBox(height: 4),
            itemBuilder: (context, index) {
              final notification = displayNotifications[index];
              return Card(
                elevation: 0,
                margin: EdgeInsets.zero,
                color: notification.isRead
                    ? theme.colorScheme.surface
                    : theme.colorScheme.primary.withOpacity(0.05),
                child: ListTile(
                  dense: true,
                  leading: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: notification.type.color.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      notification.type.icon,
                      color: notification.type.color,
                      size: 16,
                    ),
                  ),
                  title: Text(
                    notification.title,
                    style: theme.textTheme.bodySmall?.copyWith(
                      fontWeight: notification.isRead
                          ? FontWeight.normal
                          : FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  subtitle: notification.message != null
                      ? Text(
                          notification.message!,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface.withOpacity(0.6),
                            fontSize: 11,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        )
                      : null,
                  trailing: !notification.isRead
                      ? Container(
                          width: 6,
                          height: 6,
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary,
                            borderRadius: BorderRadius.circular(3),
                          ),
                        )
                      : null,
                  onTap: () => onNotificationTap?.call(notification),
                ),
              );
            },
          ),
      ],
    );
  }
}