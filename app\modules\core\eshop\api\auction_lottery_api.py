"""
Auction and Lottery API Endpoints
=================================

REST API endpoints for auction and lottery operations.
"""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from sqlalchemy import desc, and_, or_, func, select

from app.core.db_dependencies import get_db
from app.core.auth import get_current_user, get_current_tenant
from app.modules.core.users.models.user import User
from app.modules.core.tenants.models.tenant import Tenant
from app.modules.core.eshop.models.auction_lottery import (
    Auction, AuctionBid, Lottery, LotteryTicket,
    AuctionStatus, LotteryStatus
)
from app.modules.core.eshop.models.product import Product
from app.modules.core.eshop.schemas.auction_lottery_schemas import (
    AuctionCreate, AuctionUpdate, AuctionResponse, AuctionListResponse,
    BidCreate, BidResponse, BidListResponse,
    LotteryCreate, LotteryUpdate, LotteryResponse, LotteryListResponse,
    TicketCreate, TicketResponse, TicketListResponse,
    AuctionStats, LotteryStats
)
from app.modules.core.eshop.services.auction_lottery_service import (
    AuctionService, LotteryService
)
from app.modules.core.eshop.websockets.auction_lottery_websockets import (
    auction_lottery_ws_manager
)

router = APIRouter()


# Auction Endpoints
@router.post("/auctions", response_model=AuctionResponse)
async def create_auction(
    auction_data: AuctionCreate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """Create a new auction."""
    service = AuctionService(db)
    
    try:
        auction = service.create_auction(
            auction_data=auction_data,
            vendor_id=current_user.id,
            tenant_id=current_tenant.id
        )
        
        # Load related data for response
        auction_with_relations = db.query(Auction).options(
            joinedload(Auction.product),
            joinedload(Auction.vendor)
        ).filter(Auction.id == auction.id).first()
        
        return _build_auction_response(auction_with_relations)
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/auctions", response_model=AuctionListResponse)
async def list_auctions(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    status: Optional[AuctionStatus] = None,
    vendor_id: Optional[UUID] = None,
    search: Optional[str] = None,
    db: Session = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """List auctions with filtering and pagination."""
    query = db.query(Auction).filter(
        Auction.tenant_id == current_tenant.id
    ).options(
        joinedload(Auction.product),
        joinedload(Auction.vendor)
    )
    
    # Apply filters
    if status:
        query = query.filter(Auction.status == status)
    
    if vendor_id:
        query = query.filter(Auction.vendor_id == vendor_id)
    
    if search:
        query = query.filter(
            or_(
                Auction.title.ilike(f"%{search}%"),
                Auction.description.ilike(f"%{search}%")
            )
        )
    
    # Order by creation date (newest first)
    query = query.order_by(desc(Auction.created_at))
    
    # Get total count
    total = query.count()
    
    # Apply pagination
    auctions = query.offset((page - 1) * size).limit(size).all()
    
    # Build response
    items = [_build_auction_response(auction) for auction in auctions]
    
    return AuctionListResponse(
        items=items,
        total=total,
        page=page,
        size=size,
        pages=(total + size - 1) // size
    )


@router.get("/auctions/{auction_id}", response_model=AuctionResponse)
async def get_auction(
    auction_id: UUID,
    db: Session = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """Get auction details."""
    auction = db.query(Auction).options(
        joinedload(Auction.product),
        joinedload(Auction.vendor),
        joinedload(Auction.winner)
    ).filter(
        Auction.id == auction_id,
        Auction.tenant_id == current_tenant.id
    ).first()
    
    if not auction:
        raise HTTPException(status_code=404, detail="Auction not found")
    
    # Increment view count
    auction.views_count += 1
    db.commit()
    
    return _build_auction_response(auction)


@router.put("/auctions/{auction_id}", response_model=AuctionResponse)
async def update_auction(
    auction_id: UUID,
    auction_data: AuctionUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """Update an auction."""
    service = AuctionService(db)
    
    try:
        auction = service.update_auction(
            auction_id=auction_id,
            auction_data=auction_data,
            vendor_id=current_user.id
        )
        
        # Load related data for response
        auction_with_relations = db.query(Auction).options(
            joinedload(Auction.product),
            joinedload(Auction.vendor)
        ).filter(Auction.id == auction.id).first()
        
        return _build_auction_response(auction_with_relations)
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/auctions/{auction_id}/activate", response_model=AuctionResponse)
async def activate_auction(
    auction_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Activate a draft auction."""
    service = AuctionService(db)
    
    try:
        auction = service.activate_auction(
            auction_id=auction_id,
            vendor_id=current_user.id
        )
        
        # Load related data for response
        auction_with_relations = db.query(Auction).options(
            joinedload(Auction.product),
            joinedload(Auction.vendor)
        ).filter(Auction.id == auction.id).first()
        
        return _build_auction_response(auction_with_relations)
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/auctions/{auction_id}/cancel", response_model=AuctionResponse)
async def cancel_auction(
    auction_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Cancel an auction."""
    service = AuctionService(db)
    
    try:
        auction = service.cancel_auction(
            auction_id=auction_id,
            vendor_id=current_user.id
        )
        
        # Notify WebSocket clients
        await auction_lottery_ws_manager.emit_auction_ended(
            auction_id=auction_id,
            reason="cancelled"
        )
        
        # Load related data for response
        auction_with_relations = db.query(Auction).options(
            joinedload(Auction.product),
            joinedload(Auction.vendor)
        ).filter(Auction.id == auction.id).first()
        
        return _build_auction_response(auction_with_relations)
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/auctions/{auction_id}/bid", response_model=BidResponse)
async def place_bid(
    auction_id: UUID,
    bid_data: BidCreate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Place a bid on an auction."""
    service = AuctionService(db)
    
    try:
        bid, is_extension_triggered = service.place_bid(
            bid_data=bid_data,
            bidder_id=current_user.id
        )
        
        # Load related data for response
        bid_with_relations = db.query(AuctionBid).options(
            joinedload(AuctionBid.bidder),
            joinedload(AuctionBid.auction)
        ).filter(AuctionBid.id == bid.id).first()
        
        # Get updated auction for time remaining
        auction = db.query(Auction).filter(Auction.id == auction_id).first()
        
        # Notify WebSocket clients
        background_tasks.add_task(
            auction_lottery_ws_manager.emit_new_bid,
            auction_id=auction_id,
            bid_data=_build_bid_response(bid_with_relations).__dict__,
            time_remaining=auction.time_remaining
        )
        
        # Notify about extension if triggered
        if is_extension_triggered:
            background_tasks.add_task(
                auction_lottery_ws_manager.emit_auction_extended,
                auction_id=auction_id,
                new_end_time=auction.end_time,
                extension_minutes=auction.extend_minutes,
                extensions_used=auction.extensions_used,
                max_extensions=auction.max_extensions
            )
        
        return _build_bid_response(bid_with_relations)
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/auctions/{auction_id}/buy-now", response_model=AuctionResponse)
async def buy_now(
    auction_id: UUID,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Execute buy-now purchase."""
    service = AuctionService(db)
    
    try:
        auction = service.buy_now(
            auction_id=auction_id,
            buyer_id=current_user.id
        )
        
        # Notify WebSocket clients
        background_tasks.add_task(
            auction_lottery_ws_manager.emit_buy_now_purchase,
            auction_id=auction_id,
            buyer_id=current_user.id,
            purchase_price=float(auction.buy_now_price)
        )
        
        background_tasks.add_task(
            auction_lottery_ws_manager.emit_auction_ended,
            auction_id=auction_id,
            winner_id=current_user.id,
            winning_bid=float(auction.winning_bid),
            reason="buy_now"
        )
        
        # Load related data for response
        auction_with_relations = db.query(Auction).options(
            joinedload(Auction.product),
            joinedload(Auction.vendor),
            joinedload(Auction.winner)
        ).filter(Auction.id == auction.id).first()
        
        return _build_auction_response(auction_with_relations)
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/auctions/{auction_id}/bids", response_model=BidListResponse)
async def get_auction_bids(
    auction_id: UUID,
    page: int = Query(1, ge=1),
    size: int = Query(50, ge=1, le=100),
    db: Session = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """Get bids for an auction."""
    # Verify auction exists and belongs to tenant
    auction = db.query(Auction).filter(
        Auction.id == auction_id,
        Auction.tenant_id == current_tenant.id
    ).first()
    
    if not auction:
        raise HTTPException(status_code=404, detail="Auction not found")
    
    service = AuctionService(db)
    bids, total = service.get_auction_bids(auction_id, page, size)
    
    # Load related data
    bids_with_relations = db.query(AuctionBid).options(
        joinedload(AuctionBid.bidder)
    ).filter(
        AuctionBid.auction_id == auction_id
    ).order_by(desc(AuctionBid.created_at)).offset((page - 1) * size).limit(size).all()
    
    items = [_build_bid_response(bid) for bid in bids_with_relations]
    
    return BidListResponse(
        items=items,
        total=total,
        page=page,
        size=size,
        pages=(total + size - 1) // size
    )


# Lottery Endpoints
@router.post("/lotteries", response_model=LotteryResponse)
async def create_lottery(
    lottery_data: LotteryCreate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """Create a new lottery."""
    service = LotteryService(db)

    try:
        lottery = service.create_lottery(
            lottery_data=lottery_data,
            vendor_id=current_user.id,
            tenant_id=current_tenant.id
        )

        # Load related data for response
        lottery_with_relations = db.query(Lottery).options(
            joinedload(Lottery.product),
            joinedload(Lottery.vendor)
        ).filter(Lottery.id == lottery.id).first()

        return _build_lottery_response(lottery_with_relations)

    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/lotteries", response_model=LotteryListResponse)
async def list_lotteries(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    status: Optional[LotteryStatus] = None,
    vendor_id: Optional[UUID] = None,
    search: Optional[str] = None,
    db: Session = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """List lotteries with filtering and pagination."""
    query = db.query(Lottery).filter(
        Lottery.tenant_id == current_tenant.id
    ).options(
        joinedload(Lottery.product),
        joinedload(Lottery.vendor)
    )

    # Apply filters
    if status:
        query = query.filter(Lottery.status == status)

    if vendor_id:
        query = query.filter(Lottery.vendor_id == vendor_id)

    if search:
        query = query.filter(
            or_(
                Lottery.title.ilike(f"%{search}%"),
                Lottery.description.ilike(f"%{search}%")
            )
        )

    # Order by creation date (newest first)
    query = query.order_by(desc(Lottery.created_at))

    # Get total count
    total = query.count()

    # Apply pagination
    lotteries = query.offset((page - 1) * size).limit(size).all()

    # Build response
    items = [_build_lottery_response(lottery) for lottery in lotteries]

    return LotteryListResponse(
        items=items,
        total=total,
        page=page,
        size=size,
        pages=(total + size - 1) // size
    )


@router.get("/lotteries/{lottery_id}", response_model=LotteryResponse)
async def get_lottery(
    lottery_id: UUID,
    db: Session = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """Get lottery details."""
    lottery = db.query(Lottery).options(
        joinedload(Lottery.product),
        joinedload(Lottery.vendor),
        joinedload(Lottery.winner)
    ).filter(
        Lottery.id == lottery_id,
        Lottery.tenant_id == current_tenant.id
    ).first()

    if not lottery:
        raise HTTPException(status_code=404, detail="Lottery not found")

    return _build_lottery_response(lottery)


@router.post("/lotteries/{lottery_id}/tickets", response_model=List[TicketResponse])
async def buy_lottery_tickets(
    lottery_id: UUID,
    ticket_data: TicketCreate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Buy lottery tickets."""
    service = LotteryService(db)

    try:
        tickets = service.buy_tickets(
            ticket_data=ticket_data,
            participant_id=current_user.id
        )

        # Get updated lottery for notification
        lottery = db.query(Lottery).filter(Lottery.id == lottery_id).first()

        # Notify WebSocket clients
        background_tasks.add_task(
            auction_lottery_ws_manager.emit_lottery_ticket_purchased,
            lottery_id=lottery_id,
            participant_id=current_user.id,
            tickets_purchased=len(tickets),
            total_tickets_sold=lottery.tickets_sold
        )

        # Load related data for response
        tickets_with_relations = db.query(LotteryTicket).options(
            joinedload(LotteryTicket.participant),
            joinedload(LotteryTicket.lottery)
        ).filter(
            LotteryTicket.id.in_([ticket.id for ticket in tickets])
        ).all()

        return [_build_ticket_response(ticket) for ticket in tickets_with_relations]

    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/lotteries/{lottery_id}/draw", response_model=LotteryResponse)
async def draw_lottery_winner(
    lottery_id: UUID,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Draw a winner for the lottery."""
    service = LotteryService(db)

    try:
        lottery, winning_ticket = service.draw_winner(lottery_id)

        # Count total participants
        total_participants = db.query(func.count(func.distinct(LotteryTicket.participant_id))).filter(
            LotteryTicket.lottery_id == lottery_id
        ).scalar()

        # Notify WebSocket clients
        background_tasks.add_task(
            auction_lottery_ws_manager.emit_lottery_winner_drawn,
            lottery_id=lottery_id,
            winner_id=lottery.winner_id,
            winning_ticket_number=winning_ticket.ticket_number,
            total_participants=total_participants
        )

        # Load related data for response
        lottery_with_relations = db.query(Lottery).options(
            joinedload(Lottery.product),
            joinedload(Lottery.vendor),
            joinedload(Lottery.winner)
        ).filter(Lottery.id == lottery.id).first()

        return _build_lottery_response(lottery_with_relations)

    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


# Helper functions
def _build_auction_response(auction: Auction) -> AuctionResponse:
    """Build auction response with related data."""
    return AuctionResponse(
        id=auction.id,
        tenant_id=auction.tenant_id,
        product_id=auction.product_id,
        vendor_id=auction.vendor_id,
        title=auction.title,
        description=auction.description,
        starting_bid=auction.starting_bid,
        buy_now_price=auction.buy_now_price,
        bid_increment=auction.bid_increment,
        start_time=auction.start_time,
        end_time=auction.end_time,
        status=auction.status,
        current_bid=auction.current_bid,
        winning_bid=auction.winning_bid,
        winner_id=auction.winner_id,
        auto_extend=auction.auto_extend,
        extend_minutes=auction.extend_minutes,
        max_extensions=auction.max_extensions,
        extensions_used=auction.extensions_used,
        total_bids=auction.total_bids,
        unique_bidders=auction.unique_bidders,
        views_count=auction.views_count,
        time_remaining=auction.time_remaining,
        created_at=auction.created_at,
        updated_at=auction.updated_at,
        # Related data
        product_name=auction.product.name if auction.product else None,
        product_image=auction.product.image_url if auction.product else None,
        vendor_name=auction.vendor.name if auction.vendor else None,
        winner_name=auction.winner.name if auction.winner else None
    )


def _build_bid_response(bid: AuctionBid) -> BidResponse:
    """Build bid response with related data."""
    return BidResponse(
        id=bid.id,
        auction_id=bid.auction_id,
        bidder_id=bid.bidder_id,
        bid_amount=bid.bid_amount,
        is_winning=bid.is_winning,
        is_auto_bid=bid.is_auto_bid,
        created_at=bid.created_at,
        # Related data
        bidder_name=bid.bidder.name if bid.bidder else None
    )


def _build_lottery_response(lottery: Lottery) -> LotteryResponse:
    """Build lottery response with related data."""
    return LotteryResponse(
        id=lottery.id,
        tenant_id=lottery.tenant_id,
        product_id=lottery.product_id,
        vendor_id=lottery.vendor_id,
        title=lottery.title,
        description=lottery.description,
        is_free=lottery.is_free,
        ticket_price=lottery.ticket_price,
        max_tickets_per_user=lottery.max_tickets_per_user,
        total_tickets_available=lottery.total_tickets_available,
        start_time=lottery.start_time,
        end_time=lottery.end_time,
        draw_time=lottery.draw_time,
        status=lottery.status,
        tickets_sold=lottery.tickets_sold,
        winner_id=lottery.winner_id,
        winning_ticket_id=lottery.winning_ticket_id,
        drawn_at=lottery.drawn_at,
        created_at=lottery.created_at,
        updated_at=lottery.updated_at,
        # Related data
        product_name=lottery.product.name if lottery.product else None,
        product_image=lottery.product.image_url if lottery.product else None,
        vendor_name=lottery.vendor.name if lottery.vendor else None,
        winner_name=lottery.winner.name if lottery.winner else None
    )


# Statistics Endpoints
@router.get("/auctions/stats", response_model=AuctionStats)
async def get_auction_stats(
    db: Session = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """Get auction statistics for the tenant."""
    # Total auctions
    total_auctions = db.query(func.count(Auction.id)).filter(
        Auction.tenant_id == current_tenant.id
    ).scalar()

    # Active auctions
    active_auctions = db.query(func.count(Auction.id)).filter(
        Auction.tenant_id == current_tenant.id,
        Auction.status == AuctionStatus.ACTIVE
    ).scalar()

    # Total bids
    total_bids = db.query(func.count(AuctionBid.id)).join(Auction).filter(
        Auction.tenant_id == current_tenant.id
    ).scalar()

    # Average bid amount
    avg_bid = db.query(func.avg(AuctionBid.bid_amount)).join(Auction).filter(
        Auction.tenant_id == current_tenant.id
    ).scalar()

    # Highest winning bid
    highest_bid = db.query(func.max(Auction.winning_bid)).filter(
        Auction.tenant_id == current_tenant.id,
        Auction.status == AuctionStatus.ENDED
    ).scalar()

    return AuctionStats(
        total_auctions=total_auctions or 0,
        active_auctions=active_auctions or 0,
        total_bids=total_bids or 0,
        average_bid_amount=avg_bid,
        highest_winning_bid=highest_bid
    )


@router.get("/lotteries/stats", response_model=LotteryStats)
async def get_lottery_stats(
    db: Session = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """Get lottery statistics for the tenant."""
    # Total lotteries
    total_lotteries = db.query(func.count(Lottery.id)).filter(
        Lottery.tenant_id == current_tenant.id
    ).scalar()

    # Active lotteries
    active_lotteries = db.query(func.count(Lottery.id)).filter(
        Lottery.tenant_id == current_tenant.id,
        Lottery.status == LotteryStatus.ACTIVE
    ).scalar()

    # Total tickets sold
    total_tickets = db.query(func.count(LotteryTicket.id)).join(Lottery).filter(
        Lottery.tenant_id == current_tenant.id
    ).scalar()

    # Total revenue (from paid lotteries)
    total_revenue = db.query(func.sum(LotteryTicket.payment_amount)).join(Lottery).filter(
        Lottery.tenant_id == current_tenant.id,
        LotteryTicket.payment_amount.isnot(None)
    ).scalar()

    # Average participation (tickets per lottery)
    avg_participation = None
    if total_lotteries and total_lotteries > 0:
        avg_participation = total_tickets / total_lotteries

    return LotteryStats(
        total_lotteries=total_lotteries or 0,
        active_lotteries=active_lotteries or 0,
        total_tickets_sold=total_tickets or 0,
        total_revenue=total_revenue,
        average_participation=avg_participation
    )


@router.get("/lotteries/{lottery_id}/tickets", response_model=TicketListResponse)
async def get_lottery_tickets(
    lottery_id: UUID,
    page: int = Query(1, ge=1),
    size: int = Query(50, ge=1, le=100),
    db: Session = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """Get tickets for a lottery."""
    # Verify lottery exists and belongs to tenant
    lottery = db.query(Lottery).filter(
        Lottery.id == lottery_id,
        Lottery.tenant_id == current_tenant.id
    ).first()

    if not lottery:
        raise HTTPException(status_code=404, detail="Lottery not found")

    # Get tickets with pagination
    query = db.query(LotteryTicket).options(
        joinedload(LotteryTicket.participant)
    ).filter(
        LotteryTicket.lottery_id == lottery_id
    ).order_by(desc(LotteryTicket.created_at))

    total = query.count()
    tickets = query.offset((page - 1) * size).limit(size).all()

    items = [_build_ticket_response(ticket) for ticket in tickets]

    return TicketListResponse(
        items=items,
        total=total,
        page=page,
        size=size,
        pages=(total + size - 1) // size
    )


# Background task endpoints
@router.post("/admin/finalize-expired-auctions")
async def finalize_expired_auctions(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Finalize all expired auctions (admin only)."""
    # TODO: Add admin role check
    service = AuctionService(db)

    try:
        expired_auctions = service.finalize_expired_auctions()

        # Notify WebSocket clients for each finalized auction
        for auction in expired_auctions:
            background_tasks.add_task(
                auction_lottery_ws_manager.emit_auction_ended,
                auction_id=auction.id,
                winner_id=auction.winner_id,
                winning_bid=float(auction.winning_bid) if auction.winning_bid else None,
                reason="time_expired"
            )

        return {"message": f"Finalized {len(expired_auctions)} expired auctions"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


def _build_ticket_response(ticket: LotteryTicket) -> TicketResponse:
    """Build ticket response with related data."""
    return TicketResponse(
        id=ticket.id,
        lottery_id=ticket.lottery_id,
        participant_id=ticket.participant_id,
        ticket_number=ticket.ticket_number,
        is_winning=ticket.is_winning,
        is_paid=ticket.is_paid,
        payment_amount=ticket.payment_amount,
        payment_reference=ticket.payment_reference,
        created_at=ticket.created_at,
        # Related data
        participant_name=ticket.participant.name if ticket.participant else None,
        lottery_title=ticket.lottery.title if ticket.lottery else None
    )
