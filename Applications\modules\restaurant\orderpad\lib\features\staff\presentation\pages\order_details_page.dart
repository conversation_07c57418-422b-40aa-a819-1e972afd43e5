import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/models/order_model.dart';
import '../../../../core/providers/orders_provider.dart';
import '../../../../shared/widgets/custom_button.dart';


class OrderDetailsPage extends ConsumerStatefulWidget {
  final String orderId;
  
  const OrderDetailsPage({
    super.key,
    required this.orderId,
  });

  @override
  ConsumerState<OrderDetailsPage> createState() => _OrderDetailsPageState();
}

class _OrderDetailsPageState extends ConsumerState<OrderDetailsPage>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final ordersState = ref.watch(ordersProvider);
    final theme = Theme.of(context);
    
    final order = ordersState.orders.firstWhere(
      (o) => o.id == widget.orderId,
      orElse: () => throw Exception('Order not found'),
    );

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: Text('Pedido #${order.orderNumber}'),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: () => _printOrder(order),
          ),
          PopupMenuButton<String>(
            onSelected: (value) => _onMenuSelected(value, order),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'duplicate',
                child: Row(
                  children: [
                    Icon(Icons.copy),
                    SizedBox(width: 8),
                    Text('Duplicar Pedido'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'cancel',
                child: Row(
                  children: [
                    Icon(Icons.cancel, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Cancelar Pedido'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: ordersState.isLoading
          ? const Center(child: CircularProgressIndicator())
          : FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildOrderHeader(order, theme),
                      const SizedBox(height: 24),
                      _buildCustomerInfo(order, theme),
                      const SizedBox(height: 24),
                      _buildOrderItems(order, theme),
                      const SizedBox(height: 24),
                      _buildOrderSummary(order, theme),
                      const SizedBox(height: 24),
                      _buildPaymentInfo(order, theme),
                      const SizedBox(height: 24),
                      _buildOrderTimeline(order, theme),
                      const SizedBox(height: 24),
                      _buildOrderNotes(order, theme),
                      const SizedBox(height: 32),
                      _buildActionButtons(order, theme),
                      const SizedBox(height: 16),
                    ],
                  ),
                ),
              ),
            ),
    );
  }

  Widget _buildOrderHeader(OrderModel order, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Pedido #${order.orderNumber}',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _formatDateTime(order.createdAt),
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
              ),
              _buildStatusChip(order.status, theme),
            ],
          ),
          
          const SizedBox(height: 16),
          
          Row(
            children: [
              _buildInfoChip(
                Icons.restaurant,
                order.typeDisplayName,
                _getOrderTypeColor(order.type),
                theme,
              ),
              const SizedBox(width: 12),
              _buildInfoChip(
                Icons.table_restaurant,
                'Mesa ${order.tableNumber}',
                theme.colorScheme.primary,
                theme,
              ),
              // Waiter name functionality commented out as waiterName field doesn't exist in OrderModel
              // if (order.waiterName != null) ...[
              //   const SizedBox(width: 12),
              //   _buildInfoChip(
              //     Icons.person,
              //     order.waiterName!,
              //     theme.colorScheme.secondary,
              //     theme,
              //   ),
              // ],
            ],
          ),
          
          // Tempo estimado removido - campo não existe no modelo
        ],
      ),
    );
  }

  Widget _buildCustomerInfo(OrderModel order, ThemeData theme) {
    if (order.customerName == null) {
      return const SizedBox.shrink();
    }
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Informações do Cliente',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          if (order.customerName != null) ...[
            Row(
              children: [
                Icon(
                  Icons.person,
                  size: 20,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
                const SizedBox(width: 12),
                Text(
                  order.customerName!,
                  style: theme.textTheme.bodyLarge,
                ),
              ],
            ),
            const SizedBox(height: 12),
          ],
          
          // Phone functionality commented out as customerPhone field doesn't exist in OrderModel
          // if (order.customerPhone != null) ...[
          //   Row(
          //     children: [
          //       Icon(
          //         Icons.phone,
          //         size: 20,
          //         color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          //       ),
          //       const SizedBox(width: 12),
          //       Text(
          //         order.customerPhone!,
          //         style: theme.textTheme.bodyLarge,
          //       ),
          //       const Spacer(),
          //       IconButton(
          //         icon: const Icon(Icons.call),
          //         onPressed: () => _callCustomer(order.customerPhone!),
          //         color: theme.colorScheme.primary,
          //       ),
          //     ],
          //   ),
          // ],
        ],
      ),
    );
  }

  Widget _buildOrderItems(OrderModel order, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Itens do Pedido',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Text(
                '${order.itemCount} ${order.itemCount == 1 ? 'item' : 'itens'}',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          ...order.items.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            
            return Column(
              children: [
                if (index > 0) const Divider(height: 24),
                _buildOrderItemCard(item, theme),
              ],
            );
          }),
        ],
      ),
    );
  }

  Widget _buildOrderItemCard(OrderItem item, ThemeData theme) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Quantity badge
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              '${item.quantity}',
              style: theme.textTheme.labelLarge?.copyWith(
                color: theme.colorScheme.onPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        
        const SizedBox(width: 16),
        
        // Item details
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                item.menuItem.name,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              
              if (item.menuItem.description.isNotEmpty) ...[
                const SizedBox(height: 4),
                Text(
                  item.menuItem.description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
              
              // Modifiers
              if (item.selectedModifiers?.isNotEmpty ?? false) ...[
                const SizedBox(height: 8),
                ...item.selectedModifiers!.map((modifier) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    children: [
                      Container(
                        width: 4,
                        height: 4,
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary,
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        modifier,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                )),
              ],
              
              // Special instructions
              if (item.notes?.isNotEmpty ?? false) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.secondaryContainer.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.note,
                        size: 16,
                        color: theme.colorScheme.secondary,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          item.notes!,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.secondary,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
        
        // Price
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              'R\$ ${item.totalPrice.toStringAsFixed(2)}',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            if (item.quantity > 1) ...[
              const SizedBox(height: 2),
              Text(
                'R\$ ${(item.unitPrice ?? item.price).toStringAsFixed(2)} cada',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }

  Widget _buildOrderSummary(OrderModel order, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Resumo do Pedido',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          _buildSummaryRow(
            'Subtotal',
            'R\$ ${order.totalAmount.toStringAsFixed(2)}',
            theme,
          ),
          
          if (order.discount > 0) ...[
            const SizedBox(height: 8),
            _buildSummaryRow(
              'Desconto',
              '-R\$ ${order.discount.toStringAsFixed(2)}',
              theme,
              valueColor: Colors.green,
            ),
          ],
          
          // Taxa de entrega e impostos removidos - campos não existem no modelo
          // if (order.deliveryFee > 0) ...[
          //   const SizedBox(height: 8),
          //   _buildSummaryRow(
          //     'Taxa de entrega',
          //     'R\$ ${order.deliveryFee.toStringAsFixed(2)}',
          //     theme,
          //   ),
          // ],
          // 
          // const SizedBox(height: 8),
          // _buildSummaryRow(
          //   'Taxas e impostos',
          //   'R\$ ${order.taxAmount.toStringAsFixed(2)}',
          //   theme,
          // ),
          
          const Divider(height: 24),
          
          _buildSummaryRow(
            'Total',
            'R\$ ${order.totalAmount.toStringAsFixed(2)}',
            theme,
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(
    String label,
    String value,
    ThemeData theme, {
    Color? valueColor,
    bool isTotal = false,
  }) {
    return Row(
      children: [
        Text(
          label,
          style: isTotal
              ? theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                )
              : theme.textTheme.bodyLarge,
        ),
        const Spacer(),
        Text(
          value,
          style: isTotal
              ? theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                )
              : theme.textTheme.bodyLarge?.copyWith(
                  color: valueColor ?? theme.colorScheme.onSurface,
                  fontWeight: FontWeight.w600,
                ),
        ),
      ],
    );
  }

  Widget _buildPaymentInfo(OrderModel order, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Informações de Pagamento',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          Row(
            children: [
              Icon(
                _getPaymentIcon(order.paymentMethod),
                size: 24,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 12),
              Text(
                _getPaymentMethodName(order.paymentMethod),
                style: theme.textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              // Status de pagamento removido - campo isPaid não existe no modelo
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'Pagamento: ${_getPaymentMethodName(order.paymentMethod)}',
                    style: theme.textTheme.labelMedium?.copyWith(
                      color: Colors.blue,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
          
          if (order.paymentMethod == 'paid') ...[
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  size: 16,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
                const SizedBox(width: 8),
                Text(
                  'Pago em ${_formatDateTime(order.updatedAt ?? order.createdAt)}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildOrderTimeline(OrderModel order, ThemeData theme) {
    final events = _getOrderEvents(order);
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Histórico do Pedido',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          ...events.asMap().entries.map((entry) {
            final index = entry.key;
            final event = entry.value;
            final isLast = index == events.length - 1;
            
            return _buildTimelineEvent(
              event['title']!,
              event['time']!,
              event['icon'] as IconData,
              event['color'] as Color,
              theme,
              isLast: isLast,
            );
          }),
        ],
      ),
    );
  }

  Widget _buildTimelineEvent(
    String title,
    String time,
    IconData icon,
    Color color,
    ThemeData theme, {
    bool isLast = false,
  }) {
    return Row(
      children: [
        Column(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 16,
                color: Colors.white,
              ),
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 32,
                color: theme.colorScheme.outline.withValues(alpha: 0.3),
              ),
          ],
        ),
        
        const SizedBox(width: 16),
        
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                time,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildOrderNotes(OrderModel order, ThemeData theme) {
    if (order.notes?.isEmpty ?? true) {
      return const SizedBox.shrink();
    }
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.note,
                size: 20,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                'Observações',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.secondaryContainer.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              order.notes!,
              style: theme.textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(OrderModel order, ThemeData theme) {
    return Column(
      children: [
        if (order.status == 'pending') ...[
          Row(
            children: [
              Expanded(
                child: SecondaryButton(
                  onPressed: () => _updateOrderStatus(order, 'cancelled'),
                  text: 'Cancelar',
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: PrimaryButton(
                  onPressed: () => _updateOrderStatus(order, 'preparing'),
                  text: 'Iniciar Preparo',
                ),
              ),
            ],
          ),
        ] else if (order.status == 'preparing') ...[
          SizedBox(
            width: double.infinity,
            child: PrimaryButton(
              onPressed: () => _updateOrderStatus(order, 'ready'),
              text: 'Marcar como Pronto',
            ),
          ),
        ] else if (order.status == 'ready') ...[
          SizedBox(
            width: double.infinity,
            child: SuccessButton(
              onPressed: () => _updateOrderStatus(order, 'completed'),
              text: 'Entregar Pedido',
            ),
          ),
        ],
        
        const SizedBox(height: 12),
        
        // Botão de marcar como pago removido - campo isPaid não existe no modelo
        // if (order.status != OrderStatus.cancelled) ...[
        //   SizedBox(
        //     width: double.infinity,
        //     child: OutlinedButton(
        //       onPressed: () => _markAsPaid(order),
        //       child: const Text('Marcar como Pago'),
        //     ),
        //   ),
        // ],
      ],
    );
  }

  Widget _buildStatusChip(String status, ThemeData theme) {
    final color = _getStatusColor(status);
    
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 12,
        vertical: 6,
      ),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        _getStatusDisplayName(status),
        style: theme.textTheme.labelMedium?.copyWith(
          color: color,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildInfoChip(
    IconData icon,
    String label,
    Color color,
    ThemeData theme,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 12,
        vertical: 6,
      ),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: color,
          ),
          const SizedBox(width: 6),
          Text(
            label,
            style: theme.textTheme.labelMedium?.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day.toString().padLeft(2, '0')}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.year} às ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'preparing':
        return Colors.blue;
      case 'ready':
        return Colors.green;
      case 'completed':
        return Colors.teal;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusDisplayName(String status) {
    switch (status) {
      case 'pending':
        return 'Pendente';
      case 'preparing':
        return 'Preparando';
      case 'ready':
        return 'Pronto';
      case 'completed':
        return 'Entregue';
      case 'cancelled':
        return 'Cancelado';
      default:
        return 'Desconhecido';
    }
  }

  Color _getOrderTypeColor(String type) {
    switch (type) {
      case 'dineIn':
        return Colors.blue;
      case 'takeaway':
        return Colors.orange;
      case 'delivery':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  IconData _getPaymentIcon(String paymentMethod) {
    switch (paymentMethod) {
      case 'cash':
        return Icons.money;
      case 'credit_card':
        return Icons.credit_card;
      case 'debit_card':
        return Icons.payment;
      case 'pix':
        return Icons.qr_code;
      default:
        return Icons.payment;
    }
  }

  String _getPaymentMethodName(String paymentMethod) {
    switch (paymentMethod) {
      case 'cash':
        return 'Dinheiro';
      case 'credit_card':
        return 'Cartão de Crédito';
      case 'debit_card':
        return 'Cartão de Débito';
      case 'pix':
        return 'PIX';
      default:
        return 'Não informado';
    }
  }

  // Função _getRemainingTime removida - campo estimatedPreparationTime não existe

  List<Map<String, dynamic>> _getOrderEvents(OrderModel order) {
    final events = <Map<String, dynamic>>[
      {
        'title': 'Pedido criado',
        'time': _formatDateTime(order.createdAt),
        'icon': Icons.add_circle,
        'color': Colors.blue,
      },
    ];
    
    if (order.status != 'pending') {
      events.add({
        'title': 'Preparo iniciado',
        'time': _formatDateTime(order.updatedAt ?? order.createdAt),
        'icon': Icons.restaurant,
        'color': Colors.orange,
      });
    }
    
    if (order.status == 'ready' || order.status == 'completed') {
      events.add({
        'title': 'Pedido pronto',
        'time': _formatDateTime(order.updatedAt ?? order.createdAt),
        'icon': Icons.check_circle,
        'color': Colors.green,
      });
    }
    
    if (order.status == 'completed') {
      events.add({
        'title': 'Pedido entregue',
        'time': _formatDateTime(order.updatedAt ?? order.createdAt),
        'icon': Icons.done_all,
        'color': Colors.teal,
      });
    }
    
    if (order.status == 'cancelled') {
      events.add({
        'title': 'Pedido cancelado',
        'time': _formatDateTime(order.updatedAt ?? order.createdAt),
        'icon': Icons.cancel,
        'color': Colors.red,
      });
    }
    
    if (order.paymentMethod == 'paid') {
      events.add({
        'title': 'Pagamento recebido',
        'time': _formatDateTime(order.updatedAt ?? order.createdAt),
        'icon': Icons.payment,
        'color': Colors.green,
      });
    }
    
    return events;
  }

  // Action methods
  void _updateOrderStatus(OrderModel order, String newStatus) {
    ref.read(ordersProvider.notifier).updateOrderStatus(order.id, newStatus);
  }

  // Função _markAsPaid removida - campo isPaid não existe no modelo

  void _printOrder(OrderModel order) {
    // TODO: Implement print functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Funcionalidade de impressão será implementada'),
      ),
    );
  }

  void _onMenuSelected(String value, OrderModel order) {
    switch (value) {
      case 'duplicate':
        _duplicateOrder(order);
        break;
      case 'cancel':
        _showCancelOrderDialog(order);
        break;
    }
  }

  void _duplicateOrder(OrderModel order) {
    // TODO: Implement order duplication
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Funcionalidade de duplicação será implementada'),
      ),
    );
  }

  void _showCancelOrderDialog(OrderModel order) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancelar Pedido'),
        content: Text(
          'Tem certeza que deseja cancelar o pedido #${order.orderNumber}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Não'),
          ),
          TextButton(
            onPressed: () {
              _updateOrderStatus(order, OrderStatus.cancelled.toString().split('.').last);
              Navigator.pop(context);
              context.pop(); // Return to previous page
            },
            child: Text(
              'Sim, Cancelar',
              style: TextStyle(
                color: Theme.of(context).colorScheme.error,
              ),
            ),
          ),
        ],
      ),
    );
  }
}