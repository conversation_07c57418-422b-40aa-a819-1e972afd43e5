"""
SEO Service

Core business logic for multilingual SEO operations.
"""

import uuid
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, delete
from sqlalchemy.orm import selectinload

from app.modules.core.seo.models.seo_meta import SEOMeta
from app.modules.core.seo.models.url_slug import URLSlug
from app.modules.core.seo.models.sitemap_entry import SitemapEntry
from app.modules.core.seo.schemas.seo_schemas import (
    SEOMetaCreate, SEOMetaUpdate, SEOAnalysisResponse
)
from app.modules.core.i18n.models.language import Language


class SEOService:
    """Core SEO service for multilingual SEO operations."""

    async def create_seo_meta(
        self,
        db: AsyncSession,
        seo_data: SEOMetaCreate,
        user_id: Optional[uuid.UUID] = None
    ) -> SEOMeta:
        """Create SEO metadata for content."""
        seo_meta = SEOMeta(
            **seo_data.dict(),
            created_by_id=user_id,
            updated_by_id=user_id
        )
        
        db.add(seo_meta)
        await db.commit()
        await db.refresh(seo_meta)
        return seo_meta

    async def get_seo_meta(
        self,
        db: AsyncSession,
        content_type: str,
        content_id: uuid.UUID,
        language_code: str
    ) -> Optional[SEOMeta]:
        """Get SEO metadata for specific content and language."""
        result = await db.execute(
            select(SEOMeta).filter(
                and_(
                    SEOMeta.content_type == content_type,
                    SEOMeta.content_id == content_id,
                    SEOMeta.language_code == language_code,
                    SEOMeta.is_active == True
                )
            )
        )
        return result.scalar_one_or_none()

    async def get_all_seo_meta_for_content(
        self,
        db: AsyncSession,
        content_type: str,
        content_id: uuid.UUID
    ) -> List[SEOMeta]:
        """Get all SEO metadata for content across all languages."""
        result = await db.execute(
            select(SEOMeta).filter(
                and_(
                    SEOMeta.content_type == content_type,
                    SEOMeta.content_id == content_id,
                    SEOMeta.is_active == True
                )
            ).order_by(SEOMeta.language_code)
        )
        return result.scalars().all()

    async def update_seo_meta(
        self,
        db: AsyncSession,
        seo_meta_id: uuid.UUID,
        seo_data: SEOMetaUpdate,
        user_id: Optional[uuid.UUID] = None
    ) -> Optional[SEOMeta]:
        """Update SEO metadata."""
        result = await db.execute(
            select(SEOMeta).filter(SEOMeta.id == seo_meta_id)
        )
        seo_meta = result.scalar_one_or_none()
        
        if not seo_meta:
            return None

        update_data = seo_data.dict(exclude_unset=True)
        if update_data:
            for field, value in update_data.items():
                setattr(seo_meta, field, value)
            
            seo_meta.updated_by_id = user_id
            await db.commit()
            await db.refresh(seo_meta)
        
        return seo_meta

    async def delete_seo_meta(
        self,
        db: AsyncSession,
        content_type: str,
        content_id: uuid.UUID,
        language_code: Optional[str] = None
    ) -> bool:
        """Delete SEO metadata for content."""
        query = select(SEOMeta).filter(
            and_(
                SEOMeta.content_type == content_type,
                SEOMeta.content_id == content_id
            )
        )
        
        if language_code:
            query = query.filter(SEOMeta.language_code == language_code)
        
        result = await db.execute(query)
        seo_metas = result.scalars().all()
        
        if seo_metas:
            for seo_meta in seo_metas:
                await db.delete(seo_meta)
            await db.commit()
            return True
        
        return False

    async def analyze_seo(
        self,
        db: AsyncSession,
        content_type: str,
        content_id: uuid.UUID,
        language_code: str,
        content_text: str,
        focus_keyword: Optional[str] = None
    ) -> SEOAnalysisResponse:
        """Analyze SEO for content."""
        seo_meta = await self.get_seo_meta(db, content_type, content_id, language_code)
        
        # Basic analysis
        title_length = len(seo_meta.meta_title) if seo_meta and seo_meta.meta_title else 0
        description_length = len(seo_meta.meta_description) if seo_meta and seo_meta.meta_description else 0
        content_length = len(content_text)
        
        # Keyword analysis
        keyword_density = 0.0
        if focus_keyword and content_text:
            keyword_count = content_text.lower().count(focus_keyword.lower())
            word_count = len(content_text.split())
            keyword_density = (keyword_count / word_count * 100) if word_count > 0 else 0.0

        # Generate recommendations
        recommendations = []
        warnings = []
        errors = []

        # Title analysis
        if title_length == 0:
            errors.append("Missing meta title")
        elif title_length < 30:
            warnings.append("Meta title is too short (recommended: 30-60 characters)")
        elif title_length > 60:
            warnings.append("Meta title is too long (recommended: 30-60 characters)")
        else:
            recommendations.append("Meta title length is optimal")

        # Description analysis
        if description_length == 0:
            errors.append("Missing meta description")
        elif description_length < 120:
            warnings.append("Meta description is too short (recommended: 120-160 characters)")
        elif description_length > 160:
            warnings.append("Meta description is too long (recommended: 120-160 characters)")
        else:
            recommendations.append("Meta description length is optimal")

        # Content analysis
        if content_length < 300:
            warnings.append("Content is too short for good SEO (recommended: 300+ words)")
        elif content_length > 2000:
            recommendations.append("Content length is excellent for SEO")

        # Keyword density analysis
        if focus_keyword:
            if keyword_density == 0:
                warnings.append(f"Focus keyword '{focus_keyword}' not found in content")
            elif keyword_density < 0.5:
                warnings.append(f"Focus keyword density is too low ({keyword_density:.1f}%)")
            elif keyword_density > 3.0:
                warnings.append(f"Focus keyword density is too high ({keyword_density:.1f}%)")
            else:
                recommendations.append(f"Focus keyword density is optimal ({keyword_density:.1f}%)")

        # Calculate scores
        seo_score = self._calculate_seo_score(
            title_length, description_length, content_length, keyword_density
        )
        readability_score = self._calculate_readability_score(content_text)

        return SEOAnalysisResponse(
            content_type=content_type,
            content_id=content_id,
            language_code=language_code,
            seo_score=seo_score,
            readability_score=readability_score,
            recommendations=recommendations,
            warnings=warnings,
            errors=errors,
            title_length=title_length,
            description_length=description_length,
            content_length=content_length,
            keyword_density=keyword_density
        )

    async def get_active_languages(self, db: AsyncSession) -> List[Language]:
        """Get all active languages."""
        result = await db.execute(
            select(Language).filter(Language.is_active == True).order_by(Language.code)
        )
        return result.scalars().all()

    async def create_multilingual_seo(
        self,
        db: AsyncSession,
        content_type: str,
        content_id: uuid.UUID,
        seo_data_by_language: Dict[str, SEOMetaCreate],
        user_id: Optional[uuid.UUID] = None
    ) -> List[SEOMeta]:
        """Create SEO metadata for multiple languages."""
        seo_metas = []
        
        for language_code, seo_data in seo_data_by_language.items():
            seo_data.content_type = content_type
            seo_data.content_id = content_id
            seo_data.language_code = language_code
            
            seo_meta = await self.create_seo_meta(db, seo_data, user_id)
            seo_metas.append(seo_meta)
        
        return seo_metas

    def _calculate_seo_score(
        self, 
        title_length: int, 
        description_length: int, 
        content_length: int, 
        keyword_density: float
    ) -> int:
        """Calculate SEO score based on various factors."""
        score = 0
        
        # Title score (25 points)
        if 30 <= title_length <= 60:
            score += 25
        elif title_length > 0:
            score += 15
        
        # Description score (25 points)
        if 120 <= description_length <= 160:
            score += 25
        elif description_length > 0:
            score += 15
        
        # Content length score (25 points)
        if content_length >= 2000:
            score += 25
        elif content_length >= 1000:
            score += 20
        elif content_length >= 500:
            score += 15
        elif content_length >= 300:
            score += 10
        
        # Keyword density score (25 points)
        if 0.5 <= keyword_density <= 3.0:
            score += 25
        elif keyword_density > 0:
            score += 15
        
        return min(score, 100)

    def _calculate_readability_score(self, content: str) -> int:
        """Calculate readability score (simplified)."""
        if not content:
            return 0
        
        sentences = content.count('.') + content.count('!') + content.count('?')
        words = len(content.split())
        
        if sentences == 0 or words == 0:
            return 0
        
        avg_sentence_length = words / sentences
        
        # Simple readability calculation
        if avg_sentence_length <= 15:
            return 90  # Excellent
        elif avg_sentence_length <= 20:
            return 75  # Good
        elif avg_sentence_length <= 25:
            return 60  # Fair
        else:
            return 40  # Difficult
