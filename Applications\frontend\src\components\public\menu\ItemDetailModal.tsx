'use client';

import React, { Fragment, useState, useEffect, useCallback } from 'react';
import NextImage from 'next/image';
import { Dialog, Transition } from '@headlessui/react';
import {
  XMarkIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  StarIcon as StarIconSolid,
  FireIcon,
  SparklesIcon,
  PlusIcon,
  MinusIcon,
  ShoppingCartIcon,
  CheckIcon,
  HeartIcon,
  CameraIcon,
  ChevronDownIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';
import {
  PublicMenuItem,
  PublicVariantGroup,
  PublicModifierGroup,
  PublicOptionalGroup
} from '@/hooks/usePublicMenu';

interface ItemDetailModalProps {
  item: PublicMenuItem | null;
  isOpen: boolean;
  onClose: () => void;
  formatPrice: (price: string) => string;
  onAddToCart?: (item: PublicMenuItem, quantity: number, customizations?: any) => void;
}

export function ItemDetailModal({
  item,
  isOpen,
  onClose,
  formatPrice,
  onAddToCart
}: ItemDetailModalProps) {
  const [quantity, setQuantity] = useState(1);
  const [selectedVariants, setSelectedVariants] = useState<Record<string, string>>({});
  const [selectedModifiers, setSelectedModifiers] = useState<Record<string, boolean>>({});
  const [selectedOptionals, setSelectedOptionals] = useState<Record<string, boolean>>({});
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [specialInstructions, setSpecialInstructions] = useState('');
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    variants: false,
    modifiers: false,
    optionals: false,
    observations: false
  });
  const [isDarkBackground, setIsDarkBackground] = useState(true);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen && item) {
      setQuantity(1);
      setSelectedVariants({});
      setSelectedModifiers({});
      setSelectedOptionals({});
      setSpecialInstructions('');
      setCurrentImageIndex(0);

      // Set default variants if required
      item.variant_groups?.forEach(group => {
        if (group.is_required && group.options && group.options.length > 0) {
          const defaultVariant = group.options.find(v => v.is_default) || group.options[0];
          setSelectedVariants(prev => ({
            ...prev,
            [group.id]: defaultVariant.id
          }));
        }
      });
    }
  }, [isOpen, item]);

  // Funções do carrossel de imagens
  const getItemImages = () => {
    const images = [];
    if (item?.image_url) images.push(item.image_url);
    if (item?.images) images.push(...item.images);
    return Array.from(new Set(images)); // Remove duplicatas
  };

  const itemImages = getItemImages();
  const hasMultipleImages = itemImages.length > 1;

  // Funções de navegação do carrossel
  const nextImage = useCallback(() => {
    setCurrentImageIndex((prev) =>
      prev === itemImages.length - 1 ? 0 : prev + 1
    );
  }, [itemImages.length]);

  const prevImage = useCallback(() => {
    setCurrentImageIndex((prev) =>
      prev === 0 ? itemImages.length - 1 : prev - 1
    );
  }, [itemImages.length]);

  const goToImage = (index: number) => {
    setCurrentImageIndex(index);
  };

  // Função para detectar se a imagem tem fundo claro ou escuro
  const analyzeImageBrightness = (imageUrl: string) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    img.onload = () => {
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        // Redimensionar para análise mais rápida
        const maxSize = 100;
        const scale = Math.min(maxSize / img.width, maxSize / img.height);
        canvas.width = img.width * scale;
        canvas.height = img.height * scale;

        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

        // Analisar toda a imagem redimensionada
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;

        let totalBrightness = 0;
        let sampleCount = 0;

        // Analisar cada pixel (RGBA)
        for (let i = 0; i < data.length; i += 4) {
          const r = data[i];
          const g = data[i + 1];
          const b = data[i + 2];
          const a = data[i + 3];

          // Ignorar pixels transparentes
          if (a > 0) {
            // Calcular luminosidade usando a fórmula padrão
            const brightness = (0.299 * r + 0.587 * g + 0.114 * b);
            totalBrightness += brightness;
            sampleCount++;
          }
        }

        if (sampleCount > 0) {
          const averageBrightness = totalBrightness / sampleCount;
          const isDark = averageBrightness < 140; // Threshold mais alto para melhor detecção
          setIsDarkBackground(isDark);
        } else {
          setIsDarkBackground(true);
        }
      } catch (error) {
        setIsDarkBackground(true);
      }
    };

    img.onerror = () => {
      setIsDarkBackground(true);
    };

    img.src = imageUrl;
  };

  // Analisar luminosidade da imagem atual
  useEffect(() => {
    if (itemImages.length > 0) {
      analyzeImageBrightness(itemImages[currentImageIndex]);
    } else {
      // Para cards sem foto, usar fundo padrão (gradiente laranja = claro)
      setIsDarkBackground(false); // Gradiente laranja é claro
    }
  }, [currentImageIndex, itemImages]);

  // Navegação por teclado para o carrossel
  useEffect(() => {
    if (!isOpen || !hasMultipleImages) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'ArrowLeft') {
        event.preventDefault();
        prevImage();
      } else if (event.key === 'ArrowRight') {
        event.preventDefault();
        nextImage();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, hasMultipleImages, itemImages.length, nextImage, prevImage]);

  if (!item) return null;

  const handleAddToCart = async () => {
    if (!onAddToCart) return;

    setIsAddingToCart(true);
    try {
      await onAddToCart(item, quantity, {
        variants: selectedVariants,
        modifiers: selectedModifiers,
        optionals: selectedOptionals,
        specialInstructions
      });
      handleClose();
    } catch (error) {
      console.error('Error adding to cart:', error);
    } finally {
      setIsAddingToCart(false);
    }
  };

  const calculateTotalPrice = () => {
    let total = parseFloat(item.base_price);

    // Add variant prices
    Object.entries(selectedVariants).forEach(([groupId, variantId]) => {
      const group = item.variant_groups?.find(g => g.id === groupId);
      const variant = group?.options?.find(v => v.id === variantId);
      if (variant?.price_adjustment) {
        total += parseFloat(variant.price_adjustment.toString());
      }
    });

    // Add modifier prices
    Object.entries(selectedModifiers).forEach(([modifierId, selected]) => {
      if (selected) {
        item.modifier_groups?.forEach(group => {
          const modifier = group.options?.find(m => m.id === modifierId);
          if (modifier?.price_adjustment) {
            total += parseFloat(modifier.price_adjustment.toString());
          }
        });
      }
    });

    // Add optional prices
    Object.entries(selectedOptionals).forEach(([optionalId, selected]) => {
      if (selected) {
        item.optional_groups?.forEach(group => {
          const optional = group.options?.find(o => o.id === optionalId);
          if (optional?.price_adjustment) {
            total += parseFloat(optional.price_adjustment.toString());
          }
        });
      }
    });

    return total * quantity;
  };

  const handleVariantSelect = (groupId: string, variantId: string) => {
    setSelectedVariants(prev => ({
      ...prev,
      [groupId]: variantId
    }));
  };

  const handleModifierToggle = (modifierId: string, groupId: string) => {
    const group = item.modifier_groups?.find(g => g.id === groupId);
    if (!group) return;

    setSelectedModifiers(prev => {
      const newModifiers = { ...prev };
      const currentSelected = Object.entries(newModifiers)
        .filter(([id, selected]) => selected &&
          group.options?.some(m => m.id === id))
        .length;

      if (newModifiers[modifierId]) {
        delete newModifiers[modifierId];
      } else {
        if (!group.max_selection || currentSelected < group.max_selection) {
          newModifiers[modifierId] = true;
        }
      }

      return newModifiers;
    });
  };

  const handleOptionalToggle = (optionalId: string, groupId: string) => {
    const group = item.optional_groups?.find(g => g.id === groupId);
    if (!group) return;

    setSelectedOptionals(prev => {
      const newOptionals = { ...prev };
      const currentSelected = Object.entries(newOptionals)
        .filter(([id, selected]) => selected &&
          group.options?.some(o => o.id === id))
        .length;

      if (newOptionals[optionalId]) {
        delete newOptionals[optionalId];
      } else {
        if (!group.max_selection || currentSelected < group.max_selection) {
          newOptionals[optionalId] = true;
        }
      }

      return newOptionals;
    });
  };

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleClose = () => {
    onClose();
  };

  // Função para normalizar strings removendo acentos e convertendo para lowercase
  const normalizeString = (str: string): string => {
    return str
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, ''); // Remove acentos
  };

  // Mapeamento de alérgenos com ícones - incluindo versões com e sem acentos
  const allergenIcons: Record<string, { icon: string; name: string }> = {
    // Glúten (com e sem acento)
    'gluten': { icon: '🌾', name: 'Glúten' },
    'glúten': { icon: '🌾', name: 'Glúten' },

    // Leite/Lactose (ambos os nomes)
    'leite': { icon: '🥛', name: 'Leite' },
    'lactose': { icon: '🥛', name: 'Lactose' },

    // Ovos
    'ovos': { icon: '🥚', name: 'Ovos' },

    // Nozes (corrigindo ícone)
    'nozes': { icon: '🌰', name: 'Nozes' },

    // Amendoim
    'amendoim': { icon: '🥜', name: 'Amendoim' },

    // Soja
    'soja': { icon: '🫘', name: 'Soja' },

    // Peixes
    'peixe': { icon: '🐟', name: 'Peixe' },
    'peixes': { icon: '🐟', name: 'Peixes' },

    // Frutos do mar / Crustáceos
    'frutos_do_mar': { icon: '🦐', name: 'Frutos do Mar' },
    'crustaceos': { icon: '🦐', name: 'Crustáceos' },
    'crustáceos': { icon: '🦐', name: 'Crustáceos' },

    // Gergelim/Sésamo
    'sesamo': { icon: '🌰', name: 'Sésamo' },
    'sésamo': { icon: '🌰', name: 'Sésamo' },
    'gergelim': { icon: '🌰', name: 'Gergelim' },

    // Mostarda
    'mostarda': { icon: '🌭', name: 'Mostarda' },

    // Aipo
    'aipo': { icon: '🥬', name: 'Aipo' },

    // Sulfitos / Dióxido de Enxofre
    'sulfitos': { icon: '⚗️', name: 'Sulfitos' },
    'dioxido_de_enxofre': { icon: '⚗️', name: 'Dióxido de Enxofre' },
    'dióxido de enxofre': { icon: '⚗️', name: 'Dióxido de Enxofre' },

    // Tremoço/Lupino
    'lupino': { icon: '🫛', name: 'Lupino' },
    'tremoco': { icon: '🫛', name: 'Tremoço' },
    'tremoço': { icon: '🫛', name: 'Tremoço' },

    // Moluscos
    'moluscos': { icon: '🦪', name: 'Moluscos' }
  };

  // Função para encontrar informações do alérgeno com normalização
  const findAllergenInfo = (allergenName: string) => {
    // Primeiro tenta busca exata
    let allergenInfo = allergenIcons[allergenName.toLowerCase()];

    // Se não encontrar, tenta busca normalizada
    if (!allergenInfo) {
      const normalizedName = normalizeString(allergenName);
      allergenInfo = allergenIcons[normalizedName];

      // Se ainda não encontrar, tenta buscar por todas as chaves normalizadas
      if (!allergenInfo) {
        const foundKey = Object.keys(allergenIcons).find(key =>
          normalizeString(key) === normalizedName
        );
        if (foundKey) {
          allergenInfo = allergenIcons[foundKey];
        }
      }
    }

    return allergenInfo;
  };

  // Função para processar alérgenos do item
  const getItemAllergens = () => {
    if (!item.allergens) return [];

    // Se for string, dividir por vírgulas
    if (typeof item.allergens === 'string') {
      return item.allergens.split(',').map(a => a.trim()).filter(name => name);
    }

    // Se for array de objetos (formato da API), extrair o nome
    if (Array.isArray(item.allergens)) {
      return (item.allergens as any[]).map((allergen: any) => {
        // Se for objeto com propriedade name, usar o name
        if (typeof allergen === 'object' && allergen.name) {
          return allergen.name.trim();
        }
        // Se for string, usar diretamente
        if (typeof allergen === 'string') {
          return allergen.trim();
        }
        return '';
      }).filter(name => name); // Remove strings vazias
    }

    return [];
  };

  // Classes CSS dinâmicas baseadas na luminosidade
  const getHeaderElementClasses = (baseClasses: string = '') => {
    const adaptiveClasses = isDarkBackground
      ? 'bg-white/20 backdrop-blur-md border-white/30 text-white hover:bg-white/30'
      : 'bg-black/20 backdrop-blur-md border-black/30 text-black hover:bg-black/30';

    return `${baseClasses} ${adaptiveClasses}`;
  };

  const getIconClasses = () => {
    return isDarkBackground ? 'text-white' : 'text-black';
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-[9999]" onClose={handleClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/60 backdrop-blur-sm" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-4xl transform overflow-hidden rounded-3xl bg-white shadow-2xl transition-all">
                {/* Header com Carrossel de Imagens */}
                <div className="relative">
                  <div className="aspect-[16/9] bg-gradient-to-br from-orange-50 to-amber-50 relative overflow-hidden">
                    {itemImages.length > 0 ? (
                      <>
                        {/* Imagem Principal */}
                        <NextImage
                          src={itemImages[currentImageIndex]}
                          alt={`${item.name} - Imagem ${currentImageIndex + 1}`}
                          fill
                          className="object-cover transition-opacity duration-300"
                        />

                        {/* Controles do Carrossel - Apenas se houver múltiplas imagens */}
                        {hasMultipleImages && (
                          <>
                            {/* Botão Anterior */}
                            <button
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                prevImage();
                              }}
                              className={getHeaderElementClasses("absolute left-4 top-1/2 transform -translate-y-1/2 p-3 rounded-full transition-all duration-300 hover:scale-110 shadow-lg z-10")}
                              title="Imagem anterior"
                            >
                              <ChevronLeftIcon className="h-5 w-5" />
                            </button>

                            {/* Botão Próximo */}
                            <button
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                nextImage();
                              }}
                              className={getHeaderElementClasses("absolute right-4 top-1/2 transform -translate-y-1/2 p-3 rounded-full transition-all duration-300 hover:scale-110 shadow-lg z-10")}
                              title="Próxima imagem"
                            >
                              <ChevronRightIcon className="h-5 w-5" />
                            </button>

                            {/* Indicadores de Imagem */}
                            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2 z-10">
                              {itemImages.map((_, index) => (
                                <button
                                  key={index}
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    goToImage(index);
                                  }}
                                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                                    index === currentImageIndex
                                      ? 'bg-white shadow-lg scale-125'
                                      : 'bg-white/50 hover:bg-white/80'
                                  }`}
                                  title={`Ver imagem ${index + 1}`}
                                />
                              ))}
                            </div>

                            {/* Contador removido - já existe nos indicadores */}
                          </>
                        )}
                      </>
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-orange-100 to-amber-100">
                        <div className="text-8xl">🍽️</div>
                      </div>
                    )}

                    {/* Overlay com gradiente */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent pointer-events-none" />

                    {/* Badges sobre a imagem */}
                    <div className="absolute top-6 left-6 flex gap-3">
                      {item.is_featured && (
                        <div className="bg-yellow-500/90 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg border border-yellow-400/50">
                          ⭐ Destaque
                        </div>
                      )}
                      {item.is_popular && (
                        <div className="bg-red-500/90 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg border border-red-400/50">
                          🔥 Popular
                        </div>
                      )}
                    </div>

                    {/* Ações do Header */}
                    <div className="absolute top-6 right-6 flex gap-3">
                      {/* Card Personalizável */}
                      {((item.variant_groups?.length ?? 0) > 0 || (item.modifier_groups?.length ?? 0) > 0 || (item.optional_groups?.length ?? 0) > 0) && (
                        <div className={getHeaderElementClasses("flex items-center gap-2 px-3 py-2 rounded-full shadow-lg")}>
                          <SparklesIcon className={`h-4 w-4 ${getIconClasses()}`} />
                          <span className={`text-sm font-medium ${getIconClasses()}`}>Personalizável</span>
                        </div>
                      )}

                      <button
                        onClick={() => setIsFavorite(!isFavorite)}
                        className={getHeaderElementClasses("p-3 rounded-full transition-all duration-300 hover:scale-110 shadow-lg")}
                      >
                        <HeartIcon className={`h-5 w-5 ${isFavorite ? 'fill-red-500 text-red-500' : getIconClasses()}`} />
                      </button>
                      <button
                        onClick={handleClose}
                        className={getHeaderElementClasses("p-3 rounded-full transition-all duration-300 hover:scale-110 shadow-lg")}
                      >
                        <XMarkIcon className={`h-5 w-5 ${getIconClasses()}`} />
                      </button>
                    </div>

                    {/* Título e Preço sobre a imagem */}
                    <div className="absolute bottom-6 left-6 right-6">
                      <h2 className="text-4xl font-bold text-white mb-2 drop-shadow-lg">{item.name}</h2>
                      <div className="text-3xl font-bold text-orange-300 drop-shadow-lg">
                        {formatPrice(calculateTotalPrice().toString())}
                      </div>
                    </div>

                    {/* Status de disponibilidade */}
                    {!item.is_available && (
                      <div className="absolute inset-0 bg-black/70 flex items-center justify-center">
                        <div className="bg-white/90 backdrop-blur-md text-gray-900 px-8 py-4 rounded-2xl font-semibold border border-white/50 text-xl">
                          Temporariamente Indisponível
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Galeria de Miniaturas - Apenas se houver múltiplas imagens */}
                  {hasMultipleImages && (
                    <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
                      <div className="flex gap-3 overflow-x-auto scrollbar-hide">
                        {itemImages.map((image, index) => (
                          <button
                            key={index}
                            onClick={() => goToImage(index)}
                            className={`flex-shrink-0 w-20 h-16 rounded-lg overflow-hidden border-2 transition-all duration-300 ${
                              index === currentImageIndex
                                ? 'border-orange-500 shadow-lg scale-105'
                                : 'border-gray-200 hover:border-orange-300 hover:scale-105'
                            }`}
                          >
                            <div className="relative w-full h-full">
                              <NextImage
                                src={image}
                                alt={`${item.name} - Miniatura ${index + 1}`}
                                fill
                                className="object-cover"
                              />
                            </div>
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Conteúdo Principal */}
                <div className="p-8 max-h-[60vh] overflow-y-auto">
                  {/* Descrição */}
                  {item.description && (
                    <div className="mb-8">
                      <p className="text-gray-600 leading-relaxed text-lg">{item.description}</p>
                    </div>
                  )}

                  {/* Informações Rápidas */}
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-8">
                    {item.preparation_time && (
                      <div className="flex items-center gap-3 p-4 bg-blue-50 rounded-xl border border-blue-100">
                        <ClockIcon className="h-5 w-5 text-blue-600" />
                        <div>
                          <div className="text-sm font-medium text-blue-900">Preparo</div>
                          <div className="text-xs text-blue-700">{item.preparation_time} min</div>
                        </div>
                      </div>
                    )}

                    {/* Alérgenos removidos daqui - mostrados no final */}
                    {/* Personalizável movido para o header */}
                  </div>

                  {/* Seção de Variantes */}
                  {item.variant_groups && item.variant_groups.length > 0 && (
                    <div className="mb-8">
                      {item.variant_groups.map((group) => (
                        <div key={group.id} className="mb-6 p-6 bg-gray-50 rounded-2xl border border-gray-100">
                          <div className="flex items-center justify-between mb-4">
                            <h4 className="text-lg font-semibold text-gray-900">
                              {group.name}
                              {group.is_required && <span className="text-red-500 ml-1">*</span>}
                            </h4>
                            <span className="text-sm text-gray-500">
                              {group.is_required ? 'Obrigatório' : 'Opcional'}
                            </span>
                          </div>

                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                            {group.options?.map((variant) => {
                              const isSelected = selectedVariants[group.id] === variant.id;
                              const priceAdjustment = variant.price_adjustment || 0;

                              return (
                                <button
                                  key={variant.id}
                                  onClick={() => handleVariantSelect(group.id, variant.id)}
                                  className={`p-4 rounded-xl border-2 transition-all duration-200 text-left ${
                                    isSelected
                                      ? 'border-orange-500 bg-orange-50 shadow-md'
                                      : 'border-gray-200 bg-white hover:border-orange-300 hover:bg-orange-25'
                                  }`}
                                >
                                  <div className="flex items-center justify-between">
                                    <div>
                                      <div className="font-medium text-gray-900">{variant.name}</div>
                                      {priceAdjustment !== 0 && (
                                        <div className={`text-sm ${priceAdjustment > 0 ? 'text-green-600' : 'text-red-600'}`}>
                                          {priceAdjustment > 0 ? '+' : ''}{formatPrice(priceAdjustment.toString())}
                                        </div>
                                      )}
                                    </div>
                                    {isSelected && (
                                      <CheckIcon className="h-5 w-5 text-orange-500" />
                                    )}
                                  </div>
                                </button>
                              );
                            })}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Seção de Modificadores */}
                  {item.modifier_groups && item.modifier_groups.length > 0 && (
                    <div className="mb-8">

                      {item.modifier_groups.map((group) => (
                        <div key={group.id} className="mb-6 p-6 bg-green-50 rounded-2xl border border-green-100">
                          <div className="flex items-center justify-between mb-4">
                            <h4 className="text-lg font-semibold text-gray-900">
                              {group.name}
                              {group.is_required && <span className="text-red-500 ml-1">*</span>}
                            </h4>
                            <span className="text-sm text-gray-500">
                              {group.max_selection ? `Máx. ${group.max_selection}` : 'Ilimitado'}
                            </span>
                          </div>

                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                            {group.options?.map((modifier) => {
                              const isSelected = selectedModifiers[modifier.id];
                              const priceAdjustment = modifier.price_adjustment || 0;

                              return (
                                <button
                                  key={modifier.id}
                                  onClick={() => handleModifierToggle(modifier.id, group.id)}
                                  className={`p-4 rounded-xl border-2 transition-all duration-200 text-left ${
                                    isSelected
                                      ? 'border-green-500 bg-green-50 shadow-md'
                                      : 'border-gray-200 bg-white hover:border-green-300 hover:bg-green-25'
                                  }`}
                                >
                                  <div className="flex items-center justify-between">
                                    <div>
                                      <div className="font-medium text-gray-900">{modifier.name}</div>
                                      {priceAdjustment !== 0 && (
                                        <div className={`text-sm ${priceAdjustment > 0 ? 'text-green-600' : 'text-red-600'}`}>
                                          {priceAdjustment > 0 ? '+' : ''}{formatPrice(priceAdjustment.toString())}
                                        </div>
                                      )}
                                    </div>
                                    {isSelected && (
                                      <CheckIcon className="h-5 w-5 text-green-500" />
                                    )}
                                  </div>
                                </button>
                              );
                            })}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Seção de Opcionais */}
                  {item.optional_groups && item.optional_groups.length > 0 && (
                    <div className="mb-8">

                      {item.optional_groups.map((group) => (
                        <div key={group.id} className="mb-6 p-6 bg-blue-50 rounded-2xl border border-blue-100">
                          <div className="flex items-center justify-between mb-4">
                            <h4 className="text-lg font-semibold text-gray-900">
                              {group.name}
                            </h4>
                            <span className="text-sm text-gray-500">
                              {group.max_selection ? `Máx. ${group.max_selection}` : 'Ilimitado'}
                            </span>
                          </div>

                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                            {group.options?.map((optional) => {
                              const isSelected = selectedOptionals[optional.id];
                              const priceAdjustment = optional.price_adjustment || 0;

                              return (
                                <button
                                  key={optional.id}
                                  onClick={() => handleOptionalToggle(optional.id, group.id)}
                                  className={`p-4 rounded-xl border-2 transition-all duration-200 text-left ${
                                    isSelected
                                      ? 'border-blue-500 bg-blue-50 shadow-md'
                                      : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-25'
                                  }`}
                                >
                                  <div className="flex items-center justify-between">
                                    <div>
                                      <div className="font-medium text-gray-900">{optional.name}</div>
                                      {priceAdjustment !== 0 && (
                                        <div className={`text-sm ${priceAdjustment > 0 ? 'text-green-600' : 'text-red-600'}`}>
                                          {priceAdjustment > 0 ? '+' : ''}{formatPrice(priceAdjustment.toString())}
                                        </div>
                                      )}
                                    </div>
                                    {isSelected && (
                                      <CheckIcon className="h-5 w-5 text-blue-500" />
                                    )}
                                  </div>
                                </button>
                              );
                            })}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Observações Especiais */}
                  <div className="mb-8">
                    <button
                      onClick={() => toggleSection('observations')}
                      className="w-full flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 rounded-xl border border-gray-200 transition-colors"
                    >
                      <div className="flex items-center gap-2">
                        <span className="text-lg font-semibold text-gray-900">Observações especiais</span>
                        <span className="text-sm text-gray-500">(opcional)</span>
                      </div>
                      <ChevronDownIcon
                        className={`h-5 w-5 text-gray-500 transition-transform duration-200 ${
                          expandedSections.observations ? 'rotate-180' : ''
                        }`}
                      />
                    </button>

                    {expandedSections.observations && (
                      <div className="mt-4 space-y-3">
                        <textarea
                          value={specialInstructions}
                          onChange={(e) => setSpecialInstructions(e.target.value)}
                          placeholder="Alguma observação para a cozinha? (Ex: sem cebola, ponto da carne, etc.)"
                          className="w-full p-4 border border-gray-200 rounded-xl resize-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors"
                          rows={3}
                          maxLength={200}
                        />
                        <div className="text-xs text-gray-500">
                          {specialInstructions.length}/200 caracteres
                        </div>
                      </div>
                    )}
                  </div>
                  {/* Seção de Alérgenos */}
                  {getItemAllergens().length > 0 && (
                    <div>
                      <div className="px-6 py-4 bg-red-50 rounded-2xl border border-red-100">
                        <div className="flex items-center justify-between">
                          <h4 className="text-lg font-semibold text-red-900 flex items-center gap-2">
                            <ExclamationTriangleIcon className="h-5 w-5 text-red-600" />
                            Alérgenos
                          </h4>

                          <div className="flex items-center gap-2">
                            {/* Alérgenos reais do item */}
                            {getItemAllergens().map((allergen) => {
                              const allergenInfo = findAllergenInfo(allergen);
                              if (!allergenInfo) return null;

                              return (
                                <div
                                  key={allergen}
                                  className="relative group"
                                  title={allergenInfo.name}
                                >
                                  <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center text-2xl hover:bg-red-200 transition-colors cursor-help">
                                    {allergenInfo.icon}
                                  </div>
                                  {/* Tooltip */}
                                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black/80 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                                    {allergenInfo.name}
                                  </div>
                                </div>
                              );
                            })}


                          </div>
                        </div>

                        {/* Texto adicional removido */}
                      </div>
                    </div>
                  )}
                </div>

                {/* Rodapé Fixo com Controles */}
                <div className="border-t border-gray-100 p-6 bg-white">
                  {/* Botões de Ação com Quantidade */}
                  <div className="flex items-center gap-4">
                    {/* Controle de Quantidade */}
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => setQuantity(Math.max(1, quantity - 1))}
                        disabled={quantity <= 1}
                        className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 hover:scale-110"
                      >
                        <MinusIcon className="h-4 w-4 text-gray-600" />
                      </button>
                      <span className="text-xl font-bold text-gray-900 min-w-[3rem] text-center">
                        {quantity}
                      </span>
                      <button
                        onClick={() => setQuantity(quantity + 1)}
                        className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-all duration-200 hover:scale-110"
                      >
                        <PlusIcon className="h-4 w-4 text-gray-600" />
                      </button>
                    </div>

                    {/* Botão Adicionar ao Carrinho */}
                    {item.is_available && onAddToCart && (
                      <button
                        onClick={handleAddToCart}
                        disabled={isAddingToCart}
                        className="flex-1 py-4 px-8 bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white font-bold rounded-2xl transition-all duration-300 hover:scale-105 hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-3"
                      >
                        {isAddingToCart ? (
                          <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                        ) : (
                          <>
                            <ShoppingCartIcon className="h-5 w-5" />
                            Adicionar • {formatPrice(calculateTotalPrice().toString())}
                          </>
                        )}
                      </button>
                    )}
                  </div>

                  {/* Alérgenos removidos do final */}
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
