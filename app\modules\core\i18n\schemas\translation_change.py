"""
Translation change schemas for the i18n module.
"""

import uuid
from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, List

from app.modules.core.i18n.models.translation_change import ChangeType


class TranslationChangeBase(BaseModel):
    """Base schema for TranslationChange."""

    key: str = Field(..., min_length=1, max_length=255, description="Translation key")
    sector: str = Field(
        ..., min_length=1, max_length=50, description="Sector/namespace for the translation"
    )
    change_type: ChangeType = Field(..., description="Type of change (added, updated, deleted)")
    new_text: Optional[str] = Field(None, description="New text value (null if deleted)")
    previous_text: Optional[str] = Field(None, description="Previous text value (null if added)")
    language_id: uuid.UUID = Field(..., description="ID of the language")
    version_code: str = Field(
        ..., min_length=6, max_length=6, description="Version code after this change"
    )


class TranslationChangeCreate(TranslationChangeBase):
    """Schema for creating a new TranslationChange."""

    changed_by_id: Optional[uuid.UUID] = Field(
        None, description="ID of the user who made the change"
    )


class TranslationChangeRead(TranslationChangeBase):
    """Schema for reading a TranslationChange."""

    id: uuid.UUID
    changed_at: datetime
    changed_by_id: Optional[uuid.UUID] = None

    model_config = ConfigDict(from_attributes=True)


class TranslationChangeFilter(BaseModel):
    """Schema for filtering TranslationChanges."""

    language_code: str = Field(..., description="Language code")
    since_version: str = Field(
        ..., min_length=6, max_length=6, description="Get changes since this version"
    )
    sector: Optional[str] = Field(None, description="Filter by sector")


class SectorChanges(BaseModel):
    """Schema for grouping changes by sector."""

    sector: str
    changes: List[TranslationChangeRead]


class LanguageChanges(BaseModel):
    """Schema for all changes for a language since a specific version."""

    language_code: str
    current_version_code: str
    has_changes: bool
    sectors: List[SectorChanges]
