"""
Slug Service

Business logic for URL slug generation and management.
"""

import uuid
import re
import unicodedata
from typing import Dict, List, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func

from app.modules.core.seo.models.url_slug import URLSlug
from app.modules.core.seo.schemas.seo_schemas import URLSlugCreate, URLSlugUpdate
from app.modules.core.i18n.models.language import Language


class SlugService:
    """Service for URL slug generation and management."""

    async def create_slug(
        self,
        db: AsyncSession,
        slug_data: URLSlugCreate,
        user_id: Optional[uuid.UUID] = None
    ) -> URLSlug:
        """Create a new URL slug."""
        # Ensure slug is URL-friendly
        slug_data.slug = self.generate_slug(slug_data.slug)
        
        # Check for uniqueness and handle conflicts
        unique_slug = await self._ensure_unique_slug(
            db, slug_data.slug, slug_data.content_type, slug_data.language_code
        )
        slug_data.slug = unique_slug
        
        # If this is primary, make sure no other slug is primary for this content/language
        if slug_data.is_primary:
            await self._unset_other_primary_slugs(
                db, slug_data.content_type, slug_data.content_id, slug_data.language_code
            )
        
        url_slug = URLSlug(
            **slug_data.dict(),
            created_by_id=user_id
        )
        
        db.add(url_slug)
        await db.commit()
        await db.refresh(url_slug)
        return url_slug

    async def get_slug(
        self,
        db: AsyncSession,
        content_type: str,
        content_id: uuid.UUID,
        language_code: str,
        primary_only: bool = True
    ) -> Optional[URLSlug]:
        """Get URL slug for specific content and language."""
        query = select(URLSlug).filter(
            and_(
                URLSlug.content_type == content_type,
                URLSlug.content_id == content_id,
                URLSlug.language_code == language_code,
                URLSlug.is_active == True
            )
        )
        
        if primary_only:
            query = query.filter(URLSlug.is_primary == True)
        
        result = await db.execute(query)
        return result.scalar_one_or_none()

    async def get_all_slugs_for_content(
        self,
        db: AsyncSession,
        content_type: str,
        content_id: uuid.UUID
    ) -> List[URLSlug]:
        """Get all URL slugs for content across all languages."""
        result = await db.execute(
            select(URLSlug).filter(
                and_(
                    URLSlug.content_type == content_type,
                    URLSlug.content_id == content_id,
                    URLSlug.is_active == True
                )
            ).order_by(URLSlug.language_code, URLSlug.is_primary.desc())
        )
        return result.scalars().all()

    async def find_content_by_slug(
        self,
        db: AsyncSession,
        slug: str,
        content_type: Optional[str] = None,
        language_code: Optional[str] = None
    ) -> Optional[URLSlug]:
        """Find content by slug."""
        query = select(URLSlug).filter(
            and_(
                URLSlug.slug == slug,
                URLSlug.is_active == True
            )
        )
        
        if content_type:
            query = query.filter(URLSlug.content_type == content_type)
        
        if language_code:
            query = query.filter(URLSlug.language_code == language_code)
        
        result = await db.execute(query)
        return result.scalar_one_or_none()

    async def update_slug(
        self,
        db: AsyncSession,
        slug_id: uuid.UUID,
        slug_data: URLSlugUpdate
    ) -> Optional[URLSlug]:
        """Update URL slug."""
        result = await db.execute(
            select(URLSlug).filter(URLSlug.id == slug_id)
        )
        url_slug = result.scalar_one_or_none()
        
        if not url_slug:
            return None

        update_data = slug_data.dict(exclude_unset=True)
        
        # Handle slug update with uniqueness check
        if 'slug' in update_data:
            new_slug = self.generate_slug(update_data['slug'])
            unique_slug = await self._ensure_unique_slug(
                db, new_slug, url_slug.content_type, url_slug.language_code, exclude_id=slug_id
            )
            update_data['slug'] = unique_slug
        
        # Handle primary flag
        if update_data.get('is_primary', False):
            await self._unset_other_primary_slugs(
                db, url_slug.content_type, url_slug.content_id, url_slug.language_code, exclude_id=slug_id
            )
        
        if update_data:
            for field, value in update_data.items():
                setattr(url_slug, field, value)
            
            await db.commit()
            await db.refresh(url_slug)
        
        return url_slug

    async def create_multilingual_slugs(
        self,
        db: AsyncSession,
        content_type: str,
        content_id: uuid.UUID,
        slug_data_by_language: Dict[str, str],
        user_id: Optional[uuid.UUID] = None
    ) -> List[URLSlug]:
        """Create URL slugs for multiple languages."""
        slugs = []
        
        for language_code, slug_text in slug_data_by_language.items():
            slug_data = URLSlugCreate(
                content_type=content_type,
                content_id=content_id,
                language_code=language_code,
                slug=slug_text,
                is_primary=True,
                is_active=True
            )
            
            slug = await self.create_slug(db, slug_data, user_id)
            slugs.append(slug)
        
        return slugs

    async def get_hreflang_data(
        self,
        db: AsyncSession,
        content_type: str,
        content_id: uuid.UUID,
        base_url: str = ""
    ) -> List[Dict[str, str]]:
        """Get hreflang data for content across all languages."""
        slugs = await self.get_all_slugs_for_content(db, content_type, content_id)
        
        hreflang_data = []
        for slug in slugs:
            if slug.is_primary:
                hreflang_data.append({
                    "hreflang": slug.language_code,
                    "href": f"{base_url}{slug.full_url}",
                    "title": f"View in {slug.language_code}"
                })
        
        return hreflang_data

    def generate_slug(self, text: str, max_length: int = 255) -> str:
        """Generate URL-friendly slug from text."""
        if not text:
            return ""
        
        # Convert to lowercase
        text = text.lower()
        
        # Remove accents and normalize unicode
        text = unicodedata.normalize('NFKD', text)
        text = text.encode('ascii', 'ignore').decode('ascii')
        
        # Replace spaces and special characters with hyphens
        text = re.sub(r'[^a-z0-9]+', '-', text)
        
        # Remove leading/trailing hyphens
        text = text.strip('-')
        
        # Limit length
        if len(text) > max_length:
            text = text[:max_length].rstrip('-')
        
        return text or "slug"

    async def _ensure_unique_slug(
        self,
        db: AsyncSession,
        slug: str,
        content_type: str,
        language_code: str,
        exclude_id: Optional[uuid.UUID] = None
    ) -> str:
        """Ensure slug is unique by appending numbers if necessary."""
        original_slug = slug
        counter = 1
        
        while True:
            query = select(URLSlug).filter(
                and_(
                    URLSlug.slug == slug,
                    URLSlug.content_type == content_type,
                    URLSlug.language_code == language_code,
                    URLSlug.is_active == True
                )
            )
            
            if exclude_id:
                query = query.filter(URLSlug.id != exclude_id)
            
            result = await db.execute(query)
            existing_slug = result.scalar_one_or_none()
            
            if not existing_slug:
                return slug
            
            slug = f"{original_slug}-{counter}"
            counter += 1

    async def _unset_other_primary_slugs(
        self,
        db: AsyncSession,
        content_type: str,
        content_id: uuid.UUID,
        language_code: str,
        exclude_id: Optional[uuid.UUID] = None
    ):
        """Unset primary flag for other slugs of the same content/language."""
        query = select(URLSlug).filter(
            and_(
                URLSlug.content_type == content_type,
                URLSlug.content_id == content_id,
                URLSlug.language_code == language_code,
                URLSlug.is_primary == True
            )
        )
        
        if exclude_id:
            query = query.filter(URLSlug.id != exclude_id)
        
        result = await db.execute(query)
        slugs = result.scalars().all()
        
        for slug in slugs:
            slug.is_primary = False
        
        if slugs:
            await db.commit()
