"""
Testes para o MessageService.
"""

import pytest
from datetime import datetime, timezone
from uuid import uuid4

from app.modules.core.help_center.services.message_service import MessageService
from app.modules.core.help_center.schemas import MessageCreate
from app.modules.core.help_center.models import Ticket, TicketMessage, TicketStatus, TicketPriority, TicketCategory, MessageType
from app.modules.core.users.models.user import User


@pytest.fixture
async def message_service(db_session):
    """Fixture para MessageService."""
    return MessageService(db_session)


@pytest.fixture
async def test_user(db_session):
    """Fixture para usuário de teste."""
    user = User(
        id=uuid4(),
        email="<EMAIL>",
        full_name="Test User",
        system_role="user",
        is_active=True
    )
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    return user


@pytest.fixture
async def admin_user(db_session):
    """Fixture para usuário admin."""
    user = User(
        id=uuid4(),
        email="<EMAIL>",
        full_name="Admin User",
        system_role="admin",
        is_active=True
    )
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    return user


@pytest.fixture
async def test_ticket(db_session, test_user):
    """Fixture para ticket de teste."""
    ticket = Ticket(
        id=uuid4(),
        title="Ticket de teste",
        description="Descrição do ticket de teste",
        status=TicketStatus.NEW,
        priority=TicketPriority.MEDIUM,
        category=TicketCategory.QUESTION,
        user_id=test_user.id,
        created_at=datetime.now(timezone.utc),
        is_read_by_user=True,
        is_read_by_admin=False
    )
    db_session.add(ticket)
    await db_session.commit()
    await db_session.refresh(ticket)
    return ticket


class TestMessageService:
    """Testes para o MessageService."""

    async def test_create_text_message(self, message_service, test_ticket, test_user):
        """Testa criação de mensagem de texto."""
        message_data = MessageCreate(
            message_content="Esta é uma mensagem de teste",
            message_type=MessageType.TEXT
        )
        
        message = await message_service.create_message(
            ticket=test_ticket,
            message_data=message_data,
            sender=test_user
        )
        
        assert message is not None
        assert message.message_content == "Esta é uma mensagem de teste"
        assert message.message_type == MessageType.TEXT
        assert message.ticket_id == test_ticket.id
        assert message.sender_id == test_user.id
        assert message.is_read is False

    async def test_create_file_message(self, message_service, test_ticket, test_user):
        """Testa criação de mensagem com arquivo."""
        message_data = MessageCreate(
            message_content="Arquivo anexado",
            message_type=MessageType.FILE,
            file_path="/uploads/test.pdf",
            file_name="test.pdf",
            file_size=1024,
            mime_type="application/pdf"
        )
        
        message = await message_service.create_message(
            ticket=test_ticket,
            message_data=message_data,
            sender=test_user
        )
        
        assert message is not None
        assert message.message_type == MessageType.FILE
        assert message.file_path == "/uploads/test.pdf"
        assert message.file_name == "test.pdf"
        assert message.file_size == 1024
        assert message.mime_type == "application/pdf"

    async def test_create_image_message(self, message_service, test_ticket, admin_user):
        """Testa criação de mensagem com imagem."""
        message_data = MessageCreate(
            message_content="Imagem anexada",
            message_type=MessageType.IMAGE,
            file_path="/uploads/screenshot.png",
            file_name="screenshot.png",
            file_size=2048,
            mime_type="image/png"
        )
        
        message = await message_service.create_message(
            ticket=test_ticket,
            message_data=message_data,
            sender=admin_user
        )
        
        assert message is not None
        assert message.message_type == MessageType.IMAGE
        assert message.file_path == "/uploads/screenshot.png"
        assert message.mime_type == "image/png"

    async def test_list_messages_by_ticket(self, message_service, test_ticket, test_user, admin_user):
        """Testa listagem de mensagens por ticket."""
        # Criar algumas mensagens
        for i in range(3):
            message_data = MessageCreate(
                message_content=f"Mensagem {i}",
                message_type=MessageType.TEXT
            )
            await message_service.create_message(
                ticket=test_ticket,
                message_data=message_data,
                sender=test_user if i % 2 == 0 else admin_user
            )
        
        # Listar mensagens
        messages = await message_service.list_messages_by_ticket(test_ticket.id, test_user)
        
        assert len(messages) == 3
        assert all(msg.ticket_id == test_ticket.id for msg in messages)
        # Mensagens devem estar ordenadas por data de criação
        assert messages[0].created_at <= messages[1].created_at <= messages[2].created_at

    async def test_mark_message_as_read(self, message_service, test_ticket, test_user, admin_user):
        """Testa marcação de mensagem como lida."""
        # Criar mensagem do admin
        message_data = MessageCreate(
            message_content="Mensagem do admin",
            message_type=MessageType.TEXT
        )
        
        message = await message_service.create_message(
            ticket=test_ticket,
            message_data=message_data,
            sender=admin_user
        )
        
        assert message.is_read is False
        
        # Marcar como lida pelo usuário
        success = await message_service.mark_message_as_read(message.id, test_user)
        
        assert success is True
        
        # Verificar se foi marcada como lida
        updated_message = await message_service.get_message_by_id(message.id)
        assert updated_message.is_read is True

    async def test_get_message_by_id(self, message_service, test_ticket, test_user):
        """Testa obtenção de mensagem por ID."""
        # Criar mensagem
        message_data = MessageCreate(
            message_content="Mensagem para buscar",
            message_type=MessageType.TEXT
        )
        
        created_message = await message_service.create_message(
            ticket=test_ticket,
            message_data=message_data,
            sender=test_user
        )
        
        # Buscar mensagem
        found_message = await message_service.get_message_by_id(created_message.id)
        
        assert found_message is not None
        assert found_message.id == created_message.id
        assert found_message.message_content == "Mensagem para buscar"

    async def test_get_nonexistent_message(self, message_service):
        """Testa busca de mensagem inexistente."""
        nonexistent_id = uuid4()
        message = await message_service.get_message_by_id(nonexistent_id)
        
        assert message is None

    async def test_mark_nonexistent_message_as_read(self, message_service, test_user):
        """Testa marcação de mensagem inexistente como lida."""
        nonexistent_id = uuid4()
        success = await message_service.mark_message_as_read(nonexistent_id, test_user)
        
        assert success is False

    async def test_list_messages_empty_ticket(self, message_service, test_ticket, test_user):
        """Testa listagem de mensagens em ticket vazio."""
        messages = await message_service.list_messages_by_ticket(test_ticket.id, test_user)
        
        assert len(messages) == 0

    async def test_message_ordering(self, message_service, test_ticket, test_user):
        """Testa ordenação das mensagens."""
        # Criar mensagens com pequeno delay
        import asyncio
        
        message_ids = []
        for i in range(3):
            message_data = MessageCreate(
                message_content=f"Mensagem {i}",
                message_type=MessageType.TEXT
            )
            message = await message_service.create_message(
                ticket=test_ticket,
                message_data=message_data,
                sender=test_user
            )
            message_ids.append(message.id)
            await asyncio.sleep(0.01)  # Pequeno delay para garantir ordem
        
        # Listar mensagens
        messages = await message_service.list_messages_by_ticket(test_ticket.id, test_user)
        
        # Verificar ordem (mais antigas primeiro)
        assert len(messages) == 3
        for i in range(len(messages) - 1):
            assert messages[i].created_at <= messages[i + 1].created_at

    async def test_message_with_long_content(self, message_service, test_ticket, test_user):
        """Testa mensagem com conteúdo longo."""
        long_content = "A" * 5000  # 5000 caracteres
        
        message_data = MessageCreate(
            message_content=long_content,
            message_type=MessageType.TEXT
        )
        
        message = await message_service.create_message(
            ticket=test_ticket,
            message_data=message_data,
            sender=test_user
        )
        
        assert message is not None
        assert len(message.message_content) == 5000
        assert message.message_content == long_content
