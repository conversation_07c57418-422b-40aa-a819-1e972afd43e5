import logging
import uuid
from typing import List, Optional, Annotated, Any

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import selectinload
from sqlalchemy import select, func

# Set up logger
logger = logging.getLogger(__name__)

from app.modules.core.users.models.user import User
from app.modules.core.auth.dependencies.auth_dependencies import get_current_active_user
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    get_current_tenant_from_header,
    require_tenant_role,
)
from app.modules.core.tenants.models.tenant import Tenant
from app.modules.core.roles.models.roles import RolePermissions

# Import models
from app.modules.core.functions.customizations.models.variant_group import VariantGroup
from app.modules.core.functions.customizations.models.modifier_group import ModifierGroup
from app.modules.core.functions.customizations.models.optional_group import OptionalGroup

# Import schemas
from app.modules.tenants.restaurants.menu.schemas.variant_group import VariantGroupReadSimple, VariantGroupReadWithOptions
from app.modules.tenants.restaurants.menu.schemas.modifier_group import ModifierGroupReadSimple, ModifierGroupReadWithOptions
from app.modules.tenants.restaurants.menu.schemas.optional_group import OptionalGroupReadSimple, OptionalGroupReadWithOptions

from app.core.dependencies import get_db
from sqlalchemy.ext.asyncio import AsyncSession

# Router setup
router = APIRouter(
    tags=["Restaurant - Menu Groups"],
)

# Define required roles for modification endpoints
view_roles = RolePermissions.VIEW_ROLES


@router.get("/variant-groups", response_model=List[VariantGroupReadWithOptions])
async def get_variant_groups(
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))],
    db: Annotated[AsyncSession, Depends(get_db)],
    digital_menu_id: Optional[uuid.UUID] = Query(None, description="Filter groups by digital menu ID"),
    include_usage_count: bool = Query(False, description="Include usage count for each group"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=200, description="Maximum number of records to return"),
):
    """
    Retrieve variant groups for the current tenant.
    These can be used as templates for other menu items.
    """
    try:
        logger.info(f"🔍 get_variant_groups - tenant_id: {current_tenant.id}")

        # Base query for variant groups with options
        # Only return groups that have options (can be reused)
        query = select(VariantGroup).where(VariantGroup.tenant_id == current_tenant.id)

        # Filter by digital menu if specified
        if digital_menu_id:
            # Join with menu_item_variant_groups, menu_items and menu_categories to filter by digital menu
            from app.modules.tenants.restaurants.menu.models.menu_item import MenuItem
            from app.modules.tenants.restaurants.menu.models.menu_category import MenuCategory
            from app.db.base import menu_item_variant_groups
            query = query.join(
                menu_item_variant_groups, VariantGroup.id == menu_item_variant_groups.c.variant_group_id
            ).join(
                MenuItem, menu_item_variant_groups.c.menu_item_id == MenuItem.id
            ).join(
                MenuCategory, MenuItem.category_id == MenuCategory.id
            ).where(MenuCategory.digital_menu_id == digital_menu_id)

        query = query.distinct().options(selectinload(VariantGroup.options)).offset(skip).limit(limit)

        result = await db.execute(query)
        groups = result.scalars().all()

        logger.info(f"🔍 get_variant_groups - Found {len(groups)} groups")
        for group in groups:
            logger.info(f"🔍 get_variant_groups - Group: {group.id} - {group.name} - tenant: {group.tenant_id}")

        # Convert to response format - include ALL groups as potential templates
        response_groups = []
        for group in groups:
            # Convert using schema which now includes options
            group_dict = VariantGroupReadWithOptions.model_validate(group).model_dump()

            # Convert UUIDs to strings for JSON serialization
            group_dict['id'] = str(group_dict['id'])
            group_dict['tenant_id'] = str(group_dict['tenant_id'])

            # Convert option UUIDs to strings
            if group_dict.get('options'):
                for option in group_dict['options']:
                    option['id'] = str(option['id'])
                    option['tenant_id'] = str(option['tenant_id'])
                    option['variant_group_id'] = str(option['variant_group_id'])

            if include_usage_count:
                # Count how many menu items use this group
                from app.db.base import menu_item_variant_groups
                usage_query = select(func.count(menu_item_variant_groups.c.menu_item_id)).where(
                    menu_item_variant_groups.c.variant_group_id == group.id,
                    menu_item_variant_groups.c.tenant_id == current_tenant.id
                )
                usage_result = await db.execute(usage_query)
                usage_count = usage_result.scalar() or 0

                group_dict['usage_count'] = usage_count
            else:
                group_dict['usage_count'] = 0

            response_groups.append(group_dict)

        logger.info(f"🔍 get_variant_groups - Returning {len(response_groups)} groups")
        logger.info(f"🔍 get_variant_groups - Response: {response_groups}")
        return response_groups

    except Exception as e:
        logger.exception(f"Error retrieving variant groups: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving variant groups"
        )


@router.get("/variant-groups/{group_id}", response_model=VariantGroupReadWithOptions)
async def get_variant_group_by_id(
    group_id: uuid.UUID,
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))],
    db: Annotated[AsyncSession, Depends(get_db)],
):
    """
    Retrieve a specific variant group by ID.
    Used for template functionality.
    """
    try:
        # Query for the specific variant group
        query = (
            select(VariantGroup)
            .where(
                VariantGroup.id == group_id,
                VariantGroup.tenant_id == current_tenant.id
            )
            .options(selectinload(VariantGroup.options))
        )

        result = await db.execute(query)
        group = result.scalar_one_or_none()

        if not group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Variant group not found"
            )

        # Convert to response format
        group_dict = VariantGroupReadWithOptions.model_validate(group).model_dump()

        # Convert UUIDs to strings for JSON serialization
        group_dict['id'] = str(group_dict['id'])
        group_dict['tenant_id'] = str(group_dict['tenant_id'])

        # Convert option UUIDs to strings
        if group_dict.get('options'):
            for option in group_dict['options']:
                option['id'] = str(option['id'])
                option['tenant_id'] = str(option['tenant_id'])
                option['variant_group_id'] = str(option['variant_group_id'])

        return group_dict

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error retrieving variant group {group_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving variant group"
        )


@router.get("/modifier-groups", response_model=List[ModifierGroupReadWithOptions])
async def get_modifier_groups(
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))],
    db: Annotated[AsyncSession, Depends(get_db)],
    digital_menu_id: Optional[uuid.UUID] = Query(None, description="Filter groups by digital menu ID"),
    include_usage_count: bool = Query(False, description="Include usage count for each group"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=200, description="Maximum number of records to return"),
):
    """
    Retrieve modifier groups for the current tenant.
    These can be used as templates for other menu items.
    """
    try:
        # Base query for modifier groups with options
        query = select(ModifierGroup).where(ModifierGroup.tenant_id == current_tenant.id)

        # Filter by digital menu if specified
        if digital_menu_id:
            # Join with menu_item_modifier_groups, menu_items and menu_categories to filter by digital menu
            from app.modules.tenants.restaurants.menu.models.menu_item import MenuItem
            from app.modules.tenants.restaurants.menu.models.menu_category import MenuCategory
            from app.db.base import menu_item_modifier_groups
            query = query.join(
                menu_item_modifier_groups, ModifierGroup.id == menu_item_modifier_groups.c.modifier_group_id
            ).join(
                MenuItem, menu_item_modifier_groups.c.menu_item_id == MenuItem.id
            ).join(
                MenuCategory, MenuItem.category_id == MenuCategory.id
            ).where(MenuCategory.digital_menu_id == digital_menu_id)

        query = query.distinct().options(selectinload(ModifierGroup.options)).offset(skip).limit(limit)

        result = await db.execute(query)
        groups = result.scalars().all()

        # Convert to response format - include ALL groups as potential templates
        response_groups = []
        for group in groups:
            # Convert using schema which now includes options
            group_dict = ModifierGroupReadWithOptions.model_validate(group).model_dump()

            # Convert UUIDs to strings for JSON serialization
            group_dict['id'] = str(group_dict['id'])
            group_dict['tenant_id'] = str(group_dict['tenant_id'])

            # Convert option UUIDs to strings
            if group_dict.get('options'):
                for option in group_dict['options']:
                    option['id'] = str(option['id'])
                    option['tenant_id'] = str(option['tenant_id'])
                    option['modifier_group_id'] = str(option['modifier_group_id'])

            if include_usage_count:
                # Count how many menu items use this group
                from app.db.base import menu_item_modifier_groups
                usage_query = select(func.count(menu_item_modifier_groups.c.menu_item_id)).where(
                    menu_item_modifier_groups.c.modifier_group_id == group.id,
                    menu_item_modifier_groups.c.tenant_id == current_tenant.id
                )
                usage_result = await db.execute(usage_query)
                usage_count = usage_result.scalar() or 0

                group_dict['usage_count'] = usage_count
            else:
                group_dict['usage_count'] = 0

            response_groups.append(group_dict)

        return response_groups

    except Exception as e:
        logger.exception(f"Error retrieving modifier groups: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving modifier groups"
        )


@router.get("/modifier-groups/{group_id}", response_model=ModifierGroupReadWithOptions)
async def get_modifier_group_by_id(
    group_id: uuid.UUID,
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))],
    db: Annotated[AsyncSession, Depends(get_db)],
):
    """
    Retrieve a specific modifier group by ID.
    Used for template functionality.
    """
    try:
        # Query for the specific modifier group
        query = (
            select(ModifierGroup)
            .where(
                ModifierGroup.id == group_id,
                ModifierGroup.tenant_id == current_tenant.id
            )
            .options(selectinload(ModifierGroup.options))
        )

        result = await db.execute(query)
        group = result.scalar_one_or_none()

        if not group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Modifier group not found"
            )

        # Convert to response format
        group_dict = ModifierGroupReadWithOptions.model_validate(group).model_dump()

        # Convert UUIDs to strings for JSON serialization
        group_dict['id'] = str(group_dict['id'])
        group_dict['tenant_id'] = str(group_dict['tenant_id'])

        # Convert option UUIDs to strings
        if group_dict.get('options'):
            for option in group_dict['options']:
                option['id'] = str(option['id'])
                option['tenant_id'] = str(option['tenant_id'])
                option['modifier_group_id'] = str(option['modifier_group_id'])

        return group_dict

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error retrieving modifier group {group_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving modifier group"
        )


@router.get("/optional-groups", response_model=List[OptionalGroupReadWithOptions])
async def get_optional_groups(
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))],
    db: Annotated[AsyncSession, Depends(get_db)],
    digital_menu_id: Optional[uuid.UUID] = Query(None, description="Filter groups by digital menu ID"),
    include_usage_count: bool = Query(False, description="Include usage count for each group"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=200, description="Maximum number of records to return"),
):
    """
    Retrieve optional groups for the current tenant.
    These can be used as templates for other menu items.
    """
    try:
        # Base query for optional groups with options
        query = select(OptionalGroup).where(OptionalGroup.tenant_id == current_tenant.id)

        # Filter by digital menu if specified
        if digital_menu_id:
            # Join with menu_item_optional_groups, menu_items and menu_categories to filter by digital menu
            from app.modules.tenants.restaurants.menu.models.menu_item import MenuItem
            from app.modules.tenants.restaurants.menu.models.menu_category import MenuCategory
            from app.db.base import menu_item_optional_groups
            query = query.join(
                menu_item_optional_groups, OptionalGroup.id == menu_item_optional_groups.c.optional_group_id
            ).join(
                MenuItem, menu_item_optional_groups.c.menu_item_id == MenuItem.id
            ).join(
                MenuCategory, MenuItem.category_id == MenuCategory.id
            ).where(MenuCategory.digital_menu_id == digital_menu_id)

        query = query.distinct().options(selectinload(OptionalGroup.options)).offset(skip).limit(limit)

        result = await db.execute(query)
        groups = result.scalars().all()

        # Convert to response format - include ALL groups as potential templates
        response_groups = []
        for group in groups:
            # Convert using schema which now includes options
            group_dict = OptionalGroupReadWithOptions.model_validate(group).model_dump()

            # Convert UUIDs to strings for JSON serialization
            group_dict['id'] = str(group_dict['id'])
            group_dict['tenant_id'] = str(group_dict['tenant_id'])

            # Convert option UUIDs to strings
            if group_dict.get('options'):
                for option in group_dict['options']:
                    option['id'] = str(option['id'])
                    option['tenant_id'] = str(option['tenant_id'])
                    option['optional_group_id'] = str(option['optional_group_id'])

            if include_usage_count:
                # Count how many menu items use this group
                from app.db.base import menu_item_optional_groups
                usage_query = select(func.count(menu_item_optional_groups.c.menu_item_id)).where(
                    menu_item_optional_groups.c.optional_group_id == group.id,
                    menu_item_optional_groups.c.tenant_id == current_tenant.id
                )
                usage_result = await db.execute(usage_query)
                usage_count = usage_result.scalar() or 0

                group_dict['usage_count'] = usage_count
            else:
                group_dict['usage_count'] = 0

            response_groups.append(group_dict)

        return response_groups

    except Exception as e:
        logger.exception(f"Error retrieving optional groups: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving optional groups"
        )


@router.get("/optional-groups/{group_id}", response_model=OptionalGroupReadWithOptions)
async def get_optional_group_by_id(
    group_id: uuid.UUID,
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    _: Annotated[Any, Depends(require_tenant_role(required_roles=view_roles))],
    db: Annotated[AsyncSession, Depends(get_db)],
):
    """
    Retrieve a specific optional group by ID.
    Used for template functionality.
    """
    try:
        # Query for the specific optional group
        query = (
            select(OptionalGroup)
            .where(
                OptionalGroup.id == group_id,
                OptionalGroup.tenant_id == current_tenant.id
            )
            .options(selectinload(OptionalGroup.options))
        )

        result = await db.execute(query)
        group = result.scalar_one_or_none()

        if not group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Optional group not found"
            )

        # Convert to response format
        group_dict = OptionalGroupReadWithOptions.model_validate(group).model_dump()

        # Convert UUIDs to strings for JSON serialization
        group_dict['id'] = str(group_dict['id'])
        group_dict['tenant_id'] = str(group_dict['tenant_id'])

        # Convert option UUIDs to strings
        if group_dict.get('options'):
            for option in group_dict['options']:
                option['id'] = str(option['id'])
                option['tenant_id'] = str(option['tenant_id'])
                option['optional_group_id'] = str(option['optional_group_id'])

        return group_dict

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error retrieving optional group {group_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving optional group"
        )
