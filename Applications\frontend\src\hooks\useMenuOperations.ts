'use client';

import { useRef, useEffect } from 'react';
import { useAuth } from '@/lib/auth/AuthProvider';
import { useTenant } from '@/lib/tenant/TenantProvider';
import apiClient from '@/lib/api/client';
import { menuService } from '@/services/api/menuService';
import { menuCategoryService } from '@/services/api/MenuCategoryService';
import { useMenuCache } from './useMenuCache';
import * as MenuTypes from '@/types/menu';

interface UseMenuOperationsProps {
  digitalMenus: MenuTypes.DigitalMenu[];
  selectedMenu: MenuTypes.DigitalMenu | null;
  setDigitalMenus: (
    menus: MenuTypes.DigitalMenu[] | 
    ((prev: MenuTypes.DigitalMenu[]) => MenuTypes.DigitalMenu[])
  ) => void;
  setSelectedMenu: (menu: MenuTypes.DigitalMenu | null) => void;
  setCategories: (categories: MenuTypes.MenuCategory[]) => void;
  setItems: (items: MenuTypes.MenuItem[]) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  processMenuItem: (item: any) => MenuTypes.MenuItem;
}

export function useMenuOperations({
  digitalMenus,
  selectedMenu,
  setDigitalMenus,
  setSelectedMenu,
  setCategories,
  setItems,
  setLoading,
  setError,
  processMenuItem,
}: UseMenuOperationsProps) {
  const { isAuthenticated } = useAuth();
  const { currentTenant } = useTenant();
  const {
    invalidateAfterMenuOperation,
    clearCache,
    preloadMenuData,
    getMenuData
  } = useMenuCache();

  // Race condition prevention
  const abortControllerRef = useRef<AbortController | null>(null);
  const currentOperationRef = useRef<string | null>(null);

  // Helper functions
  const getSelectedMenuKey = () => `selected_menu_${currentTenant?.id}`;

  // Cancel any pending operations
  const cancelPendingOperations = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  };

  // Start a new operation with race condition protection
  const startOperation = (operationId: string): AbortController => {
    cancelPendingOperations();
    const controller = new AbortController();
    abortControllerRef.current = controller;
    currentOperationRef.current = operationId;
    return controller;
  };

  // Check if operation is still valid
  const isOperationValid = (operationId: string): boolean => {
    return currentOperationRef.current === operationId &&
           abortControllerRef.current !== null &&
           !abortControllerRef.current.signal.aborted;
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cancelPendingOperations();
    };
  }, []);

  // Digital Menu Management
  const loadDigitalMenus = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiClient.get<MenuTypes.DigitalMenu[]>(
        '/modules/restaurants/menu/digital-menus/'
      );
      const menus = response.data;

      // Ensure we always set an array - handle both array and single object responses
      let menusArray: MenuTypes.DigitalMenu[] = [];

      if (Array.isArray(menus)) {
        menusArray = menus;
      } else if (menus && typeof menus === 'object') {
        // If backend returns a single object, wrap it in an array
        menusArray = [menus as MenuTypes.DigitalMenu];
      }

      // Set the menus
      setDigitalMenus(menusArray);

      // Preload data for all menus for instant switching
      if (menusArray.length > 0) {
        preloadMenuData(
          menusArray,
          fetchCategories,
          fetchItems
        ).catch(error => {
          console.warn('Menu preload failed:', error);
        });
      }

      // Auto-select menu: restore from localStorage or select first menu
      if (menusArray.length > 0 && !selectedMenu) {
        const savedMenuId = localStorage.getItem(getSelectedMenuKey());

        // Try to restore saved menu first
        if (savedMenuId) {
          const savedMenu = menusArray.find(m => m.id === savedMenuId);
          if (savedMenu) {
            setSelectedMenu(savedMenu);
            return; // Exit early if saved menu found
          } else {
            // If saved menu doesn't exist anymore, clear localStorage
            localStorage.removeItem(getSelectedMenuKey());
          }
        }

        // Auto-select first menu if no saved menu or saved menu not found
        const firstMenu = menusArray[0];
        if (firstMenu && firstMenu.id) {
          setSelectedMenu(firstMenu);
          localStorage.setItem(getSelectedMenuKey(), firstMenu.id);
        }
      }
    } catch (error) {
      console.error('Error loading digital menus:', error);
      setError('Erro ao carregar menus digitais');
      setDigitalMenus([]);
    } finally {
      setLoading(false);
    }
  };

  const createDigitalMenu = async (
    menuData: { name: string; description?: string }
  ): Promise<MenuTypes.DigitalMenu> => {
    if (!isAuthenticated || !currentTenant) {
      throw new Error('Authentication required');
    }

    try {
      const response = await apiClient.post<MenuTypes.DigitalMenu>(
        '/modules/restaurants/menu/digital-menus/',
        {
          name: menuData.name,
          description: menuData.description,
          is_active: true,
          display_order: Array.isArray(digitalMenus) ? digitalMenus.length : 0,
        }
      );
      const newMenu = response.data;

      // Invalidate cache after creating new menu
      invalidateAfterMenuOperation();

      // Update digital menus list
      setDigitalMenus(
        (prev: MenuTypes.DigitalMenu[]) => [...prev, newMenu]
      );

      // Auto-select the new menu
      setSelectedMenu(newMenu);
      localStorage.setItem(getSelectedMenuKey(), newMenu.id);

      return newMenu;
    } catch (error) {
      console.error('Error creating digital menu:', error);
      throw error;
    }
  };

  const selectDigitalMenu = async (menu: MenuTypes.DigitalMenu | null) => {
    const operationId = `select-menu-${menu?.id || 'null'}-${Date.now()}`;

    // Cancel any pending operations and start new one
    const controller = startOperation(operationId);

    try {
      // Clear current data FIRST to prevent showing wrong data
      setCategories([]);
      setItems([]);
      setLoading(true);

      // Update selected menu after clearing data
      setSelectedMenu(menu);

      if (menu) {
        localStorage.setItem(getSelectedMenuKey(), menu.id);

        // Load data for the new menu with race condition protection
        await Promise.all([
          fetchCategoriesWithProtection(menu, operationId, controller.signal),
          fetchItemsWithProtection(menu, operationId, controller.signal)
        ]);
      } else {
        localStorage.removeItem(getSelectedMenuKey());
        setLoading(false);
      }
    } catch (error) {
      // Only handle error if operation is still valid (not aborted)
      if (isOperationValid(operationId)) {
        console.error('Error loading menu data:', error);
        setError('Erro ao carregar dados do menu');
        setLoading(false);
      }
    }
  };

  const updateDigitalMenu = async (
    menuId: string,
    menuData: Partial<MenuTypes.DigitalMenu>
  ): Promise<MenuTypes.DigitalMenu> => {
    if (!isAuthenticated || !currentTenant) {
      throw new Error('Authentication required');
    }

    const response = await apiClient.put<MenuTypes.DigitalMenu>(
      `/modules/restaurants/menu/digital-menus/${menuId}`,
      menuData
    );
    const updatedMenu = response.data;

    // Invalidate cache after updating menu
    invalidateAfterMenuOperation();

    setDigitalMenus((prev: MenuTypes.DigitalMenu[]) =>
      prev.map((menu: MenuTypes.DigitalMenu) =>
        menu.id === menuId ? updatedMenu : menu
      )
    );

    // Update selected menu if it's the one being updated
    if (selectedMenu?.id === menuId) {
      setSelectedMenu(updatedMenu);
    }

    return updatedMenu;
  };

  const deleteDigitalMenu = async (menuId: string): Promise<void> => {
    if (!isAuthenticated || !currentTenant) {
      throw new Error('Authentication required');
    }

    await apiClient.delete(`/modules/restaurants/menu/digital-menus/${menuId}`);

    // Invalidate cache after deleting menu
    invalidateAfterMenuOperation();

    // Remove from digital menus list
    setDigitalMenus((prev: MenuTypes.DigitalMenu[]) =>
      prev.filter((menu: MenuTypes.DigitalMenu) => menu.id !== menuId)
    );

    // If the deleted menu was selected, clear selection and data
    if (selectedMenu?.id === menuId) {
      setSelectedMenu(null);
      setCategories([]);
      setItems([]);
      localStorage.removeItem(getSelectedMenuKey());
    }
  };

  // Fetch categories
  // Protected version for race condition prevention
  const fetchCategoriesWithProtection = async (
    menu: MenuTypes.DigitalMenu,
    operationId: string,
    signal: AbortSignal
  ) => {
    // Always use the provided menu, never fall back to selectedMenu to prevent race conditions
    if (!menu || !isAuthenticated || !currentTenant) {
      throw new Error('Menu, authentication, or tenant required');
    }

    setLoading(true);

    try {
      const data = await menuCategoryService.getCategories({
        digital_menu_id: menu.id,
        include_children: true,
        include_items: false,
      }, { signal }); // Pass abort signal

      // Check if operation is still valid before processing
      if (!isOperationValid(operationId)) {
        return;
      }

      // Validate response data
      if (!data || !Array.isArray(data)) {
        // Set empty array instead of throwing error to prevent crashes
        if (isOperationValid(operationId)) {
          setCategories([]);
        }
        return;
      }

      // Build hierarchical structure
      const topLevelCategories: any[] = [];
      const categoryMap = new Map();

      // First pass: create all categories
      data.forEach((cat: any) => {
        categoryMap.set(cat.id, { ...cat, children: [] });
      });

      // Second pass: build hierarchy
      data.forEach((cat: any) => {
        if (cat.parent_category_id) {
          const parent = categoryMap.get(cat.parent_category_id);
          if (parent) {
            parent.children.push(categoryMap.get(cat.id));
          }
        } else {
          topLevelCategories.push(categoryMap.get(cat.id));
        }
      });

      const allCategories = Array.from(categoryMap.values());

      // Final check before setting state - ensure operation is still valid and menu matches
      if (isOperationValid(operationId) && selectedMenu?.id === menu.id) {
        setCategories(allCategories as MenuTypes.MenuCategory[]);
      }
    } catch (err: any) {
      if (err?.name === 'AbortError') {
        return;
      }

      if (isOperationValid(operationId)) {
        console.error('Error loading categories:', err);
        setError('Erro ao carregar categorias');
        setCategories([]);
      }
    } finally {
      if (isOperationValid(operationId)) {
        setLoading(false);
      }
    }
  };

  const fetchCategories = async (menu?: MenuTypes.DigitalMenu) => {
    const targetMenu = menu || selectedMenu;
    if (!isAuthenticated || !currentTenant || !targetMenu) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Fetch all categories first with children included
      const data = await menuCategoryService.getCategories({
        digital_menu_id: targetMenu.id,
        include_children: true,
        include_items: false
      });

      // Validate data structure
      if (!Array.isArray(data)) {
        throw new Error('Invalid categories data format');
      }

      // Check if default category exists, if not, create it
      const hasDefaultCategory = data.some(cat =>
        menuCategoryService.isDefaultCategory(cat)
      );

      let finalData = data;
      if (!hasDefaultCategory) {
        await menuCategoryService.ensureDefaultCategory(targetMenu.id);
        // Fetch again to get the newly created default category
        finalData = await menuCategoryService.getCategories({
          digital_menu_id: targetMenu.id,
          include_children: true,
          include_items: false
        });
      }
      
      // Build parent-child relationships on frontend with improved logic
      const categoriesMap = new Map<string, MenuTypes.MenuCategory>();
      const topLevelCategories: MenuTypes.MenuCategory[] = [];
      const allCategories: MenuTypes.MenuCategory[] = [];

      // Initialize all categories with empty children array
      finalData.forEach(cat => {
        const category = { ...cat, children: [] };
        categoriesMap.set(cat.id, category);
        allCategories.push(category);
      });

      // Build parent-child relationships
      finalData.forEach(category => {
        const currentCategory = categoriesMap.get(category.id)!;
        if (category.parent_id && categoriesMap.has(category.parent_id)) {
          // Add as child to parent
          const parent = categoriesMap.get(category.parent_id)!;
          if (!parent.children) parent.children = [];
          parent.children.push(currentCategory);
        } else {
          // Top level category
          topLevelCategories.push(currentCategory);
        }
      });

      // Sort all categories and their children by display_order
      const sortCategories = (categories: MenuTypes.MenuCategory[]) => {
        categories.sort((a, b) => (a.display_order ?? 999) - (b.display_order ?? 999));
        categories.forEach(category => {
          if (category.children && category.children.length > 0) {
            sortCategories(category.children);
          }
        });
      };

      sortCategories(allCategories);

      setCategories(allCategories as MenuTypes.MenuCategory[]);
    } catch (err) {
      console.error('Error loading categories:', err);
      setError('Erro ao carregar categorias');
    } finally {
      setLoading(false);
    }
  };

  // Protected version for race condition prevention
  const fetchItemsWithProtection = async (
    menu: MenuTypes.DigitalMenu,
    operationId: string,
    signal: AbortSignal
  ) => {
    if (!isAuthenticated || !currentTenant || !menu) {
      throw new Error('Menu, authentication, or tenant required');
    }

    setLoading(true);

    try {
      const data = await menuService.getItems({
        digital_menu_id: menu.id,
        include_details: true,
      }, { signal }); // Pass abort signal

      // Check if operation is still valid before processing
      if (!isOperationValid(operationId)) {
        return;
      }

      // Validate response data
      if (!data || !Array.isArray(data)) {
        // Set empty array instead of throwing error to prevent crashes
        if (isOperationValid(operationId)) {
          setItems([]);
        }
        return;
      }

      const processedItems = data.map(processMenuItem);

      // Final check before setting state - ensure operation is still valid and menu matches
      if (isOperationValid(operationId) && selectedMenu?.id === menu.id) {
        setItems(processedItems);
      }
    } catch (err: any) {
      if (err?.name === 'AbortError') {
        return;
      }

      if (isOperationValid(operationId)) {
        console.error('Error loading items:', err);
        setError('Erro ao carregar itens');
        setItems([]);
      }
    } finally {
      if (isOperationValid(operationId)) {
        setLoading(false);
      }
    }
  };

  // Fetch items
  const fetchItems = async (menu?: MenuTypes.DigitalMenu) => {
    const targetMenu = menu || selectedMenu;
    if (!isAuthenticated || !currentTenant || !targetMenu) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const data = await menuService.getItems({
        digital_menu_id: targetMenu.id,
        include_details: true, // CRITICAL: Include variant/modifier/optional groups with options
      });

      // Validate data structure
      if (!Array.isArray(data)) {
        throw new Error('Invalid items data format');
      }

      const processedItems = data.map(processMenuItem);
      setItems(processedItems);
    } catch (err) {
      console.error('Error loading items:', err);
      setError('Erro ao carregar itens');
    } finally {
      setLoading(false);
    }
  };

  return {
    // Digital menu operations
    loadDigitalMenus,
    createDigitalMenu,
    selectDigitalMenu,
    updateDigitalMenu,
    deleteDigitalMenu,

    // Data fetching
    fetchCategories,
    fetchItems,
  };
} 