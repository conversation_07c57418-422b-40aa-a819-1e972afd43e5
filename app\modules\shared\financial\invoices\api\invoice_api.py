"""API endpoints for managing invoices."""

import uuid
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_db
from app.modules.core.auth.dependencies.auth_dependencies import get_current_active_user
from app.modules.core.tenants.dependencies.tenant_dependencies import get_current_tenant_from_header
from app.modules.core.users.models.user import User
from ..services.invoice_service import InvoiceService
from ..schemas.invoice import InvoiceCreate, InvoiceUpdate, InvoiceRead

router = APIRouter()

@router.post("/", response_model=InvoiceRead, status_code=status.HTTP_201_CREATED)
async def create_invoice(
    invoice_data: InvoiceCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    tenant_id: uuid.UUID = Depends(get_current_tenant_from_header),
):
    service = InvoiceService(db)
    return await service.create_invoice(
        invoice_data=invoice_data, tenant_id=tenant_id, user_id=current_user.id
    )

@router.get("/", response_model=List[InvoiceRead])
async def get_invoices(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    tenant_id: uuid.UUID = Depends(get_current_tenant_from_header),
):
    service = InvoiceService(db)
    return await service.get_invoices(
        tenant_id=tenant_id, skip=skip, limit=limit, status=status
    )

@router.get("/{invoice_id}", response_model=InvoiceRead)
async def get_invoice(
    invoice_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    tenant_id: uuid.UUID = Depends(get_current_tenant_from_header),
):
    service = InvoiceService(db)
    invoice = await service.get_invoice_by_id(invoice_id=invoice_id, tenant_id=tenant_id)
    if not invoice:
        raise HTTPException(status_code=404, detail="Invoice not found")
    return invoice

@router.put("/{invoice_id}", response_model=InvoiceRead)
async def update_invoice(
    invoice_id: uuid.UUID,
    invoice_data: InvoiceUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    tenant_id: uuid.UUID = Depends(get_current_tenant_from_header),
):
    service = InvoiceService(db)
    return await service.update_invoice(
        invoice_id=invoice_id, invoice_data=invoice_data, tenant_id=tenant_id
    )

@router.delete("/{invoice_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_invoice(
    invoice_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    tenant_id: uuid.UUID = Depends(get_current_tenant_from_header),
):
    service = InvoiceService(db)
    if not await service.delete_invoice(invoice_id=invoice_id, tenant_id=tenant_id):
        raise HTTPException(status_code=404, detail="Invoice not found")
    return {"ok": True}

@router.post("/{invoice_id}/generate-pdf", status_code=status.HTTP_200_OK)
async def generate_invoice_pdf(
    invoice_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    tenant_id: uuid.UUID = Depends(get_current_tenant_from_header),
):
    service = InvoiceService(db)
    pdf_path = await service.generate_invoice_pdf(invoice_id=invoice_id, tenant_id=tenant_id)
    if not pdf_path:
        raise HTTPException(status_code=404, detail="Invoice not found")
    return {"pdf_path": pdf_path}