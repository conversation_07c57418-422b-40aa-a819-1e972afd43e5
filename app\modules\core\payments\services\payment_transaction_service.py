"""Service for managing payment transactions."""

import uuid  # noqa: E402
from typing import List, Optional, Tuple
from datetime import datetime
from decimal import Decimal
from fastapi import HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, or_
from sqlalchemy.orm import joinedload

from app.modules.core.payments.models.payment_transaction import (  # noqa: E402
    PaymentTransaction,
    PaymentRefund,
    PaymentStatus,
)
from app.modules.core.payments.schemas.payment_transaction import (  # noqa: E402
    PaymentTransactionCreate,
    PaymentTransactionUpdate,
    PaymentRefundCreate,
    PaymentRefundUpdate,
)
from app.core.exceptions import BusinessLogicError  # noqa: E402


class PaymentTransactionService:
    """Service for managing payment transactions."""

    async def get(
        self, db: AsyncSession, id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Optional[PaymentTransaction]:
        """
        Get a payment transaction by ID, ensuring it belongs to the specified tenant.
        """
        result = await db.execute(
            select(PaymentTransaction)
            .options(
                joinedload(PaymentTransaction.method),
                joinedload(PaymentTransaction.processor),
            )
            .where(PaymentTransaction.id == id, PaymentTransaction.tenant_id == tenant_id)
        )
        return result.scalars().first()

    async def get_multi(
        self,
        db: AsyncSession,
        *,
        tenant_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        status: Optional[str] = None,
        source_type: Optional[str] = None,
        source_id: Optional[uuid.UUID] = None,
        customer_id: Optional[uuid.UUID] = None,
        method_id: Optional[uuid.UUID] = None,
        processor_id: Optional[uuid.UUID] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        search: Optional[str] = None,
    ) -> List[PaymentTransaction]:
        """
        Get multiple payment transactions with optional filtering.
        """
        query = select(PaymentTransaction).where(PaymentTransaction.tenant_id == tenant_id)

        # Apply filters
        if status:
            query = query.where(PaymentTransaction.status == status)

        if source_type:
            query = query.where(PaymentTransaction.source_type == source_type)

        if source_id:
            query = query.where(PaymentTransaction.source_id == source_id)

        if customer_id:
            query = query.where(PaymentTransaction.customer_id == customer_id)

        if method_id:
            query = query.where(PaymentTransaction.method_id == method_id)

        if processor_id:
            query = query.where(PaymentTransaction.processor_id == processor_id)

        if start_date:
            query = query.where(PaymentTransaction.created_at >= start_date)

        if end_date:
            query = query.where(PaymentTransaction.created_at <= end_date)

        if search:
            search_term = f"%{search}%"
            query = query.where(
                or_(
                    PaymentTransaction.external_id.ilike(search_term),
                    PaymentTransaction.external_reference.ilike(search_term),
                    PaymentTransaction.customer_email.ilike(search_term),
                    PaymentTransaction.customer_name.ilike(search_term),
                    PaymentTransaction.description.ilike(search_term),
                )
            )

        # Order by created_at descending and apply pagination
        query = query.order_by(PaymentTransaction.created_at.desc()).offset(skip).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    async def create(
        self,
        db: AsyncSession,
        *,
        tenant_id: uuid.UUID,
        obj_in: PaymentTransactionCreate,
    ) -> PaymentTransaction:
        """
        Create a new payment transaction.
        """
        # Create the transaction
        create_data = obj_in.model_dump()

        # Remove None values for optional foreign keys
        for field in ["processor_id", "method_id"]:
            if field in create_data and create_data[field] is None:
                del create_data[field]

        db_obj = PaymentTransaction(tenant_id=tenant_id, **create_data)

        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)

        return db_obj

    async def update(
        self,
        db: AsyncSession,
        *,
        id: uuid.UUID,
        tenant_id: uuid.UUID,
        obj_in: PaymentTransactionUpdate,
    ) -> Optional[PaymentTransaction]:
        """
        Update an existing payment transaction.
        """
        transaction = await self.get(db, id, tenant_id)

        if not transaction:
            return None

        # Update the transaction
        update_data = obj_in.model_dump(exclude_unset=True)

        for field, value in update_data.items():
            setattr(transaction, field, value)

        db.add(transaction)
        await db.commit()
        await db.refresh(transaction)

        return transaction

    async def get_transaction_with_refunds(
        self, db: AsyncSession, *, id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Tuple[
        Optional[PaymentTransaction],
        List[PaymentRefund],
        Optional[Decimal],
        Optional[Decimal],
    ]:
        """
        Get a payment transaction with its refunds and calculate refund totals.

        Returns:
        - The transaction
        - List of refunds
        - Total refunded amount
        - Remaining amount available for refund
        """
        transaction = await self.get(db, id, tenant_id)

        if not transaction:
            return None, [], None, None

        # Get refunds
        result = await db.execute(
            select(PaymentRefund)
            .where(PaymentRefund.transaction_id == id, PaymentRefund.tenant_id == tenant_id)
            .order_by(PaymentRefund.created_at.desc())
        )
        refunds = result.scalars().all()

        # Calculate refund totals
        result = await db.execute(
            select(func.sum(PaymentRefund.amount)).where(
                PaymentRefund.transaction_id == id,
                PaymentRefund.tenant_id == tenant_id,
                PaymentRefund.status.in_([PaymentStatus.COMPLETED, PaymentStatus.PROCESSING]),
            )
        )
        refunded_amount = result.scalar_one_or_none() or Decimal("0.00")

        # Calculate remaining amount
        remaining_amount = transaction.amount - refunded_amount

        return transaction, refunds, refunded_amount, remaining_amount

    async def create_refund(
        self,
        db: AsyncSession,
        *,
        transaction_id: uuid.UUID,
        tenant_id: uuid.UUID,
        obj_in: PaymentRefundCreate,
        created_by: Optional[uuid.UUID] = None,
    ) -> PaymentRefund:
        """
        Create a new payment refund.

        Validates:
        - The transaction exists and belongs to the tenant
        - The refund amount is valid (not greater than the remaining amount)
        - The transaction status allows refunds
        """
        # Get transaction with refunds
        transaction, refunds, refunded_amount, remaining_amount = (
            await self.get_transaction_with_refunds(db, id=transaction_id, tenant_id=tenant_id)
        )

        if not transaction:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Transaction with id {transaction_id} not found or does not belong to the tenant.",  # noqa: E501
            )

        # Validate transaction status
        if transaction.status not in [
            PaymentStatus.COMPLETED,
            PaymentStatus.PARTIALLY_REFUNDED,
        ]:
            raise BusinessLogicError(
                f"Cannot refund transaction with status {transaction.status}. "
                f"Transaction must be in COMPLETED or PARTIALLY_REFUNDED status."
            )

        # Validate refund amount
        refund_amount = obj_in.amount
        if refund_amount > remaining_amount:
            raise BusinessLogicError(
                f"Refund amount {refund_amount} exceeds the remaining amount {remaining_amount}."
            )

        # Create the refund
        db_obj = PaymentRefund(
            transaction_id=transaction_id,
            tenant_id=tenant_id,
            created_by=created_by,
            **obj_in.model_dump(),
        )

        db.add(db_obj)

        # Update transaction status
        if refund_amount == transaction.amount:
            transaction.status = PaymentStatus.REFUNDED
        else:
            transaction.status = PaymentStatus.PARTIALLY_REFUNDED

        db.add(transaction)

        await db.commit()
        await db.refresh(db_obj)

        return db_obj

    async def update_refund(
        self,
        db: AsyncSession,
        *,
        refund_id: uuid.UUID,
        tenant_id: uuid.UUID,
        obj_in: PaymentRefundUpdate,
    ) -> Optional[PaymentRefund]:
        """
        Update an existing payment refund.
        """
        # Get the refund
        result = await db.execute(
            select(PaymentRefund).where(
                PaymentRefund.id == refund_id, PaymentRefund.tenant_id == tenant_id
            )
        )
        refund = result.scalars().first()

        if not refund:
            return None

        # Update the refund
        update_data = obj_in.model_dump(exclude_unset=True)

        for field, value in update_data.items():
            setattr(refund, field, value)

        db.add(refund)

        # If status is changing to COMPLETED, update transaction status
        if "status" in update_data and update_data["status"] == PaymentStatus.COMPLETED:
            # Get transaction with refunds
            transaction, refunds, refunded_amount, remaining_amount = (
                await self.get_transaction_with_refunds(
                    db, id=refund.transaction_id, tenant_id=tenant_id
                )
            )

            if transaction:
                # Update transaction status based on refund totals
                if refunded_amount >= transaction.amount:
                    transaction.status = PaymentStatus.REFUNDED
                else:
                    transaction.status = PaymentStatus.PARTIALLY_REFUNDED

                db.add(transaction)

        await db.commit()
        await db.refresh(refund)

        return refund


# Instance of the service to be used in endpoints
payment_transaction_service = PaymentTransactionService()
