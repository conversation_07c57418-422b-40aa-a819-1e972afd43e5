# Modificado para usar AsyncSession e operações async
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select  # Removed unused import: delete
from sqlalchemy.exc import IntegrityError
from uuid import UUID
from typing import List, Optional
from datetime import datetime, timezone

# Removed unused import: import dns.resolver
import logging  # Para logs
import os  # Para manipulação de arquivos/paths
import pathlib  # Para manipulação de paths
import aiofiles  # Para escrita assíncrona de arquivos
import httpx  # Para verificação de SSL

# Importar Enums e Serviço de Assinatura
from . import models, schemas
from app.modules.subscriptions.services import (
    tenant_has_feature,
)  # Importar a função específica

# Removed unused import: from app.models.tenant import Tenant
from app.core.config import settings  # Para obter configurações  # noqa: E402

# Import for email integration
from app.modules.shared.email.services.provision_service import ProvisionService  # noqa: E402
from app.modules.shared.email.models import EmailDomain

logger = logging.getLogger(__name__)

# TODO: Mover para settings ou uma configuração mais robusta
TRAEFIK_DYNAMIC_CONFIG_PATH = (
    pathlib.Path(settings.TRAEFIK_DYNAMIC_CONFIG_PATH)
    if hasattr(settings, "TRAEFIK_DYNAMIC_CONFIG_PATH")
    else pathlib.Path("./traefik_dynamic_config")
)
# Garante que o diretório exista
TRAEFIK_DYNAMIC_CONFIG_PATH.mkdir(parents=True, exist_ok=True)


class CustomDomainService:

    def __init__(self):
        """Initialize the service."""
        self.email_provision_service = ProvisionService()

    # --- Funções auxiliares para Traefik (Implementação inicial/placeholders) ---

    def _get_traefik_config_filename(self, domain_name: str) -> str:
        """Gera um nome de arquivo seguro para a configuração do Traefik."""
        # Substituir '.' por '_' ou usar um hash para evitar problemas com nomes de arquivo
        safe_name = domain_name.replace(".", "_")
        return f"{safe_name}.toml"  # Usando TOML como exemplo

    async def _generate_traefik_config(self, domain: models.CustomDomain) -> str:
        """Gera o conteúdo da configuração dinâmica do Traefik (TOML) para um domínio."""

        # Sanitizar o nome do domínio para usar como chave no TOML/Traefik
        router_key = domain.domain_name.replace(".", "-")
        middleware_key = f"add-tenant-header-{router_key}"  # Nome único para o middleware

        # Nomes de serviços, resolvedores e middlewares (idealmente de settings)
        trix_service_name = "trix-app-service"
        cert_resolver_name = "letsencrypt"
        https_redirect_middleware = "https-redirect@file"

        logger.info(
            f"Gerando configuração Traefik TOML para {domain.domain_name} (Router Key: {router_key})"  # noqa: E501
        )

        config_content = f"""
# Configuração dinâmica para o domínio: {domain.domain_name}
# Gerado automaticamente por Trix - NÃO EDITE MANUALMENTE

[http.routers.{router_key}-websecure]
  rule = "Host(`{domain.domain_name}`)"
  service = "{trix_service_name}"
  entryPoints = ["websecure"]  # Ponto de entrada HTTPS
  middlewares = ["{middleware_key}"]  # Adiciona header customizado
  [http.routers.{router_key}-websecure.tls]
    certResolver = "{cert_resolver_name}"

[http.routers.{router_key}-web]
  rule = "Host(`{domain.domain_name}`)"
  service = "{trix_service_name}"
  entryPoints = ["web"]  # Ponto de entrada HTTP
  middlewares = ["{https_redirect_middleware}", "{middleware_key}"]  # Redireciona para HTTPS e adiciona header  # noqa: E501

# Middleware para adicionar header com o domínio (para identificação do tenant no backend)
[http.middlewares.{middleware_key}.headers]
  [http.middlewares.{middleware_key}.headers.customRequestHeaders]
    X-Trix-Tenant-Domain = "{domain.domain_name}"
    # Poderíamos adicionar X-Trix-Tenant-Id = "{domain.tenant_id}" também, se útil

"""
        # NOTAS IMPORTANTES:
        # 1. O serviço '{trix_service_name}' DEVE ser definido na configuração estática do Traefik
        #    ou em outro arquivo de configuração dinâmica, apontando para o(s) container(s)
        #    da aplicação Trix FastAPI/Uvicorn. Exemplo:
        #    [http.services]
        #      [http.services.{trix_service_name}.loadBalancer]
        #        [[http.services.{trix_service_name}.loadBalancer.servers]]
        #          url = "http://app:8000" # 'app' é o nome do serviço no docker-compose/k8s
        #
        # 2. O middleware '{https_redirect_middleware}' DEVE ser definido também. Exemplo:
        #    [http.middlewares]
        #      [http.middlewares.https-redirect.redirectScheme]
        #        scheme = "https"
        #        permanent = true # Usar redirecionamento permanente (301)
        #
        # 3. Os entryPoints 'web' (porta 80) e 'websecure' (porta 443) devem estar definidos
        # na configuração estática do Traefik, assim como o resolvedor ACME
        # '{cert_resolver_name}'.

        return config_content.strip()  # Remover espaços extras no início/fim

    async def _write_traefik_config(self, domain_name: str, config_content: str):
        """Escreve o arquivo de configuração do Traefik de forma assíncrona."""
        filename = self._get_traefik_config_filename(domain_name)
        filepath = TRAEFIK_DYNAMIC_CONFIG_PATH / filename
        try:
            async with aiofiles.open(filepath, mode="w") as f:
                await f.write(config_content)
            logger.info(f"Configuração Traefik escrita para {domain_name} em {filepath}")
        except Exception as e:
            logger.error(f"Erro ao escrever configuração Traefik para {domain_name}: {e}")
            raise  # Re-lançar a exceção para tratamento superior

    async def _remove_traefik_config(self, domain_name: str):
        """Remove o arquivo de configuração do Traefik de forma assíncrona."""
        filename = self._get_traefik_config_filename(domain_name)
        filepath = TRAEFIK_DYNAMIC_CONFIG_PATH / filename
        try:
            if filepath.exists():
                # os.remove é síncrono, mas geralmente rápido para arquivos pequenos.
                os.remove(filepath)
                # Poderia usar aiofiles se necessário, mas requer mais
                # tratamento.
                logger.info(f"Configuração Traefik removida para {domain_name} de {filepath}")
            else:
                logger.warning(
                    f"Arquivo de configuração Traefik não encontrado para remoção: {filepath}"
                )
        except Exception as e:
            logger.error(f"Erro ao remover configuração Traefik para {domain_name}: {e}")
            # Não relançar necessariamente, pois o domínio pode já ter sido removido
            # ou o erro pode ser de permissão, etc. Log é importante.

    async def _check_domain_ssl_status(self, domain_name: str) -> bool:
        """Verifica se o domínio está servindo HTTPS com certificado válido."""
        # TODO: Implementar verificação real.
        #       - Usar httpx para fazer GET em https://{domain_name}
        #       - Verificar se status code é 2xx
        #       - Verificar se o certificado não é auto-assinado (pode ser complexo, talvez apenas verificar conexão bem-sucedida inicialmente)  # noqa: E501
        logger.info(f"Placeholder: Verificando status SSL para {domain_name}")
        try:
            # verify=True é o padrão e importante
            async with httpx.AsyncClient(verify=True) as client:
                response = await client.get(f"https://{domain_name}", timeout=10.0)
                # Verifica se a resposta foi bem-sucedida (status 2xx)
                # A própria conexão HTTPS já valida a cadeia de certificados
                if 200 <= response.status_code < 300:
                    logger.info(
                        f"Verificação SSL bem-sucedida para {domain_name} (Status: {response.status_code})"  # noqa: E501
                    )
                    return True
                else:
                    logger.warning(
                        f"Verificação SSL falhou para {domain_name} (Status: {response.status_code})"  # noqa: E501
                    )
                    return False
        except httpx.RequestError as e:
            logger.warning(f"Erro de conexão durante verificação SSL para {domain_name}: {e}")
            return False
        except Exception as e:
            logger.error(f"Erro inesperado durante verificação SSL para {domain_name}: {e}")
            return False

    # --- Métodos existentes (podem precisar de ajustes) ---
    async def get_custom_domain_by_id(
        self, db: AsyncSession, domain_id: UUID
    ) -> Optional[models.CustomDomain]:
        result = await db.execute(
            select(models.CustomDomain).filter(models.CustomDomain.id == domain_id)
        )
        return result.scalars().first()

    async def get_custom_domain_by_name(
        self, db: AsyncSession, domain_name: str
    ) -> Optional[models.CustomDomain]:
        # Garante que a comparação seja case-insensitive e remova espaços extras
        result = await db.execute(
            select(models.CustomDomain).filter(
                models.CustomDomain.domain_name == domain_name.lower().strip()
            )
        )
        return result.scalars().first()

    async def get_custom_domains_by_tenant(
        self, db: AsyncSession, tenant_id: UUID, skip: int = 0, limit: int = 100
    ) -> List[models.CustomDomain]:
        result = await db.execute(
            select(models.CustomDomain)
            .filter(models.CustomDomain.tenant_id == tenant_id)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def create_custom_domain(
        self, db: AsyncSession, domain_in: schemas.CustomDomainCreate, tenant_id: UUID
    ) -> models.CustomDomain:
        if not domain_in.domain_name or "." not in domain_in.domain_name:
            raise ValueError("Invalid domain name format.")

        # Gerar cname_target
        # Usar o tenant_id diretamente para garantir unicidade e simplicidade.
        # O domínio base deve vir de uma configuração.
        # Ex: 123e4567-e89b-12d3-a456-************.trix-sites.com
        cname_target_base = settings.CNAME_TARGET_BASE_DOMAIN  # Ex: "trix-sites.com"
        if not cname_target_base:
            logger.error("CNAME_TARGET_BASE_DOMAIN is not configured.")
            raise ValueError("System configuration error: CNAME target base domain not set.")

        cname_target = f"{str(tenant_id)}.{cname_target_base}"

        db_domain = models.CustomDomain(
            domain_name=domain_in.domain_name.lower().strip(),
            tenant_id=tenant_id,
            frontend_type=domain_in.frontend_type,
            cname_target=cname_target,  # Renomeado e valor ajustado
            verification_status=models.VerificationStatus.PENDING,  # Renomeado e usando Enum
            is_active=False,  # Permanece False até todas as verificações passarem
        )

        try:
            db.add(db_domain)
            await db.commit()
            await db.refresh(db_domain)

            # Create an EmailDomain for this CustomDomain
            try:
                await self.email_provision_service.create_email_domain_from_custom_domain(
                    db, db_domain
                )
                logger.info(f"Created EmailDomain for CustomDomain {db_domain.domain_name}")
            except Exception as email_error:
                # Log the error but don't fail the CustomDomain creation
                logger.error(
                    f"Error creating EmailDomain for CustomDomain {db_domain.domain_name}: {email_error}"  # noqa: E501
                )
        except IntegrityError as e:
            await db.rollback()
            logger.error(
                f"Integrity error creating custom domain {domain_in.domain_name} for tenant {tenant_id}: {e}"  # noqa: E501
            )
            # Verificar se é por duplicação de domain_name - precisa ser async agora
            existing_domain = await self.get_custom_domain_by_name(
                db, domain_in.domain_name.lower().strip()
            )
            if existing_domain:
                raise ValueError(f"Domain '{domain_in.domain_name}' already exists.")
            raise ValueError(
                f"Could not create domain '{domain_in.domain_name}'. An integrity constraint failed."  # noqa: E501
            )
        return db_domain

    async def update_custom_domain_verification_status(
        self,
        db: AsyncSession,
        domain_id: UUID,
        verification_status: models.VerificationStatus,
        is_active: Optional[bool] = None,
    ) -> Optional[models.CustomDomain]:
        db_domain = await self.get_custom_domain_by_id(db, domain_id)
        if db_domain:
            db_domain.verification_status = verification_status
            if is_active is not None:  # Geralmente, is_active só se torna True após SSL também
                db_domain.is_active = is_active

            # Atualizar timestamps e contadores de tentativa
            db_domain.last_dns_validation_at = datetime.now(timezone.utc)
            # dns_validation_attempts é incrementado na função de verificação

            await db.commit()
            await db.refresh(db_domain)
        return db_domain

    async def verify_domain_dns(self, db: AsyncSession, domain_id: UUID) -> models.CustomDomain:
        """
        Verifies the DNS CNAME record for a custom domain.
        Updates the domain's verification_status.
        """
        # Usar get_custom_domain_by_id assíncrono
        db_domain = await self.get_custom_domain_by_id(db, domain_id)
        if not db_domain:
            raise ValueError(f"Custom domain with id {domain_id} not found.")

        db_domain.dns_validation_attempts = (db_domain.dns_validation_attempts or 0) + 1
        db_domain.last_dns_validation_at = datetime.now(timezone.utc)

        current_verification_status = db_domain.verification_status

        try:
            logger.info(
                f"Verifying DNS for domain: {db_domain.domain_name}, expecting CNAME: {db_domain.cname_target}"  # noqa: E501
            )
            # Usar resolvedor async
            import dns.asyncresolver  # noqa: E402

            resolver = dns.asyncresolver.Resolver()
            answers = await resolver.resolve(db_domain.domain_name, "CNAME")

            verified = False
            for rdata in answers:
                # rdata.target é um objeto dns.name.Name, converter para string e remover
                # o ponto final.
                cname_value = str(rdata.target).rstrip(".")
                logger.debug(f"Found CNAME: {cname_value} for {db_domain.domain_name}")
                if cname_value.lower() == db_domain.cname_target.lower():
                    verified = True
                    break

            if verified:
                logger.info(f"DNS verification successful for {db_domain.domain_name}")
                db_domain.verification_status = models.VerificationStatus.VERIFIED
                # Não ativar (is_active=True) aqui, pois SSL ainda está pendente.
                # A ativação geral (is_active) deve ocorrer após a verificação SSL.
            else:
                logger.warning(
                    f"DNS CNAME record for {db_domain.domain_name} does not match expected target {db_domain.cname_target}. Found: {[str(r.target).rstrip('.') for r in answers]}"  # noqa: E501
                )
                db_domain.verification_status = models.VerificationStatus.FAILED

        except dns.resolver.NXDOMAIN:
            logger.warning(f"DNS record not found (NXDOMAIN) for {db_domain.domain_name}")
            db_domain.verification_status = models.VerificationStatus.FAILED
        except dns.resolver.NoAnswer:
            logger.warning(f"No CNAME record found (NoAnswer) for {db_domain.domain_name}")
            db_domain.verification_status = models.VerificationStatus.FAILED
        except dns.exception.DNSException as e:
            logger.error(f"DNS resolution error for {db_domain.domain_name}: {e}")
            db_domain.verification_status = models.VerificationStatus.FAILED

        if db_domain.verification_status != current_verification_status:
            await db.commit()
            await db.refresh(db_domain)
        else:  # Apenas atualizar os campos de tentativa e timestamp
            await db.commit()
            # Refresh pode não ser estritamente necessário se apenas timestamps foram atualizados,
            # mas é mais seguro para garantir que o objeto retornado esteja sincronizado.
            await db.refresh(db_domain)

        # Após verificar o DNS com sucesso, tentar configurar o proxy
        if db_domain.verification_status == models.VerificationStatus.VERIFIED:
            try:
                # Usar await aqui, pois configure_domain_proxy é async
                await self.configure_domain_proxy(db=db, domain=db_domain)
            except Exception as e:
                # Logar o erro mas não impedir o retorno do status de verificação DNS
                logger.error(
                    f"Erro ao iniciar configuração do proxy para {db_domain.domain_name} após verificação DNS: {e}"  # noqa: E501
                )

            # Also verify EmailDomain DNS records if they exist
            try:
                # Get the EmailDomain for this CustomDomain
                result = await db.execute(
                    select(EmailDomain).filter(EmailDomain.custom_domain_id == db_domain.id)
                )
                email_domain = result.scalars().first()
                if email_domain:
                    # Generate DNS records for the EmailDomain
                    dns_records = await self.email_provision_service.generate_email_dns_records(
                        db, email_domain.id
                    )
                    logger.info(
                        f"Generated DNS records for EmailDomain {email_domain.domain_name}: {dns_records}"  # noqa: E501
                    )
            except Exception as email_error:
                # Log the error but don't fail the CustomDomain verification
                logger.error(
                    f"Error generating DNS records for EmailDomain of CustomDomain {db_domain.domain_name}: {email_error}"  # noqa: E501
                )

        return db_domain

    # --- Métodos para gerenciamento da configuração do Proxy (Traefik) ---

    async def configure_domain_proxy(self, db: AsyncSession, domain: models.CustomDomain):
        """Verifica assinatura, gera e escreve a configuração do Traefik para um domínio verificado."""  # noqa: E501
        logger.info(
            f"Iniciando configuração de proxy para o domínio {domain.domain_name} (ID: {domain.id})"
        )

        # Garantir que o domínio esteja verificado
        if domain.verification_status != models.VerificationStatus.VERIFIED:
            logger.warning(
                f"Tentativa de configurar proxy para domínio não verificado: {domain.domain_name}"
            )
            # Garantir que o status esteja correto se chamado indevidamente
            if domain.proxy_config_status != models.ProxyConfigStatus.NOT_CONFIGURED:
                domain.proxy_config_status = models.ProxyConfigStatus.NOT_CONFIGURED
                domain.ssl_status = models.SslStatus.DISABLED  # Resetar SSL também
                domain.is_active = False
                await db.commit()
            return

        # 1. Verificar Assinatura/Feature
        try:
            # TODO: Definir o nome exato da feature flag nas configurações
            feature_name = "feature_full_custom_domain_automation"
            # Usar a função importada e o parâmetro correto (feature_key)
            has_feature = await tenant_has_feature(
                db, tenant_id=domain.tenant_id, feature_key=feature_name
            )
            if not has_feature:
                logger.info(
                    f"Tenant {domain.tenant_id} não possui a feature '{feature_name}' ativa. Proxy para {domain.domain_name} não será configurado."  # noqa: E501
                )
                # Se já estava configurado e a feature foi removida, precisa remover a config
                if domain.proxy_config_status == models.ProxyConfigStatus.CONFIGURED:
                    await self.remove_domain_proxy_configuration(db, domain)
                # Garantir que não está configurado se estado for diferente de REMOVED
                elif domain.proxy_config_status != models.ProxyConfigStatus.REMOVED:
                    domain.proxy_config_status = models.ProxyConfigStatus.NOT_CONFIGURED
                    domain.ssl_status = models.SslStatus.DISABLED
                    domain.is_active = False
                    await db.commit()
                return
        except Exception as e:
            logger.error(
                f"Erro ao verificar feature de assinatura para tenant {domain.tenant_id} / domínio {domain.domain_name}: {e}"  # noqa: E501
            )
            # Tratar como se não tivesse a feature para segurança
            domain.proxy_config_status = (
                models.ProxyConfigStatus.ERROR_CONFIGURING
            )  # Ou um status específico?
            domain.ssl_status = models.SslStatus.DISABLED
            domain.is_active = False
            await db.commit()
            return

        # 2. Gerar e Escrever Configuração (se não estiver já configurado ou pendente de remoção)
        if domain.proxy_config_status in [
            models.ProxyConfigStatus.CONFIGURED,
            models.ProxyConfigStatus.PENDING_REMOVAL,
        ]:
            logger.info(
                f"Proxy para {domain.domain_name} já está {domain.proxy_config_status}. Nenhuma ação necessária."  # noqa: E501
            )
            # Se já configurado, talvez re-disparar verificação SSL se status não for
            # ACTIVE? Ou deixar para task agendada.
            return

        try:
            config_content = await self._generate_traefik_config(domain)
            await self._write_traefik_config(domain.domain_name, config_content)

            # Atualizar status no BD
            domain.proxy_config_status = models.ProxyConfigStatus.CONFIGURED
            domain.ssl_status = models.SslStatus.PENDING_ISSUANCE  # Inicia a tentativa de SSL
            domain.is_active = False  # Ainda não ativo, SSL pendente
            await db.commit()
            await db.refresh(domain)
            logger.info(
                f"Proxy para {domain.domain_name} configurado com sucesso. Status SSL: PENDING_ISSUANCE."  # noqa: E501
            )

            # TODO: Idealmente, iniciar a verificação de SSL aqui após um pequeno delay
            #       ou deixar para uma tarefa agendada separada.

        except Exception as e:
            logger.error(
                f"Falha ao gerar ou escrever configuração Traefik para {domain.domain_name}: {e}"
            )
            domain.proxy_config_status = models.ProxyConfigStatus.ERROR_CONFIGURING
            domain.ssl_status = (
                models.SslStatus.DISABLED
            )  # Falha na configuração do proxy impede SSL
            domain.is_active = False
            await db.commit()
            # Não relançar a exceção aqui, apenas logar e marcar o status de erro

    async def remove_domain_proxy_configuration(
        self, db: AsyncSession, domain: models.CustomDomain
    ):
        """Remove a configuração do Traefik e atualiza o status do domínio."""
        logger.info(
            f"Removendo configuração de proxy para o domínio {domain.domain_name} (ID: {domain.id})"
        )

        try:
            await self._remove_traefik_config(domain.domain_name)
        except Exception as e:
            # Logar o erro, mas continuar para atualizar o status no DB de qualquer forma
            logger.error(
                f"Erro durante a remoção do arquivo de configuração Traefik para {domain.domain_name}, mas o status será atualizado: {e}"  # noqa: E501
            )

        # Atualizar status no BD independentemente do sucesso da remoção do arquivo
        # Verificar se o domínio ainda existe no banco antes de tentar atualizar
        # (Pode ter sido deletado em outra transação entre a chamada e a execução)
        # Re-ler o domínio pode ser mais seguro aqui, mas aumenta a complexidade.
        # Por ora, confiamos que o objeto 'domain' ainda é válido.
        if domain.proxy_config_status != models.ProxyConfigStatus.REMOVED:
            domain.proxy_config_status = models.ProxyConfigStatus.REMOVED
            domain.ssl_status = models.SslStatus.DISABLED
            domain.is_active = False
            try:
                await db.commit()
                # Refresh pode falhar se o objeto foi deletado concorrentemente
                await db.refresh(domain)
                logger.info(
                    f"Status do domínio {domain.domain_name} atualizado para REMOVED/DISABLED."
                )
            except Exception as commit_exc:
                logger.error(
                    f"Erro ao commitar atualização de status para REMOVED/DISABLED para domínio {domain.domain_name}: {commit_exc}"  # noqa: E501
                )
                await db.rollback()  # Rollback em caso de erro no commit

    # Esta função parece redundante agora que verify_domain_dns atualiza os timestamps.
    # Comentar ou remover se não for mais necessária.
    # async def update_custom_domain_dns_info(
    #     self,
    #     db: AsyncSession,
    #     domain_id: UUID,
    #     validation_attempts: Optional[int] = None,
    #     last_validation_at: Optional[datetime] = None
    # ) -> Optional[models.CustomDomain]:
    #     db_domain = await self.get_custom_domain_by_id(db, domain_id)
    #     if db_domain:
    #         if validation_attempts is not None:
    #             db_domain.dns_validation_attempts = validation_attempts
    #         if last_validation_at is not None:
    #             db_domain.last_dns_validation_at = last_validation_at
    #         await db.commit()
    #         await db.refresh(db_domain)
    #     return db_domain

    async def update_custom_domain_ssl_info(
        self,
        db: AsyncSession,
        domain_id: UUID,
        ssl_certificate_id: Optional[str] = None,
        ssl_issued_at: Optional[datetime] = None,
        ssl_expires_at: Optional[datetime] = None,
        clear_ssl: bool = False,
        validation_attempts: Optional[int] = None,
        last_validation_at: Optional[datetime] = None,
    ) -> Optional[models.CustomDomain]:
        db_domain = await self.get_custom_domain_by_id(db, domain_id)
        if db_domain:
            if validation_attempts is not None:
                db_domain.dns_validation_attempts = validation_attempts
            if last_validation_at is not None:
                db_domain.last_dns_validation_at = last_validation_at
            await db.commit()
            await db.refresh(db_domain)
        return db_domain

    # Removido método duplicado update_custom_domain_ssl_info

    async def delete_custom_domain(
        self, db: AsyncSession, domain_id: UUID
    ) -> Optional[models.CustomDomain]:
        db_domain = await self.get_custom_domain_by_id(db, domain_id)
        if db_domain:
            # 1. Tentar remover a configuração do proxy antes de deletar
            try:
                # Passar o objeto db_domain diretamente
                await self.remove_domain_proxy_configuration(db, db_domain)
            except Exception as e:
                # Logar erro na remoção da config, mas prosseguir com a deleção do DB
                logger.error(
                    f"Erro ao remover configuração do proxy durante a deleção do domínio {db_domain.domain_name}, mas a deleção do DB prosseguirá: {e}"  # noqa: E501
                )

            # 2. Deletar do banco de dados
            try:
                await db.delete(db_domain)
                await db.commit()
                logger.info(
                    f"Domínio {db_domain.domain_name} (ID: {domain_id}) deletado do banco de dados."
                )
                # Não podemos retornar db_domain aqui pois ele foi deletado e não pode ser refresh
                return None  # Ou retornar um status/mensagem de sucesso
            except Exception as delete_exc:
                logger.error(f"Erro ao deletar domínio {domain_id} do banco de dados: {delete_exc}")
                await db.rollback()
                raise  # Re-lançar a exceção da deleção
        return None  # Retorna None se não encontrou

    # --- Métodos para Tarefas Agendadas ---

    async def check_pending_ssl_domains(self, db: AsyncSession):
        """
        Método a ser chamado por uma tarefa agendada.
        Verifica o status SSL de domínios que estão pendentes de emissão.
        """
        logger.info("Iniciando tarefa de verificação de SSL para domínios pendentes.")

        # Buscar domínios configurados e com SSL pendente
        stmt = select(models.CustomDomain).where(
            models.CustomDomain.proxy_config_status == models.ProxyConfigStatus.CONFIGURED,
            models.CustomDomain.ssl_status == models.SslStatus.PENDING_ISSUANCE,
        )
        result = await db.execute(stmt)
        pending_domains = result.scalars().all()

        if not pending_domains:
            logger.info("Nenhum domínio com SSL pendente encontrado.")
            return

        logger.info(f"Encontrados {len(pending_domains)} domínios com SSL pendente para verificar.")

        for domain in pending_domains:
            logger.debug(f"Verificando SSL para {domain.domain_name} (ID: {domain.id})")
            try:
                ssl_ok = await self._check_domain_ssl_status(domain.domain_name)
                domain.last_ssl_check_at = datetime.now(timezone.utc)

                if ssl_ok:
                    logger.info(f"SSL confirmado para {domain.domain_name}. Marcando como ACTIVE.")
                    domain.ssl_status = models.SslStatus.ACTIVE
                    domain.is_active = True  # Finalmente ativar o domínio
                    # Opcional: Tentar obter datas de emissão/expiração se a verificação
                    # retornar isso
                else:
                    # TODO: Implementar lógica de retentativa? Por enquanto, marcar como falha.
                    # Poderia ter um contador de falhas antes de marcar como ISSUANCE_FAILED.
                    logger.warning(
                        f"Verificação SSL falhou para {domain.domain_name}. Marcando como ISSUANCE_FAILED."  # noqa: E501
                    )
                    domain.ssl_status = models.SslStatus.ISSUANCE_FAILED
                    domain.ssl_error_details = (
                        "Falha na verificação HTTPS periódica."  # Mensagem genérica
                    )
                    domain.is_active = False

                await db.commit()
                # Não precisa de refresh aqui, pois estamos apenas atualizando
            except Exception as e:
                logger.error(
                    f"Erro inesperado ao processar verificação SSL para {domain.domain_name}: {e}"
                )
                # Não marcar como falha imediatamente em caso de erro inesperado no loop,
                # tentar novamente na próxima execução da task. Considerar rollback da sessão.
                await db.rollback()  # Rollback para este domínio específico

        logger.info("Tarefa de verificação de SSL concluída.")


custom_domain_service = CustomDomainService()
