"""Interaction schemas for CRM module."""

import uuid  # noqa: E402
from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field, ConfigDict

from app.modules.shared.crm.models.interaction import (  # noqa: E402
    InteractionType,
    InteractionChannel,
)


# Base Interaction Schema
class InteractionBase(BaseModel):
    """Base schema for Interaction."""

    interaction_type: InteractionType
    channel: InteractionChannel
    subject: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None

    # Timing
    interaction_date: datetime = Field(default_factory=datetime.utcnow)
    duration_minutes: Optional[int] = None

    # Follow-up
    requires_followup: bool = False
    followup_date: Optional[datetime] = None
    followup_notes: Optional[str] = None

    # Status
    is_completed: bool = True
    outcome: Optional[str] = None

    # Additional data
    metadata: Optional[Dict[str, Any]] = None


# Schema for creating a new interaction
class InteractionCreate(InteractionBase):
    """Schema for creating a new Interaction."""

    account_id: uuid.UUID
    contact_id: Optional[uuid.UUID] = None
    created_by_user_id: Optional[uuid.UUID] = None


# Schema for updating an interaction
class InteractionUpdate(BaseModel):
    """Schema for updating an Interaction."""

    interaction_type: Optional[InteractionType] = None
    channel: Optional[InteractionChannel] = None
    subject: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None

    # Timing
    interaction_date: Optional[datetime] = None
    duration_minutes: Optional[int] = None

    # Follow-up
    requires_followup: Optional[bool] = None
    followup_date: Optional[datetime] = None
    followup_notes: Optional[str] = None

    # Status
    is_completed: Optional[bool] = None
    outcome: Optional[str] = None

    # Additional data
    metadata: Optional[Dict[str, Any]] = None


# Schema for reading an interaction
class InteractionRead(InteractionBase):
    """Schema for reading an Interaction."""

    id: uuid.UUID
    account_id: uuid.UUID
    contact_id: Optional[uuid.UUID] = None
    tenant_id: uuid.UUID
    created_by_user_id: Optional[uuid.UUID] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)
