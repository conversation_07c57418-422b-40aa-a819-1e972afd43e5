"""
Notification Schemas

Schemas Pydantic para validação e serialização do sistema de notificações.
"""

from datetime import datetime
from typing import Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, validator

from .models import (
    NotificationPriority, NotificationSenderType, NotificationStatus,
    NotificationTargetType
)


class NotificationBase(BaseModel):
    """Schema base para notificações."""
    title: str = Field(..., min_length=1, max_length=255)
    content: str = Field(..., min_length=1)
    image_url: Optional[str] = Field(None, max_length=500)
    action_url: Optional[str] = Field(None, max_length=500)
    target_type: NotificationTargetType
    target_id: Optional[UUID] = None
    tenant_id: Optional[UUID] = None
    priority: NotificationPriority = NotificationPriority.NORMAL
    auto_expire_hours: int = Field(24, ge=1, le=720)  # 1 hora a 30 dias
    max_lifetime_days: int = Field(30, ge=1, le=30)

    @validator('target_id')
    def validate_target_id(cls, v, values):
        """Valida se target_id é obrigatório para SPECIFIC_USER."""
        if values.get('target_type') == NotificationTargetType.SPECIFIC_USER and not v:
            raise ValueError('target_id é obrigatório para notificações específicas')
        return v

    @validator('tenant_id')
    def validate_tenant_id(cls, v, values):
        """Valida se tenant_id é obrigatório para notificações de tenant."""
        target_type = values.get('target_type')
        if target_type in [
            NotificationTargetType.TENANT_USERS,
            NotificationTargetType.TENANT_OWNERS,
            NotificationTargetType.TENANT_STAFF,
            NotificationTargetType.TENANT_CUSTOMERS
        ] and not v:
            raise ValueError('tenant_id é obrigatório para notificações de tenant')
        return v


class NotificationCreate(NotificationBase):
    """Schema para criação de notificação."""
    pass


class NotificationUpdate(BaseModel):
    """Schema para atualização de notificação."""
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    content: Optional[str] = Field(None, min_length=1)
    image_url: Optional[str] = Field(None, max_length=500)
    action_url: Optional[str] = Field(None, max_length=500)
    priority: Optional[NotificationPriority] = None
    auto_expire_hours: Optional[int] = Field(None, ge=1, le=720)
    status: Optional[NotificationStatus] = None


class NotificationResponse(NotificationBase):
    """Schema de resposta para notificação."""
    id: UUID
    sender_id: UUID
    sender_type: NotificationSenderType
    status: NotificationStatus
    view_count: int
    click_count: int
    delivery_count: int
    created_at: datetime
    updated_at: Optional[datetime]
    expires_at: Optional[datetime]
    sent_at: Optional[datetime]
    is_expired: bool
    
    # Campos calculados para o usuário atual
    is_read: Optional[bool] = None
    is_deleted: Optional[bool] = None

    class Config:
        from_attributes = True


class NotificationListResponse(BaseModel):
    """Schema de resposta para lista de notificações."""
    notifications: List[NotificationResponse]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


class NotificationMarkAsRead(BaseModel):
    """Schema para marcar notificação como lida."""
    notification_ids: List[UUID]


class NotificationMarkAsDeleted(BaseModel):
    """Schema para marcar notificação como deletada."""
    notification_ids: List[UUID]
    delete_for_all: bool = False  # Apenas para admins/owners


class NotificationMetricsResponse(BaseModel):
    """Schema de resposta para métricas de notificação."""
    period: str
    notifications_sent: int
    notifications_delivered: int
    notifications_read: int
    notifications_clicked: int
    notifications_failed: int
    delivery_rate: float
    open_rate: float
    click_through_rate: float


class NotificationSystemMetrics(BaseModel):
    """Schema para métricas do sistema (admin)."""
    total_notifications: int
    active_notifications: int
    expired_notifications: int
    queue_size: int
    failed_notifications: int
    average_delivery_time: float
    system_load: float
    error_rate: float


class NotificationTenantMetrics(BaseModel):
    """Schema para métricas do tenant."""
    total_sent: int
    total_delivered: int
    total_read: int
    total_clicked: int
    delivery_rate: float
    open_rate: float
    click_through_rate: float
    engagement_score: float


class NotificationQueueResponse(BaseModel):
    """Schema de resposta para fila de notificações."""
    id: UUID
    notification_id: UUID
    priority: int
    retry_count: int
    max_retries: int
    status: str
    error_message: Optional[str]
    created_at: datetime
    scheduled_at: Optional[datetime]
    processed_at: Optional[datetime]

    class Config:
        from_attributes = True


class NotificationTemplateBase(BaseModel):
    """Schema base para templates de notificação."""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    category: Optional[str] = Field(None, max_length=100)
    title_template: str = Field(..., min_length=1, max_length=255)
    content_template: str = Field(..., min_length=1)
    default_priority: NotificationPriority = NotificationPriority.NORMAL
    default_auto_expire_hours: int = Field(24, ge=1, le=720)
    is_global: bool = False


class NotificationTemplateCreate(NotificationTemplateBase):
    """Schema para criação de template."""
    pass


class NotificationTemplateUpdate(BaseModel):
    """Schema para atualização de template."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    category: Optional[str] = Field(None, max_length=100)
    title_template: Optional[str] = Field(None, min_length=1, max_length=255)
    content_template: Optional[str] = Field(None, min_length=1)
    default_priority: Optional[NotificationPriority] = None
    default_auto_expire_hours: Optional[int] = Field(None, ge=1, le=720)
    is_active: Optional[bool] = None


class NotificationTemplateResponse(NotificationTemplateBase):
    """Schema de resposta para template."""
    id: UUID
    tenant_id: Optional[UUID]
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class NotificationFilters(BaseModel):
    """Schema para filtros de notificação."""
    status: Optional[NotificationStatus] = None
    priority: Optional[NotificationPriority] = None
    sender_type: Optional[NotificationSenderType] = None
    target_type: Optional[NotificationTargetType] = None
    tenant_id: Optional[UUID] = None
    is_read: Optional[bool] = None
    is_expired: Optional[bool] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    search: Optional[str] = None


class NotificationBulkAction(BaseModel):
    """Schema para ações em lote."""
    notification_ids: List[UUID]
    action: str = Field(..., pattern="^(mark_read|mark_unread|delete|send|cancel)$")
    delete_for_all: bool = False
