import uuid
from sqlalchemy import <PERSON>um<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, Index, Text
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID

from app.db.base import Base


class ShoppingListCategory(Base):
    """Modelo para categorias de lista de compras."""
    __tablename__ = "shopping_list_categories"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False)
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    display_order = Column(Integer, nullable=False, default=0)
    is_active = Column(Boolean, default=True, nullable=False)
    color = Column(String(7), nullable=True)  # Hex color code opcional

    # Relationships
    tenant = relationship("Tenant")
    shopping_list_items = relationship("ShoppingListItem", back_populates="category")

    __table_args__ = (
        Index("ix_shopping_list_categories_tenant_id", "tenant_id"),
        Index("ix_shopping_list_categories_name", "name"),
        Index("ix_shopping_list_categories_display_order", "display_order"),
    )
