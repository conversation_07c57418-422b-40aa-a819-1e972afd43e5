"""
Additional auth dependencies for the auth module.
"""

import logging
from typing import List, Callable, Annotated, Any

from fastapi import Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.users.models.user import User
from app.modules.core.roles.models.roles import SystemRole
from app.modules.core.auth.dependencies.auth_dependencies import get_current_active_user
from app.modules.core.auth.security import permission_denied_exception

logger = logging.getLogger(__name__)


def require_system_role(allowed_roles: List[SystemRole]) -> Callable:
    """
    FastAPI dependency to require a specific system role.

    Args:
        allowed_roles: List of allowed system roles.

    Returns:
        A dependency function that checks if the current user has one of the allowed roles.
    """

    async def _require_system_role(
        current_user: Annotated[User, Depends(get_current_active_user)],
    ) -> User:
        """
        Check if the current user has one of the allowed roles.

        Args:
            current_user: The current authenticated user.

        Returns:
            The User object if the user has one of the allowed roles.

        Raises:
            HTTPException: If the user doesn't have one of the allowed roles.
        """
        if current_user.system_role not in [role.value for role in allowed_roles]:
            logger.warning(
                f"require_system_role: User {current_user.id} with role {current_user.system_role} "
                f"tried to access a resource requiring one of {[role.value for role in allowed_roles]}"
            )
            raise permission_denied_exception
        return current_user

    return _require_system_role


def require_admin_user() -> Callable:
    """
    FastAPI dependency to require an admin user.

    Returns:
        A dependency function that checks if the current user is an admin.
    """
    return require_system_role([SystemRole.ADMIN])


__all__ = [
    "require_system_role",
    "require_admin_user",
]
