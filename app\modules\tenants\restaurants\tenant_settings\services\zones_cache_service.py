"""
Cache service for restaurant zones to improve performance.
"""

import uuid
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import threading


class ZonesCacheService:
    """Service for caching restaurant zones to improve performance."""
    
    def __init__(self, cache_ttl_minutes: int = 30):
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._cache_ttl = timedelta(minutes=cache_ttl_minutes)
        self._lock = threading.Lock()
    
    def _get_cache_key(self, tenant_id: uuid.UUID) -> str:
        """Generate cache key for tenant zones."""
        return f"zones_{tenant_id}"
    
    def _is_cache_valid(self, cache_entry: Dict[str, Any]) -> bool:
        """Check if cache entry is still valid."""
        if 'timestamp' not in cache_entry:
            return False
        
        cache_time = cache_entry['timestamp']
        return datetime.now() - cache_time < self._cache_ttl
    
    def get_zones(self, tenant_id: uuid.UUID) -> Optional[List[Dict[str, Any]]]:
        """Get zones from cache if available and valid."""
        with self._lock:
            cache_key = self._get_cache_key(tenant_id)
            
            if cache_key in self._cache:
                cache_entry = self._cache[cache_key]
                if self._is_cache_valid(cache_entry):
                    return cache_entry['data']
                else:
                    # Remove expired cache
                    del self._cache[cache_key]
            
            return None
    
    def set_zones(self, tenant_id: uuid.UUID, zones_data: List[Dict[str, Any]]) -> None:
        """Cache zones data for tenant."""
        with self._lock:
            cache_key = self._get_cache_key(tenant_id)
            self._cache[cache_key] = {
                'data': zones_data,
                'timestamp': datetime.now()
            }
    
    def invalidate_zones(self, tenant_id: uuid.UUID) -> None:
        """Invalidate zones cache for tenant."""
        with self._lock:
            cache_key = self._get_cache_key(tenant_id)
            if cache_key in self._cache:
                del self._cache[cache_key]
    
    def clear_all_cache(self) -> None:
        """Clear all cached zones."""
        with self._lock:
            self._cache.clear()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._lock:
            total_entries = len(self._cache)
            valid_entries = sum(
                1 for entry in self._cache.values() 
                if self._is_cache_valid(entry)
            )
            
            return {
                'total_entries': total_entries,
                'valid_entries': valid_entries,
                'expired_entries': total_entries - valid_entries,
                'cache_ttl_minutes': self._cache_ttl.total_seconds() / 60
            }


# Create cache service instance
zones_cache_service = ZonesCacheService()
