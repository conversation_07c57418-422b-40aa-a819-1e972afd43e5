from typing import List, Optional, Dict, Any
import uuid
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

from app.core.exceptions import NotFoundError, BusinessLogicError  # Corrigido
from app.modules.tenants.restaurants.delivery.models.delivery_assignment import (
    DeliveryAssignment,
)
from app.modules.tenants.restaurants.delivery.schemas.delivery_assignment import (  # noqa: E402
    DeliveryAssignmentCreate,
)
from app.modules.tenants.restaurants.delivery.enums import DeliveryAssignmentStatus  # noqa: E402

# Commented out until the module is implemented
# from app.modules.tenants.restaurants.models.online_order import (
#     RestaurantOnlineOrder as OnlineOrder,
#     PyOrderStatusEnum as OrderStatusEnum,
#     PyOrderTypeEnum as OrderTypeEnum,
# )  # Corrigido

# Temporary enums until the module is implemented
from enum import Enum


class OrderStatusEnum(str, Enum):
    CONFIRMED_BY_RESTAURANT = "confirmed_by_restaurant"
    PREPARING = "preparing"
    READY_FOR_PICKUP = "ready_for_pickup"


class OrderTypeEnum(str, Enum):
    DELIVERY = "delivery"


# Temporary class until the module is implemented
class OnlineOrder:
    pass


# To check order status if needed
# from app.modules.tenants.restaurants.services.online_order_service import online_order_service
# To get delivery boy by user_id
from app.modules.tenants.restaurants.delivery.services.delivery_boy_service import (  # noqa: E402
    DeliveryBoyService,
)
from app.websockets.manager import emit_to_delivery_boy  # Added  # noqa: E402

# Added for payload type hint
from app.modules.tenants.restaurants.delivery.schemas.delivery_assignment import (  # noqa: E402
    DeliveryAssignmentRead,
)


class DeliveryAssignmentService:
    async def assign_order_to_delivery_boy(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID,
        online_order_id: uuid.UUID,
        delivery_boy_id: uuid.UUID,
        delivery_address_snapshot: Dict[str, Any],
    ) -> DeliveryAssignment:
        # Check if the order is already assigned
        stmt_existing = select(DeliveryAssignment).where(
            DeliveryAssignment.online_order_id == online_order_id,
            DeliveryAssignment.tenant_id == tenant_id,
        )
        existing_assignment = (await db.execute(stmt_existing)).scalars().first()
        if existing_assignment:
            raise BusinessLogicError(  # Corrigido
                f"Online order {online_order_id} is already assigned to delivery_boy {existing_assignment.delivery_boy_id}"  # noqa: E501
            )

        # TODO: Potentially check if OnlineOrder exists and is in a state that allows assignment
        # TODO: Potentially check if DeliveryBoy exists and is available

        assignment_data = DeliveryAssignmentCreate(
            tenant_id=tenant_id,
            online_order_id=online_order_id,
            delivery_boy_id=delivery_boy_id,
            delivery_address_snapshot=delivery_address_snapshot,
            # assignment_status is defaulted in schema
        )
        db_assignment = DeliveryAssignment(**assignment_data.model_dump())

        db.add(db_assignment)
        await db.commit()
        # Refresh relationships
        await db.refresh(db_assignment, attribute_names=["delivery_boy", "online_order"])

        # Emit WebSocket event for new assignment
        assignment_read_schema = DeliveryAssignmentRead.from_orm(db_assignment)
        await emit_to_delivery_boy(
            delivery_boy_id=db_assignment.delivery_boy_id,
            event="new_assignment",
            data=assignment_read_schema.model_dump(),
        )
        return db_assignment

    async def update_assignment_status(
        self,
        db: AsyncSession,
        assignment_id: uuid.UUID,
        tenant_id: uuid.UUID,
        new_status: DeliveryAssignmentStatus,
        # user_id parameter is not used currently
        user_id: Optional[uuid.UUID] = None,  # For future logging or permission checks
    ) -> DeliveryAssignment:
        stmt = (
            select(DeliveryAssignment)
            .where(
                DeliveryAssignment.id == assignment_id,
                DeliveryAssignment.tenant_id == tenant_id,
            )
            .options(
                selectinload(DeliveryAssignment.delivery_boy),
                selectinload(DeliveryAssignment.online_order),
            )
        )
        db_assignment = (await db.execute(stmt)).scalars().first()

        if not db_assignment:
            raise NotFoundError("Delivery assignment not found.")  # Corrigido

        # Basic state transition validation (can be expanded)
        if (
            db_assignment.assignment_status == DeliveryAssignmentStatus.DELIVERED
            or db_assignment.assignment_status == DeliveryAssignmentStatus.CANCELLED
        ):
            raise BusinessLogicError(
                f"Cannot update status of a {db_assignment.assignment_status.value} order."
            )  # Corrigido

        db_assignment.assignment_status = new_status
        now = datetime.utcnow()
        if new_status == DeliveryAssignmentStatus.ACCEPTED:
            db_assignment.accepted_at = now
        elif new_status == DeliveryAssignmentStatus.PICKED_UP:
            db_assignment.picked_up_at = now
        elif new_status == DeliveryAssignmentStatus.DELIVERED:
            db_assignment.delivered_at = now
            db_assignment.actual_delivery_time = now  # Or could be set separately
        elif (
            new_status == DeliveryAssignmentStatus.CANCELLED
            or new_status == DeliveryAssignmentStatus.REJECTED
        ):
            db_assignment.cancelled_at = now
            # Potentially update DeliveryBoy status back to AVAILABLE if REJECTED by boy
            # if new_status == DeliveryAssignmentStatus.REJECTED and db_assignment.delivery_boy:
            #     db_assignment.delivery_boy.current_status = DeliveryBoyStatus.AVAILABLE

        await db.commit()
        await db.refresh(db_assignment)

        # Emit WebSocket event for assignment update
        assignment_read_schema = DeliveryAssignmentRead.from_orm(db_assignment)
        await emit_to_delivery_boy(
            delivery_boy_id=db_assignment.delivery_boy_id,
            event="assignment_update",
            data=assignment_read_schema.model_dump(),
        )
        # Potentially also emit to other relevant parties (e.g., tenant admins, customer if applicable)  # noqa: E501
        # For example, if there's a general tenant room for delivery updates:
        # from app.websockets.manager import emit_to_tenant_delivery_updates # Hypothetical
        # await emit_to_tenant_delivery_updates(tenant_id=db_assignment.tenant_id,
        # event="assignment_update_for_admin",
        # data=assignment_read_schema.model_dump())

        return db_assignment

    async def get_assignments_for_delivery_boy(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID,
        delivery_boy_id: uuid.UUID,
        status: Optional[DeliveryAssignmentStatus] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[DeliveryAssignment]:
        stmt = (
            select(DeliveryAssignment)
            .where(
                DeliveryAssignment.tenant_id == tenant_id,
                DeliveryAssignment.delivery_boy_id == delivery_boy_id,
            )
            .options(
                selectinload(DeliveryAssignment.online_order)  # Eager load online_order details
            )
            .order_by(DeliveryAssignment.created_at.desc())
            .offset(skip)
            .limit(limit)
        )

        if status:
            stmt = stmt.where(DeliveryAssignment.assignment_status == status)

        result = await db.execute(stmt)
        return result.scalars().all()

    async def get_assignment_by_id(
        self, db: AsyncSession, assignment_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Optional[DeliveryAssignment]:
        stmt = (
            select(DeliveryAssignment)
            .where(
                DeliveryAssignment.id == assignment_id,
                DeliveryAssignment.tenant_id == tenant_id,
            )
            .options(
                selectinload(DeliveryAssignment.delivery_boy),
                selectinload(DeliveryAssignment.online_order),
            )
        )
        result = await db.execute(stmt)
        return result.scalars().first()

    async def get_assignment_by_order_id(
        self, db: AsyncSession, tenant_id: uuid.UUID, online_order_id: uuid.UUID
    ) -> Optional[DeliveryAssignment]:
        stmt = (
            select(DeliveryAssignment)
            .where(
                DeliveryAssignment.tenant_id == tenant_id,
                DeliveryAssignment.online_order_id == online_order_id,
            )
            .options(
                selectinload(DeliveryAssignment.delivery_boy),
                selectinload(DeliveryAssignment.online_order),  # Eager load for context
            )
        )
        result = await db.execute(stmt)
        return result.scalars().first()

    async def claim_order_for_delivery(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID,
        online_order_id: uuid.UUID,
        delivery_boy_user_id: uuid.UUID,  # User ID of the delivery boy claiming the order
    ) -> DeliveryAssignment:
        # 1. Get DeliveryBoy profile from user_id
        delivery_boy_service = DeliveryBoyService()
        delivery_boy = await delivery_boy_service.get_delivery_boy_by_user_id(
            db, user_id=delivery_boy_user_id, tenant_id=tenant_id
        )
        if not delivery_boy:
            raise NotFoundError("Delivery boy profile not found for this user.")  # Corrigido
        if not delivery_boy.is_active:
            raise BusinessLogicError("Delivery boy is not active.")  # Corrigido
        # Optionally, check if delivery_boy.current_status == DeliveryBoyStatus.AVAILABLE
        # For now, we allow claiming even if BUSY, assuming they can queue. This can be refined.

        # 2. Check if the OnlineOrder exists and is in a claimable state
        # We need the model instance to get delivery_address_snapshot if not passed
        # For now, let's assume the API will fetch and pass the snapshot.
        # A more robust way would be for this service to fetch the OnlineOrder.

        # Fetch the OnlineOrder to get its details, especially delivery_address
        online_order_stmt = select(OnlineOrder).where(
            OnlineOrder.id == online_order_id, OnlineOrder.tenant_id == tenant_id
        )
        online_order_result = await db.execute(online_order_stmt)
        online_order = online_order_result.scalar_one_or_none()

        if not online_order:
            raise NotFoundError(f"Online order {online_order_id} not found.")  # Corrigido

        if online_order.order_type != OrderTypeEnum.DELIVERY:
            raise BusinessLogicError(
                f"Order {online_order_id} is not a delivery order."
            )  # Corrigido

        if online_order.status not in [
            OrderStatusEnum.CONFIRMED_BY_RESTAURANT,
            OrderStatusEnum.PREPARING,
            OrderStatusEnum.READY_FOR_PICKUP,
        ]:
            raise BusinessLogicError(
                f"Order {online_order_id} is not in a claimable state (current status: {online_order.status})."  # noqa: E501
            )  # Corrigido

        # 3. Check if the order already has an active assignment
        existing_assignment = await self.get_assignment_by_order_id(db, tenant_id, online_order_id)
        if existing_assignment and existing_assignment.assignment_status not in [
            DeliveryAssignmentStatus.CANCELLED,
            DeliveryAssignmentStatus.REJECTED,
        ]:
            raise BusinessLogicError(
                f"Order {online_order_id} is already actively assigned."
            )  # Corrigido

        # Construct delivery_address_snapshot from the online_order
        # This is crucial as the API for claiming might not pass this.
        delivery_address_snapshot = {
            "address_line1": online_order.delivery_address_line1,
            "address_line2": online_order.delivery_address_line2,
            "city": online_order.delivery_city,
            "state": online_order.delivery_state,
            "postal_code": online_order.delivery_postal_code,
            "instructions": online_order.delivery_instructions,
            "customer_name": online_order.customer_name,  # Also include customer info
            "customer_phone": online_order.customer_phone,
        }
        if not delivery_address_snapshot.get("address_line1"):  # Basic check
            raise BusinessLogicError(
                f"Order {online_order_id} does not have a complete delivery address."
            )  # Corrigido

        # 4. Create the DeliveryAssignment
        # The assign_order_to_delivery_boy method already checks for existing assignments,
        # but we did an explicit check above for clarity and to potentially fetch it.
        # We can call it directly.
        new_assignment = await self.assign_order_to_delivery_boy(
            db=db,
            tenant_id=tenant_id,
            online_order_id=online_order_id,
            delivery_boy_id=delivery_boy.id,  # Use the ID of the DeliveryBoy profile
            delivery_address_snapshot=delivery_address_snapshot,
        )

        # 5. Optionally, update DeliveryBoy status to BUSY
        # delivery_boy.current_status = DeliveryBoyStatus.BUSY
        # db.add(delivery_boy)
        # await db.commit() # Commit this change along with assignment or separately
        # await db.refresh(delivery_boy)

        # Emit WebSocket event for new assignment (claimed)
        # Need to refresh new_assignment to get all fields for the schema
        # Ensure relationships are loaded
        await db.refresh(new_assignment, attribute_names=["delivery_boy", "online_order"])
        assignment_read_schema = DeliveryAssignmentRead.from_orm(
            new_assignment
        )  # Create schema after refresh
        await emit_to_delivery_boy(
            delivery_boy_id=new_assignment.delivery_boy_id,
            event="new_assignment",
            data=assignment_read_schema.model_dump(),
        )
        return new_assignment
