from typing import Optional, List
from pydantic import BaseModel, Field
from uuid import UUID

from .product_variant_option import ProductVariantOptionResponse


class ProductVariantGroupBase(BaseModel):
    """Base schema for product variant groups."""
    name: str = Field(..., min_length=1, max_length=100, description="Variant group name")
    description: Optional[str] = Field(None, max_length=255, description="Variant group description")
    min_selection: int = Field(1, ge=0, description="Minimum number of selections required")
    max_selection: int = Field(1, ge=1, description="Maximum number of selections allowed")
    display_order: int = Field(0, description="Display order within the product")
    is_required: bool = Field(False, description="Whether this variant group is required")
    is_active: bool = Field(True, description="Whether this variant group is active")
    requires_default_selection: bool = Field(True, description="Whether a default selection is required")
    is_template: bool = Field(False, description="Whether this is a template group")
    template_id: Optional[UUID] = Field(None, description="Reference to template group")


class ProductVariantGroupCreate(ProductVariantGroupBase):
    """Schema for creating a new product variant group."""
    tenant_id: Optional[UUID] = Field(None, description="Tenant ID (null for global groups)")


class ProductVariantGroupUpdate(BaseModel):
    """Schema for updating a product variant group."""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="Variant group name")
    description: Optional[str] = Field(None, max_length=255, description="Variant group description")
    min_selection: Optional[int] = Field(None, ge=0, description="Minimum number of selections required")
    max_selection: Optional[int] = Field(None, ge=1, description="Maximum number of selections allowed")
    display_order: Optional[int] = Field(None, description="Display order within the product")
    is_required: Optional[bool] = Field(None, description="Whether this variant group is required")
    is_active: Optional[bool] = Field(None, description="Whether this variant group is active")
    requires_default_selection: Optional[bool] = Field(None, description="Whether a default selection is required")
    is_template: Optional[bool] = Field(None, description="Whether this is a template group")
    template_id: Optional[UUID] = Field(None, description="Reference to template group")


class ProductVariantGroupResponse(ProductVariantGroupBase):
    """Schema for product variant group responses."""
    id: UUID
    tenant_id: Optional[UUID]
    options: List[ProductVariantOptionResponse] = Field(default_factory=list, description="Variant options")
    
    class Config:
        from_attributes = True
