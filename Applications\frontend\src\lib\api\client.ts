import axios, { AxiosInstance, AxiosResponse, AxiosRequestConfig } from 'axios';
import Cookies from 'js-cookie';
import { Invoice, Transaction } from '@/types/financial';

// Helper function to check if user is system admin
const isSystemAdmin = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  try {
    const token = Cookies.get('access_token');
    if (!token) return false;
    
    // Decode JWT token to get user info
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload.system_role === 'admin';
  } catch (error) {
    console.error('Error checking system admin status:', error);
    return false;
  }
};

// Determine API URL based on environment
const getApiUrl = (): string => {
  // Check if we're on the server side
  if (typeof window === 'undefined') {
    // Server-side: use internal Docker network
    return process.env.INTERNAL_API_URL || 'http://backend:8000/api';
  }

  // Client-side: use external URL (already includes /api)
  return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';
};

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: getApiUrl(),
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token and admin view headers
apiClient.interceptors.request.use(
  (config) => {
    const token = Cookies.get('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    } else {
      console.log('⚠️ No auth token found in cookies');
    }

    // Check for admin view mode first (takes priority)
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const adminView = urlParams.get('admin_view') === 'true';
      const tenantId = urlParams.get('tenant_id');

      if (adminView && tenantId) {
        config.headers['X-Tenant-ID'] = tenantId;
        config.headers['X-Admin-View'] = 'true';
        console.log(`🔍 Admin View Mode: Adding headers for tenant ${tenantId}`);
        return config; // Skip normal tenant logic
      }
    }

    // Normal tenant context if not in admin view
    // System admins don't need tenant_id for global operations
    if (isSystemAdmin()) {
      console.log('🔑 System admin detected - skipping X-Tenant-ID requirement');
      return config;
    }
    
    const tenantId = Cookies.get('tenant_id');
    if (tenantId) {
      config.headers['X-Tenant-ID'] = tenantId;
    } else {
      console.log('⚠️ No tenant_id found in cookies - X-Tenant-ID header not added');

      // Fallback: try to get tenant from localStorage
      if (typeof window !== 'undefined') {
        const storedTenant = localStorage.getItem('tenant_id');
        if (storedTenant) {
          config.headers['X-Tenant-ID'] = storedTenant;
        }
      }
    }

    // Don't override Content-Type for FormData uploads
    if (config.data instanceof FormData) {
      // Remove Content-Type to let browser set it with boundary
      delete config.headers['Content-Type'];

    }

    // Log all headers being sent for debugging


    return config;
  },
  (error) => {
    console.error('🔍 API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for token refresh
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {

    return response;
  },
  async (error) => {
    const errorInfo = {
      status: error.response?.status,
      url: error.config?.url,
      method: error.config?.method?.toUpperCase(),
      message: error.message,
      data: error.response?.data,
      requestData: error.config?.data
    };

    // Always log error information for debugging
    console.error('🔍 API Response Error:', errorInfo);

    // Log additional details if available
    if (error.response?.data) {
      console.error('🔍 API Error Response Data:', error.response.data);
    }
    if (error.config?.data) {
      console.error('🔍 API Request Data:', error.config.data);
    }

    // Special handling for 422 validation errors
    if (error.response?.status === 422) {
      console.error('🔍 422 VALIDATION ERROR DETAILS:');
      console.error('🔍 Full Response:', JSON.stringify(error.response.data, null, 2));
      console.error('🔍 Request URL:', error.config?.url);
      console.error('🔍 Request Method:', error.config?.method);
      console.error('🔍 Request Headers:', error.config?.headers);
    }

    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = Cookies.get('refresh_token');
        if (!refreshToken) {
          throw new Error('No refresh token available');
        }

        // Attempt to refresh token
        const formData = new URLSearchParams();
        formData.append('refresh_token', refreshToken);

        const response = await axios.post(
          `${getApiUrl()}/auth/refresh-token`,
          formData,
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          }
        );

        const { access_token, refresh_token } = response.data;

        // Update stored tokens
        Cookies.set('access_token', access_token, { expires: 7 });
        if (refresh_token) {
          Cookies.set('refresh_token', refresh_token, { expires: 30 });
        }

        // Retry original request with new token
        originalRequest.headers.Authorization = `Bearer ${access_token}`;
        return apiClient(originalRequest);

      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError);

        // Clear invalid tokens
        Cookies.remove('access_token');
        Cookies.remove('refresh_token');
        Cookies.remove('tenant_id');

        // Redirect to login if we're on the client side
        if (typeof window !== 'undefined') {
          window.location.href = '/auth?mode=login';
        }

        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// Generic API methods
export const apiGet = async <T = any>(
  url: string,
  config?: AxiosRequestConfig
): Promise<T> => {
  const response = await apiClient.get<T>(url, config);
  return response.data;
};

export const apiPost = async <T = any>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<T> => {
  const response = await apiClient.post<T>(url, data, config);
  return response.data;
};

export const apiPut = async <T = any>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<T> => {
  const response = await apiClient.put<T>(url, data, config);
  return response.data;
};

export const apiPatch = async <T = any>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<T> => {
  const response = await apiClient.patch<T>(url, data, config);
  return response.data;
};

export const apiDelete = async <T = any>(
  url: string,
  config?: AxiosRequestConfig
): Promise<T> => {
  const response = await apiClient.delete<T>(url, config);
  return response.data;
};

// Create a wrapper object with methods that return data directly
const apiClientWrapper = {
  get: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.get<T>(url, config);
    return response.data;
  },
  post: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.post<T>(url, data, config);
    return response.data;
  },
  put: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.put<T>(url, data, config);
    return response.data;
  },
  patch: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.patch<T>(url, data, config);
    return response.data;
  },
  delete: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.delete<T>(url, config);
    return response.data;
  },
};

// --- Financial Module API ---

export const financialApi = {
  // == Invoices ==
  getInvoices: async (params?: any): Promise<Invoice[]> => {
    const { data } = await apiClient.get('/financial/invoices/', { params });
    return data;
  },
  getInvoiceById: async (id: string): Promise<Invoice> => {
    const { data } = await apiClient.get(`/financial/invoices/${id}`);
    return data;
  },
  createInvoice: async (invoiceData: Partial<Invoice>): Promise<Invoice> => {
    const { data } = await apiClient.post('/financial/invoices/', invoiceData);
    return data;
  },
  updateInvoice: async (id: string, invoiceData: Partial<Invoice>): Promise<Invoice> => {
    const { data } = await apiClient.put(`/financial/invoices/${id}`, invoiceData);
    return data;
  },
  deleteInvoice: async (id: string): Promise<void> => {
    await apiClient.delete(`/financial/invoices/${id}`);
  },
  generateInvoicePdf: async (invoiceId: string): Promise<Blob> => {
    const { data } = await apiClient.get(`/financial/invoices/${invoiceId}/pdf`, {
      responseType: 'blob',
    });
    return data;
  },

  // == Transactions ==
  getTransactions: async (params?: any): Promise<Transaction[]> => {
    const { data } = await apiClient.get('/financial/transactions/', { params });
    return data;
  },
  getTransactionById: async (id: string): Promise<Transaction> => {
    const { data } = await apiClient.get(`/financial/transactions/${id}`);
    return data;
  },
  createTransaction: async (transactionData: Partial<Transaction>): Promise<Transaction> => {
    const { data } = await apiClient.post('/financial/transactions/', transactionData);
    return data;
  },
  updateTransaction: async (id: string, transactionData: Partial<Transaction>): Promise<Transaction> => {
    const { data } = await apiClient.put(`/financial/transactions/${id}`, transactionData);
    return data;
  },
  deleteTransaction: async (id: string): Promise<void> => {
    await apiClient.delete(`/financial/transactions/${id}`);
  },

  // == Categories ==
  getCategories: async (params?: any): Promise<any[]> => {
    const { data } = await apiClient.get('/financial/categories/', { params });
    return data;
  },
  getCategoryById: async (id: string): Promise<any> => {
    const { data } = await apiClient.get(`/financial/categories/${id}`);
    return data;
  },
  createCategory: async (categoryData: any): Promise<any> => {
    const { data } = await apiClient.post('/financial/categories/', categoryData);
    return data;
  },
  updateCategory: async (id: string, categoryData: any): Promise<any> => {
    const { data } = await apiClient.put(`/financial/categories/${id}`, categoryData);
    return data;
  },
  deleteCategory: async (id: string): Promise<void> => {
    await apiClient.delete(`/financial/categories/${id}`);
  },
};

// --- Inventory Module API ---
export const inventoryApi = {
  getItems: async (params?: any): Promise<any[]> => {
    const { data } = await apiClient.get('/inventory/items/', { params });
    return data;
  },
  getItemById: async (id: string): Promise<any> => {
    const { data } = await apiClient.get(`/inventory/items/${id}`);
    return data;
  },
  createItem: async (itemData: any): Promise<any> => {
    const { data } = await apiClient.post('/inventory/items/', itemData);
    return data;
  },
  updateItem: async (id: string, itemData: any): Promise<any> => {
    const { data } = await apiClient.put(`/inventory/items/${id}`, itemData);
    return data;
  },
  deleteItem: async (id: string): Promise<void> => {
    await apiClient.delete(`/inventory/items/${id}`);
  },
  adjustStock: async (id: string, adjustment: any): Promise<any> => {
    const { data } = await apiClient.post(`/inventory/items/${id}/adjust-stock`, adjustment);
    return data;
  },
};

// --- Shopping List Module API ---
export const shoppingListApi = {
  getLists: async (params?: any): Promise<any[]> => {
    const { data } = await apiClient.get('/shopping-lists/', { params });
    return data;
  },
  getListById: async (id: string): Promise<any> => {
    const { data } = await apiClient.get(`/shopping-lists/${id}`);
    return data;
  },
  createList: async (listData: any): Promise<any> => {
    const { data } = await apiClient.post('/shopping-lists/', listData);
    return data;
  },
  updateList: async (id: string, listData: any): Promise<any> => {
    const { data } = await apiClient.put(`/shopping-lists/${id}`, listData);
    return data;
  },
  deleteList: async (id: string): Promise<void> => {
    await apiClient.delete(`/shopping-lists/${id}`);
  },
  getItems: async (listId: string): Promise<any[]> => {
    const { data } = await apiClient.get(`/shopping-lists/${listId}/items/`);
    return data;
  },
  addItem: async (listId: string, itemData: any): Promise<any> => {
    const { data } = await apiClient.post(`/shopping-lists/${listId}/items/`, itemData);
    return data;
  },
  updateItem: async (listId: string, itemId: string, itemData: any): Promise<any> => {
    const { data } = await apiClient.put(`/shopping-lists/${listId}/items/${itemId}`, itemData);
    return data;
  },
  deleteItem: async (listId: string, itemId: string): Promise<void> => {
    await apiClient.delete(`/shopping-lists/${listId}/items/${itemId}`);
  },
};

// Export the client instance for direct use if needed
export { apiClient, apiClientWrapper };
export default apiClient;
