import uuid
from typing import TYPE_CHECKING
from sqlalchemy import (
    Integer,
    String,
    ForeignKey,
    Numeric,
    Index,
    Boolean,
)
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.orm import relationship, Mapped, mapped_column

from app.db.base import Base

if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from .optional_group import OptionalGroup


class OptionalOption(Base):
    """
    Represents an individual optional add-on within an optional group.
    """
    __tablename__ = "optional_options"

    id: Mapped[uuid.UUID] = mapped_column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    tenant_id: Mapped[uuid.UUID] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    optional_group_id: Mapped[uuid.UUID] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("optional_groups.id"), nullable=False, index=True)

    name: Mapped[str] = mapped_column(String(100), nullable=False)
    price_adjustment: Mapped[float] = mapped_column(Numeric(10, 2), default=0.00, nullable=False)
    display_order: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)

    # Relationships
    tenant: Mapped["Tenant"] = relationship("Tenant")
    optional_group: Mapped["OptionalGroup"] = relationship("OptionalGroup", back_populates="options")

    __table_args__ = (
        Index("ix_optional_options_tenant_id_optional_group_id", "tenant_id", "optional_group_id"),
        Index(
            "ix_optional_options_tenant_id_optional_group_id_name",
            "tenant_id",
            "optional_group_id",
            "name",
            unique=True,
        ),
    )

    def __repr__(self) -> str:
        return f"<OptionalOption(id={self.id}, name='{self.name}', group_id={self.optional_group_id}, tenant_id={self.tenant_id})>" 