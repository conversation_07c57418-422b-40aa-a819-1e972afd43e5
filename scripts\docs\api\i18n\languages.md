# I18N - Languages

**Categoria:** I18N
**Módulo:** Languages
**Total de Endpoints:** 7
**Gerado em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [GET /api/i18n/languages/](#get-apii18nlanguages) - Read Languages
- [POST /api/i18n/languages/](#post-apii18nlanguages) - Create Language
- [GET /api/i18n/languages/check-version/{language_code}](#get-apii18nlanguagescheck-versionlanguage-code) - Check Language Version
- [DELETE /api/i18n/languages/{language_id}](#delete-apii18nlanguageslanguage-id) - Delete Language
- [GET /api/i18n/languages/{language_id}](#get-apii18nlanguageslanguage-id) - Read Language
- [PUT /api/i18n/languages/{language_id}](#put-apii18nlanguageslanguage-id) - Update Language
- [POST /api/i18n/languages/{language_id}/set-default](#post-apii18nlanguageslanguage-idset-default) - Set Default Language

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### LanguageCreate

**Descrição:** Schema for creating a new Language.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `code` | string | ✅ | Language code (e.g., 'en', 'pt-br') |
| `name` | string | ✅ | Language name in English (e.g., 'English', 'Portuguese (Brazil)') |
| `native_name` | string | ✅ | Language name in its native form (e.g., 'English', 'Português (Brasil)') |
| `is_active` | boolean | ❌ | Whether the language is active |
| `is_default` | boolean | ❌ | Whether this is the default language |

### LanguageRead

**Descrição:** Schema for reading a Language.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `code` | string | ✅ | Language code (e.g., 'en', 'pt-br') |
| `name` | string | ✅ | Language name in English (e.g., 'English', 'Portuguese (Brazil)') |
| `native_name` | string | ✅ | Language name in its native form (e.g., 'English', 'Português (Brasil)') |
| `is_active` | boolean | ❌ | Whether the language is active |
| `is_default` | boolean | ❌ | Whether this is the default language |
| `id` | string | ✅ | - |
| `version_code` | string | ✅ | Version code for tracking language updates |

### LanguageUpdate

**Descrição:** Schema for updating an existing Language.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `code` | unknown | ❌ | - |
| `name` | unknown | ❌ | - |
| `native_name` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `is_default` | unknown | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/i18n/languages/ {#get-apii18nlanguages}

**Resumo:** Read Languages
**Descrição:** Retrieve all languages with pagination.
This endpoint is public and does not require authentication.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/i18n/languages/"
```

---

### POST /api/i18n/languages/ {#post-apii18nlanguages}

**Resumo:** Create Language
**Descrição:** Create a new language.
Requires ADMIN system role.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [LanguageCreate](#languagecreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LanguageRead](#languageread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/i18n/languages/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/i18n/languages/check-version/{language_code} {#get-apii18nlanguagescheck-versionlanguage-code}

**Resumo:** Check Language Version
**Descrição:** Check if a language version has changed.
This endpoint is public and does not require authentication.

Returns a JSON object with:
- changed: boolean indicating if the version has changed
- version_code: the current version code on the server

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `language_code` | string | path | ✅ | The language code to check |
| `client_version` | string | query | ✅ | The client's current version code |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/i18n/languages/check-version/{language_code}"
```

---

### DELETE /api/i18n/languages/{language_id} {#delete-apii18nlanguageslanguage-id}

**Resumo:** Delete Language
**Descrição:** Delete a language.
Requires ADMIN system role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `language_id` | integer | path | ✅ | The ID of the language to delete |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/i18n/languages/{language_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/i18n/languages/{language_id} {#get-apii18nlanguageslanguage-id}

**Resumo:** Read Language
**Descrição:** Retrieve a specific language by ID.
This endpoint is public and does not require authentication.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `language_id` | integer | path | ✅ | The ID of the language to retrieve |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LanguageRead](#languageread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/i18n/languages/{language_id}"
```

---

### PUT /api/i18n/languages/{language_id} {#put-apii18nlanguageslanguage-id}

**Resumo:** Update Language
**Descrição:** Update a language.
Requires ADMIN system role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `language_id` | integer | path | ✅ | The ID of the language to update |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [LanguageUpdate](#languageupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LanguageRead](#languageread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/i18n/languages/{language_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/i18n/languages/{language_id}/set-default {#post-apii18nlanguageslanguage-idset-default}

**Resumo:** Set Default Language
**Descrição:** Set a language as the default.
Requires ADMIN system role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `language_id` | integer | path | ✅ | The ID of the language to set as default |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [LanguageRead](#languageread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/i18n/languages/{language_id}/set-default" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
