"""Menu Item Image model for storing menu item images."""

import uuid
from datetime import datetime
from sqlalchemy import Column, String, Text, Boolean, ForeignKey, DateTime, Integer
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import Base


class MenuItemImage(Base):
    """
    Model for storing menu item images.
    
    This model stores images associated with menu items, including
    metadata like alt text, display order, and primary image designation.
    """
    
    __tablename__ = "menu_item_images"
    
    id = Column(
        PG_UUID(as_uuid=True), 
        primary_key=True, 
        default=uuid.uuid4,
        index=True
    )
    tenant_id = Column(
        PG_UUID(as_uuid=True),
        ForeignKey("tenants.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        comment="Tenant that owns this image"
    )
    menu_item_id = Column(
        PG_UUID(as_uuid=True),
        ForeignKey("menu_items.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        comment="Menu item this image belongs to"
    )
    image_url = Column(
        String(500),
        nullable=False,
        comment="URL of the image"
    )
    image_path = Column(
        String(500),
        nullable=True,
        comment="Local file path of the image"
    )
    alt_text = Column(
        String(255),
        nullable=True,
        comment="Alternative text for accessibility"
    )
    display_order = Column(
        Integer,
        nullable=False,
        default=0,
        comment="Order in which images should be displayed"
    )
    is_primary = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="Whether this is the primary image for the menu item"
    )
    is_active = Column(
        Boolean,
        nullable=False,
        default=True,
        comment="Whether this image is active and should be displayed"
    )
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="When this image was created"
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="When this image was last updated"
    )
    
    # Relationships
    menu_item = relationship(
        "MenuItem",
        back_populates="images"
    )
    
    def __repr__(self) -> str:
        return (f"<MenuItemImage(id={self.id}, menu_item_id={self.menu_item_id}, "
                f"is_primary={self.is_primary})>")
    
    def __str__(self) -> str:
        primary_text = " (Primary)" if self.is_primary else ""
        return f"Image {self.display_order + 1}{primary_text}"