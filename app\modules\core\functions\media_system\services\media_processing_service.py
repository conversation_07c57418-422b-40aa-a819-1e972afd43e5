"""
Media Processing Service

Serviço para processamento de mídia (thumbnails, compressão, etc.).
"""

import logging
import json
from pathlib import Path
from typing import Optional, Dict, Any
from uuid import UUID

from PIL import Image, ImageOps
from sqlalchemy.orm import Session

from ..models import MediaUpload, MediaFileType, MediaUploadStatus
from ..schemas import MediaUploadUpdate

logger = logging.getLogger(__name__)


class MediaProcessingService:
    """Serviço para processamento de mídia."""

    def __init__(self, db: Session):
        self.db = db
        self.thumbnail_size = (300, 300)
        self.compressed_quality = 85

    def process_upload(self, upload_id: UUID) -> bool:
        """
        Processa um upload (gera thumbnail, comprime, extrai metadados).
        
        Args:
            upload_id: ID do upload
            
        Returns:
            bool: True se processado com sucesso
        """
        try:
            upload = self.db.query(MediaUpload).filter(
                MediaUpload.id == upload_id
            ).first()

            if not upload:
                logger.error(f"Upload não encontrado: {upload_id}")
                return False

            # Marca como processando
            upload.upload_status = MediaUploadStatus.PROCESSING
            self.db.commit()

            success = True

            # Processa baseado no tipo de mídia
            if upload.media_type == MediaFileType.IMAGE:
                success = self._process_image(upload)
            elif upload.media_type == MediaFileType.VIDEO:
                success = self._process_video(upload)
            elif upload.media_type == MediaFileType.AUDIO:
                success = self._process_audio(upload)
            else:
                success = self._process_generic(upload)

            # Atualiza status final
            if success:
                upload.upload_status = MediaUploadStatus.COMPLETED
                logger.info(f"Upload processado com sucesso: {upload_id}")
            else:
                upload.upload_status = MediaUploadStatus.FAILED
                logger.error(f"Falha no processamento: {upload_id}")

            self.db.commit()
            return success

        except Exception as e:
            logger.error(f"Erro no processamento do upload {upload_id}: {e}")
            
            # Marca como falhou
            if 'upload' in locals():
                upload.upload_status = MediaUploadStatus.FAILED
                self.db.commit()
            
            return False

    def _process_image(self, upload: MediaUpload) -> bool:
        """
        Processa uma imagem (thumbnail, compressão, metadados).
        
        Args:
            upload: Upload para processar
            
        Returns:
            bool: True se processado com sucesso
        """
        try:
            file_path = Path(upload.file_path)
            if not file_path.exists():
                logger.error(f"Arquivo não encontrado: {file_path}")
                return False

            with Image.open(file_path) as img:
                # Extrai metadados
                metadata = self._extract_image_metadata(img)
                upload.file_metadata = json.dumps(metadata)

                # Gera thumbnail
                thumbnail_path = self._generate_thumbnail(img, upload)
                if thumbnail_path:
                    upload.thumbnail_path = str(thumbnail_path)

                # Gera versão comprimida se necessário
                compressed_path = self._compress_image(img, upload)
                if compressed_path:
                    upload.compressed_path = str(compressed_path)

            return True

        except Exception as e:
            logger.error(f"Erro no processamento de imagem: {e}")
            return False

    def _process_video(self, upload: MediaUpload) -> bool:
        """
        Processa um vídeo (metadados básicos).
        
        Args:
            upload: Upload para processar
            
        Returns:
            bool: True se processado com sucesso
        """
        try:
            # Para vídeos, por enquanto apenas extraímos metadados básicos
            file_path = Path(upload.file_path)
            metadata = {
                "type": "video",
                "file_size": upload.file_size,
                "mime_type": upload.mime_type,
                "processed_at": str(upload.created_at)
            }
            
            upload.file_metadata = json.dumps(metadata)
            return True

        except Exception as e:
            logger.error(f"Erro no processamento de vídeo: {e}")
            return False

    def _process_audio(self, upload: MediaUpload) -> bool:
        """
        Processa um áudio (metadados básicos).
        
        Args:
            upload: Upload para processar
            
        Returns:
            bool: True se processado com sucesso
        """
        try:
            # Para áudios, por enquanto apenas extraímos metadados básicos
            file_path = Path(upload.file_path)
            metadata = {
                "type": "audio",
                "file_size": upload.file_size,
                "mime_type": upload.mime_type,
                "processed_at": str(upload.created_at)
            }
            
            upload.file_metadata = json.dumps(metadata)
            return True

        except Exception as e:
            logger.error(f"Erro no processamento de áudio: {e}")
            return False

    def _process_generic(self, upload: MediaUpload) -> bool:
        """
        Processa arquivo genérico (apenas metadados básicos).
        
        Args:
            upload: Upload para processar
            
        Returns:
            bool: True se processado com sucesso
        """
        try:
            metadata = {
                "type": "generic",
                "file_size": upload.file_size,
                "mime_type": upload.mime_type,
                "processed_at": str(upload.created_at)
            }
            
            upload.file_metadata = json.dumps(metadata)
            return True

        except Exception as e:
            logger.error(f"Erro no processamento genérico: {e}")
            return False

    def _extract_image_metadata(self, img: Image.Image) -> Dict[str, Any]:
        """
        Extrai metadados de uma imagem.
        
        Args:
            img: Imagem PIL
            
        Returns:
            Dict: Metadados da imagem
        """
        metadata = {
            "type": "image",
            "width": img.width,
            "height": img.height,
            "format": img.format,
            "mode": img.mode,
            "has_transparency": img.mode in ("RGBA", "LA") or "transparency" in img.info
        }

        # Adiciona informações EXIF se disponível
        if hasattr(img, '_getexif') and img._getexif():
            exif = img._getexif()
            if exif:
                metadata["has_exif"] = True
                # Adiciona apenas alguns campos EXIF importantes
                if 272 in exif:  # Make
                    metadata["camera_make"] = exif[272]
                if 306 in exif:  # DateTime
                    metadata["date_taken"] = exif[306]

        return metadata

    def _generate_thumbnail(self, img: Image.Image, upload: MediaUpload) -> Optional[Path]:
        """
        Gera thumbnail de uma imagem.
        
        Args:
            img: Imagem PIL
            upload: Upload
            
        Returns:
            Path: Caminho do thumbnail ou None
        """
        try:
            # Cria thumbnail mantendo proporção
            img_copy = img.copy()
            img_copy.thumbnail(self.thumbnail_size, Image.Resampling.LANCZOS)

            # Define caminho do thumbnail
            file_path = Path(upload.file_path)
            thumbnail_path = file_path.parent / f"thumb_{file_path.name}"

            # Salva thumbnail
            img_copy.save(thumbnail_path, format="JPEG", quality=90)
            
            logger.info(f"Thumbnail gerado: {thumbnail_path}")
            return thumbnail_path

        except Exception as e:
            logger.error(f"Erro ao gerar thumbnail: {e}")
            return None

    def _compress_image(self, img: Image.Image, upload: MediaUpload) -> Optional[Path]:
        """
        Gera versão comprimida de uma imagem.
        
        Args:
            img: Imagem PIL
            upload: Upload
            
        Returns:
            Path: Caminho da imagem comprimida ou None
        """
        try:
            # Só comprime se a imagem for grande
            if upload.file_size < 1024 * 1024:  # Menor que 1MB
                return None

            file_path = Path(upload.file_path)
            compressed_path = file_path.parent / f"compressed_{file_path.name}"

            # Converte para RGB se necessário
            if img.mode in ("RGBA", "P"):
                img = img.convert("RGB")

            # Salva versão comprimida
            img.save(compressed_path, format="JPEG", quality=self.compressed_quality, optimize=True)
            
            logger.info(f"Imagem comprimida: {compressed_path}")
            return compressed_path

        except Exception as e:
            logger.error(f"Erro ao comprimir imagem: {e}")
            return None

    def regenerate_thumbnail(self, upload_id: UUID) -> bool:
        """
        Regenera thumbnail de um upload.
        
        Args:
            upload_id: ID do upload
            
        Returns:
            bool: True se regenerado com sucesso
        """
        try:
            upload = self.db.query(MediaUpload).filter(
                MediaUpload.id == upload_id
            ).first()

            if not upload or upload.media_type != MediaFileType.IMAGE:
                return False

            file_path = Path(upload.file_path)
            if not file_path.exists():
                return False

            with Image.open(file_path) as img:
                thumbnail_path = self._generate_thumbnail(img, upload)
                if thumbnail_path:
                    upload.thumbnail_path = str(thumbnail_path)
                    self.db.commit()
                    return True

            return False

        except Exception as e:
            logger.error(f"Erro ao regenerar thumbnail: {e}")
            return False

    def get_image_info(self, upload_id: UUID) -> Optional[Dict[str, Any]]:
        """
        Obtém informações detalhadas de uma imagem.
        
        Args:
            upload_id: ID do upload
            
        Returns:
            Dict: Informações da imagem ou None
        """
        try:
            upload = self.db.query(MediaUpload).filter(
                MediaUpload.id == upload_id
            ).first()

            if not upload or upload.media_type != MediaFileType.IMAGE:
                return None

            info = {
                "upload_id": str(upload.id),
                "filename": upload.filename,
                "original_filename": upload.original_filename,
                "file_size": upload.file_size,
                "mime_type": upload.mime_type,
                "has_thumbnail": bool(upload.thumbnail_path),
                "has_compressed": bool(upload.compressed_path),
                "metadata": json.loads(upload.file_metadata) if upload.file_metadata else {}
            }

            return info

        except Exception as e:
            logger.error(f"Erro ao obter informações da imagem: {e}")
            return None
