"""
Help Center Background Tasks

Tarefas Celery para processamento em background do help center.
"""

import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, Any

from celery import shared_task
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, func

from app.db.session import get_db
from app.modules.core.notifications.services.notification_service import NotificationService
from app.modules.core.help_center.models import Ticket, TicketMessage, TicketStatus
from app.modules.core.help_center.websockets import help_center_ws_manager
from app.modules.core.users.models.user import User

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def process_ticket_notification(self, ticket_data: Dict[str, Any], event_type: str):
    """
    Processa notificações de tickets em background.
    
    Args:
        ticket_data: Dados do ticket
        event_type: Tipo do evento (created, updated, message_sent)
    """
    try:
        import asyncio
        asyncio.run(_process_ticket_notification_async(ticket_data, event_type))
        
    except Exception as exc:
        logger.error(f"Erro ao processar notificação de ticket: {exc}")
        raise self.retry(exc=exc, countdown=60)


async def _process_ticket_notification_async(ticket_data: Dict[str, Any], event_type: str):
    """Versão async da tarefa de notificação."""
    async with get_db() as db:
        try:
            notification_service = NotificationService(db)
            
            if event_type == "ticket_created":
                # Notificar admins sobre novo ticket
                await _notify_admins_new_ticket(db, ticket_data)
                
                # Emitir evento WebSocket
                await help_center_ws_manager.emit_ticket_created(ticket_data)
                
            elif event_type == "message_sent":
                # Notificar sobre nova mensagem
                await _notify_message_sent(db, ticket_data)
                
                # Emitir evento WebSocket
                await help_center_ws_manager.emit_message_sent(
                    ticket_data["ticket_id"],
                    ticket_data["message"],
                    ticket_data["sender_id"]
                )
                
            elif event_type == "ticket_status_changed":
                # Notificar sobre mudança de status
                await _notify_status_changed(db, ticket_data)
                
                # Emitir evento WebSocket
                await help_center_ws_manager.emit_ticket_status_changed(
                    ticket_data["ticket_id"],
                    ticket_data["old_status"],
                    ticket_data["new_status"],
                    ticket_data["changed_by"]
                )
                
        except Exception as e:
            logger.error(f"Erro na notificação async: {e}")
            raise


async def _notify_admins_new_ticket(db: AsyncSession, ticket_data: Dict[str, Any]):
    """Notifica admins sobre novo ticket."""
    try:
        # Buscar todos os admins
        admin_stmt = select(User).where(User.system_role == "admin")
        admin_result = await db.execute(admin_stmt)
        admins = admin_result.scalars().all()
        
        notification_service = NotificationService(db)
        
        for admin in admins:
            await notification_service.create_notification(
                user_id=admin.id,
                title="Novo Ticket de Suporte",
                message=f"Ticket #{ticket_data['id'][:8]}: {ticket_data['title']}",
                notification_type="help_center_ticket",
                metadata={
                    "ticket_id": ticket_data["id"],
                    "priority": ticket_data.get("priority", "medium"),
                    "category": ticket_data.get("category", "question")
                }
            )
            
    except Exception as e:
        logger.error(f"Erro ao notificar admins: {e}")


async def _notify_message_sent(db: AsyncSession, data: Dict[str, Any]):
    """Notifica sobre nova mensagem."""
    try:
        ticket_id = data["ticket_id"]
        sender_id = data["sender_id"]
        
        # Buscar ticket
        ticket_stmt = select(Ticket).where(Ticket.id == ticket_id)
        ticket_result = await db.execute(ticket_stmt)
        ticket = ticket_result.scalar_one_or_none()
        
        if not ticket:
            return
        
        notification_service = NotificationService(db)
        
        # Notificar o usuário do ticket se a mensagem foi enviada por admin
        sender_stmt = select(User).where(User.id == sender_id)
        sender_result = await db.execute(sender_stmt)
        sender = sender_result.scalar_one_or_none()
        
        if sender and sender.system_role == "admin":
            # Admin respondeu, notificar usuário
            await notification_service.create_notification(
                user_id=ticket.user_id,
                title="Nova Resposta no Ticket",
                message=f"Seu ticket #{ticket.id[:8]} recebeu uma nova resposta",
                notification_type="help_center_message",
                metadata={
                    "ticket_id": str(ticket.id),
                    "message_id": data["message"]["id"]
                }
            )
        else:
            # Usuário respondeu, notificar admins
            admin_stmt = select(User).where(User.system_role == "admin")
            admin_result = await db.execute(admin_stmt)
            admins = admin_result.scalars().all()
            
            for admin in admins:
                await notification_service.create_notification(
                    user_id=admin.id,
                    title="Nova Mensagem no Ticket",
                    message=f"Ticket #{ticket.id[:8]} recebeu uma nova mensagem",
                    notification_type="help_center_message",
                    metadata={
                        "ticket_id": str(ticket.id),
                        "message_id": data["message"]["id"]
                    }
                )
                
    except Exception as e:
        logger.error(f"Erro ao notificar mensagem: {e}")


async def _notify_status_changed(db: AsyncSession, data: Dict[str, Any]):
    """Notifica sobre mudança de status."""
    try:
        ticket_id = data["ticket_id"]
        
        # Buscar ticket
        ticket_stmt = select(Ticket).where(Ticket.id == ticket_id)
        ticket_result = await db.execute(ticket_stmt)
        ticket = ticket_result.scalar_one_or_none()
        
        if not ticket:
            return
        
        notification_service = NotificationService(db)
        
        # Notificar usuário sobre mudança de status
        status_map = {
            "resolved": "resolvido",
            "closed": "fechado",
            "open": "reaberto",
            "pending": "pendente"
        }
        
        status_text = status_map.get(data["new_status"], data["new_status"])
        
        await notification_service.create_notification(
            user_id=ticket.user_id,
            title="Status do Ticket Atualizado",
            message=f"Seu ticket #{ticket.id[:8]} foi {status_text}",
            notification_type="help_center_status",
            metadata={
                "ticket_id": str(ticket.id),
                "old_status": data["old_status"],
                "new_status": data["new_status"]
            }
        )
        
    except Exception as e:
        logger.error(f"Erro ao notificar mudança de status: {e}")


@shared_task(bind=True)
def cleanup_expired_messages(self):
    """
    Remove mensagens expiradas (30 dias).
    """
    try:
        import asyncio
        result = asyncio.run(_cleanup_expired_messages_async())
        logger.info(f"Limpeza de mensagens: {result} mensagens removidas")
        return result
        
    except Exception as exc:
        logger.error(f"Erro na limpeza de mensagens: {exc}")
        raise


async def _cleanup_expired_messages_async() -> int:
    """Remove mensagens expiradas."""
    async with get_db() as db:
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=30)
            
            # Buscar mensagens expiradas
            expired_stmt = select(TicketMessage).where(
                TicketMessage.created_at < cutoff_date
            )
            
            result = await db.execute(expired_stmt)
            expired_messages = result.scalars().all()
            
            count = 0
            for message in expired_messages:
                await db.delete(message)
                count += 1
            
            await db.commit()
            return count
            
        except Exception as e:
            logger.error(f"Erro na limpeza async de mensagens: {e}")
            await db.rollback()
            return 0


@shared_task(bind=True)
def cleanup_expired_tickets(self):
    """
    Remove tickets expirados para usuários (365 dias).
    """
    try:
        import asyncio
        result = asyncio.run(_cleanup_expired_tickets_async())
        logger.info(f"Limpeza de tickets: {result} tickets removidos")
        return result
        
    except Exception as exc:
        logger.error(f"Erro na limpeza de tickets: {exc}")
        raise


async def _cleanup_expired_tickets_async() -> int:
    """Remove tickets expirados."""
    async with get_db() as db:
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=365)
            
            # Buscar tickets expirados (apenas para usuários, não admin)
            expired_stmt = select(Ticket).where(
                and_(
                    Ticket.created_at < cutoff_date,
                    Ticket.status.in_([TicketStatus.RESOLVED, TicketStatus.CLOSED])
                )
            )
            
            result = await db.execute(expired_stmt)
            expired_tickets = result.scalars().all()
            
            count = 0
            for ticket in expired_tickets:
                await db.delete(ticket)
                count += 1
            
            await db.commit()
            return count
            
        except Exception as e:
            logger.error(f"Erro na limpeza async de tickets: {e}")
            await db.rollback()
            return 0


@shared_task(bind=True)
def generate_help_center_report(self, admin_id: str):
    """
    Gera relatório do help center.
    """
    try:
        import asyncio
        asyncio.run(_generate_help_center_report_async(admin_id))
        
    except Exception as exc:
        logger.error(f"Erro ao gerar relatório: {exc}")
        raise


async def _generate_help_center_report_async(admin_id: str):
    """Gera relatório async."""
    async with get_db() as db:
        try:
            # Implementar geração de relatório
            # Por enquanto, apenas log
            logger.info(f"Relatório do help center gerado para admin {admin_id}")
            
        except Exception as e:
            logger.error(f"Erro na geração async de relatório: {e}")


@shared_task(bind=True, max_retries=3)
def send_ticket_email_notification(self, user_email: str, ticket_data: Dict[str, Any]):
    """
    Envia notificação por email sobre ticket.
    """
    try:
        # Implementar envio de email
        # Por enquanto, apenas log
        logger.info(f"Email enviado para {user_email} sobre ticket {ticket_data.get('id')}")
        
    except Exception as exc:
        logger.error(f"Erro ao enviar email: {exc}")
        raise self.retry(exc=exc, countdown=60)


@shared_task(bind=True)
def update_metrics_cache(self):
    """
    Atualiza cache de métricas do help center.
    """
    try:
        import asyncio
        asyncio.run(_update_metrics_cache_async())

    except Exception as exc:
        logger.error(f"Erro ao atualizar cache de métricas: {exc}")
        raise


async def _update_metrics_cache_async():
    """Atualiza cache de métricas async."""
    async with get_db() as db:
        try:
            from ..services.analytics_service import HelpCenterAnalyticsService

            analytics_service = HelpCenterAnalyticsService(db)
            metrics = await analytics_service.get_help_center_metrics()

            # Salvar no cache (Redis)
            # Implementar cache aqui
            logger.info("Cache de métricas atualizado com sucesso")

        except Exception as e:
            logger.error(f"Erro na atualização async de métricas: {e}")


@shared_task(bind=True)
def process_uploaded_file(self, file_data: Dict[str, Any]):
    """
    Processa arquivo enviado (thumbnails, validação, etc).
    """
    try:
        import asyncio
        asyncio.run(_process_uploaded_file_async(file_data))

    except Exception as exc:
        logger.error(f"Erro ao processar arquivo: {exc}")
        raise self.retry(exc=exc, countdown=60)


async def _process_uploaded_file_async(file_data: Dict[str, Any]):
    """Processa arquivo async."""
    try:
        # Implementar processamento de arquivo
        # - Gerar thumbnails para imagens
        # - Validar tipos de arquivo
        # - Escanear por vírus
        # - Otimizar tamanho

        logger.info(f"Arquivo processado: {file_data.get('filename')}")

    except Exception as e:
        logger.error(f"Erro no processamento async de arquivo: {e}")


@shared_task(bind=True)
def generate_daily_report(self):
    """
    Gera relatório diário do help center.
    """
    try:
        import asyncio
        asyncio.run(_generate_daily_report_async())

    except Exception as exc:
        logger.error(f"Erro ao gerar relatório diário: {exc}")
        raise


async def _generate_daily_report_async():
    """Gera relatório diário async."""
    async with get_db() as db:
        try:
            from ..services.analytics_service import HelpCenterAnalyticsService

            analytics_service = HelpCenterAnalyticsService(db)

            # Gerar métricas do dia
            today = datetime.now(timezone.utc).date()

            # Implementar geração de relatório
            logger.info(f"Relatório diário gerado para {today}")

        except Exception as e:
            logger.error(f"Erro na geração async de relatório: {e}")


@shared_task(bind=True)
def send_weekly_digest(self):
    """
    Envia digest semanal por email.
    """
    try:
        import asyncio
        asyncio.run(_send_weekly_digest_async())

    except Exception as exc:
        logger.error(f"Erro ao enviar digest semanal: {exc}")
        raise


async def _send_weekly_digest_async():
    """Envia digest semanal async."""
    async with get_db() as db:
        try:
            # Buscar admins
            admin_stmt = select(User).where(User.system_role == "admin")
            admin_result = await db.execute(admin_stmt)
            admins = admin_result.scalars().all()

            # Gerar dados da semana
            # Implementar envio de digest

            logger.info(f"Digest semanal enviado para {len(admins)} admins")

        except Exception as e:
            logger.error(f"Erro no envio async de digest: {e}")


@shared_task(bind=True)
def cleanup_orphaned_files(self):
    """
    Remove arquivos órfãos do sistema.
    """
    try:
        import asyncio
        result = asyncio.run(_cleanup_orphaned_files_async())
        logger.info(f"Limpeza de arquivos órfãos: {result} arquivos removidos")
        return result

    except Exception as exc:
        logger.error(f"Erro na limpeza de arquivos órfãos: {exc}")
        raise


async def _cleanup_orphaned_files_async() -> int:
    """Remove arquivos órfãos async."""
    async with get_db() as db:
        try:
            from ..models.file_upload import HelpCenterFileUpload

            # Buscar arquivos órfãos (sem ticket associado ou muito antigos)
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=1)

            orphaned_stmt = select(HelpCenterFileUpload).where(
                and_(
                    HelpCenterFileUpload.created_at < cutoff_date,
                    HelpCenterFileUpload.ticket_id.is_(None)
                )
            )

            result = await db.execute(orphaned_stmt)
            orphaned_files = result.scalars().all()

            count = 0
            for file_upload in orphaned_files:
                # Remover arquivo físico
                # Implementar remoção do sistema de arquivos

                # Remover do banco
                await db.delete(file_upload)
                count += 1

            await db.commit()
            return count

        except Exception as e:
            logger.error(f"Erro na limpeza async de arquivos órfãos: {e}")
            await db.rollback()
            return 0


@shared_task(bind=True)
def send_websocket_notification(self, notification_data: Dict[str, Any]):
    """
    Envia notificação via WebSocket.
    """
    try:
        import asyncio
        asyncio.run(_send_websocket_notification_async(notification_data))

    except Exception as exc:
        logger.error(f"Erro ao enviar notificação WebSocket: {exc}")
        raise self.retry(exc=exc, countdown=30)


async def _send_websocket_notification_async(notification_data: Dict[str, Any]):
    """Envia notificação WebSocket async."""
    try:
        from ..websockets.help_center_ws import help_center_ws_manager

        event_type = notification_data.get("event_type")
        data = notification_data.get("data", {})
        target_users = notification_data.get("target_users", [])

        if event_type == "ticket_notification":
            await help_center_ws_manager.emit_ticket_notification(data, target_users)
        elif event_type == "message_notification":
            await help_center_ws_manager.emit_message_notification(data, target_users)
        elif event_type == "status_change":
            await help_center_ws_manager.emit_status_change(data, target_users)

        logger.info(f"Notificação WebSocket enviada: {event_type}")

    except Exception as e:
        logger.error(f"Erro no envio async de notificação WebSocket: {e}")
