"""
Invoice Schemas for B2B Invoice Management
==========================================

Schemas de validação para sistema de faturas entre TVendorSupplier e TCostumer.
"""

import uuid
from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any, List

from pydantic import BaseModel, Field, ConfigDict, validator
from fastapi import UploadFile

from .models import InvoiceStatus, InvoiceType


class InvoiceLineItem(BaseModel):
    """Schema para item da fatura."""
    
    description: str = Field(..., max_length=255, description="Descrição do item")
    quantity: Decimal = Field(..., gt=0, description="Quantidade")
    unit_price: Decimal = Field(..., ge=0, description="Preço unitário")
    total_price: Decimal = Field(..., ge=0, description="Preço total")
    tax_rate: Optional[Decimal] = Field(None, ge=0, le=100, description="Taxa de imposto (%)")
    product_id: Optional[uuid.UUID] = Field(None, description="ID do produto (se aplicável)")


class InvoiceBase(BaseModel):
    """Schema base para fatura."""
    
    invoice_number: str = Field(..., max_length=100, description="Número da fatura")
    invoice_type: InvoiceType = Field(default=InvoiceType.STANDARD, description="Tipo de fatura")
    invoice_date: datetime = Field(..., description="Data da fatura")
    due_date: datetime = Field(..., description="Data de vencimento")
    
    subtotal: Decimal = Field(..., ge=0, description="Subtotal")
    tax_amount: Decimal = Field(default=Decimal('0.00'), ge=0, description="Valor do imposto")
    discount_amount: Decimal = Field(default=Decimal('0.00'), ge=0, description="Valor do desconto")
    total_amount: Decimal = Field(..., ge=0, description="Valor total")
    
    description: Optional[str] = Field(None, max_length=1000, description="Descrição")
    notes: Optional[str] = Field(None, max_length=2000, description="Observações")
    payment_terms: Optional[str] = Field(None, max_length=100, description="Termos de pagamento")
    payment_method: Optional[str] = Field(None, max_length=50, description="Método de pagamento")
    
    @validator('due_date')
    def validate_due_date(cls, v, values):
        """Valida se data de vencimento é posterior à data da fatura."""
        if 'invoice_date' in values and v <= values['invoice_date']:
            raise ValueError('Data de vencimento deve ser posterior à data da fatura')
        return v
    
    @validator('total_amount')
    def validate_total_amount(cls, v, values):
        """Valida se total está correto."""
        if 'subtotal' in values and 'tax_amount' in values and 'discount_amount' in values:
            expected_total = values['subtotal'] + values['tax_amount'] - values['discount_amount']
            if abs(v - expected_total) > Decimal('0.01'):
                raise ValueError('Total amount não confere com subtotal + tax - discount')
        return v


class InvoiceCreate(InvoiceBase):
    """Schema para criação de fatura."""
    
    customer_id: uuid.UUID = Field(..., description="ID do cliente")
    
    # Endereços
    billing_address: Optional[Dict[str, Any]] = Field(None, description="Endereço de cobrança")
    shipping_address: Optional[Dict[str, Any]] = Field(None, description="Endereço de entrega")
    
    # Itens da fatura
    line_items: List[InvoiceLineItem] = Field(..., min_items=1, description="Itens da fatura")
    
    # Configurações de notificação
    notify_on_view: bool = Field(default=True, description="Notificar quando visualizada")
    notify_on_due: bool = Field(default=True, description="Notificar quando vencer")
    
    # Configurações de acesso
    max_downloads: int = Field(default=10, ge=1, le=100, description="Máximo de downloads")


class InvoiceFileUpload(BaseModel):
    """Schema para upload de arquivo de fatura."""
    
    invoice_id: uuid.UUID = Field(..., description="ID da fatura")
    file: UploadFile = Field(..., description="Arquivo da fatura")
    
    class Config:
        arbitrary_types_allowed = True


class InvoiceUpdate(BaseModel):
    """Schema para atualização de fatura."""
    
    status: Optional[InvoiceStatus] = Field(None, description="Status da fatura")
    due_date: Optional[datetime] = Field(None, description="Nova data de vencimento")
    
    subtotal: Optional[Decimal] = Field(None, ge=0, description="Novo subtotal")
    tax_amount: Optional[Decimal] = Field(None, ge=0, description="Novo valor do imposto")
    discount_amount: Optional[Decimal] = Field(None, ge=0, description="Novo valor do desconto")
    total_amount: Optional[Decimal] = Field(None, ge=0, description="Novo valor total")
    
    description: Optional[str] = Field(None, max_length=1000, description="Nova descrição")
    notes: Optional[str] = Field(None, max_length=2000, description="Novas observações")
    payment_terms: Optional[str] = Field(None, max_length=100, description="Novos termos de pagamento")
    payment_method: Optional[str] = Field(None, max_length=50, description="Novo método de pagamento")
    
    billing_address: Optional[Dict[str, Any]] = Field(None, description="Novo endereço de cobrança")
    shipping_address: Optional[Dict[str, Any]] = Field(None, description="Novo endereço de entrega")
    
    line_items: Optional[List[InvoiceLineItem]] = Field(None, description="Novos itens da fatura")
    
    notify_on_view: Optional[bool] = Field(None, description="Notificar quando visualizada")
    notify_on_due: Optional[bool] = Field(None, description="Notificar quando vencer")
    max_downloads: Optional[int] = Field(None, ge=1, le=100, description="Máximo de downloads")


class InvoicePaymentUpdate(BaseModel):
    """Schema para atualização de pagamento."""
    
    paid_amount: Decimal = Field(..., gt=0, description="Valor pago")
    payment_date: Optional[datetime] = Field(None, description="Data do pagamento")
    payment_method: Optional[str] = Field(None, max_length=50, description="Método de pagamento")
    payment_reference: Optional[str] = Field(None, max_length=100, description="Referência do pagamento")
    notes: Optional[str] = Field(None, max_length=500, description="Observações do pagamento")


class InvoiceRead(InvoiceBase):
    """Schema para leitura de fatura."""
    
    id: uuid.UUID
    tenant_id: uuid.UUID
    vendor_id: uuid.UUID
    customer_id: uuid.UUID
    status: InvoiceStatus
    
    # Datas de controle
    sent_date: Optional[datetime]
    viewed_date: Optional[datetime]
    paid_date: Optional[datetime]
    
    # Valores de pagamento
    paid_amount: Decimal
    outstanding_amount: Decimal
    
    # Informações do arquivo
    file_name: str
    file_original_name: str
    file_size: int
    file_type: str
    
    # Controle de acesso
    download_count: int
    max_downloads: int
    access_expires_at: Optional[datetime]
    
    # Endereços
    billing_address: Optional[Dict[str, Any]]
    shipping_address: Optional[Dict[str, Any]]
    
    # Itens da fatura
    line_items: Optional[List[Dict[str, Any]]]
    
    # Configurações
    notify_on_view: bool
    notify_on_due: bool
    reminder_sent: bool
    
    # Timestamps
    created_at: datetime
    updated_at: datetime
    
    # Propriedades calculadas
    is_overdue: bool = False
    days_until_due: int = 0
    is_fully_paid: bool = False
    can_download: bool = True
    
    model_config = ConfigDict(from_attributes=True)


class InvoiceSummary(BaseModel):
    """Schema para resumo de fatura."""
    
    id: uuid.UUID
    invoice_number: str
    invoice_type: InvoiceType
    status: InvoiceStatus
    invoice_date: datetime
    due_date: datetime
    total_amount: Decimal
    paid_amount: Decimal
    outstanding_amount: Decimal
    is_overdue: bool
    days_until_due: int
    
    # Informações básicas do cliente e fornecedor
    vendor_company_name: Optional[str] = None
    customer_company_name: Optional[str] = None
    
    model_config = ConfigDict(from_attributes=True)


class InvoiceResponse(BaseModel):
    """Schema de resposta padrão para operações de fatura."""
    
    success: bool = Field(..., description="Sucesso da operação")
    message: str = Field(..., description="Mensagem de retorno")
    invoice: Optional[InvoiceRead] = Field(None, description="Dados da fatura")
    access_token: Optional[str] = Field(None, description="Token de acesso para download")
    
    model_config = ConfigDict(from_attributes=True)


class InvoiceListResponse(BaseModel):
    """Schema para resposta de lista de faturas."""
    
    invoices: List[InvoiceSummary] = Field(..., description="Lista de faturas")
    total: int = Field(..., description="Total de registros")
    page: int = Field(..., description="Página atual")
    limit: int = Field(..., description="Limite por página")
    total_pages: int = Field(..., description="Total de páginas")
    
    model_config = ConfigDict(from_attributes=True)


class InvoiceStats(BaseModel):
    """Schema para estatísticas de faturas."""
    
    total_invoices: int = Field(..., description="Total de faturas")
    pending_invoices: int = Field(..., description="Faturas pendentes")
    sent_invoices: int = Field(..., description="Faturas enviadas")
    viewed_invoices: int = Field(..., description="Faturas visualizadas")
    paid_invoices: int = Field(..., description="Faturas pagas")
    overdue_invoices: int = Field(..., description="Faturas vencidas")
    
    total_amount: Decimal = Field(..., description="Valor total das faturas")
    paid_amount: Decimal = Field(..., description="Valor total pago")
    outstanding_amount: Decimal = Field(..., description="Valor total em aberto")
    overdue_amount: Decimal = Field(..., description="Valor total vencido")
    
    average_invoice_value: Decimal = Field(..., description="Valor médio das faturas")
    average_payment_time: Optional[float] = Field(None, description="Tempo médio de pagamento (dias)")
    
    # Estatísticas por status
    status_stats: Dict[str, int] = Field(default_factory=dict, description="Estatísticas por status")
    
    # Estatísticas por tipo
    type_stats: Dict[str, int] = Field(default_factory=dict, description="Estatísticas por tipo")
    
    model_config = ConfigDict(from_attributes=True)


class InvoiceDownloadRequest(BaseModel):
    """Schema para solicitação de download."""
    
    access_token: Optional[str] = Field(None, description="Token de acesso")
    generate_new_token: bool = Field(default=False, description="Gerar novo token")
    expires_hours: int = Field(default=24, ge=1, le=168, description="Horas até expiração")
