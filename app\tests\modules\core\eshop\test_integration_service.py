"""
Unit tests for EShopIntegrationService.

Tests all integration functionality including:
- Inventory synchronization
- Order creation and processing
- Commission calculations
- CRM integration
- POS system integration
- Background task processing
"""

import pytest
import pytest_asyncio
from unittest.mock import Mock, AsyncMock, patch
from decimal import Decimal
from uuid import uuid4

from app.modules.core.eshop.services.integration_service import EShopIntegrationService
from app.modules.core.eshop.schemas.integration_schemas import (
    ProductSyncRequest,
    OrderCreateRequest,
    CommissionCalculationRequest
)


class TestEShopIntegrationService:
    """Unit tests for EShopIntegrationService."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        session = Mock()
        session.add = Mock()
        session.commit = AsyncMock()
        session.refresh = AsyncMock()
        session.execute = AsyncMock()
        session.scalar = AsyncMock()
        return session

    @pytest.fixture
    def mock_celery_app(self):
        """Mock Celery app for background tasks."""
        app = Mock()
        app.send_task = Mock()
        return app

    @pytest.fixture
    def integration_service(self, mock_db_session, mock_celery_app):
        """Create EShopIntegrationService instance with mocks."""
        return EShopIntegrationService(
            db_session=mock_db_session,
            celery_app=mock_celery_app
        )

    @pytest.fixture
    def sample_product_sync_request(self):
        """Sample product sync request."""
        return ProductSyncRequest(
            product_id=str(uuid4()),
            inventory_quantity=100,
            reserved_quantity=10,
            sync_type="real_time",
            source_system="eshop"
        )

    @pytest.fixture
    def sample_order_request(self):
        """Sample order creation request."""
        return OrderCreateRequest(
            cart_id=str(uuid4()),
            customer_id=str(uuid4()),
            shipping_address={
                "street": "123 Main St",
                "city": "Test City",
                "state": "TS",
                "zip_code": "12345"
            },
            payment_method="credit_card",
            items=[
                {
                    "product_id": str(uuid4()),
                    "quantity": 2,
                    "unit_price": 29.99
                },
                {
                    "product_id": str(uuid4()),
                    "quantity": 1,
                    "unit_price": 49.99
                }
            ]
        )

    @pytest_asyncio.async_test
    async def test_sync_product_inventory_success(
        self,
        integration_service,
        mock_db_session,
        sample_product_sync_request
    ):
        """Test successful product inventory synchronization."""
        # Arrange
        mock_product = Mock()
        mock_product.id = sample_product_sync_request.product_id
        mock_product.inventory_quantity = 50
        mock_db_session.scalar.return_value = mock_product

        mock_inventory_item = Mock()
        mock_inventory_item.quantity = 100
        mock_inventory_item.reserved_quantity = 10

        with patch.object(integration_service, '_get_inventory_item', 
                         return_value=mock_inventory_item):
            # Act
            result = await integration_service.sync_product_inventory(
                sample_product_sync_request
            )

            # Assert
            assert result.success is True
            assert result.updated_quantity == 100
            assert result.available_quantity == 90  # 100 - 10 reserved
            assert mock_product.inventory_quantity == 100
            mock_db_session.commit.assert_called()

    @pytest_asyncio.async_test
    async def test_sync_product_inventory_low_stock_alert(
        self,
        integration_service,
        mock_db_session,
        mock_celery_app,
        sample_product_sync_request
    ):
        """Test low stock alert during inventory sync."""
        # Arrange
        mock_product = Mock()
        mock_product.id = sample_product_sync_request.product_id
        mock_product.minimum_stock_level = 20
        mock_db_session.scalar.return_value = mock_product

        mock_inventory_item = Mock()
        mock_inventory_item.quantity = 15  # Below minimum
        mock_inventory_item.reserved_quantity = 0

        with patch.object(integration_service, '_get_inventory_item',
                         return_value=mock_inventory_item):
            # Act
            result = await integration_service.sync_product_inventory(
                sample_product_sync_request
            )

            # Assert
            assert result.low_stock_alert is True
            mock_celery_app.send_task.assert_called_with(
                'send_low_stock_notification',
                args=[sample_product_sync_request.product_id, 15, 20]
            )

    @pytest_asyncio.async_test
    async def test_create_order_from_cart_success(
        self,
        integration_service,
        mock_db_session,
        sample_order_request
    ):
        """Test successful order creation from cart."""
        # Arrange
        mock_cart = Mock()
        mock_cart.id = sample_order_request.cart_id
        mock_cart.items = sample_order_request.items
        mock_cart.total_amount = Decimal('109.97')

        mock_order = Mock()
        mock_order.id = str(uuid4())
        mock_order.status = "pending"

        with patch.object(integration_service, '_get_cart', return_value=mock_cart), \
             patch.object(integration_service, '_create_order', return_value=mock_order), \
             patch.object(integration_service, '_validate_inventory', return_value=True):
            
            # Act
            result = await integration_service.create_order_from_cart(
                sample_order_request
            )

            # Assert
            assert result.success is True
            assert result.order_id == mock_order.id
            assert result.status == "pending"
            mock_db_session.commit.assert_called()

    @pytest_asyncio.async_test
    async def test_create_order_insufficient_inventory(
        self,
        integration_service,
        sample_order_request
    ):
        """Test order creation failure due to insufficient inventory."""
        # Arrange
        mock_cart = Mock()
        mock_cart.id = sample_order_request.cart_id
        mock_cart.items = sample_order_request.items

        with patch.object(integration_service, '_get_cart', return_value=mock_cart), \
             patch.object(integration_service, '_validate_inventory', return_value=False):
            
            # Act
            result = await integration_service.create_order_from_cart(
                sample_order_request
            )

            # Assert
            assert result.success is False
            assert "insufficient inventory" in result.error_message.lower()

    @pytest_asyncio.async_test
    async def test_calculate_commission_b2b_vendor(
        self,
        integration_service,
        mock_db_session
    ):
        """Test commission calculation for B2B vendor."""
        # Arrange
        request = CommissionCalculationRequest(
            product_id=str(uuid4()),
            sale_amount=Decimal('1000.00'),
            market_type="B2B",
            vendor_id=str(uuid4())
        )

        mock_product = Mock()
        mock_product.commission_rate = Decimal('0.15')  # 15%
        mock_product.market_type = "B2B"
        mock_db_session.scalar.return_value = mock_product

        # Act
        result = await integration_service.calculate_commission(request)

        # Assert
        assert result.commission_amount == Decimal('150.00')  # 15% of 1000
        assert result.commission_rate == Decimal('0.15')
        assert result.net_amount == Decimal('850.00')  # 1000 - 150

    @pytest_asyncio.async_test
    async def test_calculate_commission_b2c_no_commission(
        self,
        integration_service,
        mock_db_session
    ):
        """Test commission calculation for B2C (no commission)."""
        # Arrange
        request = CommissionCalculationRequest(
            product_id=str(uuid4()),
            sale_amount=Decimal('500.00'),
            market_type="B2C",
            vendor_id=None
        )

        mock_product = Mock()
        mock_product.market_type = "B2C"
        mock_db_session.scalar.return_value = mock_product

        # Act
        result = await integration_service.calculate_commission(request)

        # Assert
        assert result.commission_amount == Decimal('0.00')
        assert result.commission_rate == Decimal('0.00')
        assert result.net_amount == Decimal('500.00')

    @pytest_asyncio.async_test
    async def test_integrate_with_crm_customer_sync(
        self,
        integration_service,
        mock_db_session,
        mock_celery_app
    ):
        """Test CRM integration for customer data sync."""
        # Arrange
        customer_data = {
            "customer_id": str(uuid4()),
            "email": "<EMAIL>",
            "first_name": "John",
            "last_name": "Doe",
            "phone": "+1234567890",
            "purchase_history": [
                {"order_id": str(uuid4()), "amount": 150.00},
                {"order_id": str(uuid4()), "amount": 75.50}
            ]
        }

        # Act
        result = await integration_service.integrate_with_crm(
            customer_data, 
            sync_type="customer_update"
        )

        # Assert
        assert result.success is True
        mock_celery_app.send_task.assert_called_with(
            'sync_customer_to_crm',
            args=[customer_data]
        )

    @pytest_asyncio.async_test
    async def test_enable_pos_integration_success(
        self,
        integration_service,
        mock_db_session
    ):
        """Test enabling POS integration for products."""
        # Arrange
        product_ids = [str(uuid4()), str(uuid4()), str(uuid4())]
        
        mock_products = []
        for pid in product_ids:
            mock_product = Mock()
            mock_product.id = pid
            mock_product.pos_enabled = False
            mock_products.append(mock_product)

        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = mock_products
        mock_db_session.execute.return_value = mock_result

        # Act
        result = await integration_service.enable_pos_integration(product_ids)

        # Assert
        assert result.success is True
        assert result.enabled_count == 3
        for mock_product in mock_products:
            assert mock_product.pos_enabled is True
        mock_db_session.commit.assert_called()

    @pytest_asyncio.async_test
    async def test_bulk_inventory_sync_background_task(
        self,
        integration_service,
        mock_celery_app
    ):
        """Test bulk inventory synchronization as background task."""
        # Arrange
        product_ids = [str(uuid4()) for _ in range(100)]  # Large batch
        sync_options = {
            "force_update": True,
            "send_notifications": True,
            "batch_size": 50
        }

        # Act
        result = await integration_service.bulk_sync_inventory(
            product_ids, 
            sync_options
        )

        # Assert
        assert result.task_id is not None
        assert result.status == "queued"
        mock_celery_app.send_task.assert_called_with(
            'bulk_inventory_sync',
            args=[product_ids, sync_options]
        )

    @pytest_asyncio.async_test
    async def test_real_time_inventory_update_websocket(
        self,
        integration_service,
        mock_db_session
    ):
        """Test real-time inventory updates via WebSocket."""
        # Arrange
        product_id = str(uuid4())
        tenant_id = str(uuid4())
        
        mock_product = Mock()
        mock_product.id = product_id
        mock_product.inventory_quantity = 75
        mock_db_session.scalar.return_value = mock_product

        with patch.object(integration_service, '_send_websocket_update') as mock_ws:
            # Act
            await integration_service.send_real_time_inventory_update(
                product_id, 
                tenant_id,
                new_quantity=75,
                previous_quantity=100
            )

            # Assert
            mock_ws.assert_called_once()
            call_args = mock_ws.call_args[0]
            assert call_args[0] == tenant_id
            assert "inventory_update" in call_args[1]

    @pytest_asyncio.async_test
    async def test_integration_health_check(
        self,
        integration_service,
        mock_db_session
    ):
        """Test integration service health check."""
        # Arrange
        mock_db_session.execute.return_value = Mock()

        with patch.object(integration_service, '_check_inventory_service', 
                         return_value=True), \
             patch.object(integration_service, '_check_order_service',
                         return_value=True), \
             patch.object(integration_service, '_check_crm_service',
                         return_value=True):
            
            # Act
            health = await integration_service.get_integration_health()

            # Assert
            assert health.overall_status == "healthy"
            assert health.inventory_service is True
            assert health.order_service is True
            assert health.crm_service is True

    @pytest_asyncio.async_test
    async def test_commission_calculation_tiered_rates(
        self,
        integration_service,
        mock_db_session
    ):
        """Test commission calculation with tiered rates."""
        # Arrange
        request = CommissionCalculationRequest(
            product_id=str(uuid4()),
            sale_amount=Decimal('5000.00'),  # High volume
            market_type="B2B",
            vendor_id=str(uuid4())
        )

        mock_vendor = Mock()
        mock_vendor.tier_level = "gold"  # High tier vendor
        mock_vendor.commission_rate = Decimal('0.10')  # Reduced rate for high tier

        with patch.object(integration_service, '_get_vendor_tier',
                         return_value=mock_vendor):
            # Act
            result = await integration_service.calculate_commission(request)

            # Assert
            assert result.commission_rate == Decimal('0.10')  # Tiered rate
            assert result.commission_amount == Decimal('500.00')  # 10% of 5000

    @pytest_asyncio.async_test
    async def test_order_fulfillment_integration(
        self,
        integration_service,
        mock_db_session,
        mock_celery_app
    ):
        """Test order fulfillment integration with shipping."""
        # Arrange
        order_id = str(uuid4())
        fulfillment_data = {
            "shipping_method": "standard",
            "estimated_delivery": "2024-01-15",
            "tracking_number": "TRK123456789",
            "carrier": "FedEx"
        }

        mock_order = Mock()
        mock_order.id = order_id
        mock_order.status = "confirmed"
        mock_db_session.scalar.return_value = mock_order

        # Act
        result = await integration_service.process_order_fulfillment(
            order_id,
            fulfillment_data
        )

        # Assert
        assert result.success is True
        assert mock_order.status == "shipped"
        mock_celery_app.send_task.assert_called_with(
            'send_shipping_notification',
            args=[order_id, fulfillment_data]
        )

    def test_integration_error_handling(self, integration_service):
        """Test error handling in integration scenarios."""
        # Test invalid product ID
        with pytest.raises(ValueError, match="Invalid product ID"):
            integration_service._validate_product_id("invalid-id")

        # Test invalid commission rate
        with pytest.raises(ValueError, match="Commission rate must be"):
            integration_service._validate_commission_rate(Decimal('1.5'))  # 150%

        # Test invalid sync type
        with pytest.raises(ValueError, match="Invalid sync type"):
            integration_service._validate_sync_type("invalid_sync")

    @pytest_asyncio.async_test
    async def test_batch_processing_with_error_recovery(
        self,
        integration_service,
        mock_db_session,
        mock_celery_app
    ):
        """Test batch processing with error recovery."""
        # Arrange
        product_ids = [str(uuid4()) for _ in range(10)]
        
        # Simulate some failures
        def mock_sync_product(product_id):
            if product_id == product_ids[3]:  # Simulate failure
                raise Exception("Sync failed")
            return {"success": True, "product_id": product_id}

        with patch.object(integration_service, '_sync_single_product',
                         side_effect=mock_sync_product):
            # Act
            result = await integration_service.batch_sync_with_recovery(
                product_ids,
                retry_failed=True
            )

            # Assert
            assert result.total_processed == 10
            assert result.successful_count == 9
            assert result.failed_count == 1
            assert len(result.failed_items) == 1 