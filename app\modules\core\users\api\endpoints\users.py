from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.auth_dependencies import get_current_active_user
from app.modules.core.auth.dependencies.auth_dependencies_part2 import require_system_role
from app.modules.core.roles.models.roles import SystemRole
from app.modules.core.users.models.user import User
from app.modules.core.users.schemas.user import User as UserSchema, UserCreate, UserUpdate, UserListResponse
from app.modules.core.users.services.user_service import user_service
from app.modules.core.tenants.services.tenant_service import tenant_service
from app.modules.core.tenants.schemas.tenant import Tenant as TenantSchema
from app.modules.core.users.schemas.tenant_user_association import TenantUserAssociationRead
from app.modules.core.users.models.tenant_user_association import TenantUserAssociation
from sqlalchemy.orm import selectinload
from sqlalchemy import select
import math

router = APIRouter()


@router.get("/me", response_model=UserSchema)
async def read_user_me(
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    Get current user.
    """
    return current_user


@router.put("/me", response_model=UserSchema)
async def update_user_me(
    *,
    db: AsyncSession = Depends(get_db),
    user_in: UserUpdate,
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    Update own user.
    """
    user = await user_service.update_user(db, db_obj=current_user, obj_in=user_in)
    return user


@router.get("/me/tenants", response_model=List[TenantSchema])
async def read_user_tenants(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    Get current user's tenants.
    """
    tenants_models = await tenant_service.get_user_tenants(db, user_id=current_user.id)
    # Convert ORM models to Pydantic schemas
    tenants_schemas = [TenantSchema.from_orm_model(t) for t in tenants_models]
    return tenants_schemas


@router.get("/me/tenant-associations", response_model=List[TenantUserAssociationRead])
async def read_user_tenant_associations(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    Get current user's tenant associations with roles.
    """
    try:
        stmt = (
            select(TenantUserAssociation)
            .options(
                selectinload(TenantUserAssociation.user),
                selectinload(TenantUserAssociation.tenant),
            )
            .where(TenantUserAssociation.user_id == current_user.id)
        )

        result = await db.execute(stmt)
        associations = result.scalars().all()
        return associations
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred while fetching user tenant associations: {e}",
        )


@router.get("/", response_model=UserListResponse)
async def read_users(
    db: AsyncSession = Depends(get_db),
    page: int = 1,
    limit: int = 10,
    current_user: User = Depends(require_system_role([SystemRole.ADMIN])),
) -> Any:
    """
    Retrieve users with pagination.
    """
    # Calculate skip based on page
    skip = (page - 1) * limit

    # Get users
    users = await user_service.get_users(db, skip=skip, limit=limit)

    # Get total count
    total_users_result = await db.execute(select(User))
    total = len(total_users_result.scalars().all())

    # Calculate total pages
    total_pages = math.ceil(total / limit) if total > 0 else 1

    return UserListResponse(
        users=users,
        total=total,
        page=page,
        limit=limit,
        total_pages=total_pages
    )


@router.post("/", response_model=UserSchema)
async def create_user(
    *,
    db: AsyncSession = Depends(get_db),
    user_in: UserCreate,
    current_user: User = Depends(require_system_role([SystemRole.ADMIN])),
) -> Any:
    """
    Create new user.
    """
    user = await user_service.get_user_by_email(db, email=user_in.email)
    if user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="The user with this email already exists in the system",
        )
    user = await user_service.create_user(db, obj_in=user_in)
    return user


@router.get("/stats")
async def get_user_stats(
    current_user: User = Depends(require_system_role([SystemRole.ADMIN])),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get user statistics (admin only).
    """
    # Get total users count
    total_users_result = await db.execute(select(User))
    total_users = len(total_users_result.scalars().all())

    # Get active users count
    active_users_result = await db.execute(select(User).where(User.is_active == True))
    active_users = len(active_users_result.scalars().all())

    # Get admin users count
    admin_users_result = await db.execute(select(User).where(User.system_role == SystemRole.ADMIN.value))
    admin_users = len(admin_users_result.scalars().all())

    # Get inactive users count
    inactive_users_result = await db.execute(select(User).where(User.is_active == False))
    inactive_users = len(inactive_users_result.scalars().all())

    # For now, we'll use simple counts since we don't have verified/premium/blacklisted fields
    verified_users = active_users  # Assume active users are verified
    premium_users = 0  # No premium field yet
    blacklisted_users = inactive_users  # Use inactive as blacklisted

    return {
        "total_users": total_users,
        "active_users": active_users,
        "admin_users": admin_users,
        "verified_users": verified_users,
        "premium_users": premium_users,
        "blacklisted_users": blacklisted_users,
    }


@router.get("/{user_id}", response_model=UserSchema)
async def read_user_by_id(
    user_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get a specific user by id.
    """
    user = await user_service.get_user(db, user_id=user_id)
    if user == current_user:
        return user
    if current_user.system_role != SystemRole.ADMIN.value:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="The user doesn't have enough privileges",
        )
    return user


@router.put("/{user_id}", response_model=UserSchema)
async def update_user(
    *,
    db: AsyncSession = Depends(get_db),
    user_id: str,
    user_in: UserUpdate,
    current_user: User = Depends(require_system_role([SystemRole.ADMIN])),
) -> Any:
    """
    Update a user.
    """
    user = await user_service.get_user(db, user_id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="The user with this ID does not exist in the system",
        )
    user = await user_service.update_user(db, db_obj=user, obj_in=user_in)
    return user


@router.get("/{user_id}/tenant-associations", response_model=List[TenantUserAssociationRead])
async def get_user_tenant_associations(
    user_id: str,
    current_user: User = Depends(require_system_role([SystemRole.ADMIN])),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Get tenant associations for a specific user.
    """
    try:
        stmt = (
            select(TenantUserAssociation)
            .options(
                selectinload(TenantUserAssociation.user),
                selectinload(TenantUserAssociation.tenant),
            )
            .where(TenantUserAssociation.user_id == user_id)
        )

        result = await db.execute(stmt)
        associations = result.scalars().all()
        return associations
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred while fetching user tenant associations: {e}",
        )
