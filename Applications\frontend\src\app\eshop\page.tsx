'use client';

import { useState, useEffect, useMemo } from 'react';
import { useSearchParams } from 'next/navigation';
import { Product, Category } from '@/types/eshop';

// Importando os novos componentes
import { EshopHeader } from './components/EshopHeader';
import { CategorySidebar } from './components/CategorySidebar';
import { ProductGrid } from './components/ProductGrid';
import { EshopCartProvider } from '@/contexts/EshopCartContext';
import { EshopCartSidebar } from './components/EshopCartSidebar';
import { EshopFloatingCart } from './components/EshopFloatingCart';

// Mock data (será substituído por chamada de API)
import { mockCategories, mockProducts } from '@/data/eshop-mock';
import { apiClient } from '@/lib/apiClient';


export default function EshopPage() {
  const searchParams = useSearchParams();
  
  // Estados para dados
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Estados para filtros e busca
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [sortBy, setSortBy] = useState('featured');

  useEffect(() => {
    // Seta a categoria inicial a partir dos parâmetros da URL
    const categoryFromUrl = searchParams.get('category');
    if (categoryFromUrl) {
      setSelectedCategory(categoryFromUrl);
    }
  }, [searchParams]);

  // Efeito para buscar os dados da API
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Simula uma chamada de API
        // Em um cenário real, você usaria o apiClient aqui
        // Ex: const productsData = await apiClient.get('/products');
        // Ex: const categoriesData = await apiClient.get('/categories');
        
        // Usando mock data por enquanto
        setProducts(mockProducts);
        setCategories(mockCategories);
        
      } catch (error) {
        console.error("Failed to fetch eshop data:", error);
        // Tratar erro, talvez com um toast
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, []);

  // Lógica de filtragem e ordenação
  const filteredAndSortedProducts = useMemo(() => {
    let filtered = products;

    if (searchTerm) {
      filtered = filtered.filter(p => 
        p.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        p.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    if (selectedCategory) {
      const selectedCat = categories.find(c => c.slug === selectedCategory);
      if(selectedCat) {
        filtered = filtered.filter(p => p.category_id === selectedCat.id);
      }
    }

    // Lógica de ordenação (exemplo)
    switch (sortBy) {
      case 'price-low':
        return [...filtered].sort((a, b) => (a.sale_price || a.base_price) - (b.sale_price || b.base_price));
      case 'price-high':
        return [...filtered].sort((a, b) => (b.sale_price || b.base_price) - (a.sale_price || a.base_price));
      case 'rating':
        return [...filtered].sort((a, b) => b.average_rating - a.average_rating);
      default:
        return filtered;
    }
  }, [products, categories, searchTerm, selectedCategory, sortBy]);

  return (
    <EshopCartProvider>
      <div className="min-h-screen bg-gray-50">
        <EshopHeader searchTerm={searchTerm} onSearchTermChange={setSearchTerm} />

        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex gap-8">
            <CategorySidebar
              categories={categories}
              selectedCategory={selectedCategory}
              onSelectCategory={setSelectedCategory}
            />

            <div className="flex-1">
              <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-gray-600">
                    {isLoading ? 'Carregando produtos...' : `${filteredAndSortedProducts.length} produtos encontrados`}
                  </p>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition"
                  >
                    <option value="featured">Destaques</option>
                    <option value="price-low">Menor preço</option>
                    <option value="price-high">Maior preço</option>
                    <option value="rating">Melhor avaliação</option>
                    <option value="newest">Mais recentes</option>
                  </select>
                </div>
              </div>

              <ProductGrid
                products={filteredAndSortedProducts}
                isLoading={isLoading}
              />
            </div>
          </div>
        </main>

        {/* Cart Components */}
        <EshopCartSidebar />
        <EshopFloatingCart />
      </div>
    </EshopCartProvider>
  );
}