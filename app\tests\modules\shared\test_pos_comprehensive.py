"""
Comprehensive POS Module Tests
Tests Point of Sale functionality, cash registers, transactions, and permissions
"""

import pytest
import subprocess
import json
import uuid
from typing import Dict, Any, Optional


class POSTester:
    """Helper class for POS module testing"""

    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.tokens = {}
        self.tenant_id = None
        self.cash_register_id = None
        self.transaction_id = None

    def curl_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        token: Optional[str] = None,
        tenant_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Execute curl request and return parsed JSON response"""

        cmd = ["curl", "-s", "-X", method, f"{self.base_url}{endpoint}"]

        # Add headers
        if headers:
            for key, value in headers.items():
                cmd.extend(["-H", f"{key}: {value}"])

        # Add authorization header
        if token:
            cmd.extend(["-H", f"Authorization: Bearer {token}"])

        # Add tenant header
        if tenant_id:
            cmd.extend(["-H", f"X-Tenant-ID: {tenant_id}"])

        # Add content type for POST/PUT requests
        if data:
            cmd.extend(["-H", "Content-Type: application/json"])
            cmd.extend(["-d", json.dumps(data)])

        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.stdout:
                return json.loads(result.stdout)
            return {"error": "No response", "stderr": result.stderr}
        except json.JSONDecodeError:
            return {"error": "Invalid JSON", "stdout": result.stdout}
        except Exception as e:
            return {"error": str(e)}

    def login(self, email: str, password: str) -> str:
        """Login and return access token"""
        response = self.curl_request(
            "POST", "/api/auth/login", data={"email": email, "password": password}
        )

        if "access_token" in response:
            token = response["access_token"]
            self.tokens[email] = token
            return token

        raise Exception(f"Login failed for {email}: {response}")

    def get_user_tenant(self, token: str) -> str:
        """Get user's first tenant ID"""
        response = self.curl_request("GET", "/api/users/me/tenants", token=token)

        tenants = response if isinstance(response, list) else response.get("tenants", [])
        if tenants:
            return tenants[0].get("id") or tenants[0].get("tenant_id")

        return None


@pytest.fixture
def pos_tester():
    """Fixture providing POS tester instance"""
    tester = POSTester()
    # Setup tenant context
    token = tester.login("<EMAIL>", "password")
    tester.tenant_id = tester.get_user_tenant(token)
    return tester


class TestPOSPermissions:
    """Test POS module access permissions"""

    def test_tenant_owner_can_access_pos(self, pos_tester):
        """Test tenant owner can access POS module"""
        token = pos_tester.tokens["<EMAIL>"]

        response = pos_tester.curl_request(
            "GET",
            "/api/modules/shared/pos/cash-registers",
            token=token,
            tenant_id=pos_tester.tenant_id,
        )

        # Should return list or successful response
        assert "error" not in response or response.get("status_code") != 403
        assert isinstance(response, list) or "cash_registers" in response or "items" in response

    def test_customer_cannot_access_pos(self, pos_tester):
        """Test customer cannot access POS module"""
        token = pos_tester.login("<EMAIL>", "password")

        response = pos_tester.curl_request(
            "GET",
            "/api/modules/shared/pos/cash-registers",
            token=token,
            tenant_id=pos_tester.tenant_id,
        )

        # Should return 403 Forbidden
        assert (
            "error" in response
            or "detail" in response
            or response.get("status_code") == 403
            or "Forbidden" in str(response)
            or "permission" in str(response).lower()
        )

    def test_admin_can_access_pos(self, pos_tester):
        """Test admin can access POS module"""
        token = pos_tester.login("<EMAIL>", "password")

        response = pos_tester.curl_request(
            "GET",
            "/api/modules/shared/pos/cash-registers",
            token=token,
            tenant_id=pos_tester.tenant_id,
        )

        # Admin should have access
        assert "error" not in response or response.get("status_code") != 403

    def test_unauthenticated_cannot_access_pos(self, pos_tester):
        """Test unauthenticated user cannot access POS module"""
        response = pos_tester.curl_request(
            "GET", "/api/modules/shared/pos/cash-registers", tenant_id=pos_tester.tenant_id
        )

        # Should return 401 Unauthorized
        assert (
            "error" in response
            or "detail" in response
            or "Unauthorized" in str(response)
            or "authentication" in str(response).lower()
        )


class TestCashRegisterManagement:
    """Test cash register CRUD operations"""

    def test_create_cash_register(self, pos_tester):
        """Test creating a new cash register"""
        token = pos_tester.tokens["<EMAIL>"]

        register_data = {
            "name": f"Test Register {uuid.uuid4().hex[:8]}",
            "location": "Test Location",
            "is_active": True,
        }

        response = pos_tester.curl_request(
            "POST",
            "/api/modules/shared/pos/cash-registers",
            data=register_data,
            token=token,
            tenant_id=pos_tester.tenant_id,
        )

        if "error" not in response and "detail" not in response:
            # Cash register creation successful
            assert "id" in response or "cash_register_id" in response
            assert "name" in response
            pos_tester.cash_register_id = response.get("id") or response.get("cash_register_id")
        else:
            # Check if it's a permission error or other issue
            assert "error" in response or "detail" in response

    def test_list_cash_registers(self, pos_tester):
        """Test listing cash registers"""
        token = pos_tester.tokens["<EMAIL>"]

        response = pos_tester.curl_request(
            "GET",
            "/api/modules/shared/pos/cash-registers",
            token=token,
            tenant_id=pos_tester.tenant_id,
        )

        # Should return list of cash registers
        assert "error" not in response or response.get("status_code") != 403
        assert isinstance(response, list) or "cash_registers" in response or "items" in response

    def test_get_cash_register_details(self, pos_tester):
        """Test getting cash register details"""
        if not pos_tester.cash_register_id:
            pytest.skip("No cash register ID available")

        token = pos_tester.tokens["<EMAIL>"]

        response = pos_tester.curl_request(
            "GET",
            f"/api/modules/shared/pos/cash-registers/{pos_tester.cash_register_id}",
            token=token,
            tenant_id=pos_tester.tenant_id,
        )

        if "error" not in response and "detail" not in response:
            # Should return cash register details
            assert "id" in response or "cash_register_id" in response
            assert "name" in response
        else:
            # Endpoint might not exist or register not found
            assert "error" in response or "detail" in response

    def test_update_cash_register(self, pos_tester):
        """Test updating cash register"""
        if not pos_tester.cash_register_id:
            pytest.skip("No cash register ID available")

        token = pos_tester.tokens["<EMAIL>"]

        update_data = {
            "name": f"Updated Register {uuid.uuid4().hex[:8]}",
            "location": "Updated Location",
        }

        response = pos_tester.curl_request(
            "PUT",
            f"/api/modules/shared/pos/cash-registers/{pos_tester.cash_register_id}",
            data=update_data,
            token=token,
            tenant_id=pos_tester.tenant_id,
        )

        if "error" not in response and "detail" not in response:
            # Update successful
            assert "id" in response or "cash_register_id" in response
        else:
            # Endpoint might not exist
            assert "error" in response or "detail" in response


class TestTransactionManagement:
    """Test transaction management"""

    @pytest.fixture(autouse=True)
    def setup_cash_register(self, pos_tester):
        """Setup cash register for transaction tests"""
        token = pos_tester.tokens["<EMAIL>"]

        # Try to create a cash register first
        register_data = {
            "name": f"Transaction Test Register {uuid.uuid4().hex[:8]}",
            "location": "Test Location",
            "is_active": True,
        }

        response = pos_tester.curl_request(
            "POST",
            "/api/modules/shared/pos/cash-registers",
            data=register_data,
            token=token,
            tenant_id=pos_tester.tenant_id,
        )

        if "id" in response or "cash_register_id" in response:
            pos_tester.cash_register_id = response.get("id") or response.get("cash_register_id")

    def test_create_transaction(self, pos_tester):
        """Test creating a new transaction"""
        if not pos_tester.cash_register_id:
            pytest.skip("No cash register available for transaction")

        token = pos_tester.tokens["<EMAIL>"]

        transaction_data = {"total_amount": 25.50, "payment_method": "cash", "status": "completed"}

        response = pos_tester.curl_request(
            "POST",
            f"/api/modules/shared/pos/cash-registers/{pos_tester.cash_register_id}/transactions",
            data=transaction_data,
            token=token,
            tenant_id=pos_tester.tenant_id,
        )

        if "error" not in response and "detail" not in response:
            # Transaction creation successful
            assert "id" in response or "transaction_id" in response
            assert "total_amount" in response
            pos_tester.transaction_id = response.get("id") or response.get("transaction_id")
        else:
            # Check if it's a permission error or other issue
            assert "error" in response or "detail" in response

    def test_list_transactions(self, pos_tester):
        """Test listing transactions"""
        if not pos_tester.cash_register_id:
            pytest.skip("No cash register available")

        token = pos_tester.tokens["<EMAIL>"]

        response = pos_tester.curl_request(
            "GET",
            f"/api/modules/shared/pos/cash-registers/{pos_tester.cash_register_id}/transactions",
            token=token,
            tenant_id=pos_tester.tenant_id,
        )

        # Should return list of transactions
        if "error" not in response and "detail" not in response:
            assert isinstance(response, list) or "transactions" in response or "items" in response
        else:
            # Endpoint might not exist
            assert "error" in response or "detail" in response

    def test_get_transaction_details(self, pos_tester):
        """Test getting transaction details"""
        if not pos_tester.transaction_id:
            pytest.skip("No transaction ID available")

        token = pos_tester.tokens["<EMAIL>"]

        response = pos_tester.curl_request(
            "GET",
            f"/api/modules/shared/pos/transactions/{pos_tester.transaction_id}",
            token=token,
            tenant_id=pos_tester.tenant_id,
        )

        if "error" not in response and "detail" not in response:
            # Should return transaction details
            assert "id" in response or "transaction_id" in response
            assert "total_amount" in response
        else:
            # Endpoint might not exist or transaction not found
            assert "error" in response or "detail" in response


class TestPOSTenantIsolation:
    """Test tenant isolation in POS module"""

    def test_cannot_access_other_tenant_data(self, pos_tester):
        """Test that users cannot access other tenant's POS data"""
        token = pos_tester.tokens["<EMAIL>"]
        fake_tenant_id = str(uuid.uuid4())

        response = pos_tester.curl_request(
            "GET", "/api/modules/shared/pos/cash-registers", token=token, tenant_id=fake_tenant_id
        )

        # Should return error about tenant not found or access denied
        assert (
            "error" in response
            or "detail" in response
            or "tenant" in str(response).lower()
            or "not found" in str(response).lower()
            or "access" in str(response).lower()
        )

    def test_missing_tenant_header_error(self, pos_tester):
        """Test that missing tenant header returns appropriate error"""
        token = pos_tester.tokens["<EMAIL>"]

        response = pos_tester.curl_request(
            "GET", "/api/modules/shared/pos/cash-registers", token=token
        )

        # Should return error about missing tenant header
        assert (
            "error" in response
            or "detail" in response
            or "tenant" in str(response).lower()
            or "header" in str(response).lower()
        )
