"""
Teste simples para verificar se a API B2B de invoices está funcionando
"""

import requests
import json

# URL base da API
BASE_URL = "http://localhost:8000/api/financial/invoices"

def test_b2b_invoice_endpoints():
    """Testa se os endpoints B2B estão acessíveis"""
    
    # Teste 1: Verificar se o endpoint de listagem existe
    try:
        response = requests.get(f"{BASE_URL}/b2b-invoices/")
        print(f"GET /b2b-invoices/ - Status: {response.status_code}")
        if response.status_code == 401:
            print("✓ Endpoint existe (requer autenticação)")
        elif response.status_code == 200:
            print("✓ Endpoint acessível")
        else:
            print(f"? Status inesperado: {response.status_code}")
    except Exception as e:
        print(f"✗ Erro ao acessar endpoint: {e}")
    
    # Teste 2: Verificar endpoint de estatísticas
    try:
        response = requests.get(f"{BASE_URL}/b2b-invoices/stats/summary")
        print(f"GET /b2b-invoices/stats/summary - Status: {response.status_code}")
        if response.status_code == 401:
            print("✓ Endpoint existe (requer autenticação)")
        elif response.status_code == 200:
            print("✓ Endpoint acessível")
        else:
            print(f"? Status inesperado: {response.status_code}")
    except Exception as e:
        print(f"✗ Erro ao acessar endpoint: {e}")
    
    # Teste 3: Verificar se a API está registrada corretamente
    try:
        response = requests.get("http://localhost:8000/docs")
        print(f"GET /docs - Status: {response.status_code}")
        if response.status_code == 200:
            print("✓ Documentação da API acessível")
        else:
            print(f"? Status inesperado: {response.status_code}")
    except Exception as e:
        print(f"✗ Erro ao acessar documentação: {e}")

if __name__ == "__main__":
    print("Testando API B2B de Invoices...")
    print("=" * 50)
    test_b2b_invoice_endpoints()
    print("=" * 50)
    print("Teste concluído!")
