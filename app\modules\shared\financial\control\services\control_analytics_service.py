"""Financial Control Analytics Service."""

from typing import List, Optional, Dict, Any, Tuple
from uuid import UUID
from datetime import date, datetime, timedelta
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func, case, extract
from sqlalchemy.orm import selectinload

from ..models.control_entry import FinancialControlEntry, ControlEntryStatus, ControlEntryType
from ..models.control_category import ControlCategory
from ..schemas.control_analytics_schemas import (
    ControlMetrics, CategoryBreakdown, CashFlowData, AnalyticsFilters,
    DashboardData, TrendData, ComparisonData, BudgetAnalysis
)


class ControlAnalyticsService:
    """Service for financial control analytics and reporting."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_dashboard_data(
        self, 
        tenant_id: UUID, 
        filters: Optional[AnalyticsFilters] = None
    ) -> DashboardData:
        """Get comprehensive dashboard data."""
        
        # Get basic metrics
        metrics = await self.get_control_metrics(tenant_id, filters)
        
        # Get chart data
        cash_flow_chart = await self.get_cash_flow_data(tenant_id, filters)
        category_chart = await self.get_category_breakdown(tenant_id, filters)
        trend_chart = await self.get_trend_data(tenant_id, filters)
        
        # Get budget analysis
        budget_analysis = await self.get_budget_analysis(tenant_id, filters)
        
        # Get comparison data
        comparison_data = await self.get_comparison_data(tenant_id, filters)
        
        # Get alerts
        alerts = await self.get_alerts(tenant_id)
        
        return DashboardData(
            metrics=metrics,
            cash_flow_chart=cash_flow_chart,
            category_chart=category_chart,
            trend_chart=trend_chart,
            budget_analysis=budget_analysis,
            comparison_data=comparison_data,
            alerts=alerts
        )
    
    async def get_control_metrics(
        self, 
        tenant_id: UUID, 
        filters: Optional[AnalyticsFilters] = None
    ) -> ControlMetrics:
        """Calculate comprehensive financial control metrics."""
        
        # Base query
        query = select(FinancialControlEntry).where(
            FinancialControlEntry.tenant_id == tenant_id
        )
        
        # Apply filters
        if filters:
            query = self._apply_analytics_filters(query, filters)
        
        # Get all entries
        result = await self.db.execute(query)
        entries = result.scalars().all()
        
        # Calculate basic metrics
        total_income = sum(
            entry.amount for entry in entries 
            if entry.entry_type == ControlEntryType.INCOME and entry.status != ControlEntryStatus.ARCHIVED
        )
        
        total_expense = sum(
            entry.amount for entry in entries 
            if entry.entry_type == ControlEntryType.EXPENSE and entry.status != ControlEntryStatus.ARCHIVED
        )
        
        net_balance = total_income - total_expense
        total_transactions = len([e for e in entries if e.status != ControlEntryStatus.ARCHIVED])
        
        # Calculate pending metrics
        pending_entries = [e for e in entries if e.status == ControlEntryStatus.PENDING]
        pending_invoices = len(pending_entries)
        pending_amount = sum(entry.amount for entry in pending_entries)
        
        # Calculate overdue metrics
        overdue_entries = [
            e for e in entries 
            if e.status != ControlEntryStatus.PAID and e.due_date and e.due_date < date.today()
        ]
        overdue_invoices = len(overdue_entries)
        overdue_amount = sum(entry.amount for entry in overdue_entries)
        
        # Calculate growth metrics
        growth_data = await self._calculate_growth_metrics(tenant_id, filters)
        
        # Get category breakdown
        category_breakdown = await self.get_category_breakdown(tenant_id, filters)
        
        # Get cash flow data
        cash_flow_data = await self.get_cash_flow_data(tenant_id, filters)
        
        # Get recent transactions
        recent_transactions = await self._get_recent_transactions(tenant_id, limit=5)
        
        # Determine period information
        period_start, period_end, period_type = self._get_period_info(filters)
        
        return ControlMetrics(
            total_income=total_income,
            total_expense=total_expense,
            net_balance=net_balance,
            total_transactions=total_transactions,
            pending_invoices=pending_invoices,
            pending_amount=pending_amount,
            overdue_invoices=overdue_invoices,
            overdue_amount=overdue_amount,
            monthly_growth=growth_data.get('monthly_growth', 0),
            income_growth=growth_data.get('income_growth', 0),
            expense_growth=growth_data.get('expense_growth', 0),
            category_breakdown=category_breakdown,
            cash_flow_data=cash_flow_data,
            recent_transactions=recent_transactions,
            period_start=period_start,
            period_end=period_end,
            period_type=period_type
        )
    
    async def get_category_breakdown(
        self, 
        tenant_id: UUID, 
        filters: Optional[AnalyticsFilters] = None
    ) -> List[CategoryBreakdown]:
        """Get expense breakdown by category."""
        
        # Query for category breakdown
        query = select(
            ControlCategory.id,
            ControlCategory.name,
            ControlCategory.color,
            func.sum(FinancialControlEntry.amount).label('total_amount'),
            func.count(FinancialControlEntry.id).label('transaction_count')
        ).select_from(
            FinancialControlEntry
        ).join(
            ControlCategory, FinancialControlEntry.category_id == ControlCategory.id, isouter=True
        ).where(
            and_(
                FinancialControlEntry.tenant_id == tenant_id,
                FinancialControlEntry.entry_type == ControlEntryType.EXPENSE,
                FinancialControlEntry.status != ControlEntryStatus.ARCHIVED
            )
        ).group_by(
            ControlCategory.id, ControlCategory.name, ControlCategory.color
        ).order_by(
            func.sum(FinancialControlEntry.amount).desc()
        )
        
        # Apply filters
        if filters:
            query = self._apply_analytics_filters(query, filters)
        
        result = await self.db.execute(query)
        rows = result.all()
        
        # Calculate total for percentages
        total_expense = sum(row.total_amount for row in rows if row.total_amount)
        
        # Build breakdown
        breakdown = []
        for row in rows:
            if row.total_amount and row.total_amount > 0:
                percentage = (float(row.total_amount) / float(total_expense)) * 100 if total_expense > 0 else 0
                
                breakdown.append(CategoryBreakdown(
                    category_id=row.id,
                    category_name=row.name or "Uncategorized",
                    amount=row.total_amount,
                    percentage=round(percentage, 2),
                    transaction_count=row.transaction_count,
                    color=row.color
                ))
        
        return breakdown
    
    async def get_cash_flow_data(
        self, 
        tenant_id: UUID, 
        filters: Optional[AnalyticsFilters] = None
    ) -> List[CashFlowData]:
        """Get cash flow data over time."""
        
        # Determine grouping period
        group_by = "month"  # Default to monthly
        if filters and filters.period_type:
            if filters.period_type in ["today", "week"]:
                group_by = "day"
            elif filters.period_type in ["quarter", "year"]:
                group_by = "month"
        
        # Build query based on grouping
        if group_by == "day":
            date_part = FinancialControlEntry.entry_date
            date_format = "YYYY-MM-DD"
        else:  # month
            date_part = func.date_trunc('month', FinancialControlEntry.entry_date)
            date_format = "YYYY-MM"
        
        query = select(
            date_part.label('period'),
            FinancialControlEntry.entry_type,
            func.sum(FinancialControlEntry.amount).label('total_amount'),
            func.count(FinancialControlEntry.id).label('transaction_count')
        ).where(
            and_(
                FinancialControlEntry.tenant_id == tenant_id,
                FinancialControlEntry.status != ControlEntryStatus.ARCHIVED
            )
        ).group_by(
            date_part, FinancialControlEntry.entry_type
        ).order_by(
            date_part
        )
        
        # Apply filters
        if filters:
            query = self._apply_analytics_filters(query, filters)
        
        result = await self.db.execute(query)
        rows = result.all()
        
        # Group by period
        periods = {}
        for row in rows:
            period_key = row.period.strftime("%Y-%m-%d" if group_by == "day" else "%Y-%m")
            
            if period_key not in periods:
                periods[period_key] = {
                    'period': period_key,
                    'income': Decimal('0'),
                    'expense': Decimal('0'),
                    'transaction_count': 0
                }
            
            if row.entry_type == ControlEntryType.INCOME:
                periods[period_key]['income'] += row.total_amount
            else:
                periods[period_key]['expense'] += row.total_amount
            
            periods[period_key]['transaction_count'] += row.transaction_count
        
        # Convert to CashFlowData objects
        cash_flow_data = []
        for period_data in periods.values():
            net_flow = period_data['income'] - period_data['expense']
            
            # Format period name
            if group_by == "day":
                period_name = datetime.strptime(period_data['period'], "%Y-%m-%d").strftime("%b %d, %Y")
            else:
                period_name = datetime.strptime(period_data['period'], "%Y-%m").strftime("%B %Y")
            
            cash_flow_data.append(CashFlowData(
                period=period_data['period'],
                period_name=period_name,
                income=period_data['income'],
                expense=period_data['expense'],
                net_flow=net_flow,
                transaction_count=period_data['transaction_count']
            ))
        
        return sorted(cash_flow_data, key=lambda x: x.period)
    
    async def get_trend_data(
        self, 
        tenant_id: UUID, 
        filters: Optional[AnalyticsFilters] = None
    ) -> List[TrendData]:
        """Get trend analysis data."""
        
        # Get cash flow data for trend calculation
        cash_flow_data = await self.get_cash_flow_data(tenant_id, filters)
        
        trend_data = []
        for i, current in enumerate(cash_flow_data):
            if i == 0:
                # First period, no previous data for comparison
                trend_data.append(TrendData(
                    period=current.period,
                    value=current.net_flow,
                    change=0.0,
                    change_amount=Decimal('0')
                ))
            else:
                previous = cash_flow_data[i - 1]
                change_amount = current.net_flow - previous.net_flow
                
                if previous.net_flow != 0:
                    change_percentage = (float(change_amount) / float(abs(previous.net_flow))) * 100
                else:
                    change_percentage = 100.0 if change_amount > 0 else 0.0
                
                trend_data.append(TrendData(
                    period=current.period,
                    value=current.net_flow,
                    change=round(change_percentage, 2),
                    change_amount=change_amount
                ))
        
        return trend_data
    
    async def get_budget_analysis(
        self, 
        tenant_id: UUID, 
        filters: Optional[AnalyticsFilters] = None
    ) -> List[BudgetAnalysis]:
        """Get budget vs actual analysis."""
        
        # Get categories with budget limits
        query = select(ControlCategory).where(
            and_(
                ControlCategory.tenant_id == tenant_id,
                ControlCategory.monthly_budget_limit.isnot(None)
            )
        )
        
        result = await self.db.execute(query)
        categories = result.scalars().all()
        
        budget_analysis = []
        for category in categories:
            # Get actual spending for this category
            actual_query = select(
                func.sum(FinancialControlEntry.amount)
            ).where(
                and_(
                    FinancialControlEntry.tenant_id == tenant_id,
                    FinancialControlEntry.category_id == category.id,
                    FinancialControlEntry.entry_type == ControlEntryType.EXPENSE,
                    FinancialControlEntry.status != ControlEntryStatus.ARCHIVED
                )
            )
            
            # Apply date filters if provided
            if filters:
                actual_query = self._apply_analytics_filters(actual_query, filters)
            
            actual_result = await self.db.execute(actual_query)
            actual_amount = actual_result.scalar() or Decimal('0')
            
            # Parse budget limit (assuming it's stored as JSON with monthly limit)
            try:
                import json
                budget_data = json.loads(category.monthly_budget_limit)
                budgeted_amount = Decimal(str(budget_data.get('amount', 0)))
            except:
                budgeted_amount = Decimal('0')
            
            if budgeted_amount > 0:
                variance = actual_amount - budgeted_amount
                variance_percentage = (float(variance) / float(budgeted_amount)) * 100
                is_over_budget = variance > 0
                
                budget_analysis.append(BudgetAnalysis(
                    category_id=category.id,
                    category_name=category.name,
                    budgeted_amount=budgeted_amount,
                    actual_amount=actual_amount,
                    variance=variance,
                    variance_percentage=round(variance_percentage, 2),
                    is_over_budget=is_over_budget
                ))
        
        return budget_analysis
    
    async def get_comparison_data(
        self, 
        tenant_id: UUID, 
        filters: Optional[AnalyticsFilters] = None
    ) -> Optional[ComparisonData]:
        """Get period-over-period comparison data."""
        
        if not filters or not filters.date_from or not filters.date_to:
            return None
        
        # Calculate period length
        current_start = filters.date_from
        current_end = filters.date_to
        period_length = (current_end - current_start).days
        
        # Calculate previous period
        previous_end = current_start - timedelta(days=1)
        previous_start = previous_end - timedelta(days=period_length)
        
        # Get current period metrics
        current_filters = AnalyticsFilters(
            date_from=current_start,
            date_to=current_end,
            **filters.model_dump(exclude={'date_from', 'date_to'})
        )
        current_metrics = await self.get_control_metrics(tenant_id, current_filters)
        
        # Get previous period metrics
        previous_filters = AnalyticsFilters(
            date_from=previous_start,
            date_to=previous_end,
            **filters.model_dump(exclude={'date_from', 'date_to'})
        )
        previous_metrics = await self.get_control_metrics(tenant_id, previous_filters)
        
        # Calculate comparisons
        def calculate_change(current: Decimal, previous: Decimal) -> float:
            if previous == 0:
                return 100.0 if current > 0 else 0.0
            return (float(current - previous) / float(previous)) * 100
        
        comparison = {
            'income_change': calculate_change(current_metrics.total_income, previous_metrics.total_income),
            'expense_change': calculate_change(current_metrics.total_expense, previous_metrics.total_expense),
            'balance_change': calculate_change(current_metrics.net_balance, previous_metrics.net_balance),
            'transaction_change': calculate_change(
                Decimal(current_metrics.total_transactions), 
                Decimal(previous_metrics.total_transactions)
            )
        }
        
        return ComparisonData(
            current_period=current_metrics.model_dump(),
            previous_period=previous_metrics.model_dump(),
            comparison=comparison
        )
    
    async def get_alerts(self, tenant_id: UUID) -> List[Dict[str, Any]]:
        """Get financial alerts and notifications."""
        
        alerts = []
        
        # Overdue invoices alert
        overdue_query = select(func.count(FinancialControlEntry.id)).where(
            and_(
                FinancialControlEntry.tenant_id == tenant_id,
                FinancialControlEntry.status != ControlEntryStatus.PAID,
                FinancialControlEntry.due_date < date.today()
            )
        )
        
        overdue_result = await self.db.execute(overdue_query)
        overdue_count = overdue_result.scalar()
        
        if overdue_count > 0:
            alerts.append({
                'type': 'warning',
                'title': 'Overdue Invoices',
                'message': f'You have {overdue_count} overdue invoice(s)',
                'action': 'View overdue invoices',
                'priority': 'high'
            })
        
        # Budget alerts
        budget_analysis = await self.get_budget_analysis(tenant_id)
        over_budget_categories = [b for b in budget_analysis if b.is_over_budget]
        
        if over_budget_categories:
            alerts.append({
                'type': 'error',
                'title': 'Budget Exceeded',
                'message': f'{len(over_budget_categories)} categories are over budget',
                'action': 'Review budget',
                'priority': 'high'
            })
        
        return alerts
    
    def _apply_analytics_filters(self, query, filters: AnalyticsFilters):
        """Apply analytics filters to query."""
        
        if filters.date_from:
            query = query.where(FinancialControlEntry.entry_date >= filters.date_from)
        
        if filters.date_to:
            query = query.where(FinancialControlEntry.entry_date <= filters.date_to)
        
        if filters.category_ids:
            query = query.where(FinancialControlEntry.category_id.in_(filters.category_ids))
        
        if filters.exclude_categories:
            query = query.where(~FinancialControlEntry.category_id.in_(filters.exclude_categories))
        
        if filters.entry_types:
            query = query.where(FinancialControlEntry.entry_type.in_(filters.entry_types))
        
        if filters.status_filter:
            query = query.where(FinancialControlEntry.status.in_(filters.status_filter))
        
        if filters.supplier_ids:
            query = query.where(FinancialControlEntry.supplier_id.in_(filters.supplier_ids))
        
        if filters.amount_min:
            query = query.where(FinancialControlEntry.amount >= filters.amount_min)
        
        if filters.amount_max:
            query = query.where(FinancialControlEntry.amount <= filters.amount_max)
        
        if not filters.include_archived:
            query = query.where(FinancialControlEntry.status != ControlEntryStatus.ARCHIVED)
        
        if filters.only_overdue:
            query = query.where(
                and_(
                    FinancialControlEntry.status != ControlEntryStatus.PAID,
                    FinancialControlEntry.due_date < date.today()
                )
            )
        
        if filters.only_recurring:
            query = query.where(FinancialControlEntry.is_recurring == True)
        
        if filters.only_tax_deductible:
            query = query.where(FinancialControlEntry.is_tax_deductible == True)
        
        return query
    
    async def _calculate_growth_metrics(self, tenant_id: UUID, filters: Optional[AnalyticsFilters]) -> Dict[str, float]:
        """Calculate growth metrics compared to previous period."""
        
        # This is a simplified implementation
        # In a real scenario, you'd compare with the previous period
        return {
            'monthly_growth': 0.0,
            'income_growth': 0.0,
            'expense_growth': 0.0
        }
    
    async def _get_recent_transactions(self, tenant_id: UUID, limit: int = 5) -> List[Dict[str, Any]]:
        """Get recent transactions for dashboard."""
        
        query = select(FinancialControlEntry).where(
            FinancialControlEntry.tenant_id == tenant_id
        ).order_by(
            FinancialControlEntry.created_at.desc()
        ).limit(limit)
        
        result = await self.db.execute(query)
        entries = result.scalars().all()
        
        return [
            {
                'id': str(entry.id),
                'title': entry.title,
                'amount': float(entry.amount),
                'type': entry.entry_type,
                'date': entry.entry_date.isoformat(),
                'status': entry.status
            }
            for entry in entries
        ]
    
    def _get_period_info(self, filters: Optional[AnalyticsFilters]) -> Tuple[Optional[date], Optional[date], Optional[str]]:
        """Extract period information from filters."""
        
        if not filters:
            return None, None, None
        
        return filters.date_from, filters.date_to, filters.period_type
