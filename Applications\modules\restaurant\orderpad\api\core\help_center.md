# 🎧 Help Center API

**Sistema completo de suporte ao cliente com chat em tempo real, base de conhecimento e gerenciamento de tickets.**

## 📋 Visão Geral

O Help Center é um sistema integral de suporte ao cliente que permite:

- **Tickets de Suporte**: Sistema completo de tickets com prioridades e categorias
- **Chat em Tempo Real**: Comunicação bidirecional entre usuários e administradores
- **Base de Conhecimento**: Sistema self-service com artigos e FAQs
- **Upload de Arquivos**: Suporte a imagens e documentos nos tickets
- **Métricas Administrativas**: Dashboard com analytics e relatórios
- **Notificações**: Integração com sistema de notificações em tempo real

## 🔐 Autenticação

Todos os endpoints requerem autenticação JWT:

```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

## 👥 Permissões

- **Admin**: Acesso completo a todos os tickets e configurações
- **Tenant Owner/Staff**: Pode criar tickets para seu tenant
- **Tenant Customer**: Pode criar tickets para tenants onde é cliente

## 📊 Endpoints Principais

### 🎫 Gestão de Tickets

#### Criar Ticket
```http
POST /modules/core/help-center/tickets
```

**Body:**
```json
{
  "title": "Problema com o sistema",
  "description": "Descrição detalhada do problema",
  "category": "problem",
  "tenant_id": "uuid-do-tenant"
}
```

**Response:**
```json
{
  "id": "uuid-do-ticket",
  "title": "Problema com o sistema",
  "description": "Descrição detalhada do problema",
  "status": "new",
  "priority": "medium",
  "category": "problem",
  "created_at": "2025-01-18T10:00:00Z",
  "user_id": "uuid-do-usuario",
  "tenant_id": "uuid-do-tenant",
  "is_read_by_admin": false,
  "is_read_by_user": true
}
```

#### Listar Tickets
```http
GET /modules/core/help-center/tickets?page=1&per_page=10&status=open&priority=high
```

**Query Parameters:**
- `page`: Página (padrão: 1)
- `per_page`: Itens por página (padrão: 10, máx: 100)
- `status`: Filtrar por status (new, open, pending, resolved, closed)
- `priority`: Filtrar por prioridade (low, medium, high, urgent)
- `category`: Filtrar por categoria (question, incident, problem, request, suggestion)
- `search`: Buscar por título ou descrição

#### Obter Ticket
```http
GET /modules/core/help-center/tickets/{ticket_id}
```

#### Atualizar Ticket
```http
PUT /modules/core/help-center/tickets/{ticket_id}
```

**Body (Admin):**
```json
{
  "status": "resolved",
  "priority": "high",
  "assigned_admin_id": "uuid-do-admin"
}
```

**Body (Usuário):**
```json
{
  "title": "Título atualizado",
  "description": "Descrição atualizada"
}
```

### 💬 Mensagens

#### Enviar Mensagem
```http
POST /modules/core/help-center/tickets/{ticket_id}/messages
```

**Body:**
```json
{
  "message_content": "Conteúdo da mensagem",
  "message_type": "text"
}
```

#### Listar Mensagens
```http
GET /modules/core/help-center/tickets/{ticket_id}/messages
```

#### Marcar como Lida
```http
POST /modules/core/help-center/tickets/{ticket_id}/messages/{message_id}/read
```

### 📁 Upload de Arquivos

#### Fazer Upload
```http
POST /modules/core/help-center/tickets/{ticket_id}/upload
Content-Type: multipart/form-data
```

**Form Data:**
- `file`: Arquivo (máx. 50MB)

**Tipos Suportados:**
- Imagens: JPEG, PNG, GIF, WebP
- Documentos: PDF, TXT, DOC, DOCX
- Arquivos: ZIP, RAR

#### Listar Arquivos
```http
GET /modules/core/help-center/tickets/{ticket_id}/files
```

#### Obter URL do Arquivo
```http
GET /modules/core/help-center/files/{file_id}/url
```

#### Deletar Arquivo
```http
DELETE /modules/core/help-center/files/{file_id}
```

### 📚 Base de Conhecimento

#### Listar Artigos
```http
GET /modules/core/help-center/kb/articles?search=termo&category=categoria
```

#### Obter Artigo
```http
GET /modules/core/help-center/kb/articles/{article_id}
```

#### Avaliar Artigo
```http
POST /modules/core/help-center/kb/articles/{article_id}/helpful?is_helpful=true
```

### 👨‍💼 Endpoints Administrativos

#### Listar Todos os Tickets (Admin)
```http
GET /modules/core/help-center/admin/tickets?status=open&priority=urgent&sort_by=created_at&sort_order=desc
```

#### Atualização em Lote (Admin)
```http
POST /modules/core/help-center/admin/tickets/bulk-update
```

**Body:**
```json
{
  "ticket_ids": ["uuid1", "uuid2", "uuid3"],
  "action": "status",
  "value": "resolved"
}
```

**Ações Disponíveis:**
- `status`: Alterar status (new, open, pending, resolved, closed)
- `priority`: Alterar prioridade (low, medium, high, urgent)
- `assign`: Atribuir a admin (UUID do admin)

#### Métricas do Help Center (Admin)
```http
GET /modules/core/help-center/admin/metrics
```

**Response:**
```json
{
  "total_tickets": 150,
  "open_tickets": 25,
  "resolved_tickets": 100,
  "closed_tickets": 25,
  "urgent_tickets": 5,
  "high_priority_tickets": 15,
  "medium_priority_tickets": 20,
  "low_priority_tickets": 10,
  "average_response_time_hours": 2.5,
  "average_resolution_time_hours": 24.0,
  "total_kb_articles": 50,
  "total_kb_views": 1500,
  "average_kb_helpfulness": 0.85,
  "tickets_created_today": 5,
  "tickets_resolved_today": 8,
  "tickets_created_this_week": 25,
  "tickets_resolved_this_week": 30,
  "resolution_rate": 0.75,
  "first_response_rate": 0.95
}
```

#### Criar Artigo da Base de Conhecimento (Admin)
```http
POST /modules/core/help-center/admin/kb/articles
```

**Body:**
```json
{
  "title": "Como fazer login",
  "content": "Conteúdo detalhado do artigo...",
  "category": "tutorial",
  "tags": "login,autenticacao,tutorial",
  "is_published": true
}
```

#### Limpeza de Arquivos Órfãos (Admin)
```http
POST /modules/core/help-center/admin/cleanup-orphaned-files
```

## 🔄 WebSocket - Tempo Real

### Conexão
```javascript
const ws = new WebSocket('ws://localhost:8000/ws/help-center?user_id=uuid&tenant_id=uuid&ticket_id=uuid');
```

### Eventos Recebidos
```javascript
// Nova mensagem
{
  "type": "new_message",
  "message": {
    "id": "uuid",
    "ticket_id": "uuid",
    "sender_id": "uuid",
    "message_content": "Conteúdo",
    "message_type": "text",
    "created_at": "2025-01-18T10:00:00Z",
    "sender_name": "Nome do Remetente",
    "is_admin": false
  }
}

// Ticket atualizado
{
  "type": "ticket_updated",
  "ticket": {
    "id": "uuid",
    "status": "resolved",
    "priority": "high",
    "updated_at": "2025-01-18T10:00:00Z"
  }
}

// Indicador de digitação
{
  "type": "typing_indicator",
  "typing": {
    "ticket_id": "uuid",
    "user_id": "uuid",
    "user_name": "Nome",
    "is_typing": true
  }
}

// Confirmação de leitura
{
  "type": "read_receipt",
  "receipt": {
    "ticket_id": "uuid",
    "message_id": "uuid",
    "user_id": "uuid",
    "read_at": "2025-01-18T10:00:00Z"
  }
}
```

### Eventos Enviados
```javascript
// Indicador de digitação
ws.send(JSON.stringify({
  "type": "typing_indicator",
  "ticket_id": "uuid",
  "is_typing": true
}));

// Marcar como lida
ws.send(JSON.stringify({
  "type": "mark_read",
  "ticket_id": "uuid",
  "message_id": "uuid"
}));

// Entrar em ticket
ws.send(JSON.stringify({
  "type": "join_ticket",
  "ticket_id": "uuid"
}));

// Sair de ticket
ws.send(JSON.stringify({
  "type": "leave_ticket",
  "ticket_id": "uuid"
}));
```

## 📊 Códigos de Status

- `200`: Sucesso
- `201`: Criado com sucesso
- `400`: Dados inválidos
- `401`: Não autenticado
- `403`: Sem permissão
- `404`: Não encontrado
- `413`: Arquivo muito grande
- `500`: Erro interno

## 🏷️ Categorias de Ticket

- `question`: Dúvida
- `incident`: Incidente
- `problem`: Problema
- `request`: Solicitação
- `suggestion`: Sugestão

## ⚡ Prioridades

- `low`: Baixa
- `medium`: Média (padrão)
- `high`: Alta
- `urgent`: Urgente

## 📈 Status do Ticket

- `new`: Novo (padrão)
- `open`: Aberto
- `pending`: Pendente
- `resolved`: Resolvido
- `closed`: Fechado

## 🔧 Configurações

### Limites
- **Tamanho máximo de arquivo**: 50MB
- **Tipos de arquivo suportados**: Imagens, PDFs, documentos, arquivos compactados
- **Expiração de tickets**: 365 dias para usuários
- **Expiração de mensagens**: 30 dias
- **Tickets por página**: Máximo 100

### Notificações
- Novos tickets notificam todos os admins
- Novas mensagens notificam o destinatário
- Mudanças de status notificam o usuário do ticket
- Integração com sistema de notificações existente

---

**🎧 Help Center** - Sistema completo de suporte ao cliente para TrixModular
