"""
Test Notification Service

Testes para o serviço de notificações.
"""

import pytest
from datetime import datetime, timedelta
from uuid import uuid4

from app.modules.core.notifications.services import NotificationService
from app.modules.core.notifications.schemas import NotificationCreate
from app.modules.core.notifications.models import (
    Notification, NotificationTargetType, NotificationPriority
)


class TestNotificationService:
    """Testes para NotificationService."""

    @pytest.fixture
    async def notification_service(self, db_session):
        """Fixture para o serviço de notificações."""
        return NotificationService(db_session)

    @pytest.fixture
    async def admin_user(self, db_session):
        """Fixture para usuário admin."""
        from app.modules.core.users.models.user import User
        
        user = User(
            id=uuid4(),
            email="<EMAIL>",
            system_role="admin",
            is_active=True
        )
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.fixture
    async def tenant_user(self, db_session):
        """Fixture para usuário tenant."""
        from app.modules.core.users.models.user import User
        
        user = User(
            id=uuid4(),
            email="<EMAIL>",
            system_role="user",
            is_active=True
        )
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    async def test_create_notification_admin(self, notification_service, admin_user):
        """Testa criação de notificação por admin."""
        notification_data = NotificationCreate(
            title="Teste Admin",
            content="Conteúdo de teste para admin",
            target_type=NotificationTargetType.ALL_USERS,
            priority=NotificationPriority.HIGH
        )
        
        notification = await notification_service.create_notification(
            notification_data, admin_user
        )
        
        assert notification.title == "Teste Admin"
        assert notification.sender_id == admin_user.id
        assert notification.sender_type.value == "admin"
        assert notification.target_type == NotificationTargetType.ALL_USERS
        assert notification.priority == NotificationPriority.HIGH

    async def test_create_notification_tenant_owner(self, notification_service, tenant_user):
        """Testa criação de notificação por tenant owner."""
        tenant_id = uuid4()
        
        notification_data = NotificationCreate(
            title="Teste Tenant",
            content="Conteúdo de teste para tenant",
            target_type=NotificationTargetType.TENANT_STAFF,
            tenant_id=tenant_id,
            priority=NotificationPriority.NORMAL
        )
        
        notification = await notification_service.create_notification(
            notification_data, tenant_user
        )
        
        assert notification.title == "Teste Tenant"
        assert notification.sender_id == tenant_user.id
        assert notification.sender_type.value == "tenant_owner"
        assert notification.target_type == NotificationTargetType.TENANT_STAFF
        assert notification.tenant_id == tenant_id

    async def test_notification_expiration(self, notification_service, admin_user):
        """Testa expiração automática de notificação."""
        notification_data = NotificationCreate(
            title="Teste Expiração",
            content="Conteúdo que expira",
            target_type=NotificationTargetType.ALL_USERS,
            auto_expire_hours=1
        )
        
        notification = await notification_service.create_notification(
            notification_data, admin_user
        )
        
        # Verifica se a data de expiração foi definida
        assert notification.expires_at is not None
        
        # Verifica se a expiração está aproximadamente 1 hora no futuro
        expected_expiry = datetime.utcnow() + timedelta(hours=1)
        time_diff = abs((notification.expires_at - expected_expiry).total_seconds())
        assert time_diff < 60  # Diferença menor que 1 minuto

    async def test_mark_as_read(self, notification_service, admin_user, tenant_user):
        """Testa marcação de notificação como lida."""
        # Cria notificação
        notification_data = NotificationCreate(
            title="Teste Leitura",
            content="Conteúdo para teste de leitura",
            target_type=NotificationTargetType.ALL_USERS
        )
        
        notification = await notification_service.create_notification(
            notification_data, admin_user
        )
        
        # Marca como lida
        count = await notification_service.mark_as_read(
            [notification.id], tenant_user
        )
        
        assert count == 1
        
        # Verifica se foi marcada como lida
        updated_notification = await notification_service.get_notification(
            notification.id, tenant_user
        )
        assert updated_notification.is_read_by_user(str(tenant_user.id))

    async def test_delete_notification_user(self, notification_service, admin_user, tenant_user):
        """Testa deleção de notificação por usuário."""
        # Cria notificação
        notification_data = NotificationCreate(
            title="Teste Deleção",
            content="Conteúdo para teste de deleção",
            target_type=NotificationTargetType.ALL_USERS
        )
        
        notification = await notification_service.create_notification(
            notification_data, admin_user
        )
        
        # Deleta para o usuário
        success = await notification_service.delete_notification(
            notification.id, tenant_user, delete_for_all=False
        )
        
        assert success
        
        # Verifica se foi marcada como deletada para o usuário
        updated_notification = await notification_service.get_notification(
            notification.id, admin_user  # Admin ainda pode ver
        )
        assert updated_notification.is_deleted_by_user(str(tenant_user.id))

    async def test_delete_notification_for_all(self, notification_service, admin_user):
        """Testa deleção de notificação para todos (admin)."""
        # Cria notificação
        notification_data = NotificationCreate(
            title="Teste Deleção Global",
            content="Conteúdo para teste de deleção global",
            target_type=NotificationTargetType.ALL_USERS
        )
        
        notification = await notification_service.create_notification(
            notification_data, admin_user
        )
        
        # Deleta para todos
        success = await notification_service.delete_notification(
            notification.id, admin_user, delete_for_all=True
        )
        
        assert success
        
        # Verifica se foi deletada fisicamente
        deleted_notification = await notification_service.get_notification(
            notification.id, admin_user
        )
        assert deleted_notification is None

    async def test_increment_counters(self, notification_service, admin_user, tenant_user):
        """Testa incremento de contadores de visualização e clique."""
        # Cria notificação
        notification_data = NotificationCreate(
            title="Teste Contadores",
            content="Conteúdo para teste de contadores",
            target_type=NotificationTargetType.ALL_USERS
        )
        
        notification = await notification_service.create_notification(
            notification_data, admin_user
        )
        
        initial_view_count = notification.view_count
        initial_click_count = notification.click_count
        
        # Visualiza notificação (incrementa automaticamente)
        viewed_notification = await notification_service.get_notification(
            notification.id, tenant_user
        )
        assert viewed_notification.view_count == initial_view_count + 1
        
        # Registra clique
        success = await notification_service.increment_click_count(
            notification.id, tenant_user
        )
        assert success
        
        # Verifica incremento do clique
        clicked_notification = await notification_service.get_notification(
            notification.id, tenant_user
        )
        assert clicked_notification.click_count == initial_click_count + 1

    async def test_notification_filters(self, notification_service, admin_user):
        """Testa filtros de notificação."""
        # Cria notificações com diferentes prioridades
        high_priority = NotificationCreate(
            title="Alta Prioridade",
            content="Conteúdo de alta prioridade",
            target_type=NotificationTargetType.ALL_USERS,
            priority=NotificationPriority.HIGH
        )
        
        low_priority = NotificationCreate(
            title="Baixa Prioridade",
            content="Conteúdo de baixa prioridade",
            target_type=NotificationTargetType.ALL_USERS,
            priority=NotificationPriority.LOW
        )
        
        await notification_service.create_notification(high_priority, admin_user)
        await notification_service.create_notification(low_priority, admin_user)
        
        # Testa filtro por prioridade
        from app.modules.core.notifications.schemas import NotificationFilters
        
        filters = NotificationFilters(priority=NotificationPriority.HIGH)
        notifications, total = await notification_service.get_notifications(
            admin_user, filters
        )
        
        assert total == 1
        assert notifications[0].priority == NotificationPriority.HIGH

    async def test_notification_search(self, notification_service, admin_user):
        """Testa busca em notificações."""
        # Cria notificações com conteúdo específico
        notification_data = NotificationCreate(
            title="Busca Específica",
            content="Este é um conteúdo único para busca",
            target_type=NotificationTargetType.ALL_USERS
        )
        
        await notification_service.create_notification(notification_data, admin_user)
        
        # Testa busca por título
        from app.modules.core.notifications.schemas import NotificationFilters
        
        filters = NotificationFilters(search="Busca Específica")
        notifications, total = await notification_service.get_notifications(
            admin_user, filters
        )
        
        assert total == 1
        assert "Busca Específica" in notifications[0].title
        
        # Testa busca por conteúdo
        filters = NotificationFilters(search="conteúdo único")
        notifications, total = await notification_service.get_notifications(
            admin_user, filters
        )
        
        assert total == 1
        assert "conteúdo único" in notifications[0].content
