# Core - Help Center

**Categoria:** Core
**Módulo:** Help Center
**Total de Endpoints:** 32
**Gerado em:** 27/06/2025, 20:20:26

## 📋 Endpoints

- [POST /api/modules/core/help-center/admin/cleanup-orphaned-files](#post-apimodulescorehelp-centeradmincleanup-orphaned-files) - Cleanup Orphaned Files
- [GET /api/modules/core/help-center/admin/dashboard](#get-apimodulescorehelp-centeradmindashboard) - Get Admin Dashboard Metrics
- [GET /api/modules/core/help-center/admin/kb/articles](#get-apimodulescorehelp-centeradminkbarticles) - List Admin Kb Articles
- [POST /api/modules/core/help-center/admin/kb/articles](#post-apimodulescorehelp-centeradminkbarticles) - Create Kb Article
- [DELETE /api/modules/core/help-center/admin/kb/articles/{article_id}](#delete-apimodulescorehelp-centeradminkbarticlesarticle-id) - Delete Kb Article
- [PUT /api/modules/core/help-center/admin/kb/articles/{article_id}](#put-apimodulescorehelp-centeradminkbarticlesarticle-id) - Update Kb Article
- [GET /api/modules/core/help-center/admin/metrics](#get-apimodulescorehelp-centeradminmetrics) - Get Help Center Metrics
- [GET /api/modules/core/help-center/admin/tickets](#get-apimodulescorehelp-centeradmintickets) - List Admin Tickets
- [POST /api/modules/core/help-center/admin/tickets/bulk-update](#post-apimodulescorehelp-centeradminticketsbulk-update) - Bulk Update Tickets
- [DELETE /api/modules/core/help-center/files/{file_id}](#delete-apimodulescorehelp-centerfilesfile-id) - Delete File
- [GET /api/modules/core/help-center/files/{file_id}/url](#get-apimodulescorehelp-centerfilesfile-idurl) - Get File Url
- [GET /api/modules/core/help-center/kb/articles](#get-apimodulescorehelp-centerkbarticles) - List Kb Articles
- [POST /api/modules/core/help-center/kb/articles](#post-apimodulescorehelp-centerkbarticles) - Create Kb Article
- [DELETE /api/modules/core/help-center/kb/articles/{article_id}](#delete-apimodulescorehelp-centerkbarticlesarticle-id) - Delete Kb Article
- [GET /api/modules/core/help-center/kb/articles/{article_id}](#get-apimodulescorehelp-centerkbarticlesarticle-id) - Get Kb Article
- [PUT /api/modules/core/help-center/kb/articles/{article_id}](#put-apimodulescorehelp-centerkbarticlesarticle-id) - Update Kb Article
- [POST /api/modules/core/help-center/kb/articles/{article_id}/helpful](#post-apimodulescorehelp-centerkbarticlesarticle-idhelpful) - Mark Article Helpful
- [POST /api/modules/core/help-center/kb/search](#post-apimodulescorehelp-centerkbsearch) - Search Kb Articles
- [POST /api/modules/core/help-center/messages/mark-as-read](#post-apimodulescorehelp-centermessagesmark-as-read) - Mark Messages As Read
- [DELETE /api/modules/core/help-center/messages/{message_id}](#delete-apimodulescorehelp-centermessagesmessage-id) - Delete Message
- [GET /api/modules/core/help-center/tickets](#get-apimodulescorehelp-centertickets) - List Tickets
- [POST /api/modules/core/help-center/tickets](#post-apimodulescorehelp-centertickets) - Create Ticket
- [POST /api/modules/core/help-center/tickets/assign](#post-apimodulescorehelp-centerticketsassign) - Assign Ticket
- [POST /api/modules/core/help-center/tickets/bulk-operation](#post-apimodulescorehelp-centerticketsbulk-operation) - Bulk Ticket Operation
- [POST /api/modules/core/help-center/tickets/mark-as-read](#post-apimodulescorehelp-centerticketsmark-as-read) - Mark Tickets As Read
- [DELETE /api/modules/core/help-center/tickets/{ticket_id}](#delete-apimodulescorehelp-centerticketsticket-id) - Delete Ticket
- [GET /api/modules/core/help-center/tickets/{ticket_id}](#get-apimodulescorehelp-centerticketsticket-id) - Get Ticket
- [PUT /api/modules/core/help-center/tickets/{ticket_id}](#put-apimodulescorehelp-centerticketsticket-id) - Update Ticket
- [GET /api/modules/core/help-center/tickets/{ticket_id}/files](#get-apimodulescorehelp-centerticketsticket-idfiles) - List Ticket Files
- [GET /api/modules/core/help-center/tickets/{ticket_id}/messages](#get-apimodulescorehelp-centerticketsticket-idmessages) - List Messages
- [POST /api/modules/core/help-center/tickets/{ticket_id}/messages](#post-apimodulescorehelp-centerticketsticket-idmessages) - Create Message
- [POST /api/modules/core/help-center/tickets/{ticket_id}/upload](#post-apimodulescorehelp-centerticketsticket-idupload) - Upload Ticket File

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### AdminDashboardMetrics

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `overview` | HelpCenterMetricsResponse | ✅ | - |
| `tickets_by_status` | object | ✅ | - |
| `tickets_by_category` | object | ✅ | - |
| `admin_performance` | Array[object] | ✅ | - |
| `daily_ticket_trend` | Array[object] | ✅ | - |
| `weekly_resolution_trend` | Array[object] | ✅ | - |

### Body_upload_ticket_file_api_modules_core_help_center_tickets__ticket_id__upload_post

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `file` | string | ✅ | - |

### BulkTicketOperation

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `ticket_ids` | Array[string] | ✅ | Lista de IDs dos tickets |
| `operation` | string | ✅ | Operação a ser executada |
| `value` | unknown | ❌ | Valor para a operação |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### HelpCenterMetricsResponse

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `total_tickets` | integer | ✅ | - |
| `open_tickets` | integer | ✅ | - |
| `resolved_tickets` | integer | ✅ | - |
| `closed_tickets` | integer | ✅ | - |
| `pending_tickets` | integer | ✅ | - |
| `urgent_tickets` | integer | ✅ | - |
| `high_priority_tickets` | integer | ✅ | - |
| `medium_priority_tickets` | integer | ✅ | - |
| `low_priority_tickets` | integer | ✅ | - |
| `average_response_time_hours` | number | ✅ | - |
| `average_resolution_time_hours` | number | ✅ | - |
| `total_kb_articles` | integer | ✅ | - |
| `total_kb_views` | integer | ✅ | - |
| `average_kb_helpfulness` | number | ✅ | - |
| `tickets_created_today` | integer | ✅ | - |
| `tickets_resolved_today` | integer | ✅ | - |
| `tickets_created_this_week` | integer | ✅ | - |
| `tickets_resolved_this_week` | integer | ✅ | - |
| `resolution_rate` | number | ✅ | - |
| `first_response_rate` | number | ✅ | - |

### KnowledgeBaseArticleCreate

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `title` | string | ✅ | Título do artigo |
| `content` | string | ✅ | Conteúdo do artigo |
| `category` | unknown | ❌ | Categoria do artigo |
| `tags` | unknown | ❌ | Tags separadas por vírgula |
| `is_public` | boolean | ❌ | Se o artigo é público |
| `is_internal` | boolean | ❌ | Se o artigo é apenas para uso interno |
| `is_active` | boolean | ❌ | Se o artigo está ativo |

### KnowledgeBaseArticleListResponse

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `articles` | Array[KnowledgeBaseArticleResponse] | ✅ | - |
| `total` | integer | ✅ | - |
| `page` | integer | ✅ | - |
| `per_page` | integer | ✅ | - |
| `has_next` | boolean | ✅ | - |
| `has_prev` | boolean | ✅ | - |

### KnowledgeBaseArticleResponse

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `id` | string | ✅ | - |
| `title` | string | ✅ | - |
| `content` | string | ✅ | - |
| `category` | unknown | ❌ | - |
| `tags` | unknown | ❌ | - |
| `is_public` | boolean | ✅ | - |
| `is_internal` | boolean | ✅ | - |
| `is_active` | boolean | ✅ | - |
| `created_by_admin_id` | string | ✅ | - |
| `view_count` | integer | ✅ | - |
| `helpful_count` | integer | ✅ | - |
| `not_helpful_count` | integer | ✅ | - |
| `helpfulness_ratio` | number | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | unknown | ❌ | - |
| `created_by_name` | unknown | ❌ | - |

### KnowledgeBaseArticleUpdate

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `title` | unknown | ❌ | - |
| `content` | unknown | ❌ | - |
| `category` | unknown | ❌ | - |
| `tags` | unknown | ❌ | - |
| `is_public` | unknown | ❌ | - |
| `is_internal` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |

### KnowledgeBaseSearchRequest

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `query` | string | ✅ | Termo de busca |
| `category` | unknown | ❌ | Filtrar por categoria |
| `tags` | unknown | ❌ | Filtrar por tags |
| `limit` | integer | ❌ | Limite de resultados |

### MessageMarkAsRead

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `message_ids` | Array[string] | ✅ | Lista de IDs das mensagens |

### TicketAssignmentRequest

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `ticket_id` | string | ✅ | ID do ticket |
| `admin_id` | string | ✅ | ID do admin para atribuição |

### TicketCreate

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `title` | string | ✅ | Título do ticket |
| `description` | string | ✅ | Descrição do problema |
| `category` | TicketCategoryEnum | ✅ | Categoria do ticket |
| `tenant_id` | unknown | ❌ | ID do tenant (opcional) |

### TicketListResponse

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `tickets` | Array[TicketResponse] | ✅ | - |
| `total` | integer | ✅ | - |
| `page` | integer | ✅ | - |
| `per_page` | integer | ✅ | - |
| `has_next` | boolean | ✅ | - |
| `has_prev` | boolean | ✅ | - |

### TicketMarkAsRead

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `ticket_ids` | Array[string] | ✅ | Lista de IDs dos tickets |

### TicketMessageCreate

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `ticket_id` | unknown | ❌ | ID do ticket (será definido pela URL) |
| `message_content` | string | ✅ | Conteúdo da mensagem |
| `message_type` | MessageTypeEnum | ❌ | Tipo da mensagem |
| `file_path` | unknown | ❌ | Caminho do arquivo (se aplicável) |
| `file_name` | unknown | ❌ | Nome do arquivo (se aplicável) |
| `file_size` | unknown | ❌ | Tamanho do arquivo em bytes |
| `mime_type` | unknown | ❌ | Tipo MIME do arquivo |

### TicketMessageListResponse

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `messages` | Array[TicketMessageResponse] | ✅ | - |
| `total` | integer | ✅ | - |
| `page` | integer | ✅ | - |
| `per_page` | integer | ✅ | - |
| `has_next` | boolean | ✅ | - |
| `has_prev` | boolean | ✅ | - |

### TicketMessageResponse

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `id` | string | ✅ | - |
| `ticket_id` | string | ✅ | - |
| `sender_id` | string | ✅ | - |
| `message_content` | string | ✅ | - |
| `message_type` | MessageTypeEnum | ✅ | - |
| `file_path` | unknown | ❌ | - |
| `file_name` | unknown | ❌ | - |
| `file_size` | unknown | ❌ | - |
| `mime_type` | unknown | ❌ | - |
| `is_read` | boolean | ✅ | - |
| `created_at` | string | ✅ | - |
| `expires_at` | unknown | ❌ | - |
| `is_expired` | boolean | ✅ | - |
| `sender_name` | unknown | ❌ | - |
| `sender_email` | unknown | ❌ | - |
| `is_admin` | boolean | ❌ | - |

### TicketResponse

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `id` | string | ✅ | - |
| `title` | string | ✅ | - |
| `description` | string | ✅ | - |
| `category` | TicketCategoryEnum | ✅ | - |
| `status` | TicketStatusEnum | ✅ | - |
| `priority` | TicketPriorityEnum | ✅ | - |
| `user_id` | string | ✅ | - |
| `tenant_id` | unknown | ❌ | - |
| `assigned_admin_id` | unknown | ❌ | - |
| `is_read_by_admin` | boolean | ✅ | - |
| `is_read_by_user` | boolean | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | unknown | ❌ | - |
| `resolved_at` | unknown | ❌ | - |
| `closed_at` | unknown | ❌ | - |
| `expires_at` | unknown | ❌ | - |
| `is_expired` | boolean | ✅ | - |
| `user_name` | unknown | ❌ | - |
| `user_email` | unknown | ❌ | - |
| `assigned_admin_name` | unknown | ❌ | - |
| `message_count` | unknown | ❌ | - |

### TicketUpdate

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `title` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `status` | unknown | ❌ | - |
| `priority` | unknown | ❌ | - |
| `assigned_admin_id` | unknown | ❌ | - |

## 🔗 Endpoints Detalhados

### POST /api/modules/core/help-center/admin/cleanup-orphaned-files {#post-apimodulescorehelp-centeradmincleanup-orphaned-files}

**Resumo:** Cleanup Orphaned Files
**Descrição:** Remove arquivos órfãos (apenas admin).

**🔐 Autenticação:** Requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/help-center/admin/cleanup-orphaned-files" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/help-center/admin/dashboard {#get-apimodulescorehelp-centeradmindashboard}

**Resumo:** Get Admin Dashboard Metrics
**Descrição:** Obtém métricas completas para o dashboard admin.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `days` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [AdminDashboardMetrics](#admindashboardmetrics)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/help-center/admin/dashboard" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/help-center/admin/kb/articles {#get-apimodulescorehelp-centeradminkbarticles}

**Resumo:** List Admin Kb Articles
**Descrição:** Lista artigos da base de conhecimento para admin.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `search` | string | query | ❌ | - |
| `category` | string | query | ❌ | - |
| `is_published` | string | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/help-center/admin/kb/articles" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/help-center/admin/kb/articles {#post-apimodulescorehelp-centeradminkbarticles}

**Resumo:** Create Kb Article
**Descrição:** Cria novo artigo da base de conhecimento.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** {'type': 'object', 'additionalProperties': True, 'title': 'Article Data'}

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/help-center/admin/kb/articles" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/core/help-center/admin/kb/articles/{article_id} {#delete-apimodulescorehelp-centeradminkbarticlesarticle-id}

**Resumo:** Delete Kb Article
**Descrição:** Deleta artigo da base de conhecimento.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `article_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/core/help-center/admin/kb/articles/{article_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/core/help-center/admin/kb/articles/{article_id} {#put-apimodulescorehelp-centeradminkbarticlesarticle-id}

**Resumo:** Update Kb Article
**Descrição:** Atualiza artigo da base de conhecimento.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `article_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** {'type': 'object', 'additionalProperties': True, 'title': 'Article Data'}

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/core/help-center/admin/kb/articles/{article_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/help-center/admin/metrics {#get-apimodulescorehelp-centeradminmetrics}

**Resumo:** Get Help Center Metrics
**Descrição:** Obtém métricas do help center (apenas admin).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `days` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [HelpCenterMetricsResponse](#helpcentermetricsresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/help-center/admin/metrics" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/help-center/admin/tickets {#get-apimodulescorehelp-centeradmintickets}

**Resumo:** List Admin Tickets
**Descrição:** Lista todos os tickets para admin.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `page` | integer | query | ❌ | - |
| `per_page` | integer | query | ❌ | - |
| `status` | string | query | ❌ | - |
| `priority` | string | query | ❌ | - |
| `category` | string | query | ❌ | - |
| `search` | string | query | ❌ | - |
| `assigned` | string | query | ❌ | - |
| `sort_by` | string | query | ❌ | - |
| `sort_order` | string | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/help-center/admin/tickets" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/help-center/admin/tickets/bulk-update {#post-apimodulescorehelp-centeradminticketsbulk-update}

**Resumo:** Bulk Update Tickets
**Descrição:** Atualização em lote de tickets (apenas admin).

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** {'additionalProperties': True, 'type': 'object', 'title': 'Request'}

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/help-center/admin/tickets/bulk-update" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/core/help-center/files/{file_id} {#delete-apimodulescorehelp-centerfilesfile-id}

**Resumo:** Delete File
**Descrição:** Deleta um arquivo.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `file_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/core/help-center/files/{file_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/help-center/files/{file_id}/url {#get-apimodulescorehelp-centerfilesfile-idurl}

**Resumo:** Get File Url
**Descrição:** Obtém URL de acesso a um arquivo.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `file_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/help-center/files/{file_id}/url" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/help-center/kb/articles {#get-apimodulescorehelp-centerkbarticles}

**Resumo:** List Kb Articles
**Descrição:** Lista artigos da base de conhecimento.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `page` | integer | query | ❌ | - |
| `per_page` | integer | query | ❌ | - |
| `category` | string | query | ❌ | - |
| `search` | string | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [KnowledgeBaseArticleListResponse](#knowledgebasearticlelistresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/help-center/kb/articles" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/help-center/kb/articles {#post-apimodulescorehelp-centerkbarticles}

**Resumo:** Create Kb Article
**Descrição:** Cria um novo artigo da base de conhecimento (apenas admin).

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [KnowledgeBaseArticleCreate](#knowledgebasearticlecreate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [KnowledgeBaseArticleResponse](#knowledgebasearticleresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/help-center/kb/articles" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/core/help-center/kb/articles/{article_id} {#delete-apimodulescorehelp-centerkbarticlesarticle-id}

**Resumo:** Delete Kb Article
**Descrição:** Deleta um artigo da base de conhecimento (apenas admin).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `article_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/core/help-center/kb/articles/{article_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/help-center/kb/articles/{article_id} {#get-apimodulescorehelp-centerkbarticlesarticle-id}

**Resumo:** Get Kb Article
**Descrição:** Obtém um artigo específico da base de conhecimento.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `article_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [KnowledgeBaseArticleResponse](#knowledgebasearticleresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/help-center/kb/articles/{article_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/core/help-center/kb/articles/{article_id} {#put-apimodulescorehelp-centerkbarticlesarticle-id}

**Resumo:** Update Kb Article
**Descrição:** Atualiza um artigo da base de conhecimento (apenas admin).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `article_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [KnowledgeBaseArticleUpdate](#knowledgebasearticleupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [KnowledgeBaseArticleResponse](#knowledgebasearticleresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/core/help-center/kb/articles/{article_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/core/help-center/kb/articles/{article_id}/helpful {#post-apimodulescorehelp-centerkbarticlesarticle-idhelpful}

**Resumo:** Mark Article Helpful
**Descrição:** Marca artigo como útil ou não útil.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `article_id` | string | path | ✅ | - |
| `is_helpful` | boolean | query | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/help-center/kb/articles/{article_id}/helpful" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/help-center/kb/search {#post-apimodulescorehelp-centerkbsearch}

**Resumo:** Search Kb Articles
**Descrição:** Busca artigos na base de conhecimento.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [KnowledgeBaseSearchRequest](#knowledgebasesearchrequest)

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/help-center/kb/search" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/core/help-center/messages/mark-as-read {#post-apimodulescorehelp-centermessagesmark-as-read}

**Resumo:** Mark Messages As Read
**Descrição:** Marca mensagens como lidas.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [MessageMarkAsRead](#messagemarkasread)

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/help-center/messages/mark-as-read" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/core/help-center/messages/{message_id} {#delete-apimodulescorehelp-centermessagesmessage-id}

**Resumo:** Delete Message
**Descrição:** Deleta uma mensagem.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `message_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/core/help-center/messages/{message_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/help-center/tickets {#get-apimodulescorehelp-centertickets}

**Resumo:** List Tickets
**Descrição:** Lista tickets com filtros.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `page` | integer | query | ❌ | - |
| `per_page` | integer | query | ❌ | - |
| `status` | string | query | ❌ | - |
| `priority` | string | query | ❌ | - |
| `category` | string | query | ❌ | - |
| `search` | string | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TicketListResponse](#ticketlistresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/help-center/tickets" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/help-center/tickets {#post-apimodulescorehelp-centertickets}

**Resumo:** Create Ticket
**Descrição:** Cria um novo ticket de suporte.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TicketCreate](#ticketcreate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TicketResponse](#ticketresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/help-center/tickets" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/core/help-center/tickets/assign {#post-apimodulescorehelp-centerticketsassign}

**Resumo:** Assign Ticket
**Descrição:** Atribui ticket a um admin.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TicketAssignmentRequest](#ticketassignmentrequest)

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/help-center/tickets/assign" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/core/help-center/tickets/bulk-operation {#post-apimodulescorehelp-centerticketsbulk-operation}

**Resumo:** Bulk Ticket Operation
**Descrição:** Executa operação em lote nos tickets.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [BulkTicketOperation](#bulkticketoperation)

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/help-center/tickets/bulk-operation" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/core/help-center/tickets/mark-as-read {#post-apimodulescorehelp-centerticketsmark-as-read}

**Resumo:** Mark Tickets As Read
**Descrição:** Marca tickets como lidos.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TicketMarkAsRead](#ticketmarkasread)

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/help-center/tickets/mark-as-read" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/core/help-center/tickets/{ticket_id} {#delete-apimodulescorehelp-centerticketsticket-id}

**Resumo:** Delete Ticket
**Descrição:** Deleta um ticket (apenas admin).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `ticket_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/core/help-center/tickets/{ticket_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/help-center/tickets/{ticket_id} {#get-apimodulescorehelp-centerticketsticket-id}

**Resumo:** Get Ticket
**Descrição:** Obtém um ticket específico.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `ticket_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TicketResponse](#ticketresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/help-center/tickets/{ticket_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/core/help-center/tickets/{ticket_id} {#put-apimodulescorehelp-centerticketsticket-id}

**Resumo:** Update Ticket
**Descrição:** Atualiza um ticket.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `ticket_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TicketUpdate](#ticketupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TicketResponse](#ticketresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/core/help-center/tickets/{ticket_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/help-center/tickets/{ticket_id}/files {#get-apimodulescorehelp-centerticketsticket-idfiles}

**Resumo:** List Ticket Files
**Descrição:** Lista arquivos de um ticket.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `ticket_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/help-center/tickets/{ticket_id}/files" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/help-center/tickets/{ticket_id}/messages {#get-apimodulescorehelp-centerticketsticket-idmessages}

**Resumo:** List Messages
**Descrição:** Lista mensagens de um ticket.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `ticket_id` | string | path | ✅ | - |
| `page` | integer | query | ❌ | - |
| `per_page` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TicketMessageListResponse](#ticketmessagelistresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/help-center/tickets/{ticket_id}/messages" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/help-center/tickets/{ticket_id}/messages {#post-apimodulescorehelp-centerticketsticket-idmessages}

**Resumo:** Create Message
**Descrição:** Cria uma nova mensagem em um ticket.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `ticket_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TicketMessageCreate](#ticketmessagecreate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TicketMessageResponse](#ticketmessageresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/help-center/tickets/{ticket_id}/messages" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/core/help-center/tickets/{ticket_id}/upload {#post-apimodulescorehelp-centerticketsticket-idupload}

**Resumo:** Upload Ticket File
**Descrição:** Faz upload de arquivo para um ticket.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `ticket_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `multipart/form-data`
**Schema:** [Body_upload_ticket_file_api_modules_core_help_center_tickets__ticket_id__upload_post](#body_upload_ticket_file_api_modules_core_help_center_tickets__ticket_id__upload_post)

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/help-center/tickets/{ticket_id}/upload" \
  -H "Content-Type: multipart/form-data" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
