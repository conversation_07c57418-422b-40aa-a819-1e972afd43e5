import { useState, useEffect } from 'react';
import Image from 'next/image';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { Loader2 } from 'lucide-react';
import Cookies from 'js-cookie';

interface ImageViewerProps {
  src: string;
  alt: string;
  isOpen: boolean;
  onClose: () => void;
  type?: 'image' | 'video';
}

export function ImageViewer({ src, alt, isOpen, onClose, type }: ImageViewerProps) {
  const [imageSrc, setImageSrc] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  // Auto-detect type if not provided
  const mediaType = type || (src.includes('video') || src.match(/\.(mp4|webm|ogg|avi|mov|wmv)$/i) ? 'video' : 'image');

  useEffect(() => {
    if (!isOpen || !src) {
      setImageSrc('');
      setLoading(true);
      setError(false);
      return;
    }

    const loadImage = async () => {
      try {
        setLoading(true);
        setError(false);

        // If it's a blob URL, use it directly
        if (src.startsWith('blob:')) {
          setImageSrc(src);
          setLoading(false);
          return;
        }

        // For API URLs, fetch with authentication
        const token = Cookies.get('access_token');
        if (!token) {
          throw new Error('No access token available');
        }

        const response = await fetch(src, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }

        const blob = await response.blob();
        const imageUrl = URL.createObjectURL(blob);
        setImageSrc(imageUrl);
        setLoading(false);
      } catch (err) {
        console.error('Failed to load image for viewer:', err);
        setError(true);
        setLoading(false);
      }
    };

    loadImage();

    // Cleanup blob URL when component unmounts or src changes
    return () => {
      if (imageSrc && imageSrc.startsWith('blob:') && imageSrc !== src) {
        URL.revokeObjectURL(imageSrc);
      }
    };
  }, [src, isOpen, imageSrc]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Prevent body scroll
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-90"
      onClick={onClose}
    >
      {/* Close button */}
      <button
        onClick={onClose}
        className="absolute top-4 right-4 z-10 p-2 text-white hover:text-gray-300 transition-colors"
        aria-label="Fechar visualização"
      >
        <XMarkIcon className="h-8 w-8" />
      </button>

      {/* Image container */}
      <div 
        className="relative max-w-[90vw] max-h-[90vh] flex items-center justify-center"
        onClick={(e) => e.stopPropagation()}
      >
        {loading && (
          <div className="flex items-center justify-center">
            <Loader2 className="h-12 w-12 animate-spin text-white" />
          </div>
        )}

        {error && (
          <div className="text-center text-white">
            <XMarkIcon className="h-16 w-16 mx-auto mb-4 text-red-400" />
            <p className="text-lg">Erro ao carregar imagem</p>
            <p className="text-sm text-gray-300 mt-2">Tente novamente mais tarde</p>
          </div>
        )}

        {!loading && !error && imageSrc && (
          <>
            {mediaType === 'video' ? (
              <video
                src={imageSrc}
                controls
                className="max-w-full max-h-full object-contain shadow-2xl"
                onClick={(e) => e.stopPropagation()}
                autoPlay
                muted
              />
            ) : (
              <div className="relative max-w-full max-h-full">
                <Image
                  src={imageSrc}
                  alt={alt}
                  width={800}
                  height={600}
                  className="max-w-full max-h-full object-contain shadow-2xl"
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
            )}
          </>
        )}
      </div>

      {/* Image info */}
      {!loading && !error && (
        <div className="absolute bottom-4 left-4 text-white bg-black bg-opacity-50 px-3 py-2 rounded">
          <p className="text-sm">{alt}</p>
        </div>
      )}
    </div>
  );
}
