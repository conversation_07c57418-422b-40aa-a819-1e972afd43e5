import uuid

# datetime import removed as it's not used
from typing import List, TYPE_CHECKING  # Added

from sqlalchemy import (
    Column,
    String,
    Boolean,
    ForeignKey,
    Enum as SAEnum,
    Float,
    DateTime,
)
from sqlalchemy.orm import relationship, Mapped  # Added Mapped  # noqa: E402
from sqlalchemy.dialects.postgresql import UUID
from app.db.base import Base
from app.db.base import TimestampMixin
from app.modules.tenants.restaurants.delivery.enums import DeliveryBoyStatus

if TYPE_CHECKING:  # Added
    from .delivery_boy_location import DeliveryBoyLocation  # noqa: F401 # Added  # noqa: E402
    from .delivery_assignment import DeliveryAssignment  # noqa: F401 # Added


class DeliveryBoy(Base, TimestampMixin):
    __tablename__ = "delivery_boys"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        unique=True,
        index=True,
    )

    name = Column(String, nullable=False)
    phone_number = Column(String, nullable=True)
    vehicle_type = Column(String, nullable=True)  # E.g., "Moto", "Bicicleta", "Carro"
    vehicle_plate = Column(String, nullable=True)

    current_status = Column(
        SAEnum(DeliveryBoyStatus, name="delivery_boy_status_enum"),  # Removed create_type=False
        nullable=False,
        default=DeliveryBoyStatus.AVAILABLE,
        # server_default=DeliveryBoyStatus.AVAILABLE.value, # Optional, default usually sufficient
    )
    is_active = Column(Boolean, default=True, nullable=False)

    # Fields for last known location
    last_latitude = Column(Float, nullable=True)
    last_longitude = Column(Float, nullable=True)
    last_location_update_at = Column(DateTime, nullable=True)

    # Relationships
    user = relationship("User")
    tenant = relationship("Tenant")
    assigned_orders: Mapped[List["DeliveryAssignment"]] = relationship(
        "DeliveryAssignment", back_populates="delivery_boy"
    )  # Adjusted
    locations: Mapped[List["DeliveryBoyLocation"]] = relationship(
        "DeliveryBoyLocation",
        back_populates="delivery_boy",
        order_by="desc(DeliveryBoyLocation.timestamp)",
    )  # Adjusted

    def __repr__(self):
        return f"<DeliveryBoy(id={self.id}, name='{self.name}', tenant_id={self.tenant_id})>"
