import uuid
from typing import List, Optional, Sequence, TYPE_CHECKING
from fastapi import APIRouter, Depends, HTTPException, Query, status, Body
from sqlalchemy.ext.asyncio import AsyncSession

# Adicionado require_tenant_role
from app.core.db_dependencies import get_db
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    get_current_tenant,
    require_tenant_role,
)
from app.modules.core.roles.models.roles import RolePermissions  # Adicionado para roles
from app.modules.core.tenants.models.tenant import Tenant

if TYPE_CHECKING:
    from app.modules.core.users.models.user import User

# from app.modules.shared.inventory import schemas, services #
# Removido - Importar específicos
from app.modules.core.functions.inventory.schemas.inventory_item import (
    InventoryItemRead,
    InventoryItemCreate,
    InventoryItemUpdate,
    InventoryItemAdjustStock,
    InventoryCategoryCreate,
    InventoryCategoryRead,
    InventoryCategoryUpdate,
)
from app.modules.core.functions.inventory.services.inventory_service import (  # noqa: E402
    InventoryService,
)
from app.modules.core.functions.inventory.services.inventory_integration_service import (
    InventoryIntegrationService,
)
from app.core.exceptions import NotFoundError  # Reutilizando exceções  # noqa: E402

router = APIRouter()


@router.post(
    "/items",
    response_model=InventoryItemRead,  # Corrigido
    status_code=status.HTTP_201_CREATED,
    summary="Criar Item de Inventário",
    description="Cria um novo item de inventário para o tenant atual.",
)
async def create_inventory_item(
    item_in: InventoryItemCreate,
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),  # Mantido para obter tenant.id
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
):
    """
    Endpoint para criar um novo item de inventário.
    Requer role STAFF, MANAGER ou OWNER.
    """
    return await inventory_service.create_item(db=db, item_in=item_in, tenant_id=current_tenant.id)


@router.get(
    "/items",
    response_model=List[InventoryItemRead],  # Corrigido
    summary="Listar Itens de Inventário",
    description="Lista os itens de inventário pertencentes ao tenant atual, com filtros opcionais.",
)
async def list_inventory_items(
    skip: int = Query(0, ge=0, description="Número de itens a pular"),
    limit: int = Query(100, ge=1, le=200, description="Número máximo de itens a retornar"),
    name: Optional[str] = Query(
        None, description="Filtrar por nome (busca parcial, case-insensitive)"
    ),
    sku: Optional[str] = Query(None, description="Filtrar por SKU (busca exata)"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),  # Mantido para obter tenant.id
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
) -> Sequence[InventoryItemRead]:
    """
    Endpoint para listar itens de inventário do tenant logado.
    Permite paginação e filtragem por nome e SKU.
    Requer role STAFF, MANAGER ou OWNER.
    """
    items = await inventory_service.get_items(
        db=db,
        tenant_id=current_tenant.id,
        skip=skip,
        limit=limit,
        name=name,
        sku=sku,
    )
    # Pydantic v2 pode converter automaticamente, mas explícito é mais claro se necessário
    # return [schemas.InventoryItemRead.from_orm(item) for item in items]
    return items


@router.get(
    "/items/{item_id}",
    response_model=InventoryItemRead,  # Corrigido
    summary="Obter Item de Inventário",
    description="Obtém os detalhes de um item de inventário específico pelo seu ID, pertencente ao tenant atual.",  # noqa: E501
    responses={404: {"description": "Item não encontrado para este tenant"}},
)
async def get_inventory_item(
    item_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),  # Mantido para obter tenant.id
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
):
    """
    Endpoint para obter um item de inventário específico pelo ID.
    Verifica se o item pertence ao tenant logado.
    Requer role STAFF, MANAGER ou OWNER.
    """
    try:
        db_item = await inventory_service.get_item_or_404(
            db=db, item_id=item_id, tenant_id=current_tenant.id
        )
        return db_item
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))


@router.put(
    "/items/{item_id}",
    response_model=InventoryItemRead,  # Corrigido
    summary="Atualizar Item de Inventário",
    description="Atualiza os detalhes de um item de inventário (exceto quantidade). A quantidade deve ser ajustada via endpoint específico.",  # noqa: E501
    responses={404: {"description": "Item não encontrado para este tenant"}},
)
async def update_inventory_item(
    item_id: uuid.UUID,
    item_in: InventoryItemUpdate,
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),  # Mantido para obter tenant.id
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
):
    """
    Endpoint para atualizar informações de um item (nome, descrição, custo, SKU).
    Não permite atualizar a quantidade diretamente aqui.
    Requer role STAFF, MANAGER ou OWNER.
    """
    try:
        updated_item = await inventory_service.update_item(
            db=db, item_id=item_id, item_in=item_in, tenant_id=current_tenant.id
        )
        return updated_item
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))


@router.post(
    "/items/{item_id}/adjust-stock",
    response_model=InventoryItemRead,  # Corrigido
    summary="Ajustar Estoque do Item",
    description="Ajusta a quantidade em estoque de um item específico. Use um valor positivo para adicionar e negativo para remover.",  # noqa: E501
    responses={
        404: {"description": "Item não encontrado para este tenant"},
        400: {"description": "Ajuste inválido (ex: resultaria em estoque negativo)"},
    },
)
async def adjust_inventory_item_stock(
    item_id: uuid.UUID,
    adjustment: InventoryItemAdjustStock = Body(...),
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),  # Mantido para obter tenant.id
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
):
    """
    Endpoint para ajustar a quantidade em estoque de um item.
    Recebe a quantidade a ser adicionada ou removida no corpo da requisição.
    Requer role STAFF, MANAGER ou OWNER.
    """
    try:
        adjusted_item = await inventory_service.adjust_stock(
            db=db,
            item_id=item_id,
            tenant_id=current_tenant.id,
            change=adjustment.change,
        )
        return adjusted_item
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except ValueError as e:  # Captura o erro de estoque negativo do serviço
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


# === Category Endpoints ===

@router.get(
    "/categories",
    response_model=List[InventoryCategoryRead],
    summary="Listar Categorias de Inventário",
    description="Lista as categorias de inventário do tenant.",
)
async def list_inventory_categories(
    active_only: bool = Query(True, description="Apenas categorias ativas"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
):
    """
    Lista categorias de inventário do tenant.
    Requer role STAFF, MANAGER ou OWNER.
    """
    categories = await inventory_category_service.get_categories(
        db=db,
        tenant_id=current_tenant.id,
        active_only=active_only
    )
    return categories


@router.post(
    "/categories",
    response_model=InventoryCategoryRead,
    status_code=status.HTTP_201_CREATED,
    summary="Criar Categoria de Inventário",
    description="Cria uma nova categoria de inventário.",
)
async def create_inventory_category(
    category_data: InventoryCategoryCreate,
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.ADMIN_ROLES, tenant_id_source="header")
    ),
):
    """
    Cria nova categoria de inventário.
    Requer role MANAGER ou OWNER.
    """
    category = await inventory_category_service.create_category(
        db=db,
        category_data=category_data,
        tenant_id=current_tenant.id
    )
    return category


@router.get(
    "/categories/{category_id}",
    response_model=InventoryCategoryRead,
    summary="Obter Categoria de Inventário",
    description="Obtém detalhes de uma categoria específica.",
)
async def get_inventory_category(
    category_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
):
    """
    Obtém categoria por ID.
    Requer role STAFF, MANAGER ou OWNER.
    """
    try:
        category = await inventory_category_service.get_category_by_id(
            db=db,
            category_id=category_id,
            tenant_id=current_tenant.id
        )
        if not category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Category not found"
            )
        return category
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch category"
        )


@router.put(
    "/categories/{category_id}",
    response_model=InventoryCategoryRead,
    summary="Atualizar Categoria de Inventário",
    description="Atualiza dados de uma categoria.",
)
async def update_inventory_category(
    category_id: uuid.UUID,
    category_data: InventoryCategoryUpdate,
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.ADMIN_ROLES, tenant_id_source="header")
    ),
):
    """
    Atualiza categoria.
    Requer role MANAGER ou OWNER.
    """
    try:
        category = await inventory_category_service.update_category(
            db=db,
            category_id=category_id,
            category_data=category_data,
            tenant_id=current_tenant.id
        )
        return category
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update category"
        )


@router.delete(
    "/categories/{category_id}",
    response_model=InventoryCategoryRead,
    summary="Excluir Categoria de Inventário",
    description="Exclui uma categoria (soft delete).",
)
async def delete_inventory_category(
    category_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.ADMIN_ROLES, tenant_id_source="header")
    ),
):
    """
    Exclui categoria (soft delete).
    Requer role MANAGER ou OWNER.
    """
    try:
        category = await inventory_category_service.delete_category(
            db=db,
            category_id=category_id,
            tenant_id=current_tenant.id
        )
        return category
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete category"
        )


# === Integration Endpoints ===

@router.post(
    "/sync-to-shopping-list",
    response_model=dict,
    summary="Sincronizar com Lista de Compras",
    description="Automaticamente adiciona itens com estoque baixo à lista de compras.",
)
async def sync_inventory_to_shopping_list(
    threshold: int = Query(10, ge=1, le=100, description="Limite de estoque baixo"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: "User" = Depends(
        require_tenant_role(RolePermissions.STAFF_ROLES, tenant_id_source="header")
    ),
):
    """
    Sincroniza automaticamente itens com estoque baixo para a lista de compras.
    Requer role STAFF, MANAGER ou OWNER.
    """
    try:
        result = await inventory_integration_service.sync_inventory_to_shopping_list(
            db=db,
            tenant_id=current_tenant.id,
            threshold=threshold
        )
        return {"status": "Sync successful", "items_added": result}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to sync inventory to shopping list"
        )
