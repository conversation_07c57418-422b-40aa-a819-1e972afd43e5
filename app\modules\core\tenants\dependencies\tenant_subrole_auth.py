import uuid
import logging
from typing import Optional, Annotated, List, Literal, TYPE_CHECKING

from fastapi import Depends, HTTPException, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.modules.core.tenants.models.tenant import Tenant
from app.core.db_dependencies import get_db
from app.modules.core.tenants.services.tenant_service import tenant_service
from app.modules.core.users.services.tenant_user_association_service import (
    tenant_user_association_service,
)
from app.modules.core.auth.dependencies.user_auth import get_current_active_user
from app.modules.core.roles.models.roles import (
    SystemRole,
    TenantRole,
    TenantStaffSubRole,
    RolePermissions,
)
from app.modules.core.tenants.dependencies.tenant_context import get_current_tenant

# Use TYPE_CHECKING to avoid circular import
if TYPE_CHECKING:
    from app.modules.core.users.models.user import User

logger = logging.getLogger(__name__)

# Standard exception for permission denied
forbidden_exception = HTTPException(
    status_code=status.HTTP_403_FORBIDDEN,
    detail="Operation not permitted",
)


def require_tenant_staff_subrole(
    required_subroles: List[TenantStaffSubRole], tenant_id_source: Literal["path", "header"]
):
    """
    Factory function to check if the current user has required staff sub-roles within a tenant.
    This is more granular than require_tenant_role and specifically checks staff sub-roles.
    Tenant ID must be explicitly sourced from 'path' or 'header' (X-Tenant-ID).
    Allows System Admins and Tenant Owners automatically.
    """

    async def tenant_subrole_checker(
        request: Request,
        db: Annotated[AsyncSession, Depends(get_db)],
        current_user: Annotated["User", Depends(get_current_active_user)],
    ) -> "User":
        resolved_tenant_id: Optional[uuid.UUID] = None

        logger.debug(
            f"require_tenant_staff_subrole: User '{current_user.email}' (ID: {current_user.id}, "
            f"SystemRole: {current_user.system_role}). Required sub-roles: "
            f"{[r.value for r in required_subroles]}. Tenant ID source: {tenant_id_source}"
        )

        # Resolve tenant ID from path or header
        if tenant_id_source == "path":
            path_tenant_id_str = request.path_params.get("tenant_id")
            if not path_tenant_id_str:
                logger.error(
                    "require_tenant_staff_subrole: Tenant ID source is 'path' but 'tenant_id' not found "
                    "in path parameters."
                )
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Tenant ID source is 'path' but tenant_id not found in path parameters.",
                )
            try:
                resolved_tenant_id = uuid.UUID(path_tenant_id_str)
                logger.debug(
                    f"require_tenant_staff_subrole: Resolved tenant_id from path: {resolved_tenant_id}"
                )
            except ValueError:
                logger.error(
                    f"require_tenant_staff_subrole: Invalid tenant_id format in path: {path_tenant_id_str}"
                )
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid tenant_id format in path.",
                )

        elif tenant_id_source == "header":
            try:
                # This will internally call get_current_tenant which handles X-Tenant-ID
                header_tenant = await get_current_tenant(request, db)
                resolved_tenant_id = header_tenant.id
                logger.debug(
                    f"require_tenant_staff_subrole: Resolved tenant_id from header: {resolved_tenant_id} "
                    f"(Tenant: {header_tenant.name})"
                )
            except HTTPException as e:  # Catch exceptions from get_current_tenant
                logger.error(
                    f"require_tenant_staff_subrole: Error resolving tenant_id from header: {e.detail}"
                )
                raise  # Re-raise the original exception from get_current_tenant
        else:
            # This case should ideally not be reached if Literal type hint is effective
            logger.critical(
                f"require_tenant_staff_subrole: Invalid tenant_id_source: {tenant_id_source}. "
                f"Must be 'path' or 'header'."
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Invalid tenant_id_source: {tenant_id_source}. Must be 'path' or 'header'.",
            )

        if not resolved_tenant_id:  # Should be caught by specific source logic, but as a safeguard
            logger.error(
                "require_tenant_staff_subrole: Tenant ID could not be resolved. "
                "This indicates an unexpected state."
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Tenant ID could not be resolved.",
            )

        # Allow System Admins automatically
        if current_user.system_role == SystemRole.ADMIN.value:
            logger.info(
                f"require_tenant_staff_subrole: User '{current_user.email}' is SystemRole.ADMIN. "
                f"Access granted to tenant '{resolved_tenant_id}'."
            )
            return current_user

        # For SystemRole.USER, check their role and sub-role within the specific tenant
        if current_user.system_role == SystemRole.USER.value:
            # Get the user's association with the tenant
            association = await tenant_user_association_service.get_association_by_user_and_tenant(
                db=db, user_id=current_user.id, tenant_id=resolved_tenant_id
            )

            if not association:
                logger.warning(
                    f"require_tenant_staff_subrole: User '{current_user.email}' has no association "
                    f"with tenant '{resolved_tenant_id}'. Access denied."
                )
                raise forbidden_exception

            user_tenant_role = association.role
            user_staff_subrole = association.staff_sub_role

            logger.debug(
                f"require_tenant_staff_subrole: User '{current_user.email}' in tenant '{resolved_tenant_id}' "
                f"has role: '{user_tenant_role}' and staff_sub_role: '{user_staff_subrole}'. "
                f"Required sub-roles: {[r.value for r in required_subroles]}"
            )

            # Allow Tenant Owners automatically
            if user_tenant_role == TenantRole.OWNER.value:
                logger.info(
                    f"require_tenant_staff_subrole: User '{current_user.email}' is TenantRole.OWNER "
                    f"in tenant '{resolved_tenant_id}'. Access granted."
                )
                return current_user

            # For STAFF role, check the sub-role
            if user_tenant_role == TenantRole.STAFF.value:
                if not user_staff_subrole:
                    logger.warning(
                        f"require_tenant_staff_subrole: User '{current_user.email}' is STAFF "
                        f"but has no staff_sub_role in tenant '{resolved_tenant_id}'. Access denied."
                    )
                    raise forbidden_exception

                # Check if the user's sub-role is in the required list
                required_subrole_values = [r.value for r in required_subroles]
                if user_staff_subrole not in required_subrole_values:
                    logger.warning(
                        f"require_tenant_staff_subrole: User '{current_user.email}' with staff_sub_role "
                        f"'{user_staff_subrole}' in tenant '{resolved_tenant_id}' "
                        f"does not have required sub-role permissions. "
                        f"Required: {required_subrole_values}. Access denied."
                    )
                    raise forbidden_exception

                logger.info(
                    f"require_tenant_staff_subrole: User '{current_user.email}' with staff_sub_role "
                    f"'{user_staff_subrole}' in tenant '{resolved_tenant_id}' "
                    f"has required sub-role permissions. Access granted."
                )
                return current_user

            # For other roles (MANAGER, CUSTOMER), deny access since sub-roles are staff-specific
            logger.warning(
                f"require_tenant_staff_subrole: User '{current_user.email}' with role "
                f"'{user_tenant_role}' in tenant '{resolved_tenant_id}' "
                f"cannot have staff sub-roles. Access denied."
            )
            raise forbidden_exception

        # If user is not SystemRole.ADMIN and not SystemRole.USER (e.g., an
        # unhandled system role), deny access.
        logger.error(
            f"require_tenant_staff_subrole: User '{current_user.email}' has an unhandled system_role "
            f"'{current_user.system_role}'. Access denied to tenant '{resolved_tenant_id}'."
        )
        raise forbidden_exception

    return tenant_subrole_checker
