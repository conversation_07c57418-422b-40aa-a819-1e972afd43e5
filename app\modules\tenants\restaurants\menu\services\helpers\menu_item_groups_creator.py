import logging
from typing import List
from sqlalchemy.ext.asyncio import AsyncSession
import uuid

# Import models
from app.modules.tenants.restaurants.menu.models.menu_item import MenuItem
from app.modules.core.functions.customizations.models.variant_group import VariantGroup
from app.modules.core.functions.customizations.models.variant_option import VariantOption
from app.modules.core.functions.customizations.models.modifier_group import ModifierGroup
from app.modules.core.functions.customizations.models.modifier_option import ModifierOption
from app.modules.core.functions.customizations.models.optional_group import OptionalGroup
from app.modules.core.functions.customizations.models.optional_option import OptionalOption

# Import schemas
from app.modules.tenants.restaurants.menu.schemas.menu_item import MenuItemCreate

logger = logging.getLogger(__name__)


class MenuItemGroupsCreator:
    def __init__(self, db_session: AsyncSession):
        self.db = db_session

    async def create_all_groups(self, db_item: MenuItem, item_in: MenuItemCreate, tenant_id: uuid.UUID):
        """Create all groups (variant, modifier, optional) for a menu item."""
        if item_in.variant_groups:
            await self._create_variant_groups(db_item, item_in.variant_groups, tenant_id)
        
        if item_in.modifier_groups:
            await self._create_modifier_groups(db_item, item_in.modifier_groups, tenant_id)
        
        if item_in.optional_groups:
            await self._create_optional_groups(db_item, item_in.optional_groups, tenant_id)

    async def _create_variant_groups(self, db_item: MenuItem, variant_groups, tenant_id: uuid.UUID):
        """Create variant groups and their associations."""
        from app.db.base import menu_item_variant_groups

        for idx, vg_in in enumerate(variant_groups):
            vg_data = vg_in.model_dump(exclude={"options"})
            group_id = vg_data.get('id')
            is_required = vg_data.pop('is_required', False)
            is_active = vg_data.pop('is_active', True)

            if group_id and not str(group_id).startswith('temp_'):
                # Existing group - just create association
                await self._create_variant_group_association(
                    db_item.id, group_id, tenant_id, idx, is_required, is_active
                )
            else:
                # New group - create group and association
                db_vg = await self._create_new_variant_group(vg_data, vg_in.options, tenant_id)
                await self._create_variant_group_association(
                    db_item.id, db_vg.id, tenant_id, idx, is_required, is_active
                )

    async def _create_new_variant_group(self, vg_data: dict, options, tenant_id: uuid.UUID) -> VariantGroup:
        """Create a new variant group with its options."""
        vg_data.pop('id', None)  # Remove temp IDs
        db_vg = VariantGroup(**vg_data, tenant_id=tenant_id)
        self.db.add(db_vg)
        await self.db.flush()  # Get db_vg.id

        # Create options for the variant group
        options_list = list(options)
        
        # Ensure there's always a default option if requires_default_selection is True
        if vg_data.get('requires_default_selection', False) and options_list:
            has_default = any(opt.is_default for opt in options_list)
            if not has_default:
                options_list[0].is_default = True

        for vo_in in options_list:
            vo_data = vo_in.model_dump()
            vo_data.pop('id', None)  # Remove temp IDs
            db_vo = VariantOption(
                **vo_data,
                variant_group_id=db_vg.id,
                tenant_id=tenant_id,
            )
            self.db.add(db_vo)

        return db_vg

    async def _create_variant_group_association(self, item_id: uuid.UUID, group_id: uuid.UUID, 
                                              tenant_id: uuid.UUID, display_order: int, 
                                              is_required: bool, is_active: bool):
        """Create association between menu item and variant group."""
        from app.db.base import menu_item_variant_groups
        
        await self.db.execute(
            menu_item_variant_groups.insert().values(
                menu_item_id=item_id,
                variant_group_id=group_id,
                tenant_id=tenant_id,
                display_order=display_order,
                is_required=is_required,
                is_active=is_active
            )
        )

    async def _create_modifier_groups(self, db_item: MenuItem, modifier_groups, tenant_id: uuid.UUID):
        """Create modifier groups and their associations."""
        from app.db.base import menu_item_modifier_groups

        for idx, mg_in in enumerate(modifier_groups):
            mg_data = mg_in.model_dump(exclude={"options"})
            group_id = mg_data.get('id')
            is_required = mg_data.pop('is_required', False)
            is_active = mg_data.pop('is_active', True)

            if group_id and not str(group_id).startswith('temp_'):
                # Existing group - just create association
                await self._create_modifier_group_association(
                    db_item.id, group_id, tenant_id, idx, is_required, is_active
                )
            else:
                # New group - create group and association
                db_mg = await self._create_new_modifier_group(mg_data, mg_in.options, tenant_id)
                await self._create_modifier_group_association(
                    db_item.id, db_mg.id, tenant_id, idx, is_required, is_active
                )

    async def _create_new_modifier_group(self, mg_data: dict, options, tenant_id: uuid.UUID) -> ModifierGroup:
        """Create a new modifier group with its options."""
        mg_data.pop('id', None)  # Remove temp IDs
        db_mg = ModifierGroup(**mg_data, tenant_id=tenant_id)
        self.db.add(db_mg)
        await self.db.flush()  # Get db_mg.id

        # Create options for the modifier group
        for mo_in in options:
            mo_data = mo_in.model_dump()
            mo_data.pop('id', None)  # Remove temp IDs
            db_mo = ModifierOption(
                **mo_data,
                modifier_group_id=db_mg.id,
                tenant_id=tenant_id,
            )
            self.db.add(db_mo)

        return db_mg

    async def _create_modifier_group_association(self, item_id: uuid.UUID, group_id: uuid.UUID,
                                               tenant_id: uuid.UUID, display_order: int,
                                               is_required: bool, is_active: bool):
        """Create association between menu item and modifier group."""
        from app.db.base import menu_item_modifier_groups
        
        await self.db.execute(
            menu_item_modifier_groups.insert().values(
                menu_item_id=item_id,
                modifier_group_id=group_id,
                tenant_id=tenant_id,
                display_order=display_order,
                is_required=is_required,
                is_active=is_active
            )
        )

    async def _create_optional_groups(self, db_item: MenuItem, optional_groups, tenant_id: uuid.UUID):
        """Create optional groups and their associations."""
        from app.db.base import menu_item_optional_groups

        for idx, og_in in enumerate(optional_groups):
            og_data = og_in.model_dump(exclude={"options"})
            group_id = og_data.get('id')
            is_required = og_data.pop('is_required', False)  # Always false for optionals
            is_active = og_data.pop('is_active', True)

            if group_id and not str(group_id).startswith('temp_'):
                # Existing group - just create association
                await self._create_optional_group_association(
                    db_item.id, group_id, tenant_id, idx, is_required, is_active
                )
            else:
                # New group - create group and association
                db_og = await self._create_new_optional_group(og_data, og_in.options, tenant_id)
                await self._create_optional_group_association(
                    db_item.id, db_og.id, tenant_id, idx, is_required, is_active
                )

    async def _create_new_optional_group(self, og_data: dict, options, tenant_id: uuid.UUID) -> OptionalGroup:
        """Create a new optional group with its options."""
        og_data.pop('id', None)  # Remove temp IDs
        db_og = OptionalGroup(**og_data, tenant_id=tenant_id)
        self.db.add(db_og)
        await self.db.flush()  # Get db_og.id

        # Create options for the optional group
        for oo_in in options:
            oo_data = oo_in.model_dump()
            oo_data.pop('id', None)  # Remove temp IDs
            db_oo = OptionalOption(
                **oo_data,
                optional_group_id=db_og.id,
                tenant_id=tenant_id,
            )
            self.db.add(db_oo)

        return db_og

    async def _create_optional_group_association(self, item_id: uuid.UUID, group_id: uuid.UUID,
                                               tenant_id: uuid.UUID, display_order: int,
                                               is_required: bool, is_active: bool):
        """Create association between menu item and optional group."""
        from app.db.base import menu_item_optional_groups
        
        await self.db.execute(
            menu_item_optional_groups.insert().values(
                menu_item_id=item_id,
                optional_group_id=group_id,
                tenant_id=tenant_id,
                display_order=display_order,
                is_required=is_required,
                is_active=is_active
            )
        )