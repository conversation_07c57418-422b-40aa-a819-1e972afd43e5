'use client';

import { useState, useEffect, useCallback } from 'react';
import Cookies from 'js-cookie';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CreditCard, 
  Calendar, 
  DollarSign, 
  Hash, 
  Upload, 
  AlertCircle,
  FileText
} from 'lucide-react';
import { useCurrency } from '@/hooks/useCurrency';

interface Invoice {
  id: string;
  tenant_id: string;
  amount: number;
  description: string;
  transaction_date: string;
  reference_number: string;
  payment_method_id?: string;
  notes?: string;
  is_paid: boolean;
}

interface PaymentMethod {
  id: string;
  name: string;
  type: string;
  is_active: boolean;
}

interface PaymentData {
  payment_method_id: string;
  payment_date: string;
  payment_amount: string;
  payment_reference?: string;
  payment_receipt_file?: string;
  notes?: string;
}

interface PaymentRegistrationModalProps {
  isOpen: boolean;
  onClose: () => void;
  invoice: Invoice;
  onPaymentRegistered: () => void;
  onRegisterPayment: (invoiceId: string, paymentData: PaymentData) => Promise<void>;
}

export default function PaymentRegistrationModal({
  isOpen,
  onClose,
  invoice,
  onPaymentRegistered,
  onRegisterPayment
}: PaymentRegistrationModalProps) {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [formData, setFormData] = useState<PaymentData>({
    payment_method_id: '',
    payment_date: new Date().toISOString().split('T')[0],
    payment_amount: invoice?.amount?.toString() || '',
    payment_reference: '',
    payment_receipt_file: '',
    notes: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [submitError, setSubmitError] = useState<string>('');

  const { formatCurrency } = useCurrency();

  const fetchPaymentMethods = useCallback(async () => {
    try {
      const token = Cookies.get('access_token');
      const response = await fetch('/api/modules/payment-methods', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'X-Tenant-ID': invoice.tenant_id,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setPaymentMethods(data.filter((pm: PaymentMethod) => pm.is_active));
      }
    } catch (error) {
      console.error('Error fetching payment methods:', error);
    }
  }, [invoice.tenant_id]);

  useEffect(() => {
    if (isOpen && invoice) {
      setFormData({
        payment_method_id: '',
        payment_date: new Date().toISOString().split('T')[0],
        payment_amount: invoice.amount.toString(),
        payment_reference: '',
        payment_receipt_file: '',
        notes: ''
      });
      setErrors({});
      setSubmitError('');
      fetchPaymentMethods();
    }
  }, [isOpen, invoice, fetchPaymentMethods]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.payment_method_id) {
      newErrors.payment_method_id = 'Método de pagamento é obrigatório';
    }

    if (!formData.payment_date) {
      newErrors.payment_date = 'Data de pagamento é obrigatória';
    }

    if (!formData.payment_amount.trim()) {
      newErrors.payment_amount = 'Valor pago é obrigatório';
    } else {
      const amount = parseFloat(formData.payment_amount);
      if (isNaN(amount) || amount <= 0) {
        newErrors.payment_amount = 'Valor deve ser um número positivo';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitError('');

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      await onRegisterPayment(invoice.id, formData);
      onPaymentRegistered();
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : 'Erro ao registrar pagamento');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof PaymentData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };



  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <CreditCard className="h-5 w-5 text-blue-600" />
            <span>Registrar Pagamento de Fatura</span>
          </DialogTitle>
          <DialogDescription>
            Registre o pagamento da fatura do fornecedor com comprovante e detalhes.
          </DialogDescription>
        </DialogHeader>

        {/* Invoice Summary */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <h3 className="font-medium text-gray-900 mb-2">Detalhes da Fatura</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <Hash className="h-4 w-4 text-gray-500" />
              <span className="text-gray-600">Número:</span>
              <span className="font-medium">{invoice.reference_number}</span>
            </div>
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-green-600" />
              <span className="text-gray-600">Valor:</span>
              <span className="font-medium">{formatCurrency(invoice.amount)}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-blue-600" />
              <span className="text-gray-600">Data da Fatura:</span>
              <span className="font-medium">{formatDate(invoice.transaction_date)}</span>
            </div>
            <div className="flex items-center space-x-2">
              <FileText className="h-4 w-4 text-gray-600" />
              <span className="text-gray-600">Descrição:</span>
              <span className="font-medium truncate">{invoice.description}</span>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {submitError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{submitError}</AlertDescription>
            </Alert>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Método de Pagamento */}
            <div className="space-y-2">
              <Label htmlFor="payment_method_id">Método de Pagamento *</Label>
              <Select
                value={formData.payment_method_id}
                onValueChange={(value) => handleInputChange('payment_method_id', value)}
              >
                <SelectTrigger className={errors.payment_method_id ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Selecione o método" />
                </SelectTrigger>
                <SelectContent>
                  {paymentMethods.map((method) => (
                    <SelectItem key={method.id} value={method.id}>
                      {method.name} ({method.type})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.payment_method_id && (
                <p className="text-sm text-red-500">{errors.payment_method_id}</p>
              )}
            </div>

            {/* Data do Pagamento */}
            <div className="space-y-2">
              <Label htmlFor="payment_date">Data do Pagamento *</Label>
              <Input
                id="payment_date"
                type="date"
                value={formData.payment_date}
                onChange={(e) => handleInputChange('payment_date', e.target.value)}
                className={errors.payment_date ? 'border-red-500' : ''}
              />
              {errors.payment_date && (
                <p className="text-sm text-red-500">{errors.payment_date}</p>
              )}
            </div>

            {/* Valor Pago */}
            <div className="space-y-2">
              <Label htmlFor="payment_amount">Valor Pago *</Label>
              <Input
                id="payment_amount"
                type="number"
                step="0.01"
                min="0"
                value={formData.payment_amount}
                onChange={(e) => handleInputChange('payment_amount', e.target.value)}
                placeholder="0.00"
                className={errors.payment_amount ? 'border-red-500' : ''}
              />
              {errors.payment_amount && (
                <p className="text-sm text-red-500">{errors.payment_amount}</p>
              )}
            </div>

            {/* Referência do Pagamento */}
            <div className="space-y-2">
              <Label htmlFor="payment_reference">Referência do Pagamento</Label>
              <Input
                id="payment_reference"
                value={formData.payment_reference}
                onChange={(e) => handleInputChange('payment_reference', e.target.value)}
                placeholder="Ex: TED123456, PIX789"
              />
            </div>
          </div>

          {/* Comprovante */}
          <div className="space-y-2">
            <Label htmlFor="payment_receipt_file">Comprovante de Pagamento</Label>
            <Input
              id="payment_receipt_file"
              type="file"
              accept="image/*,application/pdf"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) {
                  // In a real implementation, you would upload the file and get a URL
                  handleInputChange('payment_receipt_file', file.name);
                }
              }}
            />
            <p className="text-sm text-gray-500">
              Formatos aceitos: PDF, JPG, PNG (máx. 5MB)
            </p>
          </div>

          {/* Observações */}
          <div className="space-y-2">
            <Label htmlFor="notes">Observações</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="Informações adicionais sobre o pagamento..."
              rows={3}
            />
          </div>

          {/* Botões */}
          <div className="flex justify-end space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              Cancelar
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Registrando...' : 'Registrar Pagamento'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
