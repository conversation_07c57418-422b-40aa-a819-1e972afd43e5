import os
from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import AnyHttpUrl, field_validator
from typing import List, Union

# Define o caminho base do projeto
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
ROOT_DIR = os.path.dirname(BASE_DIR)  # Diretório raiz do projeto (trix/)

# Determine the .env file to use
# Defaults to .env.docker if ENV_FILE_PATH is not set
# This allows tests to specify a different .env file (e.g., .env.test)
ENV_FILE_TO_LOAD = os.getenv("ENV_FILE_PATH", os.path.join(ROOT_DIR, ".env.docker"))
print(f"DEBUG_CONFIG_PY: ENV_FILE_TO_LOAD determined as: {ENV_FILE_TO_LOAD}")


class Settings(BaseSettings):
    """
    Configurações da aplicação carregadas do ambiente ou de um arquivo .env.
    Valores padrão aqui são apenas para referência e serão sobrescritos.
    A ausência de um valor no .env/ambiente para campos sem padrão causará erro.
    """

    # Carrega variáveis de um arquivo .env na raiz do projeto
    model_config = SettingsConfigDict(
        env_file=ENV_FILE_TO_LOAD,  # Use the dynamically determined .env file
        env_file_encoding="utf-8",
        extra="ignore",
    )

    # Configurações do Banco de Dados (Obrigatório no .env)
    DATABASE_URL: str

    # Configurações Redis (Obrigatório no .env ou via docker-compose)
    REDIS_URL: str

    # Configurações JWT (Obrigatório no .env)
    SECRET_KEY: str
    # O validador customizado para SECRET_KEY foi removido.
    # Pydantic carregará SECRET_KEY do ambiente (influenciado por .env.docker).
    # Se não estiver definida, Pydantic levantará um erro de validação, pois é um campo obrigatório.

    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7

    # Configurações da Aplicação (Obrigatório no .env)
    PROJECT_NAME: str = "Trix"
    DESCRIPTION: str = "Trix Platform API"
    VERSION: str = "0.1.0"
    DEBUG: bool = False
    TESTING_MODE: bool = False  # Added for disabling features in tests

    ENVIRONMENT: str = "development"

    API_PREFIX: str = "/api"  # Prefixo principal da API (ex: /api)

    # Configurações de CORS (Obrigatório no .env, mesmo que vazio)
    # Espera uma string separada por vírgulas no .env
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = []

    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            # Se for uma string e não parecer uma lista JSON, assume que é separada por vírgulas
            return [item.strip() for item in v.split(",") if item.strip()]
        elif isinstance(v, (list, str)):
            # Se já for uma lista ou uma string formatada como lista JSON, retorna como está
            return v
        raise ValueError(v)

    # Configurações de i18n (Obrigatório no .env)
    DEFAULT_LANGUAGE: str  # Idioma padrão da aplicação
    SUPPORTED_LANGUAGES: List[str]  # Idiomas suportados (string separada por vírgula no .env)
    # Define o caminho dinamicamente baseado na localização deste arquivo
    LOCALES_DIR: str = os.path.join(ROOT_DIR, "locales")

    @field_validator("SUPPORTED_LANGUAGES", mode="before")
    @classmethod
    def assemble_supported_languages(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            # Se for uma string e não parecer uma lista JSON, assume que é separada por vírgulas
            return [item.strip() for item in v.split(",") if item.strip()]
        elif isinstance(v, (list, str)):
            # Se já for uma lista ou uma string formatada como lista JSON, retorna como está
            return v
        raise ValueError(v)

    # Configurações de Domínio Personalizado / Traefik
    # Domínio base para CNAME (ex: trix-sites.com) - Obrigatório no .env
    CNAME_TARGET_BASE_DOMAIN: str
    TRAEFIK_DYNAMIC_CONFIG_PATH: (
        str  # Caminho para configs dinâmicas do Traefik - Obrigatório no .env
    )
    TRAEFIK_TRIX_SERVICE_NAME: str = (
        "trix-app-service"  # Nome do serviço Traefik para o backend Trix
    )
    TRAEFIK_CERT_RESOLVER_NAME: str = "letsencrypt"  # Nome do resolvedor ACME/LetsEncrypt
    TRAEFIK_HTTPS_REDIRECT_MIDDLEWARE: str = (
        "https-redirect@file"  # Nome do middleware de redirect HTTPS
    )
    # Código da feature de assinatura
    FEATURE_CODE_CUSTOM_DOMAIN_AUTOMATION: str = "feature_full_custom_domain_automation"

    # Configurações de E-mail (Opcional - Defina no .env se for usar)
    # SMTP_TLS: bool = True
    # SMTP_PORT: Optional[int] = None
    # SMTP_HOST: Optional[str] = None
    # SMTP_USER: Optional[str] = None
    # SMTP_PASSWORD: Optional[str] = None
    # EMAILS_FROM_EMAIL: Optional[EmailStr] = None
    # EMAILS_FROM_NAME: Optional[str] = None


# print('DEBUG_CONFIG_PY: Attempting to load Settings...', flush=True) # DEBUG Removido

# Instância global das configurações
settings = Settings()
# print(f"DEBUG_CONFIG_PY: Settings loaded. SECRET_KEY='{getattr(settings,
# 'SECRET_KEY', 'NOT_FOUND')}'", flush=True) # DEBUG Removido

# Exemplo de como acessar:
# from app.core.config import settings
# db_url = settings.DATABASE_URL
# print(f"Loaded CORS origins: {settings.BACKEND_CORS_ORIGINS}")
