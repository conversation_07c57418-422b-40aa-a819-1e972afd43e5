"""Schemas for Email module."""

import uuid  # noqa: E402
from datetime import datetime
from typing import List, Optional, Dict, Any

from pydantic import BaseModel, EmailStr, Field, ConfigDict  # noqa: E402


# Email Domain Schemas


class EmailDomainBase(BaseModel):
    """Base schema for email domain."""

    domain_name: str = Field(..., description="Domain name")


class EmailDomainCreate(EmailDomainBase):
    """Schema for creating an email domain."""

    domain_registration_id: Optional[uuid.UUID] = Field(
        None,
        description="ID of the domain registration (if registered through the system)",
    )


class EmailDomainUpdate(BaseModel):
    """Schema for updating an email domain."""

    is_active: Optional[bool] = Field(None, description="Whether the domain is active")
    dkim_selector: Optional[str] = Field(None, description="DKIM selector")


class EmailDomainRead(EmailDomainBase):
    """Schema for reading an email domain."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    domain_registration_id: Optional[uuid.UUID]
    mx_records_configured: bool
    spf_record_configured: bool
    dkim_configured: bool
    dmarc_configured: bool
    dkim_selector: str
    is_active: bool
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class EmailDomainDNSRecords(BaseModel):
    """Schema for DNS records for an email domain."""

    mx_records: List[str]
    spf_record: str
    dkim_record: str
    dmarc_record: str


# Email Account Schemas


class EmailAccountBase(BaseModel):
    """Base schema for email account."""

    username: str = Field(..., description="Username (part before @)")
    full_email: EmailStr = Field(..., description="Full email address")


class EmailAccountCreate(EmailAccountBase):
    """Schema for creating an email account."""

    email_domain_id: uuid.UUID = Field(..., description="ID of the email domain")
    password: str = Field(..., min_length=8, description="Password")
    quota_mb: int = Field(1024, description="Quota in MB")
    user_id: Optional[uuid.UUID] = Field(None, description="ID of the system user (if applicable)")


class EmailAccountUpdate(BaseModel):
    """Schema for updating an email account."""

    quota_mb: Optional[int] = Field(None, description="Quota in MB")
    is_active: Optional[bool] = Field(None, description="Whether the account is active")


class EmailAccountRead(EmailAccountBase):
    """Schema for reading an email account."""

    id: uuid.UUID
    email_domain_id: uuid.UUID
    quota_mb: int
    is_active: bool
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime]
    user_id: Optional[uuid.UUID]

    model_config = ConfigDict(from_attributes=True)


class EmailAccountUsage(BaseModel):
    """Schema for email account usage."""

    used_bytes: int
    quota_bytes: int
    usage_percentage: float


class EmailAccountPasswordChange(BaseModel):
    """Schema for changing an email account password."""

    current_password: str = Field(..., description="Current password")
    new_password: str = Field(..., min_length=8, description="New password")


class EmailAccountPasswordReset(BaseModel):
    """Schema for resetting an email account password (admin only)."""

    new_password: str = Field(..., min_length=8, description="New password")


# Email Alias Schemas


class EmailAliasBase(BaseModel):
    """Base schema for email alias."""

    source: str = Field(..., description="Source email (alias)")
    destination: str = Field(..., description="Destination email")


class EmailAliasCreate(EmailAliasBase):
    """Schema for creating an email alias."""

    email_domain_id: uuid.UUID = Field(..., description="ID of the email domain")


class EmailAliasUpdate(BaseModel):
    """Schema for updating an email alias."""

    destination: Optional[str] = Field(None, description="Destination email")
    is_active: Optional[bool] = Field(None, description="Whether the alias is active")


class EmailAliasRead(EmailAliasBase):
    """Schema for reading an email alias."""

    id: uuid.UUID
    email_domain_id: uuid.UUID
    is_active: bool
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Email Metadata Schemas (for webmail)


class EmailMetadataRead(BaseModel):
    """Schema for reading email metadata."""

    id: uuid.UUID
    message_id: str
    from_address: str
    to_addresses: str
    cc_addresses: Optional[str]
    subject: str
    received_date: datetime
    mailbox: str
    is_read: bool
    is_flagged: bool
    is_deleted: bool
    size_bytes: int

    model_config = ConfigDict(from_attributes=True)


class EmailContent(BaseModel):
    """Schema for email content."""

    metadata: EmailMetadataRead
    html_body: Optional[str]
    text_body: Optional[str]
    attachments: List[Dict[str, Any]]


class EmailSend(BaseModel):
    """Schema for sending an email."""

    to_addresses: List[str]
    cc_addresses: Optional[List[str]]
    bcc_addresses: Optional[List[str]]
    subject: str
    html_body: Optional[str]
    text_body: Optional[str]
    attachments: Optional[List[Dict[str, Any]]]


class EmailMove(BaseModel):
    """Schema for moving an email."""

    target_mailbox: str


class EmailRead(BaseModel):
    """Schema for marking an email as read/unread."""

    is_read: bool


class EmailFlag(BaseModel):
    """Schema for flagging an email."""

    is_flagged: bool


class MailboxCreate(BaseModel):
    """Schema for creating a mailbox."""

    name: str
