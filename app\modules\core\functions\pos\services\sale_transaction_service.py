import uuid
from typing import List, Optional
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import (
    update as sqlalchemy_update,
    delete as sqlalchemy_delete,
    and_,
    func,
)
from fastapi import HTTPException, status  # noqa: E402
from datetime import datetime

from app.modules.core.functions.pos.models.sale_transaction import SaleTransaction  # noqa: E402
from app.modules.core.functions.pos.models.refund import Refund
from app.modules.core.functions.pos.models.cash_register_session import CashRegisterSession
from app.modules.core.functions.pos.schemas.sale_transaction import (
    SaleTransactionCreate,
    SaleTransactionUpdate,
    SaleTransactionRead,
)
from app.modules.core.functions.pos.schemas.refund import RefundCreate  # noqa: E402
from app.modules.core.functions.pos.services.cash_register_service import (
    cash_register_service,
)  # Para validar o caixa
from app.modules.core.users.services.user_service import user_service  # noqa: E402
from app.modules.core.users.services.tenant_user_association_service import (
    tenant_user_association_service,
)
from app.modules.core.users.models.user import User  # Import User model
from app.core.exceptions import BusinessLogicError


class SaleTransactionService:
    async def get(
        self,
        db: AsyncSession,
        id: uuid.UUID,
        tenant_id: uuid.UUID,
        include_refunds: bool = False,
    ) -> Optional[SaleTransaction]:
        """
        Obtém uma transação pelo ID, garantindo que pertença ao tenant especificado.

        Args:
            db: Sessão do banco de dados
            id: ID da transação
            tenant_id: ID do tenant
            include_refunds: Se True, carrega os estornos relacionados à transação

        Returns:
            A transação encontrada ou None se não existir
        """
        result = await db.execute(
            select(SaleTransaction).where(
                SaleTransaction.id == id, SaleTransaction.tenant_id == tenant_id
            )
        )
        transaction = result.scalars().first()

        if transaction and include_refunds:
            # Carregar os estornos relacionados
            result = await db.execute(
                select(Refund)
                .where(Refund.transaction_id == id, Refund.tenant_id == tenant_id)
                .order_by(Refund.created_at)
            )
            transaction.refunds = result.scalars().all()

        return transaction

    async def get_multi(
        self,
        db: AsyncSession,
        *,
        tenant_id: uuid.UUID,
        cash_register_id: Optional[uuid.UUID] = None,
        customer_id: Optional[uuid.UUID] = None,
        # Adicionar filtros de data aqui se necessário (start_date, end_date)
        skip: int = 0,
        limit: int = 100,
    ) -> List[SaleTransaction]:
        """
        Obtém uma lista de transações para um tenant específico, com filtros opcionais.
        """
        filters = [SaleTransaction.tenant_id == tenant_id]
        if cash_register_id:
            filters.append(SaleTransaction.cash_register_id == cash_register_id)
        if customer_id:
            filters.append(SaleTransaction.customer_id == customer_id)
        # Adicionar filtros de data aqui

        query = (
            select(SaleTransaction)
            .where(and_(*filters))
            .offset(skip)
            .limit(limit)
            .order_by(SaleTransaction.created_at.desc())  # Ordenar pelas mais recentes
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def create_transaction(
        self,
        db: AsyncSession,
        *,
        obj_in: SaleTransactionCreate,
        tenant_id: uuid.UUID,
        cash_register_id: uuid.UUID,
    ) -> SaleTransaction:
        """
        Cria uma nova transação de venda.

        Valida o caixa, busca/vincula o cliente (ou armazena identificador) e cria a transação.
        """
        # 1. Validar Cash Register
        cash_register = await cash_register_service.get(
            db=db, id=cash_register_id, tenant_id=tenant_id
        )
        if not cash_register:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cash Register with id {cash_register_id} not found or does not belong to the tenant.",  # noqa: E501
            )
        if not cash_register.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cash Register with id {cash_register_id} is not active.",
            )

        # 1.1 Check for active session
        result = await db.execute(
            select(CashRegisterSession).where(
                CashRegisterSession.cash_register_id == cash_register_id,
                CashRegisterSession.tenant_id == tenant_id,
                CashRegisterSession.is_open,
            )
        )
        active_session = result.scalars().first()

        if not active_session:
            raise BusinessLogicError(
                f"Cash register with id {cash_register_id} does not have an active session. Please open a session before creating transactions."  # noqa: E501
            )

        # 2. Lógica de Vínculo do Cliente
        customer: Optional[User] = None
        customer_id_to_save: Optional[uuid.UUID] = None
        unregistered_email: Optional[str] = None
        unregistered_phone: Optional[str] = None

        identifier = obj_in.customer_email or obj_in.customer_phone  # Email tem precedência

        if identifier:
            if obj_in.customer_email:
                customer = await user_service.get_user_by_email(db, email=obj_in.customer_email)
            elif obj_in.customer_phone:  # Somente busca por telefone se email não foi fornecido
                customer = await user_service.get_user_by_phone(
                    db, phone_number=obj_in.customer_phone
                )

            if customer:
                customer_id_to_save = customer.id
                # Garantir associação TenantUser com role 'costumer_customer'
                await tenant_user_association_service.add_user_to_tenant(
                    db=db,
                    tenant_id=tenant_id,
                    user_id=customer.id,
                    role_name="costumer_customer",  # Garante a role correta
                )
            else:
                # Cliente não encontrado, armazenar identificador para pré-registro
                if obj_in.customer_email:
                    unregistered_email = obj_in.customer_email
                elif obj_in.customer_phone:
                    unregistered_phone = obj_in.customer_phone

        # 3. Criar a Transação
        transaction_data = obj_in.model_dump(exclude={"customer_email", "customer_phone"})
        db_obj = SaleTransaction(
            **transaction_data,
            tenant_id=tenant_id,
            cash_register_id=cash_register_id,
            # TODO: Re-enable when session_id column exists in database
            # session_id=active_session.id,  # Associate with the active session
            customer_id=customer_id_to_save,  # Pode ser None se não encontrado/não fornecido
            unregistered_customer_email=unregistered_email,
            unregistered_customer_phone=unregistered_phone,
        )
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def update(
        self,
        db: AsyncSession,
        *,
        id: uuid.UUID,
        obj_in: SaleTransactionUpdate,
        tenant_id: uuid.UUID,
    ) -> Optional[SaleTransaction]:
        """
        Atualiza uma transação existente (exemplo, pode não ser usado inicialmente).
        Garante que a transação pertença ao tenant especificado.
        """
        update_data = obj_in.model_dump(exclude_unset=True)
        if not update_data:
            return await self.get(db=db, id=id, tenant_id=tenant_id)

        # Verifica se a transação existe e pertence ao tenant
        db_obj = await self.get(db=db, id=id, tenant_id=tenant_id)
        if not db_obj:
            return None

        # Validar customer_id se estiver sendo atualizado
        if "customer_id" in update_data and update_data["customer_id"] is not None:
            customer = await user_service.get(db=db, id=update_data["customer_id"])
            if not customer:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Customer (User) with id {update_data['customer_id']} not found.",
                )

        await db.execute(
            sqlalchemy_update(SaleTransaction)
            .where(SaleTransaction.id == id, SaleTransaction.tenant_id == tenant_id)
            .values(**update_data)
        )
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def remove(
        self, db: AsyncSession, *, id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Optional[SaleTransaction]:
        """
        Remove uma transação pelo ID, garantindo que pertença ao tenant especificado.
        """
        db_obj = await self.get(db=db, id=id, tenant_id=tenant_id)
        if not db_obj:
            return None

        await db.execute(
            sqlalchemy_delete(SaleTransaction).where(
                SaleTransaction.id == id, SaleTransaction.tenant_id == tenant_id
            )
        )
        await db.commit()
        return db_obj

    async def get_transaction_refunds(
        self, db: AsyncSession, *, transaction_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> List[Refund]:
        """
        Obtém todos os estornos de uma transação específica.
        """
        result = await db.execute(
            select(Refund)
            .where(Refund.transaction_id == transaction_id, Refund.tenant_id == tenant_id)
            .order_by(Refund.created_at)
        )
        return result.scalars().all()

    async def calculate_refunded_amount(
        self, db: AsyncSession, *, transaction_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Decimal:
        """
        Calcula o valor total já estornado para uma transação.
        """
        result = await db.execute(
            select(func.sum(Refund.amount)).where(
                Refund.transaction_id == transaction_id, Refund.tenant_id == tenant_id
            )
        )
        total_refunded = result.scalar_one_or_none()
        return total_refunded or Decimal("0.00")

    async def create_refund(
        self,
        db: AsyncSession,
        *,
        transaction_id: uuid.UUID,
        tenant_id: uuid.UUID,
        refund_data: RefundCreate,
        user_id: Optional[uuid.UUID] = None,
    ) -> Refund:
        """
        Cria um novo estorno para uma transação.

        Verifica se a transação existe, se pertence ao tenant e se o valor do estorno é válido.
        """
        # 1. Verificar se a transação existe e pertence ao tenant
        transaction = await self.get(db=db, id=transaction_id, tenant_id=tenant_id)
        if not transaction:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Transaction with id {transaction_id} not found or does not belong to the tenant.",  # noqa: E501
            )

        # 2. Verificar se a transação está em um estado que permite estorno
        if transaction.status != "completed":
            raise BusinessLogicError(
                f"Cannot refund transaction with status '{transaction.status}'. Only 'completed' transactions can be refunded."  # noqa: E501
            )

        # 3. Calcular o valor já estornado
        total_refunded = await self.calculate_refunded_amount(
            db, transaction_id=transaction_id, tenant_id=tenant_id
        )

        # 4. Verificar se o valor do estorno é válido
        remaining_amount = transaction.total_amount - total_refunded
        if refund_data.amount > remaining_amount:
            raise BusinessLogicError(
                f"Refund amount ({refund_data.amount}) exceeds the remaining amount available for refund ({remaining_amount})."  # noqa: E501
            )

        # 5. Criar o estorno
        refund = Refund(
            transaction_id=transaction_id,
            tenant_id=tenant_id,
            amount=refund_data.amount,
            reason=refund_data.reason,
            created_by=user_id,
            created_at=datetime.utcnow(),
        )

        # 6. Atualizar o status da transação se o estorno for total
        new_total_refunded = total_refunded + refund_data.amount
        if new_total_refunded >= transaction.total_amount:
            transaction.status = "refunded"
        elif new_total_refunded > 0:
            transaction.status = "partially_refunded"

        # 7. Salvar as alterações
        db.add(refund)
        db.add(transaction)
        await db.commit()
        await db.refresh(refund)

        return refund


# Instância do serviço
sale_transaction_service = SaleTransactionService()
