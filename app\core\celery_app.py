from app.core.redis_client import celery_redis_url
import logging
from celery import Celery
from app.core.config import settings

# Configurar logging
logger = logging.getLogger(__name__)

# Importar URLs do Redis do módulo centralizado

# Configurar URLs do broker e backend
broker_url = getattr(settings, "CELERY_BROKER_URL", celery_redis_url)
result_backend_url = getattr(settings, "CELERY_RESULT_BACKEND", celery_redis_url)

logger.info(
    f"Configurando Celery com broker_url={broker_url}, result_backend_url={result_backend_url}"
)

# Criar a instância do Celery
# O primeiro argumento é o nome do módulo atual, importante para autodiscovery de tasks
celery_app = Celery(
    "trix_tasks",
    broker=broker_url,
    backend=result_backend_url,
)

# Configurações adicionais do Celery (podem ser movidas para settings)
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],  # Evitar pickle por segurança
    result_serializer="json",
    # Usar timezone das settings se disponível
    timezone=settings.TIMEZONE if hasattr(settings, "TIMEZONE") else "UTC",
    enable_utc=True,
    # Configuração do Celery Beat (agendador)
    beat_schedule={
        "check-pending-ssl-every-5-minutes": {
            "task": "app.tasks.domain_tasks.check_pending_ssl_task",  # Nome completo da task
            "schedule": 300.0,  # Executar a cada 300 segundos (5 minutos)
            # 'args': (16, 16), # Argumentos posicionais para a task (se necessário)
            # 'kwargs': {'arg1': 'value'}, # Argumentos nomeados para a task (se necessário)
            # 'options': {'queue': 'domains'}, # Opções de roteamento (se necessário)
        },
        # Adicione outras tarefas periódicas aqui no futuro
    },
    # Configuração de roteamento (opcional, mas bom para organizar)
    # task_routes = {
    #     'app.tasks.domain_tasks.*': {'queue': 'domains'},
    # }
)

# Configure task discovery and registration
def setup_tasks():
    """Setup and register all tasks."""
    logger.info("Setting up Celery tasks...")

    # Method 1: Explicit import
    try:
        logger.info("Importing tasks explicitly...")
        import app.tasks.domain_tasks  # noqa: F401
        logger.info("✅ Successfully imported app.tasks.domain_tasks")
    except ImportError as e:
        logger.error(f"❌ Failed to import app.tasks.domain_tasks: {e}")

    # Method 2: Autodiscover
    try:
        logger.info("Running autodiscover_tasks...")
        celery_app.autodiscover_tasks(['app.tasks'])
        logger.info("✅ Autodiscover completed")
    except Exception as e:
        logger.error(f"❌ Autodiscover failed: {e}")

    # Method 3: Force registration check
    try:
        logger.info("Checking registered tasks...")
        registered_tasks = [name for name in celery_app.tasks.keys() if not name.startswith('celery.')]
        logger.info(f"📋 Registered tasks ({len(registered_tasks)}): {registered_tasks}")

        # Check for our specific task
        target_task = "app.tasks.domain_tasks.check_pending_ssl_task"
        if target_task in celery_app.tasks:
            logger.info(f"✅ Target task {target_task} is registered!")
        else:
            logger.error(f"❌ Target task {target_task} is NOT registered!")

    except Exception as e:
        logger.error(f"❌ Error checking registered tasks: {e}")

# Setup tasks
setup_tasks()

if __name__ == "__main__":
    # Permite executar o worker Celery diretamente com: python -m app.core.celery_app worker -l info
    celery_app.start()
