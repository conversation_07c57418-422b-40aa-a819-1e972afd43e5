import uuid
import uuid
from sqlalchemy import Column, String, Foreign<PERSON>ey, Boolean, Index, Text, Enum as SAEnum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, Mapped, mapped_column
from typing import Optional

from app.db.base import Base
from app.core.enums import MarketType

class eshopCategory(Base):
    __tablename__ = "eshop_categories"

    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name: Mapped[str] = mapped_column(String(100), nullable=False, index=True)
    slug: Mapped[str] = mapped_column(String(100), nullable=False, unique=True, index=True)
    description: Mapped[str | None] = mapped_column(Text)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)

    parent_id: Mapped[uuid.UUID | None] = mapped_column(UUID(as_uuid=True), ForeignKey("eshop_categories.id"), nullable=True, index=True)

    market_type: Mapped[Optional[MarketType]] = mapped_column(SAEnum(MarketType), nullable=False, default=MarketType.PUBLIC, server_default=MarketType.PUBLIC.value)

    # Relationships
    # Note: eshopCategory is separate from ProductCategory - products use ProductCategory

    parent: Mapped[Optional["eshopCategory"]] = relationship(
        "eshopCategory",
        remote_side=[id],
        back_populates="children",
        foreign_keys=[parent_id],
    )
    children: Mapped[list["eshopCategory"]] = relationship(
        "eshopCategory",
        back_populates="parent",
        cascade="all, delete-orphan",
    )
    # products: Mapped[list["Product"]] = relationship("app.modules.core.eshop.models.product.Product", back_populates="category")

    __table_args__ = (
        Index("ix_eshop_global_categories_name_parent", "name", "parent_id", unique=True),
    )

    def __repr__(self) -> str:
        return f"<eshopCategory(id={self.id}, name='{self.name}')>"