/**
 * Serviço de API para gerenciamento de tenants
 * Integração completa com o backend de tenant management
 */

import { apiClient } from '@/lib/api/client';
import { Tenant, TenantUserAssociation } from '@/types/auth';

export interface TenantCreate {
  name: string;
  subdomain?: string;
  tenant_type?: string;
  description?: string;
}

export interface TenantUpdate {
  name?: string;
  subdomain?: string;
  tenant_type?: string;
  description?: string;
  is_active?: boolean;
}

export interface TenantFilters {
  skip?: number;
  limit?: number;
  is_active?: boolean;
}

export interface AddUserToTenantPayload {
  user_id: string;
  role: 'owner' | 'staff' | 'customer';
  staff_sub_role?: string;
  data_sharing_consent?: boolean;
}

class TenantService {
  private readonly baseUrl = '/tenants';

  /**
   * Lista todos os tenants (apenas para admins do sistema)
   */
  async getTenants(filters: TenantFilters = {}): Promise<Tenant[]> {
    try {
      const params = new URLSearchParams();
      
      if (filters.skip !== undefined) {
        params.append('skip', filters.skip.toString());
      }
      if (filters.limit !== undefined) {
        params.append('limit', filters.limit.toString());
      }

      const queryString = params.toString();
      const url = queryString ? `${this.baseUrl}/?${queryString}` : `${this.baseUrl}/`;

      console.log('🔍 TenantService.getTenants - URL being called:', url);
      
      const response = await apiClient.get<Tenant[]>(url);
      
      console.log('🔍 TenantService.getTenants - Response received:', response);

      return response.data;
    } catch (error) {
      console.error('Error fetching tenants:', error);
      throw error;
    }
  }

  /**
   * Obtém detalhes de um tenant específico
   */
  async getTenant(tenantId: string): Promise<Tenant> {
    try {
      // Validate tenantId before making API call
      if (!tenantId || tenantId === 'undefined' || tenantId === 'null') {
        throw new Error(`Invalid tenantId: ${tenantId}`);
      }

      // Validate UUID format
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(tenantId)) {
        throw new Error(`Invalid UUID format: ${tenantId}`);
      }

      console.log('🔍 TenantService.getTenant - Getting tenant:', tenantId);

      const response = await apiClient.get<Tenant>(`${this.baseUrl}/${tenantId}`);

      console.log('🔍 TenantService.getTenant - Response received:', response);

      return response.data;
    } catch (error) {
      console.error('Error fetching tenant:', error);
      throw error;
    }
  }

  /**
   * Cria um novo tenant
   */
  async createTenant(tenantData: TenantCreate): Promise<Tenant> {
    try {
      console.log('🔍 TenantService.createTenant - Creating tenant:', tenantData);
      
      const response = await apiClient.post<Tenant>(`${this.baseUrl}/`, tenantData);
      
      console.log('🔍 TenantService.createTenant - Response received:', response);

      return response.data;
    } catch (error) {
      console.error('Error creating tenant:', error);
      throw error;
    }
  }

  /**
   * Atualiza um tenant existente
   */
  async updateTenant(tenantId: string, tenantData: TenantUpdate): Promise<Tenant> {
    try {
      console.log('🔍 TenantService.updateTenant - Updating tenant:', tenantId, tenantData);
      
      const response = await apiClient.put<Tenant>(`${this.baseUrl}/${tenantId}`, tenantData);
      
      console.log('🔍 TenantService.updateTenant - Response received:', response);

      return response.data;
    } catch (error) {
      console.error('Error updating tenant:', error);
      throw error;
    }
  }

  /**
   * Lista usuários de um tenant
   */
  async getTenantUsers(tenantId: string): Promise<TenantUserAssociation[]> {
    try {
      console.log('🔍 TenantService.getTenantUsers - Getting users for tenant:', tenantId);
      
      const response = await apiClient.get<TenantUserAssociation[]>(
        `${this.baseUrl}/${tenantId}/users`
      );
      
      console.log('🔍 TenantService.getTenantUsers - Response received:', response);

      return response.data;
    } catch (error) {
      console.error('Error fetching tenant users:', error);
      throw error;
    }
  }

  /**
   * Adiciona um usuário ao tenant
   */
  async addUserToTenant(
    tenantId: string, 
    userData: AddUserToTenantPayload
  ): Promise<TenantUserAssociation> {
    try {
      console.log('🔍 TenantService.addUserToTenant - Adding user to tenant:', tenantId, userData);
      
      const response = await apiClient.post<TenantUserAssociation>(
        `${this.baseUrl}/${tenantId}/users`,
        userData
      );
      
      console.log('🔍 TenantService.addUserToTenant - Response received:', response);

      return response.data;
    } catch (error) {
      console.error('Error adding user to tenant:', error);
      throw error;
    }
  }

  /**
   * Valida se um tenant está ativo e acessível
   */
  async validateTenantAccess(tenantId: string): Promise<boolean> {
    try {
      const tenant = await this.getTenant(tenantId);
      return tenant.is_active;
    } catch (error) {
      console.error('Error validating tenant access:', error);
      return false;
    }
  }

  /**
   * Busca tenants por nome (para busca no TenantSelector)
   */
  async searchTenants(query: string, limit: number = 10): Promise<Tenant[]> {
    try {
      // Por enquanto, busca todos e filtra no frontend
      // TODO: Implementar busca no backend quando disponível
      const allTenants = await this.getTenants({ limit: 100 });
      
      const filteredTenants = allTenants.filter(tenant =>
        tenant.id.toLowerCase().includes(query.toLowerCase())
      );
      
      return filteredTenants.slice(0, limit);
    } catch (error) {
      console.error('Error searching tenants:', error);
      return [];
    }
  }

  /**
   * Obtém estatísticas básicas de um tenant
   */
  async getTenantStats(tenantId: string): Promise<{
    userCount: number;
    isActive: boolean;
    createdAt: string;
  }> {
    try {
      const [tenant, users] = await Promise.all([
        this.getTenant(tenantId),
        this.getTenantUsers(tenantId)
      ]);

      return {
        userCount: users.length,
        isActive: tenant.is_active,
        createdAt: tenant.created_at
      };
    } catch (error) {
      console.error('Error fetching tenant stats:', error);
      throw error;
    }
  }
}

// Exportar instância única do serviço
export const tenantService = new TenantService();
export default tenantService;
