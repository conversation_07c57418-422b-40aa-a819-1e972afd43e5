"""Financial integration service for shopping list module."""

from decimal import Decimal
from datetime import date, datetime
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
import uuid

from app.modules.shared.financial.transactions.models.transaction import (
    FinancialTransaction, TransactionType
)
from app.modules.shared.shopping_list.models.shopping_list import (
    ShoppingList, ShoppingListItem, ShoppingListItemStatus
)
from app.modules.shared.supplier.models.supplier import Supplier


class FinancialIntegrationService:
    """Service for integrating shopping list with financial module."""

    async def register_supplier_invoice(
        self,
        db: AsyncSession,
        *,
        shopping_list_items: List[uuid.UUID],
        supplier_id: uuid.UUID,
        tenant_id: uuid.UUID,
        created_by: uuid.UUID,
        invoice_number: str,
        invoice_date: date,
        total_amount: Decimal,
        delivery_note_number: Optional[str] = None,
        due_date: Optional[date] = None,
        notes: Optional[str] = None
    ) -> FinancialTransaction:
        """
        Register a supplier invoice in the financial system as an expense transaction.

        This is called when a supplier submits their invoice for delivered items.
        The invoice is registered as a financial outflow (expense).
        """

        # Get supplier information
        result = await db.execute(
            select(Supplier).where(Supplier.id == supplier_id)
        )
        supplier = result.scalar_one_or_none()

        if not supplier:
            raise ValueError("Supplier not found")

        # Get shopping list items
        if shopping_list_items:
            result = await db.execute(
                select(ShoppingListItem).where(
                    ShoppingListItem.id.in_(shopping_list_items)
                )
            )
            items = result.scalars().all()

            # Build description from items
            item_descriptions = [f"{item.name} (Qty: {item.quantity_received or item.quantity_needed})" for item in items[:3]]
            if len(items) > 3:
                item_descriptions.append(f"... and {len(items) - 3} more items")
            description = f"Supplier Invoice {invoice_number} - {', '.join(item_descriptions)}"
        else:
            description = f"Supplier Invoice {invoice_number} from {supplier.name}"

        # Create expense transaction
        transaction = FinancialTransaction(
            tenant_id=tenant_id,
            transaction_type=TransactionType.EXPENSE,
            amount=total_amount,
            description=description,
            transaction_date=invoice_date,
            reference_number=invoice_number,
            notes=notes or f"Supplier invoice registered. Delivery note: {delivery_note_number}" if delivery_note_number else "Supplier invoice registered",
            created_by=created_by
        )

        db.add(transaction)
        await db.commit()
        await db.refresh(transaction)

        return transaction

    async def register_invoice_payment(
        self,
        db: AsyncSession,
        *,
        transaction_id: uuid.UUID,
        tenant_id: uuid.UUID,
        payment_method_id: uuid.UUID,
        payment_date: date,
        payment_amount: Decimal,
        payment_reference: Optional[str] = None,
        payment_receipt_file: Optional[str] = None,
        notes: Optional[str] = None,
        updated_by: uuid.UUID
    ) -> FinancialTransaction:
        """
        Register payment for a supplier invoice.

        This is called when the tenant adds a payment receipt for a supplier invoice.
        Updates the transaction status to PAID and records payment details.
        """

        # Get the transaction
        result = await db.execute(
            select(FinancialTransaction).where(
                and_(
                    FinancialTransaction.id == transaction_id,
                    FinancialTransaction.tenant_id == tenant_id
                )
            )
        )
        transaction = result.scalar_one_or_none()

        if not transaction:
            raise ValueError("Transaction not found")

        # Update transaction with payment details
        transaction.payment_method_id = payment_method_id

        # Handle partial payments and add notes
        original_notes = transaction.notes or ""
        payment_notes = []

        if payment_amount != transaction.amount:
            payment_notes.append(f"Partial payment: {payment_amount} of {transaction.amount}")
        else:
            payment_notes.append(f"Full payment: {payment_amount}")

        if payment_reference:
            payment_notes.append(f"Payment reference: {payment_reference}")

        if payment_receipt_file:
            payment_notes.append(f"Receipt file: {payment_receipt_file}")

        if notes:
            payment_notes.append(f"Payment notes: {notes}")

        # Update notes
        if payment_notes:
            transaction.notes = f"{original_notes}\n{' | '.join(payment_notes)}" if original_notes else ' | '.join(payment_notes)

        await db.commit()
        await db.refresh(transaction)

        return transaction

    async def get_pending_supplier_invoices(
        self,
        db: AsyncSession,
        *,
        tenant_id: uuid.UUID,
        supplier_id: Optional[uuid.UUID] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[FinancialTransaction]:
        """Get supplier invoices (expenses) for payment."""

        query = select(FinancialTransaction).where(
            and_(
                FinancialTransaction.tenant_id == tenant_id,
                FinancialTransaction.transaction_type == TransactionType.EXPENSE,
                FinancialTransaction.reference_number.isnot(None),  # Has invoice number
                FinancialTransaction.payment_method_id.is_(None)  # Not yet paid
            )
        )

        # Filter by supplier if provided (using notes field to identify supplier)
        if supplier_id:
            # We'll need to join with supplier table or use notes field
            pass  # For now, skip supplier filtering

        query = query.order_by(FinancialTransaction.transaction_date.desc())
        query = query.offset(skip).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    async def get_supplier_invoice_summary(
        self,
        db: AsyncSession,
        *,
        tenant_id: uuid.UUID,
        supplier_id: Optional[uuid.UUID] = None
    ) -> dict:
        """Get summary of supplier invoices for the tenant."""

        base_query = select(FinancialTransaction).where(
            and_(
                FinancialTransaction.tenant_id == tenant_id,
                FinancialTransaction.transaction_type == TransactionType.EXPENSE,
                FinancialTransaction.reference_number.isnot(None)  # Has invoice number
            )
        )

        # Get all expense transactions (supplier invoices)
        result = await db.execute(base_query)
        all_invoices = result.scalars().all()

        # Separate pending and paid
        pending_invoices = [inv for inv in all_invoices if inv.payment_method_id is None]
        paid_invoices = [inv for inv in all_invoices if inv.payment_method_id is not None]

        # Calculate totals
        pending_total = sum(inv.amount for inv in pending_invoices)
        paid_total = sum(inv.amount for inv in paid_invoices)

        return {
            "pending_count": len(pending_invoices),
            "pending_total": pending_total,
            "paid_count": len(paid_invoices),
            "paid_total": paid_total,
            "total_invoices": len(all_invoices),
            "total_amount": pending_total + paid_total
        }


# Service instance
financial_integration_service = FinancialIntegrationService()
