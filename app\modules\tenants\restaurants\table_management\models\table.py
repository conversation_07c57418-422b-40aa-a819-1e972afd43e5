from __future__ import annotations

import enum
import uuid
from typing import TYPE_CHECKING, List, Optional

from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    Enum as SQLAlchemyEnum,
    Float,
    <PERSON><PERSON>ey,
    Integer,
    JSON,
    String,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.db.base import Base
from app.modules.core.functions.orders.models.order import Order

if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.shared.financial.orders.models.order import Order
    from app.modules.tenants.restaurants.qrcodes.models.qrcode import QRCodeScan
    from app.modules.tenants.restaurants.table_reservation.models.reservation import (
        Reservation,
    )


class TableStatus(str, enum.Enum):
    """Enum for table status."""

    AVAILABLE = "available"
    OCCUPIED = "occupied"
    RESERVED = "reserved"
    OUT_OF_SERVICE = "out_of_service"


class Table(Base):
    """Table model for restaurant table management."""

    __tablename__ = "restaurant_tables"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    tenant_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True
    )
    layout_id: Mapped[Optional[uuid.UUID]] = mapped_column(
        UUID(as_uuid=True), ForeignKey("restaurant_table_layouts.id")
    )

    # Table details
    table_number: Mapped[str] = mapped_column(String, nullable=False)
    name: Mapped[Optional[str]] = mapped_column(String)
    capacity: Mapped[int] = mapped_column(Integer, nullable=False)
    zone: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True, index=True
    )
    status: Mapped[TableStatus] = mapped_column(
        SQLAlchemyEnum(TableStatus),
        default=TableStatus.AVAILABLE,
        nullable=False,
        index=True,
    )

    # Position in layout
    position_x: Mapped[Optional[float]] = mapped_column(Float)
    position_y: Mapped[Optional[float]] = mapped_column(Float)
    width: Mapped[Optional[float]] = mapped_column(Float)
    height: Mapped[Optional[float]] = mapped_column(Float)

    # Additional information
    shape: Mapped[Optional[str]] = mapped_column(
        String
    )  # 'rectangle', 'circle', 'custom'
    custom_shape_data: Mapped[Optional[dict]] = mapped_column(JSON)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    notes: Mapped[Optional[str]] = mapped_column(String)

    # QR code information
    qrcode_id: Mapped[Optional[str]] = mapped_column(
        String(50), unique=True, index=True
    )
    qrcode_secret: Mapped[Optional[str]] = mapped_column(String(100))
    qrcode_enabled: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)

    # Relationships
    tenant: Mapped["Tenant"] = relationship()
    layout: Mapped["TableLayout"] = relationship(back_populates="tables")
    reservations: Mapped[List["Reservation"]] = relationship(
        back_populates="table", cascade="all, delete-orphan"
    )
    orders: Mapped[List["Order"]] = relationship(
        back_populates="table", cascade="all, delete-orphan"
    )
    qrcode_scans: Mapped[List["QRCodeScan"]] = relationship(
        back_populates="table", cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<Table(id={self.id}, tenant_id={self.tenant_id}, table_number={self.table_number}, status={self.status.value})>"


class TableLayout(Base):
    """Table layout model for restaurant floor plans."""

    __tablename__ = "restaurant_table_layouts"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    tenant_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True
    )

    # Layout details
    name: Mapped[str] = mapped_column(String, nullable=False)
    description: Mapped[Optional[str]] = mapped_column(String)
    floor_number: Mapped[Optional[int]] = mapped_column(Integer)
    width: Mapped[Optional[float]] = mapped_column(Float)
    height: Mapped[Optional[float]] = mapped_column(Float)
    background_image_url: Mapped[Optional[str]] = mapped_column(String)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)

    # Additional information
    layout_data: Mapped[Optional[dict]] = mapped_column(JSON)

    # Relationships
    tenant: Mapped["Tenant"] = relationship()
    tables: Mapped[List["Table"]] = relationship(
        back_populates="layout", cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<TableLayout(id={self.id}, tenant_id={self.tenant_id}, name={self.name})>"
