"""Configuration schemas for the Domain Rent module."""

import uuid  # noqa: E402
from datetime import datetime
from typing import Dict, Any, Optional

from pydantic import BaseModel, Field, ConfigDict  # noqa: E402

from app.modules.shared.domain_rent.models.registrar_config import RegistrarType  # noqa: E402


class RegistrarConfigBase(BaseModel):
    """Base schema for registrar configuration."""

    registrar: RegistrarType = Field(..., description="Registrar type")
    is_active: bool = Field(True, description="Whether this registrar is active")
    config_data: Optional[Dict[str, Any]] = Field(None, description="Additional configuration data")


class RegistrarConfigCreate(RegistrarConfigBase):
    """Schema for creating a registrar configuration."""

    pass


class RegistrarConfigUpdate(BaseModel):
    """Schema for updating a registrar configuration."""

    is_active: Optional[bool] = Field(None, description="Whether this registrar is active")
    config_data: Optional[Dict[str, Any]] = Field(None, description="Additional configuration data")


class RegistrarConfigRead(RegistrarConfigBase):
    """Schema for reading a registrar configuration."""

    id: uuid.UUID
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)
