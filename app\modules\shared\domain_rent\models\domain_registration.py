"""Domain Registration model for the Domain Rent module."""

import uuid  # noqa: E402
import enum
from datetime import datetime
from typing import TYPE_CHECKING, List, Optional

from sqlalchemy import (  # noqa: E402
    Column,
    String,
    Boolean,
    DateTime,
    ForeignKey,
    Enum,
    Index,
)
from sqlalchemy.dialects.postgresql import UUID  # noqa: E402
from sqlalchemy.orm import relationship

from app.db.base import Base  # noqa: E402

if TYPE_CHECKING:
    from app.modules.shared.email.models.email_domain import EmailDomain  # noqa: E402


class DomainStatus(str, enum.Enum):
    """Status of a domain registration."""

    PENDING = "pending"
    ACTIVE = "active"
    EXPIRED = "expired"
    TRANSFERRED_OUT = "transferred_out"
    REDEMPTION = "redemption"
    PENDING_TRANSFER = "pending_transfer"
    CANCELLED = "cancelled"


class DomainRegistration(Base):
    """Domain Registration model.

    Stores information about domains registered through the system.
    """

    __tablename__ = "domain_registrations"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True, index=True)

    domain_name = Column(String, nullable=False, index=True)
    tld = Column(String, nullable=False, index=True)
    registrar = Column(String, nullable=False, index=True)

    registration_date = Column(DateTime, nullable=False)
    expiry_date = Column(DateTime, nullable=False, index=True)

    status = Column(Enum(DomainStatus), nullable=False, default=DomainStatus.PENDING, index=True)

    auto_renew = Column(Boolean, default=False)
    whois_privacy_enabled = Column(Boolean, default=False)

    # Registrar-specific data (order ID, domain ID, etc.)
    registrar_data = Column(String, nullable=True)

    # Relationships
    contacts = relationship(
        "DomainContact",
        back_populates="domain_registration",
        cascade="all, delete-orphan",
    )

    nameservers = relationship(
        "DomainNameserver",
        back_populates="domain_registration",
        cascade="all, delete-orphan",
        order_by="DomainNameserver.sort_order",
    )

    # Relationships to User and Tenant
    user = relationship("User", back_populates="domain_registrations")
    tenant = relationship("Tenant", back_populates="domain_registrations")

    # Relationship to CustomDomain
    custom_domains = relationship(
        "CustomDomain",
        back_populates="domain_registration",
        foreign_keys="[CustomDomain.domain_registration_id]",
    )

    # Relationship to EmailDomain
    email_domains = relationship(
        "app.modules.shared.email.models.email_domain.EmailDomain",
        back_populates="domain_registration",
        foreign_keys="[app.modules.shared.email.models.email_domain.EmailDomain.domain_registration_id]",  # noqa: E501
    )

    # Indexes
    __table_args__ = (
        Index("ix_domain_registrations_domain_name_tld", domain_name, tld, unique=True),
    )

    def __repr__(self):
        return f"<DomainRegistration(id={self.id}, domain_name='{self.domain_name}', tld='{self.tld}')>"  # noqa: E501
