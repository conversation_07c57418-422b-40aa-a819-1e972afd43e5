"""Email Domain model for the Email module."""

import uuid  # noqa: E402
from datetime import datetime
from typing import TYPE_CHECKING, List, Optional

from sqlalchemy import (  # noqa: E402
    Column,
    String,
    Boolean,
    DateTime,
    ForeignKey,
    Index,
    Text,
)
from sqlalchemy.dialects.postgresql import UUID  # noqa: E402
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import Base  # noqa: E402

if TYPE_CHECKING:
    from app.models.tenant import Tenant  # noqa: E402
    from app.modules.shared.domain_rent.models.domain_registration import (
        DomainRegistration,
    )
    from app.modules.core.custom_domains.models import CustomDomain  # noqa: E402
    from app.modules.shared.email.models.email_account import EmailAccount
    from app.modules.shared.email.models.email_alias import EmailAlias


class EmailDomain(Base):
    """Email Domain model.

    Represents a domain configured for email services.
    """

    __tablename__ = "email_domains"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    domain_name = Column(String, nullable=False, index=True)

    # Relationships to Tenant and DomainRegistration
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    domain_registration_id = Column(
        UUID(as_uuid=True),
        ForeignKey("domain_registrations.id"),
        nullable=True,
        index=True,
    )

    # DNS configuration status
    mx_records_configured = Column(Boolean, default=False)
    spf_record_configured = Column(Boolean, default=False)
    dkim_configured = Column(Boolean, default=False)
    dmarc_configured = Column(Boolean, default=False)

    # DKIM configuration
    dkim_private_key = Column(Text, nullable=True)
    dkim_selector = Column(String, default="mail")

    # Status
    is_active = Column(Boolean, default=False)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )

    # Relationships
    tenant = relationship("Tenant", back_populates="email_domains")
    domain_registration = relationship(
        "DomainRegistration",
        back_populates="email_domains",
        foreign_keys=[domain_registration_id],
    )
    custom_domain = relationship(
        "CustomDomain",
        back_populates="email_domain",
        uselist=False,
        primaryjoin="EmailDomain.domain_name == CustomDomain.domain_name",
    )

    # Commented out until the module is implemented
    # email_accounts = relationship(
    #     "EmailAccount", back_populates="email_domain", cascade="all, delete-orphan"
    # )

    email_aliases = relationship(
        "EmailAlias", back_populates="email_domain", cascade="all, delete-orphan"
    )

    # Indexes
    __table_args__ = (
        Index(
            "ix_email_domains_tenant_id_domain_name",
            tenant_id,
            domain_name,
            unique=True,
        ),
    )

    def __repr__(self):
        return f"<EmailDomain(id={self.id}, domain_name='{self.domain_name}')>"
