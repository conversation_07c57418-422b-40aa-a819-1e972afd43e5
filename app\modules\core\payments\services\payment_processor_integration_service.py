"""Service for integrating payment processors with the core payment module."""

import uuid  # noqa: E402
import logging
from typing import Dict, Any, Optional
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.core.exceptions import BusinessLogicError  # noqa: E402
from app.modules.core.payments.models.payment_transaction import (
    PaymentTransaction,
    PaymentRefund,
    PaymentStatus,
)
from app.modules.core.payments.processors.base import PaymentProcessorFactory  # noqa: E402
from app.modules.core.payments.services.payment_processor_service import (
    payment_processor_service,
)
from app.modules.core.payments.services.payment_transaction_service import (  # noqa: E402
    payment_transaction_service,
)

logger = logging.getLogger(__name__)


class PaymentProcessorIntegrationService:
    """
    Service for integrating payment processors with the core payment module.

    This service provides methods for processing payments and refunds using
    the appropriate payment processor based on the processor ID.
    """

    async def process_payment(
        self,
        db: AsyncSession,
        *,
        transaction_id: uuid.UUID,
        tenant_id: uuid.UUID,
        payment_method_id: Optional[uuid.UUID] = None,
        customer_id: Optional[uuid.UUID] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> PaymentTransaction:
        """
        Process a payment using the appropriate payment processor.

        Args:
            db: The database session.
            transaction_id: The ID of the transaction to process.
            tenant_id: The ID of the tenant.
            payment_method_id: An optional ID of the payment method to use.
            customer_id: An optional ID of the customer.
            metadata: Optional additional data to include with the payment.

        Returns:
            The updated transaction.

        Raises:
            BusinessLogicError: If the transaction or processor is not found,
                or if the payment processing fails.
        """
        # Get the transaction
        transaction = await payment_transaction_service.get(
            db, id=transaction_id, tenant_id=tenant_id
        )

        if not transaction:
            raise BusinessLogicError(f"Transaction with ID {transaction_id} not found")

        # Get the processor
        processor_id = transaction.processor_id
        if not processor_id:
            raise BusinessLogicError("Transaction does not have a processor assigned")

        processor_db = await payment_processor_service.get(db, id=processor_id, tenant_id=tenant_id)

        if not processor_db:
            raise BusinessLogicError(f"Payment processor with ID {processor_id} not found")

        # Create processor instance
        try:
            processor_config = {
                "api_key": processor_db.api_key,
                "api_secret": processor_db.api_secret,
                "sandbox_mode": processor_db.sandbox_mode,
                "webhook_url": processor_db.webhook_url,
                "webhook_secret": processor_db.webhook_secret,
            }

            # Add additional config if available
            if processor_db.additional_config:
                processor_config.update(processor_db.additional_config)

            processor = PaymentProcessorFactory.create_processor(
                processor_db.processor_type, processor_config
            )

            if not processor:
                logger.error(
                    f"Payment processor {processor_db.processor_type} could not be initialized"
                )
                raise BusinessLogicError(
                    f"Payment processor {processor_db.processor_type} is not available"
                )

            # Process the payment
            payment_result = await processor.process_payment(
                amount=transaction.amount,
                currency=transaction.currency,
                payment_method_id=payment_method_id.hex if payment_method_id else None,
                customer_id=customer_id.hex if customer_id else None,
                metadata=metadata,
            )

            # Update the transaction
            update_data = {
                "status": payment_result.get("status", PaymentStatus.PENDING),
                "external_id": payment_result.get("external_id"),
                "external_reference": (
                    payment_result.get("client_secret")
                    if "client_secret" in payment_result
                    else None
                ),
                "processed_at": datetime.now(),
            }

            # Add any additional data to metadata
            if not transaction.metadata:
                transaction.metadata = {}

            transaction.metadata["payment_result"] = {
                k: v
                for k, v in payment_result.items()
                if k not in ["status", "external_id", "error"]
            }

            if "error" in payment_result:
                transaction.metadata["payment_error"] = payment_result["error"]

            # Update the transaction
            updated_transaction = await payment_transaction_service.update(
                db, id=transaction_id, tenant_id=tenant_id, obj_in=update_data
            )

            return updated_transaction

        except Exception as e:
            logger.error(f"Error processing payment: {str(e)}")
            raise BusinessLogicError(f"Error processing payment: {str(e)}")

    async def process_refund(
        self,
        db: AsyncSession,
        *,
        refund_id: uuid.UUID,
        tenant_id: uuid.UUID,
        reason: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> PaymentRefund:
        """
        Process a refund using the appropriate payment processor.

        Args:
            db: The database session.
            refund_id: The ID of the refund to process.
            tenant_id: The ID of the tenant.
            reason: An optional reason for the refund.
            metadata: Optional additional data to include with the refund.

        Returns:
            The updated refund.

        Raises:
            BusinessLogicError: If the refund, transaction, or processor is not found,
                or if the refund processing fails.
        """
        # Get the refund
        result = await db.execute(
            select(PaymentRefund).where(
                PaymentRefund.id == refund_id, PaymentRefund.tenant_id == tenant_id
            )
        )
        refund = result.scalars().first()

        if not refund:
            raise BusinessLogicError(f"Refund with ID {refund_id} not found")

        # Get the transaction
        transaction = await payment_transaction_service.get(
            db, id=refund.transaction_id, tenant_id=tenant_id
        )

        if not transaction:
            raise BusinessLogicError(f"Transaction with ID {refund.transaction_id} not found")

        # Get the processor
        processor_id = transaction.processor_id
        if not processor_id:
            raise BusinessLogicError("Transaction does not have a processor assigned")

        processor_db = await payment_processor_service.get(db, id=processor_id, tenant_id=tenant_id)

        if not processor_db:
            raise BusinessLogicError(f"Payment processor with ID {processor_id} not found")

        # Create processor instance
        try:
            processor_config = {
                "api_key": processor_db.api_key,
                "api_secret": processor_db.api_secret,
                "sandbox_mode": processor_db.sandbox_mode,
                "webhook_url": processor_db.webhook_url,
                "webhook_secret": processor_db.webhook_secret,
            }

            # Add additional config if available
            if processor_db.additional_config:
                processor_config.update(processor_db.additional_config)

            processor = PaymentProcessorFactory.create_processor(
                processor_db.processor_type, processor_config
            )

            if not processor:
                logger.error(
                    f"Payment processor {processor_db.processor_type} could not be initialized"
                )
                raise BusinessLogicError(
                    f"Payment processor {processor_db.processor_type} is not available"
                )

            # Process the refund
            refund_result = await processor.process_refund(
                payment_id=transaction.external_id,
                amount=refund.amount,
                reason=reason,
                metadata=metadata,
            )

            # Update the refund
            refund.status = refund_result.get("status", PaymentStatus.PENDING)
            refund.external_id = refund_result.get("external_id")
            refund.processed_at = datetime.now()

            # Add any additional data to metadata
            if not refund.metadata:
                refund.metadata = {}

            refund.metadata["refund_result"] = {
                k: v
                for k, v in refund_result.items()
                if k not in ["status", "external_id", "error"]
            }

            if "error" in refund_result:
                refund.metadata["refund_error"] = refund_result["error"]

            # Save the refund
            db.add(refund)
            await db.commit()
            await db.refresh(refund)

            # Update the transaction status if needed
            if refund.status == PaymentStatus.COMPLETED:
                # Get all completed refunds for this transaction
                result = await db.execute(
                    select(PaymentRefund).where(
                        PaymentRefund.transaction_id == transaction.id,
                        PaymentRefund.status == PaymentStatus.COMPLETED,
                    )
                )
                completed_refunds = result.scalars().all()

                # Calculate total refunded amount
                total_refunded = sum(r.amount for r in completed_refunds)

                # Update transaction status
                if total_refunded >= transaction.amount:
                    transaction.status = PaymentStatus.REFUNDED
                else:
                    transaction.status = PaymentStatus.PARTIALLY_REFUNDED

                db.add(transaction)
                await db.commit()

            return refund

        except Exception as e:
            logger.error(f"Error processing refund: {str(e)}")
            raise BusinessLogicError(f"Error processing refund: {str(e)}")

    async def check_payment_status(
        self, db: AsyncSession, *, transaction_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> PaymentTransaction:
        """
        Check the status of a payment with the payment processor.

        Args:
            db: The database session.
            transaction_id: The ID of the transaction to check.
            tenant_id: The ID of the tenant.

        Returns:
            The updated transaction.

        Raises:
            BusinessLogicError: If the transaction or processor is not found,
                or if the status check fails.
        """
        # Get the transaction
        transaction = await payment_transaction_service.get(
            db, id=transaction_id, tenant_id=tenant_id
        )

        if not transaction:
            raise BusinessLogicError(f"Transaction with ID {transaction_id} not found")

        # If the transaction doesn't have an external ID, it hasn't been processed yet
        if not transaction.external_id:
            return transaction

        # Get the processor
        processor_id = transaction.processor_id
        if not processor_id:
            raise BusinessLogicError("Transaction does not have a processor assigned")

        processor_db = await payment_processor_service.get(db, id=processor_id, tenant_id=tenant_id)

        if not processor_db:
            raise BusinessLogicError(f"Payment processor with ID {processor_id} not found")

        # Create processor instance
        try:
            processor_config = {
                "api_key": processor_db.api_key,
                "api_secret": processor_db.api_secret,
                "sandbox_mode": processor_db.sandbox_mode,
                "webhook_url": processor_db.webhook_url,
                "webhook_secret": processor_db.webhook_secret,
            }

            # Add additional config if available
            if processor_db.additional_config:
                processor_config.update(processor_db.additional_config)

            processor = PaymentProcessorFactory.create_processor(
                processor_db.processor_type, processor_config
            )

            if not processor:
                logger.error(
                    f"Payment processor {processor_db.processor_type} could not be initialized"
                )
                raise BusinessLogicError(
                    f"Payment processor {processor_db.processor_type} is not available"
                )

            # Check the payment status
            status_result = await processor.get_payment_status(transaction.external_id)

            # Update the transaction
            update_data = {
                "status": status_result.get("status", transaction.status),
                "processed_at": (
                    datetime.now()
                    if status_result.get("status") != transaction.status
                    else transaction.processed_at
                ),
            }

            # Add any additional data to metadata
            if not transaction.metadata:
                transaction.metadata = {}

            transaction.metadata["status_check_result"] = {
                k: v for k, v in status_result.items() if k not in ["status", "error"]
            }

            if "error" in status_result:
                transaction.metadata["status_check_error"] = status_result["error"]

            # Update the transaction
            updated_transaction = await payment_transaction_service.update(
                db, id=transaction_id, tenant_id=tenant_id, obj_in=update_data
            )

            return updated_transaction

        except Exception as e:
            logger.error(f"Error checking payment status: {str(e)}")
            raise BusinessLogicError(f"Error checking payment status: {str(e)}")

    async def check_refund_status(
        self, db: AsyncSession, *, refund_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> PaymentRefund:
        """
        Check the status of a refund with the payment processor.

        Args:
            db: The database session.
            refund_id: The ID of the refund to check.
            tenant_id: The ID of the tenant.

        Returns:
            The updated refund.

        Raises:
            BusinessLogicError: If the refund, transaction, or processor is not found,
                or if the status check fails.
        """
        # Get the refund
        result = await db.execute(
            select(PaymentRefund).where(
                PaymentRefund.id == refund_id, PaymentRefund.tenant_id == tenant_id
            )
        )
        refund = result.scalars().first()

        if not refund:
            raise BusinessLogicError(f"Refund with ID {refund_id} not found")

        # If the refund doesn't have an external ID, it hasn't been processed yet
        if not refund.external_id:
            return refund

        # Get the transaction
        transaction = await payment_transaction_service.get(
            db, id=refund.transaction_id, tenant_id=tenant_id
        )

        if not transaction:
            raise BusinessLogicError(f"Transaction with ID {refund.transaction_id} not found")

        # Get the processor
        processor_id = transaction.processor_id
        if not processor_id:
            raise BusinessLogicError("Transaction does not have a processor assigned")

        processor_db = await payment_processor_service.get(db, id=processor_id, tenant_id=tenant_id)

        if not processor_db:
            raise BusinessLogicError(f"Payment processor with ID {processor_id} not found")

        # Create processor instance
        try:
            processor_config = {
                "api_key": processor_db.api_key,
                "api_secret": processor_db.api_secret,
                "sandbox_mode": processor_db.sandbox_mode,
                "webhook_url": processor_db.webhook_url,
                "webhook_secret": processor_db.webhook_secret,
            }

            # Add additional config if available
            if processor_db.additional_config:
                processor_config.update(processor_db.additional_config)

            processor = PaymentProcessorFactory.create_processor(
                processor_db.processor_type, processor_config
            )

            if not processor:
                logger.error(
                    f"Payment processor {processor_db.processor_type} could not be initialized"
                )
                raise BusinessLogicError(
                    f"Payment processor {processor_db.processor_type} is not available"
                )

            # Check the refund status
            status_result = await processor.get_refund_status(refund.external_id)

            # Update the refund
            old_status = refund.status
            refund.status = status_result.get("status", refund.status)

            if refund.status != old_status:
                refund.processed_at = datetime.now()

            # Add any additional data to metadata
            if not refund.metadata:
                refund.metadata = {}

            refund.metadata["status_check_result"] = {
                k: v for k, v in status_result.items() if k not in ["status", "error"]
            }

            if "error" in status_result:
                refund.metadata["status_check_error"] = status_result["error"]

            # Save the refund
            db.add(refund)
            await db.commit()
            await db.refresh(refund)

            # Update the transaction status if needed
            if refund.status == PaymentStatus.COMPLETED and old_status != PaymentStatus.COMPLETED:
                # Get all completed refunds for this transaction
                result = await db.execute(
                    select(PaymentRefund).where(
                        PaymentRefund.transaction_id == transaction.id,
                        PaymentRefund.status == PaymentStatus.COMPLETED,
                    )
                )
                completed_refunds = result.scalars().all()

                # Calculate total refunded amount
                total_refunded = sum(r.amount for r in completed_refunds)

                # Update transaction status
                if total_refunded >= transaction.amount:
                    transaction.status = PaymentStatus.REFUNDED
                else:
                    transaction.status = PaymentStatus.PARTIALLY_REFUNDED

                db.add(transaction)
                await db.commit()

            return refund

        except Exception as e:
            logger.error(f"Error checking refund status: {str(e)}")
            raise BusinessLogicError(f"Error checking refund status: {str(e)}")


# Instance of the service to be used in endpoints
payment_processor_integration_service = PaymentProcessorIntegrationService()
