"""Services for custom domains module."""

# Import the service classes and instances
from .domain_integration_service import DomainIntegrationService  # noqa: F401

# Import the main service instance from services.py
# Using a lazy import to avoid potential circular imports
def get_custom_domain_service():
    """Get the custom domain service instance."""
    from .services import custom_domain_service
    return custom_domain_service

# For backward compatibility, also expose the service directly
try:
    from .services import custom_domain_service  # noqa: F401
except ImportError:
    # Handle potential circular import issues
    custom_domain_service = None

__all__ = ["DomainIntegrationService", "custom_domain_service", "get_custom_domain_service"]
