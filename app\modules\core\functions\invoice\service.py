"""
Invoice Service for B2B Invoice Management
==========================================

Serviço para gestão de faturas com upload de arquivos, controle de acesso
e integração com sistema de notificações.
"""

import os
import hashlib
import secrets
import mimetypes
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Optional, List, Dict, Any, Tuple
from pathlib import Path

from fastapi import UploadFile, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc
from sqlalchemy.orm import selectinload

from app.core.config import settings
from .models import TvendorInvoice, InvoiceStatus, InvoiceType
from .schemas import (
    InvoiceCreate, InvoiceUpdate, InvoicePaymentUpdate,
    InvoiceRead, InvoiceStats, InvoiceSummary
)


class InvoiceService:
    """Serviço para gestão de faturas B2B."""
    
    # Tipos de arquivo permitidos
    ALLOWED_FILE_TYPES = {
        'application/pdf': '.pdf',
        'application/msword': '.doc',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
        'application/vnd.ms-excel': '.xls',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',
        'image/jpeg': '.jpg',
        'image/png': '.png'
    }
    
    # Tamanho máximo do arquivo (10MB)
    MAX_FILE_SIZE = 10 * 1024 * 1024
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.upload_dir = Path(settings.UPLOAD_DIR) / "invoices"
        self.upload_dir.mkdir(parents=True, exist_ok=True)
    
    async def create_invoice(
        self, 
        invoice_data: InvoiceCreate, 
        vendor_id: str,
        tenant_id: str
    ) -> TvendorInvoice:
        """
        Cria nova fatura.
        
        Args:
            invoice_data: Dados da fatura
            vendor_id: ID do fornecedor
            tenant_id: ID do tenant
            
        Returns:
            TvendorInvoice: Fatura criada
        """
        # Verificar se número da fatura já existe para o vendor
        existing = await self.db.execute(
            select(TvendorInvoice).where(
                and_(
                    TvendorInvoice.vendor_id == vendor_id,
                    TvendorInvoice.invoice_number == invoice_data.invoice_number,
                    TvendorInvoice.deleted_at.is_(None)
                )
            )
        )
        
        if existing.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invoice number already exists for this vendor"
            )
        
        # Criar fatura
        invoice = TvendorInvoice(
            tenant_id=tenant_id,
            vendor_id=vendor_id,
            customer_id=invoice_data.customer_id,
            invoice_number=invoice_data.invoice_number,
            invoice_type=invoice_data.invoice_type,
            invoice_date=invoice_data.invoice_date,
            due_date=invoice_data.due_date,
            subtotal=invoice_data.subtotal,
            tax_amount=invoice_data.tax_amount,
            discount_amount=invoice_data.discount_amount,
            total_amount=invoice_data.total_amount,
            description=invoice_data.description,
            notes=invoice_data.notes,
            payment_terms=invoice_data.payment_terms,
            payment_method=invoice_data.payment_method,
            billing_address=invoice_data.billing_address,
            shipping_address=invoice_data.shipping_address,
            line_items=[item.dict() for item in invoice_data.line_items],
            notify_on_view=invoice_data.notify_on_view,
            notify_on_due=invoice_data.notify_on_due,
            max_downloads=invoice_data.max_downloads,
            # Campos temporários até upload do arquivo
            file_name="",
            file_original_name="",
            file_path="",
            file_size=0,
            file_type="",
            file_hash=""
        )
        
        self.db.add(invoice)
        await self.db.commit()
        await self.db.refresh(invoice)
        
        return invoice
    
    async def upload_invoice_file(
        self, 
        invoice_id: str, 
        file: UploadFile,
        vendor_id: str
    ) -> TvendorInvoice:
        """
        Faz upload do arquivo da fatura.
        
        Args:
            invoice_id: ID da fatura
            file: Arquivo a ser enviado
            vendor_id: ID do fornecedor
            
        Returns:
            TvendorInvoice: Fatura atualizada
        """
        # Buscar fatura
        result = await self.db.execute(
            select(TvendorInvoice).where(
                and_(
                    TvendorInvoice.id == invoice_id,
                    TvendorInvoice.vendor_id == vendor_id,
                    TvendorInvoice.deleted_at.is_(None)
                )
            )
        )
        
        invoice = result.scalar_one_or_none()
        if not invoice:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invoice not found"
            )
        
        # Validar arquivo
        await self._validate_file(file)
        
        # Ler conteúdo do arquivo
        file_content = await file.read()
        file_hash = hashlib.sha256(file_content).hexdigest()
        
        # Gerar nome único para o arquivo
        file_extension = self.ALLOWED_FILE_TYPES.get(file.content_type, '.bin')
        unique_filename = f"{invoice_id}_{secrets.token_hex(8)}{file_extension}"
        
        # Criar estrutura de diretórios
        year_month = invoice.invoice_date.strftime("%Y/%m")
        file_dir = self.upload_dir / invoice.tenant_id / vendor_id / year_month
        file_dir.mkdir(parents=True, exist_ok=True)
        
        # Salvar arquivo
        file_path = file_dir / unique_filename
        with open(file_path, "wb") as f:
            f.write(file_content)
        
        # Atualizar fatura com informações do arquivo
        invoice.file_name = unique_filename
        invoice.file_original_name = file.filename
        invoice.file_path = str(file_path)
        invoice.file_size = len(file_content)
        invoice.file_type = file.content_type
        invoice.file_hash = file_hash
        invoice.status = InvoiceStatus.PENDING
        invoice.updated_at = datetime.utcnow()
        
        await self.db.commit()
        await self.db.refresh(invoice)
        
        return invoice
    
    async def get_invoice(
        self, 
        invoice_id: str, 
        user_id: str,
        include_file_info: bool = True
    ) -> Optional[TvendorInvoice]:
        """
        Busca fatura por ID com verificação de acesso.
        
        Args:
            invoice_id: ID da fatura
            user_id: ID do usuário
            include_file_info: Incluir informações do arquivo
            
        Returns:
            TvendorInvoice: Fatura encontrada
        """
        query = select(TvendorInvoice).where(
            and_(
                TvendorInvoice.id == invoice_id,
                TvendorInvoice.deleted_at.is_(None)
            )
        )
        
        if include_file_info:
            query = query.options(
                selectinload(TvendorInvoice.vendor),
                selectinload(TvendorInvoice.customer)
            )
        
        result = await self.db.execute(query)
        invoice = result.scalar_one_or_none()
        
        if not invoice:
            return None
        
        # Verificar acesso (vendor ou customer)
        # TODO: Implementar verificação de acesso baseada em roles
        
        return invoice
    
    async def list_invoices(
        self,
        vendor_id: Optional[str] = None,
        customer_id: Optional[str] = None,
        status: Optional[InvoiceStatus] = None,
        overdue_only: bool = False,
        page: int = 1,
        limit: int = 20
    ) -> Tuple[List[TvendorInvoice], int]:
        """
        Lista faturas com filtros.
        
        Args:
            vendor_id: Filtrar por fornecedor
            customer_id: Filtrar por cliente
            status: Filtrar por status
            overdue_only: Apenas faturas vencidas
            page: Página
            limit: Limite por página
            
        Returns:
            Tuple[List[TvendorInvoice], int]: Lista de faturas e total
        """
        # Construir query base
        query = select(TvendorInvoice).where(
            TvendorInvoice.deleted_at.is_(None)
        )
        
        # Aplicar filtros
        if vendor_id:
            query = query.where(TvendorInvoice.vendor_id == vendor_id)
        
        if customer_id:
            query = query.where(TvendorInvoice.customer_id == customer_id)
        
        if status:
            query = query.where(TvendorInvoice.status == status)
        
        if overdue_only:
            query = query.where(
                and_(
                    TvendorInvoice.due_date < datetime.utcnow(),
                    TvendorInvoice.status.notin_([InvoiceStatus.PAID, InvoiceStatus.CANCELLED])
                )
            )
        
        # Contar total
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # Aplicar paginação e ordenação
        query = query.order_by(desc(TvendorInvoice.created_at))
        query = query.offset((page - 1) * limit).limit(limit)
        
        # Executar query
        result = await self.db.execute(query)
        invoices = result.scalars().all()
        
        return list(invoices), total
    
    async def update_invoice(
        self, 
        invoice_id: str, 
        update_data: InvoiceUpdate,
        vendor_id: str
    ) -> TvendorInvoice:
        """
        Atualiza fatura.
        
        Args:
            invoice_id: ID da fatura
            update_data: Dados para atualização
            vendor_id: ID do fornecedor
            
        Returns:
            TvendorInvoice: Fatura atualizada
        """
        # Buscar fatura
        result = await self.db.execute(
            select(TvendorInvoice).where(
                and_(
                    TvendorInvoice.id == invoice_id,
                    TvendorInvoice.vendor_id == vendor_id,
                    TvendorInvoice.deleted_at.is_(None)
                )
            )
        )
        
        invoice = result.scalar_one_or_none()
        if not invoice:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invoice not found"
            )
        
        # Aplicar atualizações
        update_dict = update_data.dict(exclude_unset=True)
        
        for field, value in update_dict.items():
            if hasattr(invoice, field):
                setattr(invoice, field, value)
        
        invoice.updated_at = datetime.utcnow()
        
        await self.db.commit()
        await self.db.refresh(invoice)
        
        return invoice
    
    async def mark_as_paid(
        self, 
        invoice_id: str, 
        payment_data: InvoicePaymentUpdate,
        user_id: str
    ) -> TvendorInvoice:
        """
        Marca fatura como paga.
        
        Args:
            invoice_id: ID da fatura
            payment_data: Dados do pagamento
            user_id: ID do usuário
            
        Returns:
            TvendorInvoice: Fatura atualizada
        """
        # Buscar fatura
        result = await self.db.execute(
            select(TvendorInvoice).where(
                and_(
                    TvendorInvoice.id == invoice_id,
                    TvendorInvoice.deleted_at.is_(None)
                )
            )
        )
        
        invoice = result.scalar_one_or_none()
        if not invoice:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invoice not found"
            )
        
        # Marcar como paga
        invoice.mark_as_paid(
            paid_amount=payment_data.paid_amount,
            payment_date=payment_data.payment_date
        )
        
        if payment_data.payment_method:
            invoice.payment_method = payment_data.payment_method
        
        await self.db.commit()
        await self.db.refresh(invoice)
        
        return invoice
    
    async def generate_download_token(
        self, 
        invoice_id: str, 
        user_id: str,
        expires_hours: int = 24
    ) -> str:
        """
        Gera token para download da fatura.
        
        Args:
            invoice_id: ID da fatura
            user_id: ID do usuário
            expires_hours: Horas até expiração
            
        Returns:
            str: Token de acesso
        """
        invoice = await self.get_invoice(invoice_id, user_id)
        
        if not invoice:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invoice not found"
            )
        
        if not invoice.can_download:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Download limit exceeded or access expired"
            )
        
        # Gerar token
        token = invoice.generate_access_token(expires_hours)
        
        await self.db.commit()
        
        return token
    
    async def get_invoice_stats(
        self,
        vendor_id: Optional[str] = None,
        customer_id: Optional[str] = None,
        tenant_id: Optional[str] = None
    ) -> InvoiceStats:
        """
        Calcula estatísticas de faturas.
        
        Args:
            vendor_id: Filtrar por fornecedor
            customer_id: Filtrar por cliente
            tenant_id: Filtrar por tenant
            
        Returns:
            InvoiceStats: Estatísticas calculadas
        """
        # Query base
        base_query = select(TvendorInvoice).where(
            TvendorInvoice.deleted_at.is_(None)
        )
        
        # Aplicar filtros
        if vendor_id:
            base_query = base_query.where(TvendorInvoice.vendor_id == vendor_id)
        
        if customer_id:
            base_query = base_query.where(TvendorInvoice.customer_id == customer_id)
        
        if tenant_id:
            base_query = base_query.where(TvendorInvoice.tenant_id == tenant_id)
        
        # Executar queries para estatísticas
        result = await self.db.execute(base_query)
        invoices = result.scalars().all()
        
        # Calcular estatísticas
        total_invoices = len(invoices)
        
        status_counts = {}
        type_counts = {}
        total_amount = Decimal('0.00')
        paid_amount = Decimal('0.00')
        outstanding_amount = Decimal('0.00')
        overdue_amount = Decimal('0.00')
        
        for invoice in invoices:
            # Contadores por status
            status_counts[invoice.status.value] = status_counts.get(invoice.status.value, 0) + 1
            
            # Contadores por tipo
            type_counts[invoice.invoice_type.value] = type_counts.get(invoice.invoice_type.value, 0) + 1
            
            # Valores financeiros
            total_amount += invoice.total_amount
            paid_amount += invoice.paid_amount
            outstanding_amount += invoice.outstanding_amount
            
            if invoice.is_overdue:
                overdue_amount += invoice.outstanding_amount
        
        # Calcular médias
        average_invoice_value = total_amount / total_invoices if total_invoices > 0 else Decimal('0.00')
        
        return InvoiceStats(
            total_invoices=total_invoices,
            pending_invoices=status_counts.get(InvoiceStatus.PENDING.value, 0),
            sent_invoices=status_counts.get(InvoiceStatus.SENT.value, 0),
            viewed_invoices=status_counts.get(InvoiceStatus.VIEWED.value, 0),
            paid_invoices=status_counts.get(InvoiceStatus.PAID.value, 0),
            overdue_invoices=status_counts.get(InvoiceStatus.OVERDUE.value, 0),
            total_amount=total_amount,
            paid_amount=paid_amount,
            outstanding_amount=outstanding_amount,
            overdue_amount=overdue_amount,
            average_invoice_value=average_invoice_value,
            status_stats=status_counts,
            type_stats=type_counts
        )
    
    async def _validate_file(self, file: UploadFile):
        """
        Valida arquivo de upload.
        
        Args:
            file: Arquivo a ser validado
        """
        # Verificar tipo de arquivo
        if file.content_type not in self.ALLOWED_FILE_TYPES:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File type {file.content_type} not allowed"
            )
        
        # Verificar tamanho
        file.file.seek(0, 2)  # Ir para o final do arquivo
        file_size = file.file.tell()
        file.file.seek(0)  # Voltar para o início
        
        if file_size > self.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File size {file_size} exceeds maximum {self.MAX_FILE_SIZE}"
            )
        
        # Verificar se arquivo não está vazio
        if file_size == 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File is empty"
            )

    async def delete_invoice(
        self,
        invoice_id: str,
        vendor_id: str
    ) -> bool:
        """
        Soft delete de fatura.

        Args:
            invoice_id: ID da fatura
            vendor_id: ID do fornecedor

        Returns:
            bool: Sucesso da operação
        """
        # Buscar fatura
        result = await self.db.execute(
            select(TvendorInvoice).where(
                and_(
                    TvendorInvoice.id == invoice_id,
                    TvendorInvoice.vendor_id == vendor_id,
                    TvendorInvoice.deleted_at.is_(None)
                )
            )
        )

        invoice = result.scalar_one_or_none()
        if not invoice:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invoice not found"
            )

        # Soft delete
        invoice.soft_delete()

        await self.db.commit()

        return True

    async def send_invoice(
        self,
        invoice_id: str,
        vendor_id: str
    ) -> TvendorInvoice:
        """
        Envia fatura para cliente.

        Args:
            invoice_id: ID da fatura
            vendor_id: ID do fornecedor

        Returns:
            TvendorInvoice: Fatura atualizada
        """
        # Buscar fatura
        result = await self.db.execute(
            select(TvendorInvoice).where(
                and_(
                    TvendorInvoice.id == invoice_id,
                    TvendorInvoice.vendor_id == vendor_id,
                    TvendorInvoice.deleted_at.is_(None)
                )
            )
        )

        invoice = result.scalar_one_or_none()
        if not invoice:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invoice not found"
            )

        # Verificar se tem arquivo
        if not invoice.file_path:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invoice file not uploaded"
            )

        # Atualizar status
        invoice.status = InvoiceStatus.SENT
        invoice.sent_date = datetime.utcnow()
        invoice.updated_at = datetime.utcnow()

        await self.db.commit()
        await self.db.refresh(invoice)

        # TODO: Enviar notificação para cliente

        return invoice
