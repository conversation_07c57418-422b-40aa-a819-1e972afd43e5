# General - Financial Categories

**Categoria:** General
**Módulo:** Financial Categories
**Total de Endpoints:** 7
**Gerado em:** 27/06/2025, 20:20:26

## 📋 Endpoints

- [GET /api/financial/categories/](#get-apifinancialcategories) - List financial categories
- [POST /api/financial/categories/](#post-apifinancialcategories) - Create financial category
- [POST /api/financial/categories/defaults](#post-apifinancialcategoriesdefaults) - Create default categories
- [GET /api/financial/categories/tree](#get-apifinancialcategoriestree) - Get category tree
- [DELETE /api/financial/categories/{category_id}](#delete-apifinancialcategoriescategory-id) - Delete financial category
- [GET /api/financial/categories/{category_id}](#get-apifinancialcategoriescategory-id) - Get financial category
- [PUT /api/financial/categories/{category_id}](#put-apifinancialcategoriescategory-id) - Update financial category

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### FinancialCategoryCreate

**Descrição:** Schema for creating a financial category.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Category name |
| `description` | unknown | ❌ | Category description |
| `category_type` | CategoryType | ✅ | Category type (income or expense) |
| `parent_id` | unknown | ❌ | Parent category ID for hierarchical structure |
| `display_order` | integer | ❌ | Display order for sorting |
| `is_active` | boolean | ❌ | Whether the category is active |
| `color` | unknown | ❌ | Hex color code for the category |
| `icon` | unknown | ❌ | Icon class or URL for the category |

### FinancialCategoryListResponse

**Descrição:** Schema for paginated category list response.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `categories` | Array[FinancialCategoryRead] | ❌ | List of categories |
| `total` | integer | ❌ | Total number of categories |
| `page` | integer | ❌ | Current page number |
| `per_page` | integer | ❌ | Items per page |
| `pages` | integer | ❌ | Total number of pages |

### FinancialCategoryRead

**Descrição:** Schema for reading a financial category.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Category name |
| `description` | unknown | ❌ | Category description |
| `category_type` | CategoryType | ✅ | Category type (income or expense) |
| `parent_id` | unknown | ❌ | Parent category ID for hierarchical structure |
| `display_order` | integer | ❌ | Display order for sorting |
| `is_active` | boolean | ❌ | Whether the category is active |
| `color` | unknown | ❌ | Hex color code for the category |
| `icon` | unknown | ❌ | Icon class or URL for the category |
| `id` | string | ✅ | Category ID |
| `tenant_id` | string | ✅ | Tenant ID |
| `is_default` | boolean | ❌ | Whether this is a default category |
| `parent_name` | unknown | ❌ | Parent category name |
| `children_count` | integer | ❌ | Number of child categories |
| `transactions_count` | integer | ❌ | Number of transactions in this category |
| `total_income` | unknown | ❌ | Total income in this category |
| `total_expense` | unknown | ❌ | Total expense in this category |

### FinancialCategoryUpdate

**Descrição:** Schema for updating a financial category.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | Category name |
| `description` | unknown | ❌ | Category description |
| `category_type` | unknown | ❌ | Category type (income or expense) |
| `parent_id` | unknown | ❌ | Parent category ID for hierarchical structure |
| `display_order` | unknown | ❌ | Display order for sorting |
| `is_active` | unknown | ❌ | Whether the category is active |
| `color` | unknown | ❌ | Hex color code for the category |
| `icon` | unknown | ❌ | Icon class or URL for the category |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/financial/categories/ {#get-apifinancialcategories}

**Resumo:** List financial categories
**Descrição:** Get paginated list of financial categories with optional filters.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `page` | integer | query | ❌ | Page number |
| `per_page` | integer | query | ❌ | Items per page |
| `order_by` | string | query | ❌ | Order by field |
| `order_direction` | string | query | ❌ | Order direction |
| `category_type` | string | query | ❌ | Filter by category type |
| `parent_id` | string | query | ❌ | Filter by parent category ID |
| `is_active` | string | query | ❌ | Filter by active status |
| `search` | string | query | ❌ | Search in name and description |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [FinancialCategoryListResponse](#financialcategorylistresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/categories/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/financial/categories/ {#post-apifinancialcategories}

**Resumo:** Create financial category
**Descrição:** Create a new financial category for the current tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [FinancialCategoryCreate](#financialcategorycreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [FinancialCategoryRead](#financialcategoryread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/financial/categories/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/financial/categories/defaults {#post-apifinancialcategoriesdefaults}

**Resumo:** Create default categories
**Descrição:** Create default financial categories for the current tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**201:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/financial/categories/defaults" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/financial/categories/tree {#get-apifinancialcategoriestree}

**Resumo:** Get category tree
**Descrição:** Get hierarchical tree of financial categories.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_type` | string | query | ❌ | Filter by category type |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/categories/tree" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### DELETE /api/financial/categories/{category_id} {#delete-apifinancialcategoriescategory-id}

**Resumo:** Delete financial category
**Descrição:** Delete a specific financial category.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/financial/categories/{category_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/financial/categories/{category_id} {#get-apifinancialcategoriescategory-id}

**Resumo:** Get financial category
**Descrição:** Get a specific financial category by ID.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [FinancialCategoryRead](#financialcategoryread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/categories/{category_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/financial/categories/{category_id} {#put-apifinancialcategoriescategory-id}

**Resumo:** Update financial category
**Descrição:** Update a specific financial category.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `category_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [FinancialCategoryUpdate](#financialcategoryupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [FinancialCategoryRead](#financialcategoryread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/financial/categories/{category_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
