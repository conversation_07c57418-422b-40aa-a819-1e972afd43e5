import uuid
from sqlalchemy import Column, String, ForeignKey, Index, Boolean, Integer, Numeric
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.orm import relationship

from app.db.base import Base
from app.modules.core.tenants.models.tenant import Tenant


class ProductVariantOption(Base):
    """
    Represents an individual option within a product variant group (e.g., "Small", "Medium", "Large" for Size).
    """

    __tablename__ = "eshop_product_variant_options"

    # Primary key
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Tenant isolation
    tenant_id = Column(PG_UUID(as_uuid=True), ForeignKey(Tenant.id), nullable=True, index=True)
    
    # Parent variant group
    variant_group_id = Column(PG_UUID(as_uuid=True), ForeignKey("eshop_product_variant_groups.id"), nullable=False, index=True)
    
    # Option properties
    name = Column(String(100), nullable=False)  # e.g., "Small", "Red", "Cotton"
    description = Column(String(255), nullable=True)  # Optional description
    value = Column(String(100), nullable=True)  # Machine-readable value (e.g., "S", "#FF0000", "cotton")
    
    # Pricing
    price_adjustment = Column(Numeric(10, 2), default=0.0, nullable=False)  # Price difference from base
    price_adjustment_type = Column(String(20), default="fixed", nullable=False)  # "fixed" or "percentage"
    
    # Stock and availability (for variants that affect stock)
    stock_quantity = Column(Integer, nullable=True)  # Override product stock for this variant
    sku_suffix = Column(String(50), nullable=True)  # Suffix to add to product SKU
    
    # Display and status
    display_order = Column(Integer, default=0, nullable=False)
    is_default = Column(Boolean, default=False, nullable=False)  # Default selection for this group
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Visual representation
    color_code = Column(String(7), nullable=True)  # Hex color code for color variants
    image_url = Column(String(500), nullable=True)  # Image for this variant option
    
    # Relationships
    tenant = relationship("Tenant")
    variant_group = relationship("ProductVariantGroup", back_populates="options")

    __table_args__ = (
        Index("ix_eshop_variant_options_tenant_id", "tenant_id"),
        Index("ix_eshop_variant_options_variant_group_id", "variant_group_id"),
        Index("ix_eshop_variant_options_tenant_group", "tenant_id", "variant_group_id"),
    )

    def __repr__(self):
        return f"<ProductVariantOption(id={self.id}, name='{self.name}', variant_group_id={self.variant_group_id})>"
