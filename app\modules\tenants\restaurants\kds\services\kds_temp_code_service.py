"""
Serviço para gerenciar códigos temporários de autenticação do KDS.
"""
import uuid
import secrets
import string
from datetime import datetime, timezone, timedelta
from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from app.modules.tenants.restaurants.kds.models.kds_temp_code import KDSTempCode
from app.modules.core.tenants.models.tenant import Tenant


class KDSTempCodeService:
    """Serviço para gerenciar códigos temporários de autenticação do KDS."""

    @staticmethod
    def _generate_6_digit_code() -> str:
        """Gera um código de 6 dígitos aleatório."""
        return ''.join(secrets.choice(string.digits) for _ in range(6))

    async def generate_temp_code(
        self, 
        db: AsyncSession, 
        tenant_id: uuid.UUID, 
        expires_hours: int = 24
    ) -> KDSTempCode:
        """
        Gera um novo código temporário para o tenant.
        
        Args:
            db: Sessão do banco de dados
            tenant_id: ID do tenant (restaurante)
            expires_hours: Horas até expiração (padrão: 24h)
            
        Returns:
            Objeto KDSTempCode criado
        """
        # Desativar códigos ativos anteriores para o tenant
        await self._deactivate_existing_codes(db, tenant_id)
        
        # Gerar novo código único
        code = await self._generate_unique_code(db)
        
        # Calcular data de expiração
        expires_at = datetime.now(timezone.utc) + timedelta(hours=expires_hours)
        
        # Criar novo código temporário
        temp_code = KDSTempCode(
            tenant_id=tenant_id,
            code=code,
            expires_at=expires_at,
            is_active=True
        )
        
        db.add(temp_code)
        await db.commit()
        await db.refresh(temp_code)
        
        return temp_code

    async def _deactivate_existing_codes(self, db: AsyncSession, tenant_id: uuid.UUID):
        """Desativa todos os códigos ativos existentes para o tenant."""
        statement = select(KDSTempCode).where(
            and_(
                KDSTempCode.tenant_id == tenant_id,
                KDSTempCode.is_active == True
            )
        )
        result = await db.execute(statement)
        existing_codes = result.scalars().all()
        
        for code in existing_codes:
            code.is_active = False
        
        await db.commit()

    async def _generate_unique_code(self, db: AsyncSession) -> str:
        """Gera um código de 6 dígitos único (não usado atualmente)."""
        max_attempts = 100
        
        for _ in range(max_attempts):
            code = self._generate_6_digit_code()
            
            # Verificar se o código já existe e está ativo
            statement = select(KDSTempCode).where(
                and_(
                    KDSTempCode.code == code,
                    KDSTempCode.is_active == True,
                    KDSTempCode.expires_at > datetime.now(timezone.utc)
                )
            )
            result = await db.execute(statement)
            existing_code = result.scalar_one_or_none()
            
            if not existing_code:
                return code
        
        # Se não conseguir gerar código único, usar timestamp
        timestamp = str(int(datetime.now().timestamp()))[-6:]
        return timestamp

    async def validate_temp_code(
        self, 
        db: AsyncSession, 
        restaurant_uuid: uuid.UUID, 
        temp_code: str
    ) -> Optional[KDSTempCode]:
        """
        Valida um código temporário para autenticação.
        
        Args:
            db: Sessão do banco de dados
            restaurant_uuid: UUID do restaurante
            temp_code: Código de 6 dígitos
            
        Returns:
            Objeto KDSTempCode se válido, None caso contrário
        """
        statement = select(KDSTempCode).where(
            and_(
                KDSTempCode.tenant_id == restaurant_uuid,
                KDSTempCode.code == temp_code,
                KDSTempCode.is_active == True,
                KDSTempCode.expires_at > datetime.now(timezone.utc),
                KDSTempCode.used_at.is_(None)
            )
        )
        
        result = await db.execute(statement)
        temp_code_obj = result.scalar_one_or_none()
        
        if temp_code_obj and temp_code_obj.is_valid:
            return temp_code_obj
        
        return None

    async def mark_code_as_used(self, db: AsyncSession, temp_code: KDSTempCode):
        """Marca um código temporário como usado."""
        temp_code.mark_as_used()
        await db.commit()

    async def get_active_code_for_tenant(
        self, 
        db: AsyncSession, 
        tenant_id: uuid.UUID
    ) -> Optional[KDSTempCode]:
        """
        Obtém o código ativo atual para um tenant.
        
        Args:
            db: Sessão do banco de dados
            tenant_id: ID do tenant
            
        Returns:
            Código ativo se existir, None caso contrário
        """
        statement = select(KDSTempCode).where(
            and_(
                KDSTempCode.tenant_id == tenant_id,
                KDSTempCode.is_active == True,
                KDSTempCode.expires_at > datetime.now(timezone.utc),
                KDSTempCode.used_at.is_(None)
            )
        ).order_by(KDSTempCode.created_at.desc())
        
        result = await db.execute(statement)
        return result.scalar_one_or_none()

    async def cleanup_expired_codes(self, db: AsyncSession):
        """Remove códigos expirados do banco de dados."""
        statement = select(KDSTempCode).where(
            KDSTempCode.expires_at <= datetime.now(timezone.utc)
        )
        result = await db.execute(statement)
        expired_codes = result.scalars().all()
        
        for code in expired_codes:
            await db.delete(code)
        
        await db.commit()
        return len(expired_codes)
