import uuid
from sqlalchemy import (
    <PERSON>um<PERSON>,
    Integer,
    String,
    Text,
    ForeignKey,
    Boolean,
    Numeric,
    Index,
    JSON,
)
from sqlalchemy.dialects.postgresql import UUID as PG_UUID  # Renamed to avoid clash  # noqa: E402
from sqlalchemy.orm import relationship, Mapped, mapped_column

from typing import TYPE_CHECKING  # Import TYPE_CHECKING  # noqa: E402

from app.db.base import Base, menu_item_inventory_association, menu_item_variant_groups, menu_item_modifier_groups, menu_item_optional_groups, menu_item_allergens  # noqa: E402
from app.modules.core.tenants.models.tenant import Tenant
from app.modules.core.functions.allergens.models.allergen import Allergen

# Use TYPE_CHECKING to avoid circular import for type hints
if TYPE_CHECKING:
    from app.modules.tenants.restaurants.menu.models.menu_category import MenuCategory
    from app.modules.core.functions.customizations.models.variant_group import VariantGroup
    from app.modules.core.functions.customizations.models.modifier_group import ModifierGroup
    from app.modules.core.functions.customizations.models.optional_group import OptionalGroup
    from app.modules.shared.inventory.models.inventory_item import InventoryItem

# Association Table moved to app/db/base.py


class MenuItem(Base):
    """
    Represents an item available in the digital menu (e.g., Pizza, Burger).
    """

    __tablename__ = "menu_items"

    # Changed to UUID
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    # Changed to UUID
    tenant_id = Column(PG_UUID(as_uuid=True), ForeignKey(Tenant.id), nullable=False, index=True)
    # Changed to UUID to match MenuCategory.id
    category_id = Column(
        PG_UUID(as_uuid=True), ForeignKey("menu_categories.id"), nullable=True, index=True
    )
    name = Column(String(150), nullable=False, index=True)
    description = Column(Text, nullable=True)
    base_price = Column(Numeric(10, 2), nullable=False)  # Use Numeric for currency
    image_url = Column(String(255), nullable=True)
    is_available = Column(Boolean, default=True, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)  # Added for consistency
    is_combo = Column(Boolean, default=False, nullable=False)
    discount_percentage = Column(Numeric(5, 2), nullable=True)  # e.g., 10.00 for 10%
    display_order = Column(Integer, default=0, nullable=False)

    # Relationships
    tenant: Mapped["Tenant"] = relationship("Tenant")
    category: Mapped["MenuCategory"] = relationship("MenuCategory", back_populates="menu_items")

    # Many-to-many relationships with groups
    variant_groups: Mapped[list["VariantGroup"]] = relationship(
        "VariantGroup",
        secondary=menu_item_variant_groups,
    )
    modifier_groups: Mapped[list["ModifierGroup"]] = relationship(
        "ModifierGroup",
        secondary=menu_item_modifier_groups,
    )
    optional_groups: Mapped[list["OptionalGroup"]] = relationship(
        "OptionalGroup",
        secondary=menu_item_optional_groups,
    )

    # Many-to-Many relationship with InventoryItem
    inventory_items: Mapped[list["InventoryItem"]] = relationship(
        "InventoryItem",
        secondary=menu_item_inventory_association,
        viewonly=True,
    )

    # Many-to-Many relationship with Allergens (via association table)
    allergens = relationship(
        "Allergen",
        secondary=menu_item_allergens,
        back_populates="menu_items",
        viewonly=True
    )
    
    # One-to-Many relationship with MenuItemImage
    images = relationship(
        "MenuItemImage",
        back_populates="menu_item",
        cascade="all, delete-orphan",
        lazy="select"
    )

    __table_args__ = (
        Index("ix_menu_items_tenant_id_category_id", "tenant_id", "category_id"),
        Index("ix_menu_items_tenant_id_name", "tenant_id", "name"),
        Index("ix_menu_items_tenant_id_display_order", "tenant_id", "display_order"),
    )

    def __repr__(self):
        return f"<MenuItem(id={self.id}, name='{self.name}', tenant_id={self.tenant_id})>"
