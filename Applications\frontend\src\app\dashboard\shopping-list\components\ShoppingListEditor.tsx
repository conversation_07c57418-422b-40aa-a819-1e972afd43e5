'use client';

import { useState, useMemo, useEffect } from 'react';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { ShoppingListEditorLayout } from './ShoppingListEditorLayout';
import { AddItemModal } from './AddItemModal';
import { CategoryModal } from './CategoryModal';
import { SuggestionsModal } from './SuggestionsModal';
import { useShoppingList } from '@/hooks/useShoppingList';
import { useShoppingListCategories } from '@/hooks/useShoppingListCategories';
import { ShoppingListItem, ShoppingListCategory, Priority } from '@/types/shopping-list';

export function ShoppingListEditor() {
  // Shopping List hook
  const {
    lists,
    currentList,
    items: shoppingItems,
    suggestions,
    isLoading,
    error,
    fetchLists,
    getListWithItems,
    createList,
    updateList,
    deleteList,
    createItem,
    updateItem,
    deleteItem,
    togglePurchased,
    getPendingItems,
    getTotalEstimatedCost,
    fetchLowStockSuggestions,
    createItemFromInventory
  } = useShoppingList();

  // Categories hook
  const {
    categories,
    isLoading: categoriesLoading,
    error: categoriesError,
    createCategory,
    updateCategory,
    deleteCategory,
    getActiveCategories
  } = useShoppingListCategories();

  const [selectedCategory, setSelectedCategory] = useState<ShoppingListCategory | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
  const [showPreview, setShowPreview] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [activeId, setActiveId] = useState(null);
  const [dragType, setDragType] = useState<'category' | 'item' | null>(null);
  const [savingItem, setSavingItem] = useState(false);

  // Modal states
  const [showItemModal, setShowItemModal] = useState(false);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [showSuggestionsModal, setShowSuggestionsModal] = useState(false);
  const [editingItem, setEditingItem] = useState<ShoppingListItem | null>(null);
  const [editingCategory, setEditingCategory] = useState<ShoppingListCategory | null>(null);

  // Load shopping lists and default list on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        await fetchLists();
        // For now, we'll work with a default list or create one if none exists
        // This will be enhanced when we implement list selection UI
      } catch (err) {
        console.error('Failed to load shopping lists:', err);
      }
    };

    loadData();
  }, [fetchLists]);

  // Load items for the first list when lists are available
  useEffect(() => {
    if (lists.length > 0 && !currentList) {
      getListWithItems(lists[0].id);
    }
  }, [lists, currentList, getListWithItems]);

  // Computed values
  const displayCategories = useMemo(() => {
    return getActiveCategories();
  }, [getActiveCategories]);

  const filteredItems = useMemo(() => {
    let filtered = [...shoppingItems];

    if (selectedCategory) {
      // Note: Real API data doesn't have category_id, using name matching for now
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(selectedCategory.name.toLowerCase())
      );
    }

    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.notes && item.notes.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    if (priorityFilter !== 'all') {
      filtered = filtered.filter(item => item.priority === priorityFilter);
    }

    if (statusFilter !== 'all') {
      if (statusFilter === 'purchased') {
        filtered = filtered.filter(item => item.purchased);
      } else if (statusFilter === 'pending') {
        filtered = filtered.filter(item => !item.purchased);
      }
      // Note: low_stock filter removed as it's not applicable to shopping list items
    }

    return filtered.sort((a, b) => {
      // Sort by purchased status first (pending items first)
      if (a.purchased !== b.purchased) {
        return a.purchased ? 1 : -1;
      }
      // Then by priority
      const priorityOrder = { [Priority.HIGH]: 0, [Priority.MEDIUM]: 1, [Priority.LOW]: 2 };
      return priorityOrder[a.priority] - priorityOrder[b.priority];
    });
  }, [shoppingItems, selectedCategory, searchTerm, priorityFilter, statusFilter]);

  // Handlers
  const handleCreateCategory = () => {
    setEditingCategory(null);
    setShowCategoryModal(true);
  };

  const handleEditCategory = (category: ShoppingListCategory) => {
    setEditingCategory(category);
    setShowCategoryModal(true);
  };

  const handleDeleteCategory = async (categoryId: string) => {
    if (confirm('Are you sure you want to delete this category?')) {
      await deleteCategory(categoryId);
      // Clear selection if deleted category was selected
      if (selectedCategory?.id === categoryId) {
        setSelectedCategory(null);
      }
    }
  };

  const handleCreateItem = (categoryId: any) => {
    setEditingItem(null);
    setShowItemModal(true);
  };

  const handleEditItem = (item: any) => {
    setEditingItem(item);
    setShowItemModal(true);
  };

  const handleDeleteItem = async (itemId: string) => {
    if (confirm('Are you sure you want to delete this item?')) {
      try {
        if (!currentList?.id) return;
        await deleteItem(currentList.id, itemId);
      } catch (err) {
        console.error('Failed to delete item:', err);
      }
    }
  };

  const handleTogglePurchased = async (itemId: string) => {
    try {
      const item = shoppingItems.find(item => item.id === itemId);
      if (item) {
        await togglePurchased(itemId, !item.purchased);
      }
    } catch (err) {
      console.error('Failed to toggle purchased status:', err);
    }
  };

  const handleCategorySelect = (categoryId: string) => {
    const category = categories.find(cat => cat.id === categoryId);
    setSelectedCategory(category || null);
  };

  const handleShowSuggestions = async () => {
    try {
      await fetchLowStockSuggestions(10); // threshold of 10
      setShowSuggestionsModal(true);
    } catch (err) {
      console.error('Failed to fetch suggestions:', err);
    }
  };

  const handleAddSuggestionToList = async (inventoryItemId: string, quantity: number) => {
    try {
      if (currentList) {
        await createItemFromInventory(currentList.id, inventoryItemId, quantity);
      } else if (lists.length > 0) {
        await createItemFromInventory(lists[0].id, inventoryItemId, quantity);
      } else {
        // Create a default list first
        const newList = await createList({
          name: 'Shopping List',
          description: 'Default shopping list'
        });
        await createItemFromInventory(newList.id, inventoryItemId, quantity);
      }
    } catch (err) {
      console.error('Failed to add suggestion to list:', err);
      throw err;
    }
  };

  const handleDragStart = (event: any) => {
    setActiveId(event.active.id);
    const isDraggingCategory = categories.some(cat => cat.id === event.active.id);
    setDragType(isDraggingCategory ? 'category' : 'item');
  };

  const handleDragEnd = (event: any) => {
    setActiveId(null);
    setDragType(null);
    // Handle reordering logic here
  };

  return (
    <DndContext
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="h-full flex flex-col">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
            <p className="font-medium">Error loading shopping list</p>
            <p className="text-sm">{error}</p>
            <button
              onClick={() => fetchLists()}
              className="text-sm underline hover:no-underline mt-1"
            >
              Try again
            </button>
          </div>
        )}

        <ShoppingListEditorLayout
          displayCategories={displayCategories}
          selectedCategory={selectedCategory}
          filteredItems={filteredItems}
          shoppingItems={shoppingItems}
          categories={categories}
          viewMode={viewMode}
          showPreview={showPreview}
          searchTerm={searchTerm}
          priorityFilter={priorityFilter}
          statusFilter={statusFilter}
          selectedCategoryForView={selectedCategory?.id || ''}
          activeId={activeId}
          dragType={dragType}
          savingItem={savingItem}
          isLoading={isLoading}
          handleCreateCategory={handleCreateCategory}
          handleCreateItem={handleCreateItem}
          handleEditCategory={handleEditCategory}
          handleDeleteCategory={handleDeleteCategory}
          handleEditItem={handleEditItem}
          handleDeleteItem={handleDeleteItem}
          handleTogglePurchased={handleTogglePurchased}
          handleCategorySelect={handleCategorySelect}
          handleShowSuggestions={handleShowSuggestions}
          setViewMode={setViewMode}
          setShowPreview={setShowPreview}
          setSearchTerm={setSearchTerm}
          setPriorityFilter={setPriorityFilter}
          setStatusFilter={setStatusFilter}
        />
      </div>

      {/* Modals */}
      {showItemModal && (
        <AddItemModal
          item={editingItem}
          categories={categories}
          onClose={() => setShowItemModal(false)}
          onSave={async (itemData: any) => {
            try {
              setSavingItem(true);
              if (editingItem) {
                // Update existing item
                if (!currentList?.id) return;
                await updateItem(currentList.id, editingItem.id, itemData);
              } else {
                // Create new item - need a list to add to
                if (currentList) {
                  await createItem(currentList.id, itemData);
                } else if (lists.length > 0) {
                  await createItem(lists[0].id, itemData);
                } else {
                  // Create a default list first
                  const newList = await createList({
                    name: 'Shopping List',
                    description: 'Default shopping list'
                  });
                  await createItem(newList.id, itemData);
                }
              }
              setShowItemModal(false);
            } catch (err) {
              console.error('Failed to save item:', err);
            } finally {
              setSavingItem(false);
            }
          }}
        />
      )}

      {showCategoryModal && (
        <CategoryModal
          category={editingCategory || undefined}
          onClose={() => setShowCategoryModal(false)}
          onSave={async (categoryData: any) => {
            try {
              if (editingCategory) {
                await updateCategory(editingCategory.id, categoryData);
              } else {
                await createCategory(categoryData);
              }
              setShowCategoryModal(false);
            } catch (err) {
              console.error('Failed to save category:', err);
            }
          }}
        />
      )}

      {showSuggestionsModal && (
        <SuggestionsModal
          isOpen={showSuggestionsModal}
          onClose={() => setShowSuggestionsModal(false)}
          suggestions={suggestions}
          onAddToList={handleAddSuggestionToList}
          isLoading={isLoading}
        />
      )}
    </DndContext>
  );
}
