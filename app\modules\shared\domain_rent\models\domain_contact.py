"""Domain Contact model for the Domain Rent module."""

import uuid  # noqa: E402
import enum
from typing import TYPE_CHECKING

from sqlalchemy import (  # noqa: E402
    Column,
    String,
    ForeignKey,
    Enum,
    Index,
)
from sqlalchemy.dialects.postgresql import UUID  # noqa: E402
from sqlalchemy.orm import relationship

from app.db.base import Base  # noqa: E402


class ContactType(str, enum.Enum):
    """Type of domain contact."""

    REGISTRANT = "registrant"
    ADMIN = "admin"
    TECHNICAL = "technical"
    BILLING = "billing"


class DomainContact(Base):
    """Domain Contact model.

    Stores contact information for domain registrations.
    """

    __tablename__ = "domain_contacts"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    domain_registration_id = Column(
        UUID(as_uuid=True),
        ForeignKey("domain_registrations.id"),
        nullable=False,
        index=True,
    )

    contact_type = Column(Enum(ContactType), nullable=False, index=True)

    # Contact information
    first_name = Column(String, nullable=False)
    last_name = Column(String, nullable=False)
    organization = Column(String, nullable=True)
    email = Column(String, nullable=False)
    phone = Column(String, nullable=False)

    # Address information
    address_line_1 = Column(String, nullable=False)
    address_line_2 = Column(String, nullable=True)
    city = Column(String, nullable=False)
    state_province = Column(String, nullable=False)
    postal_code = Column(String, nullable=False)
    country = Column(String, nullable=False)

    # Relationship to DomainRegistration
    domain_registration = relationship("DomainRegistration", back_populates="contacts")

    # Indexes
    __table_args__ = (
        Index(
            "ix_domain_contacts_domain_registration_id_contact_type",
            domain_registration_id,
            contact_type,
            unique=True,
        ),
    )

    def __repr__(self):
        return (
            f"<DomainContact(id={self.id}, "
            f"domain_registration_id='{self.domain_registration_id}', "
            f"contact_type='{self.contact_type}')>"
        )
