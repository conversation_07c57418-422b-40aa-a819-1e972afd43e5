# Shops - Catalog

**Categoria:** Shops
**Módulo:** Catalog
**Total de Endpoints:** 5
**Gerado em:** 08/06/2025, 17:26:01

## 📋 Endpoints

- [GET /api/modules/shops/catalog/catalog/products/](#get-apimodulesshopscatalogcatalogproducts) - List products
- [POST /api/modules/shops/catalog/catalog/products/](#post-apimodulesshopscatalogcatalogproducts) - Create a new product
- [DELETE /api/modules/shops/catalog/catalog/products/{product_id}](#delete-apimodulesshopscatalogcatalogproductsproduct-id) - Delete a product
- [GET /api/modules/shops/catalog/catalog/products/{product_id}](#get-apimodulesshopscatalogcatalogproductsproduct-id) - Get product details
- [PUT /api/modules/shops/catalog/catalog/products/{product_id}](#put-apimodulesshopscatalogcatalogproductsproduct-id) - Update a product

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### ProductCreate

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Name of the product |
| `description` | unknown | ❌ | Detailed description of the product |
| `price` | unknown | ✅ | Price of the product |
| `sku` | unknown | ❌ | Stock Keeping Unit |
| `image_url` | unknown | ❌ | URL of the product image |
| `is_active` | boolean | ❌ | Is the product currently available for sale? |
| `is_featured` | boolean | ❌ | Is the product featured in the online store? |
| `category_id` | unknown | ❌ | ID of the product category |
| `inventory_item_id` | unknown | ❌ | ID of the linked inventory item for stock tracking |

### ProductRead

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | Name of the product |
| `description` | unknown | ❌ | Detailed description of the product |
| `price` | string | ✅ | Price of the product |
| `sku` | unknown | ❌ | Stock Keeping Unit |
| `image_url` | unknown | ❌ | URL of the product image |
| `is_active` | boolean | ❌ | Is the product currently available for sale? |
| `is_featured` | boolean | ❌ | Is the product featured in the online store? |
| `category_id` | unknown | ❌ | ID of the product category |
| `inventory_item_id` | unknown | ❌ | ID of the linked inventory item for stock tracking |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `category` | unknown | ❌ | - |

### ProductUpdate

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `price` | unknown | ❌ | - |
| `sku` | unknown | ❌ | - |
| `image_url` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `is_featured` | unknown | ❌ | - |
| `category_id` | unknown | ❌ | - |
| `inventory_item_id` | unknown | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/modules/shops/catalog/catalog/products/ {#get-apimodulesshopscatalogcatalogproducts}

**Resumo:** List products
**Descrição:** Retrieves a list of products for the current tenant, with optional filters.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | Number of items to skip |
| `limit` | integer | query | ❌ | Maximum number of items to return |
| `name` | string | query | ❌ | Filter products by name (case-insensitive, partial match) |
| `sku` | string | query | ❌ | Filter products by SKU (exact match) |
| `category_id` | string | query | ❌ | Filter by category ID |
| `is_active` | string | query | ❌ | Filter by active status |
| `is_featured` | string | query | ❌ | Filter by featured status |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/shops/catalog/catalog/products/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/shops/catalog/catalog/products/ {#post-apimodulesshopscatalogcatalogproducts}

**Resumo:** Create a new product
**Descrição:** Creates a new product associated with the current tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ProductCreate](#productcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductRead](#productread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/shops/catalog/catalog/products/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/shops/catalog/catalog/products/{product_id} {#delete-apimodulesshopscatalogcatalogproductsproduct-id}

**Resumo:** Delete a product
**Descrição:** Deletes a specific product belonging to the current tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `product_id` | string | path | ✅ | ID of the product to delete |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductRead](#productread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/shops/catalog/catalog/products/{product_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/shops/catalog/catalog/products/{product_id} {#get-apimodulesshopscatalogcatalogproductsproduct-id}

**Resumo:** Get product details
**Descrição:** Retrieves details for a specific product belonging to the current tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `product_id` | string | path | ✅ | ID of the product to retrieve |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductRead](#productread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/shops/catalog/catalog/products/{product_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/shops/catalog/catalog/products/{product_id} {#put-apimodulesshopscatalogcatalogproductsproduct-id}

**Resumo:** Update a product
**Descrição:** Updates an existing product belonging to the current tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `product_id` | string | path | ✅ | ID of the product to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ProductUpdate](#productupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductRead](#productread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/shops/catalog/catalog/products/{product_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
