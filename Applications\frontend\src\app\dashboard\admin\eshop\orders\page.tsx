'use client';

import { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import {
  ShoppingBag,
  Eye,
  Check,
  X,
  Truck,
  Clock,
  DollarSign,
  User,
  Building2,
  Search,
  Filter,
  Play
} from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { toast } from 'sonner';
import { getOrders, getOrderStats, updateOrderStatus, confirmOrder, cancelOrder } from '@/lib/api/ordersApi';
import { Order, OrderStats, OrderStatus, OrderFilters } from '@/types/order';
import { AuthDebug } from '@/components/debug/AuthDebug';

// Types are now imported from @/types/order

// Mock data removed - now using real API data

const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending': return 'bg-yellow-100 text-yellow-800';
    case 'confirmed': return 'bg-blue-100 text-blue-800';
    case 'processing': return 'bg-purple-100 text-purple-800';
    case 'shipped': return 'bg-indigo-100 text-indigo-800';
    case 'delivered': return 'bg-green-100 text-green-800';
    case 'cancelled': return 'bg-red-100 text-red-800';
    case 'refunded': return 'bg-gray-100 text-gray-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getMarketTypeColor = (type: string) => {
  switch (type) {
    case 'b2b': return 'bg-blue-100 text-blue-800';
    case 'public': return 'bg-green-100 text-green-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getPaymentStatusColor = (status: string) => {
  switch (status) {
    case 'paid': return 'bg-green-100 text-green-800';
    case 'pending': return 'bg-yellow-100 text-yellow-800';
    case 'failed': return 'bg-red-100 text-red-800';
    case 'refunded': return 'bg-gray-100 text-gray-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

export default function EShopOrdersPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [stats, setStats] = useState<OrderStats | null>(null);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [filterStatus, setFilterStatus] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalOrders, setTotalOrders] = useState(0);
  const perPage = 10;

  // Load orders and stats
  const loadOrders = useCallback(async () => {
    setIsLoading(true);
    try {
      const filters: OrderFilters = {};
      if (filterStatus !== 'all') {
        filters.status = filterStatus as OrderStatus;
      }
      if (searchTerm) {
        filters.order_number = searchTerm;
        filters.customer_name = searchTerm;
        filters.customer_email = searchTerm;
      }

      const response = await getOrders(currentPage, perPage, filters);
      setOrders(response.orders);
      setTotalPages(response.total_pages);
      setTotalOrders(response.total);
    } catch (error) {
      console.error('Error loading orders:', error);
      toast.error('Erro ao carregar pedidos');
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, perPage, filterStatus, searchTerm]);

  const loadStats = useCallback(async () => {
    try {
      const statsData = await getOrderStats();
      setStats(statsData);
    } catch (error) {
      console.error('Error loading stats:', error);
      toast.error('Erro ao carregar estatísticas');
    }
  }, []);

  useEffect(() => {
    loadOrders();
  }, [currentPage, filterStatus, searchTerm, loadOrders]);

  useEffect(() => {
    loadStats();
  }, [loadStats]);

  // Filtering is now handled by the backend

  const handleUpdateOrderStatus = async (orderId: string, newStatus: OrderStatus) => {
    setIsLoading(true);
    try {
      await updateOrderStatus(orderId, { status: newStatus });
      toast.success(`Status do pedido atualizado para ${newStatus}`);
      loadOrders(); // Reload orders to get updated data
    } catch (error) {
      console.error('Error updating order status:', error);
      toast.error('Erro ao atualizar status do pedido');
    } finally {
      setIsLoading(false);
    }
  };

  const handleConfirmOrder = async (orderId: string) => {
    setIsLoading(true);
    try {
      await confirmOrder(orderId);
      toast.success('Pedido confirmado com sucesso');
      loadOrders();
    } catch (error) {
      console.error('Error confirming order:', error);
      toast.error('Erro ao confirmar pedido');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelOrder = async (orderId: string) => {
    setIsLoading(true);
    try {
      await cancelOrder(orderId);
      toast.success('Pedido cancelado com sucesso');
      loadOrders();
    } catch (error) {
      console.error('Error cancelling order:', error);
      toast.error('Erro ao cancelar pedido');
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewOrder = (order: Order) => {
    setSelectedOrder(order);
    setIsDetailModalOpen(true);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="border-b border-gray-200 pb-4">
        <h1 className="text-2xl font-bold text-gray-900">Gerenciamento de Pedidos</h1>
        <p className="text-gray-600 mt-1">
          Gestão unificada para pedidos B2B (TShop) e público (Cuponic)
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white p-6 rounded-lg shadow border border-gray-200"
        >
          <div className="flex items-center">
            <ShoppingBag className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total de Pedidos</p>
              <p className="text-2xl font-bold text-gray-900">{stats?.total_orders?.toLocaleString() || '0'}</p>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white p-6 rounded-lg shadow border border-gray-200"
        >
          <div className="flex items-center">
            <Clock className="h-8 w-8 text-yellow-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Pendentes</p>
              <p className="text-2xl font-bold text-gray-900">{stats?.pending_orders || '0'}</p>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white p-6 rounded-lg shadow border border-gray-200"
        >
          <div className="flex items-center">
            <DollarSign className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Receita Total</p>
              <p className="text-2xl font-bold text-gray-900">R$ {stats?.total_revenue?.toLocaleString('pt-BR', { minimumFractionDigits: 2 }) || '0,00'}</p>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white p-6 rounded-lg shadow border border-gray-200"
        >
          <div className="flex items-center">
            <Truck className="h-8 w-8 text-purple-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Valor Médio</p>
              <p className="text-2xl font-bold text-gray-900">R$ {stats?.average_order_value?.toLocaleString('pt-BR', { minimumFractionDigits: 2 }) || '0,00'}</p>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white p-6 rounded-lg shadow border border-gray-200">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Buscar por número do pedido, cliente ou vendedor..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <div className="flex gap-4">
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filtrar por status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os Status</SelectItem>
                <SelectItem value="pending">Pendente</SelectItem>
                <SelectItem value="confirmed">Confirmado</SelectItem>
                <SelectItem value="processing">Processando</SelectItem>
                <SelectItem value="shipped">Enviado</SelectItem>
                <SelectItem value="delivered">Entregue</SelectItem>
                <SelectItem value="cancelled">Cancelado</SelectItem>
                <SelectItem value="refunded">Reembolsado</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Orders Table */}
      <div className="bg-white rounded-lg shadow border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Pedidos</h2>
          <p className="text-sm text-gray-600 mt-1">
            {totalOrders} pedido{totalOrders !== 1 ? 's' : ''} encontrado{totalOrders !== 1 ? 's' : ''}
          </p>
        </div>

        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Pedido</TableHead>
                <TableHead>Cliente</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Valor</TableHead>
                <TableHead>Data</TableHead>
                <TableHead>Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                      <span className="ml-2">Carregando pedidos...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : orders.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    <p className="text-gray-500">Nenhum pedido encontrado</p>
                  </TableCell>
                </TableRow>
              ) : (
                orders.map((order) => (
                  <TableRow key={order.id} className="hover:bg-gray-50">
                    <TableCell>
                      <div>
                        <div className="text-sm font-medium text-gray-900">{order.order_number}</div>
                        <div className="text-sm text-gray-500">{order.items_count} itens</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="text-sm font-medium text-gray-900">{order.customer_name}</div>
                        <div className="text-sm text-gray-500">{order.customer_email}</div>
                        {order.customer_phone && (
                          <div className="text-sm text-gray-500">{order.customer_phone}</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(order.status)}>
                         {order.status === 'pending' && 'Pendente'}
                         {order.status === 'confirmed' && 'Confirmado'}
                         {order.status === 'preparing' && 'Preparando'}
                         {order.status === 'ready' && 'Pronto'}
                         {order.status === 'delivered' && 'Entregue'}
                         {order.status === 'cancelled' && 'Cancelado'}
                         {order.status === 'refunded' && 'Reembolsado'}
                       </Badge>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm font-medium text-gray-900">R$ {order.total_amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</span>
                    </TableCell>
                    <TableCell>
                       <div className="text-sm text-gray-900">
                         {new Date(order.created_at).toLocaleDateString('pt-BR')}
                       </div>
                     </TableCell>
                    <TableCell>
                      <div className="flex space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewOrder(order)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        {order.status === 'pending' && (
                          <>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleConfirmOrder(order.id)}
                              className="text-green-600 hover:text-green-700"
                              disabled={isLoading}
                            >
                              <Check className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleCancelOrder(order.id)}
                              className="text-red-600 hover:text-red-700"
                              disabled={isLoading}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
        
        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between px-6 py-4 border-t">
            <div className="text-sm text-gray-500">
              Mostrando {((currentPage - 1) * perPage) + 1} a {Math.min(currentPage * perPage, totalOrders)} de {totalOrders} pedidos
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1 || isLoading}
              >
                Anterior
              </Button>
              <span className="flex items-center px-3 py-1 text-sm">
                Página {currentPage} de {totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages || isLoading}
              >
                Próxima
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Order Details Modal */}
      <Dialog open={!!selectedOrder} onOpenChange={() => setSelectedOrder(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Detalhes do Pedido</DialogTitle>
          </DialogHeader>
          
          {selectedOrder && (
            <div className="space-y-6">
              {/* Order Header */}
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold mb-3">Informações do Pedido</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Número do Pedido:</span>
                      <span className="font-medium">{selectedOrder.order_number}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Status:</span>
                      <Badge className={getStatusColor(selectedOrder.status)}>
                         {selectedOrder.status === 'pending' && 'Pendente'}
                         {selectedOrder.status === 'confirmed' && 'Confirmado'}
                         {selectedOrder.status === 'preparing' && 'Preparando'}
                         {selectedOrder.status === 'ready' && 'Pronto'}
                         {selectedOrder.status === 'delivered' && 'Entregue'}
                         {selectedOrder.status === 'cancelled' && 'Cancelado'}
                         {selectedOrder.status === 'refunded' && 'Reembolsado'}
                       </Badge>
                    </div>
                    <div className="flex justify-between">
                       <span className="text-gray-600">Criado em:</span>
                       <span>{new Date(selectedOrder.created_at).toLocaleString('pt-BR')}</span>
                     </div>
                     {selectedOrder.updated_at && (
                       <div className="flex justify-between">
                         <span className="text-gray-600">Atualizado em:</span>
                         <span>{new Date(selectedOrder.updated_at).toLocaleString('pt-BR')}</span>
                       </div>
                     )}
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold mb-3">Informações do Cliente</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Nome:</span>
                      <span className="font-medium">{selectedOrder.customer_name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Email:</span>
                      <span>{selectedOrder.customer_email}</span>
                    </div>
                    {selectedOrder.customer_phone && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Telefone:</span>
                        <span>{selectedOrder.customer_phone}</span>
                      </div>
                    )}
                    {selectedOrder.delivery_address && (
                       <div className="flex justify-between">
                         <span className="text-gray-600">Endereço:</span>
                         <span className="text-right max-w-48">{selectedOrder.delivery_address}</span>
                       </div>
                     )}
                  </div>
                </div>
              </div>
              
              {/* Order Totals */}
              <div className="border-t pt-4">
                <div className="flex justify-end">
                  <div className="w-64 space-y-2">
                    <div className="flex justify-between font-semibold text-lg border-t pt-2">
                      <span>Total:</span>
                      <span>R$ {selectedOrder.total_amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</span>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Action Buttons */}
              <div className="flex justify-end space-x-3 pt-4 border-t">
                {selectedOrder.status === 'pending' && (
                  <>
                    <Button
                      onClick={() => {
                        handleConfirmOrder(selectedOrder.id);
                        setSelectedOrder(null);
                      }}
                      className="bg-green-600 hover:bg-green-700"
                      disabled={isLoading}
                    >
                      <Check className="h-4 w-4 mr-2" />
                      Confirmar Pedido
                    </Button>
                    <Button
                      variant="destructive"
                      onClick={() => {
                        handleCancelOrder(selectedOrder.id);
                        setSelectedOrder(null);
                      }}
                      disabled={isLoading}
                    >
                      <X className="h-4 w-4 mr-2" />
                      Cancelar Pedido
                    </Button>
                  </>
                )}
                {selectedOrder.status === 'confirmed' && (
                   <Button
                     onClick={() => {
                       handleUpdateOrderStatus(selectedOrder.id, OrderStatus.PREPARING);
                       setSelectedOrder(null);
                     }}
                     className="bg-blue-600 hover:bg-blue-700"
                     disabled={isLoading}
                   >
                     <Play className="h-4 w-4 mr-2" />
                     Iniciar Preparação
                   </Button>
                 )}
                 {selectedOrder.status === 'preparing' && (
                   <Button
                     onClick={() => {
                       handleUpdateOrderStatus(selectedOrder.id, OrderStatus.READY);
                       setSelectedOrder(null);
                     }}
                     className="bg-purple-600 hover:bg-purple-700"
                     disabled={isLoading}
                   >
                     <Check className="h-4 w-4 mr-2" />
                     Marcar como Pronto
                   </Button>
                 )}
                 {selectedOrder.status === 'ready' && (
                   <Button
                     onClick={() => {
                       handleUpdateOrderStatus(selectedOrder.id, OrderStatus.DELIVERED);
                       setSelectedOrder(null);
                     }}
                     className="bg-green-600 hover:bg-green-700"
                     disabled={isLoading}
                   >
                     <Truck className="h-4 w-4 mr-2" />
                     Marcar como Entregue
                   </Button>
                 )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
      <AuthDebug />
    </div>
  );
}