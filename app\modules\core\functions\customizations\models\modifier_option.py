import uuid
from typing import TYPE_CHECKING
from sqlalchemy import (
    Integer,
    String,
    ForeignKey,
    Numeric,
    Index,
    Boolean,
)
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.orm import relationship, Mapped, mapped_column

from app.db.base import Base

if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from .modifier_group import ModifierGroup
    # from app.modules.shared.inventory.models.inventory_item import InventoryItem

class ModifierOption(Base):
    """
    Represents a specific choice within a ModifierGroup (e.g., Extra Cheese, Remove Onions).
    """
    __tablename__ = "modifier_options"

    id: Mapped[uuid.UUID] = mapped_column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    tenant_id: Mapped[uuid.UUID] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    modifier_group_id: Mapped[uuid.UUID] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("modifier_groups.id"), nullable=False, index=True)

    name: Mapped[str] = mapped_column(String(100), nullable=False)
    price_adjustment: Mapped[float] = mapped_column(Numeric(10, 2), default=0.00, nullable=False)
    is_default: Mapped[bool | None] = mapped_column(Boolean, default=False)
    display_order: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)

    # Relationships
    tenant: Mapped["Tenant"] = relationship("Tenant")
    modifier_group: Mapped["ModifierGroup"] = relationship("ModifierGroup", back_populates="options")

    # Re-enabled this relationship to fix SQLAlchemy configuration error
    inventory_items: Mapped[list["InventoryItem"]] = relationship(
        "InventoryItem",
        secondary="modifier_option_inventory_association",
        back_populates="modifier_options",
    )

    __table_args__ = (
        Index("ix_modifier_options_tenant_id_group_id", "tenant_id", "modifier_group_id"),
        Index(
            "ix_modifier_options_tenant_id_group_id_name",
            "tenant_id",
            "modifier_group_id",
            "name",
            unique=True,
        ),
    )

    def __repr__(self) -> str:
        return f"<ModifierOption(id={self.id}, name='{self.name}', group_id={self.modifier_group_id}, tenant_id={self.tenant_id})>" 