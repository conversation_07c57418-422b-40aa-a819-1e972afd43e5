# Tenant Settings - Currency

**Categoria:** Tenant Settings
**Módulo:** Currency
**Total de Endpoints:** 2
**Gerado em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [PUT /api/tenants/settings/currency/](#put-apitenantssettingscurrency) - Update Currency Settings
- [PUT /api/tenants/tenant-settings/currency/](#put-apitenantstenant-settingscurrency) - Update Currency Settings

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### CurrencySettingsUpdate

**Descrição:** Schema for updating currency settings.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `default_currency` | unknown | ❌ | Default currency code (1:1 ratio for stability) |
| `currency_config` | unknown | ❌ | Multi-currency configuration with exchange rates and formatting |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### TenantSettingsRead

**Descrição:** Schema for reading tenant settings.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `business_name` | unknown | ❌ | Business name |
| `business_type` | unknown | ❌ | Type of business |
| `tenant_slug` | unknown | ❌ | Unique slug for tenant identification in public URLs |
| `default_currency` | string | ❌ | Default currency code (1:1 ratio for stability) |
| `currency_config` | unknown | ❌ | Multi-currency configuration with exchange rates and formatting |
| `timezone` | string | ❌ | Timezone identifier |
| `operating_hours` | unknown | ❌ | Operating hours configuration by day of week |
| `multi_language_enabled` | boolean | ❌ | Enable multi-language support |
| `available_languages` | unknown | ❌ | List of available language codes |
| `default_language` | string | ❌ | Default language code |
| `loyalty_enabled` | boolean | ❌ | Enable loyalty system |
| `loyalty_config` | unknown | ❌ | Loyalty system configuration |
| `country` | unknown | ❌ | Country code (ISO 3166-1 alpha-2) |
| `address` | unknown | ❌ | Complete address with coordinates |
| `base_tax_rate` | string | ❌ | Base tax rate percentage |
| `tax_calculation_method` | string | ❌ | Tax calculation method: 'incremental' or 'inclusive' |
| `wifi_networks` | unknown | ❌ | WiFi networks configuration by zone |
| `social_media_links` | unknown | ❌ | Social media platform links with icons |
| `subscription_plan` | unknown | ❌ | Current subscription plan |
| `subscription_status` | string | ❌ | Subscription status |
| `additional_settings` | unknown | ❌ | Additional configuration settings |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | unknown | ❌ | - |
| `updated_at` | unknown | ❌ | - |
| `has_social_media_links` | boolean | ❌ | Whether social media links are configured |

## 🔗 Endpoints Detalhados

### PUT /api/tenants/settings/currency/ {#put-apitenantssettingscurrency}

**Resumo:** Update Currency Settings
**Descrição:** Update multi-currency configuration.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [CurrencySettingsUpdate](#currencysettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/tenants/settings/currency/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/tenants/tenant-settings/currency/ {#put-apitenantstenant-settingscurrency}

**Resumo:** Update Currency Settings
**Descrição:** Update multi-currency configuration.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [CurrencySettingsUpdate](#currencysettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/tenants/tenant-settings/currency/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
