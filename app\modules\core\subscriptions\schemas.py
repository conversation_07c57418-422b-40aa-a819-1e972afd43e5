import uuid
from pydantic import BaseModel, Field, ConfigDict  # Adicionado ConfigDict
from typing import List, Optional
from datetime import datetime
from decimal import Decimal

# Import Enums from models
from .models import BillingPeriodEnum, FeatureValueTypeEnum, SubscriptionStatusEnum

# ============================================
# Feature Schemas
# ============================================


class FeatureBase(BaseModel):
    name: str = Field(..., max_length=255)
    key: str = Field(
        ...,
        max_length=100,
        description="Chave programática única (ex: max_custom_domains)",
    )
    description: Optional[str] = None
    value_type: FeatureValueTypeEnum
    unit: Optional[str] = Field(
        None,
        max_length=50,
        description="Unidade para NUMERIC (Ex: 'usuários', 'GB', '/mês')",
    )


class FeatureCreate(FeatureBase):
    pass


class FeatureUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    value_type: Optional[FeatureValueTypeEnum] = None
    unit: Optional[str] = Field(None, max_length=50)


class FeatureInDBBase(FeatureBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)  # Compatibility with SQLAlchemy models


# Properties to return to client


class Feature(FeatureInDBBase):
    pass


# ============================================
# Plan Schemas
# ============================================

# Schema para representar o valor de uma feature dentro de um plano


class PlanFeatureValue(BaseModel):
    feature_key: str = Field(..., alias="key")  # Usa a chave da feature
    value: str

    model_config = ConfigDict(
        from_attributes=True, populate_by_name=True  # Permite usar 'key' no input
    )


class PlanBase(BaseModel):
    name: str = Field(..., max_length=255)
    description: Optional[str] = None
    price: Decimal = Field(..., gt=0, decimal_places=2)
    currency: str = Field("BRL", max_length=10)
    billing_period: BillingPeriodEnum
    is_active: bool = True
    display_order: int = 0


class PlanCreate(PlanBase):
    pass


class PlanUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    price: Optional[Decimal] = Field(None, gt=0, decimal_places=2)
    currency: Optional[str] = Field(None, max_length=10)
    billing_period: Optional[BillingPeriodEnum] = None
    is_active: Optional[bool] = None
    display_order: Optional[int] = None


class PlanInDBBase(PlanBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


# Propriedades retornadas ao cliente, incluindo features vinculadas


class Plan(PlanInDBBase):
    features: List[PlanFeatureValue] = []  # Lista simplificada para o cliente


# Schema para vincular/atualizar feature a um plano (usado em API Admin)


class PlanFeatureLinkCreate(BaseModel):
    feature_id: int
    value: str = Field(..., max_length=255)


class PlanFeatureLinkUpdate(BaseModel):
    value: str = Field(..., max_length=255)


# ============================================
# Tenant Subscription Schemas
# ============================================


class TenantSubscriptionBase(BaseModel):
    tenant_id: uuid.UUID  # Assumindo que Tenant.id é UUID
    plan_id: int
    status: SubscriptionStatusEnum = SubscriptionStatusEnum.IN_TRIAL
    start_date: Optional[datetime] = None  # Pode ser definido automaticamente ou manualmente
    end_date: Optional[datetime] = None
    next_billing_date: Optional[datetime] = None
    trial_ends_at: Optional[datetime] = None
    payment_gateway_subscription_id: Optional[str] = Field(None, max_length=255)
    cancellation_reason: Optional[str] = None


class TenantSubscriptionCreate(TenantSubscriptionBase):
    start_date: datetime = Field(default_factory=datetime.utcnow)  # Default to now if not provided


# Schema para Admin criar/modificar assinatura manualmente


class TenantSubscriptionAdminCreate(TenantSubscriptionBase):
    start_date: datetime
    status: SubscriptionStatusEnum  # Admin deve definir explicitamente


class TenantSubscriptionAdminUpdate(BaseModel):
    plan_id: Optional[int] = None
    status: Optional[SubscriptionStatusEnum] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    next_billing_date: Optional[datetime] = None
    trial_ends_at: Optional[datetime] = None
    payment_gateway_subscription_id: Optional[str] = Field(None, max_length=255)
    cancellation_reason: Optional[str] = None
    cancelled_at: Optional[datetime] = None


class TenantSubscriptionUpdate(BaseModel):
    # O que o tenant pode atualizar? Geralmente limitado a cancelamento
    cancellation_reason: Optional[str] = None


class TenantSubscriptionInDBBase(TenantSubscriptionBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    cancelled_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


# Propriedades retornadas ao cliente


class TenantSubscription(TenantSubscriptionInDBBase):
    plan: Plan  # Inclui detalhes do plano na resposta


# Schema para solicitar mudança de plano


class ChangePlanRequest(BaseModel):
    new_plan_id: int
    billing_period: Optional[BillingPeriodEnum] = None  # Se o novo plano tiver opções


# Schema para solicitar cancelamento


class CancelSubscriptionRequest(BaseModel):
    reason: Optional[str] = None


# Schema para Admin atualizar status


class UpdateSubscriptionStatusRequest(BaseModel):
    new_status: SubscriptionStatusEnum
    reason: Optional[str] = None
