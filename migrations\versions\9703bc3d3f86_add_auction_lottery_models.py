"""add_auction_lottery_models

Revision ID: 9703bc3d3f86
Revises: 1dc0855363fb
Create Date: 2025-06-27 15:17:21.800103

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9703bc3d3f86'
down_revision: Union[str, None] = '1dc0855363fb'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Removed problematic index operations due to constraint dependencies
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_languages_code', table_name='languages')
    op.create_index(op.f('ix_languages_code'), 'languages', ['code'], unique=True)
    # ### end Alembic commands ###
