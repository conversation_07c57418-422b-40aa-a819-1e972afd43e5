"""
Review Moderation Schemas

Schemas para moderação e denúncias de reviews.
"""

from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, Field, validator

from app.modules.core.functions.reviews.models.review_moderation import (
    ReportReason, 
    ReportStatus
)


class ReviewReportCreate(BaseModel):
    """Schema para criação de denúncia"""
    review_id: UUID = Field(..., description="ID da review denunciada")
    reason: ReportReason = Field(..., description="Motivo da denúncia")
    description: Optional[str] = Field(
        None, 
        max_length=500, 
        description="Descrição adicional da denúncia"
    )
    
    @validator('description')
    def validate_description(cls, v):
        if v is not None:
            v = v.strip()
            if len(v) == 0:
                return None
        return v


class ReviewReportResponse(BaseModel):
    """Schema de resposta para denúncia"""
    id: UUID
    review_id: UUID
    reporter_id: UUID
    reason: ReportReason
    description: Optional[str]
    status: ReportStatus
    created_at: datetime
    reviewed_at: Optional[datetime]
    reviewed_by: Optional[UUID]
    moderator_notes: Optional[str]
    action_taken: Optional[str]
    
    class Config:
        from_attributes = True


class ReviewHelpfulnessCreate(BaseModel):
    """Schema para marcar review como útil/não útil"""
    review_id: UUID = Field(..., description="ID da review")
    is_helpful: bool = Field(..., description="True se útil, False se não útil")


class ReviewHelpfulnessResponse(BaseModel):
    """Schema de resposta para voto de utilidade"""
    id: UUID
    review_id: UUID
    user_id: UUID
    is_helpful: bool
    created_at: datetime
    
    class Config:
        from_attributes = True


class ModerationActionCreate(BaseModel):
    """Schema para ação de moderação"""
    review_id: UUID = Field(..., description="ID da review")
    action_type: str = Field(..., description="Tipo de ação")
    reason: str = Field(..., min_length=10, description="Motivo da ação")
    
    @validator('action_type')
    def validate_action_type(cls, v):
        allowed = ['hide_text', 'hide_complete', 'restore']
        if v not in allowed:
            raise ValueError(f'action_type deve ser um de: {allowed}')
        return v


class ModerationActionResponse(BaseModel):
    """Schema de resposta para ação de moderação"""
    id: UUID
    review_id: UUID
    moderator_id: UUID
    action_type: str
    reason: str
    previous_status: str
    new_status: str
    created_at: datetime
    
    class Config:
        from_attributes = True


class ReportModerationRequest(BaseModel):
    """Schema para moderação de denúncia"""
    action: str = Field(..., description="Ação: dismiss, take_action")
    notes: Optional[str] = Field(None, description="Notas do moderador")
    review_action: Optional[str] = Field(
        None, 
        description="Ação na review se take_action: hide_text, hide_complete"
    )
    
    @validator('action')
    def validate_action(cls, v):
        allowed = ['dismiss', 'take_action']
        if v not in allowed:
            raise ValueError(f'action deve ser um de: {allowed}')
        return v
    
    @validator('review_action')
    def validate_review_action(cls, v, values):
        if values.get('action') == 'take_action' and not v:
            raise ValueError('review_action é obrigatório quando action é take_action')
        if v and v not in ['hide_text', 'hide_complete']:
            raise ValueError('review_action deve ser hide_text ou hide_complete')
        return v


class ModerationQueueResponse(BaseModel):
    """Schema para fila de moderação"""
    pending_reports: int
    reported_reviews: int
    hidden_reviews: int
    recent_actions: int
    
    class Config:
        from_attributes = True


class ModerationStatsResponse(BaseModel):
    """Schema para estatísticas de moderação"""
    total_reports: int
    reports_by_reason: dict
    reports_by_status: dict
    actions_by_type: dict
    moderator_activity: dict
    
    class Config:
        from_attributes = True