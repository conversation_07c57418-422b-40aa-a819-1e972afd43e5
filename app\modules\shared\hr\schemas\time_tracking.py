from typing import Optional, Dict, Any, List
import uuid
from datetime import datetime, date
from pydantic import BaseModel, Field, ConfigDict

from app.modules.shared.hr.core.models.time_tracking import (
    TimeRecordType,
    TimeRecordSource,
    TimeBankTransactionType,
    VerificationStatus,
    TimeMirrorStatus,
)

# Time Record Schemas


class TimeRecordBase(BaseModel):
    """Base schema for TimeRecord."""

    record_time: datetime = Field(default_factory=datetime.utcnow)
    record_type: TimeRecordType
    source: TimeRecordSource = TimeRecordSource.WEB
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    location_name: Optional[str] = None
    notes: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    device_id: Optional[str] = None
    device_info: Optional[Dict[str, Any]] = None


class TimeRecordCreate(TimeRecordBase):
    """Schema for creating a TimeRecord."""

    employee_id: uuid.UUID


class TimeRecordUpdate(BaseModel):
    """Schema for updating a TimeRecord."""

    record_time: Optional[datetime] = None
    record_type: Optional[TimeRecordType] = None
    source: Optional[TimeRecordSource] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    location_name: Optional[str] = None
    notes: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    is_verified: Optional[bool] = None
    verification_status: Optional[VerificationStatus] = None
    verified_by: Optional[uuid.UUID] = None
    verification_time: Optional[datetime] = None
    device_id: Optional[str] = None
    device_info: Optional[Dict[str, Any]] = None


class TimeRecordRead(TimeRecordBase):
    """Schema for reading a TimeRecord."""

    id: uuid.UUID
    employee_id: uuid.UUID
    tenant_id: uuid.UUID
    is_verified: bool
    verification_status: VerificationStatus
    verified_by: Optional[uuid.UUID] = None
    verification_time: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


# Overtime Record Schemas


class OvertimeRecordBase(BaseModel):
    """Base schema for OvertimeRecord."""

    date: datetime
    start_time: datetime
    end_time: datetime
    duration_minutes: int
    is_paid: bool = True
    is_banked: bool = False
    reason: Optional[str] = None
    notes: Optional[str] = None
    is_night_shift: bool = False
    night_shift_percentage: Optional[float] = None
    is_holiday: bool = False
    holiday_percentage: Optional[float] = None


class OvertimeRecordCreate(OvertimeRecordBase):
    """Schema for creating an OvertimeRecord."""

    employee_id: uuid.UUID


class OvertimeRecordUpdate(BaseModel):
    """Schema for updating an OvertimeRecord."""

    date: Optional[datetime] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration_minutes: Optional[int] = None
    is_paid: Optional[bool] = None
    is_banked: Optional[bool] = None
    reason: Optional[str] = None
    notes: Optional[str] = None
    is_approved: Optional[bool] = None
    approved_by: Optional[uuid.UUID] = None
    approval_time: Optional[datetime] = None
    is_night_shift: Optional[bool] = None
    night_shift_percentage: Optional[float] = None
    is_holiday: Optional[bool] = None
    holiday_percentage: Optional[float] = None


class OvertimeRecordRead(OvertimeRecordBase):
    """Schema for reading an OvertimeRecord."""

    id: uuid.UUID
    employee_id: uuid.UUID
    tenant_id: uuid.UUID
    is_approved: bool
    approved_by: Optional[uuid.UUID] = None
    approval_time: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


# Time Bank Schemas


class TimeBankBase(BaseModel):
    """Base schema for TimeBank."""

    balance_minutes: int = 0
    has_expiry: bool = False
    expiry_date: Optional[datetime] = None
    compensation_rate: float = 1.0
    max_balance_minutes: Optional[int] = None
    min_withdrawal_minutes: int = 60


class TimeBankCreate(TimeBankBase):
    """Schema for creating a TimeBank."""

    employee_id: uuid.UUID


class TimeBankUpdate(BaseModel):
    """Schema for updating a TimeBank."""

    balance_minutes: Optional[int] = None
    has_expiry: Optional[bool] = None
    expiry_date: Optional[datetime] = None
    compensation_rate: Optional[float] = None
    max_balance_minutes: Optional[int] = None
    min_withdrawal_minutes: Optional[int] = None


class TimeBankRead(TimeBankBase):
    """Schema for reading a TimeBank."""

    id: uuid.UUID
    employee_id: uuid.UUID
    tenant_id: uuid.UUID
    last_updated: datetime

    model_config = ConfigDict(from_attributes=True)


# Time Bank Transaction Schemas


class TimeBankTransactionBase(BaseModel):
    """Base schema for TimeBankTransaction."""

    transaction_time: datetime = Field(default_factory=datetime.utcnow)
    transaction_type: TimeBankTransactionType
    minutes: int
    reference_id: Optional[uuid.UUID] = None
    reference_type: Optional[str] = None
    description: Optional[str] = None
    notes: Optional[str] = None


class TimeBankTransactionCreate(TimeBankTransactionBase):
    """Schema for creating a TimeBankTransaction."""

    time_bank_id: uuid.UUID
    created_by: Optional[uuid.UUID] = None


class TimeBankTransactionRead(TimeBankTransactionBase):
    """Schema for reading a TimeBankTransaction."""

    id: uuid.UUID
    time_bank_id: uuid.UUID
    tenant_id: uuid.UUID
    created_by: Optional[uuid.UUID] = None

    model_config = ConfigDict(from_attributes=True)


# Time Mirror Schemas


class TimeMirrorBase(BaseModel):
    """Base schema for TimeMirror."""

    start_date: datetime
    end_date: datetime
    total_worked_minutes: int = 0
    total_overtime_minutes: int = 0
    total_absence_minutes: int = 0
    notes: Optional[str] = None


class TimeMirrorCreate(TimeMirrorBase):
    """Schema for creating a TimeMirror."""

    employee_id: uuid.UUID


class TimeMirrorUpdate(BaseModel):
    """Schema for updating a TimeMirror."""

    total_worked_minutes: Optional[int] = None
    total_overtime_minutes: Optional[int] = None
    total_absence_minutes: Optional[int] = None
    status: Optional[TimeMirrorStatus] = None
    submitted_at: Optional[datetime] = None
    approved_at: Optional[datetime] = None
    approved_by: Optional[uuid.UUID] = None
    finalized_at: Optional[datetime] = None
    notes: Optional[str] = None
    adjustment_reason: Optional[str] = None
    payroll_processed: Optional[bool] = None
    payroll_reference: Optional[str] = None


class TimeMirrorRead(TimeMirrorBase):
    """Schema for reading a TimeMirror."""

    id: uuid.UUID
    employee_id: uuid.UUID
    tenant_id: uuid.UUID
    generation_date: datetime
    status: TimeMirrorStatus
    submitted_at: Optional[datetime] = None
    approved_at: Optional[datetime] = None
    approved_by: Optional[uuid.UUID] = None
    finalized_at: Optional[datetime] = None
    adjustment_reason: Optional[str] = None
    payroll_processed: bool
    payroll_reference: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


class TimeMirrorWithRecords(TimeMirrorRead):
    """Schema for reading a TimeMirror with its time records."""

    time_records: List[TimeRecordRead] = []

    model_config = ConfigDict(from_attributes=True)


# Overtime Rule Schemas


class OvertimeRuleBase(BaseModel):
    """Base schema for OvertimeRule."""

    name: str
    description: Optional[str] = None
    is_active: bool = True
    department: Optional[str] = None
    job_title: Optional[str] = None
    regular_rate: float = 1.5
    weekend_rate: float = 2.0
    holiday_rate: float = 2.0
    night_shift_start: Optional[str] = None
    night_shift_end: Optional[str] = None
    night_shift_rate: float = 1.2
    checkin_tolerance: int = 15
    checkout_tolerance: int = 15
    allow_banking: bool = True
    max_bank_hours: Optional[int] = None
    banking_expiry_days: Optional[int] = None


class OvertimeRuleCreate(OvertimeRuleBase):
    """Schema for creating an OvertimeRule."""


class OvertimeRuleUpdate(BaseModel):
    """Schema for updating an OvertimeRule."""

    name: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None
    department: Optional[str] = None
    job_title: Optional[str] = None
    regular_rate: Optional[float] = None
    weekend_rate: Optional[float] = None
    holiday_rate: Optional[float] = None
    night_shift_start: Optional[str] = None
    night_shift_end: Optional[str] = None
    night_shift_rate: Optional[float] = None
    checkin_tolerance: Optional[int] = None
    checkout_tolerance: Optional[int] = None
    allow_banking: Optional[bool] = None
    max_bank_hours: Optional[int] = None
    banking_expiry_days: Optional[int] = None


class OvertimeRuleRead(OvertimeRuleBase):
    """Schema for reading an OvertimeRule."""

    id: uuid.UUID
    tenant_id: uuid.UUID

    model_config = ConfigDict(from_attributes=True)


# Specialized Time Tracking Input/Output Schemas


class ClockInRequest(BaseModel):
    """Schema for clocking in."""

    employee_id: uuid.UUID
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    location_name: Optional[str] = None
    notes: Optional[str] = None
    source: TimeRecordSource = TimeRecordSource.WEB
    device_id: Optional[str] = None
    device_info: Optional[Dict[str, Any]] = None


class ClockOutRequest(BaseModel):
    """Schema for clocking out."""

    employee_id: uuid.UUID
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    location_name: Optional[str] = None
    notes: Optional[str] = None
    source: TimeRecordSource = TimeRecordSource.WEB
    device_id: Optional[str] = None
    device_info: Optional[Dict[str, Any]] = None


class TimeCardSummary(BaseModel):
    """Schema for time card summary."""

    employee_id: uuid.UUID
    employee_name: str
    date: datetime
    clock_in_time: Optional[datetime] = None
    clock_out_time: Optional[datetime] = None
    break_minutes: int = 0
    total_hours: float = 0.0
    is_complete: bool = False

    model_config = ConfigDict(from_attributes=True)


class GenerateTimeMirrorRequest(BaseModel):
    """Schema for generating a time mirror."""

    employee_id: uuid.UUID
    start_date: date
    end_date: date
    auto_approve: bool = False


class PayrollIntegrationRequest(BaseModel):
    """Schema for integrating time mirror with payroll."""

    time_mirror_id: uuid.UUID
    include_overtime: bool = True
    payroll_reference: Optional[str] = None
