'use client';

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { useAuth } from '@/lib/auth/AuthProvider';
import { useTenant } from '@/lib/tenant/TenantProvider';
import { apiClient } from '@/lib/apiClient';
import { toast } from 'sonner';

// Types for TShop B2B Cart
export interface TShopCartItem {
  id: string;
  product_id: string;
  product_name: string;
  product_image?: string;
  quantity: number;
  unit_price: number; // B2B price
  total_price: number;
  supplier_id: string;
  supplier_name: string;
  minimum_order_quantity?: number;
  bulk_discount_tier?: string;
  estimated_delivery_days?: number;
}

export interface TShopCart {
  id: string;
  tenant_id: string;
  user_id: string;
  status: 'active' | 'expired' | 'converted' | 'abandoned';
  market_context: 'b2b';
  subtotal: number;
  tax_amount: number;
  discount_amount: number;
  bulk_discount_amount: number;
  total_amount: number;
  currency: string;
  items: TShopCartItem[];
  total_items: number;
  estimated_delivery_date?: string;
  requires_approval: boolean;
  approval_status?: 'pending' | 'approved' | 'rejected';
  credit_limit_check: boolean;
  is_expired: boolean;
}

// Cart Actions
type TShopCartAction =
  | { type: 'SET_CART'; payload: TShopCart }
  | { type: 'ADD_ITEM'; payload: { product_id: string; quantity: number; supplier_id: string } }
  | { type: 'UPDATE_ITEM'; payload: { item_id: string; quantity: number } }
  | { type: 'REMOVE_ITEM'; payload: { item_id: string } }
  | { type: 'CLEAR_CART' }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null };

// Cart State
interface TShopCartState {
  cart: TShopCart | null;
  isLoading: boolean;
  error: string | null;
  isCartOpen: boolean;
}

// Context
interface TShopCartContextType extends TShopCartState {
  addToCart: (productId: string, quantity: number, supplierId: string) => Promise<void>;
  updateCartItem: (itemId: string, quantity: number) => Promise<void>;
  removeFromCart: (itemId: string) => Promise<void>;
  clearCart: () => Promise<void>;
  openCart: () => void;
  closeCart: () => void;
  refreshCart: () => Promise<void>;
  requestBulkQuote: (items: Array<{product_id: string; quantity: number}>) => Promise<void>;
}

const TShopCartContext = createContext<TShopCartContextType | undefined>(undefined);

// Reducer
function tshopCartReducer(state: TShopCartState, action: TShopCartAction): TShopCartState {
  switch (action.type) {
    case 'SET_CART':
      return {
        ...state,
        cart: action.payload,
        isLoading: false,
        error: null,
      };
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };
    case 'CLEAR_CART':
      return {
        ...state,
        cart: null,
        isLoading: false,
        error: null,
      };
    default:
      return state;
  }
}

// Provider Component
export function TShopCartProvider({ children }: { children: ReactNode }) {
  const { user, isAuthenticated } = useAuth();
  const { currentTenant } = useTenant();
  
  const [state, dispatch] = useReducer(tshopCartReducer, {
    cart: null,
    isLoading: false,
    error: null,
    isCartOpen: false,
  });

  // Load cart on mount and when user/tenant changes
  useEffect(() => {
    if (isAuthenticated && user && currentTenant) {
      loadCart();
    }
  }, [isAuthenticated, user?.id, currentTenant?.id]);

  const loadCart = async () => {
    if (!isAuthenticated || !user || !currentTenant) return;

    dispatch({ type: 'SET_LOADING', payload: true });
    
    try {
      const response = await apiClient('/cart/b2b', {
        headers: {
          'X-Market-Context': 'b2b',
          'X-Tenant-ID': currentTenant.id,
        },
      });

      if (response) {
        dispatch({ type: 'SET_CART', payload: response });
      }
    } catch (error: any) {
      console.error('Failed to load B2B cart:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Falha ao carregar carrinho B2B' });
    }
  };

  const addToCart = async (productId: string, quantity: number, supplierId: string) => {
    if (!isAuthenticated || !currentTenant) {
      toast.error('Você precisa estar logado para adicionar itens ao carrinho');
      return;
    }

    dispatch({ type: 'SET_LOADING', payload: true });

    try {
      const response = await apiClient('/cart/b2b/items', {
        method: 'POST',
        headers: {
          'X-Market-Context': 'b2b',
          'X-Tenant-ID': currentTenant.id,
        },
        body: JSON.stringify({
          product_id: productId,
          quantity,
          supplier_id: supplierId,
        }),
      });

      if (response) {
        dispatch({ type: 'SET_CART', payload: response });
        toast.success('Produto adicionado ao carrinho B2B');
      }
    } catch (error: any) {
      console.error('Failed to add item to B2B cart:', error);
      const errorMessage = error.message || 'Falha ao adicionar item ao carrinho';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      toast.error(errorMessage);
    }
  };

  const updateCartItem = async (itemId: string, quantity: number) => {
    if (!isAuthenticated || !currentTenant) return;

    dispatch({ type: 'SET_LOADING', payload: true });

    try {
      const response = await apiClient(`/cart/b2b/items/${itemId}`, {
        method: 'PUT',
        headers: {
          'X-Market-Context': 'b2b',
          'X-Tenant-ID': currentTenant.id,
        },
        body: JSON.stringify({ quantity }),
      });

      if (response) {
        dispatch({ type: 'SET_CART', payload: response });
      }
    } catch (error: any) {
      console.error('Failed to update B2B cart item:', error);
      const errorMessage = error.message || 'Falha ao atualizar item';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      toast.error(errorMessage);
    }
  };

  const removeFromCart = async (itemId: string) => {
    if (!isAuthenticated || !currentTenant) return;

    dispatch({ type: 'SET_LOADING', payload: true });

    try {
      const response = await apiClient(`/cart/b2b/items/${itemId}`, {
        method: 'DELETE',
        headers: {
          'X-Market-Context': 'b2b',
          'X-Tenant-ID': currentTenant.id,
        },
      });

      if (response) {
        dispatch({ type: 'SET_CART', payload: response });
        toast.success('Item removido do carrinho');
      }
    } catch (error: any) {
      console.error('Failed to remove item from B2B cart:', error);
      const errorMessage = error.message || 'Falha ao remover item';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      toast.error(errorMessage);
    }
  };

  const clearCart = async () => {
    if (!isAuthenticated || !currentTenant) return;

    dispatch({ type: 'SET_LOADING', payload: true });

    try {
      await apiClient('/cart/b2b/clear', {
        method: 'DELETE',
        headers: {
          'X-Market-Context': 'b2b',
          'X-Tenant-ID': currentTenant.id,
        },
      });

      dispatch({ type: 'CLEAR_CART' });
      toast.success('Carrinho limpo');
    } catch (error: any) {
      console.error('Failed to clear B2B cart:', error);
      const errorMessage = error.message || 'Falha ao limpar carrinho';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      toast.error(errorMessage);
    }
  };

  const requestBulkQuote = async (items: Array<{product_id: string; quantity: number}>) => {
    if (!isAuthenticated || !currentTenant) return;

    try {
      const response = await apiClient('/cart/b2b/bulk-quote', {
        method: 'POST',
        headers: {
          'X-Market-Context': 'b2b',
          'X-Tenant-ID': currentTenant.id,
        },
        body: JSON.stringify({ items }),
      });

      if (response) {
        toast.success('Cotação em lote solicitada com sucesso');
      }
    } catch (error: any) {
      console.error('Failed to request bulk quote:', error);
      toast.error('Falha ao solicitar cotação em lote');
    }
  };

  const openCart = () => {
    // This will be handled by the cart sidebar component
  };

  const closeCart = () => {
    // This will be handled by the cart sidebar component
  };

  const refreshCart = async () => {
    await loadCart();
  };

  const contextValue: TShopCartContextType = {
    ...state,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    openCart,
    closeCart,
    refreshCart,
    requestBulkQuote,
  };

  return (
    <TShopCartContext.Provider value={contextValue}>
      {children}
    </TShopCartContext.Provider>
  );
}

// Hook to use the context
export function useTShopCart() {
  const context = useContext(TShopCartContext);
  if (context === undefined) {
    throw new Error('useTShopCart must be used within a TShopCartProvider');
  }
  return context;
}
