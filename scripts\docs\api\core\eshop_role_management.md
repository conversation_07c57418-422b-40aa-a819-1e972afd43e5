# Core - Eshop Role Management

**Categoria:** Core
**Mó<PERSON><PERSON>:** Eshop Role Management
**Total de Endpoints:** 10
**Gerado em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [POST /api/modules/core/eshop/roles/api/v1/eshop/roles/b2b/approve/{association_id}](#post-apimodulescoreeshoprolesapiv1eshoprolesb2bapproveassociation-id) - Approve B2B Authorization
- [GET /api/modules/core/eshop/roles/api/v1/eshop/roles/b2b/pending](#get-apimodulescoreeshoprolesapiv1eshoprolesb2bpending) - List Pending B2B Requests
- [POST /api/modules/core/eshop/roles/api/v1/eshop/roles/b2b/reject/{association_id}](#post-apimodulescoreeshoprolesapiv1eshoprolesb2brejectassociation-id) - Reject B2B Authorization
- [POST /api/modules/core/eshop/roles/api/v1/eshop/roles/b2b/request](#post-apimodulescoreeshoprolesapiv1eshoprolesb2brequest) - Request B2B Authorization
- [GET /api/modules/core/eshop/roles/api/v1/eshop/roles/context/current](#get-apimodulescoreeshoprolesapiv1eshoprolescontextcurrent) - Get Current Context
- [POST /api/modules/core/eshop/roles/api/v1/eshop/roles/context/switch](#post-apimodulescoreeshoprolesapiv1eshoprolescontextswitch) - Switch Market Context
- [GET /api/modules/core/eshop/roles/api/v1/eshop/roles/customer/dashboard](#get-apimodulescoreeshoprolesapiv1eshoprolescustomerdashboard) - Get Customer Dashboard
- [GET /api/modules/core/eshop/roles/api/v1/eshop/roles/health](#get-apimodulescoreeshoprolesapiv1eshoproleshealth) - Role Management Health Check
- [GET /api/modules/core/eshop/roles/api/v1/eshop/roles/info](#get-apimodulescoreeshoprolesapiv1eshoprolesinfo) - Get Role Info
- [GET /api/modules/core/eshop/roles/api/v1/eshop/roles/vendor/dashboard](#get-apimodulescoreeshoprolesapiv1eshoprolesvendordashboard) - Get Vendor Dashboard

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### B2BApprovalRequest

**Descrição:** Request schema for B2B authorization approval.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `commission_rate` | unknown | ❌ | Commission rate for vendors (percentage) |
| `pricing_tier` | unknown | ❌ | Pricing tier for customers |
| `approval_notes` | unknown | ❌ | Approval notes |

### B2BAuthorizationRequest

**Descrição:** Request schema for B2B role authorization.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `requested_role` | string | ✅ | Requested B2B role (tvendor or tcostumer) |
| `business_name` | string | ✅ | Business name |
| `business_registration_number` | string | ✅ | Business registration number |
| `requester_notes` | unknown | ❌ | Additional notes from requester |

### B2BAuthorizationResponse

**Descrição:** Response schema for B2B authorization operations.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `association_id` | string | ✅ | - |
| `status` | string | ✅ | - |
| `message` | string | ✅ | - |
| `requested_role` | unknown | ❌ | - |
| `authorization_date` | unknown | ❌ | - |

### B2BRejectionRequest

**Descrição:** Request schema for B2B authorization rejection.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `rejection_reason` | string | ✅ | Reason for rejection |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### MarketContextSwitchRequest

**Descrição:** Request schema for market context switching.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `new_context` | string | ✅ | New market context (b2b, b2c, marketplace) |

### RoleInfoResponse

**Descrição:** Response schema for user role information.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `association_id` | string | ✅ | - |
| `role` | string | ✅ | - |
| `market_context` | string | ✅ | - |
| `is_b2b_role` | boolean | ✅ | - |
| `b2b_authorized` | boolean | ✅ | - |
| `business_verification_status` | unknown | ✅ | - |
| `pricing_tier` | unknown | ✅ | - |
| `commission_rate` | unknown | ✅ | - |
| `permissions` | object | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

## 🔗 Endpoints Detalhados

### POST /api/modules/core/eshop/roles/api/v1/eshop/roles/b2b/approve/{association_id} {#post-apimodulescoreeshoprolesapiv1eshoprolesb2bapproveassociation-id}

**Resumo:** Approve B2B Authorization
**Descrição:** Approve B2B authorization request (Admin/Manager only).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `association_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [B2BApprovalRequest](#b2bapprovalrequest)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [B2BAuthorizationResponse](#b2bauthorizationresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/eshop/roles/api/v1/eshop/roles/b2b/approve/{association_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/eshop/roles/api/v1/eshop/roles/b2b/pending {#get-apimodulescoreeshoprolesapiv1eshoprolesb2bpending}

**Resumo:** List Pending B2B Requests
**Descrição:** List pending B2B authorization requests (Admin/Manager only).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `limit` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/eshop/roles/api/v1/eshop/roles/b2b/pending" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/eshop/roles/api/v1/eshop/roles/b2b/reject/{association_id} {#post-apimodulescoreeshoprolesapiv1eshoprolesb2brejectassociation-id}

**Resumo:** Reject B2B Authorization
**Descrição:** Reject B2B authorization request (Admin/Manager only).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `association_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [B2BRejectionRequest](#b2brejectionrequest)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [B2BAuthorizationResponse](#b2bauthorizationresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/eshop/roles/api/v1/eshop/roles/b2b/reject/{association_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/core/eshop/roles/api/v1/eshop/roles/b2b/request {#post-apimodulescoreeshoprolesapiv1eshoprolesb2brequest}

**Resumo:** Request B2B Authorization
**Descrição:** Request B2B authorization for TVENDOR or TCOSTUMER role.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [B2BAuthorizationRequest](#b2bauthorizationrequest)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [B2BAuthorizationResponse](#b2bauthorizationresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/eshop/roles/api/v1/eshop/roles/b2b/request" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/eshop/roles/api/v1/eshop/roles/context/current {#get-apimodulescoreeshoprolesapiv1eshoprolescontextcurrent}

**Resumo:** Get Current Context
**Descrição:** Get current market context and user information.

**🔐 Autenticação:** Não requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/eshop/roles/api/v1/eshop/roles/context/current"
```

---

### POST /api/modules/core/eshop/roles/api/v1/eshop/roles/context/switch {#post-apimodulescoreeshoprolesapiv1eshoprolescontextswitch}

**Resumo:** Switch Market Context
**Descrição:** Switch user's market context (B2B/B2C/Marketplace).

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [MarketContextSwitchRequest](#marketcontextswitchrequest)

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/eshop/roles/api/v1/eshop/roles/context/switch" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/eshop/roles/api/v1/eshop/roles/customer/dashboard {#get-apimodulescoreeshoprolesapiv1eshoprolescustomerdashboard}

**Resumo:** Get Customer Dashboard
**Descrição:** Get B2B customer-specific dashboard information.

**🔐 Autenticação:** Não requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/eshop/roles/api/v1/eshop/roles/customer/dashboard"
```

---

### GET /api/modules/core/eshop/roles/api/v1/eshop/roles/health {#get-apimodulescoreeshoprolesapiv1eshoproleshealth}

**Resumo:** Role Management Health Check
**Descrição:** Health check for role management services.

**🔐 Autenticação:** Não requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/eshop/roles/api/v1/eshop/roles/health"
```

---

### GET /api/modules/core/eshop/roles/api/v1/eshop/roles/info {#get-apimodulescoreeshoprolesapiv1eshoprolesinfo}

**Resumo:** Get Role Info
**Descrição:** Get comprehensive role information for current user.

**🔐 Autenticação:** Requerida

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [RoleInfoResponse](#roleinforesponse)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/eshop/roles/api/v1/eshop/roles/info" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/eshop/roles/api/v1/eshop/roles/vendor/dashboard {#get-apimodulescoreeshoprolesapiv1eshoprolesvendordashboard}

**Resumo:** Get Vendor Dashboard
**Descrição:** Get vendor-specific dashboard information.

**🔐 Autenticação:** Não requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/eshop/roles/api/v1/eshop/roles/vendor/dashboard"
```

---
