# General - Financial Control Analytics

**Categoria:** General
**Módulo:** Financial Control Analytics
**Total de Endpoints:** 8
**Gerado em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [GET /api/financial/control/analytics/alerts](#get-apifinancialcontrolanalyticsalerts) - Get Alerts
- [GET /api/financial/control/analytics/budget-analysis](#get-apifinancialcontrolanalyticsbudget-analysis) - Get Budget Analysis
- [GET /api/financial/control/analytics/cash-flow](#get-apifinancialcontrolanalyticscash-flow) - Get Cash Flow Data
- [GET /api/financial/control/analytics/category-breakdown](#get-apifinancialcontrolanalyticscategory-breakdown) - Get Category Breakdown
- [GET /api/financial/control/analytics/dashboard](#get-apifinancialcontrolanalyticsdashboard) - Get Dashboard Data
- [GET /api/financial/control/analytics/health](#get-apifinancialcontrolanalyticshealth) - Analytics Health Check
- [GET /api/financial/control/analytics/metrics](#get-apifinancialcontrolanalyticsmetrics) - Get Control Metrics
- [GET /api/financial/control/analytics/trends](#get-apifinancialcontrolanalyticstrends) - Get Trend Data

## 📊 Schemas

### ControlMetrics

**Descrição:** Schema for financial control metrics.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `total_income` | string | ❌ | - |
| `total_expense` | string | ❌ | - |
| `net_balance` | string | ❌ | - |
| `total_transactions` | integer | ❌ | - |
| `pending_invoices` | integer | ❌ | - |
| `pending_amount` | string | ❌ | - |
| `overdue_invoices` | integer | ❌ | - |
| `overdue_amount` | string | ❌ | - |
| `monthly_growth` | number | ❌ | - |
| `income_growth` | number | ❌ | - |
| `expense_growth` | number | ❌ | - |
| `category_breakdown` | Array[CategoryBreakdown] | ❌ | - |
| `cash_flow_data` | Array[CashFlowData] | ❌ | - |
| `recent_transactions` | Array[object] | ❌ | - |
| `period_start` | unknown | ❌ | - |
| `period_end` | unknown | ❌ | - |
| `period_type` | unknown | ❌ | - |

### DashboardData

**Descrição:** Schema for dashboard data.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `metrics` | ControlMetrics | ✅ | - |
| `cash_flow_chart` | Array[CashFlowData] | ✅ | - |
| `category_chart` | Array[CategoryBreakdown] | ✅ | - |
| `trend_chart` | Array[TrendData] | ✅ | - |
| `budget_analysis` | Array[BudgetAnalysis] | ❌ | - |
| `comparison_data` | unknown | ❌ | - |
| `alerts` | Array[object] | ❌ | - |
| `last_updated` | string | ❌ | - |
| `data_freshness` | string | ❌ | - |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/financial/control/analytics/alerts {#get-apifinancialcontrolanalyticsalerts}

**Resumo:** Get Alerts
**Descrição:** Get financial alerts and notifications.

**🔐 Autenticação:** Não requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/control/analytics/alerts"
```

---

### GET /api/financial/control/analytics/budget-analysis {#get-apifinancialcontrolanalyticsbudget-analysis}

**Resumo:** Get Budget Analysis
**Descrição:** Get budget vs actual analysis.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `date_from` | string | query | ❌ | - |
| `date_to` | string | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/control/analytics/budget-analysis"
```

---

### GET /api/financial/control/analytics/cash-flow {#get-apifinancialcontrolanalyticscash-flow}

**Resumo:** Get Cash Flow Data
**Descrição:** Get cash flow data over time.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `date_from` | string | query | ❌ | - |
| `date_to` | string | query | ❌ | - |
| `period_type` | string | query | ❌ | - |
| `include_archived` | boolean | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/control/analytics/cash-flow"
```

---

### GET /api/financial/control/analytics/category-breakdown {#get-apifinancialcontrolanalyticscategory-breakdown}

**Resumo:** Get Category Breakdown
**Descrição:** Get expense breakdown by category.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `date_from` | string | query | ❌ | - |
| `date_to` | string | query | ❌ | - |
| `include_archived` | boolean | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/control/analytics/category-breakdown"
```

---

### GET /api/financial/control/analytics/dashboard {#get-apifinancialcontrolanalyticsdashboard}

**Resumo:** Get Dashboard Data
**Descrição:** Get comprehensive dashboard data with analytics.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `date_from` | string | query | ❌ | - |
| `date_to` | string | query | ❌ | - |
| `period_type` | string | query | ❌ | - |
| `category_ids` | string | query | ❌ | - |
| `entry_types` | string | query | ❌ | - |
| `include_archived` | boolean | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [DashboardData](#dashboarddata)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/control/analytics/dashboard"
```

---

### GET /api/financial/control/analytics/health {#get-apifinancialcontrolanalyticshealth}

**Resumo:** Analytics Health Check
**Descrição:** Health check for financial control analytics.

**🔐 Autenticação:** Não requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/control/analytics/health"
```

---

### GET /api/financial/control/analytics/metrics {#get-apifinancialcontrolanalyticsmetrics}

**Resumo:** Get Control Metrics
**Descrição:** Get financial control metrics.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `date_from` | string | query | ❌ | - |
| `date_to` | string | query | ❌ | - |
| `period_type` | string | query | ❌ | - |
| `category_ids` | string | query | ❌ | - |
| `include_archived` | boolean | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ControlMetrics](#controlmetrics)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/control/analytics/metrics"
```

---

### GET /api/financial/control/analytics/trends {#get-apifinancialcontrolanalyticstrends}

**Resumo:** Get Trend Data
**Descrição:** Get trend analysis data.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `date_from` | string | query | ❌ | - |
| `date_to` | string | query | ❌ | - |
| `period_type` | string | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/financial/control/analytics/trends"
```

---
