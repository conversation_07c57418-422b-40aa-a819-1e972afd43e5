"""Invoice schemas."""

import uuid
from datetime import date, datetime
from decimal import Decimal
from typing import Optional, List
from pydantic import BaseModel, Field, validator

from ..models.invoice import InvoiceStatus, InvoiceType


class InvoiceItemBase(BaseModel):
    """Base schema for invoice items."""
    
    description: str = Field(
        ..., 
        min_length=1, 
        max_length=500,
        description="Item description"
    )
    quantity: Decimal = Field(
        ..., 
        gt=0, 
        max_digits=10, 
        decimal_places=3,
        description="Item quantity"
    )
    unit_price: Decimal = Field(
        ..., 
        ge=0, 
        max_digits=10, 
        decimal_places=2,
        description="Unit price"
    )
    unit: Optional[str] = Field(
        None,
        max_length=20,
        description="Unit of measurement"
    )
    product_code: Optional[str] = Field(
        None,
        max_length=50,
        description="Product code"
    )
    notes: Optional[str] = Field(
        None,
        max_length=1000,
        description="Item notes"
    )
    tax_rate: Decimal = Field(
        default=Decimal('0.00'),
        ge=0,
        le=100,
        max_digits=5,
        decimal_places=2,
        description="Tax rate percentage"
    )
    sort_order: int = Field(
        default=0,
        ge=0,
        description="Display order"
    )


class InvoiceItemCreate(InvoiceItemBase):
    """Schema for creating invoice items."""
    pass


class InvoiceItemUpdate(BaseModel):
    """Schema for updating invoice items."""
    
    description: Optional[str] = Field(
        None,
        min_length=1,
        max_length=500,
        description="Item description"
    )
    quantity: Optional[Decimal] = Field(
        None,
        gt=0,
        max_digits=10,
        decimal_places=3,
        description="Item quantity"
    )
    unit_price: Optional[Decimal] = Field(
        None,
        ge=0,
        max_digits=10,
        decimal_places=2,
        description="Unit price"
    )
    unit: Optional[str] = Field(
        None,
        max_length=20,
        description="Unit of measurement"
    )
    product_code: Optional[str] = Field(
        None,
        max_length=50,
        description="Product code"
    )
    notes: Optional[str] = Field(
        None,
        max_length=1000,
        description="Item notes"
    )
    tax_rate: Optional[Decimal] = Field(
        None,
        ge=0,
        le=100,
        max_digits=5,
        decimal_places=2,
        description="Tax rate percentage"
    )
    sort_order: Optional[int] = Field(
        None,
        ge=0,
        description="Display order"
    )


class InvoiceItemRead(InvoiceItemBase):
    """Schema for reading invoice items."""
    
    id: uuid.UUID = Field(..., description="Item ID")
    invoice_id: uuid.UUID = Field(..., description="Invoice ID")
    total_price: Decimal = Field(..., description="Total price (quantity * unit_price)")
    tax_amount: Decimal = Field(..., description="Tax amount")
    
    class Config:
        from_attributes = True


class InvoiceBase(BaseModel):
    """Base schema for invoices."""
    
    invoice_type: InvoiceType = Field(
        ...,
        description="Type of invoice"
    )
    customer_name: str = Field(
        ...,
        min_length=1,
        max_length=200,
        description="Customer name"
    )
    customer_email: Optional[str] = Field(
        None,
        max_length=200,
        description="Customer email"
    )
    customer_phone: Optional[str] = Field(
        None,
        max_length=50,
        description="Customer phone"
    )
    customer_address: Optional[str] = Field(
        None,
        max_length=1000,
        description="Customer address"
    )
    customer_tax_id: Optional[str] = Field(
        None,
        max_length=50,
        description="Customer tax ID"
    )
    issue_date: date = Field(
        ...,
        description="Invoice issue date"
    )
    due_date: Optional[date] = Field(
        None,
        description="Payment due date"
    )
    tax_rate: Decimal = Field(
        default=Decimal('0.00'),
        ge=0,
        le=100,
        max_digits=5,
        decimal_places=2,
        description="Default tax rate percentage"
    )
    discount_amount: Decimal = Field(
        default=Decimal('0.00'),
        ge=0,
        max_digits=10,
        decimal_places=2,
        description="Discount amount"
    )
    payment_method: Optional[str] = Field(
        None,
        max_length=50,
        description="Payment method"
    )
    notes: Optional[str] = Field(
        None,
        max_length=2000,
        description="Invoice notes"
    )
    terms_and_conditions: Optional[str] = Field(
        None,
        max_length=2000,
        description="Terms and conditions"
    )
    order_id: Optional[uuid.UUID] = Field(
        None,
        description="Related order ID"
    )

    # B2B specific fields
    vendor_id: Optional[uuid.UUID] = Field(
        None,
        description="B2B vendor ID"
    )
    customer_b2b_id: Optional[uuid.UUID] = Field(
        None,
        description="B2B customer ID"
    )

    # File upload for B2B invoices
    notify_on_view: bool = Field(
        default=True,
        description="Notify when invoice is viewed"
    )
    notify_on_due: bool = Field(
        default=True,
        description="Notify when invoice is due"
    )

    @validator('customer_email')
    def validate_email(cls, v):
        """Validate email format."""
        if v and '@' not in v:
            raise ValueError('Invalid email format')
        return v

    @validator('due_date')
    def validate_due_date(cls, v, values):
        """Validate due date is not before issue date."""
        if v and 'issue_date' in values and v < values['issue_date']:
            raise ValueError('Due date cannot be before issue date')
        return v


class InvoiceCreate(InvoiceBase):
    """Schema for creating invoices."""
    
    items: List[InvoiceItemCreate] = Field(
        default_factory=list,
        description="Invoice items"
    )

    @validator('items')
    def validate_items(cls, v):
        """Validate at least one item is provided."""
        if not v:
            raise ValueError('Invoice must have at least one item')
        return v


class InvoiceUpdate(BaseModel):
    """Schema for updating invoices."""
    
    invoice_type: Optional[InvoiceType] = Field(
        None,
        description="Type of invoice"
    )
    status: Optional[InvoiceStatus] = Field(
        None,
        description="Invoice status"
    )
    customer_name: Optional[str] = Field(
        None,
        min_length=1,
        max_length=200,
        description="Customer name"
    )
    customer_email: Optional[str] = Field(
        None,
        max_length=200,
        description="Customer email"
    )
    customer_phone: Optional[str] = Field(
        None,
        max_length=50,
        description="Customer phone"
    )
    customer_address: Optional[str] = Field(
        None,
        max_length=1000,
        description="Customer address"
    )
    customer_tax_id: Optional[str] = Field(
        None,
        max_length=50,
        description="Customer tax ID"
    )
    issue_date: Optional[date] = Field(
        None,
        description="Invoice issue date"
    )
    due_date: Optional[date] = Field(
        None,
        description="Payment due date"
    )
    paid_date: Optional[date] = Field(
        None,
        description="Payment date"
    )
    tax_rate: Optional[Decimal] = Field(
        None,
        ge=0,
        le=100,
        max_digits=5,
        decimal_places=2,
        description="Default tax rate percentage"
    )
    discount_amount: Optional[Decimal] = Field(
        None,
        ge=0,
        max_digits=10,
        decimal_places=2,
        description="Discount amount"
    )
    payment_method: Optional[str] = Field(
        None,
        max_length=50,
        description="Payment method"
    )
    payment_reference: Optional[str] = Field(
        None,
        max_length=100,
        description="Payment reference"
    )
    notes: Optional[str] = Field(
        None,
        max_length=2000,
        description="Invoice notes"
    )
    terms_and_conditions: Optional[str] = Field(
        None,
        max_length=2000,
        description="Terms and conditions"
    )

    @validator('customer_email')
    def validate_email(cls, v):
        """Validate email format."""
        if v and '@' not in v:
            raise ValueError('Invalid email format')
        return v


class InvoiceRead(InvoiceBase):
    """Schema for reading invoices."""
    
    id: uuid.UUID = Field(..., description="Invoice ID")
    tenant_id: uuid.UUID = Field(..., description="Tenant ID")
    invoice_number: str = Field(..., description="Invoice number")
    status: InvoiceStatus = Field(..., description="Invoice status")
    created_by: uuid.UUID = Field(..., description="Created by user ID")
    
    # Calculated fields
    subtotal: Decimal = Field(..., description="Subtotal amount")
    tax_amount: Decimal = Field(..., description="Tax amount")
    total_amount: Decimal = Field(..., description="Total amount")
    
    # Optional related data
    paid_date: Optional[date] = Field(None, description="Payment date")
    payment_reference: Optional[str] = Field(None, description="Payment reference")
    created_by_name: Optional[str] = Field(None, description="Created by user name")
    order_number: Optional[str] = Field(None, description="Related order number")
    
    # PDF info
    pdf_generated_at: Optional[str] = Field(None, description="PDF generation timestamp")
    pdf_available: bool = Field(default=False, description="Whether PDF is available")
    
    # Items
    items: List[InvoiceItemRead] = Field(
        default_factory=list,
        description="Invoice items"
    )
    
    class Config:
        from_attributes = True


class InvoiceListResponse(BaseModel):
    """Schema for paginated invoice list response."""
    
    invoices: List[InvoiceRead] = Field(
        default_factory=list,
        description="List of invoices"
    )
    total: int = Field(
        default=0,
        description="Total number of invoices"
    )
    page: int = Field(
        default=1,
        description="Current page number"
    )
    per_page: int = Field(
        default=20,
        description="Items per page"
    )
    pages: int = Field(
        default=0,
        description="Total number of pages"
    )


class InvoiceFilter(BaseModel):
    """Schema for filtering invoices."""
    
    status: Optional[InvoiceStatus] = Field(
        None,
        description="Filter by status"
    )
    invoice_type: Optional[InvoiceType] = Field(
        None,
        description="Filter by type"
    )
    customer_email: Optional[str] = Field(
        None,
        description="Filter by customer email"
    )
    date_from: Optional[date] = Field(
        None,
        description="Filter from issue date"
    )
    date_to: Optional[date] = Field(
        None,
        description="Filter to issue date"
    )
    amount_min: Optional[Decimal] = Field(
        None,
        ge=0,
        description="Minimum total amount"
    )
    amount_max: Optional[Decimal] = Field(
        None,
        ge=0,
        description="Maximum total amount"
    )
    search: Optional[str] = Field(
        None,
        max_length=100,
        description="Search in invoice number, customer name, or notes"
    )


class InvoiceGenerateRequest(BaseModel):
    """Schema for generating invoice from order."""

    order_id: uuid.UUID = Field(..., description="Order ID to generate invoice from")
    template_id: Optional[str] = Field(
        None,
        description="Template ID for invoice generation"
    )
    send_email: bool = Field(
        default=False,
        description="Whether to send invoice by email"
    )
    email_to: Optional[str] = Field(
        None,
        description="Email address to send invoice to"
    )
    notes: Optional[str] = Field(
        None,
        max_length=2000,
        description="Additional notes for the invoice"
    )

    @validator('email_to')
    def validate_email_to(cls, v, values):
        """Validate email is provided when send_email is True."""
        if values.get('send_email') and not v:
            raise ValueError('Email address is required when send_email is True')
        if v and '@' not in v:
            raise ValueError('Invalid email format')
        return v


class InvoiceEmailRequest(BaseModel):
    """Schema for sending invoice by email."""

    invoice_id: uuid.UUID = Field(..., description="Invoice ID to send")
    email_to: str = Field(..., description="Email address to send to")
    email_subject: Optional[str] = Field(
        None,
        max_length=200,
        description="Email subject (optional, will use default)"
    )
    email_message: Optional[str] = Field(
        None,
        max_length=2000,
        description="Email message (optional, will use default)"
    )
    include_pdf: bool = Field(
        default=True,
        description="Whether to include PDF attachment"
    )

    @validator('email_to')
    def validate_email(cls, v):
        """Validate email format."""
        if '@' not in v:
            raise ValueError('Invalid email format')
        return v


class InvoicePDFResponse(BaseModel):
    """Schema for PDF generation response."""

    invoice_id: uuid.UUID = Field(..., description="Invoice ID")
    pdf_url: str = Field(..., description="PDF download URL")
    generated_at: str = Field(..., description="Generation timestamp")
    file_size: Optional[int] = Field(None, description="File size in bytes")


class InvoiceEmailResponse(BaseModel):
    """Schema for email sending response."""

    invoice_id: uuid.UUID = Field(..., description="Invoice ID")
    email_to: str = Field(..., description="Email address sent to")
    sent_at: str = Field(..., description="Email sent timestamp")
    message_id: Optional[str] = Field(None, description="Email message ID")
    success: bool = Field(..., description="Whether email was sent successfully")


# B2B specific schemas
class B2BInvoiceCreate(InvoiceBase):
    """Schema for creating B2B invoices."""

    vendor_id: uuid.UUID = Field(..., description="B2B vendor ID")
    customer_b2b_id: uuid.UUID = Field(..., description="B2B customer ID")
    invoice_type: InvoiceType = Field(
        default=InvoiceType.B2B_VENDOR,
        description="Must be B2B type"
    )
    items: List[InvoiceItemCreate] = Field(
        default_factory=list,
        description="Invoice items"
    )

    @validator('invoice_type')
    def validate_b2b_type(cls, v):
        """Validate invoice type is B2B."""
        if v not in [InvoiceType.B2B_VENDOR, InvoiceType.B2B_CUSTOMER]:
            raise ValueError('Invoice type must be B2B_VENDOR or B2B_CUSTOMER')
        return v

    @validator('items')
    def validate_items(cls, v):
        """Validate at least one item is provided."""
        if not v:
            raise ValueError('Invoice must have at least one item')
        return v


class B2BInvoiceFileUpload(BaseModel):
    """Schema for B2B invoice file upload."""

    file_name: str = Field(..., description="Original file name")
    file_size: int = Field(..., description="File size in bytes")
    file_type: str = Field(..., description="MIME type")

    @validator('file_size')
    def validate_file_size(cls, v):
        """Validate file size (max 10MB)."""
        max_size = 10 * 1024 * 1024  # 10MB
        if v > max_size:
            raise ValueError(f'File size cannot exceed {max_size} bytes')
        return v


class B2BInvoiceDownloadRequest(BaseModel):
    """Schema for requesting B2B invoice download."""

    expires_hours: int = Field(
        default=24,
        ge=1,
        le=168,  # Max 1 week
        description="Token expiration in hours"
    )


class B2BInvoiceStats(BaseModel):
    """Schema for B2B invoice statistics."""

    total_invoices: int = Field(default=0, description="Total number of invoices")
    total_amount: Decimal = Field(default=Decimal('0.00'), description="Total amount")
    paid_amount: Decimal = Field(default=Decimal('0.00'), description="Total paid amount")
    outstanding_amount: Decimal = Field(default=Decimal('0.00'), description="Total outstanding amount")

    # Status breakdown
    draft_count: int = Field(default=0, description="Draft invoices")
    pending_count: int = Field(default=0, description="Pending invoices")
    sent_count: int = Field(default=0, description="Sent invoices")
    viewed_count: int = Field(default=0, description="Viewed invoices")
    paid_count: int = Field(default=0, description="Paid invoices")
    overdue_count: int = Field(default=0, description="Overdue invoices")
    cancelled_count: int = Field(default=0, description="Cancelled invoices")
    disputed_count: int = Field(default=0, description="Disputed invoices")

    # Time-based stats
    this_month_amount: Decimal = Field(default=Decimal('0.00'), description="This month amount")
    last_month_amount: Decimal = Field(default=Decimal('0.00'), description="Last month amount")
    avg_payment_days: float = Field(default=0.0, description="Average payment days")


class B2BInvoiceResponse(BaseModel):
    """Schema for B2B invoice API responses."""

    success: bool = Field(..., description="Operation success")
    message: str = Field(..., description="Response message")
    invoice: Optional[InvoiceRead] = Field(None, description="Invoice data")
    access_token: Optional[str] = Field(None, description="Download access token")
    stats: Optional[B2BInvoiceStats] = Field(None, description="Statistics data")


class B2BInvoiceListResponse(BaseModel):
    """Schema for B2B invoice list response."""

    invoices: List[dict] = Field(default_factory=list, description="Invoice summaries")
    total: int = Field(default=0, description="Total invoices")
    page: int = Field(default=1, description="Current page")
    limit: int = Field(default=20, description="Items per page")
    total_pages: int = Field(default=0, description="Total pages")
    stats: Optional[B2BInvoiceStats] = Field(None, description="Summary statistics")
