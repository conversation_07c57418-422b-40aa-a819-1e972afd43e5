import logging
from typing import Optional, Sequence
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from sqlalchemy.exc import IntegrityError
from fastapi import HTTPException, status
import uuid

# Import models
from app.modules.tenants.restaurants.menu.models.menu_item import MenuItem
from app.modules.core.functions.customizations.models.modifier_group import (
    ModifierGroup,
)
from app.modules.core.functions.customizations.models.modifier_option import (
    ModifierOption,
)

# Import schemas
from app.modules.tenants.restaurants.menu.schemas.modifier_group import (
    ModifierGroupCreate,
    ModifierGroupUpdate,
)
from app.modules.tenants.restaurants.menu.schemas.modifier_option import (
    ModifierOptionCreate,
    ModifierOptionUpdate,
)

logger = logging.getLogger(__name__)


class MenuModifierService:
    def __init__(self, db_session: AsyncSession):
        self.db = db_session

    # --- Modifier Group Operations ---

    async def create_modifier_group(
        self,
        menu_item_id: uuid.UUID,
        group_in: ModifierGroupCreate,
        tenant_id: uuid.UUID,
        commit: bool = True,
    ) -> ModifierGroup:
        """Creates a new modifier group for a menu item."""
        # Ensure menu item exists for tenant
        stmt = select(MenuItem).where(
            MenuItem.id == menu_item_id,
            MenuItem.tenant_id == tenant_id
        )
        result = await self.db.execute(stmt)
        item = result.scalars().first()
        if not item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"MenuItem {menu_item_id} not found.",
            )

        try:
            group_data = group_in.model_dump(exclude={"options"})
            db_group = ModifierGroup(**group_data, menu_item_id=menu_item_id, tenant_id=tenant_id)
            self.db.add(db_group)
            await self.db.flush()  # Get ID

            for option_in in group_in.options:
                db_option = ModifierOption(
                    **option_in.model_dump(),
                    modifier_group_id=db_group.id,
                    tenant_id=tenant_id,
                )
                self.db.add(db_option)

            if commit:
                await self.db.commit()
                # Refresh and reload options
                await self.db.refresh(db_group, attribute_names=["options"])
            logger.info(
                f"ModifierGroup created: {db_group.id} for item {menu_item_id}, tenant {tenant_id}"
            )
            return db_group
        except IntegrityError as e:
            if commit:
                await self.db.rollback()
            logger.error(
                f"Error creating modifier group for item {menu_item_id}, tenant {tenant_id}: {e}"
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Error creating modifier group.",
            )
        except Exception as e:
            if commit:
                await self.db.rollback()
            logger.exception(f"Unexpected error creating modifier group: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def get_modifier_group(
        self, group_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Optional[ModifierGroup]:
        """Gets a modifier group by ID for the given tenant."""
        stmt = (
            select(ModifierGroup)
            .options(selectinload(ModifierGroup.options))
            .where(ModifierGroup.id == group_id, ModifierGroup.tenant_id == tenant_id)
        )
        result = await self.db.execute(stmt)
        return result.scalars().first()

    async def update_modifier_group(
        self, group_id: uuid.UUID, group_in: ModifierGroupUpdate, tenant_id: uuid.UUID
    ) -> Optional[ModifierGroup]:
        """Updates a modifier group."""
        db_group = await self.get_modifier_group(group_id, tenant_id)
        if not db_group:
            return None

        update_data = group_in.model_dump(exclude_unset=True, exclude={"options"})
        if not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No fields provided for modifier group update.",
            )

        try:
            for key, value in update_data.items():
                setattr(db_group, key, value)

            self.db.add(db_group)
            await self.db.commit()
            await self.db.refresh(db_group, attribute_names=["options"])
            logger.info(f"ModifierGroup {group_id} updated for tenant {tenant_id}")
            return db_group
        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"Integrity error updating modifier group {group_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Error updating modifier group.",
            )
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Unexpected error updating modifier group {group_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def delete_modifier_group(self, group_id: uuid.UUID, tenant_id: uuid.UUID) -> bool:
        """Deletes a modifier group and all its options."""
        db_group = await self.get_modifier_group(group_id, tenant_id)
        if not db_group:
            logger.warning(
                f"Attempted to delete non-existent modifier group {group_id} for tenant {tenant_id}"
            )
            return False

        try:
            # Delete options first (or rely on cascade delete in DB)
            await self.db.execute(
                select(ModifierOption)
                .where(
                    ModifierOption.modifier_group_id == group_id,
                    ModifierOption.tenant_id == tenant_id,
                )
                .delete()
            )

            # Then delete the group
            await self.db.delete(db_group)
            await self.db.commit()
            logger.info(f"ModifierGroup {group_id} deleted for tenant {tenant_id}")
            return True
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Error deleting modifier group {group_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    # --- Modifier Option Operations ---

    async def create_modifier_option(
        self, group_id: uuid.UUID, option_in: ModifierOptionCreate, tenant_id: uuid.UUID
    ) -> ModifierOption:
        """Creates a new modifier option for a modifier group."""
        # Ensure modifier group exists for tenant
        group = await self.get_modifier_group(group_id, tenant_id)
        if not group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"ModifierGroup {group_id} not found.",
            )

        try:
            db_option = ModifierOption(
                **option_in.model_dump(),
                modifier_group_id=group_id,
                tenant_id=tenant_id,
            )
            self.db.add(db_option)
            await self.db.commit()
            await self.db.refresh(db_option)
            logger.info(
                f"ModifierOption created: {db_option.id} for group {group_id}, tenant {tenant_id}"
            )
            return db_option
        except IntegrityError as e:
            await self.db.rollback()
            logger.error(
                f"Error creating modifier option for group {group_id}, tenant {tenant_id}: {e}"
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Error creating modifier option.",
            )
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Unexpected error creating modifier option: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def get_modifier_option(
        self, option_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Optional[ModifierOption]:
        """Gets a modifier option by ID for the given tenant."""
        stmt = select(ModifierOption).where(
            ModifierOption.id == option_id, ModifierOption.tenant_id == tenant_id
        )
        result = await self.db.execute(stmt)
        return result.scalars().first()

    async def update_modifier_option(
        self,
        option_id: uuid.UUID,
        option_in: ModifierOptionUpdate,
        tenant_id: uuid.UUID,
    ) -> Optional[ModifierOption]:
        """Updates a modifier option."""
        db_option = await self.get_modifier_option(option_id, tenant_id)
        if not db_option:
            return None

        update_data = option_in.model_dump(exclude_unset=True)
        if not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No fields provided for modifier option update.",
            )

        try:
            for key, value in update_data.items():
                setattr(db_option, key, value)

            self.db.add(db_option)
            await self.db.commit()
            await self.db.refresh(db_option)
            logger.info(f"ModifierOption {option_id} updated for tenant {tenant_id}")
            return db_option
        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"Integrity error updating modifier option {option_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Error updating modifier option.",
            )
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Unexpected error updating modifier option {option_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def delete_modifier_option(self, option_id: uuid.UUID, tenant_id: uuid.UUID) -> bool:
        """Deletes a modifier option."""
        db_option = await self.get_modifier_option(option_id, tenant_id)
        if not db_option:
            logger.warning(
                f"Attempted to delete non-existent modifier option {option_id} for tenant {tenant_id}"  # noqa: E501
            )
            return False

        try:
            await self.db.delete(db_option)
            await self.db.commit()
            logger.info(f"ModifierOption {option_id} deleted for tenant {tenant_id}")
            return True
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Error deleting modifier option {option_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )
