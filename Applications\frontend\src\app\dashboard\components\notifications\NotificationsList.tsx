'use client';

import React from 'react';
import Image from 'next/image';
import {
  CheckIcon,
  TrashIcon,
  EyeIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  PhotoIcon,
  LinkIcon
} from '@heroicons/react/24/outline';
import { CheckIcon as CheckSolidIcon } from '@heroicons/react/24/solid';

interface Notification {
  id: string;
  title: string;
  content: string;
  image_url?: string;
  action_url?: string;
  sender_id: string;
  sender_type: string;
  target_type: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  status: string;
  view_count: number;
  click_count: number;
  created_at: string;
  expires_at?: string;
  is_expired: boolean;
  is_read?: boolean;
}

interface NotificationsListProps {
  notifications: Notification[];
  loading: boolean;
  selectedNotifications: string[];
  onSelectionChange: (selected: string[]) => void;
  onToggleSelectAll: () => void;
  onMarkAsRead: (ids: string[]) => void;
  onDelete: (ids: string[], deleteForAll?: boolean) => void;
  onRefresh: () => void;
}

export default function NotificationsList({
  notifications,
  loading,
  selectedNotifications,
  onSelectionChange,
  onToggleSelectAll,
  onMarkAsRead,
  onDelete,
  onRefresh
}: NotificationsListProps) {
  
  const toggleSelection = (notificationId: string) => {
    if (selectedNotifications.includes(notificationId)) {
      onSelectionChange(selectedNotifications.filter(id => id !== notificationId));
    } else {
      onSelectionChange([...selectedNotifications, notificationId]);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'normal': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'low': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'urgent': return <ExclamationTriangleIcon className="h-4 w-4" />;
      case 'high': return <ExclamationTriangleIcon className="h-4 w-4" />;
      default: return null;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60);
      return `${diffInMinutes}m atrás`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h atrás`;
    } else {
      return date.toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  };

  const handleActionClick = async (notification: Notification) => {
    // Registra clique
    try {
      await fetch(`/api/modules/core/notifications/${notification.id}/click`, {
        method: 'POST'
      });
    } catch (error) {
      console.error('Erro ao registrar clique:', error);
    }

    // Abre URL se disponível
    if (notification.action_url) {
      window.open(notification.action_url, '_blank');
    }
  };

  if (loading) {
    return (
      <div className="p-8 text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-2 text-gray-600">Carregando notificações...</p>
      </div>
    );
  }

  if (notifications.length === 0) {
    return (
      <div className="p-8 text-center">
        <div className="mx-auto h-12 w-12 text-gray-400">
          <CheckSolidIcon />
        </div>
        <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhuma notificação</h3>
        <p className="mt-1 text-sm text-gray-500">
          Você não possui notificações no momento.
        </p>
      </div>
    );
  }

  return (
    <div className="divide-y divide-gray-200">
      {/* Header com seleção */}
      <div className="px-6 py-3 bg-gray-50 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={selectedNotifications.length === notifications.length}
              onChange={onToggleSelectAll}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <span className="text-sm font-medium text-gray-700">
              {notifications.length} notificação(ões)
            </span>
          </div>
          
          <button
            onClick={onRefresh}
            className="text-sm text-blue-600 hover:text-blue-800"
          >
            Atualizar
          </button>
        </div>
      </div>

      {/* Lista de notificações */}
      {notifications.map((notification) => (
        <div
          key={notification.id}
          className={`px-6 py-4 hover:bg-gray-50 transition-colors ${
            !notification.is_read ? 'bg-blue-50' : ''
          } ${notification.is_expired ? 'opacity-60' : ''}`}
        >
          <div className="flex items-start space-x-4">
            {/* Checkbox de seleção */}
            <input
              type="checkbox"
              checked={selectedNotifications.includes(notification.id)}
              onChange={() => toggleSelection(notification.id)}
              className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />

            {/* Imagem (se disponível) */}
            {notification.image_url && (
              <div className="flex-shrink-0 h-12 w-12 relative">
                <Image
                  src={notification.image_url}
                  alt=""
                  fill
                  className="rounded-lg object-cover cursor-pointer"
                  onClick={() => handleActionClick(notification)}
                />
              </div>
            )}

            {/* Conteúdo principal */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  {/* Título e badges */}
                  <div className="flex items-center space-x-2 mb-1">
                    <h3 className={`text-sm font-medium ${
                      notification.is_read ? 'text-gray-900' : 'text-gray-900 font-semibold'
                    }`}>
                      {notification.title}
                    </h3>
                    
                    {/* Badge de prioridade */}
                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ${
                      getPriorityColor(notification.priority)
                    }`}>
                      {getPriorityIcon(notification.priority)}
                      <span className="ml-1">{notification.priority.toUpperCase()}</span>
                    </span>
                    
                    {/* Badge de não lida */}
                    {!notification.is_read && (
                      <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Nova
                      </span>
                    )}
                    
                    {/* Badge de expirada */}
                    {notification.is_expired && (
                      <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        <ClockIcon className="h-3 w-3 mr-1" />
                        Expirada
                      </span>
                    )}
                  </div>

                  {/* Conteúdo */}
                  <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                    {notification.content}
                  </p>

                  {/* Metadados */}
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <span className="flex items-center">
                      <ClockIcon className="h-3 w-3 mr-1" />
                      {formatDate(notification.created_at)}
                    </span>
                    
                    <span className="flex items-center">
                      <EyeIcon className="h-3 w-3 mr-1" />
                      {notification.view_count} visualizações
                    </span>
                    
                    {notification.click_count > 0 && (
                      <span className="flex items-center">
                        <LinkIcon className="h-3 w-3 mr-1" />
                        {notification.click_count} cliques
                      </span>
                    )}
                    
                    {notification.expires_at && (
                      <span className="flex items-center">
                        <ClockIcon className="h-3 w-3 mr-1" />
                        Expira: {formatDate(notification.expires_at)}
                      </span>
                    )}
                  </div>
                </div>

                {/* Ações */}
                <div className="flex items-center space-x-2 ml-4">
                  {/* Botão de ação (se tiver URL) */}
                  {notification.action_url && (
                    <button
                      onClick={() => handleActionClick(notification)}
                      className="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded"
                      title="Abrir link"
                    >
                      <LinkIcon className="h-4 w-4" />
                    </button>
                  )}

                  {/* Marcar como lida */}
                  {!notification.is_read && (
                    <button
                      onClick={() => onMarkAsRead([notification.id])}
                      className="p-1 text-green-600 hover:text-green-800 hover:bg-green-100 rounded"
                      title="Marcar como lida"
                    >
                      <CheckIcon className="h-4 w-4" />
                    </button>
                  )}

                  {/* Deletar */}
                  <button
                    onClick={() => onDelete([notification.id])}
                    className="p-1 text-red-600 hover:text-red-800 hover:bg-red-100 rounded"
                    title="Deletar notificação"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
