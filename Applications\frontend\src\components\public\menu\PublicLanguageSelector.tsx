'use client';

import { Fragment, useState } from 'react';
import Image from 'next/image';
import { Menu, Transition } from '@headlessui/react';
import { clsx } from 'clsx';

const languages = [
  { code: 'pt', name: 'Português', flagPath: '/country_flag/svg/brazil--3747.svg' },
  { code: 'en', name: 'English', flagPath: '/country_flag/svg/united-states--3718.svg' },
  { code: 'es', name: 'Español', flagPath: '/country_flag/svg/spain--3619.svg' },
  { code: 'fr', name: 'Français', flagPath: '/country_flag/svg/france--3686.svg' },
];

interface PublicLanguageSelectorProps {
  currentLanguage?: string;
  onLanguageChange?: (languageCode: string) => void;
  variant?: 'header' | 'floating';
}

export function PublicLanguageSelector({ 
  currentLanguage = 'pt',
  onLanguageChange,
  variant = 'header'
}: PublicLanguageSelectorProps) {
  const [selectedLanguage, setSelectedLanguage] = useState(
    languages.find(lang => lang.code === currentLanguage) || languages[0]
  );

  const handleLanguageChange = (language: typeof languages[0]) => {
    setSelectedLanguage(language);
    onLanguageChange?.(language.code);
    
    // Update URL with new language parameter
    const url = new URL(window.location.href);
    url.searchParams.set('lang', language.code);
    window.history.pushState({}, '', url.toString());
  };

  const buttonClasses = variant === 'header'
    ? "w-10 h-10 bg-white/20 backdrop-blur-md border border-white/30 rounded-full hover:bg-white/30 transition-all duration-300 hover:scale-110 shadow-lg overflow-hidden"
    : "w-12 h-12 bg-white/90 backdrop-blur-sm border border-gray-200 rounded-full hover:bg-white transition-all duration-300 hover:scale-110 shadow-lg overflow-hidden";

  return (
    <Menu as="div" className="relative z-50">
      <Menu.Button className={buttonClasses}>
        <span className="sr-only">Selecionar idioma - {selectedLanguage.name}</span>
        <div className="relative w-full h-full">
          <Image
            src={selectedLanguage.flagPath}
            alt={`Bandeira ${selectedLanguage.name}`}
            fill
            className="object-cover"
            onError={(e) => {
              console.log('Erro ao carregar bandeira:', selectedLanguage.flagPath);
              // Fallback para ícone de idioma se a imagem não carregar
              e.currentTarget.outerHTML = `<div class="w-full h-full flex items-center justify-center bg-gray-200 rounded-full"><svg class="w-6 h-6 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path></svg></div>`;
            }}
          />
        </div>
      </Menu.Button>

      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <Menu.Items className="absolute left-0 z-50 mt-2 w-48 origin-top-left bg-white/95 backdrop-blur-lg rounded-xl py-2 shadow-xl border border-gray-200/50 focus:outline-none">
          <div className="px-3 py-2 text-xs font-semibold text-gray-600 border-b border-gray-200/50">
            Selecionar Idioma
          </div>
          {languages.map((language) => (
            <Menu.Item key={language.code}>
              {({ active }) => (
                <button
                  onClick={() => handleLanguageChange(language)}
                  className={clsx(
                    'flex w-full items-center gap-3 px-3 py-2 text-sm transition-colors duration-200',
                    active ? 'bg-orange-50' : '',
                    selectedLanguage.code === language.code
                      ? 'text-orange-600 font-medium bg-orange-50/50'
                      : 'text-gray-900'
                  )}
                >
                  <div className="relative w-6 h-6 rounded-sm overflow-hidden flex-shrink-0">
                    <Image
                      src={language.flagPath}
                      alt={`Bandeira ${language.name}`}
                      fill
                      className="object-cover"
                      onError={(e) => {
                        console.log('Erro ao carregar bandeira:', language.flagPath);
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                  </div>
                  <span>{language.name}</span>
                  {selectedLanguage.code === language.code && (
                    <span className="ml-auto text-orange-600">✓</span>
                  )}
                </button>
              )}
            </Menu.Item>
          ))}
        </Menu.Items>
      </Transition>
    </Menu>
  );
}
