'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  TruckIcon,
  DocumentTextIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { formatCurrency } from '@/lib/utils';

interface Order {
  id: string;
  order_number: string;
  status: 'pending' | 'approved' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  total_amount: number;
  created_at: string;
  estimated_delivery: string;
  supplier_name: string;
  items_count: number;
  requires_approval: boolean;
}

// Mock data - replace with real API call
const mockOrders: Order[] = [
  {
    id: '1',
    order_number: 'ORD-2024-001',
    status: 'delivered',
    total_amount: 2450.00,
    created_at: '2024-01-15T10:30:00Z',
    estimated_delivery: '2024-01-20T00:00:00Z',
    supplier_name: 'TechSupply Pro',
    items_count: 5,
    requires_approval: false
  },
  {
    id: '2',
    order_number: 'ORD-2024-002',
    status: 'shipped',
    total_amount: 1890.50,
    created_at: '2024-01-18T14:15:00Z',
    estimated_delivery: '2024-01-25T00:00:00Z',
    supplier_name: 'Office Solutions',
    items_count: 3,
    requires_approval: false
  },
  {
    id: '3',
    order_number: 'ORD-2024-003',
    status: 'pending',
    total_amount: 5200.00,
    created_at: '2024-01-20T09:45:00Z',
    estimated_delivery: '2024-01-30T00:00:00Z',
    supplier_name: 'Industrial Equipment Co',
    items_count: 2,
    requires_approval: true
  }
];

export function OrderHistory() {
  const [orders] = useState<Order[]>(mockOrders);
  const [activeTab, setActiveTab] = useState('all');

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <ClockIcon className="h-4 w-4" />;
      case 'approved':
      case 'processing':
        return <ClockIcon className="h-4 w-4" />;
      case 'shipped':
        return <TruckIcon className="h-4 w-4" />;
      case 'delivered':
        return <CheckCircleIcon className="h-4 w-4" />;
      case 'cancelled':
        return <XCircleIcon className="h-4 w-4" />;
      default:
        return <ClockIcon className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'shipped':
        return 'bg-purple-100 text-purple-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Pendente';
      case 'approved':
        return 'Aprovado';
      case 'processing':
        return 'Processando';
      case 'shipped':
        return 'Enviado';
      case 'delivered':
        return 'Entregue';
      case 'cancelled':
        return 'Cancelado';
      default:
        return status;
    }
  };

  const filteredOrders = orders.filter(order => {
    switch (activeTab) {
      case 'pending':
        return order.status === 'pending';
      case 'active':
        return ['approved', 'processing', 'shipped'].includes(order.status);
      case 'completed':
        return ['delivered', 'cancelled'].includes(order.status);
      default:
        return true;
    }
  });

  const handleViewOrder = (orderId: string) => {
    // Navigate to order details
    window.location.href = `/dashboard/tshop/orders/${orderId}`;
  };

  const handleReorder = (orderId: string) => {
    // Add order items to cart
    console.log('Reordering:', orderId);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <DocumentTextIcon className="h-5 w-5 mr-2" />
            Histórico de Pedidos B2B
          </CardTitle>
          <CardDescription>
            Acompanhe todos os seus pedidos e solicitações de cotação
          </CardDescription>
        </CardHeader>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="all">Todos</TabsTrigger>
          <TabsTrigger value="pending">Pendentes</TabsTrigger>
          <TabsTrigger value="active">Ativos</TabsTrigger>
          <TabsTrigger value="completed">Concluídos</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          {filteredOrders.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">
                  {activeTab === 'all' 
                    ? 'Nenhum pedido encontrado' 
                    : `Nenhum pedido ${activeTab === 'pending' ? 'pendente' : activeTab === 'active' ? 'ativo' : 'concluído'} encontrado`
                  }
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredOrders.map((order) => (
                <Card key={order.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="space-y-3">
                        {/* Order Header */}
                        <div className="flex items-center space-x-3">
                          <h3 className="font-semibold text-lg">{order.order_number}</h3>
                          <Badge className={getStatusColor(order.status)}>
                            <div className="flex items-center space-x-1">
                              {getStatusIcon(order.status)}
                              <span>{getStatusText(order.status)}</span>
                            </div>
                          </Badge>
                          {order.requires_approval && (
                            <Badge variant="outline" className="text-yellow-600 border-yellow-600">
                              Requer Aprovação
                            </Badge>
                          )}
                        </div>

                        {/* Order Details */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          <div>
                            <p className="text-gray-500">Fornecedor</p>
                            <p className="font-medium">{order.supplier_name}</p>
                          </div>
                          <div>
                            <p className="text-gray-500">Data do Pedido</p>
                            <p className="font-medium">
                              {new Date(order.created_at).toLocaleDateString('pt-BR')}
                            </p>
                          </div>
                          <div>
                            <p className="text-gray-500">Entrega Estimada</p>
                            <p className="font-medium">
                              {new Date(order.estimated_delivery).toLocaleDateString('pt-BR')}
                            </p>
                          </div>
                        </div>

                        {/* Order Summary */}
                        <div className="flex items-center space-x-6 text-sm">
                          <div>
                            <span className="text-gray-500">{order.items_count} itens</span>
                          </div>
                          <div>
                            <span className="text-gray-500">Total: </span>
                            <span className="font-bold text-lg text-green-600">
                              {formatCurrency(order.total_amount)}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex flex-col space-y-2">
                        <Button
                          onClick={() => handleViewOrder(order.id)}
                          size="sm"
                          variant="outline"
                        >
                          <EyeIcon className="h-4 w-4 mr-2" />
                          Ver Detalhes
                        </Button>
                        
                        {order.status === 'delivered' && (
                          <Button
                            onClick={() => handleReorder(order.id)}
                            size="sm"
                            variant="outline"
                          >
                            Pedir Novamente
                          </Button>
                        )}
                      </div>
                    </div>

                    {/* Progress Bar for Active Orders */}
                    {['approved', 'processing', 'shipped'].includes(order.status) && (
                      <div className="mt-4">
                        <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
                          <span>Aprovado</span>
                          <span>Processando</span>
                          <span>Enviado</span>
                          <span>Entregue</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{
                              width: order.status === 'approved' ? '25%' : 
                                     order.status === 'processing' ? '50%' : 
                                     order.status === 'shipped' ? '75%' : '100%'
                            }}
                          ></div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
