# General - Websocket

**Categoria:** General
**<PERSON><PERSON><PERSON><PERSON>:** Websocket
**Total de Endpoints:** 1
**Gerado em:** 08/06/2025, 17:26:01

## 📋 Endpoints

- [GET /ws-test](#get-ws-test) - Websocket Test

## 🔗 Endpoints Detalhados

### GET /ws-test {#get-ws-test}

**Resumo:** Websocket Test
**Descrição:** Endpoint para testar se o WebSocket está configurado.

**🔐 Autenticação:** Não requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/ws-test"
```

---
