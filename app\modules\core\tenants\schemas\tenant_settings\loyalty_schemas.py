"""
Loyalty specific schemas for tenant settings.
"""

from typing import Optional, Dict, Any

from pydantic import BaseModel, Field


class LoyaltySettingsUpdate(BaseModel):
    """Schema for updating loyalty settings."""
    
    loyalty_enabled: Optional[bool] = Field(
        None, 
        description="Enable loyalty system"
    )
    loyalty_config: Optional[Dict[str, Any]] = Field(
        None, 
        description="Loyalty system configuration"
    )
