'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import {
  ClockIcon,
  MapPinIcon,
  PhoneIcon,
  GlobeAltIcon,
  StarIcon,
  HeartIcon,
  ShareIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { PublicLanguageSelector } from './PublicLanguageSelector';
import { PublicUserMenu } from './PublicUserMenu';
import { CallButtons } from './CallButtons';

interface RestaurantHeaderProps {
  restaurantName: string;
  menuName: string;
  menuDescription?: string;
  isScheduleEnabled?: boolean;
  tableNumber?: string;
  language?: string;
  logoUrl?: string;
  coverImageUrl?: string;
  restaurantInfo?: {
    rating?: number;
    totalReviews?: number;
    address?: string;
    phone?: string;
    website?: string;
    isOpen?: boolean;
    openingHours?: string;
    facebook_url?: string;
    instagram_url?: string;
    twitter_url?: string;
    youtube_url?: string;
    linkedin_url?: string;
    tiktok_url?: string;
  };
}

export function RestaurantHeader({
  restaurantName,
  menuName,
  menuDescription,
  isScheduleEnabled,
  tableNumber,
  language,
  logoUrl,
  coverImageUrl,
  restaurantInfo
}: RestaurantHeaderProps) {
  const [isFavorite, setIsFavorite] = useState(false);

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `${restaurantName} - ${menuName}`,
          text: menuDescription || `Confira o menu do ${restaurantName}`,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Erro ao compartilhar:', error);
      }
    } else {
      navigator.clipboard.writeText(window.location.href);
    }
  };

  return (
    <>
      {/* Header Principal - Redesigned */}
      <div className="relative max-h-[200px] h-[200px] overflow-visible">
        {/* Background com imagem de capa ou gradiente gastronômico */}
        {coverImageUrl ? (
          <div className="absolute inset-0">
            <Image
              src={coverImageUrl}
              alt={`${restaurantName} ambiente`}
              fill
              className="object-cover blur-sm"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-black/10" />
          </div>
        ) : (
          <div className="absolute inset-0">
            {/* Imagem de fundo padrão gastronômica */}
            <div className="absolute inset-0">
              <div
                className="absolute inset-0 blur-sm"
                style={{
                  backgroundImage: `url("https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80")`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  backgroundRepeat: 'no-repeat'
                }}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-black/10" />
            </div>

          </div>
        )}

        {/* Elementos flutuantes gastronômicos */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-orange-400/10 rounded-full blur-xl animate-float" />
        <div className="absolute bottom-20 right-10 w-24 h-24 bg-amber-500/10 rounded-full blur-xl animate-float-delayed" />
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-red-400/10 rounded-full blur-lg animate-pulse" />

        <div className="relative max-w-6xl mx-auto px-6 py-6 h-full">
          {/* Top Navigation Bar */}
          <div className="flex items-center justify-between mb-6">
            {/* Language Selector - Left */}
            <div className="flex items-center">
              <PublicLanguageSelector
                currentLanguage={language}
                variant="header"
              />
            </div>

            {/* User Menu and Actions - Right */}
            <div className="flex items-center gap-3">
              {/* Favorite and Share buttons */}
              <button
                onClick={() => setIsFavorite(!isFavorite)}
                className="p-2 bg-white/20 backdrop-blur-md border border-white/30 rounded-full hover:bg-white/30 transition-all duration-300 hover:scale-110 shadow-lg"
              >
                <HeartIcon className={`h-5 w-5 ${isFavorite ? 'fill-red-500 text-red-500' : 'text-white'}`} />
              </button>
              <button
                onClick={handleShare}
                className="p-2 bg-white/20 backdrop-blur-md border border-white/30 rounded-full hover:bg-white/30 transition-all duration-300 hover:scale-110 shadow-lg"
              >
                <ShareIcon className="h-5 w-5 text-white" />
              </button>

              {/* User Menu */}
              <PublicUserMenu
                variant="header"
                userLoyalty={{
                  points: 1250,
                  title: "VIP",
                  level: "Gold"
                }}
              />
            </div>
          </div>



          {/* Logo e Nome do Restaurante - Logo extends below header */}
          <div className="flex items-start justify-between relative">
            {/* Logo and Restaurant Info - Left Side */}
            <div className="flex items-start gap-6 relative">
              {logoUrl ? (
                <div className="relative z-30">
                  <div className="w-32 h-32 sm:w-40 sm:h-40 rounded-full bg-white/95 backdrop-blur-sm border-4 border-white/70 shadow-2xl p-3 transform translate-y-8">
                    <div className="relative w-full h-full rounded-full overflow-hidden">
                      <Image
                        src={logoUrl}
                        alt={`${restaurantName} logo`}
                        fill
                        className="object-cover"
                      />
                    </div>
                  </div>
                  <div className="absolute -inset-4 rounded-full border-2 border-orange-300/60 animate-pulse transform translate-y-8"></div>
                  <div className="absolute -inset-6 rounded-full border border-amber-300/40 animate-ping transform translate-y-8"></div>
                </div>
              ) : (
                <div className="relative z-30">
                  <div className="w-32 h-32 sm:w-40 sm:h-40 rounded-full bg-gradient-to-br from-orange-500 via-red-500 to-amber-500 shadow-2xl flex items-center justify-center border-4 border-white/70 transform translate-y-8">
                    <div className="text-white text-4xl sm:text-5xl font-bold">
                      🍽️
                    </div>
                  </div>
                  <div className="absolute -inset-4 rounded-full border-2 border-orange-300/60 animate-pulse transform translate-y-8"></div>
                  <div className="absolute -inset-6 rounded-full border border-amber-300/40 animate-ping transform translate-y-8"></div>
                </div>
              )}

              <div className="text-left pt-4">
                <h1 className="text-2xl sm:text-3xl font-bold mb-1 tracking-tight text-white drop-shadow-lg">
                  {restaurantName}
                </h1>
                <h2 className="text-lg sm:text-xl font-medium text-orange-100 drop-shadow-md mb-2 pl-2.5">
                  {menuName}
                </h2>

                {/* Restaurant Social Media */}
                <div className="flex items-center gap-3 mb-2 pl-7 relative z-40">
                  <a
                    href={restaurantInfo?.facebook_url || "#"}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="relative group p-2 bg-white/20 backdrop-blur-md border border-white/30 rounded-full hover:bg-white/30 transition-all duration-300 hover:scale-110 shadow-lg"
                    aria-label="Facebook do restaurante"
                  >
                    <svg className="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
                    </svg>
                    {/* Tooltip */}
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black/80 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                      Facebook
                    </div>
                  </a>

                  <a
                    href={restaurantInfo?.instagram_url || "#"}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="relative group p-2 bg-white/20 backdrop-blur-md border border-white/30 rounded-full hover:bg-white/30 transition-all duration-300 hover:scale-110 shadow-lg"
                    aria-label="Instagram do restaurante"
                  >
                    <svg className="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path fillRule="evenodd" d="M12.017 0C8.396 0 7.989.013 7.041.048 6.094.082 5.52.204 5.036.388a3.892 3.892 0 0 0-1.407.923A3.892 3.892 0 0 0 2.706 2.72c-.184.484-.306 1.058-.34 2.005C2.331 5.673 2.318 6.08 2.318 9.701v4.598c0 3.621.013 4.028.048 4.976.034.947.156 1.521.34 2.005a3.892 3.892 0 0 0 .923 1.407 3.892 3.892 0 0 0 1.407.923c.484.184 1.058.306 2.005.34.948.035 1.355.048 4.976.048h4.598c3.621 0 4.028-.013 4.976-.048.947-.034 1.521-.156 2.005-.34a3.892 3.892 0 0 0 1.407-.923 3.892 3.892 0 0 0 .923-1.407c.184-.484.306-1.058.34-2.005.035-.948.048-1.355.048-4.976V9.701c0-3.621-.013-4.028-.048-4.976-.034-.947-.156-1.521-.34-2.005a3.892 3.892 0 0 0-.923-1.407A3.892 3.892 0 0 0 19.982 2.72c-.484-.184-1.058-.306-2.005-.34C17.029.013 16.622 0 13.001 0h-1.984zm-.717 1.442h2.004c3.136 0 3.506.012 4.74.048.856.039 1.32.181 1.629.3.41.16.703.35.982.629.28.28.469.572.629.982.119.309.261.773.3 1.629.036 1.234.048 1.604.048 4.74v2.004c0 3.136-.012 3.506-.048 4.74-.039.856-.181 1.32-.3 1.629-.16.41-.35.703-.629.982-.28.28-.572.469-.982.629-.309.119-.773.261-1.629.3-1.234.036-1.604.048-4.74.048h-2.004c-3.136 0-3.506-.012-4.74-.048-.856-.039-1.32-.181-1.629-.3a2.65 2.65 0 0 1-.982-.629 2.65 2.65 0 0 1-.629-.982c-.119-.309-.261-.773-.3-1.629-.036-1.234-.048-1.604-.048-4.74V9.701c0-3.136.012-3.506.048-4.74.039-.856.181-1.32.3-1.629.16-.41.35-.703.629-.982.28-.28.572-.469.982-.629.309-.119.773-.261 1.629-.3 1.234-.036 1.604-.048 4.74-.048z" clipRule="evenodd" />
                      <path fillRule="evenodd" d="M12 5.838a6.162 6.162 0 1 0 0 12.324 6.162 6.162 0 0 0 0-12.324zM12 16a4 4 0 1 1 0-8 4 4 0 0 1 0 8zm6.406-11.845a1.44 1.44 0 1 0 0 2.881 1.44 1.44 0 0 0 0-2.881z" clipRule="evenodd" />
                    </svg>
                    {/* Tooltip */}
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black/80 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                      Instagram
                    </div>
                  </a>

                  <a
                    href={restaurantInfo?.twitter_url || "#"}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="relative group p-2 bg-white/20 backdrop-blur-md border border-white/30 rounded-full hover:bg-white/30 transition-all duration-300 hover:scale-110 shadow-lg"
                    aria-label="Twitter do restaurante"
                  >
                    <svg className="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                    </svg>
                    {/* Tooltip */}
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black/80 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                      Twitter
                    </div>
                  </a>

                  <a
                    href={restaurantInfo?.youtube_url || "#"}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="relative group p-2 bg-white/20 backdrop-blur-md border border-white/30 rounded-full hover:bg-white/30 transition-all duration-300 hover:scale-110 shadow-lg"
                    aria-label="YouTube do restaurante"
                  >
                    <svg className="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path fillRule="evenodd" d="M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418 4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0 1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998 5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z" clipRule="evenodd" />
                    </svg>
                    {/* Tooltip */}
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black/80 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                      YouTube
                    </div>
                  </a>

                  <a
                    href={restaurantInfo?.linkedin_url || "#"}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="relative group p-2 bg-white/20 backdrop-blur-md border border-white/30 rounded-full hover:bg-white/30 transition-all duration-300 hover:scale-110 shadow-lg"
                    aria-label="LinkedIn do restaurante"
                  >
                    <svg className="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path fillRule="evenodd" d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" clipRule="evenodd" />
                    </svg>
                    {/* Tooltip */}
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black/80 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                      LinkedIn
                    </div>
                  </a>

                  <a
                    href={restaurantInfo?.tiktok_url || "#"}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="relative group p-2 bg-white/20 backdrop-blur-md border border-white/30 rounded-full hover:bg-white/30 transition-all duration-300 hover:scale-110 shadow-lg"
                    aria-label="TikTok do restaurante"
                  >
                    <svg className="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
                    </svg>
                    {/* Tooltip */}
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black/80 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                      TikTok
                    </div>
                  </a>
                </div>

                {/* Call Buttons - Below social media */}
                <div className="mt-2">
                  <CallButtons
                    phone={restaurantInfo?.phone}
                    whatsapp={restaurantInfo?.phone}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Compact Info Section */}
          <div className="mt-6 flex flex-wrap items-center justify-center gap-3">
            {/* Table Number */}
            {tableNumber && (
              <div className="inline-flex items-center gap-2 px-3 py-1.5 bg-white/80 backdrop-blur-sm rounded-full text-gray-700 text-sm font-medium border border-orange-200/50">
                <svg className="w-4 h-4 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
                Mesa {tableNumber}
              </div>
            )}

            {/* Rating */}
            {restaurantInfo?.rating && (
              <div className="flex items-center gap-2 px-3 py-1.5 bg-white/80 backdrop-blur-sm rounded-full border border-yellow-200/50 shadow-sm">
                <StarIconSolid className="h-4 w-4 text-yellow-500" />
                <span className="text-sm font-bold text-yellow-600">
                  {restaurantInfo.rating.toFixed(1)}
                </span>
                {restaurantInfo.totalReviews && (
                  <span className="text-gray-600 text-xs">
                    ({restaurantInfo.totalReviews})
                  </span>
                )}
              </div>
            )}

            {/* Status de funcionamento */}
            {restaurantInfo?.isOpen !== undefined && (
              <div className={`flex items-center gap-2 px-3 py-1.5 backdrop-blur-sm rounded-full border shadow-sm ${
                restaurantInfo.isOpen
                  ? 'bg-green-100/80 text-green-800 border-green-200/50'
                  : 'bg-red-100/80 text-red-800 border-red-200/50'
              }`}>
                <div className={`w-2 h-2 rounded-full ${
                  restaurantInfo.isOpen ? 'bg-green-500 animate-pulse' : 'bg-red-500'
                }`} />
                <span className="text-xs font-medium">
                  {restaurantInfo.isOpen ? 'Aberto' : 'Fechado'}
                </span>
              </div>
            )}
          </div>
          {/* Additional Restaurant Info - Compact */}
          {restaurantInfo && (
            <div className="mt-4 flex flex-wrap justify-center gap-2 text-xs">
              {restaurantInfo.address && (
                <div className="flex items-center gap-1 px-2 py-1 bg-white/60 backdrop-blur-sm rounded-full border border-gray-200/50">
                  <MapPinIcon className="h-3 w-3 text-orange-500" />
                  <span className="text-gray-700 max-w-xs truncate">{restaurantInfo.address}</span>
                </div>
              )}
              {restaurantInfo.website && (
                <div className="flex items-center gap-1 px-2 py-1 bg-white/60 backdrop-blur-sm rounded-full border border-gray-200/50">
                  <GlobeAltIcon className="h-3 w-3 text-orange-500" />
                  <span className="text-gray-700">{restaurantInfo.website}</span>
                </div>
              )}
            </div>
          )}


        </div>
      </div>


    </>
  );
}
