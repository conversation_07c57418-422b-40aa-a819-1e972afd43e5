import uuid
from typing import List, Optional

from sqlalchemy import select, update, delete
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload

from . import models, schemas
from app.models.tenant import Tenant  # noqa: F401

# ============================================
# Feature Service Functions
# ============================================


async def create_feature(db: AsyncSession, feature_in: schemas.FeatureCreate) -> models.Feature:
    """Cria uma nova feature global."""
    db_feature = models.Feature(**feature_in.dict())
    db.add(db_feature)
    await db.commit()
    await db.refresh(db_feature)
    return db_feature


async def get_feature(db: AsyncSession, feature_id: int) -> Optional[models.Feature]:
    """Obtém uma feature pelo ID."""
    result = await db.execute(select(models.Feature).filter(models.Feature.id == feature_id))
    return result.scalars().first()


async def get_feature_by_key(db: AsyncSession, feature_key: str) -> Optional[models.Feature]:
    """Obtém uma feature pela sua chave única."""
    result = await db.execute(select(models.Feature).filter(models.Feature.key == feature_key))
    return result.scalars().first()


async def get_features(db: AsyncSession, skip: int = 0, limit: int = 100) -> List[models.Feature]:
    """Lista todas as features globais."""
    result = await db.execute(select(models.Feature).offset(skip).limit(limit))
    return result.scalars().all()


async def update_feature(
    db: AsyncSession, db_feature: models.Feature, feature_in: schemas.FeatureUpdate
) -> models.Feature:
    """Atualiza uma feature existente."""
    update_data = feature_in.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_feature, field, value)
    await db.commit()
    await db.refresh(db_feature)
    return db_feature


async def delete_feature(db: AsyncSession, db_feature: models.Feature) -> Optional[models.Feature]:
    """Deleta uma feature (usar com cautela)."""
    # Adicionar verificação se a feature está em uso por algum plano?
    await db.delete(db_feature)
    await db.commit()
    return db_feature  # Retorna o objeto deletado (sem ID após commit talvez)


# ============================================
# Plan Service Functions
# ============================================


async def create_plan(db: AsyncSession, plan_in: schemas.PlanCreate) -> models.Plan:
    """Cria um novo plano."""
    db_plan = models.Plan(**plan_in.dict())
    db.add(db_plan)
    await db.commit()
    await db.refresh(db_plan)
    return db_plan


async def get_plan(db: AsyncSession, plan_id: int) -> Optional[models.Plan]:
    """Obtém um plano pelo ID, incluindo suas features vinculadas."""
    result = await db.execute(
        select(models.Plan)
        .options(
            selectinload(models.Plan.features_links).joinedload(models.PlanFeatureLink.feature)
        )
        .filter(models.Plan.id == plan_id)
    )
    return result.scalars().unique().first()


async def get_plans(
    db: AsyncSession, skip: int = 0, limit: int = 100, is_active: Optional[bool] = None
) -> List[models.Plan]:
    """Lista planos, opcionalmente filtrando por ativos/inativos."""
    query = select(models.Plan).order_by(models.Plan.display_order, models.Plan.name)
    if is_active is not None:
        query = query.filter(models.Plan.is_active == is_active)
    result = await db.execute(query.offset(skip).limit(limit))
    return result.scalars().all()


async def update_plan(
    db: AsyncSession, db_plan: models.Plan, plan_in: schemas.PlanUpdate
) -> models.Plan:
    """Atualiza um plano existente."""
    update_data = plan_in.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_plan, field, value)
    await db.commit()
    await db.refresh(db_plan)
    return db_plan


async def delete_plan(db: AsyncSession, db_plan: models.Plan) -> Optional[models.Plan]:
    """Desativa um plano (soft delete)."""
    db_plan.is_active = False
    await db.commit()
    await db.refresh(db_plan)
    return db_plan


# ============================================
# Plan Feature Link Service Functions
# ============================================


async def link_feature_to_plan(
    db: AsyncSession, plan_id: int, feature_id: int, value: str
) -> models.PlanFeatureLink:
    """Vincula uma feature a um plano com um valor específico."""
    # Verificar se o link já existe? Ou deixar o DB lançar erro de PK?
    db_link = models.PlanFeatureLink(plan_id=plan_id, feature_id=feature_id, value=value)
    db.add(db_link)
    await db.commit()
    await db.refresh(db_link)
    return db_link


async def update_plan_feature_link(
    db: AsyncSession, plan_id: int, feature_id: int, value: str
) -> Optional[models.PlanFeatureLink]:
    """Atualiza o valor de uma feature vinculada a um plano."""
    result = await db.execute(
        select(models.PlanFeatureLink).filter(
            models.PlanFeatureLink.plan_id == plan_id,
            models.PlanFeatureLink.feature_id == feature_id,
        )
    )
    db_link = result.scalars().first()
    if db_link:
        db_link.value = value
        await db.commit()
        await db.refresh(db_link)
    return db_link


async def unlink_feature_from_plan(db: AsyncSession, plan_id: int, feature_id: int) -> bool:
    """Desvincula uma feature de um plano."""
    result = await db.execute(
        delete(models.PlanFeatureLink).filter(
            models.PlanFeatureLink.plan_id == plan_id,
            models.PlanFeatureLink.feature_id == feature_id,
        )
    )
    await db.commit()
    return result.rowcount > 0


# ============================================
# Tenant Subscription Service Functions
# ============================================


async def get_tenant_subscription(
    db: AsyncSession, tenant_id: uuid.UUID, active_only: bool = True
) -> Optional[models.TenantSubscription]:
    """Obtém a assinatura de um tenant (geralmente a mais recente ou ativa)."""
    query = (
        select(models.TenantSubscription)
        .options(joinedload(models.TenantSubscription.plan))  # Carrega dados do plano junto
        .filter(models.TenantSubscription.tenant_id == tenant_id)
        .order_by(models.TenantSubscription.created_at.desc())  # Pega a mais recente
    )
    if active_only:
        # Considera ACTIVE e IN_TRIAL como ativas para acesso
        query = query.filter(
            models.TenantSubscription.status.in_(
                [
                    models.SubscriptionStatusEnum.ACTIVE,
                    models.SubscriptionStatusEnum.IN_TRIAL,
                ]
            )
        )

    result = await db.execute(query)
    return (
        result.scalars().first()
    )  # Retorna a mais recente ativa/em trial ou a mais recente qualquer


async def get_tenant_feature_value(
    db: AsyncSession, tenant_id: uuid.UUID, feature_key: str
) -> Optional[str]:
    """Obtém o valor bruto (string) de uma feature específica para a assinatura ativa do tenant."""
    subscription = await get_tenant_subscription(db, tenant_id=tenant_id, active_only=True)
    if not subscription or not subscription.plan:
        # Se não há assinatura ativa ou plano associado, a feature não está disponível com valor específico  # noqa: E501
        # Poderíamos retornar um valor padrão global aqui se fizesse sentido
        return None

    # Carrega as features do plano se não foram carregadas por get_tenant_subscription
    # (joinedload em get_tenant_subscription deveria ter carregado o plano, mas não as features do plano)  # noqa: E501
    # Precisamos garantir que plan.features_links esteja carregado
    # Reconsulta o plano com as features carregadas
    plan_with_features = await db.execute(
        select(models.Plan)
        .options(
            selectinload(models.Plan.features_links).joinedload(models.PlanFeatureLink.feature)
        )
        .filter(models.Plan.id == subscription.plan_id)
    )
    plan = plan_with_features.scalars().unique().first()

    if not plan:
        return None  # Plano não encontrado, embora a assinatura exista (estado inconsistente?)

    for link in plan.features_links:
        if link.feature.key == feature_key:
            return link.value

    # Se o link específico não foi encontrado no plano, a feature não tem
    # valor definido para este plano
    return None


async def tenant_has_feature(db: AsyncSession, tenant_id: uuid.UUID, feature_key: str) -> bool:
    """Verifica se uma feature booleana está habilitada para a assinatura ativa do tenant."""
    value_str = await get_tenant_feature_value(db, tenant_id, feature_key)
    # Considera habilitada se o valor for explicitamente "true" (case-insensitive)
    return value_str is not None and value_str.lower() == "true"


async def get_tenant_feature_limit(
    db: AsyncSession, tenant_id: uuid.UUID, feature_key: str
) -> Optional[int]:
    """Obtém o limite numérico de uma feature para a assinatura ativa do tenant."""
    value_str = await get_tenant_feature_value(db, tenant_id, feature_key)
    if value_str is None:
        return None  # Feature não definida ou sem valor para o plano

    # Tenta converter o valor para inteiro. Retorna None se falhar.
    try:
        return int(value_str)
    except (ValueError, TypeError):
        # Logar um erro aqui seria útil, pois indica um valor mal configurado para
        # uma feature numérica
        print(
            f"Error: Could not convert feature '{feature_key}' value '{value_str}' to integer for tenant {tenant_id}"  # noqa: E501
        )
        return None


async def assign_subscription_to_tenant(
    db: AsyncSession,
    tenant_id: uuid.UUID,
    subscription_details: schemas.TenantSubscriptionAdminCreate,
) -> models.TenantSubscription:
    """
    Cria ou atualiza a assinatura de um tenant (usado por Admin).
    Pode ser necessário desativar assinaturas antigas se uma nova for criada.
    """
    # Lógica para desativar/cancelar assinaturas antigas pode ser adicionada aqui
    db_subscription = models.TenantSubscription(tenant_id=tenant_id, **subscription_details.dict())
    db.add(db_subscription)
    await db.commit()
    await db.refresh(db_subscription)
    return db_subscription


async def update_tenant_subscription_status(
    db: AsyncSession,
    subscription_id: int,
    new_status: models.SubscriptionStatusEnum,
    reason: Optional[str] = None,
) -> Optional[models.TenantSubscription]:
    """Atualiza o status de uma assinatura específica (Admin)."""
    result = await db.execute(
        select(models.TenantSubscription).filter(models.TenantSubscription.id == subscription_id)
    )
    db_subscription = result.scalars().first()
    if db_subscription:
        from datetime import datetime  # noqa: E402

        update_data = {"status": new_status}
        if new_status == models.SubscriptionStatusEnum.CANCELLED:
            update_data["cancelled_at"] = datetime.utcnow()
            if reason:
                update_data["cancellation_reason"] = reason
        # Adicionar outras lógicas de status se necessário (ex: end_date)

        stmt = (
            update(models.TenantSubscription)
            .where(models.TenantSubscription.id == subscription_id)
            .values(**update_data)
            .execution_options(synchronize_session="fetch")
        )
        await db.execute(stmt)
        await db.commit()
        await db.refresh(db_subscription)  # Recarrega para obter valores atualizados
    return db_subscription


async def get_subscription_by_id(
    db: AsyncSession, subscription_id: int
) -> Optional[models.TenantSubscription]:
    """Obtém uma assinatura pelo seu ID."""
    result = await db.execute(
        select(models.TenantSubscription)
        .options(joinedload(models.TenantSubscription.plan))
        .filter(models.TenantSubscription.id == subscription_id)
    )
    return result.scalars().first()
