'use client';

import React, { useState, useRef, useCallback, useMemo } from 'react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverEvent,
  MouseSensor,
  TouchSensor,
} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  rectSortingStrategy,
} from '@dnd-kit/sortable';
import { Table } from '@/types/pos';
import DraggableTable from './DraggableTable';
import GridOverlay from './GridOverlay';
import {
  DEFAULT_GRID_CONFIG,
  CANVAS_DIMENSIONS,
  ZOOM_LEVELS,
  GridConfig,
  snapToGrid,
  isValidPosition,
  calculateFitZoom,
} from '@/utils/table/GridPatterns';

// ===============================
// INTERFACES
// ===============================

export interface LayoutCanvasProps {
  tables: Table[];
  selectedZone?: string;
  selectedTable?: Table | null;
  isEditing?: boolean;
  gridConfig?: Partial<GridConfig>;
  zoom?: number;
  canvasWidth?: number;
  canvasHeight?: number;
  onTableUpdate?: (table: Table) => void;
  onTableCreate?: (position: { x: number; y: number }) => void;
  onTableSelect?: (table: Table | null) => void;
  onTableEdit?: (table: Table) => void;
  onTableDelete?: (table: Table) => void;
  onZoomChange?: (zoom: number) => void;
  className?: string;
}

export interface CanvasPosition {
  x: number;
  y: number;
}

// ===============================
// COMPONENT
// ===============================

export default function LayoutCanvas({
  tables,
  selectedZone,
  selectedTable,
  isEditing = false,
  gridConfig = {},
  zoom = ZOOM_LEVELS.default,
  canvasWidth = CANVAS_DIMENSIONS.defaultWidth,
  canvasHeight = CANVAS_DIMENSIONS.defaultHeight,
  onTableUpdate,
  onTableCreate,
  onTableSelect,
  onTableEdit,
  onTableDelete,
  onZoomChange,
  className = ''
}: LayoutCanvasProps) {
  const canvasRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [draggedTable, setDraggedTable] = useState<Table | null>(null);

  // Merge grid config with defaults
  const finalGridConfig = useMemo((): GridConfig => ({
    ...DEFAULT_GRID_CONFIG,
    ...gridConfig,
  }), [gridConfig]);

  // Filter tables by zone (always required)
  const filteredTables = useMemo(() => {
    if (!selectedZone) return [];
    return tables.filter(table => table.zone === selectedZone);
  }, [tables, selectedZone]);



  // Configure drag sensors
  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 200,
        tolerance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Handle drag start
  const handleDragStart = useCallback((event: DragStartEvent) => {
    const { active } = event;
    const table = filteredTables.find(t => t.id === active.id);
    
    if (table) {
      setDraggedTable(table);
      setIsDragging(true);
    }
  }, [filteredTables]);

  // Handle drag end
  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, delta } = event;
    
    setIsDragging(false);
    setDraggedTable(null);

    if (!delta || (delta.x === 0 && delta.y === 0)) return;

    const table = filteredTables.find(t => t.id === active.id);
    if (!table || !onTableUpdate) return;

    // Calculate new position
    let newX = (table.position_x || 0) + delta.x;
    let newY = (table.position_y || 0) + delta.y;

    // Snap to grid if enabled
    if (finalGridConfig.snapToGrid) {
      newX = snapToGrid(newX, finalGridConfig.cellSize);
      newY = snapToGrid(newY, finalGridConfig.cellSize);
    }

    // Validate position bounds
    const tableWidth = table.width || 120;
    const tableHeight = table.height || 80;
    
    if (isValidPosition(newX, newY, canvasWidth - tableWidth, canvasHeight - tableHeight)) {
      const updatedTable: Table = {
        ...table,
        position_x: newX,
        position_y: newY,
      };
      
      onTableUpdate(updatedTable);
    }
  }, [filteredTables, onTableUpdate, finalGridConfig, canvasWidth, canvasHeight]);

  // Handle canvas click for creating new tables
  const handleCanvasClick = useCallback((event: React.MouseEvent) => {
    if (!onTableCreate || isEditing) return;

    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;

    // Calculate click position relative to canvas
    const x = (event.clientX - rect.left) / zoom;
    const y = (event.clientY - rect.top) / zoom;

    // Snap to grid if enabled
    const finalX = finalGridConfig.snapToGrid 
      ? snapToGrid(x, finalGridConfig.cellSize) 
      : x;
    const finalY = finalGridConfig.snapToGrid 
      ? snapToGrid(y, finalGridConfig.cellSize) 
      : y;

    // Validate position
    if (isValidPosition(finalX, finalY, canvasWidth - 120, canvasHeight - 80)) {
      onTableCreate({ x: finalX, y: finalY });
    }
  }, [onTableCreate, isEditing, zoom, finalGridConfig, canvasWidth, canvasHeight]);

  // Handle table selection
  const handleTableSelect = useCallback((table: Table) => {
    if (onTableSelect) {
      const newSelection = selectedTable?.id === table.id ? null : table;
      onTableSelect(newSelection);
    }
  }, [onTableSelect, selectedTable]);



  return (
    <div className={`relative bg-white rounded-lg border border-gray-200 overflow-hidden ${className}`}>
      {/* Canvas Container */}
      <div 
        ref={canvasRef}
        className="relative bg-gray-50 cursor-crosshair"
        style={{
          width: canvasWidth * zoom,
          height: canvasHeight * zoom,
          minWidth: CANVAS_DIMENSIONS.minWidth * zoom,
          minHeight: CANVAS_DIMENSIONS.minHeight * zoom,
        }}
        onClick={handleCanvasClick}
      >
        {/* Grid Overlay */}
        <GridOverlay
          width={canvasWidth}
          height={canvasHeight}
          zoom={zoom}
          gridConfig={finalGridConfig}
        />

        {/* Drag & Drop Context */}
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
        >
          <SortableContext
            items={filteredTables.map(t => t.id)}
            strategy={rectSortingStrategy}
          >
            {/* Tables */}
            {filteredTables.map((table) => (
              <div
                key={table.id}
                className="absolute"
                style={{
                  left: (table.position_x || 0) * zoom,
                  top: (table.position_y || 0) * zoom,
                  transform: `scale(${zoom})`,
                  transformOrigin: 'top left',
                }}
              >
                <DraggableTable
                  table={table}
                  isSelected={selectedTable?.id === table.id}
                  isDragging={draggedTable?.id === table.id}
                  isEditing={isEditing && selectedTable?.id === table.id}
                  scale={1} // Scale is handled by container
                  onSelect={handleTableSelect}
                  onEdit={onTableEdit}
                  onDelete={onTableDelete}
                />
              </div>
            ))}
          </SortableContext>
        </DndContext>

        {/* Drop Zone Indicator */}
        {!isEditing && filteredTables.length === 0 && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center text-gray-400 pointer-events-none">
              <div className="text-lg font-medium mb-2">Canvas Vazio</div>
              <div className="text-sm">Clique para criar uma nova mesa</div>
              <div className="text-xs mt-1">
                {finalGridConfig.snapToGrid && 'Snap-to-grid ativado'}
              </div>
            </div>
          </div>
        )}

        {/* Zone Indicator */}
        {selectedZone && (
          <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2 shadow-sm border">
            <div className="text-sm font-medium text-gray-900">
              Zona: {selectedZone}
            </div>
            <div className="text-xs text-gray-500">
              {filteredTables.length} mesa{filteredTables.length !== 1 ? 's' : ''}
            </div>
          </div>
        )}

        {/* Zoom Indicator */}
        <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2 shadow-sm border">
          <div className="text-xs text-gray-600">
            Zoom: {Math.round(zoom * 100)}%
          </div>
        </div>
      </div>
    </div>
  );
}
