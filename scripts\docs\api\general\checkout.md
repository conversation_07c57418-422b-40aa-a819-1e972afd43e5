# General - Checkout

**Categoria:** General
**<PERSON><PERSON><PERSON><PERSON>:** Checkout
**Total de Endpoints:** 6
**<PERSON><PERSON><PERSON> em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [POST /api/checkout/initiate](#post-apicheckoutinitiate) - Initiate Checkout
- [POST /api/checkout/shipping/quote](#post-apicheckoutshippingquote) - Get Shipping Quote
- [GET /api/checkout/{checkout_id}](#get-apicheckoutcheckout-id) - Get Checkout Session
- [PUT /api/checkout/{checkout_id}](#put-apicheckoutcheckout-id) - Update Checkout Session
- [DELETE /api/checkout/{checkout_id}/cancel](#delete-apicheckoutcheckout-idcancel) - Cancel Checkout
- [POST /api/checkout/{checkout_id}/complete](#post-apicheckoutcheckout-idcomplete) - Complete Checkout

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### CheckoutCompleteRequest

**Descrição:** Schema para completar checkout.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `payment_method` | PaymentMethod | ✅ | Método de pagamento |
| `payment_provider` | unknown | ❌ | Provedor de pagamento |
| `payment_token` | unknown | ❌ | Token de pagamento |
| `save_payment_method` | boolean | ❌ | Salvar método de pagamento |

### CheckoutInitiateRequest

**Descrição:** Schema para iniciar processo de checkout.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `cart_id` | string | ✅ | ID do carrinho |
| `market_context` | string | ❌ | Contexto do mercado |
| `shipping_address` | unknown | ❌ | Endereço de entrega |
| `billing_address` | unknown | ❌ | Endereço de cobrança |
| `shipping_method` | unknown | ❌ | Método de entrega preferido |

### CheckoutResponse

**Descrição:** Schema de resposta padrão para operações de checkout.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `success` | boolean | ✅ | Sucesso da operação |
| `message` | string | ✅ | Mensagem de retorno |
| `checkout_session` | unknown | ❌ | Dados da sessão de checkout |
| `payment_url` | unknown | ❌ | URL para pagamento (se aplicável) |
| `order_id` | unknown | ❌ | ID do pedido criado (se completado) |

### CheckoutSessionRead

**Descrição:** Schema para leitura de sessão de checkout.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `market_context` | string | ❌ | Contexto do mercado (b2b/b2c) |
| `shipping_method` | unknown | ❌ | Método de entrega |
| `payment_method` | unknown | ❌ | Método de pagamento |
| `customer_notes` | unknown | ❌ | Observações do cliente |
| `special_instructions` | unknown | ❌ | Instruções especiais |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `cart_id` | string | ✅ | - |
| `user_id` | unknown | ✅ | - |
| `order_id` | unknown | ✅ | - |
| `status` | CheckoutStatus | ✅ | - |
| `shipping_address` | unknown | ✅ | - |
| `billing_address` | unknown | ✅ | - |
| `subtotal` | string | ✅ | - |
| `tax_amount` | string | ✅ | - |
| `discount_amount` | string | ✅ | - |
| `shipping_cost` | string | ✅ | - |
| `total_amount` | string | ✅ | - |
| `currency` | string | ✅ | - |
| `payment_provider` | unknown | ✅ | - |
| `payment_external_id` | unknown | ✅ | - |
| `payment_reference` | unknown | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `expires_at` | unknown | ✅ | - |
| `payment_confirmed_at` | unknown | ✅ | - |
| `completed_at` | unknown | ✅ | - |
| `is_expired` | boolean | ❌ | - |
| `is_payment_pending` | boolean | ❌ | - |
| `is_completed` | boolean | ❌ | - |

### CheckoutSessionUpdate

**Descrição:** Schema para atualização de sessão de checkout.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `shipping_address` | unknown | ❌ | Endereço de entrega atualizado |
| `billing_address` | unknown | ❌ | Endereço de cobrança atualizado |
| `shipping_method` | unknown | ❌ | Método de entrega atualizado |
| `payment_method` | unknown | ❌ | Método de pagamento atualizado |
| `customer_notes` | unknown | ❌ | Observações atualizadas |
| `special_instructions` | unknown | ❌ | Instruções especiais atualizadas |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### ShippingQuoteRequest

**Descrição:** Schema para solicitar cotação de frete.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `destination_postal_code` | string | ✅ | CEP de destino |
| `items` | Array[object] | ✅ | Itens para cálculo |
| `shipping_methods` | unknown | ❌ | Métodos específicos para cotação |

### ShippingQuoteResponse

**Descrição:** Schema para resposta de cotação de frete.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `quotes` | Array[object] | ✅ | Cotações disponíveis |
| `estimated_delivery_days` | object | ❌ | Dias estimados por método |

## 🔗 Endpoints Detalhados

### POST /api/checkout/initiate {#post-apicheckoutinitiate}

**Resumo:** Initiate Checkout
**Descrição:** Inicia processo de checkout a partir de um carrinho.

Cria uma sessão de checkout com cálculo de totais,
validação de estoque e preparação para pagamento.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [CheckoutInitiateRequest](#checkoutinitiaterequest)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CheckoutResponse](#checkoutresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/checkout/initiate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/checkout/shipping/quote {#post-apicheckoutshippingquote}

**Resumo:** Get Shipping Quote
**Descrição:** Obtém cotação de frete para itens do carrinho.

Calcula custos e prazos de entrega para diferentes
métodos de envio.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ShippingQuoteRequest](#shippingquoterequest)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ShippingQuoteResponse](#shippingquoteresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/checkout/shipping/quote" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/checkout/{checkout_id} {#get-apicheckoutcheckout-id}

**Resumo:** Get Checkout Session
**Descrição:** Obtém detalhes de uma sessão de checkout.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `checkout_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CheckoutSessionRead](#checkoutsessionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/checkout/{checkout_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/checkout/{checkout_id} {#put-apicheckoutcheckout-id}

**Resumo:** Update Checkout Session
**Descrição:** Atualiza sessão de checkout.

Permite alterar endereços, métodos de entrega e pagamento
antes da finalização.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `checkout_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [CheckoutSessionUpdate](#checkoutsessionupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CheckoutResponse](#checkoutresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/checkout/{checkout_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/checkout/{checkout_id}/cancel {#delete-apicheckoutcheckout-idcancel}

**Resumo:** Cancel Checkout
**Descrição:** Cancela sessão de checkout.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `checkout_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CheckoutResponse](#checkoutresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/checkout/{checkout_id}/cancel" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/checkout/{checkout_id}/complete {#post-apicheckoutcheckout-idcomplete}

**Resumo:** Complete Checkout
**Descrição:** Completa o processo de checkout.

Processa pagamento, cria pedido e finaliza a transação.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `checkout_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [CheckoutCompleteRequest](#checkoutcompleterequest)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CheckoutResponse](#checkoutresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/checkout/{checkout_id}/complete" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
