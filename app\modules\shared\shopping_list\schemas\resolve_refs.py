"""Resolve forward references for shopping list schemas."""

def resolve_shopping_list_refs():
    """Resolve forward references after all schemas are loaded."""
    try:
        from .shopping_list import ShoppingListItemRead, ShoppingListWithItems
        from .shopping_list_category import ShoppingListCategoryRead
        
        # Update forward reference
        ShoppingListItemRead.model_rebuild()
        ShoppingListWithItems.model_rebuild()
        
    except ImportError:
        # If schemas are not available, skip resolution
        pass
