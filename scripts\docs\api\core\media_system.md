# Core - Media System

**Categoria:** Core
**Módulo:** Media System
**Total de Endpoints:** 13
**Gerado em:** 27/06/2025, 20:20:26

## 📋 Endpoints

- [GET /api/modules/core/media/cleanup/preview](#get-apimodulescoremediacleanuppreview) - Preview Cleanup
- [POST /api/modules/core/media/cleanup/run](#post-apimodulescoremediacleanuprun) - Run Cleanup
- [GET /api/modules/core/media/download/{upload_id}](#get-apimodulescoremediadownloadupload-id) - Download File
- [POST /api/modules/core/media/menu-items/{menu_item_id}/link](#post-apimodulescoremediamenu-itemsmenu-item-idlink) - Link Upload To Menu Item
- [GET /api/modules/core/media/menu-items/{menu_item_id}/media](#get-apimodulescoremediamenu-itemsmenu-item-idmedia) - Get Menu Item Media
- [PUT /api/modules/core/media/menu-items/{menu_item_id}/order](#put-apimodulescoremediamenu-itemsmenu-item-idorder) - Update Media Order
- [PUT /api/modules/core/media/menu-items/{menu_item_id}/primary](#put-apimodulescoremediamenu-itemsmenu-item-idprimary) - Set Primary Media
- [POST /api/modules/core/media/menu-items/{menu_item_id}/upload](#post-apimodulescoremediamenu-itemsmenu-item-idupload) - Upload Menu Item Media
- [GET /api/modules/core/media/quota](#get-apimodulescoremediaquota) - Get Quota Info
- [POST /api/modules/core/media/quota/check](#post-apimodulescoremediaquotacheck) - Check Quota
- [POST /api/modules/core/media/upload](#post-apimodulescoremediaupload) - Upload File
- [GET /api/modules/core/media/uploads](#get-apimodulescoremediauploads) - List Uploads
- [DELETE /api/modules/core/media/uploads/{upload_id}](#delete-apimodulescoremediauploadsupload-id) - Delete Upload

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### Body_check_quota_api_modules_core_media_quota_check_post

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `context_type` | MediaContextType | ✅ | - |
| `context_id` | unknown | ❌ | - |
| `file_size_mb` | integer | ✅ | - |

### Body_upload_file_api_modules_core_media_upload_post

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `file` | string | ✅ | - |
| `context_type` | string | ✅ | - |
| `context_id` | unknown | ❌ | - |
| `directory_id` | unknown | ❌ | - |

### Body_upload_menu_item_media_api_modules_core_media_menu_items__menu_item_id__upload_post

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `file` | string | ✅ | - |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### LinkUploadRequest

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `upload_id` | string | ✅ | - |

### MediaOrderRequest

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `media_order` | Array[MediaOrderItem] | ✅ | - |

### MediaQuotaCheckResponse

**Descrição:** Schema de resposta para verificação de quota.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `can_upload` | boolean | ✅ | - |
| `reason` | unknown | ❌ | - |
| `quota_limit_mb` | unknown | ❌ | - |
| `used_space_mb` | unknown | ❌ | - |
| `available_space_mb` | unknown | ❌ | - |
| `file_size_mb` | unknown | ❌ | - |

### MediaQuotaResponse

**Descrição:** Schema de resposta para informações de quota.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `context_id` | string | ✅ | - |
| `context_type` | MediaContextType | ✅ | - |
| `quota_limit_mb` | integer | ✅ | - |
| `used_space_mb` | integer | ✅ | - |
| `available_space_mb` | integer | ✅ | - |
| `quota_usage_percentage` | number | ✅ | - |
| `is_quota_enabled` | boolean | ✅ | - |
| `is_quota_exceeded` | boolean | ✅ | - |

### MediaUploadResponse

**Descrição:** Schema de resposta para upload.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `success` | boolean | ✅ | - |
| `message` | string | ✅ | - |
| `upload_id` | unknown | ❌ | - |
| `file_url` | unknown | ❌ | - |
| `thumbnail_url` | unknown | ❌ | - |

### SetPrimaryMediaRequest

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `upload_id` | string | ✅ | - |

## 🔗 Endpoints Detalhados

### GET /api/modules/core/media/cleanup/preview {#get-apimodulescoremediacleanuppreview}

**Resumo:** Preview Cleanup
**Descrição:** Visualiza quais uploads seriam removidos na limpeza automática.

Args:
    current_user: Usuário atual
    db: Sessão do banco de dados

Returns:
    dict: Lista de uploads candidatos à remoção

**🔐 Autenticação:** Requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/media/cleanup/preview" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/media/cleanup/run {#post-apimodulescoremediacleanuprun}

**Resumo:** Run Cleanup
**Descrição:** Executa limpeza automática de uploads não vinculados.

Args:
    current_user: Usuário atual
    db: Sessão do banco de dados

Returns:
    dict: Estatísticas da limpeza executada

**🔐 Autenticação:** Requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/media/cleanup/run" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/media/download/{upload_id} {#get-apimodulescoremediadownloadupload-id}

**Resumo:** Download File
**Descrição:** Faz download de um arquivo.

Args:
    upload_id: ID do upload
    thumbnail: Se deve baixar thumbnail
    compressed: Se deve baixar versão comprimida
    current_user: Usuário atual
    upload_service: Serviço de upload
    
Returns:
    StreamingResponse: Arquivo para download

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `upload_id` | string | path | ✅ | - |
| `thumbnail` | boolean | query | ❌ | Baixar thumbnail |
| `compressed` | boolean | query | ❌ | Baixar versão comprimida |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/media/download/{upload_id}"
```

---

### POST /api/modules/core/media/menu-items/{menu_item_id}/link {#post-apimodulescoremediamenu-itemsmenu-item-idlink}

**Resumo:** Link Upload To Menu Item
**Descrição:** Vincula um upload temporário a um item do menu.

Args:
    menu_item_id: ID do item do menu
    request: ID do upload a ser vinculado
    current_user: Usuário atual
    upload_service: Serviço de upload

Returns:
    dict: Resultado da operação

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `menu_item_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [LinkUploadRequest](#linkuploadrequest)

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/media/menu-items/{menu_item_id}/link" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/media/menu-items/{menu_item_id}/media {#get-apimodulescoremediamenu-itemsmenu-item-idmedia}

**Resumo:** Get Menu Item Media
**Descrição:** Lista mídia de um item do menu com informações de is_primary e display_order.

Args:
    menu_item_id: ID do item do menu
    current_user: Usuário atual
    upload_service: Serviço de upload

Returns:
    List: Lista de mídias do item com informações completas

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `menu_item_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/media/menu-items/{menu_item_id}/media"
```

---

### PUT /api/modules/core/media/menu-items/{menu_item_id}/order {#put-apimodulescoremediamenu-itemsmenu-item-idorder}

**Resumo:** Update Media Order
**Descrição:** Atualiza a ordem das mídias de um item do menu.

Args:
    menu_item_id: ID do item do menu
    request: Nova ordem das mídias
    current_user: Usuário atual
    upload_service: Serviço de upload

Returns:
    dict: Resultado da operação

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `menu_item_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [MediaOrderRequest](#mediaorderrequest)

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/core/media/menu-items/{menu_item_id}/order" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/modules/core/media/menu-items/{menu_item_id}/primary {#put-apimodulescoremediamenu-itemsmenu-item-idprimary}

**Resumo:** Set Primary Media
**Descrição:** Define a mídia primária de um item do menu.

Args:
    menu_item_id: ID do item do menu
    request: ID da mídia a ser definida como primária
    current_user: Usuário atual
    upload_service: Serviço de upload

Returns:
    dict: Resultado da operação

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `menu_item_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [SetPrimaryMediaRequest](#setprimarymediarequest)

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/core/media/menu-items/{menu_item_id}/primary" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/core/media/menu-items/{menu_item_id}/upload {#post-apimodulescoremediamenu-itemsmenu-item-idupload}

**Resumo:** Upload Menu Item Media
**Descrição:** Faz upload de mídia para um item do menu e cria a associação automaticamente.

Args:
    menu_item_id: ID do item do menu
    file: Arquivo para upload
    current_user: Usuário atual
    upload_service: Serviço de upload
    context_service: Serviço de contexto

Returns:
    MediaUploadResponse: Resposta do upload

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `menu_item_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `multipart/form-data`
**Schema:** [Body_upload_menu_item_media_api_modules_core_media_menu_items__menu_item_id__upload_post](#body_upload_menu_item_media_api_modules_core_media_menu_items__menu_item_id__upload_post)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [MediaUploadResponse](#mediauploadresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/media/menu-items/{menu_item_id}/upload" \
  -H "Content-Type: multipart/form-data" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/media/quota {#get-apimodulescoremediaquota}

**Resumo:** Get Quota Info
**Descrição:** Obtém informações de quota de um contexto.

Args:
    context_type: Tipo de contexto
    context_id: ID do contexto
    current_user: Usuário atual
    context_service: Serviço de contexto
    
Returns:
    MediaQuotaResponse: Informações de quota

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `context_type` | string | query | ✅ | - |
| `context_id` | string | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [MediaQuotaResponse](#mediaquotaresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/media/quota" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/media/quota/check {#post-apimodulescoremediaquotacheck}

**Resumo:** Check Quota
**Descrição:** Verifica se um arquivo pode ser enviado considerando a quota.

Args:
    context_type: Tipo de contexto
    context_id: ID do contexto
    file_size_mb: Tamanho do arquivo em MB
    current_user: Usuário atual
    context_service: Serviço de contexto
    
Returns:
    MediaQuotaCheckResponse: Resultado da verificação

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/x-www-form-urlencoded`
**Schema:** [Body_check_quota_api_modules_core_media_quota_check_post](#body_check_quota_api_modules_core_media_quota_check_post)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [MediaQuotaCheckResponse](#mediaquotacheckresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/media/quota/check" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/core/media/upload {#post-apimodulescoremediaupload}

**Resumo:** Upload File
**Descrição:** Faz upload de um arquivo.

Args:
    file: Arquivo para upload
    context_type: Tipo de contexto (USER ou TENANT)
    context_id: ID do contexto (obrigatório para TENANT)
    directory_id: ID do diretório (opcional)
    current_user: Usuário atual
    upload_service: Serviço de upload
    context_service: Serviço de contexto
    
Returns:
    MediaUploadResponse: Resposta do upload

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `multipart/form-data`
**Schema:** [Body_upload_file_api_modules_core_media_upload_post](#body_upload_file_api_modules_core_media_upload_post)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [MediaUploadResponse](#mediauploadresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/media/upload" \
  -H "Content-Type: multipart/form-data" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/media/uploads {#get-apimodulescoremediauploads}

**Resumo:** List Uploads
**Descrição:** Lista uploads por contexto.

Args:
    context_type: Tipo de contexto (USER ou TENANT)
    context_id: ID do contexto (obrigatório para TENANT)
    media_type: Filtro por tipo de mídia (opcional)
    skip: Número de registros para pular
    limit: Limite de registros
    current_user: Usuário atual
    upload_service: Serviço de upload
    context_service: Serviço de contexto

Returns:
    List[MediaUploadRead]: Lista de uploads

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `context_type` | string | query | ✅ | - |
| `context_id` | string | query | ❌ | - |
| `media_type` | string | query | ❌ | - |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/media/uploads" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### DELETE /api/modules/core/media/uploads/{upload_id} {#delete-apimodulescoremediauploadsupload-id}

**Resumo:** Delete Upload
**Descrição:** Remove um upload.

Args:
    upload_id: ID do upload
    current_user: Usuário atual
    upload_service: Serviço de upload
    
Returns:
    dict: Resultado da operação

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `upload_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/core/media/uploads/{upload_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
