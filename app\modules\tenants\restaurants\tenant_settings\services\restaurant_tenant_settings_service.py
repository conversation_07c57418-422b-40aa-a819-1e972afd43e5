"""
Service for managing restaurant-specific tenant settings.
"""

import uuid
import re
from typing import Optional, Dict, Any, List
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError
from sqlalchemy import select, func
from urllib.parse import urlparse

from app.modules.tenants.restaurants.tenant_settings.models.restaurant_tenant_settings import (
    RestaurantTenantSettings
)
# Import table model for zones integration
from app.modules.tenants.restaurants.table_management.models.table import Table
from app.modules.tenants.restaurants.tenant_settings.schemas.restaurant_tenant_settings import (
    RestaurantTenantSettingsCreate,
    RestaurantTenantSettingsUpdate,
)
from app.modules.tenants.restaurants.tenant_settings.services.validation_service import (
    restaurant_validation_service,
)
from app.core.exceptions import (
    ValidationError,
    NotFoundError,
    ConflictError,
)


class RestaurantTenantSettingsService:
    """Service for restaurant tenant settings operations."""

    def __init__(self):
        pass

    def get_by_tenant_id(
        self,
        db: Session,
        tenant_id: uuid.UUID
    ) -> Optional[RestaurantTenantSettings]:
        """Get restaurant settings by tenant ID."""
        return db.query(RestaurantTenantSettings).filter(
            RestaurantTenantSettings.tenant_id == tenant_id
        ).first()

    async def async_get_by_tenant_id(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID
    ) -> Optional[RestaurantTenantSettings]:
        """Get restaurant settings by tenant ID (async version)."""
        result = await db.execute(
            select(RestaurantTenantSettings).filter(
                RestaurantTenantSettings.tenant_id == tenant_id
            )
        )
        return result.scalar_one_or_none()

    def create(
        self, 
        db: Session, 
        obj_in: RestaurantTenantSettingsCreate
    ) -> RestaurantTenantSettings:
        """Create new restaurant tenant settings."""
        try:
            db_obj = RestaurantTenantSettings(**obj_in.model_dump())
            db.add(db_obj)
            db.commit()
            db.refresh(db_obj)
            return db_obj
        except IntegrityError as e:
            db.rollback()
            if "unique constraint" in str(e).lower():
                raise ConflictError("Restaurant settings already exist for this tenant")
            raise ValidationError(f"Database integrity error: {str(e)}")

    async def async_create(
        self,
        db: AsyncSession,
        obj_in: RestaurantTenantSettingsCreate
    ) -> RestaurantTenantSettings:
        """Create new restaurant tenant settings (async version)."""
        try:
            db_obj = RestaurantTenantSettings(**obj_in.model_dump())
            db.add(db_obj)
            await db.commit()
            await db.refresh(db_obj)
            return db_obj
        except IntegrityError as e:
            await db.rollback()
            if "unique constraint" in str(e).lower():
                raise ConflictError("Restaurant settings already exist for this tenant")
            raise ValidationError(f"Database integrity error: {str(e)}")

    def update(
        self, 
        db: Session, 
        db_obj: RestaurantTenantSettings, 
        obj_in: RestaurantTenantSettingsUpdate
    ) -> RestaurantTenantSettings:
        """Update restaurant tenant settings."""
        update_data = obj_in.model_dump(exclude_unset=True)
        
        for field, value in update_data.items():
            setattr(db_obj, field, value)
        
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    async def async_update(
        self,
        db: AsyncSession,
        db_obj: RestaurantTenantSettings,
        obj_in: RestaurantTenantSettingsUpdate
    ) -> RestaurantTenantSettings:
        """Update restaurant tenant settings (async version)."""
        update_data = obj_in.model_dump(exclude_unset=True)

        for field, value in update_data.items():
            setattr(db_obj, field, value)

        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    def get_or_create(
        self, 
        db: Session, 
        tenant_id: uuid.UUID
    ) -> RestaurantTenantSettings:
        """Get existing restaurant settings or create new ones."""
        settings = self.get_by_tenant_id(db, tenant_id)
        if not settings:
            create_data = RestaurantTenantSettingsCreate(tenant_id=tenant_id)
            settings = self.create(db, create_data)
        return settings

    async def async_get_or_create(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID
    ) -> RestaurantTenantSettings:
        """Get existing restaurant settings or create new ones (async version)."""
        settings = await self.async_get_by_tenant_id(db, tenant_id)
        if not settings:
            create_data = RestaurantTenantSettingsCreate(tenant_id=tenant_id)
            settings = await self.async_create(db, create_data)
        return settings

    def update_tenant_slug(
        self, 
        db: Session, 
        tenant_id: uuid.UUID, 
        tenant_slug: str
    ) -> RestaurantTenantSettings:
        """Update tenant slug for restaurant."""
        settings = self.get_or_create(db, tenant_id)
        update_data = RestaurantTenantSettingsUpdate(tenant_slug=tenant_slug)
        return self.update(db, settings, update_data)

    def update_wifi_networks(
        self, 
        db: Session, 
        tenant_id: uuid.UUID, 
        wifi_networks: List[Dict[str, Any]]
    ) -> RestaurantTenantSettings:
        """Update WiFi networks for restaurant."""
        settings = self.get_or_create(db, tenant_id)
        update_data = RestaurantTenantSettingsUpdate(wifi_networks=wifi_networks)
        return self.update(db, settings, update_data)

    def update_social_media_links(
        self, 
        db: Session, 
        tenant_id: uuid.UUID, 
        social_media_links: List[Dict[str, Any]]
    ) -> RestaurantTenantSettings:
        """Update social media links for restaurant."""
        settings = self.get_or_create(db, tenant_id)
        update_data = RestaurantTenantSettingsUpdate(social_media_links=social_media_links)
        return self.update(db, settings, update_data)

    def update_address_extensions(
        self, 
        db: Session, 
        tenant_id: uuid.UUID, 
        address_extensions: Dict[str, Any]
    ) -> RestaurantTenantSettings:
        """Update address extensions for restaurant."""
        settings = self.get_or_create(db, tenant_id)
        update_data = RestaurantTenantSettingsUpdate(address_extensions=address_extensions)
        return self.update(db, settings, update_data)

    def delete(self, db: Session, tenant_id: uuid.UUID) -> bool:
        """Delete restaurant tenant settings."""
        settings = self.get_by_tenant_id(db, tenant_id)
        if settings:
            db.delete(settings)
            db.commit()
            return True
        return False

    def validate_tenant_slug(self, db: Session, tenant_slug: str, tenant_id: uuid.UUID = None) -> bool:
        """Validate that tenant slug is unique and has correct format."""
        # Validate format first
        if not restaurant_validation_service.validate_tenant_slug_format(tenant_slug):
            return False

        # Check uniqueness in database
        query = db.query(RestaurantTenantSettings).filter(
            RestaurantTenantSettings.tenant_slug == tenant_slug
        )

        if tenant_id:
            query = query.filter(RestaurantTenantSettings.tenant_id != tenant_id)

        existing = query.first()
        return existing is None

    async def async_validate_tenant_slug(self, db: AsyncSession, tenant_slug: str, tenant_id: uuid.UUID = None) -> bool:
        """Validate that tenant slug is unique and has correct format (async version)."""
        # Validate format first
        if not restaurant_validation_service.validate_tenant_slug_format(tenant_slug):
            return False

        # Check uniqueness in database
        query = select(RestaurantTenantSettings).filter(
            RestaurantTenantSettings.tenant_slug == tenant_slug
        )

        if tenant_id:
            query = query.filter(RestaurantTenantSettings.tenant_id != tenant_id)

        result = await db.execute(query)
        existing = result.scalar_one_or_none()
        return existing is None

    def get_available_zones(self, db: Session, tenant_id: uuid.UUID, use_cache: bool = True) -> List[Dict[str, Any]]:
        """Get all available zones from tables and custom zones."""
        # Try to get from cache first
        if use_cache:
            from app.modules.tenants.restaurants.tenant_settings.services.zones_cache_service import zones_cache_service
            cached_zones = zones_cache_service.get_zones(tenant_id)
            if cached_zones is not None:
                return cached_zones

        try:
            # Get zones from tables
            result = db.execute(
                select(Table.zone, func.count(Table.id).label('table_count'))
                .where(Table.tenant_id == tenant_id)
                .where(Table.zone.isnot(None))
                .group_by(Table.zone)
            )
            table_zones = result.fetchall()

            # Get custom zones from restaurant settings
            settings = self.get_by_tenant_id(db, tenant_id)
            custom_zones = []
            if settings and settings.additional_restaurant_settings:
                custom_zones = settings.additional_restaurant_settings.get('custom_zones', [])

            # Combine zones
            zones_data = []

            # Add zones from tables
            for zone_name, table_count in table_zones:
                zones_data.append({
                    "name": zone_name,
                    "type": "table_zone",
                    "table_count": table_count,
                    "is_active": True,
                    "is_custom": False
                })

            # Add custom zones (zones without tables)
            table_zone_names = {zone_name for zone_name, _ in table_zones}
            for custom_zone in custom_zones:
                if custom_zone not in table_zone_names:
                    zones_data.append({
                        "name": custom_zone,
                        "type": "custom_zone",
                        "table_count": 0,
                        "is_active": True,
                        "is_custom": True
                    })

            # Sort zones by name
            zones_data.sort(key=lambda x: x["name"])

            # Cache the result
            if use_cache:
                zones_cache_service.set_zones(tenant_id, zones_data)

            return zones_data

        except Exception as e:
            raise ValidationError(f"Error retrieving zones: {str(e)}")

    def add_custom_zone(self, db: Session, tenant_id: uuid.UUID, zone_name: str) -> RestaurantTenantSettings:
        """Add a custom zone to restaurant settings."""
        if not zone_name or not zone_name.strip():
            raise ValidationError("Zone name cannot be empty")

        settings = self.get_or_create(db, tenant_id)

        # Get current custom zones
        additional_settings = settings.additional_restaurant_settings or {}
        custom_zones = additional_settings.get('custom_zones', [])

        # Check if zone already exists
        if zone_name in custom_zones:
            raise ConflictError(f"Custom zone '{zone_name}' already exists")

        # Add new zone
        custom_zones.append(zone_name)
        additional_settings['custom_zones'] = custom_zones

        update_data = RestaurantTenantSettingsUpdate(
            additional_restaurant_settings=additional_settings
        )

        # Invalidate zones cache
        from app.modules.tenants.restaurants.tenant_settings.services.zones_cache_service import zones_cache_service
        zones_cache_service.invalidate_zones(tenant_id)

        return self.update(db, settings, update_data)

    def remove_custom_zone(self, db: Session, tenant_id: uuid.UUID, zone_name: str) -> RestaurantTenantSettings:
        """Remove a custom zone from restaurant settings."""
        settings = self.get_or_create(db, tenant_id)

        # Get current custom zones
        additional_settings = settings.additional_restaurant_settings or {}
        custom_zones = additional_settings.get('custom_zones', [])

        # Remove zone if exists
        if zone_name in custom_zones:
            custom_zones.remove(zone_name)
            additional_settings['custom_zones'] = custom_zones

            update_data = RestaurantTenantSettingsUpdate(
                additional_restaurant_settings=additional_settings
            )

            # Invalidate zones cache
            from app.modules.tenants.restaurants.tenant_settings.services.zones_cache_service import zones_cache_service
            zones_cache_service.invalidate_zones(tenant_id)

            return self.update(db, settings, update_data)

        raise NotFoundError(f"Custom zone '{zone_name}' not found")

    def validate_social_media_links(self, social_media_links: List[Dict[str, Any]]) -> bool:
        """Validate social media links format and URLs."""
        if not social_media_links:
            return True

        for link in social_media_links:
            # Validate required fields
            if not all(key in link for key in ['platform', 'url', 'display_name']):
                raise ValidationError("Social media link missing required fields")

            # Validate URL format
            url = link.get('url', '')
            if not url.startswith(('http://', 'https://')):
                raise ValidationError(f"Invalid URL format: {url}")

            # Basic URL validation
            try:
                parsed = urlparse(url)
                if not parsed.netloc:
                    raise ValidationError(f"Invalid URL: {url}")
            except Exception:
                raise ValidationError(f"Invalid URL: {url}")

            # Validate platform
            platform = link.get('platform', '').lower()
            supported_platforms = [
                'facebook', 'instagram', 'twitter', 'linkedin', 'youtube',
                'tiktok', 'whatsapp', 'telegram', 'pinterest', 'snapchat',
                'discord', 'twitch', 'reddit', 'custom'
            ]

            if platform not in supported_platforms and len(platform) < 2:
                raise ValidationError(f"Invalid platform: {platform}")

        return True

    def validate_wifi_networks(self, wifi_networks: List[Dict[str, Any]]) -> bool:
        """Validate WiFi networks configuration."""
        if not wifi_networks:
            return True

        for network in wifi_networks:
            # Validate required fields
            if not all(key in network for key in ['name', 'password']):
                raise ValidationError("WiFi network missing required fields")

            # Validate security type
            security_type = network.get('security_type', 'WPA2')
            valid_types = ["Open", "WEP", "WPA", "WPA2", "WPA3"]
            if security_type not in valid_types:
                raise ValidationError(f"Invalid security type: {security_type}")

            # Validate network name length
            name = network.get('name', '')
            if len(name) > 100:
                raise ValidationError("WiFi network name too long")

            # Validate password length
            password = network.get('password', '')
            if len(password) > 255:
                raise ValidationError("WiFi network password too long")

        return True

    def validate_address_extensions(self, address_extensions: Dict[str, Any]) -> bool:
        """Validate address extensions for restaurant."""
        if not address_extensions:
            return True

        # Validate delivery radius
        delivery_radius = address_extensions.get('delivery_radius_km')
        if delivery_radius is not None:
            if not isinstance(delivery_radius, (int, float)) or delivery_radius < 0:
                raise ValidationError("Delivery radius must be a positive number")

        # Validate service area notes length
        service_notes = address_extensions.get('service_area_notes', '')
        if len(service_notes) > 300:
            raise ValidationError("Service area notes too long")

        return True

    def update_with_validation(
        self,
        db: Session,
        tenant_id: uuid.UUID,
        update_data: RestaurantTenantSettingsUpdate
    ) -> RestaurantTenantSettings:
        """Update restaurant settings with comprehensive validation."""
        # Validate tenant slug if provided
        if update_data.tenant_slug is not None:
            if not self.validate_tenant_slug(db, update_data.tenant_slug, tenant_id):
                raise ValidationError("Invalid or duplicate tenant slug")

        # Validate social media links if provided
        if update_data.social_media_links is not None:
            self.validate_social_media_links(update_data.social_media_links)

        # Validate WiFi networks if provided
        if update_data.wifi_networks is not None:
            self.validate_wifi_networks(update_data.wifi_networks)

        # Validate address extensions if provided
        if update_data.address_extensions is not None:
            self.validate_address_extensions(update_data.address_extensions)

        # Validate operating hours if provided
        if update_data.operating_hours is not None:
            restaurant_validation_service.validate_operating_hours(update_data.operating_hours)

        # Validate special calendar if provided
        if update_data.special_calendar is not None:
            restaurant_validation_service.validate_special_calendar(update_data.special_calendar)

        # Perform update
        settings = self.get_or_create(db, tenant_id)
        return self.update(db, settings, update_data)

    async def async_update_with_validation(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID,
        update_data: RestaurantTenantSettingsUpdate
    ) -> RestaurantTenantSettings:
        """Update restaurant settings with comprehensive validation (async version)."""
        # Validate tenant slug if provided
        if update_data.tenant_slug is not None:
            if not await self.async_validate_tenant_slug(db, update_data.tenant_slug, tenant_id):
                raise ValidationError("Invalid or duplicate tenant slug")

        # Validate social media links if provided
        if update_data.social_media_links is not None:
            self.validate_social_media_links(update_data.social_media_links)

        # Validate WiFi networks if provided
        if update_data.wifi_networks is not None:
            self.validate_wifi_networks(update_data.wifi_networks)

        # Validate address extensions if provided
        if update_data.address_extensions is not None:
            self.validate_address_extensions(update_data.address_extensions)

        # Validate operating hours if provided
        if update_data.operating_hours is not None:
            restaurant_validation_service.validate_operating_hours(update_data.operating_hours)

        # Validate special calendar if provided
        if update_data.special_calendar is not None:
            restaurant_validation_service.validate_special_calendar(update_data.special_calendar)

        # Perform update
        settings = await self.async_get_or_create(db, tenant_id)
        return await self.async_update(db, settings, update_data)

    def update_operating_hours(
        self,
        db: Session,
        tenant_id: uuid.UUID,
        operating_hours: Dict[str, Any]
    ) -> RestaurantTenantSettings:
        """Update operating hours for restaurant."""
        # Validate operating hours structure
        restaurant_validation_service.validate_operating_hours(operating_hours)

        settings = self.get_or_create(db, tenant_id)
        update_data = RestaurantTenantSettingsUpdate(operating_hours=operating_hours)

        # Invalidate zones cache since operating hours might affect zones
        from app.modules.tenants.restaurants.tenant_settings.services.zones_cache_service import zones_cache_service
        zones_cache_service.invalidate_zones(tenant_id)

        return self.update(db, settings, update_data)

    def update_special_calendar(
        self,
        db: Session,
        tenant_id: uuid.UUID,
        special_calendar: Dict[str, Any]
    ) -> RestaurantTenantSettings:
        """Update special calendar for restaurant."""
        # Validate special calendar structure
        restaurant_validation_service.validate_special_calendar(special_calendar)

        settings = self.get_or_create(db, tenant_id)
        update_data = RestaurantTenantSettingsUpdate(special_calendar=special_calendar)
        return self.update(db, settings, update_data)

    async def async_update_operating_hours(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID,
        operating_hours: Dict[str, Any]
    ) -> RestaurantTenantSettings:
        """Update operating hours for restaurant (async version)."""
        # Validate operating hours structure
        restaurant_validation_service.validate_operating_hours(operating_hours)

        settings = await self.async_get_or_create(db, tenant_id)
        update_data = RestaurantTenantSettingsUpdate(operating_hours=operating_hours)

        # Invalidate zones cache since operating hours might affect zones
        from app.modules.tenants.restaurants.tenant_settings.services.zones_cache_service import zones_cache_service
        zones_cache_service.invalidate_zones(tenant_id)

        return await self.async_update(db, settings, update_data)

    async def async_update_special_calendar(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID,
        special_calendar: Dict[str, Any]
    ) -> RestaurantTenantSettings:
        """Update special calendar for restaurant (async version)."""
        # Validate special calendar structure
        restaurant_validation_service.validate_special_calendar(special_calendar)

        settings = await self.async_get_or_create(db, tenant_id)
        update_data = RestaurantTenantSettingsUpdate(special_calendar=special_calendar)
        return await self.async_update(db, settings, update_data)


# Create service instance
restaurant_tenant_settings_service = RestaurantTenantSettingsService()
