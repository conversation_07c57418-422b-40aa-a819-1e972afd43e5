'use client';

import { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { useAuth } from '@/lib/auth/AuthProvider';
import { apiClient } from '@/lib/api/client';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { 
  TicketIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  UserIcon,
  CalendarIcon,
  FunnelIcon,
  ArrowPathIcon,
  EllipsisVerticalIcon
} from '@heroicons/react/24/outline';

interface Ticket {
  id: string;
  title: string;
  description: string;
  status: 'new' | 'open' | 'pending' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'question' | 'incident' | 'problem' | 'request' | 'suggestion';
  created_at: string;
  updated_at?: string;
  is_read_by_admin: boolean;
  is_read_by_user: boolean;
  user_name?: string;
  user_email?: string;
  assigned_admin_name?: string;
  message_count?: number;
  tenant_name?: string;
}

interface TicketListResponse {
  tickets: Ticket[];
  total: number;
  page: number;
  per_page: number;
  has_next: boolean;
  has_prev: boolean;
}

export function AdminTicketManagement() {
  const { user, isAdmin } = useAuth();
  const [tickets, setTickets] = useState<Ticket[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTickets, setSelectedTickets] = useState<Set<string>>(new Set());
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    status: '',
    priority: '',
    category: '',
    search: '',
    assigned: ''
  });
  const [sortBy, setSortBy] = useState<'created_at' | 'updated_at' | 'priority'>('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const fetchTickets = useCallback(async (page = 1) => {
    try {
      console.log('🎫 AdminTicketManagement - Iniciando fetchTickets...');
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: page.toString(),
        per_page: '20',
        sort_by: sortBy,
        sort_order: sortOrder
      });

      if (filters.status) params.append('status', filters.status);
      if (filters.priority) params.append('priority', filters.priority);
      if (filters.category) params.append('category', filters.category);
      if (filters.search) params.append('search', filters.search);
      if (filters.assigned) params.append('assigned', filters.assigned);

      const url = `/modules/core/help-center/admin/tickets?${params.toString()}`;
      console.log('🎫 AdminTicketManagement - URL:', url);

      const response = await apiClient.get<TicketListResponse>(url);

      console.log('🎫 AdminTicketManagement - Response completa:', response);
      console.log('🎫 AdminTicketManagement - Response.data:', response.data);
      console.log('🎫 AdminTicketManagement - Response.data.tickets:', response.data?.tickets);
      console.log('🎫 AdminTicketManagement - Response.data.total:', response.data?.total);

      // A resposta do apiClient vem em response.data
      const data = response.data || response;

      setTickets(data.tickets || []);
      setCurrentPage(data.page || 1);
      setTotalPages(Math.ceil((data.total || 0) / (data.per_page || 20)));

      console.log('🎫 AdminTicketManagement - Tickets carregados:', data.tickets?.length);
      console.log('🎫 AdminTicketManagement - Estado tickets após setTickets:', data.tickets);
    } catch (err: any) {
      console.error('❌ AdminTicketManagement - Erro ao carregar tickets:', err);
      console.error('❌ AdminTicketManagement - Erro response:', err.response);
      setError('Erro ao carregar tickets. Tente novamente.');
    } finally {
      setIsLoading(false);
      console.log('🎫 AdminTicketManagement - fetchTickets finalizado');
    }
  }, [sortBy, sortOrder, filters]);

  useEffect(() => {
    if (isAdmin()) {
      fetchTickets(currentPage);
    }
  }, [currentPage, filters, sortBy, sortOrder, fetchTickets, isAdmin]);

  const handleTicketSelect = (ticketId: string) => {
    const newSelected = new Set(selectedTickets);
    if (newSelected.has(ticketId)) {
      newSelected.delete(ticketId);
    } else {
      newSelected.add(ticketId);
    }
    setSelectedTickets(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedTickets.size === (tickets?.length || 0)) {
      setSelectedTickets(new Set());
    } else {
      setSelectedTickets(new Set((tickets || []).map(t => t.id)));
    }
  };

  const handleBulkAction = async (action: string, value?: string) => {
    if (selectedTickets.size === 0) return;

    try {
      setIsLoading(true);
      
      const ticketIds = Array.from(selectedTickets);
      
      await apiClient.post('/modules/core/help-center/admin/tickets/bulk-update', {
        ticket_ids: ticketIds,
        action,
        value
      });

      setSelectedTickets(new Set());
      await fetchTickets(currentPage);
    } catch (err: any) {
      console.error('Erro na ação em lote:', err);
      setError('Erro ao executar ação em lote.');
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'new':
        return <TicketIcon className="h-4 w-4 text-blue-500" />;
      case 'open':
        return <ClockIcon className="h-4 w-4 text-yellow-500" />;
      case 'pending':
        return <ExclamationTriangleIcon className="h-4 w-4 text-orange-500" />;
      case 'resolved':
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
      case 'closed':
        return <XCircleIcon className="h-4 w-4 text-gray-500" />;
      default:
        return <TicketIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!isAdmin()) {
    return (
      <div className="text-center py-12">
        <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">
          Acesso Negado
        </h3>
        <p className="mt-1 text-sm text-gray-500">
          Apenas administradores podem acessar o gerenciamento de tickets.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">
            Gerenciamento de Tickets
          </h2>
          <p className="text-sm text-gray-600">
            Gerencie todos os tickets de suporte do sistema
          </p>
        </div>
        
        <button
          onClick={() => fetchTickets(currentPage)}
          className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
        >
          <ArrowPathIcon className="h-4 w-4 mr-2" />
          Atualizar
        </button>
      </div>

      {/* Filters */}
      <div className="glass rounded-lg p-4">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              value={filters.status}
              onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Todos</option>
              <option value="new">Novo</option>
              <option value="open">Aberto</option>
              <option value="pending">Pendente</option>
              <option value="resolved">Resolvido</option>
              <option value="closed">Fechado</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Prioridade
            </label>
            <select
              value={filters.priority}
              onChange={(e) => setFilters(prev => ({ ...prev, priority: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Todas</option>
              <option value="urgent">Urgente</option>
              <option value="high">Alta</option>
              <option value="medium">Média</option>
              <option value="low">Baixa</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Categoria
            </label>
            <select
              value={filters.category}
              onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Todas</option>
              <option value="question">Dúvida</option>
              <option value="incident">Incidente</option>
              <option value="problem">Problema</option>
              <option value="request">Solicitação</option>
              <option value="suggestion">Sugestão</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Ordenar por
            </label>
            <select
              value={`${sortBy}-${sortOrder}`}
              onChange={(e) => {
                const [field, order] = e.target.value.split('-');
                setSortBy(field as any);
                setSortOrder(order as any);
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="created_at-desc">Mais recentes</option>
              <option value="created_at-asc">Mais antigos</option>
              <option value="updated_at-desc">Atualizados recentemente</option>
              <option value="priority-desc">Prioridade alta</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Buscar
            </label>
            <input
              type="text"
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              placeholder="Buscar tickets..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedTickets.size > 0 && (
        <div className="glass rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-700">
              {selectedTickets.size} ticket(s) selecionado(s)
            </span>
            
            <div className="flex items-center space-x-2">
              <select
                onChange={(e) => {
                  if (e.target.value) {
                    const [action, value] = e.target.value.split(':');
                    handleBulkAction(action, value);
                    e.target.value = '';
                  }
                }}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="">Ações em lote</option>
                <option value="status:open">Marcar como Aberto</option>
                <option value="status:resolved">Marcar como Resolvido</option>
                <option value="status:closed">Marcar como Fechado</option>
                <option value="priority:high">Definir Prioridade Alta</option>
                <option value="priority:medium">Definir Prioridade Média</option>
                <option value="priority:low">Definir Prioridade Baixa</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Tickets Table */}
      <div className="glass rounded-lg overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <div className="text-red-600 mb-4">{error}</div>
            <button
              onClick={() => fetchTickets(currentPage)}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Tentar Novamente
            </button>
          </div>
        ) : (tickets?.length || 0) === 0 ? (
          <div className="text-center py-12">
            <TicketIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhum ticket encontrado</h3>
            <p className="mt-1 text-sm text-gray-500">
              Nenhum ticket corresponde aos filtros selecionados.
            </p>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left">
                      <input
                        type="checkbox"
                        checked={selectedTickets.size === (tickets?.length || 0) && (tickets?.length || 0) > 0}
                        onChange={handleSelectAll}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ticket
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Prioridade
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Usuário
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Criado
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {tickets.map((ticket) => (
                    <tr 
                      key={ticket.id}
                      className={`hover:bg-gray-50 ${!ticket.is_read_by_admin ? 'bg-blue-50' : ''}`}
                    >
                      <td className="px-6 py-4">
                        <input
                          type="checkbox"
                          checked={selectedTickets.has(ticket.id)}
                          onChange={() => handleTicketSelect(ticket.id)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-start space-x-3">
                          {getStatusIcon(ticket.status)}
                          <div className="flex-1 min-w-0">
                            <Link
                              href={`/dashboard/help_center/tickets/${ticket.id}`}
                              className="text-sm font-medium text-gray-900 hover:text-blue-600 truncate block"
                            >
                              {ticket.title}
                            </Link>
                            <p className="text-sm text-gray-500 truncate">
                              {ticket.description}
                            </p>
                            {ticket.tenant_name && (
                              <p className="text-xs text-gray-400">
                                Tenant: {ticket.tenant_name}
                              </p>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          {ticket.status}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getPriorityColor(ticket.priority)}`}>
                          {ticket.priority}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center space-x-2">
                          <UserIcon className="h-4 w-4 text-gray-400" />
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {ticket.user_name || 'Usuário'}
                            </div>
                            {ticket.user_email && (
                              <div className="text-xs text-gray-500">
                                {ticket.user_email}
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center space-x-1">
                          <CalendarIcon className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-500">
                            {formatDate(ticket.created_at)}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <Link
                          href={`/dashboard/help_center/tickets/${ticket.id}`}
                          className="text-blue-600 hover:text-blue-900 text-sm font-medium"
                        >
                          Ver Detalhes
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                <div className="flex items-center justify-between">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Anterior
                  </button>
                  
                  <span className="text-sm text-gray-700">
                    Página {currentPage} de {totalPages}
                  </span>
                  
                  <button
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    disabled={currentPage === totalPages}
                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Próxima
                  </button>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
