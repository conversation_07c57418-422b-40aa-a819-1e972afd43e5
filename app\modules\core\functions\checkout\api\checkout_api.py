"""
Checkout API for EShop System
=============================

APIs para gerenciamento do processo de checkout com integração
a carrinho, pagamentos e criação de pedidos.
"""

import uuid
import logging
from typing import Optional, Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status, Header
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.auth_dependencies import get_current_user_optional
from app.modules.core.users.models.user import User
from app.modules.core.functions.checkout.services.checkout_service import get_checkout_service
from app.modules.core.functions.checkout.schemas.checkout import (
    CheckoutSessionRead, CheckoutResponse, CheckoutInitiateRequest,
    CheckoutCompleteRequest, CheckoutSummary, ShippingQuoteRequest,
    ShippingQuoteResponse, CheckoutSessionUpdate
)

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/checkout",
    tags=["Checkout"]
)


@router.post("/initiate", response_model=CheckoutResponse)
async def initiate_checkout(
    request: CheckoutInitiateRequest,
    current_user: Optional[User] = Depends(get_current_user_optional),
    db: AsyncSession = Depends(get_db)
):
    """
    Inicia processo de checkout a partir de um carrinho.
    
    Cria uma sessão de checkout com cálculo de totais,
    validação de estoque e preparação para pagamento.
    """
    try:
        checkout_service = get_checkout_service(db)
        
        # Determinar tenant_id (placeholder - deve vir do contexto)
        tenant_id = uuid.uuid4()  # TODO: Obter do contexto real
        user_id = current_user.id if current_user else None
        
        checkout_session = await checkout_service.initiate_checkout(
            request=request,
            tenant_id=tenant_id,
            user_id=user_id
        )
        
        return CheckoutResponse(
            success=True,
            message="Checkout iniciado com sucesso",
            checkout_session=checkout_session
        )
        
    except Exception as e:
        logger.error(f"Erro ao iniciar checkout: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno do servidor"
        )


@router.get("/{checkout_id}", response_model=CheckoutSessionRead)
async def get_checkout_session(
    checkout_id: uuid.UUID,
    current_user: Optional[User] = Depends(get_current_user_optional),
    db: AsyncSession = Depends(get_db)
):
    """
    Obtém detalhes de uma sessão de checkout.
    """
    try:
        checkout_service = get_checkout_service(db)
        
        checkout_session = await checkout_service.get_checkout_by_id(checkout_id)
        
        if not checkout_session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Sessão de checkout não encontrada"
            )
        
        # Verificar permissão (usuário deve ser dono da sessão)
        if current_user and checkout_session.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Acesso negado"
            )
        
        return checkout_session
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao obter checkout: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno do servidor"
        )


@router.put("/{checkout_id}", response_model=CheckoutResponse)
async def update_checkout_session(
    checkout_id: uuid.UUID,
    request: CheckoutSessionUpdate,
    current_user: Optional[User] = Depends(get_current_user_optional),
    db: AsyncSession = Depends(get_db)
):
    """
    Atualiza sessão de checkout.
    
    Permite alterar endereços, métodos de entrega e pagamento
    antes da finalização.
    """
    try:
        checkout_service = get_checkout_service(db)
        
        # Verificar se a sessão existe e pertence ao usuário
        existing_session = await checkout_service.get_checkout_by_id(checkout_id)
        if not existing_session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Sessão de checkout não encontrada"
            )
        
        if current_user and existing_session.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Acesso negado"
            )
        
        # Atualizar sessão
        updates = request.model_dump(exclude_unset=True)
        updated_session = await checkout_service.update_checkout_session(
            checkout_id, updates
        )
        
        return CheckoutResponse(
            success=True,
            message="Checkout atualizado com sucesso",
            checkout_session=updated_session
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao atualizar checkout: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno do servidor"
        )


@router.post("/{checkout_id}/complete", response_model=CheckoutResponse)
async def complete_checkout(
    checkout_id: uuid.UUID,
    request: CheckoutCompleteRequest,
    current_user: Optional[User] = Depends(get_current_user_optional),
    db: AsyncSession = Depends(get_db)
):
    """
    Completa o processo de checkout.
    
    Processa pagamento, cria pedido e finaliza a transação.
    """
    try:
        checkout_service = get_checkout_service(db)
        
        # Verificar permissões
        existing_session = await checkout_service.get_checkout_by_id(checkout_id)
        if not existing_session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Sessão de checkout não encontrada"
            )
        
        if current_user and existing_session.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Acesso negado"
            )
        
        # Completar checkout
        result = await checkout_service.complete_checkout(checkout_id, request)
        
        if result['success']:
            return CheckoutResponse(
                success=True,
                message="Checkout completado com sucesso",
                order_id=result['order_id']
            )
        else:
            return CheckoutResponse(
                success=False,
                message=result.get('error', 'Falha no checkout')
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao completar checkout: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno do servidor"
        )


@router.delete("/{checkout_id}/cancel", response_model=CheckoutResponse)
async def cancel_checkout(
    checkout_id: uuid.UUID,
    current_user: Optional[User] = Depends(get_current_user_optional),
    db: AsyncSession = Depends(get_db)
):
    """
    Cancela sessão de checkout.
    """
    try:
        checkout_service = get_checkout_service(db)
        
        # Verificar permissões
        existing_session = await checkout_service.get_checkout_by_id(checkout_id)
        if not existing_session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Sessão de checkout não encontrada"
            )
        
        if current_user and existing_session.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Acesso negado"
            )
        
        # Cancelar checkout
        success = await checkout_service.cancel_checkout(checkout_id)
        
        if success:
            return CheckoutResponse(
                success=True,
                message="Checkout cancelado com sucesso"
            )
        else:
            return CheckoutResponse(
                success=False,
                message="Falha ao cancelar checkout"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao cancelar checkout: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno do servidor"
        )


@router.post("/shipping/quote", response_model=ShippingQuoteResponse)
async def get_shipping_quote(
    request: ShippingQuoteRequest,
    current_user: Optional[User] = Depends(get_current_user_optional),
    db: AsyncSession = Depends(get_db)
):
    """
    Obtém cotação de frete para itens do carrinho.
    
    Calcula custos e prazos de entrega para diferentes
    métodos de envio.
    """
    try:
        # Implementação simplificada de cotação de frete
        quotes = [
            {
                "method": "standard",
                "name": "Entrega Padrão",
                "cost": 10.00,
                "estimated_days": 5,
                "description": "Entrega em até 5 dias úteis"
            },
            {
                "method": "express",
                "name": "Entrega Expressa",
                "cost": 20.00,
                "estimated_days": 2,
                "description": "Entrega em até 2 dias úteis"
            },
            {
                "method": "overnight",
                "name": "Entrega no Próximo Dia",
                "cost": 35.00,
                "estimated_days": 1,
                "description": "Entrega no próximo dia útil"
            }
        ]
        
        estimated_delivery_days = {
            "standard": 5,
            "express": 2,
            "overnight": 1
        }
        
        return ShippingQuoteResponse(
            quotes=quotes,
            estimated_delivery_days=estimated_delivery_days
        )
        
    except Exception as e:
        logger.error(f"Erro ao obter cotação de frete: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno do servidor"
        )
