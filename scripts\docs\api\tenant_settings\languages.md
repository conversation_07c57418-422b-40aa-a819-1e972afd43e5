# Tenant Settings - Languages

**Categoria:** Tenant Settings
**Módulo:** Languages
**Total de Endpoints:** 2
**Gerado em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [PUT /api/tenants/settings/languages/](#put-apitenantssettingslanguages) - Update Language Settings
- [PUT /api/tenants/tenant-settings/languages/](#put-apitenantstenant-settingslanguages) - Update Language Settings

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### LanguageSettingsUpdate

**Descrição:** Schema for updating language settings.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `multi_language_enabled` | unknown | ❌ | Enable multi-language support |
| `available_languages` | unknown | ❌ | List of available language codes |
| `default_language` | unknown | ❌ | Default language code |

### TenantSettingsRead

**Descrição:** Schema for reading tenant settings.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `business_name` | unknown | ❌ | Business name |
| `business_type` | unknown | ❌ | Type of business |
| `tenant_slug` | unknown | ❌ | Unique slug for tenant identification in public URLs |
| `default_currency` | string | ❌ | Default currency code (1:1 ratio for stability) |
| `currency_config` | unknown | ❌ | Multi-currency configuration with exchange rates and formatting |
| `timezone` | string | ❌ | Timezone identifier |
| `operating_hours` | unknown | ❌ | Operating hours configuration by day of week |
| `multi_language_enabled` | boolean | ❌ | Enable multi-language support |
| `available_languages` | unknown | ❌ | List of available language codes |
| `default_language` | string | ❌ | Default language code |
| `loyalty_enabled` | boolean | ❌ | Enable loyalty system |
| `loyalty_config` | unknown | ❌ | Loyalty system configuration |
| `country` | unknown | ❌ | Country code (ISO 3166-1 alpha-2) |
| `address` | unknown | ❌ | Complete address with coordinates |
| `base_tax_rate` | string | ❌ | Base tax rate percentage |
| `tax_calculation_method` | string | ❌ | Tax calculation method: 'incremental' or 'inclusive' |
| `wifi_networks` | unknown | ❌ | WiFi networks configuration by zone |
| `social_media_links` | unknown | ❌ | Social media platform links with icons |
| `subscription_plan` | unknown | ❌ | Current subscription plan |
| `subscription_status` | string | ❌ | Subscription status |
| `additional_settings` | unknown | ❌ | Additional configuration settings |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | unknown | ❌ | - |
| `updated_at` | unknown | ❌ | - |
| `has_social_media_links` | boolean | ❌ | Whether social media links are configured |

## 🔗 Endpoints Detalhados

### PUT /api/tenants/settings/languages/ {#put-apitenantssettingslanguages}

**Resumo:** Update Language Settings
**Descrição:** Update multi-language configuration.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [LanguageSettingsUpdate](#languagesettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/tenants/settings/languages/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PUT /api/tenants/tenant-settings/languages/ {#put-apitenantstenant-settingslanguages}

**Resumo:** Update Language Settings
**Descrição:** Update multi-language configuration.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [LanguageSettingsUpdate](#languagesettingsupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TenantSettingsRead](#tenantsettingsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/tenants/tenant-settings/languages/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
