"""
TCostumer Schemas for EShop B2B System
======================================

Schemas de validação para clientes B2B (TCostumer) com campos específicos
para gestão de crédito, verificação empresarial e termos de pagamento.
"""

import uuid
from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any, List

from pydantic import BaseModel, Field, ConfigDict, validator, EmailStr

from app.modules.core.eshop.models.tcostumer import (
    TCostumerStatus, BusinessType, PaymentTerms
)


class BusinessAddressSchema(BaseModel):
    """Schema para endereço empresarial."""
    
    street: str = Field(..., max_length=255, description="Rua")
    number: str = Field(..., max_length=20, description="Número")
    complement: Optional[str] = Field(None, max_length=100, description="Complemento")
    neighborhood: str = Field(..., max_length=100, description="Bairro")
    city: str = Field(..., max_length=100, description="Cidade")
    state: str = Field(..., max_length=50, description="Estado")
    postal_code: str = Field(..., max_length=20, description="CEP")
    country: str = Field(default='BR', max_length=2, description="País")


class TCostumerBase(BaseModel):
    """Schema base para TCostumer."""
    
    company_name: str = Field(..., max_length=255, description="Nome da empresa")
    business_type: BusinessType = Field(..., description="Tipo de negócio")
    tax_id: str = Field(..., max_length=50, description="CNPJ/Tax ID")
    business_registration_number: Optional[str] = Field(
        None, max_length=100, description="Número de registro empresarial"
    )
    business_phone: Optional[str] = Field(
        None, max_length=20, description="Telefone empresarial"
    )
    business_email: Optional[EmailStr] = Field(
        None, description="Email empresarial"
    )
    website: Optional[str] = Field(
        None, max_length=255, description="Website da empresa"
    )
    payment_terms: PaymentTerms = Field(
        default=PaymentTerms.NET_30, description="Termos de pagamento"
    )
    custom_payment_days: Optional[int] = Field(
        None, ge=1, le=365, description="Dias customizados para pagamento"
    )
    
    @validator('tax_id')
    def validate_tax_id(cls, v):
        """Valida formato do CNPJ/Tax ID."""
        # Remover caracteres especiais
        clean_id = ''.join(filter(str.isdigit, v))
        
        # Validação básica de CNPJ (14 dígitos)
        if len(clean_id) == 14:
            return clean_id
        # Validação básica de CPF (11 dígitos) para MEI
        elif len(clean_id) == 11:
            return clean_id
        else:
            raise ValueError('Tax ID deve ter 11 (CPF) ou 14 (CNPJ) dígitos')


class TCostumerCreate(TCostumerBase):
    """Schema para criação de TCostumer."""
    
    user_id: uuid.UUID = Field(..., description="ID do usuário")
    business_address: Optional[BusinessAddressSchema] = Field(
        None, description="Endereço empresarial"
    )
    credit_limit: Decimal = Field(
        default=Decimal('0.00'), ge=0, description="Limite de crédito"
    )
    default_discount_rate: Decimal = Field(
        default=Decimal('0.00'), ge=0, le=100, description="Taxa de desconto padrão"
    )
    annual_revenue: Optional[Decimal] = Field(
        None, ge=0, description="Receita anual"
    )
    employee_count: Optional[int] = Field(
        None, ge=1, description="Número de funcionários"
    )
    years_in_business: Optional[int] = Field(
        None, ge=0, description="Anos de operação"
    )
    sales_rep_id: Optional[uuid.UUID] = Field(
        None, description="ID do representante de vendas"
    )
    notes: Optional[str] = Field(
        None, max_length=2000, description="Observações"
    )


class TCostumerUpdate(BaseModel):
    """Schema para atualização de TCostumer."""
    
    company_name: Optional[str] = Field(
        None, max_length=255, description="Nome da empresa"
    )
    business_type: Optional[BusinessType] = Field(
        None, description="Tipo de negócio"
    )
    business_registration_number: Optional[str] = Field(
        None, max_length=100, description="Número de registro empresarial"
    )
    business_address: Optional[Dict[str, Any]] = Field(
        None, description="Endereço empresarial"
    )
    business_phone: Optional[str] = Field(
        None, max_length=20, description="Telefone empresarial"
    )
    business_email: Optional[EmailStr] = Field(
        None, description="Email empresarial"
    )
    website: Optional[str] = Field(
        None, max_length=255, description="Website da empresa"
    )
    credit_limit: Optional[Decimal] = Field(
        None, ge=0, description="Limite de crédito"
    )
    payment_terms: Optional[PaymentTerms] = Field(
        None, description="Termos de pagamento"
    )
    custom_payment_days: Optional[int] = Field(
        None, ge=1, le=365, description="Dias customizados para pagamento"
    )
    default_discount_rate: Optional[Decimal] = Field(
        None, ge=0, le=100, description="Taxa de desconto padrão"
    )
    annual_revenue: Optional[Decimal] = Field(
        None, ge=0, description="Receita anual"
    )
    employee_count: Optional[int] = Field(
        None, ge=1, description="Número de funcionários"
    )
    years_in_business: Optional[int] = Field(
        None, ge=0, description="Anos de operação"
    )
    sales_rep_id: Optional[uuid.UUID] = Field(
        None, description="ID do representante de vendas"
    )
    auto_approve_orders: Optional[bool] = Field(
        None, description="Aprovação automática de pedidos"
    )
    require_po_number: Optional[bool] = Field(
        None, description="Requer número de PO"
    )
    notes: Optional[str] = Field(
        None, max_length=2000, description="Observações"
    )


class TCostumerRead(TCostumerBase):
    """Schema para leitura de TCostumer."""
    
    id: uuid.UUID
    tenant_id: uuid.UUID
    user_id: uuid.UUID
    status: TCostumerStatus
    
    # Endereço empresarial
    business_address: Optional[Dict[str, Any]]
    
    # Gestão de crédito
    credit_limit: Decimal
    available_credit: Decimal
    credit_used: Decimal
    
    # Desconto padrão
    default_discount_rate: Decimal
    
    # Verificação empresarial
    business_verification_status: str
    business_verification_date: Optional[datetime]
    business_verification_notes: Optional[str]
    
    # Informações financeiras
    annual_revenue: Optional[Decimal]
    employee_count: Optional[int]
    years_in_business: Optional[int]
    
    # Estatísticas de compras
    total_orders: int
    total_spent: Decimal
    average_order_value: Decimal
    last_order_date: Optional[datetime]
    
    # Configurações de conta
    auto_approve_orders: bool
    require_po_number: bool
    
    # Representante de vendas
    sales_rep_id: Optional[uuid.UUID]
    
    # Metadados
    notes: Optional[str]
    
    # Timestamps
    created_at: datetime
    updated_at: datetime
    approved_at: Optional[datetime]
    last_activity_at: Optional[datetime]
    
    # Propriedades calculadas
    is_verified: bool = False
    is_active: bool = False
    credit_utilization_rate: float = 0.0
    has_available_credit: bool = False
    
    model_config = ConfigDict(from_attributes=True)


class TCostumerApprovalRequest(BaseModel):
    """Schema para aprovação de TCostumer."""
    
    approved: bool = Field(..., description="Aprovado ou rejeitado")
    notes: Optional[str] = Field(
        None, max_length=1000, description="Notas da aprovação/rejeição"
    )
    credit_limit: Optional[Decimal] = Field(
        None, ge=0, description="Limite de crédito aprovado"
    )
    default_discount_rate: Optional[Decimal] = Field(
        None, ge=0, le=100, description="Taxa de desconto aprovada"
    )


class TCostumerCreditUpdateRequest(BaseModel):
    """Schema para atualização de crédito."""
    
    amount: Decimal = Field(..., description="Valor a ser adicionado/subtraído")
    operation: str = Field(..., regex="^(add|subtract)$", description="Operação")
    reason: str = Field(..., max_length=500, description="Motivo da alteração")


class TCostumerSummary(BaseModel):
    """Schema para resumo de TCostumer."""
    
    id: uuid.UUID
    company_name: str
    status: TCostumerStatus
    credit_limit: Decimal
    available_credit: Decimal
    total_orders: int
    total_spent: Decimal
    business_verification_status: str
    created_at: datetime
    
    model_config = ConfigDict(from_attributes=True)


class TCostumerResponse(BaseModel):
    """Schema de resposta padrão para operações de TCostumer."""
    
    success: bool = Field(..., description="Sucesso da operação")
    message: str = Field(..., description="Mensagem de retorno")
    tcostumer: Optional[TCostumerRead] = Field(
        None, description="Dados do TCostumer"
    )
    
    model_config = ConfigDict(from_attributes=True)


class TCostumerStats(BaseModel):
    """Schema para estatísticas de TCostumers."""
    
    total_customers: int = Field(..., description="Total de clientes")
    active_customers: int = Field(..., description="Clientes ativos")
    pending_customers: int = Field(..., description="Clientes pendentes")
    verified_customers: int = Field(..., description="Clientes verificados")
    suspended_customers: int = Field(..., description="Clientes suspensos")
    
    total_credit_limit: Decimal = Field(..., description="Limite total de crédito")
    total_credit_used: Decimal = Field(..., description="Crédito total utilizado")
    total_revenue: Decimal = Field(..., description="Receita total")
    average_order_value: Decimal = Field(..., description="Valor médio do pedido")
    
    # Estatísticas por tipo de negócio
    business_type_stats: Dict[str, int] = Field(
        default_factory=dict, description="Estatísticas por tipo de negócio"
    )
    
    # Estatísticas por termos de pagamento
    payment_terms_stats: Dict[str, int] = Field(
        default_factory=dict, description="Estatísticas por termos de pagamento"
    )
    
    model_config = ConfigDict(from_attributes=True)


class TCostumerListResponse(BaseModel):
    """Schema para resposta de lista de TCostumers."""
    
    customers: List[TCostumerSummary] = Field(..., description="Lista de clientes")
    total: int = Field(..., description="Total de registros")
    page: int = Field(..., description="Página atual")
    limit: int = Field(..., description="Limite por página")
    total_pages: int = Field(..., description="Total de páginas")
    
    model_config = ConfigDict(from_attributes=True)
