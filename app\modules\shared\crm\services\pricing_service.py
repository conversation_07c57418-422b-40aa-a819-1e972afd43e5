"""Pricing service for CRM module."""

import uuid  # noqa: E402
import logging
from typing import List, Optional
from datetime import datetime
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError

from app.modules.shared.crm.models.pricing import (  # noqa: E402
    PricingTier,
    CustomerPricingAssignment,
    PricingRule,
    PricingRuleType,
)
from app.modules.shared.crm.schemas.pricing import (  # noqa: E402
    PricingTierCreate,
    PricingTierUpdate,
    CustomerPricingAssignmentCreate,
    CustomerPricingAssignmentUpdate,
    PricingRuleCreate,
    PricingRuleUpdate,
)

logger = logging.getLogger(__name__)


class PricingService:
    """Service for managing CRM pricing tiers and rules."""

    # ==================== Pricing Tier Methods ====================

    @staticmethod
    async def create_pricing_tier(
        db: AsyncSession, tenant_id: uuid.UUID, tier_in: PricingTierCreate
    ) -> PricingTier:
        """Create a new pricing tier."""
        try:
            tier_data = tier_in.model_dump(exclude_unset=True)
            db_tier = PricingTier(tenant_id=tenant_id, **tier_data)

            db.add(db_tier)
            await db.commit()
            await db.refresh(db_tier)

            return db_tier
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(f"Error creating pricing tier: {e}")
            raise

    @staticmethod
    async def get_pricing_tier(
        db: AsyncSession, tenant_id: uuid.UUID, tier_id: uuid.UUID
    ) -> Optional[PricingTier]:
        """Get a pricing tier by ID."""
        try:
            query = select(PricingTier).where(
                PricingTier.tenant_id == tenant_id, PricingTier.id == tier_id
            )
            result = await db.execute(query)
            return result.scalars().first()
        except SQLAlchemyError as e:
            logger.error(f"Error getting pricing tier {tier_id}: {e}")
            raise

    @staticmethod
    async def get_pricing_tiers(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        is_active: Optional[bool] = None,
    ) -> List[PricingTier]:
        """Get all pricing tiers with optional filtering."""
        try:
            query = select(PricingTier).where(PricingTier.tenant_id == tenant_id)

            # Apply filters if provided
            if is_active is not None:
                query = query.where(PricingTier.is_active == is_active)

            # Apply pagination
            query = query.offset(skip).limit(limit)

            result = await db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            logger.error(f"Error getting pricing tiers: {e}")
            raise

    @staticmethod
    async def update_pricing_tier(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        tier_id: uuid.UUID,
        tier_in: PricingTierUpdate,
    ) -> Optional[PricingTier]:
        """Update a pricing tier."""
        try:
            # Get the tier first to ensure it exists and belongs to the tenant
            db_tier = await PricingService.get_pricing_tier(db, tenant_id, tier_id)
            if not db_tier:
                return None

            # Update the tier
            tier_data = tier_in.model_dump(exclude_unset=True)

            # Update the tier in the database
            query = (
                update(PricingTier)
                .where(PricingTier.id == tier_id, PricingTier.tenant_id == tenant_id)
                .values(**tier_data)
                .execution_options(synchronize_session="fetch")
            )
            await db.execute(query)
            await db.commit()

            # Refresh and return the updated tier
            return await PricingService.get_pricing_tier(db, tenant_id, tier_id)
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(f"Error updating pricing tier {tier_id}: {e}")
            raise

    # ==================== Customer Pricing Assignment Methods ====================

    @staticmethod
    async def assign_pricing_tier_to_customer(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        assignment_in: CustomerPricingAssignmentCreate,
    ) -> CustomerPricingAssignment:
        """Assign a pricing tier to a customer."""
        try:
            # Check if the customer already has an active assignment
            existing_assignments = await PricingService.get_customer_pricing_assignments(
                db, tenant_id, assignment_in.account_id, is_active=True
            )

            # If there are existing active assignments, deactivate them
            for assignment in existing_assignments:
                assignment.is_active = False
                assignment.end_date = datetime.utcnow()

            # Create the new assignment
            assignment_data = assignment_in.model_dump(exclude_unset=True)
            db_assignment = CustomerPricingAssignment(tenant_id=tenant_id, **assignment_data)

            db.add(db_assignment)
            await db.commit()
            await db.refresh(db_assignment)

            return db_assignment
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(f"Error assigning pricing tier to customer: {e}")
            raise

    @staticmethod
    async def get_customer_pricing_assignment(
        db: AsyncSession, tenant_id: uuid.UUID, assignment_id: uuid.UUID
    ) -> Optional[CustomerPricingAssignment]:
        """Get a customer pricing assignment by ID."""
        try:
            query = select(CustomerPricingAssignment).where(
                CustomerPricingAssignment.tenant_id == tenant_id,
                CustomerPricingAssignment.id == assignment_id,
            )
            result = await db.execute(query)
            return result.scalars().first()
        except SQLAlchemyError as e:
            logger.error(f"Error getting customer pricing assignment {assignment_id}: {e}")
            raise

    @staticmethod
    async def get_customer_pricing_assignments(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        account_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        is_active: Optional[bool] = None,
    ) -> List[CustomerPricingAssignment]:
        """Get all pricing assignments for a customer."""
        try:
            query = select(CustomerPricingAssignment).where(
                CustomerPricingAssignment.tenant_id == tenant_id,
                CustomerPricingAssignment.account_id == account_id,
            )

            # Apply filters if provided
            if is_active is not None:
                query = query.where(CustomerPricingAssignment.is_active == is_active)

            # Apply pagination
            query = query.offset(skip).limit(limit)

            result = await db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            logger.error(f"Error getting pricing assignments for customer {account_id}: {e}")
            raise

    @staticmethod
    async def update_customer_pricing_assignment(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        assignment_id: uuid.UUID,
        assignment_in: CustomerPricingAssignmentUpdate,
    ) -> Optional[CustomerPricingAssignment]:
        """Update a customer pricing assignment."""
        try:
            # Get the assignment first to ensure it exists and belongs to the tenant
            db_assignment = await PricingService.get_customer_pricing_assignment(
                db, tenant_id, assignment_id
            )
            if not db_assignment:
                return None

            # Update the assignment
            assignment_data = assignment_in.model_dump(exclude_unset=True)

            # Update the assignment in the database
            query = (
                update(CustomerPricingAssignment)
                .where(
                    CustomerPricingAssignment.id == assignment_id,
                    CustomerPricingAssignment.tenant_id == tenant_id,
                )
                .values(**assignment_data)
                .execution_options(synchronize_session="fetch")
            )
            await db.execute(query)
            await db.commit()

            # Refresh and return the updated assignment
            return await PricingService.get_customer_pricing_assignment(
                db, tenant_id, assignment_id
            )
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(f"Error updating customer pricing assignment {assignment_id}: {e}")
            raise

    # ==================== Pricing Rule Methods ====================

    @staticmethod
    async def create_pricing_rule(
        db: AsyncSession, tenant_id: uuid.UUID, rule_in: PricingRuleCreate
    ) -> PricingRule:
        """Create a new pricing rule."""
        try:
            rule_data = rule_in.model_dump(exclude_unset=True)
            db_rule = PricingRule(tenant_id=tenant_id, **rule_data)

            db.add(db_rule)
            await db.commit()
            await db.refresh(db_rule)

            return db_rule
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(f"Error creating pricing rule: {e}")
            raise

    @staticmethod
    async def get_pricing_rule(
        db: AsyncSession, tenant_id: uuid.UUID, rule_id: uuid.UUID
    ) -> Optional[PricingRule]:
        """Get a pricing rule by ID."""
        try:
            query = select(PricingRule).where(
                PricingRule.tenant_id == tenant_id, PricingRule.id == rule_id
            )
            result = await db.execute(query)
            return result.scalars().first()
        except SQLAlchemyError as e:
            logger.error(f"Error getting pricing rule {rule_id}: {e}")
            raise

    @staticmethod
    async def get_pricing_rules_for_tier(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        tier_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        is_active: Optional[bool] = None,
        rule_type: Optional[PricingRuleType] = None,
    ) -> List[PricingRule]:
        """Get all pricing rules for a tier."""
        try:
            query = select(PricingRule).where(
                PricingRule.tenant_id == tenant_id,
                PricingRule.pricing_tier_id == tier_id,
            )

            # Apply filters if provided
            if is_active is not None:
                query = query.where(PricingRule.is_active == is_active)

            if rule_type:
                query = query.where(PricingRule.rule_type == rule_type)

            # Apply pagination
            query = query.offset(skip).limit(limit)

            result = await db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            logger.error(f"Error getting pricing rules for tier {tier_id}: {e}")
            raise

    @staticmethod
    async def update_pricing_rule(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        rule_id: uuid.UUID,
        rule_in: PricingRuleUpdate,
    ) -> Optional[PricingRule]:
        """Update a pricing rule."""
        try:
            # Get the rule first to ensure it exists and belongs to the tenant
            db_rule = await PricingService.get_pricing_rule(db, tenant_id, rule_id)
            if not db_rule:
                return None

            # Update the rule
            rule_data = rule_in.model_dump(exclude_unset=True)

            # Update the rule in the database
            query = (
                update(PricingRule)
                .where(PricingRule.id == rule_id, PricingRule.tenant_id == tenant_id)
                .values(**rule_data)
                .execution_options(synchronize_session="fetch")
            )
            await db.execute(query)
            await db.commit()

            # Refresh and return the updated rule
            return await PricingService.get_pricing_rule(db, tenant_id, rule_id)
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(f"Error updating pricing rule {rule_id}: {e}")
            raise


# Create a singleton instance
pricing_service = PricingService()
