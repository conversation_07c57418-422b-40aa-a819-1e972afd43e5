"""
Notifications API Router

Router principal para as APIs de notificações.
"""

from fastapi import APIRouter

from .notifications_api import router as notifications_router
from .metrics_api import router as metrics_router
from .queue_api import router as queue_router

router = APIRouter()

# Inclui todos os sub-routers
router.include_router(
    notifications_router,
    prefix="/notifications",
    tags=["Notifications"]
)

router.include_router(
    metrics_router,
    prefix="/notifications/metrics",
    tags=["Notification Metrics"]
)

router.include_router(
    queue_router,
    prefix="/notifications/queue",
    tags=["Notification Queue"]
)
