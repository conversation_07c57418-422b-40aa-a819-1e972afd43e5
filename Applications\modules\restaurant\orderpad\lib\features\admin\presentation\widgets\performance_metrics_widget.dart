import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';

class PerformanceMetricsWidget extends StatefulWidget {
  final PerformanceData performanceData;
  final bool isLoading;
  final VoidCallback? onRefresh;

  const PerformanceMetricsWidget({
    super.key,
    required this.performanceData,
    this.isLoading = false,
    this.onRefresh,
  });

  @override
  State<PerformanceMetricsWidget> createState() => _PerformanceMetricsWidgetState();
}

class _PerformanceMetricsWidgetState extends State<PerformanceMetricsWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late List<AnimationController> _progressControllers;
  late List<Animation<double>> _progressAnimations;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _progressControllers = List.generate(
      4,
      (index) => AnimationController(
        duration: Duration(milliseconds: 800 + (index * 200)),
        vsync: this,
      ),
    );
    
    _progressAnimations = _progressControllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeOutCubic),
      );
    }).toList();
    
    _startAnimations();
  }

  void _startAnimations() {
    _animationController.forward();
    for (int i = 0; i < _progressControllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 100), () {
        if (mounted) {
          _progressControllers[i].forward();
        }
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    for (final controller in _progressControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.analytics,
                  color: theme.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Métricas de Performance',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                
                if (widget.onRefresh != null)
                  IconButton(
                    onPressed: widget.onRefresh,
                    icon: const Icon(Icons.refresh),
                    tooltip: 'Atualizar',
                  ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            if (widget.isLoading)
              _buildLoadingState()
            else
              _buildMetricsContent(theme),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(child: _buildLoadingMetric()),
            const SizedBox(width: 16),
            Expanded(child: _buildLoadingMetric()),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(child: _buildLoadingMetric()),
            const SizedBox(width: 16),
            Expanded(child: _buildLoadingMetric()),
          ],
        ),
      ],
    );
  }

  Widget _buildLoadingMetric() {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(12),
      ),
    );
  }

  Widget _buildMetricsContent(ThemeData theme) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: AnimatedBuilder(
                animation: _progressAnimations[0],
                builder: (context, child) {
                  return _buildMetricCard(
                    title: 'Tempo Médio de Preparo',
                    value: '${widget.performanceData.averagePrepTime.toStringAsFixed(1)} min',
                    progress: widget.performanceData.averagePrepTime / 60,
                    animatedProgress: _progressAnimations[0].value * (widget.performanceData.averagePrepTime / 60),
                    color: Colors.blue,
                    icon: Icons.timer,
                    theme: theme,
                    target: '< 15 min',
                  );
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: AnimatedBuilder(
                animation: _progressAnimations[1],
                builder: (context, child) {
                  return _buildMetricCard(
                    title: 'Satisfação do Cliente',
                    value: '${(widget.performanceData.customerSatisfaction * 100).toStringAsFixed(1)}%',
                    progress: widget.performanceData.customerSatisfaction,
                    animatedProgress: _progressAnimations[1].value * widget.performanceData.customerSatisfaction,
                    color: Colors.green,
                    icon: Icons.sentiment_very_satisfied,
                    theme: theme,
                    target: '> 90%',
                  );
                },
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        Row(
          children: [
            Expanded(
              child: AnimatedBuilder(
                animation: _progressAnimations[2],
                builder: (context, child) {
                  return _buildMetricCard(
                    title: 'Taxa de Rotatividade',
                    value: '${widget.performanceData.turnoverRate.toStringAsFixed(1)}/h',
                    progress: widget.performanceData.turnoverRate / 10,
                    animatedProgress: _progressAnimations[2].value * (widget.performanceData.turnoverRate / 10),
                    color: Colors.orange,
                    icon: Icons.rotate_right,
                    theme: theme,
                    target: '> 3/h',
                  );
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: AnimatedBuilder(
                animation: _progressAnimations[3],
                builder: (context, child) {
                  return _buildMetricCard(
                    title: 'Ticket Médio',
                    value: 'R\$ ${widget.performanceData.averageTicketValue.toStringAsFixed(2)}',
                    progress: widget.performanceData.averageTicketValue / 200,
                    animatedProgress: _progressAnimations[3].value * (widget.performanceData.averageTicketValue / 200),
                    color: Colors.purple,
                    icon: Icons.receipt_long,
                    theme: theme,
                    target: '> R\$ 80',
                  );
                },
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 24),
        
        _buildPerformanceChart(theme),
      ],
    );
  }

  Widget _buildMetricCard({
    required String title,
    required String value,
    required double progress,
    required double animatedProgress,
    required Color color,
    required IconData icon,
    required ThemeData theme,
    required String target,
  }) {
    final clampedProgress = progress.clamp(0.0, 1.0);
    final clampedAnimatedProgress = animatedProgress.clamp(0.0, 1.0);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 6,
                  vertical: 2,
                ),
                decoration: BoxDecoration(
                  color: _getPerformanceColor(clampedProgress).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  _getPerformanceLabel(clampedProgress),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: _getPerformanceColor(clampedProgress),
                    fontWeight: FontWeight.w600,
                    fontSize: 10,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          Text(
            title,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              fontWeight: FontWeight.w500,
            ),
          ),
          
          const SizedBox(height: 4),
          
          Text(
            value,
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          
          const SizedBox(height: 8),
          
          Row(
            children: [
              Expanded(
                child: LinearProgressIndicator(
                  value: clampedAnimatedProgress,
                  backgroundColor: color.withValues(alpha: 0.2),
                  valueColor: AlwaysStoppedAnimation<Color>(color),
                  minHeight: 4,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 4),
          
          Text(
            'Meta: $target',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceChart(ThemeData theme) {
    return Container(
      height: 200,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.dividerColor.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Performance Semanal',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 16),
          
          Expanded(
            child: AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return BarChart(
                  BarChartData(
                    alignment: BarChartAlignment.spaceAround,
                    maxY: 100,
                    barTouchData: BarTouchData(
                      enabled: true,
                      touchTooltipData: BarTouchTooltipData(
                        tooltipBgColor: theme.cardColor,
                        tooltipRoundedRadius: 8,
                        getTooltipItem: (group, groupIndex, rod, rodIndex) {
                          return BarTooltipItem(
                            '${_getWeekDay(group.x.toInt())}\n${rod.toY.toStringAsFixed(1)}%',
                            TextStyle(
                              color: theme.colorScheme.onSurface,
                              fontWeight: FontWeight.bold,
                            ),
                          );
                        },
                      ),
                    ),
                    titlesData: FlTitlesData(
                      show: true,
                      rightTitles: const AxisTitles(
                        sideTitles: SideTitles(showTitles: false),
                      ),
                      topTitles: const AxisTitles(
                        sideTitles: SideTitles(showTitles: false),
                      ),
                      bottomTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          getTitlesWidget: (value, meta) {
                            return Padding(
                              padding: const EdgeInsets.only(top: 8),
                              child: Text(
                                _getWeekDay(value.toInt()),
                                style: theme.textTheme.bodySmall,
                              ),
                            );
                          },
                        ),
                      ),
                      leftTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          reservedSize: 40,
                          getTitlesWidget: (value, meta) {
                            return Text(
                              '${value.toInt()}%',
                              style: theme.textTheme.bodySmall,
                            );
                          },
                        ),
                      ),
                    ),
                    borderData: FlBorderData(show: false),
                    barGroups: widget.performanceData.weeklyPerformance
                        .asMap()
                        .entries
                        .map((entry) {
                      return BarChartGroupData(
                        x: entry.key,
                        barRods: [
                          BarChartRodData(
                            toY: entry.value * _animationController.value,
                            color: theme.primaryColor,
                            width: 16,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(4),
                              topRight: Radius.circular(4),
                            ),
                          ),
                        ],
                      );
                    }).toList(),
                    gridData: FlGridData(
                      show: true,
                      drawVerticalLine: false,
                      horizontalInterval: 25,
                      getDrawingHorizontalLine: (value) {
                        return FlLine(
                          color: theme.dividerColor.withValues(alpha: 0.3),
                          strokeWidth: 1,
                        );
                      },
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Color _getPerformanceColor(double progress) {
    if (progress >= 0.8) return Colors.green;
    if (progress >= 0.6) return Colors.orange;
    return Colors.red;
  }

  String _getPerformanceLabel(double progress) {
    if (progress >= 0.8) return 'EXCELENTE';
    if (progress >= 0.6) return 'BOM';
    return 'PRECISA MELHORAR';
  }

  String _getWeekDay(int index) {
    const days = ['Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb', 'Dom'];
    return days[index % days.length];
  }
}

class PerformanceData {
  final double averagePrepTime;
  final double customerSatisfaction;
  final double turnoverRate;
  final double averageTicketValue;
  final List<double> weeklyPerformance;

  const PerformanceData({
    required this.averagePrepTime,
    required this.customerSatisfaction,
    required this.turnoverRate,
    required this.averageTicketValue,
    required this.weeklyPerformance,
  });

  factory PerformanceData.mock() {
    return const PerformanceData(
      averagePrepTime: 12.5,
      customerSatisfaction: 0.92,
      turnoverRate: 3.8,
      averageTicketValue: 85.50,
      weeklyPerformance: [85, 92, 78, 95, 88, 90, 87],
    );
  }
}

class CompactPerformanceWidget extends StatelessWidget {
  final PerformanceData performanceData;
  final VoidCallback? onTap;

  const CompactPerformanceWidget({
    super.key,
    required this.performanceData,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.analytics,
                    color: theme.primaryColor,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Performance',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              Row(
                children: [
                  Expanded(
                    child: _buildCompactMetric(
                      'Satisfação',
                      '${(performanceData.customerSatisfaction * 100).toStringAsFixed(0)}%',
                      Colors.green,
                      theme,
                    ),
                  ),
                  Expanded(
                    child: _buildCompactMetric(
                      'Preparo',
                      '${performanceData.averagePrepTime.toStringAsFixed(0)}min',
                      Colors.blue,
                      theme,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCompactMetric(
    String title,
    String value,
    Color color,
    ThemeData theme,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          value,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          title,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }
}