import uuid
from sqlalchemy import <PERSON>umn, <PERSON>te<PERSON>, String, ForeignKey, Index, Boolean, Table
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.orm import relationship

from app.db.base import Base
from app.modules.core.tenants.models.tenant import Tenant


# Association table for Product-OptionalGroup many-to-many relationship
eshop_product_optional_groups_association = Table(
    'eshop_product_optional_groups_association',
    Base.metadata,
    Column('product_id', PG_UUID(as_uuid=True), ForeignKey('eshop_products.id'), primary_key=True),
    Column('optional_group_id', PG_UUID(as_uuid=True), ForeignKey('eshop_product_optional_groups.id'), primary_key=True),
    Column('tenant_id', PG_UUID(as_uuid=True), ForeignKey('tenants.id'), nullable=True),  # For tenant isolation
    Index('ix_eshop_product_optional_groups_assoc_product_id', 'product_id'),
    Index('ix_eshop_product_optional_groups_assoc_optional_group_id', 'optional_group_id'),
    Index('ix_eshop_product_optional_groups_assoc_tenant_id', 'tenant_id'),
)


class ProductOptionalGroup(Base):
    """
    Represents a group of optional add-ons for a product (e.g., Accessories, Related Products).
    Similar to modifier groups but specifically for optional extras.
    """

    __tablename__ = "eshop_product_optional_groups"

    # Primary key
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Tenant isolation
    tenant_id = Column(PG_UUID(as_uuid=True), ForeignKey(Tenant.id), nullable=True, index=True)
    
    # Group properties
    name = Column(String(100), nullable=False)  # e.g., "Accessories", "Related Products", "Extras"
    description = Column(String(255), nullable=True)  # Optional description
    min_selection = Column(Integer, default=0, nullable=False)  # Usually 0 for optional groups
    max_selection = Column(Integer, default=5, nullable=False)  # Allow multiple optional selections
    display_order = Column(Integer, default=0, nullable=False)  # Order within the product
    
    # Group status
    is_required = Column(Boolean, default=False, nullable=False)  # Usually false for optional groups
    is_active = Column(Boolean, default=True, nullable=False)  # Is this group active?
    
    # Template system
    is_template = Column(Boolean, default=False, nullable=False)  # Is this a template group?
    template_id = Column(PG_UUID(as_uuid=True), ForeignKey("eshop_product_optional_groups.id"), nullable=True)  # Reference to template
    
    # Relationships
    tenant = relationship("Tenant")
    template = relationship("ProductOptionalGroup", remote_side=[id])
    
    # Many-to-many relationship with Product
    products = relationship(
        "app.modules.core.eshop.models.product.Product",
        secondary=eshop_product_optional_groups_association,
        back_populates="optional_groups"
    )
    
    options = relationship(
        "ProductOptionalOption", 
        back_populates="optional_group", 
        cascade="all, delete-orphan"
    )

    __table_args__ = (
        Index("ix_eshop_optional_groups_tenant_id", "tenant_id"),
        Index("ix_eshop_optional_groups_tenant_id_name", "tenant_id", "name"),
        Index("ix_eshop_optional_groups_template_id", "template_id"),
    )

    def __repr__(self):
        return f"<ProductOptionalGroup(id={self.id}, name='{self.name}', tenant_id={self.tenant_id})>"
