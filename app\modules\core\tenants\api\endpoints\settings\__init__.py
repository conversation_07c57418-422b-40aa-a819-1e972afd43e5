"""
API endpoints for tenant settings management - modular structure.
"""

from fastapi import APIRouter

from app.modules.core.tenants.api.endpoints.settings.main import router as main_router
from app.modules.core.tenants.api.endpoints.settings.business import router as business_router
from app.modules.core.tenants.api.endpoints.settings.operating_hours import router as hours_router
from app.modules.core.tenants.api.endpoints.settings.languages import router as languages_router
from app.modules.core.tenants.api.endpoints.settings.loyalty import router as loyalty_router
from app.modules.core.tenants.api.endpoints.settings.location import router as location_router
from app.modules.core.tenants.api.endpoints.settings.tax import router as tax_router

from app.modules.core.tenants.api.endpoints.settings.currency import router as currency_router
from app.modules.core.tenants.api.endpoints.settings.social_media import router as social_media_router
from app.modules.core.tenants.api.endpoints.settings.categories import router as categories_router

# Main router for tenant settings
router = APIRouter()

# Include all sub-routers
router.include_router(main_router, tags=["Tenant Settings - Main"])
router.include_router(business_router, prefix="/business", tags=["Tenant Settings - Business"])
router.include_router(hours_router, prefix="/operating-hours", tags=["Tenant Settings - Hours"])
router.include_router(languages_router, prefix="/languages", tags=["Tenant Settings - Languages"])
router.include_router(loyalty_router, prefix="/loyalty", tags=["Tenant Settings - Loyalty"])
router.include_router(location_router, prefix="/location", tags=["Tenant Settings - Location"])
router.include_router(tax_router, prefix="/tax", tags=["Tenant Settings - Tax"])

router.include_router(currency_router, prefix="/currency", tags=["Tenant Settings - Currency"])
router.include_router(social_media_router, prefix="/social-media", tags=["Tenant Settings - Social Media"])
router.include_router(categories_router, prefix="/categories", tags=["Tenant Settings - Categories"])

__all__ = ["router"]
