"""
Pydantic schemas for tenant payment method configuration operations.
"""

import uuid
from datetime import datetime
from typing import Optional, Dict, Any, List

from pydantic import BaseModel, ConfigDict, Field, validator


class TenantPaymentMethodConfigBase(BaseModel):
    """Base schema for tenant payment method configuration."""
    
    is_enabled: bool = Field(
        True, 
        description="Whether this payment method is enabled for the tenant"
    )
    display_order: int = Field(
        0, 
        ge=0, 
        description="Display order for this payment method"
    )
    custom_config: Optional[Dict[str, Any]] = Field(
        None, 
        description="Custom configuration for this payment method"
    )
    custom_name: Optional[str] = Field(
        None, 
        max_length=100, 
        description="Custom display name for this payment method"
    )
    custom_icon: Optional[str] = Field(
        None, 
        max_length=255, 
        description="Custom icon URL or name for this payment method"
    )
    is_default: bool = Field(
        False, 
        description="Whether this is the default payment method for the tenant"
    )
    fee_percentage: Optional[float] = Field(
        None, 
        ge=0, 
        le=100, 
        description="Fee percentage for this payment method"
    )
    fee_fixed: Optional[float] = Field(
        None, 
        ge=0, 
        description="Fixed fee for this payment method"
    )
    min_amount: Optional[float] = Field(
        None, 
        ge=0, 
        description="Minimum transaction amount for this payment method"
    )
    max_amount: Optional[float] = Field(
        None, 
        ge=0, 
        description="Maximum transaction amount for this payment method"
    )

    @validator('max_amount')
    def validate_max_amount(cls, v, values):
        """Validate that max_amount is greater than min_amount if both are set."""
        if v is not None and 'min_amount' in values and values['min_amount'] is not None:
            if v <= values['min_amount']:
                raise ValueError('Maximum amount must be greater than minimum amount')
        return v


class TenantPaymentMethodConfigCreate(TenantPaymentMethodConfigBase):
    """Schema for creating tenant payment method configuration."""
    
    payment_method_id: uuid.UUID = Field(
        ..., 
        description="ID of the payment method to configure"
    )


class TenantPaymentMethodConfigUpdate(BaseModel):
    """Schema for updating tenant payment method configuration."""
    
    is_enabled: Optional[bool] = Field(
        None, 
        description="Whether this payment method is enabled for the tenant"
    )
    display_order: Optional[int] = Field(
        None, 
        ge=0, 
        description="Display order for this payment method"
    )
    custom_config: Optional[Dict[str, Any]] = Field(
        None, 
        description="Custom configuration for this payment method"
    )
    custom_name: Optional[str] = Field(
        None, 
        max_length=100, 
        description="Custom display name for this payment method"
    )
    custom_icon: Optional[str] = Field(
        None, 
        max_length=255, 
        description="Custom icon URL or name for this payment method"
    )
    is_default: Optional[bool] = Field(
        None, 
        description="Whether this is the default payment method for the tenant"
    )
    fee_percentage: Optional[float] = Field(
        None, 
        ge=0, 
        le=100, 
        description="Fee percentage for this payment method"
    )
    fee_fixed: Optional[float] = Field(
        None, 
        ge=0, 
        description="Fixed fee for this payment method"
    )
    min_amount: Optional[float] = Field(
        None, 
        ge=0, 
        description="Minimum transaction amount for this payment method"
    )
    max_amount: Optional[float] = Field(
        None, 
        ge=0, 
        description="Maximum transaction amount for this payment method"
    )

    @validator('max_amount')
    def validate_max_amount(cls, v, values):
        """Validate that max_amount is greater than min_amount if both are set."""
        if v is not None and 'min_amount' in values and values['min_amount'] is not None:
            if v <= values['min_amount']:
                raise ValueError('Maximum amount must be greater than minimum amount')
        return v


class TenantPaymentMethodConfigRead(TenantPaymentMethodConfigBase):
    """Schema for reading tenant payment method configuration."""
    
    id: uuid.UUID
    tenant_id: uuid.UUID
    payment_method_id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    
    model_config = ConfigDict(from_attributes=True)


class TenantPaymentMethodConfigWithMethod(TenantPaymentMethodConfigRead):
    """Schema for reading tenant payment method configuration with payment method details."""
    
    payment_method: Optional[Dict[str, Any]] = Field(
        None, 
        description="Payment method details"
    )
    
    model_config = ConfigDict(from_attributes=True)


# Bulk operations schemas
class PaymentMethodConfigBulkUpdate(BaseModel):
    """Schema for bulk updating payment method configurations."""
    
    configs: List[Dict[str, Any]] = Field(
        ..., 
        description="List of payment method configurations to update"
    )


class PaymentMethodConfigBulkResponse(BaseModel):
    """Schema for bulk update response."""
    
    updated_count: int = Field(
        ..., 
        description="Number of configurations updated"
    )
    errors: Optional[List[Dict[str, Any]]] = Field(
        None, 
        description="List of errors encountered during bulk update"
    )
