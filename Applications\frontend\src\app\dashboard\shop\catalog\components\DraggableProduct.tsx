'use client';

import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import Image from 'next/image';
import {
  PencilIcon,
  TrashIcon,
  Bars3Icon,
  EyeIcon,
  EyeSlashIcon,
  PhotoIcon,
  CubeIcon,
  TagIcon
} from '@heroicons/react/24/outline';

interface DraggableProductProps {
  product: any;
  category?: any;
  viewMode: 'grid' | 'list';
  onEdit: (product: any) => void;
  onDelete: (productId: string) => void;
  isActive: boolean;
  isLoading?: boolean;
}

export function DraggableProduct({
  product,
  category,
  viewMode,
  onEdit,
  onDelete,
  isActive,
  isLoading = false
}: DraggableProductProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: product.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEdit(product);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete(product.id);
  };

  if (viewMode === 'grid') {
    return (
      <div
        ref={setNodeRef}
        style={style}
        className={`group relative bg-white/90 backdrop-blur-sm rounded-lg border
          transition-all duration-300 ease-out cursor-pointer select-none
          min-h-[200px] touch-manipulation
          active:scale-[0.98]
          ${
          isDragging
            ? 'shadow-xl border-blue-400 scale-105 rotate-1 z-50'
            : isActive
            ? 'border-blue-300 shadow-md hover:shadow-lg'
            : 'border-gray-200 hover:border-blue-300 hover:shadow-md hover:scale-[1.02]'
        } ${isLoading ? 'opacity-50 pointer-events-none' : ''}`}
      >
        {/* Drag Handle */}
        <div
          {...attributes}
          {...listeners}
          className="absolute top-2 left-2 p-1 text-gray-400 hover:text-gray-600 
            cursor-grab active:cursor-grabbing opacity-0 group-hover:opacity-100 
            transition-opacity z-10 bg-white/80 rounded"
        >
          <Bars3Icon className="h-4 w-4" />
        </div>

        {/* Product Image */}
        <div className="aspect-square bg-gray-100 rounded-t-lg relative overflow-hidden">
          {product.images && product.images.length > 0 ? (
            <Image
              src={product.images[0].url}
              alt={product.name}
              fill
              className="object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <PhotoIcon className="h-12 w-12 text-gray-400" />
            </div>
          )}
          
          {/* Status indicator */}
          {!product.is_active && (
            <div className="absolute top-2 right-2 p-1 bg-red-100 rounded-full">
              <EyeSlashIcon className="h-4 w-4 text-red-600" />
            </div>
          )}
        </div>

        {/* Product Info */}
        <div className="p-4">
          <div className="flex items-start justify-between mb-2">
            <h4 className="font-medium text-gray-900 text-sm leading-tight line-clamp-2">
              {product.name}
            </h4>
          </div>
          
          {product.description && (
            <p className="text-xs text-gray-500 mb-3 line-clamp-2">
              {product.description}
            </p>
          )}
          
          <div className="flex items-center justify-between">
            <div>
              <span className="text-lg font-bold text-green-600">
                ${parseFloat(product.base_price).toFixed(2)}
              </span>
              {product.stock_quantity !== undefined && (
                <p className="text-xs text-gray-500">
                  Stock: {product.stock_quantity}
                </p>
              )}
            </div>
            
            {category && (
              <div className="flex items-center text-xs text-gray-500">
                <TagIcon className="h-3 w-3 mr-1" />
                <span className="truncate max-w-[80px]">{category.name}</span>
              </div>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="absolute top-2 right-2 flex items-center space-x-1 
          opacity-0 group-hover:opacity-100 transition-opacity">
          <button
            onClick={handleEdit}
            className="p-1.5 text-blue-600 hover:bg-blue-50 rounded transition-colors bg-white/80"
            title="Edit Product"
          >
            <PencilIcon className="h-3.5 w-3.5" />
          </button>
          
          <button
            onClick={handleDelete}
            className="p-1.5 text-red-600 hover:bg-red-50 rounded transition-colors bg-white/80"
            title="Delete Product"
          >
            <TrashIcon className="h-3.5 w-3.5" />
          </button>
        </div>
      </div>
    );
  }

  // List view
  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`group relative bg-white/90 backdrop-blur-sm rounded-lg border
        transition-all duration-300 ease-out cursor-pointer select-none
        min-h-[80px] touch-manipulation
        active:scale-[0.98]
        ${
        isDragging
          ? 'shadow-xl border-blue-400 scale-105 z-50'
          : isActive
          ? 'border-blue-300 shadow-md hover:shadow-lg'
          : 'border-gray-200 hover:border-blue-300 hover:shadow-md'
      } ${isLoading ? 'opacity-50 pointer-events-none' : ''}`}
    >
      {/* Drag Handle */}
      <div
        {...attributes}
        {...listeners}
        className="absolute left-2 top-1/2 transform -translate-y-1/2 p-1 
          text-gray-400 hover:text-gray-600 cursor-grab active:cursor-grabbing 
          opacity-0 group-hover:opacity-100 transition-opacity z-10"
      >
        <Bars3Icon className="h-4 w-4" />
      </div>

      {/* Content */}
      <div className="flex items-center p-4 pl-10">
        {/* Product Image */}
        <div className="relative w-16 h-16 bg-gray-100 rounded-lg flex-shrink-0 overflow-hidden">
          {product.images && product.images.length > 0 ? (
            <Image
              src={product.images[0].url}
              alt={product.name}
              fill
              className="object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <PhotoIcon className="h-6 w-6 text-gray-400" />
            </div>
          )}
        </div>

        {/* Product Info */}
        <div className="flex-1 ml-4 min-w-0">
          <div className="flex items-center space-x-2 mb-1">
            <h4 className="font-medium text-gray-900 truncate">
              {product.name}
            </h4>
            {!product.is_active && (
              <EyeSlashIcon className="h-4 w-4 text-red-600 flex-shrink-0" />
            )}
          </div>
          
          {product.description && (
            <p className="text-sm text-gray-500 truncate mb-1">
              {product.description}
            </p>
          )}
          
          <div className="flex items-center space-x-4 text-sm">
            <span className="font-bold text-green-600">
              ${parseFloat(product.base_price).toFixed(2)}
            </span>
            
            {product.stock_quantity !== undefined && (
              <span className="text-gray-500">
                Stock: {product.stock_quantity}
              </span>
            )}
            
            {category && (
              <div className="flex items-center text-gray-500">
                <TagIcon className="h-3 w-3 mr-1" />
                <span>{category.name}</span>
              </div>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <button
            onClick={handleEdit}
            className="p-2 text-blue-600 hover:bg-blue-50 rounded transition-colors"
            title="Edit Product"
          >
            <PencilIcon className="h-4 w-4" />
          </button>
          
          <button
            onClick={handleDelete}
            className="p-2 text-red-600 hover:bg-red-50 rounded transition-colors"
            title="Delete Product"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
}
