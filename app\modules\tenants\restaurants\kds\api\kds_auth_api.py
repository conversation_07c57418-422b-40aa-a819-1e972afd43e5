"""
APIs para autenticação e códigos temporários do KDS.
"""
from fastapi import APIRouter, Depends, HTTPException, status
from typing import Annotated, Any, TYPE_CHECKING
import uuid
from datetime import datetime, timezone

# Use TYPE_CHECKING to avoid circular import
if TYPE_CHECKING:
    from app.modules.core.users.models.user import User
from app.modules.core.tenants.models.tenant import Tenant
from app.modules.core.auth.security import create_access_token
from app.modules.core.auth.services.auth_service import auth_service

from app.modules.tenants.restaurants.kds.schemas.kds_temp_code import (
    KDSTempCodeCreate,
    KDSTempCodeGenerate,
    KDSAuthRequest,
    KDSAuthResponse,
    KDSAuthError,
)
from app.modules.tenants.restaurants.kds.services.kds_temp_code_service import KDSTempCodeService

from app.modules.core.auth.dependencies.auth_dependencies import (
    get_current_active_user,
)
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    get_current_tenant_from_header,
    require_tenant_role,
)
from app.core.db_dependencies import get_db
from app.modules.core.roles.models.roles import RolePermissions
from sqlalchemy.ext.asyncio import AsyncSession

router = APIRouter()

# Apenas owners e managers podem gerar códigos temporários
generate_code_roles = RolePermissions.ADMIN_ROLES  # OWNER, MANAGER


@router.post("/generate-temp-code", response_model=KDSTempCodeGenerate)
async def generate_kds_temp_code(
    expires_hours: int = 24,
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(generate_code_roles, tenant_id_source="header"))] = None,
):
    """
    Gera um código temporário de 6 dígitos para autenticação do KDS.
    Apenas OWNERS e MANAGERS podem gerar códigos.
    
    Args:
        expires_hours: Horas até expiração (padrão: 24h, máximo: 168h)
    
    Returns:
        Código gerado com informações de expiração
    """
    if expires_hours < 1 or expires_hours > 168:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Horas de expiração devem estar entre 1 e 168 (7 dias)"
        )
    
    temp_code_service = KDSTempCodeService()
    
    try:
        temp_code = await temp_code_service.generate_temp_code(
            db=db,
            tenant_id=current_tenant.id,
            expires_hours=expires_hours
        )
        
        return KDSTempCodeGenerate(
            code=temp_code.code,
            expires_at=temp_code.expires_at,
            expires_in_hours=expires_hours,
            tenant_id=current_tenant.id,
            message=f"Código temporário gerado com sucesso. Válido por {expires_hours} horas."
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao gerar código temporário: {str(e)}"
        )


@router.post("/auth", response_model=KDSAuthResponse)
async def authenticate_kds(
    auth_request: KDSAuthRequest,
    db: AsyncSession = Depends(get_db),
):
    """
    Autentica um app KDS usando UUID do restaurante + código temporário de 6 dígitos.
    Endpoint público - não requer autenticação prévia.
    
    Args:
        auth_request: UUID do restaurante e código temporário
    
    Returns:
        Token JWT para acesso às APIs do KDS
    """
    temp_code_service = KDSTempCodeService()
    
    try:
        # Validar código temporário
        temp_code = await temp_code_service.validate_temp_code(
            db=db,
            restaurant_uuid=auth_request.restaurant_uuid,
            temp_code=auth_request.temp_code
        )
        
        if not temp_code:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Código temporário inválido, expirado ou já utilizado",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Buscar informações do tenant
        tenant = await db.get(Tenant, auth_request.restaurant_uuid)
        if not tenant or not tenant.is_active:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Restaurante não encontrado ou inativo"
            )
        
        # Marcar código como usado
        await temp_code_service.mark_code_as_used(db, temp_code)
        
        # Criar token JWT especial para KDS
        # Usamos o tenant_id como subject para identificar o restaurante
        access_token = create_access_token(
            subject=str(tenant.id),
            additional_claims={
                "type": "kds_auth",
                "tenant_id": str(tenant.id),
                "tenant_name": tenant.name,
                "auth_method": "temp_code"
            }
        )
        
        return KDSAuthResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=3600,  # 1 hora
            tenant_id=tenant.id,
            tenant_name=tenant.name
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro interno durante autenticação: {str(e)}"
        )


@router.get("/current-code")
async def get_current_temp_code(
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    _: Annotated[Any, Depends(require_tenant_role(generate_code_roles, tenant_id_source="header"))] = None,
):
    """
    Obtém o código temporário ativo atual para o tenant.
    Apenas OWNERS e MANAGERS podem visualizar códigos.
    
    Returns:
        Código ativo se existir, None caso contrário
    """
    temp_code_service = KDSTempCodeService()
    
    try:
        active_code = await temp_code_service.get_active_code_for_tenant(
            db=db,
            tenant_id=current_tenant.id
        )
        
        if not active_code:
            return {
                "message": "Nenhum código temporário ativo encontrado",
                "has_active_code": False
            }
        
        return {
            "code": active_code.code,
            "expires_at": active_code.expires_at,
            "created_at": active_code.created_at,
            "is_valid": active_code.is_valid,
            "has_active_code": True,
            "message": "Código temporário ativo encontrado"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao buscar código ativo: {str(e)}"
        )
