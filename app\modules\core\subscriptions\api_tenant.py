from typing import List, Annotated, Optional

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.dependencies import get_db, get_current_active_user, require_tenant_role
from app.core.roles import TenantRole  # Import roles needed for tenant actions
from app.models.user import User
from . import schemas, services

# Roteador específico para endpoints de assinatura voltados ao tenant
router = APIRouter()

AuthUserDep = Annotated[User, Depends(get_current_active_user)]
DbDep = Annotated[AsyncSession, Depends(get_db)]

# Dependência para verificar se o usuário tem permissão de gerenciar a assinatura no tenant
# Ajustar os papéis conforme necessário (ex: OWNER, ADMIN)
ManageSubscriptionDep = Annotated[
    User,
    Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER], tenant_id_source="header")),
]  # Passar lista de Enums


# ============================================
# Tenant: Plan Viewing Endpoints
# ============================================


@router.get(
    "/subscription/plans/",
    response_model=List[schemas.Plan],
    summary="Tenant: Listar Planos Disponíveis",
    # Não requer role específica, apenas usuário autenticado no contexto do tenant? Verificar.
    # Se qualquer usuário do tenant pode ver, usar apenas AuthUserDep.
    # Se precisar de role, usar ManageSubscriptionDep ou criar uma dependência mais permissiva.
    # Assume qualquer usuário logado pode ver os planos
    dependencies=[Depends(get_current_active_user)],
)
async def tenant_read_available_plans(
    db: DbDep,
    skip: int = 0,
    limit: int = 100,
):
    """Lista os planos de assinatura ativos que podem ser escolhidos pelo tenant."""
    # Retorna apenas planos ativos
    plans = await services.get_plans(db=db, skip=skip, limit=limit, is_active=True)
    # Transforma para o schema de resposta, carregando features
    response_plans = []
    for plan in plans:
        # É preciso carregar as features de cada plano aqui se get_plans não o fizer
        db_plan_with_features = await services.get_plan(db, plan_id=plan.id)
        if db_plan_with_features:
            plan_data = schemas.Plan.from_orm(db_plan_with_features)
            plan_data.features = [
                schemas.PlanFeatureValue(key=link.feature.key, value=link.value)
                for link in db_plan_with_features.features_links
            ]
            response_plans.append(plan_data)
    return response_plans


# ============================================
# Tenant: Current Subscription Endpoints
# ============================================


@router.get(
    "/subscription/current/",
    response_model=Optional[schemas.TenantSubscription],  # Pode ser nulo se não tiver assinatura
    summary="Tenant: Ver Assinatura Atual",
    # Requer permissão para ver detalhes da assinatura
    dependencies=[Depends(ManageSubscriptionDep)],
)
async def tenant_read_current_subscription(
    current_user: ManageSubscriptionDep,  # Obtém o usuário validado pela dependência
    db: DbDep,
):
    """Obtém os detalhes da assinatura ativa ou mais recente do tenant atual."""
    # O tenant_id é inferido pelo ManageSubscriptionDep ou pelo contexto da requisição
    # Assumindo que o tenant_id pode ser extraído do usuário ou de um contexto
    # Se ManageSubscriptionDep não prover isso, precisaremos de get_current_tenant

    # Exemplo: Precisamos obter o tenant_id do header ou de outra forma
    # Esta parte precisa ser ajustada conforme a lógica de obtenção do tenant_id para o usuário logado  # noqa: E501
    # Por enquanto, vamos assumir que temos o tenant_id
    # tenant_id = await get_tenant_id_for_user(current_user) # Função hipotética

    # Simulação - Substituir pela lógica real de obtenção do tenant_id
    # Vamos buscar o primeiro tenant associado ao usuário para este exemplo
    # IMPORTANTE: Esta lógica pode não ser a correta para a aplicação real.
    # Precisa de uma forma clara de saber qual tenant o usuário está gerenciando na sessão.
    if not current_user.tenants:  # Assumindo que current_user tem a lista de tenants
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Usuário não associado a nenhum tenant.",
        )

    # Usando o ID do primeiro tenant associado como exemplo
    # A forma correta seria usar o tenant_id do header via get_current_tenant
    tenant_id = current_user.tenants[0].id  # !!! AJUSTAR ESTA LÓGICA !!!

    # Mostra a mais recente, mesmo inativa
    subscription = await services.get_tenant_subscription(
        db=db, tenant_id=tenant_id, active_only=False
    )
    return subscription


@router.post(
    "/subscription/change_plan/",
    # O response_model dependerá da Fase 2/3 (pode ser status, URL de checkout, etc.)
    response_model=schemas.TenantSubscription,  # Placeholder - ajustar na Fase 2
    summary="Tenant: Iniciar Mudança de Plano",
    dependencies=[Depends(ManageSubscriptionDep)],
)
async def tenant_request_plan_change(
    current_user: ManageSubscriptionDep,
    plan_change_request: schemas.ChangePlanRequest,
    db: DbDep,
):
    """Inicia o processo de mudança para um novo plano de assinatura."""
    # Implementação da Fase 2/3:
    # 1. Obter tenant_id (ajustar lógica como no GET /current)
    # 2. Validar o new_plan_id
    # 3. Chamar serviço para processar a mudança (pode envolver gateway de pagamento na Fase 3)
    # 4. Retornar resposta apropriada
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="Endpoint de mudança de plano ainda não implementado.",
    )


@router.post(
    "/subscription/cancel/",
    response_model=schemas.TenantSubscription,  # Retorna a assinatura cancelada
    summary="Tenant: Cancelar Assinatura Atual",
    dependencies=[Depends(ManageSubscriptionDep)],
)
async def tenant_request_subscription_cancellation(
    current_user: ManageSubscriptionDep,
    cancel_request: schemas.CancelSubscriptionRequest,
    db: DbDep,
):
    """Solicita o cancelamento da assinatura ativa do tenant."""
    # Implementação da Fase 2:
    # 1. Obter tenant_id (ajustar lógica como no GET /current)
    # 2. Encontrar a assinatura ativa
    # 3. Chamar serviço para marcar como cancelada (definir end_date, status, cancelled_at, reason)
    # 4. Retornar a assinatura atualizada
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="Endpoint de cancelamento de assinatura ainda não implementado.",
    )
