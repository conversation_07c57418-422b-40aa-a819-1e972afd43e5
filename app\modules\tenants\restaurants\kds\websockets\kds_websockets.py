import uuid
import logging
from typing import Dict, Any
from app.core.socketio_manager import SocketIOManager, sio_server
from app.modules.tenants.restaurants.kds.schemas.kitchen_order import KitchenOrderRead

logger = logging.getLogger(__name__)


async def emit_new_order(tenant_id: uuid.UUID, order: KitchenOrderRead) -> None:
    """
    Emite um evento WebSocket quando um novo pedido é criado.

    Args:
        tenant_id: ID do tenant para o qual emitir o evento
        order: Dados do pedido criado
    """
    try:
        # Converter o modelo Pydantic para um dicionário
        order_data = order.model_dump()

        # Emitir evento para o tenant
        await SocketIOManager.emit_to_tenant(
            tenant_id=tenant_id,
            event="kds_new_order",
            data=order_data,
        )
        logger.info(f"Emitido evento kds_new_order para tenant {tenant_id}, pedido {order.id}")
    except Exception as e:
        logger.error(f"Erro ao emitir evento kds_new_order: {e}")


async def emit_order_update(tenant_id: uuid.UUID, order: KitchenOrderRead) -> None:
    """
    Emite um evento WebSocket quando um pedido é atualizado.

    Args:
        tenant_id: ID do tenant para o qual emitir o evento
        order: Dados atualizados do pedido
    """
    try:
        # Converter o modelo Pydantic para um dicionário
        order_data = order.model_dump()

        # Emitir evento para o tenant
        await SocketIOManager.emit_to_tenant(
            tenant_id=tenant_id,
            event="kds_order_update",
            data=order_data,
        )
        logger.info(
            f"Emitido evento kds_order_update para tenant {tenant_id}, pedido {order.id}, status {order.status}"  # noqa: E501
        )
    except Exception as e:
        logger.error(f"Erro ao emitir evento kds_order_update: {e}")


def register_kds_handlers(sio) -> None:
    """
    Registra todos os handlers de WebSocket do KDS.

    Args:
        sio: Instância do servidor Socket.IO
    """

    @sio.on("connect")
    async def handle_connect(sid: str, environ: dict, auth: dict = None) -> None:
        """
        Handler para conexão WebSocket.
        """
        try:
            logger.info(f"🔌 Nova tentativa de conexão WebSocket - SID: {sid}")
            logger.info(f"🔌 Auth data: {auth}")
            logger.info(f"🔌 Environ keys: {list(environ.keys())}")

            # Obter tenant_id dos query parameters ou auth
            tenant_id = None

            if auth and 'tenant_id' in auth:
                tenant_id = auth['tenant_id']
                logger.info(f"🔌 Tenant ID encontrado no auth: {tenant_id}")
            elif 'QUERY_STRING' in environ:
                query_string = environ['QUERY_STRING']
                logger.info(f"🔌 Query string: {query_string}")
                if 'tenant_id=' in query_string:
                    for param in query_string.split('&'):
                        if param.startswith('tenant_id='):
                            tenant_id = param.split('=')[1]
                            logger.info(f"🔌 Tenant ID encontrado na query: {tenant_id}")
                            break

            if tenant_id:
                # Salvar tenant_id na sessão
                await sio.save_session(sid, {'tenant_id': tenant_id})
                logger.info(f"✅ Cliente {sid} conectado com tenant_id: {tenant_id}")
            else:
                logger.warning(f"⚠️ Cliente {sid} conectado sem tenant_id")

        except Exception as e:
            logger.error(f"❌ Erro ao processar conexão: {e}")

    @sio.on("join_kds_room")
    async def handle_join_kds_room(sid: str, data: Dict[str, Any] = None) -> None:
        """
        Handler para cliente entrar na sala do KDS.
        """
        try:
            # Obter informações da sessão
            session = await sio.get_session(sid)
            tenant_id = session.get("tenant_id")

            if not tenant_id:
                logger.warning(f"Cliente {sid} tentou entrar na sala KDS sem tenant_id")
                return

            # Entrar na sala específica do tenant para KDS
            room_name = f"kds_tenant_{tenant_id}"
            await sio.enter_room(sid, room_name)

            logger.info(f"Cliente {sid} entrou na sala KDS do tenant {tenant_id}")

            # Confirmar entrada na sala
            await sio.emit("kds_room_joined", {"room": room_name}, room=sid)

        except Exception as e:
            logger.error(f"Erro ao processar join_kds_room: {e}")

    @sio.on("leave_kds_room")
    async def handle_leave_kds_room(sid: str, data: Dict[str, Any] = None) -> None:
        """
        Handler para cliente sair da sala do KDS.
        """
        try:
            # Obter informações da sessão
            session = await sio.get_session(sid)
            tenant_id = session.get("tenant_id")

            if not tenant_id:
                return

            # Sair da sala específica do tenant para KDS
            room_name = f"kds_tenant_{tenant_id}"
            await sio.leave_room(sid, room_name)

            logger.info(f"Cliente {sid} saiu da sala KDS do tenant {tenant_id}")

        except Exception as e:
            logger.error(f"Erro ao processar leave_kds_room: {e}")

    logger.info("Handlers de WebSocket do KDS registrados com sucesso")
