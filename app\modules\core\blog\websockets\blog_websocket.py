"""
Blog WebSocket Router

WebSocket endpoints for real-time blog features.
"""

import json
import uuid
from typing import Dict, Any
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
from fastapi.websockets import WebSocketState

# from app.core.dependencies import get_current_user_websocket  # TODO: Implement if needed
from .blog_notifications import BlogNotificationManager

router = APIRouter()
notification_manager = BlogNotificationManager()


@router.websocket("/ws/blog/{client_id}")
async def blog_websocket_endpoint(
    websocket: WebSocket,
    client_id: str,
    user_id: str = None,
):
    """
    WebSocket endpoint for blog real-time features.

    Supports:
    - New comment notifications
    - Post publication notifications
    - Live comment updates
    - Admin moderation notifications
    """
    await websocket.accept()

    # Register client
    await notification_manager.connect(client_id, websocket, user_id)

    try:
        while True:
            # Wait for messages from client
            data = await websocket.receive_text()

            try:
                message = json.loads(data)
                await handle_websocket_message(client_id, message)
            except json.JSONDecodeError:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "Invalid JSON format"
                }))
            except Exception as e:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": f"Error processing message: {str(e)}"
                }))

    except WebSocketDisconnect:
        await notification_manager.disconnect(client_id)
    except Exception as e:
        print(f"WebSocket error: {e}")
        await notification_manager.disconnect(client_id)


async def handle_websocket_message(client_id: str, message: Dict[str, Any]):
    """
    Handle incoming WebSocket messages from clients.

    Args:
        client_id: Client identifier
        message: Parsed message data
    """
    message_type = message.get("type")

    if message_type == "subscribe_post":
        # Subscribe to notifications for a specific post
        post_id = message.get("post_id")
        if post_id:
            await notification_manager.subscribe_to_post(client_id, post_id)

    elif message_type == "unsubscribe_post":
        # Unsubscribe from post notifications
        post_id = message.get("post_id")
        if post_id:
            await notification_manager.unsubscribe_from_post(client_id, post_id)

    elif message_type == "subscribe_admin":
        # Subscribe to admin notifications (moderation, etc.)
        await notification_manager.subscribe_to_admin(client_id)

    elif message_type == "ping":
        # Heartbeat/ping message
        await notification_manager.send_to_client(client_id, {
            "type": "pong",
            "timestamp": str(uuid.uuid4())
        })

    elif message_type == "typing":
        # User is typing a comment
        post_id = message.get("post_id")
        user_name = message.get("user_name", "Anonymous")

        if post_id:
            await notification_manager.broadcast_to_post(post_id, {
                "type": "user_typing",
                "post_id": post_id,
                "user_name": user_name,
                "client_id": client_id
            }, exclude_client=client_id)

    elif message_type == "stop_typing":
        # User stopped typing
        post_id = message.get("post_id")

        if post_id:
            await notification_manager.broadcast_to_post(post_id, {
                "type": "user_stop_typing",
                "post_id": post_id,
                "client_id": client_id
            }, exclude_client=client_id)


@router.websocket("/ws/blog/admin/{admin_id}")
async def blog_admin_websocket_endpoint(
    websocket: WebSocket,
    admin_id: str,
):
    """
    WebSocket endpoint for blog admin features.

    Provides real-time notifications for:
    - New comments awaiting moderation
    - Spam detection alerts
    - User activity monitoring
    """
    await websocket.accept()

    # Register admin client
    await notification_manager.connect(f"admin_{admin_id}", websocket, admin_id)
    await notification_manager.subscribe_to_admin(f"admin_{admin_id}")

    try:
        while True:
            # Wait for admin messages
            data = await websocket.receive_text()

            try:
                message = json.loads(data)

                # Handle admin-specific messages
                if message.get("type") == "moderate_comment":
                    comment_id = message.get("comment_id")
                    action = message.get("action")  # approve, reject, spam

                    # TODO: Implement comment moderation
                    # This would trigger the actual moderation action
                    # and broadcast the result to relevant clients

                    await notification_manager.send_to_client(f"admin_{admin_id}", {
                        "type": "moderation_result",
                        "comment_id": comment_id,
                        "action": action,
                        "status": "success"
                    })

            except json.JSONDecodeError:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "Invalid JSON format"
                }))

    except WebSocketDisconnect:
        await notification_manager.disconnect(f"admin_{admin_id}")
    except Exception as e:
        print(f"Admin WebSocket error: {e}")
        await notification_manager.disconnect(f"admin_{admin_id}")
