"""Dependencies for financial module."""

from typing import Optional
from fastapi import Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.modules.core.auth.dependencies.auth_dependencies import get_current_user
from app.core.database import get_db
from app.modules.core.users.models.user import User
from app.modules.core.tenants.models.tenant import Tenant


async def get_current_tenant_user(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> tuple[User, Optional[Tenant]]:
    """
    Get current user and their associated tenant for financial operations.
    
    Returns:
        Tuple of (user, tenant) where tenant can be None for admin users
    """
    # For now, return user and None tenant - will be enhanced when tenant 
    # association logic is implemented
    return current_user, None


async def require_tenant_access(
    user_tenant: tuple[User, Optional[Tenant]] = Depends(get_current_tenant_user)
) -> tuple[User, Tenant]:
    """
    Require that the current user has access to a tenant for financial operations.
    
    Raises:
        HTTPException: If user doesn't have tenant access
        
    Returns:
        Tuple of (user, tenant)
    """
    user, tenant = user_tenant
    
    if not tenant:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Tenant access required for financial operations"
        )
    
    return user, tenant


async def get_financial_db_session(
    db: AsyncSession = Depends(get_db)
) -> AsyncSession:
    """Get database session for financial operations."""
    return db
