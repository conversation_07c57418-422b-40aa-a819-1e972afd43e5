'use client';

import React, { useState } from 'react';
import LayoutCanvas from './LayoutCanvas';
import { Table, TableStatus } from '@/types/pos';
import { 
  DEFAULT_GRID_CONFIG, 
  ZOOM_LEVELS, 
  CANVAS_DIMENSIONS,
  GridConfig 
} from '@/utils/table/GridPatterns';
import {
  MagnifyingGlassMinusIcon,
  MagnifyingGlassPlusIcon,
  Squares2X2Icon,
  EyeIcon,
  EyeSlashIcon,
  PlusIcon,
  TrashIcon
} from '@heroicons/react/24/outline';

// Mock data for demonstration
const mockTables: Table[] = [
  {
    id: 'table-1',
    tenant_id: 'demo-tenant',
    table_number: '1',
    name: 'Mesa Principal',
    capacity: 4,
    zone: 'Zona 1',
    status: 'available' as TableStatus,
    position_x: 100,
    position_y: 100,
    width: 120,
    height: 80,
    is_active: true,
    qrcode_enabled: true,
    number: '1',
    shape: 'rectangle'
  },
  {
    id: 'table-2',
    tenant_id: 'demo-tenant',
    table_number: '2',
    name: 'Mesa VIP',
    capacity: 6,
    zone: 'Zona 1',
    status: 'occupied' as TableStatus,
    position_x: 300,
    position_y: 150,
    width: 140,
    height: 100,
    is_active: true,
    qrcode_enabled: true,
    number: '2',
    shape: 'rectangle'
  },
  {
    id: 'table-3',
    tenant_id: 'demo-tenant',
    table_number: '3',
    name: 'Mesa Redonda',
    capacity: 8,
    zone: 'Zona da Terraça',
    status: 'reserved' as TableStatus,
    position_x: 500,
    position_y: 200,
    width: 120,
    height: 120,
    is_active: true,
    qrcode_enabled: true,
    number: '3',
    shape: 'circle'
  }
];

const zones = ['Zona 1', 'Zona da Terraça'];

export default function CanvasDemo() {
  const [tables, setTables] = useState<Table[]>(mockTables);
  const [selectedZone, setSelectedZone] = useState<string | null>(null);
  const [selectedTable, setSelectedTable] = useState<Table | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [zoom, setZoom] = useState<number>(ZOOM_LEVELS.default);
  const [gridConfig, setGridConfig] = useState<GridConfig>(DEFAULT_GRID_CONFIG);

  const handleTableUpdate = (updatedTable: Table) => {
    setTables(prevTables => 
      prevTables.map(table => 
        table.id === updatedTable.id ? updatedTable : table
      )
    );
  };

  const handleTableCreate = (position: { x: number; y: number }) => {
    const newTable: Table = {
      id: `table-${Date.now()}`,
      tenant_id: 'demo-tenant',
      table_number: `${tables.length + 1}`,
      name: `Nova Mesa ${tables.length + 1}`,
      capacity: 4,
      zone: selectedZone || 'Zona 1',
      status: 'available' as TableStatus,
      position_x: position.x,
      position_y: position.y,
      width: 120,
      height: 80,
      is_active: true,
      qrcode_enabled: true,
      number: `${tables.length + 1}`,
      shape: 'rectangle'
    };

    setTables(prevTables => [...prevTables, newTable]);
  };

  const handleTableDelete = (table: Table) => {
    if (confirm(`Tem certeza que deseja deletar a ${table.name}?`)) {
      setTables(prevTables => prevTables.filter(t => t.id !== table.id));
      if (selectedTable?.id === table.id) {
        setSelectedTable(null);
      }
    }
  };

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + ZOOM_LEVELS.step, ZOOM_LEVELS.max));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - ZOOM_LEVELS.step, ZOOM_LEVELS.min));
  };

  const handleZoomReset = () => {
    setZoom(ZOOM_LEVELS.default);
  };

  const toggleGrid = () => {
    setGridConfig(prev => ({ ...prev, showGrid: !prev.showGrid }));
  };

  const toggleSnapToGrid = () => {
    setGridConfig(prev => ({ ...prev, snapToGrid: !prev.snapToGrid }));
  };

  const filteredTables = selectedZone 
    ? tables.filter(table => table.zone === selectedZone)
    : tables;

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Demo: Layout Canvas com Sistema Visual
        </h2>
        <p className="text-gray-600">
          Canvas interativo com drag-and-drop, grid, zoom e controles avançados
        </p>
      </div>

      {/* Controls */}
      <div className="mb-6 bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex flex-wrap items-center gap-4">
          {/* Zone Selector */}
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700">Zona:</label>
            <select
              value={selectedZone || ''}
              onChange={(e) => setSelectedZone(e.target.value || null)}
              className="text-sm border border-gray-300 rounded px-2 py-1"
            >
              <option value="">Todas as zonas</option>
              {zones.map(zone => (
                <option key={zone} value={zone}>{zone}</option>
              ))}
            </select>
          </div>

          {/* Zoom Controls */}
          <div className="flex items-center space-x-2">
            <button
              onClick={handleZoomOut}
              className="p-1 border border-gray-300 rounded hover:bg-gray-50"
              title="Zoom Out"
            >
              <MagnifyingGlassMinusIcon className="h-4 w-4" />
            </button>
            <span className="text-sm text-gray-600 min-w-[60px] text-center">
              {Math.round(zoom * 100)}%
            </span>
            <button
              onClick={handleZoomIn}
              className="p-1 border border-gray-300 rounded hover:bg-gray-50"
              title="Zoom In"
            >
              <MagnifyingGlassPlusIcon className="h-4 w-4" />
            </button>
            <button
              onClick={handleZoomReset}
              className="px-2 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50"
            >
              Reset
            </button>
          </div>

          {/* Grid Controls */}
          <div className="flex items-center space-x-2">
            <button
              onClick={toggleGrid}
              className={`p-1 border rounded ${
                gridConfig.showGrid 
                  ? 'bg-blue-100 border-blue-300 text-blue-700' 
                  : 'border-gray-300 hover:bg-gray-50'
              }`}
              title="Toggle Grid"
            >
              {gridConfig.showGrid ? <EyeIcon className="h-4 w-4" /> : <EyeSlashIcon className="h-4 w-4" />}
            </button>
            <button
              onClick={toggleSnapToGrid}
              className={`p-1 border rounded ${
                gridConfig.snapToGrid 
                  ? 'bg-green-100 border-green-300 text-green-700' 
                  : 'border-gray-300 hover:bg-gray-50'
              }`}
              title="Toggle Snap to Grid"
            >
              <Squares2X2Icon className="h-4 w-4" />
            </button>
          </div>

          {/* Edit Mode */}
          <div className="flex items-center space-x-2">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={isEditing}
                onChange={(e) => setIsEditing(e.target.checked)}
                className="rounded"
              />
              <span className="text-sm text-gray-700">Modo Edição</span>
            </label>
          </div>
        </div>

        {/* Selected Table Info */}
        {selectedTable && (
          <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
            <h4 className="font-medium text-blue-900">Mesa Selecionada:</h4>
            <p className="text-blue-800 text-sm">
              {selectedTable.name} - {selectedTable.capacity} pessoas - {selectedTable.zone}
            </p>
            <p className="text-blue-700 text-xs">
              Posição: ({selectedTable.position_x}, {selectedTable.position_y}) | 
              Tamanho: {selectedTable.width}x{selectedTable.height}
            </p>
          </div>
        )}
      </div>

      {/* Canvas */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="text-lg font-semibold">Layout das Mesas</h3>
          <div className="text-sm text-gray-600">
            {filteredTables.length} mesa(s) {selectedZone && `em ${selectedZone}`}
          </div>
        </div>
        
        <div className="border border-gray-300 rounded-lg overflow-auto max-h-[600px]">
          <LayoutCanvas
            tables={tables}
            selectedZone={selectedZone || undefined}
            selectedTable={selectedTable}
            isEditing={isEditing}
            gridConfig={gridConfig}
            zoom={zoom}
            canvasWidth={CANVAS_DIMENSIONS.defaultWidth}
            canvasHeight={CANVAS_DIMENSIONS.defaultHeight}
            onTableUpdate={handleTableUpdate}
            onTableCreate={handleTableCreate}
            onTableSelect={setSelectedTable}
            onTableEdit={(table) => console.log('Edit table:', table)}
            onTableDelete={handleTableDelete}
            onZoomChange={setZoom}
          />
        </div>
      </div>

      {/* Debug Info */}
      <div className="mt-6 bg-white rounded-lg border border-gray-200 p-4">
        <h3 className="text-lg font-semibold mb-3">Debug Info</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="font-medium">Total de mesas:</span> {tables.length}
          </div>
          <div>
            <span className="font-medium">Zona selecionada:</span> {selectedZone || 'Todas'}
          </div>
          <div>
            <span className="font-medium">Mesa selecionada:</span> {selectedTable?.name || 'Nenhuma'}
          </div>
          <div>
            <span className="font-medium">Modo edição:</span> {isEditing ? 'Ativo' : 'Inativo'}
          </div>
          <div>
            <span className="font-medium">Zoom:</span> {Math.round(zoom * 100)}%
          </div>
          <div>
            <span className="font-medium">Grid:</span> {gridConfig.showGrid ? 'Visível' : 'Oculto'}
          </div>
          <div>
            <span className="font-medium">Snap to Grid:</span> {gridConfig.snapToGrid ? 'Ativo' : 'Inativo'}
          </div>
          <div>
            <span className="font-medium">Grid Size:</span> {gridConfig.cellSize}px
          </div>
        </div>
      </div>
    </div>
  );
}
