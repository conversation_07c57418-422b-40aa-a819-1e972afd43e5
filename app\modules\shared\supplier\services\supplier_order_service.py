import uuid
from datetime import datetime
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, func, update
from sqlalchemy.orm import selectinload

from app.modules.shared.supplier.models.supplier_order import (
    SupplierOrder,
    SupplierOrderItem,
    SupplierOrderHistory,
    OrderStatus,
    ItemAvailability
)
from app.modules.shared.supplier.schemas.supplier_order import (
    SupplierOrderCreate,
    SupplierOrderUpdate,
    SupplierOrderRead,
    SupplierOrderWithItems,
    OrderAvailabilityUpdate,
    SupplierOrderStats
)
from app.core.exceptions import NotFoundError


class SupplierOrderService:
    """Serviço para gerenciar pedidos de suppliers."""

    async def create_order(
        self,
        db: AsyncSession,
        *,
        order_data: SupplierOrderCreate,
        tenant_id: uuid.UUID
    ) -> SupplierOrderWithItems:
        """Cria um novo pedido para supplier."""
        
        # Gerar número do pedido
        order_number = await self._generate_order_number(db, tenant_id)
        
        # Criar o pedido
        db_order = SupplierOrder(
            tenant_id=tenant_id,
            supplier_id=order_data.supplier_id,
            order_number=order_number,
            status=OrderStatus.PENDING,
            notes=order_data.notes
        )
        
        db.add(db_order)
        await db.flush()  # Para obter o ID
        
        # Criar os itens do pedido
        total_amount = 0.0
        for item_data in order_data.items:
            db_item = SupplierOrderItem(
                order_id=db_order.id,
                shopping_list_item_id=item_data.shopping_list_item_id,
                name=item_data.name,
                quantity_requested=item_data.quantity_requested,
                unit_price=item_data.unit_price,
                notes=item_data.notes
            )
            
            if item_data.unit_price:
                db_item.total_price = item_data.unit_price * item_data.quantity_requested
                total_amount += db_item.total_price
            
            db.add(db_item)
        
        # Atualizar total do pedido
        db_order.total_amount = total_amount if total_amount > 0 else None
        
        # Criar histórico
        await self._create_history_entry(
            db, db_order.id, None, OrderStatus.PENDING, "Sistema"
        )
        
        await db.commit()
        await db.refresh(db_order)
        
        # Buscar com itens
        return await self.get_order_with_items(db, db_order.id, tenant_id)

    async def get_orders_by_supplier(
        self,
        db: AsyncSession,
        *,
        supplier_user_id: uuid.UUID,
        status: Optional[OrderStatus] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[SupplierOrderWithItems]:
        """Busca pedidos para um supplier específico."""
        
        # Primeiro, buscar os tenants onde o usuário é supplier
        from app.modules.core.users.models.tenant_user_association import TenantUserAssociation
        
        supplier_tenants_query = select(TenantUserAssociation.tenant_id).where(
            and_(
                TenantUserAssociation.user_id == supplier_user_id,
                TenantUserAssociation.role == "supplier"
            )
        )
        
        supplier_tenants_result = await db.execute(supplier_tenants_query)
        allowed_tenant_ids = [row[0] for row in supplier_tenants_result.all()]
        
        if not allowed_tenant_ids:
            return []
        
        # Buscar pedidos
        query = select(SupplierOrder).options(
            selectinload(SupplierOrder.items)
        ).where(
            SupplierOrder.tenant_id.in_(allowed_tenant_ids)
        )
        
        if status:
            query = query.where(SupplierOrder.status == status)
        
        query = query.order_by(SupplierOrder.created_at.desc())
        query = query.offset(skip).limit(limit)
        
        result = await db.execute(query)
        orders = result.scalars().all()
        
        return [SupplierOrderWithItems.from_orm(order) for order in orders]

    async def update_order_availability(
        self,
        db: AsyncSession,
        *,
        availability_data: OrderAvailabilityUpdate,
        supplier_user_id: uuid.UUID
    ) -> SupplierOrderWithItems:
        """Atualiza a disponibilidade dos itens de um pedido."""
        
        # Verificar se o supplier tem acesso a este pedido
        order = await self._get_order_for_supplier(
            db, availability_data.order_id, supplier_user_id
        )
        
        if not order:
            raise NotFoundError("Order not found or access denied")
        
        # Atualizar itens
        total_amount = 0.0
        all_available = True
        any_available = False
        
        for item_update in availability_data.items:
            # Buscar o item
            result = await db.execute(
                select(SupplierOrderItem).where(
                    and_(
                        SupplierOrderItem.id == item_update.item_id,
                        SupplierOrderItem.order_id == availability_data.order_id
                    )
                )
            )
            item = result.scalar_one_or_none()
            
            if not item:
                continue
            
            # Atualizar item
            item.availability = item_update.availability
            item.quantity_available = item_update.quantity_available
            item.quantity_shipped = item_update.quantity_shipped
            item.notes = item_update.notes
            
            if item_update.unit_price:
                item.unit_price = item_update.unit_price
                if item.quantity_available:
                    item.total_price = item_update.unit_price * item.quantity_available
                    total_amount += item.total_price
            
            # Verificar disponibilidade geral
            if item_update.availability == ItemAvailability.UNAVAILABLE:
                all_available = False
            elif item_update.availability in [ItemAvailability.AVAILABLE, ItemAvailability.PARTIAL]:
                any_available = True
            else:
                all_available = False
        
        # Atualizar status do pedido baseado na disponibilidade
        new_status = order.status
        if all_available and any_available:
            new_status = OrderStatus.CONFIRMED
        elif any_available:
            new_status = OrderStatus.PARTIALLY_AVAILABLE
        
        # Atualizar pedido
        order.total_amount = total_amount if total_amount > 0 else None
        order.invoice_number = availability_data.invoice_number
        order.delivery_note = availability_data.delivery_note
        order.notes = availability_data.notes
        
        if new_status != order.status:
            old_status = order.status
            order.status = new_status
            if new_status == OrderStatus.CONFIRMED:
                order.confirmed_at = datetime.utcnow()
            
            # Criar histórico
            await self._create_history_entry(
                db, order.id, old_status, new_status, f"supplier:{supplier_user_id}"
            )
        
        await db.commit()
        await db.refresh(order)
        
        return await self.get_order_with_items(db, order.id, order.tenant_id)

    async def get_order_with_items(
        self,
        db: AsyncSession,
        order_id: uuid.UUID,
        tenant_id: uuid.UUID
    ) -> SupplierOrderWithItems:
        """Busca um pedido com seus itens."""
        
        result = await db.execute(
            select(SupplierOrder)
            .options(selectinload(SupplierOrder.items))
            .where(
                and_(
                    SupplierOrder.id == order_id,
                    SupplierOrder.tenant_id == tenant_id
                )
            )
        )
        
        order = result.scalar_one_or_none()
        if not order:
            raise NotFoundError("Order not found")
        
        return SupplierOrderWithItems.from_orm(order)

    async def _get_order_for_supplier(
        self,
        db: AsyncSession,
        order_id: uuid.UUID,
        supplier_user_id: uuid.UUID
    ) -> Optional[SupplierOrder]:
        """Verifica se o supplier tem acesso ao pedido."""
        
        # Buscar tenants do supplier
        from app.modules.core.users.models.tenant_user_association import TenantUserAssociation
        
        supplier_tenants_query = select(TenantUserAssociation.tenant_id).where(
            and_(
                TenantUserAssociation.user_id == supplier_user_id,
                TenantUserAssociation.role == "supplier"
            )
        )
        
        supplier_tenants_result = await db.execute(supplier_tenants_query)
        allowed_tenant_ids = [row[0] for row in supplier_tenants_result.all()]
        
        if not allowed_tenant_ids:
            return None
        
        # Buscar pedido
        result = await db.execute(
            select(SupplierOrder).where(
                and_(
                    SupplierOrder.id == order_id,
                    SupplierOrder.tenant_id.in_(allowed_tenant_ids)
                )
            )
        )
        
        return result.scalar_one_or_none()

    async def _generate_order_number(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID
    ) -> str:
        """Gera um número único para o pedido."""
        
        # Buscar último número do tenant
        result = await db.execute(
            select(func.count(SupplierOrder.id))
            .where(SupplierOrder.tenant_id == tenant_id)
        )
        
        count = result.scalar() or 0
        return f"ORD-{datetime.now().strftime('%Y%m%d')}-{count + 1:04d}"

    async def _create_history_entry(
        self,
        db: AsyncSession,
        order_id: uuid.UUID,
        previous_status: Optional[OrderStatus],
        new_status: OrderStatus,
        changed_by: str
    ) -> None:
        """Cria entrada no histórico."""
        
        history = SupplierOrderHistory(
            order_id=order_id,
            previous_status=previous_status.value if previous_status else None,
            new_status=new_status.value,
            changed_by=changed_by
        )
        
        db.add(history)


# Instância do serviço
supplier_order_service = SupplierOrderService()
