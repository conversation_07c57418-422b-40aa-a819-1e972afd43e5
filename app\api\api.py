from fastapi import APIRouter

# Import health and models endpoints
from app.api.endpoints import health_router, models_router

# Import core module routers
from app.modules.core.auth.api.router import router as auth_router
from app.modules.core.users.api.router import router as users_router
from app.modules.core.tenants.api.router import router as tenants_router

# Import shared module routers
from app.modules.core.functions.pos import (
    router as pos_router,
)  # Importa o router do módulo POS

# Importa e renomeia o router correto
from app.modules.core.functions.inventory.api.inventory_api import router as inventory_router  # noqa: E402
from app.modules.shared.shopping_list import router as shopping_list_router  # noqa: E402
from app.modules.shared.hr.api import router as hr_router  # Importa o router de HR
from app.modules.shared.supplier.api.supplier_api import router as supplier_router
from app.modules.shared.supplier.api.supplier_tenant_api import router as supplier_tenant_router  # noqa: E402

# Importa o router do módulo Allergens
from app.modules.core.functions.allergens import router as allergens_router

# Importa o router de Orders
from app.modules.core.functions.orders.api import orders_router
from app.modules.shared.financial.api import router as financial_router

# Importa o router do módulo CRM
from app.modules.shared.crm import router as crm_router  # noqa: E402

# Removido - será incluído via restaurants_api_router
# from app.modules.tenants.restaurants.api import kds_api
# Removido - será incluído via restaurants_api_router
# from app.modules.tenants.restaurants.api import menu_api
# Removido - será incluído via restaurants_api_router
# from app.modules.tenants.restaurants.api.kiosk_api import kiosk_router
# Importa o router agregado de restaurantes
from app.modules.tenants.restaurants.api import restaurants_api_router  # noqa: E402
# Importa a API pública de menu
from app.modules.tenants.restaurants.menu.api.public_menu_api import router as public_menu_router  # noqa: E402
from app.modules.tenants.shops.api import (
    catalog_api,
)  # Importa o router do Catálogo de Lojas
from app.modules.tenants.shops.api import (  # noqa: E402
    online_store_api,
)  # Importa o router da Loja Online

# Removido - menu_categories deve estar dentro de restaurants_api_router ou menu_api
# from app.api.endpoints import menu_categories
# Importa o router de Domínios Personalizados
from app.modules.shared.custom_domains.api import router as custom_domains_router  # noqa: E402

# Importa os routers do novo módulo de Subscriptions
from app.modules.shared.subscriptions import api_admin as subscriptions_admin_api  # noqa: E402
from app.modules.shared.subscriptions import api_tenant as subscriptions_tenant_api
from app.modules.core.i18n import i18n_router  # Importa o router do módulo i18n

# Importa o router do módulo Payments
from app.modules.core.payments import router as payments_router  # noqa: E402

# Importa o router do módulo Blog
from app.modules.core.blog.api.router import router as blog_router  # noqa: E402

# Importa o router do módulo FTP System
# from app.modules.core.ftp_system.api import ftp_router  # noqa: E402 - Moved to media_system

# Importa o router do módulo Media System
from app.modules.core.functions.media_system.api.media_api import router as media_router  # noqa: E402

# Importa o router do módulo Notifications
from app.modules.core.notifications.api import router as notifications_router  # noqa: E402

# Importa o router do módulo Help Center
from app.modules.core.help_center.api import router as help_center_router  # noqa: E402

# Roteador principal para a versão v1 da API
api_router = APIRouter()

# Inclui o roteador de health check (sem prefixo para ficar em /api/health)
api_router.include_router(health_router, tags=["Health"])

# Inclui o roteador de models (sem prefixo para ficar em /api/models e /api/v1/models)
api_router.include_router(models_router, tags=["Models"])

# Inclui os roteadores dos endpoints específicos
api_router.include_router(auth_router, prefix="/auth", tags=["Authentication"])

# Adicione outros roteadores aqui conforme são criados
api_router.include_router(users_router, prefix="/users", tags=["Users"])
api_router.include_router(tenants_router, prefix="/tenants", tags=["Tenants"])

# Inclui o roteador do módulo POS
api_router.include_router(pos_router, prefix="/modules/pos", tags=["POS"])

# Inclui o roteador do módulo Inventário
api_router.include_router(inventory_router, prefix="/modules/inventory", tags=["Inventory"])

# Inclui o roteador do módulo Supplier
api_router.include_router(supplier_router, prefix="/modules/supplier", tags=["Supplier"])
api_router.include_router(supplier_tenant_router, prefix="/modules", tags=["Supplier-Tenant"])

# Inclui o roteador do módulo Allergens
api_router.include_router(allergens_router, prefix="/modules", tags=["Allergens"])

# Inclui o roteador do módulo Shopping List
api_router.include_router(shopping_list_router, prefix="/modules/shopping-list", tags=["Shopping List"])

# Note: Removed duplicate tenant shopping list module - using only shared shopping list module

# Inclui o roteador do módulo Staff Management
api_router.include_router(hr_router, prefix="/modules/hr", tags=["HR Management"])

# Inclui o roteador do módulo Financial
api_router.include_router(financial_router, prefix="/financial", tags=["Financial"])
api_router.include_router(orders_router, prefix="/orders", tags=["Orders"])

# Inclui o roteador do módulo Financial Control
from app.modules.shared.financial.control.api import control_router, analytics_router  # noqa: E402
api_router.include_router(control_router, prefix="/financial", tags=["Financial Control"])
api_router.include_router(analytics_router, prefix="/financial", tags=["Financial Control Analytics"])

# Inclui o roteador do módulo CRM
api_router.include_router(crm_router, prefix="/modules/crm", tags=["CRM"])

# Inclui o roteador agregado do módulo Restaurantes
api_router.include_router(
    restaurants_api_router, prefix="/modules/restaurants", tags=["Restaurants"]
)

# Inclui a API pública de menu (sem autenticação)
api_router.include_router(
    public_menu_router, tags=["Public Menu API"]
)

# Inclui a API pública de tenants (sem autenticação)
from app.modules.core.tenants.api.endpoints.public_tenant_api import router as public_tenant_router
api_router.include_router(
    public_tenant_router, tags=["Public Tenant API"]
)
# Inclui o roteador do módulo Lojas - Catálogo
api_router.include_router(
    catalog_api.router, prefix="/modules/shops/catalog", tags=["Shops - Catalog"]
)
# Added Online Store API Router
api_router.include_router(
    online_store_api.router,
    prefix="/modules/shops/store",
    tags=["Shops - Online Store"],
)
# O router menu_categories já deve estar incluído dentro de menu_api.router
# ou restaurants_api_router

# Inclui o roteador do módulo Custom Domains
api_router.include_router(
    custom_domains_router, prefix="/modules/custom-domains", tags=["Custom Domains"]
)

# Inclui o roteador do módulo Domain Rent
from app.modules.shared.domain_rent.api import router as domain_rent_router  # noqa: E402

api_router.include_router(
    domain_rent_router, prefix="/modules/shared/domain_rent", tags=["Domain Rent"]
)

# Inclui o roteador de integração entre Custom Domains e Domain Rent
from app.modules.core.custom_domains.api import router as domain_integration_router  # noqa: E402

api_router.include_router(
    domain_integration_router,
    prefix="/modules/custom-domains/integration",
    tags=["Domain Integration"],
)

# Inclui os roteadores do módulo Subscriptions
api_router.include_router(
    subscriptions_admin_api.router,
    prefix="/modules/subscriptions/admin",  # Rota para administração
    tags=["Subscriptions - Admin"],
)

# Inclui o roteador do módulo Auction/Lottery
from app.modules.core.eshop.api.auction_lottery_router import router as auction_lottery_router  # noqa: E402

api_router.include_router(
    auction_lottery_router,
    prefix="/modules/eshop",
    tags=["EShop - Auction & Lottery"],
)
api_router.include_router(
    subscriptions_tenant_api.router,
    prefix="/modules/subscriptions",  # Rota para tenants
    tags=["Subscriptions - Tenant"],
)

# Roteador do módulo Customer removido - agora usando o módulo CRM

# Inclui o roteador do módulo I18n
api_router.include_router(i18n_router, prefix="/i18n", tags=["I18n - TrixLingua"])

# Inclui o roteador do módulo Payments
api_router.include_router(payments_router, prefix="/modules/core", tags=["Core - Payments"])

# Inclui o roteador do módulo Blog
api_router.include_router(blog_router, prefix="/modules/core", tags=["Core - Blog"])

# Inclui o roteador do módulo FTP System
# api_router.include_router(ftp_router, prefix="/modules/core", tags=["Core - FTP System"]) - Moved to media_system

# Inclui o roteador do módulo Media System
api_router.include_router(media_router, prefix="/modules/core", tags=["Core - Media System"])

# Inclui o roteador do módulo Notifications
api_router.include_router(notifications_router, prefix="/modules/core", tags=["Core - Notifications"])

# Inclui o roteador do módulo Help Center
api_router.include_router(help_center_router, prefix="/modules/core", tags=["Core - Help Center"])

# Import couponic APIs (migrado para eshop)
# from app.modules.core.couponic.api.couponic_api import router as couponic_core_router
# from app.modules.shared.couponic.api.audit_api import router as couponic_audit_router  # Temporarily disabled - missing schemas

# Include couponic core API (migrado para eshop)
# api_router.include_router(
#     couponic_core_router,
#     prefix="/modules/core/couponic",
#     tags=["Core - Couponic"]
# )

# Include couponic audit API
# api_router.include_router(
#     couponic_audit_router,
#     prefix="/modules/shared/couponic",
#     tags=["Shared - Couponic Audit"]
# )  # Temporarily disabled - missing schemas

# Import and include Shipping, Offerts, Reviews and eshop routers - CORRECTED PATHS
from app.modules.core.functions.shipping.api import router as shipping_router
from app.modules.core.functions.offerts.api import router as offerts_router
from app.modules.core.functions.reviews.api.reviews import router as reviews_router
from app.modules.core.eshop.api import eshop_router
# from app.modules.shared.couponic.api import router as couponic_router  # Migrado para eshop

api_router.include_router(
    shipping_router,
    prefix="/modules/core/functions/shipping",
    tags=["Core Functions - Shipping"]
)

api_router.include_router(
    offerts_router,
    prefix="/modules/core/functions/offerts",
    tags=["Core Functions - Offerts"]
)

api_router.include_router(
    reviews_router,
    prefix="/modules/core/functions/reviews",
    tags=["Core Functions - Reviews"]
)

api_router.include_router(
    eshop_router,
    prefix="/eshop",
    tags=["Eshop"]
)

# api_router.include_router(
#     couponic_router,
#     prefix="/modules/shared/couponic",
#     tags=["Shared - Couponic"]
# )  # Migrado para eshop

# Tenant Settings now consolidated into tenants module
# Legacy endpoint maintained for backward compatibility via tenants router

# EShop additional routers (main eshop_router already included above)
from app.modules.core.eshop.api.integration_api import router as eshop_integration_router
from app.modules.core.eshop.api.role_management_api import router as eshop_role_management_router
from app.modules.core.eshop.api.tcustomers_api import router as eshop_tcustomers_router

# Include additional EShop routers
api_router.include_router(
    eshop_integration_router,
    prefix="/modules/core/eshop/integration",
    tags=["Core - EShop Integration"]
)
api_router.include_router(
    eshop_role_management_router,
    prefix="/modules/core/eshop/roles",
    tags=["Core - EShop Role Management"]
)
api_router.include_router(
    eshop_tcustomers_router,
    prefix="/modules/core/eshop/tcustomers",
    tags=["Core - EShop TCustomers"]
)
