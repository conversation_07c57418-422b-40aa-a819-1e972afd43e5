from typing import List, Optional
import uuid
from datetime import datetime, date
from fastapi import APIRouter, Depends, HTTPException, Query, status, Body
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.dependencies import get_db, get_current_tenant, require_tenant_role
from app.core.roles import TenantRole
from app.models.user import User
from app.models.tenant import Tenant

from app.modules.shared.hr.core.schemas.time_tracking import (
    TimeRecordRead,
    TimeRecordCreate,
    TimeRecordUpdate,
    OvertimeRecordRead,
    OvertimeRecordCreate,
    OvertimeRecordUpdate,
    TimeBankRead,
    TimeBankTransactionRead,
    TimeBankTransactionCreate,
    ClockInRequest,
    ClockOutRequest,
    TimeCardSummary,
    GenerateTimeMirrorRequest,
    TimeMirrorRead,
    TimeMirrorWithRecords,
    OvertimeRuleCreate,
    OvertimeRuleUpdate,
    OvertimeRuleRead,
)
from app.modules.shared.hr.core.models.time_tracking import (  # noqa: E402
    TimeRecordType,
    TimeMirrorStatus,
)
from app.modules.shared.hr.core.services.time_tracking_service import (  # noqa: E402
    time_tracking_service,
)

router = APIRouter(tags=["HR - Time Tracking"])

# Time Records Routes


@router.post(
    "/time-records",
    response_model=TimeRecordRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create Time Record",
    description="Create a new time record.",
)
async def create_time_record(
    record_data: TimeRecordCreate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Create a new time record."""
    return await time_tracking_service.create_time_record(
        db=db, tenant_id=tenant.id, record_data=record_data
    )


@router.get(
    "/time-records/{record_id}",
    response_model=TimeRecordRead,
    summary="Get Time Record",
    description="Get a time record by ID.",
)
async def get_time_record(
    record_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Get a time record by ID."""
    return await time_tracking_service.get_time_record(
        db=db, tenant_id=tenant.id, record_id=record_id
    )


@router.get(
    "/time-records",
    response_model=List[TimeRecordRead],
    summary="List Time Records",
    description="Get time records with optional filtering.",
)
async def get_time_records(
    employee_id: Optional[uuid.UUID] = None,
    record_type: Optional[TimeRecordType] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Get time records with optional filtering."""
    return await time_tracking_service.get_time_records(
        db=db,
        tenant_id=tenant.id,
        employee_id=employee_id,
        record_type=record_type,
        start_date=start_date,
        end_date=end_date,
        skip=skip,
        limit=limit,
    )


@router.put(
    "/time-records/{record_id}",
    response_model=TimeRecordRead,
    summary="Update Time Record",
    description="Update a time record by ID.",
)
async def update_time_record(
    record_id: uuid.UUID,
    record_data: TimeRecordUpdate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Update a time record."""
    return await time_tracking_service.update_time_record(
        db=db, tenant_id=tenant.id, record_id=record_id, record_data=record_data
    )


@router.delete(
    "/time-records/{record_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete Time Record",
    description="Delete a time record by ID.",
)
async def delete_time_record(
    record_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Delete a time record."""
    await time_tracking_service.delete_time_record(db=db, tenant_id=tenant.id, record_id=record_id)
    return None


# Clock In/Out Routes


@router.post(
    "/clock-in",
    response_model=TimeRecordRead,
    status_code=status.HTTP_201_CREATED,
    summary="Clock In",
    description="Clock in an employee.",
)
async def clock_in(
    clock_in_data: ClockInRequest,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Clock in an employee."""
    return await time_tracking_service.clock_in(
        db=db, tenant_id=tenant.id, clock_in_data=clock_in_data
    )


@router.post(
    "/clock-out",
    response_model=TimeRecordRead,
    status_code=status.HTTP_201_CREATED,
    summary="Clock Out",
    description="Clock out an employee.",
)
async def clock_out(
    clock_out_data: ClockOutRequest,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Clock out an employee."""
    return await time_tracking_service.clock_out(
        db=db, tenant_id=tenant.id, clock_out_data=clock_out_data
    )


@router.get(
    "/time-card/{employee_id}",
    response_model=TimeCardSummary,
    summary="Get Time Card",
    description="Get a time card summary for an employee.",
)
async def get_time_card(
    employee_id: uuid.UUID,
    for_date: Optional[date] = None,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Get a time card summary for an employee."""
    return await time_tracking_service.get_time_card(
        db=db, tenant_id=tenant.id, employee_id=employee_id, for_date=for_date
    )


# Overtime Routes


@router.post(
    "/overtime",
    response_model=OvertimeRecordRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create Overtime Record",
    description="Create a new overtime record.",
)
async def create_overtime_record(
    record_data: OvertimeRecordCreate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Create a new overtime record."""
    return await time_tracking_service.create_overtime_record(
        db=db, tenant_id=tenant.id, overtime_data=record_data
    )


@router.get(
    "/overtime/{record_id}",
    response_model=OvertimeRecordRead,
    summary="Get Overtime Record",
    description="Get an overtime record by ID.",
)
async def get_overtime_record(
    record_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Get an overtime record by ID."""
    return await time_tracking_service.get_overtime_record(
        db=db, tenant_id=tenant.id, record_id=record_id
    )


@router.get(
    "/overtime",
    response_model=List[OvertimeRecordRead],
    summary="List Overtime Records",
    description="Get overtime records with optional filtering.",
)
async def get_overtime_records(
    employee_id: Optional[uuid.UUID] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    is_approved: Optional[bool] = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Get overtime records with optional filtering."""
    return await time_tracking_service.get_overtime_records(
        db=db,
        tenant_id=tenant.id,
        employee_id=employee_id,
        start_date=start_date,
        end_date=end_date,
        is_approved=is_approved,
        skip=skip,
        limit=limit,
    )


@router.put(
    "/overtime/{record_id}",
    response_model=OvertimeRecordRead,
    summary="Update Overtime Record",
    description="Update an overtime record by ID.",
)
async def update_overtime_record(
    record_id: uuid.UUID,
    record_data: OvertimeRecordUpdate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Update an overtime record."""
    return await time_tracking_service.update_overtime_record(
        db=db, tenant_id=tenant.id, record_id=record_id, record_data=record_data
    )


@router.delete(
    "/overtime/{record_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete Overtime Record",
    description="Delete an overtime record by ID.",
)
async def delete_overtime_record(
    record_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Delete an overtime record."""
    await time_tracking_service.delete_overtime_record(
        db=db, tenant_id=tenant.id, record_id=record_id
    )
    return None


# Time Bank Routes


@router.get(
    "/time-bank/{employee_id}",
    response_model=TimeBankRead,
    summary="Get Time Bank",
    description="Get or create a time bank for an employee.",
)
async def get_time_bank(
    employee_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Get or create a time bank for an employee."""
    return await time_tracking_service.get_or_create_timebank(
        db=db, tenant_id=tenant.id, employee_id=employee_id
    )


@router.post(
    "/time-bank/transactions",
    response_model=TimeBankTransactionRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create Time Bank Transaction",
    description="Create a new time bank transaction.",
)
async def create_time_bank_transaction(
    transaction_data: TimeBankTransactionCreate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Create a new time bank transaction."""
    _, transaction = await time_tracking_service.add_transaction_to_timebank(
        db=db, tenant_id=tenant.id, transaction_data=transaction_data
    )
    return transaction


# Time Mirror (Espelho de Ponto) Routes


@router.post(
    "/time-mirror/generate",
    response_model=TimeMirrorWithRecords,
    status_code=status.HTTP_201_CREATED,
    summary="Generate Time Mirror",
    description="Generates a new time mirror (espelho de ponto) for an employee for a given period.",  # noqa: E501
)
async def generate_time_mirror_endpoint(
    request_data: GenerateTimeMirrorRequest,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    return await time_tracking_service.generate_time_mirror(
        db=db, tenant_id=tenant.id, request_data=request_data
    )


@router.get(
    "/time-mirror/{mirror_id}",
    response_model=TimeMirrorWithRecords,
    summary="Get Time Mirror by ID",
    description="Retrieves a specific time mirror by its ID, including associated time records.",
)
async def get_time_mirror_endpoint(
    mirror_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    mirror = await time_tracking_service.get_time_mirror(
        db=db, tenant_id=tenant.id, mirror_id=mirror_id
    )
    if not mirror:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Time mirror not found")
    return mirror


@router.get(
    "/time-mirrors",
    response_model=List[TimeMirrorRead],
    summary="List Time Mirrors",
    description="Lists time mirrors with optional filtering by employee, status, and date range.",
)
async def list_time_mirrors_endpoint(
    employee_id: Optional[uuid.UUID] = Query(None, description="Filter by employee ID"),
    status_filter: Optional[TimeMirrorStatus] = Query(
        None, alias="status", description="Filter by time mirror status"
    ),
    start_date_filter: Optional[date] = Query(None, description="Filter by period start date"),
    end_date_filter: Optional[date] = Query(None, description="Filter by period end date"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=200),
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    return await time_tracking_service.list_time_mirrors(
        db=db,
        tenant_id=tenant.id,
        employee_id=employee_id,
        status=status_filter,
        start_date_filter=start_date_filter,
        end_date_filter=end_date_filter,
        skip=skip,
        limit=limit,
    )


@router.patch(
    "/time-mirror/{mirror_id}/status",
    response_model=TimeMirrorRead,
    summary="Update Time Mirror Status",
    description="Updates the status of a specific time mirror (e.g., submit, approve, reject).",
)
async def update_time_mirror_status_endpoint(
    mirror_id: uuid.UUID,
    new_status: TimeMirrorStatus = Body(
        ..., embed=True, description="The new status for the time mirror"
    ),
    notes: Optional[str] = Body(
        None, embed=True, description="Optional notes for the status change"
    ),
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    user_id_for_action = current_user.id if new_status == TimeMirrorStatus.APPROVED else None
    from fastapi import HTTPException  # noqa: E402
    from app.core.exceptions import NotFoundError, ValidationError

    try:
        return await time_tracking_service.update_time_mirror_status(
            db=db,
            tenant_id=tenant.id,
            mirror_id=mirror_id,
            new_status=new_status,
            user_id=user_id_for_action,
            notes=notes,
        )
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except ValidationError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


# Overtime Rules Routes


@router.post(
    "/overtime-rules",
    response_model=OvertimeRuleRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create Overtime Rule",
    description="Creates a new overtime rule for the tenant.",
)
async def create_overtime_rule_endpoint(
    rule_data: OvertimeRuleCreate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    return await time_tracking_service.create_overtime_rule(
        db=db, tenant_id=tenant.id, rule_data=rule_data
    )


@router.get(
    "/overtime-rules/{rule_id}",
    response_model=OvertimeRuleRead,
    summary="Get Overtime Rule by ID",
    description="Retrieves a specific overtime rule by its ID.",
)
async def get_overtime_rule_endpoint(
    rule_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    rule = await time_tracking_service.get_overtime_rule(
        db=db, tenant_id=tenant.id, rule_id=rule_id
    )
    if not rule:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Overtime rule not found")
    return rule


@router.get(
    "/overtime-rules",
    response_model=List[OvertimeRuleRead],
    summary="List Overtime Rules",
    description="Lists all overtime rules for the tenant, with optional filtering by active status.",  # noqa: E501
)
async def list_overtime_rules_endpoint(
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    return await time_tracking_service.list_overtime_rules(
        db=db, tenant_id=tenant.id, is_active=is_active, skip=skip, limit=limit
    )


@router.put(
    "/overtime-rules/{rule_id}",
    response_model=OvertimeRuleRead,
    summary="Update Overtime Rule",
    description="Updates an existing overtime rule.",
)
async def update_overtime_rule_endpoint(
    rule_id: uuid.UUID,
    rule_data: OvertimeRuleUpdate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    from app.core.exceptions import NotFoundError  # noqa: E402

    try:
        return await time_tracking_service.update_overtime_rule(
            db=db, tenant_id=tenant.id, rule_id=rule_id, rule_data=rule_data
        )
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))


@router.delete(
    "/overtime-rules/{rule_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete Overtime Rule",
    description="Deletes an overtime rule.",
)
async def delete_overtime_rule_endpoint(
    rule_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    from fastapi import Response  # noqa: E402
    from app.core.exceptions import NotFoundError

    try:
        deleted = await time_tracking_service.delete_overtime_rule(
            db=db, tenant_id=tenant.id, rule_id=rule_id
        )
        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Overtime rule not found or could not be deleted",
            )
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    return Response(status_code=status.HTTP_204_NO_CONTENT)
