"""
Tenant Mixin module.

This module provides a mixin class for tenant-related models.
"""

from sqlalchemy import Column, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.dialects.postgresql import UUID

from app.db.base import Base


class TenantMixin:
    """Mixin for tenant-related models."""

    @declared_attr
    def tenant_id(cls):
        return Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False, index=True)

    @declared_attr
    def tenant(cls):
        return relationship("Tenant")
