import uuid
from typing import Optional, List, TYPE_CHECKING, ForwardRef
from pydantic import BaseModel, Field, ConfigDict
from decimal import Decimal
import datetime
from enum import Enum

if TYPE_CHECKING:
    from .shopping_list_category import ShoppingListCategoryRead

# Forward reference for category
ShoppingListCategoryRead = ForwardRef('ShoppingListCategoryRead')


class Priority(str, Enum):
    """Enum para prioridades dos itens da lista de compras."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


# --- Shopping List Schemas ---

class ShoppingListBase(BaseModel):
    """Schema base com campos comuns para ShoppingList."""
    
    name: str = Field(..., description="Nome da lista de compras.")
    description: Optional[str] = Field(None, description="Descrição da lista.")
    is_active: bool = Field(True, description="Se a lista está ativa.")
    
    model_config = ConfigDict(from_attributes=True)


class ShoppingListCreate(ShoppingListBase):
    """Schema para criar uma nova lista de compras."""
    pass


class ShoppingListUpdate(BaseModel):
    """Schema para atualizar uma lista de compras."""
    
    name: Optional[str] = Field(None, description="Novo nome da lista.")
    description: Optional[str] = Field(None, description="Nova descrição da lista.")
    is_active: Optional[bool] = Field(None, description="Novo status da lista.")
    
    model_config = ConfigDict(from_attributes=True)


class ShoppingListInDBBase(ShoppingListBase):
    """Schema base para listas como estão no banco de dados."""
    
    id: uuid.UUID
    tenant_id: uuid.UUID
    created_at: datetime.datetime
    updated_at: datetime.datetime
    
    model_config = ConfigDict(from_attributes=True)


class ShoppingListRead(ShoppingListInDBBase):
    """Schema para retornar dados de uma lista de compras ao cliente."""
    pass


# --- Shopping List Item Schemas ---

class ShoppingListItemBase(BaseModel):
    """Schema base com campos comuns para ShoppingListItem."""
    
    name: str = Field(..., description="Nome do item.")
    quantity: int = Field(1, description="Quantidade do item.", ge=1)
    unit: Optional[str] = Field(None, description="Unidade de medida (kg, units, etc).")
    priority: Priority = Field(Priority.MEDIUM, description="Prioridade do item.")
    estimated_price: Optional[Decimal] = Field(
        None, description="Preço estimado do item.", ge=0
    )
    purchased: bool = Field(False, description="Se o item foi comprado.")
    notes: Optional[str] = Field(None, description="Observações sobre o item.")
    inventory_item_id: Optional[uuid.UUID] = Field(
        None, description="ID do item de inventário relacionado."
    )
    category_id: Optional[uuid.UUID] = Field(
        None, description="ID da categoria do item."
    )

    model_config = ConfigDict(from_attributes=True)


class ShoppingListItemCreate(ShoppingListItemBase):
    """Schema para criar um novo item de lista de compras."""
    pass


class ShoppingListItemUpdate(BaseModel):
    """Schema para atualizar um item de lista de compras."""
    
    name: Optional[str] = Field(None, description="Novo nome do item.")
    quantity: Optional[int] = Field(None, description="Nova quantidade.", ge=1)
    unit: Optional[str] = Field(None, description="Nova unidade de medida.")
    priority: Optional[Priority] = Field(None, description="Nova prioridade.")
    estimated_price: Optional[Decimal] = Field(
        None, description="Novo preço estimado.", ge=0
    )
    purchased: Optional[bool] = Field(None, description="Novo status de compra.")
    notes: Optional[str] = Field(None, description="Novas observações.")
    inventory_item_id: Optional[uuid.UUID] = Field(
        None, description="Novo ID do item de inventário."
    )
    category_id: Optional[uuid.UUID] = Field(
        None, description="Novo ID da categoria do item."
    )

    model_config = ConfigDict(from_attributes=True)


class ShoppingListItemInDBBase(ShoppingListItemBase):
    """Schema base para itens como estão no banco de dados."""
    
    id: uuid.UUID
    shopping_list_id: uuid.UUID
    created_at: datetime.datetime
    updated_at: datetime.datetime
    
    model_config = ConfigDict(from_attributes=True)


class ShoppingListItemRead(ShoppingListItemInDBBase):
    """Schema para retornar dados de um item de lista ao cliente."""
    category: Optional[ShoppingListCategoryRead] = None


# Schema para toggle purchased
class ShoppingListItemTogglePurchased(BaseModel):
    """Schema para alternar status de comprado de um item."""
    purchased: bool = Field(..., description="Novo status de comprado.")


# Schema para lista com itens
class ShoppingListWithItems(ShoppingListRead):
    """Schema para retornar lista com seus itens."""
    items: List[ShoppingListItemRead] = Field(default_factory=list)


# Schema para resumo de lista
class ShoppingListSummary(BaseModel):
    """Schema for shopping list summary information."""
    id: uuid.UUID
    name: str
    status: str
    created_date: Optional[datetime.date] = None
    target_date: Optional[datetime.date] = None
    estimated_total: Optional[Decimal] = None
    items_count: int = 0
    pending_items: int = 0
    ordered_items: int = 0
    received_items: int = 0
    high_priority_items: int = 0
    auto_generated: Optional[bool] = False

    model_config = ConfigDict(from_attributes=True)


# Schema para auto-geração
class AutoGenerateShoppingListRequest(BaseModel):
    """Schema for auto-generating shopping list from low stock items."""
    name: str = Field(..., max_length=200, description="Shopping list name")
    description: Optional[str] = Field(None, description="Shopping list description")
    target_date: Optional[datetime.date] = Field(None, description="Target completion date")
    include_categories: Optional[List[str]] = Field(None, description="Categories to include")
    exclude_categories: Optional[List[str]] = Field(None, description="Categories to exclude")
    minimum_stock_threshold: Optional[int] = Field(10, ge=1, description="Minimum stock threshold")

    model_config = ConfigDict(from_attributes=True)
