"""
Schema.org Structured Data Generators

Functions to generate Schema.org JSON-LD structured data.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from decimal import Decimal


def generate_article_schema(
    title: str,
    description: str,
    author_name: str,
    published_date: datetime,
    modified_date: Optional[datetime] = None,
    image_url: Optional[str] = None,
    url: Optional[str] = None,
    organization_name: Optional[str] = None,
    organization_logo: Optional[str] = None
) -> Dict[str, Any]:
    """Generate Article schema.org structured data."""
    schema = {
        "@context": "https://schema.org",
        "@type": "Article",
        "headline": title,
        "description": description,
        "author": {
            "@type": "Person",
            "name": author_name
        },
        "datePublished": published_date.isoformat(),
        "dateModified": (modified_date or published_date).isoformat()
    }
    
    if image_url:
        schema["image"] = {
            "@type": "ImageObject",
            "url": image_url
        }
    
    if url:
        schema["url"] = url
        schema["mainEntityOfPage"] = {
            "@type": "WebPage",
            "@id": url
        }
    
    if organization_name:
        publisher = {
            "@type": "Organization",
            "name": organization_name
        }
        if organization_logo:
            publisher["logo"] = {
                "@type": "ImageObject",
                "url": organization_logo
            }
        schema["publisher"] = publisher
    
    return schema


def generate_product_schema(
    name: str,
    description: str,
    price: Decimal,
    currency: str = "USD",
    availability: str = "InStock",
    condition: str = "NewCondition",
    brand_name: Optional[str] = None,
    sku: Optional[str] = None,
    gtin: Optional[str] = None,
    image_urls: Optional[List[str]] = None,
    url: Optional[str] = None,
    review_count: Optional[int] = None,
    rating_value: Optional[float] = None,
    rating_best: Optional[int] = 5
) -> Dict[str, Any]:
    """Generate Product schema.org structured data."""
    schema = {
        "@context": "https://schema.org",
        "@type": "Product",
        "name": name,
        "description": description,
        "offers": {
            "@type": "Offer",
            "price": str(price),
            "priceCurrency": currency,
            "availability": f"https://schema.org/{availability}",
            "itemCondition": f"https://schema.org/{condition}"
        }
    }
    
    if brand_name:
        schema["brand"] = {
            "@type": "Brand",
            "name": brand_name
        }
    
    if sku:
        schema["sku"] = sku
    
    if gtin:
        schema["gtin"] = gtin
    
    if image_urls:
        if len(image_urls) == 1:
            schema["image"] = image_urls[0]
        else:
            schema["image"] = image_urls
    
    if url:
        schema["url"] = url
        schema["offers"]["url"] = url
    
    if review_count and rating_value:
        schema["aggregateRating"] = {
            "@type": "AggregateRating",
            "ratingValue": rating_value,
            "reviewCount": review_count,
            "bestRating": rating_best
        }
    
    return schema


def generate_breadcrumb_schema(breadcrumbs: List[Dict[str, str]]) -> Dict[str, Any]:
    """Generate BreadcrumbList schema.org structured data.
    
    Args:
        breadcrumbs: List of dicts with 'name' and 'url' keys
    """
    if not breadcrumbs:
        return {}
    
    items = []
    for i, crumb in enumerate(breadcrumbs, 1):
        items.append({
            "@type": "ListItem",
            "position": i,
            "name": crumb["name"],
            "item": crumb["url"]
        })
    
    return {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": items
    }


def generate_organization_schema(
    name: str,
    url: str,
    logo_url: Optional[str] = None,
    description: Optional[str] = None,
    address: Optional[Dict[str, str]] = None,
    contact_info: Optional[Dict[str, str]] = None,
    social_media: Optional[List[str]] = None
) -> Dict[str, Any]:
    """Generate Organization schema.org structured data.
    
    Args:
        name: Organization name
        url: Organization website URL
        logo_url: Logo image URL
        description: Organization description
        address: Dict with address fields (street, city, region, postal_code, country)
        contact_info: Dict with contact fields (phone, email)
        social_media: List of social media URLs
    """
    schema = {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": name,
        "url": url
    }
    
    if logo_url:
        schema["logo"] = logo_url
    
    if description:
        schema["description"] = description
    
    if address:
        schema["address"] = {
            "@type": "PostalAddress",
            "streetAddress": address.get("street", ""),
            "addressLocality": address.get("city", ""),
            "addressRegion": address.get("region", ""),
            "postalCode": address.get("postal_code", ""),
            "addressCountry": address.get("country", "")
        }
    
    if contact_info:
        contact_point = {
            "@type": "ContactPoint",
            "contactType": "customer service"
        }
        if contact_info.get("phone"):
            contact_point["telephone"] = contact_info["phone"]
        if contact_info.get("email"):
            contact_point["email"] = contact_info["email"]
        
        schema["contactPoint"] = contact_point
    
    if social_media:
        schema["sameAs"] = social_media
    
    return schema


def generate_website_schema(
    name: str,
    url: str,
    description: Optional[str] = None,
    search_url: Optional[str] = None
) -> Dict[str, Any]:
    """Generate WebSite schema.org structured data."""
    schema = {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": name,
        "url": url
    }
    
    if description:
        schema["description"] = description
    
    if search_url:
        schema["potentialAction"] = {
            "@type": "SearchAction",
            "target": {
                "@type": "EntryPoint",
                "urlTemplate": search_url
            },
            "query-input": "required name=search_term_string"
        }
    
    return schema


def generate_faq_schema(faqs: List[Dict[str, str]]) -> Dict[str, Any]:
    """Generate FAQPage schema.org structured data.
    
    Args:
        faqs: List of dicts with 'question' and 'answer' keys
    """
    if not faqs:
        return {}
    
    main_entity = []
    for faq in faqs:
        main_entity.append({
            "@type": "Question",
            "name": faq["question"],
            "acceptedAnswer": {
                "@type": "Answer",
                "text": faq["answer"]
            }
        })
    
    return {
        "@context": "https://schema.org",
        "@type": "FAQPage",
        "mainEntity": main_entity
    }


def generate_local_business_schema(
    name: str,
    business_type: str,
    address: Dict[str, str],
    phone: str,
    url: str,
    opening_hours: Optional[List[str]] = None,
    price_range: Optional[str] = None,
    rating: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """Generate LocalBusiness schema.org structured data.
    
    Args:
        name: Business name
        business_type: Type of business (e.g., 'Restaurant', 'Store')
        address: Dict with address fields
        phone: Phone number
        url: Business website URL
        opening_hours: List of opening hours (e.g., ['Mo-Fr 09:00-17:00'])
        price_range: Price range (e.g., '$', '$$', '$$$')
        rating: Dict with 'value', 'count', and 'best' keys
    """
    schema = {
        "@context": "https://schema.org",
        "@type": business_type,
        "name": name,
        "address": {
            "@type": "PostalAddress",
            "streetAddress": address.get("street", ""),
            "addressLocality": address.get("city", ""),
            "addressRegion": address.get("region", ""),
            "postalCode": address.get("postal_code", ""),
            "addressCountry": address.get("country", "")
        },
        "telephone": phone,
        "url": url
    }
    
    if opening_hours:
        schema["openingHours"] = opening_hours
    
    if price_range:
        schema["priceRange"] = price_range
    
    if rating:
        schema["aggregateRating"] = {
            "@type": "AggregateRating",
            "ratingValue": rating["value"],
            "reviewCount": rating["count"],
            "bestRating": rating.get("best", 5)
        }
    
    return schema
