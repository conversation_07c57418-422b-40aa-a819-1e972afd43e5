"""
Schemas for the tenants module.
"""

from app.modules.core.tenants.schemas.tenant import (
    TenantBase,
    TenantCreate,
    TenantUpdate,
    TenantInDBBase,
    Tenant,
)

# Import tenant settings schemas
from app.modules.core.tenants.schemas.tenant_settings import (
    TenantSettingsBase,
    TenantSettingsCreate,
    TenantSettingsUpdate,
    TenantSettingsRead,
    BusinessSettingsUpdate,
    OperatingHoursUpdate,
    LanguageSettingsUpdate,
    LoyaltySettingsUpdate,
    LocationSettingsUpdate,
    TaxSettingsUpdate,
    CurrencySettingsUpdate,
    TimeSlotSchema,
    DayScheduleSchema,
    OperatingHoursSchema,
    AddressSchema,
    AddressUpdateSchema,
)

from app.modules.core.tenants.schemas.tenant_settings.payment_method_config import (
    TenantPaymentMethodConfigBase,
    TenantPaymentMethodConfigCreate,
    TenantPaymentMethodConfigUpdate,
    TenantPaymentMethodConfigRead,
    TenantPaymentMethodConfigWithMethod,
    PaymentMethodConfigBulkUpdate,
    PaymentMethodConfigBulkResponse,
)

from app.modules.core.tenants.schemas.tenant_settings.responses import (
    LanguageOption,
    CurrencyOption,
    CurrencyConfiguration,
    TimezoneOption,

    SubscriptionInfo,
    OperatingHoursDay,
    LoyaltySystemConfig,
    TaxCalculationPreview,
    SettingsValidationError,
)

__all__ = [
    # Tenant schemas
    "TenantBase",
    "TenantCreate",
    "TenantUpdate",
    "TenantInDBBase",
    "Tenant",
    # Tenant Settings schemas
    "TenantSettingsBase",
    "TenantSettingsCreate",
    "TenantSettingsUpdate",
    "TenantSettingsRead",
    "BusinessSettingsUpdate",
    "OperatingHoursUpdate",
    "LanguageSettingsUpdate",
    "LoyaltySettingsUpdate",
    "LocationSettingsUpdate",
    "TaxSettingsUpdate",
    "CurrencySettingsUpdate",
    "TimeSlotSchema",
    "DayScheduleSchema",
    "OperatingHoursSchema",
    "AddressSchema",
    "AddressUpdateSchema",
    # Payment Method Config schemas
    "TenantPaymentMethodConfigBase",
    "TenantPaymentMethodConfigCreate",
    "TenantPaymentMethodConfigUpdate",
    "TenantPaymentMethodConfigRead",
    "TenantPaymentMethodConfigWithMethod",
    "PaymentMethodConfigBulkUpdate",
    "PaymentMethodConfigBulkResponse",
    # Response schemas
    "LanguageOption",
    "CurrencyOption",
    "CurrencyConfiguration",
    "TimezoneOption",

    "SubscriptionInfo",
    "OperatingHoursDay",
    "LoyaltySystemConfig",
    "TaxCalculationPreview",
    "SettingsValidationError",
]
