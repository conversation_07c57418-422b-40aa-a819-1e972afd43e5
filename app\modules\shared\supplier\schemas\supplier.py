from pydantic import BaseModel, EmailStr, Field
from typing import Optional, List
from datetime import datetime
from decimal import Decimal
import uuid

from app.modules.shared.supplier.models.supplier import (
    SupplierStatus, 
    PriorityLevel
)


# === Supplier Schemas ===

class SupplierBase(BaseModel):
    """Base schema para fornecedor."""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    contact_email: Optional[EmailStr] = None
    contact_phone: Optional[str] = None
    address: Optional[str] = None
    tax_id: Optional[str] = None
    website: Optional[str] = None
    auto_replenishment_enabled: bool = False
    competitive_pricing_visible: bool = True


class SupplierCreate(SupplierBase):
    """Schema para criação de fornecedor."""
    status: SupplierStatus = SupplierStatus.ACTIVE


class SupplierUpdate(BaseModel):
    """Schema para atualização de fornecedor."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    contact_email: Optional[EmailStr] = None
    contact_phone: Optional[str] = None
    address: Optional[str] = None
    tax_id: Optional[str] = None
    website: Optional[str] = None
    status: Optional[SupplierStatus] = None
    auto_replenishment_enabled: Optional[bool] = None
    competitive_pricing_visible: Optional[bool] = None


class SupplierRead(SupplierBase):
    """Schema para leitura de fornecedor."""
    id: uuid.UUID
    tenant_id: uuid.UUID
    status: SupplierStatus
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# === Product Supplier Schemas ===

class ProductSupplierBase(BaseModel):
    """Base schema para associação produto-fornecedor."""
    supplier_product_code: Optional[str] = None
    supplier_product_name: Optional[str] = None
    current_price: Optional[Decimal] = Field(None, ge=0)
    minimum_order_quantity: int = Field(1, ge=1)
    lead_time_days: int = Field(1, ge=0)
    priority_level: PriorityLevel = PriorityLevel.SECONDARY
    priority_order: int = Field(1, ge=1)
    auto_replenishment_authorized: bool = False


class ProductSupplierCreate(ProductSupplierBase):
    """Schema para criação de associação produto-fornecedor."""
    inventory_item_id: uuid.UUID
    supplier_id: uuid.UUID


class ProductSupplierUpdate(BaseModel):
    """Schema para atualização de associação produto-fornecedor."""
    supplier_product_code: Optional[str] = None
    supplier_product_name: Optional[str] = None
    current_price: Optional[Decimal] = Field(None, ge=0)
    minimum_order_quantity: Optional[int] = Field(None, ge=1)
    lead_time_days: Optional[int] = Field(None, ge=0)
    priority_level: Optional[PriorityLevel] = None
    priority_order: Optional[int] = Field(None, ge=1)
    is_active: Optional[bool] = None
    auto_replenishment_authorized: Optional[bool] = None


class ProductSupplierRead(ProductSupplierBase):
    """Schema para leitura de associação produto-fornecedor."""
    id: uuid.UUID
    tenant_id: uuid.UUID
    inventory_item_id: uuid.UUID
    supplier_id: uuid.UUID
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# === Price History Schemas ===

class SupplierPriceHistoryBase(BaseModel):
    """Base schema para histórico de preços."""
    price: Decimal = Field(..., ge=0)
    notes: Optional[str] = None
    effective_date: Optional[datetime] = None


class SupplierPriceHistoryCreate(SupplierPriceHistoryBase):
    """Schema para criação de histórico de preços."""
    product_supplier_id: uuid.UUID
    updated_by_supplier: bool = False


class SupplierPriceHistoryRead(SupplierPriceHistoryBase):
    """Schema para leitura de histórico de preços."""
    id: uuid.UUID
    tenant_id: uuid.UUID
    product_supplier_id: uuid.UUID
    supplier_id: uuid.UUID
    previous_price: Optional[Decimal]
    price_change_percentage: Optional[Decimal]
    updated_by_supplier: bool
    updated_by_user_id: Optional[uuid.UUID]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# === Combined Schemas ===

class SupplierWithProducts(SupplierRead):
    """Fornecedor com produtos associados."""
    product_suppliers: List[ProductSupplierRead] = []


class ProductSupplierWithDetails(ProductSupplierRead):
    """Associação produto-fornecedor com detalhes."""
    supplier: SupplierRead
    price_history: List[SupplierPriceHistoryRead] = []


class CompetitivePricingView(BaseModel):
    """Visão de preços competitivos para um produto."""
    inventory_item_id: uuid.UUID
    inventory_item_name: str
    suppliers: List[dict] = []  # Lista de fornecedores com preços
    lowest_price: Optional[Decimal] = None
    highest_price: Optional[Decimal] = None
    price_range_percentage: Optional[Decimal] = None


class AutoReplenishmentSuggestion(BaseModel):
    """Sugestão de reposição automática."""
    inventory_item_id: uuid.UUID
    inventory_item_name: str
    current_stock: int
    suggested_quantity: int
    recommended_supplier_id: uuid.UUID
    recommended_supplier_name: str
    estimated_cost: Decimal
    lead_time_days: int
