import uuid
import enum
from datetime import datetime
from sqlalchemy import (
    <PERSON>umn,
    String,
    <PERSON><PERSON><PERSON>,
    <PERSON>olean,
    DateTime,
    Integer,
    Enum,
    Text,
)
from sqlalchemy.dialects.postgresql import UUID, JSONB  # noqa: E402
from sqlalchemy.orm import relationship

from app.db.base import Base  # noqa: E402
from app.core.tenant_mixin import TenantMixin


class DocumentType(str, enum.Enum):
    """Document type enum."""

    CONTRACT = "contract"
    IDENTIFICATION = "identification"
    CERTIFICATE = "certificate"
    MEDICAL = "medical"
    PERFORMANCE = "performance"
    TRAINING = "training"
    PAYROLL = "payroll"
    TAX = "tax"
    POLICY = "policy"
    OTHER = "other"


class DocumentStatus(str, enum.Enum):
    """Document status enum."""

    DRAFT = "draft"
    ACTIVE = "active"
    EXPIRED = "expired"
    ARCHIVED = "archived"
    PENDING_APPROVAL = "pending_approval"
    REJECTED = "rejected"


class Document(Base, TenantMixin):
    """Document model for document management.

    This represents a document stored in the system.
    """

    __tablename__ = "hr_documents"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    employee_id = Column(UUID(as_uuid=True), ForeignKey("hr_employees.id"), nullable=True)

    # Document details
    title = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    document_type = Column(Enum(DocumentType), nullable=False)
    file_path = Column(String, nullable=False)
    file_name = Column(String, nullable=False)
    file_size = Column(Integer, nullable=False)  # Size in bytes
    file_type = Column(String, nullable=False)  # MIME type

    # Status and dates
    status = Column(Enum(DocumentStatus), nullable=False, default=DocumentStatus.ACTIVE)
    upload_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    expiry_date = Column(DateTime, nullable=True)

    # Version control
    version = Column(Integer, nullable=False, default=1)
    is_latest_version = Column(Boolean, default=True)
    previous_version_id = Column(UUID(as_uuid=True), ForeignKey("hr_documents.id"), nullable=True)

    # Access control
    is_confidential = Column(Boolean, default=False)
    access_level = Column(String, nullable=True)  # Can be used to define custom access levels

    # Metadata
    metadata = Column(JSONB, default={})
    tags = Column(JSONB, default=[])

    # Relationships
    employee = relationship("Employee", backref="documents")
    previous_version = relationship("Document", remote_side=[id], backref="next_versions")
    document_accesses = relationship(
        "DocumentAccess", back_populates="document", cascade="all, delete-orphan"
    )
    document_signatures = relationship(
        "DocumentSignature", back_populates="document", cascade="all, delete-orphan"
    )


class DocumentAccess(Base, TenantMixin):
    """Document access model.

    This tracks who accessed a document and when.
    """

    __tablename__ = "hr_document_accesses"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), ForeignKey("hr_documents.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), nullable=False)

    # Access details
    access_time = Column(DateTime, nullable=False, default=datetime.utcnow)
    access_type = Column(String, nullable=False)  # view, download, edit, etc.

    # Additional info
    ip_address = Column(String, nullable=True)
    user_agent = Column(String, nullable=True)

    # Relationship
    document = relationship("Document", back_populates="document_accesses")


class DocumentSignature(Base, TenantMixin):
    """Document signature model.

    This represents a signature on a document.
    """

    __tablename__ = "hr_document_signatures"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), ForeignKey("hr_documents.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), nullable=False)

    # Signature details
    signature_time = Column(DateTime, nullable=False, default=datetime.utcnow)
    signature_type = Column(String, nullable=False)  # electronic, digital, etc.
    signature_data = Column(String, nullable=True)  # Could be a base64 encoded image

    # Verification
    is_verified = Column(Boolean, default=False)
    verification_method = Column(String, nullable=True)
    verification_time = Column(DateTime, nullable=True)

    # Relationship
    document = relationship("Document", back_populates="document_signatures")


class DocumentTemplate(Base, TenantMixin):
    """Document template model.

    This represents a template for creating documents.
    """

    __tablename__ = "hr_document_templates"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Template details
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    document_type = Column(Enum(DocumentType), nullable=False)
    file_path = Column(String, nullable=False)

    # Template content
    content = Column(Text, nullable=True)
    variables = Column(JSONB, default=[])

    # Status
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Metadata
    metadata = Column(JSONB, default={})
