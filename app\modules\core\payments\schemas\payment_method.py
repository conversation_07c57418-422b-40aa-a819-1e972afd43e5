"""Schemas for payment methods."""

from app.modules.core.payments.schemas.payment_processor import PaymentProcessorRead  # noqa: E402
import uuid
from typing import Optional, Dict, Any
from pydantic import BaseModel, ConfigDict, Field

from app.modules.core.payments.models.payment_method import PaymentMethodType  # noqa: E402

# --- PaymentMethod Schemas ---

# Base schema for payment methods


class PaymentMethodBase(BaseModel):
    """Base schema for payment methods."""

    name: str = Field(..., min_length=1, max_length=100)
    method_type: PaymentMethodType
    is_active: bool = True
    is_default: bool = False
    icon: Optional[str] = None
    display_order: int = 0
    processor_method_id: Optional[str] = None
    processor_config: Optional[Dict[str, Any]] = None


# Schema for creating a new payment method


class PaymentMethodCreate(PaymentMethodBase):
    """Schema for creating a new payment method."""

    processor_id: Optional[uuid.UUID] = None


# Schema for updating an existing payment method


class PaymentMethodUpdate(BaseModel):
    """Schema for updating an existing payment method."""

    name: Optional[str] = Field(None, min_length=1, max_length=100)
    method_type: Optional[PaymentMethodType] = None
    is_active: Optional[bool] = None
    is_default: Optional[bool] = None
    icon: Optional[str] = None
    display_order: Optional[int] = None
    processor_id: Optional[uuid.UUID] = None
    processor_method_id: Optional[str] = None
    processor_config: Optional[Dict[str, Any]] = None


# Schema for reading a payment method


class PaymentMethodRead(PaymentMethodBase):
    """Schema for reading a payment method."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    processor_id: Optional[uuid.UUID] = None

    model_config = ConfigDict(from_attributes=True)


# Schema for reading a payment method with processor details


class PaymentMethodReadWithProcessor(PaymentMethodRead):
    """Schema for reading a payment method with processor details."""

    processor: Optional["PaymentProcessorRead"] = None

    model_config = ConfigDict(from_attributes=True)


# Import at the end to avoid circular imports
PaymentMethodReadWithProcessor.model_rebuild()  # Update forward references
