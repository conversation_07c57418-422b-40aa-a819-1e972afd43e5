'use client';

import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  Bars3Icon,
  EyeIcon,
  EyeSlashIcon,
  TagIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { ShoppingListCategory, ShoppingListItem } from '@/types/shopping-list';

interface DraggableCategoryProps {
  category: ShoppingListCategory;
  items: ShoppingListItem[];
  onEdit: (category: ShoppingListCategory) => void;
  onDelete: (categoryId: string) => void;
  onCreateItem: (categoryId: string) => void;
  onSelect?: (categoryId: string) => void;
  isActive: boolean;
  isSelected?: boolean;
  filteredItemsCount?: number;
}

export function DraggableCategory({
  category,
  items,
  onEdit,
  onDelete,
  onCreateItem,
  onSelect,
  isActive,
  isSelected = false,
  filteredItemsCount
}: DraggableCategoryProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: category.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const handleCategoryClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (onSelect) {
      onSelect(category.id);
    }
  };

  const itemCount = filteredItemsCount !== undefined 
    ? filteredItemsCount 
    : items.length;

  const pendingItems = items.filter(item => !item.purchased).length;
  const highPriorityItems = items.filter(item => item.priority === 'high').length;

  return (
    <div
      ref={setNodeRef}
      style={style}
      onClick={handleCategoryClick}
      className={`group relative bg-white/90 backdrop-blur-sm rounded-lg border
        transition-all duration-300 ease-out cursor-pointer select-none
        min-h-[44px] touch-manipulation
        active:scale-[0.98] active:bg-blue-25
        ${
        isDragging
          ? 'shadow-xl border-blue-400 scale-105 rotate-2 z-50'
          : isSelected
          ? 'border-blue-500 shadow-lg bg-blue-50/80 ring-2 ring-blue-200 transform scale-[1.02]'
          : isActive
          ? 'border-blue-300 shadow-md hover:shadow-lg'
          : 'border-gray-200 hover:border-blue-300 hover:shadow-md hover:bg-blue-50/40 hover:scale-[1.01]'
      }`}
    >
      {/* Touch feedback overlay */}
      <div className={`absolute inset-0 rounded-lg transition-all duration-150 
        ${isSelected ? 'bg-blue-500/5' : 'hover:bg-blue-500/3'}`} 
      />
      
      {/* Drag Handle */}
      <div
        {...attributes}
        {...listeners}
        className="absolute left-2 top-1/2 transform -translate-y-1/2 p-1 
          text-gray-400 hover:text-gray-600 cursor-grab active:cursor-grabbing 
          opacity-0 group-hover:opacity-100 transition-opacity z-10"
      >
        <Bars3Icon className="h-4 w-4" />
      </div>

      {/* Content */}
      <div className="relative p-3 pl-8">
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-1">
              {category.color ? (
                <div
                  className="w-4 h-4 rounded-full border border-gray-300 flex-shrink-0"
                  style={{ backgroundColor: category.color }}
                />
              ) : (
                <TagIcon className="h-4 w-4 text-gray-400 flex-shrink-0" />
              )}
              <h4 className={`font-medium truncate ${
                isSelected ? 'text-blue-900' : 'text-gray-900'
              }`}>
                {category.name}
              </h4>
              {!category.is_active && (
                <EyeSlashIcon className="h-4 w-4 text-gray-400" />
              )}
              {highPriorityItems > 0 && (
                <div className="flex items-center space-x-1">
                  <ExclamationTriangleIcon className="h-4 w-4 text-red-500" />
                  <span className="text-xs text-red-600">{highPriorityItems}</span>
                </div>
              )}
            </div>
            
            {category.description && (
              <p className={`text-xs truncate ${
                isSelected ? 'text-blue-600' : 'text-gray-500'
              }`}>
                {category.description}
              </p>
            )}
            
            <div className="flex items-center justify-between mt-2">
              <span className={`text-xs ${
                isSelected ? 'text-blue-600' : 'text-gray-500'
              }`}>
                {itemCount} {itemCount === 1 ? 'item' : 'items'}
                {pendingItems > 0 && (
                  <span className="ml-1 text-orange-600">
                    • {pendingItems} pending
                  </span>
                )}
              </span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <button
              onClick={(e) => {
                e.stopPropagation();
                onCreateItem(category.id);
              }}
              className="p-1.5 text-green-600 hover:bg-green-50 rounded transition-colors"
              title="Add Item"
            >
              <PlusIcon className="h-3.5 w-3.5" />
            </button>
            
            <button
              onClick={(e) => {
                e.stopPropagation();
                onEdit(category);
              }}
              className="p-1.5 text-blue-600 hover:bg-blue-50 rounded transition-colors"
              title="Edit Category"
            >
              <PencilIcon className="h-3.5 w-3.5" />
            </button>
            
            <button
              onClick={(e) => {
                e.stopPropagation();
                onDelete(category.id);
              }}
              className="p-1.5 text-red-600 hover:bg-red-50 rounded transition-colors"
              title="Delete Category"
            >
              <TrashIcon className="h-3.5 w-3.5" />
            </button>
          </div>
        </div>
      </div>

      {/* Selection indicator */}
      {isSelected && (
        <div className="absolute inset-0 rounded-lg ring-2 ring-blue-500 ring-opacity-50 pointer-events-none" />
      )}
    </div>
  );
}
