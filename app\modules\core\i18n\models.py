from datetime import datetime
from typing import List, Optional, TYPE_CHECKING
from uuid import UUID as PythonUUID  # Renomeado para clareza e evitar conflito
from enum import Enum as PyEnum

# Removido SQLModel, Field de sqlmodel. Adici<PERSON>do Integer, ForeignKey, Mapped, mapped_column.
from sqlalchemy import (
    Column,
    Text,
    Index,
    Enum as SQLAlchemyEnum,
    DateTime,
    Integer,
    ForeignKey,
    Boolean,
    UniqueConstraint,
    String,
)
from sqlalchemy.dialects.postgresql import UUID as PG_UUID  # Importar PG_UUID
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.sql import func

from app.db.base import Base  # Base já inclui TimestampMixin

# SQLModel.metadata = Base.metadata # Não mais necessário, pois as classes herdarão de Base

if TYPE_CHECKING:
    from app.models.user import User


class Language(Base):  # Herda de Base
    __tablename__ = "languages"

    id: Mapped[int] = mapped_column(
        Integer, primary_key=True, index=True, default=None
    )  # Usar Mapped e mapped_column
    code: Mapped[str] = mapped_column(Text, unique=True, index=True, nullable=False)
    name: Mapped[str] = mapped_column(String, nullable=False)  # String em vez de Field sem tipo SQL
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    is_default: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)

    # Timestamps vêm de Base (TimestampMixin)

    translations: Mapped[List["Translation"]] = relationship(back_populates="language")
    translation_suggestions: Mapped[List["TranslationSuggestion"]] = relationship(
        back_populates="language"
    )


class TranslationKey(Base):  # Herda de Base
    __tablename__ = "translation_keys"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True, default=None)
    key_string: Mapped[str] = mapped_column(Text, unique=True, index=True, nullable=False)
    module: Mapped[Optional[str]] = mapped_column(String, index=True, nullable=True, default=None)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True, default=None)

    # Timestamps vêm de Base

    translations: Mapped[List["Translation"]] = relationship(back_populates="translation_key")
    suggestions: Mapped[List["TranslationSuggestion"]] = relationship(
        back_populates="translation_key"
    )


class Translation(Base):  # Herda de Base
    __tablename__ = "translations"
    __table_args__ = (
        UniqueConstraint("key_id", "language_id", name="uq_translation_key_language"),
        Index("ix_translations_key_id", "key_id"),
        Index("ix_translations_language_id", "language_id"),
    )

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True, default=None)
    translated_text: Mapped[str] = mapped_column(Text, nullable=False)
    is_approved: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    version: Mapped[int] = mapped_column(Integer, default=1, nullable=False)

    key_id: Mapped[int] = mapped_column(Integer, ForeignKey("translation_keys.id"), nullable=False)
    language_id: Mapped[int] = mapped_column(Integer, ForeignKey("languages.id"), nullable=False)
    last_updated_by_user_id: Mapped[Optional[PythonUUID]] = mapped_column(
        PG_UUID, ForeignKey("users.id"), nullable=True, default=None
    )

    # Timestamps vêm de Base

    translation_key: Mapped["TranslationKey"] = relationship(back_populates="translations")
    language: Mapped["Language"] = relationship(back_populates="translations")
    last_updated_by_user: Mapped[Optional["User"]] = relationship(
        back_populates="updated_translations", foreign_keys=[last_updated_by_user_id]
    )


class TranslationSuggestionStatus(str, PyEnum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    IMPLEMENTED_MANUALLY = "implemented_manually"


class TranslationSuggestion(Base):  # Herda de Base
    __tablename__ = "translation_suggestions"
    __table_args__ = (
        Index("ix_translation_suggestions_key_id", "key_id"),
        Index("ix_translation_suggestions_language_id", "language_id"),
        Index("ix_translation_suggestions_user_id", "user_id"),
        Index("ix_translation_suggestions_status", "status"),
    )

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True, default=None)
    suggested_text: Mapped[str] = mapped_column(Text, nullable=False)
    current_text_on_suggestion: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True, default=None
    )
    status: Mapped[TranslationSuggestionStatus] = mapped_column(
        SQLAlchemyEnum(TranslationSuggestionStatus),
        nullable=False,
        default=TranslationSuggestionStatus.PENDING,
    )
    admin_notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True, default=None)
    reviewed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, default=None
    )

    key_id: Mapped[int] = mapped_column(Integer, ForeignKey("translation_keys.id"), nullable=False)
    language_id: Mapped[int] = mapped_column(Integer, ForeignKey("languages.id"), nullable=False)
    user_id: Mapped[PythonUUID] = mapped_column(PG_UUID, ForeignKey("users.id"), nullable=False)

    # Timestamps vêm de Base

    translation_key: Mapped["TranslationKey"] = relationship(back_populates="suggestions")
    language: Mapped["Language"] = relationship(back_populates="translation_suggestions")
    user: Mapped["User"] = relationship(
        back_populates="translation_suggestions", foreign_keys=[user_id]
    )


# Adicionar ao User model (em app/models/user.py ou onde estiver definido):
# updated_translations: List["Translation"] = Relationship(back_populates="last_updated_by_user")
# translation_suggestions: List["TranslationSuggestion"] = Relationship(back_populates="user")
