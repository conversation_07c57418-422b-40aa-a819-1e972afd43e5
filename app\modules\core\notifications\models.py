"""
Notification Models

Modelos de banco de dados para o sistema de notificações.
"""

import uuid
from datetime import datetime, timedelta, timezone, timezone
from enum import Enum
from typing import Dict, List, Optional

from sqlalchemy import (
    Boolean, Column, DateTime, Enum as SQLEnum, Foreign<PERSON>ey, Integer, 
    JSON, String, Text, UUID
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import Base


class NotificationSenderType(str, Enum):
    """Tipos de remetente de notificação."""
    ADMIN = "admin"
    TENANT_OWNER = "tenant_owner"
    SYSTEM = "system"


class NotificationTargetType(str, Enum):
    """Tipos de destinatário de notificação."""
    ALL_USERS = "all_users"
    TENANT_USERS = "tenant_users"
    TENANT_OWNERS = "tenant_owners"
    TENANT_STAFF = "tenant_staff"
    TENANT_CUSTOMERS = "tenant_customers"
    SPECIFIC_USER = "specific_user"


class NotificationStatus(str, Enum):
    """Status da notificação."""
    DRAFT = "draft"
    QUEUED = "queued"
    SENDING = "sending"
    SENT = "sent"
    FAILED = "failed"
    EXPIRED = "expired"


class NotificationPriority(str, Enum):
    """Prioridade da notificação."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class Notification(Base):
    """Modelo de notificação."""
    
    __tablename__ = "notifications"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Conteúdo da notificação
    title = Column(String(255), nullable=False)
    content = Column(Text, nullable=False)
    image_url = Column(String(500), nullable=True)
    action_url = Column(String(500), nullable=True)
    
    # Remetente
    sender_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    sender_type = Column(SQLEnum(NotificationSenderType), nullable=False)
    
    # Destinatário
    target_type = Column(SQLEnum(NotificationTargetType), nullable=False)
    target_id = Column(UUID(as_uuid=True), nullable=True)  # Para usuário específico
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    
    # Configurações
    priority = Column(SQLEnum(NotificationPriority), default=NotificationPriority.NORMAL)
    status = Column(SQLEnum(NotificationStatus), default=NotificationStatus.DRAFT)
    auto_expire_hours = Column(Integer, default=24)  # Auto-expirar em X horas
    max_lifetime_days = Column(Integer, default=30)  # Máximo 30 dias
    
    # Controle de leitura e exclusão (JSON com user_ids)
    read_by = Column(JSON, default=dict)  # {user_id: timestamp}
    deleted_by = Column(JSON, default=dict)  # {user_id: timestamp}
    
    # Métricas
    view_count = Column(Integer, default=0)
    click_count = Column(Integer, default=0)
    delivery_count = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    expires_at = Column(DateTime(timezone=True), nullable=True)
    sent_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relacionamentos
    sender = relationship("User", foreign_keys=[sender_id])
    tenant = relationship("Tenant", foreign_keys=[tenant_id])
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if self.auto_expire_hours and not self.expires_at:
            self.expires_at = datetime.now(timezone.utc) + timedelta(hours=self.auto_expire_hours)
    
    @property
    def is_expired(self) -> bool:
        """Verifica se a notificação expirou."""
        if not self.expires_at:
            return False
        now = datetime.now(timezone.utc)
        # Garante que ambos os datetimes são timezone-aware
        expires_at = self.expires_at
        if expires_at.tzinfo is None:
            expires_at = expires_at.replace(tzinfo=timezone.utc)
        return now > expires_at
    
    def is_read_by_user(self, user_id: str) -> bool:
        """Verifica se foi lida por um usuário específico."""
        return str(user_id) in (self.read_by or {})

    def is_deleted_by_user(self, user_id: str) -> bool:
        """Verifica se foi deletada por um usuário específico."""
        return str(user_id) in (self.deleted_by or {})
    
    def mark_as_read(self, user_id: str) -> None:
        """Marca como lida por um usuário."""
        if not self.read_by:
            self.read_by = {}
        self.read_by[str(user_id)] = datetime.utcnow().isoformat()
    
    def mark_as_deleted(self, user_id: str) -> None:
        """Marca como deletada por um usuário."""
        if not self.deleted_by:
            self.deleted_by = {}
        self.deleted_by[str(user_id)] = datetime.utcnow().isoformat()
    
    def increment_view_count(self) -> None:
        """Incrementa contador de visualizações."""
        self.view_count = (self.view_count or 0) + 1
    
    def increment_click_count(self) -> None:
        """Incrementa contador de cliques."""
        self.click_count = (self.click_count or 0) + 1


class NotificationQueue(Base):
    """Fila de notificações para processamento assíncrono."""
    
    __tablename__ = "notification_queue"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    notification_id = Column(UUID(as_uuid=True), ForeignKey("notifications.id"), nullable=False)
    
    # Configurações da fila
    priority = Column(Integer, default=0)  # Maior número = maior prioridade
    retry_count = Column(Integer, default=0)
    max_retries = Column(Integer, default=3)
    
    # Status
    status = Column(String(50), default="pending")  # pending, processing, completed, failed
    error_message = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    scheduled_at = Column(DateTime(timezone=True), nullable=True)
    processed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relacionamento
    notification = relationship("Notification", backref="queue_entries")


class NotificationMetrics(Base):
    """Métricas agregadas do sistema de notificações."""
    
    __tablename__ = "notification_metrics"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Escopo das métricas
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    sender_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    
    # Período das métricas
    date = Column(DateTime(timezone=True), nullable=False)
    period_type = Column(String(20), nullable=False)  # daily, weekly, monthly
    
    # Métricas
    notifications_sent = Column(Integer, default=0)
    notifications_delivered = Column(Integer, default=0)
    notifications_read = Column(Integer, default=0)
    notifications_clicked = Column(Integer, default=0)
    notifications_failed = Column(Integer, default=0)
    
    # Métricas calculadas
    delivery_rate = Column(Integer, default=0)  # Porcentagem
    open_rate = Column(Integer, default=0)  # Porcentagem
    click_through_rate = Column(Integer, default=0)  # Porcentagem
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relacionamentos
    tenant = relationship("Tenant", foreign_keys=[tenant_id])
    sender = relationship("User", foreign_keys=[sender_id])


class NotificationTemplate(Base):
    """Templates de notificação para reutilização."""
    
    __tablename__ = "notification_templates"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Identificação
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    category = Column(String(100), nullable=True)
    
    # Conteúdo do template
    title_template = Column(String(255), nullable=False)
    content_template = Column(Text, nullable=False)
    
    # Configurações padrão
    default_priority = Column(SQLEnum(NotificationPriority), default=NotificationPriority.NORMAL)
    default_auto_expire_hours = Column(Integer, default=24)
    
    # Escopo
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    is_global = Column(Boolean, default=False)  # Templates globais (admin)
    
    # Status
    is_active = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relacionamento
    tenant = relationship("Tenant", foreign_keys=[tenant_id])
