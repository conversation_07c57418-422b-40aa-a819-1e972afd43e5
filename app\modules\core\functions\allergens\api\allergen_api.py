"""API endpoints for allergen management."""

import uuid
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.role_auth import require_admin_user
from app.modules.core.functions.allergens.services.allergen_service import AllergenService
from app.modules.core.functions.allergens.schemas.allergen import (
    AllergenCreate,
    AllergenUpdate,
    AllergenRead,
    AllergenReadSimple,
)

router = APIRouter(prefix="/allergens", tags=["allergens"])


@router.get("/", response_model=List[AllergenReadSimple])
async def get_allergens(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    active_only: bool = Query(True, description="Return only active allergens"),
    db: AsyncSession = Depends(get_db),
):
    """
    Get list of allergens.
    
    This endpoint is accessible to all authenticated users as they need
    to see allergens when viewing menu items.
    """
    service = AllergenService(db)
    allergens = await service.get_allergens(
        skip=skip, 
        limit=limit, 
        active_only=active_only
    )
    return allergens


@router.get("/{allergen_id}", response_model=AllergenRead)
async def get_allergen(
    allergen_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
):
    """
    Get a specific allergen by ID.
    
    This endpoint is accessible to all authenticated users.
    """
    service = AllergenService(db)
    allergen = await service.get_allergen(allergen_id)
    
    if not allergen:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Allergen not found"
        )
    
    return allergen


@router.post("/", response_model=AllergenRead, status_code=status.HTTP_201_CREATED)
async def create_allergen(
    allergen_in: AllergenCreate,
    db: AsyncSession = Depends(get_db),
    _: dict = Depends(require_admin_user),  # Only admins can create allergens
):
    """
    Create a new allergen.
    
    Only system administrators can create allergens.
    """
    service = AllergenService(db)
    return await service.create_allergen(allergen_in)


@router.put("/{allergen_id}", response_model=AllergenRead)
async def update_allergen(
    allergen_id: uuid.UUID,
    allergen_in: AllergenUpdate,
    db: AsyncSession = Depends(get_db),
    _: dict = Depends(require_admin_user),  # Only admins can update allergens
):
    """
    Update an existing allergen.
    
    Only system administrators can update allergens.
    """
    service = AllergenService(db)
    allergen = await service.update_allergen(allergen_id, allergen_in)
    
    if not allergen:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Allergen not found"
        )
    
    return allergen


@router.delete("/{allergen_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_allergen(
    allergen_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    _: dict = Depends(require_admin_user),  # Only admins can delete allergens
):
    """
    Delete an allergen.
    
    Only system administrators can delete allergens.
    """
    service = AllergenService(db)
    success = await service.delete_allergen(allergen_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Allergen not found"
        )
