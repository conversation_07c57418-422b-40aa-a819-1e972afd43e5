import uuid
from pydantic import BaseModel, ConfigDict, Field, HttpUrl
from typing import Optional, List, Any  # Any for JSON allergens for now
from decimal import Decimal

# Import related schemas
from .menu_category import (
    MenuCategoryReadSimple,
)  # Use the simplified read schema to avoid greenlet issues
from .variant_group import VariantGroupRead, VariantGroupCreate, VariantGroupReadSimple  # noqa: E402
from .modifier_group import ModifierGroupRead, ModifierGroupCreate, ModifierGroupReadSimple
from .optional_group import OptionalGroupRead, OptionalGroupCreate, OptionalGroupReadSimple

# Import allergen schemas
from app.modules.core.functions.allergens.schemas.allergen import AllergenReadSimple

# Assuming a simple read schema exists for InventoryItem
# from app.modules.sharedModules.inventory.schemas.inventory_item import InventoryItemReadSimple


class MenuItemBase(BaseModel):
    """Base schema for Menu Item."""

    name: str = Field(
        ..., max_length=150, description="Name of the menu item (e.g., Classic Burger)"
    )
    description: Optional[str] = Field(None, description="Detailed description of the item")
    base_price: Decimal = Field(
        ..., ge=0, description="Base price of the item before variants/modifiers"
    )
    image_url: Optional[str] = Field(None, description="URL for the item's primary image")
    # Legacy allergen field removed - use allergen_ids instead
    allergen_ids: Optional[List[uuid.UUID]] = Field(
        None,
        description="List of allergen IDs associated with this menu item"
    )
    is_available: bool = Field(True, description="Is the item currently available for ordering?")
    is_active: bool = Field(True, description="Is the item active in the system (for soft delete)?")
    is_combo: bool = Field(False, description="Is this item a combo meal?")
    discount_percentage: Optional[Decimal] = Field(
        None,
        ge=0,
        le=100,
        description="Optional discount percentage (e.g., 10.5 for 10.5%)",
    )
    display_order: int = Field(0, description="Order within the menu category")


class MenuItemCreate(MenuItemBase):
    """Schema for creating a new Menu Item, including category and potentially nested groups."""

    category_id: uuid.UUID = Field(..., description="ID of the category this item belongs to")
    # Allow creating variant/modifier groups and options directly when creating an item
    variant_groups: List[VariantGroupCreate] = Field(
        [], description="Variant groups (e.g., Size) and their options"
    )
    modifier_groups: List[ModifierGroupCreate] = Field(
        [], description="Modifier groups (e.g., Add-ons) and their options"
    )
    optional_groups: List[OptionalGroupCreate] = Field(
        [], description="Optional groups (e.g., Sides, Drinks) and their options"
    )
    allergen_ids: List[str] = Field([], description="List of allergen IDs associated with this item")
    # Optional: Link existing inventory items by ID during creation
    # inventory_item_ids: List[int] = Field([], description="IDs of inventory items linked to this menu item")  # noqa: E501
    # tenant_id added by service


class MenuItemUpdate(MenuItemBase):
    """Schema for updating an existing Menu Item. All fields optional."""

    category_id: Optional[uuid.UUID] = None
    name: Optional[str] = Field(None, max_length=150)
    description: Optional[str] = None
    base_price: Optional[Decimal] = Field(None, ge=0)
    image_url: Optional[str] = None
    allergens_json: Optional[Any] = Field(None, alias="allergens")
    allergen_ids: Optional[List[str]] = None
    is_available: Optional[bool] = None
    is_active: Optional[bool] = None
    is_combo: Optional[bool] = None
    discount_percentage: Optional[Decimal] = Field(None, ge=0, le=100)
    display_order: Optional[int] = None
    # Allow updating variant/modifier groups during item update
    variant_groups: Optional[List[VariantGroupCreate]] = Field(
        None, description="Variant groups to replace existing ones"
    )
    modifier_groups: Optional[List[ModifierGroupCreate]] = Field(
        None, description="Modifier groups to replace existing ones"
    )
    optional_groups: Optional[List[OptionalGroupCreate]] = Field(
        None, description="Optional groups to replace existing ones"
    )
    # inventory_item_ids: Optional[List[int]] = None


class MenuItemRead(MenuItemBase):
    """Schema for reading a Menu Item, including all details and relations."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    category_id: uuid.UUID  # Keep category_id for reference
    category: MenuCategoryReadSimple  # Embed simplified category details
    variant_groups: List[VariantGroupRead] = []  # Embed variant groups and their options
    modifier_groups: List[ModifierGroupRead] = []  # Embed modifier groups and their options
    optional_groups: List[OptionalGroupRead] = []  # Embed optional groups and their options
    allergens: List[AllergenReadSimple] = []  # Embed allergen details
    # Optional: Include linked inventory items
    # inventory_items: List[InventoryItemReadSimple] = []

    model_config = ConfigDict(from_attributes=True)


# Simplified version for lists without full group details
class MenuItemReadSimple(MenuItemBase):
    """Schema for reading a Menu Item without full group details."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    category_id: uuid.UUID
    category: MenuCategoryReadSimple
    # Groups without options for performance
    variant_groups: List[VariantGroupReadSimple] = []
    modifier_groups: List[ModifierGroupReadSimple] = []
    optional_groups: List[OptionalGroupReadSimple] = []
    allergens: List[AllergenReadSimple] = []  # Include allergens in simple read

    model_config = ConfigDict(from_attributes=True)


# Public API version without redundant category object
class MenuItemReadPublic(MenuItemBase):
    """Schema for reading a Menu Item in public API without redundant category object."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    category_id: uuid.UUID  # Keep only category_id, not the full object
    variant_groups: List[VariantGroupRead] = []  # Full variant groups for public API
    modifier_groups: List[ModifierGroupRead] = []  # Full modifier groups for public API
    optional_groups: List[OptionalGroupRead] = []  # Full optional groups for public API
    allergens: List[AllergenReadSimple] = []  # Include allergens in public API

    model_config = ConfigDict(from_attributes=True)
