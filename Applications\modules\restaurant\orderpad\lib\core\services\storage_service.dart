import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive/hive.dart';
import '../constants/app_constants.dart';
import '../models/table_model.dart';
import '../models/order_model.dart';
import '../models/menu_item_model.dart';
import '../models/menu_model.dart';
import '../models/user_model.dart';

class StorageService {
  static late SharedPreferences _prefs;
  static late Box _userBox;
  static late Box _settingsBox;
  static late Box _ordersBox;
  static late Box _tablesBox;
  static late Box _menuBox;

  static Future<void> init() async {
    // Initialize SharedPreferences
    _prefs = await SharedPreferences.getInstance();
    
    // Register Hive adapters
    Hive.registerAdapter(TableModelAdapter());
    Hive.registerAdapter(OrderModelAdapter());
    Hive.registerAdapter(MenuItemModelAdapter());
    Hive.registerAdapter(MenuModelAdapter());
    Hive.registerAdapter(UserModelAdapter());
    
    // Initialize Hive boxes
    _userBox = await Hive.openBox(AppConstants.userBoxKey);
    _settingsBox = await Hive.openBox(AppConstants.settingsBoxKey);
    _ordersBox = await Hive.openBox(AppConstants.ordersBoxKey);
    _tablesBox = await Hive.openBox(AppConstants.tablesBoxKey);
    _menuBox = await Hive.openBox(AppConstants.menuBoxKey);
  }

  // SharedPreferences methods
  static Future<bool> setBool(String key, bool value) async {
    return await _prefs.setBool(key, value);
  }

  static bool getBool(String key, {bool defaultValue = false}) {
    return _prefs.getBool(key) ?? defaultValue;
  }

  static Future<bool> setString(String key, String value) async {
    return await _prefs.setString(key, value);
  }

  static String getString(String key, {String defaultValue = ''}) {
    return _prefs.getString(key) ?? defaultValue;
  }

  static Future<bool> setInt(String key, int value) async {
    return await _prefs.setInt(key, value);
  }

  static int getInt(String key, {int defaultValue = 0}) {
    return _prefs.getInt(key) ?? defaultValue;
  }

  static Future<bool> setDouble(String key, double value) async {
    return await _prefs.setDouble(key, value);
  }

  static double getDouble(String key, {double defaultValue = 0.0}) {
    return _prefs.getDouble(key) ?? defaultValue;
  }

  static Future<bool> remove(String key) async {
    return await _prefs.remove(key);
  }

  static Future<bool> clear() async {
    return await _prefs.clear();
  }

  // Hive box getters
  static Box get userBox => _userBox;
  static Box get settingsBox => _settingsBox;
  static Box get ordersBox => _ordersBox;
  static Box get tablesBox => _tablesBox;
  static Box get menuBox => _menuBox;

  // User-specific methods
  static Future<void> saveCurrentUser(Map<String, dynamic> user) async {
    await _userBox.put(AppConstants.currentUserKey, user);
    await setBool(AppConstants.isLoggedInKey, true);
  }

  static Map<String, dynamic>? getCurrentUser() {
    final user = _userBox.get(AppConstants.currentUserKey);
    if (user != null) {
      return Map<String, dynamic>.from(user);
    }
    return null;
  }

  static Future<void> clearCurrentUser() async {
    await _userBox.delete(AppConstants.currentUserKey);
    await setBool(AppConstants.isLoggedInKey, false);
  }

  static bool isLoggedIn() {
    return getBool(AppConstants.isLoggedInKey);
  }

  static String? getCurrentUserRole() {
    final user = getCurrentUser();
    return user?['role'];
  }

  // Orders methods
  static Future<void> saveOrder(String orderId, Map<String, dynamic> order) async {
    await _ordersBox.put(orderId, order);
  }

  static Map<String, dynamic>? getOrder(String orderId) {
    final order = _ordersBox.get(orderId);
    if (order != null) {
      return Map<String, dynamic>.from(order);
    }
    return null;
  }

  static List<Map<String, dynamic>> getAllOrders() {
    return _ordersBox.values
        .map((order) => Map<String, dynamic>.from(order))
        .toList();
  }

  static Future<void> deleteOrder(String orderId) async {
    await _ordersBox.delete(orderId);
  }

  static Future<void> clearAllOrders() async {
    await _ordersBox.clear();
  }

  // Tables methods
  static Future<void> saveTable(TableModel table) async {
    await _tablesBox.put(table.id, table.toJson());
  }
  
  static Future<void> saveTableData(String tableId, Map<String, dynamic> table) async {
    await _tablesBox.put(tableId, table);
  }

  static Map<String, dynamic>? getTable(String tableId) {
    final table = _tablesBox.get(tableId);
    if (table != null) {
      return Map<String, dynamic>.from(table);
    }
    return null;
  }

  static List<Map<String, dynamic>> getAllTables() {
    return _tablesBox.values
        .map((table) => Map<String, dynamic>.from(table))
        .toList();
  }

  static Future<void> deleteTable(String tableId) async {
    await _tablesBox.delete(tableId);
  }

  static Future<void> clearAllTables() async {
    await _tablesBox.clear();
  }

  // Menu methods
  static Future<void> saveMenuItem(String itemId, Map<String, dynamic> item) async {
    await _menuBox.put(itemId, item);
  }

  static Map<String, dynamic>? getMenuItem(String itemId) {
    final item = _menuBox.get(itemId);
    if (item != null) {
      return Map<String, dynamic>.from(item);
    }
    return null;
  }

  static List<Map<String, dynamic>> getAllMenuItems() {
    return _menuBox.values
        .map((item) => Map<String, dynamic>.from(item))
        .toList();
  }

  static Future<void> deleteMenuItem(String itemId) async {
    await _menuBox.delete(itemId);
  }

  static Future<void> clearAllMenuItems() async {
    await _menuBox.clear();
  }

  // Settings methods
  static Future<void> saveSetting(String key, dynamic value) async {
    await _settingsBox.put(key, value);
  }

  static T? getSetting<T>(String key, {T? defaultValue}) {
    return _settingsBox.get(key, defaultValue: defaultValue);
  }

  static Future<void> deleteSetting(String key) async {
    await _settingsBox.delete(key);
  }

  static Future<void> clearAllSettings() async {
    await _settingsBox.clear();
  }

  // Bulk operations
  static Future<void> clearAllData() async {
    await Future.wait([
      _userBox.clear(),
      _settingsBox.clear(),
      _ordersBox.clear(),
      _tablesBox.clear(),
      _menuBox.clear(),
      _prefs.clear(),
    ]);
  }

  // Export/Import methods for backup
  static Map<String, dynamic> exportData() {
    return {
      'users': _userBox.toMap(),
      'settings': _settingsBox.toMap(),
      'orders': _ordersBox.toMap(),
      'tables': _tablesBox.toMap(),
      'menu': _menuBox.toMap(),
    };
  }

  static Future<void> importData(Map<String, dynamic> data) async {
    if (data['users'] != null) {
      await _userBox.clear();
      await _userBox.putAll(Map<String, dynamic>.from(data['users']));
    }
    
    if (data['settings'] != null) {
      await _settingsBox.clear();
      await _settingsBox.putAll(Map<String, dynamic>.from(data['settings']));
    }
    
    if (data['orders'] != null) {
      await _ordersBox.clear();
      await _ordersBox.putAll(Map<String, dynamic>.from(data['orders']));
    }
    
    if (data['tables'] != null) {
      await _tablesBox.clear();
      await _tablesBox.putAll(Map<String, dynamic>.from(data['tables']));
    }
    
    if (data['menu'] != null) {
      await _menuBox.clear();
      await _menuBox.putAll(Map<String, dynamic>.from(data['menu']));
    }
  }
}