import uuid
import logging
from app.websockets.manager import emit_to_tenant
from app.modules.tenants.restaurants.table_management.models.table import TableStatus

logger = logging.getLogger(__name__)


async def emit_table_status_change(
    tenant_id: uuid.UUID, table_id: uuid.UUID, new_status: TableStatus
) -> None:
    """
    Emit a WebSocket event when a table's status changes.

    Args:
        tenant_id: ID of the tenant to emit the event to
        table_id: ID of the table that changed status
        new_status: New status of the table
    """
    try:
        # Prepare event data
        event_data = {
            "table_id": str(table_id),
            "new_status": new_status,
            "timestamp": str(uuid.uuid1().time),  # Use UUID1 time component as timestamp
        }

        # Emit event to the tenant
        await emit_to_tenant(
            tenant_id=tenant_id,
            event="table_status_changed",
            data=event_data,
        )
        logger.info(
            f"Emitted event table_status_changed for tenant {tenant_id}, table {table_id}, status {new_status}"
        )
    except Exception as e:
        logger.error(f"Error emitting table_status_changed event: {e}")


async def emit_table_update(tenant_id: uuid.UUID, table_data: dict) -> None:
    """
    Emit a WebSocket event when a table is updated.

    Args:
        tenant_id: ID of the tenant to emit the event to
        table_data: Updated table data
    """
    try:
        # Emit event to the tenant
        await emit_to_tenant(
            tenant_id=tenant_id,
            event="table_updated",
            data=table_data,
        )
        logger.info(
            f"Emitted event table_updated for tenant {tenant_id}, table {table_data.get('id')}"
        )
    except Exception as e:
        logger.error(f"Error emitting table_updated event: {e}")
