import uuid
from datetime import datetime
from typing import Optional, Any, Dict
from pydantic import BaseModel, Field, ConfigDict  # Adicionado ConfigDict

# --- Base Schema ---


class KitchenOrderBase(BaseModel):
    """Schema base com campos comuns para KitchenOrder."""

    order_details: Dict[str, Any] = Field(
        ..., description="Detalhes do pedido (itens, quantidades, notas, etc.)"
    )
    status: str = Field(
        default="pending",
        description="Status atual do pedido (pending, preparing, ready, served)",
    )
    source_sale_id: Optional[str] = Field(
        None, description="ID opcional da transação de venda de origem (POS)"
    )
    source_online_order_id: Optional[uuid.UUID] = Field(
        None, description="ID opcional do pedido online de origem"
    )
    source_order_id: Optional[uuid.UUID] = Field(
        None, description="ID opcional do pedido compartilhado de origem"
    )
    creator_user_id: Optional[uuid.UUID] = Field(
        None,
        description="ID opcional do usuário que criou o pedido (ex: usuário do pedido online)",
    )


# --- Create Schema ---


class KitchenOrderCreate(KitchenOrderBase):
    """Schema para criar um novo KitchenOrder. tenant_id será adicionado no serviço."""

    # tenant_id será adicionado pelo serviço/endpoint com base no tenant atual
    pass  # Herda todos os campos de KitchenOrderBase


# --- Update Schema ---


class KitchenOrderUpdate(BaseModel):
    """Schema para atualizar um KitchenOrder existente. Permite atualizar status e detalhes."""

    status: Optional[str] = Field(
        None, description="Novo status do pedido (pending, preparing, ready, served)"
    )
    order_details: Optional[Dict[str, Any]] = Field(
        None, description="Detalhes atualizados do pedido (itens, notas, etc.)"
    )


# --- InDBBase Schema (Campos do Banco de Dados) ---


class KitchenOrderInDBBase(KitchenOrderBase):
    """Schema base que inclui campos gerenciados pelo banco de dados."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)  # Permite mapear atributos do modelo SQLAlchemy


# --- Read Schema (Resposta da API) ---


class KitchenOrderRead(KitchenOrderInDBBase):
    """Schema para retornar dados de KitchenOrder na API."""

    pass  # Herda todos os campos de KitchenOrderInDBBase
