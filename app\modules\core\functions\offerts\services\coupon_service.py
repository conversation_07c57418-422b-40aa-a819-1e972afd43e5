import uuid
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from app.modules.core.functions.offerts.models.coupon import Coupon
from app.modules.core.functions.offerts.schemas.coupon import CouponCreate, CouponUpdate

class CouponService:
    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session

    async def create_coupon(self, coupon_in: CouponCreate) -> Coupon:
        coupon = Coupon(**coupon_in.dict())
        self.db_session.add(coupon)
        await self.db_session.commit()
        await self.db_session.refresh(coupon)
        return coupon

    async def get_coupon_by_code(self, code: str) -> Coupon | None:
        result = await self.db_session.execute(select(Coupon).filter(Coupon.code == code))
        return result.scalars().first()

    async def validate_coupon(self, code: str) -> bool:
        coupon = await self.get_coupon_by_code(code)
        if not coupon:
            return False
        if not coupon.is_active:
            return False
        if coupon.expires_at and coupon.expires_at < datetime.utcnow():
            return False
        if coupon.usage_limit is not None and coupon.usage_count >= coupon.usage_limit:
            return False
        return True

    async def use_coupon(self, code: str) -> Coupon | None:
        coupon = await self.get_coupon_by_code(code)
        if coupon and await self.validate_coupon(code):
            coupon.usage_count += 1
            await self.db_session.commit()
            await self.db_session.refresh(coupon)
            return coupon
        return None

    async def update_coupon(self, code: str, coupon_in: CouponUpdate) -> Coupon | None:
        coupon = await self.get_coupon_by_code(code)
        if coupon:
            update_data = coupon_in.dict(exclude_unset=True)
            for key, value in update_data.items():
                setattr(coupon, key, value)
            await self.db_session.commit()
            await self.db_session.refresh(coupon)
        return coupon

    async def delete_coupon(self, code: str) -> bool:
        coupon = await self.get_coupon_by_code(code)
        if coupon:
            await self.db_session.delete(coupon)
            await self.db_session.commit()
            return True
        return False 