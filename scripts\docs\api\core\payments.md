# Core - Payments

**Categoria:** Core
**Módulo:** Payments
**Total de Endpoints:** 22
**Gerado em:** 27/06/2025, 20:20:26

## 📋 Endpoints

- [GET /api/modules/core/payments/methods](#get-apimodulescorepaymentsmethods) - List Payment Methods
- [POST /api/modules/core/payments/methods](#post-apimodulescorepaymentsmethods) - Create Payment Method
- [DELETE /api/modules/core/payments/methods/{method_id}](#delete-apimodulescorepaymentsmethodsmethod-id) - Delete Payment Method
- [GET /api/modules/core/payments/methods/{method_id}](#get-apimodulescorepaymentsmethodsmethod-id) - Get Payment Method
- [PUT /api/modules/core/payments/methods/{method_id}](#put-apimodulescorepaymentsmethodsmethod-id) - Update Payment Method
- [GET /api/modules/core/payments/processors](#get-apimodulescorepaymentsprocessors) - List Payment Processors
- [POST /api/modules/core/payments/processors](#post-apimodulescorepaymentsprocessors) - Create Payment Processor
- [DELETE /api/modules/core/payments/processors/{processor_id}](#delete-apimodulescorepaymentsprocessorsprocessor-id) - Delete Payment Processor
- [GET /api/modules/core/payments/processors/{processor_id}](#get-apimodulescorepaymentsprocessorsprocessor-id) - Get Payment Processor
- [PUT /api/modules/core/payments/processors/{processor_id}](#put-apimodulescorepaymentsprocessorsprocessor-id) - Update Payment Processor
- [GET /api/modules/core/payments/processors/{processor_id}/admin](#get-apimodulescorepaymentsprocessorsprocessor-idadmin) - Get Payment Processor (Admin)
- [PUT /api/modules/core/payments/refunds/{refund_id}](#put-apimodulescorepaymentsrefundsrefund-id) - Update Payment Refund
- [POST /api/modules/core/payments/refunds/{refund_id}/check-status](#post-apimodulescorepaymentsrefundsrefund-idcheck-status) - Check Refund Status
- [POST /api/modules/core/payments/refunds/{refund_id}/process](#post-apimodulescorepaymentsrefundsrefund-idprocess) - Process Refund
- [GET /api/modules/core/payments/transactions](#get-apimodulescorepaymentstransactions) - List Payment Transactions
- [POST /api/modules/core/payments/transactions](#post-apimodulescorepaymentstransactions) - Create Payment Transaction
- [GET /api/modules/core/payments/transactions/{transaction_id}](#get-apimodulescorepaymentstransactionstransaction-id) - Get Payment Transaction
- [PUT /api/modules/core/payments/transactions/{transaction_id}](#put-apimodulescorepaymentstransactionstransaction-id) - Update Payment Transaction
- [POST /api/modules/core/payments/transactions/{transaction_id}/check-status](#post-apimodulescorepaymentstransactionstransaction-idcheck-status) - Check Payment Status
- [POST /api/modules/core/payments/transactions/{transaction_id}/process](#post-apimodulescorepaymentstransactionstransaction-idprocess) - Process Payment
- [POST /api/modules/core/payments/transactions/{transaction_id}/refunds](#post-apimodulescorepaymentstransactionstransaction-idrefunds) - Create Payment Refund
- [GET /api/modules/core/payments/transactions/{transaction_id}/with-refunds](#get-apimodulescorepaymentstransactionstransaction-idwith-refunds) - Get Transaction with Refunds

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### PaymentProcessorCreate

**Descrição:** Schema for creating a new payment processor.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `processor_type` | PaymentProcessorType | ✅ | - |
| `is_active` | boolean | ❌ | - |
| `is_default` | boolean | ❌ | - |
| `sandbox_mode` | boolean | ❌ | - |
| `webhook_url` | unknown | ❌ | - |
| `additional_config` | unknown | ❌ | - |
| `api_key` | unknown | ❌ | - |
| `api_secret` | unknown | ❌ | - |
| `webhook_secret` | unknown | ❌ | - |

### PaymentProcessorRead

**Descrição:** Schema for reading a payment processor.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `processor_type` | PaymentProcessorType | ✅ | - |
| `is_active` | boolean | ❌ | - |
| `is_default` | boolean | ❌ | - |
| `sandbox_mode` | boolean | ❌ | - |
| `webhook_url` | unknown | ❌ | - |
| `additional_config` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |

### PaymentProcessorReadAdmin

**Descrição:** Schema for reading a payment processor with sensitive information.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `processor_type` | PaymentProcessorType | ✅ | - |
| `is_active` | boolean | ❌ | - |
| `is_default` | boolean | ❌ | - |
| `sandbox_mode` | boolean | ❌ | - |
| `webhook_url` | unknown | ❌ | - |
| `additional_config` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `api_key` | unknown | ❌ | - |
| `webhook_secret` | unknown | ❌ | - |

### PaymentProcessorUpdate

**Descrição:** Schema for updating an existing payment processor.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | - |
| `processor_type` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `is_default` | unknown | ❌ | - |
| `sandbox_mode` | unknown | ❌ | - |
| `api_key` | unknown | ❌ | - |
| `api_secret` | unknown | ❌ | - |
| `webhook_url` | unknown | ❌ | - |
| `webhook_secret` | unknown | ❌ | - |
| `additional_config` | unknown | ❌ | - |

### PaymentRefundCreate

**Descrição:** Schema for creating a new payment refund.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `amount` | unknown | ✅ | - |
| `reason` | unknown | ❌ | - |
| `status` | PaymentStatus | ❌ | - |
| `external_id` | unknown | ❌ | - |
| `external_reference` | unknown | ❌ | - |
| `metadata` | unknown | ❌ | - |

### PaymentRefundRead

**Descrição:** Schema for reading a payment refund.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `amount` | string | ✅ | - |
| `reason` | unknown | ❌ | - |
| `status` | PaymentStatus | ❌ | - |
| `external_id` | unknown | ❌ | - |
| `external_reference` | unknown | ❌ | - |
| `metadata` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `transaction_id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `processed_at` | unknown | ❌ | - |
| `created_by` | unknown | ❌ | - |

### PaymentRefundUpdate

**Descrição:** Schema for updating an existing payment refund.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `status` | unknown | ❌ | - |
| `external_id` | unknown | ❌ | - |
| `external_reference` | unknown | ❌ | - |
| `processed_at` | unknown | ❌ | - |
| `metadata` | unknown | ❌ | - |

### PaymentTransactionCreate

**Descrição:** Schema for creating a new payment transaction.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `amount` | unknown | ✅ | - |
| `currency` | string | ❌ | - |
| `status` | PaymentStatus | ❌ | - |
| `source_type` | string | ✅ | - |
| `source_id` | unknown | ❌ | - |
| `external_id` | unknown | ❌ | - |
| `external_reference` | unknown | ❌ | - |
| `customer_id` | unknown | ❌ | - |
| `customer_email` | unknown | ❌ | - |
| `customer_name` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `metadata` | unknown | ❌ | - |
| `processor_id` | unknown | ❌ | - |
| `method_id` | unknown | ❌ | - |

### PaymentTransactionRead

**Descrição:** Schema for reading a payment transaction.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `amount` | string | ✅ | - |
| `currency` | string | ❌ | - |
| `status` | PaymentStatus | ❌ | - |
| `source_type` | string | ✅ | - |
| `source_id` | unknown | ❌ | - |
| `external_id` | unknown | ❌ | - |
| `external_reference` | unknown | ❌ | - |
| `customer_id` | unknown | ❌ | - |
| `customer_email` | unknown | ❌ | - |
| `customer_name` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `metadata` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `processor_id` | unknown | ❌ | - |
| `method_id` | unknown | ❌ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `processed_at` | unknown | ❌ | - |

### PaymentTransactionUpdate

**Descrição:** Schema for updating an existing payment transaction.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `status` | unknown | ❌ | - |
| `external_id` | unknown | ❌ | - |
| `external_reference` | unknown | ❌ | - |
| `processed_at` | unknown | ❌ | - |
| `metadata` | unknown | ❌ | - |

### PaymentTransactionWithRefundsRead

**Descrição:** Schema for reading a payment transaction with refunds.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `amount` | string | ✅ | - |
| `currency` | string | ❌ | - |
| `status` | PaymentStatus | ❌ | - |
| `source_type` | string | ✅ | - |
| `source_id` | unknown | ❌ | - |
| `external_id` | unknown | ❌ | - |
| `external_reference` | unknown | ❌ | - |
| `customer_id` | unknown | ❌ | - |
| `customer_email` | unknown | ❌ | - |
| `customer_name` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `metadata` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `processor_id` | unknown | ❌ | - |
| `method_id` | unknown | ❌ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `processed_at` | unknown | ❌ | - |
| `refunds` | Array[PaymentRefundRead] | ❌ | - |
| `refunded_amount` | unknown | ❌ | - |
| `remaining_amount` | unknown | ❌ | - |

### app__modules__core__payments__schemas__payment_method__PaymentMethodCreate

**Descrição:** Schema for creating a new payment method.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `method_type` | app__modules__core__payments__models__payment_method__PaymentMethodType | ✅ | - |
| `is_active` | boolean | ❌ | - |
| `is_default` | boolean | ❌ | - |
| `icon` | unknown | ❌ | - |
| `display_order` | integer | ❌ | - |
| `processor_method_id` | unknown | ❌ | - |
| `processor_config` | unknown | ❌ | - |
| `processor_id` | unknown | ❌ | - |

### app__modules__core__payments__schemas__payment_method__PaymentMethodRead

**Descrição:** Schema for reading a payment method.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `method_type` | app__modules__core__payments__models__payment_method__PaymentMethodType | ✅ | - |
| `is_active` | boolean | ❌ | - |
| `is_default` | boolean | ❌ | - |
| `icon` | unknown | ❌ | - |
| `display_order` | integer | ❌ | - |
| `processor_method_id` | unknown | ❌ | - |
| `processor_config` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `processor_id` | unknown | ❌ | - |

### app__modules__core__payments__schemas__payment_method__PaymentMethodUpdate

**Descrição:** Schema for updating an existing payment method.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | - |
| `method_type` | unknown | ❌ | - |
| `is_active` | unknown | ❌ | - |
| `is_default` | unknown | ❌ | - |
| `icon` | unknown | ❌ | - |
| `display_order` | unknown | ❌ | - |
| `processor_id` | unknown | ❌ | - |
| `processor_method_id` | unknown | ❌ | - |
| `processor_config` | unknown | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/modules/core/payments/methods {#get-apimodulescorepaymentsmethods}

**Resumo:** List Payment Methods
**Descrição:** List all payment methods for the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `is_active` | string | query | ❌ | Filter by active status |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/payments/methods" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/payments/methods {#post-apimodulescorepaymentsmethods}

**Resumo:** Create Payment Method
**Descrição:** Create a new payment method for the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [app__modules__core__payments__schemas__payment_method__PaymentMethodCreate](#app__modules__core__payments__schemas__payment_method__paymentmethodcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [app__modules__core__payments__schemas__payment_method__PaymentMethodRead](#app__modules__core__payments__schemas__payment_method__paymentmethodread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/payments/methods" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/core/payments/methods/{method_id} {#delete-apimodulescorepaymentsmethodsmethod-id}

**Resumo:** Delete Payment Method
**Descrição:** Delete an existing payment method.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `method_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/core/payments/methods/{method_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/payments/methods/{method_id} {#get-apimodulescorepaymentsmethodsmethod-id}

**Resumo:** Get Payment Method
**Descrição:** Get details of a specific payment method.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `method_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [app__modules__core__payments__schemas__payment_method__PaymentMethodRead](#app__modules__core__payments__schemas__payment_method__paymentmethodread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/payments/methods/{method_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/core/payments/methods/{method_id} {#put-apimodulescorepaymentsmethodsmethod-id}

**Resumo:** Update Payment Method
**Descrição:** Update an existing payment method.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `method_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [app__modules__core__payments__schemas__payment_method__PaymentMethodUpdate](#app__modules__core__payments__schemas__payment_method__paymentmethodupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [app__modules__core__payments__schemas__payment_method__PaymentMethodRead](#app__modules__core__payments__schemas__payment_method__paymentmethodread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/core/payments/methods/{method_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/payments/processors {#get-apimodulescorepaymentsprocessors}

**Resumo:** List Payment Processors
**Descrição:** List all payment processors for the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `is_active` | string | query | ❌ | Filter by active status |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/payments/processors" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/payments/processors {#post-apimodulescorepaymentsprocessors}

**Resumo:** Create Payment Processor
**Descrição:** Create a new payment processor for the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [PaymentProcessorCreate](#paymentprocessorcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PaymentProcessorRead](#paymentprocessorread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/payments/processors" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/core/payments/processors/{processor_id} {#delete-apimodulescorepaymentsprocessorsprocessor-id}

**Resumo:** Delete Payment Processor
**Descrição:** Delete an existing payment processor.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `processor_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/core/payments/processors/{processor_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/payments/processors/{processor_id} {#get-apimodulescorepaymentsprocessorsprocessor-id}

**Resumo:** Get Payment Processor
**Descrição:** Get details of a specific payment processor.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `processor_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PaymentProcessorRead](#paymentprocessorread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/payments/processors/{processor_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/core/payments/processors/{processor_id} {#put-apimodulescorepaymentsprocessorsprocessor-id}

**Resumo:** Update Payment Processor
**Descrição:** Update an existing payment processor.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `processor_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [PaymentProcessorUpdate](#paymentprocessorupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PaymentProcessorRead](#paymentprocessorread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/core/payments/processors/{processor_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/payments/processors/{processor_id}/admin {#get-apimodulescorepaymentsprocessorsprocessor-idadmin}

**Resumo:** Get Payment Processor (Admin)
**Descrição:** Get details of a specific payment processor, including sensitive information.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `processor_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PaymentProcessorReadAdmin](#paymentprocessorreadadmin)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/payments/processors/{processor_id}/admin" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/core/payments/refunds/{refund_id} {#put-apimodulescorepaymentsrefundsrefund-id}

**Resumo:** Update Payment Refund
**Descrição:** Update an existing payment refund.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `refund_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [PaymentRefundUpdate](#paymentrefundupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PaymentRefundRead](#paymentrefundread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/core/payments/refunds/{refund_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/core/payments/refunds/{refund_id}/check-status {#post-apimodulescorepaymentsrefundsrefund-idcheck-status}

**Resumo:** Check Refund Status
**Descrição:** Check the status of a refund with the payment processor.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `refund_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PaymentRefundRead](#paymentrefundread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/payments/refunds/{refund_id}/check-status" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/payments/refunds/{refund_id}/process {#post-apimodulescorepaymentsrefundsrefund-idprocess}

**Resumo:** Process Refund
**Descrição:** Process a refund using the appropriate payment processor.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `refund_id` | string | path | ✅ | - |
| `reason` | string | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** {'type': 'object', 'additionalProperties': True, 'default': {}, 'title': 'Metadata'}

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PaymentRefundRead](#paymentrefundread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/payments/refunds/{refund_id}/process" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/payments/transactions {#get-apimodulescorepaymentstransactions}

**Resumo:** List Payment Transactions
**Descrição:** List all payment transactions for the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `status_filter` | string | query | ❌ | Filter by transaction status |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/payments/transactions" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/payments/transactions {#post-apimodulescorepaymentstransactions}

**Resumo:** Create Payment Transaction
**Descrição:** Create a new payment transaction for the tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [PaymentTransactionCreate](#paymenttransactioncreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PaymentTransactionRead](#paymenttransactionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/payments/transactions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/payments/transactions/{transaction_id} {#get-apimodulescorepaymentstransactionstransaction-id}

**Resumo:** Get Payment Transaction
**Descrição:** Get details of a specific payment transaction.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `transaction_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PaymentTransactionRead](#paymenttransactionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/payments/transactions/{transaction_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/core/payments/transactions/{transaction_id} {#put-apimodulescorepaymentstransactionstransaction-id}

**Resumo:** Update Payment Transaction
**Descrição:** Update an existing payment transaction.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `transaction_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [PaymentTransactionUpdate](#paymenttransactionupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PaymentTransactionRead](#paymenttransactionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/core/payments/transactions/{transaction_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/core/payments/transactions/{transaction_id}/check-status {#post-apimodulescorepaymentstransactionstransaction-idcheck-status}

**Resumo:** Check Payment Status
**Descrição:** Check the status of a payment with the payment processor.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `transaction_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PaymentTransactionRead](#paymenttransactionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/payments/transactions/{transaction_id}/check-status" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/payments/transactions/{transaction_id}/process {#post-apimodulescorepaymentstransactionstransaction-idprocess}

**Resumo:** Process Payment
**Descrição:** Process a payment using the appropriate payment processor.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `transaction_id` | string | path | ✅ | - |
| `payment_method_id` | string | query | ❌ | - |
| `customer_id` | string | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** {'type': 'object', 'additionalProperties': True, 'default': {}, 'title': 'Metadata'}

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PaymentTransactionRead](#paymenttransactionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/payments/transactions/{transaction_id}/process" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/core/payments/transactions/{transaction_id}/refunds {#post-apimodulescorepaymentstransactionstransaction-idrefunds}

**Resumo:** Create Payment Refund
**Descrição:** Create a new refund for a payment transaction.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `transaction_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [PaymentRefundCreate](#paymentrefundcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PaymentRefundRead](#paymentrefundread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/payments/transactions/{transaction_id}/refunds" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/payments/transactions/{transaction_id}/with-refunds {#get-apimodulescorepaymentstransactionstransaction-idwith-refunds}

**Resumo:** Get Transaction with Refunds
**Descrição:** Get a transaction with its refunds and calculated refund totals.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `transaction_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PaymentTransactionWithRefundsRead](#paymenttransactionwithrefundsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/payments/transactions/{transaction_id}/with-refunds" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
