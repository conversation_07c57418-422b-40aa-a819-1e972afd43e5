import uuid
from typing import Optional, Dict, Any, List
from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel, Field, ConfigDict, validator

from app.modules.tenants.restaurants.table_reservation.models.reservation import (
    ReservationStatus,
    BlacklistType,
)


class ReservationBase(BaseModel):
    """Base schema for reservation."""

    party_size: int = Field(..., ge=1, description="Number of people in the party")
    reservation_date: datetime = Field(..., description="Date and time of the reservation")
    duration_minutes: Optional[int] = Field(
        90, ge=30, description="Duration of the reservation in minutes"
    )
    special_requests: Optional[str] = Field(
        None, description="Special requests for the reservation"
    )
    occasion: Optional[str] = Field(None, description="Occasion for the reservation")
    notes: Optional[str] = Field(None, description="Additional notes about the reservation")


class ReservationCreate(ReservationBase):
    """Schema for creating a new reservation."""

    table_id: Optional[uuid.UUID] = Field(None, description="ID of the table for the reservation")
    customer_id: Optional[uuid.UUID] = Field(
        None, description="ID of the customer making the reservation"
    )

    # Guest information (for non-registered customers)
    guest_name: Optional[str] = Field(
        None, description="Name of the guest (for non-registered customers)"
    )
    guest_email: Optional[str] = Field(
        None, description="Email of the guest (for non-registered customers)"
    )
    guest_phone: Optional[str] = Field(
        None, description="Phone number of the guest (for non-registered customers)"
    )

    # Deposit information
    deposit_required: Optional[bool] = Field(False, description="Whether a deposit is required")
    deposit_amount: Optional[Decimal] = Field(None, description="Amount of the deposit")

    @validator("guest_name", "guest_email", "guest_phone")
    def validate_guest_info(cls, v, values):
        """Validate that guest information is provided if customer_id is not."""
        if "customer_id" not in values or values["customer_id"] is None:
            if v is None:
                raise ValueError("Guest information is required when customer_id is not provided")
        return v


class ReservationUpdate(BaseModel):
    """Schema for updating an existing reservation."""

    table_id: Optional[uuid.UUID] = Field(None, description="ID of the table for the reservation")
    party_size: Optional[int] = Field(None, ge=1, description="Number of people in the party")
    reservation_date: Optional[datetime] = Field(
        None, description="Date and time of the reservation"
    )
    duration_minutes: Optional[int] = Field(
        None, ge=30, description="Duration of the reservation in minutes"
    )
    status: Optional[ReservationStatus] = Field(None, description="Status of the reservation")
    special_requests: Optional[str] = Field(
        None, description="Special requests for the reservation"
    )
    occasion: Optional[str] = Field(None, description="Occasion for the reservation")
    notes: Optional[str] = Field(None, description="Additional notes about the reservation")

    # Deposit information
    deposit_required: Optional[bool] = Field(None, description="Whether a deposit is required")
    deposit_amount: Optional[Decimal] = Field(None, description="Amount of the deposit")
    deposit_paid: Optional[bool] = Field(None, description="Whether the deposit has been paid")
    payment_id: Optional[str] = Field(None, description="Reference to payment")


class ReservationRead(ReservationBase):
    """Schema for reading a reservation."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    table_id: Optional[uuid.UUID] = None
    customer_id: Optional[uuid.UUID] = None

    # Reservation details
    reservation_number: str
    status: ReservationStatus

    # Guest information
    guest_name: Optional[str] = None
    guest_email: Optional[str] = None
    guest_phone: Optional[str] = None

    # Deposit information
    deposit_required: bool
    deposit_amount: Optional[Decimal] = None
    deposit_paid: bool
    payment_id: Optional[str] = None

    # Timestamps
    created_at: datetime
    updated_at: datetime

    # Include related data in the response if needed
    table: Optional[Dict[str, Any]] = None
    customer: Optional[Dict[str, Any]] = None

    model_config = ConfigDict(from_attributes=True)


class CustomerBlacklistBase(BaseModel):
    """Base schema for customer blacklist."""

    blacklist_type: BlacklistType = Field(
        default=BlacklistType.SUSPENDED, description="Type of blacklist entry"
    )
    reason: Optional[str] = Field(None, description="Reason for blacklisting")

    # For suspended customers
    suspension_start_date: Optional[datetime] = Field(None, description="Start date of suspension")
    suspension_end_date: Optional[datetime] = Field(None, description="End date of suspension")

    # Deposit requirements after suspension
    require_deposit: bool = Field(
        False, description="Whether to require a deposit for future reservations"
    )
    deposit_amount: Optional[Decimal] = Field(None, description="Amount of the deposit to require")


class CustomerBlacklistCreate(CustomerBlacklistBase):
    """Schema for creating a new customer blacklist entry."""

    customer_id: Optional[uuid.UUID] = Field(None, description="ID of the customer to blacklist")

    # Guest information (for non-registered customers)
    guest_name: Optional[str] = Field(
        None, description="Name of the guest (for non-registered customers)"
    )
    guest_email: Optional[str] = Field(
        None, description="Email of the guest (for non-registered customers)"
    )
    guest_phone: Optional[str] = Field(
        None, description="Phone number of the guest (for non-registered customers)"
    )

    @validator("guest_name", "guest_email", "guest_phone")
    def validate_guest_info(cls, v, values):
        """Validate that guest information is provided if customer_id is not."""
        if "customer_id" not in values or values["customer_id"] is None:
            if v is None:
                raise ValueError("Guest information is required when customer_id is not provided")
        return v

    @validator("suspension_end_date")
    def validate_suspension_dates(cls, v, values):
        """Validate that suspension end date is after start date."""
        if (
            v is not None
            and "suspension_start_date" in values
            and values["suspension_start_date"] is not None
        ):
            if v <= values["suspension_start_date"]:
                raise ValueError("Suspension end date must be after start date")
        return v


class CustomerBlacklistUpdate(BaseModel):
    """Schema for updating an existing customer blacklist entry."""

    blacklist_type: Optional[BlacklistType] = Field(None, description="Type of blacklist entry")
    reason: Optional[str] = Field(None, description="Reason for blacklisting")

    # For suspended customers
    suspension_start_date: Optional[datetime] = Field(None, description="Start date of suspension")
    suspension_end_date: Optional[datetime] = Field(None, description="End date of suspension")

    # Deposit requirements after suspension
    require_deposit: Optional[bool] = Field(
        None, description="Whether to require a deposit for future reservations"
    )
    deposit_amount: Optional[Decimal] = Field(None, description="Amount of the deposit to require")

    @validator("suspension_end_date")
    def validate_suspension_dates(cls, v, values):
        """Validate that suspension end date is after start date."""
        if (
            v is not None
            and "suspension_start_date" in values
            and values["suspension_start_date"] is not None
        ):
            if v <= values["suspension_start_date"]:
                raise ValueError("Suspension end date must be after start date")
        return v


class CustomerBlacklistRead(CustomerBlacklistBase):
    """Schema for reading a customer blacklist entry."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    customer_id: Optional[uuid.UUID] = None

    # Guest information
    guest_name: Optional[str] = None
    guest_email: Optional[str] = None
    guest_phone: Optional[str] = None

    # Timestamps
    created_at: datetime
    updated_at: datetime

    # Include related data in the response if needed
    customer: Optional[Dict[str, Any]] = None

    # Computed property
    is_active: bool

    model_config = ConfigDict(from_attributes=True)
