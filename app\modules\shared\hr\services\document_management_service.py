from typing import List, Optional
import uuid
import os
import shutil
from fastapi import UploadFile
from sqlalchemy import select, or_
from sqlalchemy.ext.asyncio import AsyncSession

from app.modules.shared.hr.models.document_management import (
    Document,
    DocumentAccess,
    DocumentSignature,
    DocumentTemplate,
    DocumentStatus,
    DocumentType,
)
from app.modules.shared.hr.models.employee import Employee  # noqa: E402
from app.modules.shared.hr.schemas.document_management import (
    DocumentCreate,
    DocumentUpdate,
    DocumentAccessCreate,
    DocumentSignatureCreate,
    DocumentTemplateCreate,
    GenerateDocumentRequest,
)
from app.core.exceptions import NotFoundError  # noqa: E402
from app.core.config import settings


class DocumentManagementService:
    """Service for document management operations."""

    @staticmethod
    async def create_document(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        document_data: DocumentCreate,
        file: UploadFile,
    ) -> Document:
        """Create a new document with file upload."""
        # Verify that the employee exists if employee_id is provided
        if document_data.employee_id:
            stmt = select(Employee).where(
                Employee.id == document_data.employee_id,
                Employee.tenant_id == tenant_id,
            )
            result = await db.execute(stmt)
            employee = result.scalars().first()

            if not employee:
                raise NotFoundError(f"Employee with id {document_data.employee_id} not found")

        # Create directory if it doesn't exist
        upload_dir = os.path.join(settings.MEDIA_ROOT, "documents", str(tenant_id))
        os.makedirs(upload_dir, exist_ok=True)

        # Generate unique filename
        file_extension = os.path.splitext(file.filename)[1]
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = os.path.join(upload_dir, unique_filename)

        # Save file
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        # Create document record
        document = Document(
            tenant_id=tenant_id,
            employee_id=document_data.employee_id,
            title=document_data.title,
            description=document_data.description,
            document_type=document_data.document_type,
            file_path=file_path,
            file_name=document_data.file_name,
            file_size=document_data.file_size,
            file_type=document_data.file_type,
            is_confidential=document_data.is_confidential,
            access_level=document_data.access_level,
            expiry_date=document_data.expiry_date,
            tags=document_data.tags or [],
            metadata=document_data.metadata or {},
        )

        db.add(document)
        await db.commit()
        await db.refresh(document)
        return document

    @staticmethod
    async def get_document(
        db: AsyncSession, tenant_id: uuid.UUID, document_id: uuid.UUID
    ) -> Document:
        """Get a document by ID."""
        stmt = select(Document).where(Document.id == document_id, Document.tenant_id == tenant_id)
        result = await db.execute(stmt)
        document = result.scalars().first()

        if not document:
            raise NotFoundError(f"Document with id {document_id} not found")

        return document

    @staticmethod
    async def get_documents(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        employee_id: Optional[uuid.UUID] = None,
        document_type: Optional[DocumentType] = None,
        status: Optional[DocumentStatus] = None,
        is_confidential: Optional[bool] = None,
        search: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[Document]:
        """Get all documents with optional filtering."""
        query = select(Document).where(Document.tenant_id == tenant_id)

        # Apply filters if provided
        if employee_id:
            query = query.where(Document.employee_id == employee_id)

        if document_type:
            query = query.where(Document.document_type == document_type)

        if status:
            query = query.where(Document.status == status)

        if is_confidential is not None:
            query = query.where(Document.is_confidential == is_confidential)

        if search:
            query = query.where(
                or_(
                    Document.title.ilike(f"%{search}%"),
                    Document.description.ilike(f"%{search}%"),
                    Document.file_name.ilike(f"%{search}%"),
                )
            )

        # Apply pagination
        query = query.offset(skip).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def update_document(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        document_id: uuid.UUID,
        document_data: DocumentUpdate,
    ) -> Document:
        """Update a document."""
        # Get the document first to ensure it exists
        document = await DocumentManagementService.get_document(db, tenant_id, document_id)

        # Update document fields
        for key, value in document_data.model_dump(exclude_unset=True).items():
            setattr(document, key, value)

        await db.commit()
        await db.refresh(document)
        return document

    @staticmethod
    async def delete_document(
        db: AsyncSession, tenant_id: uuid.UUID, document_id: uuid.UUID
    ) -> None:
        """Delete a document."""
        document = await DocumentManagementService.get_document(db, tenant_id, document_id)

        # Delete the file
        if os.path.exists(document.file_path):
            os.remove(document.file_path)

        # Delete the database record
        await db.delete(document)
        await db.commit()

    @staticmethod
    async def record_document_access(
        db: AsyncSession, tenant_id: uuid.UUID, access_data: DocumentAccessCreate
    ) -> DocumentAccess:
        """Record a document access."""
        # Verify that the document exists
        await DocumentManagementService.get_document(db, tenant_id, access_data.document_id)

        # Create access record
        access = DocumentAccess(tenant_id=tenant_id, **access_data.model_dump())

        db.add(access)
        await db.commit()
        await db.refresh(access)
        return access

    @staticmethod
    async def create_document_signature(
        db: AsyncSession, tenant_id: uuid.UUID, signature_data: DocumentSignatureCreate
    ) -> DocumentSignature:
        """Create a document signature."""
        # Verify that the document exists
        await DocumentManagementService.get_document(db, tenant_id, signature_data.document_id)

        # Create signature record
        signature = DocumentSignature(tenant_id=tenant_id, **signature_data.model_dump())

        db.add(signature)
        await db.commit()
        await db.refresh(signature)
        return signature

    @staticmethod
    async def create_document_template(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        template_data: DocumentTemplateCreate,
        file: Optional[UploadFile] = None,
    ) -> DocumentTemplate:
        """Create a document template."""
        # Create directory if it doesn't exist
        upload_dir = os.path.join(settings.MEDIA_ROOT, "templates", str(tenant_id))
        os.makedirs(upload_dir, exist_ok=True)

        file_path = template_data.file_path

        # If file is provided, save it
        if file:
            file_extension = os.path.splitext(file.filename)[1]
            unique_filename = f"{uuid.uuid4()}{file_extension}"
            file_path = os.path.join(upload_dir, unique_filename)

            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)

        # Create template record
        template = DocumentTemplate(
            tenant_id=tenant_id,
            file_path=file_path,
            **template_data.model_dump(exclude={"file_path"}),
        )

        db.add(template)
        await db.commit()
        await db.refresh(template)
        return template

    @staticmethod
    async def generate_document_from_template(
        db: AsyncSession, tenant_id: uuid.UUID, request_data: GenerateDocumentRequest
    ) -> Document:
        """Generate a document from a template."""
        # Get the template
        stmt = select(DocumentTemplate).where(
            DocumentTemplate.id == request_data.template_id,
            DocumentTemplate.tenant_id == tenant_id,
        )
        result = await db.execute(stmt)
        template = result.scalars().first()

        if not template:
            raise NotFoundError(f"Template with id {request_data.template_id} not found")

        # TODO: Implement document generation logic
        # This would involve replacing variables in the template with values from
        # request_data.variable_values

        # For now, we'll just create a new document based on the template
        document = Document(
            tenant_id=tenant_id,
            employee_id=request_data.employee_id,
            title=request_data.title or template.name,
            description=request_data.description or template.description,
            document_type=template.document_type,
            file_path=template.file_path,  # This should be a new file path in a real implementation
            file_name=f"{template.name}_generated.pdf",  # This would be the generated file name
            file_size=0,  # This would be the size of the generated file
            file_type="application/pdf",  # This would be the type of the generated file
            is_confidential=request_data.is_confidential,
            access_level=request_data.access_level,
            expiry_date=request_data.expiry_date,
            tags=request_data.tags or [],
            metadata=request_data.metadata or {},
        )

        db.add(document)
        await db.commit()
        await db.refresh(document)
        return document


# Create a singleton instance
document_management_service = DocumentManagementService()
