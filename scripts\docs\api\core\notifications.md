# Core - Notifications

**Categoria:** Core
**<PERSON><PERSON><PERSON><PERSON>:** Notifications
**Total de Endpoints:** 33
**<PERSON><PERSON><PERSON> em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [GET /api/modules/core/notifications/](#get-apimodulescorenotifications) - List Notifications
- [POST /api/modules/core/notifications/](#post-apimodulescorenotifications) - Create Notification
- [GET /api/modules/core/notifications/admin/all](#get-apimodulescorenotificationsadminall) - Admin List All Notifications
- [POST /api/modules/core/notifications/bulk-action](#post-apimodulescorenotificationsbulk-action) - Bulk Action
- [POST /api/modules/core/notifications/events/auctions/end](#post-apimodulescorenotificationseventsauctionsend) - Trigger Auction End
- [POST /api/modules/core/notifications/events/auctions/new-bid](#post-apimodulescorenotificationseventsauctionsnew-bid) - Trigger Auction Bid
- [POST /api/modules/core/notifications/events/b2b/approval-request](#post-apimodulescorenotificationseventsb2bapproval-request) - Trigger B2B Approval Request
- [POST /api/modules/core/notifications/events/b2b/approval-status](#post-apimodulescorenotificationseventsb2bapproval-status) - Trigger B2B Approval Status
- [POST /api/modules/core/notifications/events/email/test](#post-apimodulescorenotificationseventsemailtest) - Send Test Email
- [POST /api/modules/core/notifications/events/invoices/generated](#post-apimodulescorenotificationseventsinvoicesgenerated) - Trigger Invoice Generated
- [POST /api/modules/core/notifications/events/orders/new-order](#post-apimodulescorenotificationseventsordersnew-order) - Trigger New Order
- [POST /api/modules/core/notifications/events/orders/status-change](#post-apimodulescorenotificationseventsordersstatus-change) - Trigger Order Status Change
- [POST /api/modules/core/notifications/events/system/maintenance](#post-apimodulescorenotificationseventssystemmaintenance) - Trigger System Maintenance
- [POST /api/modules/core/notifications/mark-read](#post-apimodulescorenotificationsmark-read) - Mark Notifications As Read
- [GET /api/modules/core/notifications/metrics/dashboard-stats](#get-apimodulescorenotificationsmetricsdashboard-stats) - Get Dashboard Stats
- [GET /api/modules/core/notifications/metrics/engagement-report](#get-apimodulescorenotificationsmetricsengagement-report) - Get Engagement Report
- [GET /api/modules/core/notifications/metrics/my-metrics](#get-apimodulescorenotificationsmetricsmy-metrics) - Get My Metrics
- [GET /api/modules/core/notifications/metrics/performance-insights](#get-apimodulescorenotificationsmetricsperformance-insights) - Get Performance Insights
- [POST /api/modules/core/notifications/metrics/record-daily](#post-apimodulescorenotificationsmetricsrecord-daily) - Record Daily Metrics
- [GET /api/modules/core/notifications/metrics/system](#get-apimodulescorenotificationsmetricssystem) - Get System Metrics
- [GET /api/modules/core/notifications/metrics/tenant/{tenant_id}](#get-apimodulescorenotificationsmetricstenanttenant-id) - Get Tenant Metrics
- [GET /api/modules/core/notifications/metrics/user/{user_id}](#get-apimodulescorenotificationsmetricsuseruser-id) - Get User Metrics
- [POST /api/modules/core/notifications/queue/cleanup](#post-apimodulescorenotificationsqueuecleanup) - Cleanup Old Entries
- [GET /api/modules/core/notifications/queue/entries](#get-apimodulescorenotificationsqueueentries) - List Queue Entries
- [GET /api/modules/core/notifications/queue/health](#get-apimodulescorenotificationsqueuehealth) - Get Queue Health
- [POST /api/modules/core/notifications/queue/process-batch](#post-apimodulescorenotificationsqueueprocess-batch) - Process Batch
- [POST /api/modules/core/notifications/queue/retry-failed](#post-apimodulescorenotificationsqueueretry-failed) - Retry Failed Notifications
- [GET /api/modules/core/notifications/queue/stats](#get-apimodulescorenotificationsqueuestats) - Get Queue Stats
- [DELETE /api/modules/core/notifications/{notification_id}](#delete-apimodulescorenotificationsnotification-id) - Delete Notification
- [GET /api/modules/core/notifications/{notification_id}](#get-apimodulescorenotificationsnotification-id) - Get Notification
- [PUT /api/modules/core/notifications/{notification_id}](#put-apimodulescorenotificationsnotification-id) - Update Notification
- [POST /api/modules/core/notifications/{notification_id}/click](#post-apimodulescorenotificationsnotification-idclick) - Register Click
- [GET /api/modules/core/notifications/{notification_id}/preview](#get-apimodulescorenotificationsnotification-idpreview) - Get Delivery Preview

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### NotificationBulkAction

**Descrição:** Schema para ações em lote.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `notification_ids` | Array[string] | ✅ | - |
| `action` | string | ✅ | - |
| `delete_for_all` | boolean | ❌ | - |

### NotificationCreate

**Descrição:** Schema para criação de notificação.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `title` | string | ✅ | - |
| `content` | string | ✅ | - |
| `image_url` | unknown | ❌ | - |
| `action_url` | unknown | ❌ | - |
| `target_type` | NotificationTargetType | ✅ | - |
| `target_id` | unknown | ❌ | - |
| `tenant_id` | unknown | ❌ | - |
| `priority` | NotificationPriority | ❌ | - |
| `auto_expire_hours` | integer | ❌ | - |
| `max_lifetime_days` | integer | ❌ | - |

### NotificationListResponse

**Descrição:** Schema de resposta para lista de notificações.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `notifications` | Array[NotificationResponse] | ✅ | - |
| `total` | integer | ✅ | - |
| `page` | integer | ✅ | - |
| `per_page` | integer | ✅ | - |
| `has_next` | boolean | ✅ | - |
| `has_prev` | boolean | ✅ | - |

### NotificationMarkAsRead

**Descrição:** Schema para marcar notificação como lida.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `notification_ids` | Array[string] | ✅ | - |

### NotificationResponse

**Descrição:** Schema de resposta para notificação.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `title` | string | ✅ | - |
| `content` | string | ✅ | - |
| `image_url` | unknown | ❌ | - |
| `action_url` | unknown | ❌ | - |
| `target_type` | NotificationTargetType | ✅ | - |
| `target_id` | unknown | ❌ | - |
| `tenant_id` | unknown | ❌ | - |
| `priority` | NotificationPriority | ❌ | - |
| `auto_expire_hours` | integer | ❌ | - |
| `max_lifetime_days` | integer | ❌ | - |
| `id` | string | ✅ | - |
| `sender_id` | string | ✅ | - |
| `sender_type` | NotificationSenderType | ✅ | - |
| `status` | NotificationStatus | ✅ | - |
| `view_count` | integer | ✅ | - |
| `click_count` | integer | ✅ | - |
| `delivery_count` | integer | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | unknown | ✅ | - |
| `expires_at` | unknown | ✅ | - |
| `sent_at` | unknown | ✅ | - |
| `is_expired` | boolean | ✅ | - |
| `is_read` | unknown | ❌ | - |
| `is_deleted` | unknown | ❌ | - |

### NotificationSystemMetrics

**Descrição:** Schema para métricas do sistema (admin).

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `total_notifications` | integer | ✅ | - |
| `active_notifications` | integer | ✅ | - |
| `expired_notifications` | integer | ✅ | - |
| `queue_size` | integer | ✅ | - |
| `failed_notifications` | integer | ✅ | - |
| `average_delivery_time` | number | ✅ | - |
| `system_load` | number | ✅ | - |
| `error_rate` | number | ✅ | - |

### NotificationTenantMetrics

**Descrição:** Schema para métricas do tenant.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `total_sent` | integer | ✅ | - |
| `total_delivered` | integer | ✅ | - |
| `total_read` | integer | ✅ | - |
| `total_clicked` | integer | ✅ | - |
| `delivery_rate` | number | ✅ | - |
| `open_rate` | number | ✅ | - |
| `click_through_rate` | number | ✅ | - |
| `engagement_score` | number | ✅ | - |

### NotificationUpdate

**Descrição:** Schema para atualização de notificação.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `title` | unknown | ❌ | - |
| `content` | unknown | ❌ | - |
| `image_url` | unknown | ❌ | - |
| `action_url` | unknown | ❌ | - |
| `priority` | unknown | ❌ | - |
| `auto_expire_hours` | unknown | ❌ | - |
| `status` | unknown | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/modules/core/notifications/ {#get-apimodulescorenotifications}

**Resumo:** List Notifications
**Descrição:** Lista notificações do usuário com filtros e paginação.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `page` | integer | query | ❌ | - |
| `per_page` | integer | query | ❌ | - |
| `status` | string | query | ❌ | - |
| `priority` | string | query | ❌ | - |
| `is_read` | string | query | ❌ | - |
| `search` | string | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [NotificationListResponse](#notificationlistresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/notifications/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/notifications/ {#post-apimodulescorenotifications}

**Resumo:** Create Notification
**Descrição:** Cria uma nova notificação.

Requer autenticação. Admins podem criar qualquer tipo de notificação.
Tenant owners podem criar notificações para seus tenants.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [NotificationCreate](#notificationcreate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [NotificationResponse](#notificationresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/notifications/admin/all {#get-apimodulescorenotificationsadminall}

**Resumo:** Admin List All Notifications
**Descrição:** Lista todas as notificações do sistema (apenas admin).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `page` | integer | query | ❌ | - |
| `per_page` | integer | query | ❌ | - |
| `status` | string | query | ❌ | - |
| `sender_type` | string | query | ❌ | - |
| `target_type` | string | query | ❌ | - |
| `tenant_id` | string | query | ❌ | - |
| `search` | string | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [NotificationListResponse](#notificationlistresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/notifications/admin/all" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/notifications/bulk-action {#post-apimodulescorenotificationsbulk-action}

**Resumo:** Bulk Action
**Descrição:** Executa ação em lote nas notificações.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [NotificationBulkAction](#notificationbulkaction)

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/bulk-action" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/core/notifications/events/auctions/end {#post-apimodulescorenotificationseventsauctionsend}

**Resumo:** Trigger Auction End
**Descrição:** Dispara notificação de fim de leilão.

Args:
    auction_id: ID do leilão
    winner_id: ID do vencedor
    winning_bid: Lance vencedor

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `auction_id` | string | query | ✅ | - |
| `winner_id` | string | query | ❌ | - |
| `winning_bid` | string | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/events/auctions/end" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/notifications/events/auctions/new-bid {#post-apimodulescorenotificationseventsauctionsnew-bid}

**Resumo:** Trigger Auction Bid
**Descrição:** Dispara notificação de novo lance em leilão.

Args:
    auction_id: ID do leilão
    bidder_id: ID do licitante
    bid_amount: Valor do lance
    previous_bidder_id: ID do licitante anterior

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `auction_id` | string | query | ✅ | - |
| `bidder_id` | string | query | ✅ | - |
| `bid_amount` | number | query | ✅ | - |
| `previous_bidder_id` | string | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/events/auctions/new-bid" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/notifications/events/b2b/approval-request {#post-apimodulescorenotificationseventsb2bapproval-request}

**Resumo:** Trigger B2B Approval Request
**Descrição:** Dispara notificação de solicitação de aprovação B2B.

Args:
    user_id: ID do usuário solicitante
    user_type: Tipo de aprovação (tcustomer/tvendor_supplier)
    tenant_id: ID do tenant (opcional)

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `user_id` | string | query | ✅ | - |
| `user_type` | string | query | ✅ | - |
| `tenant_id` | string | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/events/b2b/approval-request" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/notifications/events/b2b/approval-status {#post-apimodulescorenotificationseventsb2bapproval-status}

**Resumo:** Trigger B2B Approval Status
**Descrição:** Dispara notificação de mudança de status de aprovação B2B.

Args:
    user_id: ID do usuário
    user_type: Tipo de aprovação
    approval_status: Status (approved/rejected)
    reason: Motivo (se rejeitado)

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `user_id` | string | query | ✅ | - |
| `user_type` | string | query | ✅ | - |
| `approval_status` | string | query | ✅ | - |
| `reason` | string | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/events/b2b/approval-status" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/notifications/events/email/test {#post-apimodulescorenotificationseventsemailtest}

**Resumo:** Send Test Email
**Descrição:** Envia email de teste.

Args:
    to_email: Email de destino

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `to_email` | string | query | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/events/email/test" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/notifications/events/invoices/generated {#post-apimodulescorenotificationseventsinvoicesgenerated}

**Resumo:** Trigger Invoice Generated
**Descrição:** Dispara notificação de fatura gerada.

Args:
    invoice_id: ID da fatura
    customer_id: ID do cliente
    tenant_id: ID do tenant
    invoice_total: Valor da fatura

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `invoice_id` | string | query | ✅ | - |
| `customer_id` | string | query | ✅ | - |
| `tenant_id` | string | query | ✅ | - |
| `invoice_total` | number | query | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/events/invoices/generated" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/notifications/events/orders/new-order {#post-apimodulescorenotificationseventsordersnew-order}

**Resumo:** Trigger New Order
**Descrição:** Dispara notificação de novo pedido.

Args:
    order_id: ID do pedido
    customer_id: ID do cliente
    tenant_id: ID do tenant
    order_total: Valor total do pedido

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `order_id` | string | query | ✅ | - |
| `customer_id` | string | query | ✅ | - |
| `tenant_id` | string | query | ✅ | - |
| `order_total` | number | query | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/events/orders/new-order" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/notifications/events/orders/status-change {#post-apimodulescorenotificationseventsordersstatus-change}

**Resumo:** Trigger Order Status Change
**Descrição:** Dispara notificação de mudança de status de pedido.

Args:
    order_id: ID do pedido
    customer_id: ID do cliente
    old_status: Status anterior
    new_status: Novo status
    tenant_id: ID do tenant

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `order_id` | string | query | ✅ | - |
| `customer_id` | string | query | ✅ | - |
| `old_status` | string | query | ✅ | - |
| `new_status` | string | query | ✅ | - |
| `tenant_id` | string | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/events/orders/status-change" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/notifications/events/system/maintenance {#post-apimodulescorenotificationseventssystemmaintenance}

**Resumo:** Trigger System Maintenance
**Descrição:** Dispara notificação de manutenção do sistema.

Args:
    title: Título da manutenção
    message: Mensagem detalhada
    start_time: Horário de início
    end_time: Horário de fim

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `title` | string | query | ✅ | - |
| `message` | string | query | ✅ | - |
| `start_time` | string | query | ✅ | - |
| `end_time` | string | query | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/events/system/maintenance" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/notifications/mark-read {#post-apimodulescorenotificationsmark-read}

**Resumo:** Mark Notifications As Read
**Descrição:** Marca notificações como lidas.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [NotificationMarkAsRead](#notificationmarkasread)

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/mark-read" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/notifications/metrics/dashboard-stats {#get-apimodulescorenotificationsmetricsdashboard-stats}

**Resumo:** Get Dashboard Stats
**Descrição:** Obtém estatísticas resumidas para o dashboard.

**🔐 Autenticação:** Requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/notifications/metrics/dashboard-stats" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/notifications/metrics/engagement-report {#get-apimodulescorenotificationsmetricsengagement-report}

**Resumo:** Get Engagement Report
**Descrição:** Obtém relatório de engajamento detalhado.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_id` | string | query | ❌ | - |
| `period_days` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/notifications/metrics/engagement-report" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/notifications/metrics/my-metrics {#get-apimodulescorenotificationsmetricsmy-metrics}

**Resumo:** Get My Metrics
**Descrição:** Obtém métricas do usuário atual.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `period_days` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/notifications/metrics/my-metrics" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/notifications/metrics/performance-insights {#get-apimodulescorenotificationsmetricsperformance-insights}

**Resumo:** Get Performance Insights
**Descrição:** Obtém insights de performance das notificações.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_id` | string | query | ❌ | - |
| `period_days` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/notifications/metrics/performance-insights" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/notifications/metrics/record-daily {#post-apimodulescorenotificationsmetricsrecord-daily}

**Resumo:** Record Daily Metrics
**Descrição:** Força o registro de métricas diárias.

Normalmente executado automaticamente via cron.

**🔐 Autenticação:** Requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/metrics/record-daily" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/notifications/metrics/system {#get-apimodulescorenotificationsmetricssystem}

**Resumo:** Get System Metrics
**Descrição:** Obtém métricas gerais do sistema de notificações.

Apenas para administradores.

**🔐 Autenticação:** Requerida

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [NotificationSystemMetrics](#notificationsystemmetrics)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/notifications/metrics/system" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/notifications/metrics/tenant/{tenant_id} {#get-apimodulescorenotificationsmetricstenanttenant-id}

**Resumo:** Get Tenant Metrics
**Descrição:** Obtém métricas de um tenant específico.

Admins podem ver qualquer tenant.
Tenant owners podem ver apenas seus próprios tenants.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `tenant_id` | string | path | ✅ | - |
| `period_days` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [NotificationTenantMetrics](#notificationtenantmetrics)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/notifications/metrics/tenant/{tenant_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/notifications/metrics/user/{user_id} {#get-apimodulescorenotificationsmetricsuseruser-id}

**Resumo:** Get User Metrics
**Descrição:** Obtém métricas de um usuário específico.

Usuários podem ver apenas suas próprias métricas.
Admins podem ver qualquer usuário.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `user_id` | string | path | ✅ | - |
| `period_days` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/notifications/metrics/user/{user_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/notifications/queue/cleanup {#post-apimodulescorenotificationsqueuecleanup}

**Resumo:** Cleanup Old Entries
**Descrição:** Remove entradas antigas da fila.

Apenas para administradores.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `days_old` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/queue/cleanup" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/notifications/queue/entries {#get-apimodulescorenotificationsqueueentries}

**Resumo:** List Queue Entries
**Descrição:** Lista entradas da fila de notificações.

Apenas para administradores.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `status` | string | query | ❌ | - |
| `limit` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/notifications/queue/entries" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/notifications/queue/health {#get-apimodulescorenotificationsqueuehealth}

**Resumo:** Get Queue Health
**Descrição:** Verifica a saúde da fila de notificações.

Apenas para administradores.

**🔐 Autenticação:** Requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/notifications/queue/health" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/notifications/queue/process-batch {#post-apimodulescorenotificationsqueueprocess-batch}

**Resumo:** Process Batch
**Descrição:** Processa um lote de notificações da fila.

Apenas para administradores.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `batch_size` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/queue/process-batch" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/notifications/queue/retry-failed {#post-apimodulescorenotificationsqueueretry-failed}

**Resumo:** Retry Failed Notifications
**Descrição:** Recoloca notificações falhadas na fila para retry.

Apenas para administradores.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `max_age_hours` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/queue/retry-failed" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/notifications/queue/stats {#get-apimodulescorenotificationsqueuestats}

**Resumo:** Get Queue Stats
**Descrição:** Obtém estatísticas da fila de notificações.

Apenas para administradores.

**🔐 Autenticação:** Requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/notifications/queue/stats" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### DELETE /api/modules/core/notifications/{notification_id} {#delete-apimodulescorenotificationsnotification-id}

**Resumo:** Delete Notification
**Descrição:** Deleta uma notificação.

Se delete_for_all=True, deleta para todos os usuários (apenas admin/owner).
Caso contrário, deleta apenas para o usuário atual.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `notification_id` | string | path | ✅ | - |
| `delete_for_all` | boolean | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/core/notifications/{notification_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/notifications/{notification_id} {#get-apimodulescorenotificationsnotification-id}

**Resumo:** Get Notification
**Descrição:** Obtém uma notificação específica.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `notification_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [NotificationResponse](#notificationresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/notifications/{notification_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/core/notifications/{notification_id} {#put-apimodulescorenotificationsnotification-id}

**Resumo:** Update Notification
**Descrição:** Atualiza uma notificação.

Apenas o remetente ou admin podem editar.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `notification_id` | string | path | ✅ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [NotificationUpdate](#notificationupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [NotificationResponse](#notificationresponse)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/core/notifications/{notification_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/core/notifications/{notification_id}/click {#post-apimodulescorenotificationsnotification-idclick}

**Resumo:** Register Click
**Descrição:** Registra um clique na notificação.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `notification_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/notifications/{notification_id}/click" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/core/notifications/{notification_id}/preview {#get-apimodulescorenotificationsnotification-idpreview}

**Resumo:** Get Delivery Preview
**Descrição:** Obtém preview da entrega da notificação.

Mostra quantos usuários receberão a notificação.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `notification_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/notifications/{notification_id}/preview" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
