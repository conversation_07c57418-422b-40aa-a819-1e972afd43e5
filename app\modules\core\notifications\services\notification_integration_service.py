"""
Notification Integration Service

Serviço para integração de notificações com todos os módulos do sistema.
"""

import logging
from typing import Dict, List, Optional
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession

from app.modules.core.users.models.user import User

from .system_notification_service import SystemNotificationService
from .email_notification_service import EmailNotificationService
from .notification_delivery_service import NotificationDeliveryService
from ..models import Notification, NotificationPriority

logger = logging.getLogger(__name__)


class NotificationIntegrationService:
    """Serviço principal para integração de notificações com o sistema."""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.system_service = SystemNotificationService(db)
        self.email_service = EmailNotificationService(db)
        self.delivery_service = NotificationDeliveryService(db)

    # ===== B2B INTEGRATION =====
    
    async def handle_b2b_approval_request(
        self,
        user_id: UUID,
        user_type: str,
        tenant_id: Optional[UUID] = None
    ) -> Dict[str, any]:
        """
        Processa solicitação de aprovação B2B.
        
        Args:
            user_id: ID do usuário
            user_type: Tipo de aprovação (tcustomer/tvendor_supplier)
            tenant_id: ID do tenant
            
        Returns:
            Resultado do processamento
        """
        try:
            # Cria notificação no sistema
            notification = await self.system_service.notify_b2b_approval_request(
                user_id, user_type, tenant_id
            )

            # Busca destinatários (owners/admins)
            recipients = await self.delivery_service.get_target_users(notification)

            # Envia emails para notificações críticas
            email_stats = await self.email_service.send_bulk_notification_emails(
                notification, recipients, "b2b_approval_request"
            )

            logger.info(f"B2B approval request processada: {user_id} - {user_type}")
            
            return {
                "notification_id": str(notification.id),
                "recipients_count": len(recipients),
                "email_stats": email_stats,
                "status": "success"
            }

        except Exception as e:
            logger.error(f"Erro ao processar B2B approval request: {e}")
            return {"status": "error", "message": str(e)}

    async def handle_b2b_approval_status(
        self,
        user_id: UUID,
        user_type: str,
        status: str,
        reason: Optional[str] = None
    ) -> Dict[str, any]:
        """
        Processa mudança de status de aprovação B2B.
        
        Args:
            user_id: ID do usuário
            user_type: Tipo de aprovação
            status: Status (approved/rejected)
            reason: Motivo (se rejeitado)
            
        Returns:
            Resultado do processamento
        """
        try:
            # Cria notificação no sistema
            notification = await self.system_service.notify_b2b_approval_status(
                user_id, user_type, status, reason
            )

            # Busca usuário destinatário
            recipients = await self.delivery_service.get_target_users(notification)

            # Sempre envia email para mudanças de status B2B
            email_stats = await self.email_service.send_bulk_notification_emails(
                notification, recipients, "b2b_approval_status"
            )

            logger.info(f"B2B approval status processada: {user_id} - {status}")
            
            return {
                "notification_id": str(notification.id),
                "recipients_count": len(recipients),
                "email_stats": email_stats,
                "status": "success"
            }

        except Exception as e:
            logger.error(f"Erro ao processar B2B approval status: {e}")
            return {"status": "error", "message": str(e)}

    # ===== ESHOP INTEGRATION =====
    
    async def handle_order_status_change(
        self,
        order_id: UUID,
        customer_id: UUID,
        old_status: str,
        new_status: str,
        tenant_id: Optional[UUID] = None
    ) -> Dict[str, any]:
        """
        Processa mudança de status de pedido.
        
        Args:
            order_id: ID do pedido
            customer_id: ID do cliente
            old_status: Status anterior
            new_status: Novo status
            tenant_id: ID do tenant
            
        Returns:
            Resultado do processamento
        """
        try:
            # Cria notificação no sistema
            notification = await self.system_service.notify_order_status_change(
                order_id, customer_id, old_status, new_status, tenant_id
            )

            # Busca destinatário
            recipients = await self.delivery_service.get_target_users(notification)

            # Envia email para status importantes
            email_stats = {}
            if new_status in ["delivered", "cancelled", "shipped"]:
                email_stats = await self.email_service.send_bulk_notification_emails(
                    notification, recipients, "order_status_change"
                )

            logger.info(f"Order status change processada: {order_id} - {new_status}")
            
            return {
                "notification_id": str(notification.id),
                "recipients_count": len(recipients),
                "email_stats": email_stats,
                "status": "success"
            }

        except Exception as e:
            logger.error(f"Erro ao processar order status change: {e}")
            return {"status": "error", "message": str(e)}

    async def handle_new_order(
        self,
        order_id: UUID,
        customer_id: UUID,
        tenant_id: UUID,
        order_total: float
    ) -> Dict[str, any]:
        """
        Processa novo pedido.
        
        Args:
            order_id: ID do pedido
            customer_id: ID do cliente
            tenant_id: ID do tenant
            order_total: Valor total do pedido
            
        Returns:
            Resultado do processamento
        """
        try:
            # Notifica tenant owners sobre novo pedido
            from ..models import NotificationTargetType
            
            notification = await self.system_service._create_system_notification(
                title="Novo Pedido Recebido",
                content=f"Novo pedido #{str(order_id)[:8]} no valor de R$ {order_total:.2f}",
                target_type=NotificationTargetType.TENANT_OWNERS,
                tenant_id=tenant_id,
                priority=NotificationPriority.NORMAL,
                action_url=f"/admin/orders/{order_id}",
                metadata={
                    "event_type": "new_order",
                    "order_id": str(order_id),
                    "customer_id": str(customer_id),
                    "order_total": order_total
                }
            )

            # Busca destinatários
            recipients = await self.delivery_service.get_target_users(notification)

            logger.info(f"New order processado: {order_id}")
            
            return {
                "notification_id": str(notification.id),
                "recipients_count": len(recipients),
                "status": "success"
            }

        except Exception as e:
            logger.error(f"Erro ao processar new order: {e}")
            return {"status": "error", "message": str(e)}

    # ===== AUCTION/LOTTERY INTEGRATION =====
    
    async def handle_auction_bid(
        self,
        auction_id: UUID,
        bidder_id: UUID,
        bid_amount: float,
        previous_bidder_id: Optional[UUID] = None
    ) -> Dict[str, any]:
        """
        Processa novo lance em leilão.
        
        Args:
            auction_id: ID do leilão
            bidder_id: ID do licitante
            bid_amount: Valor do lance
            previous_bidder_id: ID do licitante anterior
            
        Returns:
            Resultado do processamento
        """
        try:
            # Cria notificações no sistema
            notifications = await self.system_service.notify_auction_bid(
                auction_id, bidder_id, bid_amount, previous_bidder_id
            )

            total_recipients = 0
            email_stats = {"sent": 0, "failed": 0, "skipped": 0}

            # Processa cada notificação
            for notification in notifications:
                recipients = await self.delivery_service.get_target_users(notification)
                total_recipients += len(recipients)

                # Envia email para licitante anterior
                if notification.target_id == previous_bidder_id:
                    stats = await self.email_service.send_bulk_notification_emails(
                        notification, recipients, "auction_outbid"
                    )
                    for key in email_stats:
                        email_stats[key] += stats.get(key, 0)

            logger.info(f"Auction bid processado: {auction_id} - R$ {bid_amount}")
            
            return {
                "notifications_count": len(notifications),
                "recipients_count": total_recipients,
                "email_stats": email_stats,
                "status": "success"
            }

        except Exception as e:
            logger.error(f"Erro ao processar auction bid: {e}")
            return {"status": "error", "message": str(e)}

    async def handle_auction_end(
        self,
        auction_id: UUID,
        winner_id: Optional[UUID] = None,
        winning_bid: Optional[float] = None
    ) -> Dict[str, any]:
        """
        Processa fim de leilão.
        
        Args:
            auction_id: ID do leilão
            winner_id: ID do vencedor
            winning_bid: Lance vencedor
            
        Returns:
            Resultado do processamento
        """
        try:
            # Cria notificações no sistema
            notifications = await self.system_service.notify_auction_end(
                auction_id, winner_id, winning_bid
            )

            total_recipients = 0
            email_stats = {"sent": 0, "failed": 0, "skipped": 0}

            # Processa cada notificação
            for notification in notifications:
                recipients = await self.delivery_service.get_target_users(notification)
                total_recipients += len(recipients)

                # Sempre envia email para vencedor
                if notification.target_id == winner_id:
                    stats = await self.email_service.send_bulk_notification_emails(
                        notification, recipients, "auction_won"
                    )
                    for key in email_stats:
                        email_stats[key] += stats.get(key, 0)

            logger.info(f"Auction end processado: {auction_id}")
            
            return {
                "notifications_count": len(notifications),
                "recipients_count": total_recipients,
                "email_stats": email_stats,
                "status": "success"
            }

        except Exception as e:
            logger.error(f"Erro ao processar auction end: {e}")
            return {"status": "error", "message": str(e)}

    # ===== INVOICE INTEGRATION =====
    
    async def handle_invoice_generated(
        self,
        invoice_id: UUID,
        customer_id: UUID,
        tenant_id: UUID,
        invoice_total: float
    ) -> Dict[str, any]:
        """
        Processa geração de fatura.
        
        Args:
            invoice_id: ID da fatura
            customer_id: ID do cliente
            tenant_id: ID do tenant
            invoice_total: Valor da fatura
            
        Returns:
            Resultado do processamento
        """
        try:
            from ..models import NotificationTargetType
            
            # Notifica cliente sobre nova fatura
            notification = await self.system_service._create_system_notification(
                title="Nova Fatura Disponível",
                content=f"Uma nova fatura no valor de R$ {invoice_total:.2f} foi gerada para sua conta.",
                target_type=NotificationTargetType.SPECIFIC_USER,
                target_id=customer_id,
                tenant_id=tenant_id,
                priority=NotificationPriority.HIGH,
                action_url=f"/invoices/{invoice_id}",
                metadata={
                    "event_type": "invoice_generated",
                    "invoice_id": str(invoice_id),
                    "customer_id": str(customer_id),
                    "invoice_total": invoice_total
                }
            )

            # Busca destinatário
            recipients = await self.delivery_service.get_target_users(notification)

            # Sempre envia email para faturas
            email_stats = await self.email_service.send_bulk_notification_emails(
                notification, recipients, "invoice_generated"
            )

            logger.info(f"Invoice generated processada: {invoice_id}")
            
            return {
                "notification_id": str(notification.id),
                "recipients_count": len(recipients),
                "email_stats": email_stats,
                "status": "success"
            }

        except Exception as e:
            logger.error(f"Erro ao processar invoice generated: {e}")
            return {"status": "error", "message": str(e)}

    # ===== SYSTEM EVENTS =====
    
    async def handle_system_maintenance(
        self,
        title: str,
        message: str,
        start_time: str,
        end_time: str
    ) -> Dict[str, any]:
        """
        Processa notificação de manutenção do sistema.
        
        Args:
            title: Título da manutenção
            message: Mensagem detalhada
            start_time: Horário de início
            end_time: Horário de fim
            
        Returns:
            Resultado do processamento
        """
        try:
            from ..models import NotificationTargetType
            
            notification = await self.system_service._create_system_notification(
                title=title,
                content=f"{message}\n\nInício: {start_time}\nFim: {end_time}",
                target_type=NotificationTargetType.ALL_USERS,
                priority=NotificationPriority.HIGH,
                metadata={
                    "event_type": "system_maintenance",
                    "start_time": start_time,
                    "end_time": end_time
                }
            )

            # Busca todos os usuários
            recipients = await self.delivery_service.get_target_users(notification)

            # Envia email para todos sobre manutenção
            email_stats = await self.email_service.send_bulk_notification_emails(
                notification, recipients, "system_maintenance"
            )

            logger.info(f"System maintenance processada: {title}")
            
            return {
                "notification_id": str(notification.id),
                "recipients_count": len(recipients),
                "email_stats": email_stats,
                "status": "success"
            }

        except Exception as e:
            logger.error(f"Erro ao processar system maintenance: {e}")
            return {"status": "error", "message": str(e)}
