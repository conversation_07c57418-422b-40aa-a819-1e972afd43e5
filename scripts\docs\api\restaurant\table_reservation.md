# Restaurant - Table Reservation

**Categoria:** Restaurant
**Módulo:** Table Reservation
**Total de Endpoints:** 13
**Gerado em:** 27/06/2025, 20:20:27

## 📋 Endpoints

- [GET /api/modules/restaurants/reservations/blacklist/](#get-apimodulesrestaurantsreservationsblacklist) - Get all blacklist entries
- [POST /api/modules/restaurants/reservations/blacklist/](#post-apimodulesrestaurantsreservationsblacklist) - Create a new blacklist entry
- [GET /api/modules/restaurants/reservations/blacklist/check](#get-apimodulesrestaurantsreservationsblacklistcheck) - Check if a customer is blacklisted
- [DELETE /api/modules/restaurants/reservations/blacklist/{blacklist_id}](#delete-apimodulesrestaurantsreservationsblacklistblacklist-id) - Delete a blacklist entry
- [GET /api/modules/restaurants/reservations/blacklist/{blacklist_id}](#get-apimodulesrestaurantsreservationsblacklistblacklist-id) - Get a blacklist entry by ID
- [PUT /api/modules/restaurants/reservations/blacklist/{blacklist_id}](#put-apimodulesrestaurantsreservationsblacklistblacklist-id) - Update a blacklist entry
- [GET /api/modules/restaurants/reservations/reservations/](#get-apimodulesrestaurantsreservationsreservations) - Get all reservations
- [POST /api/modules/restaurants/reservations/reservations/](#post-apimodulesrestaurantsreservationsreservations) - Create a new reservation
- [GET /api/modules/restaurants/reservations/reservations/available-tables](#get-apimodulesrestaurantsreservationsreservationsavailable-tables) - Find available tables for a reservation
- [DELETE /api/modules/restaurants/reservations/reservations/{reservation_id}](#delete-apimodulesrestaurantsreservationsreservationsreservation-id) - Delete a reservation
- [GET /api/modules/restaurants/reservations/reservations/{reservation_id}](#get-apimodulesrestaurantsreservationsreservationsreservation-id) - Get a reservation by ID
- [PUT /api/modules/restaurants/reservations/reservations/{reservation_id}](#put-apimodulesrestaurantsreservationsreservationsreservation-id) - Update a reservation
- [PATCH /api/modules/restaurants/reservations/reservations/{reservation_id}/status](#patch-apimodulesrestaurantsreservationsreservationsreservation-idstatus) - Update reservation status

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### CustomerBlacklistCreate

**Descrição:** Schema for creating a new customer blacklist entry.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `blacklist_type` | BlacklistType | ❌ | Type of blacklist entry |
| `reason` | unknown | ❌ | Reason for blacklisting |
| `suspension_start_date` | unknown | ❌ | Start date of suspension |
| `suspension_end_date` | unknown | ❌ | End date of suspension |
| `require_deposit` | boolean | ❌ | Whether to require a deposit for future reservations |
| `deposit_amount` | unknown | ❌ | Amount of the deposit to require |
| `customer_id` | unknown | ❌ | ID of the customer to blacklist |
| `guest_name` | unknown | ❌ | Name of the guest (for non-registered customers) |
| `guest_email` | unknown | ❌ | Email of the guest (for non-registered customers) |
| `guest_phone` | unknown | ❌ | Phone number of the guest (for non-registered customers) |

### CustomerBlacklistRead

**Descrição:** Schema for reading a customer blacklist entry.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `blacklist_type` | BlacklistType | ❌ | Type of blacklist entry |
| `reason` | unknown | ❌ | Reason for blacklisting |
| `suspension_start_date` | unknown | ❌ | Start date of suspension |
| `suspension_end_date` | unknown | ❌ | End date of suspension |
| `require_deposit` | boolean | ❌ | Whether to require a deposit for future reservations |
| `deposit_amount` | unknown | ❌ | Amount of the deposit to require |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `customer_id` | unknown | ❌ | - |
| `guest_name` | unknown | ❌ | - |
| `guest_email` | unknown | ❌ | - |
| `guest_phone` | unknown | ❌ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `customer` | unknown | ❌ | - |
| `is_active` | boolean | ✅ | - |

### CustomerBlacklistUpdate

**Descrição:** Schema for updating an existing customer blacklist entry.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `blacklist_type` | unknown | ❌ | Type of blacklist entry |
| `reason` | unknown | ❌ | Reason for blacklisting |
| `suspension_start_date` | unknown | ❌ | Start date of suspension |
| `suspension_end_date` | unknown | ❌ | End date of suspension |
| `require_deposit` | unknown | ❌ | Whether to require a deposit for future reservations |
| `deposit_amount` | unknown | ❌ | Amount of the deposit to require |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### ReservationCreate

**Descrição:** Schema for creating a new reservation.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `party_size` | integer | ✅ | Number of people in the party |
| `reservation_date` | string | ✅ | Date and time of the reservation |
| `duration_minutes` | unknown | ❌ | Duration of the reservation in minutes |
| `special_requests` | unknown | ❌ | Special requests for the reservation |
| `occasion` | unknown | ❌ | Occasion for the reservation |
| `notes` | unknown | ❌ | Additional notes about the reservation |
| `table_id` | unknown | ❌ | ID of the table for the reservation |
| `customer_id` | unknown | ❌ | ID of the customer making the reservation |
| `guest_name` | unknown | ❌ | Name of the guest (for non-registered customers) |
| `guest_email` | unknown | ❌ | Email of the guest (for non-registered customers) |
| `guest_phone` | unknown | ❌ | Phone number of the guest (for non-registered customers) |
| `deposit_required` | unknown | ❌ | Whether a deposit is required |
| `deposit_amount` | unknown | ❌ | Amount of the deposit |

### ReservationRead

**Descrição:** Schema for reading a reservation.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `party_size` | integer | ✅ | Number of people in the party |
| `reservation_date` | string | ✅ | Date and time of the reservation |
| `duration_minutes` | unknown | ❌ | Duration of the reservation in minutes |
| `special_requests` | unknown | ❌ | Special requests for the reservation |
| `occasion` | unknown | ❌ | Occasion for the reservation |
| `notes` | unknown | ❌ | Additional notes about the reservation |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `table_id` | unknown | ❌ | - |
| `customer_id` | unknown | ❌ | - |
| `reservation_number` | string | ✅ | - |
| `status` | ReservationStatus | ✅ | - |
| `guest_name` | unknown | ❌ | - |
| `guest_email` | unknown | ❌ | - |
| `guest_phone` | unknown | ❌ | - |
| `deposit_required` | boolean | ✅ | - |
| `deposit_amount` | unknown | ❌ | - |
| `deposit_paid` | boolean | ✅ | - |
| `payment_id` | unknown | ❌ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `table` | unknown | ❌ | - |
| `customer` | unknown | ❌ | - |

### ReservationUpdate

**Descrição:** Schema for updating an existing reservation.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `table_id` | unknown | ❌ | ID of the table for the reservation |
| `party_size` | unknown | ❌ | Number of people in the party |
| `reservation_date` | unknown | ❌ | Date and time of the reservation |
| `duration_minutes` | unknown | ❌ | Duration of the reservation in minutes |
| `status` | unknown | ❌ | Status of the reservation |
| `special_requests` | unknown | ❌ | Special requests for the reservation |
| `occasion` | unknown | ❌ | Occasion for the reservation |
| `notes` | unknown | ❌ | Additional notes about the reservation |
| `deposit_required` | unknown | ❌ | Whether a deposit is required |
| `deposit_amount` | unknown | ❌ | Amount of the deposit |
| `deposit_paid` | unknown | ❌ | Whether the deposit has been paid |
| `payment_id` | unknown | ❌ | Reference to payment |

## 🔗 Endpoints Detalhados

### GET /api/modules/restaurants/reservations/blacklist/ {#get-apimodulesrestaurantsreservationsblacklist}

**Resumo:** Get all blacklist entries
**Descrição:** Get all blacklist entries for the current tenant with optional filters.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `customer_id` | string | query | ❌ | Filter by customer ID |
| `blacklist_type` | string | query | ❌ | Filter by blacklist type |
| `is_active` | string | query | ❌ | Filter by active status |
| `skip` | integer | query | ❌ | Number of entries to skip |
| `limit` | integer | query | ❌ | Maximum number of entries to return |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/reservations/blacklist/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/restaurants/reservations/blacklist/ {#post-apimodulesrestaurantsreservationsblacklist}

**Resumo:** Create a new blacklist entry
**Descrição:** Create a new blacklist entry for the current tenant.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [CustomerBlacklistCreate](#customerblacklistcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CustomerBlacklistRead](#customerblacklistread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/restaurants/reservations/blacklist/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/restaurants/reservations/blacklist/check {#get-apimodulesrestaurantsreservationsblacklistcheck}

**Resumo:** Check if a customer is blacklisted
**Descrição:** Check if a customer is blacklisted for the current tenant.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `customer_id` | string | query | ❌ | Customer ID to check |
| `guest_email` | string | query | ❌ | Guest email to check |
| `guest_phone` | string | query | ❌ | Guest phone to check |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/reservations/blacklist/check" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### DELETE /api/modules/restaurants/reservations/blacklist/{blacklist_id} {#delete-apimodulesrestaurantsreservationsblacklistblacklist-id}

**Resumo:** Delete a blacklist entry
**Descrição:** Delete a blacklist entry for the current tenant.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `blacklist_id` | string | path | ✅ | The ID of the blacklist entry to delete |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/restaurants/reservations/blacklist/{blacklist_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/reservations/blacklist/{blacklist_id} {#get-apimodulesrestaurantsreservationsblacklistblacklist-id}

**Resumo:** Get a blacklist entry by ID
**Descrição:** Get a blacklist entry by ID for the current tenant.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `blacklist_id` | string | path | ✅ | The ID of the blacklist entry to get |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CustomerBlacklistRead](#customerblacklistread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/reservations/blacklist/{blacklist_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/restaurants/reservations/blacklist/{blacklist_id} {#put-apimodulesrestaurantsreservationsblacklistblacklist-id}

**Resumo:** Update a blacklist entry
**Descrição:** Update a blacklist entry for the current tenant.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `blacklist_id` | string | path | ✅ | The ID of the blacklist entry to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [CustomerBlacklistUpdate](#customerblacklistupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CustomerBlacklistRead](#customerblacklistread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/reservations/blacklist/{blacklist_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/restaurants/reservations/reservations/ {#get-apimodulesrestaurantsreservationsreservations}

**Resumo:** Get all reservations
**Descrição:** Get all reservations for the current tenant with optional filters.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `table_id` | string | query | ❌ | Filter by table ID |
| `customer_id` | string | query | ❌ | Filter by customer ID |
| `status` | string | query | ❌ | Filter by reservation status |
| `date_from` | string | query | ❌ | Filter by reservation date (from) |
| `date_to` | string | query | ❌ | Filter by reservation date (to) |
| `skip` | integer | query | ❌ | Number of reservations to skip |
| `limit` | integer | query | ❌ | Maximum number of reservations to return |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/reservations/reservations/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/restaurants/reservations/reservations/ {#post-apimodulesrestaurantsreservationsreservations}

**Resumo:** Create a new reservation
**Descrição:** Create a new reservation for the current tenant.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ReservationCreate](#reservationcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ReservationRead](#reservationread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/restaurants/reservations/reservations/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/restaurants/reservations/reservations/available-tables {#get-apimodulesrestaurantsreservationsreservationsavailable-tables}

**Resumo:** Find available tables for a reservation
**Descrição:** Find available tables for a reservation with the specified parameters.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `reservation_date` | string | query | ✅ | Date and time of the reservation |
| `duration_minutes` | integer | query | ❌ | Duration of the reservation in minutes |
| `party_size` | integer | query | ✅ | Number of people in the party |
| `layout_id` | string | query | ❌ | Filter by layout ID |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/reservations/reservations/available-tables" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### DELETE /api/modules/restaurants/reservations/reservations/{reservation_id} {#delete-apimodulesrestaurantsreservationsreservationsreservation-id}

**Resumo:** Delete a reservation
**Descrição:** Delete a reservation for the current tenant.
Requires OWNER or MANAGER tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `reservation_id` | string | path | ✅ | The ID of the reservation to delete |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/restaurants/reservations/reservations/{reservation_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/restaurants/reservations/reservations/{reservation_id} {#get-apimodulesrestaurantsreservationsreservationsreservation-id}

**Resumo:** Get a reservation by ID
**Descrição:** Get a reservation by ID for the current tenant.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `reservation_id` | string | path | ✅ | The ID of the reservation to get |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ReservationRead](#reservationread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/restaurants/reservations/reservations/{reservation_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/restaurants/reservations/reservations/{reservation_id} {#put-apimodulesrestaurantsreservationsreservationsreservation-id}

**Resumo:** Update a reservation
**Descrição:** Update a reservation for the current tenant.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `reservation_id` | string | path | ✅ | The ID of the reservation to update |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ReservationUpdate](#reservationupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ReservationRead](#reservationread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/restaurants/reservations/reservations/{reservation_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### PATCH /api/modules/restaurants/reservations/reservations/{reservation_id}/status {#patch-apimodulesrestaurantsreservationsreservationsreservation-idstatus}

**Resumo:** Update reservation status
**Descrição:** Update the status of a reservation for the current tenant.
Requires at least STAFF tenant role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `reservation_id` | string | path | ✅ | The ID of the reservation to update |
| `status` | string | query | ✅ | The new status for the reservation |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ReservationRead](#reservationread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PATCH "http://localhost:8000/api/modules/restaurants/reservations/reservations/{reservation_id}/status" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
