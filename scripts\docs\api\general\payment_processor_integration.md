# General - Payment Processor Integration

**Categoria:** General
**Módulo:** Payment Processor Integration
**Total de Endpoints:** 5
**Gerado em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [POST /api/modules/core/payments/refunds/{refund_id}/check-status](#post-apimodulescorepaymentsrefundsrefund-idcheck-status) - Check Refund Status
- [POST /api/modules/core/payments/refunds/{refund_id}/process](#post-apimodulescorepaymentsrefundsrefund-idprocess) - Process Refund
- [POST /api/modules/core/payments/transactions/{transaction_id}/check-status](#post-apimodulescorepaymentstransactionstransaction-idcheck-status) - Check Payment Status
- [POST /api/modules/core/payments/transactions/{transaction_id}/process](#post-apimodulescorepaymentstransactionstransaction-idprocess) - Process Payment
- [GET /api/modules/core/payments/transactions/{transaction_id}/with-refunds](#get-apimodulescorepaymentstransactionstransaction-idwith-refunds) - Get Transaction with Refunds

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### PaymentRefundRead

**Descrição:** Schema for reading a payment refund.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `amount` | string | ✅ | - |
| `reason` | unknown | ❌ | - |
| `status` | PaymentStatus | ❌ | - |
| `external_id` | unknown | ❌ | - |
| `external_reference` | unknown | ❌ | - |
| `metadata` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `transaction_id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `processed_at` | unknown | ❌ | - |
| `created_by` | unknown | ❌ | - |

### PaymentTransactionRead

**Descrição:** Schema for reading a payment transaction.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `amount` | string | ✅ | - |
| `currency` | string | ❌ | - |
| `status` | PaymentStatus | ❌ | - |
| `source_type` | string | ✅ | - |
| `source_id` | unknown | ❌ | - |
| `external_id` | unknown | ❌ | - |
| `external_reference` | unknown | ❌ | - |
| `customer_id` | unknown | ❌ | - |
| `customer_email` | unknown | ❌ | - |
| `customer_name` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `metadata` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `processor_id` | unknown | ❌ | - |
| `method_id` | unknown | ❌ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `processed_at` | unknown | ❌ | - |

### PaymentTransactionWithRefundsRead

**Descrição:** Schema for reading a payment transaction with refunds.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `amount` | string | ✅ | - |
| `currency` | string | ❌ | - |
| `status` | PaymentStatus | ❌ | - |
| `source_type` | string | ✅ | - |
| `source_id` | unknown | ❌ | - |
| `external_id` | unknown | ❌ | - |
| `external_reference` | unknown | ❌ | - |
| `customer_id` | unknown | ❌ | - |
| `customer_email` | unknown | ❌ | - |
| `customer_name` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `metadata` | unknown | ❌ | - |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `processor_id` | unknown | ❌ | - |
| `method_id` | unknown | ❌ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |
| `processed_at` | unknown | ❌ | - |
| `refunds` | Array[PaymentRefundRead] | ❌ | - |
| `refunded_amount` | unknown | ❌ | - |
| `remaining_amount` | unknown | ❌ | - |

## 🔗 Endpoints Detalhados

### POST /api/modules/core/payments/refunds/{refund_id}/check-status {#post-apimodulescorepaymentsrefundsrefund-idcheck-status}

**Resumo:** Check Refund Status
**Descrição:** Check the status of a refund with the payment processor.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `refund_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PaymentRefundRead](#paymentrefundread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/payments/refunds/{refund_id}/check-status" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/payments/refunds/{refund_id}/process {#post-apimodulescorepaymentsrefundsrefund-idprocess}

**Resumo:** Process Refund
**Descrição:** Process a refund using the appropriate payment processor.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `refund_id` | string | path | ✅ | - |
| `reason` | string | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** {'type': 'object', 'additionalProperties': True, 'default': {}, 'title': 'Metadata'}

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PaymentRefundRead](#paymentrefundread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/payments/refunds/{refund_id}/process" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/modules/core/payments/transactions/{transaction_id}/check-status {#post-apimodulescorepaymentstransactionstransaction-idcheck-status}

**Resumo:** Check Payment Status
**Descrição:** Check the status of a payment with the payment processor.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `transaction_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PaymentTransactionRead](#paymenttransactionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/payments/transactions/{transaction_id}/check-status" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/core/payments/transactions/{transaction_id}/process {#post-apimodulescorepaymentstransactionstransaction-idprocess}

**Resumo:** Process Payment
**Descrição:** Process a payment using the appropriate payment processor.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `transaction_id` | string | path | ✅ | - |
| `payment_method_id` | string | query | ❌ | - |
| `customer_id` | string | query | ❌ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** {'type': 'object', 'additionalProperties': True, 'default': {}, 'title': 'Metadata'}

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PaymentTransactionRead](#paymenttransactionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/payments/transactions/{transaction_id}/process" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/payments/transactions/{transaction_id}/with-refunds {#get-apimodulescorepaymentstransactionstransaction-idwith-refunds}

**Resumo:** Get Transaction with Refunds
**Descrição:** Get a transaction with its refunds and calculated refund totals.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `transaction_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [PaymentTransactionWithRefundsRead](#paymenttransactionwithrefundsread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/payments/transactions/{transaction_id}/with-refunds" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
