'use client';

import { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { useAuth } from '@/lib/auth/AuthProvider';
import { apiClient } from '@/lib/api/client';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { 
  TicketIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';

interface Ticket {
  id: string;
  title: string;
  description: string;
  status: 'new' | 'open' | 'pending' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'question' | 'incident' | 'problem' | 'request' | 'suggestion';
  created_at: string;
  updated_at?: string;
  is_read_by_admin: boolean;
  is_read_by_user: boolean;
  message_count?: number;
  user_name?: string;
  assigned_admin_name?: string;
}

interface TicketListResponse {
  tickets: Ticket[];
  total: number;
  page: number;
  per_page: number;
  has_next: boolean;
  has_prev: boolean;
}

export function TicketList() {
  const { user, isAdmin } = useAuth();
  const [tickets, setTickets] = useState<Ticket[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    status: '',
    priority: '',
    search: ''
  });

  const fetchTickets = useCallback(async (page = 1) => {
    try {
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: page.toString(),
        per_page: '10',
        sort_by: 'created_at',
        sort_order: 'desc'
      });

      if (filters.status) params.append('status', filters.status);
      if (filters.priority) params.append('priority', filters.priority);
      if (filters.search) params.append('search', filters.search);

      // Admin usa endpoint diferente para ver todos os tickets
      const endpoint = isAdmin()
        ? `/modules/core/help-center/admin/tickets?${params.toString()}`
        : `/modules/core/help-center/tickets?${params.toString()}`;

      console.log('🎫 TicketList - Endpoint:', endpoint);
      console.log('🎫 TicketList - isAdmin():', isAdmin());

      // Log do token para debug (buscar nos cookies, não localStorage)
      const token = document.cookie.split('; ').find(row => row.startsWith('access_token='))?.split('=')[1];
      console.log('🎫 TicketList - Token:', token ? token.substring(0, 50) + '...' : 'NO TOKEN');

      console.log('🎫 TicketList - Making request to:', endpoint);

      const response = await apiClient.get<TicketListResponse>(endpoint);

      // O apiClient pode retornar diferentes estruturas, vamos tratar todas
      let tickets = [];
      let total = 0;

      if (response && typeof response === 'object') {
        // Se response tem tickets diretamente
        if (Array.isArray((response as any).tickets)) {
          tickets = (response as any).tickets;
          total = (response as any).total || tickets.length;
        }
        // Se response.data tem tickets
        else if ((response as any).data && Array.isArray((response as any).data.tickets)) {
          tickets = (response as any).data.tickets;
          total = (response as any).data.total || tickets.length;
        }
        // Se response é um array diretamente
        else if (Array.isArray(response)) {
          tickets = response;
          total = tickets.length;
        }
        // Se response.data é um array diretamente
        else if ((response as any).data && Array.isArray((response as any).data)) {
          tickets = (response as any).data;
          total = tickets.length;
        }
      }

      setTickets(tickets);
      setCurrentPage(1); // Reset to first page when filters change
      setTotalPages(Math.ceil(total / 10)); // Assuming 10 items per page
    } catch (err: any) {
      console.error('Erro ao carregar tickets:', err);
      setError('Erro ao carregar tickets. Tente novamente.');
    } finally {
      setIsLoading(false);
    }
  }, [filters, isAdmin]);

  useEffect(() => {
    fetchTickets(currentPage);
  }, [currentPage, filters, fetchTickets]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'new':
        return <TicketIcon className="h-5 w-5 text-blue-500" />;
      case 'open':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'pending':
        return <ExclamationTriangleIcon className="h-5 w-5 text-orange-500" />;
      case 'resolved':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'closed':
        return <XCircleIcon className="h-5 w-5 text-gray-500" />;
      default:
        return <TicketIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    const statusMap = {
      new: 'Novo',
      open: 'Aberto',
      pending: 'Pendente',
      resolved: 'Resolvido',
      closed: 'Fechado'
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityText = (priority: string) => {
    const priorityMap = {
      urgent: 'Urgente',
      high: 'Alta',
      medium: 'Média',
      low: 'Baixa'
    };
    return priorityMap[priority as keyof typeof priorityMap] || priority;
  };

  const getCategoryText = (category: string) => {
    const categoryMap = {
      question: 'Dúvida',
      incident: 'Incidente',
      problem: 'Problema',
      request: 'Solicitação',
      suggestion: 'Sugestão'
    };
    return categoryMap[category as keyof typeof categoryMap] || category;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading && (!tickets || tickets.length === 0)) {
    return (
      <div className="flex items-center justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">{error}</div>
        <button
          onClick={() => fetchTickets(currentPage)}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Tentar Novamente
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Status
          </label>
          <select
            value={filters.status}
            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Todos</option>
            <option value="new">Novo</option>
            <option value="open">Aberto</option>
            <option value="pending">Pendente</option>
            <option value="resolved">Resolvido</option>
            <option value="closed">Fechado</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Prioridade
          </label>
          <select
            value={filters.priority}
            onChange={(e) => setFilters(prev => ({ ...prev, priority: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Todas</option>
            <option value="urgent">Urgente</option>
            <option value="high">Alta</option>
            <option value="medium">Média</option>
            <option value="low">Baixa</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Buscar
          </label>
          <input
            type="text"
            value={filters.search}
            onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
            placeholder="Buscar por título ou descrição..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* Tickets List */}
      {!tickets || tickets.length === 0 ? (
        <div className="text-center py-12">
          <TicketIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhum ticket encontrado</h3>
          <p className="mt-1 text-sm text-gray-500">
            Comece criando um novo ticket de suporte.
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {tickets?.map((ticket) => (
            <Link
              key={ticket.id}
              href={`/dashboard/help_center/tickets/${ticket.id}`}
              className="block glass rounded-lg p-6 hover:shadow-lg transition-shadow duration-200"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    {getStatusIcon(ticket.status)}
                    <h3 className="text-lg font-medium text-gray-900 truncate">
                      {ticket.title}
                    </h3>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(ticket.priority)}`}>
                      {getPriorityText(ticket.priority)}
                    </span>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                    {ticket.description}
                  </p>
                  
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <span>Status: {getStatusText(ticket.status)}</span>
                    <span>Categoria: {getCategoryText(ticket.category)}</span>
                    <span>Criado: {formatDate(ticket.created_at)}</span>
                    {isAdmin() && ticket.user_name && (
                      <span>Usuário: {ticket.user_name}</span>
                    )}
                    {isAdmin() && ticket.assigned_admin_name && (
                      <span>Atribuído: {ticket.assigned_admin_name}</span>
                    )}
                    {ticket.message_count && (
                      <div className="flex items-center space-x-1">
                        <ChatBubbleLeftRightIcon className="h-4 w-4" />
                        <span>{ticket.message_count} mensagens</span>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="ml-4 flex-shrink-0">
                  {!ticket.is_read_by_user && (
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  )}
                </div>
              </div>
            </Link>
          ))}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <button
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Anterior
          </button>
          
          <span className="text-sm text-gray-700">
            Página {currentPage} de {totalPages}
          </span>
          
          <button
            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
            disabled={currentPage === totalPages}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Próxima
          </button>
        </div>
      )}
    </div>
  );
}
