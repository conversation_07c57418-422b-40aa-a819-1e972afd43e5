"""
Service for QR code management in the table management module.
"""

import uuid
import secrets
import string
from typing import Optional, List, Dict, Any
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.modules.tenants.restaurants.table_management.models.table import Table
from app.modules.tenants.restaurants.table_management.models.universal_qrcode import UniversalQRCode
from app.modules.tenants.restaurants.table_management.models.qrcode_scan import QRCodeScan
from app.modules.tenants.restaurants.table_management.schemas.qrcode import (
    TableQRCodeCreate,
    TableQRCodeUpdate,
    UniversalQRCodeCreate,
    UniversalQRCodeUpdate,
    QRCodeScanCreate,
    QRCodeScanUpdate,
    QRCodeScanStats,
)


class QRCodeService:
    """Service for QR code management."""

    @staticmethod
    async def generate_table_qrcode(
        db: AsyncSession, table_id: uuid.UUID, data: TableQRCodeCreate
    ) -> Table:
        """Generate a QR code for a table."""
        # Get the table
        table = await db.get(Table, table_id)
        if not table:
            raise ValueError(f"Table with ID {table_id} not found")

        # Generate a unique QR code ID
        qrcode_id = f"table_{uuid.uuid4().hex[:8]}"

        # Generate a secret for the QR code
        alphabet = string.ascii_letters + string.digits
        qrcode_secret = "".join(secrets.choice(alphabet) for _ in range(32))

        # Update the table
        table.qrcode_id = qrcode_id
        table.qrcode_secret = qrcode_secret
        table.qrcode_enabled = data.qrcode_enabled

        await db.commit()
        await db.refresh(table)

        return table

    @staticmethod
    async def update_table_qrcode(
        db: AsyncSession, table_id: uuid.UUID, data: TableQRCodeUpdate
    ) -> Table:
        """Update a table QR code."""
        # Get the table
        table = await db.get(Table, table_id)
        if not table:
            raise ValueError(f"Table with ID {table_id} not found")

        # Update the table
        if data.qrcode_enabled is not None:
            table.qrcode_enabled = data.qrcode_enabled

        await db.commit()
        await db.refresh(table)

        return table

    @staticmethod
    async def regenerate_table_qrcode(db: AsyncSession, table_id: uuid.UUID) -> Table:
        """Regenerate a QR code for a table."""
        # Get the table
        table = await db.get(Table, table_id)
        if not table:
            raise ValueError(f"Table with ID {table_id} not found")

        # Generate a new QR code ID
        qrcode_id = f"table_{uuid.uuid4().hex[:8]}"

        # Generate a new secret for the QR code
        alphabet = string.ascii_letters + string.digits
        qrcode_secret = "".join(secrets.choice(alphabet) for _ in range(32))

        # Update the table
        table.qrcode_id = qrcode_id
        table.qrcode_secret = qrcode_secret

        await db.commit()
        await db.refresh(table)

        return table

    @staticmethod
    async def create_universal_qrcode(
        db: AsyncSession, tenant_id: uuid.UUID, data: UniversalQRCodeCreate
    ) -> UniversalQRCode:
        """Create a universal QR code."""
        # Generate a unique QR code ID
        qrcode_id = f"universal_{uuid.uuid4().hex[:8]}"

        # Generate a secret for the QR code
        alphabet = string.ascii_letters + string.digits
        qrcode_secret = "".join(secrets.choice(alphabet) for _ in range(32))

        # Create the universal QR code
        universal_qrcode = UniversalQRCode(
            tenant_id=tenant_id,
            name=data.name,
            description=data.description,
            qrcode_id=qrcode_id,
            qrcode_secret=qrcode_secret,
            is_active=data.is_active,
            requires_approval=data.requires_approval,
        )

        db.add(universal_qrcode)
        await db.commit()
        await db.refresh(universal_qrcode)

        return universal_qrcode

    @staticmethod
    async def update_universal_qrcode(
        db: AsyncSession, qrcode_id: uuid.UUID, data: UniversalQRCodeUpdate
    ) -> UniversalQRCode:
        """Update a universal QR code."""
        # Get the universal QR code
        universal_qrcode = await db.get(UniversalQRCode, qrcode_id)
        if not universal_qrcode:
            raise ValueError(f"Universal QR code with ID {qrcode_id} not found")

        # Update the universal QR code
        if data.name is not None:
            universal_qrcode.name = data.name
        if data.description is not None:
            universal_qrcode.description = data.description
        if data.is_active is not None:
            universal_qrcode.is_active = data.is_active
        if data.requires_approval is not None:
            universal_qrcode.requires_approval = data.requires_approval

        await db.commit()
        await db.refresh(universal_qrcode)

        return universal_qrcode

    @staticmethod
    async def regenerate_universal_qrcode(
        db: AsyncSession, qrcode_id: uuid.UUID
    ) -> UniversalQRCode:
        """Regenerate a universal QR code."""
        # Get the universal QR code
        universal_qrcode = await db.get(UniversalQRCode, qrcode_id)
        if not universal_qrcode:
            raise ValueError(f"Universal QR code with ID {qrcode_id} not found")

        # Generate a new QR code ID
        new_qrcode_id = f"universal_{uuid.uuid4().hex[:8]}"

        # Generate a new secret for the QR code
        alphabet = string.ascii_letters + string.digits
        qrcode_secret = "".join(secrets.choice(alphabet) for _ in range(32))

        # Update the universal QR code
        universal_qrcode.qrcode_id = new_qrcode_id
        universal_qrcode.qrcode_secret = qrcode_secret

        await db.commit()
        await db.refresh(universal_qrcode)

        return universal_qrcode

    @staticmethod
    async def record_qrcode_scan(
        db: AsyncSession, tenant_id: uuid.UUID, data: QRCodeScanCreate
    ) -> QRCodeScan:
        """Record a QR code scan."""
        # Create the QR code scan
        qrcode_scan = QRCodeScan(
            tenant_id=tenant_id,
            table_id=data.table_id,
            universal_qrcode_id=data.universal_qrcode_id,
            user_id=data.user_id,
            customer_id=data.customer_id,
            ip_address=data.ip_address,
            user_agent=data.user_agent,
            session_id=data.session_id,
            resulted_in_order=False,
        )

        db.add(qrcode_scan)
        await db.commit()
        await db.refresh(qrcode_scan)

        return qrcode_scan

    @staticmethod
    async def update_qrcode_scan(
        db: AsyncSession, scan_id: uuid.UUID, data: QRCodeScanUpdate
    ) -> QRCodeScan:
        """Update a QR code scan."""
        # Get the QR code scan
        qrcode_scan = await db.get(QRCodeScan, scan_id)
        if not qrcode_scan:
            raise ValueError(f"QR code scan with ID {scan_id} not found")

        # Update the QR code scan
        if data.resulted_in_order is not None:
            qrcode_scan.resulted_in_order = data.resulted_in_order

        await db.commit()
        await db.refresh(qrcode_scan)

        return qrcode_scan

    @staticmethod
    async def get_qrcode_scan_stats(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
    ) -> QRCodeScanStats:
        """Get QR code scan statistics."""
        # Base query
        query = select(QRCodeScan).where(QRCodeScan.tenant_id == tenant_id)

        # Add date filters if provided
        if start_date:
            query = query.where(QRCodeScan.scan_time >= start_date)
        if end_date:
            query = query.where(QRCodeScan.scan_time <= end_date)

        # Execute the query
        result = await db.execute(query)
        scans = result.scalars().all()

        # Calculate statistics
        total_scans = len(scans)
        scans_with_orders = sum(1 for scan in scans if scan.resulted_in_order)
        conversion_rate = (scans_with_orders / total_scans) * 100 if total_scans > 0 else 0

        # Group scans by table
        scans_by_table: Dict[uuid.UUID, int] = {}
        for scan in scans:
            if scan.table_id:
                scans_by_table[scan.table_id] = scans_by_table.get(scan.table_id, 0) + 1

        # Group scans by universal QR code
        scans_by_universal_qrcode: Dict[uuid.UUID, int] = {}
        for scan in scans:
            if scan.universal_qrcode_id:
                scans_by_universal_qrcode[scan.universal_qrcode_id] = (
                    scans_by_universal_qrcode.get(scan.universal_qrcode_id, 0) + 1
                )

        # Return statistics
        return QRCodeScanStats(
            total_scans=total_scans,
            scans_with_orders=scans_with_orders,
            conversion_rate=conversion_rate,
            scans_by_table=[
                {"table_id": table_id, "count": count} for table_id, count in scans_by_table.items()
            ],
            scans_by_universal_qrcode=[
                {"universal_qrcode_id": qrcode_id, "count": count}
                for qrcode_id, count in scans_by_universal_qrcode.items()
            ],
            scans_by_day=[],  # To be implemented
            scans_by_hour=[],  # To be implemented
        )
