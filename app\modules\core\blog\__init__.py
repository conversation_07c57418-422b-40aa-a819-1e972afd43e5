"""
Blog Module

A comprehensive blog system with SEO optimization and multi-language support.

Features:
- Multi-language content support
- SEO optimization (meta tags, slugs, structured data)
- Category and tag management
- Author management
- Comment system
- Content scheduling
- Rich text content with media support
- Search functionality
- Analytics integration
"""

import os

# Conditionally import the router to prevent circular dependencies during migrations
if os.environ.get("ALEMBIC_RUNNING") != "true":
    from .api.router import router as blog_router
else:
    blog_router = None

__all__ = ["blog_router"]
