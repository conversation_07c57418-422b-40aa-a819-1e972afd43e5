"""
Public tenant API endpoints for accessing basic tenant information.
"""

import logging
import uuid
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from app.core.db_dependencies import get_db
from app.core.exceptions import NotFoundError
from app.modules.core.tenants.services.tenant_service import TenantService
from app.modules.core.tenants.services.tenant_settings import tenant_settings_service

logger = logging.getLogger(__name__)

# Router setup
router = APIRouter(
    prefix="/public/tenants",
    tags=["Public Tenant API"],
    # No authentication required for public endpoints
)

# Simple in-memory cache for tenant lookups
_tenant_cache = {}

class PublicCurrencyResponse(BaseModel):
    """Response schema for public currency information."""
    currency: str
    currency_format: Dict[str, Any]

async def get_tenant_by_slug(tenant_slug: str, db: AsyncSession = Depends(get_db)) -> uuid.UUID:
    """
    Dependency to get tenant ID by slug for public endpoints.
    Includes caching for performance.
    """
    # Check cache first
    if tenant_slug in _tenant_cache:
        return _tenant_cache[tenant_slug]
    
    try:
        tenant_service = TenantService()
        tenant = await tenant_service.get_tenant_by_slug_or_id(db, tenant_slug)

        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Tenant with slug '{tenant_slug}' not found"
            )

        # Cache the result
        _tenant_cache[tenant_slug] = tenant.id
        return tenant.id
        
    except Exception as e:
        logger.error(f"Error getting tenant by slug {tenant_slug}: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Tenant with slug '{tenant_slug}' not found"
        )

@router.get("/{tenant_slug}/currency", response_model=PublicCurrencyResponse)
async def get_public_tenant_currency(
    tenant_slug: str,
    db: AsyncSession = Depends(get_db)
):
    """
    Get public currency configuration for a tenant.
    Public endpoint - no authentication required.
    """
    try:
        logger.info(f"Getting public currency config for tenant: {tenant_slug}")
        
        # Get tenant ID by slug
        tenant_id = await get_tenant_by_slug(tenant_slug, db)
        
        # Get tenant settings
        settings = await tenant_settings_service.get_settings(db, tenant_id)
        
        if not settings:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Settings not found for tenant '{tenant_slug}'"
            )
        
        # Extract currency information from settings
        currency = settings.default_currency or "BRL"

        # Smart default currency formats based on currency
        DEFAULT_CURRENCY_FORMATS = {
            'USD': {"decimal_separator": ".", "thousands_separator": ",", "symbol_position": "left", "symbol_spacing": False},
            'BRL': {"decimal_separator": ",", "thousands_separator": ".", "symbol_position": "left", "symbol_spacing": True},
            'EUR': {"decimal_separator": ",", "thousands_separator": ".", "symbol_position": "right", "symbol_spacing": True},
            'GBP': {"decimal_separator": ".", "thousands_separator": ",", "symbol_position": "left", "symbol_spacing": False},
            'JPY': {"decimal_separator": ".", "thousands_separator": ",", "symbol_position": "left", "symbol_spacing": False},
        }

        # Get default format for currency
        currency_format = DEFAULT_CURRENCY_FORMATS.get(currency, DEFAULT_CURRENCY_FORMATS['BRL'])

        # Try to get currency formatting from currency_config
        if settings.currency_config and isinstance(settings.currency_config, dict):
            logger.info(f"Found currency_config for {tenant_slug}: {settings.currency_config}")

            currency_formatting = settings.currency_config.get("currency_formatting", {})
            if currency in currency_formatting:
                format_data = currency_formatting[currency]
                logger.info(f"Found formatting for {currency}: {format_data}")

                currency_format.update({
                    "decimal_separator": format_data.get("decimal_separator", ","),
                    "thousands_separator": format_data.get("thousands_separator", "."),
                    "symbol_position": format_data.get("symbol_position", "left"),
                    "symbol_spacing": format_data.get("symbol_spacing", True)
                })
            else:
                logger.warning(f"No formatting found for currency {currency} in currency_formatting")
        else:
            logger.warning(f"No currency_config found for tenant {tenant_slug}")

        logger.info(f"Returning currency config for {tenant_slug}: {currency} with format {currency_format}")

        # Return public currency information
        return PublicCurrencyResponse(
            currency=currency,
            currency_format=currency_format
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving public currency for tenant {tenant_slug}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving currency configuration"
        )
