"""API endpoints for custom domains module."""

from fastapi import APIRouter  # noqa: E402

# Create a simple router for custom domains
router = APIRouter(prefix="/api/modules/core/custom_domains", tags=["custom_domains"])


@router.get("/")
async def get_custom_domains_status():
    """
    Get custom domains module status.
    """
    return {"message": "Custom domains module is active"}


__all__ = ["router"]
