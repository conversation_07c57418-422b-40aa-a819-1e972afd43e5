import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';

class RevenueChartWidget extends StatefulWidget {
  final List<RevenueData> dailyData;
  final List<RevenueData> weeklyData;
  final List<RevenueData> monthlyData;
  final String selectedPeriod;
  final Function(String) onPeriodChanged;

  const RevenueChartWidget({
    super.key,
    required this.dailyData,
    required this.weeklyData,
    required this.monthlyData,
    required this.selectedPeriod,
    required this.onPeriodChanged,
  });

  @override
  State<RevenueChartWidget> createState() => _RevenueChartWidgetState();
}

class _RevenueChartWidgetState extends State<RevenueChartWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  List<RevenueData> get currentData {
    switch (widget.selectedPeriod) {
      case 'Diário':
        return widget.dailyData;
      case 'Semanal':
        return widget.weeklyData;
      case 'Mensal':
        return widget.monthlyData;
      default:
        return widget.dailyData;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.trending_up,
                  color: theme.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Receita',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                _buildPeriodSelector(theme),
              ],
            ),
            
            const SizedBox(height: 24),
            
            SizedBox(
              height: 300,
              child: AnimatedBuilder(
                animation: _animation,
                builder: (context, child) {
                  return LineChart(
                    LineChartData(
                      gridData: FlGridData(
                        show: true,
                        drawVerticalLine: false,
                        horizontalInterval: _getHorizontalInterval(),
                        getDrawingHorizontalLine: (value) {
                          return FlLine(
                            color: theme.dividerColor.withValues(alpha: 0.3),
                            strokeWidth: 1,
                          );
                        },
                      ),
                      titlesData: FlTitlesData(
                        show: true,
                        rightTitles: const AxisTitles(
                          sideTitles: SideTitles(showTitles: false),
                        ),
                        topTitles: const AxisTitles(
                          sideTitles: SideTitles(showTitles: false),
                        ),
                        bottomTitles: AxisTitles(
                          sideTitles: SideTitles(
                            showTitles: true,
                            reservedSize: 30,
                            interval: 1,
                            getTitlesWidget: (value, meta) {
                              return _buildBottomTitle(value.toInt(), theme);
                            },
                          ),
                        ),
                        leftTitles: AxisTitles(
                          sideTitles: SideTitles(
                            showTitles: true,
                            interval: _getHorizontalInterval(),
                            reservedSize: 60,
                            getTitlesWidget: (value, meta) {
                              return _buildLeftTitle(value, theme);
                            },
                          ),
                        ),
                      ),
                      borderData: FlBorderData(
                        show: true,
                        border: Border(
                          bottom: BorderSide(
                            color: theme.dividerColor.withValues(alpha: 0.3),
                          ),
                          left: BorderSide(
                            color: theme.dividerColor.withValues(alpha: 0.3),
                          ),
                        ),
                      ),
                      minX: 0,
                      maxX: (currentData.length - 1).toDouble(),
                      minY: 0,
                      maxY: _getMaxY(),
                      lineBarsData: [
                        LineChartBarData(
                          spots: currentData.asMap().entries.map((entry) {
                            return FlSpot(
                              entry.key.toDouble(),
                              entry.value.amount * _animation.value,
                            );
                          }).toList(),
                          isCurved: true,
                          gradient: LinearGradient(
                            colors: [
                              theme.primaryColor,
                              theme.primaryColor.withValues(alpha: 0.7),
                            ],
                          ),
                          barWidth: 3,
                          isStrokeCapRound: true,
                          dotData: FlDotData(
                            show: true,
                            getDotPainter: (spot, percent, barData, index) {
                              return FlDotCirclePainter(
                                radius: 4,
                                color: theme.primaryColor,
                                strokeWidth: 2,
                                strokeColor: Colors.white,
                              );
                            },
                          ),
                          belowBarData: BarAreaData(
                            show: true,
                            gradient: LinearGradient(
                              colors: [
                                theme.primaryColor.withValues(alpha: 0.3),
                                theme.primaryColor.withValues(alpha: 0.1),
                              ],
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                            ),
                          ),
                        ),
                      ],
                      lineTouchData: LineTouchData(
                        enabled: true,
                        touchTooltipData: LineTouchTooltipData(
                          tooltipBgColor: theme.cardColor,
                          tooltipRoundedRadius: 8,
                          tooltipPadding: const EdgeInsets.all(8),
                          getTooltipItems: (touchedSpots) {
                            return touchedSpots.map((spot) {
                              final data = currentData[spot.x.toInt()];
                              return LineTooltipItem(
                                '${data.label}\nR\$ ${data.amount.toStringAsFixed(2)}',
                                TextStyle(
                                  color: theme.colorScheme.onSurface,
                                  fontWeight: FontWeight.bold,
                                ),
                              );
                            }).toList();
                          },
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            
            const SizedBox(height: 16),
            
            _buildSummary(theme),
          ],
        ),
      ),
    );
  }

  Widget _buildPeriodSelector(ThemeData theme) {
    const periods = ['Diário', 'Semanal', 'Mensal'];
    
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.dividerColor.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: periods.map((period) {
          final isSelected = period == widget.selectedPeriod;
          return GestureDetector(
            onTap: () => widget.onPeriodChanged(period),
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 6,
              ),
              decoration: BoxDecoration(
                color: isSelected
                    ? theme.primaryColor
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                period,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: isSelected
                      ? Colors.white
                      : theme.colorScheme.onSurface,
                  fontWeight: isSelected
                      ? FontWeight.w600
                      : FontWeight.normal,
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildBottomTitle(int index, ThemeData theme) {
    if (index >= 0 && index < currentData.length) {
      return Padding(
        padding: const EdgeInsets.only(top: 8),
        child: Text(
          currentData[index].label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
      );
    }
    return const SizedBox.shrink();
  }

  Widget _buildLeftTitle(double value, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: Text(
        'R\$ ${(value / 1000).toStringAsFixed(0)}k',
        style: theme.textTheme.bodySmall?.copyWith(
          color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
        ),
      ),
    );
  }

  Widget _buildSummary(ThemeData theme) {
    final total = currentData.fold<double>(
      0,
      (sum, data) => sum + data.amount,
    );
    final average = total / currentData.length;
    final highest = currentData.reduce(
      (a, b) => a.amount > b.amount ? a : b,
    );
    
    return Row(
      children: [
        Expanded(
          child: _buildSummaryItem(
            'Total',
            'R\$ ${total.toStringAsFixed(2)}',
            Icons.attach_money,
            theme.primaryColor,
            theme,
          ),
        ),
        Expanded(
          child: _buildSummaryItem(
            'Média',
            'R\$ ${average.toStringAsFixed(2)}',
            Icons.trending_up,
            Colors.blue,
            theme,
          ),
        ),
        Expanded(
          child: _buildSummaryItem(
            'Maior',
            'R\$ ${highest.amount.toStringAsFixed(2)}',
            Icons.star,
            Colors.orange,
            theme,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryItem(
    String title,
    String value,
    IconData icon,
    Color color,
    ThemeData theme,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 20,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  double _getMaxY() {
    if (currentData.isEmpty) return 100;
    final maxValue = currentData
        .map((data) => data.amount)
        .reduce((a, b) => a > b ? a : b);
    return (maxValue * 1.2).ceilToDouble();
  }

  double _getHorizontalInterval() {
    final maxY = _getMaxY();
    return (maxY / 5).ceilToDouble();
  }
}

class RevenueData {
  final String label;
  final double amount;
  final DateTime date;

  const RevenueData({
    required this.label,
    required this.amount,
    required this.date,
  });
}

class CompactRevenueChart extends StatelessWidget {
  final List<RevenueData> data;
  final Color color;
  final double height;

  const CompactRevenueChart({
    super.key,
    required this.data,
    required this.color,
    this.height = 100,
  });

  @override
  Widget build(BuildContext context) {
    if (data.isEmpty) {
      return SizedBox(
        height: height,
        child: const Center(
          child: Text('Sem dados'),
        ),
      );
    }

    return SizedBox(
      height: height,
      child: LineChart(
        LineChartData(
          gridData: const FlGridData(show: false),
          titlesData: const FlTitlesData(show: false),
          borderData: FlBorderData(show: false),
          minX: 0,
          maxX: (data.length - 1).toDouble(),
          minY: 0,
          maxY: data.map((d) => d.amount).reduce((a, b) => a > b ? a : b) * 1.1,
          lineBarsData: [
            LineChartBarData(
              spots: data.asMap().entries.map((entry) {
                return FlSpot(
                  entry.key.toDouble(),
                  entry.value.amount,
                );
              }).toList(),
              isCurved: true,
              color: color,
              barWidth: 2,
              isStrokeCapRound: true,
              dotData: const FlDotData(show: false),
              belowBarData: BarAreaData(
                show: true,
                color: color.withValues(alpha: 0.2),
              ),
            ),
          ],
          lineTouchData: const LineTouchData(enabled: false),
        ),
      ),
    );
  }
}