import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/providers/auth_provider.dart';
import '../../../../core/providers/orders_provider.dart';
import '../../../../core/providers/tables_provider.dart';
import '../../../../core/providers/menu_provider.dart';
import '../../../../shared/widgets/loading_overlay.dart';
import '../widgets/admin_stats_card.dart';
import '../widgets/admin_quick_actions.dart';
import '../widgets/revenue_chart_widget.dart';
import '../widgets/recent_activity_widget.dart';
import '../widgets/performance_metrics_widget.dart';
import '../widgets/admin_quick_actions_widget.dart';
import '../widgets/notifications_widget.dart';

class AdminDashboardPage extends ConsumerStatefulWidget {
  const AdminDashboardPage({super.key});

  @override
  ConsumerState<AdminDashboardPage> createState() => _AdminDashboardPageState();
}

class _AdminDashboardPageState extends ConsumerState<AdminDashboardPage>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final user = ref.watch(currentUserProvider);
    
    return Scaffold(
      backgroundColor: theme.colorScheme.background,
      appBar: AppBar(
        title: const Text('Painel Administrativo'),
        elevation: 0,
        backgroundColor: theme.colorScheme.surface,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () => _showNotifications(context),
          ),
          PopupMenuButton<String>(
            icon: CircleAvatar(
              backgroundColor: theme.colorScheme.primary,
              child: Text(
                user?.name.substring(0, 1).toUpperCase() ?? 'A',
                style: TextStyle(
                  color: theme.colorScheme.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'profile',
                child: Row(
                  children: [
                    Icon(Icons.person_outline, color: theme.colorScheme.onSurface),
                    const SizedBox(width: 12),
                    const Text('Perfil'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings_outlined, color: theme.colorScheme.onSurface),
                    const SizedBox(width: 12),
                    const Text('Configurações'),
                  ],
                ),
              ),
              const PopupMenuDivider(),
              PopupMenuItem(
                value: 'logout',
                child: Row(
                  children: [
                    Icon(Icons.logout, color: theme.colorScheme.error),
                    const SizedBox(width: 12),
                    Text(
                      'Sair',
                      style: TextStyle(color: theme.colorScheme.error),
                    ),
                  ],
                ),
              ),
            ],
            onSelected: (value) => _handleMenuAction(value, context),
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: RefreshIndicator(
            onRefresh: _refreshData,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Welcome section
                  _buildWelcomeSection(theme, user?.name ?? 'Administrador'),
                  
                  const SizedBox(height: 32),
                  
                  // Statistics cards
                  _buildStatsSection(theme),
                  
                  const SizedBox(height: 32),
                  
                  // Quick actions
                  AdminQuickActionsWidget(
                    actions: _getQuickActions(),
                    crossAxisCount: 4,
                  ),
                  
                  const SizedBox(height: 32),
                  
                  // Notifications Section
                  NotificationsWidget(
                    notifications: _getNotifications(),
                    onViewAll: () => _showComingSoon('Todas as Notificações'),
                    onNotificationTap: (notification) {
                      _showComingSoon('Detalhes da Notificação');
                    },
                    onMarkAsRead: (notification) {
                      // TODO: Implement mark as read
                    },
                    onDismiss: (notification) {
                      // TODO: Implement dismiss notification
                    },
                  ),
                  
                  const SizedBox(height: 32),
                  
                  // Charts and analytics
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Revenue chart
                      Expanded(
                        flex: 2,
                        child: _buildRevenueSection(theme),
                      ),
                      
                      const SizedBox(width: 24),
                      
                      // Recent activity
                      Expanded(
                        child: _buildRecentActivitySection(theme),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 32),
                  
                  // Performance metrics
                  _buildPerformanceSection(theme),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeSection(ThemeData theme, String userName) {
    final now = DateTime.now();
    final hour = now.hour;
    String greeting;
    
    if (hour < 12) {
      greeting = 'Bom dia';
    } else if (hour < 18) {
      greeting = 'Boa tarde';
    } else {
      greeting = 'Boa noite';
    }
    
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary,
            theme.colorScheme.primary.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.primary.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '$greeting, $userName!',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    color: theme.colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Aqui está um resumo do seu restaurante hoje',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: theme.colorScheme.onPrimary.withOpacity(0.9),
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  _formatDate(now),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onPrimary.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.dashboard,
            size: 64,
            color: theme.colorScheme.onPrimary.withOpacity(0.3),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsSection(ThemeData theme) {
    final ordersState = ref.watch(ordersProvider);
    final tablesState = ref.watch(tablesProvider);
    final menuState = ref.watch(menuProvider);
    
    final todayOrders = ordersState.todayOrders;
    final todayRevenue = todayOrders.fold<double>(
      0.0,
      (sum, order) => sum + order.totalAmount,
    );
    
    final occupiedTables = tablesState.occupiedTables.length;
    final totalTables = tablesState.tables.length;
    final occupancyRate = totalTables > 0 ? (occupiedTables / totalTables) * 100 : 0.0;
    
    final activeMenuItems = menuState.availableItems.length;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Estatísticas de Hoje',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        
        Row(
          children: [
            Expanded(
              child: AdminStatsCard(
                title: 'Receita',
                value: 'R\$ ${todayRevenue.toStringAsFixed(2)}',
                subtitle: '${todayOrders.length} pedidos',
                icon: Icons.attach_money,
                color: Colors.green,
                trend: '+12.5%',
                isPositiveTrend: true,
              ),
            ),
            const SizedBox(width: 16),
            
            Expanded(
              child: AdminStatsCard(
                title: 'Ocupação',
                value: '${occupancyRate.toStringAsFixed(1)}%',
                subtitle: '$occupiedTables de $totalTables mesas',
                icon: Icons.table_restaurant,
                color: Colors.blue,
                trend: '+5.2%',
                isPositiveTrend: true,
              ),
            ),
            const SizedBox(width: 16),
            
            Expanded(
              child: AdminStatsCard(
                title: 'Menu Ativo',
                value: '$activeMenuItems',
                subtitle: 'itens disponíveis',
                icon: Icons.restaurant_menu,
                color: Colors.orange,
                trend: '+2',
                isPositiveTrend: true,
              ),
            ),
            const SizedBox(width: 16),
            
            Expanded(
              child: AdminStatsCard(
                title: 'Pedidos Pendentes',
                value: '${ordersState.pendingOrders.length}',
                subtitle: 'aguardando preparo',
                icon: Icons.pending_actions,
                color: Colors.amber,
                trend: '-3',
                isPositiveTrend: false,
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Mock data for quick actions
  List<AdminQuickAction> _getQuickActions() {
    return [
      AdminQuickAction.viewReports(
        onTap: () => _showComingSoon('Relatórios'),
      ),
      AdminQuickAction.manageMenu(
        onTap: () => _showComingSoon('Gerenciar Menu'),
        badge: '3',
      ),
      AdminQuickAction.manageStaff(
        onTap: () => _showComingSoon('Funcionários'),
      ),
      AdminQuickAction.manageTables(
        onTap: () => _showComingSoon('Mesas'),
      ),
      AdminQuickAction.viewOrders(
        onTap: () => _showComingSoon('Pedidos'),
        badge: '12',
      ),
      AdminQuickAction.settings(
        onTap: () => _showComingSoon('Configurações'),
      ),
      AdminQuickAction.inventory(
        onTap: () => _showComingSoon('Estoque'),
        badge: '2',
      ),
      AdminQuickAction.promotions(
        onTap: () => _showComingSoon('Promoções'),
      ),
    ];
  }

  // Mock data for notifications
  List<NotificationItem> _getNotifications() {
    final now = DateTime.now();
    return [
      NotificationItem.newOrder(
        orderId: '001',
        tableNumber: '5',
        timestamp: now.subtract(const Duration(minutes: 2)),
      ),
      NotificationItem.lowStock(
        itemName: 'Hambúrguer',
        quantity: 3,
        timestamp: now.subtract(const Duration(minutes: 15)),
      ),
      NotificationItem.paymentReceived(
        orderId: '002',
        amount: 120.00,
        timestamp: now.subtract(const Duration(minutes: 30)),
      ),
      NotificationItem.staffLogin(
        staffName: 'Ana Costa',
        timestamp: now.subtract(const Duration(hours: 1)),
      ),
      NotificationItem.customerFeedback(
        customerName: 'Cliente Mesa 3',
        rating: 5,
        comment: 'Excelente atendimento!',
        timestamp: now.subtract(const Duration(hours: 2)),
      ),
    ];
  }

  Widget _buildRevenueSection(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Receita dos Últimos 7 Dias',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.more_vert),
                onPressed: () => _showRevenueOptions(context),
              ),
            ],
          ),
          const SizedBox(height: 24),
          
          const RevenueChartWidget(),
        ],
      ),
    );
  }

  Widget _buildRecentActivitySection(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Atividade Recente',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),
          
          const RecentActivityWidget(),
        ],
      ),
    );
  }

  Widget _buildPerformanceSection(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Métricas de Performance',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),
          
          Row(
            children: [
              Expanded(
                child: _buildPerformanceMetric(
                  'Tempo Médio de Preparo',
                  '18 min',
                  Icons.timer,
                  Colors.blue,
                  theme,
                ),
              ),
              const SizedBox(width: 16),
              
              Expanded(
                child: _buildPerformanceMetric(
                  'Satisfação do Cliente',
                  '4.8/5.0',
                  Icons.star,
                  Colors.amber,
                  theme,
                ),
              ),
              const SizedBox(width: 16),
              
              Expanded(
                child: _buildPerformanceMetric(
                  'Taxa de Rotatividade',
                  '2.3x/dia',
                  Icons.refresh,
                  Colors.green,
                  theme,
                ),
              ),
              const SizedBox(width: 16),
              
              Expanded(
                child: _buildPerformanceMetric(
                  'Ticket Médio',
                  'R\$ 45.80',
                  Icons.receipt,
                  Colors.purple,
                  theme,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceMetric(
    String title,
    String value,
    IconData icon,
    Color color,
    ThemeData theme,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.3),
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            size: 32,
            color: color,
          ),
          const SizedBox(height: 12),
          
          Text(
            value,
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          
          Text(
            title,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Helper methods
  String _formatDate(DateTime date) {
    const months = [
      'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
      'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
    ];
    
    const weekdays = [
      'Segunda-feira', 'Terça-feira', 'Quarta-feira', 'Quinta-feira',
      'Sexta-feira', 'Sábado', 'Domingo'
    ];
    
    final weekday = weekdays[date.weekday - 1];
    final day = date.day;
    final month = months[date.month - 1];
    final year = date.year;
    
    return '$weekday, $day de $month de $year';
  }

  // Action methods
  Future<void> _refreshData() async {
    await Future.wait([
      ref.read(ordersProvider.notifier).loadOrders(),
      ref.read(tablesProvider.notifier).loadTables(),
      ref.read(menuProvider.notifier).loadMenu(),
    ]);
  }

  void _handleMenuAction(String action, BuildContext context) {
    switch (action) {
      case 'profile':
        _showProfileDialog(context);
        break;
      case 'settings':
        context.push('/admin/settings');
        break;
      case 'logout':
        _showLogoutDialog(context);
        break;
    }
  }

  void _showNotifications(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Notificações'),
        content: SizedBox(
          width: 400,
          height: 300,
          child: ListView(
            children: [
              ListTile(
                leading: CircleAvatar(
                  backgroundColor: Colors.orange,
                  child: const Icon(Icons.warning, color: Colors.white),
                ),
                title: const Text('Mesa 5 aguardando há 15 min'),
                subtitle: const Text('Pedido #1234 - Status: Preparando'),
                trailing: const Text('2 min'),
              ),
              ListTile(
                leading: CircleAvatar(
                  backgroundColor: Colors.green,
                  child: const Icon(Icons.check, color: Colors.white),
                ),
                title: const Text('Pedido #1235 finalizado'),
                subtitle: const Text('Mesa 3 - Total: R\$ 89,50'),
                trailing: const Text('5 min'),
              ),
              ListTile(
                leading: CircleAvatar(
                  backgroundColor: Colors.blue,
                  child: const Icon(Icons.info, color: Colors.white),
                ),
                title: const Text('Novo funcionário cadastrado'),
                subtitle: const Text('João Silva - Garçom'),
                trailing: const Text('1h'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fechar'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Navigate to notifications page
            },
            child: const Text('Ver Todas'),
          ),
        ],
      ),
    );
  }

  void _showProfileDialog(BuildContext context) {
    final user = ref.read(currentUserProvider);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Perfil do Usuário'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircleAvatar(
              radius: 40,
              backgroundColor: Theme.of(context).colorScheme.primary,
              child: Text(
                user?.name.substring(0, 1).toUpperCase() ?? 'A',
                style: TextStyle(
                  fontSize: 32,
                  color: Theme.of(context).colorScheme.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 16),
            
            Text(
              user?.name ?? 'Administrador',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            
            Text(
              user?.email ?? '<EMAIL>',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 8),
            
            Chip(
              label: Text(user?.role.toUpperCase() ?? 'OWNER'),
              backgroundColor: Theme.of(context).colorScheme.primaryContainer,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fechar'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Navigate to edit profile
            },
            child: const Text('Editar'),
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmar Saída'),
        content: const Text('Tem certeza que deseja sair do sistema?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancelar'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ref.read(authProvider.notifier).logout();
              context.go('/login');
            },
            child: Text(
              'Sair',
              style: TextStyle(
                color: Theme.of(context).colorScheme.error,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showRevenueOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.calendar_today),
              title: const Text('Últimos 7 dias'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(Icons.calendar_month),
              title: const Text('Último mês'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(Icons.calendar_today),
              title: const Text('Último ano'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(Icons.download),
              title: const Text('Exportar dados'),
              onTap: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
    );
  }

  void _showComingSoon(String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Em Breve'),
        content: Text('A funcionalidade "$feature" estará disponível em breve.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}