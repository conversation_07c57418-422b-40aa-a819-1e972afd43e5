"""
Cart Schemas for EShop System
=============================

Schemas de validação para carrinho de compras com suporte a
produtos do eshop, customizações e contexto B2B/B2C.
"""

import uuid
from datetime import datetime
from decimal import Decimal
from typing import Optional, List, Dict, Any

from pydantic import BaseModel, Field, ConfigDict, validator

from app.modules.core.functions.cart.models.cart import CartStatus


class CartItemBase(BaseModel):
    """Schema base para itens do carrinho."""
    
    product_id: uuid.UUID = Field(..., description="ID do produto")
    quantity: int = Field(..., ge=1, le=999, description="Quantidade do item")
    selected_variants: Optional[Dict[str, str]] = Field(
        None, description="Variações selecionadas {variant_group_id: option_id}"
    )
    selected_modifiers: Optional[List[str]] = Field(
        None, description="IDs dos modificadores selecionados"
    )
    special_instructions: Optional[str] = Field(
        None, max_length=500, description="Instruções especiais"
    )


class CartItemCreate(CartItemBase):
    """Schema para criação de item do carrinho."""
    pass


class CartItemUpdate(BaseModel):
    """Schema para atualização de item do carrinho."""
    
    quantity: Optional[int] = Field(None, ge=1, le=999, description="Nova quantidade")
    selected_variants: Optional[Dict[str, str]] = Field(
        None, description="Variações atualizadas"
    )
    selected_modifiers: Optional[List[str]] = Field(
        None, description="Modificadores atualizados"
    )
    special_instructions: Optional[str] = Field(
        None, max_length=500, description="Instruções especiais atualizadas"
    )


class CartItemRead(CartItemBase):
    """Schema para leitura de item do carrinho."""
    
    id: uuid.UUID
    cart_id: uuid.UUID
    unit_price: Decimal
    total_price: Decimal
    created_at: datetime
    updated_at: datetime
    
    # Dados do produto (incluídos via join)
    product_name: Optional[str] = None
    product_image_url: Optional[str] = None
    product_sku: Optional[str] = None
    
    model_config = ConfigDict(from_attributes=True)


class CartBase(BaseModel):
    """Schema base para carrinho."""
    
    market_context: str = Field(
        default='b2c', 
        description="Contexto do mercado (b2b/b2c)"
    )
    notes: Optional[str] = Field(
        None, max_length=1000, description="Notas do carrinho"
    )


class CartCreate(CartBase):
    """Schema para criação de carrinho."""
    
    session_id: Optional[str] = Field(
        None, description="ID da sessão para carrinho anônimo"
    )
    items: Optional[List[CartItemCreate]] = Field(
        default=[], description="Itens iniciais do carrinho"
    )


class CartUpdate(BaseModel):
    """Schema para atualização de carrinho."""
    
    notes: Optional[str] = Field(
        None, max_length=1000, description="Notas atualizadas"
    )
    market_context: Optional[str] = Field(
        None, description="Contexto do mercado atualizado"
    )


class CartRead(CartBase):
    """Schema para leitura de carrinho."""
    
    id: uuid.UUID
    tenant_id: uuid.UUID
    user_id: Optional[uuid.UUID]
    session_id: Optional[str]
    status: CartStatus
    
    # Totais
    subtotal: Decimal
    tax_amount: Decimal
    discount_amount: Decimal
    shipping_amount: Decimal
    total_amount: Decimal
    currency: str
    
    # Timestamps
    created_at: datetime
    updated_at: datetime
    expires_at: Optional[datetime]
    converted_at: Optional[datetime]
    
    # Itens do carrinho
    items: List[CartItemRead] = []
    
    # Propriedades calculadas
    item_count: int = 0
    is_expired: bool = False
    
    model_config = ConfigDict(from_attributes=True)


class CartSummary(BaseModel):
    """Schema para resumo do carrinho."""
    
    id: uuid.UUID
    item_count: int
    total_amount: Decimal
    currency: str
    status: CartStatus
    market_context: str
    updated_at: datetime
    
    model_config = ConfigDict(from_attributes=True)


class CartStats(BaseModel):
    """Schema para estatísticas de carrinho."""
    
    total_carts: int = Field(..., description="Total de carrinhos")
    active_carts: int = Field(..., description="Carrinhos ativos")
    abandoned_carts: int = Field(..., description="Carrinhos abandonados")
    converted_carts: int = Field(..., description="Carrinhos convertidos")
    expired_carts: int = Field(..., description="Carrinhos expirados")
    
    total_value: Decimal = Field(..., description="Valor total dos carrinhos")
    average_cart_value: Decimal = Field(..., description="Valor médio do carrinho")
    conversion_rate: float = Field(..., description="Taxa de conversão (%)")
    abandonment_rate: float = Field(..., description="Taxa de abandono (%)")
    
    model_config = ConfigDict(from_attributes=True)


class AddToCartRequest(BaseModel):
    """Schema para adicionar item ao carrinho."""
    
    product_id: uuid.UUID = Field(..., description="ID do produto")
    quantity: int = Field(1, ge=1, le=999, description="Quantidade")
    selected_variants: Optional[Dict[str, str]] = Field(
        None, description="Variações selecionadas"
    )
    selected_modifiers: Optional[List[str]] = Field(
        None, description="Modificadores selecionados"
    )
    special_instructions: Optional[str] = Field(
        None, max_length=500, description="Instruções especiais"
    )


class UpdateCartItemRequest(BaseModel):
    """Schema para atualizar item do carrinho."""
    
    quantity: int = Field(..., ge=1, le=999, description="Nova quantidade")
    selected_variants: Optional[Dict[str, str]] = Field(
        None, description="Variações atualizadas"
    )
    selected_modifiers: Optional[List[str]] = Field(
        None, description="Modificadores atualizados"
    )
    special_instructions: Optional[str] = Field(
        None, max_length=500, description="Instruções especiais"
    )


class CartResponse(BaseModel):
    """Schema de resposta padrão para operações de carrinho."""
    
    success: bool = Field(..., description="Sucesso da operação")
    message: str = Field(..., description="Mensagem de retorno")
    cart: Optional[CartRead] = Field(None, description="Dados do carrinho")
    
    model_config = ConfigDict(from_attributes=True)
