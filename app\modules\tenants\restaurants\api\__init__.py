from fastapi import APIRouter

# Create a router for all restaurant-related endpoints
restaurants_api_router = APIRouter()

# Import and include other restaurant-related routers here
from app.modules.tenants.restaurants.menu.api import (
    menu_router,
    digital_menu_router,
    public_menu_router,
    groups_router,
)
from app.modules.tenants.restaurants.kds.api.kds_api import router as kds_router
from app.modules.tenants.restaurants.kds.api.kds_auth_api import router as kds_auth_router
from app.modules.tenants.restaurants.table_management.api import table_router
from app.modules.tenants.restaurants.table_reservation.api import reservation_router
from app.modules.tenants.restaurants.tenant_settings.api import settings_router

# Include the menu router
restaurants_api_router.include_router(
    menu_router, prefix="/menu", tags=["Restaurant - Menu Management"]
)

# Include the digital menu router
restaurants_api_router.include_router(
    digital_menu_router, prefix="/menu", tags=["Restaurant - Digital Menu Management"]
)

# Include the groups router
restaurants_api_router.include_router(
    groups_router, prefix="/menu", tags=["Restaurant - Menu Groups"]
)

# Include the KDS router
restaurants_api_router.include_router(
    kds_router, prefix="/kds", tags=["Restaurant - Kitchen Display System"]
)

# Include the KDS Auth router
restaurants_api_router.include_router(
    kds_auth_router, prefix="/kds", tags=["Restaurant - KDS Authentication"]
)

# Include the table management router
restaurants_api_router.include_router(
    table_router, prefix="/tables", tags=["Restaurant - Table Management"]
)

# Include the table reservation router
restaurants_api_router.include_router(
    reservation_router, prefix="/reservations", tags=["Restaurant - Table Reservation"]
)

# Include the tenant settings router
restaurants_api_router.include_router(
    settings_router, prefix="/settings", tags=["Restaurant - Tenant Settings"]
)

# Export the router
__all__ = ["restaurants_api_router"]
