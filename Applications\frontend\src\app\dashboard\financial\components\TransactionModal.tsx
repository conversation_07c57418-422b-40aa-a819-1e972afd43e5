'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Transaction } from '@/types/financial';
import { financialApi } from '@/lib/api/client';
import { mediaUploadService, MediaUploadResponse, UploadProgress } from '@/lib/services/mediaUploadService';
import { Upload, X, FileText, Image, Loader2 } from 'lucide-react';

interface TransactionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (transaction: Partial<Transaction>) => void;
  transaction?: Transaction | null;
}

interface Category {
  id: string;
  name: string;
  category_type: 'income' | 'expense';
}

interface UploadedDocument {
  id: string;
  filename: string;
  file_url: string;
  isUploading?: boolean;
  uploadProgress?: UploadProgress;
}

const TransactionModal: React.FC<TransactionModalProps> = ({ isOpen, onClose, onSubmit, transaction }) => {
  const [formData, setFormData] = useState<Partial<Transaction>>({});
  const [categories, setCategories] = useState<Category[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(false);
  const [uploadedDocuments, setUploadedDocuments] = useState<UploadedDocument[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load categories on mount
  useEffect(() => {
    const loadCategories = async () => {
      setLoadingCategories(true);
      try {
        const response = await financialApi.getCategories();
        setCategories(response);
      } catch (error) {
        console.error('Failed to load categories:', error);
      } finally {
        setLoadingCategories(false);
      }
    };

    if (isOpen) {
      loadCategories();
    }
  }, [isOpen]);

  // Initialize form data
  useEffect(() => {
    if (transaction) {
      setFormData(transaction);
      // Load existing documents if any
      if (transaction.media_uploads) {
        setUploadedDocuments(
          transaction.media_uploads.map((media: any) => ({
            id: media.id,
            filename: media.filename,
            file_url: media.file_url || mediaUploadService.getMediaUrl(media.id),
          }))
        );
      }
    } else {
      setFormData({
        transaction_type: 'expense',
        transaction_date: new Date().toISOString().split('T')[0],
      });
      setUploadedDocuments([]);
    }
  }, [transaction, isOpen]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: keyof Transaction, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // File upload handler
  const handleFileUpload = async (files: FileList) => {
    const fileArray = Array.from(files);

    for (const file of fileArray) {
      // Validate file type (documents only)
      const allowedTypes = [
        'application/pdf',
        'image/jpeg',
        'image/png',
        'image/jpg',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ];

      if (!allowedTypes.includes(file.type)) {
        alert(`File type ${file.type} is not allowed. Please upload PDF, images, or Word documents.`);
        continue;
      }

      // Create temporary document entry
      const tempId = `temp-${Date.now()}-${Math.random()}`;
      const tempDoc: UploadedDocument = {
        id: tempId,
        filename: file.name,
        file_url: '',
        isUploading: true,
        uploadProgress: { loaded: 0, total: file.size, percentage: 0 }
      };

      setUploadedDocuments(prev => [...prev, tempDoc]);

      try {
        // Upload to user context for financial documents
        const response = await mediaUploadService.uploadUserMedia([file], {
          onProgress: (progress) => {
            setUploadedDocuments(prev =>
              prev.map(doc =>
                doc.id === tempId
                  ? { ...doc, uploadProgress: progress }
                  : doc
              )
            );
          }
        });

        if (response.success && response.uploads && response.uploads.length > 0) {
          const upload = response.uploads[0];
          setUploadedDocuments(prev =>
            prev.map(doc =>
              doc.id === tempId
                ? {
                    id: upload.id,
                    filename: upload.filename,
                    file_url: upload.file_url,
                    isUploading: false
                  }
                : doc
            )
          );
        } else {
          throw new Error('Upload failed');
        }
      } catch (error) {
        console.error('Upload failed:', error);
        setUploadedDocuments(prev => prev.filter(doc => doc.id !== tempId));
        alert(`Failed to upload ${file.name}`);
      }
    }
  };

  // Remove document
  const handleRemoveDocument = (documentId: string) => {
    setUploadedDocuments(prev => prev.filter(doc => doc.id !== documentId));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Include uploaded documents in the transaction data
      const transactionData = {
        ...formData,
        media_uploads: uploadedDocuments.filter(doc => !doc.isUploading).map(doc => ({ id: doc.id }))
      };

      await onSubmit(transactionData);

      // Reset form on successful submission
      setFormData({
        transaction_type: 'expense',
        transaction_date: new Date().toISOString().split('T')[0],
      });
      setUploadedDocuments([]);
    } catch (error) {
      console.error('Failed to submit transaction:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Filter categories based on transaction type
  const filteredCategories = categories.filter(
    cat => cat.category_type === formData.transaction_type
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[525px] bg-white/90 backdrop-blur-lg border-white/30">
        <DialogHeader>
          <DialogTitle>{transaction ? 'Edit Transaction' : 'New Transaction'}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="transaction_type">Type</Label>
              <Select
                value={formData.transaction_type}
                onValueChange={(value) => handleSelectChange('transaction_type', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="expense">Expense</SelectItem>
                  <SelectItem value="income">Income</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="amount">Amount</Label>
              <Input
                id="amount"
                name="amount"
                type="number"
                value={formData.amount || ''}
                onChange={handleChange}
                placeholder="0.00"
                required
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Input
              id="description"
              name="description"
              value={formData.description || ''}
              onChange={handleChange}
              placeholder="e.g., Office supplies"
              required
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="transaction_date">Date</Label>
              <Input
                id="transaction_date"
                name="transaction_date"
                type="date"
                value={formData.transaction_date || ''}
                onChange={handleChange}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="category_id">Category</Label>
              <Select
                value={formData.category_id || ''}
                onValueChange={(value) => handleSelectChange('category_id', value)}
                disabled={loadingCategories}
              >
                <SelectTrigger>
                  <SelectValue placeholder={loadingCategories ? "Loading categories..." : "Select category"} />
                </SelectTrigger>
                <SelectContent>
                  {filteredCategories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                  {filteredCategories.length === 0 && !loadingCategories && (
                    <SelectItem value="" disabled>
                      No categories available for {formData.transaction_type}
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
                id="notes"
                name="notes"
                value={formData.notes || ''}
                onChange={handleChange}
                placeholder="Add any extra details..."
            />
          </div>
          <div className="space-y-2">
            <Label>Documents & Receipts</Label>

            {/* File Upload Area */}
            <div className="space-y-3">
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors cursor-pointer"
                onClick={() => document.getElementById('file-upload')?.click()}
                onDragOver={(e) => e.preventDefault()}
                onDrop={(e) => {
                  e.preventDefault();
                  const files = e.dataTransfer.files;
                  if (files.length > 0) {
                    handleFileUpload(files);
                  }
                }}
              >
                <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600">
                  Click to upload or drag and drop
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  PDF, Images, Word documents (max 10MB each)
                </p>
              </div>

              <input
                id="file-upload"
                type="file"
                multiple
                accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                className="hidden"
                onChange={(e) => {
                  if (e.target.files) {
                    handleFileUpload(e.target.files);
                  }
                }}
              />

              {/* Uploaded Documents List */}
              {uploadedDocuments.length > 0 && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Uploaded Documents:</Label>
                  {uploadedDocuments.map((doc) => (
                    <div
                      key={doc.id}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
                    >
                      <div className="flex items-center space-x-3">
                        {doc.filename.toLowerCase().includes('.pdf') ? (
                          <FileText className="h-5 w-5 text-red-500" />
                        ) : (
                          <Image className="h-5 w-5 text-blue-500" aria-label="Imagem" />
                        )}
                        <div>
                          <p className="text-sm font-medium text-gray-900">{doc.filename}</p>
                          {doc.isUploading && doc.uploadProgress && (
                            <div className="flex items-center space-x-2">
                              <div className="w-32 bg-gray-200 rounded-full h-2">
                                <div
                                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                  style={{ width: `${doc.uploadProgress.percentage}%` }}
                                />
                              </div>
                              <span className="text-xs text-gray-500">
                                {doc.uploadProgress.percentage}%
                              </span>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        {doc.isUploading ? (
                          <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
                        ) : (
                          <button
                            type="button"
                            onClick={() => handleRemoveDocument(doc.id)}
                            className="text-gray-400 hover:text-red-500 transition-colors"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" variant="outline" disabled={isSubmitting}>
                Cancel
              </Button>
            </DialogClose>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  {transaction ? 'Saving...' : 'Creating...'}
                </>
              ) : (
                transaction ? 'Save Changes' : 'Create Transaction'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default TransactionModal; 