"""
Review Moderation Models

Sistema de moderação e denúncias para reviews.
"""

from datetime import datetime
from enum import Enum
from typing import Optional
from uuid import UUID, uuid4

from sqlalchemy import Column, String, Text, DateTime, ForeignKey, Boolean
from sqlalchemy.dialects.postgresql import UUID as PostgreSQLUUID
from sqlalchemy.orm import relationship

from app.db.base import Base


class ReportReason(str, Enum):
    """Motivos de denúncia"""
    INAPPROPRIATE_LANGUAGE = "inappropriate_language"
    SPAM = "spam"
    FAKE_REVIEW = "fake_review"
    OFF_TOPIC = "off_topic"
    PERSONAL_ATTACK = "personal_attack"
    MISLEADING_INFO = "misleading_info"
    OTHER = "other"


class ReportStatus(str, Enum):
    """Status da denúncia"""
    PENDING = "pending"
    REVIEWED = "reviewed"
    DISMISSED = "dismissed"
    ACTION_TAKEN = "action_taken"


class ReviewReport(Base):
    """
    Modelo para denúncias de reviews.
    
    Permite que usuários denunciem reviews inadequadas.
    """
    __tablename__ = "eshop_review_reports"

    id: UUID = Column(
        PostgreSQLUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        index=True
    )
    
    # Relacionamentos
    review_id: UUID = Column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("eshop_reviews.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    reporter_id: UUID = Column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    # Dados da denúncia
    reason: ReportReason = Column(String(50), nullable=False)
    description: Optional[str] = Column(Text, nullable=True)
    status: ReportStatus = Column(String(20), default=ReportStatus.PENDING, nullable=False)
    
    # Timestamps
    created_at: datetime = Column(DateTime, default=datetime.utcnow, nullable=False)
    reviewed_at: Optional[datetime] = Column(DateTime, nullable=True)
    
    # Moderação
    reviewed_by: Optional[UUID] = Column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="SET NULL"),
        nullable=True
    )
    moderator_notes: Optional[str] = Column(Text, nullable=True)
    action_taken: Optional[str] = Column(String(100), nullable=True)
    
    # Relacionamentos SQLAlchemy
    review = relationship("Review")
    reporter = relationship("User", foreign_keys=[reporter_id])
    moderator = relationship("User", foreign_keys=[reviewed_by])
    
    def __repr__(self) -> str:
        return f"<ReviewReport(id={self.id}, review_id={self.review_id}, reason={self.reason})>"
    
    def mark_reviewed(self, moderator_id: UUID, action: str, notes: str = None) -> None:
        """Marca a denúncia como revisada"""
        self.status = ReportStatus.REVIEWED
        self.reviewed_by = moderator_id
        self.reviewed_at = datetime.utcnow()
        self.action_taken = action
        if notes:
            self.moderator_notes = notes
    
    def dismiss(self, moderator_id: UUID, notes: str = None) -> None:
        """Descarta a denúncia"""
        self.status = ReportStatus.DISMISSED
        self.reviewed_by = moderator_id
        self.reviewed_at = datetime.utcnow()
        self.action_taken = "dismissed"
        if notes:
            self.moderator_notes = notes


class ReviewHelpfulness(Base):
    """
    Modelo para votos de utilidade de reviews.
    
    Permite que usuários marquem reviews como úteis ou não úteis.
    """
    __tablename__ = "eshop_review_helpfulness"

    id: UUID = Column(
        PostgreSQLUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        index=True
    )
    
    # Relacionamentos
    review_id: UUID = Column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("eshop_reviews.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    user_id: UUID = Column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    # Dados do voto
    is_helpful: bool = Column(Boolean, nullable=False)
    created_at: datetime = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relacionamentos SQLAlchemy
    review = relationship("Review")
    user = relationship("User")
    
    def __repr__(self) -> str:
        return f"<ReviewHelpfulness(review_id={self.review_id}, user_id={self.user_id}, helpful={self.is_helpful})>"


class ModerationAction(Base):
    """
    Modelo para histórico de ações de moderação.
    
    Registra todas as ações tomadas pelos moderadores.
    """
    __tablename__ = "eshop_moderation_actions"

    id: UUID = Column(
        PostgreSQLUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        index=True
    )
    
    # Relacionamentos
    review_id: UUID = Column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("eshop_reviews.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    moderator_id: UUID = Column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    # Dados da ação
    action_type: str = Column(String(50), nullable=False)  # hide_text, hide_complete, restore
    reason: str = Column(Text, nullable=False)
    previous_status: str = Column(String(20), nullable=False)
    new_status: str = Column(String(20), nullable=False)
    
    # Timestamp
    created_at: datetime = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relacionamentos SQLAlchemy
    review = relationship("Review")
    moderator = relationship("User")
    
    def __repr__(self) -> str:
        return f"<ModerationAction(id={self.id}, action={self.action_type}, review_id={self.review_id})>"