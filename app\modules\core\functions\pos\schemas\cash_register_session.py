"""Schemas for cash register sessions."""

import uuid  # noqa: E402
from typing import Optional
from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel, ConfigDict, Field

# --- CashRegisterSession Schemas ---

# Base schema for opening a cash register session


class CashRegisterSessionOpenBase(BaseModel):
    """Base schema for opening a cash register session."""

    opening_balance: Decimal = Field(..., max_digits=10, decimal_places=2, ge=0)
    opening_notes: Optional[str] = None


# Schema for creating a new cash register session


class CashRegisterSessionOpen(CashRegisterSessionOpenBase):
    """Schema for opening a cash register session."""

    pass  # operator_id and cash_register_id will be added from the endpoint context


# Base schema for closing a cash register session


class CashRegisterSessionCloseBase(BaseModel):
    """Base schema for closing a cash register session."""

    closing_balance: Decimal = Field(..., max_digits=10, decimal_places=2, ge=0)
    closing_notes: Optional[str] = None
    actual_cash_amount: Decimal = Field(..., max_digits=10, decimal_places=2, ge=0)
    discrepancy_reason: Optional[str] = None


# Schema for closing an existing cash register session


class CashRegisterSessionClose(CashRegisterSessionCloseBase):
    """Schema for closing a cash register session."""

    pass  # The session ID will come from the URL path


# Schema for reading a cash register session


class CashRegisterSessionRead(BaseModel):
    """Schema for reading a cash register session."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    cash_register_id: uuid.UUID
    operator_id: uuid.UUID
    is_open: bool

    # Opening details
    opening_timestamp: datetime
    opening_balance: Decimal
    opening_notes: Optional[str] = None

    # Closing details (may be None if session is still open)
    closing_timestamp: Optional[datetime] = None
    closing_balance: Optional[Decimal] = None
    closing_notes: Optional[str] = None

    # Reconciliation details (may be None if session is still open)
    expected_cash_amount: Optional[Decimal] = None
    actual_cash_amount: Optional[Decimal] = None
    discrepancy_amount: Optional[Decimal] = None
    discrepancy_reason: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)
