"""Allergen model for food allergen management."""

import uuid
from sqlalchemy import Column, String, Text, Boolean, ForeignKey
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.orm import relationship

from app.db.base import Base


class Allergen(Base):
    """
    Model for storing food allergens.
    
    This model stores the master list of allergens that can be assigned
    to menu items. Only system administrators can create/modify allergens.
    """
    
    __tablename__ = "allergens"
    
    id = Column(
        PG_UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        index=True
    )
    # tenant_id removed - allergens are global in the current system
    # TODO: Add tenant_id support when implementing tenant-specific allergens
    name = Column(
        String(100), 
        nullable=False, 
        unique=True, 
        index=True,
        comment="Name of the allergen (e.g., 'Glúten', 'Lactose')"
    )
    icon = Column(
        String(10), 
        nullable=True,
        comment="Emoji or icon representation (e.g., '🌾', '🥛')"
    )
    description = Column(
        Text, 
        nullable=True,
        comment="Detailed description of the allergen"
    )
    is_active = Column(
        Boolean, 
        default=True, 
        nullable=False,
        comment="Whether this allergen is active and available for use"
    )
    
    # Relationship with menu items (many-to-many)
    # This will be defined in the association table
    menu_items = relationship(
        "MenuItem",
        secondary="menu_item_allergens",
        back_populates="allergens",
        viewonly=True  # Managed through the association table
    )
    
    def __repr__(self) -> str:
        return f"<Allergen(id={self.id}, name='{self.name}', icon='{self.icon}')>"
    
    def __str__(self) -> str:
        return f"{self.icon} {self.name}" if self.icon else self.name
