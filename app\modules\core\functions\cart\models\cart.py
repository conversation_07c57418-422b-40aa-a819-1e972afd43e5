"""
Cart Models for EShop System
============================

Modelos para carrinho de compras persistente com suporte a sessões de usuário,
produtos do eshop, customizações e integração com sistema B2B/B2C.
"""

import uuid
import enum
from datetime import datetime, timed<PERSON>ta
from typing import TYPE_CHECKING, Optional, List
from decimal import Decimal

from sqlalchemy import (
    Column, String, ForeignKey, Text, Enum, DateTime, 
    Boolean, Index, func, Numeric, Integer, JSON
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, Mapped, mapped_column

from app.db.base import Base

# Use TYPE_CHECKING to handle circular dependencies
if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.core.users.models.user import User
    from app.modules.core.eshop.models.product import Product


class CartStatus(str, enum.Enum):
    """Status do carrinho de compras."""
    
    ACTIVE = "active"
    ABANDONED = "abandoned"
    CONVERTED = "converted"
    EXPIRED = "expired"


class Cart(Base):
    """
    Modelo para carrinho de compras persistente.
    
    Suporta carrinho para usuários logados e sessões anônimas,
    com expiração automática e conversão para pedidos.
    """
    
    __tablename__ = "eshop_carts"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("tenants.id"), 
        nullable=False, 
        index=True
    )
    
    # Usuário (opcional para carrinho anônimo)
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=True,
        index=True
    )
    
    # Identificador de sessão para carrinho anônimo
    session_id = Column(String(255), nullable=True, index=True)
    
    # Status e configurações
    status = Column(
        Enum(CartStatus), 
        default=CartStatus.ACTIVE, 
        nullable=False, 
        index=True
    )
    
    # Contexto de mercado (B2B/B2C)
    market_context = Column(String(20), nullable=False, default='b2c')
    
    # Totais calculados
    subtotal = Column(Numeric(10, 2), default=0.00, nullable=False)
    tax_amount = Column(Numeric(10, 2), default=0.00, nullable=False)
    discount_amount = Column(Numeric(10, 2), default=0.00, nullable=False)
    shipping_amount = Column(Numeric(10, 2), default=0.00, nullable=False)
    total_amount = Column(Numeric(10, 2), default=0.00, nullable=False)
    
    # Metadados e configurações
    currency = Column(String(3), default='BRL', nullable=False)
    notes = Column(Text, nullable=True)
    cart_metadata = Column(JSON, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )
    expires_at = Column(DateTime, nullable=True)
    converted_at = Column(DateTime, nullable=True)
    
    # Relacionamentos
    tenant = relationship("Tenant")
    user = relationship("User")
    items = relationship(
        "CartItem", 
        back_populates="cart", 
        cascade="all, delete-orphan",
        order_by="CartItem.created_at"
    )
    
    # Índices para performance
    __table_args__ = (
        Index("ix_carts_tenant_user", "tenant_id", "user_id"),
        Index("ix_carts_tenant_session", "tenant_id", "session_id"),
        Index("ix_carts_status_expires", "status", "expires_at"),
        Index("ix_carts_market_context", "market_context"),
    )
    
    def __repr__(self):
        return (
            f"<Cart(id={self.id}, "
            f"user_id={self.user_id}, "
            f"status='{self.status}', "
            f"total={self.total_amount})>"
        )
    
    @property
    def is_expired(self) -> bool:
        """Verifica se o carrinho está expirado."""
        if self.expires_at is None:
            return False
        return datetime.utcnow() > self.expires_at
    
    @property
    def item_count(self) -> int:
        """Retorna o número total de itens no carrinho."""
        return sum(item.quantity for item in self.items)
    
    def set_expiration(self, hours: int = 24):
        """Define a expiração do carrinho."""
        self.expires_at = datetime.utcnow() + timedelta(hours=hours)


class CartItem(Base):
    """
    Item individual do carrinho de compras.
    
    Representa um produto específico com quantidade,
    customizações e preços calculados.
    """
    
    __tablename__ = "eshop_cart_items"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    cart_id = Column(
        UUID(as_uuid=True),
        ForeignKey("eshop_carts.id"),
        nullable=False,
        index=True
    )
    
    # Produto do eshop
    product_id = Column(
        UUID(as_uuid=True),
        ForeignKey("eshop_products.id"),
        nullable=False,
        index=True
    )
    
    # Detalhes do item
    quantity = Column(Integer, nullable=False, default=1)
    unit_price = Column(Numeric(10, 2), nullable=False)
    total_price = Column(Numeric(10, 2), nullable=False)
    
    # Customizações e variações
    selected_variants = Column(JSON, nullable=True)  # {variant_group_id: option_id}
    selected_modifiers = Column(JSON, nullable=True)  # [modifier_option_ids]
    special_instructions = Column(Text, nullable=True)
    
    # Metadados
    item_metadata = Column(JSON, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )
    
    # Relacionamentos
    cart = relationship("Cart", back_populates="items")
    product = relationship("app.modules.core.eshop.models.product.Product")
    
    # Índices para performance
    __table_args__ = (
        Index("ix_cart_items_cart_product", "cart_id", "product_id"),
        Index("ix_cart_items_created", "created_at"),
    )
    
    def __repr__(self):
        return (
            f"<CartItem(id={self.id}, "
            f"product_id={self.product_id}, "
            f"quantity={self.quantity}, "
            f"total={self.total_price})>"
        )
    
    def calculate_total(self):
        """Calcula o preço total baseado na quantidade e preço unitário."""
        self.total_price = self.unit_price * self.quantity
        return self.total_price
