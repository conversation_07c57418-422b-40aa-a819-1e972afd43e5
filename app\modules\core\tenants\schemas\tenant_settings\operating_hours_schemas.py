"""
Operating Hours specific schemas for tenant settings.
"""

from typing import Optional, List, Dict, Any

from pydantic import BaseModel, Field, validator


class TimeSlotSchema(BaseModel):
    """Schema for individual time slots."""

    id: str = Field(..., description="Unique identifier for the time slot")
    open: str = Field(..., pattern=r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$', description="Opening time in HH:MM format")
    close: str = Field(..., pattern=r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$', description="Closing time in HH:MM format")
    type: str = Field(..., description="Type of time slot: service, break, or happy_hour")
    label: Optional[str] = Field(None, max_length=100, description="Optional label for the time slot")
    description: Optional[str] = Field(None, max_length=200, description="Optional description")

    @validator('type')
    def validate_type(cls, v):
        """Validate time slot type."""
        allowed_types = ['service', 'break', 'happy_hour']
        if v not in allowed_types:
            raise ValueError(f'Time slot type must be one of: {allowed_types}')
        return v

    @validator('close')
    def validate_close_after_open(cls, v, values):
        """Validate that close time is after open time."""
        if 'open' in values and v <= values['open']:
            raise ValueError('Close time must be after open time')
        return v


class DayScheduleSchema(BaseModel):
    """Schema for a day's complete schedule."""

    is_open: bool = Field(False, description="Whether the business is open on this day")
    service_hours: List[TimeSlotSchema] = Field(default_factory=list, description="Main service hours")
    break_periods: List[TimeSlotSchema] = Field(default_factory=list, description="Break periods when service is unavailable")
    happy_hour: List[TimeSlotSchema] = Field(default_factory=list, description="Happy hour periods with special pricing")

    # Legacy support for backward compatibility
    slots: Optional[List[Dict[str, Any]]] = Field(None, description="Legacy slots format (deprecated)")


class OperatingHoursSchema(BaseModel):
    """Schema for complete operating hours configuration."""

    monday: Optional[DayScheduleSchema] = None
    tuesday: Optional[DayScheduleSchema] = None
    wednesday: Optional[DayScheduleSchema] = None
    thursday: Optional[DayScheduleSchema] = None
    friday: Optional[DayScheduleSchema] = None
    saturday: Optional[DayScheduleSchema] = None
    sunday: Optional[DayScheduleSchema] = None


class OperatingHoursUpdate(BaseModel):
    """Schema for updating operating hours."""
    
    operating_hours: Optional[Dict[str, Any]] = Field(
        None, 
        description="Operating hours configuration by day of week"
    )
