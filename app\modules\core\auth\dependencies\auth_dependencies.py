"""
Authentication dependencies for the FastAPI application.
Handles JWT token validation, user authentication, and role-based access control.
"""

import logging
from typing import Optional, TYPE_CHECKING

from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db

# Use TYPE_CHECKING to avoid circular import
if TYPE_CHECKING:
    from app.modules.core.users.models.user import User

from app.modules.core.auth.security.token_utils import decode_token
from app.modules.core.users.services.user_service import user_service

logger = logging.getLogger(__name__)

# HTTP Bearer token scheme
security = HTTPBearer()


async def get_current_user(
    db: AsyncSession = Depends(get_db),
    credentials: HTTPAuthorizationCredentials = Depends(security),
) -> Optional["User"]:
    """
    Dependency to get the current user from the JWT token.
    
    Args:
        db: Database session
        credentials: HTTP authorization credentials containing the JWT token
        
    Returns:
        User object if valid token and user exists, None otherwise
        
    Raises:
        HTTPException: If token is invalid or user not found
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        # Decode the JWT token
        token_payload = decode_token(credentials.credentials)
        if token_payload is None:
            logger.warning("Invalid token: Could not decode")
            raise credentials_exception

        user_id: str = token_payload.sub
        if user_id is None:
            logger.warning("Invalid token: No user ID in payload")
            raise credentials_exception

        logger.debug(f"Token decoded successfully for user ID: {user_id}")

    except Exception as e:
        logger.error(f"Token validation error: {e}")
        raise credentials_exception

    # Get user from database
    user = await user_service.get_user_by_id(db, user_id=user_id)
    if user is None:
        logger.warning(f"User not found for ID: {user_id}")
        raise credentials_exception

    logger.debug(f"User authenticated: {user.email} (ID: {user.id})")
    return user


async def get_current_active_user(
    current_user: "User" = Depends(get_current_user),
) -> "User":
    """
    Dependency to get the current active user.
    
    Args:
        current_user: Current user from get_current_user dependency
        
    Returns:
        User object if user is active
        
    Raises:
        HTTPException: If user is inactive
    """
    if not current_user.is_active:
        logger.warning(f"Inactive user attempted access: {current_user.email}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    
    return current_user


async def get_current_admin_user(
    current_user: "User" = Depends(get_current_active_user),
) -> "User":
    """
    Dependency to get the current user and verify they are an admin.
    
    Args:
        current_user: Current active user
        
    Returns:
        User object if the user is an admin
        
    Raises:
        HTTPException: If the user is not an admin
    """
    if current_user.system_role != "ADMIN":
        logger.warning(
            f"Non-admin user {current_user.email} attempted to access an admin-only endpoint."
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="The user does not have sufficient privileges for this operation.",
        )
    return current_user


async def get_optional_current_user(
    db: AsyncSession = Depends(get_db),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False)),
) -> Optional["User"]:
    """
    Dependency to optionally get the current user from the JWT token.
    Does not raise an exception if no token is provided or if the token is invalid.
    
    Args:
        db: Database session
        credentials: Optional HTTP authorization credentials containing the JWT token
        
    Returns:
        User object if valid token and user exists, None otherwise
    """
    if credentials is None:
        return None

    try:
        # Decode the JWT token
        token_payload = decode_token(credentials.credentials)
        if token_payload is None:
            return None

        user_id: str = token_payload.sub
        if user_id is None:
            return None

        # Get user from database
        user = await user_service.get_user_by_id(db, user_id=user_id)
        if user is None or not user.is_active:
            return None

        return user

    except Exception as e:
        logger.debug(f"Optional user authentication failed: {e}")
        return None


async def get_current_user_from_token(request) -> Optional["User"]:
    """
    Extract user from JWT token in request headers.
    Used by middleware to get user without FastAPI dependency injection.
    
    Args:
        request: FastAPI Request object
        
    Returns:
        User object if valid token and user exists, None otherwise
    """
    try:
        # Extract token from Authorization header
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            return None
            
        token = auth_header.split(" ")[1]
        
        # Decode the JWT token
        token_payload = decode_token(token)
        if token_payload is None:
            return None

        user_id: str = token_payload.sub
        if user_id is None:
            return None

        # Get database session using async context manager
        from app.core.db_dependencies import get_async_session
        async with get_async_session() as db:
            # Get user from database
            user = await user_service.get_user_by_id(db, user_id=user_id)
            if user is None or not user.is_active:
                return None

            return user

    except Exception as e:
        logger.debug(f"Token extraction from request failed: {e}")
        return None
