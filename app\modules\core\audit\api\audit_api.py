"""
Audit API

API endpoints for querying and managing audit logs.
"""

import logging
from typing import Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_db
from app.modules.core.auth.dependencies.auth_dependencies import get_current_user
from app.modules.core.users.models.user import User
from app.modules.core.audit.services.audit_service import AuditService
from app.modules.core.audit.schemas.audit import (
    AuditAction, AuditResource, OperationType, AuditStatus, SecurityLevel,
    AuditLogFilter, AuditLogListResponse, AuditLogStats, AuditLogRead
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/audit", tags=["Audit"])


def get_audit_service(db: AsyncSession = Depends(get_db)) -> AuditService:
    """Get audit service instance."""
    return AuditService(db)


@router.get("/logs", response_model=AuditLogListResponse)
async def get_audit_logs(
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    tenant_id: Optional[str] = Query(None, description="Filter by tenant ID"),
    action: Optional[AuditAction] = Query(None, description="Filter by action"),
    resource: Optional[AuditResource] = Query(None, description="Filter by resource"),
    resource_id: Optional[str] = Query(None, description="Filter by resource ID"),
    operation_type: Optional[OperationType] = Query(None, description="Filter by operation type"),
    status: Optional[AuditStatus] = Query(None, description="Filter by status"),
    security_level: Optional[SecurityLevel] = Query(None, description="Filter by security level"),
    start_date: Optional[datetime] = Query(None, description="Start date for filtering"),
    end_date: Optional[datetime] = Query(None, description="End date for filtering"),
    search_term: Optional[str] = Query(None, description="Search term"),
    ip_address: Optional[str] = Query(None, description="Filter by IP address"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    sort_by: str = Query("timestamp", description="Field to sort by"),
    sort_order: str = Query("desc", pattern="^(asc|desc)$", description="Sort order"),
    audit_service: AuditService = Depends(get_audit_service),
    current_user: User = Depends(get_current_user),
):
    """
    Get audit logs with comprehensive filtering.
    
    Requires admin role or appropriate permissions.
    """
    try:
        # Check permissions (only admins can view all audit logs)
        if not current_user.is_admin:
            # Non-admin users can only see their own audit logs
            user_id = str(current_user.id)
        
        # Create filter object
        filters = AuditLogFilter(
            user_id=user_id,
            tenant_id=tenant_id,
            action=action,
            resource=resource,
            resource_id=resource_id,
            operation_type=operation_type,
            status=status,
            security_level=security_level,
            start_date=start_date,
            end_date=end_date,
            search_term=search_term,
            ip_address=ip_address,
            skip=skip,
            limit=limit,
            sort_by=sort_by,
            sort_order=sort_order,
        )
        
        return await audit_service.get_audit_logs(filters)
        
    except Exception as e:
        logger.error(f"Error getting audit logs: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/logs/{audit_id}", response_model=AuditLogRead)
async def get_audit_log(
    audit_id: str,
    audit_service: AuditService = Depends(get_audit_service),
    current_user: User = Depends(get_current_user),
):
    """
    Get a specific audit log entry by ID.
    
    Requires admin role or ownership of the audit log.
    """
    try:
        # Get the audit log
        filters = AuditLogFilter(skip=0, limit=1)
        # This would need to be implemented in the service
        # For now, return a simple response
        
        # TODO: Implement get_audit_log_by_id in service
        raise HTTPException(status_code=501, detail="Not implemented yet")
        
    except Exception as e:
        logger.error(f"Error getting audit log {audit_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/stats", response_model=AuditLogStats)
async def get_audit_stats(
    start_date: Optional[datetime] = Query(None, description="Start date for stats"),
    end_date: Optional[datetime] = Query(None, description="End date for stats"),
    tenant_id: Optional[str] = Query(None, description="Filter by tenant ID"),
    audit_service: AuditService = Depends(get_audit_service),
    current_user: User = Depends(get_current_user),
):
    """
    Get audit statistics and metrics.
    
    Requires admin role.
    """
    try:
        # Check permissions
        if not current_user.is_admin:
            raise HTTPException(status_code=403, detail="Admin access required")
        
        # Default to last 30 days if no dates provided
        if not end_date:
            end_date = datetime.utcnow()
        if not start_date:
            start_date = end_date - timedelta(days=30)
        
        # TODO: Implement get_audit_stats in service
        return AuditLogStats(
            total_entries=0,
            success_count=0,
            failure_count=0,
            pending_count=0,
            resource_breakdown={},
            action_breakdown={},
            recent_activity_count=0,
            sensitive_operations_count=0,
            critical_operations_count=0,
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting audit stats: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/user/{user_id}/logs", response_model=AuditLogListResponse)
async def get_user_audit_logs(
    user_id: str,
    start_date: Optional[datetime] = Query(None, description="Start date for filtering"),
    end_date: Optional[datetime] = Query(None, description="End date for filtering"),
    action: Optional[AuditAction] = Query(None, description="Filter by action"),
    resource: Optional[AuditResource] = Query(None, description="Filter by resource"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    audit_service: AuditService = Depends(get_audit_service),
    current_user: User = Depends(get_current_user),
):
    """
    Get audit logs for a specific user.
    
    Users can only see their own logs unless they are admin.
    """
    try:
        # Check permissions
        if not current_user.is_admin and str(current_user.id) != user_id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Create filter object
        filters = AuditLogFilter(
            user_id=user_id,
            start_date=start_date,
            end_date=end_date,
            action=action,
            resource=resource,
            skip=skip,
            limit=limit,
            sort_by="timestamp",
            sort_order="desc",
        )
        
        return await audit_service.get_audit_logs(filters)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user audit logs: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/resource/{resource}/{resource_id}/logs", response_model=AuditLogListResponse)
async def get_resource_audit_logs(
    resource: AuditResource,
    resource_id: str,
    start_date: Optional[datetime] = Query(None, description="Start date for filtering"),
    end_date: Optional[datetime] = Query(None, description="End date for filtering"),
    action: Optional[AuditAction] = Query(None, description="Filter by action"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    audit_service: AuditService = Depends(get_audit_service),
    current_user: User = Depends(get_current_user),
):
    """
    Get audit logs for a specific resource.
    
    Requires admin role or appropriate resource permissions.
    """
    try:
        # Check permissions (basic check - could be more granular)
        if not current_user.is_admin:
            raise HTTPException(status_code=403, detail="Admin access required")
        
        # Create filter object
        filters = AuditLogFilter(
            resource=resource,
            resource_id=resource_id,
            start_date=start_date,
            end_date=end_date,
            action=action,
            skip=skip,
            limit=limit,
            sort_by="timestamp",
            sort_order="desc",
        )
        
        return await audit_service.get_audit_logs(filters)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting resource audit logs: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")
