"""
Testes para o WebSocket do Help Center.
"""

import pytest
import json
from unittest.mock import AsyncMock, MagicMock
from uuid import uuid4

from app.modules.core.help_center.websockets.help_center_ws import HelpCenterWebSocketManager


@pytest.fixture
def ws_manager():
    """Fixture para WebSocket manager."""
    return HelpCenterWebSocketManager()


@pytest.fixture
def mock_websocket():
    """Fixture para WebSocket mock."""
    ws = AsyncMock()
    ws.send_text = AsyncMock()
    ws.close = AsyncMock()
    return ws


@pytest.fixture
def test_user_id():
    """Fixture para ID de usuário de teste."""
    return str(uuid4())


@pytest.fixture
def test_ticket_id():
    """Fixture para ID de ticket de teste."""
    return str(uuid4())


class TestHelpCenterWebSocketManager:
    """Testes para o HelpCenterWebSocketManager."""

    async def test_connect_user(self, ws_manager, mock_websocket, test_user_id):
        """Testa conexão de usuário."""
        await ws_manager.connect(mock_websocket, test_user_id)
        
        assert test_user_id in ws_manager.active_connections
        assert mock_websocket in ws_manager.active_connections[test_user_id]

    async def test_disconnect_user(self, ws_manager, mock_websocket, test_user_id):
        """Testa desconexão de usuário."""
        # Conectar primeiro
        await ws_manager.connect(mock_websocket, test_user_id)
        assert test_user_id in ws_manager.active_connections
        
        # Desconectar
        await ws_manager.disconnect(mock_websocket, test_user_id)
        
        # Verificar se foi removido
        assert test_user_id not in ws_manager.active_connections or \
               mock_websocket not in ws_manager.active_connections.get(test_user_id, [])

    async def test_join_ticket_room(self, ws_manager, mock_websocket, test_user_id, test_ticket_id):
        """Testa entrada em sala de ticket."""
        await ws_manager.connect(mock_websocket, test_user_id)
        await ws_manager.join_ticket(test_user_id, test_ticket_id)
        
        assert test_ticket_id in ws_manager.ticket_rooms
        assert test_user_id in ws_manager.ticket_rooms[test_ticket_id]

    async def test_leave_ticket_room(self, ws_manager, mock_websocket, test_user_id, test_ticket_id):
        """Testa saída de sala de ticket."""
        # Conectar e entrar na sala
        await ws_manager.connect(mock_websocket, test_user_id)
        await ws_manager.join_ticket(test_user_id, test_ticket_id)
        
        # Sair da sala
        await ws_manager.leave_ticket(test_user_id, test_ticket_id)
        
        # Verificar se foi removido
        assert test_user_id not in ws_manager.ticket_rooms.get(test_ticket_id, set())

    async def test_send_personal_message(self, ws_manager, mock_websocket, test_user_id):
        """Testa envio de mensagem pessoal."""
        await ws_manager.connect(mock_websocket, test_user_id)
        
        message = {"type": "test", "content": "Hello"}
        await ws_manager.send_personal_message(test_user_id, message)
        
        # Verificar se a mensagem foi enviada
        mock_websocket.send_text.assert_called_once()
        sent_data = mock_websocket.send_text.call_args[0][0]
        assert json.loads(sent_data) == message

    async def test_broadcast_to_ticket(self, ws_manager, test_ticket_id):
        """Testa broadcast para ticket."""
        # Criar múltiplos usuários conectados ao ticket
        user_ids = [str(uuid4()) for _ in range(3)]
        websockets = [AsyncMock() for _ in range(3)]
        
        for user_id, ws in zip(user_ids, websockets):
            await ws_manager.connect(ws, user_id)
            await ws_manager.join_ticket(user_id, test_ticket_id)
        
        message = {"type": "broadcast", "content": "Message to all"}
        await ws_manager.broadcast_to_ticket(test_ticket_id, message)
        
        # Verificar se todos receberam a mensagem
        for ws in websockets:
            ws.send_text.assert_called_once()

    async def test_emit_ticket_created(self, ws_manager):
        """Testa emissão de evento de ticket criado."""
        ticket_data = {
            "id": str(uuid4()),
            "title": "New Ticket",
            "user_id": str(uuid4())
        }
        
        # Mock do método broadcast_to_admins
        ws_manager.broadcast_to_admins = AsyncMock()
        
        await ws_manager.emit_ticket_created(ticket_data)
        
        # Verificar se foi chamado
        ws_manager.broadcast_to_admins.assert_called_once()

    async def test_emit_message_sent(self, ws_manager, test_ticket_id):
        """Testa emissão de evento de mensagem enviada."""
        message_data = {
            "id": str(uuid4()),
            "content": "Test message",
            "sender_id": str(uuid4())
        }
        
        # Mock do método broadcast_to_ticket
        ws_manager.broadcast_to_ticket = AsyncMock()
        
        await ws_manager.emit_message_sent(test_ticket_id, message_data, message_data["sender_id"])
        
        # Verificar se foi chamado
        ws_manager.broadcast_to_ticket.assert_called_once_with(
            test_ticket_id,
            {
                "type": "new_message",
                "message": message_data
            },
            exclude_user=message_data["sender_id"]
        )

    async def test_emit_typing_indicator(self, ws_manager, test_ticket_id):
        """Testa emissão de indicador de digitação."""
        user_id = str(uuid4())
        
        # Mock do método broadcast_to_ticket
        ws_manager.broadcast_to_ticket = AsyncMock()
        
        await ws_manager.emit_typing_indicator(test_ticket_id, user_id, "Test User", True)
        
        # Verificar se foi chamado
        ws_manager.broadcast_to_ticket.assert_called_once_with(
            test_ticket_id,
            {
                "type": "typing_indicator",
                "typing": {
                    "ticket_id": test_ticket_id,
                    "user_id": user_id,
                    "user_name": "Test User",
                    "is_typing": True
                }
            },
            exclude_user=user_id
        )

    async def test_emit_read_receipt(self, ws_manager, test_ticket_id):
        """Testa emissão de confirmação de leitura."""
        message_id = str(uuid4())
        user_id = str(uuid4())
        
        # Mock do método broadcast_to_ticket
        ws_manager.broadcast_to_ticket = AsyncMock()
        
        await ws_manager.emit_read_receipt(test_ticket_id, message_id, user_id)
        
        # Verificar se foi chamado
        ws_manager.broadcast_to_ticket.assert_called_once()

    async def test_multiple_connections_same_user(self, ws_manager, test_user_id):
        """Testa múltiplas conexões do mesmo usuário."""
        ws1 = AsyncMock()
        ws2 = AsyncMock()
        
        # Conectar duas vezes com o mesmo usuário
        await ws_manager.connect(ws1, test_user_id)
        await ws_manager.connect(ws2, test_user_id)
        
        # Ambas as conexões devem estar ativas
        assert len(ws_manager.active_connections[test_user_id]) == 2
        assert ws1 in ws_manager.active_connections[test_user_id]
        assert ws2 in ws_manager.active_connections[test_user_id]

    async def test_send_to_disconnected_user(self, ws_manager):
        """Testa envio para usuário desconectado."""
        disconnected_user_id = str(uuid4())
        message = {"type": "test", "content": "Hello"}
        
        # Tentar enviar para usuário não conectado (não deve gerar erro)
        await ws_manager.send_personal_message(disconnected_user_id, message)
        
        # Não deve haver conexões ativas
        assert disconnected_user_id not in ws_manager.active_connections

    async def test_cleanup_empty_rooms(self, ws_manager, test_user_id, test_ticket_id):
        """Testa limpeza de salas vazias."""
        mock_websocket = AsyncMock()
        
        # Conectar, entrar na sala e sair
        await ws_manager.connect(mock_websocket, test_user_id)
        await ws_manager.join_ticket(test_user_id, test_ticket_id)
        await ws_manager.leave_ticket(test_user_id, test_ticket_id)
        
        # A sala deve ser removida se estiver vazia
        assert test_ticket_id not in ws_manager.ticket_rooms or \
               len(ws_manager.ticket_rooms[test_ticket_id]) == 0

    async def test_handle_connection_error(self, ws_manager, test_user_id):
        """Testa tratamento de erro de conexão."""
        mock_websocket = AsyncMock()
        mock_websocket.send_text.side_effect = Exception("Connection error")
        
        await ws_manager.connect(mock_websocket, test_user_id)
        
        # Tentar enviar mensagem (deve tratar o erro graciosamente)
        message = {"type": "test", "content": "Hello"}
        await ws_manager.send_personal_message(test_user_id, message)
        
        # A conexão deve ser removida após o erro
        # (implementação específica pode variar)

    async def test_broadcast_exclude_user(self, ws_manager, test_ticket_id):
        """Testa broadcast excluindo usuário específico."""
        # Criar usuários
        user1_id = str(uuid4())
        user2_id = str(uuid4())
        user3_id = str(uuid4())
        
        ws1 = AsyncMock()
        ws2 = AsyncMock()
        ws3 = AsyncMock()
        
        # Conectar todos ao ticket
        for user_id, ws in [(user1_id, ws1), (user2_id, ws2), (user3_id, ws3)]:
            await ws_manager.connect(ws, user_id)
            await ws_manager.join_ticket(user_id, test_ticket_id)
        
        message = {"type": "broadcast", "content": "Message"}
        
        # Broadcast excluindo user2
        await ws_manager.broadcast_to_ticket(test_ticket_id, message, exclude_user=user2_id)
        
        # user1 e user3 devem receber, user2 não
        ws1.send_text.assert_called_once()
        ws2.send_text.assert_not_called()
        ws3.send_text.assert_called_once()
