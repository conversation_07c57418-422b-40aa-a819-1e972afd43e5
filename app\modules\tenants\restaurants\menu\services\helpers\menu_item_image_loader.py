import logging
import uuid
from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete

from app.modules.tenants.restaurants.menu.models.menu_item import MenuItem
from app.modules.tenants.restaurants.menu.models.menu_item_image import MenuItemImage
from app.modules.core.media.services.media_service import MediaService

logger = logging.getLogger(__name__)


class MenuItemImageLoader:
    def __init__(self, db_session: AsyncSession, media_service: Optional[MediaService] = None):
        self.db = db_session
        self.media_service = media_service

    async def load_images_from_urls(self, db_item: MenuItem, image_urls: List[str], 
                                  tenant_id: uuid.UUID) -> List[MenuItemImage]:
        """Load images from URLs and create MenuItemImage records."""
        if not image_urls:
            return []

        try:
            loaded_images = []
            
            for idx, image_url in enumerate(image_urls):
                if not image_url or not image_url.strip():
                    continue
                    
                image_record = await self._process_single_image_url(
                    db_item, image_url.strip(), idx, tenant_id
                )
                
                if image_record:
                    loaded_images.append(image_record)

            logger.info(f"Loaded {len(loaded_images)} images for item {db_item.id}")
            return loaded_images

        except Exception as e:
            logger.error(f"Error loading images for item {db_item.id}: {e}")
            raise

    async def _process_single_image_url(self, db_item: MenuItem, image_url: str, 
                                      display_order: int, tenant_id: uuid.UUID) -> Optional[MenuItemImage]:
        """Process a single image URL and create MenuItemImage record."""
        try:
            # Check if media_service is available
            if not self.media_service:
                logger.warning("MediaService not available, skipping image download")
                return None

            # Download and save the image using MediaService
            media_result = await self.media_service.download_and_save_image(
                image_url=image_url,
                tenant_id=tenant_id,
                folder_path=f"menu_items/{db_item.id}",
                filename_prefix=f"image_{display_order}"
            )

            if not media_result:
                logger.warning(f"Failed to download image from URL: {image_url}")
                return None

            # Create MenuItemImage record
            image_record = MenuItemImage(
                tenant_id=tenant_id,
                menu_item_id=db_item.id,
                image_url=media_result.get('url'),
                image_path=media_result.get('path'),
                alt_text=f"{db_item.name} - Image {display_order + 1}",
                display_order=display_order,
                is_primary=display_order == 0,  # First image is primary
                is_active=True
            )

            self.db.add(image_record)
            await self.db.flush()

            logger.info(f"Created image record {image_record.id} for item {db_item.id}")
            return image_record

        except Exception as e:
            logger.error(f"Error processing image URL {image_url} for item {db_item.id}: {e}")
            return None

    async def update_item_images(self, db_item: MenuItem, images_data: List[Dict[str, Any]], 
                               tenant_id: uuid.UUID) -> List[MenuItemImage]:
        """Update images for a menu item based on provided data."""
        try:
            # Remove existing images
            await self._remove_existing_images(db_item.id, tenant_id)

            # Process new images
            updated_images = []
            
            for idx, image_data in enumerate(images_data):
                image_record = await self._process_image_data(
                    db_item, image_data, idx, tenant_id
                )
                
                if image_record:
                    updated_images.append(image_record)

            logger.info(f"Updated {len(updated_images)} images for item {db_item.id}")
            return updated_images

        except Exception as e:
            logger.error(f"Error updating images for item {db_item.id}: {e}")
            raise

    async def _remove_existing_images(self, item_id: uuid.UUID, tenant_id: uuid.UUID):
        """Remove existing images for a menu item."""
        # Get existing images to delete files
        existing_images = await self.db.execute(
            select(MenuItemImage).where(
                MenuItemImage.menu_item_id == item_id,
                MenuItemImage.tenant_id == tenant_id
            )
        )
        existing_images = existing_images.scalars().all()

        # Delete physical files
        for image in existing_images:
            if image.image_path and self.media_service:
                try:
                    await self.media_service.delete_file(image.image_path)
                except Exception as e:
                    logger.warning(f"Failed to delete image file {image.image_path}: {e}")

        # Delete database records
        await self.db.execute(
            delete(MenuItemImage).where(
                MenuItemImage.menu_item_id == item_id,
                MenuItemImage.tenant_id == tenant_id
            )
        )
        await self.db.flush()

    async def _process_image_data(self, db_item: MenuItem, image_data: Dict[str, Any], 
                                display_order: int, tenant_id: uuid.UUID) -> Optional[MenuItemImage]:
        """Process image data and create MenuItemImage record."""
        try:
            image_url = image_data.get('url') or image_data.get('image_url')
            
            if not image_url:
                logger.warning(f"No image URL provided in image data: {image_data}")
                return None

            # Check if this is an existing image (has ID and no URL change needed)
            existing_id = image_data.get('id')
            if existing_id and not str(existing_id).startswith('temp_'):
                # This is an existing image, just update metadata
                return await self._update_existing_image(
                    existing_id, image_data, display_order, tenant_id
                )

            # This is a new image, download and create
            return await self._process_single_image_url(
                db_item, image_url, display_order, tenant_id
            )

        except Exception as e:
            logger.error(f"Error processing image data {image_data}: {e}")
            return None

    async def _update_existing_image(self, image_id: uuid.UUID, image_data: Dict[str, Any], 
                                   display_order: int, tenant_id: uuid.UUID) -> Optional[MenuItemImage]:
        """Update an existing image record."""
        try:
            # Get existing image
            result = await self.db.execute(
                select(MenuItemImage).where(
                    MenuItemImage.id == image_id,
                    MenuItemImage.tenant_id == tenant_id
                )
            )
            existing_image = result.scalar_one_or_none()

            if not existing_image:
                logger.warning(f"Existing image {image_id} not found")
                return None

            # Update metadata
            existing_image.alt_text = image_data.get('alt_text', existing_image.alt_text)
            existing_image.display_order = display_order
            existing_image.is_primary = image_data.get('is_primary', display_order == 0)
            existing_image.is_active = image_data.get('is_active', True)

            await self.db.flush()
            return existing_image

        except Exception as e:
            logger.error(f"Error updating existing image {image_id}: {e}")
            return None

    async def set_primary_image(self, item_id: uuid.UUID, image_id: uuid.UUID, tenant_id: uuid.UUID):
        """Set a specific image as the primary image for a menu item."""
        try:
            # First, unset all primary flags for this item
            await self.db.execute(
                select(MenuItemImage).where(
                    MenuItemImage.menu_item_id == item_id,
                    MenuItemImage.tenant_id == tenant_id
                ).update({MenuItemImage.is_primary: False})
            )

            # Set the specified image as primary
            result = await self.db.execute(
                select(MenuItemImage).where(
                    MenuItemImage.id == image_id,
                    MenuItemImage.menu_item_id == item_id,
                    MenuItemImage.tenant_id == tenant_id
                )
            )
            target_image = result.scalar_one_or_none()

            if target_image:
                target_image.is_primary = True
                await self.db.flush()
                logger.info(f"Set image {image_id} as primary for item {item_id}")
            else:
                logger.warning(f"Image {image_id} not found for item {item_id}")

        except Exception as e:
            logger.error(f"Error setting primary image {image_id} for item {item_id}: {e}")
            raise

    async def get_item_images(self, item_id: uuid.UUID, tenant_id: uuid.UUID) -> List[MenuItemImage]:
        """Get all images for a menu item."""
        try:
            result = await self.db.execute(
                select(MenuItemImage).where(
                    MenuItemImage.menu_item_id == item_id,
                    MenuItemImage.tenant_id == tenant_id,
                    MenuItemImage.is_active == True
                ).order_by(MenuItemImage.display_order)
            )
            return result.scalars().all()

        except Exception as e:
            logger.error(f"Error getting images for item {item_id}: {e}")
            return []

    async def delete_item_image(self, image_id: uuid.UUID, tenant_id: uuid.UUID):
        """Delete a specific image."""
        try:
            # Get the image to delete the file
            result = await self.db.execute(
                select(MenuItemImage).where(
                    MenuItemImage.id == image_id,
                    MenuItemImage.tenant_id == tenant_id
                )
            )
            image = result.scalar_one_or_none()

            if not image:
                logger.warning(f"Image {image_id} not found")
                return

            # Delete physical file
            if image.image_path and self.media_service:
                try:
                    await self.media_service.delete_file(image.image_path)
                except Exception as e:
                    logger.warning(f"Failed to delete image file {image.image_path}: {e}")

            # Delete database record
            await self.db.execute(
                delete(MenuItemImage).where(
                    MenuItemImage.id == image_id,
                    MenuItemImage.tenant_id == tenant_id
                )
            )
            await self.db.flush()

            logger.info(f"Deleted image {image_id}")

        except Exception as e:
            logger.error(f"Error deleting image {image_id}: {e}")
            raise

    async def load_all_images(self, items: List[MenuItem]):
        """Load all images for a list of menu items (compatibility method)."""
        try:
            if not items:
                return

            # Get all item IDs
            item_ids = [item.id for item in items]
            
            # Load all images for these items in one query
            result = await self.db.execute(
                select(MenuItemImage)
                .where(MenuItemImage.menu_item_id.in_(item_ids))
                .where(MenuItemImage.is_active == True)
                .order_by(MenuItemImage.display_order.asc())
            )
            
            images = result.scalars().all()
            
            # Group images by menu item ID
            images_by_item = {}
            for image in images:
                if image.menu_item_id not in images_by_item:
                    images_by_item[image.menu_item_id] = []
                images_by_item[image.menu_item_id].append(image)
            
            # Assign images to items as dynamic attributes
            for item in items:
                item_images = images_by_item.get(item.id, [])
                
                # Process image data for frontend compatibility
                processed_images = []
                for image in item_images:
                    image_data = {
                        'id': str(image.id),
                        'url': self._get_safe_image_url(image.image_path, image.image_url),
                        'alt_text': image.alt_text,
                        'is_primary': image.is_primary,
                        'display_order': image.display_order
                    }
                    processed_images.append(image_data)
                
                # Set images as dynamic attribute
                setattr(item, 'processed_images', processed_images)
                
                # Set primary image URL for convenience
                primary_image = next((img for img in processed_images if img['is_primary']), None)
                if primary_image:
                    setattr(item, 'primary_image_url', primary_image['url'])
                elif processed_images:
                    setattr(item, 'primary_image_url', processed_images[0]['url'])
                else:
                    setattr(item, 'primary_image_url', None)

            logger.info(f"Loaded images for {len(items)} menu items")

        except Exception as e:
            logger.error(f"Error loading images for items: {e}")
            # Set empty images for all items on error
            for item in items:
                setattr(item, 'processed_images', [])
                setattr(item, 'primary_image_url', None)

    def _get_safe_image_url(self, image_path: str, image_url: str = None) -> str:
        """Get image URL safely without async context issues."""
        try:
            if image_url:
                return image_url
            
            if image_path:
                # Simple URL construction without calling async MediaService
                if image_path.startswith('http'):
                    return image_path
                else:
                    return f"/media/{image_path}"
            
            return "/static/images/no-image.png"  # Fallback
            
        except Exception as e:
            logger.warning(f"Error getting image URL for {image_path}: {e}")
            return "/static/images/no-image.png"