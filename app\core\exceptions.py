from fastapi import HTTPException, status


class BaseAppException(HTTPException):
    """Base exception for the application."""

    def __init__(self, status_code: int, detail: str):
        super().__init__(status_code=status_code, detail=detail)


class NotFoundError(BaseAppException):
    """Raised when an item is not found."""

    def __init__(self, detail: str = "Item not found"):
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)


class ForbiddenError(BaseAppException):
    """Raised when an action is forbidden for the current user."""

    def __init__(self, detail: str = "Operation forbidden"):
        super().__init__(status_code=status.HTTP_403_FORBIDDEN, detail=detail)


class BusinessLogicError(BaseAppException):
    """Raised for general business logic violations."""

    def __init__(self, detail: str = "Business logic violation"):
        # Using 400 Bad Request, but could use 409 Conflict or others depending on context
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)


class AuthenticationError(BaseAppException):
    """Raised for authentication failures."""

    def __init__(self, detail: str = "Authentication failed"):
        super().__init__(status_code=status.HTTP_401_UNAUTHORIZED, detail=detail)


class ValidationError(BaseAppException):
    """Raised for validation errors."""

    def __init__(self, detail: str = "Validation error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)


class ConflictError(BaseAppException):
    """Raised when there's a conflict with existing data."""

    def __init__(self, detail: str = "Conflict with existing data"):
        super().__init__(status_code=status.HTTP_409_CONFLICT, detail=detail)


class AuthorizationError(BaseAppException):
    """Raised for authorization failures."""

    def __init__(self, detail: str = "Authorization failed"):
        super().__init__(status_code=status.HTTP_403_FORBIDDEN, detail=detail)
