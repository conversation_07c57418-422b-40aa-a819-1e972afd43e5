"""Base interface for payment processors."""

import abc  # noqa: E402
from typing import Dict, Any, Optional, Union
from decimal import Decimal

from app.modules.core.payments.models.payment_processor import PaymentProcessorType  # noqa: E402
from app.modules.core.payments.models.payment_transaction import PaymentStatus


class PaymentProcessorInterface(abc.ABC):
    """
    Abstract base class for payment processors.

    All payment processor implementations must inherit from this class
    and implement its abstract methods.
    """

    def __init__(self, processor_config: Dict[str, Any]):
        """
        Initialize the payment processor with the given configuration.

        Args:
            processor_config: A dictionary containing the processor configuration.
                This should include API keys, secrets, and other settings.
        """
        self.config = processor_config
        self.sandbox_mode = processor_config.get("sandbox_mode", True)

    @abc.abstractmethod
    async def process_payment(
        self,
        amount: Decimal,
        currency: str,
        payment_method_id: Optional[str],
        customer_id: Optional[str],
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Process a payment using this processor.

        Args:
            amount: The amount to charge.
            currency: The currency code (e.g., 'USD', 'BRL').
            payment_method_id: An optional ID of the payment method to use.
            customer_id: An optional ID of the customer in the processor's system.
            metadata: Optional additional data to include with the payment.

        Returns:
            A dictionary containing the payment details, including:
                - external_id: The ID of the payment in the processor's system.
                - status: The status of the payment.
                - error: An error message if the payment failed.
        """

    @abc.abstractmethod
    async def process_refund(
        self,
        payment_id: str,
        amount: Optional[Decimal] = None,
        reason: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Process a refund for a payment.

        Args:
            payment_id: The ID of the payment in the processor's system.
            amount: The amount to refund. If None, refund the full amount.
            reason: An optional reason for the refund.
            metadata: Optional additional data to include with the refund.

        Returns:
            A dictionary containing the refund details, including:
                - external_id: The ID of the refund in the processor's system.
                - status: The status of the refund.
                - error: An error message if the refund failed.
        """

    @abc.abstractmethod
    async def get_payment_status(self, payment_id: str) -> Dict[str, Any]:
        """
        Get the status of a payment.

        Args:
            payment_id: The ID of the payment in the processor's system.

        Returns:
            A dictionary containing the payment details, including:
                - status: The status of the payment.
                - error: An error message if the status check failed.
        """

    @abc.abstractmethod
    async def get_refund_status(self, refund_id: str) -> Dict[str, Any]:
        """
        Get the status of a refund.

        Args:
            refund_id: The ID of the refund in the processor's system.

        Returns:
            A dictionary containing the refund details, including:
                - status: The status of the refund.
                - error: An error message if the status check failed.
        """

    @abc.abstractmethod
    async def create_payment_method(
        self,
        method_type: str,
        method_details: Dict[str, Any],
        customer_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Create a payment method in the processor's system.

        Args:
            method_type: The type of payment method (e.g., 'card', 'bank_account').
            method_details: Details of the payment method.
            customer_id: An optional ID of the customer to associate with the method.

        Returns:
            A dictionary containing the payment method details, including:
                - external_id: The ID of the payment method in the processor's system.
                - error: An error message if the creation failed.
        """

    @abc.abstractmethod
    async def delete_payment_method(self, method_id: str) -> Dict[str, Any]:
        """
        Delete a payment method from the processor's system.

        Args:
            method_id: The ID of the payment method in the processor's system.

        Returns:
            A dictionary containing the result, including:
                - success: Whether the deletion was successful.
                - error: An error message if the deletion failed.
        """

    @abc.abstractmethod
    async def create_customer(self, customer_details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a customer in the processor's system.

        Args:
            customer_details: Details of the customer.

        Returns:
            A dictionary containing the customer details, including:
                - external_id: The ID of the customer in the processor's system.
                - error: An error message if the creation failed.
        """

    @abc.abstractmethod
    async def update_customer(
        self, customer_id: str, customer_details: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Update a customer in the processor's system.

        Args:
            customer_id: The ID of the customer in the processor's system.
            customer_details: Updated details of the customer.

        Returns:
            A dictionary containing the customer details, including:
                - external_id: The ID of the customer in the processor's system.
                - error: An error message if the update failed.
        """

    @abc.abstractmethod
    async def delete_customer(self, customer_id: str) -> Dict[str, Any]:
        """
        Delete a customer from the processor's system.

        Args:
            customer_id: The ID of the customer in the processor's system.

        Returns:
            A dictionary containing the result, including:
                - success: Whether the deletion was successful.
                - error: An error message if the deletion failed.
        """

    @staticmethod
    def map_status_to_internal(processor_status: str) -> PaymentStatus:
        """
        Map a processor-specific status to an internal PaymentStatus.

        Args:
            processor_status: The status from the payment processor.

        Returns:
            The corresponding internal PaymentStatus.
        """
        # Default implementation - should be overridden by subclasses
        status_map = {
            "succeeded": PaymentStatus.COMPLETED,
            "pending": PaymentStatus.PENDING,
            "processing": PaymentStatus.PROCESSING,
            "failed": PaymentStatus.FAILED,
            "refunded": PaymentStatus.REFUNDED,
            "partially_refunded": PaymentStatus.PARTIALLY_REFUNDED,
            "canceled": PaymentStatus.CANCELLED,
        }
        return status_map.get(processor_status.lower(), PaymentStatus.PENDING)


class PaymentProcessorFactory:
    """
    Factory for creating payment processor instances.
    """

    @staticmethod
    def create_processor(
        processor_type: PaymentProcessorType, config: Dict[str, Any]
    ) -> Optional[PaymentProcessorInterface]:
        """
        Create a payment processor instance based on the processor type.

        Only creates the processor if the required API keys are present in the config.
        If keys are missing, returns None to indicate the processor is not available.

        Args:
            processor_type: The type of payment processor to create.
            config: The configuration for the processor.

        Returns:
            An instance of a PaymentProcessorInterface implementation, or None if
            the processor cannot be initialized due to missing configuration.

        Raises:
            ValueError: If the processor type is not supported.
        """
        import logging  # noqa: E402

        logger = logging.getLogger(__name__)

        if processor_type == PaymentProcessorType.STRIPE:
            # Check if Stripe API keys are available
            api_key = (
                config.get("api_key")
                if not config.get("sandbox_mode", True)
                else config.get("test_api_key")
            )
            if not api_key:
                logger.warning("Stripe processor not initialized: API key missing")
                return None

            try:
                from .stripe import StripeProcessor  # noqa: E402

                return StripeProcessor(config)
            except ImportError as e:
                logger.warning(f"Stripe processor not available: {str(e)}")
                return None

        elif processor_type == PaymentProcessorType.PAYPAL:
            # Check if PayPal API credentials are available
            client_id = config.get("client_id")
            client_secret = config.get("client_secret")
            if not client_id or not client_secret:
                logger.warning("PayPal processor not initialized: API credentials missing")
                return None

            try:
                from .paypal import PayPalProcessor  # noqa: E402

                return PayPalProcessor(config)
            except ImportError as e:
                logger.warning(f"PayPal processor not available: {str(e)}")
                return None

        elif processor_type == PaymentProcessorType.MANUAL:
            # Manual/bank transfer doesn't require external APIs
            try:
                from .bank_transfer import BankTransferProcessor  # noqa: E402

                return BankTransferProcessor(config)
            except ImportError as e:
                logger.warning(f"Bank transfer processor not available: {str(e)}")
                return None
        else:
            raise ValueError(f"Unsupported payment processor type: {processor_type}")
