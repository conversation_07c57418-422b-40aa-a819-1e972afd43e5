"""
Notification Service

Serviço principal para gerenciamento de notificações.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from uuid import UUID

from sqlalchemy import and_, desc, func, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from sqlalchemy.future import select

from app.modules.core.users.models.user import User
from app.modules.core.tenants.models.tenant import Tenant

from ..models import (
    Notification, NotificationMetrics, NotificationPriority, 
    NotificationSenderType, NotificationStatus, NotificationTargetType,
    NotificationTemplate
)
from ..schemas import (
    NotificationCreate, NotificationFilters, NotificationUpdate
)

logger = logging.getLogger(__name__)


class NotificationService:
    """Serviço para gerenciamento de notificações."""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def create_notification(
        self,
        notification_data: NotificationCreate,
        sender: User
    ) -> Notification:
        """
        Cria uma nova notificação.
        
        Args:
            notification_data: Dados da notificação
            sender: Usuário remetente
            
        Returns:
            Notificação criada
        """
        # Determina o tipo de remetente
        sender_type = NotificationSenderType.ADMIN if sender.system_role == "admin" else NotificationSenderType.TENANT_OWNER
        
        # Calcula data de expiração
        expires_at = None
        if notification_data.auto_expire_hours:
            expires_at = datetime.utcnow() + timedelta(hours=notification_data.auto_expire_hours)
        
        notification = Notification(
            title=notification_data.title,
            content=notification_data.content,
            image_url=notification_data.image_url,
            action_url=notification_data.action_url,
            sender_id=sender.id,
            sender_type=sender_type,
            target_type=notification_data.target_type,
            target_id=notification_data.target_id,
            tenant_id=notification_data.tenant_id,
            priority=notification_data.priority,
            auto_expire_hours=notification_data.auto_expire_hours,
            max_lifetime_days=notification_data.max_lifetime_days,
            expires_at=expires_at,
            status=NotificationStatus.DRAFT
        )
        
        self.db.add(notification)
        await self.db.commit()
        await self.db.refresh(notification)
        
        logger.info(f"Notificação criada: {notification.id} por {sender.id}")
        return notification

    async def get_notification(
        self,
        notification_id: UUID,
        user: User
    ) -> Optional[Notification]:
        """
        Obtém uma notificação específica.
        
        Args:
            notification_id: ID da notificação
            user: Usuário solicitante
            
        Returns:
            Notificação ou None se não encontrada/sem acesso
        """
        stmt = select(Notification).where(Notification.id == notification_id)
        result = await self.db.execute(stmt)
        notification = result.scalar_one_or_none()

        # Verifica acesso (simplificado por enquanto)
        if not notification:
            return None
        
        if notification:
            # Incrementa contador de visualizações
            notification.increment_view_count()
            await self.db.commit()
        
        return notification

    async def get_notifications(
        self,
        user: User,
        filters: Optional[NotificationFilters] = None,
        page: int = 1,
        per_page: int = 20
    ) -> Tuple[List[Notification], int]:
        """
        Lista notificações com filtros e paginação.
        
        Args:
            user: Usuário solicitante
            filters: Filtros opcionais
            page: Página atual
            per_page: Itens por página
            
        Returns:
            Tupla com (notificações, total)
        """
        # Implementação simplificada por enquanto
        stmt = select(Notification).order_by(desc(Notification.created_at))

        # Aplica paginação
        offset = (page - 1) * per_page
        stmt = stmt.offset(offset).limit(per_page)

        result = await self.db.execute(stmt)
        notifications = result.scalars().all()

        # Conta total (simplificado)
        count_stmt = select(func.count(Notification.id))
        count_result = await self.db.execute(count_stmt)
        total = count_result.scalar()
        
        return notifications, total

    async def update_notification(
        self,
        notification_id: UUID,
        update_data: NotificationUpdate,
        user: User
    ) -> Optional[Notification]:
        """
        Atualiza uma notificação.

        Args:
            notification_id: ID da notificação
            update_data: Dados para atualização
            user: Usuário solicitante

        Returns:
            Notificação atualizada ou None
        """
        notification = await self.get_notification(notification_id, user)
        if not notification:
            return None

        # Aplica atualizações (simplificado)
        for field, value in update_data.dict(exclude_unset=True).items():
            setattr(notification, field, value)

        notification.updated_at = datetime.utcnow()

        await self.db.commit()
        await self.db.refresh(notification)

        logger.info(f"Notificação atualizada: {notification.id} por {user.id}")
        return notification

    async def delete_notification(
        self,
        notification_id: UUID,
        user: User,
        delete_for_all: bool = False
    ) -> bool:
        """
        Deleta uma notificação.
        
        Args:
            notification_id: ID da notificação
            user: Usuário solicitante
            delete_for_all: Se True, deleta para todos (apenas admin/owner)
            
        Returns:
            True se deletada com sucesso
        """
        notification = await self.get_notification(notification_id, user)
        if not notification:
            return False
        
        if delete_for_all:
            # Deleta fisicamente (simplificado)
            await self.db.delete(notification)
            logger.info(f"Notificação deletada para todos: {notification.id} por {user.id}")
        else:
            # Marca como deletada apenas para o usuário
            notification.mark_as_deleted(str(user.id))
            logger.info(f"Notificação deletada para usuário: {notification.id} por {user.id}")
        
        await self.db.commit()
        return True

    async def mark_as_read(
        self,
        notification_ids: List[UUID],
        user: User
    ) -> int:
        """
        Marca notificações como lidas.
        
        Args:
            notification_ids: IDs das notificações
            user: Usuário
            
        Returns:
            Número de notificações marcadas
        """
        stmt = select(Notification).where(Notification.id.in_(notification_ids))
        result = await self.db.execute(stmt)
        notifications = result.scalars().all()
        count = 0
        
        for notification in notifications:
            if not notification.is_read_by_user(str(user.id)):
                notification.mark_as_read(str(user.id))
                count += 1
        
        await self.db.commit()
        logger.info(f"Marcadas {count} notificações como lidas por {user.id}")
        return count

    async def increment_click_count(
        self,
        notification_id: UUID,
        user: User
    ) -> bool:
        """
        Incrementa contador de cliques.
        
        Args:
            notification_id: ID da notificação
            user: Usuário que clicou
            
        Returns:
            True se incrementado com sucesso
        """
        notification = await self.get_notification(notification_id, user)
        if not notification:
            return False
        
        notification.increment_click_count()
        await self.db.commit()
        
        logger.info(f"Click registrado na notificação {notification.id} por {user.id}")
        return True

    # TODO: Implementar métodos de filtros e validação de acesso
