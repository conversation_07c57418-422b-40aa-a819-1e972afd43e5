"""Processors for Domain Rent module."""

from app.modules.shared.domain_rent.processors.base_processor import BaseProcessor  # noqa: E402
from app.modules.shared.domain_rent.processors.godaddy_processor import GoDaddyProcessor
from app.modules.shared.domain_rent.processors.namecheap_processor import (
    NamecheapProcessor,
)
from app.modules.shared.domain_rent.processors.opensrs_processor import (
    OpenSRSProcessor,
)  # noqa: E402
from app.modules.shared.domain_rent.processors.resellerclub_processor import (
    ResellerClubProcessor,
)
from app.modules.shared.domain_rent.processors.processor_aggregator import (  # noqa: E402
    ProcessorAggregator,
)

__all__ = [
    "BaseProcessor",
    "GoDaddyProcessor",
    "NamecheapProcessor",
    "OpenSRSProcessor",
    "ResellerClubProcessor",
    "ProcessorAggregator",
]
