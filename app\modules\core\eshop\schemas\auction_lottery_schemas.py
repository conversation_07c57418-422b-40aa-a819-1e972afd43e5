"""
Auction and Lottery Schemas
===========================

Pydantic schemas for auction and lottery API requests and responses.
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, List
from uuid import UUID

from pydantic import BaseModel, Field, validator
from enum import Enum


# Enums
class AuctionStatus(str, Enum):
    DRAFT = "draft"
    ACTIVE = "active"
    ENDED = "ended"
    CANCELLED = "cancelled"


class LotteryStatus(str, Enum):
    DRAFT = "draft"
    ACTIVE = "active"
    ENDED = "ended"
    CANCELLED = "cancelled"
    DRAWN = "drawn"


# Base Schemas
class AuctionBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=2000)
    starting_bid: Decimal = Field(..., gt=0, decimal_places=2)
    buy_now_price: Optional[Decimal] = Field(None, gt=0, decimal_places=2)
    bid_increment: Decimal = Field(..., gt=0, decimal_places=2)
    start_time: datetime
    end_time: datetime
    auto_extend: bool = Field(default=True)
    extend_minutes: int = Field(default=5, ge=1, le=60)
    max_extensions: int = Field(default=3, ge=0, le=10)
    
    @validator('end_time')
    def end_time_must_be_after_start_time(cls, v, values):
        if 'start_time' in values and v <= values['start_time']:
            raise ValueError('end_time must be after start_time')
        return v
    
    @validator('buy_now_price')
    def buy_now_must_be_higher_than_starting_bid(cls, v, values):
        if v is not None and 'starting_bid' in values and v <= values['starting_bid']:
            raise ValueError('buy_now_price must be higher than starting_bid')
        return v


class AuctionCreate(AuctionBase):
    product_id: UUID


class AuctionUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=2000)
    buy_now_price: Optional[Decimal] = Field(None, gt=0, decimal_places=2)
    end_time: Optional[datetime] = None
    auto_extend: Optional[bool] = None
    extend_minutes: Optional[int] = Field(None, ge=1, le=60)
    max_extensions: Optional[int] = Field(None, ge=0, le=10)


class AuctionResponse(AuctionBase):
    id: UUID
    tenant_id: UUID
    product_id: UUID
    vendor_id: UUID
    status: AuctionStatus
    current_bid: Optional[Decimal]
    winning_bid: Optional[Decimal]
    winner_id: Optional[UUID]
    total_bids: int
    unique_bidders: int
    views_count: int
    extensions_used: int
    time_remaining: Optional[int]  # seconds
    created_at: datetime
    updated_at: datetime
    
    # Related data
    product_name: Optional[str] = None
    product_image: Optional[str] = None
    vendor_name: Optional[str] = None
    winner_name: Optional[str] = None
    
    class Config:
        from_attributes = True


# Bid Schemas
class BidCreate(BaseModel):
    auction_id: UUID
    bid_amount: Decimal = Field(..., gt=0, decimal_places=2)


class BidResponse(BaseModel):
    id: UUID
    auction_id: UUID
    bidder_id: UUID
    bid_amount: Decimal
    is_winning: bool
    is_auto_bid: bool
    created_at: datetime
    
    # Related data
    bidder_name: Optional[str] = None
    
    class Config:
        from_attributes = True


# Lottery Schemas
class LotteryBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=2000)
    is_free: bool = Field(default=True)
    ticket_price: Optional[Decimal] = Field(None, gt=0, decimal_places=2)
    max_tickets_per_user: Optional[int] = Field(None, ge=1)
    total_tickets_available: Optional[int] = Field(None, ge=1)
    start_time: datetime
    end_time: datetime
    draw_time: Optional[datetime] = None
    
    @validator('end_time')
    def end_time_must_be_after_start_time(cls, v, values):
        if 'start_time' in values and v <= values['start_time']:
            raise ValueError('end_time must be after start_time')
        return v
    
    @validator('draw_time')
    def draw_time_validation(cls, v, values):
        if v is not None:
            if 'start_time' in values and v <= values['start_time']:
                raise ValueError('draw_time must be after start_time')
            if 'end_time' in values and v < values['end_time']:
                raise ValueError('draw_time must be after or equal to end_time')
        return v
    
    @validator('ticket_price')
    def ticket_price_validation(cls, v, values):
        if not values.get('is_free', True) and v is None:
            raise ValueError('ticket_price is required when is_free is False')
        if values.get('is_free', True) and v is not None:
            raise ValueError('ticket_price must be None when is_free is True')
        return v


class LotteryCreate(LotteryBase):
    product_id: UUID


class LotteryUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=2000)
    ticket_price: Optional[Decimal] = Field(None, gt=0, decimal_places=2)
    max_tickets_per_user: Optional[int] = Field(None, ge=1)
    total_tickets_available: Optional[int] = Field(None, ge=1)
    end_time: Optional[datetime] = None
    draw_time: Optional[datetime] = None


class LotteryResponse(LotteryBase):
    id: UUID
    tenant_id: UUID
    product_id: UUID
    vendor_id: UUID
    status: LotteryStatus
    tickets_sold: int
    winner_id: Optional[UUID]
    winning_ticket_id: Optional[UUID]
    drawn_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    
    # Related data
    product_name: Optional[str] = None
    product_image: Optional[str] = None
    vendor_name: Optional[str] = None
    winner_name: Optional[str] = None
    
    class Config:
        from_attributes = True


# Ticket Schemas
class TicketCreate(BaseModel):
    lottery_id: UUID
    quantity: int = Field(default=1, ge=1, le=100)


class TicketResponse(BaseModel):
    id: UUID
    lottery_id: UUID
    participant_id: UUID
    ticket_number: str
    is_winning: bool
    is_paid: bool
    payment_amount: Optional[Decimal]
    payment_reference: Optional[str]
    created_at: datetime
    
    # Related data
    participant_name: Optional[str] = None
    lottery_title: Optional[str] = None
    
    class Config:
        from_attributes = True


# WebSocket Message Schemas
class WebSocketMessage(BaseModel):
    type: str
    data: dict
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class BidUpdateMessage(WebSocketMessage):
    type: str = "bid_update"
    auction_id: UUID
    new_bid: BidResponse
    time_remaining: Optional[int]


class AuctionStatusMessage(WebSocketMessage):
    type: str = "auction_status"
    auction_id: UUID
    status: AuctionStatus
    winner_id: Optional[UUID] = None


class LotteryDrawMessage(WebSocketMessage):
    type: str = "lottery_draw"
    lottery_id: UUID
    winner_id: UUID
    winning_ticket: TicketResponse


# List Response Schemas
class AuctionListResponse(BaseModel):
    items: List[AuctionResponse]
    total: int
    page: int
    size: int
    pages: int


class LotteryListResponse(BaseModel):
    items: List[LotteryResponse]
    total: int
    page: int
    size: int
    pages: int


class BidListResponse(BaseModel):
    items: List[BidResponse]
    total: int
    page: int
    size: int
    pages: int


class TicketListResponse(BaseModel):
    items: List[TicketResponse]
    total: int
    page: int
    size: int
    pages: int


# Statistics Schemas
class AuctionStats(BaseModel):
    total_auctions: int
    active_auctions: int
    total_bids: int
    average_bid_amount: Optional[Decimal]
    highest_winning_bid: Optional[Decimal]


class LotteryStats(BaseModel):
    total_lotteries: int
    active_lotteries: int
    total_tickets_sold: int
    total_revenue: Optional[Decimal]
    average_participation: Optional[float]
