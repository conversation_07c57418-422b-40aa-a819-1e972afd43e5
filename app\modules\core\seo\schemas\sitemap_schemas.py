"""
Sitemap Schemas

Pydantic models for sitemap validation and serialization.
"""

import uuid
from datetime import datetime
from typing import List, Optional
from decimal import Decimal

from pydantic import BaseModel, Field, validator


class SitemapEntryBase(BaseModel):
    """Base schema for sitemap entries."""
    content_type: str = Field(..., max_length=50, description="Type of content")
    content_id: uuid.UUID = Field(..., description="ID of the content")
    language_code: str = Field(..., max_length=10, description="Language code")
    url: str = Field(..., max_length=500, description="URL for sitemap")
    priority: Decimal = Field(default=Decimal("0.5"), ge=0, le=1, description="Sitemap priority (0.0-1.0)")
    change_frequency: str = Field(default="weekly", description="Change frequency")
    is_active: bool = Field(default=True, description="Is entry active")
    is_indexed: bool = Field(default=True, description="Should be included in sitemap")
    title: Optional[str] = Field(None, max_length=200, description="Page title")
    description: Optional[str] = Field(None, description="Page description")
    image_url: Optional[str] = Field(None, max_length=500, description="Page image URL")

    @validator('change_frequency')
    def validate_change_frequency(cls, v):
        valid_frequencies = ["always", "hourly", "daily", "weekly", "monthly", "yearly", "never"]
        if v not in valid_frequencies:
            raise ValueError(f'Change frequency must be one of: {valid_frequencies}')
        return v


class SitemapEntryCreate(SitemapEntryBase):
    """Schema for creating sitemap entries."""
    pass


class SitemapEntryUpdate(BaseModel):
    """Schema for updating sitemap entries."""
    url: Optional[str] = Field(None, max_length=500)
    priority: Optional[Decimal] = Field(None, ge=0, le=1)
    change_frequency: Optional[str] = None
    is_active: Optional[bool] = None
    is_indexed: Optional[bool] = None
    title: Optional[str] = Field(None, max_length=200)
    description: Optional[str] = None
    image_url: Optional[str] = Field(None, max_length=500)


class SitemapEntryResponse(SitemapEntryBase):
    """Schema for sitemap entry response."""
    id: uuid.UUID
    last_modified: datetime
    created_at: datetime
    updated_at: datetime
    crawl_count: int
    last_crawled_at: Optional[datetime]
    created_by_id: Optional[uuid.UUID]

    class Config:
        from_attributes = True


class SitemapURLEntry(BaseModel):
    """Schema for individual sitemap URL entry."""
    loc: str = Field(..., description="URL location")
    lastmod: Optional[str] = Field(None, description="Last modification date (ISO format)")
    changefreq: Optional[str] = Field(None, description="Change frequency")
    priority: Optional[float] = Field(None, ge=0.0, le=1.0, description="Priority")
    
    # Hreflang alternates
    alternates: List[dict] = Field(default_factory=list, description="Hreflang alternates")
    
    # Image information
    images: List[dict] = Field(default_factory=list, description="Image information")


class SitemapResponse(BaseModel):
    """Schema for sitemap XML response."""
    urls: List[SitemapURLEntry]
    total_urls: int
    language_code: Optional[str] = None
    generated_at: datetime
    
    class Config:
        from_attributes = True


class SitemapIndexEntry(BaseModel):
    """Schema for sitemap index entry."""
    loc: str = Field(..., description="Sitemap location")
    lastmod: Optional[str] = Field(None, description="Last modification date")
    language_code: Optional[str] = Field(None, description="Language code for this sitemap")


class SitemapIndexResponse(BaseModel):
    """Schema for sitemap index XML response."""
    sitemaps: List[SitemapIndexEntry]
    total_sitemaps: int
    generated_at: datetime
    
    class Config:
        from_attributes = True


class SEOSitemapStats(BaseModel):
    """Schema for sitemap statistics."""
    total_entries: int
    active_entries: int
    indexed_entries: int
    languages: List[str]
    content_types: List[str]
    last_generated: Optional[datetime]
    
    class Config:
        from_attributes = True
