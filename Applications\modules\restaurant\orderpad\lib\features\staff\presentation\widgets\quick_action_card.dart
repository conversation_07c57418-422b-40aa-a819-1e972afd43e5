import 'package:flutter/material.dart';

class QuickActionCard extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;
  final bool isEnabled;
  final Widget? badge;

  const QuickActionCard({
    super.key,
    required this.title,
    this.subtitle,
    required this.icon,
    required this.color,
    required this.onTap,
    this.isEnabled = true,
    this.badge,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: isEnabled ? onTap : null,
        borderRadius: BorderRadius.circular(16),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isEnabled 
                ? theme.colorScheme.surface 
                : theme.colorScheme.surface.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isEnabled 
                  ? color.withValues(alpha: 0.3)
                  : theme.colorScheme.outline.withValues(alpha: 0.2),
              width: 1.5,
            ),
            boxShadow: isEnabled ? [
              BoxShadow(
                color: color.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ] : null,
          ),
          child: Stack(
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Icon container
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: isEnabled 
                          ? color.withValues(alpha: 0.15)
                          : theme.colorScheme.outline.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      icon,
                      color: isEnabled 
                          ? color 
                          : theme.colorScheme.outline,
                      size: 24,
                    ),
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // Title
                  Text(
                    title,
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: isEnabled 
                          ? theme.colorScheme.onSurface
                          : theme.colorScheme.onSurface.withValues(alpha: 0.5),
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  // Subtitle if provided
                  if (subtitle != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      subtitle!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: isEnabled 
                            ? theme.colorScheme.onSurface.withValues(alpha: 0.6)
                            : theme.colorScheme.onSurface.withValues(alpha: 0.3),
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
              
              // Badge if provided
              if (badge != null)
                Positioned(
                  top: 0,
                  right: 0,
                  child: badge!,
                ),
            ],
          ),
        ),
      ),
    );
  }
}

// Specialized quick action cards
class NewOrderActionCard extends StatelessWidget {
  final VoidCallback onTap;
  final bool isEnabled;

  const NewOrderActionCard({
    super.key,
    required this.onTap,
    this.isEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return QuickActionCard(
      title: 'Novo Pedido',
      subtitle: 'Criar pedido',
      icon: Icons.add_shopping_cart,
      color: Colors.blue,
      onTap: onTap,
      isEnabled: isEnabled,
    );
  }
}

class ViewTablesActionCard extends StatelessWidget {
  final VoidCallback onTap;
  final int? availableCount;
  final bool isEnabled;

  const ViewTablesActionCard({
    super.key,
    required this.onTap,
    this.availableCount,
    this.isEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return QuickActionCard(
      title: 'Mesas',
      subtitle: availableCount != null 
          ? '$availableCount disponíveis'
          : 'Ver mesas',
      icon: Icons.table_restaurant,
      color: Colors.green,
      onTap: onTap,
      isEnabled: isEnabled,
      badge: availableCount != null && availableCount! > 0
          ? _buildBadge(context, availableCount.toString())
          : null,
    );
  }

  Widget _buildBadge(BuildContext context, String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.green,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}

class ViewOrdersActionCard extends StatelessWidget {
  final VoidCallback onTap;
  final int? pendingCount;
  final bool isEnabled;

  const ViewOrdersActionCard({
    super.key,
    required this.onTap,
    this.pendingCount,
    this.isEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return QuickActionCard(
      title: 'Pedidos',
      subtitle: pendingCount != null 
          ? '$pendingCount pendentes'
          : 'Ver pedidos',
      icon: Icons.receipt_long,
      color: Colors.orange,
      onTap: onTap,
      isEnabled: isEnabled,
      badge: pendingCount != null && pendingCount! > 0
          ? _buildBadge(context, pendingCount.toString())
          : null,
    );
  }

  Widget _buildBadge(BuildContext context, String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.orange,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}

class DeliveryActionCard extends StatelessWidget {
  final VoidCallback onTap;
  final int? activeDeliveries;
  final bool isEnabled;

  const DeliveryActionCard({
    super.key,
    required this.onTap,
    this.activeDeliveries,
    this.isEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return QuickActionCard(
      title: 'Delivery',
      subtitle: activeDeliveries != null 
          ? '$activeDeliveries ativos'
          : 'Ver entregas',
      icon: Icons.delivery_dining,
      color: Colors.purple,
      onTap: onTap,
      isEnabled: isEnabled,
      badge: activeDeliveries != null && activeDeliveries! > 0
          ? _buildBadge(context, activeDeliveries.toString())
          : null,
    );
  }

  Widget _buildBadge(BuildContext context, String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.purple,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}

class MenuActionCard extends StatelessWidget {
  final VoidCallback onTap;
  final bool isEnabled;

  const MenuActionCard({
    super.key,
    required this.onTap,
    this.isEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return QuickActionCard(
      title: 'Cardápio',
      subtitle: 'Ver itens',
      icon: Icons.restaurant_menu,
      color: Colors.teal,
      onTap: onTap,
      isEnabled: isEnabled,
    );
  }
}

class ReportsActionCard extends StatelessWidget {
  final VoidCallback onTap;
  final bool isEnabled;

  const ReportsActionCard({
    super.key,
    required this.onTap,
    this.isEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return QuickActionCard(
      title: 'Relatórios',
      subtitle: 'Ver dados',
      icon: Icons.analytics,
      color: Colors.indigo,
      onTap: onTap,
      isEnabled: isEnabled,
    );
  }
}

// Grid layout for quick actions
class QuickActionsGrid extends StatelessWidget {
  final List<Widget> actions;
  final int crossAxisCount;
  final double childAspectRatio;
  final double spacing;

  const QuickActionsGrid({
    super.key,
    required this.actions,
    this.crossAxisCount = 2,
    this.childAspectRatio = 1.2,
    this.spacing = 12,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: crossAxisCount,
      childAspectRatio: childAspectRatio,
      crossAxisSpacing: spacing,
      mainAxisSpacing: spacing,
      children: actions,
    );
  }
}