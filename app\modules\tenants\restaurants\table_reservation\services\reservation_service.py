import uuid
import logging
import random
import string
from typing import List, Optional, Dict, Any, Tu<PERSON>
from datetime import datetime, timedelta
from sqlalchemy import select, update as sqlalchemy_update, delete, and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.exc import IntegrityError
from fastapi import HTTPException, status

from app.modules.tenants.restaurants.table_reservation.models.reservation import (
    Reservation,
    ReservationStatus,
    CustomerBlacklist,
    BlacklistType,
)
from app.modules.tenants.restaurants.table_reservation.schemas.reservation import (
    ReservationCreate,
    ReservationUpdate,
)
from app.modules.tenants.restaurants.table_management.models.table import Table, TableStatus
from app.modules.tenants.restaurants.table_management.services.table_service import TableService

# Import WebSocket utility for real-time updates
from app.websockets.manager import emit_to_tenant
from app.modules.tenants.restaurants.table_reservation.websockets.reservation_websockets import (
    emit_new_reservation,
    emit_reservation_update,
)

# Import CRM service for customer management
from app.modules.shared.crm.services.account_service import AccountService
from app.modules.shared.crm.models.account import Account

logger = logging.getLogger(__name__)


class ReservationService:
    """Service for managing restaurant reservations."""

    def __init__(self, db_session: Optional[AsyncSession] = None):
        """Initialize the service with an optional database session."""
        self.db = db_session
        self.table_service = TableService(db_session)
        self.account_service = AccountService()

    async def _generate_reservation_number(self, db: AsyncSession, tenant_id: uuid.UUID) -> str:
        """Generate a unique reservation number."""
        # Generate a random 6-character alphanumeric string
        chars = string.ascii_uppercase + string.digits
        while True:
            reservation_number = "".join(random.choice(chars) for _ in range(6))

            # Check if the reservation number already exists
            query = select(Reservation).where(
                Reservation.tenant_id == tenant_id,
                Reservation.reservation_number == reservation_number,
            )
            result = await db.execute(query)
            if not result.scalars().first():
                return reservation_number

    async def _check_table_availability(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID,
        table_id: uuid.UUID,
        reservation_date: datetime,
        duration_minutes: int,
        exclude_reservation_id: Optional[uuid.UUID] = None,
    ) -> bool:
        """Check if a table is available for the specified time slot."""
        # Calculate end time
        end_time = reservation_date + timedelta(minutes=duration_minutes)

        # Check if the table exists and is active
        table_query = select(Table).where(
            Table.id == table_id,
            Table.tenant_id == tenant_id,
            Table.is_active == True,
        )
        table_result = await db.execute(table_query)
        table = table_result.scalars().first()

        if not table:
            return False

        # Check if the table is out of service
        if table.status == TableStatus.OUT_OF_SERVICE:
            return False

        # Check for overlapping reservations
        query = select(Reservation).where(
            Reservation.tenant_id == tenant_id,
            Reservation.table_id == table_id,
            Reservation.status.in_(
                [
                    ReservationStatus.PENDING,
                    ReservationStatus.CONFIRMED,
                    ReservationStatus.SEATED,
                ]
            ),
            or_(
                # Reservation starts during our time slot
                and_(
                    Reservation.reservation_date >= reservation_date,
                    Reservation.reservation_date < end_time,
                ),
                # Reservation ends during our time slot
                and_(
                    Reservation.reservation_date
                    + func.make_interval(mins=Reservation.duration_minutes)
                    > reservation_date,
                    Reservation.reservation_date <= reservation_date,
                ),
            ),
        )

        # Exclude the current reservation if updating
        if exclude_reservation_id:
            query = query.where(Reservation.id != exclude_reservation_id)

        result = await db.execute(query)
        overlapping_reservations = result.scalars().all()

        return len(overlapping_reservations) == 0

    async def _find_matching_customer(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID,
        guest_email: Optional[str] = None,
        guest_phone: Optional[str] = None,
    ) -> Optional[Account]:
        """Find a matching customer based on email or phone."""
        if not guest_email and not guest_phone:
            return None

        query = select(Account).where(Account.tenant_id == tenant_id)

        if guest_email:
            query = query.where(Account.email == guest_email)
        elif guest_phone:
            query = query.where(Account.phone == guest_phone)

        result = await db.execute(query)
        return result.scalars().first()

    async def _check_blacklist(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID,
        customer_id: Optional[uuid.UUID] = None,
        guest_email: Optional[str] = None,
        guest_phone: Optional[str] = None,
    ) -> Tuple[bool, Optional[CustomerBlacklist]]:
        """Check if a customer is blacklisted."""
        if not customer_id and not guest_email and not guest_phone:
            return False, None

        query = select(CustomerBlacklist).where(CustomerBlacklist.tenant_id == tenant_id)

        conditions = []
        if customer_id:
            conditions.append(CustomerBlacklist.customer_id == customer_id)
        if guest_email:
            conditions.append(CustomerBlacklist.guest_email == guest_email)
        if guest_phone:
            conditions.append(CustomerBlacklist.guest_phone == guest_phone)

        query = query.where(or_(*conditions))
        result = await db.execute(query)
        blacklist_entry = result.scalars().first()

        if blacklist_entry and blacklist_entry.is_active:
            return True, blacklist_entry

        return False, blacklist_entry

    async def create_reservation(
        self,
        db: AsyncSession,
        reservation_in: ReservationCreate,
        tenant_id: uuid.UUID,
        user_id: Optional[uuid.UUID] = None,
    ) -> Reservation:
        """Create a new reservation for the specified tenant."""
        try:
            # Check if the customer is blacklisted
            is_blacklisted, blacklist_entry = await self._check_blacklist(
                db,
                tenant_id,
                reservation_in.customer_id,
                reservation_in.guest_email,
                reservation_in.guest_phone,
            )

            if is_blacklisted:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Customer is blacklisted and cannot make reservations",
                )

            # Check if we need to require a deposit based on blacklist
            deposit_required = reservation_in.deposit_required
            deposit_amount = reservation_in.deposit_amount

            if blacklist_entry and not is_blacklisted and blacklist_entry.require_deposit:
                deposit_required = True
                deposit_amount = blacklist_entry.deposit_amount

            # Check table availability if a table is specified
            if reservation_in.table_id:
                is_available = await self._check_table_availability(
                    db,
                    tenant_id,
                    reservation_in.table_id,
                    reservation_in.reservation_date,
                    reservation_in.duration_minutes,
                )

                if not is_available:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Table is not available for the specified time slot",
                    )

            # Try to find a matching customer if not provided
            customer_id = reservation_in.customer_id
            if not customer_id and (reservation_in.guest_email or reservation_in.guest_phone):
                matching_customer = await self._find_matching_customer(
                    db,
                    tenant_id,
                    reservation_in.guest_email,
                    reservation_in.guest_phone,
                )

                if matching_customer:
                    customer_id = matching_customer.id

            # Generate reservation number
            reservation_number = await self._generate_reservation_number(db, tenant_id)

            # Create reservation object
            reservation_data = reservation_in.model_dump(exclude_unset=True)

            # Override with our values
            if customer_id:
                reservation_data["customer_id"] = customer_id

            db_reservation = Reservation(
                **reservation_data,
                tenant_id=tenant_id,
                reservation_number=reservation_number,
                status=ReservationStatus.PENDING,
                deposit_required=deposit_required,
                deposit_amount=deposit_amount,
            )

            # Add reservation to database
            db.add(db_reservation)
            await db.commit()
            await db.refresh(db_reservation)

            # Emit WebSocket event for real-time updates
            try:
                await emit_new_reservation(tenant_id, db_reservation)
            except Exception as ws_error:
                logger.error(f"Failed to emit WebSocket event for new reservation: {ws_error}")

            logger.info(f"Created reservation {db_reservation.id} for tenant {tenant_id}")
            return db_reservation

        except IntegrityError as e:
            await db.rollback()
            logger.error(f"Integrity error creating reservation for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Error creating reservation. Check input data.",
            )
        except HTTPException:
            await db.rollback()
            raise
        except Exception as e:
            await db.rollback()
            logger.exception(f"Unexpected error creating reservation for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def get_reservation(
        self,
        db: AsyncSession,
        reservation_id: uuid.UUID,
        tenant_id: uuid.UUID,
    ) -> Optional[Reservation]:
        """Get a reservation by ID for the specified tenant."""
        try:
            query = (
                select(Reservation)
                .options(
                    selectinload(Reservation.table),
                    selectinload(Reservation.customer),
                )
                .where(Reservation.id == reservation_id, Reservation.tenant_id == tenant_id)
            )
            result = await db.execute(query)
            return result.scalars().first()
        except Exception as e:
            logger.exception(
                f"Error getting reservation {reservation_id} for tenant {tenant_id}: {e}"
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def get_reservations(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID,
        table_id: Optional[uuid.UUID] = None,
        customer_id: Optional[uuid.UUID] = None,
        status: Optional[ReservationStatus] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> List[Reservation]:
        """Get reservations for the specified tenant with optional filters."""
        try:
            query = (
                select(Reservation)
                .options(
                    selectinload(Reservation.table),
                    selectinload(Reservation.customer),
                )
                .where(Reservation.tenant_id == tenant_id)
                .order_by(Reservation.reservation_date)
                .offset(skip)
                .limit(limit)
            )

            # Apply filters if provided
            if table_id:
                query = query.where(Reservation.table_id == table_id)
            if customer_id:
                query = query.where(Reservation.customer_id == customer_id)
            if status:
                query = query.where(Reservation.status == status)
            if date_from:
                query = query.where(Reservation.reservation_date >= date_from)
            if date_to:
                query = query.where(Reservation.reservation_date <= date_to)

            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.exception(f"Error getting reservations for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def update_reservation(
        self,
        db: AsyncSession,
        reservation_id: uuid.UUID,
        reservation_in: ReservationUpdate,
        tenant_id: uuid.UUID,
    ) -> Optional[Reservation]:
        """Update a reservation for the specified tenant."""
        try:
            # Check if reservation exists and belongs to tenant
            db_reservation = await self.get_reservation(db, reservation_id, tenant_id)
            if not db_reservation:
                return None

            # Get update data
            update_data = reservation_in.model_dump(exclude_unset=True)

            # Check table availability if changing table or time
            if (
                "table_id" in update_data
                or "reservation_date" in update_data
                or "duration_minutes" in update_data
            ):
                table_id = update_data.get("table_id", db_reservation.table_id)
                reservation_date = update_data.get(
                    "reservation_date", db_reservation.reservation_date
                )
                duration_minutes = update_data.get(
                    "duration_minutes", db_reservation.duration_minutes
                )

                if table_id:
                    is_available = await self._check_table_availability(
                        db,
                        tenant_id,
                        table_id,
                        reservation_date,
                        duration_minutes,
                        exclude_reservation_id=reservation_id,
                    )

                    if not is_available:
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail="Table is not available for the specified time slot",
                        )

            # Update reservation
            query = (
                sqlalchemy_update(Reservation)
                .where(Reservation.id == reservation_id, Reservation.tenant_id == tenant_id)
                .values(**update_data)
                .execution_options(synchronize_session="fetch")
            )
            await db.execute(query)
            await db.commit()

            # Refresh reservation data
            db_reservation = await self.get_reservation(db, reservation_id, tenant_id)

            # Emit WebSocket event for status change
            try:
                await emit_reservation_update(tenant_id, db_reservation)
            except Exception as ws_error:
                logger.error(f"Failed to emit WebSocket event for reservation update: {ws_error}")

            logger.info(f"Updated reservation {reservation_id} for tenant {tenant_id}")
            return db_reservation

        except IntegrityError as e:
            await db.rollback()
            logger.error(
                f"Integrity error updating reservation {reservation_id} for tenant {tenant_id}: {e}"
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Error updating reservation. Check input data.",
            )
        except HTTPException:
            await db.rollback()
            raise
        except Exception as e:
            await db.rollback()
            logger.exception(
                f"Unexpected error updating reservation {reservation_id} for tenant {tenant_id}: {e}"
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def delete_reservation(
        self,
        db: AsyncSession,
        reservation_id: uuid.UUID,
        tenant_id: uuid.UUID,
    ) -> bool:
        """Delete a reservation for the specified tenant."""
        try:
            # Check if reservation exists and belongs to tenant
            db_reservation = await self.get_reservation(db, reservation_id, tenant_id)
            if not db_reservation:
                return False

            # Delete reservation
            query = (
                delete(Reservation)
                .where(Reservation.id == reservation_id, Reservation.tenant_id == tenant_id)
                .execution_options(synchronize_session="fetch")
            )
            await db.execute(query)
            await db.commit()

            logger.info(f"Deleted reservation {reservation_id} for tenant {tenant_id}")
            return True

        except Exception as e:
            await db.rollback()
            logger.exception(
                f"Error deleting reservation {reservation_id} for tenant {tenant_id}: {e}"
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def update_reservation_status(
        self,
        db: AsyncSession,
        reservation_id: uuid.UUID,
        new_status: ReservationStatus,
        tenant_id: uuid.UUID,
    ) -> Optional[Reservation]:
        """Update the status of a reservation for the specified tenant."""
        try:
            # Check if reservation exists and belongs to tenant
            db_reservation = await self.get_reservation(db, reservation_id, tenant_id)
            if not db_reservation:
                return None

            # Update reservation status
            query = (
                sqlalchemy_update(Reservation)
                .where(Reservation.id == reservation_id, Reservation.tenant_id == tenant_id)
                .values(status=new_status)
                .execution_options(synchronize_session="fetch")
            )
            await db.execute(query)
            await db.commit()

            # Refresh reservation data
            db_reservation = await self.get_reservation(db, reservation_id, tenant_id)

            # Update table status if needed
            if db_reservation.table_id:
                if new_status == ReservationStatus.SEATED:
                    await self.table_service.update_table_status(
                        db, db_reservation.table_id, TableStatus.OCCUPIED, tenant_id
                    )
                elif new_status in [
                    ReservationStatus.COMPLETED,
                    ReservationStatus.CANCELLED,
                    ReservationStatus.NO_SHOW,
                ]:
                    await self.table_service.update_table_status(
                        db, db_reservation.table_id, TableStatus.AVAILABLE, tenant_id
                    )

            # Emit WebSocket event for status change
            try:
                await emit_reservation_update(tenant_id, db_reservation)
            except Exception as ws_error:
                logger.error(
                    f"Failed to emit WebSocket event for reservation status update: {ws_error}"
                )

            logger.info(
                f"Updated reservation {reservation_id} status to {new_status} for tenant {tenant_id}"
            )
            return db_reservation

        except Exception as e:
            await db.rollback()
            logger.exception(
                f"Error updating reservation {reservation_id} status for tenant {tenant_id}: {e}"
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def find_available_tables(
        self,
        db: AsyncSession,
        tenant_id: uuid.UUID,
        reservation_date: datetime,
        duration_minutes: int,
        party_size: int,
        layout_id: Optional[uuid.UUID] = None,
    ) -> List[Table]:
        """Find available tables for the specified time slot."""
        try:
            # Get all active tables that can accommodate the party size
            tables_query = select(Table).where(
                Table.tenant_id == tenant_id,
                Table.is_active == True,
                Table.status != TableStatus.OUT_OF_SERVICE,
                Table.capacity >= party_size,
            )

            if layout_id:
                tables_query = tables_query.where(Table.layout_id == layout_id)

            tables_result = await db.execute(tables_query)
            tables = tables_result.scalars().all()

            # Calculate end time
            end_time = reservation_date + timedelta(minutes=duration_minutes)

            # Get all reservations that overlap with the requested time slot
            reservations_query = select(Reservation).where(
                Reservation.tenant_id == tenant_id,
                Reservation.status.in_(
                    [
                        ReservationStatus.PENDING,
                        ReservationStatus.CONFIRMED,
                        ReservationStatus.SEATED,
                    ]
                ),
                or_(
                    # Reservation starts during our time slot
                    and_(
                        Reservation.reservation_date >= reservation_date,
                        Reservation.reservation_date < end_time,
                    ),
                    # Reservation ends during our time slot
                    and_(
                        Reservation.reservation_date
                        + func.make_interval(mins=Reservation.duration_minutes)
                        > reservation_date,
                        Reservation.reservation_date <= reservation_date,
                    ),
                ),
            )

            reservations_result = await db.execute(reservations_query)
            reservations = reservations_result.scalars().all()

            # Get table IDs that are already reserved
            reserved_table_ids = {r.table_id for r in reservations if r.table_id}

            # Filter out reserved tables
            available_tables = [t for t in tables if t.id not in reserved_table_ids]

            return available_tables

        except Exception as e:
            logger.exception(f"Error finding available tables for tenant {tenant_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )


# Create a singleton instance
reservation_service = ReservationService()
