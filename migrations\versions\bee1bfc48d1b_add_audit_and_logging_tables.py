"""Add audit and logging tables

Revision ID: bee1bfc48d1b
Revises: 5dae8512970a
Create Date: 2025-06-27 16:16:59.495613

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'bee1bfc48d1b'
down_revision: Union[str, None] = '5dae8512970a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('audit_logs',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('timestamp', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=True),
    sa.Column('session_id', sa.String(length=255), nullable=True),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.Text(), nullable=True),
    sa.Column('action', sa.String(length=50), nullable=False),
    sa.Column('resource', sa.String(length=50), nullable=False),
    sa.Column('resource_id', sa.String(length=255), nullable=True),
    sa.Column('tenant_id', sa.UUID(), nullable=True),
    sa.Column('operation_type', sa.String(length=20), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('old_values', sa.JSON(), nullable=True),
    sa.Column('new_values', sa.JSON(), nullable=True),
    sa.Column('details', sa.JSON(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('request_id', sa.String(length=255), nullable=True),
    sa.Column('correlation_id', sa.String(length=255), nullable=True),
    sa.Column('duration_ms', sa.String(length=20), nullable=True),
    sa.Column('security_level', sa.String(length=20), nullable=False),
    sa.Column('compliance_tags', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_audit_action_resource', 'audit_logs', ['action', 'resource'], unique=False)
    op.create_index(op.f('ix_audit_logs_action'), 'audit_logs', ['action'], unique=False)
    op.create_index(op.f('ix_audit_logs_correlation_id'), 'audit_logs', ['correlation_id'], unique=False)
    op.create_index(op.f('ix_audit_logs_id'), 'audit_logs', ['id'], unique=False)
    op.create_index(op.f('ix_audit_logs_operation_type'), 'audit_logs', ['operation_type'], unique=False)
    op.create_index(op.f('ix_audit_logs_request_id'), 'audit_logs', ['request_id'], unique=False)
    op.create_index(op.f('ix_audit_logs_resource'), 'audit_logs', ['resource'], unique=False)
    op.create_index(op.f('ix_audit_logs_resource_id'), 'audit_logs', ['resource_id'], unique=False)
    op.create_index(op.f('ix_audit_logs_session_id'), 'audit_logs', ['session_id'], unique=False)
    op.create_index(op.f('ix_audit_logs_tenant_id'), 'audit_logs', ['tenant_id'], unique=False)
    op.create_index(op.f('ix_audit_logs_timestamp'), 'audit_logs', ['timestamp'], unique=False)
    op.create_index(op.f('ix_audit_logs_user_id'), 'audit_logs', ['user_id'], unique=False)
    op.create_index('ix_audit_operation_type', 'audit_logs', ['operation_type'], unique=False)
    op.create_index('ix_audit_resource_id_timestamp', 'audit_logs', ['resource', 'resource_id', 'timestamp'], unique=False)
    op.create_index('ix_audit_resource_timestamp', 'audit_logs', ['resource', 'timestamp'], unique=False)
    op.create_index('ix_audit_security_level', 'audit_logs', ['security_level'], unique=False)
    op.create_index('ix_audit_status_timestamp', 'audit_logs', ['status', 'timestamp'], unique=False)
    op.create_index('ix_audit_tenant_resource_timestamp', 'audit_logs', ['tenant_id', 'resource', 'timestamp'], unique=False)
    op.create_index('ix_audit_tenant_timestamp', 'audit_logs', ['tenant_id', 'timestamp'], unique=False)
    op.create_index('ix_audit_user_action_timestamp', 'audit_logs', ['user_id', 'action', 'timestamp'], unique=False)
    op.create_index('ix_audit_user_timestamp', 'audit_logs', ['user_id', 'timestamp'], unique=False)
    op.create_table('system_logs',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('timestamp', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('level', sa.String(length=20), nullable=False),
    sa.Column('category', sa.String(length=50), nullable=False),
    sa.Column('logger_name', sa.String(length=255), nullable=False),
    sa.Column('module', sa.String(length=255), nullable=True),
    sa.Column('function', sa.String(length=255), nullable=True),
    sa.Column('line_number', sa.Integer(), nullable=True),
    sa.Column('message', sa.Text(), nullable=False),
    sa.Column('details', sa.JSON(), nullable=True),
    sa.Column('user_id', sa.UUID(), nullable=True),
    sa.Column('tenant_id', sa.UUID(), nullable=True),
    sa.Column('session_id', sa.String(length=255), nullable=True),
    sa.Column('request_id', sa.String(length=255), nullable=True),
    sa.Column('correlation_id', sa.String(length=255), nullable=True),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.Text(), nullable=True),
    sa.Column('exception_type', sa.String(length=255), nullable=True),
    sa.Column('exception_message', sa.Text(), nullable=True),
    sa.Column('stack_trace', sa.Text(), nullable=True),
    sa.Column('duration_ms', sa.String(length=20), nullable=True),
    sa.Column('memory_usage_mb', sa.String(length=20), nullable=True),
    sa.Column('cpu_usage_percent', sa.String(length=10), nullable=True),
    sa.Column('business_process', sa.String(length=100), nullable=True),
    sa.Column('workflow_step', sa.String(length=100), nullable=True),
    sa.Column('external_service', sa.String(length=100), nullable=True),
    sa.Column('api_endpoint', sa.String(length=500), nullable=True),
    sa.Column('http_method', sa.String(length=10), nullable=True),
    sa.Column('http_status_code', sa.Integer(), nullable=True),
    sa.Column('audit_log_id', sa.UUID(), nullable=True),
    sa.Column('is_alert', sa.Boolean(), nullable=True),
    sa.Column('alert_severity', sa.String(length=20), nullable=True),
    sa.Column('is_resolved', sa.Boolean(), nullable=True),
    sa.Column('resolved_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('resolved_by', sa.UUID(), nullable=True),
    sa.Column('environment', sa.String(length=50), nullable=True),
    sa.Column('version', sa.String(length=50), nullable=True),
    sa.Column('tags', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_logs_alert_severity', 'system_logs', ['is_alert', 'alert_severity', 'timestamp'], unique=False)
    op.create_index('ix_logs_audit_correlation', 'system_logs', ['audit_log_id'], unique=False)
    op.create_index('ix_logs_business_process', 'system_logs', ['business_process', 'timestamp'], unique=False)
    op.create_index('ix_logs_category_timestamp', 'system_logs', ['category', 'timestamp'], unique=False)
    op.create_index('ix_logs_correlation', 'system_logs', ['correlation_id', 'timestamp'], unique=False)
    op.create_index('ix_logs_errors', 'system_logs', ['level', 'exception_type', 'timestamp'], unique=False)
    op.create_index('ix_logs_external_service', 'system_logs', ['external_service', 'timestamp'], unique=False)
    op.create_index('ix_logs_level_timestamp', 'system_logs', ['level', 'timestamp'], unique=False)
    op.create_index('ix_logs_logger_timestamp', 'system_logs', ['logger_name', 'timestamp'], unique=False)
    op.create_index('ix_logs_performance', 'system_logs', ['category', 'duration_ms', 'timestamp'], unique=False)
    op.create_index('ix_logs_tenant_timestamp', 'system_logs', ['tenant_id', 'timestamp'], unique=False)
    op.create_index('ix_logs_unresolved_alerts', 'system_logs', ['is_alert', 'is_resolved', 'timestamp'], unique=False)
    op.create_index('ix_logs_user_timestamp', 'system_logs', ['user_id', 'timestamp'], unique=False)
    op.create_index(op.f('ix_system_logs_audit_log_id'), 'system_logs', ['audit_log_id'], unique=False)
    op.create_index(op.f('ix_system_logs_business_process'), 'system_logs', ['business_process'], unique=False)
    op.create_index(op.f('ix_system_logs_category'), 'system_logs', ['category'], unique=False)
    op.create_index(op.f('ix_system_logs_correlation_id'), 'system_logs', ['correlation_id'], unique=False)
    op.create_index(op.f('ix_system_logs_environment'), 'system_logs', ['environment'], unique=False)
    op.create_index(op.f('ix_system_logs_external_service'), 'system_logs', ['external_service'], unique=False)
    op.create_index(op.f('ix_system_logs_id'), 'system_logs', ['id'], unique=False)
    op.create_index(op.f('ix_system_logs_is_alert'), 'system_logs', ['is_alert'], unique=False)
    op.create_index(op.f('ix_system_logs_is_resolved'), 'system_logs', ['is_resolved'], unique=False)
    op.create_index(op.f('ix_system_logs_level'), 'system_logs', ['level'], unique=False)
    op.create_index(op.f('ix_system_logs_logger_name'), 'system_logs', ['logger_name'], unique=False)
    op.create_index(op.f('ix_system_logs_module'), 'system_logs', ['module'], unique=False)
    op.create_index(op.f('ix_system_logs_request_id'), 'system_logs', ['request_id'], unique=False)
    op.create_index(op.f('ix_system_logs_session_id'), 'system_logs', ['session_id'], unique=False)
    op.create_index(op.f('ix_system_logs_tenant_id'), 'system_logs', ['tenant_id'], unique=False)
    op.create_index(op.f('ix_system_logs_timestamp'), 'system_logs', ['timestamp'], unique=False)
    op.create_index(op.f('ix_system_logs_user_id'), 'system_logs', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_system_logs_user_id'), table_name='system_logs')
    op.drop_index(op.f('ix_system_logs_timestamp'), table_name='system_logs')
    op.drop_index(op.f('ix_system_logs_tenant_id'), table_name='system_logs')
    op.drop_index(op.f('ix_system_logs_session_id'), table_name='system_logs')
    op.drop_index(op.f('ix_system_logs_request_id'), table_name='system_logs')
    op.drop_index(op.f('ix_system_logs_module'), table_name='system_logs')
    op.drop_index(op.f('ix_system_logs_logger_name'), table_name='system_logs')
    op.drop_index(op.f('ix_system_logs_level'), table_name='system_logs')
    op.drop_index(op.f('ix_system_logs_is_resolved'), table_name='system_logs')
    op.drop_index(op.f('ix_system_logs_is_alert'), table_name='system_logs')
    op.drop_index(op.f('ix_system_logs_id'), table_name='system_logs')
    op.drop_index(op.f('ix_system_logs_external_service'), table_name='system_logs')
    op.drop_index(op.f('ix_system_logs_environment'), table_name='system_logs')
    op.drop_index(op.f('ix_system_logs_correlation_id'), table_name='system_logs')
    op.drop_index(op.f('ix_system_logs_category'), table_name='system_logs')
    op.drop_index(op.f('ix_system_logs_business_process'), table_name='system_logs')
    op.drop_index(op.f('ix_system_logs_audit_log_id'), table_name='system_logs')
    op.drop_index('ix_logs_user_timestamp', table_name='system_logs')
    op.drop_index('ix_logs_unresolved_alerts', table_name='system_logs')
    op.drop_index('ix_logs_tenant_timestamp', table_name='system_logs')
    op.drop_index('ix_logs_performance', table_name='system_logs')
    op.drop_index('ix_logs_logger_timestamp', table_name='system_logs')
    op.drop_index('ix_logs_level_timestamp', table_name='system_logs')
    op.drop_index('ix_logs_external_service', table_name='system_logs')
    op.drop_index('ix_logs_errors', table_name='system_logs')
    op.drop_index('ix_logs_correlation', table_name='system_logs')
    op.drop_index('ix_logs_category_timestamp', table_name='system_logs')
    op.drop_index('ix_logs_business_process', table_name='system_logs')
    op.drop_index('ix_logs_audit_correlation', table_name='system_logs')
    op.drop_index('ix_logs_alert_severity', table_name='system_logs')
    op.drop_table('system_logs')
    op.drop_index('ix_audit_user_timestamp', table_name='audit_logs')
    op.drop_index('ix_audit_user_action_timestamp', table_name='audit_logs')
    op.drop_index('ix_audit_tenant_timestamp', table_name='audit_logs')
    op.drop_index('ix_audit_tenant_resource_timestamp', table_name='audit_logs')
    op.drop_index('ix_audit_status_timestamp', table_name='audit_logs')
    op.drop_index('ix_audit_security_level', table_name='audit_logs')
    op.drop_index('ix_audit_resource_timestamp', table_name='audit_logs')
    op.drop_index('ix_audit_resource_id_timestamp', table_name='audit_logs')
    op.drop_index('ix_audit_operation_type', table_name='audit_logs')
    op.drop_index(op.f('ix_audit_logs_user_id'), table_name='audit_logs')
    op.drop_index(op.f('ix_audit_logs_timestamp'), table_name='audit_logs')
    op.drop_index(op.f('ix_audit_logs_tenant_id'), table_name='audit_logs')
    op.drop_index(op.f('ix_audit_logs_session_id'), table_name='audit_logs')
    op.drop_index(op.f('ix_audit_logs_resource_id'), table_name='audit_logs')
    op.drop_index(op.f('ix_audit_logs_resource'), table_name='audit_logs')
    op.drop_index(op.f('ix_audit_logs_request_id'), table_name='audit_logs')
    op.drop_index(op.f('ix_audit_logs_operation_type'), table_name='audit_logs')
    op.drop_index(op.f('ix_audit_logs_id'), table_name='audit_logs')
    op.drop_index(op.f('ix_audit_logs_correlation_id'), table_name='audit_logs')
    op.drop_index(op.f('ix_audit_logs_action'), table_name='audit_logs')
    op.drop_index('ix_audit_action_resource', table_name='audit_logs')
    op.drop_table('audit_logs')
    # ### end Alembic commands ###
