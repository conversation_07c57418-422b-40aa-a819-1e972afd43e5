import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../models/order_model.dart';
import '../models/menu_item_model.dart';
import '../models/table_model.dart';
import '../services/storage_service.dart';
import '../constants/app_constants.dart';
import 'auth_provider.dart';

// Orders state
class OrdersState {
  final List<OrderModel> orders;
  final bool isLoading;
  final String? error;
  final OrderModel? selectedOrder;

  const OrdersState({
    this.orders = const [],
    this.isLoading = false,
    this.error,
    this.selectedOrder,
  });

  OrdersState copyWith({
    List<OrderModel>? orders,
    bool? isLoading,
    String? error,
    OrderModel? selectedOrder,
  }) {
    return OrdersState(
      orders: orders ?? this.orders,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      selectedOrder: selectedOrder ?? this.selectedOrder,
    );
  }
}

// Orders notifier
class OrdersNotifier extends StateNotifier<OrdersState> {
  OrdersNotifier() : super(const OrdersState()) {
    loadOrders();
  }

  final _uuid = const Uuid();

  Future<void> loadOrders() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final ordersData = await StorageService.getAllOrders();
      final orders = ordersData.map((orderJson) => OrderModel.fromJson(orderJson)).toList();
      state = state.copyWith(
        orders: orders,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Erro ao carregar pedidos: ${e.toString()}',
      );
    }
  }

  Future<OrderModel?> createOrder({
    required String orderType,
    required String waiterId,
    required String waiterName,
    String? tableId,
    String? tableName,
    String? customerName,
    String? customerPhone,
    String? deliveryAddress,
    String? notes,
  }) async {
    try {
      final orderNumber = 'ORD${DateTime.now().millisecondsSinceEpoch.toString().substring(8)}';
      
      final order = OrderModel(
        id: _uuid.v4(),
        tableNumber: orderNumber,
        orderType: orderType,
        status: 'pending',



        customerName: customerName,
        deliveryAddress: deliveryAddress,
        items: [],
        totalAmount: 0.0,
        discount: 0.0,
        paymentMethod: 'pending',
        createdAt: DateTime.now(),
        notes: notes,
      );

      await StorageService.saveOrder(order.id, order.toJson());
      
      final updatedOrders = [...state.orders, order];
      state = state.copyWith(orders: updatedOrders);
      
      return order;
    } catch (e) {
      state = state.copyWith(
        error: 'Erro ao criar pedido: ${e.toString()}',
      );
      return null;
    }
  }

  Future<bool> addItemToOrder(String orderId, MenuItemModel menuItem, 
      {int quantity = 1, List<String> modifiers = const []}) async {
    try {
      final orderIndex = state.orders.indexWhere((o) => o.id == orderId);
      if (orderIndex == -1) return false;

      final order = state.orders[orderIndex];
      
      // Calculate item price with modifiers
      double itemPrice = menuItem.price;
      // Modifiers are just strings, no additional price calculation needed
      
      final orderItem = OrderItem(
        id: _uuid.v4(),
        menuItemId: menuItem.id,
        name: menuItem.name,
        price: itemPrice,
        quantity: quantity,
        unitPrice: itemPrice,
        modifiers: modifiers,
        notes: '',
      );

      final updatedItems = [...order.items, orderItem];
      final updatedOrder = order.copyWith(
        items: updatedItems,
      );
      
      // Recalculate totals
      final tax = updatedOrder.subtotal * 0.1; // 10% tax
      final total = updatedOrder.subtotal + tax - updatedOrder.discount;
      
      final finalOrder = updatedOrder.copyWith(
        updatedAt: DateTime.now(),
      );

      await StorageService.saveOrder(finalOrder.id, finalOrder.toJson());
      
      final updatedOrders = [...state.orders];
      updatedOrders[orderIndex] = finalOrder;
      
      state = state.copyWith(orders: updatedOrders);
      return true;
    } catch (e) {
      state = state.copyWith(
        error: 'Erro ao adicionar item: ${e.toString()}',
      );
      return false;
    }
  }

  Future<bool> removeItemFromOrder(String orderId, String itemId) async {
    try {
      final orderIndex = state.orders.indexWhere((o) => o.id == orderId);
      if (orderIndex == -1) return false;

      final order = state.orders[orderIndex];
      final updatedItems = order.items.where((item) => item.id != itemId).toList();
      
      final updatedOrder = order.copyWith(
        items: updatedItems,
      );
      
      // Recalculate totals
      final tax = updatedOrder.subtotal * 0.1;
      final total = updatedOrder.subtotal + tax - updatedOrder.discount;
      
      final finalOrder = updatedOrder.copyWith(
        updatedAt: DateTime.now(),
      );

      await StorageService.saveOrder(finalOrder.id, finalOrder.toJson());
      
      final updatedOrders = [...state.orders];
      updatedOrders[orderIndex] = finalOrder;
      
      state = state.copyWith(orders: updatedOrders);
      return true;
    } catch (e) {
      state = state.copyWith(
        error: 'Erro ao remover item: ${e.toString()}',
      );
      return false;
    }
  }

  Future<bool> updateOrderStatus(String orderId, String newStatus) async {
    try {
      final orderIndex = state.orders.indexWhere((o) => o.id == orderId);
      if (orderIndex == -1) return false;

      final order = state.orders[orderIndex];
      final updatedOrder = order.copyWith(
        status: newStatus,
        updatedAt: DateTime.now(),
      );

      await StorageService.saveOrder(updatedOrder.id, updatedOrder.toJson());
      
      final updatedOrders = [...state.orders];
      updatedOrders[orderIndex] = updatedOrder;
      
      state = state.copyWith(orders: updatedOrders);
      return true;
    } catch (e) {
      state = state.copyWith(
        error: 'Erro ao atualizar status: ${e.toString()}',
      );
      return false;
    }
  }

  Future<bool> updatePaymentMethod(String orderId, String paymentMethod) async {
    try {
      final orderIndex = state.orders.indexWhere((o) => o.id == orderId);
      if (orderIndex == -1) return false;

      final order = state.orders[orderIndex];
      final updatedOrder = order.copyWith(
        paymentMethod: paymentMethod,
        updatedAt: DateTime.now(),
      );

      await StorageService.saveOrder(updatedOrder.id, updatedOrder.toJson());
      
      final updatedOrders = [...state.orders];
      updatedOrders[orderIndex] = updatedOrder;
      
      state = state.copyWith(orders: updatedOrders);
      return true;
    } catch (e) {
      state = state.copyWith(
        error: 'Erro ao atualizar pagamento: ${e.toString()}',
      );
      return false;
    }
  }

  Future<bool> applyDiscount(String orderId, double discountAmount) async {
    try {
      final orderIndex = state.orders.indexWhere((o) => o.id == orderId);
      if (orderIndex == -1) return false;

      final order = state.orders[orderIndex];
      final total = order.subtotal + order.tax - discountAmount;
      
      final updatedOrder = order.copyWith(
        discount: discountAmount,
        updatedAt: DateTime.now(),
      );

      await StorageService.saveOrder(updatedOrder.id, updatedOrder.toJson());
      
      final updatedOrders = [...state.orders];
      updatedOrders[orderIndex] = updatedOrder;
      
      state = state.copyWith(orders: updatedOrders);
      return true;
    } catch (e) {
      state = state.copyWith(
        error: 'Erro ao aplicar desconto: ${e.toString()}',
      );
      return false;
    }
  }

  void selectOrder(OrderModel? order) {
    state = state.copyWith(selectedOrder: order);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  int _calculatePrepTime(List<OrderItem> items) {
    if (items.isEmpty) return 0;
    
    int totalTime = 0;
    for (final item in items) {
      totalTime += (item.menuItem.preparationTime * item.quantity).round();
    }
    
    // Add base preparation time and consider kitchen efficiency
    return (totalTime * 0.8).round() + 5; // 20% efficiency bonus + 5 min base
  }

  // Getters for filtered orders
  List<OrderModel> get pendingOrders => 
      state.orders.where((o) => o.status == OrderStatus.pending).toList();
  
  List<OrderModel> get preparingOrders => 
      state.orders.where((o) => o.status == OrderStatus.preparing).toList();
  
  List<OrderModel> get readyOrders => 
      state.orders.where((o) => o.status == OrderStatus.ready).toList();
  
  List<OrderModel> get completedOrders => 
      state.orders.where((o) => o.status == OrderStatus.completed).toList();
  
  List<OrderModel> get todayOrders {
    final today = DateTime.now();
    return state.orders.where((o) => 
        o.createdAt.year == today.year &&
        o.createdAt.month == today.month &&
        o.createdAt.day == today.day
    ).toList();
  }
}

// Orders provider
final ordersProvider = StateNotifierProvider<OrdersNotifier, OrdersState>((ref) {
  return OrdersNotifier();
});

// Convenience providers
final pendingOrdersProvider = Provider<List<OrderModel>>((ref) {
  return ref.watch(ordersProvider.notifier).pendingOrders;
});

final preparingOrdersProvider = Provider<List<OrderModel>>((ref) {
  return ref.watch(ordersProvider.notifier).preparingOrders;
});

final readyOrdersProvider = Provider<List<OrderModel>>((ref) {
  return ref.watch(ordersProvider.notifier).readyOrders;
});

final completedOrdersProvider = Provider<List<OrderModel>>((ref) {
  return ref.watch(ordersProvider.notifier).completedOrders;
});

final todayOrdersProvider = Provider<List<OrderModel>>((ref) {
  return ref.watch(ordersProvider.notifier).todayOrders;
});

final selectedOrderProvider = Provider<OrderModel?>((ref) {
  return ref.watch(ordersProvider).selectedOrder;
});