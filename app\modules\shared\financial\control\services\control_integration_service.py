"""Financial Control Integration Service."""

from typing import Optional, Dict, Any
from uuid import UUID
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from ..models.control_entry import FinancialControlEntry, ControlEntryType, ControlEntryStatus
from ..schemas.control_entry_schemas import ControlEntryCreate
from .control_service import ControlService


class ControlIntegrationService:
    """Service for integrating financial control with other modules."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.control_service = ControlService(db)
    
    async def create_entry_from_supplier_invoice(
        self,
        tenant_id: UUID,
        user_id: UUID,
        supplier_id: UUID,
        invoice_data: Dict[str, Any]
    ) -> FinancialControlEntry:
        """Create a control entry from a supplier invoice."""
        
        entry_data = ControlEntryCreate(
            entry_type=ControlEntryType.EXPENSE,
            amount=Decimal(str(invoice_data.get('total_amount', 0))),
            gross_amount=Decimal(str(invoice_data.get('gross_amount', 0))),
            tax_amount=Decimal(str(invoice_data.get('tax_amount', 0))),
            title=f"Supplier Invoice - {invoice_data.get('invoice_number', 'N/A')}",
            description=invoice_data.get('description', ''),
            reference_number=invoice_data.get('invoice_number'),
            external_reference=invoice_data.get('supplier_reference'),
            entry_date=invoice_data.get('invoice_date'),
            due_date=invoice_data.get('due_date'),
            supplier_id=supplier_id,
            status=ControlEntryStatus.PENDING,
            is_automated=True,
            notes=f"Auto-generated from supplier invoice: {invoice_data.get('invoice_number')}"
        )
        
        return await self.control_service.create_entry(entry_data, tenant_id, user_id)
    
    async def create_entry_from_order(
        self,
        tenant_id: UUID,
        user_id: UUID,
        order_id: UUID,
        order_data: Dict[str, Any]
    ) -> FinancialControlEntry:
        """Create a control entry from an order."""
        
        entry_data = ControlEntryCreate(
            entry_type=ControlEntryType.EXPENSE,
            amount=Decimal(str(order_data.get('total_amount', 0))),
            title=f"Purchase Order - {order_data.get('order_number', 'N/A')}",
            description=f"Order with {order_data.get('items_count', 0)} items",
            reference_number=order_data.get('order_number'),
            entry_date=order_data.get('order_date'),
            order_id=order_id,
            supplier_id=order_data.get('supplier_id'),
            status=ControlEntryStatus.PENDING,
            is_automated=True,
            notes=f"Auto-generated from purchase order: {order_data.get('order_number')}"
        )
        
        return await self.control_service.create_entry(entry_data, tenant_id, user_id)
    
    async def update_entry_from_payment(
        self,
        entry_id: UUID,
        tenant_id: UUID,
        user_id: UUID,
        payment_data: Dict[str, Any]
    ) -> Optional[FinancialControlEntry]:
        """Update a control entry when payment is made."""
        
        entry = await self.control_service.get_entry(entry_id, tenant_id)
        if not entry:
            return None
        
        return await self.control_service.mark_as_paid(
            entry_id=entry_id,
            tenant_id=tenant_id,
            user_id=user_id,
            payment_date=payment_data.get('payment_date'),
            payment_method=payment_data.get('payment_method'),
            payment_reference=payment_data.get('payment_reference')
        )
    
    async def sync_inventory_low_stock_to_shopping_list(
        self,
        tenant_id: UUID,
        user_id: UUID
    ) -> int:
        """Sync low stock inventory items to shopping list and create control entries."""
        
        # This would integrate with inventory and shopping list modules
        # For now, return 0 as placeholder
        return 0
    
    async def process_supplier_invoice_workflow(
        self,
        tenant_id: UUID,
        user_id: UUID,
        supplier_id: UUID,
        invoice_data: Dict[str, Any],
        auto_approve: bool = False
    ) -> Dict[str, Any]:
        """Process complete supplier invoice workflow."""
        
        # Create control entry
        control_entry = await self.create_entry_from_supplier_invoice(
            tenant_id, user_id, supplier_id, invoice_data
        )
        
        # Auto-approve if requested
        if auto_approve:
            # This would trigger approval workflow
            pass
        
        return {
            "control_entry_id": str(control_entry.id),
            "status": control_entry.status,
            "amount": float(control_entry.amount),
            "reference_number": control_entry.reference_number
        }
