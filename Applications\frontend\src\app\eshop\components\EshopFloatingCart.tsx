'use client';

import React from 'react';
import { ShoppingCartIcon, SparklesIcon } from '@heroicons/react/24/outline';
import { useEshopCart } from '@/contexts/EshopCartContext';
import { formatCurrency } from '@/lib/utils';

export function EshopFloatingCart() {
  const { cart, openCart, marketContext } = useEshopCart();

  const totalItems = cart?.total_items || 0;
  const totalAmount = cart?.total_amount || 0;

  if (totalItems === 0) return null;

  return (
    <div className="fixed bottom-6 right-6 z-40">
      <button
        onClick={openCart}
        className="group relative flex items-center space-x-3 rounded-full bg-indigo-600 px-6 py-4 text-white shadow-lg hover:bg-indigo-700 transition-all duration-200 hover:scale-105"
      >
        {/* Cart Icon with Badge */}
        <div className="relative">
          <ShoppingCartIcon className="h-6 w-6" />
          {totalItems > 0 && (
            <span className="absolute -top-2 -right-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full min-w-[1.25rem]">
              {totalItems > 99 ? '99+' : totalItems}
            </span>
          )}
        </div>

        {/* Cart Info */}
        <div className="flex flex-col items-start">
          <div className="flex items-center space-x-1">
            <span className="text-sm font-medium">
              {totalItems} {totalItems === 1 ? 'item' : 'itens'}
            </span>
            {marketContext === 'b2b' && (
              <SparklesIcon className="h-3 w-3 text-yellow-300" />
            )}
          </div>
          <span className="text-lg font-bold">
            {formatCurrency(totalAmount)}
          </span>
        </div>

        {/* Pulse Animation */}
        <div className="absolute inset-0 rounded-full bg-indigo-600 opacity-75 animate-ping"></div>
      </button>

      {/* Market Context Badge */}
      {marketContext === 'b2b' && (
        <div className="absolute -top-2 -left-2 bg-yellow-500 text-black text-xs font-bold px-2 py-1 rounded-full">
          B2B
        </div>
      )}
    </div>
  );
}
