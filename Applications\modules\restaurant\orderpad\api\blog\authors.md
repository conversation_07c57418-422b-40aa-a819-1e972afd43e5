# Blog - Authors

**Categoria:** Blog
**Módulo:** Authors
**Total de Endpoints:** 6
**Gerado em:** 08/06/2025, 17:26:01

## 📋 Endpoints

- [GET /api/modules/core/blog/authors/](#get-apimodulescoreblogauthors) - Get Authors
- [POST /api/modules/core/blog/authors/](#post-apimodulescoreblogauthors) - Create Author
- [GET /api/modules/core/blog/authors/slug/{slug}](#get-apimodulescoreblogauthorsslugslug) - Get Author By Slug
- [GET /api/modules/core/blog/authors/{author_id}](#get-apimodulescoreblogauthorsauthor-id) - Get Author
- [PUT /api/modules/core/blog/authors/{author_id}/link-user/{user_id}](#put-apimodulescoreblogauthorsauthor-idlink-useruser-id) - Link User To Author
- [DELETE /api/modules/core/blog/authors/{author_id}/unlink-user](#delete-apimodulescoreblogauthorsauthor-idunlink-user) - Unlink User From Author

## 📊 Schemas

### BlogAuthorCreate

**Descrição:** Schema for creating blog authors.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `slug` | string | ✅ | - |
| `display_name` | string | ✅ | - |
| `email` | unknown | ❌ | - |
| `bio` | unknown | ❌ | - |
| `avatar_url` | unknown | ❌ | - |
| `website_url` | unknown | ❌ | - |
| `twitter_handle` | unknown | ❌ | - |
| `linkedin_url` | unknown | ❌ | - |
| `github_url` | unknown | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `is_featured` | boolean | ❌ | - |
| `user_id` | unknown | ❌ | - |

### BlogAuthorProfile

**Descrição:** Schema for public author profile.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `slug` | string | ✅ | - |
| `display_name` | string | ✅ | - |
| `bio` | unknown | ✅ | - |
| `avatar_url` | unknown | ✅ | - |
| `website_url` | unknown | ✅ | - |
| `twitter_handle` | unknown | ✅ | - |
| `linkedin_url` | unknown | ✅ | - |
| `github_url` | unknown | ✅ | - |
| `post_count` | integer | ❌ | - |
| `total_views` | integer | ❌ | - |

### BlogAuthorRead

**Descrição:** Schema for reading blog authors.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `slug` | string | ✅ | - |
| `display_name` | string | ✅ | - |
| `email` | unknown | ❌ | - |
| `bio` | unknown | ❌ | - |
| `avatar_url` | unknown | ❌ | - |
| `website_url` | unknown | ❌ | - |
| `twitter_handle` | unknown | ❌ | - |
| `linkedin_url` | unknown | ❌ | - |
| `github_url` | unknown | ❌ | - |
| `is_active` | boolean | ❌ | - |
| `is_featured` | boolean | ❌ | - |
| `id` | string | ✅ | - |
| `user_id` | unknown | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/modules/core/blog/authors/ {#get-apimodulescoreblogauthors}

**Resumo:** Get Authors
**Descrição:** Get blog authors with filtering and pagination.

Results include statistics like post count and total views.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `skip` | integer | query | ❌ | Number of authors to skip |
| `limit` | integer | query | ❌ | Number of authors to return |
| `is_active` | string | query | ❌ | Filter by active status |
| `is_featured` | string | query | ❌ | Filter featured authors |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/authors/"
```

---

### POST /api/modules/core/blog/authors/ {#post-apimodulescoreblogauthors}

**Resumo:** Create Author
**Descrição:** Create a new blog author.

Requires authentication and admin privileges.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `authorization` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [BlogAuthorCreate](#blogauthorcreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogAuthorRead](#blogauthorread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/core/blog/authors/" \
  -H "Content-Type: application/json" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/core/blog/authors/slug/{slug} {#get-apimodulescoreblogauthorsslugslug}

**Resumo:** Get Author By Slug
**Descrição:** Get a blog author's public profile by slug.

Returns public information suitable for author profile pages.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `slug` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogAuthorProfile](#blogauthorprofile)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/authors/slug/{slug}"
```

---

### GET /api/modules/core/blog/authors/{author_id} {#get-apimodulescoreblogauthorsauthor-id}

**Resumo:** Get Author
**Descrição:** Get a specific blog author by ID.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `author_id` | string | path | ✅ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogAuthorRead](#blogauthorread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/core/blog/authors/{author_id}"
```

---

### PUT /api/modules/core/blog/authors/{author_id}/link-user/{user_id} {#put-apimodulescoreblogauthorsauthor-idlink-useruser-id}

**Resumo:** Link User To Author
**Descrição:** Link a system user to an existing blog author.

Requires admin privileges. This allows the user to be associated with the author profile.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `author_id` | string | path | ✅ | - |
| `user_id` | string | path | ✅ | - |
| `authorization` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogAuthorRead](#blogauthorread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/core/blog/authors/{author_id}/link-user/{user_id}"
```

---

### DELETE /api/modules/core/blog/authors/{author_id}/unlink-user {#delete-apimodulescoreblogauthorsauthor-idunlink-user}

**Resumo:** Unlink User From Author
**Descrição:** Unlink a system user from a blog author.

Requires admin privileges. This removes the association between the user and author.

**🔐 Autenticação:** Não requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `author_id` | string | path | ✅ | - |
| `authorization` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [BlogAuthorRead](#blogauthorread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/core/blog/authors/{author_id}/unlink-user"
```

---
