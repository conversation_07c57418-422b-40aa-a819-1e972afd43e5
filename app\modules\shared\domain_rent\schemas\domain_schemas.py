"""Domain schemas for the Domain Rent module."""

import uuid  # noqa: E402
from datetime import datetime
from typing import List, Optional, Dict, Any, Union

from pydantic import BaseModel, Field, EmailStr, HttpUrl, validator, ConfigDict  # noqa: E402

from app.modules.shared.domain_rent.models.domain_registration import DomainStatus  # noqa: E402


class DomainRegistrationBase(BaseModel):
    """Base schema for domain registration."""

    domain_name: str = Field(..., description="Domain name without TLD")
    tld: str = Field(..., description="Top-level domain (e.g., 'com', 'org')")
    registrar: str = Field(..., description="Registrar used for this domain")
    auto_renew: bool = Field(False, description="Whether to automatically renew the domain")
    whois_privacy_enabled: bool = Field(False, description="Whether WHOIS privacy is enabled")


class DomainRegistrationCreate(DomainRegistrationBase):
    """Schema for creating a domain registration."""

    user_id: uuid.UUID = Field(..., description="ID of the user who owns this domain")
    tenant_id: Optional[uuid.UUID] = Field(
        None, description="ID of the tenant this domain is associated with (optional)"
    )
    registration_date: datetime = Field(..., description="Date when domain was registered")
    expiry_date: datetime = Field(..., description="Date when domain will expire")
    status: DomainStatus = Field(DomainStatus.PENDING, description="Current status of the domain")
    registrar_data: Optional[str] = Field(None, description="Registrar-specific data (JSON string)")


class DomainRegistrationUpdate(BaseModel):
    """Schema for updating a domain registration."""

    auto_renew: Optional[bool] = Field(
        None, description="Whether to automatically renew the domain"
    )
    whois_privacy_enabled: Optional[bool] = Field(
        None, description="Whether WHOIS privacy is enabled"
    )
    status: Optional[DomainStatus] = Field(None, description="Current status of the domain")
    expiry_date: Optional[datetime] = Field(None, description="Date when domain will expire")
    registrar_data: Optional[str] = Field(None, description="Registrar-specific data (JSON string)")


class DomainRegistrationRead(DomainRegistrationBase):
    """Schema for reading a domain registration."""

    id: uuid.UUID
    user_id: uuid.UUID
    tenant_id: Optional[uuid.UUID]
    registration_date: datetime
    expiry_date: datetime
    status: DomainStatus
    registrar_data: Optional[str]
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class DomainAvailabilityRequest(BaseModel):
    """Schema for domain availability request."""

    domain_name: str = Field(..., description="Domain name without TLD")
    tlds: Optional[List[str]] = Field(
        None, description="List of TLDs to check (if None, check common TLDs)"
    )


class DomainPriceInfo(BaseModel):
    """Schema for domain price information."""

    currency: str = Field(..., description="Currency code (e.g., 'USD')")
    registration: Dict[str, float] = Field(
        ...,
        description="Registration prices by period (e.g., {'1': 10.99, '2': 20.99})",
    )
    renewal: Dict[str, float] = Field(
        ..., description="Renewal prices by period (e.g., {'1': 12.99, '2': 24.99})"
    )
    transfer: Optional[Dict[str, float]] = Field(
        None, description="Transfer prices by period (if applicable)"
    )
    restore: Optional[float] = Field(
        None, description="Price to restore an expired domain (if applicable)"
    )


class DomainAvailabilityResult(BaseModel):
    """Schema for domain availability result."""

    domain_name: str = Field(..., description="Full domain name (including TLD)")
    tld: str = Field(..., description="TLD part of the domain")
    available: bool = Field(..., description="Whether the domain is available")
    registrar: str = Field(..., description="Registrar that provided this result")
    price_info: Optional[DomainPriceInfo] = Field(
        None, description="Price information (if available)"
    )
    reason: Optional[str] = Field(
        None, description="Reason why domain is unavailable (if applicable)"
    )


class NameserverInfo(BaseModel):
    """Schema for nameserver information."""

    hostname: str = Field(..., description="Nameserver hostname")
    sort_order: int = Field(0, description="Sort order for this nameserver")


class ContactInfo(BaseModel):
    """Schema for contact information."""

    first_name: str
    last_name: str
    organization: Optional[str] = None
    email: EmailStr
    phone: str
    address_line_1: str
    address_line_2: Optional[str] = None
    city: str
    state_province: str
    postal_code: str
    country: str


class DomainRegistrationRequest(BaseModel):
    """Schema for domain registration request."""

    domain_name: str = Field(..., description="Domain name without TLD")
    tld: str = Field(..., description="TLD to register")
    registrar: Optional[str] = Field(
        None, description="Preferred registrar (if None, use best price)"
    )
    period_years: int = Field(1, description="Registration period in years", ge=1, le=10)
    auto_renew: bool = Field(False, description="Whether to automatically renew the domain")
    whois_privacy: bool = Field(False, description="Whether to enable WHOIS privacy protection")
    nameservers: Optional[List[str]] = Field(None, description="List of nameserver hostnames")
    contacts: Dict[str, ContactInfo] = Field(..., description="Contact information for the domain")


class DomainTransferRequest(BaseModel):
    """Schema for domain transfer request."""

    domain_name: str = Field(..., description="Full domain name (including TLD)")
    auth_code: str = Field(..., description="Authorization code for transfer")
    registrar: Optional[str] = Field(None, description="Preferred registrar to transfer to")
    nameservers: Optional[List[str]] = Field(None, description="List of nameserver hostnames")
    contacts: Dict[str, ContactInfo] = Field(..., description="Contact information for the domain")


class DomainRenewalRequest(BaseModel):
    """Schema for domain renewal request."""

    domain_id: uuid.UUID = Field(..., description="ID of the domain to renew")
    period_years: int = Field(1, description="Renewal period in years", ge=1, le=10)


class NameserverUpdateRequest(BaseModel):
    """Schema for nameserver update request."""

    domain_id: uuid.UUID = Field(..., description="ID of the domain to update")
    nameservers: List[str] = Field(
        ..., description="List of nameserver hostnames", min_items=2, max_items=13
    )


class WhoisPrivacyRequest(BaseModel):
    """Schema for WHOIS privacy update request."""

    domain_id: uuid.UUID = Field(..., description="ID of the domain to update")
    enable: bool = Field(..., description="Whether to enable WHOIS privacy")


class AutoRenewRequest(BaseModel):
    """Schema for auto-renew update request."""

    domain_id: uuid.UUID = Field(..., description="ID of the domain to update")
    enable: bool = Field(..., description="Whether to enable auto-renewal")
