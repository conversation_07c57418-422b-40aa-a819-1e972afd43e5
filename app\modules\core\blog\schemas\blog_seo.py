"""
Blog SEO Schemas

Pydantic models for blog SEO validation and serialization.
"""

import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional

from pydantic import BaseModel, Field, validator


class BlogSEOBase(BaseModel):
    """Base schema for blog SEO."""
    canonical_url: Optional[str] = Field(None, max_length=500)
    robots_directive: str = Field(default="index,follow", max_length=100)
    
    # Open Graph
    og_title: Optional[str] = Field(None, max_length=95)
    og_description: Optional[str] = Field(None, max_length=300)
    og_image_url: Optional[str] = Field(None, max_length=500)
    og_image_alt: Optional[str] = Field(None, max_length=255)
    og_type: str = Field(default="article", max_length=50)
    
    # Twitter Cards
    twitter_card_type: str = Field(default="summary_large_image", max_length=50)
    twitter_title: Optional[str] = Field(None, max_length=70)
    twitter_description: Optional[str] = Field(None, max_length=200)
    twitter_image_url: Optional[str] = Field(None, max_length=500)
    twitter_image_alt: Optional[str] = Field(None, max_length=255)
    twitter_creator: Optional[str] = Field(None, max_length=100)
    
    # Structured Data
    structured_data: Optional[Dict[str, Any]] = None
    additional_meta_tags: Optional[Dict[str, str]] = None
    
    # Schema.org Article properties
    article_section: Optional[str] = Field(None, max_length=100)
    article_tags: Optional[List[str]] = None
    
    # SEO Analysis
    focus_keyword: Optional[str] = Field(None, max_length=100)
    keyword_density: Optional[str] = Field(None, max_length=10)
    readability_score: Optional[str] = Field(None, max_length=20)
    
    @validator('robots_directive')
    def validate_robots_directive(cls, v):
        allowed_directives = [
            'index,follow', 'index,nofollow', 'noindex,follow', 'noindex,nofollow'
        ]
        if v not in allowed_directives:
            raise ValueError(f'Robots directive must be one of: {allowed_directives}')
        return v
    
    @validator('twitter_card_type')
    def validate_twitter_card_type(cls, v):
        allowed_types = ['summary', 'summary_large_image', 'app', 'player']
        if v not in allowed_types:
            raise ValueError(f'Twitter card type must be one of: {allowed_types}')
        return v


class BlogSEOCreate(BlogSEOBase):
    """Schema for creating blog SEO."""
    post_id: uuid.UUID


class BlogSEOUpdate(BaseModel):
    """Schema for updating blog SEO."""
    canonical_url: Optional[str] = Field(None, max_length=500)
    robots_directive: Optional[str] = Field(None, max_length=100)
    
    # Open Graph
    og_title: Optional[str] = Field(None, max_length=95)
    og_description: Optional[str] = Field(None, max_length=300)
    og_image_url: Optional[str] = Field(None, max_length=500)
    og_image_alt: Optional[str] = Field(None, max_length=255)
    og_type: Optional[str] = Field(None, max_length=50)
    
    # Twitter Cards
    twitter_card_type: Optional[str] = Field(None, max_length=50)
    twitter_title: Optional[str] = Field(None, max_length=70)
    twitter_description: Optional[str] = Field(None, max_length=200)
    twitter_image_url: Optional[str] = Field(None, max_length=500)
    twitter_image_alt: Optional[str] = Field(None, max_length=255)
    twitter_creator: Optional[str] = Field(None, max_length=100)
    
    # Structured Data
    structured_data: Optional[Dict[str, Any]] = None
    additional_meta_tags: Optional[Dict[str, str]] = None
    
    # Schema.org Article properties
    article_section: Optional[str] = Field(None, max_length=100)
    article_tags: Optional[List[str]] = None
    
    # SEO Analysis
    focus_keyword: Optional[str] = Field(None, max_length=100)
    keyword_density: Optional[str] = Field(None, max_length=10)
    readability_score: Optional[str] = Field(None, max_length=20)


class BlogSEORead(BlogSEOBase):
    """Schema for reading blog SEO."""
    id: uuid.UUID
    post_id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class BlogSEOAnalysis(BaseModel):
    """Schema for SEO analysis results."""
    focus_keyword: Optional[str]
    keyword_density: float
    title_length: int
    meta_description_length: int
    content_length: int
    readability_score: str
    
    # SEO recommendations
    recommendations: List[str] = []
    warnings: List[str] = []
    errors: List[str] = []
    
    # Scores (0-100)
    seo_score: int
    readability_score_numeric: int
    
    class Config:
        from_attributes = True


class BlogSEOPreview(BaseModel):
    """Schema for SEO preview (how it appears in search results)."""
    title: str
    description: str
    url: str
    
    # Social media previews
    og_preview: Dict[str, str]
    twitter_preview: Dict[str, str]
    
    class Config:
        from_attributes = True
