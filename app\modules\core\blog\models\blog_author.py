"""
Blog Author Models

Author management for blog posts with profile information.
"""

import uuid
from datetime import datetime

from sqlalchemy import Column, DateTime, ForeignKey, String, Text, Boolean
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base


class BlogAuthor(Base):
    """
    Blog author model.

    Represents authors who can write blog posts.
    Can be linked to system users or be independent author profiles.
    """

    __tablename__ = "blog_authors"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Link to system user (optional)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"),
                    nullable=True)

    # Author information
    slug = Column(String(255), unique=True, nullable=False, index=True)
    display_name = Column(String(100), nullable=False)
    email = Column(String(255), nullable=True)

    # Profile information
    bio = Column(Text, nullable=True)
    avatar_url = Column(String(500), nullable=True)
    website_url = Column(String(500), nullable=True)

    # Social media links
    twitter_handle = Column(String(100), nullable=True)
    linkedin_url = Column(String(500), nullable=True)
    github_url = Column(String(500), nullable=True)

    # Status
    is_active = Column(Boolean, default=True)
    is_featured = Column(Boolean, default=False)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow,
                       onupdate=datetime.utcnow, nullable=False)

    # Relationships
    user = relationship("User", backref="blog_author")
    posts = relationship("BlogPost", back_populates="author")

    def __repr__(self):
        return f"<BlogAuthor(id={self.id}, display_name='{self.display_name}')>"
