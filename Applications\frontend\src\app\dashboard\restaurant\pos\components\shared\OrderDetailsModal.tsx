'use client';

import React from 'react';
import { 
  XMarkIcon,
  PrinterIcon,
  DocumentTextIcon,
  CalendarDaysIcon,
  ClockIcon,
  UserIcon,
  TableCellsIcon,
  CurrencyDollarIcon,
  ReceiptPercentIcon
} from '@heroicons/react/24/outline';
import { useCurrency } from '@/hooks/useCurrency';

// ===============================
// INTERFACES
// ===============================

interface OrderItem {
  name: string;
  quantity: number;
  price: number;
  total: number;
  special_instructions?: string;
}

interface OrderDetails {
  id: string;
  table_name: string;
  customer_name?: string;
  total_amount: number;
  status: 'completed' | 'paid';
  created_at: string;
  completed_at: string;
  payment_method: string;
  items: OrderItem[];
  entry_time?: string;
  exit_time?: string;
  duration?: string;
  subtotal?: number;
  tax_amount?: number;
  discount_amount?: number;
}

interface OrderDetailsModalProps {
  isOpen: boolean;
  order: OrderDetails | null;
  onClose: () => void;
  onPrintTicket: (order: OrderDetails) => void;
  onGenerateInvoice: (order: OrderDetails) => void;
}

// ===============================
// COMPONENT
// ===============================

export default function OrderDetailsModal({
  isOpen,
  order,
  onClose,
  onPrintTicket,
  onGenerateInvoice
}: OrderDetailsModalProps) {
  const { formatCurrency } = useCurrency();

  if (!isOpen || !order) return null;

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString('pt-BR'),
      time: date.toLocaleTimeString('pt-BR', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    };
  };

  const calculateDuration = (start: string, end: string) => {
    const startTime = new Date(start);
    const endTime = new Date(end);
    const diffMs = endTime.getTime() - startTime.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 60) {
      return `${diffMins}min`;
    } else {
      const hours = Math.floor(diffMins / 60);
      const minutes = diffMins % 60;
      return minutes > 0 ? `${hours}h${minutes}min` : `${hours}h`;
    }
  };

  const entryDateTime = formatDateTime(order.created_at);
  const exitDateTime = formatDateTime(order.completed_at);
  const duration = calculateDuration(order.created_at, order.completed_at);

  // Calculate financial details
  const subtotal = order.subtotal || order.items.reduce((sum, item) => sum + item.total, 0);
  const taxAmount = order.tax_amount || 0;
  const discountAmount = order.discount_amount || 0;
  const finalTotal = subtotal + taxAmount - discountAmount;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gray-50">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              Detalhes do Pedido #{order.id.slice(-6)}
            </h2>
            <p className="text-sm text-gray-600">
              {order.table_name} • {exitDateTime.date}
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-200 rounded-lg transition-colors"
          >
            <XMarkIcon className="h-6 w-6 text-gray-600" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left Column - Order Info */}
            <div className="space-y-6">
              {/* Customer & Table Info */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold text-gray-900 mb-3">Informações do Atendimento</h3>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <TableCellsIcon className="h-5 w-5 text-gray-600" />
                    <div>
                      <span className="text-sm text-gray-600">Mesa:</span>
                      <span className="ml-2 font-medium">{order.table_name}</span>
                    </div>
                  </div>

                  {order.customer_name && (
                    <div className="flex items-center space-x-3">
                      <UserIcon className="h-5 w-5 text-gray-600" />
                      <div>
                        <span className="text-sm text-gray-600">Cliente:</span>
                        <span className="ml-2 font-medium">{order.customer_name}</span>
                      </div>
                    </div>
                  )}

                  <div className="flex items-center space-x-3">
                    <CurrencyDollarIcon className="h-5 w-5 text-gray-600" />
                    <div>
                      <span className="text-sm text-gray-600">Pagamento:</span>
                      <span className="ml-2 font-medium">{order.payment_method}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Timing Info */}
              <div className="bg-blue-50 rounded-lg p-4">
                <h3 className="font-semibold text-gray-900 mb-3">Horários</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <CalendarDaysIcon className="h-5 w-5 text-blue-600" />
                      <span className="text-sm text-gray-600">Entrada:</span>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{entryDateTime.time}</div>
                      <div className="text-xs text-gray-500">{entryDateTime.date}</div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <CalendarDaysIcon className="h-5 w-5 text-green-600" />
                      <span className="text-sm text-gray-600">Saída:</span>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{exitDateTime.time}</div>
                      <div className="text-xs text-gray-500">{exitDateTime.date}</div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between border-t border-blue-200 pt-3">
                    <div className="flex items-center space-x-3">
                      <ClockIcon className="h-5 w-5 text-purple-600" />
                      <span className="text-sm text-gray-600">Permanência:</span>
                    </div>
                    <div className="font-medium text-purple-600">{duration}</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Items & Financial */}
            <div className="space-y-6">
              {/* Order Items */}
              <div>
                <h3 className="font-semibold text-gray-900 mb-3">Itens do Pedido</h3>
                <div className="bg-gray-50 rounded-lg p-4 max-h-64 overflow-auto">
                  <div className="space-y-3">
                    {order.items.map((item, index) => (
                      <div key={index} className="flex items-center justify-between py-2 border-b border-gray-200 last:border-b-0">
                        <div className="flex-1">
                          <div className="font-medium text-gray-900">{item.name}</div>
                          <div className="text-sm text-gray-600">
                            {item.quantity} × {formatCurrency(item.price)}
                          </div>
                          {item.special_instructions && (
                            <div className="text-xs text-blue-600 mt-1">
                              Obs: {item.special_instructions}
                            </div>
                          )}
                        </div>
                        <div className="font-semibold text-gray-900">
                          {formatCurrency(item.total)}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Financial Summary */}
              <div className="bg-green-50 rounded-lg p-4">
                <h3 className="font-semibold text-gray-900 mb-3">Resumo Financeiro</h3>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Subtotal:</span>
                    <span className="font-medium">{formatCurrency(subtotal)}</span>
                  </div>

                  {discountAmount > 0 && (
                    <div className="flex items-center justify-between text-red-600">
                      <span className="text-sm">Desconto:</span>
                      <span className="font-medium">-{formatCurrency(discountAmount)}</span>
                    </div>
                  )}

                  {taxAmount > 0 && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Taxa de Serviço:</span>
                      <span className="font-medium">+{formatCurrency(taxAmount)}</span>
                    </div>
                  )}

                  <div className="border-t border-green-200 pt-2 flex items-center justify-between">
                    <span className="font-semibold text-gray-900">Total Pago:</span>
                    <span className="text-xl font-bold text-green-600">
                      {formatCurrency(finalTotal)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer Actions */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-500">
            Pedido finalizado em {exitDateTime.date} às {exitDateTime.time}
          </div>

          <div className="flex space-x-3">
            <button
              onClick={() => onPrintTicket(order)}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <PrinterIcon className="h-4 w-4" />
              <span>Imprimir Ticket</span>
            </button>

            <button
              onClick={() => onGenerateInvoice(order)}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <DocumentTextIcon className="h-4 w-4" />
              <span>Gerar Fatura</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
