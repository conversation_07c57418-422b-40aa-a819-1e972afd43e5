"""
EShop Role Management Service - B2B Authorization and Context Switching.

Manages TVendor and TCostumer role authorization, business verification,
and market context switching for B2B/B2C operations.
"""

import uuid
import logging
from typing import Dict, List, Optional, Any, Tuple
from decimal import Decimal
from datetime import datetime, timed<PERSON>ta

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.core.exceptions import ValidationError, NotFoundError, AuthorizationError
from app.modules.core.users.models.tenant_user_association import TenantUserAssociation
from app.modules.core.roles.models.roles import (
    TenantRole, MarketContext, RolePermissions, 
    TVendorPermissions, TCostumerPermissions
)
from app.modules.core.notifications.services.notification_service import NotificationService
from app.modules.core.users.models.user import User
from app.modules.core.tenants.models.tenant import Tenant

logger = logging.getLogger(__name__)


class RoleManagementService:
    """Service for managing B2B role authorization and context switching."""

    def __init__(self, db: Session):
        self.db = db
        self.notification_service = NotificationService(db)

    async def request_b2b_authorization(
        self,
        user_id: uuid.UUID,
        tenant_id: uuid.UUID,
        requested_role: str,
        business_data: Dict[str, Any],
        requester_notes: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Request B2B authorization for TVENDOR or TCOSTUMER role.
        
        Args:
            user_id: User requesting authorization
            tenant_id: Tenant context
            requested_role: 'tvendor' or 'tcostumer'
            business_data: Business verification data
            requester_notes: Optional notes from requester
            
        Returns:
            Dict containing request status and details
        """
        if requested_role not in ['tvendor', 'tcostumer']:
            raise ValidationError("Invalid B2B role requested")

        # Check if user already has association
        existing_association = self.db.query(TenantUserAssociation).filter(
            and_(
                TenantUserAssociation.user_id == user_id,
                TenantUserAssociation.tenant_id == tenant_id
            )
        ).first()

        if existing_association and existing_association.is_b2b_role:
            raise ValidationError("User already has B2B role request pending or approved")

        # Validate business data
        required_fields = ['business_name', 'business_registration_number']
        for field in required_fields:
            if not business_data.get(field):
                raise ValidationError(f"Missing required field: {field}")

        # Create or update association
        if existing_association:
            # Update existing association to B2B role
            existing_association.role = requested_role
            existing_association.market_context = 'b2b'
            existing_association.business_name = business_data['business_name']
            existing_association.business_registration_number = business_data['business_registration_number']
            existing_association.business_verification_status = 'pending'
            existing_association.b2b_authorization_notes = requester_notes
            existing_association.updated_at = datetime.utcnow()
            association = existing_association
        else:
            # Create new B2B association
            association = TenantUserAssociation(
                user_id=user_id,
                tenant_id=tenant_id,
                role=requested_role,
                market_context='b2b',
                business_name=business_data['business_name'],
                business_registration_number=business_data['business_registration_number'],
                business_verification_status='pending',
                b2b_authorization_notes=requester_notes,
                b2b_authorized=False
            )
            self.db.add(association)

        self.db.commit()

        # Send notification to tenant admins
        await self._notify_b2b_request(association, 'requested')

        logger.info(f"B2B authorization requested: user={user_id}, role={requested_role}")

        return {
            'association_id': association.id,
            'status': 'pending',
            'requested_role': requested_role,
            'business_verification_status': 'pending',
            'message': 'B2B authorization request submitted successfully'
        }

    async def approve_b2b_authorization(
        self,
        association_id: uuid.UUID,
        approver_user_id: uuid.UUID,
        approval_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Approve B2B authorization request.
        
        Args:
            association_id: Association to approve
            approver_user_id: User approving the request
            approval_data: Approval configuration data
            
        Returns:
            Dict containing approval status and details
        """
        association = self.db.query(TenantUserAssociation).filter(
            TenantUserAssociation.id == association_id
        ).first()

        if not association:
            raise NotFoundError("Association not found")

        if not association.is_b2b_role:
            raise ValidationError("Association is not a B2B role")

        if association.b2b_authorized:
            raise ValidationError("Association is already authorized")

        # Verify approver has permission
        await self._verify_approval_permission(approver_user_id, association.tenant_id)

        # Update authorization
        association.b2b_authorized = True
        association.b2b_authorization_date = datetime.utcnow()
        association.b2b_authorized_by = approver_user_id
        association.business_verification_status = 'verified'
        association.business_verification_date = datetime.utcnow()

        # Set role-specific configurations
        if association.role == 'tvendor':
            association.commission_rate = approval_data.get('commission_rate', Decimal('5.00'))
        elif association.role == 'tcostumer':
            association.pricing_tier = approval_data.get('pricing_tier', 'standard')

        association.updated_at = datetime.utcnow()
        self.db.commit()

        # Send notification
        await self._notify_b2b_request(association, 'approved')

        logger.info(f"B2B authorization approved: association={association_id}")

        return {
            'association_id': association_id,
            'status': 'approved',
            'role': association.role,
            'authorization_date': association.b2b_authorization_date,
            'message': 'B2B authorization approved successfully'
        }

    async def reject_b2b_authorization(
        self,
        association_id: uuid.UUID,
        rejector_user_id: uuid.UUID,
        rejection_reason: str
    ) -> Dict[str, Any]:
        """
        Reject B2B authorization request.
        
        Args:
            association_id: Association to reject
            rejector_user_id: User rejecting the request
            rejection_reason: Reason for rejection
            
        Returns:
            Dict containing rejection status and details
        """
        association = self.db.query(TenantUserAssociation).filter(
            TenantUserAssociation.id == association_id
        ).first()

        if not association:
            raise NotFoundError("Association not found")

        if not association.is_b2b_role:
            raise ValidationError("Association is not a B2B role")

        # Verify rejector has permission
        await self._verify_approval_permission(rejector_user_id, association.tenant_id)

        # Update status
        association.business_verification_status = 'rejected'
        association.business_verification_date = datetime.utcnow()
        association.b2b_authorization_notes = f"Rejected: {rejection_reason}"
        association.updated_at = datetime.utcnow()

        self.db.commit()

        # Send notification
        await self._notify_b2b_request(association, 'rejected')

        logger.info(f"B2B authorization rejected: association={association_id}")

        return {
            'association_id': association_id,
            'status': 'rejected',
            'rejection_reason': rejection_reason,
            'message': 'B2B authorization rejected'
        }

    async def switch_market_context(
        self,
        user_id: uuid.UUID,
        tenant_id: uuid.UUID,
        new_context: str
    ) -> Dict[str, Any]:
        """
        Switch user's market context (B2B/B2C/Marketplace).
        
        Args:
            user_id: User switching context
            tenant_id: Tenant context
            new_context: New market context ('b2b', 'b2c', 'marketplace')
            
        Returns:
            Dict containing context switch status
        """
        if new_context not in [ctx.value for ctx in MarketContext]:
            raise ValidationError("Invalid market context")

        association = self.db.query(TenantUserAssociation).filter(
            and_(
                TenantUserAssociation.user_id == user_id,
                TenantUserAssociation.tenant_id == tenant_id
            )
        ).first()

        if not association:
            raise NotFoundError("User association not found")

        # Check if user can access the requested context
        if not association.can_access_market_context(new_context):
            raise AuthorizationError(f"User role '{association.role}' cannot access context '{new_context}'")

        # For B2B context, verify authorization
        if new_context == 'b2b' and association.is_b2b_role:
            if not association.is_authorized_for_b2b:
                raise AuthorizationError("User is not authorized for B2B operations")

        # Update context
        association.market_context = new_context
        association.updated_at = datetime.utcnow()
        self.db.commit()

        logger.info(f"Market context switched: user={user_id}, context={new_context}")

        return {
            'user_id': user_id,
            'tenant_id': tenant_id,
            'new_context': new_context,
            'role': association.role,
            'permissions': association.get_effective_permissions(),
            'message': f'Market context switched to {new_context}'
        }

    def get_user_role_info(
        self,
        user_id: uuid.UUID,
        tenant_id: uuid.UUID
    ) -> Dict[str, Any]:
        """
        Get comprehensive role information for a user.
        
        Args:
            user_id: User ID
            tenant_id: Tenant ID
            
        Returns:
            Dict containing complete role information
        """
        association = self.db.query(TenantUserAssociation).filter(
            and_(
                TenantUserAssociation.user_id == user_id,
                TenantUserAssociation.tenant_id == tenant_id
            )
        ).first()

        if not association:
            raise NotFoundError("User association not found")

        return {
            'association_id': association.id,
            'role': association.role,
            'market_context': association.market_context,
            'is_b2b_role': association.is_b2b_role,
            'b2b_authorized': association.is_authorized_for_b2b,
            'business_verification_status': association.business_verification_status,
            'pricing_tier': association.pricing_tier,
            'commission_rate': float(association.commission_rate) if association.commission_rate else None,
            'permissions': association.get_effective_permissions(),
            'created_at': association.created_at,
            'updated_at': association.updated_at
        }

    def list_pending_b2b_requests(
        self,
        tenant_id: uuid.UUID,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        List pending B2B authorization requests for a tenant.
        
        Args:
            tenant_id: Tenant ID
            limit: Maximum number of results
            
        Returns:
            List of pending requests
        """
        associations = self.db.query(TenantUserAssociation).filter(
            and_(
                TenantUserAssociation.tenant_id == tenant_id,
                TenantUserAssociation.role.in_(['tvendor', 'tcostumer']),
                TenantUserAssociation.business_verification_status == 'pending'
            )
        ).limit(limit).all()

        return [
            {
                'association_id': assoc.id,
                'user_id': assoc.user_id,
                'requested_role': assoc.role,
                'business_name': assoc.business_name,
                'business_registration_number': assoc.business_registration_number,
                'requester_notes': assoc.b2b_authorization_notes,
                'created_at': assoc.created_at
            }
            for assoc in associations
        ]

    async def _notify_b2b_request(
        self,
        association: TenantUserAssociation,
        action: str
    ) -> None:
        """Send notification for B2B request action."""
        try:
            # Get tenant admins
            admin_associations = self.db.query(TenantUserAssociation).filter(
                and_(
                    TenantUserAssociation.tenant_id == association.tenant_id,
                    TenantUserAssociation.role.in_(['owner', 'manager'])
                )
            ).all()

            for admin_assoc in admin_associations:
                await self.notification_service.create_notification(
                    user_id=admin_assoc.user_id,
                    title=f"B2B Authorization {action.title()}",
                    message=f"B2B {association.role} role {action} for {association.business_name}",
                    notification_type='role_management',
                    tenant_id=association.tenant_id
                )
        except Exception as e:
            logger.error(f"Failed to send B2B notification: {e}")

    async def _verify_approval_permission(
        self,
        user_id: uuid.UUID,
        tenant_id: uuid.UUID
    ) -> None:
        """Verify user has permission to approve B2B requests."""
        association = self.db.query(TenantUserAssociation).filter(
            and_(
                TenantUserAssociation.user_id == user_id,
                TenantUserAssociation.tenant_id == tenant_id
            )
        ).first()

        if not association:
            raise AuthorizationError("User not associated with tenant")

        if association.role not in ['owner', 'manager']:
            raise AuthorizationError("Insufficient permissions to approve B2B requests") 