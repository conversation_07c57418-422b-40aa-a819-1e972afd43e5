"""Schemas for domain integration."""

from typing import Dict, List, Optional  # noqa: E402
from uuid import UUID

from pydantic import BaseModel  # noqa: E402

from app.modules.shared.domain_rent.schemas.domain_schemas import ContactInfo  # noqa: E402


class DomainRegistrationWithTenantCreate(BaseModel):
    """Schema for registering a domain and associating it with a tenant."""

    # Domain registration details
    domain_name: str
    tld: str
    registrar: Optional[str] = None
    period_years: int = 1
    auto_renew: bool = False
    whois_privacy: bool = False
    nameservers: Optional[List[str]] = None
    contacts: Dict[str, ContactInfo]

    # Tenant association details
    tenant_id: UUID
    frontend_type: str
