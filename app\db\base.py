import datetime
from sqlalchemy import Column, DateTime, func, Table, Foreign<PERSON>ey, Integer, Boolean, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import DeclarativeBase, mapped_column, Mapped

# Removido: from typing import TYPE_CHECKING (Não mais necessário aqui)

# Removidas importações de modelos específicos para evitar importação circular.
# Alembic's env.py deve ser configurado para descobrir modelos que herdam de Base.


class TimestampMixin:
    """Mixin que adiciona colunas created_at e updated_at."""

    created_at: Mapped[datetime.datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at: Mapped[datetime.datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )


# Removido bloco if TYPE_CHECKING


class Base(DeclarativeBase, TimestampMixin):
    """
    Classe base declarativa para todos os modelos SQLAlchemy.
    Inclui timestamps (created_at, updated_at).
    """


# Tabelas de Associação Muitos-para-Muitos

# Menu Item - Variant Groups Association
menu_item_variant_groups = Table(
    "menu_item_variant_groups",
    Base.metadata,
    Column(
        "menu_item_id",
        UUID(as_uuid=True),
        ForeignKey("menu_items.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column(
        "variant_group_id",
        UUID(as_uuid=True),
        ForeignKey("variant_groups.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column(
        "tenant_id",
        UUID(as_uuid=True),
        ForeignKey("tenants.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column("display_order", Integer, default=0, nullable=False),
    Column("is_required", Boolean, default=False, nullable=False, server_default='false'),
    Column("is_active", Boolean, default=True, nullable=False, server_default='true'),
)

# Menu Item - Modifier Groups Association
menu_item_modifier_groups = Table(
    "menu_item_modifier_groups",
    Base.metadata,
    Column(
        "menu_item_id",
        UUID(as_uuid=True),
        ForeignKey("menu_items.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column(
        "modifier_group_id",
        UUID(as_uuid=True),
        ForeignKey("modifier_groups.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column(
        "tenant_id",
        UUID(as_uuid=True),
        ForeignKey("tenants.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column("display_order", Integer, default=0, nullable=False),
    Column("is_required", Boolean, default=False, nullable=False, server_default='false'),
    Column("is_active", Boolean, default=True, nullable=False, server_default='true'),
)

# Menu Item - Optional Groups Association
menu_item_optional_groups = Table(
    "menu_item_optional_groups",
    Base.metadata,
    Column(
        "menu_item_id",
        UUID(as_uuid=True),
        ForeignKey("menu_items.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column(
        "optional_group_id",
        UUID(as_uuid=True),
        ForeignKey("optional_groups.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column(
        "tenant_id",
        UUID(as_uuid=True),
        ForeignKey("tenants.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column("display_order", Integer, default=0, nullable=False),
    Column("is_required", Boolean, default=False, nullable=False, server_default='false'),
    Column("is_active", Boolean, default=True, nullable=False, server_default='true'),
)

menu_item_inventory_association = Table(
    "menu_item_inventory_association",
    Base.metadata,
    Column(
        "menu_item_id",
        UUID(as_uuid=True),
        ForeignKey("menu_items.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column(
        "inventory_item_id",
        UUID(as_uuid=True),
        ForeignKey("inventory_items.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column(
        "tenant_id",
        UUID(as_uuid=True),
        ForeignKey("tenants.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    # Adicionar quaisquer outras colunas necessárias, como quantidade, se aplicável
)

modifier_option_inventory_association = Table(
    "modifier_option_inventory_association",
    Base.metadata,
    Column(
        "modifier_option_id",
        UUID(as_uuid=True),
        ForeignKey("modifier_options.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column(
        "inventory_item_id",
        UUID(as_uuid=True),
        ForeignKey("inventory_items.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column(
        "tenant_id",
        UUID(as_uuid=True),
        ForeignKey("tenants.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    # Adicionar quaisquer outras colunas necessárias
)

# Menu Item - Allergens Association
menu_item_allergens = Table(
    "menu_item_allergens",
    Base.metadata,
    Column(
        "id",
        UUID(as_uuid=True),
        primary_key=True,
        default=func.uuid_generate_v4(),
    ),
    Column(
        "tenant_id",
        UUID(as_uuid=True),
        ForeignKey("tenants.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    ),
    Column(
        "menu_item_id",
        UUID(as_uuid=True),
        ForeignKey("menu_items.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    ),
    Column(
        "allergen_id",
        UUID(as_uuid=True),
        ForeignKey("allergens.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    ),
    Column(
        "severity_level",
        String(20),
        nullable=False,
        default="medium",
    ),
    Column(
        "is_trace",
        Boolean,
        nullable=False,
        default=False,
    ),
    Column(
        "notes",
        Text,
        nullable=True,
    ),
    Column(
        "created_at",
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
    ),
    Column(
        "updated_at",
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    ),
)

# Removed duplicate table definitions - now defined in their respective model files
# The following tables are now defined in app/modules/core/eshop/models/:
# - eshop_product_variant_groups_association (in product_variant_group.py)
# - eshop_product_modifier_groups_association (in product_modifier_group.py) 
# - eshop_product_optional_groups_association (in product_optional_group.py)

# Ensure all models inheriting from Base are imported somewhere before env.py accesses Base.metadata
# Example imports (adjust paths as needed):
# from app.modules.some_module.models import SomeModel
# from app.modules.another_module.models import AnotherModel
# from app.modules.some_module.models import SomeModel
# from app.modules.another_module.models import AnotherModel

# Importar modelos para garantir que sejam registrados com Base.metadata
# Isso é crucial para que Alembic e SQLAlchemy possam resolver relacionamentos e FKs.

# Removidas importações diretas de app.models para evitar ciclos.
# A descoberta de modelos deve ser tratada no entrypoint da aplicação ou no env.py do Alembic.
# from app.models.user import User
# from app.models.tenant import Tenant
# from app.models.tenant_user_association import TenantUserAssociation
# Adicione outras importações de modelos de app.models aqui, se necessário

# Comentado temporariamente para resolver o erro de importação
# from app.modules.core.subscriptions.models import Plan, Feature, PlanFeatureLink, TenantSubscription
# from app.modules.custom_domains.models import CustomDomain
# from app.modules.core.payments.models import (
#     PaymentProcessor, PaymentMethod, PaymentTransaction, PaymentRefund
# )

# Importar i18n por último para evitar ciclos de importação
from app.modules.core.i18n.models.language import Language
from app.modules.core.i18n.models.translation import Translation
from app.modules.core.i18n.models.translation_suggestion import TranslationSuggestion

# Blog models - circular import resolved with TYPE_CHECKING pattern
from app.modules.core.blog.models.blog_post import BlogPost, BlogPostTranslation
from app.modules.core.blog.models.blog_category import BlogCategory, BlogCategoryTranslation
from app.modules.core.blog.models.blog_tag import BlogTag, BlogTagTranslation
from app.modules.core.blog.models.blog_author import BlogAuthor
from app.modules.core.blog.models.blog_comment import BlogComment
from app.modules.core.blog.models.blog_seo import BlogSEO

# Importar modelos de inventory
from app.modules.core.functions.inventory.models.inventory_item import InventoryItem, InventoryCategory

# Importar modelos de supplier
from app.modules.shared.supplier.models.supplier import (
    Supplier,
    ProductSupplier,
    SupplierPriceHistory
)

# Importar modelos de purchase order
from app.modules.shared.supplier.models.purchase_order import (
    PurchaseOrder,
    PurchaseOrderItem
)

# Importar modelos de shopping list
from app.modules.shared.shopping_list.models.shopping_list import (
    ShoppingList,
    ShoppingListItem
)

# Importar modelos de menu
from app.modules.tenants.restaurants.menu.models.menu_item import MenuItem
from app.modules.tenants.restaurants.menu.models.menu_category import MenuCategory
from app.modules.tenants.restaurants.menu.models.digital_menu import DigitalMenu

# Importar modelos de orders
from app.modules.core.functions.orders.models.order import Order

# Importar modelos de table management
from app.modules.tenants.restaurants.table_management.models.table import Table

# Importar modelos de CRM
from app.modules.shared.crm.models.account import Account

# Importar modelos de online orders
from app.modules.tenants.restaurants.models.online_order import OnlineOrder

# Importar modelos de POS
from app.modules.core.functions.pos.models.sale_transaction import SaleTransaction

# Importar modelos de KDS
from app.modules.tenants.restaurants.kds.models.kitchen_order import KitchenOrder

# Importar modelos de usuários e tenants
from app.modules.core.users.models.user import User
from app.modules.core.tenants.models.tenant import Tenant
from app.modules.core.users.models.tenant_user_association import TenantUserAssociation

# Importar modelos de Media System
from app.modules.core.functions.media_system.models import (
    MediaContext, MediaDirectory, MediaUpload, MediaMenuItemMedia, MediaFTPUser
)

# Importar modelos de Financial System
from app.modules.shared.financial.transactions.models import FinancialTransaction
from app.modules.shared.financial.categories.models import FinancialCategory
from app.modules.shared.financial.invoices.models.invoice import Invoice, InvoiceItem

# Importar modelos do Financial Control System
from app.modules.shared.financial.control.models.control_entry import FinancialControlEntry
from app.modules.shared.financial.control.models.control_category import ControlCategory
from app.modules.shared.financial.control.models.control_document import ControlDocument
from app.modules.shared.financial.control.models.control_report import ControlReport

# Importar modelos do Help Center System
from app.modules.core.help_center.models import Ticket, TicketMessage, KnowledgeBaseArticle

# Importar modelos do Tenant Settings System (migrated to tenants module)
from app.modules.core.tenants.models.tenant_settings import TenantSettings
from app.modules.core.tenants.models.tenant_payment_method_config import TenantPaymentMethodConfig

# Importar modelos do Restaurant Tenant Settings System
from app.modules.tenants.restaurants.tenant_settings.models.restaurant_tenant_settings import RestaurantTenantSettings

# Importar modelos do Allergens System
from app.modules.core.functions.allergens.models.allergen import Allergen

# Importar modelos do Notifications System
from app.modules.core.notifications.models import (
    Notification, NotificationQueue, NotificationMetrics, NotificationTemplate
)

# Importar modelos de shipping
from app.modules.core.functions.shipping.models.shipping import (
    ShippingMethod,
    ShippingCarrier,
    Shipment
)

# Importar modelos de offerts
from app.modules.core.functions.offerts.models.coupon import Coupon

# Importar modelos de eshop
from app.modules.core.eshop.models.eshop_category import eshopCategory
from app.modules.core.eshop.models.product import Product as EshopProduct
# eshop_review removed - using generic reviews system instead
from app.modules.core.eshop.models.product_variant_group import ProductVariantGroup
from app.modules.core.eshop.models.product_modifier_group import ProductModifierGroup
from app.modules.core.eshop.models.product_optional_group import ProductOptionalGroup

# Importar modelos de shops
from app.modules.tenants.shops.models.product import Product as ShopProduct

# Import generic reviews system
from app.modules.core.functions.reviews.models.review import Review

# Importar modelos de Couponic (migrados para eshop)
# from app.modules.shared.couponic.models.couponic_offer import CouponicOffer
# from app.modules.shared.couponic.models.couponic_redemption import CouponicRedemption

# Importar modelos de Funções Core (Customizations)
from app.modules.core.functions.customizations.models.variant_group import VariantGroup
from app.modules.core.functions.customizations.models.variant_option import VariantOption
from app.modules.core.functions.customizations.models.modifier_group import ModifierGroup
from app.modules.core.functions.customizations.models.modifier_option import ModifierOption
from app.modules.core.functions.customizations.models.optional_group import OptionalGroup
from app.modules.core.functions.customizations.models.optional_option import OptionalOption

# Importe modelos de outros módulos aqui
# Exemplo: from app.modules.some_other_module.models import SomeOtherModel

# A importação dos módulos acima garante que suas classes de modelo (que herdam de Base
# ou usam SQLModel.metadata = Base.metadata) sejam registradas com Base.metadata.
# A chamada explícita a configure_mappers() pode não ser necessária se as importações
# forem feitas no local correto e na ordem correta (geralmente no entrypoint da aplicação
# ou em um local central como este, que é importado pelo entrypoint).
# from sqlalchemy.orm import configure_mappers
# configure_mappers()

"""
Este arquivo serve como um ponto central para importar todos os modelos de dados (models)
da aplicação, para que o Alembic possa detectá-los e gerar as migrações de banco de dados
corretamente.
"""
# Importar a classe Base declarativa do SQLAlchemy
# from app.db.base_class import Base

# Importar modelos de tenants
# from app.modules.core.tenants.models.tenant import Tenant
# from app.modules.core.tenants.models.tenant_settings import TenantSettings
# from app.modules.core.tenants.models.tenant_user import TenantUser
# ... (e todos os outros imports)
