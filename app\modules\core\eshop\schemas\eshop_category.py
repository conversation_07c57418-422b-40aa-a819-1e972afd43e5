import uuid
from pydantic import BaseModel, <PERSON>
from typing import List, Optional
import uuid
from app.core.enums import MarketType

class eshopCategoryBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    slug: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    is_active: bool = Field(default=True)
    parent_id: Optional[uuid.UUID] = Field(default=None)
    market_type: MarketType = Field(default=MarketType.PUBLIC)

    class Config:
        from_attributes = True

class eshopCategoryCreate(eshopCategoryBase):
    pass

class eshopCategoryUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=100)
    slug: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = None
    is_active: Optional[bool] = None
    parent_id: Optional[uuid.UUID] = None

class eshopCategoryRead(eshopCategoryBase):
    id: uuid.UUID
    # Removed children field to avoid MissingGreenlet error
    # children: List['eshopCategoryRead'] = []

    class Config:
        from_attributes = True

eshopCategoryRead.update_forward_refs()