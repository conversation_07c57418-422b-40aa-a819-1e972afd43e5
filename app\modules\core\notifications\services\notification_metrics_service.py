"""
Notification Metrics Service

Serviço para métricas e analytics do sistema de notificações.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from uuid import UUID

from sqlalchemy import and_, desc, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.modules.core.users.models.user import User

from ..models import Notification, NotificationMetrics, NotificationStatus

logger = logging.getLogger(__name__)


class NotificationMetricsService:
    """Serviço para métricas do sistema de notificações."""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_system_metrics(self) -> Dict:
        """
        Obtém métricas gerais do sistema (para admins).
        
        Returns:
            Dicionário com métricas do sistema
        """
        now = datetime.utcnow()
        today = now.date()
        week_ago = now - timedelta(days=7)
        month_ago = now - timedelta(days=30)
        
        # Total de notificações
        stmt_total = select(func.count(Notification.id))
        total_result = await self.db.execute(stmt_total)
        total_notifications = total_result.scalar()
        
        # Notificações ativas (não expiradas)
        stmt_active = select(func.count(Notification.id)).where(
            or_(
                Notification.expires_at.is_(None),
                Notification.expires_at > now
            )
        )
        active_result = await self.db.execute(stmt_active)
        active_notifications = active_result.scalar()
        
        # Notificações expiradas
        expired_notifications = total_notifications - active_notifications
        
        # Notificações por status
        stmt_status = select(
            Notification.status,
            func.count(Notification.id)
        ).group_by(Notification.status)
        
        status_result = await self.db.execute(stmt_status)
        status_counts = dict(status_result.fetchall())
        
        # Métricas de engajamento (últimos 30 dias)
        stmt_engagement = select(
            func.sum(Notification.view_count),
            func.sum(Notification.click_count),
            func.sum(Notification.delivery_count)
        ).where(Notification.created_at >= month_ago)
        
        engagement_result = await self.db.execute(stmt_engagement)
        views, clicks, deliveries = engagement_result.fetchone()
        
        # Taxa de cliques
        ctr = (clicks / views * 100) if views and views > 0 else 0
        
        # Taxa de entrega
        delivery_rate = (deliveries / total_notifications * 100) if total_notifications > 0 else 0
        
        # Notificações por dia (última semana)
        stmt_daily = select(
            func.date(Notification.created_at).label('date'),
            func.count(Notification.id).label('count')
        ).where(
            Notification.created_at >= week_ago
        ).group_by(
            func.date(Notification.created_at)
        ).order_by('date')
        
        daily_result = await self.db.execute(stmt_daily)
        daily_stats = [
            {"date": str(row.date), "count": row.count}
            for row in daily_result.fetchall()
        ]
        
        return {
            "total_notifications": total_notifications,
            "active_notifications": active_notifications,
            "expired_notifications": expired_notifications,
            "status_breakdown": status_counts,
            "engagement": {
                "total_views": views or 0,
                "total_clicks": clicks or 0,
                "total_deliveries": deliveries or 0,
                "click_through_rate": round(ctr, 2),
                "delivery_rate": round(delivery_rate, 2)
            },
            "daily_stats": daily_stats,
            "queue_stats": await self._get_queue_stats()
        }

    async def get_tenant_metrics(
        self,
        tenant_id: UUID,
        period_days: int = 30
    ) -> Dict:
        """
        Obtém métricas de um tenant específico.
        
        Args:
            tenant_id: ID do tenant
            period_days: Período em dias para análise
            
        Returns:
            Dicionário com métricas do tenant
        """
        start_date = datetime.utcnow() - timedelta(days=period_days)
        
        # Notificações do tenant
        stmt_base = select(Notification).where(
            and_(
                Notification.tenant_id == tenant_id,
                Notification.created_at >= start_date
            )
        )
        
        result = await self.db.execute(stmt_base)
        notifications = result.scalars().all()
        
        if not notifications:
            return self._empty_tenant_metrics()
        
        # Calcula métricas
        total_sent = len(notifications)
        total_delivered = sum(n.delivery_count for n in notifications)
        total_views = sum(n.view_count for n in notifications)
        total_clicks = sum(n.click_count for n in notifications)
        
        # Calcula taxas
        delivery_rate = (total_delivered / total_sent * 100) if total_sent > 0 else 0
        open_rate = (total_views / total_delivered * 100) if total_delivered > 0 else 0
        ctr = (total_clicks / total_views * 100) if total_views > 0 else 0
        
        # Score de engajamento (fórmula personalizada)
        engagement_score = (open_rate * 0.4 + ctr * 0.6) if total_views > 0 else 0
        
        # Breakdown por prioridade
        priority_breakdown = {}
        for notification in notifications:
            priority = notification.priority.value
            if priority not in priority_breakdown:
                priority_breakdown[priority] = {
                    "count": 0,
                    "views": 0,
                    "clicks": 0
                }
            priority_breakdown[priority]["count"] += 1
            priority_breakdown[priority]["views"] += notification.view_count
            priority_breakdown[priority]["clicks"] += notification.click_count
        
        # Top performing notifications
        top_notifications = sorted(
            notifications,
            key=lambda n: n.click_count + (n.view_count * 0.1),
            reverse=True
        )[:5]
        
        top_performing = [
            {
                "id": str(n.id),
                "title": n.title,
                "views": n.view_count,
                "clicks": n.click_count,
                "ctr": (n.click_count / n.view_count * 100) if n.view_count > 0 else 0
            }
            for n in top_notifications
        ]
        
        return {
            "period_days": period_days,
            "total_sent": total_sent,
            "total_delivered": total_delivered,
            "total_views": total_views,
            "total_clicks": total_clicks,
            "delivery_rate": round(delivery_rate, 2),
            "open_rate": round(open_rate, 2),
            "click_through_rate": round(ctr, 2),
            "engagement_score": round(engagement_score, 2),
            "priority_breakdown": priority_breakdown,
            "top_performing": top_performing
        }

    async def get_user_metrics(
        self,
        user_id: UUID,
        period_days: int = 30
    ) -> Dict:
        """
        Obtém métricas de um usuário específico.
        
        Args:
            user_id: ID do usuário
            period_days: Período em dias para análise
            
        Returns:
            Dicionário com métricas do usuário
        """
        start_date = datetime.utcnow() - timedelta(days=period_days)
        
        # Notificações enviadas pelo usuário
        stmt_sent = select(Notification).where(
            and_(
                Notification.sender_id == user_id,
                Notification.created_at >= start_date
            )
        )
        
        sent_result = await self.db.execute(stmt_sent)
        sent_notifications = sent_result.scalars().all()
        
        # Notificações recebidas pelo usuário (aproximação)
        stmt_received = select(Notification).where(
            and_(
                Notification.created_at >= start_date,
                or_(
                    Notification.target_type == "all_users",
                    and_(
                        Notification.target_type == "specific_user",
                        Notification.target_id == user_id
                    )
                )
            )
        )
        
        received_result = await self.db.execute(stmt_received)
        received_notifications = received_result.scalars().all()
        
        # Métricas de envio
        sent_metrics = {
            "total_sent": len(sent_notifications),
            "total_views": sum(n.view_count for n in sent_notifications),
            "total_clicks": sum(n.click_count for n in sent_notifications),
            "avg_engagement": 0
        }
        
        if sent_notifications:
            total_engagement = sum(
                n.view_count + (n.click_count * 2) for n in sent_notifications
            )
            sent_metrics["avg_engagement"] = total_engagement / len(sent_notifications)
        
        # Métricas de recebimento
        user_id_str = str(user_id)
        read_count = sum(
            1 for n in received_notifications
            if n.read_by and user_id_str in n.read_by
        )
        
        received_metrics = {
            "total_received": len(received_notifications),
            "total_read": read_count,
            "read_rate": (read_count / len(received_notifications) * 100) if received_notifications else 0
        }
        
        return {
            "period_days": period_days,
            "sent": sent_metrics,
            "received": received_metrics
        }

    async def record_daily_metrics(self, date: Optional[datetime] = None) -> None:
        """
        Registra métricas diárias agregadas.
        
        Args:
            date: Data para registrar métricas (padrão: hoje)
        """
        if not date:
            date = datetime.utcnow().date()
        
        # Evita duplicatas
        existing_stmt = select(NotificationMetrics).where(
            and_(
                func.date(NotificationMetrics.date) == date,
                NotificationMetrics.period_type == "daily"
            )
        )
        
        existing_result = await self.db.execute(existing_stmt)
        if existing_result.scalar_one_or_none():
            logger.info(f"Métricas diárias já existem para {date}")
            return
        
        # Calcula métricas do dia
        start_datetime = datetime.combine(date, datetime.min.time())
        end_datetime = start_datetime + timedelta(days=1)
        
        stmt = select(
            func.count(Notification.id).label('sent'),
            func.sum(Notification.delivery_count).label('delivered'),
            func.sum(Notification.view_count).label('views'),
            func.sum(Notification.click_count).label('clicks'),
            func.sum(
                func.case(
                    (Notification.status == NotificationStatus.FAILED, 1),
                    else_=0
                )
            ).label('failed')
        ).where(
            and_(
                Notification.created_at >= start_datetime,
                Notification.created_at < end_datetime
            )
        )
        
        result = await self.db.execute(stmt)
        metrics_data = result.fetchone()
        
        if not metrics_data or not metrics_data.sent:
            logger.info(f"Nenhuma notificação encontrada para {date}")
            return
        
        # Calcula taxas
        delivery_rate = (metrics_data.delivered / metrics_data.sent * 100) if metrics_data.sent > 0 else 0
        open_rate = (metrics_data.views / metrics_data.delivered * 100) if metrics_data.delivered > 0 else 0
        ctr = (metrics_data.clicks / metrics_data.views * 100) if metrics_data.views > 0 else 0
        
        # Cria registro de métricas
        daily_metrics = NotificationMetrics(
            date=start_datetime,
            period_type="daily",
            notifications_sent=metrics_data.sent,
            notifications_delivered=metrics_data.delivered,
            notifications_read=metrics_data.views,
            notifications_clicked=metrics_data.clicks,
            notifications_failed=metrics_data.failed,
            delivery_rate=int(delivery_rate),
            open_rate=int(open_rate),
            click_through_rate=int(ctr)
        )
        
        self.db.add(daily_metrics)
        await self.db.commit()
        
        logger.info(f"Métricas diárias registradas para {date}")

    def _empty_tenant_metrics(self) -> Dict:
        """Retorna métricas vazias para tenant."""
        return {
            "total_sent": 0,
            "total_delivered": 0,
            "total_views": 0,
            "total_clicks": 0,
            "delivery_rate": 0,
            "open_rate": 0,
            "click_through_rate": 0,
            "engagement_score": 0,
            "priority_breakdown": {},
            "top_performing": []
        }

    async def _get_queue_stats(self) -> Dict:
        """Obtém estatísticas da fila de notificações."""
        from .notification_queue_service import NotificationQueueService
        
        queue_service = NotificationQueueService(self.db)
        return await queue_service.get_queue_stats()
