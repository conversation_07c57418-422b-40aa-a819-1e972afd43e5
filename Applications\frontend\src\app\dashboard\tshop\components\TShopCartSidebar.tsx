'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>alogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { 
  ShoppingCartIcon,
  XMarkIcon,
  MinusIcon,
  PlusIcon,
  BuildingOfficeIcon,
  CreditCardIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { useTShopCart } from '@/contexts/TShopCartContext';
import { formatCurrency } from '@/lib/utils';
import { toast } from 'sonner';

interface TShopCartSidebarProps {
  isOpen?: boolean;
  onClose?: () => void;
}

export function TShopCartSidebar({ isOpen = false, onClose }: TShopCartSidebarProps) {
  const { 
    cart, 
    isLoading, 
    updateCartItem, 
    removeFromCart, 
    clearCart 
  } = useTShopCart();
  
  const [isCheckingOut, setIsCheckingOut] = useState(false);

  const handleQuantityChange = async (itemId: string, newQuantity: number) => {
    if (newQuantity < 1) return;
    await updateCartItem(itemId, newQuantity);
  };

  const handleRemoveItem = async (itemId: string) => {
    await removeFromCart(itemId);
  };

  const handleClearCart = async () => {
    if (window.confirm('Tem certeza que deseja limpar o carrinho?')) {
      await clearCart();
    }
  };

  const handleCheckout = () => {
    if (!cart || cart.items.length === 0) {
      toast.error('Carrinho vazio');
      return;
    }

    if (cart.requires_approval) {
      toast.info('Este pedido requer aprovação devido ao valor ou limite de crédito');
    }

    setIsCheckingOut(true);
    // Navigate to B2B checkout
    window.location.href = '/dashboard/tshop/checkout';
  };

  const groupedItems = cart?.items.reduce((groups, item) => {
    const supplierId = item.supplier_id;
    if (!groups[supplierId]) {
      groups[supplierId] = {
        supplier_name: item.supplier_name,
        items: [],
        subtotal: 0
      };
    }
    groups[supplierId].items.push(item);
    groups[supplierId].subtotal += item.total_price;
    return groups;
  }, {} as Record<string, { supplier_name: string; items: any[]; subtotal: number }>) || {};

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md h-full max-h-screen overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <ShoppingCartIcon className="h-5 w-5 mr-2" />
              Carrinho B2B
            </div>
            <Badge variant="secondary" className="bg-indigo-100 text-indigo-800">
              {cart?.total_items || 0} itens
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          {!cart || cart.items.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center p-6">
              <ShoppingCartIcon className="h-16 w-16 text-gray-300 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Carrinho vazio
              </h3>
              <p className="text-gray-500 mb-4">
                Adicione produtos B2B ao seu carrinho para começar
              </p>
              <Button onClick={onClose} variant="outline">
                Continuar comprando
              </Button>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Approval Warning */}
              {cart.requires_approval && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <div className="flex items-start">
                    <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mt-0.5 mr-2" />
                    <div className="text-sm">
                      <p className="font-medium text-yellow-800">Aprovação necessária</p>
                      <p className="text-yellow-700">
                        Este pedido requer aprovação devido ao valor ou limite de crédito.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Credit Limit Warning */}
              {!cart.credit_limit_check && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <div className="flex items-start">
                    <ExclamationTriangleIcon className="h-5 w-5 text-red-600 mt-0.5 mr-2" />
                    <div className="text-sm">
                      <p className="font-medium text-red-800">Limite de crédito excedido</p>
                      <p className="text-red-700">
                        O valor do pedido excede seu limite de crédito disponível.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Items grouped by supplier */}
              {Object.entries(groupedItems).map(([supplierId, group]) => (
                <div key={supplierId} className="border rounded-lg p-4">
                  <div className="flex items-center mb-3">
                    <BuildingOfficeIcon className="h-4 w-4 mr-2 text-gray-500" />
                    <span className="font-medium text-sm">{group.supplier_name}</span>
                    <Badge variant="outline" className="ml-auto">
                      {formatCurrency(group.subtotal)}
                    </Badge>
                  </div>

                  <div className="space-y-3">
                    {group.items.map((item) => (
                      <div key={item.id} className="flex items-center space-x-3">
                        {/* Product Image */}
                        <div className="w-12 h-12 bg-gray-100 rounded-lg flex-shrink-0">
                          {item.product_image ? (
                            <img
                              src={item.product_image}
                              alt={item.product_name}
                              className="w-full h-full object-cover rounded-lg"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center">
                              <BuildingOfficeIcon className="h-6 w-6 text-gray-400" />
                            </div>
                          )}
                        </div>

                        {/* Product Info */}
                        <div className="flex-1 min-w-0">
                          <h4 className="text-sm font-medium text-gray-900 truncate">
                            {item.product_name}
                          </h4>
                          <p className="text-sm text-gray-500">
                            {formatCurrency(item.unit_price)} cada
                          </p>
                          {item.minimum_order_quantity && (
                            <p className="text-xs text-gray-400">
                              Mín: {item.minimum_order_quantity}
                            </p>
                          )}
                        </div>

                        {/* Quantity Controls */}
                        <div className="flex items-center space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                            disabled={isLoading || item.quantity <= (item.minimum_order_quantity || 1)}
                            className="h-8 w-8 p-0"
                          >
                            <MinusIcon className="h-3 w-3" />
                          </Button>
                          
                          <Input
                            type="number"
                            value={item.quantity}
                            onChange={(e) => {
                              const newQuantity = parseInt(e.target.value) || 1;
                              handleQuantityChange(item.id, newQuantity);
                            }}
                            className="w-16 h-8 text-center"
                            min={item.minimum_order_quantity || 1}
                          />
                          
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                            disabled={isLoading}
                            className="h-8 w-8 p-0"
                          >
                            <PlusIcon className="h-3 w-3" />
                          </Button>
                        </div>

                        {/* Remove Button */}
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleRemoveItem(item.id)}
                          disabled={isLoading}
                          className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                        >
                          <XMarkIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              ))}

              {/* Estimated Delivery */}
              {cart.estimated_delivery_date && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <div className="flex items-center text-sm">
                    <DocumentTextIcon className="h-4 w-4 text-blue-600 mr-2" />
                    <span className="text-blue-800">
                      Entrega estimada: {new Date(cart.estimated_delivery_date).toLocaleDateString('pt-BR')}
                    </span>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer with totals and actions */}
        {cart && cart.items.length > 0 && (
          <div className="flex-shrink-0 border-t pt-4 space-y-4">
            {/* Totals */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Subtotal:</span>
                <span>{formatCurrency(cart.subtotal)}</span>
              </div>
              
              {cart.bulk_discount_amount > 0 && (
                <div className="flex justify-between text-sm text-green-600">
                  <span>Desconto em lote:</span>
                  <span>-{formatCurrency(cart.bulk_discount_amount)}</span>
                </div>
              )}
              
              {cart.discount_amount > 0 && (
                <div className="flex justify-between text-sm text-green-600">
                  <span>Desconto:</span>
                  <span>-{formatCurrency(cart.discount_amount)}</span>
                </div>
              )}
              
              <div className="flex justify-between text-sm">
                <span>Impostos:</span>
                <span>{formatCurrency(cart.tax_amount)}</span>
              </div>
              
              <Separator />
              
              <div className="flex justify-between font-bold text-lg">
                <span>Total:</span>
                <span>{formatCurrency(cart.total_amount)}</span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="space-y-2">
              <Button
                onClick={handleCheckout}
                disabled={isCheckingOut || !cart.credit_limit_check}
                className="w-full"
              >
                <CreditCardIcon className="h-4 w-4 mr-2" />
                {cart.requires_approval ? 'Solicitar Aprovação' : 'Finalizar Pedido'}
              </Button>
              
              <Button
                onClick={handleClearCart}
                variant="outline"
                disabled={isLoading}
                className="w-full"
              >
                Limpar Carrinho
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
