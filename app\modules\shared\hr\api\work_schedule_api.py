from typing import List, Optional
import uuid
from datetime import date
from fastapi import APIRouter, Depends, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.dependencies import get_db, get_current_tenant, require_tenant_role
from app.core.roles import TenantRole
from app.models.user import User
from app.models.tenant import Tenant

from app.modules.shared.hr.core.schemas.work_schedule import (
    ScheduleTemplateRead,
    ScheduleTemplateCreate,
    ScheduleTemplateUpdate,
    ScheduleTemplateWithEntries,
    ScheduleTemplateEntryRead,
    ScheduleTemplateEntryCreate,
    ScheduleTemplateEntryUpdate,
    WorkScheduleRead,
    WorkScheduleCreate,
    WorkScheduleUpdate,
    WorkScheduleWithShifts,
    WorkShiftRead,
    WorkShiftCreate,
    WorkShiftUpdate,
    LeaveRequestRead,
    LeaveRequestCreate,
    LeaveRequestUpdate,
    GenerateScheduleRequest,
    LeaveBalanceResponse,
)
from app.modules.shared.hr.core.models.work_schedule import LeaveRequestStatus  # noqa: E402
from app.modules.shared.hr.core.services.work_schedule_service import (
    work_schedule_service,
)

router = APIRouter(tags=["HR - Work Schedule"])

# Schedule Template Routes


@router.post(
    "/templates",
    response_model=ScheduleTemplateRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create Schedule Template",
    description="Create a new schedule template.",
)
async def create_schedule_template(
    template_data: ScheduleTemplateCreate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Create a new schedule template."""
    return await work_schedule_service.create_schedule_template(
        db=db, tenant_id=tenant.id, template_data=template_data
    )


@router.get(
    "/templates/{template_id}",
    response_model=ScheduleTemplateWithEntries,
    summary="Get Schedule Template",
    description="Get a schedule template with its entries.",
)
async def get_schedule_template(
    template_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Get a schedule template with its entries."""
    template, entries = await work_schedule_service.get_schedule_template_with_entries(
        db=db, tenant_id=tenant.id, template_id=template_id
    )

    # Convert to response model
    template_read = ScheduleTemplateRead.model_validate(template)
    entries_read = [ScheduleTemplateEntryRead.model_validate(entry) for entry in entries]

    return ScheduleTemplateWithEntries(**template_read.model_dump(), entries=entries_read)


@router.get(
    "/templates",
    response_model=List[ScheduleTemplateRead],
    summary="List Schedule Templates",
    description="Get all schedule templates with optional filtering.",
)
async def get_schedule_templates(
    is_active: Optional[bool] = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Get all schedule templates with optional filtering."""
    return await work_schedule_service.get_schedule_templates(
        db=db, tenant_id=tenant.id, is_active=is_active, skip=skip, limit=limit
    )


@router.put(
    "/templates/{template_id}",
    response_model=ScheduleTemplateRead,
    summary="Update Schedule Template",
    description="Update a schedule template.",
)
async def update_schedule_template(
    template_id: uuid.UUID,
    template_data: ScheduleTemplateUpdate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Update a schedule template."""
    return await work_schedule_service.update_schedule_template(
        db=db, tenant_id=tenant.id, template_id=template_id, template_data=template_data
    )


@router.delete(
    "/templates/{template_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete Schedule Template",
    description="Delete a schedule template.",
)
async def delete_schedule_template(
    template_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Delete a schedule template."""
    await work_schedule_service.delete_schedule_template(
        db=db, tenant_id=tenant.id, template_id=template_id
    )
    return None


@router.post(
    "/templates/{template_id}/entries",
    response_model=ScheduleTemplateEntryRead,
    status_code=status.HTTP_201_CREATED,
    summary="Add Template Entry",
    description="Add an entry to a schedule template.",
)
async def add_template_entry(
    template_id: uuid.UUID,
    entry_data: ScheduleTemplateEntryCreate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Add an entry to a schedule template."""
    return await work_schedule_service.add_template_entry(
        db=db, tenant_id=tenant.id, template_id=template_id, entry_data=entry_data
    )


@router.put(
    "/template-entries/{entry_id}",
    response_model=ScheduleTemplateEntryRead,
    summary="Update Template Entry",
    description="Update a schedule template entry.",
)
async def update_template_entry(
    entry_id: uuid.UUID,
    entry_data: ScheduleTemplateEntryUpdate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Update a schedule template entry."""
    return await work_schedule_service.update_template_entry(
        db=db, tenant_id=tenant.id, entry_id=entry_id, entry_data=entry_data
    )


@router.delete(
    "/template-entries/{entry_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete Template Entry",
    description="Delete a schedule template entry.",
)
async def delete_template_entry(
    entry_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Delete a schedule template entry."""
    await work_schedule_service.delete_template_entry(db=db, tenant_id=tenant.id, entry_id=entry_id)
    return None


# Work Schedule Routes


@router.post(
    "/schedules",
    response_model=WorkScheduleRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create Work Schedule",
    description="Create a new work schedule.",
)
async def create_work_schedule(
    schedule_data: WorkScheduleCreate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Create a new work schedule."""
    return await work_schedule_service.create_work_schedule(
        db=db, tenant_id=tenant.id, schedule_data=schedule_data
    )


@router.get(
    "/schedules/{schedule_id}",
    response_model=WorkScheduleWithShifts,
    summary="Get Work Schedule",
    description="Get a work schedule with its shifts.",
)
async def get_work_schedule(
    schedule_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Get a work schedule with its shifts."""
    schedule, shifts = await work_schedule_service.get_work_schedule_with_shifts(
        db=db, tenant_id=tenant.id, schedule_id=schedule_id
    )

    # Convert to response model
    schedule_read = WorkScheduleRead.model_validate(schedule)
    shifts_read = [WorkShiftRead.model_validate(shift) for shift in shifts]

    template = None
    if schedule.template_id:
        template_obj = await work_schedule_service.get_schedule_template(
            db=db, tenant_id=tenant.id, template_id=schedule.template_id
        )
        template = ScheduleTemplateRead.model_validate(template_obj)

    return WorkScheduleWithShifts(
        **schedule_read.model_dump(), shifts=shifts_read, template=template
    )


@router.get(
    "/employee/{employee_id}/schedules",
    response_model=List[WorkScheduleRead],
    summary="List Employee Schedules",
    description="Get all work schedules for an employee with optional filtering.",
)
async def get_employee_schedules(
    employee_id: uuid.UUID,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    is_active: Optional[bool] = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Get all work schedules for an employee with optional filtering."""
    return await work_schedule_service.get_employee_schedules(
        db=db,
        tenant_id=tenant.id,
        employee_id=employee_id,
        start_date=start_date,
        end_date=end_date,
        is_active=is_active,
        skip=skip,
        limit=limit,
    )


@router.put(
    "/schedules/{schedule_id}",
    response_model=WorkScheduleRead,
    summary="Update Work Schedule",
    description="Update a work schedule.",
)
async def update_work_schedule(
    schedule_id: uuid.UUID,
    schedule_data: WorkScheduleUpdate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Update a work schedule."""
    return await work_schedule_service.update_work_schedule(
        db=db, tenant_id=tenant.id, schedule_id=schedule_id, schedule_data=schedule_data
    )


@router.delete(
    "/schedules/{schedule_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete Work Schedule",
    description="Delete a work schedule.",
)
async def delete_work_schedule(
    schedule_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Delete a work schedule."""
    await work_schedule_service.delete_work_schedule(
        db=db, tenant_id=tenant.id, schedule_id=schedule_id
    )
    return None


@router.post(
    "/schedules/{schedule_id}/shifts",
    response_model=WorkShiftRead,
    status_code=status.HTTP_201_CREATED,
    summary="Add Work Shift",
    description="Add a shift to a work schedule.",
)
async def add_work_shift(
    schedule_id: uuid.UUID,
    shift_data: WorkShiftCreate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Add a shift to a work schedule."""
    return await work_schedule_service.add_work_shift(
        db=db, tenant_id=tenant.id, schedule_id=schedule_id, shift_data=shift_data
    )


@router.put(
    "/shifts/{shift_id}",
    response_model=WorkShiftRead,
    summary="Update Work Shift",
    description="Update a work shift.",
)
async def update_work_shift(
    shift_id: uuid.UUID,
    shift_data: WorkShiftUpdate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Update a work shift."""
    return await work_schedule_service.update_work_shift(
        db=db, tenant_id=tenant.id, shift_id=shift_id, shift_data=shift_data
    )


@router.delete(
    "/shifts/{shift_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete Work Shift",
    description="Delete a work shift.",
)
async def delete_work_shift(
    shift_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Delete a work shift."""
    await work_schedule_service.delete_work_shift(db=db, tenant_id=tenant.id, shift_id=shift_id)
    return None


@router.post(
    "/generate-schedule",
    response_model=WorkScheduleRead,
    status_code=status.HTTP_201_CREATED,
    summary="Generate Schedule",
    description="Generate a work schedule from a template.",
)
async def generate_schedule(
    request_data: GenerateScheduleRequest,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Generate a work schedule from a template."""
    return await work_schedule_service.generate_schedule_from_template(
        db=db, tenant_id=tenant.id, request_data=request_data
    )


# Leave Request Routes


@router.post(
    "/leave-requests",
    response_model=LeaveRequestRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create Leave Request",
    description="Create a new leave request.",
)
async def create_leave_request(
    request_data: LeaveRequestCreate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Create a new leave request."""
    return await work_schedule_service.create_leave_request(
        db=db, tenant_id=tenant.id, request_data=request_data
    )


@router.get(
    "/leave-requests/{request_id}",
    response_model=LeaveRequestRead,
    summary="Get Leave Request",
    description="Get a leave request by ID.",
)
async def get_leave_request(
    request_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Get a leave request by ID."""
    return await work_schedule_service.get_leave_request(
        db=db, tenant_id=tenant.id, request_id=request_id
    )


@router.get(
    "/employee/{employee_id}/leave-requests",
    response_model=List[LeaveRequestRead],
    summary="List Employee Leave Requests",
    description="Get all leave requests for an employee with optional filtering.",
)
async def get_employee_leave_requests(
    employee_id: uuid.UUID,
    status: Optional[LeaveRequestStatus] = None,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Get all leave requests for an employee with optional filtering."""
    return await work_schedule_service.get_employee_leave_requests(
        db=db,
        tenant_id=tenant.id,
        employee_id=employee_id,
        status=status,
        start_date=start_date,
        end_date=end_date,
        skip=skip,
        limit=limit,
    )


@router.put(
    "/leave-requests/{request_id}",
    response_model=LeaveRequestRead,
    summary="Update Leave Request",
    description="Update a leave request.",
)
async def update_leave_request(
    request_id: uuid.UUID,
    request_data: LeaveRequestUpdate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Update a leave request."""
    return await work_schedule_service.update_leave_request(
        db=db, tenant_id=tenant.id, request_id=request_id, request_data=request_data
    )


@router.delete(
    "/leave-requests/{request_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete Leave Request",
    description="Delete a leave request.",
)
async def delete_leave_request(
    request_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Delete a leave request."""
    await work_schedule_service.delete_leave_request(
        db=db, tenant_id=tenant.id, request_id=request_id
    )
    return None


@router.get(
    "/employee/{employee_id}/leave-balance",
    response_model=LeaveBalanceResponse,
    summary="Get Leave Balance",
    description="Get an employee's leave balance.",
)
async def get_employee_leave_balance(
    employee_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Get an employee's leave balance."""
    return await work_schedule_service.get_employee_leave_balance(
        db=db, tenant_id=tenant.id, employee_id=employee_id
    )
