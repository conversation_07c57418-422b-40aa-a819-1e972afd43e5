"""add_remaining_cart_checkout_auction_lottery_systems

Revision ID: 1dc0855363fb
Revises: 3b4a691c64a6
Create Date: 2025-06-27 15:15:47.717334

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1dc0855363fb'
down_revision: Union[str, None] = '3b4a691c64a6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('eshop_carts',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=True),
    sa.Column('session_id', sa.String(length=255), nullable=True),
    sa.Column('status', sa.Enum('ACTIVE', 'ABANDONED', 'CONVERTED', 'EXPIRED', name='cartstatus'), nullable=False),
    sa.Column('market_context', sa.String(length=20), nullable=False),
    sa.Column('subtotal', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('tax_amount', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('discount_amount', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('shipping_amount', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('total_amount', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('currency', sa.String(length=3), nullable=False),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('cart_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('expires_at', sa.DateTime(), nullable=True),
    sa.Column('converted_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_carts_market_context', 'eshop_carts', ['market_context'], unique=False)
    op.create_index('ix_carts_status_expires', 'eshop_carts', ['status', 'expires_at'], unique=False)
    op.create_index('ix_carts_tenant_session', 'eshop_carts', ['tenant_id', 'session_id'], unique=False)
    op.create_index('ix_carts_tenant_user', 'eshop_carts', ['tenant_id', 'user_id'], unique=False)
    op.create_index(op.f('ix_eshop_carts_session_id'), 'eshop_carts', ['session_id'], unique=False)
    op.create_index(op.f('ix_eshop_carts_status'), 'eshop_carts', ['status'], unique=False)
    op.create_index(op.f('ix_eshop_carts_tenant_id'), 'eshop_carts', ['tenant_id'], unique=False)
    op.create_index(op.f('ix_eshop_carts_user_id'), 'eshop_carts', ['user_id'], unique=False)
    op.create_table('eshop_cart_items',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('cart_id', sa.UUID(), nullable=False),
    sa.Column('product_id', sa.UUID(), nullable=False),
    sa.Column('quantity', sa.Integer(), nullable=False),
    sa.Column('unit_price', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('total_price', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('selected_variants', sa.JSON(), nullable=True),
    sa.Column('selected_modifiers', sa.JSON(), nullable=True),
    sa.Column('special_instructions', sa.Text(), nullable=True),
    sa.Column('item_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['cart_id'], ['eshop_carts.id'], ),
    sa.ForeignKeyConstraint(['product_id'], ['eshop_products.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_cart_items_cart_product', 'eshop_cart_items', ['cart_id', 'product_id'], unique=False)
    op.create_index('ix_cart_items_created', 'eshop_cart_items', ['created_at'], unique=False)
    op.create_index(op.f('ix_eshop_cart_items_cart_id'), 'eshop_cart_items', ['cart_id'], unique=False)
    op.create_index(op.f('ix_eshop_cart_items_product_id'), 'eshop_cart_items', ['product_id'], unique=False)
    op.create_table('eshop_checkout_sessions',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('cart_id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=True),
    sa.Column('order_id', sa.UUID(), nullable=True),
    sa.Column('status', sa.Enum('INITIATED', 'PAYMENT_PENDING', 'PAYMENT_PROCESSING', 'PAYMENT_CONFIRMED', 'PAYMENT_FAILED', 'COMPLETED', 'CANCELLED', 'EXPIRED', name='checkoutstatus'), nullable=False),
    sa.Column('market_context', sa.String(length=20), nullable=False),
    sa.Column('shipping_address', sa.JSON(), nullable=True),
    sa.Column('billing_address', sa.JSON(), nullable=True),
    sa.Column('shipping_method', sa.Enum('STANDARD', 'EXPRESS', 'OVERNIGHT', 'PICKUP', 'SAME_DAY', name='shippingmethod'), nullable=True),
    sa.Column('shipping_cost', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('shipping_notes', sa.Text(), nullable=True),
    sa.Column('payment_method', sa.Enum('CREDIT_CARD', 'DEBIT_CARD', 'PIX', 'BANK_TRANSFER', 'DIGITAL_WALLET', 'CASH_ON_DELIVERY', 'BANK_SLIP', name='paymentmethod'), nullable=True),
    sa.Column('payment_provider', sa.String(length=50), nullable=True),
    sa.Column('payment_external_id', sa.String(length=255), nullable=True),
    sa.Column('payment_reference', sa.String(length=255), nullable=True),
    sa.Column('subtotal', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('tax_amount', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('discount_amount', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('total_amount', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('currency', sa.String(length=3), nullable=False),
    sa.Column('customer_notes', sa.Text(), nullable=True),
    sa.Column('special_instructions', sa.Text(), nullable=True),
    sa.Column('checkout_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('expires_at', sa.DateTime(), nullable=True),
    sa.Column('payment_confirmed_at', sa.DateTime(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['cart_id'], ['eshop_carts.id'], ),
    sa.ForeignKeyConstraint(['order_id'], ['orders.id'], ),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_checkout_created', 'eshop_checkout_sessions', ['created_at'], unique=False)
    op.create_index('ix_checkout_market_context', 'eshop_checkout_sessions', ['market_context'], unique=False)
    op.create_index('ix_checkout_payment_method', 'eshop_checkout_sessions', ['payment_method'], unique=False)
    op.create_index('ix_checkout_status_expires', 'eshop_checkout_sessions', ['status', 'expires_at'], unique=False)
    op.create_index('ix_checkout_tenant_user', 'eshop_checkout_sessions', ['tenant_id', 'user_id'], unique=False)
    op.create_index(op.f('ix_eshop_checkout_sessions_cart_id'), 'eshop_checkout_sessions', ['cart_id'], unique=False)
    op.create_index(op.f('ix_eshop_checkout_sessions_order_id'), 'eshop_checkout_sessions', ['order_id'], unique=False)
    op.create_index(op.f('ix_eshop_checkout_sessions_status'), 'eshop_checkout_sessions', ['status'], unique=False)
    op.create_index(op.f('ix_eshop_checkout_sessions_tenant_id'), 'eshop_checkout_sessions', ['tenant_id'], unique=False)
    op.create_index(op.f('ix_eshop_checkout_sessions_user_id'), 'eshop_checkout_sessions', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_eshop_checkout_sessions_user_id'), table_name='eshop_checkout_sessions')
    op.drop_index(op.f('ix_eshop_checkout_sessions_tenant_id'), table_name='eshop_checkout_sessions')
    op.drop_index(op.f('ix_eshop_checkout_sessions_status'), table_name='eshop_checkout_sessions')
    op.drop_index(op.f('ix_eshop_checkout_sessions_order_id'), table_name='eshop_checkout_sessions')
    op.drop_index(op.f('ix_eshop_checkout_sessions_cart_id'), table_name='eshop_checkout_sessions')
    op.drop_index('ix_checkout_tenant_user', table_name='eshop_checkout_sessions')
    op.drop_index('ix_checkout_status_expires', table_name='eshop_checkout_sessions')
    op.drop_index('ix_checkout_payment_method', table_name='eshop_checkout_sessions')
    op.drop_index('ix_checkout_market_context', table_name='eshop_checkout_sessions')
    op.drop_index('ix_checkout_created', table_name='eshop_checkout_sessions')
    op.drop_table('eshop_checkout_sessions')
    op.drop_index(op.f('ix_eshop_cart_items_product_id'), table_name='eshop_cart_items')
    op.drop_index(op.f('ix_eshop_cart_items_cart_id'), table_name='eshop_cart_items')
    op.drop_index('ix_cart_items_created', table_name='eshop_cart_items')
    op.drop_index('ix_cart_items_cart_product', table_name='eshop_cart_items')
    op.drop_table('eshop_cart_items')
    op.drop_index(op.f('ix_eshop_carts_user_id'), table_name='eshop_carts')
    op.drop_index(op.f('ix_eshop_carts_tenant_id'), table_name='eshop_carts')
    op.drop_index(op.f('ix_eshop_carts_status'), table_name='eshop_carts')
    op.drop_index(op.f('ix_eshop_carts_session_id'), table_name='eshop_carts')
    op.drop_index('ix_carts_tenant_user', table_name='eshop_carts')
    op.drop_index('ix_carts_tenant_session', table_name='eshop_carts')
    op.drop_index('ix_carts_status_expires', table_name='eshop_carts')
    op.drop_index('ix_carts_market_context', table_name='eshop_carts')
    op.drop_table('eshop_carts')
    # ### end Alembic commands ###
