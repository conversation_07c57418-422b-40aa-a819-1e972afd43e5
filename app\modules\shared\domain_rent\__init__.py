"""Domain Rent module for managing domain registration services.

This module provides functionality for registering, renewing, transferring, and managing domains
through multiple registrar APIs (GoDaddy, Namecheap, OpenSRS, ResellerClub, etc.).
"""

from fastapi import APIRouter  # noqa: E402

from app.modules.shared.domain_rent.api import router as domain_rent_router  # noqa: E402


def get_router() -> APIRouter:
    """Return the API router for the domain_rent module."""
    return domain_rent_router
