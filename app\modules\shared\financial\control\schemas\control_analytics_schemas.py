"""Financial Control Analytics Schemas."""

from typing import Optional, List, Dict, Any
from datetime import date, datetime
from decimal import Decimal
from pydantic import BaseModel, Field
from uuid import UUID

from ..models.control_entry import ControlEntryType, ControlEntryStatus


class CategoryBreakdown(BaseModel):
    """Schema for category breakdown data."""
    
    category_id: Optional[UUID]
    category_name: str
    amount: Decimal
    percentage: float = Field(..., ge=0, le=100)
    transaction_count: int = Field(..., ge=0)
    color: Optional[str] = None


class CashFlowData(BaseModel):
    """Schema for cash flow data."""
    
    period: str  # e.g., "2024-01", "2024-Q1"
    period_name: str  # e.g., "January 2024", "Q1 2024"
    income: Decimal = Field(default=0)
    expense: Decimal = Field(default=0)
    net_flow: Decimal = Field(default=0)
    transaction_count: int = Field(default=0)


class ControlMetrics(BaseModel):
    """Schema for financial control metrics."""
    
    # Summary metrics
    total_income: Decimal = Field(default=0)
    total_expense: Decimal = Field(default=0)
    net_balance: Decimal = Field(default=0)
    total_transactions: int = Field(default=0)
    
    # Pending metrics
    pending_invoices: int = Field(default=0)
    pending_amount: Decimal = Field(default=0)
    overdue_invoices: int = Field(default=0)
    overdue_amount: Decimal = Field(default=0)
    
    # Growth metrics
    monthly_growth: float = Field(default=0)
    income_growth: float = Field(default=0)
    expense_growth: float = Field(default=0)
    
    # Category breakdown
    category_breakdown: List[CategoryBreakdown] = Field(default_factory=list)
    
    # Cash flow data
    cash_flow_data: List[CashFlowData] = Field(default_factory=list)
    
    # Recent transactions
    recent_transactions: List[Dict[str, Any]] = Field(default_factory=list)
    
    # Period information
    period_start: Optional[date] = None
    period_end: Optional[date] = None
    period_type: Optional[str] = None  # "month", "quarter", "year", "custom"


class AnalyticsFilters(BaseModel):
    """Schema for analytics filters."""
    
    # Date filters
    date_from: Optional[date] = None
    date_to: Optional[date] = None
    period_type: Optional[str] = Field(None, pattern="^(today|week|month|quarter|year|custom)$")
    
    # Category filters
    category_ids: Optional[List[UUID]] = None
    exclude_categories: Optional[List[UUID]] = None
    
    # Type filters
    entry_types: Optional[List[ControlEntryType]] = None
    status_filter: Optional[List[ControlEntryStatus]] = None
    
    # Supplier filters
    supplier_ids: Optional[List[UUID]] = None
    
    # Amount filters
    amount_min: Optional[Decimal] = Field(None, ge=0)
    amount_max: Optional[Decimal] = Field(None, ge=0)
    
    # Special filters
    include_archived: bool = Field(False, description="Include archived entries")
    only_overdue: bool = Field(False, description="Only overdue entries")
    only_recurring: bool = Field(False, description="Only recurring entries")
    only_tax_deductible: bool = Field(False, description="Only tax deductible entries")
    
    # Grouping options
    group_by: Optional[str] = Field(None, pattern="^(category|supplier|month|quarter|year)$")
    include_subcategories: bool = Field(True, description="Include subcategory breakdown")


class TrendData(BaseModel):
    """Schema for trend analysis data."""
    
    period: str
    value: Decimal
    change: float  # Percentage change from previous period
    change_amount: Decimal  # Absolute change from previous period


class ComparisonData(BaseModel):
    """Schema for period comparison data."""
    
    current_period: Dict[str, Any]
    previous_period: Dict[str, Any]
    comparison: Dict[str, float]  # Percentage changes


class BudgetAnalysis(BaseModel):
    """Schema for budget analysis data."""
    
    category_id: Optional[UUID]
    category_name: str
    budgeted_amount: Decimal
    actual_amount: Decimal
    variance: Decimal
    variance_percentage: float
    is_over_budget: bool


class DashboardData(BaseModel):
    """Schema for dashboard data."""
    
    # Key metrics
    metrics: ControlMetrics
    
    # Charts data
    cash_flow_chart: List[CashFlowData]
    category_chart: List[CategoryBreakdown]
    trend_chart: List[TrendData]
    
    # Analysis data
    budget_analysis: List[BudgetAnalysis] = Field(default_factory=list)
    comparison_data: Optional[ComparisonData] = None
    
    # Alerts and notifications
    alerts: List[Dict[str, Any]] = Field(default_factory=list)
    
    # Last updated
    last_updated: datetime = Field(default_factory=datetime.utcnow)
    data_freshness: str = "real-time"  # "real-time", "cached", "stale"


class ExportRequest(BaseModel):
    """Schema for data export requests."""
    
    format: str = Field(..., pattern="^(csv|excel|pdf)$")
    filters: Optional[AnalyticsFilters] = None
    include_charts: bool = Field(True, description="Include charts in export")
    include_summary: bool = Field(True, description="Include summary data")
    include_details: bool = Field(True, description="Include detailed transactions")

    # Export options
    date_format: str = Field("YYYY-MM-DD", description="Date format for export")
    currency_format: str = Field("USD", description="Currency format")
    locale: str = Field("en-US", description="Locale for formatting")


class ReportSchedule(BaseModel):
    """Schema for scheduled reports."""

    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None

    # Schedule configuration
    frequency: str = Field(..., pattern="^(daily|weekly|monthly|quarterly|yearly)$")
    day_of_week: Optional[int] = Field(None, ge=0, le=6)  # 0=Monday, 6=Sunday
    day_of_month: Optional[int] = Field(None, ge=1, le=31)
    time_of_day: str = Field("09:00", pattern="^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$")

    # Report configuration
    report_type: str = Field(..., pattern="^(dashboard|cash_flow|category|budget|custom)$")
    filters: Optional[AnalyticsFilters] = None
    export_format: str = Field("pdf", pattern="^(csv|excel|pdf)$")
    
    # Recipients
    email_recipients: List[str] = Field(..., min_items=1)
    
    # Status
    is_active: bool = Field(True)
    next_run: Optional[datetime] = None
    last_run: Optional[datetime] = None
