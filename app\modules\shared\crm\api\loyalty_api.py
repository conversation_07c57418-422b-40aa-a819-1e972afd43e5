"""API endpoints for CRM loyalty programs."""

import uuid  # noqa: E402
from typing import List, Optional, Annotated
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db  # noqa: E402
from app.modules.core.auth.dependencies.auth_dependencies import (
    get_current_active_user,
)  # noqa: E402
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    require_tenant_role,
)  # noqa: E402
from app.modules.core.roles.models.roles import TenantRole
from app.modules.core.users.models.user import User
from app.modules.shared.crm.models.loyalty import (
    LoyaltyMembershipStatus,
    LoyaltyTransactionType,
)
from app.modules.shared.crm.schemas.loyalty import (  # noqa: E402
    LoyaltyProgramCreate,
    LoyaltyProgramUpdate,
    LoyaltyProgramRead,
    LoyaltyMembershipCreate,
    LoyaltyMembershipUpdate,
    LoyaltyMembershipRead,
    Loyalty<PERSON>ransaction<PERSON><PERSON>,
    LoyaltyTransactionRead,
)
from app.modules.shared.crm.services.loyalty_service import loyalty_service  # noqa: E402

router = APIRouter(prefix="/loyalty", tags=["CRM - Loyalty"])


# ==================== Loyalty Program Endpoints ====================


@router.post(
    "/programs",
    response_model=LoyaltyProgramRead,
    status_code=status.HTTP_201_CREATED,
    dependencies=[Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER], "header"))],
)
async def create_loyalty_program(
    program_in: LoyaltyProgramCreate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    db: Annotated[AsyncSession, Depends(get_db)],
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Create a new loyalty program.

    Requires tenant owner or manager role.
    """
    try:
        db_program = await loyalty_service.create_program(db, tenant_id, program_in)
        return db_program
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create loyalty program: {str(e)}",
        )


@router.get(
    "/programs/{program_id}",
    response_model=LoyaltyProgramRead,
    dependencies=[
        Depends(
            require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF], "header")
        )
    ],
)
async def get_loyalty_program(
    program_id: uuid.UUID = Path(..., description="The ID of the loyalty program to retrieve"),
    db: Annotated[AsyncSession, Depends(get_db)] = None,
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Get a loyalty program by ID.

    Requires tenant owner, manager, or staff role.
    """
    db_program = await loyalty_service.get_program(db, tenant_id, program_id)
    if not db_program:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Loyalty program with ID {program_id} not found",
        )
    return db_program


@router.get(
    "/programs",
    response_model=List[LoyaltyProgramRead],
    dependencies=[
        Depends(
            require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF], "header")
        )
    ],
)
async def get_loyalty_programs(
    skip: int = Query(0, ge=0, description="Number of programs to skip"),
    limit: int = Query(100, ge=1, le=100, description="Maximum number of programs to return"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    db: Annotated[AsyncSession, Depends(get_db)] = None,
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Get all loyalty programs with optional filtering.

    Requires tenant owner, manager, or staff role.
    """
    db_programs = await loyalty_service.get_programs(db, tenant_id, skip, limit, is_active)
    return db_programs


@router.put(
    "/programs/{program_id}",
    response_model=LoyaltyProgramRead,
    dependencies=[Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER], "header"))],
)
async def update_loyalty_program(
    program_in: LoyaltyProgramUpdate,
    program_id: uuid.UUID = Path(..., description="The ID of the loyalty program to update"),
    db: Annotated[AsyncSession, Depends(get_db)] = None,
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Update a loyalty program.

    Requires tenant owner or manager role.
    """
    db_program = await loyalty_service.update_program(db, tenant_id, program_id, program_in)
    if not db_program:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Loyalty program with ID {program_id} not found",
        )
    return db_program


# ==================== Loyalty Membership Endpoints ====================


@router.post(
    "/memberships",
    response_model=LoyaltyMembershipRead,
    status_code=status.HTTP_201_CREATED,
    dependencies=[
        Depends(
            require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF], "header")
        )
    ],
)
async def create_loyalty_membership(
    membership_in: LoyaltyMembershipCreate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    db: Annotated[AsyncSession, Depends(get_db)],
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Create a new loyalty membership for a customer.

    Requires tenant owner, manager, or staff role.
    """
    try:
        db_membership = await loyalty_service.create_membership(db, tenant_id, membership_in)
        return db_membership
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create loyalty membership: {str(e)}",
        )


@router.get(
    "/memberships/{membership_id}",
    response_model=LoyaltyMembershipRead,
    dependencies=[
        Depends(
            require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF], "header")
        )
    ],
)
async def get_loyalty_membership(
    membership_id: uuid.UUID = Path(
        ..., description="The ID of the loyalty membership to retrieve"
    ),
    db: Annotated[AsyncSession, Depends(get_db)] = None,
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Get a loyalty membership by ID.

    Requires tenant owner, manager, or staff role.
    """
    db_membership = await loyalty_service.get_membership(db, tenant_id, membership_id)
    if not db_membership:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Loyalty membership with ID {membership_id} not found",
        )
    return db_membership


@router.get(
    "/memberships/account/{account_id}",
    response_model=List[LoyaltyMembershipRead],
    dependencies=[
        Depends(
            require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF], "header")
        )
    ],
)
async def get_account_loyalty_memberships(
    account_id: uuid.UUID = Path(..., description="The ID of the account to get memberships for"),
    skip: int = Query(0, ge=0, description="Number of memberships to skip"),
    limit: int = Query(100, ge=1, le=100, description="Maximum number of memberships to return"),
    status: Optional[LoyaltyMembershipStatus] = Query(
        None, description="Filter by membership status"
    ),
    db: Annotated[AsyncSession, Depends(get_db)] = None,
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Get all loyalty memberships for a specific account.

    Requires tenant owner, manager, or staff role.
    """
    db_memberships = await loyalty_service.get_account_memberships(
        db, tenant_id, account_id, skip, limit, status
    )
    return db_memberships


@router.put(
    "/memberships/{membership_id}",
    response_model=LoyaltyMembershipRead,
    dependencies=[
        Depends(
            require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF], "header")
        )
    ],
)
async def update_loyalty_membership(
    membership_in: LoyaltyMembershipUpdate,
    membership_id: uuid.UUID = Path(..., description="The ID of the loyalty membership to update"),
    db: Annotated[AsyncSession, Depends(get_db)] = None,
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Update a loyalty membership.

    Requires tenant owner, manager, or staff role.
    """
    db_membership = await loyalty_service.update_membership(
        db, tenant_id, membership_id, membership_in
    )
    if not db_membership:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Loyalty membership with ID {membership_id} not found",
        )
    return db_membership


# ==================== Loyalty Transaction Endpoints ====================


@router.post(
    "/transactions",
    response_model=LoyaltyTransactionRead,
    status_code=status.HTTP_201_CREATED,
    dependencies=[
        Depends(
            require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF], "header")
        )
    ],
)
async def create_loyalty_transaction(
    transaction_in: LoyaltyTransactionCreate,
    current_user: Annotated[User, Depends(get_current_active_user)],
    db: Annotated[AsyncSession, Depends(get_db)],
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Create a new loyalty transaction (earn or redeem points).

    Requires tenant owner, manager, or staff role.
    """
    try:
        db_transaction, _ = await loyalty_service.create_transaction(db, tenant_id, transaction_in)
        return db_transaction
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create loyalty transaction: {str(e)}",
        )


@router.get(
    "/transactions/membership/{membership_id}",
    response_model=List[LoyaltyTransactionRead],
    dependencies=[
        Depends(
            require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF], "header")
        )
    ],
)
async def get_membership_transactions(
    membership_id: uuid.UUID = Path(
        ..., description="The ID of the membership to get transactions for"
    ),
    skip: int = Query(0, ge=0, description="Number of transactions to skip"),
    limit: int = Query(100, ge=1, le=100, description="Maximum number of transactions to return"),
    transaction_type: Optional[LoyaltyTransactionType] = Query(
        None, description="Filter by transaction type"
    ),
    start_date: Optional[datetime] = Query(None, description="Filter by start date (inclusive)"),
    end_date: Optional[datetime] = Query(None, description="Filter by end date (inclusive)"),
    db: Annotated[AsyncSession, Depends(get_db)] = None,
    tenant_id: uuid.UUID = Depends(lambda: uuid.UUID(Depends(get_current_active_user).tenant_id)),
):
    """
    Get all transactions for a specific loyalty membership.

    Requires tenant owner, manager, or staff role.
    """
    db_transactions = await loyalty_service.get_membership_transactions(
        db,
        tenant_id,
        membership_id,
        skip,
        limit,
        transaction_type,
        start_date,
        end_date,
    )
    return db_transactions
