"""
Help Center Models

Modelos de banco de dados para o sistema de help center.
"""

import uuid
from datetime import datetime, timedelta, timezone
from enum import Enum
from typing import Optional

from sqlalchemy import (
    Boolean, Column, DateTime, Enum as SQLEnum, ForeignKey, 
    Integer, String, Text, UUID
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import Base


class TicketStatus(str, Enum):
    """Status do ticket."""
    NEW = "new"
    OPEN = "open"
    PENDING = "pending"
    RESOLVED = "resolved"
    CLOSED = "closed"


class TicketPriority(str, Enum):
    """Prioridade do ticket."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class TicketCategory(str, Enum):
    """Categoria do ticket."""
    QUESTION = "question"
    INCIDENT = "incident"
    PROBLEM = "problem"
    REQUEST = "request"
    SUGGESTION = "suggestion"


class MessageType(str, Enum):
    """Tipo de mensagem."""
    TEXT = "text"
    IMAGE = "image"
    FILE = "file"


class Ticket(Base):
    """Modelo de ticket de suporte."""
    
    __tablename__ = "help_center_tickets"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Informações básicas
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    
    # Status e prioridade
    status = Column(SQLEnum(TicketStatus), default=TicketStatus.NEW, nullable=False)
    priority = Column(SQLEnum(TicketPriority), default=TicketPriority.MEDIUM, nullable=False)
    category = Column(SQLEnum(TicketCategory), nullable=False)
    
    # Relacionamentos
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    assigned_admin_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    
    # Controle de leitura
    is_read_by_admin = Column(Boolean, default=False)
    is_read_by_user = Column(Boolean, default=True)  # Usuário sempre "leu" seu próprio ticket
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    resolved_at = Column(DateTime(timezone=True), nullable=True)
    closed_at = Column(DateTime(timezone=True), nullable=True)
    expires_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relacionamentos
    user = relationship("User", foreign_keys=[user_id], back_populates="help_tickets")
    tenant = relationship("Tenant", foreign_keys=[tenant_id])
    assigned_admin = relationship("User", foreign_keys=[assigned_admin_id])
    messages = relationship("TicketMessage", back_populates="ticket", cascade="all, delete-orphan")
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Define expiração para 365 dias para usuários
        if not self.expires_at:
            self.expires_at = datetime.now(timezone.utc) + timedelta(days=365)
    
    @property
    def is_expired(self) -> bool:
        """Verifica se o ticket expirou."""
        if not self.expires_at:
            return False
        return datetime.now(timezone.utc) > self.expires_at
    
    def mark_as_read_by_admin(self):
        """Marca ticket como lido pelo admin."""
        self.is_read_by_admin = True
    
    def mark_as_read_by_user(self):
        """Marca ticket como lido pelo usuário."""
        self.is_read_by_user = True


class TicketMessage(Base):
    """Modelo de mensagem de ticket."""

    __tablename__ = "help_center_ticket_messages"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Relacionamentos
    ticket_id = Column(UUID(as_uuid=True), ForeignKey("help_center_tickets.id"), nullable=False)
    sender_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)

    # Conteúdo da mensagem
    message_content = Column(Text, nullable=False)
    message_type = Column(SQLEnum(MessageType), default=MessageType.TEXT, nullable=False)

    # Arquivo anexo (integração com sistema de mídia)
    file_path = Column(String(500), nullable=True)
    file_name = Column(String(255), nullable=True)
    file_size = Column(Integer, nullable=True)
    mime_type = Column(String(100), nullable=True)

    # Controle de leitura
    is_read = Column(Boolean, default=False)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    expires_at = Column(DateTime(timezone=True), nullable=True)

    # Relacionamentos
    ticket = relationship("Ticket", back_populates="messages")
    sender = relationship("User", foreign_keys=[sender_id])

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Define expiração para 30 dias para notificações
        if not self.expires_at:
            self.expires_at = datetime.now(timezone.utc) + timedelta(days=30)

    @property
    def is_expired(self) -> bool:
        """Verifica se a mensagem expirou."""
        if not self.expires_at:
            return False
        return datetime.now(timezone.utc) > self.expires_at

    def mark_as_read(self):
        """Marca mensagem como lida."""
        self.is_read = True


class KnowledgeBaseArticle(Base):
    """Modelo de artigo da base de conhecimento."""

    __tablename__ = "help_center_kb_articles"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Conteúdo do artigo
    title = Column(String(255), nullable=False)
    content = Column(Text, nullable=False)
    category = Column(String(100), nullable=True)
    tags = Column(String(500), nullable=True)  # Tags separadas por vírgula

    # Configurações de visibilidade
    is_public = Column(Boolean, default=True)  # Para self-service dos clientes
    is_internal = Column(Boolean, default=False)  # Para uso interno dos admins
    is_active = Column(Boolean, default=True)

    # Relacionamentos
    created_by_admin_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)

    # Métricas
    view_count = Column(Integer, default=0)
    helpful_count = Column(Integer, default=0)
    not_helpful_count = Column(Integer, default=0)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relacionamentos
    created_by = relationship("User", foreign_keys=[created_by_admin_id])

    def increment_view_count(self):
        """Incrementa contador de visualizações."""
        self.view_count += 1

    def mark_as_helpful(self):
        """Marca artigo como útil."""
        self.helpful_count += 1

    def mark_as_not_helpful(self):
        """Marca artigo como não útil."""
        self.not_helpful_count += 1

    @property
    def helpfulness_ratio(self) -> float:
        """Calcula a taxa de utilidade do artigo."""
        total_votes = self.helpful_count + self.not_helpful_count
        if total_votes == 0:
            return 0.0
        return self.helpful_count / total_votes
