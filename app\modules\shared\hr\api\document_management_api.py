from typing import List, Optional
import uuid
from fastapi import APIRouter, Depends, Query, status, UploadFile, File, Form
from sqlalchemy.ext.asyncio import AsyncSession
import json

from app.core.dependencies import get_db, get_current_tenant, require_tenant_role
from app.core.roles import TenantRole
from app.models.user import User
from app.models.tenant import Tenant

from app.modules.shared.hr.models.document_management import (
    DocumentType,
    DocumentStatus,
)
from app.modules.shared.hr.schemas.document_management import (  # noqa: E402
    DocumentRead,
    DocumentCreate,
    DocumentUpdate,
    DocumentAccessRead,
    DocumentAccessCreate,
    DocumentSignatureRead,
    DocumentSignatureCreate,
    DocumentTemplateRead,
    DocumentTemplateCreate,
    GenerateDocumentRequest,
)
from app.modules.shared.hr.services.document_management_service import (  # noqa: E402
    document_management_service,
)

router = APIRouter(prefix="/documents", tags=["HR - Document Management"])


@router.post(
    "",
    response_model=DocumentRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create Document",
    description="Upload and create a new document.",
)
async def create_document(
    title: str = Form(...),
    document_type: DocumentType = Form(...),
    file: UploadFile = File(...),
    description: Optional[str] = Form(None),
    employee_id: Optional[uuid.UUID] = Form(None),
    is_confidential: bool = Form(False),
    access_level: Optional[str] = Form(None),
    expiry_date: Optional[str] = Form(None),
    tags: Optional[str] = Form(None),
    metadata: Optional[str] = Form(None),
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Upload and create a new document."""
    # Parse JSON strings
    tags_list = json.loads(tags) if tags else None
    metadata_dict = json.loads(metadata) if metadata else None

    document_data = DocumentCreate(
        title=title,
        description=description,
        document_type=document_type,
        employee_id=employee_id,
        file_name=file.filename,
        file_type=file.content_type,
        file_size=0,  # Will be calculated during upload
        file_path="",  # Will be set during upload
        is_confidential=is_confidential,
        access_level=access_level,
        expiry_date=expiry_date,
        tags=tags_list,
        metadata=metadata_dict,
    )

    return await document_management_service.create_document(
        db=db, tenant_id=tenant.id, document_data=document_data, file=file
    )


@router.get(
    "/{document_id}",
    response_model=DocumentRead,
    summary="Get Document",
    description="Get a document by ID.",
)
async def get_document(
    document_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Get a document by ID."""
    return await document_management_service.get_document(
        db=db, tenant_id=tenant.id, document_id=document_id
    )


@router.get(
    "",
    response_model=List[DocumentRead],
    summary="List Documents",
    description="Get all documents with optional filtering.",
)
async def get_documents(
    employee_id: Optional[uuid.UUID] = None,
    document_type: Optional[DocumentType] = None,
    status: Optional[DocumentStatus] = None,
    is_confidential: Optional[bool] = None,
    search: Optional[str] = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Get all documents with optional filtering."""
    return await document_management_service.get_documents(
        db=db,
        tenant_id=tenant.id,
        employee_id=employee_id,
        document_type=document_type,
        status=status,
        is_confidential=is_confidential,
        search=search,
        skip=skip,
        limit=limit,
    )


@router.put(
    "/{document_id}",
    response_model=DocumentRead,
    summary="Update Document",
    description="Update a document by ID.",
)
async def update_document(
    document_id: uuid.UUID,
    document_data: DocumentUpdate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Update a document by ID."""
    return await document_management_service.update_document(
        db=db, tenant_id=tenant.id, document_id=document_id, document_data=document_data
    )


@router.delete(
    "/{document_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete Document",
    description="Delete a document by ID.",
)
async def delete_document(
    document_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Delete a document by ID."""
    await document_management_service.delete_document(
        db=db, tenant_id=tenant.id, document_id=document_id
    )


@router.post(
    "/access",
    response_model=DocumentAccessRead,
    status_code=status.HTTP_201_CREATED,
    summary="Record Document Access",
    description="Record a document access event.",
)
async def record_document_access(
    access_data: DocumentAccessCreate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Record a document access event."""
    return await document_management_service.record_document_access(
        db=db, tenant_id=tenant.id, access_data=access_data
    )


@router.post(
    "/signature",
    response_model=DocumentSignatureRead,
    status_code=status.HTTP_201_CREATED,
    summary="Sign Document",
    description="Sign a document.",
)
async def sign_document(
    signature_data: DocumentSignatureCreate,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER, TenantRole.STAFF])
    ),
):
    """Sign a document."""
    return await document_management_service.create_document_signature(
        db=db, tenant_id=tenant.id, signature_data=signature_data
    )


@router.post(
    "/templates",
    response_model=DocumentTemplateRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create Document Template",
    description="Create a new document template.",
)
async def create_document_template(
    name: str = Form(...),
    document_type: DocumentType = Form(...),
    file: Optional[UploadFile] = File(None),
    description: Optional[str] = Form(None),
    content: Optional[str] = Form(None),
    variables: Optional[str] = Form(None),
    metadata: Optional[str] = Form(None),
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Create a new document template."""
    # Parse JSON strings
    variables_list = json.loads(variables) if variables else None
    metadata_dict = json.loads(metadata) if metadata else None

    template_data = DocumentTemplateCreate(
        name=name,
        description=description,
        document_type=document_type,
        content=content,
        variables=variables_list,
        metadata=metadata_dict,
        file_path="",  # Will be set during upload
    )

    return await document_management_service.create_document_template(
        db=db, tenant_id=tenant.id, template_data=template_data, file=file
    )


@router.post(
    "/generate",
    response_model=DocumentRead,
    status_code=status.HTTP_201_CREATED,
    summary="Generate Document from Template",
    description="Generate a new document from a template.",
)
async def generate_document(
    request_data: GenerateDocumentRequest,
    db: AsyncSession = Depends(get_db),
    tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(require_tenant_role([TenantRole.OWNER, TenantRole.MANAGER])),
):
    """Generate a new document from a template."""
    return await document_management_service.generate_document_from_template(
        db=db, tenant_id=tenant.id, request_data=request_data
    )
