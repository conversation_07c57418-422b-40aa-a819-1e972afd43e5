from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from app.db.session import get_db
from app.modules.core.functions.offerts.services.offerts_service import OffertsService
from app.modules.core.functions.offerts.schemas import offerts as offerts_schemas
import uuid

router = APIRouter()

@router.post("/coupons/", response_model=offerts_schemas.DiscountCoupon)
def create_discount_coupon(coupon: offerts_schemas.DiscountCouponCreate, db: Session = Depends(get_db)):
    service = OffertsService(db)
    return service.create_coupon(coupon=coupon)

@router.get("/coupons/", response_model=List[offerts_schemas.DiscountCoupon])
def get_discount_coupons(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    service = OffertsService(db)
    return service.get_coupons(skip=skip, limit=limit)

@router.get("/coupons/{code}", response_model=offerts_schemas.DiscountCoupon)
def get_discount_coupon_by_code(code: str, db: Session = Depends(get_db)):
    service = OffertsService(db)
    coupon = service.get_coupon_by_code(code=code)
    if not coupon:
        raise HTTPException(status_code=404, detail="Coupon not found")
    return coupon

@router.post("/apply-coupon/", response_model=offerts_schemas.AppliedCoupon)
def apply_coupon_to_order(
    order_id: uuid.UUID,
    code: str,
    user_id: uuid.UUID, # In a real app, this would come from the authenticated user
    db: Session = Depends(get_db)
):
    service = OffertsService(db)
    try:
        applied_coupon = service.apply_coupon(order_id=order_id, code=code, user_id=user_id)
        return applied_coupon
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))