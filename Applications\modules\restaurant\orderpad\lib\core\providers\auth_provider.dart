import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/user_model.dart';
import '../services/storage_service.dart';
import '../constants/app_constants.dart';

// Auth state
class AuthState {
  final bool isLoading;
  final bool isAuthenticated;
  final UserModel? user;
  final String? error;

  const AuthState({
    this.isLoading = false,
    this.isAuthenticated = false,
    this.user,
    this.error,
  });

  AuthState copyWith({
    bool? isLoading,
    bool? isAuthenticated,
    UserModel? user,
    String? error,
  }) {
    return AuthState(
      isLoading: isLoading ?? this.isLoading,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      user: user ?? this.user,
      error: error ?? this.error,
    );
  }
}

// Auth notifier
class AuthNotifier extends StateNotifier<AuthState> {
  AuthNotifier() : super(const AuthState()) {
    _checkAuthStatus();
  }

  void _checkAuthStatus() {
    final isLoggedIn = StorageService.isLoggedIn();
    if (isLoggedIn) {
      final user = StorageService.getCurrentUser();
      if (user != null) {
        state = state.copyWith(
          isAuthenticated: true,
          user: user,
        );
      }
    }
  }

  Future<bool> login(String email, String password) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Simulate API delay
      await Future.delayed(const Duration(milliseconds: 1500));

      // Check mock credentials
      UserModel? user;
      
      if (email == AppConstants.mockOwnerEmail && 
          password == AppConstants.mockOwnerPassword) {
        user = UserModel(
          id: 'owner-001',
          name: 'Admin Owner',
          email: email,
          role: AppConstants.roleOwner,
          phone: '+55 11 99999-9999',
          isActive: true,
          createdAt: DateTime.now().subtract(const Duration(days: 30)),
          lastLoginAt: DateTime.now(),
          permissions: UserPermissions.allPermissions,
        );
      } else if (email == AppConstants.mockStaffEmail && 
                 password == AppConstants.mockStaffPassword) {
        user = UserModel(
          id: 'staff-001',
          name: 'Garçom Silva',
          email: email,
          role: AppConstants.roleStaff,
          phone: '+55 11 88888-8888',
          isActive: true,
          createdAt: DateTime.now().subtract(const Duration(days: 15)),
          lastLoginAt: DateTime.now(),
          permissions: UserPermissions.defaultStaffPermissions,
        );
      }

      if (user != null) {
        await StorageService.saveCurrentUser(user);
        state = state.copyWith(
          isLoading: false,
          isAuthenticated: true,
          user: user,
        );
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: 'Email ou senha incorretos',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Erro ao fazer login: ${e.toString()}',
      );
      return false;
    }
  }

  Future<void> logout() async {
    state = state.copyWith(isLoading: true);
    
    try {
      await StorageService.clearCurrentUser();
      state = const AuthState();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Erro ao fazer logout: ${e.toString()}',
      );
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  bool get isOwner => state.user?.role == AppConstants.roleOwner;
  bool get isStaff => state.user?.role == AppConstants.roleStaff;
  
  bool hasPermission(String permission) {
    return state.user?.hasPermission(permission) ?? false;
  }
}

// Auth provider
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier();
});

// Convenience providers
final currentUserProvider = Provider<UserModel?>((ref) {
  return ref.watch(authProvider).user;
});

final isAuthenticatedProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isAuthenticated;
});

final userRoleProvider = Provider<String?>((ref) {
  return ref.watch(authProvider).user?.role;
});

final isOwnerProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).user?.role == AppConstants.roleOwner;
});

final isStaffProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).user?.role == AppConstants.roleStaff;
});