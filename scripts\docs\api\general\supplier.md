# General - Supplier

**Categoria:** General
**Módulo:** Supplier
**Total de Endpoints:** 11
**Gerado em:** 27/06/2025, 20:20:27

## 📋 Endpoints

- [GET /api/modules/supplier/auto-replenishment/suggestions](#get-apimodulessupplierauto-replenishmentsuggestions) - Obter Sugestões de Reposição Automática
- [GET /api/modules/supplier/competitive-pricing/{inventory_item_id}](#get-apimodulessuppliercompetitive-pricinginventory-item-id) - Obter Preços Competitivos
- [GET /api/modules/supplier/product-suppliers](#get-apimodulessupplierproduct-suppliers) - Listar Associações Produto-Fornecedor
- [POST /api/modules/supplier/product-suppliers](#post-apimodulessupplierproduct-suppliers) - Criar Associação Produto-Fornecedor
- [GET /api/modules/supplier/suppliers](#get-apimodulessuppliersuppliers) - Listar Fornecedores
- [POST /api/modules/supplier/suppliers](#post-apimodulessuppliersuppliers) - Criar Fornecedor
- [DELETE /api/modules/supplier/suppliers/{supplier_id}](#delete-apimodulessuppliersupplierssupplier-id) - Excluir Fornecedor
- [GET /api/modules/supplier/suppliers/{supplier_id}](#get-apimodulessuppliersupplierssupplier-id) - Obter Fornecedor
- [PUT /api/modules/supplier/suppliers/{supplier_id}](#put-apimodulessuppliersupplierssupplier-id) - Atualizar Fornecedor
- [GET /api/modules/supplier/suppliers/{supplier_id}/invoices](#get-apimodulessuppliersupplierssupplier-idinvoices) - Listar Faturas do Fornecedor
- [POST /api/modules/supplier/suppliers/{supplier_id}/invoices](#post-apimodulessuppliersupplierssupplier-idinvoices) - Registrar Fatura do Fornecedor

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### CompetitivePricingView

**Descrição:** Visão de preços competitivos para um produto.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `inventory_item_id` | string | ✅ | - |
| `inventory_item_name` | string | ✅ | - |
| `suppliers` | Array[object] | ❌ | - |
| `lowest_price` | unknown | ❌ | - |
| `highest_price` | unknown | ❌ | - |
| `price_range_percentage` | unknown | ❌ | - |

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### ProductSupplierCreate

**Descrição:** Schema para criação de associação produto-fornecedor.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `supplier_product_code` | unknown | ❌ | - |
| `supplier_product_name` | unknown | ❌ | - |
| `current_price` | unknown | ❌ | - |
| `minimum_order_quantity` | integer | ❌ | - |
| `lead_time_days` | integer | ❌ | - |
| `priority_level` | PriorityLevel | ❌ | - |
| `priority_order` | integer | ❌ | - |
| `auto_replenishment_authorized` | boolean | ❌ | - |
| `inventory_item_id` | string | ✅ | - |
| `supplier_id` | string | ✅ | - |

### ProductSupplierRead

**Descrição:** Schema para leitura de associação produto-fornecedor.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `supplier_product_code` | unknown | ❌ | - |
| `supplier_product_name` | unknown | ❌ | - |
| `current_price` | unknown | ❌ | - |
| `minimum_order_quantity` | integer | ❌ | - |
| `lead_time_days` | integer | ❌ | - |
| `priority_level` | PriorityLevel | ❌ | - |
| `priority_order` | integer | ❌ | - |
| `auto_replenishment_authorized` | boolean | ❌ | - |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `inventory_item_id` | string | ✅ | - |
| `supplier_id` | string | ✅ | - |
| `is_active` | boolean | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### SupplierCreate

**Descrição:** Schema para criação de fornecedor.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `description` | unknown | ❌ | - |
| `contact_email` | unknown | ❌ | - |
| `contact_phone` | unknown | ❌ | - |
| `address` | unknown | ❌ | - |
| `tax_id` | unknown | ❌ | - |
| `website` | unknown | ❌ | - |
| `auto_replenishment_enabled` | boolean | ❌ | - |
| `competitive_pricing_visible` | boolean | ❌ | - |
| `status` | SupplierStatus | ❌ | - |

### SupplierInvoiceCreate

**Descrição:** Schema for supplier to register an invoice.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `shopping_list_items` | Array[string] | ✅ | Shopping list items delivered |
| `invoice_number` | string | ✅ | Invoice number |
| `invoice_date` | string | ✅ | Invoice date |
| `total_amount` | unknown | ✅ | Total invoice amount |
| `delivery_note_number` | unknown | ❌ | Delivery note number |
| `due_date` | unknown | ❌ | Payment due date |
| `notes` | unknown | ❌ | Additional notes |

### SupplierInvoiceRead

**Descrição:** Schema for reading supplier invoice transactions.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `amount` | string | ✅ | - |
| `description` | string | ✅ | - |
| `transaction_date` | string | ✅ | - |
| `reference_number` | string | ✅ | - |
| `notes` | unknown | ❌ | - |
| `payment_method_id` | unknown | ❌ | - |
| `is_paid` | boolean | ❌ | Whether invoice is paid |

### SupplierRead

**Descrição:** Schema para leitura de fornecedor.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | string | ✅ | - |
| `description` | unknown | ❌ | - |
| `contact_email` | unknown | ❌ | - |
| `contact_phone` | unknown | ❌ | - |
| `address` | unknown | ❌ | - |
| `tax_id` | unknown | ❌ | - |
| `website` | unknown | ❌ | - |
| `auto_replenishment_enabled` | boolean | ❌ | - |
| `competitive_pricing_visible` | boolean | ❌ | - |
| `id` | string | ✅ | - |
| `tenant_id` | string | ✅ | - |
| `status` | SupplierStatus | ✅ | - |
| `created_at` | string | ✅ | - |
| `updated_at` | string | ✅ | - |

### SupplierUpdate

**Descrição:** Schema para atualização de fornecedor.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `name` | unknown | ❌ | - |
| `description` | unknown | ❌ | - |
| `contact_email` | unknown | ❌ | - |
| `contact_phone` | unknown | ❌ | - |
| `address` | unknown | ❌ | - |
| `tax_id` | unknown | ❌ | - |
| `website` | unknown | ❌ | - |
| `status` | unknown | ❌ | - |
| `auto_replenishment_enabled` | unknown | ❌ | - |
| `competitive_pricing_visible` | unknown | ❌ | - |

## 🔗 Endpoints Detalhados

### GET /api/modules/supplier/auto-replenishment/suggestions {#get-apimodulessupplierauto-replenishmentsuggestions}

**Resumo:** Obter Sugestões de Reposição Automática
**Descrição:** Obtém sugestões de reposição automática para itens com estoque baixo.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `low_stock_threshold` | integer | query | ❌ | Limite de estoque baixo |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/supplier/auto-replenishment/suggestions" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/supplier/competitive-pricing/{inventory_item_id} {#get-apimodulessuppliercompetitive-pricinginventory-item-id}

**Resumo:** Obter Preços Competitivos
**Descrição:** Obtém visão de preços competitivos para um produto.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `inventory_item_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [CompetitivePricingView](#competitivepricingview)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/supplier/competitive-pricing/{inventory_item_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/supplier/product-suppliers {#get-apimodulessupplierproduct-suppliers}

**Resumo:** Listar Associações Produto-Fornecedor
**Descrição:** Lista associações entre produtos e fornecedores.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `inventory_item_id` | string | query | ❌ | Filtrar por produto |
| `supplier_id` | string | query | ❌ | Filtrar por fornecedor |
| `active_only` | boolean | query | ❌ | Apenas associações ativas |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/supplier/product-suppliers" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/supplier/product-suppliers {#post-apimodulessupplierproduct-suppliers}

**Resumo:** Criar Associação Produto-Fornecedor
**Descrição:** Cria associação entre produto e fornecedor.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [ProductSupplierCreate](#productsuppliercreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [ProductSupplierRead](#productsupplierread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/supplier/product-suppliers" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/supplier/suppliers {#get-apimodulessuppliersuppliers}

**Resumo:** Listar Fornecedores
**Descrição:** Lista todos os fornecedores do tenant.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `status` | string | query | ❌ | Filtrar por status |
| `skip` | integer | query | ❌ | Número de registros para pular |
| `limit` | integer | query | ❌ | Limite de registros |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/supplier/suppliers" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/supplier/suppliers {#post-apimodulessuppliersuppliers}

**Resumo:** Criar Fornecedor
**Descrição:** Cria um novo fornecedor.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [SupplierCreate](#suppliercreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [SupplierRead](#supplierread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/supplier/suppliers" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### DELETE /api/modules/supplier/suppliers/{supplier_id} {#delete-apimodulessuppliersupplierssupplier-id}

**Resumo:** Excluir Fornecedor
**Descrição:** Exclui um fornecedor (soft delete).

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `supplier_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [SupplierRead](#supplierread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/modules/supplier/suppliers/{supplier_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/modules/supplier/suppliers/{supplier_id} {#get-apimodulessuppliersupplierssupplier-id}

**Resumo:** Obter Fornecedor
**Descrição:** Obtém detalhes de um fornecedor específico.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `supplier_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [SupplierRead](#supplierread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/supplier/suppliers/{supplier_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/modules/supplier/suppliers/{supplier_id} {#put-apimodulessuppliersupplierssupplier-id}

**Resumo:** Atualizar Fornecedor
**Descrição:** Atualiza dados de um fornecedor.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `supplier_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [SupplierUpdate](#supplierupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [SupplierRead](#supplierread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/modules/supplier/suppliers/{supplier_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/modules/supplier/suppliers/{supplier_id}/invoices {#get-apimodulessuppliersupplierssupplier-idinvoices}

**Resumo:** Listar Faturas do Fornecedor
**Descrição:** Lista faturas registradas pelo fornecedor.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `supplier_id` | string | path | ✅ | - |
| `skip` | integer | query | ❌ | Número de registros para pular |
| `limit` | integer | query | ❌ | Limite de registros |
| `X-Tenant-ID` | string | header | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/modules/supplier/suppliers/{supplier_id}/invoices" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/modules/supplier/suppliers/{supplier_id}/invoices {#post-apimodulessuppliersupplierssupplier-idinvoices}

**Resumo:** Registrar Fatura do Fornecedor
**Descrição:** Permite ao fornecedor registrar uma fatura para itens entregues.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `supplier_id` | string | path | ✅ | - |
| `X-Tenant-ID` | string | header | ❌ | - |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [SupplierInvoiceCreate](#supplierinvoicecreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [SupplierInvoiceRead](#supplierinvoiceread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/modules/supplier/suppliers/{supplier_id}/invoices" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---
