"""
Schemas para códigos temporários de autenticação do KDS.
"""
import uuid
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field


class KDSTempCodeBase(BaseModel):
    """Schema base para códigos temporários do KDS."""
    pass


class KDSTempCodeCreate(KDSTempCodeBase):
    """Schema para criação de código temporário do KDS."""
    tenant_id: uuid.UUID = Field(..., description="ID do tenant (restaurante)")
    expires_hours: Optional[int] = Field(
        default=24, 
        ge=1, 
        le=168,  # máximo 7 dias
        description="Horas até expiração (padrão: 24h, máximo: 168h)"
    )


class KDSTempCodeRead(KDSTempCodeBase):
    """Schema para leitura de código temporário do KDS."""
    id: uuid.UUID
    tenant_id: uuid.UUID
    code: str = Field(..., description="Código de 6 dígitos")
    created_at: datetime
    expires_at: datetime
    is_active: bool
    used_at: Optional[datetime] = None
    is_expired: bool = Field(..., description="Se o código está expirado")
    is_valid: bool = Field(..., description="Se o código é válido para uso")

    class Config:
        from_attributes = True


class KDSTempCodeGenerate(BaseModel):
    """Schema para resposta de geração de código temporário."""
    code: str = Field(..., description="Código de 6 dígitos gerado")
    expires_at: datetime = Field(..., description="Data/hora de expiração")
    expires_in_hours: int = Field(..., description="Horas até expiração")
    tenant_id: uuid.UUID = Field(..., description="ID do tenant")
    message: str = Field(..., description="Mensagem informativa")


class KDSAuthRequest(BaseModel):
    """Schema para requisição de autenticação do KDS."""
    restaurant_uuid: uuid.UUID = Field(..., description="UUID do restaurante")
    temp_code: str = Field(
        ..., 
        min_length=6, 
        max_length=6,
        description="Código temporário de 6 dígitos"
    )


class KDSAuthResponse(BaseModel):
    """Schema para resposta de autenticação do KDS."""
    access_token: str = Field(..., description="Token de acesso JWT")
    token_type: str = Field(default="bearer", description="Tipo do token")
    expires_in: int = Field(..., description="Segundos até expiração do token")
    tenant_id: uuid.UUID = Field(..., description="ID do tenant autenticado")
    tenant_name: str = Field(..., description="Nome do restaurante")


class KDSAuthError(BaseModel):
    """Schema para erros de autenticação do KDS."""
    error: str = Field(..., description="Tipo do erro")
    message: str = Field(..., description="Mensagem de erro")
    details: Optional[str] = Field(None, description="Detalhes adicionais")
