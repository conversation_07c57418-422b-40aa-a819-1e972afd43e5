'use client';

import { useState, useEffect, useRef } from 'react';
import { ExclamationTriangleIcon, CheckCircleIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import AddressAutocomplete from './AddressAutocomplete';
import MapSelector from './MapSelector';
import StateSelector from './StateSelector';
import CitySelector from './CitySelector';
import CountrySelector from './CountrySelector';

interface Address {
  street: string;
  number?: string;
  complement?: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  latitude?: number;
  longitude?: number;
}

interface AddressFormProps {
  address: Address;
  onUpdate: (field: keyof Address, value: any) => void;
  onLocationSelect?: (lat: number, lng: number, addressData?: Partial<Address>) => void;
  onSave?: (address: Address) => Promise<void>;
}

interface ValidationErrors {
  street?: string;
  number?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  general?: string;
}

const COUNTRIES = [
  { code: 'US', name: 'United States', flag: '🇺🇸' },
  { code: 'BR', name: 'Brazil', flag: '🇧🇷' },
  { code: 'CA', name: 'Canada', flag: '🇨🇦' },
  { code: 'GB', name: 'United Kingdom', flag: '🇬🇧' },
  { code: 'DE', name: 'Germany', flag: '🇩🇪' },
  { code: 'FR', name: 'France', flag: '🇫🇷' },
  { code: 'IT', name: 'Italy', flag: '🇮🇹' },
  { code: 'ES', name: 'Spain', flag: '🇪🇸' },
  { code: 'PT', name: 'Portugal', flag: '🇵🇹' },
  { code: 'AU', name: 'Australia', flag: '🇦🇺' },
  { code: 'JP', name: 'Japan', flag: '🇯🇵' },
  { code: 'MX', name: 'Mexico', flag: '🇲🇽' },
  { code: 'AR', name: 'Argentina', flag: '🇦🇷' },
  { code: 'CL', name: 'Chile', flag: '🇨🇱' },
  { code: 'CO', name: 'Colombia', flag: '🇨🇴' },
];

export default function AddressForm({ address, onUpdate, onLocationSelect, onSave }: AddressFormProps) {
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [isValidating, setIsValidating] = useState(false);
  const [isValid, setIsValid] = useState(false);

  const validateField = (field: keyof Address, value: string): string | undefined => {
    switch (field) {
      case 'street':
        if (!value.trim()) return 'Street address is required';
        if (value.length < 5) return 'Street address must be at least 5 characters';
        break;
      case 'city':
        if (!value.trim()) return 'City is required';
        if (value.length < 2) return 'City must be at least 2 characters';
        break;
      case 'state':
        if (!value.trim()) return 'State/Province is required';
        break;
      case 'zipCode':
        if (!value.trim()) return 'ZIP/Postal code is required';
        // Basic ZIP code validation (can be enhanced for specific countries)
        if (!/^[A-Za-z0-9\s-]{3,10}$/.test(value)) {
          return 'Invalid ZIP/Postal code format';
        }
        break;
    }
    return undefined;
  };

  const handleUpdate = (field: keyof Address, value: string) => {
    const error = validateField(field, value);
    setErrors(prev => ({ ...prev, [field]: error }));
    onUpdate(field, value);

    // Check if form is valid
    const newErrors = { ...errors, [field]: error };
    const hasErrors = Object.values(newErrors).some(error => error);
    const hasAllFields = address.street && address.city && address.state && address.zipCode;
    setIsValid(!hasErrors && !!hasAllFields);
  };

  const handleAddressSelect = (selectedAddress: Address) => {
    console.log('🏠 AddressForm: Address selected from autocomplete:', selectedAddress);

    // Clear any previous errors
    setErrors({});

    // Update fields incrementally (preserve existing data) - UI only
    const fieldsToUpdate = ['street', 'city', 'state', 'zipCode', 'latitude', 'longitude'] as const;

    fieldsToUpdate.forEach(field => {
      if (selectedAddress[field] !== undefined && selectedAddress[field] !== address[field]) {
        console.log(`🔄 AddressForm: Updating ${field} from "${address[field]}" to "${selectedAddress[field]}" (UI only)`);
        onUpdate(field, selectedAddress[field]);
      }
    });

    // Mark as valid since it came from a verified source
    setIsValid(true);

    // Clear general error if it exists
    setErrors(prev => ({ ...prev, general: undefined }));

    console.log('ℹ️ AddressForm: Address updated in UI. Click "Validate Address" to save.');
  };

  const validateAddress = async () => {
    if (!address.street || !address.city) {
      setErrors(prev => ({ ...prev, general: 'Please provide at least street and city' }));
      return;
    }

    setIsValidating(true);
    setErrors(prev => ({ ...prev, general: undefined }));

    try {
      // Build a more flexible query for better results
      const streetWithNumber = address.number
        ? `${address.number} ${address.street}`.trim()
        : address.street;

      const queryParts = [streetWithNumber, address.city];
      if (address.state) queryParts.push(address.state);
      if (address.zipCode) queryParts.push(address.zipCode);

      const query = queryParts.join(', ');

      console.log('🔍 AddressForm: Validating address:', query);

      // Use Nominatim with better parameters
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?` +
        `format=json&` +
        `q=${encodeURIComponent(query)}&` +
        `limit=1&` +
        `addressdetails=1&` +
        `countrycodes=${address.country.toLowerCase()}&` +
        `dedupe=1`
      );

      if (!response.ok) {
        throw new Error('Geocoding service unavailable');
      }

      const data = await response.json();

      if (data && data.length > 0) {
        const lat = parseFloat(data[0].lat);
        const lng = parseFloat(data[0].lon);

        // Update coordinates in UI
        onUpdate('latitude', lat);
        onUpdate('longitude', lng);
        setIsValid(true);

        // Clear any previous errors
        setErrors(prev => ({ ...prev, general: undefined }));

        // Now save the complete address to backend
        console.log('💾 AddressForm: Address validated, saving to backend...');
        if (onSave) {
          await onSave(address);
          console.log('✅ AddressForm: Address saved successfully');
        }

      } else {
        // Try a simpler query with just city if the full address fails
        const fallbackQuery = `${address.city}, ${address.country}`;
        const fallbackResponse = await fetch(
          `https://nominatim.openstreetmap.org/search?` +
          `format=json&` +
          `q=${encodeURIComponent(fallbackQuery)}&` +
          `limit=1&` +
          `countrycodes=${address.country.toLowerCase()}`
        );

        if (fallbackResponse.ok) {
          const fallbackData = await fallbackResponse.json();
          if (fallbackData && fallbackData.length > 0) {
            setErrors(prev => ({
              ...prev,
              general: 'Exact address not found, but city was located. Please use the autocomplete suggestions above for better results.'
            }));
          } else {
            setErrors(prev => ({
              ...prev,
              general: 'Address not found. Please check your address details or try using the autocomplete suggestions.'
            }));
          }
        } else {
          setErrors(prev => ({
            ...prev,
            general: 'Address not found. Please check your address details or try using the autocomplete suggestions.'
          }));
        }
        setIsValid(false);
      }
    } catch (error) {
      console.error('Geocoding error:', error);
      setErrors(prev => ({ ...prev, general: 'Failed to validate address. Please try again or use the autocomplete suggestions.' }));
      setIsValid(false);
    } finally {
      setIsValidating(false);
    }
  };

  const getFieldStyle = (field: keyof Address) => {
    const hasError = (errors as any)[field];
    const hasValue = address[field];
    
    if (hasError) return 'border-red-300 focus:border-red-500 focus:ring-red-500';
    if (hasValue && !hasError) return 'border-green-300 focus:border-green-500 focus:ring-green-500';
    return 'border-gray-300 focus:border-primary-500 focus:ring-primary-500';
  };

  return (
    <div className="space-y-4">


      <div className="grid grid-cols-1 gap-4">
        {/* Country, State, City - First Row */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Country *
            </label>
            <CountrySelector
              selectedCountry={address.country}
              onCountryChange={(country) => {
                console.log('🌍 AddressForm: Country changed to:', country);
                console.log('🧹 AddressForm: Clearing state and city due to country change');
                handleUpdate('country', country);
                // Clear state and city when country changes
                handleUpdate('state', '');
                handleUpdate('city', '');
              }}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              State/Province *
            </label>
            <StateSelector
              countryCode={address.country}
              selectedState={address.state}
              onStateChange={(state) => {
                console.log('🏛️ AddressForm: State changed to:', state);
                console.log('🧹 AddressForm: Clearing city due to state change');
                handleUpdate('state', state);
                // Clear city when state changes
                handleUpdate('city', '');
              }}
              className={getFieldStyle('state')}
            />
            {errors.state && (
              <p className="mt-1 text-sm text-red-600 flex items-center">
                <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                {errors.state}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              City *
            </label>
            <CitySelector
              countryCode={address.country}
              stateName={address.state}
              selectedCity={address.city}
              onCityChange={(city) => {
                console.log('🏙️ AddressForm: City changed to:', city);
                handleUpdate('city', city);
              }}
              className={getFieldStyle('city')}
            />
            {errors.city && (
              <p className="mt-1 text-sm text-red-600 flex items-center">
                <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                {errors.city}
              </p>
            )}
          </div>
        </div>

        {/* Street Address with Autocomplete + Number */}
        <div className="grid grid-cols-1 gap-4">
          <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
            <div className="md:col-span-5">
              <AddressAutocomplete
                address={address}
                onUpdate={handleUpdate}
                onAddressSelect={handleAddressSelect}
                countryCode={address.country}
                stateName={address.state}
                cityName={address.city}
              />
              {errors.street && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                  {errors.street}
                </p>
              )}
            </div>

            <div className="md:col-span-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Number
              </label>
              <input
                type="text"
                value={address.number || ''}
                onChange={(e) => handleUpdate('number', e.target.value)}
                onFocus={(e) => e.target.removeAttribute('readonly')}
                placeholder="123"
                autoComplete="new-password"
                data-lpignore="true"
                data-1p-ignore="true"
                name={`number-${Math.random().toString(36).substr(2, 9)}`}
                readOnly
                className={`w-full px-3 py-2 border rounded-md focus:outline-none ${getFieldStyle('number')}`}
              />
              {errors.number && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                  {errors.number}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Complement + ZIP Code + Validate Button */}
        <div className="grid grid-cols-1 gap-4">
          <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
            {/* Complement */}
            <div className="md:col-span-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Complement
              </label>
              <input
                type="text"
                value={address.complement || ''}
                onChange={(e) => handleUpdate('complement', e.target.value)}
                onFocus={(e) => e.target.removeAttribute('readonly')}
                placeholder="Apt 4B, Suite 200, etc."
                autoComplete="new-password"
                data-lpignore="true"
                data-1p-ignore="true"
                name={`complement-${Math.random().toString(36).substr(2, 9)}`}
                readOnly
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            {/* ZIP Code */}
            <div className="md:col-span-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                ZIP/Postal Code *
              </label>
              <input
                type="text"
                value={address.zipCode}
                onChange={(e) => handleUpdate('zipCode', e.target.value)}
                onFocus={(e) => e.target.removeAttribute('readonly')}
                placeholder="10001"
                autoComplete="new-password"
                data-lpignore="true"
                data-1p-ignore="true"
                name={`zipcode-${Math.random().toString(36).substr(2, 9)}`}
                readOnly
                className={`w-full px-3 py-2 border rounded-md focus:outline-none ${getFieldStyle('zipCode')}`}
              />
              {errors.zipCode && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                  {errors.zipCode}
                </p>
              )}
            </div>

            {/* Validate Button */}
            <div className="md:col-span-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                &nbsp;
              </label>
              <button
                onClick={validateAddress}
                disabled={isValidating || !address.street || !address.city}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none text-sm font-medium transition-colors ${
                  isValidating || !address.street || !address.city
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed border-gray-300'
                    : 'bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 border-primary-600 focus:border-primary-500'
                }`}
                style={{ height: 'auto', minHeight: '42px' }}
                title="Validate and save address"
              >
                {isValidating ? 'Validating...' : 'Validate & Save'}
              </button>
            </div>
          </div>

          {/* Validation Status */}
          {isValid && address.latitude && address.longitude && (
            <div className="flex items-center space-x-1 mt-2">
              <CheckCircleIcon className="h-4 w-4 text-green-500" />
              <span className="text-sm text-green-700">Address validated</span>
              <span className="text-xs text-gray-500">
                ({address.latitude.toFixed(4)}, {address.longitude.toFixed(4)})
              </span>
            </div>
          )}
        </div>
      </div>

      {/* General Error */}
      {errors.general && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600 flex items-center">
            <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
            {errors.general}
          </p>
        </div>
      )}



      {/* Map Selector */}
      {address.street && address.city && (
        <div className="mt-4">
          <MapSelector
            address={address}
            onLocationSelect={onLocationSelect || ((lat, lng) => {
              onUpdate('latitude', lat);
              onUpdate('longitude', lng);
            })}
          />
        </div>
      )}
    </div>
  );
}
