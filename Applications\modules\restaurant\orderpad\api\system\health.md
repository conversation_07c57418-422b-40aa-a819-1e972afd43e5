# System - Health

**Categoria:** System
**Módulo:** Health
**Total de Endpoints:** 4
**Gerado em:** 08/06/2025, 17:26:01

## 📋 Endpoints

- [GET /api/health](#get-apihealth) - Basic Health
- [GET /api/health/detailed](#get-apihealthdetailed) - Detailed Health
- [GET /api/health/live](#get-apihealthlive) - Liveness Check
- [GET /api/health/ready](#get-apihealthready) - Readiness Check

## 🔗 Endpoints Detalhados

### GET /api/health {#get-apihealth}

**Resumo:** Basic Health
**Descrição:** Basic health check endpoint.

**🔐 Autenticação:** Não requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/health"
```

---

### GET /api/health/detailed {#get-apihealthdetailed}

**Resumo:** Detailed Health
**Descrição:** Detailed health check with all dependencies.

**🔐 Autenticação:** Não requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/health/detailed"
```

---

### GET /api/health/live {#get-apihealthlive}

**Resumo:** Liveness Check
**Descrição:** Kubernetes-style liveness probe.

**🔐 Autenticação:** Não requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/health/live"
```

---

### GET /api/health/ready {#get-apihealthready}

**Resumo:** Readiness Check
**Descrição:** Kubernetes-style readiness probe.

**🔐 Autenticação:** Não requerida

**📥 Respostas:**

**200:** Successful Response

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/health/ready"
```

---
