import logging
import uuid
from typing import Optional, Annotated, List, Literal, Union, TYPE_CHECKING

from fastapi import Depends, HTTPException, Request, status, Header
from sqlalchemy.ext.asyncio import AsyncSession

from app.modules.core.tenants.models.tenant import Tenant
from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.user_auth import get_current_active_user
from app.modules.core.roles.models.roles import (
    SystemRole,
    TenantRole,
    TenantType,
)
from app.modules.core.tenants.dependencies.tenant_context import get_current_tenant
from app.modules.core.tenants.services.tenant_service import tenant_service

# Use TYPE_CHECKING to avoid circular import
if TYPE_CHECKING:
    from app.modules.core.users.models.user import User

logger = logging.getLogger(__name__)


async def get_current_tenant(request: Request, db: AsyncSession = Depends(get_db)) -> Tenant:
    """
    FastAPI dependency to get the current tenant from the X-Tenant-ID header.

    Raises HTTPException if the header is missing, invalid, or if the tenant
    is not found or inactive.

    Args:
        request: The current request object.
        db: The async database session.

    Returns:
        The Tenant object corresponding to the ID in the header.

    Raises:
        HTTPException:
            - 400 Bad Request: If the X-Tenant-ID header is missing or invalid.
            - 404 Not Found: If the tenant is not found or not active.
    """
    tenant_id_str = request.headers.get("X-Tenant-ID")
    if not tenant_id_str:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="X-Tenant-ID header missing",
        )

    try:
        tenant_uuid = uuid.UUID(tenant_id_str)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid X-Tenant-ID format",
        )

    tenant = await tenant_service.get_tenant(db=db, tenant_id=tenant_uuid)

    if not tenant or not tenant.is_active:  # Ensure tenant exists AND is active
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tenant not found or not active",
        )

    return tenant


async def get_current_tenant_from_header(
    x_tenant_id: Annotated[Optional[str], Header(alias="X-Tenant-ID")] = None,
    db: Annotated[AsyncSession, Depends(get_db)] = None,
) -> Optional[Tenant]:
    """
    FastAPI dependency to get the current tenant from the X-Tenant-ID header.
    Used in routes that operate in the context of a specific tenant for an authenticated user.
    Raises HTTPException 400 if X-Tenant-ID is missing or malformed.
    Raises HTTPException 404 if the tenant is not found or inactive.
    """
    if not x_tenant_id:
        logger.warning("get_current_tenant_from_header: X-Tenant-ID header missing.")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="X-Tenant-ID header is required.",
        )

    try:
        tenant_uuid = uuid.UUID(x_tenant_id)
    except ValueError:
        logger.warning(
            f"get_current_tenant_from_header: X-Tenant-ID '{x_tenant_id}' is not a valid UUID."
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid X-Tenant-ID.",
        )

    # db will not be None here since x_tenant_id is not None
    tenant = await tenant_service.get_tenant(db, tenant_id=tenant_uuid)

    if not tenant:
        logger.warning(
            f"get_current_tenant_from_header: Tenant with ID '{tenant_uuid}' " f"not found."
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tenant not found.",
        )

    if not tenant.is_active:
        logger.warning(
            f"get_current_tenant_from_header: Tenant '{tenant_uuid}' "
            f"(ID: {tenant.id}) is inactive."
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Tenant is inactive.",
        )

    return tenant


async def get_tenant_for_admin_or_header(
    x_tenant_id: Annotated[Optional[str], Header(alias="X-Tenant-ID")] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    db: Annotated[AsyncSession, Depends(get_db)] = None,
) -> Optional[Tenant]:
    """
    FastAPI dependency that allows system admins to access without X-Tenant-ID header.
    For regular users, X-Tenant-ID is still required.
    
    Returns:
        - None if user is system admin (global access)
        - Tenant object if X-Tenant-ID is provided and valid
        - Raises HTTPException if regular user without X-Tenant-ID
    """
    # System admins can access without tenant context
    if current_user and current_user.system_role == SystemRole.ADMIN:
        logger.info(f"System admin {current_user.email} accessing without tenant context")
        return None
    
    # For regular users, X-Tenant-ID is required
    if not x_tenant_id:
        logger.warning("get_tenant_for_admin_or_header: X-Tenant-ID header missing for non-admin user.")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="X-Tenant-ID header is required.",
        )

    try:
        tenant_uuid = uuid.UUID(x_tenant_id)
    except ValueError:
        logger.warning(
            f"get_tenant_for_admin_or_header: X-Tenant-ID '{x_tenant_id}' is not a valid UUID."
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid X-Tenant-ID.",
        )

    tenant = await tenant_service.get_tenant(db, tenant_id=tenant_uuid)
    
    if not tenant:
        logger.warning(
            f"get_tenant_for_admin_or_header: Tenant with ID '{tenant_uuid}' not found."
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tenant not found.",
        )

    if not tenant.is_active:
        logger.warning(
            f"get_tenant_for_admin_or_header: Tenant '{tenant_uuid}' is inactive."
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Tenant is inactive.",
        )

    return tenant


async def get_optional_tenant_from_header(
    x_tenant_id: Annotated[Optional[str], Header(alias="X-Tenant-ID")] = None,
    db: Annotated[AsyncSession, Depends(get_db)] = None,
) -> Optional[Tenant]:
    """
    FastAPI dependency to optionally get the current tenant from the X-Tenant-ID header.
    Returns the Tenant object if the header is present and valid, otherwise returns None.
    This dependency DOES NOT raise an exception if the header is missing.
    """
    if not x_tenant_id:
        return None

    try:
        tenant_uuid = uuid.UUID(x_tenant_id)
    except ValueError:
        # Silently ignore invalid UUID format for an optional header
        return None

    tenant = await tenant_service.get_tenant(db, tenant_id=tenant_uuid)

    if not tenant or not tenant.is_active:
        # Silently ignore if tenant not found or inactive
        return None

    return tenant


async def get_tenant_id(
    tenant: Tenant = Depends(get_current_tenant_from_header),
) -> uuid.UUID:
    """
    Dependency that provides the ID of the current tenant.
    Relies on `get_current_tenant_from_header` to have already run.
    """
    return tenant.id


def require_tenant_role(
    required_roles: Union[List[TenantRole], List[str]],
    tenant_id_source: Literal["path", "header"] = "header",
    # Make specific tenant type optional or required
    required_tenant_type: Optional[TenantType] = None,
):
    """
    Factory function to create a dependency that checks if the current user
    has one of the required roles in the current tenant.

    Args:
        required_roles: List of TenantRole values that are allowed to access the endpoint.
        tenant_id_source: Where to get the tenant ID from ("path" or "header").
        required_tenant_type: Optional tenant type that must match.

    Returns:
        A dependency function that checks if the current user has one of the required roles.
    """
    from app.modules.core.auth.security import permission_denied_exception

    async def _require_tenant_role(
        db: Annotated[AsyncSession, Depends(get_db)],
        current_user: Annotated["User", Depends(get_current_active_user)],
        current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)],
    ) -> "User":
        """
        Check if the current user has one of the required roles in the current tenant.

        Args:
            db: The async database session.
            current_user: The current authenticated user.
            current_tenant: The current tenant.

        Returns:
            The User object if the user has one of the required roles.

        Raises:
            HTTPException: If the user doesn't have one of the required roles.
        """
        # 1. Check tenant_type if required_tenant_type is specified
        # Note: tenant_type is now stored in TenantSettings, not Tenant model
        # For now, we skip this validation since we're focusing on restaurant functionality
        # TODO: If needed, implement validation by querying TenantSettings.business_type
        if required_tenant_type:
            logger.debug(
                f"require_tenant_role: Tenant type validation skipped for tenant '{current_tenant.id}' "
                f"(tenant_type is now in TenantSettings, not Tenant model)"
            )

        # 2. Allow System Admins automatically
        if current_user.system_role == SystemRole.ADMIN.value:
            logger.info(
                f"require_tenant_role: User '{current_user.email}' "
                f"is SystemRole.ADMIN. Access granted to tenant "
                f"'{current_tenant.id}'."
            )
            return current_user

        # 3. For SystemRole.USER, check their association and roles within the tenant
        if current_user.system_role == SystemRole.USER.value:
            from app.modules.core.users.services.tenant_user_association_service import (
                tenant_user_association_service,
            )
            
            association = await tenant_user_association_service.get_association_by_user_and_tenant(
                db=db, user_id=current_user.id, tenant_id=current_tenant.id
            )

            if not association:
                logger.warning(
                    f"require_tenant_role: User '{current_user.email}' "
                    f"has no association with tenant '{current_tenant.name}'. "
                    f"Access denied."
                )
                raise permission_denied_exception

            user_tenant_role_str = association.role if association.role else None

            # Convert required_roles to strings for comparison
            required_roles_str = [
                role.value if hasattr(role, "value") else str(role) for role in required_roles
            ]

            logger.debug(
                f"require_tenant_role: User '{current_user.email}' "
                f"in tenant '{current_tenant.id}' has TenantRole: "
                f"'{user_tenant_role_str}'. Required roles: {required_roles_str}."
            )

            if not user_tenant_role_str or user_tenant_role_str not in required_roles_str:
                logger.warning(
                    f"require_tenant_role: User '{current_user.email}' "
                    f"with role '{user_tenant_role_str}' in tenant "
                    f"'{current_tenant.id}' does not have one of the "
                    f"required roles: {required_roles_str}. Access denied."
                )
                raise permission_denied_exception

            logger.info(
                f"require_tenant_role: User '{current_user.email}' "
                f"with role '{user_tenant_role_str}' in tenant "
                f"'{current_tenant.id}' has required role. Access granted."
            )
            return current_user

        # Fallback: Unhandled system_role
        logger.error(
            f"require_tenant_role: User '{current_user.email}' has an "
            f"unhandled system_role '{current_user.system_role}'. "
            f"Access denied in tenant '{current_tenant.id}'."
        )
        raise permission_denied_exception

    return _require_tenant_role


async def get_current_active_user_with_tenant_access(
    request: Request,
    db: Annotated[AsyncSession, Depends(get_db)],
    current_user: Annotated["User", Depends(get_current_active_user)],
) -> "User":
    """
    Dependency that ensures the current user has access to the tenant specified in X-Tenant-ID header.
    
    Returns the current user if they have access to the tenant.
    Raises HTTPException if the user doesn't have access.
    """
    logger.debug(
        f"get_current_active_user_with_tenant_access: Checking access for user '{current_user.email}' "
        f"(ID: {current_user.id}, SystemRole: {current_user.system_role})"
    )

    # System admins have access to all tenants
    if current_user.system_role == SystemRole.ADMIN.value:
        logger.info(
            f"get_current_active_user_with_tenant_access: User '{current_user.email}' is SystemRole.ADMIN. "
            f"Access granted to any tenant."
        )
        return current_user

    # Get the current tenant from the X-Tenant-ID header
    try:
        current_tenant = await get_current_tenant(request, db)
        logger.debug(
            f"get_current_active_user_with_tenant_access: Current tenant resolved: "
            f"'{current_tenant.name}' (ID: {current_tenant.id})"
        )
    except HTTPException:
        # Re-raise the exception from get_current_tenant
        logger.error(
            f"get_current_active_user_with_tenant_access: Could not resolve current tenant for user "
            f"'{current_user.email}'"
        )
        raise

    # Check if the user has any association with the tenant
    from app.modules.core.users.services.tenant_user_association_service import (
        tenant_user_association_service,
    )
    
    association = await tenant_user_association_service.get_association_by_user_and_tenant(
        db=db, user_id=current_user.id, tenant_id=current_tenant.id
    )

    if not association:
        logger.warning(
            f"get_current_active_user_with_tenant_access: User '{current_user.email}' has no association "
            f"with tenant '{current_tenant.name}' (ID: {current_tenant.id}). Access denied."
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"Access denied to tenant '{current_tenant.name}'",
        )

    logger.info(
        f"get_current_active_user_with_tenant_access: User '{current_user.email}' has access to tenant "
        f"'{current_tenant.name}' (ID: {current_tenant.id}) with role '{association.role}'"
    )
    return current_user


__all__ = [
    "get_current_tenant",
    "get_current_tenant_from_header",
    "require_tenant_role",
    "get_current_active_user_with_tenant_access",
]
