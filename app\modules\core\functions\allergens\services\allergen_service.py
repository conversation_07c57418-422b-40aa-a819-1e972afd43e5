"""Service for managing allergens."""

import uuid
from typing import List, Optional, Sequence
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from sqlalchemy.orm import selectinload
from fastapi import HTTPException, status

from app.modules.core.functions.allergens.models.allergen import Allergen
from app.modules.core.functions.allergens.schemas.allergen import (
    AllergenCreate,
    AllergenUpdate,
)


class AllergenService:
    """Service for managing allergens."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_allergen(self, allergen_id: uuid.UUID) -> Optional[Allergen]:
        """Get a single allergen by ID."""
        result = await self.db.execute(
            select(Allergen).where(Allergen.id == allergen_id)
        )
        return result.scalar_one_or_none()
    
    async def get_allergens(
        self,
        skip: int = 0,
        limit: int = 100,
        active_only: bool = True
    ) -> Sequence[Allergen]:
        """Get a list of allergens."""
        query = select(Allergen)
        
        if active_only:
            query = query.where(Allergen.is_active == True)
        
        query = query.order_by(Allergen.name).offset(skip).limit(limit)
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_allergen_by_name(self, name: str) -> Optional[Allergen]:
        """Get an allergen by name."""
        result = await self.db.execute(
            select(Allergen).where(Allergen.name == name)
        )
        return result.scalar_one_or_none()
    
    async def create_allergen(self, allergen_in: AllergenCreate) -> Allergen:
        """Create a new allergen."""
        # Check if allergen with same name already exists
        existing = await self.get_allergen_by_name(allergen_in.name)
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Allergen with name '{allergen_in.name}' already exists"
            )
        
        allergen_data = allergen_in.model_dump()
        db_allergen = Allergen(**allergen_data)
        
        self.db.add(db_allergen)
        await self.db.commit()
        await self.db.refresh(db_allergen)
        
        return db_allergen
    
    async def update_allergen(
        self,
        allergen_id: uuid.UUID,
        allergen_in: AllergenUpdate
    ) -> Optional[Allergen]:
        """Update an existing allergen."""
        db_allergen = await self.get_allergen(allergen_id)
        if not db_allergen:
            return None
        
        # Check name uniqueness if name is being updated
        if allergen_in.name and allergen_in.name != db_allergen.name:
            existing = await self.get_allergen_by_name(allergen_in.name)
            if existing:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Allergen with name '{allergen_in.name}' already exists"
                )
        
        update_data = allergen_in.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_allergen, field, value)
        
        await self.db.commit()
        await self.db.refresh(db_allergen)
        
        return db_allergen
    
    async def delete_allergen(self, allergen_id: uuid.UUID) -> bool:
        """Delete an allergen."""
        db_allergen = await self.get_allergen(allergen_id)
        if not db_allergen:
            return False
        
        await self.db.delete(db_allergen)
        await self.db.commit()
        
        return True
    
    async def get_allergens_by_ids(
        self, 
        allergen_ids: List[uuid.UUID]
    ) -> Sequence[Allergen]:
        """Get multiple allergens by their IDs."""
        if not allergen_ids:
            return []
        
        result = await self.db.execute(
            select(Allergen).where(Allergen.id.in_(allergen_ids))
        )
        return result.scalars().all()
