'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { EyeIcon, TagIcon, ArchiveBoxIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { useMenuFilters } from '@/hooks/useMenuFilters';
import { useCurrency } from '@/hooks/useCurrency';
import { useMenuItemMedia } from '@/hooks/useMenuItemMedia';

interface ReadOnlyMenuViewProps {
  displayMenu: any;
  categories: any[];
  items: any[];
  currentTenant: any;
}

export function ReadOnlyMenuView({
  displayMenu,
  categories,
  items,
  currentTenant
}: ReadOnlyMenuViewProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<any>(null);
  const [selectedSubcategoryId, setSelectedSubcategoryId] = useState<string | null>(null);
  const { formatCurrency } = useCurrency();

  // Use the same filter logic as the main editor
  const {
    filteredItems,
    filteredCategories,
    finalFilteredItems,
    selectedCategoryWithChildren
  } = useMenuFilters({
    categories,
    items,
    selectedCategory,
    selectedSubcategoryId,
    searchQuery,
    filterBy: 'category' as const,
    availabilityFilter: 'all' as const,
    typeFilter: 'all' as const,
  });

  // Auto-select first category when none selected
  useEffect(() => {
    if (!selectedCategory && filteredCategories.length > 0) {
      setSelectedCategory(filteredCategories[0]);
    }
  }, [filteredCategories, selectedCategory]);

  return (
    <div className="min-h-[calc(100vh-200px)] flex flex-col bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20 rounded-xl">
    
      {/* Header com Badge de Cliente */}
      <div className="bg-white/90 backdrop-blur-lg border-b border-white/30 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Menu - {displayMenu?.name || 'Visualização'}
            </h1>
          </div>
          
          <div className="flex items-center space-x-3">
            <div className="flex items-center bg-blue-100 text-blue-800 px-3 py-1.5 rounded-full text-sm font-medium">
              <EyeIcon className="h-4 w-4 mr-1.5" />
              Visualização do Cliente
            </div>
          </div>
        </div>
      </div>

      {/* Search Bar */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-white/20 p-4">
        <div className="relative max-w-md">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
            type="text"
            placeholder="Buscar itens do menu..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/90 backdrop-blur-sm"
          />
        </div>
      </div>

      {/* Categories */}
      <div className="bg-white/60 backdrop-blur-sm border-b border-white/20 p-4">
        <div className="flex space-x-2 overflow-x-auto pb-2">
          {filteredCategories.map((category) => (
            <button
              key={category.id}
              onClick={() => {
                setSelectedCategory(category);
                setSelectedSubcategoryId(null);
              }}
              className={`flex-shrink-0 px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                selectedCategory?.id === category.id
                  ? 'bg-blue-500 text-white shadow-lg'
                  : 'bg-white/80 text-gray-700 hover:bg-white hover:shadow-md'
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>
      </div>

      {/* Subcategories */}
      {selectedCategoryWithChildren?.children && selectedCategoryWithChildren.children.length > 0 && (
        <div className="bg-white/40 backdrop-blur-sm border-b border-white/20 p-4">
          <div className="flex space-x-2 overflow-x-auto pb-2">
            <button
              onClick={() => setSelectedSubcategoryId(null)}
              className={`flex-shrink-0 px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-200 ${
                !selectedSubcategoryId
                  ? 'bg-purple-500 text-white shadow-md'
                  : 'bg-white/80 text-gray-600 hover:bg-white hover:shadow-sm'
              }`}
            >
              Todos
            </button>
            {selectedCategoryWithChildren.children.map((subcategory) => (
              <button
                key={subcategory.id}
                onClick={() => setSelectedSubcategoryId(subcategory.id)}
                className={`flex-shrink-0 px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-200 ${
                  selectedSubcategoryId === subcategory.id
                    ? 'bg-purple-500 text-white shadow-md'
                    : 'bg-white/80 text-gray-600 hover:bg-white hover:shadow-sm'
                }`}
              >
                {subcategory.name}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Menu Items */}
      <div className="flex-1 p-6 overflow-y-auto">
        {finalFilteredItems.length === 0 ? (
          <div className="text-center py-12">
            <ArchiveBoxIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhum item encontrado</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchQuery ? 'Tente ajustar sua busca' : 'Esta categoria não possui itens'}
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {finalFilteredItems.map((item) => (
              <MenuItemCard
                key={item.id}
                item={item}
                formatCurrency={formatCurrency}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

// Menu Item Card Component
function MenuItemCard({ item, formatCurrency }: { item: any; formatCurrency: (value: number) => string }) {
  const { primaryImage } = useMenuItemMedia(item.id);
  const imageUrl = primaryImage;

  return (
    <div className="bg-white/90 backdrop-blur-sm rounded-xl shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden border border-white/30">
      {/* Image */}
      {imageUrl && (
        <div className="relative aspect-video bg-gray-100 overflow-hidden">
          <Image
            src={imageUrl}
            alt={item.name}
            fill
            className="object-cover"
          />
        </div>
      )}
      
      {/* Content */}
      <div className="p-4">
        <div className="flex justify-between items-start mb-2">
          <h3 className="font-semibold text-gray-900 text-lg leading-tight">
            {item.name}
          </h3>
          <span className="text-lg font-bold text-blue-600 ml-2">
            {formatCurrency(item.price)}
          </span>
        </div>
        
        {item.description && (
          <p className="text-gray-600 text-sm mb-3 line-clamp-2">
            {item.description}
          </p>
        )}
        
        {/* Tags */}
        <div className="flex flex-wrap gap-1">
          {!item.is_available && (
            <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">
              Indisponível
            </span>
          )}
          {item.variant_groups && item.variant_groups.length > 0 && (
            <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
              Variações
            </span>
          )}
          {item.modifier_groups && item.modifier_groups.length > 0 && (
            <span className="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded">
              Opcionais
            </span>
          )}
        </div>
      </div>
    </div>
  );
}
