import logging
import uuid
from typing import Optional, TYPE_CHECKING
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from sqlalchemy.exc import IntegrityError
from fastapi import HTTPException, status

# Core models - use the customizations models
from app.modules.core.functions.customizations.models.variant_group import VariantGroup
from app.modules.core.functions.customizations.models.variant_option import VariantOption

# Schemas - use the restaurant menu schemas
from app.modules.tenants.restaurants.menu.schemas.variant_group import (
    VariantGroupCreate,
    VariantGroupUpdate,
    VariantGroupRead,
)
from app.modules.tenants.restaurants.menu.schemas.variant_option import (
    VariantOptionCreate,
    VariantOptionUpdate,
)

# Menu models
from app.modules.tenants.restaurants.menu.models.menu_item import MenuItem

# TYPE_CHECKING imports to avoid circular dependencies
if TYPE_CHECKING:
    pass  # Remove the circular import since it's not used

logger = logging.getLogger(__name__)


class MenuVariantService:
    def __init__(self, db_session: AsyncSession):
        self.db = db_session

    # --- Variant Group Operations ---

    async def create_variant_group(
        self,
        menu_item_id: uuid.UUID,
        group_in: VariantGroupCreate,
        tenant_id: uuid.UUID,
        commit: bool = True,
    ) -> VariantGroup:
        """Creates a new variant group for a menu item."""
        # Ensure menu item exists for tenant
        stmt = select(MenuItem).where(
            MenuItem.id == menu_item_id,
            MenuItem.tenant_id == tenant_id
        )
        result = await self.db.execute(stmt)
        item = result.scalars().first()
        if not item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"MenuItem {menu_item_id} not found.",
            )

        try:
            group_data = group_in.model_dump(exclude={"options"})
            db_group = VariantGroup(**group_data, menu_item_id=menu_item_id, tenant_id=tenant_id)
            self.db.add(db_group)
            await self.db.flush()  # Get ID

            for option_in in group_in.options:
                db_option = VariantOption(
                    **option_in.model_dump(),
                    variant_group_id=db_group.id,
                    tenant_id=tenant_id,
                )
                self.db.add(db_option)

            if commit:
                await self.db.commit()
                # Refresh and reload options
                await self.db.refresh(db_group, attribute_names=["options"])
            logger.info(
                f"VariantGroup created: {db_group.id} for item {menu_item_id}, tenant {tenant_id}"
            )
            return db_group
        except IntegrityError as e:
            if commit:
                await self.db.rollback()
            logger.error(
                f"Error creating variant group for item {menu_item_id}, tenant {tenant_id}: {e}"
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Error creating variant group.",
            )
        except Exception as e:
            if commit:
                await self.db.rollback()
            logger.exception(f"Unexpected error creating variant group: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def get_variant_group(
        self, group_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Optional[VariantGroup]:
        """Gets a variant group by ID for the given tenant."""
        stmt = (
            select(VariantGroup)
            .options(selectinload(VariantGroup.options))
            .where(VariantGroup.id == group_id, VariantGroup.tenant_id == tenant_id)
        )
        result = await self.db.execute(stmt)
        return result.scalars().first()

    async def update_variant_group(
        self, group_id: uuid.UUID, group_in: VariantGroupUpdate, tenant_id: uuid.UUID
    ) -> Optional[VariantGroup]:
        """Updates a variant group."""
        db_group = await self.get_variant_group(group_id, tenant_id)
        if not db_group:
            return None

        update_data = group_in.model_dump(exclude_unset=True, exclude={"options"})
        if not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No fields provided for variant group update.",
            )

        try:
            for key, value in update_data.items():
                setattr(db_group, key, value)

            self.db.add(db_group)
            await self.db.commit()
            await self.db.refresh(db_group, attribute_names=["options"])
            logger.info(f"VariantGroup {group_id} updated for tenant {tenant_id}")
            return db_group
        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"Integrity error updating variant group {group_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Error updating variant group.",
            )
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Unexpected error updating variant group {group_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def delete_variant_group(self, group_id: uuid.UUID, tenant_id: uuid.UUID) -> bool:
        """Deletes a variant group and all its options."""
        db_group = await self.get_variant_group(group_id, tenant_id)
        if not db_group:
            logger.warning(
                f"Attempted to delete non-existent variant group {group_id} for tenant {tenant_id}"
            )
            return False

        try:
            # Delete options first (or rely on cascade delete in DB)
            await self.db.execute(
                select(VariantOption)
                .where(
                    VariantOption.variant_group_id == group_id,
                    VariantOption.tenant_id == tenant_id,
                )
                .delete()
            )

            # Then delete the group
            await self.db.delete(db_group)
            await self.db.commit()
            logger.info(f"VariantGroup {group_id} deleted for tenant {tenant_id}")
            return True
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Error deleting variant group {group_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    # --- Variant Option Operations ---

    async def create_variant_option(
        self, group_id: uuid.UUID, option_in: VariantOptionCreate, tenant_id: uuid.UUID
    ) -> VariantOption:
        """Creates a new variant option for a variant group."""
        # Ensure variant group exists for tenant
        group = await self.get_variant_group(group_id, tenant_id)
        if not group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"VariantGroup {group_id} not found.",
            )

        try:
            db_option = VariantOption(
                **option_in.model_dump(), variant_group_id=group_id, tenant_id=tenant_id
            )
            self.db.add(db_option)
            await self.db.commit()
            await self.db.refresh(db_option)
            logger.info(
                f"VariantOption created: {db_option.id} for group {group_id}, tenant {tenant_id}"
            )
            return db_option
        except IntegrityError as e:
            await self.db.rollback()
            logger.error(
                f"Error creating variant option for group {group_id}, tenant {tenant_id}: {e}"
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Error creating variant option.",
            )
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Unexpected error creating variant option: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def get_variant_option(
        self, option_id: uuid.UUID, tenant_id: uuid.UUID
    ) -> Optional[VariantOption]:
        """Gets a variant option by ID for the given tenant."""
        stmt = select(VariantOption).where(
            VariantOption.id == option_id, VariantOption.tenant_id == tenant_id
        )
        result = await self.db.execute(stmt)
        return result.scalars().first()

    async def update_variant_option(
        self, option_id: uuid.UUID, option_in: VariantOptionUpdate, tenant_id: uuid.UUID
    ) -> Optional[VariantOption]:
        """Updates a variant option."""
        db_option = await self.get_variant_option(option_id, tenant_id)
        if not db_option:
            return None

        update_data = option_in.model_dump(exclude_unset=True)
        if not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No fields provided for variant option update.",
            )

        try:
            for key, value in update_data.items():
                setattr(db_option, key, value)

            self.db.add(db_option)
            await self.db.commit()
            await self.db.refresh(db_option)
            logger.info(f"VariantOption {option_id} updated for tenant {tenant_id}")
            return db_option
        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"Integrity error updating variant option {option_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Error updating variant option.",
            )
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Unexpected error updating variant option {option_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )

    async def delete_variant_option(self, option_id: uuid.UUID, tenant_id: uuid.UUID) -> bool:
        """Deletes a variant option."""
        db_option = await self.get_variant_option(option_id, tenant_id)
        if not db_option:
            logger.warning(
                f"Attempted to delete non-existent variant option {option_id} for tenant {tenant_id}"  # noqa: E501
            )
            return False

        try:
            await self.db.delete(db_option)
            await self.db.commit()
            logger.info(f"VariantOption {option_id} deleted for tenant {tenant_id}")
            return True
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Error deleting variant option {option_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )
