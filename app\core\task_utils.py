"""
Utilitários para tarefas Celery, especialmente para lidar com código assíncrono.
"""

import asyncio  # noqa: E402
import logging
import functools
from typing import Callable, TypeVar, Awaitable

# Configuração de logging
logger = logging.getLogger(__name__)

# Tipo genérico para funções assíncronas
T = TypeVar("T")


def run_async_task(async_func: Callable[..., Awaitable[T]], *args, **kwargs) -> T:
    """
    Executa uma função assíncrona em um loop de eventos.

    Esta função é útil para executar código assíncrono a partir de tarefas Celery,
    que são síncronas.

    Args:
        async_func: A função assíncrona a ser executada
        *args: Argumentos posicionais para a função
        **kwargs: Argumentos nomeados para a função

    Returns:
        O resultado da função assíncrona

    Raises:
        Exception: Qualquer exceção lançada pela função assíncrona
    """
    try:
        # Tentar usar asyncio.run() primeiro (método recomendado)
        return asyncio.run(async_func(*args, **kwargs))
    except RuntimeError as e:
        # Se asyncio.run() falhar (por exemplo, se já houver um loop em execução),
        # tentar criar um novo loop
        logger.warning(
            f"asyncio.run() falhou com RuntimeError: {e}. Tentando executar em um novo loop."
        )
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(async_func(*args, **kwargs))
        finally:
            loop.close()


def async_to_sync(async_func: Callable[..., Awaitable[T]]) -> Callable[..., T]:
    """
    Decorador para converter uma função assíncrona em síncrona.

    Útil para criar wrappers síncronos para funções assíncronas que serão
    chamadas por tarefas Celery.

    Args:
        async_func: A função assíncrona a ser convertida

    Returns:
        Uma função síncrona que executa a função assíncrona
    """

    @functools.wraps(async_func)
    def wrapper(*args, **kwargs):
        return run_async_task(async_func, *args, **kwargs)

    return wrapper


def create_task_wrapper(async_func: Callable[..., Awaitable[T]]) -> Callable[..., T]:
    """
    Cria um wrapper de tarefa Celery para uma função assíncrona.

    Esta função é útil para criar tarefas Celery que executam código assíncrono.

    Args:
        async_func: A função assíncrona a ser executada pela tarefa

    Returns:
        Uma função síncrona que pode ser usada como tarefa Celery
    """

    @functools.wraps(async_func)
    def task_wrapper(*args, **kwargs):
        logger.info(
            f"Executando tarefa para {async_func.__name__} com args={args}, kwargs={kwargs}"
        )
        try:
            result = run_async_task(async_func, *args, **kwargs)
            logger.info(f"Tarefa {async_func.__name__} concluída com sucesso")
            return result
        except Exception as e:
            logger.error(
                f"Erro durante a execução da tarefa {async_func.__name__}: {e}",
                exc_info=True,
            )
            # Retornar um valor serializável em caso de erro
            return {"success": False, "error": str(e)}

    return task_wrapper
