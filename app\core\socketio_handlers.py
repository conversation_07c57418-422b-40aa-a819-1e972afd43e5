"""
Registro centralizado de handlers para Socket.IO.

Este módulo registra todos os handlers de WebSocket dos diferentes módulos da aplicação.
"""

import logging
from app.core.socketio_manager import sio_server
from app.modules.core.i18n.websockets import register_i18n_handlers
from app.modules.tenants.restaurants.kds.websockets.kds_websockets import register_kds_handlers
from app.modules.core.help_center.websockets.help_center_websockets import register_help_center_handlers

# Configuração de logging
logger = logging.getLogger(__name__)


def register_all_handlers():
    """
    Registra todos os handlers de WebSocket dos diferentes módulos.
    """
    # Registrar handlers do módulo i18n
    register_i18n_handlers(sio_server)

    # Registrar handlers do módulo KDS
    register_kds_handlers(sio_server)

    # Registrar handlers do módulo Help Center
    register_help_center_handlers(sio_server)

    # Adicionar outros registros de handlers aqui

    logger.info("Todos os handlers de WebSocket foram registrados com sucesso")


# Registrar todos os handlers quando este módulo for importado
register_all_handlers()
