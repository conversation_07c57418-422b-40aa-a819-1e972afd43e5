"""
Notification WebSockets

WebSockets para notificações em tempo real.
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Set
from uuid import UUID

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.auth.dependencies.user_auth import get_current_active_user
from app.modules.core.users.models.user import User

logger = logging.getLogger(__name__)

router = APIRouter()


class NotificationWebSocketManager:
    """Gerenciador de WebSockets para notificações."""

    def __init__(self):
        # Conexões ativas: {user_id: {websocket, tenant_subscriptions}}
        self.active_connections: Dict[UUID, Dict] = {}
        
        # Assinantes por tenant: {tenant_id: {user_ids}}
        self.tenant_subscribers: Dict[UUID, Set[UUID]] = {}
        
        # Assinantes globais (admins)
        self.global_subscribers: Set[UUID] = set()

    async def connect(self, websocket: WebSocket, user: User):
        """
        Conecta um usuário via WebSocket.
        
        Args:
            websocket: Conexão WebSocket
            user: Usuário conectado
        """
        await websocket.accept()
        
        self.active_connections[user.id] = {
            "websocket": websocket,
            "user": user,
            "tenant_subscriptions": set(),
            "connected_at": datetime.utcnow()
        }
        
        # Se for admin, adiciona aos assinantes globais
        if user.system_role == "admin":
            self.global_subscribers.add(user.id)
        
        logger.info(f"Usuário conectado via WebSocket: {user.id} ({user.email})")
        
        # Envia mensagem de boas-vindas
        await self.send_personal_message(user.id, {
            "type": "connection_established",
            "message": "Conectado ao sistema de notificações",
            "user_id": str(user.id),
            "timestamp": datetime.utcnow().isoformat()
        })

    def disconnect(self, user_id: UUID):
        """
        Desconecta um usuário.
        
        Args:
            user_id: ID do usuário
        """
        if user_id in self.active_connections:
            # Remove das assinaturas de tenant
            subscriptions = self.active_connections[user_id]["tenant_subscriptions"]
            for tenant_id in subscriptions:
                if tenant_id in self.tenant_subscribers:
                    self.tenant_subscribers[tenant_id].discard(user_id)
                    if not self.tenant_subscribers[tenant_id]:
                        del self.tenant_subscribers[tenant_id]
            
            # Remove dos assinantes globais
            self.global_subscribers.discard(user_id)
            
            # Remove conexão
            del self.active_connections[user_id]
            logger.info(f"Usuário desconectado: {user_id}")

    async def subscribe_to_tenant(self, user_id: UUID, tenant_id: UUID):
        """
        Inscreve usuário para receber notificações de um tenant.
        
        Args:
            user_id: ID do usuário
            tenant_id: ID do tenant
        """
        if user_id not in self.active_connections:
            return

        # Adiciona à assinatura do usuário
        self.active_connections[user_id]["tenant_subscriptions"].add(tenant_id)
        
        # Adiciona aos assinantes do tenant
        if tenant_id not in self.tenant_subscribers:
            self.tenant_subscribers[tenant_id] = set()
        self.tenant_subscribers[tenant_id].add(user_id)
        
        logger.info(f"Usuário {user_id} inscrito no tenant {tenant_id}")
        
        await self.send_personal_message(user_id, {
            "type": "tenant_subscribed",
            "tenant_id": str(tenant_id),
            "message": "Inscrito para receber notificações do tenant",
            "timestamp": datetime.utcnow().isoformat()
        })

    async def unsubscribe_from_tenant(self, user_id: UUID, tenant_id: UUID):
        """
        Remove inscrição de usuário de um tenant.
        
        Args:
            user_id: ID do usuário
            tenant_id: ID do tenant
        """
        if user_id not in self.active_connections:
            return

        # Remove da assinatura do usuário
        self.active_connections[user_id]["tenant_subscriptions"].discard(tenant_id)
        
        # Remove dos assinantes do tenant
        if tenant_id in self.tenant_subscribers:
            self.tenant_subscribers[tenant_id].discard(user_id)
            if not self.tenant_subscribers[tenant_id]:
                del self.tenant_subscribers[tenant_id]
        
        logger.info(f"Usuário {user_id} desinscrito do tenant {tenant_id}")
        
        await self.send_personal_message(user_id, {
            "type": "tenant_unsubscribed",
            "tenant_id": str(tenant_id),
            "message": "Desinscrito das notificações do tenant",
            "timestamp": datetime.utcnow().isoformat()
        })

    async def send_personal_message(self, user_id: UUID, message: dict):
        """
        Envia mensagem para um usuário específico.
        
        Args:
            user_id: ID do usuário
            message: Mensagem a enviar
        """
        if user_id in self.active_connections:
            try:
                websocket = self.active_connections[user_id]["websocket"]
                await websocket.send_text(json.dumps(message, default=str))
            except Exception as e:
                logger.error(f"Erro ao enviar mensagem para {user_id}: {e}")
                self.disconnect(user_id)

    async def broadcast_to_tenant(self, tenant_id: UUID, message: dict):
        """
        Envia mensagem para todos os assinantes de um tenant.
        
        Args:
            tenant_id: ID do tenant
            message: Mensagem a enviar
        """
        if tenant_id not in self.tenant_subscribers:
            return

        subscribers = self.tenant_subscribers[tenant_id].copy()
        for user_id in subscribers:
            await self.send_personal_message(user_id, message)

    async def broadcast_to_all(self, message: dict):
        """
        Envia mensagem para todos os usuários conectados.
        
        Args:
            message: Mensagem a enviar
        """
        all_users = list(self.active_connections.keys())
        for user_id in all_users:
            await self.send_personal_message(user_id, message)

    async def broadcast_to_admins(self, message: dict):
        """
        Envia mensagem apenas para administradores.
        
        Args:
            message: Mensagem a enviar
        """
        admin_users = self.global_subscribers.copy()
        for user_id in admin_users:
            await self.send_personal_message(user_id, message)

    async def notify_new_notification(
        self,
        notification_id: UUID,
        title: str,
        content: str,
        target_type: str,
        target_id: Optional[UUID] = None,
        tenant_id: Optional[UUID] = None,
        priority: str = "normal",
        image_url: Optional[str] = None
    ):
        """
        Notifica sobre nova notificação.
        
        Args:
            notification_id: ID da notificação
            title: Título da notificação
            content: Conteúdo da notificação
            target_type: Tipo de destinatário
            target_id: ID do destinatário específico (se aplicável)
            tenant_id: ID do tenant (se aplicável)
            priority: Prioridade da notificação
            image_url: URL da imagem (se aplicável)
        """
        notification_data = {
            "type": "new_notification",
            "notification": {
                "id": str(notification_id),
                "title": title,
                "content": content,
                "target_type": target_type,
                "target_id": str(target_id) if target_id else None,
                "tenant_id": str(tenant_id) if tenant_id else None,
                "priority": priority,
                "image_url": image_url,
                "timestamp": datetime.utcnow().isoformat()
            }
        }
        
        # Determina destinatários baseado no target_type
        if target_type == "all_users":
            await self.broadcast_to_all(notification_data)
        elif target_type == "specific_user" and target_id:
            await self.send_personal_message(target_id, notification_data)
        elif target_type.startswith("tenant_") and tenant_id:
            await self.broadcast_to_tenant(tenant_id, notification_data)
        
        logger.info(f"Notificação {notification_id} enviada via WebSocket")

    async def notify_notification_read(
        self,
        notification_id: UUID,
        user_id: UUID
    ):
        """
        Notifica que uma notificação foi lida.
        
        Args:
            notification_id: ID da notificação
            user_id: ID do usuário que leu
        """
        message = {
            "type": "notification_read",
            "notification_id": str(notification_id),
            "user_id": str(user_id),
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Notifica apenas admins sobre leituras
        await self.broadcast_to_admins(message)

    async def notify_notification_deleted(
        self,
        notification_id: UUID,
        deleted_by: UUID,
        deleted_for_all: bool = False
    ):
        """
        Notifica que uma notificação foi deletada.
        
        Args:
            notification_id: ID da notificação
            deleted_by: ID do usuário que deletou
            deleted_for_all: Se foi deletada para todos
        """
        message = {
            "type": "notification_deleted",
            "notification_id": str(notification_id),
            "deleted_by": str(deleted_by),
            "deleted_for_all": deleted_for_all,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        if deleted_for_all:
            # Se deletada para todos, notifica todos os usuários
            await self.broadcast_to_all(message)
        else:
            # Se deletada apenas para o usuário, notifica apenas ele
            await self.send_personal_message(deleted_by, message)

    def get_connection_stats(self) -> dict:
        """
        Obtém estatísticas das conexões.
        
        Returns:
            dict: Estatísticas das conexões
        """
        total_connections = len(self.active_connections)
        admin_connections = len(self.global_subscribers)
        tenant_subscriptions = sum(
            len(subscribers) for subscribers in self.tenant_subscribers.values()
        )
        
        return {
            "total_connections": total_connections,
            "admin_connections": admin_connections,
            "tenant_subscriptions": tenant_subscriptions,
            "active_tenants": len(self.tenant_subscribers),
            "connection_details": [
                {
                    "user_id": str(user_id),
                    "email": conn["user"].email,
                    "role": conn["user"].system_role,
                    "connected_at": conn["connected_at"].isoformat(),
                    "tenant_subscriptions": len(conn["tenant_subscriptions"])
                }
                for user_id, conn in self.active_connections.items()
            ]
        }


# Instância global do gerenciador
notification_ws_manager = NotificationWebSocketManager()


@router.websocket("/ws/notifications")
async def websocket_endpoint(
    websocket: WebSocket,
    db: AsyncSession = Depends(get_db)
):
    """
    Endpoint WebSocket para notificações.

    Args:
        websocket: Conexão WebSocket
        db: Sessão do banco de dados
    """
    # TODO: Implementar autenticação via token no WebSocket
    # Por enquanto, aceita conexão sem autenticação
    await websocket.accept()

    # Simula usuário para teste
    user = None
    
    try:
        while True:
            # Recebe mensagens do cliente
            data = await websocket.receive_text()
            message = json.loads(data)

            message_type = message.get("type")

            if message_type == "ping":
                await websocket.send_text(json.dumps({
                    "type": "pong",
                    "message": "WebSocket ativo",
                    "timestamp": datetime.utcnow().isoformat()
                }))
            else:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "WebSocket em desenvolvimento - autenticação necessária",
                    "timestamp": datetime.utcnow().isoformat()
                }))

    except WebSocketDisconnect:
        logger.info("WebSocket desconectado")
    except Exception as e:
        logger.error(f"Erro no WebSocket: {e}")
        await websocket.close()
