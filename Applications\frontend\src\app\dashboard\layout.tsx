'use client';

import { useAuth } from '@/lib/auth/AuthProvider';
import { useTenant } from '@/lib/tenant/TenantProvider';
import { DashboardSidebar } from '@/components/dashboard/DashboardSidebar';
import { DashboardHeader } from '@/components/dashboard/DashboardHeader';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { AdminViewHeader } from '@/components/admin/AdminViewHeader';
import { useState, Suspense } from 'react';
import { usePathname } from 'next/navigation';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isLoading, isAuthenticated } = useAuth();
  const { currentTenant, isMultiTenant } = useTenant();
  const pathname = usePathname();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // DEBUG LOGS
  console.log('[DashboardLayout] isLoading:', isLoading);
  console.log('[DashboardLayout] isAuthenticated:', isAuthenticated);
  console.log('[DashboardLayout] currentTenant (from TenantProvider):', currentTenant);


  // Rotas do KDS e POS que devem usar layout próprio
  const fullscreenRoutes = [
    '/dashboard/restaurant/kds',
    '/dashboard/restaurant/pos'
  ];

  const isFullscreenRoute = fullscreenRoutes.some(route => pathname.startsWith(route));

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Middleware will redirect to login
  }

  // Se for rota do KDS ou POS, renderizar apenas o children (que terá seu próprio layout)
  if (isFullscreenRoute) {
    return <>{children}</>;
  }

  return (
    <div className="dashboard-bg">
      <Suspense fallback={null}>
        <DashboardSidebar
          collapsed={sidebarCollapsed}
          onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
        />
      </Suspense>

      <div className={`transition-all duration-500 ${sidebarCollapsed ? 'lg:pl-16' : 'lg:pl-72'}`}>
        <Suspense fallback={null}>
          <AdminViewHeader />
        </Suspense>
        <DashboardHeader
          sidebarCollapsed={sidebarCollapsed}
          onToggleSidebar={() => setSidebarCollapsed(!sidebarCollapsed)}
        />

        <main className="py-6 px-6">
          <div className="w-full animate-fade-in">
            {/* Show tenant selection for multi-tenant users */}
            {isMultiTenant && !currentTenant && (
              <div className="mb-6 glass rounded-xl p-6 border-l-4 border-yellow-400 max-w-4xl">
                <div className="flex">
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-yellow-800">
                      Select a tenant to continue
                    </h3>
                    <p className="mt-1 text-sm text-yellow-700">
                      You have access to multiple tenants. Please select one to view the dashboard.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
