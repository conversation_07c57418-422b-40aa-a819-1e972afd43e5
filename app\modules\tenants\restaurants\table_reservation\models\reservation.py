import uuid
import enum
from typing import TYPE_CHECKING, List, Optional
from datetime import datetime, timedelta
from sqlalchemy import (
    <PERSON>umn,
    String,
    <PERSON><PERSON>ey,
    Integer,
    Enum,
    JSON,
    Boolean,
    DateTime,
    Text,
    Numeric,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import Base

# Use TYPE_CHECKING to handle circular dependencies for type hinting
if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.core.users.models.user import User
    from app.modules.shared.crm.models.account import Account
    from app.modules.tenants.restaurants.table_management.models.table import Table


class ReservationStatus(str, enum.Enum):
    """Enum for reservation status."""

    PENDING = "pending"
    CONFIRMED = "confirmed"
    SEATED = "seated"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    NO_SHOW = "no_show"


class BlacklistType(str, enum.Enum):
    """Enum for blacklist types."""

    BANNED = "banned"  # Permanently banned
    SUSPENDED = "suspended"  # Temporarily suspended


class Reservation(Base):
    """Reservation model for restaurant table reservations."""

    __tablename__ = "restaurant_reservations"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    table_id = Column(
        UUID(as_uuid=True),
        ForeignKey("restaurant_tables.id"),
        nullable=True,  # Can be null for waitlist or unassigned reservations
        index=True,
    )

    # Customer information
    customer_id = Column(
        UUID(as_uuid=True),
        ForeignKey("crm_accounts.id"),
        nullable=True,  # Can be null for non-registered customers
        index=True,
    )

    # Guest information (for non-registered customers)
    guest_name = Column(String, nullable=True)
    guest_email = Column(String, nullable=True)
    guest_phone = Column(String, nullable=True)

    # Reservation details
    reservation_number = Column(String, nullable=False, unique=True)
    party_size = Column(Integer, nullable=False)
    reservation_date = Column(DateTime(timezone=True), nullable=False, index=True)
    end_time = Column(DateTime(timezone=True), nullable=True)  # Optional end time
    duration_minutes = Column(Integer, nullable=True, default=90)  # Default 90 minutes
    status = Column(
        Enum(ReservationStatus), default=ReservationStatus.PENDING, nullable=False, index=True
    )

    # Additional information
    special_requests = Column(Text, nullable=True)
    occasion = Column(String, nullable=True)
    notes = Column(Text, nullable=True)

    # Deposit information
    deposit_required = Column(Boolean, default=False, nullable=False)
    deposit_amount = Column(Numeric(10, 2), nullable=True)
    deposit_paid = Column(Boolean, default=False, nullable=False)
    payment_id = Column(String, nullable=True)  # Reference to payment

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )

    # Relationships
    tenant = relationship(
        "app.modules.core.tenants.models.tenant.Tenant", back_populates="reservations"
    )
    table = relationship(
        "app.modules.tenants.restaurants.table_management.models.table.Table",
        back_populates="reservations",
    )
    customer = relationship("app.modules.shared.crm.models.account.Account", viewonly=True)

    def __repr__(self):
        return f"<Reservation(id={self.id}, tenant_id={self.tenant_id}, status={self.status})>"


class CustomerBlacklist(Base):
    """Customer blacklist model for restaurant reservations."""

    __tablename__ = "restaurant_customer_blacklist"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)

    # Customer information
    customer_id = Column(
        UUID(as_uuid=True),
        ForeignKey("crm_accounts.id"),
        nullable=True,  # Can be null for non-registered customers
        index=True,
    )

    # Guest information (for non-registered customers)
    guest_name = Column(String, nullable=True)
    guest_email = Column(String, nullable=True)
    guest_phone = Column(String, nullable=True)

    # Blacklist details
    blacklist_type = Column(Enum(BlacklistType), default=BlacklistType.SUSPENDED, nullable=False)
    reason = Column(Text, nullable=True)

    # For suspended customers
    suspension_start_date = Column(DateTime(timezone=True), nullable=True)
    suspension_end_date = Column(DateTime(timezone=True), nullable=True)

    # Deposit requirements after suspension
    require_deposit = Column(Boolean, default=False, nullable=False)
    deposit_amount = Column(Numeric(10, 2), nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )

    # Relationships
    tenant = relationship("app.modules.core.tenants.models.tenant.Tenant", viewonly=True)
    customer = relationship("app.modules.shared.crm.models.account.Account", viewonly=True)

    def __repr__(self):
        return f"<CustomerBlacklist(id={self.id}, tenant_id={self.tenant_id}, type={self.blacklist_type})>"

    @property
    def is_active(self):
        """Check if the blacklist entry is currently active."""
        if self.blacklist_type == BlacklistType.BANNED:
            return True

        if self.blacklist_type == BlacklistType.SUSPENDED:
            now = datetime.now()
            if self.suspension_start_date and self.suspension_end_date:
                return self.suspension_start_date <= now <= self.suspension_end_date

        return False
