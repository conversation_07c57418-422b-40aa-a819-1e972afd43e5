'use client';

import React, { useState } from 'react';
import LayoutCanvas from './LayoutCanvas';
import LayoutToolbar from './LayoutToolbar';
import ZoomControls from './ZoomControls';
import TablePropertiesPanel from './TablePropertiesPanel';
import { Table, TableStatus } from '@/types/pos';
import { 
  DEFAULT_GRID_CONFIG, 
  ZOOM_LEVELS, 
  CANVAS_DIMENSIONS,
  GridConfig 
} from '@/utils/table/GridPatterns';

// Mock data for demonstration
const mockTables: Table[] = [
  {
    id: 'table-1',
    tenant_id: 'demo-tenant',
    table_number: '1',
    name: 'Mesa Principal',
    capacity: 4,
    zone: 'Zona 1',
    status: 'available' as TableStatus,
    position_x: 100,
    position_y: 100,
    width: 120,
    height: 80,
    is_active: true,
    qrcode_enabled: true,
    number: '1',
    shape: 'rectangle'
  },
  {
    id: 'table-2',
    tenant_id: 'demo-tenant',
    table_number: '2',
    name: 'Mesa VIP',
    capacity: 6,
    zone: 'Zona 1',
    status: 'occupied' as TableStatus,
    position_x: 300,
    position_y: 150,
    width: 140,
    height: 100,
    is_active: true,
    qrcode_enabled: true,
    number: '2',
    shape: 'rectangle'
  },
  {
    id: 'table-3',
    tenant_id: 'demo-tenant',
    table_number: '3',
    name: 'Mesa Redonda',
    capacity: 8,
    zone: 'Zona da Terraça',
    status: 'reserved' as TableStatus,
    position_x: 500,
    position_y: 200,
    width: 120,
    height: 120,
    is_active: true,
    qrcode_enabled: true,
    number: '3',
    shape: 'circle'
  }
];

const zones = ['Zona 1', 'Zona da Terraça'];

export default function IntegratedDemo() {
  const [tables, setTables] = useState<Table[]>(mockTables);
  const [selectedTable, setSelectedTable] = useState<Table | null>(null);
  const [viewMode, setViewMode] = useState<'list' | 'visual'>('visual');
  const [zoom, setZoom] = useState<number>(ZOOM_LEVELS.default);
  const [gridConfig, setGridConfig] = useState<GridConfig>(DEFAULT_GRID_CONFIG);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showPropertiesPanel, setShowPropertiesPanel] = useState(false);

  const handleTableUpdate = (updatedTable: Table) => {
    setTables(prevTables => 
      prevTables.map(table => 
        table.id === updatedTable.id ? updatedTable : table
      )
    );
    setHasUnsavedChanges(true);
  };

  const handleTableCreate = () => {
    const newTable: Table = {
      id: `table-${Date.now()}`,
      tenant_id: 'demo-tenant',
      table_number: `${tables.length + 1}`,
      name: `Nova Mesa ${tables.length + 1}`,
      capacity: 4,
      zone: 'Zona 1',
      status: 'available' as TableStatus,
      position_x: 200,
      position_y: 200,
      width: 120,
      height: 80,
      is_active: true,
      qrcode_enabled: true,
      number: `${tables.length + 1}`,
      shape: 'rectangle'
    };

    setTables(prevTables => [...prevTables, newTable]);
    setSelectedTable(newTable);
    setShowPropertiesPanel(true);
    setHasUnsavedChanges(true);
  };

  const handleTableSelect = (table: Table | null) => {
    setSelectedTable(table);
    setShowPropertiesPanel(!!table);
  };

  const handleTableEdit = (table: Table) => {
    setSelectedTable(table);
    setShowPropertiesPanel(true);
  };

  const handleTableDelete = (table: Table) => {
    if (confirm(`Tem certeza que deseja deletar a ${table.name}?`)) {
      setTables(prevTables => prevTables.filter(t => t.id !== table.id));
      if (selectedTable?.id === table.id) {
        setSelectedTable(null);
        setShowPropertiesPanel(false);
      }
      setHasUnsavedChanges(true);
    }
  };

  const handleSaveChanges = async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setHasUnsavedChanges(false);
      console.log('Changes saved successfully');
    } catch (error) {
      console.error('Error saving changes:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGridConfigChange = (config: Partial<GridConfig>) => {
    setGridConfig(prev => ({ ...prev, ...config }));
  };

  const handleZoomReset = () => {
    setZoom(ZOOM_LEVELS.default);
  };

  const handlePropertiesSave = (updatedTable: Table) => {
    handleTableUpdate(updatedTable);
    setShowPropertiesPanel(false);
  };

  const handlePropertiesClose = () => {
    setShowPropertiesPanel(false);
    setSelectedTable(null);
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Demo: Sistema Integrado de Controles
        </h2>
        <p className="text-gray-600">
          Demonstração completa com toolbar, zoom controls e painel de propriedades
        </p>
      </div>

      {/* Layout Toolbar */}
      <div className="mb-6">
        <LayoutToolbar
          viewMode={viewMode}
          onViewModeChange={setViewMode}
          zoom={zoom}
          onZoomChange={setZoom}
          onZoomReset={handleZoomReset}
          gridConfig={gridConfig}
          onGridConfigChange={handleGridConfigChange}
          onCreateTable={handleTableCreate}
          onSaveChanges={handleSaveChanges}
          hasUnsavedChanges={hasUnsavedChanges}
          isLoading={isLoading}
        />
      </div>

      {/* Main Content */}
      <div className="flex gap-6">
        
        {/* Canvas Area */}
        <div className="flex-1">
          {viewMode === 'visual' ? (
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <div className="mb-4 flex items-center justify-between">
                <h3 className="text-lg font-semibold">Layout Visual das Mesas</h3>
                <div className="text-sm text-gray-600">
                  {tables.length} mesa(s) total
                </div>
              </div>
              
              <div className="border border-gray-300 rounded-lg overflow-auto max-h-[600px]">
                <LayoutCanvas
                  tables={tables}
                  selectedTable={selectedTable}
                  gridConfig={gridConfig}
                  zoom={zoom}
                  canvasWidth={CANVAS_DIMENSIONS.defaultWidth}
                  canvasHeight={CANVAS_DIMENSIONS.defaultHeight}
                  onTableUpdate={handleTableUpdate}
                  onTableSelect={handleTableSelect}
                  onTableEdit={handleTableEdit}
                  onTableDelete={handleTableDelete}
                  onZoomChange={setZoom}
                />
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold mb-4">Modo Lista</h3>
              <div className="space-y-3">
                {tables.map(table => (
                  <div key={table.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                    <div>
                      <div className="font-medium">{table.name}</div>
                      <div className="text-sm text-gray-600">
                        {table.capacity} pessoas • {table.zone}
                      </div>
                    </div>
                    <div className="text-sm text-gray-500">
                      {table.status}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="w-80 space-y-6">
          
          {/* Zoom Controls */}
          {viewMode === 'visual' && (
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <h4 className="text-sm font-semibold text-gray-900 mb-3">Controles de Zoom</h4>
              <ZoomControls
                zoom={zoom}
                onZoomChange={setZoom}
                contentWidth={CANVAS_DIMENSIONS.defaultWidth}
                contentHeight={CANVAS_DIMENSIONS.defaultHeight}
                containerWidth={800}
                containerHeight={600}
                showSlider={true}
                showPercentage={true}
                showFitButton={true}
                orientation="horizontal"
              />
            </div>
          )}

          {/* Selected Table Info */}
          {selectedTable && (
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <h4 className="text-sm font-semibold text-gray-900 mb-3">Mesa Selecionada</h4>
              <div className="space-y-2 text-sm">
                <div><span className="font-medium">Nome:</span> {selectedTable.name}</div>
                <div><span className="font-medium">Capacidade:</span> {selectedTable.capacity} pessoas</div>
                <div><span className="font-medium">Zona:</span> {selectedTable.zone}</div>
                <div><span className="font-medium">Status:</span> {selectedTable.status}</div>
                <div><span className="font-medium">Posição:</span> ({selectedTable.position_x}, {selectedTable.position_y})</div>
              </div>
              <button
                onClick={() => setShowPropertiesPanel(true)}
                className="mt-3 w-full px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Editar Propriedades
              </button>
            </div>
          )}

          {/* Debug Info */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h4 className="text-sm font-semibold text-gray-900 mb-3">Debug Info</h4>
            <div className="space-y-1 text-xs text-gray-600">
              <div>Modo: {viewMode}</div>
              <div>Zoom: {Math.round(zoom * 100)}%</div>
              <div>Grid: {gridConfig.showGrid ? 'Visível' : 'Oculto'}</div>
              <div>Snap: {gridConfig.snapToGrid ? 'Ativo' : 'Inativo'}</div>
              <div>Alterações: {hasUnsavedChanges ? 'Sim' : 'Não'}</div>
              <div>Loading: {isLoading ? 'Sim' : 'Não'}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Table Properties Panel */}
      {showPropertiesPanel && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="w-full max-w-2xl mx-4">
            <TablePropertiesPanel
              table={selectedTable}
              zones={zones}
              isOpen={showPropertiesPanel}
              onClose={handlePropertiesClose}
              onSave={handlePropertiesSave}
              onDelete={handleTableDelete}
            />
          </div>
        </div>
      )}
    </div>
  );
}
