import uuid
import enum
from datetime import datetime
from sqlalchemy import (
    <PERSON>um<PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>olean,
    DateTime,
    Time,
    Date,
    Integer,
    Enum,
    Text,
    Float,
)
from sqlalchemy.dialects.postgresql import UUID, JSONB, ARRAY  # noqa: E402
from sqlalchemy.orm import relationship

from app.db.base import Base  # noqa: E402
from app.core.tenant_mixin import TenantMixin


class Day(str, enum.Enum):
    """Days of the week."""

    MONDAY = "monday"
    TUESDAY = "tuesday"
    WEDNESDAY = "wednesday"
    THURSDAY = "thursday"
    FRIDAY = "friday"
    SATURDAY = "saturday"
    SUNDAY = "sunday"


class ScheduleType(str, enum.Enum):
    """Schedule type enum."""

    REGULAR = "regular"
    SHIFT = "shift"
    CUSTOM = "custom"
    ROTATION = "rotation"


class ScheduleRecurrence(str, enum.Enum):
    """Schedule recurrence enum."""

    DAILY = "daily"
    WEEKLY = "weekly"
    BIWEEKLY = "biweekly"
    MONTHLY = "monthly"
    CUSTOM = "custom"


class ScheduleTemplate(Base, TenantMixin):
    """Schedule template model.

    This represents a template for recurring schedules (e.g., 9-5, M-F).
    """

    __tablename__ = "hr_schedule_templates"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Template details
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    schedule_type = Column(Enum(ScheduleType), nullable=False, default=ScheduleType.REGULAR)
    recurrence = Column(Enum(ScheduleRecurrence), nullable=False, default=ScheduleRecurrence.WEEKLY)

    # For daily schedules
    default_start_time = Column(Time, nullable=True)
    default_end_time = Column(Time, nullable=True)

    # Active days (for weekly schedules)
    active_days = Column(ARRAY(String), nullable=True)

    # Is this template active
    is_active = Column(Boolean, default=True)

    # Relationships
    schedule_entries = relationship(
        "ScheduleTemplateEntry", back_populates="template", cascade="all, delete-orphan"
    )
    work_schedules = relationship("WorkSchedule", back_populates="template")


class ScheduleTemplateEntry(Base, TenantMixin):
    """Schedule template entry model.

    This represents a single entry in a schedule template (e.g., Monday 9-5).
    """

    __tablename__ = "hr_schedule_template_entries"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    template_id = Column(UUID(as_uuid=True), ForeignKey("hr_schedule_templates.id"), nullable=False)

    # Entry details
    day_of_week = Column(Enum(Day), nullable=False)
    start_time = Column(Time, nullable=False)
    end_time = Column(Time, nullable=False)
    break_duration_minutes = Column(Integer, default=0)

    # Additional settings
    is_day_off = Column(Boolean, default=False)

    # Relationship
    template = relationship("ScheduleTemplate", back_populates="schedule_entries")


class WorkSchedule(Base, TenantMixin):
    """Work schedule model.

    This represents an assigned schedule for an employee.
    """

    __tablename__ = "hr_work_schedules"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    employee_id = Column(UUID(as_uuid=True), ForeignKey("hr_employees.id"), nullable=False)
    template_id = Column(UUID(as_uuid=True), ForeignKey("hr_schedule_templates.id"), nullable=True)

    # Schedule details
    name = Column(String, nullable=True)
    start_date = Column(Date, nullable=False)
    end_date = Column(Date, nullable=True)  # Null means indefinite

    # Status
    is_active = Column(Boolean, default=True)

    # Custom schedule metadata
    metadata = Column(JSONB, default={})

    # Approval
    is_approved = Column(Boolean, default=False)
    approved_by = Column(UUID(as_uuid=True), nullable=True)
    approval_date = Column(DateTime, nullable=True)

    # Relationships
    employee = relationship("Employee", back_populates="schedules")
    template = relationship("ScheduleTemplate", back_populates="work_schedules")
    schedule_shifts = relationship(
        "WorkShift", back_populates="schedule", cascade="all, delete-orphan"
    )


class WorkShift(Base, TenantMixin):
    """Work shift model.

    This represents a single shift in a work schedule.
    """

    __tablename__ = "hr_work_shifts"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    schedule_id = Column(UUID(as_uuid=True), ForeignKey("hr_work_schedules.id"), nullable=False)

    # Shift details
    date = Column(Date, nullable=False)
    start_time = Column(Time, nullable=False)
    end_time = Column(Time, nullable=False)
    break_duration_minutes = Column(Integer, default=0)

    # Status
    is_completed = Column(Boolean, default=False)
    is_modified = Column(Boolean, default=False)  # Was this modified from the template

    # Notes
    notes = Column(Text, nullable=True)

    # Relationships
    schedule = relationship("WorkSchedule", back_populates="schedule_shifts")


class LeaveType(str, enum.Enum):
    """Leave type enum."""

    VACATION = "vacation"
    SICK = "sick"
    PERSONAL = "personal"
    BEREAVEMENT = "bereavement"
    MATERNITY = "maternity"
    PATERNITY = "paternity"
    UNPAID = "unpaid"
    OTHER = "other"


class LeaveRequestStatus(str, enum.Enum):
    """Leave request status enum."""

    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    CANCELLED = "cancelled"


class LeaveRequest(Base, TenantMixin):
    """Leave request model.

    This represents a request for time off.
    """

    __tablename__ = "hr_leave_requests"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    employee_id = Column(UUID(as_uuid=True), ForeignKey("hr_employees.id"), nullable=False)

    # Leave details
    leave_type = Column(Enum(LeaveType), nullable=False)
    start_date = Column(Date, nullable=False)
    end_date = Column(Date, nullable=False)
    duration_days = Column(Float, nullable=False)  # Can be fractional for partial days

    # Request details
    request_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    reason = Column(Text, nullable=True)

    # Status
    status = Column(Enum(LeaveRequestStatus), nullable=False, default=LeaveRequestStatus.PENDING)

    # Approval
    reviewed_by = Column(UUID(as_uuid=True), nullable=True)
    review_date = Column(DateTime, nullable=True)
    review_notes = Column(Text, nullable=True)

    # Relationships
    employee = relationship("Employee", back_populates="leave_requests")
