import logging
import uuid
from typing import List, Optional, Annotated, Any
from decimal import Decimal

from fastapi import (
    APIRouter,
    Depends,
    HTTPException,
    status,
    Query,
    Path,
    Request,
)
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession

# Set up logger
logger = logging.getLogger(__name__)

# Import models and dependencies
from app.modules.core.users.models.user import User
from app.modules.core.auth.dependencies.auth_dependencies import get_current_active_user, get_current_admin_user
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    get_current_tenant_from_header,
    require_tenant_role,
    get_optional_tenant_from_header,
    get_tenant_id,
)
from app.modules.core.tenants.models.tenant import Tenant
from app.modules.core.roles.models.roles import RolePermissions, SystemRole, TenantRole
from app.modules.core.users.models.tenant_user_association import TenantUserAssociation

# Import eshop services and schemas
from app.modules.core.eshop.services.product_service import ProductService
from app.modules.core.eshop.models.product import (
    ApprovalStatus,
    # MarketType importado de app.core.enums
    ProductStatus,
)
from app.modules.core.eshop.schemas.product import (
    ProductCreate,
    ProductUpdate,
    ProductResponse,
    ProductListResponse,
    ProductDetailResponse,
)
from app.core.dependencies import get_db
from app.core.enums import MarketType
from app.modules.core.eshop.schemas.eshop_product import (
    eshopProductCreate,
    eshopProductRead,
    eshopProductUpdate,
)

# Custom dependency for write access (Admin or TVendorSupplier)
# This will just get the user, the permission check will be done in the endpoint
async def get_writer_user(current_user: User = Depends(get_current_active_user)):
    return current_user


# Dependency to get product service
async def get_product_service(db: AsyncSession = Depends(get_db)) -> ProductService:
    return ProductService(db)

# Router setup
router = APIRouter(
    prefix="/products",
    tags=["eshop - Products"],
)


@router.post("/", response_model=eshopProductRead, status_code=status.HTTP_201_CREATED)
async def create_product(
    product_in: eshopProductCreate,
    service: ProductService = Depends(get_product_service),
    current_user: User = Depends(get_writer_user),
    tenant: Optional[Tenant] = Depends(get_optional_tenant_from_header),
):
    """
    Creates a new product.

    Only users with an **Admin** role or a **TVendorSupplier** association can create products.
    The `vendor_id` is automatically set to the current user's ID.
    If a tenant is present in the header, the product is associated with that tenant.
    """
    # Permission Check
    is_admin = current_user.system_role == SystemRole.ADMIN
    is_tvendor = any(
        assoc.role == TenantRole.TVENDOR
        for assoc in current_user.tenant_associations
    )
    if not (is_admin or is_tvendor):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User does not have permission to create or modify products."
        )

    tenant_id = tenant.id if tenant else None
    return await service.create_product(
        product_in=product_in, 
        vendor_id=current_user.id, 
        tenant_id=tenant_id
    )


@router.get(
    "/",
    response_model=List[eshopProductRead],
    dependencies=[
        Depends(require_tenant_role(required_roles=["owner", "admin", "staff"]))
    ],
)
async def list_products(
    request: Request,
    db: AsyncSession = Depends(get_db),
    product_service: ProductService = Depends(get_product_service),
    tenant_id: uuid.UUID = Depends(get_tenant_id),
    market_type: Optional[str] = None,  # Changed to string to handle case-insensitivity
    category_slug: Optional[str] = None,
    status: Optional[ApprovalStatus] = ApprovalStatus.APPROVED,
    min_price: Optional[float] = None,
    max_price: Optional[float] = None,
    search: Optional[str] = None,
    page: int = Query(1, ge=1),
    page_size: int = Query(100, ge=1, le=200),
):
    """
    List products for the current tenant, with optional filters.
    Requires 'owner', 'admin', or 'staff' role.
    """
    market_type_enum: Optional[MarketType] = None
    if market_type:
        try:
            # Convert to upper-case to match the Enum definition
            market_type_enum = MarketType(market_type.upper())
        except ValueError:
            # Silently ignore invalid market_type values
            pass

    # Determine if the request is for the public-facing store
    # This is a simplified check; you might need a more robust way to determine this
    is_public_request = "authorization" not in request.headers

    products = await product_service.get_products(
        tenant_id=tenant_id,
        market_type=market_type_enum,  # Pass the validated enum member
        category_slug=category_slug,
        status=status,
        min_price=min_price,
        max_price=max_price,
        search=search,
        is_public_request=is_public_request,
        page=page,
        page_size=page_size,
    )

    return products


@router.get("/{product_id}", response_model=eshopProductRead)
async def get_product(product_id: uuid.UUID, db: AsyncSession = Depends(get_db)):
    service = ProductService(db)
    product = await service.get_product(product_id)
    if not product:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Product not found")
    return product


@router.put("/{product_id}", response_model=eshopProductRead)
async def update_product(
    product_id: uuid.UUID,
    product_in: eshopProductUpdate,
    service: ProductService = Depends(get_product_service),
    current_user: User = Depends(get_writer_user),
):
    """
    Updates a product.

    Only the product's original vendor or an Admin can update it.
    This check is performed within the service layer.
    """
    product = await service.update_product(
        product_id=product_id, 
        product_in=product_in, 
        current_user_id=current_user.id,
        is_admin=current_user.system_role == SystemRole.ADMIN,
    )
    if not product:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Product not found")
    return product


@router.delete("/{product_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_product(
    product_id: uuid.UUID,
    service: ProductService = Depends(get_product_service),
    current_user: User = Depends(get_writer_user),
):
    """
    Deletes a product.

    Only the product's original vendor or an Admin can delete it.
    """
    success = await service.delete_product(
        product_id=product_id,
        current_user_id=current_user.id,
        is_admin=current_user.system_role == SystemRole.ADMIN,
    )
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Product not found or permission denied")
    return

@router.patch("/{product_id}/stock", response_model=ProductResponse)
async def update_product_stock(
    product_id: Annotated[uuid.UUID, Path(..., description="The ID of the product to update stock")],
    current_user: Annotated[User, Depends(get_current_active_user)],
    current_tenant: Annotated[Optional[Tenant], Depends(get_optional_tenant_from_header)],
    product_service: Annotated[ProductService, Depends(get_product_service)],
    _: Annotated[Any, Depends(get_writer_user)],
    quantity_change: int = Query(..., description="Quantity change (positive or negative)"),
):
    """
    Update product stock quantity.
    Only the product owner (vendor) or admin can update stock.
    """
    try:
        tenant_id = current_tenant.id if current_tenant else None
        
        updated_product = await product_service.update_stock(
            product_id=product_id,
            quantity_change=quantity_change,
            current_user_id=current_user.id,
            tenant_id=tenant_id
        )
        
        if updated_product is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Product not found",
            )

        return ProductResponse.model_validate(updated_product)
    
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception(f"Unexpected error updating stock for product {product_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while updating product stock",
        )
