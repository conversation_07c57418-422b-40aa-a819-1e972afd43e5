import logging
from typing import Optional, Sequence, List
from sqlalchemy import select, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.exc import IntegrityError
from fastapi import HTTPException, status
import uuid

# Import models
from app.modules.core.eshop.models.product_category import ProductCategory
from app.modules.core.eshop.models.product import Product
from app.core.enums import MarketType

# Import schemas
from app.modules.core.eshop.schemas.product_category import (
    ProductCategoryCreate,
    ProductCategoryUpdate,
    ProductCategoryResponse,
    ProductCategoryTreeResponse,
)

# Import WebSocket utility
from app.websockets.manager import emit_to_tenant

logger = logging.getLogger(__name__)


class ProductCategoryService:
    def __init__(self, db_session: AsyncSession):
        self.db = db_session

    async def create_category(
        self, 
        category_in: ProductCategoryCreate,
        current_user_id: uuid.UUID
    ) -> ProductCategory:
        """Creates a new product category."""
        logger.info(f"Creating product category: {category_in.name}")

        # Validate parent category if specified
        if category_in.parent_id:
            parent = await self.get_category(category_in.parent_id, category_in.tenant_id)
            if not parent:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Parent category with id {category_in.parent_id} not found.",
                )

        try:
            category_data = category_in.model_dump()
            db_category = ProductCategory(**category_data)
            self.db.add(db_category)
            await self.db.commit()
            
            # Re-fetch the category with relationships loaded to avoid lazy-loading issues
            created_category = await self.get_category(
                db_category.id, 
                tenant_id=category_in.tenant_id,
                include_children=True
            )
            if not created_category:
                # This should not happen, but as a safeguard
                raise HTTPException(status_code=404, detail="Failed to retrieve created category.")

            # Emit WebSocket notification
            if category_in.tenant_id:
                await emit_to_tenant(
                    category_in.tenant_id,
                    "product_category_created",
                    {"category_id": str(created_category.id), "category_name": created_category.name}
                )

            logger.info(f"Product category created: {created_category.id}")
            return created_category

        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"IntegrityError creating category: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Error creating category. Database constraint violation: {str(e)}",
            )
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Unexpected error creating category: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal server error: {str(e)}",
            )

    async def get_category(
        self, 
        category_id: uuid.UUID, 
        tenant_id: Optional[uuid.UUID] = None,
        include_children: bool = False
    ) -> Optional[ProductCategory]:
        """Gets a specific product category by ID."""
        query = select(ProductCategory).where(ProductCategory.id == category_id)
        
        if tenant_id:
            query = query.where(
                or_(ProductCategory.tenant_id == tenant_id, ProductCategory.tenant_id.is_(None))
            )

        if include_children:
            query = query.options(selectinload(ProductCategory.children))

        result = await self.db.execute(query)
        category = result.scalars().first()

        if not category:
            logger.warning(f"Category {category_id} not found")
            return None

        return category

    async def get_categories(
        self,
        tenant_id: Optional[uuid.UUID] = None,
        parent_id: Optional[uuid.UUID] = None,
        is_active: Optional[bool] = None,
        is_featured: Optional[bool] = None,
        include_children: bool = False,
        skip: int = 0,
        limit: int = 100,
    ) -> Sequence[ProductCategory]:
        """Gets a list of product categories with filtering options."""
        query = select(ProductCategory)

        # Apply filters
        filters = []
        
        if tenant_id:
            filters.append(
                or_(ProductCategory.tenant_id == tenant_id, ProductCategory.tenant_id.is_(None))
            )
        
        if parent_id is not None:
            filters.append(ProductCategory.parent_id == parent_id)
        else:
            # If no parent_id specified, get root categories (parent_id is None)
            filters.append(ProductCategory.parent_id.is_(None))
            
        if is_active is not None:
            filters.append(ProductCategory.is_active == is_active)
            
        if is_featured is not None:
            filters.append(ProductCategory.is_featured == is_featured)

        if filters:
            query = query.where(and_(*filters))

        # Apply ordering and pagination
        query = query.order_by(ProductCategory.display_order, ProductCategory.name)
        query = query.offset(skip).limit(limit)

        # Load children if requested
        if include_children:
            query = query.options(selectinload(ProductCategory.children))

        result = await self.db.execute(query)
        categories = result.scalars().all()

        return categories

    async def get_category_tree(
        self, 
        tenant_id: Optional[uuid.UUID] = None,
        is_active: Optional[bool] = None,
        market_type: Optional[MarketType] = None
    ) -> List[ProductCategory]:
        """Gets the complete category tree structure."""
        # Get all categories for the tenant
        query = select(ProductCategory)
        
        filters = []
        if tenant_id:
            filters.append(
                or_(ProductCategory.tenant_id == tenant_id, ProductCategory.tenant_id.is_(None))
            )
        if is_active is not None:
            filters.append(ProductCategory.is_active == is_active)
        if market_type and market_type != MarketType.ALL:
            filters.append(ProductCategory.market_type == market_type)
            
        if filters:
            query = query.where(and_(*filters))
            
        query = query.order_by(ProductCategory.display_order, ProductCategory.name)
        query = query.options(selectinload(ProductCategory.children))

        result = await self.db.execute(query)
        all_categories = result.scalars().all()

        # Build tree structure (only root categories, children are loaded via relationship)
        root_categories = [cat for cat in all_categories if cat.parent_id is None]
        
        return root_categories

    async def update_category(
        self, 
        category_id: uuid.UUID, 
        category_in: ProductCategoryUpdate,
        current_user_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID] = None
    ) -> Optional[ProductCategory]:
        """Updates an existing product category."""
        db_category = await self.get_category(category_id, tenant_id)
        if not db_category:
            return None

        try:
            update_data = category_in.model_dump(exclude_unset=True)
            
            # Validate parent category if changed
            if "parent_id" in update_data and update_data["parent_id"]:
                # Prevent circular references
                if update_data["parent_id"] == category_id:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Category cannot be its own parent.",
                    )
                
                parent = await self.get_category(update_data["parent_id"], tenant_id)
                if not parent:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"Parent category with id {update_data['parent_id']} not found.",
                    )

            # Apply updates
            for field, value in update_data.items():
                setattr(db_category, field, value)

            await self.db.commit()
            await self.db.refresh(db_category)

            # Emit WebSocket notification
            if db_category.tenant_id:
                await emit_to_tenant(
                    db_category.tenant_id,
                    "product_category_updated",
                    {"category_id": str(db_category.id), "category_name": db_category.name}
                )

            logger.info(f"Product category updated: {db_category.id}")
            return db_category

        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"IntegrityError updating category: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Error updating category. Database constraint violation: {str(e)}",
            )
        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Unexpected error updating category: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal server error: {str(e)}",
            )

    async def delete_category(
        self, 
        category_id: uuid.UUID,
        current_user_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID] = None
    ) -> bool:
        """Deletes a product category."""
        db_category = await self.get_category(category_id, tenant_id, include_children=True)
        if not db_category:
            return False

        # Check if category has children
        if db_category.children:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete category with subcategories. Delete subcategories first.",
            )

        # Check if category has products
        products_query = select(Product).where(Product.category_id == category_id)
        result = await self.db.execute(products_query)
        products = result.scalars().first()
        
        if products:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete category with products. Move or delete products first.",
            )

        try:
            await self.db.delete(db_category)
            await self.db.commit()

            # Emit WebSocket notification
            if db_category.tenant_id:
                await emit_to_tenant(
                    db_category.tenant_id,
                    "product_category_deleted",
                    {"category_id": str(category_id), "category_name": db_category.name}
                )

            logger.info(f"Product category deleted: {category_id}")
            return True

        except Exception as e:
            await self.db.rollback()
            logger.exception(f"Error deleting category: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal server error: {str(e)}",
            )
