"""Allergen schemas for API serialization and validation."""

import uuid
from pydantic import BaseModel, ConfigDict, Field
from typing import Optional


class AllergenBase(BaseModel):
    """Base schema for Allergen with common fields."""
    
    name: str = Field(
        ..., 
        max_length=100,
        description="Name of the allergen (e.g., 'G<PERSON><PERSON>ten', 'Lactose')"
    )
    icon: Optional[str] = Field(
        None,
        max_length=10,
        description="Emoji or icon representation (e.g., '🌾', '🥛')"
    )
    description: Optional[str] = Field(
        None,
        description="Detailed description of the allergen"
    )
    is_active: bool = Field(
        True,
        description="Whether this allergen is active and available for use"
    )
    
    model_config = ConfigDict(from_attributes=True)


class AllergenCreate(AllergenBase):
    """Schema for creating a new allergen."""
    pass


class AllergenUpdate(BaseModel):
    """Schema for updating an existing allergen."""
    
    name: Optional[str] = Field(
        None,
        max_length=100,
        description="Name of the allergen"
    )
    icon: Optional[str] = Field(
        None,
        max_length=10,
        description="Emoji or icon representation"
    )
    description: Optional[str] = Field(
        None,
        description="Detailed description of the allergen"
    )
    is_active: Optional[bool] = Field(
        None,
        description="Whether this allergen is active"
    )
    
    model_config = ConfigDict(from_attributes=True)


class AllergenRead(AllergenBase):
    """Schema for reading allergen data with all fields."""
    
    id: uuid.UUID = Field(..., description="Unique identifier for the allergen")
    
    model_config = ConfigDict(from_attributes=True)


class AllergenReadSimple(BaseModel):
    """Simplified schema for reading allergen data in lists/references."""
    
    id: uuid.UUID = Field(..., description="Unique identifier for the allergen")
    name: str = Field(..., description="Name of the allergen")
    icon: Optional[str] = Field(None, description="Icon representation")
    
    model_config = ConfigDict(from_attributes=True)
