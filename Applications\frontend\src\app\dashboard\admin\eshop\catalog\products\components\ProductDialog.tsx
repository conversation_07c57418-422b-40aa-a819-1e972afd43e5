'use client';

import React, { useState, useEffect } from "react";
import Image from 'next/image';
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  useUniversalEshopProducts,
  productFormSchemaTransformed,
  ProductFormData,
  ProductType,
  EshopProduct
} from "@/hooks/useUniversalEshopProducts";
import { useUniversalEshopCategories } from "@/hooks/useUniversalEshopCategories";

import { LoadingSpinner } from "@/components/ui/LoadingSpinner";
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";

interface ProductDialogProps {
  children: React.ReactNode;
  product?: EshopProduct;
}

export function ProductDialog({ children, product }: ProductDialogProps) {
  const [open, setOpen] = useState(false);
  const [tagInput, setTagInput] = useState('');
  const [imageInput, setImageInput] = useState('');
  
  const { createProduct, updateProduct } = useUniversalEshopProducts();
  const { categories } = useUniversalEshopCategories();
  const isEditing = !!product;

  const form = useForm<ProductFormData>({
    resolver: zodResolver(productFormSchemaTransformed),
    defaultValues: {
      name: product?.name || '',
      description_short: product?.description_short || '',
      description_long: product?.description_long || '',
      base_price: product?.base_price || 0,
      category_id: product?.category_id || '',
      supplier_id: product?.supplier_id || undefined,
      is_active: product?.is_active ?? true,
      product_type: product?.product_type || 'physical',
      stock_quantity: product?.stock_quantity || 0,
      sku: product?.sku || '',
      weight: product?.weight || undefined,
      dimensions: product?.dimensions ? {
        length: product.dimensions.length ?? undefined,
        width: product.dimensions.width ?? undefined,
        height: product.dimensions.height ?? undefined,
      } : undefined,
      tags: product?.tags || [],
      images: product?.images || [],
    }
  });

  const { handleSubmit, formState: { errors, isSubmitting }, reset, watch, setValue } = form;
  const watchedTags = watch('tags') || [];
  const watchedImages = watch('images') || [];

  // Flatten categories for select options
  const flattenCategories = (cats: any[], level = 0): any[] => {
    let flatList: any[] = [];
    for (const cat of cats) {
      flatList.push({ ...cat, level });
      if (cat.children && cat.children.length > 0) {
        flatList = flatList.concat(flattenCategories(cat.children, level + 1));
      }
    }
    return flatList;
  };

  const flatCategories = flattenCategories(categories);

  const addTag = () => {
    if (tagInput.trim() && !watchedTags.includes(tagInput.trim())) {
      setValue('tags', [...watchedTags, tagInput.trim()]);
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setValue('tags', watchedTags.filter(tag => tag !== tagToRemove));
  };

  const addImage = () => {
    if (imageInput.trim() && !watchedImages.includes(imageInput.trim())) {
      setValue('images', [...watchedImages, imageInput.trim()]);
      setImageInput('');
    }
  };

  const removeImage = (imageToRemove: string) => {
    setValue('images', watchedImages.filter(img => img !== imageToRemove));
  };

  const onSubmit = async (data: ProductFormData) => {
    try {
      const productData = {
        ...data,
        supplier_id: data.supplier_id || null,
        stock_quantity: data.stock_quantity ?? 0,
      };

      if (isEditing && product) {
        await updateProduct(product.id, productData);
        toast.success('Produto atualizado com sucesso!');
      } else {
        await createProduct(productData);
        toast.success('Produto criado com sucesso!');
      }
      
      reset();
      setOpen(false);
    } catch (error: any) {
      console.error(error);
      
      let errorMessage = isEditing ? 'Erro ao atualizar produto' : 'Erro ao criar produto';
      
      if (error?.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      } else if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      
      toast.error(errorMessage);
    }
  };

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (open) {
      if (product) {
        reset({
          name: product.name,
          description_short: product.description_short || '',
          description_long: product.description_long || '',
          base_price: product.base_price,
          category_id: product.category_id,
          supplier_id: product.supplier_id || undefined,
          is_active: product.is_active,
          product_type: product.product_type,
          stock_quantity: product.stock_quantity || 0,
          sku: product.sku || '',
          weight: product.weight || undefined,
          dimensions: product.dimensions ? {
            length: product.dimensions.length ?? undefined,
            width: product.dimensions.width ?? undefined,
            height: product.dimensions.height ?? undefined,
          } : undefined,
          tags: product.tags || [],
          images: product.images || [],
        });
      } else {
        reset({
          name: '',
          description_short: '',
          description_long: '',
          base_price: 0,
          category_id: '',
          supplier_id: undefined,
          is_active: true,
          product_type: 'physical',
          stock_quantity: 0,
          sku: '',
          weight: undefined,
          dimensions: undefined,
          tags: [],
          images: [],
        });
      }
    }
  }, [open, product, reset]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Editar Produto' : 'Criar Novo Produto'}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? 'Atualize as informações do produto abaixo.' 
              : 'Preencha as informações para criar um novo produto.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Informações Básicas</h3>
                
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nome do Produto *</FormLabel>
                      <FormControl>
                        <Input placeholder="Digite o nome do produto" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description_short"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Descrição Curta</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Descrição curta do produto..."
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description_long"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Descrição Longa</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Descrição detalhada do produto..."
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="category_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Categoria *</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione uma categoria" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {flatCategories.map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                              <span style={{ paddingLeft: `${category.level * 1.5}rem` }}>
                                {category.level > 0 && '↳ '}
                                {category.name}
                              </span>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="product_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tipo de Produto *</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o tipo" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="physical">Físico</SelectItem>
                          <SelectItem value="digital">Digital</SelectItem>
                          <SelectItem value="service">Serviço</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="sku"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>SKU</FormLabel>
                      <FormControl>
                        <Input placeholder="Código do produto" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Pricing and Inventory */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Preços e Estoque</h3>
                
                <FormField
                  control={form.control}
                  name="base_price"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Preço *</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          step="0.01" 
                          placeholder="0.00" 
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />



                <FormField
                  control={form.control}
                  name="stock_quantity"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Quantidade em Estoque</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          placeholder="0" 
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="weight"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Peso (kg)</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          step="0.01" 
                          placeholder="0.00" 
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="is_active"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Produto Ativo</FormLabel>
                        <div className="text-sm text-muted-foreground">
                          Produto visível para os clientes
                        </div>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Tags */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Tags</h3>
              <div className="flex gap-2">
                <Input
                  placeholder="Adicionar tag"
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      addTag();
                    }
                  }}
                />
                <Button type="button" onClick={addTag} variant="outline">
                  Adicionar
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {watchedTags.map((tag, index) => (
                  <Badge key={index} variant="secondary" className="flex items-center gap-1">
                    {tag}
                    <X 
                      className="h-3 w-3 cursor-pointer" 
                      onClick={() => removeTag(tag)}
                    />
                  </Badge>
                ))}
              </div>
            </div>

            {/* Images */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Imagens</h3>
              <div className="flex gap-2">
                <Input
                  placeholder="URL da imagem"
                  value={imageInput}
                  onChange={(e) => setImageInput(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      addImage();
                    }
                  }}
                />
                <Button type="button" onClick={addImage} variant="outline">
                  Adicionar
                </Button>
              </div>
              <div className="space-y-2">
                {watchedImages.map((image, index) => (
                  <div key={index} className="flex items-center gap-2 p-2 border rounded">
                    <div className="w-12 h-12 relative">
                      <Image src={image} alt={`Preview ${index + 1}`} fill className="object-cover rounded" />
                    </div>
                    <span className="flex-1 text-sm truncate">{image}</span>
                    <X 
                      className="h-4 w-4 cursor-pointer text-red-500" 
                      onClick={() => removeImage(image)}
                    />
                  </div>
                ))}
              </div>
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setOpen(false)}>
                Cancelar
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <LoadingSpinner className="mr-2" />
                    {isEditing ? 'Atualizando...' : 'Criando...'}
                  </>
                ) : (
                  isEditing ? 'Atualizar Produto' : 'Criar Produto'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}