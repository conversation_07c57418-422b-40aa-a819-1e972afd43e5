/**
 * Tenant Settings API Service
 * Handles all API communications for tenant settings management
 */

import apiClient from '@/lib/api/client';

export interface TenantSettings {
  // Business Information
  business_name?: string;
  business_type?: string;
  tenant_slug?: string;

  // Currency & Financial Settings
  currency: string;
  currency_format?: {
    decimal_separator: '.' | ',';
    thousands_separator: '.' | ',' | ' ' | '';
    symbol_position: 'left' | 'right';
    symbol_spacing: boolean;
  };
  timezone: string;
  subscription_plan?: string;
  subscription_status?: string;

  // Operating Hours
  operating_hours: {
    [key: string]: {
      is_open: boolean;
      service_hours?: Array<{
        id: string;
        open: string;
        close: string;
        type: 'service';
        label?: string;
        description?: string;
      }>;
      break_periods?: Array<{
        id: string;
        open: string;
        close: string;
        type: 'break';
        label?: string;
        description?: string;
      }>;
      happy_hour?: Array<{
        id: string;
        open: string;
        close: string;
        type: 'happy_hour';
        label?: string;
        description?: string;
      }>;
      // Legacy support
      slots?: Array<{
        id: string;
        open: string;
        close: string;
        isBreak?: boolean;
        label?: string;
      }>;
    };
  };

  // Payment Methods
  payment_methods: {
    [key: string]: boolean;
  };

  // Language Settings
  multi_language_enabled: boolean;
  available_languages: Array<{
    code: string;
    name: string;
    nativeName: string;
    flag: string;
    isEnabled: boolean;
    isDefault: boolean;
    completeness: number;
  }>;

  // Loyalty System
  loyalty_config: {
    enabled: boolean;
    pointsPerDollar: number;
    dollarPerPoint: number;
    minimumRedemption: number;
    pointsExpiration: number;
    tierSystem: boolean;
    welcomeBonus: number;
  };

  // Location & Address
  address: {
    street: string;
    number?: string;
    complement?: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    latitude?: number;
    longitude?: number;
    phone?: string;
    phone_secondary?: string;
    fax?: string;
    email?: string;
    website?: string;
    business_hours_note?: string;
  };

  // Tax Configuration
  tax_config: {
    baseTaxRate: number;
    calculationMethod: 'incremental' | 'inclusive';
    allowProductOverrides: boolean;
    taxName: string;
    taxDescription: string;
  };

  // WiFi Networks
  wifi_networks: Array<{
    id: string;
    name: string;
    password: string;
    zone: string | null;
    isEnabled: boolean;
    isGuest: boolean;
    bandwidth?: string;
    maxDevices?: number;
    description?: string;
  }>;

  // Social Media Links
  social_media_links?: Array<{
    id: string;
    platform: string;
    url: string;
    display_name: string;
    icon: string;
    is_active: boolean;
    order: number;
  }>;
}

export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

class TenantSettingsService {
  private baseUrl = '/tenants/settings';

  /**
   * Get current tenant settings
   */
  async getSettings(): Promise<TenantSettings> {
    try {
      console.log('🔍 TenantSettingsService.getSettings - Fetching settings');

      const response = await apiClient.get<any>(
        `${this.baseUrl}/`
      );

      console.log('🔍 TenantSettingsService.getSettings - Raw response:', response);

      // Extract data from axios response
      const data = response.data || response;
      console.log('🔍 TenantSettingsService.getSettings - Extracted data:', data);

      // Map backend response to frontend format

      const mappedSettings: TenantSettings = {
        business_name: data.business_name,
        business_type: data.business_type,
        tenant_slug: data.tenant_slug,
        currency: data.default_currency || data.currency || 'BRL',
        currency_format: (() => {
          const currency = data.default_currency || data.currency || 'BRL';
          const formatFromBackend = data.currency_config?.currency_formatting?.[currency];

          console.log(`[TenantSettingsService] Loading currency_format for ${currency}:`, formatFromBackend);
          console.log(`[TenantSettingsService] Full currency_config:`, data.currency_config);

          if (formatFromBackend) {
            console.log(`[TenantSettingsService] Using format from backend:`, formatFromBackend);
            return formatFromBackend;
          }

          // Use correct default formats based on currency
          const DEFAULT_FORMATS: Record<string, any> = {
            'USD': { decimal_separator: '.', thousands_separator: ',', symbol_position: 'left', symbol_spacing: false },
            'BRL': { decimal_separator: ',', thousands_separator: '.', symbol_position: 'left', symbol_spacing: true },
            'EUR': { decimal_separator: ',', thousands_separator: '.', symbol_position: 'right', symbol_spacing: true },
            'GBP': { decimal_separator: '.', thousands_separator: ',', symbol_position: 'left', symbol_spacing: false },
            'JPY': { decimal_separator: '.', thousands_separator: ',', symbol_position: 'left', symbol_spacing: false },
          };

          const defaultFormat = DEFAULT_FORMATS[currency] || DEFAULT_FORMATS['USD'];
          console.log(`[TenantSettingsService] Using default format for ${currency}:`, defaultFormat);
          return defaultFormat;
        })(),
        timezone: data.timezone || 'America/Sao_Paulo',
        subscription_plan: data.subscription_plan,
        subscription_status: data.subscription_status,
        operating_hours: data.operating_hours || {},
        payment_methods: data.payment_methods || {},
        multi_language_enabled: data.multi_language_enabled || false,
        available_languages: data.available_languages || [],
        loyalty_config: data.loyalty_config || {
          enabled: data.loyalty_enabled || false,
          pointsPerDollar: 1,
          dollarPerPoint: 0.01,
          minimumRedemption: 100,
          pointsExpiration: 365,
          tierSystem: false,
          welcomeBonus: 100
        },
        address: data.address || {
          street: '',
          number: '',
          complement: '',
          city: '',
          state: '',
          zipCode: '',
          country: data.country || 'US',
          phone: '',
          phone_is_whatsapp: false,
          phone_secondary: '',
          phone_secondary_is_whatsapp: false,
          fax: ''
        },
        tax_config: data.tax_config || {
          baseTaxRate: parseFloat(data.base_tax_rate || '0'),
          calculationMethod: data.tax_calculation_method || 'incremental',
          allowProductOverrides: true,
          taxName: 'Tax',
          taxDescription: 'Standard tax applied to orders'
        },
        wifi_networks: data.wifi_networks || [],
        social_media_links: data.social_media_links || []
      };

      console.log('🔍 TenantSettingsService.getSettings - Mapped settings:', mappedSettings);

      return mappedSettings;
    } catch (error) {
      console.error('🔍 TenantSettingsService.getSettings - Error:', error);
      throw error;
    }
  }

  /**
   * Update tenant settings
   */
  async updateSettings(settings: Partial<TenantSettings>): Promise<TenantSettings> {
    try {
      console.log('🔍 TenantSettingsService.updateSettings - Updating:', settings);

      // Filter out fields that belong to restaurant settings, not tenant settings
      const excludedFields = ['wifi_networks', 'social_media_links'];
      const filteredSettings = Object.fromEntries(
        Object.entries(settings).filter(([key]) => !excludedFields.includes(key))
      );

      const response = await apiClient.put<ApiResponse<TenantSettings>>(
        `${this.baseUrl}/`,
        filteredSettings
      );

      console.log('🔍 TenantSettingsService.updateSettings - Raw response:', response);

      // ✅ CRITICAL FIX: Get fresh data with proper mapping instead of returning raw response
      // This ensures consistency between getSettings and updateSettings data structure
      console.log('🔍 TenantSettingsService.updateSettings - Fetching fresh mapped data...');
      const freshSettings = await this.getSettings();
      console.log('🔍 TenantSettingsService.updateSettings - Fresh mapped settings:', freshSettings);

      return freshSettings;
    } catch (error) {
      console.error('🔍 TenantSettingsService.updateSettings - Error:', error);
      throw error;
    }
  }

  /**
   * Update specific setting section
   */
  async updateSection(section: string, data: any): Promise<TenantSettings> {
    try {
      console.log(`🔍 TenantSettingsService.updateSection - Section: ${section}`, data);

      const response = await apiClient.put<ApiResponse<TenantSettings>>(
        `${this.baseUrl}/${section}/`,
        data
      );

      console.log('🔍 TenantSettingsService.updateSection - Response:', response);
      return response.data.data || response.data;
    } catch (error) {
      console.error('🔍 TenantSettingsService.updateSection - Error:', error);
      throw error;
    }
  }

  /**
   * Update location settings including address with retry logic
   */
  async updateLocationSettings(locationData: {
    address?: any;
    country?: string;
  }): Promise<TenantSettings> {
    const maxRetries = 2;
    let lastError: any;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔍 TenantSettingsService.updateLocationSettings - Attempt ${attempt}/${maxRetries}:`, locationData);

        const response = await apiClient.put<ApiResponse<TenantSettings>>(
          `${this.baseUrl}/location/`,
          locationData
        );

        console.log('🔍 TenantSettingsService.updateLocationSettings - Success:', response);
        return response.data.data || response.data;

      } catch (error: any) {
        lastError = error;
        console.error(`🔍 TenantSettingsService.updateLocationSettings - Attempt ${attempt} failed:`, error);

        // If it's a 401 error and not the last attempt, wait and retry
        if (error.response?.status === 401 && attempt < maxRetries) {
          console.log(`⏳ Retrying in 1 second... (attempt ${attempt + 1}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, 1000));
          continue;
        }

        // If it's not a retryable error or last attempt, throw immediately
        break;
      }
    }

    console.error('🔍 TenantSettingsService.updateLocationSettings - All attempts failed');
    throw lastError;
  }

  /**
   * Validate address and get coordinates
   */
  async validateAddress(address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  }): Promise<{
    isValid: boolean;
    latitude?: number;
    longitude?: number;
    formattedAddress?: string;
  }> {
    try {
      console.log('🔍 TenantSettingsService.validateAddress - Validating:', address);

      const response = await apiClient.put<{
        isValid: boolean;
        latitude?: number;
        longitude?: number;
        formattedAddress?: string;
      }>(`${this.baseUrl}/location/`, { address });

      console.log('🔍 TenantSettingsService.validateAddress - Response:', response);
      return response.data;
    } catch (error) {
      console.error('🔍 TenantSettingsService.validateAddress - Error:', error);
      throw error;
    }
  }

  /**
   * Get available subscription plans
   */
  async getSubscriptionPlans(): Promise<Array<{
    id: string;
    name: string;
    price: number;
    interval: 'monthly' | 'yearly';
    features: string[];
    limits: {
      orders: number;
      users: number;
      storage: string;
    };
  }>> {
    try {
      console.log('🔍 TenantSettingsService.getSubscriptionPlans - Fetching plans');
      
      const response = await apiClient.get<Array<any>>(
        `${this.baseUrl}/subscription-plans`
      );

      console.log('🔍 TenantSettingsService.getSubscriptionPlans - Response:', response);
      return response.data;
    } catch (error) {
      console.error('🔍 TenantSettingsService.getSubscriptionPlans - Error:', error);
      throw error;
    }
  }

  /**
   * Update subscription plan
   */
  async updateSubscriptionPlan(planId: string): Promise<{
    success: boolean;
    message: string;
    nextBillingDate?: string;
  }> {
    try {
      console.log('🔍 TenantSettingsService.updateSubscriptionPlan - Plan ID:', planId);
      
      const response = await apiClient.post<{
        success: boolean;
        message: string;
        nextBillingDate?: string;
      }>(`${this.baseUrl}/subscription/upgrade`, { planId });

      console.log('🔍 TenantSettingsService.updateSubscriptionPlan - Response:', response);
      return response.data;
    } catch (error) {
      console.error('🔍 TenantSettingsService.updateSubscriptionPlan - Error:', error);
      throw error;
    }
  }

  /**
   * Get available zones for WiFi management
   */
  async getZones(): Promise<string[]> {
    try {
      console.log('🔍 TenantSettingsService.getZones - Fetching zones');

      const response = await apiClient.get<Array<{
        name: string;
        type: string;
        table_count: number;
        is_active: boolean;
        is_custom: boolean;
      }>>(
        `${this.baseUrl}/categories/zones`
      );

      console.log('🔍 TenantSettingsService.getZones - Response:', response);

      // Extract zone names from the response
      const zones = Array.isArray(response) ? response.map(zone => zone.name) : [];
      return zones;
    } catch (error) {
      console.error('🔍 TenantSettingsService.getZones - Error:', error);
      // Return default zones if API fails
      return [
        'Main Dining',
        'Private Dining',
        'Bar Area',
        'Outdoor Patio',
        'Kitchen',
        'Office'
      ];
    }
  }

  /**
   * Create a custom zone
   */
  async createZone(zoneName: string): Promise<{
    name: string;
    type: string;
    table_count: number;
    is_active: boolean;
    is_custom: boolean;
    message: string;
  }> {
    try {
      console.log('🔍 TenantSettingsService.createZone - Zone name:', zoneName);

      const response = await apiClient.post<{
        name: string;
        type: string;
        table_count: number;
        is_active: boolean;
        is_custom: boolean;
        message: string;
      }>(`${this.baseUrl}/categories/zones?zone_name=${encodeURIComponent(zoneName)}`);

      console.log('🔍 TenantSettingsService.createZone - Response:', response);
      return response.data;
    } catch (error) {
      console.error('🔍 TenantSettingsService.createZone - Error:', error);
      throw error;
    }
  }

  /**
   * Delete a custom zone
   */
  async deleteZone(zoneName: string): Promise<{ message: string }> {
    try {
      console.log('🔍 TenantSettingsService.deleteZone - Zone name:', zoneName);

      const response = await apiClient.delete<{ message: string }>(
        `${this.baseUrl}/categories/zones/${encodeURIComponent(zoneName)}`
      );

      console.log('🔍 TenantSettingsService.deleteZone - Response:', response);
      return response.data;
    } catch (error) {
      console.error('🔍 TenantSettingsService.deleteZone - Error:', error);
      throw error;
    }
  }

  /**
   * Test WiFi network connectivity
   */
  async testWiFiNetwork(networkId: string): Promise<{
    success: boolean;
    message: string;
    signalStrength?: number;
    connectedDevices?: number;
  }> {
    try {
      console.log('🔍 TenantSettingsService.testWiFiNetwork - Network ID:', networkId);

      const response = await apiClient.post<{
        success: boolean;
        message: string;
        signalStrength?: number;
        connectedDevices?: number;
      }>(`${this.baseUrl}/wifi/test/`, { networkId });

      console.log('🔍 TenantSettingsService.testWiFiNetwork - Response:', response);
      return response.data;
    } catch (error) {
      console.error('🔍 TenantSettingsService.testWiFiNetwork - Error:', error);
      throw error;
    }
  }

  /**
   * Update social media links
   */
  async updateSocialMediaLinks(links: Array<{
    id: string;
    platform: string;
    url: string;
    display_name: string;
    icon: string;
    is_active: boolean;
    order: number;
  }>): Promise<TenantSettings> {
    try {
      console.log('🔍 TenantSettingsService.updateSocialMediaLinks - Links:', links);

      const response = await apiClient.put<TenantSettings>(
        `${this.baseUrl}/social-media/`,
        { social_media_links: links }
      );

      console.log('🔍 TenantSettingsService.updateSocialMediaLinks - Response:', response);
      return response.data;
    } catch (error) {
      console.error('🔍 TenantSettingsService.updateSocialMediaLinks - Error:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const tenantSettingsService = new TenantSettingsService();
export default tenantSettingsService;
