"""Loyalty service for CRM module."""

import uuid  # noqa: E402
import logging
from typing import List, Optional, Tuple
from datetime import datetime
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError

from app.modules.shared.crm.models.loyalty import (  # noqa: E402
    LoyaltyProgram,
    LoyaltyMembership,
    LoyaltyTransaction,
    LoyaltyTransactionType,
    LoyaltyMembershipStatus,
)
from app.modules.shared.crm.schemas.loyalty import (  # noqa: E402
    LoyaltyProgramCreate,
    LoyaltyProgramUpdate,
    LoyaltyMembershipCreate,
    LoyaltyMembershipUpdate,
    LoyaltyTransactionCreate,
)

logger = logging.getLogger(__name__)


class LoyaltyService:
    """Service for managing CRM loyalty programs."""

    # ==================== Loyalty Program Methods ====================

    @staticmethod
    async def create_program(
        db: AsyncSession, tenant_id: uuid.UUID, program_in: LoyaltyProgramCreate
    ) -> LoyaltyProgram:
        """Create a new loyalty program."""
        try:
            program_data = program_in.model_dump(exclude_unset=True)
            db_program = LoyaltyProgram(tenant_id=tenant_id, **program_data)

            db.add(db_program)
            await db.commit()
            await db.refresh(db_program)

            return db_program
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(f"Error creating loyalty program: {e}")
            raise

    @staticmethod
    async def get_program(
        db: AsyncSession, tenant_id: uuid.UUID, program_id: uuid.UUID
    ) -> Optional[LoyaltyProgram]:
        """Get a loyalty program by ID."""
        try:
            query = select(LoyaltyProgram).where(
                LoyaltyProgram.tenant_id == tenant_id, LoyaltyProgram.id == program_id
            )
            result = await db.execute(query)
            return result.scalars().first()
        except SQLAlchemyError as e:
            logger.error(f"Error getting loyalty program {program_id}: {e}")
            raise

    @staticmethod
    async def get_programs(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        is_active: Optional[bool] = None,
    ) -> List[LoyaltyProgram]:
        """Get all loyalty programs with optional filtering."""
        try:
            query = select(LoyaltyProgram).where(LoyaltyProgram.tenant_id == tenant_id)

            # Apply filters if provided
            if is_active is not None:
                query = query.where(LoyaltyProgram.is_active == is_active)

            # Apply pagination
            query = query.offset(skip).limit(limit)

            result = await db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            logger.error(f"Error getting loyalty programs: {e}")
            raise

    @staticmethod
    async def update_program(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        program_id: uuid.UUID,
        program_in: LoyaltyProgramUpdate,
    ) -> Optional[LoyaltyProgram]:
        """Update a loyalty program."""
        try:
            # Get the program first to ensure it exists and belongs to the tenant
            db_program = await LoyaltyService.get_program(db, tenant_id, program_id)
            if not db_program:
                return None

            # Update the program
            program_data = program_in.model_dump(exclude_unset=True)

            # Update the program in the database
            query = (
                update(LoyaltyProgram)
                .where(
                    LoyaltyProgram.id == program_id,
                    LoyaltyProgram.tenant_id == tenant_id,
                )
                .values(**program_data)
                .execution_options(synchronize_session="fetch")
            )
            await db.execute(query)
            await db.commit()

            # Refresh and return the updated program
            return await LoyaltyService.get_program(db, tenant_id, program_id)
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(f"Error updating loyalty program {program_id}: {e}")
            raise

    # ==================== Loyalty Membership Methods ====================

    @staticmethod
    async def create_membership(
        db: AsyncSession, tenant_id: uuid.UUID, membership_in: LoyaltyMembershipCreate
    ) -> LoyaltyMembership:
        """Create a new loyalty membership."""
        try:
            membership_data = membership_in.model_dump(exclude_unset=True)
            db_membership = LoyaltyMembership(tenant_id=tenant_id, **membership_data)

            db.add(db_membership)
            await db.commit()
            await db.refresh(db_membership)

            return db_membership
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(f"Error creating loyalty membership: {e}")
            raise

    @staticmethod
    async def get_membership(
        db: AsyncSession, tenant_id: uuid.UUID, membership_id: uuid.UUID
    ) -> Optional[LoyaltyMembership]:
        """Get a loyalty membership by ID."""
        try:
            query = select(LoyaltyMembership).where(
                LoyaltyMembership.tenant_id == tenant_id,
                LoyaltyMembership.id == membership_id,
            )
            result = await db.execute(query)
            return result.scalars().first()
        except SQLAlchemyError as e:
            logger.error(f"Error getting loyalty membership {membership_id}: {e}")
            raise

    @staticmethod
    async def get_account_memberships(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        account_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        status: Optional[LoyaltyMembershipStatus] = None,
    ) -> List[LoyaltyMembership]:
        """Get all loyalty memberships for an account."""
        try:
            query = select(LoyaltyMembership).where(
                LoyaltyMembership.tenant_id == tenant_id,
                LoyaltyMembership.account_id == account_id,
            )

            # Apply filters if provided
            if status:
                query = query.where(LoyaltyMembership.status == status)

            # Apply pagination
            query = query.offset(skip).limit(limit)

            result = await db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            logger.error(f"Error getting loyalty memberships for account {account_id}: {e}")
            raise

    @staticmethod
    async def update_membership(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        membership_id: uuid.UUID,
        membership_in: LoyaltyMembershipUpdate,
    ) -> Optional[LoyaltyMembership]:
        """Update a loyalty membership."""
        try:
            # Get the membership first to ensure it exists and belongs to the tenant
            db_membership = await LoyaltyService.get_membership(db, tenant_id, membership_id)
            if not db_membership:
                return None

            # Update the membership
            membership_data = membership_in.model_dump(exclude_unset=True)

            # Update the membership in the database
            query = (
                update(LoyaltyMembership)
                .where(
                    LoyaltyMembership.id == membership_id,
                    LoyaltyMembership.tenant_id == tenant_id,
                )
                .values(**membership_data)
                .execution_options(synchronize_session="fetch")
            )
            await db.execute(query)
            await db.commit()

            # Refresh and return the updated membership
            return await LoyaltyService.get_membership(db, tenant_id, membership_id)
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(f"Error updating loyalty membership {membership_id}: {e}")
            raise

    # ==================== Loyalty Transaction Methods ====================

    @staticmethod
    async def create_transaction(
        db: AsyncSession, tenant_id: uuid.UUID, transaction_in: LoyaltyTransactionCreate
    ) -> Tuple[LoyaltyTransaction, LoyaltyMembership]:
        """Create a new loyalty transaction and update membership balance."""
        try:
            # Get the membership first to ensure it exists and belongs to the tenant
            membership_id = transaction_in.membership_id
            db_membership = await LoyaltyService.get_membership(db, tenant_id, membership_id)
            if not db_membership:
                raise ValueError(f"Membership {membership_id} not found")

            # Create the transaction
            transaction_data = transaction_in.model_dump(exclude_unset=True)
            db_transaction = LoyaltyTransaction(tenant_id=tenant_id, **transaction_data)

            # Update the membership balance
            new_balance = db_membership.points_balance + transaction_in.points
            db_membership.points_balance = new_balance
            db_membership.last_activity_date = transaction_in.transaction_date or datetime.utcnow()

            # Add the transaction and update the membership
            db.add(db_transaction)
            await db.commit()
            await db.refresh(db_transaction)
            await db.refresh(db_membership)

            return db_transaction, db_membership
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(f"Error creating loyalty transaction: {e}")
            raise

    @staticmethod
    async def get_membership_transactions(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        membership_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        transaction_type: Optional[LoyaltyTransactionType] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
    ) -> List[LoyaltyTransaction]:
        """Get all transactions for a membership."""
        try:
            query = select(LoyaltyTransaction).where(
                LoyaltyTransaction.tenant_id == tenant_id,
                LoyaltyTransaction.membership_id == membership_id,
            )

            # Apply filters if provided
            if transaction_type:
                query = query.where(LoyaltyTransaction.transaction_type == transaction_type)

            if start_date:
                query = query.where(LoyaltyTransaction.transaction_date >= start_date)

            if end_date:
                query = query.where(LoyaltyTransaction.transaction_date <= end_date)

            # Order by transaction date (most recent first)
            query = query.order_by(LoyaltyTransaction.transaction_date.desc())

            # Apply pagination
            query = query.offset(skip).limit(limit)

            result = await db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            logger.error(f"Error getting loyalty transactions for membership {membership_id}: {e}")
            raise


# Create a singleton instance
loyalty_service = LoyaltyService()
