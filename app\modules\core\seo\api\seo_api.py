"""
SEO API

REST API endpoints for SEO metadata and URL slug management.
"""

import uuid
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.users.dependencies import get_current_user
from app.modules.core.users.models.user import User
from app.modules.core.seo.services.seo_service import SEOService
from app.modules.core.seo.services.slug_service import SlugService
from app.modules.core.seo.services.meta_service import MetaService
from app.modules.core.seo.schemas.seo_schemas import (
    SEOMetaCreate, SEOMetaUpdate, SEOMetaResponse,
    URLSlugCreate, URLSlugUpdate, URLSlugResponse,
    SEOAnalysisResponse, MetaTagsResponse, HreflangResponse
)

router = APIRouter()

# Initialize services
seo_service = SEOService()
slug_service = SlugService()
meta_service = MetaService()


@router.post("/meta", response_model=SEOMetaResponse)
async def create_seo_meta(
    seo_data: SEOMetaCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create SEO metadata for content."""
    try:
        seo_meta = await seo_service.create_seo_meta(db, seo_data, current_user.id)
        return seo_meta
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/meta/{content_type}/{content_id}/{language_code}", response_model=SEOMetaResponse)
async def get_seo_meta(
    content_type: str,
    content_id: uuid.UUID,
    language_code: str,
    db: AsyncSession = Depends(get_db)
):
    """Get SEO metadata for specific content and language."""
    seo_meta = await seo_service.get_seo_meta(db, content_type, content_id, language_code)
    if not seo_meta:
        raise HTTPException(status_code=404, detail="SEO metadata not found")
    return seo_meta


@router.get("/meta/{content_type}/{content_id}", response_model=List[SEOMetaResponse])
async def get_all_seo_meta_for_content(
    content_type: str,
    content_id: uuid.UUID,
    db: AsyncSession = Depends(get_db)
):
    """Get all SEO metadata for content across all languages."""
    seo_metas = await seo_service.get_all_seo_meta_for_content(db, content_type, content_id)
    return seo_metas


@router.put("/meta/{seo_meta_id}", response_model=SEOMetaResponse)
async def update_seo_meta(
    seo_meta_id: uuid.UUID,
    seo_data: SEOMetaUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update SEO metadata."""
    seo_meta = await seo_service.update_seo_meta(db, seo_meta_id, seo_data, current_user.id)
    if not seo_meta:
        raise HTTPException(status_code=404, detail="SEO metadata not found")
    return seo_meta


@router.delete("/meta/{content_type}/{content_id}")
async def delete_seo_meta(
    content_type: str,
    content_id: uuid.UUID,
    language_code: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete SEO metadata for content."""
    success = await seo_service.delete_seo_meta(db, content_type, content_id, language_code)
    if not success:
        raise HTTPException(status_code=404, detail="SEO metadata not found")
    return {"message": "SEO metadata deleted successfully"}


@router.post("/slugs", response_model=URLSlugResponse)
async def create_url_slug(
    slug_data: URLSlugCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create URL slug for content."""
    try:
        url_slug = await slug_service.create_slug(db, slug_data, current_user.id)
        return url_slug
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/slugs/{content_type}/{content_id}/{language_code}", response_model=URLSlugResponse)
async def get_url_slug(
    content_type: str,
    content_id: uuid.UUID,
    language_code: str,
    primary_only: bool = Query(True),
    db: AsyncSession = Depends(get_db)
):
    """Get URL slug for specific content and language."""
    url_slug = await slug_service.get_slug(db, content_type, content_id, language_code, primary_only)
    if not url_slug:
        raise HTTPException(status_code=404, detail="URL slug not found")
    return url_slug


@router.get("/slugs/{content_type}/{content_id}", response_model=List[URLSlugResponse])
async def get_all_url_slugs_for_content(
    content_type: str,
    content_id: uuid.UUID,
    db: AsyncSession = Depends(get_db)
):
    """Get all URL slugs for content across all languages."""
    url_slugs = await slug_service.get_all_slugs_for_content(db, content_type, content_id)
    return url_slugs


@router.get("/slugs/find/{slug}", response_model=URLSlugResponse)
async def find_content_by_slug(
    slug: str,
    content_type: Optional[str] = Query(None),
    language_code: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db)
):
    """Find content by slug."""
    url_slug = await slug_service.find_content_by_slug(db, slug, content_type, language_code)
    if not url_slug:
        raise HTTPException(status_code=404, detail="Content not found for this slug")
    return url_slug


@router.put("/slugs/{slug_id}", response_model=URLSlugResponse)
async def update_url_slug(
    slug_id: uuid.UUID,
    slug_data: URLSlugUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update URL slug."""
    url_slug = await slug_service.update_slug(db, slug_id, slug_data)
    if not url_slug:
        raise HTTPException(status_code=404, detail="URL slug not found")
    return url_slug


@router.post("/slugs/multilingual", response_model=List[URLSlugResponse])
async def create_multilingual_slugs(
    content_type: str,
    content_id: uuid.UUID,
    slug_data: Dict[str, str],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create URL slugs for multiple languages."""
    try:
        url_slugs = await slug_service.create_multilingual_slugs(
            db, content_type, content_id, slug_data, current_user.id
        )
        return url_slugs
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/analyze/{content_type}/{content_id}/{language_code}", response_model=SEOAnalysisResponse)
async def analyze_seo(
    content_type: str,
    content_id: uuid.UUID,
    language_code: str,
    content_text: str = Query(..., description="Content text to analyze"),
    focus_keyword: Optional[str] = Query(None, description="Focus keyword for analysis"),
    db: AsyncSession = Depends(get_db)
):
    """Analyze SEO for content."""
    try:
        analysis = await seo_service.analyze_seo(
            db, content_type, content_id, language_code, content_text, focus_keyword
        )
        return analysis
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/meta-tags/{content_type}/{content_id}/{language_code}", response_model=MetaTagsResponse)
async def generate_meta_tags(
    content_type: str,
    content_id: uuid.UUID,
    language_code: str,
    base_url: str = Query("https://example.com", description="Base URL for links"),
    db: AsyncSession = Depends(get_db)
):
    """Generate HTML meta tags for content."""
    try:
        meta_tags = await meta_service.generate_meta_tags(
            db, content_type, content_id, language_code, base_url
        )
        return meta_tags
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/hreflang/{content_type}/{content_id}", response_model=List[HreflangResponse])
async def get_hreflang_tags(
    content_type: str,
    content_id: uuid.UUID,
    base_url: str = Query("https://example.com", description="Base URL for links"),
    db: AsyncSession = Depends(get_db)
):
    """Get hreflang tags for content."""
    try:
        hreflang_tags = await meta_service.generate_hreflang_tags(
            db, content_type, content_id, base_url
        )
        return hreflang_tags
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
