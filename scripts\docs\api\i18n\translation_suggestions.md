# I18N - Translation Suggestions

**Categoria:** I18N
**Módulo:** Translation Suggestions
**Total de Endpoints:** 8
**Gerado em:** 29/06/2025, 13:17:27

## 📋 Endpoints

- [GET /api/i18n/translation-suggestions/](#get-apii18ntranslation-suggestions) - Read Translation Suggestions
- [POST /api/i18n/translation-suggestions/](#post-apii18ntranslation-suggestions) - Create Translation Suggestion
- [GET /api/i18n/translation-suggestions/my](#get-apii18ntranslation-suggestionsmy) - Read My Translation Suggestions
- [DELETE /api/i18n/translation-suggestions/{suggestion_id}](#delete-apii18ntranslation-suggestionssuggestion-id) - Delete Translation Suggestion
- [GET /api/i18n/translation-suggestions/{suggestion_id}](#get-apii18ntranslation-suggestionssuggestion-id) - Read Translation Suggestion
- [PUT /api/i18n/translation-suggestions/{suggestion_id}](#put-apii18ntranslation-suggestionssuggestion-id) - Update Translation Suggestion
- [POST /api/i18n/translation-suggestions/{suggestion_id}/approve](#post-apii18ntranslation-suggestionssuggestion-idapprove) - Approve Translation Suggestion
- [POST /api/i18n/translation-suggestions/{suggestion_id}/reject](#post-apii18ntranslation-suggestionssuggestion-idreject) - Reject Translation Suggestion

## 🔐 Autenticação

Os endpoints desta seção requerem autenticação JWT.

### Headers Obrigatórios

```http
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

## 📊 Schemas

### HTTPValidationError

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `detail` | Array[ValidationError] | ❌ | - |

### TranslationSuggestionCreate

**Descrição:** Schema for creating a new TranslationSuggestion.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `key` | string | ✅ | Translation key |
| `suggested_text` | string | ✅ | Suggested translation text |
| `language_id` | string | ✅ | ID of the language |

### TranslationSuggestionRead

**Descrição:** Schema for reading a TranslationSuggestion.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `key` | string | ✅ | Translation key |
| `suggested_text` | string | ✅ | Suggested translation text |
| `language_id` | string | ✅ | ID of the language |
| `id` | string | ✅ | - |
| `user_id` | string | ✅ | - |
| `status` | string | ✅ | - |

### TranslationSuggestionUpdate

**Descrição:** Schema for updating an existing TranslationSuggestion.

**Propriedades:**

| Campo | Tipo | Obrigatório | Descrição |
|-------|------|-------------|-----------|
| `key` | unknown | ❌ | - |
| `suggested_text` | unknown | ❌ | - |
| `language_id` | unknown | ❌ | - |
| `status` | unknown | ❌ | Status of the suggestion (pending, approved, rejected) |

## 🔗 Endpoints Detalhados

### GET /api/i18n/translation-suggestions/ {#get-apii18ntranslation-suggestions}

**Resumo:** Read Translation Suggestions
**Descrição:** Retrieve all translation suggestions with pagination and optional filtering.
Requires ADMIN system role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `language_id` | string | query | ❌ | Filter by language ID |
| `key` | string | query | ❌ | Filter by translation key |
| `status` | string | query | ❌ | Filter by status (pending, approved, rejected) |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/i18n/translation-suggestions/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/i18n/translation-suggestions/ {#post-apii18ntranslation-suggestions}

**Resumo:** Create Translation Suggestion
**Descrição:** Create a new translation suggestion.
Any authenticated user can create a suggestion.

**🔐 Autenticação:** Requerida

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TranslationSuggestionCreate](#translationsuggestioncreate)

**📥 Respostas:**

**201:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TranslationSuggestionRead](#translationsuggestionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/i18n/translation-suggestions/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### GET /api/i18n/translation-suggestions/my {#get-apii18ntranslation-suggestionsmy}

**Resumo:** Read My Translation Suggestions
**Descrição:** Retrieve all translation suggestions made by the current user.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `language_id` | string | query | ❌ | Filter by language ID |
| `key` | string | query | ❌ | Filter by translation key |
| `status` | string | query | ❌ | Filter by status (pending, approved, rejected) |
| `skip` | integer | query | ❌ | - |
| `limit` | integer | query | ❌ | - |

**📥 Respostas:**

**200:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/i18n/translation-suggestions/my" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### DELETE /api/i18n/translation-suggestions/{suggestion_id} {#delete-apii18ntranslation-suggestionssuggestion-id}

**Resumo:** Delete Translation Suggestion
**Descrição:** Delete a translation suggestion.
Users can only delete their own pending suggestions.
Admins can delete any suggestion.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `suggestion_id` | integer | path | ✅ | The ID of the suggestion to delete |

**📥 Respostas:**

**204:** Successful Response
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/i18n/translation-suggestions/{suggestion_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### GET /api/i18n/translation-suggestions/{suggestion_id} {#get-apii18ntranslation-suggestionssuggestion-id}

**Resumo:** Read Translation Suggestion
**Descrição:** Retrieve a specific translation suggestion by ID.
Users can only view their own suggestions unless they are admins.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `suggestion_id` | integer | path | ✅ | The ID of the suggestion to retrieve |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TranslationSuggestionRead](#translationsuggestionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X GET "http://localhost:8000/api/i18n/translation-suggestions/{suggestion_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### PUT /api/i18n/translation-suggestions/{suggestion_id} {#put-apii18ntranslation-suggestionssuggestion-id}

**Resumo:** Update Translation Suggestion
**Descrição:** Update a translation suggestion.
Users can only update their own pending suggestions.
Admins can update any suggestion, including changing the status.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `suggestion_id` | integer | path | ✅ | The ID of the suggestion to update |

**📤 Corpo da Requisição:**

**Content-Type:** `application/json`
**Schema:** [TranslationSuggestionUpdate](#translationsuggestionupdate)

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TranslationSuggestionRead](#translationsuggestionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X PUT "http://localhost:8000/api/i18n/translation-suggestions/{suggestion_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"example": "data"}'
```

---

### POST /api/i18n/translation-suggestions/{suggestion_id}/approve {#post-apii18ntranslation-suggestionssuggestion-idapprove}

**Resumo:** Approve Translation Suggestion
**Descrição:** Approve a translation suggestion and create a translation from it.
Requires ADMIN system role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `suggestion_id` | integer | path | ✅ | The ID of the suggestion to approve |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TranslationSuggestionRead](#translationsuggestionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/i18n/translation-suggestions/{suggestion_id}/approve" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### POST /api/i18n/translation-suggestions/{suggestion_id}/reject {#post-apii18ntranslation-suggestionssuggestion-idreject}

**Resumo:** Reject Translation Suggestion
**Descrição:** Reject a translation suggestion.
Requires ADMIN system role.

**🔐 Autenticação:** Requerida

**📝 Parâmetros:**

| Nome | Tipo | Local | Obrigatório | Descrição |
|------|------|-------|-------------|-----------|
| `suggestion_id` | integer | path | ✅ | The ID of the suggestion to reject |

**📥 Respostas:**

**200:** Successful Response
  - **Content-Type:** `application/json`
  - **Schema:** [TranslationSuggestionRead](#translationsuggestionread)
**422:** Validation Error
  - **Content-Type:** `application/json`
  - **Schema:** [HTTPValidationError](#httpvalidationerror)

**💻 Exemplo cURL:**
```bash
curl -X POST "http://localhost:8000/api/i18n/translation-suggestions/{suggestion_id}/reject" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---
