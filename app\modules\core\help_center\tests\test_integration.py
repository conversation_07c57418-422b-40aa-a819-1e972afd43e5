"""
Testes de integração para o Help Center.
"""

import pytest
from fastapi.testclient import TestClient
from uuid import uuid4
import json

from app.main import app
from app.modules.core.users.models.user import User
from app.modules.core.help_center.models import Ticket, TicketStatus, TicketPriority, TicketCategory


@pytest.fixture
def client():
    """Fixture para cliente de teste."""
    return TestClient(app)


@pytest.fixture
async def test_user(db_session):
    """Fixture para usuário de teste."""
    user = User(
        id=uuid4(),
        email="<EMAIL>",
        full_name="Integration Test User",
        system_role="user",
        is_active=True
    )
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    return user


@pytest.fixture
async def admin_user(db_session):
    """Fixture para usuário admin."""
    user = User(
        id=uuid4(),
        email="<EMAIL>",
        full_name="Admin Integration User",
        system_role="admin",
        is_active=True
    )
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    return user


@pytest.fixture
def auth_headers(test_user):
    """Fixture para headers de autenticação."""
    # Mock JWT token - em teste real, geraria token válido
    return {"Authorization": f"Bearer mock_token_{test_user.id}"}


@pytest.fixture
def admin_auth_headers(admin_user):
    """Fixture para headers de autenticação admin."""
    return {"Authorization": f"Bearer mock_token_{admin_user.id}"}


class TestHelpCenterIntegration:
    """Testes de integração do Help Center."""

    def test_create_ticket_endpoint(self, client, auth_headers):
        """Testa endpoint de criação de ticket."""
        ticket_data = {
            "title": "Problema de integração",
            "description": "Descrição detalhada do problema",
            "category": "problem"
        }
        
        response = client.post(
            "/modules/core/help-center/tickets",
            json=ticket_data,
            headers=auth_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["title"] == ticket_data["title"]
        assert data["description"] == ticket_data["description"]
        assert data["category"] == ticket_data["category"]
        assert data["status"] == "new"
        assert data["priority"] == "medium"

    def test_list_tickets_endpoint(self, client, auth_headers):
        """Testa endpoint de listagem de tickets."""
        response = client.get(
            "/modules/core/help-center/tickets",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "tickets" in data
        assert "total" in data
        assert "page" in data
        assert "per_page" in data

    def test_get_ticket_endpoint(self, client, auth_headers, test_user, db_session):
        """Testa endpoint de obtenção de ticket."""
        # Criar ticket primeiro
        ticket = Ticket(
            id=uuid4(),
            title="Ticket para teste",
            description="Descrição do ticket",
            status=TicketStatus.NEW,
            priority=TicketPriority.MEDIUM,
            category=TicketCategory.QUESTION,
            user_id=test_user.id,
            is_read_by_user=True,
            is_read_by_admin=False
        )
        db_session.add(ticket)
        db_session.commit()
        
        response = client.get(
            f"/modules/core/help-center/tickets/{ticket.id}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == str(ticket.id)
        assert data["title"] == ticket.title

    def test_create_message_endpoint(self, client, auth_headers, test_user, db_session):
        """Testa endpoint de criação de mensagem."""
        # Criar ticket primeiro
        ticket = Ticket(
            id=uuid4(),
            title="Ticket para mensagem",
            description="Descrição",
            status=TicketStatus.NEW,
            priority=TicketPriority.MEDIUM,
            category=TicketCategory.QUESTION,
            user_id=test_user.id,
            is_read_by_user=True,
            is_read_by_admin=False
        )
        db_session.add(ticket)
        db_session.commit()
        
        message_data = {
            "message_content": "Esta é uma mensagem de teste",
            "message_type": "text"
        }
        
        response = client.post(
            f"/modules/core/help-center/tickets/{ticket.id}/messages",
            json=message_data,
            headers=auth_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["message_content"] == message_data["message_content"]
        assert data["message_type"] == message_data["message_type"]

    def test_list_messages_endpoint(self, client, auth_headers, test_user, db_session):
        """Testa endpoint de listagem de mensagens."""
        # Criar ticket primeiro
        ticket = Ticket(
            id=uuid4(),
            title="Ticket para mensagens",
            description="Descrição",
            status=TicketStatus.NEW,
            priority=TicketPriority.MEDIUM,
            category=TicketCategory.QUESTION,
            user_id=test_user.id,
            is_read_by_user=True,
            is_read_by_admin=False
        )
        db_session.add(ticket)
        db_session.commit()
        
        response = client.get(
            f"/modules/core/help-center/tickets/{ticket.id}/messages",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "messages" in data

    def test_admin_metrics_endpoint(self, client, admin_auth_headers):
        """Testa endpoint de métricas administrativas."""
        response = client.get(
            "/modules/core/help-center/admin/metrics",
            headers=admin_auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Verificar se todas as métricas esperadas estão presentes
        expected_metrics = [
            "total_tickets", "open_tickets", "resolved_tickets",
            "average_response_time_hours", "average_resolution_time_hours",
            "total_kb_articles", "resolution_rate"
        ]
        
        for metric in expected_metrics:
            assert metric in data

    def test_admin_list_tickets_endpoint(self, client, admin_auth_headers):
        """Testa endpoint administrativo de listagem de tickets."""
        response = client.get(
            "/modules/core/help-center/admin/tickets",
            headers=admin_auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "tickets" in data
        assert "total" in data

    def test_bulk_update_tickets_endpoint(self, client, admin_auth_headers, test_user, db_session):
        """Testa endpoint de atualização em lote."""
        # Criar alguns tickets
        ticket_ids = []
        for i in range(3):
            ticket = Ticket(
                id=uuid4(),
                title=f"Ticket {i}",
                description=f"Descrição {i}",
                status=TicketStatus.NEW,
                priority=TicketPriority.MEDIUM,
                category=TicketCategory.QUESTION,
                user_id=test_user.id,
                is_read_by_user=True,
                is_read_by_admin=False
            )
            db_session.add(ticket)
            ticket_ids.append(str(ticket.id))
        
        db_session.commit()
        
        bulk_data = {
            "ticket_ids": ticket_ids,
            "action": "status",
            "value": "resolved"
        }
        
        response = client.post(
            "/modules/core/help-center/admin/tickets/bulk-update",
            json=bulk_data,
            headers=admin_auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "updated_count" in data
        assert data["updated_count"] == 3

    def test_knowledge_base_articles_endpoint(self, client, auth_headers):
        """Testa endpoint de artigos da base de conhecimento."""
        response = client.get(
            "/modules/core/help-center/kb/articles",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "articles" in data

    def test_unauthorized_access(self, client):
        """Testa acesso não autorizado."""
        response = client.get("/modules/core/help-center/tickets")
        
        assert response.status_code == 401

    def test_admin_only_endpoint_access(self, client, auth_headers):
        """Testa acesso de usuário comum a endpoint de admin."""
        response = client.get(
            "/modules/core/help-center/admin/metrics",
            headers=auth_headers
        )
        
        assert response.status_code == 403

    def test_ticket_not_found(self, client, auth_headers):
        """Testa acesso a ticket inexistente."""
        nonexistent_id = str(uuid4())
        
        response = client.get(
            f"/modules/core/help-center/tickets/{nonexistent_id}",
            headers=auth_headers
        )
        
        assert response.status_code == 404

    def test_invalid_ticket_data(self, client, auth_headers):
        """Testa criação de ticket com dados inválidos."""
        invalid_data = {
            "title": "",  # Título vazio
            "description": "Descrição válida",
            "category": "invalid_category"  # Categoria inválida
        }
        
        response = client.post(
            "/modules/core/help-center/tickets",
            json=invalid_data,
            headers=auth_headers
        )
        
        assert response.status_code == 422

    def test_file_upload_endpoint(self, client, auth_headers, test_user, db_session):
        """Testa endpoint de upload de arquivo."""
        # Criar ticket primeiro
        ticket = Ticket(
            id=uuid4(),
            title="Ticket para upload",
            description="Descrição",
            status=TicketStatus.NEW,
            priority=TicketPriority.MEDIUM,
            category=TicketCategory.QUESTION,
            user_id=test_user.id,
            is_read_by_user=True,
            is_read_by_admin=False
        )
        db_session.add(ticket)
        db_session.commit()
        
        # Simular upload de arquivo
        files = {"file": ("test.txt", b"conteudo do arquivo", "text/plain")}
        
        response = client.post(
            f"/modules/core/help-center/tickets/{ticket.id}/upload",
            files=files,
            headers=auth_headers
        )
        
        # Pode retornar 201 (sucesso) ou erro dependendo da implementação
        assert response.status_code in [201, 400, 500]

    def test_pagination(self, client, auth_headers):
        """Testa paginação de tickets."""
        response = client.get(
            "/modules/core/help-center/tickets?page=1&per_page=5",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["page"] == 1
        assert data["per_page"] == 5

    def test_search_tickets(self, client, auth_headers):
        """Testa busca de tickets."""
        response = client.get(
            "/modules/core/help-center/tickets?search=problema",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "tickets" in data

    def test_filter_tickets_by_status(self, client, auth_headers):
        """Testa filtro de tickets por status."""
        response = client.get(
            "/modules/core/help-center/tickets?status=open",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "tickets" in data
