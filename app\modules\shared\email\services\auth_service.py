"""Authentication Service for Email module."""

import logging  # noqa: E402
import uuid
from datetime import datetime
from typing import Op<PERSON>

from sqlalchemy import select  # noqa: E402
from sqlalchemy.ext.asyncio import AsyncSession
import bcrypt

from app.modules.shared.email.models import EmailAccount  # noqa: E402

logger = logging.getLogger(__name__)


class EmailAuthService:
    """Service for email authentication."""

    async def hash_password(self, password: str) -> str:
        """Hash a password."""
        # Generate a salt and hash the password
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(password.encode(), salt)
        return hashed.decode()

    async def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a password against a hash."""
        return bcrypt.checkpw(plain_password.encode(), hashed_password.encode())

    async def authenticate(
        self, db: AsyncSession, email: str, password: str
    ) -> Optional[EmailAccount]:
        """Authenticate an email account."""
        # Get account
        result = await db.execute(select(EmailAccount).where(EmailAccount.full_email == email))
        account = result.scalars().first()

        if not account:
            return None

        # Verify password
        if not await self.verify_password(password, account.password_hash):
            return None

        # Update last login
        account.last_login = datetime.now()
        await db.commit()

        return account

    async def change_password(
        self, db: AsyncSession, account_id: uuid.UUID, new_password: str
    ) -> None:
        """Change password for an account."""
        # Get account
        account = await db.get(EmailAccount, account_id)
        if not account:
            raise ValueError(f"Account with ID {account_id} not found")

        # Hash new password
        account.password_hash = await self.hash_password(new_password)
        await db.commit()

    async def reset_password(
        self, db: AsyncSession, account_id: uuid.UUID, new_password: str
    ) -> None:
        """Reset password for an account (admin function)."""
        # This is the same as change_password, but doesn't require the old password
        await self.change_password(db, account_id, new_password)
