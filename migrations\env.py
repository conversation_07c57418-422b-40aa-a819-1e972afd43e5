import nest_asyncio
nest_asyncio.apply()
from dotenv import load_dotenv
load_dotenv()
import os
import sys
import importlib.util
from logging.config import fileConfig
import asyncio # Adicionado para async

# Modificado para importar create_async_engine e AsyncEngine
from sqlalchemy import pool
from sqlalchemy.ext.asyncio import create_async_engine, AsyncEngine

from alembic import context

# --- Adicionado: Ajuste do sys.path para encontrar o módulo 'app' ---
# Adiciona o diretório pai (raiz do projeto) ao sys.path
# Isso permite importar 'app.core.config' e 'app.db.base'
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)
# --------------------------------------------------------------------

# --- Adicionado: Importar configurações e Base ---
from app.core.config import settings
from app.db.base import Base  # Importa a Base de app.db.base

# --- Adicionado: Importar explicitamente TODOS os módulos com modelos ---
# Isso garante que Alembic descubra todas as tabelas e relacionamentos.

# Importar modelos específicos do core
import app.modules.core.users.models.user
import app.modules.core.tenants.models.tenant
import app.modules.core.users.models.tenant_user_association
import app.modules.core.roles.models.roles

# Importações de i18n
import app.modules.core.i18n.models.language
import app.modules.core.i18n.models.translation
import app.modules.core.i18n.models.translation_suggestion

# Importações de menu (restaurantes)
import app.modules.tenants.restaurants.menu.models.digital_menu
import app.modules.tenants.restaurants.menu.models.menu_category
import app.modules.tenants.restaurants.menu.models.menu_item

# Importações do novo módulo de funções
import app.modules.core.functions.customizations.models

# Importações de módulos shared
import app.modules.core.functions.inventory.models.inventory_item

import app.modules.shared.shopping_list.models.shopping_list
import app.modules.shared.shopping_list.models.shopping_list_category

# Importações de financial
try:
    import app.modules.tenants.shared.financial.models.transaction
    import app.modules.tenants.shared.financial.models.payment_method
    import app.modules.tenants.shared.financial.models.invoice
except ImportError:
    pass

# Importações de supplier
try:
    import app.modules.shared.supplier.models.supplier
    import app.modules.shared.supplier.models.supplier_product
    import app.modules.shared.supplier.models.purchase_order
    import app.modules.shared.supplier.models.purchase_order_item
except ImportError:
    pass

# Importações de POS/Tables
try:
    import app.modules.tenants.restaurants.pos.models.table
    import app.modules.tenants.restaurants.pos.models.zone
    import app.modules.tenants.restaurants.pos.models.order
    import app.modules.tenants.restaurants.pos.models.order_item
except ImportError:
    pass

# Importações de media/system
try:
    import app.modules.core.media.models.media
    import app.modules.core.system.models.system_config
except ImportError:
    pass

# Importações do Help Center
try:
    import app.modules.core.help_center.models.ticket
    import app.modules.core.help_center.models.ticket_message
    import app.modules.core.help_center.models.knowledge_base_article
    import app.modules.core.help_center.models.file_upload
except ImportError:
    pass

# Importações do Eshop
try:
    import app.modules.core.eshop.models.product
    import app.modules.core.eshop.models.eshop_category
    import app.modules.core.eshop.models.product_category
    import app.modules.core.eshop.models.product_review
    import app.modules.core.eshop.models.product_approval
    import app.modules.core.eshop.models.commission_settings
    import app.modules.core.eshop.models.auction_lottery
    import app.modules.core.eshop.models.tcostumer
    import app.modules.core.eshop.models.tvendor_supplier
except ImportError:
    pass

# Importações do Cart e Checkout
try:
    from app.modules.core.functions.cart.models.cart import Cart, CartItem, CartStatus
    from app.modules.core.functions.checkout.models.checkout import CheckoutSession, CheckoutStatus, PaymentMethod, ShippingMethod
    print("Successfully imported Cart/Checkout models")
except ImportError as e:
    print(f"Warning: Could not import Cart/Checkout models: {e}")
    pass

# Importações de Auction/Lottery - Temporariamente desabilitado devido a importação circular
# try:
#     from app.modules.core.eshop.models.auction_lottery import Auction, AuctionBid, Lottery, LotteryTicket
# except ImportError as e:
#     print(f"Warning: Could not import Auction/Lottery models: {e}")
#     pass

# Importações do SEO
try:
    import app.modules.core.seo.models.seo_meta
    import app.modules.core.seo.models.url_slug
    import app.modules.core.seo.models.sitemap_entry
except ImportError:
    pass

# Importações de Financial/Invoices
try:
    import app.modules.shared.financial.invoices.models.invoice
except ImportError:
    pass

# Importações de Reviews
try:
    import app.modules.core.functions.reviews.models.review
    import app.modules.core.functions.reviews.models.review_metrics
    import app.modules.core.functions.reviews.models.review_moderation
    import app.modules.core.functions.reviews.models.review_rank
except ImportError:
    pass

# Importações do Couponic (migrado para eshop)
# try:
#     import app.modules.core.couponic.models.product
#     import app.modules.core.couponic.models.category
#     import app.modules.shared.couponic.models.seller_quality
# except ImportError:
#     pass

# Importações do Shipping
try:
    import app.modules.shared.shipping.models.shipping
except ImportError:
    pass

# Importações do Offerts
try:
    import app.modules.shared.offerts.models.offerts
except ImportError:
    pass

# Tente importar outros módulos apenas se existirem
try:
    import app.modules.core.custom_domains.models
except ImportError:
    pass

try:
    import app.modules.core.subscriptions.models
except ImportError:
    pass

try:
    import app.modules.core.payments.models
except ImportError:
    pass
# --------------------------------------------------------------------

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# --- Modificado: Configurar a URL do banco de dados a partir das settings ---
# Define a URL do banco de dados no objeto de configuração do Alembic
# Isso garante que tanto o modo offline quanto online usem a URL correta.
# Garante que a URL seja uma string
db_url = str(settings.DATABASE_URL)
config.set_main_option('sqlalchemy.url', db_url)
# --------------------------------------------------------------------------

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# --- Comentário Atualizado ---
# A descoberta agora é feita pelas importações explícitas acima.
# ----------------------------

# --- Modificado: Definir target_metadata ---
# add your model's MetaData object here
# for 'autogenerate' support
target_metadata = Base.metadata
# ------------------------------------------

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.
    """
    # url já está configurado no objeto config acima
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        # Inclui a convenção de nomenclatura aqui também
        render_as_batch=False, # Alterado para False
        compare_type=True, # Compara tipos de coluna
        naming_convention=Base.metadata.naming_convention,
    )

    with context.begin_transaction():
        context.run_migrations()

# --- Modificado para Async ---
def do_run_migrations(connection):
    """Função auxiliar para configurar e executar migrações."""
    context.configure(
        connection=connection,
        target_metadata=target_metadata,
        render_as_batch=False, # Alterado para False
        compare_type=True, # Compara tipos de coluna
        naming_convention=Base.metadata.naming_convention,
    )

    with context.begin_transaction():
        context.run_migrations()

async def run_migrations_online() -> None:
    """Run migrations in 'online' mode using an async engine."""

    # Cria a engine assíncrona usando a URL das settings
    connectable = create_async_engine(
        db_url, # Usa a URL já definida
        poolclass=pool.NullPool,
    )

    # Conecta de forma assíncrona
    async with connectable.connect() as connection:
        # Executa a configuração e migração dentro do contexto da conexão
        await connection.run_sync(do_run_migrations)

    # Descarta a engine após o uso
    await connectable.dispose()
# --- Fim da Modificação Async ---


if context.is_offline_mode():
    run_migrations_offline()
else:
    # Modificado para usar asyncio.run
    asyncio.run(run_migrations_online())
