'use client';

import { useState, useMemo, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import {
  ExclamationTriangleIcon,
  XMarkIcon,
  TagIcon
} from '@heroicons/react/24/outline';
import {
  usePublicMenu,
  PublicMenuItem,
  PublicMenuCategory
} from '@/hooks/usePublicMenu';
import { useCart } from '@/contexts/CartContext';
import { ItemDetailModal } from './ItemDetailModal';
import { RestaurantHeader } from './RestaurantHeader';
import { FloatingActions } from './FloatingActions';
import { FloatingCart } from './FloatingCart';
import { CartSidebar } from './CartSidebar';
import { CategoryMenuGrid } from './CategoryMenuGrid';
import { MenuTabSelector } from './MenuTabSelector';
import { MicroFooter } from './MicroFooter';
import { WhatsAppButton } from './WhatsAppButton';
import { BackToTopButton } from './BackToTopButton';
import { SearchActionBar } from './SearchActionBar';
import { ReviewsSection } from '@/components/reviews/ReviewsSection';

interface PublicMenuViewerProps {
  tenantSlug: string;
  menuId?: string;
  tableNumber?: string;
  language?: string;
  className?: string;
}

export function PublicMenuViewer({
  tenantSlug,
  menuId,
  tableNumber,
  language,
  className = ''
}: PublicMenuViewerProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Use menuId from URL if available, otherwise use prop
  const currentMenuIdFromUrl = searchParams.get('menu') || menuId;

  const {
    menu,
    menus,
    categories,
    items,
    loading,
    error,
    formatPrice,
    getCategoryItems,
    filterItems
  } = usePublicMenu(tenantSlug, currentMenuIdFromUrl);

  const { addItem } = useCart();

  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);


  const [searchTerm, setSearchTerm] = useState('');
  const [selectedItem, setSelectedItem] = useState<PublicMenuItem | null>(null);
  const [isCategoriesSticky, setIsCategoriesSticky] = useState(false);

  // Filtrar itens baseado na categoria selecionada e termo de busca (memoizado)
  const filteredItems = useMemo(() => {
    // Se não há categoria selecionada, mostrar todos os items disponíveis
    if (!selectedCategory && !searchTerm) {
      return items.filter(item => item.is_available && item.is_active);
    }
    return filterItems(searchTerm, selectedCategory);
  }, [filterItems, searchTerm, selectedCategory, items]);

  // Memoizar função de seleção de menu para evitar re-renders
  const handleMenuSelect = useCallback((menuId: string) => {
    // Use Next.js router to update URL and trigger re-render
    const params = new URLSearchParams(searchParams.toString());
    params.set('menu', menuId);
    router.push(`/${tenantSlug}?${params.toString()}`);
  }, [router, tenantSlug, searchParams]);





  // Organizar categorias hierarquicamente
  const organizedCategories = useMemo(() => {
    const topLevelCategories = categories.filter(cat => !cat.parent_id);

    const buildCategoryTree = (category: PublicMenuCategory): PublicMenuCategory => {
      const children = categories.filter(cat => cat.parent_id === category.id);
      return {
        ...category,
        subcategories: children.map(buildCategoryTree)
      };
    };

    return topLevelCategories.map(buildCategoryTree);
  }, [categories]);

  const handleAddToCart = (item: PublicMenuItem, quantity: number, customizations?: any) => {
    addItem(item, quantity, customizations);
  };

  // Detect when categories become sticky
  useEffect(() => {
    const handleScroll = () => {
      // Categories become sticky when we scroll past the header
      const scrollY = window.scrollY;
      setIsCategoriesSticky(scrollY > 200);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-gray-900 border-t-transparent rounded-full animate-spin mx-auto mb-3"></div>
          <p className="text-gray-600 text-sm font-medium">Carregando menu...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white">
        <div className="text-center max-w-md mx-auto px-6">
          <ExclamationTriangleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Ops! Algo deu errado</h2>
          <p className="text-gray-600 mb-6 text-sm">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-gray-900 hover:bg-gray-800 text-white font-medium py-2 px-6 rounded-lg transition-colors text-sm"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  if (!menu) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white">
        <div className="text-center max-w-md mx-auto px-6">
          <TagIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Menu não encontrado</h2>
          <p className="text-gray-600 text-sm">O menu solicitado não está disponível no momento.</p>
        </div>
      </div>
    );
  }

  // Extrair o ID do tenant do objeto menu, se disponível
  const tenantId = menu?.tenant_id;

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-amber-50 relative overflow-hidden">
      {/* Animated Background Elements */}
      {/* Large floating elements */}
      <motion.div
        className="absolute top-10 left-10 w-24 h-24 bg-gradient-to-br from-orange-200 to-orange-300 rounded-full opacity-25 blur-sm"
        animate={{
          y: [0, -30, 0],
          x: [0, 15, 0],
          scale: [1, 1.2, 1],
          rotate: [0, 180, 360],
        }}
        transition={{
          duration: 12,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      <motion.div
        className="absolute top-1/4 right-16 w-20 h-20 bg-gradient-to-br from-amber-300 to-yellow-400 rounded-full opacity-30 blur-sm"
        animate={{
          y: [0, 25, 0],
          x: [0, -20, 0],
          scale: [1, 0.8, 1],
          rotate: [0, -180, -360],
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1
        }}
      />

      <motion.div
        className="absolute bottom-20 left-1/4 w-16 h-16 bg-gradient-to-br from-red-300 to-orange-400 rounded-full opacity-25 blur-sm"
        animate={{
          y: [0, -35, 0],
          x: [0, 25, 0],
          scale: [1, 1.3, 1],
          rotate: [0, 90, 180],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 2
        }}
      />

      <motion.div
        className="absolute bottom-10 right-10 w-28 h-28 bg-gradient-to-br from-yellow-200 to-amber-300 rounded-full opacity-20 blur-md"
        animate={{
          y: [0, 15, 0],
          x: [0, -12, 0],
          scale: [1, 0.9, 1],
          rotate: [0, 270, 360],
        }}
        transition={{
          duration: 14,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 0.5
        }}
      />

      {/* Medium floating elements */}
      <motion.div
        className="absolute top-1/3 left-1/3 w-14 h-14 bg-gradient-to-br from-orange-300 to-red-400 rounded-full opacity-20 blur-sm"
        animate={{
          y: [0, -20, 0],
          x: [0, 18, 0],
          scale: [1, 1.1, 1],
          rotate: [0, -90, -180],
        }}
        transition={{
          duration: 9,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 3
        }}
      />

      <motion.div
        className="absolute top-2/3 right-1/3 w-12 h-12 bg-gradient-to-br from-amber-300 to-orange-400 rounded-full opacity-25 blur-sm"
        animate={{
          y: [0, 22, 0],
          x: [0, -16, 0],
          scale: [1, 0.85, 1],
          rotate: [0, 120, 240],
        }}
        transition={{
          duration: 11,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 4
        }}
      />

      {/* Small floating elements */}
      <motion.div
        className="absolute top-16 right-1/4 w-8 h-8 bg-gradient-to-br from-yellow-300 to-orange-400 rounded-full opacity-30 blur"
        animate={{
          y: [0, -15, 0],
          x: [0, 12, 0],
          scale: [1, 1.4, 1],
          rotate: [0, 180, 360],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1.5
        }}
      />

      <motion.div
        className="absolute bottom-1/3 left-16 w-10 h-10 bg-gradient-to-br from-red-300 to-orange-400 rounded-full opacity-25 blur"
        animate={{
          y: [0, 18, 0],
          x: [0, -14, 0],
          scale: [1, 0.9, 1],
          rotate: [0, -150, -300],
        }}
        transition={{
          duration: 7,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 2.5
        }}
      />

      <motion.div
        className="absolute top-1/2 left-1/2 w-6 h-6 bg-gradient-to-br from-amber-300 to-yellow-400 rounded-full opacity-35 blur"
        animate={{
          y: [0, -12, 0],
          x: [0, 10, 0],
          scale: [1, 1.5, 1],
          rotate: [0, 360, 720],
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 3.5
        }}
      />

      <motion.div
        className="absolute top-3/4 left-20 w-7 h-7 bg-gradient-to-br from-orange-300 to-red-400 rounded-full opacity-30 blur-sm"
        animate={{
          y: [0, -14, 0],
          x: [0, 11, 0],
          scale: [1, 1.3, 1],
          rotate: [0, 90, 180],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1.8
        }}
      />

      {/* Content with higher z-index */}
      <div className="relative z-10">
        {/* Header do Restaurante */}
        <RestaurantHeader
          restaurantName={(menu as any).tenant_name || tenantSlug.replace('-', ' ')}
          menuName={menu.name}
          menuDescription={menu.description}
          isScheduleEnabled={menu.schedule_enabled}
          tableNumber={tableNumber}
          language={language}
          logoUrl={menu.logo_url}
          coverImageUrl={menu.cover_image_url}
          restaurantInfo={{
            rating: menu.rating,
            totalReviews: menu.total_reviews,
            address: menu.address,
            phone: menu.phone,
            website: menu.website,
            isOpen: menu.is_open,
            openingHours: menu.opening_hours,
            facebook_url: menu.facebook_url,
            instagram_url: menu.instagram_url,
            twitter_url: menu.twitter_url,
            youtube_url: menu.youtube_url,
          }}
        />

        {/* Seção de Avaliações do Restaurante */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <ReviewsSection entityType="tenant" entityId={tenantSlug} />
        </div>

        {/* Search and Action Bar */}
        <SearchActionBar
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          restaurantInfo={{
            phone: menu?.phone || "11999999999", // Debug: força um telefone
            address: menu?.address || "Rua Exemplo, 123 - São Paulo", // Debug: força um endereço
            wifi: {
              name: "RestauranteWiFi",
              password: "senha123"
            }
          }}
        />

        {/* Seletor de Menus (se houver múltiplos) */}
        {menus.length > 1 && (
          <div className="max-w-6xl mx-auto px-4 py-4">
            <MenuTabSelector
              menus={menus}
              selectedMenuId={menu?.id || currentMenuIdFromUrl}
              onMenuSelect={handleMenuSelect}
            />
          </div>
        )}

        {/* Navegação de Categorias Gastronômicas */}
        <div className={`sticky top-0 z-30 py-3 transition-all duration-300 ${
          isCategoriesSticky
            ? 'bg-white/80 backdrop-blur-lg border-b border-orange-100/30 shadow-lg'
            : ''
        }`}>
          <div className="max-w-6xl mx-auto px-4">
            <div className="flex justify-center overflow-x-auto scrollbar-hide gap-3 py-2">
              <button
                onClick={() => setSelectedCategory(null)}
                className={`flex-shrink-0 px-6 py-3 rounded-full text-sm font-medium transition-all duration-300
                         border shadow-sm hover:shadow-md transform hover:scale-105 ${
                  !selectedCategory
                    ? 'bg-gradient-to-r from-orange-500 to-amber-500 text-white border-orange-400 shadow-orange-200'
                    : 'bg-white text-gray-600 border-gray-200 hover:bg-orange-50 hover:text-orange-700 hover:border-orange-200'
                }`}
              >
                🍽️ Todos
              </button>
              {organizedCategories.map(category => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex-shrink-0 px-6 py-3 rounded-full text-sm font-medium transition-all duration-300
                           border shadow-sm hover:shadow-md transform hover:scale-105 ${
                    selectedCategory === category.id
                      ? 'bg-gradient-to-r from-orange-500 to-amber-500 text-white border-orange-400 shadow-orange-200'
                      : 'bg-white text-gray-600 border-gray-200 hover:bg-orange-50 hover:text-orange-700 hover:border-orange-200'
                  }`}
                >
                  {category.name}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Layout Principal - Grid de Cards */}
        <div className="max-w-6xl mx-auto px-4 py-4">
          <CategoryMenuGrid
            items={filteredItems}
            categories={categories}
            selectedCategory={selectedCategory}
            searchTerm={searchTerm}
            formatPrice={formatPrice}
            onItemClick={setSelectedItem}
            onClearSearch={() => setSearchTerm('')}
          />
        </div>

        {/* Ações Flutuantes Mobile */}
        <FloatingActions
          categories={categories}
          selectedCategory={selectedCategory}
          onCategorySelect={setSelectedCategory}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
        />

        {/* Carrinho Flutuante */}
        <FloatingCart formatPrice={formatPrice} />

        {/* Sidebar do Carrinho */}
        <CartSidebar formatPrice={formatPrice} />

        {/* Modal de detalhes do item */}
        <ItemDetailModal
          item={selectedItem}
          isOpen={!!selectedItem}
          onClose={() => setSelectedItem(null)}
          formatPrice={formatPrice}
          onAddToCart={handleAddToCart}
        />

          {/* MicroFooter */}
          <MicroFooter />
      </div>

      {/* Floating Buttons - Outside content div for proper z-index */}
      <WhatsAppButton
        phone={menu?.phone || "11999999999"}
        message={`Olá! Gostaria de fazer um pedido no ${tenantSlug.replace('-', ' ')}.`}
        restaurantName={menu?.name || tenantSlug.replace('-', ' ')}
      />
      <BackToTopButton />
    </div>
  );
}
