import uuid
from pydantic import BaseModel, Field, ConfigDict
from typing import Optional

class InventoryItemBase(BaseModel):
    product_id: uuid.UUID = Field(..., description="The ID of the product this inventory item refers to.")
    variant_id: Optional[uuid.UUID] = Field(None, description="The ID of the product variant, if applicable.")
    quantity: int = Field(..., ge=0, description="The current quantity in stock.")
    low_stock_threshold: int = Field(..., ge=0, description="The threshold at which a low stock alert is triggered.")

class InventoryItemCreate(InventoryItemBase):
    pass

class InventoryItemUpdate(BaseModel):
    quantity: Optional[int] = Field(None, ge=0, description="The new quantity in stock.")
    low_stock_threshold: Optional[int] = Field(None, ge=0, description="The new low stock threshold.")

class InventoryItemRead(InventoryItemBase):
    id: uuid.UUID
    tenant_id: uuid.UUID
    
    model_config = ConfigDict(from_attributes=True) 