"""
Schemas for the i18n module.
"""

from app.modules.core.i18n.schemas.language import (
    LanguageBase,
    LanguageCreate,
    LanguageUpdate,
    LanguageRead,
    LanguageVersionCheck,
)
from app.modules.core.i18n.schemas.translation import (
    TranslationBase,
    TranslationCreate,
    TranslationUpdate,
    TranslationRead,
    TranslationBySectorRead,
    TranslationChangeRead,
    SectorChangesRead,
    LanguageChangesRead,
)
from app.modules.core.i18n.schemas.translation_suggestion import (
    TranslationSuggestionBase,
    TranslationSuggestionCreate,
    TranslationSuggestionUpdate,
    TranslationSuggestionRead,
)
from app.modules.core.i18n.schemas.translation_change import (
    TranslationChangeBase,
    TranslationChangeCreate,
    TranslationChangeRead,
    TranslationChangeFilter,
    SectorChanges,
    LanguageChanges,
)
from app.modules.core.i18n.schemas.translation_key import (
    TranslationKeyBase,
    TranslationKeyCreate,
    TranslationKeyUpdate,
    TranslationKeyRead,
)

__all__ = [
    "LanguageBase",
    "LanguageCreate",
    "LanguageUpdate",
    "LanguageRead",
    "LanguageVersionCheck",
    "TranslationBase",
    "TranslationCreate",
    "TranslationUpdate",
    "TranslationRead",
    "TranslationBySectorRead",
    "TranslationChangeRead",
    "SectorChangesRead",
    "LanguageChangesRead",
    "TranslationSuggestionBase",
    "TranslationSuggestionCreate",
    "TranslationSuggestionUpdate",
    "TranslationSuggestionRead",
    "TranslationChangeBase",
    "TranslationChangeCreate",
    "TranslationChangeRead",
    "TranslationChangeFilter",
    "SectorChanges",
    "LanguageChanges",
    "TranslationKeyBase",
    "TranslationKeyCreate",
    "TranslationKeyUpdate",
    "TranslationKeyRead",
]
