"""
Main API endpoints for tenant settings management.
"""

import uuid
from typing import List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db_dependencies import get_db
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    get_current_tenant_from_header as get_current_tenant,
    require_tenant_role,
)
from app.modules.core.roles.models.roles import RolePermissions
from app.core.exceptions import BusinessLogicError, NotFoundError
from app.modules.core.tenants.models.tenant import Tenant
from app.modules.core.users.models.user import User

from app.modules.core.tenants.schemas.tenant_settings import (
    TenantSettingsRead,
    TenantSettingsUpdate,
)
from app.modules.core.tenants.services.tenant_settings import (
    tenant_settings_service,
)

router = APIRouter()


@router.get(
    "/",
    response_model=TenantSettingsRead,
    status_code=status.HTTP_200_OK,
    summary="Get Tenant Settings",
    description="Get current tenant settings configuration.",
)
async def get_tenant_settings(
    *,
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role(
            required_roles=RolePermissions.STAFF_ROLES, 
            tenant_id_source="header"
        )
    ),
) -> TenantSettingsRead:
    """
    Get current tenant settings configuration.
    Requires STAFF, MANAGER, or OWNER role.
    """
    try:
        settings = await tenant_settings_service.get_settings(
            db, current_tenant.id
        )
        return settings
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving tenant settings: {str(e)}"
        )


@router.put(
    "/",
    response_model=TenantSettingsRead,
    status_code=status.HTTP_200_OK,
    summary="Update Tenant Settings",
    description="Update tenant settings configuration.",
)
async def update_tenant_settings(
    *,
    db: AsyncSession = Depends(get_db),
    settings_in: TenantSettingsUpdate,
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role(
            required_roles=RolePermissions.ADMIN_ROLES, 
            tenant_id_source="header"
        )
    ),
) -> TenantSettingsRead:
    """
    Update tenant settings configuration.
    Requires MANAGER or OWNER role.
    """
    try:
        settings = await tenant_settings_service.update_settings(
            db, current_tenant.id, settings_in
        )
        return settings
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except BusinessLogicError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.post(
    "/sync-tenant-data",
    response_model=TenantSettingsRead,
    status_code=status.HTTP_200_OK,
    summary="Sync Tenant Data",
    description="Manually sync tenant data from Tenant model to TenantSettings.",
)
async def sync_tenant_data(
    *,
    db: AsyncSession = Depends(get_db),
    current_tenant: Tenant = Depends(get_current_tenant),
    current_user: User = Depends(
        require_tenant_role(
            required_roles=RolePermissions.ADMIN_ROLES,
            tenant_id_source="header"
        )
    ),
) -> TenantSettingsRead:
    """
    Manually sync tenant data from Tenant model to TenantSettings.
    This ensures data consistency between the two models.
    Requires MANAGER or OWNER role.
    """
    try:
        settings = await tenant_settings_service.get_settings(
            db, current_tenant.id
        )
        return settings
    except NotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error syncing tenant data: {str(e)}"
        )
