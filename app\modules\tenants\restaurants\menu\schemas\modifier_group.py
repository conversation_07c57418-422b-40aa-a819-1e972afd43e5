import uuid  # Import uuid
from pydantic import (
    BaseModel,
    ConfigDict,
    Field,
    model_validator,
)  # Adicionado model_validator
from typing import Optional, List, Union  # noqa: E402

# Import the schema for the options
from .modifier_option import (  # noqa: E402
    ModifierOptionRead,
    ModifierOptionCreate,
)  # Need Create for nested creation


class ModifierGroupBase(BaseModel):
    """Base schema for Modifier Group."""

    name: str = Field(
        ...,
        max_length=100,
        description="Name of the modifier group (e.g., Add-ons, Sauces)",
    )
    description: Optional[str] = Field(
        None, max_length=255, description="Optional description for templates"
    )
    # Min selection can be 0 for optional modifiers
    min_selection: int = Field(
        0, ge=0, description="Minimum number of options that must/can be selected"
    )
    # Max selection often > 1 for things like add-ons
    max_selection: int = Field(
        1, ge=1, description="Maximum number of options that can be selected"
    )
    display_order: int = Field(
        0, description="Order of this group relative to other groups for the item"
    )
    is_required: bool = Field(False, description="Is this group required for the item?")
    is_active: bool = Field(True, description="Is this group active for the item?")

    @model_validator(mode="after")
    def check_max_ge_min(self) -> "ModifierGroupBase":
        if self.max_selection < self.min_selection:
            raise ValueError("max_selection must be greater than or equal to min_selection")
        return self


class ModifierGroupCreate(ModifierGroupBase):
    """Schema for creating a new Modifier Group, potentially with options."""

    id: Optional[Union[uuid.UUID, str]] = Field(None, description="Optional ID for existing groups")
    # menu_item_id will be a path parameter or inferred
    # tenant_id will be added by the service
    options: List[ModifierOptionCreate] = Field(
        [], description="List of options to create within this group"
    )


class ModifierGroupUpdate(ModifierGroupBase):
    """Schema for updating an existing Modifier Group. All fields optional."""

    name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = Field(None, max_length=255)
    min_selection: Optional[int] = Field(None, ge=0)
    max_selection: Optional[int] = Field(None, ge=1)
    display_order: Optional[int] = None
    is_required: Optional[bool] = None
    is_active: Optional[bool] = None
    # Updating options might be handled by separate endpoints
    # options: Optional[List[Union[ModifierOptionUpdate, ModifierOptionCreate, int]]] = None

    @model_validator(mode="after")
    def check_max_ge_min_update_v2(self) -> "ModifierGroupUpdate":
        if self.min_selection is not None and self.max_selection is not None:
            if self.max_selection < self.min_selection:
                raise ValueError(
                    "max_selection must be greater than or equal to min_selection if both are provided"  # noqa: E501
                )
        return self


class ModifierGroupRead(ModifierGroupBase):
    """Schema for reading a Modifier Group, including its options."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    menu_item_id: Optional[uuid.UUID] = None  # Nullable for template groups
    options: List[ModifierOptionRead] = []  # Include the options

    model_config = ConfigDict(from_attributes=True)


# Simplified version without options for list performance
class ModifierGroupReadSimple(BaseModel):
    """Schema for reading a Modifier Group without options for list performance."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    menu_item_id: Optional[uuid.UUID] = None  # Nullable for template groups
    name: str
    description: Optional[str] = None
    min_selection: int
    max_selection: int
    display_order: int
    is_required: bool = False
    is_active: bool = True
    usage_count: Optional[int] = 0

    model_config = ConfigDict(from_attributes=True)


# Version with options for template functionality
class ModifierGroupReadWithOptions(BaseModel):
    """Schema for reading a Modifier Group with options."""

    id: uuid.UUID
    tenant_id: uuid.UUID
    menu_item_id: Optional[uuid.UUID] = None  # Nullable for template groups
    name: str
    description: Optional[str] = None
    min_selection: int
    max_selection: int
    display_order: int
    is_required: bool = False
    is_active: bool = True
    options: List[ModifierOptionRead] = []  # Include the options
    usage_count: Optional[int] = 0

    model_config = ConfigDict(from_attributes=True)


# No template-specific schemas needed anymore
# Groups are now shared directly without template/instance distinction
