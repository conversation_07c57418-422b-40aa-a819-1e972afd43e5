'use client';

import { useState, useRef } from 'react';
import Image from 'next/image';
import { PhotoIcon, XMarkIcon, ArrowUpTrayIcon } from '@heroicons/react/24/outline';

interface ImageUploadProps {
  label: string;
  description: string;
  currentImage?: string;
  onImageUpload: (file: File) => void;
  onImageRemove: () => void;
  aspectRatio?: 'square' | 'wide' | 'auto';
  maxSize?: number; // in MB
  acceptedFormats?: string[];
}

export default function ImageUpload({
  label,
  description,
  currentImage,
  onImageUpload,
  onImageRemove,
  aspectRatio = 'auto',
  maxSize = 5,
  acceptedFormats = ['image/jpeg', 'image/png', 'image/webp']
}: ImageUploadProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (file: File) => {
    setError(null);
    
    // Validate file type
    if (!acceptedFormats.includes(file.type)) {
      setError(`Please select a valid image format: ${acceptedFormats.map(f => f.split('/')[1]).join(', ')}`);
      return;
    }

    // Validate file size
    if (file.size > maxSize * 1024 * 1024) {
      setError(`File size must be less than ${maxSize}MB`);
      return;
    }

    setIsUploading(true);
    try {
      await onImageUpload(file);
    } catch (err) {
      setError('Failed to upload image. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const getAspectRatioClass = () => {
    switch (aspectRatio) {
      case 'square':
        return 'w-[300px] h-[300px]';
      case 'wide':
        return 'h-[300px] w-full';
      default:
        return 'min-h-[200px]';
    }
  };

  return (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label}
      </label>
      
      <div className={`relative border-2 border-dashed rounded-lg ${getAspectRatioClass()} ${
        isDragging 
          ? 'border-primary-400 bg-primary-50' 
          : currentImage 
            ? 'border-gray-200' 
            : 'border-gray-300'
      }`}>
        {currentImage ? (
          // Current Image Display
          <div className="relative w-full h-full">
            <Image
              src={currentImage}
              alt={label}
              fill
              className="object-cover rounded-lg"
            />
            
            {/* Overlay with actions */}
            <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-50 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 hover:opacity-100">
              <div className="flex space-x-2">
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="px-3 py-2 bg-white text-gray-700 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors"
                >
                  <ArrowUpTrayIcon className="h-4 w-4 inline mr-1" />
                  Replace
                </button>
                <button
                  onClick={onImageRemove}
                  className="px-3 py-2 bg-red-600 text-white rounded-md text-sm font-medium hover:bg-red-700 transition-colors"
                >
                  <XMarkIcon className="h-4 w-4 inline mr-1" />
                  Remove
                </button>
              </div>
            </div>
          </div>
        ) : (
          // Upload Area
          <div
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onClick={() => fileInputRef.current?.click()}
            className="w-full h-full flex flex-col items-center justify-center cursor-pointer hover:bg-gray-50 transition-colors rounded-lg"
          >
            {isUploading ? (
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-2"></div>
                <p className="text-sm text-gray-600">Uploading...</p>
              </div>
            ) : (
              <div className="text-center">
                <PhotoIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <p className="text-sm font-medium text-gray-900 mb-1">
                  Click to upload or drag and drop
                </p>
                <p className="text-xs text-gray-500">
                  {acceptedFormats.map(f => f.split('/')[1].toUpperCase()).join(', ')} up to {maxSize}MB
                </p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Description */}
      <p className="mt-2 text-xs text-gray-500">
        {description}
      </p>

      {/* Error Message */}
      {error && (
        <p className="mt-2 text-sm text-red-600">
          {error}
        </p>
      )}

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={acceptedFormats.join(',')}
        onChange={handleFileInputChange}
        className="hidden"
      />
    </div>
  );
}
