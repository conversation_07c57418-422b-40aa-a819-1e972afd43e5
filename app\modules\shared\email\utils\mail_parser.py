"""Mail Parser utility for Email module."""

import logging  # noqa: E402
import email
import os
from email.message import Message
from email.header import decode_header
from email.utils import parseaddr
from typing import Tuple, List, Dict, Any, Optional

logger = logging.getLogger(__name__)


def decode_str(s: str) -> str:
    """Decode a string that might contain encoded words."""
    if not s:
        return ""

    decoded_parts = []
    for part, encoding in decode_header(s):
        if isinstance(part, bytes):
            if encoding:
                try:
                    decoded_parts.append(part.decode(encoding))
                except UnicodeDecodeError:
                    # Fallback to utf-8 if the specified encoding fails
                    try:
                        decoded_parts.append(part.decode("utf-8"))
                    except UnicodeDecodeError:
                        # Last resort: decode with errors ignored
                        decoded_parts.append(part.decode("utf-8", errors="ignore"))
            else:
                # No encoding specified, try utf-8
                try:
                    decoded_parts.append(part.decode("utf-8"))
                except UnicodeDecodeError:
                    # Fallback to latin-1 which should never fail
                    decoded_parts.append(part.decode("latin-1"))
        else:
            # Already a string
            decoded_parts.append(part)

    return "".join(decoded_parts)


def get_email_address(header_value: str) -> str:
    """Extract email address from a header value."""
    name, addr = parseaddr(header_value)
    return addr


def parse_email(
    file_path: str,
) -> Tuple[Optional[str], Optional[str], List[Dict[str, Any]]]:
    """Parse an email file and extract content and attachments.

    Returns:
        Tuple containing:
        - HTML body (if present)
        - Plain text body (if present)
        - List of attachments
    """
    try:
        with open(file_path, "rb") as f:
            msg = email.message_from_binary_file(f)

        return _parse_message(msg)
    except Exception as e:
        logger.error(f"Error parsing email file {file_path}: {e}")
        return None, None, []


def _parse_message(
    msg: Message,
) -> Tuple[Optional[str], Optional[str], List[Dict[str, Any]]]:
    """Parse an email message and extract content and attachments."""
    html_body = None
    text_body = None
    attachments = []

    # Process each part of the message
    if msg.is_multipart():
        for part in msg.walk():
            content_type = part.get_content_type()
            content_disposition = part.get("Content-Disposition", "")

            # Skip container multipart parts
            if content_type.startswith("multipart/"):
                continue

            # Handle attachments
            if "attachment" in content_disposition:
                filename = decode_str(part.get_filename(""))
                if filename:
                    payload = part.get_payload(decode=True)
                    attachments.append(
                        {
                            "filename": filename,
                            "content_type": content_type,
                            "size": len(payload),
                            "content": payload,
                        }
                    )
                continue

            # Handle text parts
            payload = part.get_payload(decode=True)
            if payload:
                charset = part.get_content_charset() or "utf-8"
                try:
                    decoded_payload = payload.decode(charset)
                except UnicodeDecodeError:
                    # Fallback to utf-8 with errors ignored
                    decoded_payload = payload.decode("utf-8", errors="ignore")

                if content_type == "text/plain":
                    text_body = decoded_payload
                elif content_type == "text/html":
                    html_body = decoded_payload
    else:
        # Not multipart - just get the payload
        payload = msg.get_payload(decode=True)
        if payload:
            charset = msg.get_content_charset() or "utf-8"
            try:
                decoded_payload = payload.decode(charset)
            except UnicodeDecodeError:
                # Fallback to utf-8 with errors ignored
                decoded_payload = payload.decode("utf-8", errors="ignore")

            content_type = msg.get_content_type()
            if content_type == "text/plain":
                text_body = decoded_payload
            elif content_type == "text/html":
                html_body = decoded_payload

    return html_body, text_body, attachments
