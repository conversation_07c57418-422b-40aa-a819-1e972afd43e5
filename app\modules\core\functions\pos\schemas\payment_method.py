"""Schemas for payment methods."""

import uuid  # noqa: E402
from typing import Optional, Dict, Any
from pydantic import BaseModel, ConfigDict, Field

from app.modules.core.functions.pos.models.payment_method import PaymentMethodType  # noqa: E402

# --- PaymentMethod Schemas ---

# Base schema for payment methods


class PaymentMethodBase(BaseModel):
    """Base schema for payment methods."""

    name: str = Field(..., min_length=1, max_length=100)
    method_type: PaymentMethodType
    is_active: bool = True
    processor_id: Optional[str] = None
    processor_config: Optional[Dict[str, Any]] = None


# Schema for creating a new payment method


class PaymentMethodCreate(PaymentMethodBase):
    """Schema for creating a new payment method."""

    pass  # tenant_id will be added from the endpoint context


# Schema for updating an existing payment method


class PaymentMethodUpdate(BaseModel):
    """Schema for updating an existing payment method."""

    name: Optional[str] = Field(None, min_length=1, max_length=100)
    method_type: Optional[PaymentMethodType] = None
    is_active: Optional[bool] = None
    processor_id: Optional[str] = None
    processor_config: Optional[Dict[str, Any]] = None


# Schema for reading a payment method


class PaymentMethodRead(PaymentMethodBase):
    """Schema for reading a payment method."""

    id: uuid.UUID
    tenant_id: uuid.UUID

    model_config = ConfigDict(from_attributes=True)


# --- TransactionPayment Schemas ---

# Base schema for transaction payments


class TransactionPaymentBase(BaseModel):
    """Base schema for transaction payments."""

    payment_method_id: uuid.UUID
    amount: str = Field(..., min_length=1)  # Amount as string to preserve precision
    reference: Optional[str] = None
    details: Optional[Dict[str, Any]] = None


# Schema for creating a new transaction payment


class TransactionPaymentCreate(TransactionPaymentBase):
    """Schema for creating a new transaction payment."""

    pass  # transaction_id and tenant_id will be added from the endpoint context


# Schema for reading a transaction payment


class TransactionPaymentRead(TransactionPaymentBase):
    """Schema for reading a transaction payment."""

    id: uuid.UUID
    transaction_id: uuid.UUID
    tenant_id: uuid.UUID

    model_config = ConfigDict(from_attributes=True)


# Schema for reading a transaction payment with payment method details


class TransactionPaymentWithMethodRead(TransactionPaymentRead):
    """Schema for reading a transaction payment with payment method details."""

    payment_method: PaymentMethodRead

    model_config = ConfigDict(from_attributes=True)
