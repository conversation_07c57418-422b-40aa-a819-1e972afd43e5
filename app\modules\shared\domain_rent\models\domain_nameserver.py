"""Domain Nameserver model for the Domain Rent module."""

import uuid  # noqa: E402
from typing import TYPE_CHECKING

from sqlalchemy import (  # noqa: E402
    Column,
    String,
    Integer,
    ForeignKey,
    Index,
)
from sqlalchemy.dialects.postgresql import UUID  # noqa: E402
from sqlalchemy.orm import relationship

from app.db.base import Base  # noqa: E402


class DomainNameserver(Base):
    """Domain Nameserver model.

    Stores nameserver information for domain registrations.
    """

    __tablename__ = "domain_nameservers"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    domain_registration_id = Column(
        UUID(as_uuid=True),
        ForeignKey("domain_registrations.id"),
        nullable=False,
        index=True,
    )

    hostname = Column(String, nullable=False)
    sort_order = Column(Integer, nullable=False, default=0)

    # Relationship to DomainRegistration
    domain_registration = relationship("DomainRegistration", back_populates="nameservers")

    # Indexes
    __table_args__ = (
        Index(
            "ix_domain_nameservers_domain_registration_id_hostname",
            domain_registration_id,
            hostname,
            unique=True,
        ),
    )

    def __repr__(self):
        return (
            f"<DomainNameserver(id={self.id}, "
            f"domain_registration_id='{self.domain_registration_id}', "
            f"hostname='{self.hostname}')>"
        )
