"""
API endpoints for CRM Account management.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from typing import List, Optional, Annotated, TYPE_CHECKING
import uuid

from sqlalchemy.ext.asyncio import AsyncSession
from app.core.db_dependencies import get_db

# Use TYPE_CHECKING to avoid circular import
if TYPE_CHECKING:
    from app.modules.core.users.models.user import User
from app.modules.core.tenants.models.tenant import Tenant

from app.modules.shared.crm.schemas.account import (
    AccountCreate,
    AccountUpdate,
    AccountRead,
)
from app.modules.shared.crm.models.account import AccountStatus, AccountType
from app.modules.shared.crm.services.account_service import account_service

from app.modules.core.auth.dependencies.auth_dependencies import (
    get_current_active_user,
)
from app.modules.core.tenants.dependencies.tenant_dependencies import (
    get_current_tenant_from_header,
    require_tenant_role,
)
from app.modules.core.roles.models.roles import RolePermissions

router = APIRouter(prefix="/accounts", tags=["CRM - Accounts"])

# Define required roles for different operations
view_roles = RolePermissions.VIEW_ROLES  # All roles that can view
write_roles = RolePermissions.ADMIN_ROLES  # Only admin roles can modify


@router.post(
    "/",
    response_model=AccountRead,
    status_code=status.HTTP_201_CREATED,
)
async def create_account(
    account_in: AccountCreate,
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    _: Annotated[None, Depends(require_tenant_role(write_roles, tenant_id_source="header"))] = None,
):
    """
    Create a new CRM account.
    Requires OWNER or MANAGER tenant role.
    """
    try:
        db_account = await account_service.create_account(db, current_tenant.id, account_in)
        return db_account
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create account: {str(e)}",
        )


@router.get(
    "/{account_id}",
    response_model=AccountRead,
)
async def get_account(
    account_id: uuid.UUID = Path(..., description="The ID of the account to retrieve"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    _: Annotated[None, Depends(require_tenant_role(view_roles, tenant_id_source="header"))] = None,
):
    """
    Get a CRM account by ID.
    Requires at least STAFF tenant role.
    """
    db_account = await account_service.get_account(db, current_tenant.id, account_id)
    if not db_account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Account with ID {account_id} not found",
        )
    return db_account


@router.get(
    "/",
    response_model=List[AccountRead],
)
async def get_accounts(
    skip: int = Query(0, ge=0, description="Number of accounts to skip"),
    limit: int = Query(100, ge=1, le=100, description="Maximum number of accounts to return"),
    status: Optional[AccountStatus] = Query(None, description="Filter by account status"),
    account_type: Optional[AccountType] = Query(None, description="Filter by account type"),
    search: Optional[str] = Query(
        None, description="Search term for account name, email, or phone"
    ),
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    _: Annotated[None, Depends(require_tenant_role(view_roles, tenant_id_source="header"))] = None,
):
    """
    Get all CRM accounts with optional filtering.
    Requires at least STAFF tenant role.
    """
    db_accounts = await account_service.get_accounts(
        db, current_tenant.id, skip, limit, status, account_type, search
    )
    return db_accounts


@router.put(
    "/{account_id}",
    response_model=AccountRead,
)
async def update_account(
    account_in: AccountUpdate,
    account_id: uuid.UUID = Path(..., description="The ID of the account to update"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    _: Annotated[None, Depends(require_tenant_role(write_roles, tenant_id_source="header"))] = None,
):
    """
    Update a CRM account.
    Requires OWNER or MANAGER tenant role.
    """
    db_account = await account_service.update_account(db, current_tenant.id, account_id, account_in)
    if not db_account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Account with ID {account_id} not found",
        )
    return db_account


@router.delete(
    "/{account_id}",
    status_code=status.HTTP_204_NO_CONTENT,
)
async def delete_account(
    account_id: uuid.UUID = Path(..., description="The ID of the account to delete"),
    db: AsyncSession = Depends(get_db),
    current_tenant: Annotated[Tenant, Depends(get_current_tenant_from_header)] = None,
    current_user: Annotated["User", Depends(get_current_active_user)] = None,
    _: Annotated[None, Depends(require_tenant_role(write_roles, tenant_id_source="header"))] = None,
):
    """
    Delete a CRM account.
    Requires OWNER or MANAGER tenant role.
    """
    success = await account_service.delete_account(db, current_tenant.id, account_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Account with ID {account_id} not found",
        )
    return None
