"""
Core module for the application.
"""

# Import models (User import removed to avoid circular imports)
from app.modules.core.users.models.tenant_user_association import TenantUserAssociation
from app.modules.core.tenants.models.tenant import Tenant
from app.modules.core.roles.models.roles import (
    SystemRole,
    TenantRole,
    TenantStaffSubRole,
    TenantType,
    RolePermissions,
)

# Import services
from app.modules.core.users.services.user_service import user_service
from app.modules.core.users.services.tenant_user_association_service import (
    tenant_user_association_service,
)
from app.modules.core.tenants.services.tenant_service import tenant_service

# Import dependencies
from app.modules.core.auth.dependencies.user_auth import get_current_active_user
from app.modules.core.auth.dependencies.role_auth import require_system_role, require_admin_user

# Import FTP system (conditional to avoid circular imports)
try:
    from app.modules.core.ftp_system.models import (
        FTPUser, FTPUpload, FTPDirectory, MenuItemMedia
    )
    from app.modules.core.ftp_system.services.ftp_user_service import ftp_user_service
    FTP_AVAILABLE = True
except ImportError:
    FTP_AVAILABLE = False

__all__ = [
    # Models (User removed to avoid circular imports)
    "TenantUserAssociation",
    "Tenant",
    "SystemRole",
    "TenantRole",
    "TenantStaffSubRole",
    "TenantType",
    "RolePermissions",
    # Services
    "user_service",
    "tenant_user_association_service",
    "tenant_service",
    # Dependencies
    "get_current_active_user",
    "require_system_role",
    "require_admin_user",
]
