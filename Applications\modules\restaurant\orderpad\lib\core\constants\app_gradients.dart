/// Application gradient constants and utilities
/// This file contains all gradient definitions, color combinations,
/// and gradient helpers for consistent visual styling throughout the application.

import 'package:flutter/material.dart';
import 'app_colors.dart';

/// Primary gradient definitions
class AppGradients {
  // Brand gradients
  static const LinearGradient primary = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      AppColors.primary,
      AppColors.primaryContainer,
    ],
  );
  
  static const LinearGradient primaryVertical = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      AppColors.primary,
      AppColors.primaryContainer,
    ],
  );
  
  static const LinearGradient primaryHorizontal = LinearGradient(
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
    colors: [
      AppColors.primary,
      AppColors.primaryContainer,
    ],
  );
  
  static const LinearGradient secondary = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      AppColors.secondary,
      AppColors.secondaryContainer,
    ],
  );
  
  static const LinearGradient tertiary = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      AppColors.tertiary,
      AppColors.tertiaryContainer,
    ],
  );
  
  // Surface gradients
  static const LinearGradient surface = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      AppColors.surface,
      AppColors.surfaceVariant,
    ],
  );
  
  static const LinearGradient surfaceElevated = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFFFFFFFF),
      AppColors.surface,
    ],
  );
  
  static const LinearGradient backgroundToSurface = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      AppColors.background,
      AppColors.surface,
    ],
  );
  
  // Status gradients
  static const LinearGradient success = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      AppColors.success,
      Color(0xFF2E7D32),
    ],
  );
  
  static const LinearGradient error = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      AppColors.error,
      Color(0xFFC62828),
    ],
  );
  
  static const LinearGradient warning = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      AppColors.warning,
      Color(0xFFE65100),
    ],
  );
  
  static const LinearGradient info = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      AppColors.info,
      Color(0xFF1565C0),
    ],
  );
  
  // Order status gradients
  static const LinearGradient orderPending = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFFFF3E0),
      Color(0xFFFFE0B2),
    ],
  );
  
  static const LinearGradient orderConfirmed = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFE3F2FD),
      Color(0xFFBBDEFB),
    ],
  );
  
  static const LinearGradient orderPreparing = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFFFF8E1),
      Color(0xFFFFECB3),
    ],
  );
  
  static const LinearGradient orderReady = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFE8F5E8),
      Color(0xFFC8E6C9),
    ],
  );
  
  static const LinearGradient orderDelivered = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFE8F5E8),
      Color(0xFFA5D6A7),
    ],
  );
  
  static const LinearGradient orderCancelled = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFFFEBEE),
      Color(0xFFFFCDD2),
    ],
  );
  
  // Table status gradients
  static const LinearGradient tableFree = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFE8F5E8),
      Color(0xFFC8E6C9),
    ],
  );
  
  static const LinearGradient tableOccupied = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFFFEBEE),
      Color(0xFFFFCDD2),
    ],
  );
  
  static const LinearGradient tableReserved = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFFFF3E0),
      Color(0xFFFFE0B2),
    ],
  );
  
  static const LinearGradient tableCleaning = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFE3F2FD),
      Color(0xFFBBDEFB),
    ],
  );
  
  // Payment status gradients
  static const LinearGradient paymentPending = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFFFF8E1),
      Color(0xFFFFECB3),
    ],
  );
  
  static const LinearGradient paymentCompleted = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFE8F5E8),
      Color(0xFFA5D6A7),
    ],
  );
  
  static const LinearGradient paymentFailed = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFFFEBEE),
      Color(0xFFEF9A9A),
    ],
  );
  
  // Special effect gradients
  static const LinearGradient shimmer = LinearGradient(
    begin: Alignment(-1.0, -0.3),
    end: Alignment(1.0, 0.3),
    colors: [
      Color(0xFFE0E0E0),
      Color(0xFFF5F5F5),
      Color(0xFFE0E0E0),
    ],
    stops: [0.0, 0.5, 1.0],
  );
  
  static const LinearGradient shimmerDark = LinearGradient(
    begin: Alignment(-1.0, -0.3),
    end: Alignment(1.0, 0.3),
    colors: [
      Color(0xFF424242),
      Color(0xFF616161),
      Color(0xFF424242),
    ],
    stops: [0.0, 0.5, 1.0],
  );
  
  static const LinearGradient glass = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0x20FFFFFF),
      Color(0x10FFFFFF),
    ],
  );
  
  static const LinearGradient glassDark = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0x20000000),
      Color(0x10000000),
    ],
  );
  
  // Overlay gradients
  static const LinearGradient overlay = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Colors.transparent,
      Color(0x80000000),
    ],
  );
  
  static const LinearGradient overlayReverse = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0x80000000),
      Colors.transparent,
    ],
  );
  
  static const LinearGradient scrim = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Colors.transparent,
      Color(0xCC000000),
    ],
  );
  
  // Radial gradients
  static const RadialGradient radialPrimary = RadialGradient(
    center: Alignment.center,
    radius: 1.0,
    colors: [
      AppColors.primary,
      AppColors.primaryContainer,
    ],
  );
  
  static const RadialGradient radialSurface = RadialGradient(
    center: Alignment.center,
    radius: 1.0,
    colors: [
      AppColors.surface,
      AppColors.background,
    ],
  );
  
  static const RadialGradient spotlight = RadialGradient(
    center: Alignment.center,
    radius: 0.8,
    colors: [
      Color(0x40FFFFFF),
      Colors.transparent,
    ],
  );
  
  // Sweep gradients
  static const SweepGradient rainbow = SweepGradient(
    center: Alignment.center,
    colors: [
      Color(0xFFFF0000),
      Color(0xFFFF8000),
      Color(0xFFFFFF00),
      Color(0xFF80FF00),
      Color(0xFF00FF00),
      Color(0xFF00FF80),
      Color(0xFF00FFFF),
      Color(0xFF0080FF),
      Color(0xFF0000FF),
      Color(0xFF8000FF),
      Color(0xFFFF00FF),
      Color(0xFFFF0080),
      Color(0xFFFF0000),
    ],
  );
  
  static const SweepGradient progress = SweepGradient(
    center: Alignment.center,
    startAngle: -1.57, // -90 degrees
    colors: [
      AppColors.primary,
      AppColors.secondary,
      AppColors.tertiary,
      AppColors.primary,
    ],
  );
}

/// Dark theme gradients
class AppGradientsDark {
  // Brand gradients for dark theme
  static const LinearGradient primary = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF90CAF9),
      Color(0xFF1976D2),
    ],
  );
  
  static const LinearGradient secondary = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFCE93D8),
      Color(0xFF7B1FA2),
    ],
  );
  
  static const LinearGradient surface = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFF2C2C2C),
      Color(0xFF1E1E1E),
    ],
  );
  
  static const LinearGradient backgroundToSurface = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFF121212),
      Color(0xFF1E1E1E),
    ],
  );
  
  // Status gradients for dark theme
  static const LinearGradient success = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF81C784),
      Color(0xFF388E3C),
    ],
  );
  
  static const LinearGradient error = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFE57373),
      Color(0xFFD32F2F),
    ],
  );
  
  static const LinearGradient warning = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFFFB74D),
      Color(0xFFF57C00),
    ],
  );
  
  static const LinearGradient info = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF64B5F6),
      Color(0xFF1976D2),
    ],
  );
}

/// Gradient utilities and helpers
class GradientUtils {
  /// Create a linear gradient with custom colors
  static LinearGradient createLinear({
    required List<Color> colors,
    Alignment begin = Alignment.topLeft,
    Alignment end = Alignment.bottomRight,
    List<double>? stops,
    TileMode tileMode = TileMode.clamp,
  }) {
    return LinearGradient(
      begin: begin,
      end: end,
      colors: colors,
      stops: stops,
      tileMode: tileMode,
    );
  }
  
  /// Create a radial gradient with custom colors
  static RadialGradient createRadial({
    required List<Color> colors,
    Alignment center = Alignment.center,
    double radius = 0.5,
    List<double>? stops,
    TileMode tileMode = TileMode.clamp,
  }) {
    return RadialGradient(
      center: center,
      radius: radius,
      colors: colors,
      stops: stops,
      tileMode: tileMode,
    );
  }
  
  /// Create a sweep gradient with custom colors
  static SweepGradient createSweep({
    required List<Color> colors,
    Alignment center = Alignment.center,
    double startAngle = 0.0,
    double endAngle = 6.28318530718, // 2 * pi
    List<double>? stops,
    TileMode tileMode = TileMode.clamp,
  }) {
    return SweepGradient(
      center: center,
      startAngle: startAngle,
      endAngle: endAngle,
      colors: colors,
      stops: stops,
      tileMode: tileMode,
    );
  }
  
  /// Get gradient for order status
  static LinearGradient getOrderStatusGradient(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return AppGradients.orderPending;
      case 'confirmed':
        return AppGradients.orderConfirmed;
      case 'preparing':
        return AppGradients.orderPreparing;
      case 'ready':
        return AppGradients.orderReady;
      case 'delivered':
        return AppGradients.orderDelivered;
      case 'cancelled':
        return AppGradients.orderCancelled;
      default:
        return AppGradients.surface;
    }
  }
  
  /// Get gradient for table status
  static LinearGradient getTableStatusGradient(String status) {
    switch (status.toLowerCase()) {
      case 'free':
        return AppGradients.tableFree;
      case 'occupied':
        return AppGradients.tableOccupied;
      case 'reserved':
        return AppGradients.tableReserved;
      case 'cleaning':
        return AppGradients.tableCleaning;
      default:
        return AppGradients.surface;
    }
  }
  
  /// Get gradient for payment status
  static LinearGradient getPaymentStatusGradient(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return AppGradients.paymentPending;
      case 'completed':
        return AppGradients.paymentCompleted;
      case 'failed':
        return AppGradients.paymentFailed;
      default:
        return AppGradients.surface;
    }
  }
  
  /// Create gradient with opacity
  static LinearGradient withValues(alpha: LinearGradient gradient, double opacity) {
    return LinearGradient(
      begin: gradient.begin,
      end: gradient.end,
      colors: gradient.colors.map((color) => color.withValues(alpha: opacity)).toList(),
      stops: gradient.stops,
      tileMode: gradient.tileMode,
    );
  }
  
  /// Reverse gradient colors
  static LinearGradient reverse(LinearGradient gradient) {
    return LinearGradient(
      begin: gradient.end,
      end: gradient.begin,
      colors: gradient.colors.reversed.toList(),
      stops: gradient.stops?.reversed.toList(),
      tileMode: gradient.tileMode,
    );
  }
  
  /// Blend two gradients
  static LinearGradient blend(LinearGradient gradient1, LinearGradient gradient2, double ratio) {
    final colors = <Color>[];
    final maxLength = gradient1.colors.length > gradient2.colors.length
        ? gradient1.colors.length
        : gradient2.colors.length;
    
    for (int i = 0; i < maxLength; i++) {
      final color1 = i < gradient1.colors.length
          ? gradient1.colors[i]
          : gradient1.colors.last;
      final color2 = i < gradient2.colors.length
          ? gradient2.colors[i]
          : gradient2.colors.last;
      
      colors.add(Color.lerp(color1, color2, ratio)!);
    }
    
    return LinearGradient(
      begin: gradient1.begin,
      end: gradient1.end,
      colors: colors,
    );
  }
  
  /// Create animated gradient colors
  static List<Color> animateColors(List<Color> colors, double animationValue) {
    if (colors.length < 2) return colors;
    
    final animatedColors = <Color>[];
    for (int i = 0; i < colors.length - 1; i++) {
      final color = Color.lerp(
        colors[i],
        colors[i + 1],
        (animationValue + i / colors.length) % 1.0,
      )!;
      animatedColors.add(color);
    }
    animatedColors.add(colors.last);
    
    return animatedColors;
  }
  
  /// Get gradient based on theme brightness
  static LinearGradient getThemeGradient(Brightness brightness, String type) {
    final isDark = brightness == Brightness.dark;
    
    switch (type.toLowerCase()) {
      case 'primary':
        return isDark ? AppGradientsDark.primary : AppGradients.primary;
      case 'secondary':
        return isDark ? AppGradientsDark.secondary : AppGradients.secondary;
      case 'surface':
        return isDark ? AppGradientsDark.surface : AppGradients.surface;
      case 'success':
        return isDark ? AppGradientsDark.success : AppGradients.success;
      case 'error':
        return isDark ? AppGradientsDark.error : AppGradients.error;
      case 'warning':
        return isDark ? AppGradientsDark.warning : AppGradients.warning;
      case 'info':
        return isDark ? AppGradientsDark.info : AppGradients.info;
      default:
        return isDark ? AppGradientsDark.surface : AppGradients.surface;
    }
  }
  
  /// Create gradient from single color
  static LinearGradient fromColor(Color color, {double lightenAmount = 0.1}) {
    final lighterColor = Color.lerp(color, Colors.white, lightenAmount)!;
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [lighterColor, color],
    );
  }
  
  /// Create subtle gradient for cards
  static LinearGradient cardGradient(Color baseColor) {
    return LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        baseColor,
        Color.lerp(baseColor, Colors.black, 0.05)!,
      ],
    );
  }
  
  /// Create button gradient
  static LinearGradient buttonGradient(Color baseColor) {
    return LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        Color.lerp(baseColor, Colors.white, 0.1)!,
        baseColor,
        Color.lerp(baseColor, Colors.black, 0.1)!,
      ],
    );
  }
}

/// Gradient animation controller
class GradientAnimation {
  static LinearGradient animatedGradient({
    required List<Color> colors,
    required double animationValue,
    Alignment begin = Alignment.topLeft,
    Alignment end = Alignment.bottomRight,
  }) {
    final animatedColors = GradientUtils.animateColors(colors, animationValue);
    return LinearGradient(
      begin: begin,
      end: end,
      colors: animatedColors,
    );
  }
  
  static LinearGradient shimmerAnimation({
    required double animationValue,
    bool isDark = false,
  }) {
    final baseGradient = isDark ? AppGradients.shimmerDark : AppGradients.shimmer;
    final transform = Matrix4.identity()
      ..translate(animationValue * 200 - 100, 0.0);
    
    return LinearGradient(
      begin: baseGradient.begin,
      end: baseGradient.end,
      colors: baseGradient.colors,
      stops: baseGradient.stops,
      transform: GradientRotation(animationValue * 2 * 3.14159),
    );
  }
}