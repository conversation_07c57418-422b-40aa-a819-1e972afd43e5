"""
Currency specific schemas for tenant settings.
"""

from typing import Optional, Dict, Any

from pydantic import BaseModel, Field, validator


class CurrencySettingsUpdate(BaseModel):
    """Schema for updating currency settings."""
    
    default_currency: Optional[str] = Field(
        None,
        min_length=3,
        max_length=3,
        description="Default currency code (1:1 ratio for stability)"
    )
    currency_config: Optional[Dict[str, Any]] = Field(
        None,
        description="Multi-currency configuration with exchange rates and formatting"
    )

    @validator('default_currency')
    def validate_default_currency(cls, v):
        """Validate default currency code format."""
        if v and len(v) != 3:
            raise ValueError('Default currency must be a 3-letter ISO 4217 code')
        return v.upper() if v else v
