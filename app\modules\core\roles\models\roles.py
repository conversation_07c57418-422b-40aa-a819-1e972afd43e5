from enum import Enum
from typing import List, Dict


class SystemRole(str, Enum):
    """
    Defines system-wide roles for users.

    These roles determine what a user can do at the system level,
    regardless of their tenant associations.
    """

    ADMIN = "admin"  # System administrator with full access
    USER = "user"  # Regular user, permissions determined by tenant roles


class TenantRole(str, Enum):
    """
    Defines tenant-specific roles for users.

    These roles determine what a user can do within a specific tenant.
    The hierarchy is: OWNER > MANAGER > STAFF > TVENDOR > SUPPLIER > TCOSTUMER > CUSTOMER
    
    New B2B roles:
    - TVENDOR: Authorized B2B seller with product management and sales permissions
    - TCOSTUMER: Authorized B2B buyer with bulk purchasing and special pricing access
    """

    OWNER = "owner"  # Tenant owner with full administrative access
    MANAGER = "manager"  # Tenant manager with administrative access to specific areas
    STAFF = "staff"  # Tenant staff with operational permissions
    TVENDOR = "tvendor"  # Authorized B2B seller with product management permissions
    SUPPLIER = "supplier"  # External supplier with access to supplier portal
    TCOSTUMER = "tcostumer"  # Authorized B2B buyer with bulk purchasing access
    CUSTOMER = "customer"  # Tenant customer/client with limited access


class MarketContext(str, Enum):
    """
    Defines market context for role-based access control.
    Used for switching between B2B and B2C operations.
    """
    
    B2B = "b2b"  # Business-to-Business market context
    B2C = "b2c"  # Business-to-Consumer market context
    MARKETPLACE = "marketplace"  # Mixed marketplace context (both B2B and B2C)


class TenantType(str, Enum):
    """
    Defines the type or category of a Tenant, which can influence
    available features or staff sub-roles.

    Each type has specific modules and functionalities available:
    - RESTAURANT: Full restaurant management (menu, orders, KDS, delivery)
    - SHOP: Retail management (inventory, POS, sales)
    - PHARMACY: Pharmaceutical management (prescriptions, controlled substances)
    - SERVICE_COMPANY: Service-based business (appointments, contracts)
    - FACTORY: Manufacturing management (production, quality control)
    - GENERAL: Default or for tenants not fitting specific types
    """

    RESTAURANT = "restaurant"
    SHOP = "shop"
    PHARMACY = "pharmacy"
    SERVICE_COMPANY = "service_company"
    FACTORY = "factory"
    GENERAL = "general"  # Default or for tenants not fitting specific types


class TenantStaffSubRole(str, Enum):
    """
    Defines specific sub-roles for users with the 'STAFF' TenantRole.
    These sub-roles can be used for more granular permission control.
    Tenant type specific sub-roles should be validated in the service layer.
    """

    # Restaurant specific
    COOK = "cook"  # Cozinheiro, acesso ao KDS
    WAITER = "waiter"  # Garçom, acesso ao Orderpad
    DELIVERY_PERSON = "delivery_person"  # Entregador, acesso ao App de Delivery
    CLEANER = "cleaner"  # Limpeza, acesso limitado

    # Shop specific
    SELLER = "seller"  # Vendedor, acesso ao POS e gestão de produtos/pedidos da loja
    STOCKIST = "stockist"  # Estoquista, acesso ao inventário da loja

    # General (can be used if no specific sub-role fits or for broader access)
    GENERAL_STAFF = "general_staff"


class TVendorPermissions(str, Enum):
    """
    Specific permissions for TVENDOR role in B2B marketplace.
    """
    
    MANAGE_PRODUCTS = "manage_products"  # Create, update, delete products
    VIEW_ANALYTICS = "view_analytics"  # Access sales analytics and reports
    MANAGE_INVENTORY = "manage_inventory"  # Manage product inventory
    PROCESS_ORDERS = "process_orders"  # Process and fulfill orders
    SET_PRICING = "set_pricing"  # Set product pricing and discounts
    BULK_OPERATIONS = "bulk_operations"  # Perform bulk product operations


class TCostumerPermissions(str, Enum):
    """
    Specific permissions for TCOSTUMER role in B2B marketplace.
    """
    
    BULK_PURCHASE = "bulk_purchase"  # Make bulk purchases with special pricing
    VIEW_B2B_CATALOG = "view_b2b_catalog"  # Access B2B-only product catalog
    REQUEST_QUOTES = "request_quotes"  # Request custom quotes for large orders
    MANAGE_PURCHASE_ORDERS = "manage_purchase_orders"  # Create and manage POs
    VIEW_PURCHASE_HISTORY = "view_purchase_history"  # Access detailed purchase history
    NEGOTIATE_TERMS = "negotiate_terms"  # Negotiate payment and delivery terms


# Standard role combinations for various operations


class RolePermissions:
    """
    Predefined role combinations for various operations across the application.

    Use these constants in role-checking dependencies to ensure consistency.
    """

    # Roles that can perform administrative actions
    ADMIN_ROLES: List[str] = [TenantRole.OWNER.value, TenantRole.MANAGER.value]

    # Roles that can perform operational actions
    STAFF_ROLES: List[str] = [
        TenantRole.OWNER.value,
        TenantRole.MANAGER.value,
        TenantRole.STAFF.value,
    ]

    # B2B seller roles
    B2B_SELLER_ROLES: List[str] = [
        TenantRole.OWNER.value,
        TenantRole.MANAGER.value,
        TenantRole.STAFF.value,
        TenantRole.TVENDOR.value,
    ]

    # B2B buyer roles  
    B2B_BUYER_ROLES: List[str] = [
        TenantRole.TCOSTUMER.value,
    ]

    # All B2B roles
    B2B_ROLES: List[str] = [
        TenantRole.TVENDOR.value,
        TenantRole.TCOSTUMER.value,
    ]

    # Roles that can only view/read data
    VIEW_ROLES: List[str] = [
        TenantRole.OWNER.value,
        TenantRole.MANAGER.value,
        TenantRole.STAFF.value,
        TenantRole.TVENDOR.value,
        TenantRole.SUPPLIER.value,
        TenantRole.TCOSTUMER.value,
        TenantRole.CUSTOMER.value,
    ]

    # Role hierarchy for reference (higher number = more permissions)
    ROLE_HIERARCHY: Dict[str, int] = {
        TenantRole.OWNER.value: 100,
        TenantRole.MANAGER.value: 75,
        TenantRole.STAFF.value: 50,
        TenantRole.TVENDOR.value: 40,  # B2B seller with product management
        TenantRole.SUPPLIER.value: 25,
        TenantRole.TCOSTUMER.value: 20,  # B2B buyer with special access
        TenantRole.CUSTOMER.value: 10,
    }

    # Market context permissions
    MARKET_CONTEXT_PERMISSIONS: Dict[str, List[str]] = {
        MarketContext.B2B.value: [
            TenantRole.OWNER.value,
            TenantRole.MANAGER.value,
            TenantRole.STAFF.value,
            TenantRole.TVENDOR.value,
            TenantRole.TCOSTUMER.value,
        ],
        MarketContext.B2C.value: [
            TenantRole.OWNER.value,
            TenantRole.MANAGER.value,
            TenantRole.STAFF.value,
            TenantRole.SUPPLIER.value,
            TenantRole.CUSTOMER.value,
        ],
        MarketContext.MARKETPLACE.value: [
            TenantRole.OWNER.value,
            TenantRole.MANAGER.value,
            TenantRole.STAFF.value,
            TenantRole.TVENDOR.value,
            TenantRole.SUPPLIER.value,
            TenantRole.TCOSTUMER.value,
            TenantRole.CUSTOMER.value,
        ],
    }

    @staticmethod
    def has_permission(user_role: str, required_roles: List[str]) -> bool:
        """
        Check if a user's role has permission based on the required roles.
        Considers role hierarchy - higher roles automatically have permissions of lower roles.

        Args:
            user_role: The user's role within the tenant
            required_roles: List of roles that are allowed to perform the action

        Returns:
            bool: True if the user has permission, False otherwise
        """
        # Direct match - role is explicitly in the required roles
        if user_role in required_roles:
            return True

        # Check hierarchy - if user's role has higher value than any required role
        user_role_value = RolePermissions.ROLE_HIERARCHY.get(user_role, 0)
        for required_role in required_roles:
            required_role_value = RolePermissions.ROLE_HIERARCHY.get(required_role, 0)
            # If user's role has higher value than the required role, grant permission
            if user_role_value > required_role_value:
                return True

        return False

    @staticmethod
    def has_market_access(user_role: str, market_context: str) -> bool:
        """
        Check if a user's role has access to a specific market context.

        Args:
            user_role: The user's role within the tenant
            market_context: The market context (B2B, B2C, MARKETPLACE)

        Returns:
            bool: True if the user has access to the market context
        """
        allowed_roles = RolePermissions.MARKET_CONTEXT_PERMISSIONS.get(market_context, [])
        return user_role in allowed_roles

    @staticmethod
    def get_user_market_contexts(user_role: str) -> List[str]:
        """
        Get all market contexts that a user role has access to.

        Args:
            user_role: The user's role within the tenant

        Returns:
            List[str]: List of market contexts the user can access
        """
        contexts = []
        for context, roles in RolePermissions.MARKET_CONTEXT_PERMISSIONS.items():
            if user_role in roles:
                contexts.append(context)
        return contexts

    @staticmethod
    def is_b2b_role(user_role: str) -> bool:
        """
        Check if a role is specifically a B2B role.

        Args:
            user_role: The user's role within the tenant

        Returns:
            bool: True if the role is a B2B role
        """
        return user_role in RolePermissions.B2B_ROLES

    @staticmethod
    def can_manage_b2b_products(user_role: str) -> bool:
        """
        Check if a role can manage B2B products.

        Args:
            user_role: The user's role within the tenant

        Returns:
            bool: True if the role can manage B2B products
        """
        return user_role in RolePermissions.B2B_SELLER_ROLES
