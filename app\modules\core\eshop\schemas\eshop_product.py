import uuid
from pydantic import BaseModel, Field
from typing import List, Optional
from app.modules.core.eshop.models.product import ProductType
from decimal import Decimal
from datetime import datetime

# Variant Schemas
class VariantOptionRead(BaseModel):
    id: uuid.UUID
    name: str
    price_adjustment: float
    is_default: bool

    class Config:
        from_attributes = True

class VariantGroupRead(BaseModel):
    id: uuid.UUID
    name: str
    min_selection: int
    max_selection: int
    options: List[VariantOptionRead]

    class Config:
        from_attributes = True

# Modifier Schemas
class ModifierOptionRead(BaseModel):
    id: uuid.UUID
    name: str
    price_adjustment: float
    is_default: Optional[bool]

    class Config:
        from_attributes = True

class ModifierGroupRead(BaseModel):
    id: uuid.UUID
    name: str
    min_selection: int
    max_selection: int
    options: List[ModifierOptionRead]

    class Config:
        from_attributes = True

# Optional Schemas
class OptionalOptionRead(BaseModel):
    id: uuid.UUID
    name: str
    price_adjustment: float

    class Config:
        from_attributes = True

class OptionalGroupRead(BaseModel):
    id: uuid.UUID
    name: str
    min_selection: int
    max_selection: int
    options: List[OptionalOptionRead]

    class Config:
        from_attributes = True


# Product Schemas
class eshopProductBase(BaseModel):
    name: str = Field(..., max_length=150)
    description_short: Optional[str] = Field(None, max_length=255)
    description_long: Optional[str] = None
    base_price: float
    product_type: ProductType
    is_active: bool = True
    category_id: Optional[uuid.UUID] = None
    supplier_id: Optional[uuid.UUID] = None

    class Config:
        from_attributes = True

class eshopProductCreate(eshopProductBase):
    pass

class eshopProductUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=150)
    description_short: Optional[str] = Field(None, max_length=255)
    description_long: Optional[str] = None
    base_price: Optional[float] = None
    product_type: Optional[ProductType] = None
    is_active: Optional[bool] = None
    category_id: Optional[uuid.UUID] = None
    supplier_id: Optional[uuid.UUID] = None

    class Config:
        from_attributes = True

class eshopProductRead(eshopProductBase):
    id: uuid.UUID
    variant_groups: List[VariantGroupRead] = []
    modifier_groups: List[ModifierGroupRead] = []
    optional_groups: List[OptionalGroupRead] = []

    class Config:
        from_attributes = True

class ProductImageSchema(BaseModel):
    image_url: str
    alt_text: Optional[str] = None

    class Config:
        from_attributes = True


class ProductVariantSchema(BaseModel):
    id: uuid.UUID
    name: str
    price_modifier: Decimal
    stock_quantity: int

    class Config:
        from_attributes = True


class ProductReviewSchema(BaseModel):
    id: uuid.UUID
    user_name: str
    rating: int
    comment: Optional[str] = None
    created_at: datetime

    class Config:
        from_attributes = True


class ProductBase(BaseModel):
    name: str
    description: Optional[str] = None
    price: Decimal
    is_active: bool = True

    class Config:
        from_attributes = True


class ProductCreate(ProductBase):
    category_id: uuid.UUID
    tenant_id: uuid.UUID


class ProductUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    price: Optional[Decimal] = None
    is_active: Optional[bool] = None
    category_id: Optional[uuid.UUID] = None

    class Config:
        from_attributes = True


class ProductRead(ProductBase):
    id: uuid.UUID
    category: "EshopCategoryRead"  # Forward reference
    images: List[ProductImageSchema] = []
    variants: List[ProductVariantSchema] = []
    reviews: List[ProductReviewSchema] = []
    average_rating: Optional[float] = None

    class Config:
        from_attributes = True


class UniversalProductsResponse(BaseModel):
    """Response schema for universal products with pagination"""
    items: List[eshopProductRead]
    total: int
    page: int
    size: int
    pages: int

    class Config:
        from_attributes = True