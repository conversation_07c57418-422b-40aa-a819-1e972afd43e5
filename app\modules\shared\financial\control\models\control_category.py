"""Financial Control Category Models."""

import uuid
from typing import TYPE_CHECKING, List
from sqlalchemy import (
    Column, String, ForeignKey, Text, Boolean, Integer, Index, func, DateTime
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.base import Base

if TYPE_CHECKING:
    from app.modules.core.tenants.models.tenant import Tenant
    from app.modules.core.users.models.user import User
    from .control_entry import FinancialControlEntry


class ControlCategory(Base):
    """
    Model for financial control categories.
    
    Provides hierarchical categorization for financial control entries
    with support for subcategories and custom organization.
    """
    
    __tablename__ = "financial_control_categories"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("tenants.id"), 
        nullable=False, 
        index=True
    )
    
    # Category details
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    code = Column(String(20), nullable=True)  # Short code for quick reference
    color = Column(String(7), nullable=True)  # Hex color for UI
    icon = Column(String(50), nullable=True)  # Icon name for UI
    
    # Hierarchy support
    parent_id = Column(
        UUID(as_uuid=True),
        ForeignKey("financial_control_categories.id"),
        nullable=True,
        index=True
    )
    level = Column(Integer, nullable=False, default=0)
    sort_order = Column(Integer, nullable=False, default=0)
    
    # Category configuration
    is_active = Column(Boolean, nullable=False, default=True)
    is_system = Column(Boolean, nullable=False, default=False)  # System categories
    is_tax_category = Column(Boolean, nullable=False, default=False)
    
    # Budget and limits
    monthly_budget_limit = Column(String, nullable=True)  # JSON for monthly limits
    annual_budget_limit = Column(String, nullable=True)   # JSON for annual limits
    alert_threshold = Column(Integer, nullable=True)      # Percentage for alerts
    
    # Audit fields
    created_by = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        index=True
    )
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    created_at = Column(DateTime, nullable=False, default=func.now())
    updated_at = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now())
    
    # Additional metadata
    additional_data = Column(Text, nullable=True)  # JSON for additional configuration
    
    # Relationships
    tenant = relationship("app.modules.core.tenants.models.tenant.Tenant", viewonly=True)
    parent = relationship("ControlCategory", remote_side=[id], backref="children")
    entries = relationship("FinancialControlEntry", back_populates="category")
    created_by_user = relationship(
        "app.modules.core.users.models.user.User", 
        foreign_keys=[created_by],
        viewonly=True
    )
    updated_by_user = relationship(
        "app.modules.core.users.models.user.User", 
        foreign_keys=[updated_by],
        viewonly=True
    )
    
    # Table indexes for performance
    __table_args__ = (
        Index("ix_control_categories_tenant_name", "tenant_id", "name"),
        Index("ix_control_categories_tenant_parent", "tenant_id", "parent_id"),
        Index("ix_control_categories_tenant_active", "tenant_id", "is_active"),
        Index("ix_control_categories_code", "code"),
    )
    
    def __repr__(self):
        return (
            f"<ControlCategory(id={self.id}, "
            f"name='{self.name}', "
            f"level={self.level})>"
        )
    
    @property
    def full_path(self) -> str:
        """Get full category path."""
        if self.parent:
            return f"{self.parent.full_path} > {self.name}"
        return self.name
    
    @property
    def has_children(self) -> bool:
        """Check if category has subcategories."""
        return len(self.children) > 0 if hasattr(self, 'children') else False
