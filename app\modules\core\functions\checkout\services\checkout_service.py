"""
Checkout Service for EShop System
=================================

Serviço para gerenciamento do processo de checkout com integração
a carrinho, pagamentos, entrega e criação de pedidos.
"""

import uuid
import logging
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Optional, Dict, Any, List

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from sqlalchemy import and_, func

from app.core.exceptions import ValidationError, NotFoundError, BusinessLogicError
from app.modules.core.functions.checkout.models.checkout import (
    CheckoutSession, CheckoutStatus, PaymentMethod, ShippingMethod
)
from app.modules.core.functions.checkout.schemas.checkout import (
    CheckoutSessionCreate, CheckoutInitiateRequest, CheckoutCompleteRequest,
    CheckoutStats, ShippingQuoteRequest, ShippingQuoteResponse
)
from app.modules.core.functions.cart.models.cart import Cart, CartStatus
from app.modules.core.functions.orders.models.order import Order, OrderStatus
from app.modules.core.functions.orders.services.order_service import OrderService

logger = logging.getLogger(__name__)


class CheckoutService:
    """Serviço para gerenciamento do processo de checkout."""
    
    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session
        self.order_service = OrderService(db_session)
    
    async def initiate_checkout(
        self,
        request: CheckoutInitiateRequest,
        tenant_id: uuid.UUID,
        user_id: Optional[uuid.UUID] = None
    ) -> CheckoutSession:
        """
        Inicia processo de checkout a partir de um carrinho.
        
        Args:
            request: Dados para iniciar checkout
            tenant_id: ID do tenant
            user_id: ID do usuário (opcional para checkout anônimo)
            
        Returns:
            CheckoutSession: Sessão de checkout criada
        """
        try:
            # Verificar se o carrinho existe e está ativo
            cart = await self._get_active_cart(request.cart_id, tenant_id)
            if not cart:
                raise NotFoundError("Carrinho não encontrado ou inativo")
            
            if not cart.items:
                raise ValidationError("Carrinho está vazio")
            
            # Verificar se já existe checkout ativo para este carrinho
            existing_checkout = await self._get_active_checkout_by_cart(request.cart_id)
            if existing_checkout:
                # Atualizar checkout existente se não expirado
                if not existing_checkout.is_expired:
                    return existing_checkout
                else:
                    # Marcar como expirado
                    existing_checkout.status = CheckoutStatus.EXPIRED
            
            # Calcular custos de entrega se método especificado
            shipping_cost = Decimal('0.00')
            if request.shipping_method and request.shipping_address:
                shipping_cost = await self._calculate_shipping_cost(
                    cart, request.shipping_method, request.shipping_address
                )
            
            # Criar nova sessão de checkout
            checkout_session = CheckoutSession(
                tenant_id=tenant_id,
                cart_id=request.cart_id,
                user_id=user_id,
                market_context=request.market_context,
                shipping_method=request.shipping_method,
                shipping_address=request.shipping_address.model_dump() if request.shipping_address else None,
                billing_address=request.billing_address.model_dump() if request.billing_address else None,
                status=CheckoutStatus.INITIATED
            )
            
            # Calcular totais
            checkout_session.calculate_totals(cart.subtotal, shipping_cost)
            
            # Definir expiração (30 minutos)
            checkout_session.set_expiration(minutes=30)
            
            self.db_session.add(checkout_session)
            await self.db_session.commit()
            await self.db_session.refresh(checkout_session)
            
            logger.info(f"Checkout iniciado: {checkout_session.id}")
            return checkout_session
            
        except Exception as e:
            logger.error(f"Erro ao iniciar checkout: {e}")
            await self.db_session.rollback()
            raise
    
    async def update_checkout_session(
        self,
        checkout_id: uuid.UUID,
        updates: Dict[str, Any]
    ) -> CheckoutSession:
        """
        Atualiza sessão de checkout.
        
        Args:
            checkout_id: ID da sessão de checkout
            updates: Dados para atualização
            
        Returns:
            CheckoutSession: Sessão atualizada
        """
        try:
            checkout = await self.get_checkout_by_id(checkout_id)
            if not checkout:
                raise NotFoundError("Sessão de checkout não encontrada")
            
            if checkout.is_expired:
                raise ValidationError("Sessão de checkout expirada")
            
            if checkout.status not in [CheckoutStatus.INITIATED, CheckoutStatus.PAYMENT_PENDING]:
                raise ValidationError("Sessão de checkout não pode ser atualizada")
            
            # Atualizar campos permitidos
            for field, value in updates.items():
                if hasattr(checkout, field):
                    setattr(checkout, field, value)
            
            # Recalcular totais se necessário
            if 'shipping_method' in updates or 'shipping_address' in updates:
                cart = await self._get_active_cart(checkout.cart_id, checkout.tenant_id)
                shipping_cost = await self._calculate_shipping_cost(
                    cart, checkout.shipping_method, checkout.shipping_address
                )
                checkout.calculate_totals(cart.subtotal, shipping_cost)
            
            checkout.updated_at = datetime.utcnow()
            
            await self.db_session.commit()
            await self.db_session.refresh(checkout)
            
            logger.info(f"Checkout atualizado: {checkout_id}")
            return checkout
            
        except Exception as e:
            logger.error(f"Erro ao atualizar checkout: {e}")
            await self.db_session.rollback()
            raise
    
    async def complete_checkout(
        self,
        checkout_id: uuid.UUID,
        request: CheckoutCompleteRequest
    ) -> Dict[str, Any]:
        """
        Completa o processo de checkout criando pedido e processando pagamento.
        
        Args:
            checkout_id: ID da sessão de checkout
            request: Dados para completar checkout
            
        Returns:
            Dict com resultado do checkout
        """
        try:
            checkout = await self.get_checkout_by_id(checkout_id)
            if not checkout:
                raise NotFoundError("Sessão de checkout não encontrada")
            
            if checkout.is_expired:
                raise ValidationError("Sessão de checkout expirada")
            
            if checkout.status != CheckoutStatus.INITIATED:
                raise ValidationError("Checkout já foi processado")
            
            # Atualizar método de pagamento
            checkout.update_payment_info(
                payment_method=request.payment_method,
                payment_provider=request.payment_provider
            )
            checkout.status = CheckoutStatus.PAYMENT_PENDING
            
            # Obter carrinho com itens
            cart = await self._get_active_cart(checkout.cart_id, checkout.tenant_id)
            
            # Criar pedido
            order = await self._create_order_from_checkout(checkout, cart)
            
            # Simular processamento de pagamento
            payment_result = await self._process_payment(checkout, request)
            
            if payment_result['success']:
                # Confirmar pagamento e completar checkout
                checkout.confirm_payment()
                checkout.complete_checkout(order.id)
                
                # Marcar carrinho como convertido
                cart.status = CartStatus.CONVERTED
                cart.converted_at = datetime.utcnow()
                
                await self.db_session.commit()
                
                logger.info(f"Checkout completado: {checkout_id}, Pedido: {order.id}")
                
                return {
                    'success': True,
                    'checkout_id': checkout.id,
                    'order_id': order.id,
                    'order_number': order.order_number,
                    'payment_status': 'confirmed',
                    'total_amount': float(checkout.total_amount)
                }
            else:
                # Falha no pagamento
                checkout.status = CheckoutStatus.PAYMENT_FAILED
                await self.db_session.commit()
                
                return {
                    'success': False,
                    'error': payment_result.get('error', 'Falha no pagamento'),
                    'checkout_id': checkout.id
                }
                
        except Exception as e:
            logger.error(f"Erro ao completar checkout: {e}")
            await self.db_session.rollback()
            raise
    
    async def get_checkout_by_id(self, checkout_id: uuid.UUID) -> Optional[CheckoutSession]:
        """
        Busca sessão de checkout por ID.
        
        Args:
            checkout_id: ID da sessão
            
        Returns:
            CheckoutSession: Sessão encontrada ou None
        """
        query = select(CheckoutSession).options(
            selectinload(CheckoutSession.cart),
            selectinload(CheckoutSession.order)
        ).where(CheckoutSession.id == checkout_id)
        
        result = await self.db_session.execute(query)
        return result.scalars().first()
    
    async def cancel_checkout(self, checkout_id: uuid.UUID) -> bool:
        """
        Cancela sessão de checkout.
        
        Args:
            checkout_id: ID da sessão
            
        Returns:
            bool: True se cancelado com sucesso
        """
        try:
            checkout = await self.get_checkout_by_id(checkout_id)
            if not checkout:
                raise NotFoundError("Sessão de checkout não encontrada")
            
            if checkout.status in [CheckoutStatus.COMPLETED, CheckoutStatus.CANCELLED]:
                raise ValidationError("Checkout não pode ser cancelado")
            
            checkout.status = CheckoutStatus.CANCELLED
            checkout.updated_at = datetime.utcnow()
            
            await self.db_session.commit()
            
            logger.info(f"Checkout cancelado: {checkout_id}")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao cancelar checkout: {e}")
            await self.db_session.rollback()
            raise
    
    async def _get_active_cart(self, cart_id: uuid.UUID, tenant_id: uuid.UUID) -> Optional[Cart]:
        """Busca carrinho ativo com itens."""
        query = select(Cart).options(
            selectinload(Cart.items)
        ).where(
            and_(
                Cart.id == cart_id,
                Cart.tenant_id == tenant_id,
                Cart.status == CartStatus.ACTIVE
            )
        )
        
        result = await self.db_session.execute(query)
        return result.scalars().first()
    
    async def _get_active_checkout_by_cart(self, cart_id: uuid.UUID) -> Optional[CheckoutSession]:
        """Busca checkout ativo para um carrinho."""
        query = select(CheckoutSession).where(
            and_(
                CheckoutSession.cart_id == cart_id,
                CheckoutSession.status.in_([
                    CheckoutStatus.INITIATED,
                    CheckoutStatus.PAYMENT_PENDING,
                    CheckoutStatus.PAYMENT_PROCESSING
                ])
            )
        )
        
        result = await self.db_session.execute(query)
        return result.scalars().first()
    
    async def _calculate_shipping_cost(
        self, 
        cart: Cart, 
        shipping_method: ShippingMethod, 
        shipping_address: Dict[str, Any]
    ) -> Decimal:
        """Calcula custo de entrega (implementação simplificada)."""
        base_costs = {
            ShippingMethod.STANDARD: Decimal('10.00'),
            ShippingMethod.EXPRESS: Decimal('20.00'),
            ShippingMethod.OVERNIGHT: Decimal('35.00'),
            ShippingMethod.PICKUP: Decimal('0.00'),
            ShippingMethod.SAME_DAY: Decimal('50.00')
        }
        
        return base_costs.get(shipping_method, Decimal('10.00'))
    
    async def _create_order_from_checkout(
        self, 
        checkout: CheckoutSession, 
        cart: Cart
    ) -> Order:
        """Cria pedido a partir da sessão de checkout."""
        # Implementação simplificada - deve integrar com OrderService
        order_number = f"ESH-{datetime.now().strftime('%Y%m%d')}-{uuid.uuid4().hex[:8].upper()}"
        
        order = Order(
            tenant_id=checkout.tenant_id,
            user_id=checkout.user_id,
            order_number=order_number,
            order_type="eshop",
            status=OrderStatus.PENDING,
            subtotal=checkout.subtotal,
            tax=checkout.tax_amount,
            discount=checkout.discount_amount,
            total=checkout.total_amount,
            notes=checkout.customer_notes,
            order_metadata={
                'checkout_id': str(checkout.id),
                'market_context': checkout.market_context,
                'shipping_method': checkout.shipping_method,
                'payment_method': checkout.payment_method
            }
        )
        
        self.db_session.add(order)
        await self.db_session.flush()
        
        return order
    
    async def _process_payment(
        self, 
        checkout: CheckoutSession, 
        request: CheckoutCompleteRequest
    ) -> Dict[str, Any]:
        """Processa pagamento (implementação simulada)."""
        # Implementação simplificada - deve integrar com serviço de pagamento real
        
        # Simular processamento
        import random
        success = random.choice([True, True, True, False])  # 75% de sucesso
        
        if success:
            return {
                'success': True,
                'transaction_id': f"txn_{uuid.uuid4().hex[:12]}",
                'status': 'confirmed'
            }
        else:
            return {
                'success': False,
                'error': 'Pagamento recusado pelo banco'
            }


# Factory function
def get_checkout_service(db_session: AsyncSession) -> CheckoutService:
    """Factory function para obter instância do CheckoutService."""
    return CheckoutService(db_session)


# Instância global (placeholder)
checkout_service = None
